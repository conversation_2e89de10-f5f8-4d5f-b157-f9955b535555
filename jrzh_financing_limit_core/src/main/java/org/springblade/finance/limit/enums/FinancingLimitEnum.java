package org.springblade.finance.limit.enums;

import lombok.Getter;

public interface FinancingLimitEnum {

    /**
     * 水位是否推送行方枚举
     */
    @Getter
    enum IsPushCapitalEnum implements FinancingLimitEnum {
        PUSH(1, "已推送"),
        UN_PUSH(0, "未推送"),
        ;
        private Integer code;
        private String desc;
        IsPushCapitalEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 水位状态枚举
     */
    @Getter
    enum StatusEnum implements FinancingLimitEnum {
        NOT_USED(0,"未使用"),
        IN_USED(1,"使用中"),
        USED_ALREADY(2,"已使用")
        ;
        private final Integer code;
        private final String desc;

        StatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 水位是否重复使用
     */
    @Getter
    enum IsReuseEnum implements FinancingLimitEnum {
        REPEAT_USE("0","重复使用"),
        UN_REPEAT_USE("1","非重复使用"),
        ;
        private final String code;
        private final String desc;

        IsReuseEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * MySQL格式化时间
     */
    @Getter
    enum MySqlDateFormatEnum implements FinancingLimitEnum {
        MONTH("DATE_FORMAT(limit_date, '%Y-%m') = DATE_FORMAT({0}, '%Y-%m')","年月等于"),
        MONTH_GE("DATE_FORMAT(limit_date, '%Y-%m') >= DATE_FORMAT({0}, '%Y-%m')","年月大于等于"),
        MONTH_GT("DATE_FORMAT(limit_date, '%Y-%m') > DATE_FORMAT({0}, '%Y-%m')","年月大于"),
        MONTH_LE("DATE_FORMAT(limit_date, '%Y-%m') <= DATE_FORMAT({0}, '%Y-%m')","年月小于等于"),
        MONTH_LT("DATE_FORMAT(limit_date, '%Y-%m') < DATE_FORMAT({0}, '%Y-%m')","年月小于"),
        DAY("DATE_FORMAT(limit_date, '%Y-%m-%d') = DATE_FORMAT({0}, '%Y-%m-%d')","年月日等于"),
        DAY_GE("DATE_FORMAT(limit_date, '%Y-%m-%d') >= DATE_FORMAT({0}, '%Y-%m-%d')","年月日大于等于"),
        DAY_GT("DATE_FORMAT(limit_date, '%Y-%m-%d') > DATE_FORMAT({0}, '%Y-%m-%d')","年月日大于"),
        DAY_LE("DATE_FORMAT(limit_date, '%Y-%m-%d') <= DATE_FORMAT({0}, '%Y-%m-%d')","年月日小于等于"),
        DAY_LT("DATE_FORMAT(limit_date, '%Y-%m-%d') < DATE_FORMAT({0}, '%Y-%m-%d')","年月日小于"),
        ;
        private final String value;
        private final String desc;

        MySqlDateFormatEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

}
