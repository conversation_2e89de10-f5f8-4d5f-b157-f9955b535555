package org.springblade.finance.limit.handler;

import org.springblade.finance.limit.dto.OrderDataDto;
import org.springblade.finance.limit.entity.FinancingLimit;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
public interface CreateFinancingLimitHandler {

    /**
     * 参数处理
     * @param orderDataDtoList
     * @return
     */
    List<FinancingLimit> prepareParams(List<OrderDataDto> orderDataDtoList);

    void success(List<FinancingLimit> financingLimitList);

    void fail(List<FinancingLimit> financingLimitList);

}
