package org.springblade.cloud.report.strategy;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.cloud.entity.CloudAssets;
import org.springblade.cloud.entity.CloudFinancing;
import org.springblade.cloud.entity.CloudPaymentList;
import org.springblade.cloud.mapper.CloudAssetsMapper;
import org.springblade.cloud.mapper.CloudFinancingMapper;
import org.springblade.cloud.mapper.CloudPaymentListMapper;
import org.springblade.cloud.service.ICloudFinancingService;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.CloudFinanceStatusEnum;
import org.springblade.common.enums.CloudPaymentEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.QualityProductsEnum;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tool.utils.Func;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.mapper.LoanManageOverdueMapper;
import org.springblade.loan.mapper.LoanManageRepaymentMapper;
import org.springblade.loan.mapper.LoanManageRepaymentPlanMapper;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.IRepaymentBizService;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.report.dto.CreditReportDto;
import org.springblade.report.dto.LoanReport;
import org.springblade.report.dto.RepaymentReport;
import org.springblade.report.handler.IProductLoanReportStrategy;
import org.springblade.report.vo.CreditPassVO;
import org.springblade.report.vo.CreditReport;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品信贷策略类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-16  13:43
 * @Description: 产品信贷策略类
 * @Version: 1.0
 */
@Service("CLOUD_CREDIT_PRODUCT_LOAN_REPORT_STRATEGY")
@RequiredArgsConstructor
public class CloudProductLoanReportStrategy implements IProductLoanReportStrategy {
    private final CloudFinancingMapper cloudFinancingMapper;
    private final ICloudFinancingService cloudFinancingService;
    private final CloudPaymentListMapper cloudPaymentListMapper;
    private final CloudAssetsMapper cloudAssetsMapper;
    private final ILoanManageIouService loanManageIouService;
    private final LoanManageIouMapper loanManageIouMapper;
    private final LoanManageRepaymentPlanMapper loanManageRepaymentPlanMapper;
    private final LoanManageOverdueMapper loanManageOverdueMapper;
    private final LoanManageRepaymentMapper loanManageRepaymentMapper;
    private final IRepaymentBizService repaymentBizService;


    @Override
    public GoodsEnum support() {
        return GoodsEnum.CLOUD_CREDIT;
    }

    @Override
    public CreditReport creditReport(String time, String tenantId) {
        // 新增放款笔数 时间点内的放款笔数汇总
        Integer loanCount = loanManageIouMapper.loanCount(time, GoodsEnum.CLOUD_CREDIT.getCode(), tenantId);
        //放款金额汇总 时间点内的放款金额汇总
        String val = loanManageIouMapper.amountSum(time, GoodsEnum.CLOUD_CREDIT.getCode(), tenantId);
        BigDecimal loanAmount = new BigDecimal(val == null ? "0" : val);
        //约定还款笔数
        Integer needPayCount = loanManageRepaymentPlanMapper.needPayCount(time, GoodsEnum.CLOUD_CREDIT.getCode(), tenantId);
        //未还款笔数=到了时间点应该还 但是还没还的
        Integer payableCount = loanManageRepaymentPlanMapper.payableCount(time, GoodsEnum.CLOUD_CREDIT.getCode(), tenantId);
        //已还款笔数=统计时间点有多少笔还款 如果统计的数据过大 应当换成sql进行统计 而不是拿出来计算
        List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentMapper.payLoanList(time, GoodsEnum.CLOUD_CREDIT.getCode(), tenantId);
        //实还利息
        BigDecimal payedInterest = BigDecimal.ZERO;
        //实还本金
        BigDecimal payedPrincipal = BigDecimal.ZERO;
        for (LoanManageRepayment loanManageRepayment : loanManageRepayments) {
            payedInterest = payedInterest.add(loanManageRepayment.getActualInterest());
            payedPrincipal = payedPrincipal.add(loanManageRepayment.getActualAmount());
        }
        Integer payedCount = loanManageRepayments.size();
        return CreditReport.builder()
                .queryTime(time)//时间
                .numberOfLoans(loanCount)//新增放款笔数
                .numberOfPay(payedCount)//已还笔数
                .numberOfUnPay(payableCount)//未还款笔数
                .loanAmount(loanAmount)//放款金额汇总
                .payInterest(payedInterest)//实还利息
                .payAmount(payedPrincipal)//实还金额
                .needPayCount(needPayCount)
                .overDueMoney(BigDecimal.ZERO)
                .tenantId(tenantId)
                .build();
    }

    /**
     * 拼接时间 -01 格式
     * 2022 -> 2022-01-01
     * 2022-08 -> 2022-08-01
     * 2022-08-06 -> 2022-08-06
     *
     * @param time
     * @return
     */
    private String getDateStr(String time) {
        String[] split = time.split("-");
        int length = split.length;
        Integer num = 3 - (split.length);
        String str = "";
        if (num > 0) {
            for (Integer integer = 0; integer < num; integer++) {
                str = str + "-01";
            }
        }
        String timeDate = time + str;
        return timeDate;
    }

    @Override
    public List<CreditPassVO> actualRepaymentReport(CreditReportDto dto) {
        List<CloudPaymentList> clouds = new ArrayList<>();
        List<CreditPassVO> cloudVos = new ArrayList<>();
        clouds = cloudPaymentListMapper.actualPayCloudList(dto);
        cloudVos = clouds.stream().map(cloud -> {
            CreditPassVO vo = new CreditPassVO();
            vo.setQueryTime(dto.getQueryTime());
            vo.setRepaymentNo(cloud.getRepaymentNumber());
            vo.setFinanceNo(cloud.getCloudCode());
            vo.setUserName(cloud.getRepaymentName());
            vo.setType("云信");
            vo.setPayableAmount(cloud.getAmountDue());
            vo.setActualPayable(cloud.getActualAmount());
            vo.setInterest(new BigDecimal("0"));
            vo.setOtherExpenses(new BigDecimal(0));
            vo.setRepaymentType(cloud.getType() == 1 ? "当期还款" : "逾期还款");
            Object nameByObject = CloudPaymentEnum.getNameByObject(CloudPaymentEnum.class, cloud.getStatus());
            vo.setStatus(Func.toStr(nameByObject));
            return vo;
        }).collect(Collectors.toList());
        return cloudVos;
    }

    @Override
    public List<CreditPassVO> agreedRepaymentReport(CreditReportDto dto) {
        //云信应还订单
        List<CloudFinancing> payableCloud = new ArrayList<>();
        List<CreditPassVO> cloudVO = new ArrayList<>();
        payableCloud = cloudFinancingMapper.payableCloudByDtoList(dto);
        cloudVO = payableCloud.stream().map(cloud -> {
            CloudAssets assets = cloudAssetsMapper.selectById(cloud.getAssetsId());
            CreditPassVO vo = new CreditPassVO();
            vo.setQueryTime(dto.getQueryTime());
            vo.setType("云信");
            vo.setFinanceNo(cloud.getFinancingNo());
            vo.setUserName(cloud.getCompanyName());
            vo.setPeriod("1");
            vo.setRepaymentTime(assets.getEndDate().toString());
            vo.setPayableAmount(cloud.getLoanMoney());
            String valueByKey = CloudFinanceStatusEnum.getValueByKey(cloud.getStatus());
            vo.setStatus(valueByKey);
            return vo;
        }).collect(Collectors.toList());
        return cloudVO;
    }

    @Override
    public CreditReport customerCreditReport(String time, Long userId, String tenantId) {
        CreditReport creditReport = creditReport(time, tenantId);
        //逾期金额
        String overDueAmountStr = loanManageOverdueMapper.overDueAmount(time, GoodsEnum.CLOUD_CREDIT.getCode(), userId, tenantId);
        BigDecimal overDueAmount = overDueAmountStr == null ? BigDecimal.ZERO : new BigDecimal(overDueAmountStr);
        creditReport.setOverDueMoney(overDueAmount);
        return creditReport;
    }

    @Override
    public List<CreditPassVO> creditNewCreditReport(CreditReportDto dto) {
        List<CloudFinancing> newCloudList = cloudFinancingMapper.newLoansList(dto);

        return newCloudList.stream().map(cloud -> {
            //CloudAssets cloudAssets = cloudAssetsMapper.selectById(cloud.getAssetsId());

            CreditPassVO vo = new CreditPassVO();
            vo.setQueryTime(dto.getQueryTime());
            vo.setType("云信");
            vo.setFinanceNo(cloud.getFinancingNo());
            vo.setUserId(cloud.getApplyUser());
            vo.setUserName(cloud.getCompanyName());
            vo.setGoodsName(cloud.getCloudProductName());
            //放款金额为空，暂用融资金额
            vo.setIouAmount(cloud.getLoanMoney());
            vo.setLoanTime(cloud.getLoanTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            vo.setStatus(CloudFinanceStatusEnum.getValueByKey(cloud.getStatus()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer creditNewCustomerReport(String dateStr, Long newId, String tenantId) {
        List<CloudFinancing> cloudFinancings = new ArrayList<>();
        cloudFinancings = cloudFinancingMapper.listCloudByLtDateStr(dateStr, newId, tenantId);
        return cloudFinancings.size();

    }

    @Override
    public CreditReport repaymentAnalysisReport(String time, Integer year, String tentId) {
        List<Integer> goodsTypeList = Arrays.asList(GoodsEnum.CLOUD_CREDIT.getCode());
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanMapper.payableByDtoList(time, goodsTypeList, tentId);
        List<RepaymentInfoDTO> repaymentInfoDTOS = repaymentBizService.getRepaymentInfoDTOS(LocalDate.now(), loanManageRepaymentPlans);
        BigDecimal payableAmount = BigDecimal.ZERO;
        BigDecimal payableInterest = BigDecimal.ZERO;
        BigDecimal payedAmount = BigDecimal.ZERO;
        BigDecimal payedInterest = BigDecimal.ZERO;
        BigDecimal overDueAmount = BigDecimal.ZERO;
        Integer overDueCount = 0;
        Integer normalCount = 0;
        for (RepaymentInfoDTO repaymentInfoDTO : repaymentInfoDTOS) {
            payableAmount = payableAmount.add(repaymentInfoDTO.getPrincipal());
            payableInterest = payableInterest.add(repaymentInfoDTO.getPlanInterest());
            if (RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode().equals(repaymentInfoDTO.getOverdue())) {
                overDueAmount = overDueAmount.add(repaymentInfoDTO.getSubPrincipal());
                overDueCount = overDueCount + 1;
            }
            if (RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode().equals(repaymentInfoDTO.getRepaymentStatus())) {
                normalCount = normalCount + 1;
            }
            List<LoanManageRepayment> loanManageRepayments = repaymentInfoDTO.getValidRepaymentList().stream().filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus())).collect(Collectors.toList());
            BigDecimal actualPrincipal = loanManageRepayments.stream().map(LoanManageRepayment::getActualPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal actualInterest = loanManageRepayments.stream().map(LoanManageRepayment::getActualInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
            payedAmount = payedAmount.add(actualPrincipal);
            payedInterest = payedInterest.add(actualInterest);
        }
        return CreditReport.builder()
                .queryTime(time)//时间
                .payableAmount(payableAmount)//应还本金
                .payableInterest(payableInterest)//应还利息
                .payInterest(payedInterest)//实还利息
                .payAmount(payedAmount)//实还本金
                .overDueMoney(overDueAmount)//逾期待还金额
                .repaymentNum(repaymentInfoDTOS.size())//还款计划数
                .normalNum(normalCount)//正常还款件数
                .overDueNum(overDueCount)//逾期件数
                .build();
    }

    @Override
    public List<LoanReport> getLoanReport() {

        List<CloudFinancing> cloudList = cloudFinancingService.list();
        //Integer.parseInt(DateUtil.format(cloud.getCreateTime(), DatePattern.SIMPLE_MONTH_PATTERN))

        return cloudList.stream().map(cloud -> {
            //获得当年当月第一天的时间戳
            long time = getLongTime(cloud.getCreateTime());
            LoanReport loanReport = LoanReport.builder()
                    .createTime(time)
                    .businessType(GoodsEnum.CLOUD_CREDIT.getName())
                    .loanAmount(cloud.getFinancingMoney()).build();
            return loanReport;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RepaymentReport> repaymentReportMethod() {
        List<CloudFinancing> list = cloudFinancingService.list(Wrappers.<CloudFinancing>lambdaQuery()
                .eq(BaseEntity::getStatus, CloudFinanceStatusEnum.DISBURSED.getKey()));

        return list.stream().map(cloud -> {
            Date createTime = cloud.getCreateTime();
            String format = DateUtil.format(createTime, DatePattern.SIMPLE_MONTH_PATTERN);
            cloud.setLoanVoucher(format);
            BigDecimal loanMoney = cloud.getFinancingMoney();
            RepaymentReport report = RepaymentReport.builder()
                    .createTime(Integer.valueOf(format))
                    .businessType(QualityProductsEnum.QUALITY_YUNXIN.getName())
                    .unpaidTotal(loanMoney)
                    .status(1)
                    .build();
            return report;
        }).collect(Collectors.toList());
    }

    /**
     * 获得当年当月第一天的时间戳
     *
     * @param time
     * @return
     */
    public long getLongTime(Date time) {
        Calendar call = Calendar.getInstance();
        call.setTime(time);
        call.set(Calendar.HOUR_OF_DAY, 0);
        call.set(Calendar.MINUTE, 0);
        call.set(Calendar.SECOND, 0);
        call.set(Calendar.DAY_OF_MONTH, 1);
        return call.getTime().getTime();
    }
}
