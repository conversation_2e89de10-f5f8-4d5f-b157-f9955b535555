package org.springblade.cloud.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jmreport.api.data.IDataSetFactory;
import org.jeecg.modules.jmreport.desreport.model.JmPage;
import org.springblade.cloud.report.service.ICloudReportService;
import org.springblade.cloud.service.ICloudAssetsService;
import org.springblade.common.enums.CreditReportEnum;
import org.springblade.core.tool.utils.Func;
import org.springblade.plugin.jimureport.config.JimuReportTokenService;
import org.springblade.plugin.jimureport.config.JimuUtils;
import org.springblade.report.vo.CreditReport;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 云信报表 积木
 *
 * <AUTHOR>
 */
@Component("jiMuCloudAssetsReportService")
@RequiredArgsConstructor
@Slf4j
public class JiMuCloudAssetsReportService implements IDataSetFactory {

    private final ICloudAssetsService cloudAssetsService;
    private final JimuReportTokenService jimuReportTokenService;
    private final ICloudReportService cloudReportService;


    @SneakyThrows
    @Override
    public JmPage createPageData(Map<String, Object> parameters) {
        CreditReport creditReport = new CreditReport();
        Integer type = Func.toInt(parameters.get(CreditReportEnum.TYPE.getName()));
        String month = Func.toStr(parameters.get(CreditReportEnum.MONTH.getName()));
        Integer year = Func.toInt(parameters.get(CreditReportEnum.YEAR.getName()));
        int pageNo = Integer.parseInt(parameters.get("pageNo").toString());
        int pageSize = Integer.parseInt(parameters.get("pageSize").toString());
        //获取租户信息
        String token = jimuReportTokenService.getToken();
        Map<String, Object> userInfo = jimuReportTokenService.getUserInfo(token);
        User user = (User) userInfo.get("user");
        UserUtils.saveToRequest(user);
        String tentId = user.getTenantId();
        List<CreditReport> reports = cloudReportService.cloudAssetsReport(type, year, month, tentId);
        CollUtil.reverse(reports);
        List<Map<String, Object>> creditReportMap = creditReport.getCreditReportMap(reports);
        JmPage jmPage = JimuUtils.getJmPage(creditReportMap, pageNo, pageSize);
        return jmPage;
    }

    @Override
    public List<Map<String, Object>> createData(Map<String, Object> map) {
        return null;
    }


}
