/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.cloud.entity.CloudFinancing;
import org.springblade.cloud.service.ICloudFinancingService;
import org.springblade.cloud.vo.CloudFinancingDetailVO;
import org.springblade.cloud.vo.CloudFinancingVO;
import org.springblade.cloud.vo.CloudLoanDetailVO;
import org.springblade.cloud.wrapper.CloudFinancingWrapper;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

/**
 * 云信融资表 控制器
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_CLOUD + CommonConstant.WEB_BACK + "/cloud/cloudFinancing")
@Api(value = "云信融资表", tags = "云信融资表接口")
public class CloudFinancingBackController extends BladeController {

	private final ICloudFinancingService cloudFinancingService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cloudFinancing")
	public R<CloudFinancingDetailVO> detail(@ApiParam(value = "融资id", required = true) @RequestParam Long id) {
		return R.data(cloudFinancingService.financingExamineDetail(id));
	}

	/**
	 * 分页 云信融资表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cloudFinancing")
	public R<IPage<CloudFinancingVO>> list(CloudFinancing cloudFinancing, Query query) {
		IPage<CloudFinancing> pages = cloudFinancingService
			.page(Condition.getPage(query),Condition.getQueryWrapper(cloudFinancing)
				.lambda().orderByDesc(CloudFinancing::getCreateTime));
		return R.data(CloudFinancingWrapper.build().pageVO(pages));
	}





	/**
	 * 删除 云信融资表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('cloud:cloudFinancing:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cloudFinancingService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 放款申请详细
	 */
	@GetMapping("/cloudLoanApplyDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cloudFinancing")
	public R<CloudLoanDetailVO> cloudLoanApplyDetail(@ApiParam(value = "融资id", required = true) @RequestParam Long id) {
		return R.data(cloudFinancingService.cloudLoanApplyDetail(id));
	}

	/**
	 * 放款申请发起
	 */
	@PostMapping("/cloudLoanApply")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "放款申请发起", notes = "传入ids")
	public R<Boolean> cloudLoanApply(@ApiParam(value = "融资id", required = true) @RequestParam Long id) {
		return R.status(cloudFinancingService.cloudLoanApply(id));
	}

	@GetMapping("/getPage")
	@ApiOperation("获取融资记录")
	public  R<IPage<CloudFinancingVO>> getPage(Long userId, Integer type, Query query){
		IPage<CloudFinancing> page = cloudFinancingService.page(Condition.getPage(query), Wrappers.<CloudFinancing>lambdaQuery().eq(CloudFinancing::getCreateUser, userId).orderByDesc(CloudFinancing::getCreateTime));
		Page<CloudFinancingVO> cloudFinancingVOPage = CloudFinancingWrapper.build().pageVO(page);
		for (CloudFinancingVO record : cloudFinancingVOPage.getRecords()) {
			record.setGoodsType(type);
		}
		return R.data(cloudFinancingVOPage);
	}
}
