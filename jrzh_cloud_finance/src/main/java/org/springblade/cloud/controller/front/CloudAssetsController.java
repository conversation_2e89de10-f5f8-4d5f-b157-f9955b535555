/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.controller.front;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.block.entity.BlockChainInformation;
import org.springblade.block.service.IBlockChainInformationService;
import org.springblade.cloud.constant.CloudConstant;
import org.springblade.cloud.dto.CloudAssetsDTO;
import org.springblade.cloud.dto.CloudAssetsFinancingDTO;
import org.springblade.cloud.entity.*;
import org.springblade.cloud.service.*;
import org.springblade.cloud.vo.CloudAssetsVO;
import org.springblade.cloud.vo.CloudTrackRecordVO;
import org.springblade.cloud.wrapper.CloudAssetsWrapper;
import org.springblade.cloud.wrapper.CloudTrackRecordWrapper;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.feign.RemoteCustomerGoodsService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.BankCardVO;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 云信资产表 控制器
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_CLOUD + CommonConstant.WEB_FRONT + "/cloud/cloudAssets")
@Api(value = "云信资产表", tags = "云信资产表接口")
public class CloudAssetsController extends BladeController {

    private final ICloudAssetsService cloudAssetsService;

    /**
     * 云信付款明细
     */
    private final ICloudPaymentDetailService cloudPaymentDetailService;

    /**
     * 云信资产编号关联
     */
    private final ICloudTrackCodeService cloudTrackCodeService;

    /**
     * 云信轨迹图
     */
    private final ICloudTrackRecordService cloudTrackRecordService;
    private final ICloudPayService cloudPayService;

    private final RemoteCustomerGoodsService customerGoodsService;

    private final BladeRedis bladeRedis;

    /**
     * 区块链
     */
    private final IBlockChainInformationService blockChainInformationService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入cloudAssets")
    public R<CloudAssetsVO> detail(CloudAssets cloudAssets) {
        CloudAssets detail = cloudAssetsService.getOne(Condition.getQueryWrapper(cloudAssets));
        CloudAssetsVO cloudAssetsVO = CloudAssetsWrapper.build().entityVO(detail);
        if (ObjectUtil.isNotEmpty(detail)) {
            if (detail.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_SEVEN || detail.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_NINE) {
                CloudPaymentDetail cloudPaymentDetail = cloudPaymentDetailService.getOne(Wrappers.<CloudPaymentDetail>lambdaQuery().eq(CloudPaymentDetail::getCloudCode, cloudAssetsVO.getCloudCode()));
                cloudAssetsVO.setCollectionTotal(cloudPaymentDetail.getAmount());
            }
        }
        return R.data(cloudAssetsVO);
    }

    /**
     * 云信资产签收
     */
    @GetMapping("/updateCloudAssets")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入cloudAssets")
    @PreAuth("hasPermission('cloud:cloudAssets:cloud:cloudAssets:updateCloudAssets') or hasRole('admin') or hasRole('financing_admin')")
    public R<Boolean> updateCloudAssets(@ApiParam(value = "主键id", required = true) @RequestParam Long id, @ApiParam(value = "类型", required = true) @RequestParam Integer type, String denialReason, BankCardVO bankCardVO) {
        return R.status(cloudAssetsService.updateCloudAssetsConfirm(id, type, denialReason, bankCardVO));
    }

    /**
     * 根据云信编号获取核心企业云信编号
     */
    @GetMapping("/getCloudCoreCode")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据云信编号获取核心企业云信编号", notes = "传入cloudCode")
//	@PreAuth("hasPermission('cloud:cloudAssets:cloud:cloudAssets:updateCloudAssets') or hasRole('admin') or hasRole('financing_admin')")
    public R<String> updateCloudAssets(@RequestParam String cloudCode) {
        CloudTrackCode cloudTrackCode = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudCode));
        if (ObjectUtil.isNotEmpty(cloudTrackCode)) {
            cloudCode = cloudTrackCode.getCloudCoreCode();
        }
        return R.data(cloudCode);
    }

    /**
     * 分页 云信资产表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入cloudAssets")
    @PreAuth("hasPermission('cloud:cloudAssets:list') or hasRole('admin') or hasRole('financing_admin')")
    public R<IPage<CloudAssetsVO>> list(CloudAssets cloudAssets, Query query) {
        cloudAssets.setUserId(AuthUtil.getUserId());
        //TODO 暂时写转让的类型数据
        cloudAssets.setType(0);
//		IPage<CloudAssets> pages = cloudAssetsService.page(Condition.getPage(query), Condition.getQueryWrapper(cloudAssets).lambda().orderByDesc(CloudAssets::getCreateTime));
//		return R.data(CloudAssetsWrapper.build().pageVO(pages));
        IPage<CloudAssets> page = cloudAssetsService.page(Condition.getPage(query), Condition.getQueryWrapper(cloudAssets).lambda().orderByDesc(CloudAssets::getCreateTime));
        Page<CloudAssetsVO> pageVO = CloudAssetsWrapper.build().pageVO(page);
        if (page.getTotal() <= 0) {
            return R.data(pageVO);
        }
        List<CloudAssetsVO> records = pageVO.getRecords();
        List<String> cloudNo = records.stream().map(CloudAssets::getCloudCode).collect(Collectors.toList());
        List<CloudPay> list = cloudPayService.list(Wrappers.<CloudPay>lambdaQuery().in(CloudPay::getCloudCode, cloudNo));
        Map<String, CloudPay> cloudPayMap = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            cloudPayMap = list.stream().collect(Collectors.toMap(CloudPay::getCloudCode, e -> e));
        }
        for (CloudAssetsVO record : records) {
            //确定业务类型 若编号在兑付表中 则说明属于开单
            record.setBusinessType(cloudPayMap.containsKey(record.getCloudCode()) ? 1 : 2);
            CustomerGoods customerGoods = customerGoodsService.getCustomerGoodsByEnterpriseQuotaId(record.getEnterpriseId()).getData();
            if (customerGoods != null) {
                Long capitalId = customerGoods.getCapitalId();
                Dept dept = SpringUtil.getBean(RemoteDeptSearchService.class).getDeptById(capitalId).getData();
                if (dept != null) {
                    record.setCapitalName(dept.getDeptName() == null ? "" : dept.getDeptName());
                }
            }
        }

        pageVO.setRecords(records);
        return R.data(pageVO);
    }

    /**
     * 根据云信转让redis编号获取 云信转让 redis数据
     */
    @GetMapping("/cloud-redis-cloudCode")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "根据云信转让redis编号获取 云信转让 redis数据", notes = "传入cloudRedisCode")
//   @PreAuth( "hasPermission('cloud:cloudPay:detail') or hasRole('administrator')")
    public R<CloudAssetsDTO> cloudRedisCloudData(@RequestParam String cloudRedisCode) {
        CloudAssetsDTO cloudAssetsDTO = (CloudAssetsDTO) bladeRedis.get(cloudRedisCode);
        return R.data(cloudAssetsDTO);
    }

    /**
     * 转让 ---下一步
     */
    @PostMapping("/saveNextTransfer")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "转让 ---下一步", notes = "传入cloudAssets")
    public R<CloudAssetsDTO> saveNextTransfer(@RequestBody CloudAssetsDTO cloudAssets) {
        return R.data(cloudAssetsService.saveNextTransfer(cloudAssets));
    }

    /**
     * 转让 --- 提交
     */
    @PostMapping("/submitTransfer")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "转让 ---下一步", notes = "传入cloudAssets")
    public R<Boolean> submitTransfer(@RequestBody CloudAssetsDTO cloudAssets) {
        return R.status(cloudAssetsService.submitTransfer(cloudAssets));
    }

    /**
     * 资产--父级拆分列表
     */
    @GetMapping("/parent-split-list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "资产--父级拆分列表", notes = "传入cloudCode")
    public R<List<CloudAssetsVO>> parentSplitList(@ApiParam(value = "云信编号", required = true) @RequestParam String cloudCode) {

        List<Long> cloudTrackCodeIds = cloudTrackCodeService.list(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudParentCode, cloudCode)).parallelStream().map(CloudTrackCode::getAssetsId).collect(Collectors.toList());

        List<CloudAssets> cloudAssetsList = new ArrayList<>();
        List<CloudAssetsVO> cloudAssetsVOList = new ArrayList<>();

        if (CollUtil.isNotEmpty(cloudTrackCodeIds)) {
            cloudAssetsList = cloudAssetsService.listByIds(cloudTrackCodeIds);
             cloudAssetsVOList = CloudAssetsWrapper.build().listVO(cloudAssetsList);
            for (CloudAssetsVO cloudAssetsVO : cloudAssetsVOList) {
                if(cloudAssetsVO.getStatus()== CloudConstant.CLOUD_ASSETS_STATUS_TWO){
                    cloudAssetsVO.setParentCloudNo(cloudAssetsVO.getCloudCode());
                    continue;
                }
                cloudAssetsVO.setParentCloudNo(cloudCode);
            }
        }
        return R.data(cloudAssetsVOList);
    }

    /**
     * 资产--该企业所有的拆分列表
     */
    @GetMapping("/company-split-list")
    //@PreAuth("hasPermission('cloud:cloudAssets:company-split-list') or hasRole('admin') or hasRole('financing_admin')")
    public R<List<CloudAssetsVO>> companySplitList(String cloudCode, String name, String resolutionStatus) {
        LambdaQueryWrapper<CloudAssets> cloudAssetsLambdaQueryWrapper = Wrappers.<CloudAssets>lambdaQuery();

//        cloudAssetsLambdaQueryWrapper.eq(CloudAssets::getCompanyId,
//                AuthUtil.getClaimsParam(WebUtil.getRequest(), "customerId"));
        cloudAssetsLambdaQueryWrapper.eq(CloudAssets::getCompanyId,
                MyAuthUtil.getCompanyId());
        if (StrUtil.isNotBlank(cloudCode)) {
            cloudAssetsLambdaQueryWrapper.eq(CloudAssets::getCloudCode, cloudCode);
        }

        if (StrUtil.isNotBlank(name)) {
            cloudAssetsLambdaQueryWrapper.like(CloudAssets::getName, name);
        }

        if (StrUtil.isNotBlank(resolutionStatus)) {
            cloudAssetsLambdaQueryWrapper.in(CloudAssets::getResolutionStatus, Func.toIntList(resolutionStatus));
        }
//		if (type!=null){
//			cloudAssetsLambdaQueryWrapper.in(CloudAssets::getType, type);
//		}

        List<CloudAssets> cloudAssetsList = cloudAssetsService.list(cloudAssetsLambdaQueryWrapper.orderByDesc(CloudAssets::getCreateTime)).stream().filter(cloudAssetsOne -> !cloudAssetsOne.getCompanyId().equals(cloudAssetsOne.getUserId())).collect(Collectors.toList());
        List<CloudAssetsVO> cloudAssetsVOList = CloudAssetsWrapper.build().listVO(cloudAssetsList);
        List<CloudAssetsVO> cloudAssetsVOListNow= cloudAssetsService.selectTransferBeforeCloudCore(cloudAssetsVOList);
        return R.data(cloudAssetsVOListNow);
    }

    /**
     * 融资端--轨迹图
     */
    @GetMapping("/track-child")
    @ApiOperation(value = "融资端--轨迹图", notes = "传入cloudCode")
    public R<Map<String, List<CloudTrackRecordVO>>> trackChild(@ApiParam(value = "云信编号", required = true) @RequestParam String cloudCode) {
        if (StrUtil.isBlank(cloudCode)) {
            return R.fail("云信编号不能为空");
        }
        Map<String, List<CloudTrackRecordVO>> map = new HashMap<>(16);
        CloudTrackCode cloudTrackCode = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudCode));
        List<CloudTrackRecordVO> cloudTrackRecordList;
        if (ObjectUtil.isEmpty(cloudTrackCode)) {
            cloudTrackRecordList = CloudTrackRecordWrapper.build().listVO(cloudTrackRecordService.list(Wrappers.<CloudTrackRecord>lambdaQuery().eq(CloudTrackRecord::getCloudCode, cloudCode)).stream().filter(cloudTrackRecord -> StrUtil.isBlank(cloudTrackRecord.getNextCloudCode())).collect(Collectors.toList()));
        } else {
            //根据云信编号查询查询 下一级云信编号得出轨迹图，然后再查询父级云信编号，得出所有的子级云信轨迹图
            cloudTrackRecordList = CloudTrackRecordWrapper.build().listVO(cloudTrackRecordService.list(Wrappers.<CloudTrackRecord>lambdaQuery().eq(CloudTrackRecord::getNextCloudCode, cloudCode)));
        }

        if (CollUtil.isEmpty(cloudTrackRecordList)) {
            return R.fail("查询不到云信轨迹图！");
        }
        CloudTrackRecord cloudTrackRecord = cloudTrackRecordList.get(0);
        if (StrUtil.isBlank(cloudTrackRecord.getNextCloudCode())) {
            BlockChainInformation blockChainInformation = blockChainInformationService.getOne(Wrappers.<BlockChainInformation>lambdaQuery().eq(BlockChainInformation::getCloudCode, cloudTrackRecord.getCloudCode()).isNull(BlockChainInformation::getNextCloudCode));
            if (ObjectUtil.isNotEmpty(blockChainInformation)) {
                cloudTrackRecordList.get(0).setTransactionAddress(blockChainInformation.getTransactionAddress());
            }
        } else {
            BlockChainInformation blockChainInformation = blockChainInformationService.getOne(Wrappers.<BlockChainInformation>lambdaQuery().eq(BlockChainInformation::getNextCloudCode, cloudTrackRecord.getNextCloudCode()));
            if (ObjectUtil.isNotEmpty(blockChainInformation)) {
                cloudTrackRecordList.get(0).setTransactionAddress(blockChainInformation.getTransactionAddress());
            }
        }
        List<String> cloudTrackList = cloudTrackCodeService.list(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudParentCode, cloudCode)).parallelStream().map(CloudTrackCode::getCloudCode).collect(Collectors.toList());

        List<CloudTrackRecord> cloudTrackRecordChildList = cloudTrackRecordService.list(Wrappers.<CloudTrackRecord>lambdaQuery().in(CloudTrackRecord::getNextCloudCode, cloudTrackList));

        map.put("trackRecord", cloudTrackRecordList);
        if (CollUtil.isNotEmpty(cloudTrackRecordChildList)) {
            //子级云信轨迹图
            List<CloudTrackRecordVO> cloudTrackRecordVOList = CloudTrackRecordWrapper.build().listVO(cloudTrackRecordChildList);
            List<BlockChainInformation> list = blockChainInformationService.list(Wrappers.<BlockChainInformation>lambdaQuery().in(BlockChainInformation::getNextCloudCode, cloudTrackList));
            if (CollUtil.isNotEmpty(list)) {
                Map<String, String> blockChainInformationMap = list.parallelStream().collect(Collectors.toMap(BlockChainInformation::getNextCloudCode, BlockChainInformation::getTransactionAddress));
                for (CloudTrackRecordVO trackRecord : cloudTrackRecordVOList) {
                    if (blockChainInformationMap.containsKey(trackRecord.getNextCloudCode())) {
                        trackRecord.setTransactionAddress(blockChainInformationMap.get(trackRecord.getNextCloudCode()));
                    }
                }
            }
            map.put("trackRecordChild", cloudTrackRecordVOList);
        }
        return R.data(map);
    }

    /**
     * 融资申请--云信 发起
     */
    @PostMapping("/financing-cloud-submit")
    @ApiOperation(value = "融资申请--云信 发起", notes = "传入cloudAssetsFinancingDTO")
    public R<String> financingCloudSubmit(@Valid @RequestBody CloudAssetsFinancingDTO cloudAssetsFinancingDTO) {
        return R.data(cloudAssetsService.financingCloud(cloudAssetsFinancingDTO));
    }

}
