/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.controller.back;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.cloud.entity.CloudPaymentList;
import org.springblade.cloud.service.ICloudPaymentListService;
import org.springblade.cloud.vo.CloudPaymentListVO;
import org.springblade.cloud.wrapper.CloudPaymentListWrapper;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.utils.UserUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 云信付款列表 控制器
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_CLOUD + CommonConstant.WEB_BACK + "/cloud/cloudPaymentList")
@Api(value = "云信付款列表", tags = "云信付款列表接口")
public class CloudPaymentListController extends BladeController {

	private final ICloudPaymentListService cloudPaymentListService;

	private final IAttachService attachService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cloudPaymentList")
	//@PreAuth("hasPermission('paymentList:cloudPaymentList:detail') or hasRole('administrator')")
	public R<CloudPaymentListVO> detail(CloudPaymentList cloudPaymentList) {
		CloudPaymentList detail = cloudPaymentListService.getOne(Condition.getQueryWrapper(cloudPaymentList));
		CloudPaymentListVO cloudPaymentListVO = CloudPaymentListWrapper.build().entityVO(detail);
		if (StrUtil.isNotBlank(cloudPaymentListVO.getCloudAttachId())) {
			Attach attach = attachService.getById(cloudPaymentListVO.getCloudAttachId());
			cloudPaymentListVO.setAttach(attach);
		}
		return R.data(cloudPaymentListVO);
	}


	/**
	 * 根据id查询云信还款记录支付状态
	 * 1、已支付则 返回支付成功后的账户数据和付款金额
	 * 2、支付失败状态则返回 失败原因
	 * 3、除了支付中状态 其他状态则返回支付失败，没有原因，或者默认值。
	 */
	@GetMapping("/cloud-status-detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询云信还款记录支付状态", notes = "传入id")
	//@PreAuth("hasPermission('paymentList:cloudPaymentList:detail') or hasRole('administrator')")
	public R<CloudPaymentListVO> cloudStatusDetail(@ApiParam(value = "id", required = true) @RequestParam String id) {

		return R.data(cloudPaymentListService.cloudStatusDetail(id));
	}

	/**
	 * 分页 云信付款列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cloudPaymentList")
	//@PreAuth("hasPermission('paymentList:cloudPaymentList:list') or hasRole('administrator')")
	public R<IPage<CloudPaymentListVO>> list(CloudPaymentList cloudPaymentList, Query query) {
		IPage<CloudPaymentList> pages = cloudPaymentListService.page(Condition.getPage(query), Condition.getQueryWrapper(cloudPaymentList).lambda().orderByDesc(CloudPaymentList::getCreateTime));
		Page<CloudPaymentListVO> cloudPaymentListVOPage = CloudPaymentListWrapper.build().pageVO(pages);
		for (CloudPaymentListVO record : cloudPaymentListVOPage.getRecords()) {
			if (null != record.getOperateId()) {
				record.setOperateName(UserUtils.getUserById(record.getOperateId()).getName());
			}
		}
		return R.data(cloudPaymentListVOPage);
	}


	/**
	 * 新增或修改 云信付款列表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入cloudPaymentList")
	//@PreAuth("hasPermission('paymentList:cloudPaymentList:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody CloudPaymentList cloudPaymentList) {
		return R.status(cloudPaymentListService.submitCloudPayment(cloudPaymentList));
	}


}
