package org.springblade.cloud.handler.job;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.handler.IProductFinanceJobStrategy;
import org.springblade.finance.service.IFinanceApplyService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-18  14:31
 * @Description: TODO
 * @Version: 1.0
 */
@Service("CLOUD_CREDIT_FINANCE_PRODUCT_JOB_STRATEGY")
@RequiredArgsConstructor
public class CloudFinanceProductJobStrategy implements IProductFinanceJobStrategy {
    private final IFinanceApplyService financeApplyService;

    @Override
    public void overdueFinanceDeal(FinanceApply financeApply, LocalDate repaymentTime) {
        financeApplyService.changeStatus(Collections.singletonList(financeApply.getId()), FinanceApplyStatusEnum.OVERDUE_UN_SETTLED.getCode());
    }

    @Override
    public GoodsEnum support() {
        return GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE;
    }
}
