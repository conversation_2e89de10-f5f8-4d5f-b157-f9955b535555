package org.springblade.cloud.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springblade.cloud.dto.CloudFinancingDTO;
import org.springblade.cloud.entity.CloudFinancing;
import org.springblade.cloud.vo.CloudFinancingVO;

import java.util.List;

/**
 * 云信融资表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface CloudFinancingWrapper  {
    /**
      * 获取mapper对象
      * @return
      */
	static CloudFinancingWrapper build() {
		return Mappers.getMapper(CloudFinancingWrapper.class);
 	}

    /**
	 * 实体类转vo
	 * @param cloudFinancing
	 * @return
	 */
	CloudFinancingVO entityVO(CloudFinancing cloudFinancing);
    /**
	 * 实体类转dto
	 * @param cloudFinancing
	 * @return
	 */
    CloudFinancingDTO entityDTO(CloudFinancing cloudFinancing);
    /**
	 * 实体类List转VOList
	 * @param cloudFinancings
	 * @return
	 */
    List<CloudFinancingVO> listVO(List<CloudFinancing> cloudFinancings);
    /**
	 * 实体类Page转VOPage
	 * @param page
	 * @return
	 */
    default Page<CloudFinancingVO> pageVO(IPage<CloudFinancing> page) {
        List<CloudFinancingVO> cloudFinancingVOList = this.listVO(page.getRecords());
        Page<CloudFinancingVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(cloudFinancingVOList);
        pageVO.setPages(page.getPages());
        return pageVO;
    }
}
