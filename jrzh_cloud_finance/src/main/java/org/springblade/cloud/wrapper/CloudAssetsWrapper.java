package org.springblade.cloud.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springblade.cloud.entity.CloudAssets;
import org.springblade.cloud.vo.CloudAssetsVO;

import java.util.List;

/**
 * 云信资产表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface CloudAssetsWrapper  {
    /**
      * 获取mapper对象
      * @return
      */
	static CloudAssetsWrapper build() {
		return Mappers.getMapper(CloudAssetsWrapper.class);
 	}

    /**
	 * 实体类转vo
	 * @param cloudAssets
	 * @return
	 */
	CloudAssetsVO entityVO(CloudAssets cloudAssets);
//    /**
//	 * 实体类转dto
//	 * @param cloudAssets
//	 * @return
//	 */
//    CloudAssetsDTO entityDTO(CloudAssets cloudAssets);
    /**
	 * 实体类List转VOList
	 * @param cloudAssetss
	 * @return
	 */
    List<CloudAssetsVO> listVO(List<CloudAssets> cloudAssetss);
    /**
	 * 实体类Page转VOPage
	 * @param page
	 * @return
	 */
    default Page<CloudAssetsVO> pageVO(IPage<CloudAssets> page) {
        List<CloudAssetsVO> cloudAssetsVOList = this.listVO(page.getRecords());
        Page<CloudAssetsVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(cloudAssetsVOList);
        pageVO.setPages(page.getPages());
        return pageVO;
    }
}
