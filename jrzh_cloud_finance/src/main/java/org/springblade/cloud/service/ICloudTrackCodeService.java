/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.cloud.entity.CloudTrackCode;
import org.springblade.cloud.vo.CloudTrackCodeVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 云信资产编号关联表 服务类
 *
 * <AUTHOR>
 * @since 2022-05-21
 */
public interface ICloudTrackCodeService extends BaseService<CloudTrackCode> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cloudTrackCode
	 * @return
	 */
	IPage<CloudTrackCodeVO> selectCloudTrackCodePage(IPage<CloudTrackCodeVO> page, CloudTrackCodeVO cloudTrackCode);

	/**
	 * 根据核心企业云信编号 获取云信资产编号关联集合
	 * @param cloudCoreCode 核心企业云信编号
	 * @return 云信资产编号关联集合
	 */
	List<CloudTrackCode> selectCloudTrackCodeList(String cloudCoreCode);
}
