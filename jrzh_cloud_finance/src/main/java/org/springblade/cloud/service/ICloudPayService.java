/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.cloud.entity.CloudPay;
import org.springblade.cloud.entity.CloudPaymentDetail;
import org.springblade.cloud.vo.CloudPayVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

import java.util.List;
import java.util.Map;

/**
 * 云信兑付表 服务类
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
public interface ICloudPayService extends BaseService<CloudPay> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cloudPay
	 * @return
	 */
	IPage<CloudPayVO> selectCloudPayPage(IPage<CloudPayVO> page, CloudPayVO cloudPay);

	/**
	 * 云信 新增或者修改 开单信息
	 * @param cloudPay
	 * @return 云信编号
	 */
	Map<String,Object> addCloudPayBill(CloudPay cloudPay);
	/**
	 * 云信 提交信息至 云信开单流程
	 * @param cloudCode 云信编号
	 * @param companyName 收单企业名称
	 * @return
	 */
	boolean addCloudPayWorkflow(String cloudCode,String companyName);

	/**
	 * 修改状态
	 * @param processInstanceId
	 * @param id
	 * @param status
	 * @return
	 */
	 boolean updateCloudProcess(String processInstanceId, Long id, int status);

	/**
	 * 云信兑付---付款列表
	 * @param cloudCoreCode 云信编号
	 * @return
	 */
	IPage<CloudPaymentDetail> cloudPaymentDetailIPage (String cloudCoreCode, Query query, LambdaQueryWrapper<CloudPaymentDetail> cloudPaymentDetailLambdaQueryWrapper);


	/**
	 * 云信兑付---付款提交
	 * @param cloudCoreCode 核心企业云信编号
	 * @return
	 */
	Long paymentSubmit(String cloudCoreCode);

	/**
	 * 获取相关兑付列表
	 * @param heightId 上游id
	 * @param lowerId 下游id
	 * @return
	 */
	List<CloudPay> listByHeightIdAndLowerId(Long heightId, Long lowerId);

	/**
	 * 详情复核
	 * @param id 云信兑付id
	 * @param type 1、同意开单 2、拒绝开单
	 * @param reason 拒绝开单时写入，拒绝原因
	 * @return
	 */
	Boolean detailReview(Long id,Integer type,String reason);
}
