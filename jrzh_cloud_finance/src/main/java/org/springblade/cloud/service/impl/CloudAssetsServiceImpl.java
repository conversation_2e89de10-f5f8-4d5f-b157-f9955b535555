/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springblade.block.blockchain.cloud.BlockChainCloudApply;
import org.springblade.block.blockchain.cloud.BlockChainCloudReceive;
import org.springblade.block.entity.BlockChainInformation;
import org.springblade.block.service.IBlockChainInformationService;
import org.springblade.cloud.constant.CloudConstant;
import org.springblade.cloud.dto.CloudAssetsDTO;
import org.springblade.cloud.dto.CloudAssetsFinancingDTO;
import org.springblade.cloud.dto.CloudFinancingDTO;
import org.springblade.cloud.entity.*;
import org.springblade.cloud.enums.CloudMessageEnum;
import org.springblade.cloud.mapper.CloudAssetsMapper;
import org.springblade.cloud.service.*;
import org.springblade.cloud.vo.CloudAssetsVO;
import org.springblade.cloud.vo.CloudPaymentDetailVO;
import org.springblade.cloud.wrapper.CloudAssetsWrapper;
import org.springblade.cloud.wrapper.CloudPaymentDetailWrapper;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.blockchain.BlockChainEnum;
import org.springblade.common.enums.cloud.CloudResolutionStatusEnum;
import org.springblade.common.enums.rabbitmq.RabbitMqDayEnum;
import org.springblade.common.enums.rabbitmq.RabbitMqStatusEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.common.utils.rabbitmqutils.RabbitMqCommonUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.feign.RemoteEnterpriseQuotaService;
import org.springblade.customer.feign.RemoteQuotaUseDetailsService;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.BankCardVO;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanDTO;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.loan.service.impl.RepaymentBizImplService;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.product.common.entity.BillBankCarda;
import org.springblade.product.expense_relation.service.IBillBankCardaRelationService;
import org.springblade.resource.cache.ParamCache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 云信资产表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@Service
@RequiredArgsConstructor
public class CloudAssetsServiceImpl extends BaseServiceImpl<CloudAssetsMapper, CloudAssets> implements ICloudAssetsService {
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final RepaymentBizImplService repaymentBizImplService;
    private final ILoanManageIouService loanManageIouService;
    /**
     * 云信额度
     */
    private final RemoteEnterpriseQuotaService enterpriseQuotaService;

    /**
     * 云信资产编号关联
     */
    private final ICloudTrackCodeService cloudTrackCodeService;

    /**
     * 云信轨迹图
     */
    private final ICloudTrackRecordService cloudTrackRecordService;

    /**
     * 云信开单日志记录
     */
    private final ICloudPayLogsService cloudPayLogsService;

    /**
     * 云信付款明细
     */
    private final ICloudPaymentDetailService cloudPaymentDetailService;

    /**
     * 云信合同服务
     */
    private final ICloudContractService cloudContractService;

    private final ICloudPayService cloudPayService;
    private final BladeRedis bladeRedis;
    private final RemoteQuotaUseDetailsService quotaUseDetailsService;
    private final IBillBankCardaRelationService billBankCardaRelationService;
    private final ICustomerGoodsService customerGoodsService;


    /**
     * mq
     */
    private final RabbitMsgSender rabbitMsgSender;


    /**
     * 云信付款列表
     */
    private final ICloudPaymentListService cloudPaymentListService;

    /**
     * 区块链
     */
    private final IBlockChainInformationService blockChainInformationService;
    private final ICloudFinancingCoreEnterprisePaymentService cloudFinancingCoreEnterprisePaymentService;

    @Override
    public IPage<CloudAssetsVO> selectCloudAssetsPage(IPage<CloudAssetsVO> page, CloudAssetsVO cloudAssets) {
        return page.setRecords(baseMapper.selectCloudAssetsPage(page, cloudAssets));
    }

    /**
     * 签收 或者拒绝
     * 1、根据云信编号查询 资产关联表看是否存在信息，如果存在信息则表示该信息至少完成一次转让
     * 没有信息则代表是一级供应商，那么就需要扣除云信额度表中，申请中的额度和 新增已使用额度 加锁。
     * 2、如果是签收状态 云信付款明细和云信轨迹图新增记录，同时当前资产信息状态修改为持单中，资产关联表信息不存在，则云信轨迹图 核心企业云信编号和云信编号字段
     * 值一致，主要用来后续 云信轨迹图能更好的展示
     * 资产关联表存在信息：云信轨迹图 下一个云信编号字段 需要填写信息（当前资产信息的云信编号） 核心企业云信编号和云信编号都从该关联表中获取
     * 云信 轨迹图展示需要使用到递归，关联表 核心企业云信编号和云信编号一致则代表一级供应商，查询父级id则根据 云信编号查询到id，赋予给父级id（parent_id）
     * 核心企业云信编号和云信编号不一致则不是一级供应商 查询父级id则需要根据轨迹图的下级云信编号字段来进行查询
     * 3、拆分后判断是否为最后一个待签收的资产信息，如果是则需要新增 资产信息、云信付款明细、云信轨迹图
     * 4、拒绝签收后，云信金额返回给 父级资产信息，如果拆分的资产信息全部都拒绝签收，则  父级资产信息状态修改为持单中、如果本次操作是最后一个拆分信息则 父级资产状态修改为拆分已完成
     *
     * @param id           云信id
     * @param type         类型 1、签收 2、拒绝
     * @param denialReason 签收拒绝原因 拒绝时有值
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCloudAssetsConfirm(Long id, Integer type, String denialReason, BankCardVO bankCardVO) {

        CloudAssets cloudAssets = this.getById(id);
        if (cloudAssets.getStatus() != CloudConstant.CLOUD_ASSETS_STATUS_ZERO) {
            throw new ServiceException("资产状态必须为待签收");
        }
        //查询资产关联表，如果根据云信编号没有查询到值，说明第一次进行签收，该云信编号就是核心企业云信编号
        CloudTrackCode cloudTrackCode = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudAssets.getCloudCode()));

        if (cloudTrackCode == null) {
            //云信额度修改
                quotaAmount(cloudAssets.getEnterpriseId(), cloudAssets.getAmount(), type);
        }
        /*
		  签收状态，修改云信状态为持单中
		 */
        if (CloudConstant.CLOUD_ASSETS_TYPE_ONE == type) {

            //云信付款明细
            CloudPaymentDetail cloudPaymentDetail = new CloudPaymentDetail();
            //云信流转编号
            String circulationCode = CodeUtil.generateCode(CodeEnum.CLOUD_CIRCULATION_NO);
            cloudPaymentDetail.setCloudTransferCode(circulationCode);
            cloudPaymentDetail.setCloudCode(cloudAssets.getCloudCode());
            cloudPaymentDetail.setCompanyId(cloudAssets.getUserId());
            cloudPaymentDetail.setAmount(cloudAssets.getAmount());
            cloudPaymentDetail.setType(CloudConstant.CLOUD_TYPE_ZERO);
            cloudPaymentDetail.setStatus(CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_ZERO);
            cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_TWO);
            //云信 拆分状态 修改为已转让
            cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_COMPLETE.getCode());
            //签收日期
            cloudAssets.setReceiveDate(LocalDateTime.now());
            //当前用户名称
            String name = MyAuthUtil.getName();
            cloudAssets.setUserName(name);
            //新增云信轨迹图
            CloudTrackRecord cloudTrackRecord = new CloudTrackRecord();
            cloudTrackRecord.setHoldName(cloudAssets.getName());
            cloudTrackRecord.setBillName(cloudAssets.getCompanyName());
            cloudTrackRecord.setAmount(cloudAssets.getAmount());
            cloudTrackRecord.setUserId(AuthUtil.getUserId());
            ///保存开户行
            cloudAssets.setBackName(bankCardVO.getBackName());
            cloudAssets.setBankDeposit(bankCardVO.getBankDeposit());
            cloudAssets.setBankCardNo(bankCardVO.getBankCardNo());

            CloudPay cloudPay = cloudPayService.getOne(Wrappers.<CloudPay>lambdaQuery().eq(CloudPay::getCloudCode, cloudAssets.getCloudCode()));
            if (cloudTrackCode == null) {
                //尝试在开单签收后去对付款计划进行添加操作

                saveCloudRepaymentPlan(cloudAssets);
                //核心企业云信编号
                cloudPaymentDetail.setCloudCoreCode(cloudAssets.getCloudCode());

                cloudTrackRecord.setCloudCoreCode(cloudAssets.getCloudCode());
                cloudTrackRecord.setCloudCode(cloudAssets.getCloudCode());
                cloudTrackRecord.setType(CloudConstant.CLOUD_TYPE_ZERO);
                //核心企业兑付信息状态改变
                cloudPay.setStatus(CloudConstant.CLOUD_STATUS_FOUR);
                cloudPayService.saveOrUpdate(cloudPay);
                //区块链上链 --->>云信开单流程
                String value = ParamCache.getValue(CommonConstant.BLOCK_ENABLED);
                if (StrUtil.isNotBlank(value) && CommonConstant.YES == Integer.parseInt(value)) {
                    BlockChainCloudApply blockChainCloudApply = new BlockChainCloudApply();
                    blockChainCloudApply.setBillAmount(cloudPay.getAmount());
                    blockChainCloudApply.setEndDate(cloudPay.getEndDate());
                    blockChainCloudApply.setFinancingModel(cloudPay.getFinancingModel());
                    blockChainCloudApply.setUpstreamSupplierId(cloudPay.getUpstreamSupplierId());
                    blockChainCloudApply.setGoodsId(cloudPay.getCloudProductId());
                    blockChainCloudApply.setCloudCode(cloudPay.getCloudCode());
                    blockChainCloudApply.setHoldName(cloudTrackRecord.getHoldName());
                    blockChainCloudApply.setBillName(cloudTrackRecord.getBillName());
                    blockChainCloudApply.setAmount(cloudTrackRecord.getAmount());
                    blockChainCloudApply.setCloudCoreCode(cloudTrackRecord.getCloudCoreCode());
                    blockChainCloudApply.setCloudCode(cloudTrackRecord.getCloudCode());
                    blockChainCloudApply.setType(CloudConstant.CLOUD_TYPE_ZERO);
                    blockChainCloudApply.setStartDate(cloudAssets.getStartDate());
                    blockChainCloudApply.setUserId(cloudTrackRecord.getUserId());
                    blockChainCloudApply.setCoreCompanyId(cloudAssets.getCoreCompanyId());
                    blockChainCloudApply.setStatus(BlockChainEnum.CLOUD_APPLY_BILL.getCode());
                    blockChainInformationService.saveBlockChain(blockChainCloudApply);
                }
            } else {
                //从关联表中获取核心企业云信编号
                cloudPaymentDetail.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());

                cloudTrackRecord.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
                cloudTrackRecord.setCloudCode(cloudTrackCode.getCloudParentCode());
                //如果不是第一次新增云信轨迹图，那么下一个云信编号则需要填写当前进来 的 云信编号
                cloudTrackRecord.setNextCloudCode(cloudAssets.getCloudCode());
                cloudTrackRecord.setType(cloudAssets.getType());
                /*
				  后续云信轨迹图会使用递归循环，所以需要创建一个 父级id，先获取该云信轨迹上一级id
				  核心企业云信编号是否跟云信编号一致，一致则根据云信编号 查询云信轨迹图，获取到id，如果不一致则根据下一级云信编号查询 云信轨迹图
				*/
                //区块链上链 --->>云信签收流程
                BlockChainCloudReceive blockChainCloudReceive = new BlockChainCloudReceive();
                assignTrackRecord(cloudAssets, cloudTrackCode, cloudTrackRecord, blockChainCloudReceive);

                //通用方法 -- 判断 资产下面的子级状态
                commonSaveTrackRecord(cloudAssets.getCloudCode(), type, cloudAssets.getAmount());
                String value = ParamCache.getValue(CommonConstant.BLOCK_ENABLED);
                if (StrUtil.isNotBlank(value) && CommonConstant.YES == Integer.parseInt(value)) {
                    cloudCheckBlock(cloudAssets, cloudTrackRecord, blockChainCloudReceive, cloudTrackRecord.getAmount(), cloudTrackRecord.getEndDate());
                    blockChainCloudReceive.setNextCloudCode(cloudTrackRecord.getNextCloudCode());
                    blockChainCloudReceive.setStartDate(cloudAssets.getStartDate());
                    blockChainCloudReceive.setEndDate(cloudAssets.getEndDate());
                    blockChainCloudReceive.setUserId(cloudTrackRecord.getUserId());
                    blockChainCloudReceive.setCoreCompanyId(cloudAssets.getCoreCompanyId());
                    blockChainCloudReceive.setStatus(BlockChainEnum.CLOUD_SIGN.getCode());
                    blockChainInformationService.saveBlockChain(blockChainCloudReceive);
                }
            }

            cloudTrackRecord.setStartDate(cloudAssets.getStartDate());
            cloudTrackRecord.setEndDate(cloudAssets.getEndDate());
            //云信轨迹图
            cloudTrackRecordService.save(cloudTrackRecord);
            //云信付款明细
            cloudPaymentDetailService.save(cloudPaymentDetail);

            //修改云信额度使用状态
            if (ObjectUtil.isNotEmpty(cloudPay)) {
                Long quotaUseDetailsId = cloudPay.getQuotaUseDetailsId();
                Integer code = QuotaUseDetailsEnum.CLOUD_CONFIRMED.getCode();
                quotaUseDetailsService.updateStatus(quotaUseDetailsId, code);
            }
        } else {
            cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_ONE);
            //云信 拆分状态 修改为转让失败
            cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_FAILURE.getCode());

            if (StrUtil.isBlank(denialReason)) {
                throw new ServiceException("请填写拒绝原因！");
            }
            cloudAssets.setDenialReason(denialReason);
            if (cloudTrackCode == null) {
                //核心企业兑付信息状态改变
                CloudPay cloudPay = cloudPayService.getOne(Wrappers.<CloudPay>lambdaQuery().eq(CloudPay::getCloudCode, cloudAssets.getCloudCode()));
                cloudPay.setStatus(CloudConstant.CLOUD_STATUS_THREE);
                cloudPayService.saveOrUpdate(cloudPay);
            } else {
                commonSaveTrackRecord(cloudAssets.getCloudCode(), type, cloudAssets.getAmount());
            }
            //修改云信额度使用状态
            CloudPay cloudPay = cloudPayService.getOne(Wrappers.<CloudPay>lambdaQuery().eq(CloudPay::getCloudCode, cloudAssets.getCloudCode()));
            //Todo 改动当前额度针对都是核心企业的额度调整
            if (Objects.nonNull(cloudPay) && cloudPay.getQuotaUseDetailsId() != null) {
                Long quotaUseDetailsId = cloudPay.getQuotaUseDetailsId();
                Integer code = QuotaUseDetailsEnum.CLOUD_FAIL.getCode();
                quotaUseDetailsService.updateStatus(quotaUseDetailsId, code);
            }
        }
        return this.saveOrUpdate(cloudAssets);
    }

    private void cloudCheckBlock(CloudAssets cloudAssets, CloudTrackRecord cloudTrackRecord, BlockChainCloudReceive blockChainCloudReceive, BigDecimal amount, LocalDate endDate) {
        blockChainCloudReceive.setAmount(amount);
        blockChainCloudReceive.setEndDate(endDate);
        blockChainCloudReceive.setCompanyId(cloudAssets.getCompanyId());
        blockChainCloudReceive.setHoldId(cloudAssets.getUserId());
        blockChainCloudReceive.setHoldName(cloudTrackRecord.getHoldName());
        blockChainCloudReceive.setBillName(cloudTrackRecord.getBillName());
        blockChainCloudReceive.setContent(cloudTrackRecord.getContent());
        blockChainCloudReceive.setType(CloudConstant.CLOUD_TYPE_ZERO);
        blockChainCloudReceive.setCloudCode(cloudTrackRecord.getCloudCode());
        blockChainCloudReceive.setCloudCoreCode(cloudTrackRecord.getCloudCoreCode());
    }

    private void assignTrackRecord(CloudAssets cloudAssets, CloudTrackCode cloudTrackCode, CloudTrackRecord cloudTrackRecord, BlockChainCloudReceive blockChainCloudReceive) {
        Long cloudTrackId = null;
        Long parentId = null;
        String value = ParamCache.getValue(CommonConstant.BLOCK_ENABLED);
        if (cloudTrackCode.getCloudCoreCode().equals(cloudTrackCode.getCloudParentCode())) {
            cloudTrackId = cloudTrackRecordService.getOne(Wrappers.<CloudTrackRecord>lambdaQuery().eq(CloudTrackRecord::getCloudCode, cloudTrackCode.getCloudParentCode()).eq(CloudTrackRecord::getParentId, 0)).getId();
            if (StrUtil.isNotBlank(value) && CommonConstant.YES == Integer.parseInt(value)) {
                parentId = blockChainInformationService.getOne(Wrappers.<BlockChainInformation>lambdaQuery().eq(BlockChainInformation::getCloudCode, cloudTrackCode.getCloudParentCode()).eq(BlockChainInformation::getParentId, 0)).getId();
            }
        } else {
            cloudTrackId = cloudTrackRecordService.getOne(Wrappers.<CloudTrackRecord>lambdaQuery().eq(CloudTrackRecord::getNextCloudCode, cloudTrackCode.getCloudParentCode())).getId();
            if (StrUtil.isNotBlank(value) && CommonConstant.YES == Integer.parseInt(value)) {
                parentId = blockChainInformationService.getOne(Wrappers.<BlockChainInformation>lambdaQuery().eq(BlockChainInformation::getNextCloudCode, cloudTrackCode.getCloudParentCode())).getId();
            }
        }
        cloudTrackRecord.setParentId(cloudTrackId);
        blockChainCloudReceive.setParentId(parentId);
        //上一级用户操作日志
        CloudPayLogs cloudPayLogs = cloudPayLogsService.getOne(Wrappers.<CloudPayLogs>lambdaQuery().eq(CloudPayLogs::getCloudCode, cloudTrackCode.getCloudCode()));
        if (cloudAssets.getType() == CloudConstant.CLOUD_TYPE_ZERO) {
            String content = DateUtil.formatDateTime(cloudPayLogs.getCreateTime()) + "由" + cloudPayLogs.getCompanyName() + "-" + cloudPayLogs.getUserName() + "拆分";
            cloudTrackRecord.setContent(content);
            blockChainCloudReceive.setContent(content);
        } else {
            String content = DateUtil.formatDateTime(cloudPayLogs.getCreateTime()) + "融资贴现";
            blockChainCloudReceive.setContent(content);
            cloudTrackRecord.setContent(content);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commonSaveTrackRecord(String cloudCode, Integer type, BigDecimal cloudAssetsAmount) {
        //根据云信编号查询资产关联表
        CloudTrackCode cloudTrackCode = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudCode));

        //根据父级云信编号，查询出同级所有资产信息
        String cloudParentCode = cloudTrackCode.getCloudParentCode();
        List<Long> cloudAssetsIds = cloudTrackCodeService.list(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudParentCode, cloudParentCode)).parallelStream()
                .map(CloudTrackCode::getAssetsId).collect(Collectors.toList());
        List<CloudAssets> cloudAssetsList = this.listByIds(cloudAssetsIds);

        //父级资产信息
        CloudAssets cloudAssetsParent = this.getOne(Wrappers.<CloudAssets>lambdaQuery().eq(CloudAssets::getCloudCode, cloudParentCode));
        //查询所有待签收的资产信息
        long countTreat = cloudAssetsList.parallelStream().filter(e -> e.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_ZERO).count();

        long size = cloudAssetsList.size();

        long count = cloudAssetsList.parallelStream().filter(e -> e.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_ONE).count();

        if (CloudConstant.CLOUD_ASSETS_TYPE_ONE == type) {
            //只有当前节点没有签收，新增云信轨迹图，改变父级状态为已拆分
            if (countTreat == 1) {
                //父级云信金额大于0才能进行自身拆分
                if (cloudAssetsParent.getAmount().compareTo(new BigDecimal(0)) > 0) {
                    saveCloud(cloudAssetsParent, cloudTrackCode, cloudAssetsList.get(0).getStartDate());
                }
                cloudAssetsParent.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_FIVE);
                cloudAssetsParent.setAmount(new BigDecimal(0));
                cloudAssetsParent.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_COMPLETE.getCode());
            }
        } else {
            if (size == count + 1) {
                BigDecimal amount = new BigDecimal(0);
                //拆分全部都拒绝则云信金额返回给原本的资产信息
                for (CloudAssets assets : cloudAssetsList) {
                    amount = NumberUtil.add(assets.getAmount(), amount);
                }
                //父级资产信息 --- 父级云信金额
                cloudAssetsParent.setAmount(cloudAssetsParent.getCloudBillAmount());
                cloudAssetsParent.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_TWO);
                cloudAssetsParent.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_OTHER.getCode());
            } else {
                cloudAssetsParent.setAmount(NumberUtil.add(cloudAssetsParent.getAmount(), cloudAssetsAmount).setScale(2, BigDecimal.ROUND_HALF_UP));
                //只有当前节点没有签收，新增云信轨迹图，改变父级状态为已拆分
                if (countTreat == 1) {
                    //父级云信金额大于0才能进行自身拆分
                    if (cloudAssetsParent.getAmount().compareTo(new BigDecimal(0)) > 0) {
                        saveCloud(cloudAssetsParent, cloudTrackCode, cloudAssetsList.get(0).getStartDate());
                    }
                    cloudAssetsParent.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_FIVE);
                    cloudAssetsParent.setAmount(new BigDecimal(0));
                    cloudAssetsParent.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_COMPLETE.getCode());
                }
            }
        }
        this.saveOrUpdate(cloudAssetsParent);
    }

    /**
     * 转让 -- 下一步
     *
     * @param cloudAssetsDTO 资产集合
     * @return
     */
    @Override
    public CloudAssetsDTO saveNextTransfer(CloudAssetsDTO cloudAssetsDTO) {
        //根据父级云信编号查询 资产关联表，如果查询不到则该父级云信编号为核心企业云信编号

        CloudTrackCode cloudTrackCode = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudAssetsDTO.getCloudParentCode()));
        if (ObjectUtil.isNotEmpty(cloudTrackCode)) {
            cloudAssetsDTO.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
        } else {
            cloudAssetsDTO.setCloudCoreCode(cloudAssetsDTO.getCloudParentCode());
        }

        //判断集合是否重复
        List<Long> collect = cloudAssetsDTO.getCloudAssets().parallelStream().map(CloudAssets::getUserId).collect(Collectors.toList());
        HashSet set = new HashSet<>(collect);
        Boolean result = set.size() == collect.size() ? true : false;
        if (!result) {
            throw new ServiceException("开单企业不能重复");
        }
        //获取父级合同，不生成多份
//		CloudContract cloudContract = cloudContractService.list(Wrappers.<CloudContract>lambdaQuery().eq(CloudContract::getCloudParentCode, cloudAssetsDTO.getCloudCoreCode())).parallelStream()
//			.filter(cloudContractOne -> cloudContractOne.getType() == 0 && cloudContractOne.getIsDeleted() == 0)
//			.collect(Collectors.toList()).get(0);

//		List<CloudContract> cloudContractList = new ArrayList<>();
        CloudPay cloudPay = cloudPayService.getOne(Wrappers.<CloudPay>lambdaQuery().eq(CloudPay::getCloudCode, cloudAssetsDTO.getCloudCoreCode()));
        for (CloudAssets cloudAssets : cloudAssetsDTO.getCloudAssets()) {
            //创建云信编号
            String cloud = CodeUtil.generateCode(CodeEnum.CLOUD_NO);
//			//合同生成
//			CloudContract cloudContractAssets = ObjectUtil.cloneByStream(cloudContract);
            cloudAssets.setCloudCode(cloud);
            cloudAssets.setStartDate(LocalDate.now());
            cloudAssets.setEndDate(cloudPay.getEndDate());
//			cloudContractAssets.setCloudCode(cloud);
//			cloudContractAssets.setContractCode(null);
//			cloudContractAssets.setUrl(null);
//			cloudContractAssets.setCompanyId(MyAuthUtil.getUserId());
//			cloudContractAssets.setId(null);
//			cloudContractList.add(cloudContractAssets);
        }
        //生成合同
//		cloudContractService.saveBatch(cloudContractList);
        //cloudAssetsDTO数据存放到redis中
        String code = CodeUtil.generateCode(CodeEnum.TRANSFER_REDIS_CODE);

        cloudAssetsDTO.setCloudProductId(cloudPay.getCloudProductId());
        cloudAssetsDTO.setCloudRedisCode(cloudAssetsDTO.getCloudCoreCode() + code);
        bladeRedis.setEx(cloudAssetsDTO.getCloudRedisCode(), cloudAssetsDTO, Duration.ofHours(1));
        return cloudAssetsDTO;
    }

    /**
     * 转让 -- 提交
     *
     * @param cloudAssetsDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitTransfer(CloudAssetsDTO cloudAssetsDTO) {

        List<CloudAssets> cloudAssetsList = cloudAssetsDTO.getCloudAssets();
        //判断集合是否重复
        List<Long> collect = cloudAssetsList.parallelStream().map(CloudAssets::getUserId).collect(Collectors.toList());
        HashSet set = new HashSet<>(collect);
        boolean result = set.size() == collect.size();
        if (!result) {
            throw new ServiceException("开单企业不能重复");
        }

        BigDecimal amount = new BigDecimal(0);
        //拆分全部都拒绝则云信金额返回给原本的资产信息
        for (CloudAssets assets : cloudAssetsList) {
            amount = NumberUtil.add(assets.getAmount(), amount);
        }

        //根据云信编号查询资产信息
        CloudAssets cloudAssets = this.getOne(Wrappers.<CloudAssets>lambdaQuery().eq(CloudAssets::getCloudCode, cloudAssetsDTO.getCloudParentCode()));

        if (amount.compareTo(cloudAssets.getAmount()) > 0) {
            throw new ServiceException("云信金额不能大于可用额度");
        }

        if (ObjectUtil.isEmpty(cloudAssets)) {
            throw new ServiceException("查询不到父级资产信息，请联系管理员！");
        }

        for (CloudAssets assets : cloudAssetsDTO.getCloudAssets()) {
            //开单日期
            assets.setStartDate(LocalDate.now());
            assets.setDay(cloudAssets.getDay());
            assets.setEndDate(cloudAssets.getEndDate());
            assets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_ZERO);
            //云信 拆分状态 修改为  转让待收
            assets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_AWAIT.getCode());
            assets.setType(CloudConstant.CLOUD_TYPE_ZERO);
            assets.setFinancingApr(cloudAssets.getFinancingApr());
            assets.setId(null);
            assets.setCoreCompanyId(cloudAssets.getCoreCompanyId());
            assets.setCompanyId(cloudAssets.getUserId());
            assets.setCompanyName(cloudAssets.getName());
            assets.setEnterpriseId(cloudAssets.getEnterpriseId());
            CloudTrackCode cloudTrackCode = new CloudTrackCode();
            //开单记录
            cloudPayLogsService.saveFinancingCloudPayLogs(assets.getCloudCode());
            this.save(assets);
            //资产关联表
            cloudTrackCode.setCloudCode(assets.getCloudCode());
            cloudTrackCode.setCloudCoreCode(cloudAssetsDTO.getCloudCoreCode());
            cloudTrackCode.setCloudParentCode(cloudAssetsDTO.getCloudParentCode());
            cloudTrackCode.setAssetsId(assets.getId());
            cloudTrackCode.setUserId(AuthUtil.getUserId());
            cloudTrackCodeService.save(cloudTrackCode);
        }
        List<CloudAssets> cloudAssetsMap = cloudAssetsDTO.getCloudAssets();
        List<Long> cloudAssetsIdList = cloudAssetsMap.stream().map(CloudAssets::getId).collect(Collectors.toList());
        // 发送mq延时消息，融资企业超时没有签收，则作废 云信资产信息和云信兑付信息
        rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
                .messageType(CloudMessageEnum.CLOUD_BILL_SIGN.getValue())
                .ids(cloudAssetsIdList)
                .seconds(RabbitMqCommonUtils.calculateDay(cloudAssets.getDay(), RabbitMqDayEnum.RABBIT_UNIT_DAY.getCode()))
                .status(RabbitMqStatusEnum.CLOUD_BILL_TRANSFER_SIGN.getCode())
                .build());
        //父级云信剩余云信金额
        BigDecimal parentAmount = NumberUtil.sub(cloudAssets.getAmount(), amount).setScale(2, BigDecimal.ROUND_HALF_EVEN);
        cloudAssets.setAmount(parentAmount);
        //父级云信状态变更
        cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_FOUR);
//		//父级云信 拆分状态 修改为  已转让
//		cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_COMPLETE.getCode());
        //删除云信转让redis key
        bladeRedis.del(cloudAssetsDTO.getCloudRedisCode());
        return this.updateById(cloudAssets);
    }

    private void saveCloud(CloudAssets cloudAssetsParent, CloudTrackCode cloudTrackCode, LocalDate startDate) {

        //新增资产信息
        CloudAssets cloudAssets = ObjectUtil.cloneByStream(cloudAssetsParent);
        //创建云信编号
        String cloud = CodeUtil.generateCode(CodeEnum.CLOUD_NO);
        cloudAssets.setId(null);
        cloudAssets.setCloudCode(cloud);
        cloudAssets.setAmount(cloudAssetsParent.getAmount());
        cloudAssets.setCloudBillAmount(cloudAssetsParent.getAmount());
        cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_TWO);
        //云信拆分，自身转让（剩余云信额度）
        cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_OTHER.getCode());
        cloudAssets.setStartDate(startDate);
        cloudAssets.setCompanyName(cloudAssetsParent.getName());
        cloudAssets.setCompanyId(cloudAssetsParent.getUserId());
        cloudAssets.setType(CloudConstant.CLOUD_TYPE_ZERO);
        this.save(cloudAssets);

        //新增 资产关联
        CloudTrackCode cloudTrackCodeOne = new CloudTrackCode();
        cloudTrackCodeOne.setCloudCode(cloud);
        cloudTrackCodeOne.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
        cloudTrackCodeOne.setCloudParentCode(cloudTrackCode.getCloudParentCode());
        cloudTrackCodeOne.setAssetsId(cloudAssets.getId());
        cloudTrackCodeOne.setUserId(AuthUtil.getUserId());
        cloudTrackCodeService.save(cloudTrackCodeOne);

        //云信付款明细
        CloudPaymentDetail cloudPaymentDetail = new CloudPaymentDetail();
        //云信流转编号
        String circulationCode = CodeUtil.generateCode(CodeEnum.CLOUD_CIRCULATION_NO);
        cloudPaymentDetail.setCloudTransferCode(circulationCode);
        cloudPaymentDetail.setCloudCode(cloud);
        cloudPaymentDetail.setCompanyId(cloudAssetsParent.getUserId());
        cloudPaymentDetail.setAmount(cloudAssetsParent.getAmount());
        cloudPaymentDetail.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
        cloudPaymentDetail.setType(CloudConstant.CLOUD_TYPE_ZERO);
        cloudPaymentDetail.setStatus(CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_ZERO);
        cloudPaymentDetailService.save(cloudPaymentDetail);


        //云信轨迹图
        CloudTrackRecord cloudTrackRecord = new CloudTrackRecord();
        cloudTrackRecord.setHoldName(cloudAssets.getName());
        cloudTrackRecord.setBillName(cloudAssets.getCompanyName());
        cloudTrackRecord.setAmount(cloudAssets.getCloudBillAmount());
        cloudTrackRecord.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
        cloudTrackRecord.setCloudCode(cloudTrackCode.getCloudParentCode());
        cloudTrackRecord.setUserId(cloudAssets.getUserId());
        //如果不是第一次新增云信轨迹图，那么下一个云信编号则需要填写当前进来 的 云信编号
        cloudTrackRecord.setNextCloudCode(cloudAssets.getCloudCode());
        cloudTrackRecord.setStartDate(cloudAssets.getStartDate());
        cloudTrackRecord.setEndDate(cloudAssets.getEndDate());
        //自己生成的默认为转让类型
        cloudTrackRecord.setType(CloudConstant.CLOUD_TYPE_ZERO);
        BlockChainCloudReceive blockChainCloudReceive = new BlockChainCloudReceive();
        assignTrackRecord(cloudAssets, cloudTrackCode, cloudTrackRecord, blockChainCloudReceive);
        cloudTrackRecordService.save(cloudTrackRecord);
        //区块链上链 --->>云信签收流程
        String value = ParamCache.getValue(CommonConstant.BLOCK_ENABLED);
        if (StrUtil.isNotBlank(value) && CommonConstant.YES == Integer.parseInt(value)) {
            cloudCheckBlock(cloudAssets, cloudTrackRecord, blockChainCloudReceive, cloudAssets.getAmount(), cloudAssets.getEndDate());
            blockChainCloudReceive.setCoreCompanyId(cloudAssets.getCoreCompanyId());
            blockChainCloudReceive.setNextCloudCode(cloudTrackRecord.getNextCloudCode());
            blockChainCloudReceive.setStartDate(cloudTrackRecord.getStartDate());
            blockChainCloudReceive.setUserId(cloudTrackRecord.getUserId());
            blockChainCloudReceive.setStatus(BlockChainEnum.CLOUD_SIGN.getCode());
            blockChainInformationService.saveBlockChain(blockChainCloudReceive);
        }
    }

    /**
     * 云信额度改变
     * 签收流程：云信额度 申请中金额减少 已用额度增加
     * 拒绝流程：云信额度 申请中额度减少 判断改云信额度是 生效中还是冻结中。
     * 生效：可用额度增加
     * 冻结：冻结额度增加
     *
     * @param enterpriseId 云信额度 id
     * @param cloudAmount  云信金额
     * @param type         1、签收 2、拒绝
     */
    @Override
    public void quotaAmount(Long enterpriseId, BigDecimal cloudAmount, Integer type) {

        //修改云信额度
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(enterpriseId).getData();
        //可用额度
        BigDecimal quotaAmount = enterpriseQuota.getAvailableAmount();
        //申请中的额度
        BigDecimal applyAmount = enterpriseQuota.getApplyAmount();
        //冻结额度
        BigDecimal frozenAmount = enterpriseQuota.getFrozenAmount();
        //已用额度
        BigDecimal usedAmount = enterpriseQuota.getUsedAmount();
        if (CloudConstant.CLOUD_ASSETS_TYPE_ONE == type) {
            //已用额度增加
            BigDecimal availableAmount = NumberUtil.add(usedAmount, cloudAmount).setScale(2, BigDecimal.ROUND_HALF_EVEN);
            enterpriseQuota.setUsedAmount(availableAmount);
        } else {
            if (enterpriseQuota.getStatus() == EnterpriseQuotaStatusEnum.FROZEN.getCode()) {
                BigDecimal freezeAmount = NumberUtil.add(frozenAmount, cloudAmount).setScale(2, BigDecimal.ROUND_HALF_EVEN);
                enterpriseQuota.setFrozenAmount(freezeAmount);
            } else {
                BigDecimal availableAmount = NumberUtil.add(quotaAmount, cloudAmount).setScale(2, BigDecimal.ROUND_HALF_EVEN);
                enterpriseQuota.setAvailableAmount(availableAmount);
            }
        }
        //申请额度减少
        BigDecimal bigDecimal = NumberUtil.sub(applyAmount, cloudAmount).setScale(2, BigDecimal.ROUND_HALF_EVEN);
        enterpriseQuota.setApplyAmount(bigDecimal);
        //TODO 需要调整，临时方法
        enterpriseQuotaService.updateById(enterpriseQuota);
    }

    /**
     * 融资申请--云信 发起
     *
     * @param cloudAssetsFinancingDTO
     * @return 返回新生成的云信编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String financingCloud(CloudAssetsFinancingDTO cloudAssetsFinancingDTO) {
        CloudAssets cloudAssetsChild = this.getOne(Wrappers.<CloudAssets>lambdaQuery().eq(CloudAssets::getCloudCode, cloudAssetsFinancingDTO.getCloudCode()));
        if (ObjectUtil.isEmpty(cloudAssetsChild)) {
            throw new ServiceException("查询不到云信信息！");
        }

        if ((cloudAssetsFinancingDTO.getFinancingModel() == CloudConstant.CLOUD_FINANCING_MODEL_ZERO && cloudAssetsFinancingDTO.getFinancingMoney().compareTo(cloudAssetsChild.getAmount()) > 0) ||
                (cloudAssetsFinancingDTO.getFinancingModel() == CloudConstant.CLOUD_FINANCING_MODEL_ONE && cloudAssetsFinancingDTO.getCloudBillAmount().compareTo(cloudAssetsChild.getAmount()) > 0)
        ) {
            throw new ServiceException("申请的云信金额不能超出！");
        }

        //工具类，主要用来转换
        ConverterRegistry converterRegistry = ConverterRegistry.getInstance();

        //创建云信编号
        String cloud = CodeUtil.generateCode(CodeEnum.CLOUD_NO);
        //转换
        CloudAssets cloudAssets = converterRegistry.convert(CloudAssets.class, cloudAssetsFinancingDTO);
        cloudAssets.setCloudCode(cloud);
        cloudAssets.setAmount(cloudAssets.getCloudBillAmount());
        cloudAssets.setStartDate(LocalDate.now());
        cloudAssets.setDay(cloudAssetsChild.getDay());
        cloudAssets.setEndDate(cloudAssetsChild.getEndDate());
        cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_ZERO);
        cloudAssets.setType(CloudConstant.CLOUD_TYPE_ONE);
        cloudAssets.setFinancingApr(cloudAssetsChild.getFinancingApr());
        cloudAssets.setCoreCompanyId(cloudAssetsChild.getCoreCompanyId());
        cloudAssets.setEnterpriseId(cloudAssetsChild.getEnterpriseId());
        cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_FINANCING_AUDIT.getCode());
        //通过额度核心企业id获取产品id，最后通过产品id去获取到产品开通时设置的资方账户信息
        R<EnterpriseQuota> enterpriseQuota = enterpriseQuotaService.getById(cloudAssetsChild.getEnterpriseId());
        if (200 == enterpriseQuota.getCode()) {
            EnterpriseQuota e = enterpriseQuota.getData();
            //获取产品id
            Long goodsId = e.getGoodsId();
            //通过产品id去获取到产品开通时设置的资方账户信息
            BillBankCarda billBankCarda = billBankCardaRelationService.selectCardaRelationByGoodsId(goodsId, 2);
            if (Objects.nonNull(billBankCarda)) {
                cloudAssets.setBackName(billBankCarda.getBankDeposit());
                cloudAssets.setBankDeposit(billBankCarda.getBankDeposit());
                cloudAssets.setBankCardNo(billBankCarda.getBankCardNo());
            }

        }
        boolean before = cloudAssets.getStartDate().isAfter(cloudAssets.getEndDate());
        if (before) {
            throw new ServiceException("当前日期不能小于承若付款日");
        }

        //开单记录
        cloudPayLogsService.saveFinancingCloudPayLogs(cloudAssets.getCloudCode());
        //新增资产信息
        this.save(cloudAssets);

        //根据父级云信编号查询关联表，看是否存在值，如果没有值，则新增的关联表核心企业云信编号 == 父级云信编号
        CloudTrackCode cloudTrackCodeOne = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudAssetsChild.getCloudCode()));
        CloudTrackCode cloudTrackCode = new CloudTrackCode();
        if (ObjectUtil.isNotEmpty(cloudTrackCodeOne)) {
            cloudTrackCode.setCloudCoreCode(cloudTrackCodeOne.getCloudCoreCode());
        } else {
            cloudTrackCode.setCloudCoreCode(cloudAssetsChild.getCloudCode());
        }
        //资产关联表 --- 获取生成的云信编号
        cloudTrackCode.setCloudCode(cloudAssets.getCloudCode());
        //父级云信编号为传入的云信编号
        cloudTrackCode.setCloudParentCode(cloudAssetsChild.getCloudCode());
        cloudTrackCode.setAssetsId(cloudAssets.getId());
        cloudTrackCode.setUserId(AuthUtil.getUserId());
        cloudTrackCodeService.save(cloudTrackCode);

        //父级云信剩余云信金额
        BigDecimal parentAmount = NumberUtil.sub(cloudAssetsChild.getAmount(), cloudAssets.getAmount()).setScale(2, BigDecimal.ROUND_HALF_EVEN);
        cloudAssetsChild.setAmount(parentAmount);
        //父级云信状态变更
        cloudAssetsChild.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_FOUR);
        this.updateById(cloudAssetsChild);
        return cloud;
    }

    /**
     * 融资申请--云信 结束节点
     *
     * @param cloudCode 云信编号
     * @param type      类型 1、签收 2、拒绝
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean financingCloudEnd(String cloudCode, Integer type) {
        CloudAssets cloudAssets = this.getOne(Wrappers.<CloudAssets>lambdaQuery().eq(CloudAssets::getCloudCode, cloudCode));
        if (cloudAssets.getStatus() != CloudConstant.CLOUD_ASSETS_STATUS_ZERO) {
            throw new ServiceException("资产状态必须为待签收");
        }

        //查询资产关联表。
        CloudTrackCode cloudTrackCode = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudAssets.getCloudCode()));

        if (ObjectUtil.isEmpty(cloudTrackCode)) {
            throw new ServiceException("资产关联信息查询不到，请联系管理员！");
        }

        /**
         * 签收状态，修改云信状态为持单中
         */
        if (CloudConstant.CLOUD_ASSETS_TYPE_ONE == type) {
            //云信付款明细
            CloudPaymentDetail cloudPaymentDetail = new CloudPaymentDetail();
            //云信流转编号
            String circulationCode = CodeUtil.generateCode(CodeEnum.CLOUD_CIRCULATION_NO);
            cloudPaymentDetail.setCloudTransferCode(circulationCode);
            cloudPaymentDetail.setCloudCode(cloudAssets.getCloudCode());
            cloudPaymentDetail.setCompanyId(cloudAssets.getUserId());
            cloudPaymentDetail.setAmount(cloudAssets.getAmount());
            cloudPaymentDetail.setType(CloudConstant.CLOUD_TYPE_ONE);
            cloudPaymentDetail.setStatus(CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_ZERO);
            cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_TWO);
            //云信 拆分状态 修改为已融资
            cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_FINANCING_COMPLETE.getCode());
            //签收日期
            cloudAssets.setReceiveDate(LocalDateTime.now());
            //当前用户名称
            cloudAssets.setUserName(AuthUtil.getUserName());
            //新增云信轨迹图
            CloudTrackRecord cloudTrackRecord = new CloudTrackRecord();
            cloudTrackRecord.setHoldName(cloudAssets.getName());
            cloudTrackRecord.setBillName(cloudAssets.getCompanyName());
            cloudTrackRecord.setAmount(cloudAssets.getAmount());
            cloudTrackRecord.setUserId(AuthUtil.getUserId());

            //从关联表中获取核心企业云信编号
            cloudPaymentDetail.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());

            cloudTrackRecord.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
            cloudTrackRecord.setCloudCode(cloudTrackCode.getCloudParentCode());
            //如果不是第一次新增云信轨迹图，那么下一个云信编号则需要填写当前进来 的 云信编号
            cloudTrackRecord.setNextCloudCode(cloudAssets.getCloudCode());
            cloudTrackRecord.setType(cloudAssets.getType());
            /*
			  后续云信轨迹图会使用递归循环，所以需要创建一个 父级id，先获取该云信轨迹上一级id
			  核心企业云信编号是否跟云信编号一致，一致则根据云信编号 查询云信轨迹图，获取到id，如果不一致则根据下一级云信编号查询 云信轨迹图
			*/
            BlockChainCloudReceive blockChainCloudReceive = new BlockChainCloudReceive();
            assignTrackRecord(cloudAssets, cloudTrackCode, cloudTrackRecord, blockChainCloudReceive);

            //区块链上链 --->>云信签收流程
            String value = ParamCache.getValue(CommonConstant.BLOCK_ENABLED);
            if (StrUtil.isNotBlank(value) && CommonConstant.YES == Integer.parseInt(value)) {
                blockChainCloudReceive.setAmount(cloudAssets.getAmount());
                blockChainCloudReceive.setStartDate(cloudAssets.getStartDate());
                blockChainCloudReceive.setEndDate(cloudAssets.getEndDate());
                blockChainCloudReceive.setCompanyId(cloudAssets.getCompanyId());
                blockChainCloudReceive.setHoldId(cloudAssets.getUserId());
                blockChainCloudReceive.setHoldName(cloudTrackRecord.getHoldName());
                blockChainCloudReceive.setBillName(cloudTrackRecord.getBillName());
                blockChainCloudReceive.setContent(cloudTrackRecord.getContent());
                blockChainCloudReceive.setType(CloudConstant.CLOUD_TYPE_ONE);
                blockChainCloudReceive.setCloudCode(cloudTrackRecord.getCloudCode());
                blockChainCloudReceive.setCloudCoreCode(cloudTrackRecord.getCloudCoreCode());
                blockChainCloudReceive.setNextCloudCode(cloudTrackRecord.getNextCloudCode());
                blockChainCloudReceive.setStartDate(cloudTrackRecord.getStartDate());
                blockChainCloudReceive.setUserId(cloudTrackRecord.getUserId());
                blockChainCloudReceive.setCoreCompanyId(cloudAssets.getCoreCompanyId());
                blockChainCloudReceive.setStatus(BlockChainEnum.CLOUD_SIGN.getCode());
                blockChainInformationService.saveBlockChain(blockChainCloudReceive);
            }

            //通用方法 -- 判断 资产下面的子级状态
            commonSaveTrackRecord(cloudAssets.getCloudCode(), type, cloudAssets.getAmount());
            cloudTrackRecord.setStartDate(cloudAssets.getStartDate());
            cloudTrackRecord.setEndDate(cloudAssets.getEndDate());
            //云信轨迹图
            cloudTrackRecordService.save(cloudTrackRecord);
            //云信付款明细
            cloudPaymentDetailService.save(cloudPaymentDetail);
        } else {
            cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_ONE);
            //云信 拆分状态 修改为已融资
            cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_FINANCING_FAILURE.getCode());
            commonSaveTrackRecord(cloudAssets.getCloudCode(), type, cloudAssets.getAmount());
        }

        return this.saveOrUpdate(cloudAssets);
    }

    /**
     * 根据核心企业云信编号 获取云信资产编号关联ids集合
     *
     * @param cloudCoreCode 核心企业云信编号
     * @return 云信资产编号关联ids集合
     */
    @Override
    public List<Long> selectCloudTrackCodeList(String cloudCoreCode) {
        List<Long> ids = cloudTrackCodeService.selectCloudTrackCodeList(cloudCoreCode).parallelStream().map(CloudTrackCode::getAssetsId).collect(Collectors.toList());
        return ids;
    }

    /**
     * @param cloudburstList 云信资产ids
     * @return 云信付款明细集合
     */
    @Override
    public IPage<CloudPaymentDetail> selectCloudPaymentAssetsList(List<String> cloudburstList, Query query, LambdaQueryWrapper<CloudPaymentDetail> cloudPaymentDetailLambdaQueryWrapper) {

        cloudPaymentDetailLambdaQueryWrapper.in(CloudPaymentDetail::getCloudCode, cloudburstList);
        BigDecimal cloudAmount = cloudPaymentDetailService.list(cloudPaymentDetailLambdaQueryWrapper).stream().map(CloudPaymentDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        IPage<CloudPaymentDetail> page = cloudPaymentDetailService.page(Condition.getPage(query), cloudPaymentDetailLambdaQueryWrapper);
        page.getRecords().get(0).setCollectionTotal(cloudAmount);
        return page;
    }

    /**
     * @param cloudburstList 云信资产ids
     * @return 云信未付款明细集合
     */
    @Override
    public List<CloudPaymentDetail> selectCloudPaymentDetailList(List<String> cloudburstList) {

        List<CloudPaymentDetail> list = cloudPaymentDetailService.list(Wrappers.<CloudPaymentDetail>lambdaQuery().in(CloudPaymentDetail::getCloudCode, cloudburstList));
        return list;
    }


    /**
     * 根据云信编号查询 云信付款明细
     *
     * @param cloudCode 云信编号
     * @return 云信付款明细
     */
    @Override
    public CloudPaymentDetail selectCloudPaymentAssets(String cloudCode) {
        CloudPaymentDetail cloudPaymentDetail = cloudPaymentDetailService.getOne(Wrappers.<CloudPaymentDetail>lambdaQuery().eq(CloudPaymentDetail::getCloudCode, cloudCode));
        return cloudPaymentDetail;
    }

    /**
     * 根据云信编号查询 云信付款明细集合（已兑付）
     *
     * @param cloudCode 云信编号
     * @return 云信付款明细
     */
    @Override
    public List<CloudPaymentDetailVO> selectCloudPaymentDetailVoAssets(String cloudCode) {
        List<CloudPaymentDetailVO> list = CloudPaymentDetailWrapper
                .build().listVO(cloudPaymentDetailService.list(Wrappers.<CloudPaymentDetail>lambdaQuery().eq(CloudPaymentDetail::getCloudCoreCode, cloudCode).eq(CloudPaymentDetail::getStatus, CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_ONE)));
        return list;
    }

    /**
     * 云信兑付---付款提交
     *
     * @param cloudPaymentDetailList 付款数据
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long paymentCloudAssetsSubmit(List<CloudPaymentDetail> cloudPaymentDetailList) {

        if (CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_TWO == cloudPaymentDetailList.get(0).getStatus()) {
            throw new ServiceException("结算中，不能提交支付信息！");
        }

        if (CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_ONE == cloudPaymentDetailList.get(0).getStatus()) {
            throw new ServiceException("已兑付，不能提交支付信息！");
        }

        //查询云信兑付是否存在
        String cloudCoreCode = cloudPaymentDetailList.get(0).getCloudCoreCode();
        CloudPay cloudPay = cloudPayService.getOne(Wrappers.<CloudPay>lambdaQuery().eq(CloudPay::getCloudCode, cloudCoreCode));
        if (ObjectUtil.isEmpty(cloudPay)) {
            throw new ServiceException("云信兑付信息不存在!");
        }
        //获取云信编号集合
//		List<String> CloudPaymentDetailCodeList = cloudPaymentDetailList.parallelStream().map(CloudPaymentDetail::getCloudCode).collect(Collectors.toList());


        //获取当前时间
//		LocalDate startDate = LocalDate.now();
//		//到期时间
//		LocalDate endDate = cloudPay.getEndDate();
//		boolean before = startDate.isAfter(endDate);
//		int type = 1;
//		int cloudType = CloudConstant.CLOUD_ASSETS_STATUS_SEVEN;
//		int cloudPayType = CloudConstant.CLOUD_STATUS_EIGHT;
//		if (before){
//			type = 2;
//			cloudType = CloudConstant.CLOUD_ASSETS_STATUS_NINE;
//			cloudPayType = CloudConstant.CLOUD_STATUS_SEVEN;
//		}

        //找到符合条件能兑付的云信资产信息
//		int finalCloudType = cloudType;
//		List<CloudAssets> cloudAssetsList = this.list(Wrappers.<CloudAssets>lambdaQuery().in(CloudAssets::getCloudCode,CloudPaymentDetailCodeList)).parallelStream()
//			.filter(cloudAssets -> cloudAssets.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_TEN || cloudAssets.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_SIX || cloudAssets.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_EIGHT)
//			.map(cloudAssets -> {
//				cloudAssets.setStatus(finalCloudType);
//				return cloudAssets;
//			})
//			.collect(Collectors.toList());

        //校验值是否正确，然后再填充数据给 付款列表  ,后续新增云信付款列表
        for (CloudPaymentDetail cloudPaymentDetail : cloudPaymentDetailList) {
//			CloudPaymentList cloudPaymentList = new CloudPaymentList();
//			cloudPaymentList.setCloudType(cloudPaymentDetail.getType());
//			cloudPaymentList.setRepaymentNumber(CodeUtil.generateCode(CodeEnum.CLOUD_REPAYMENT_NO));
//			cloudPaymentList.setCloudCode(cloudPaymentDetail.getCloudCode());
//			cloudPaymentList.setRepaymentId(deptId);
//			cloudPaymentList.setRepaymentName(dept.getDeptName());
//			cloudPaymentList.setAmountDue(cloudPaymentDetail.getCollectionTotal());
//			cloudPaymentList.setActualAmount(cloudPaymentDetail.getCollectionTotal());
//			cloudPaymentList.setType(type);
//			cloudPaymentList.setEndDate(endDate);
//			cloudPaymentList.setCloudAttachId(cloudPaymentDetail.getCloudAttachId());
//			cloudPaymentLists.add(cloudPaymentList);
            cloudPaymentDetail.setStatus(CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_TWO);
        }

        //更新兑付信息
//		cloudPayService.update(Wrappers.<CloudPay>lambdaUpdate()
//				.eq(CloudPay::getCloudCode, cloudPaymentDetailList.get(0).getCloudCoreCode())
//				.set(CloudPay::getStatus, cloudPayType)
//				.set(CloudPay::getCloudAttachId,cloudPayListDTO.getParentCloudAttachId())
//		);

        //更新云信资产信息
//		this.saveOrUpdateBatch(cloudAssetsList);
        BigDecimal amount = cloudPaymentDetailList.stream().map(CloudPaymentDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //新增云信付款列表
        //根据核心云信编号查询开单付息表数据，累加融资付息差值
        //将计算的付息差值总和金额放在 jrzh_cloud_payment_list表中的 CostDifference中
        BigDecimal costDifferenceSum = cloudFinancingCoreEnterprisePaymentService.selectCostDifference(cloudPay.getCloudCode());
        if (Objects.isNull(costDifferenceSum)) {
            costDifferenceSum = BigDecimal.ZERO;
        }
        CloudPaymentList cloudPaymentList = new CloudPaymentList();
        cloudPaymentList.setRepaymentNumber(CodeUtil.generateCode(CodeEnum.CLOUD_REPAYMENT_NO));
        cloudPaymentList.setCloudCode(cloudCoreCode);
        cloudPaymentList.setRepaymentId(AuthUtil.getUserId());
        cloudPaymentList.setRepaymentName(AuthUtil.getUserName());
        cloudPaymentList.setType(CloudConstant.CLOUD_PAY_ON_TIME);
        cloudPaymentList.setCostDifference(costDifferenceSum);
        cloudPaymentList.setAmountDue(amount.add(cloudPaymentList.getCostDifference()));
        cloudPaymentListService.saveOrUpdate(cloudPaymentList);
        //云信付款明细更新信息
        cloudPaymentDetailService.saveOrUpdateBatch(cloudPaymentDetailList);
        return cloudPaymentList.getId();
    }

//
//	@Override
//	public List<CreditReport> cloudAssetsReport(Integer type, Integer year, String month, String tentId) {
//		type = 3;
//		List<String> timeList = new ArrayList<>(32);
//		timeList = getDateList(month, year);
//
//		ArrayList<CreditReport> creditReports = new ArrayList<>();
//		for (String time : timeList) {
//
//			//查询融资企业云信新增信贷
//			CreditReportDto dto = new CreditReportDto();
//			dto.setQueryTime(time);
//			dto.setTenantId(tentId);
//			List<CloudFinancing> newCloudList = cloudFinancingMapper.newLoansList(dto);
//
//			//云信新增回款订单
//			List<CloudPaymentList> payCloudList = cloudPaymentListMapper.payList(time, tentId);
//			//云信逾期待回订单
//			List<CloudAssets> overDueCloud = cloudAssetsMapper.overdueList(time, 0L, tentId);
//
//			Integer numberOfLoans = newCloudList.size();
//			Integer numberOfPay = payCloudList.size();
//			Integer overDueNum = overDueCloud.size();
//
//			BigDecimal payAmount = payCloudList.stream().map(CloudPaymentList::getActualAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//			BigDecimal overDueMoney = overDueCloud.stream().map(CloudAssets::getCloudBillAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//			//云信开单金额
//			BigDecimal loanAmount = newCloudList.stream().map(CloudFinancing::getLoanMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//			CreditReport creditReport = CreditReport.builder()
//				.queryTime(time)
//				.numberOfLoans(numberOfLoans)//新增放款笔数
//				.loanAmount(loanAmount)//开单金额
//				.numberOfPay(numberOfPay)//已还完笔数
//				.payAmount(payAmount)//实还金额
//				.overDueNum(overDueNum)//逾期件数
//				.overDueMoney(overDueMoney)//逾期金额
//				.build();
//			creditReports.add(creditReport);
//		}
//
//		return creditReports;
//	}

    /**
     * 资产管理新增上链哈希字段
     *
     * @param cloudAssets 查询条件
     * @param query       查询数据
     * @return CloudAssetsVO 资产信息
     */
    @Override
    public IPage<CloudAssetsVO> selectCloudBlockAddress(CloudAssets cloudAssets, Query query) {
        cloudAssets.setType(0);
        IPage<CloudAssets> pages = this.page(Condition.getPage(query), Condition.getQueryWrapper(cloudAssets).lambda().orderByDesc(CloudAssets::getCreateTime));
        Page<CloudAssetsVO> cloudAssetsVOPage = CloudAssetsWrapper.build().pageVO(pages);
        if (CollUtil.isNotEmpty(pages.getRecords())) {
            //获取云信编号
            List<String> collect = pages.getRecords().parallelStream().map(CloudAssets::getCloudCode).collect(Collectors.toList());
            //根据云信编号in区块链信息
            List<BlockChainInformation> list = blockChainInformationService.list(Wrappers.<BlockChainInformation>lambdaQuery()
                    .in(BlockChainInformation::getNextCloudCode, collect).or().in(BlockChainInformation::getCloudCoreCode, collect));
            if (CollUtil.isNotEmpty(list)) {
                Map<String, String> nextCloudCodeMap = list.parallelStream().filter(e -> StrUtil.isNotBlank(e.getNextCloudCode())).collect(Collectors.toMap(BlockChainInformation::getNextCloudCode, BlockChainInformation::getTransactionAddress));
                Map<String, String> cloudCoreCodeMap = list.parallelStream().filter(e -> StrUtil.isBlank(e.getNextCloudCode())).collect(Collectors.toMap(BlockChainInformation::getCloudCoreCode, BlockChainInformation::getTransactionAddress));
                for (CloudAssetsVO record : cloudAssetsVOPage.getRecords()) {
                    String transactionAddress = null;
                    if (cloudCoreCodeMap.containsKey(record.getCloudCode())) {
                        transactionAddress = cloudCoreCodeMap.get(record.getCloudCode());
                    } else if (nextCloudCodeMap.containsKey(record.getCloudCode())) {
                        transactionAddress = nextCloudCodeMap.get(record.getCloudCode());
                    }
                    record.setTransactionAddress(transactionAddress);
                }
            }
        }

        return cloudAssetsVOPage;
    }

    @Override
    public void selectAssetsBankInfo(List<CloudPaymentDetailVO> cloudPaymentDetailVOS) {
        for (int i = 0; i < cloudPaymentDetailVOS.size(); i++) {
            CloudPaymentDetailVO cloudPaymentDetailVO = cloudPaymentDetailVOS.get(i);
            String cloudCode = cloudPaymentDetailVO.getCloudCode();
            CloudAssets cloudAssets = getOne(Wrappers.<CloudAssets>lambdaQuery().eq(CloudAssets::getCloudCode, cloudCode));
            cloudPaymentDetailVO.setBankDeposit(cloudAssets.getBankDeposit());
            cloudPaymentDetailVO.setBankCardNo(cloudAssets.getBankCardNo());
            cloudPaymentDetailVO.setCostDifference(cloudAssets.getAmount().subtract(cloudPaymentDetailVO.getAmount()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean financingCloudEnd(CloudFinancingDTO cloudFinancing, int type) {
        CloudAssets cloudAssets = this.getOne(Wrappers.<CloudAssets>lambdaQuery().eq(CloudAssets::getCloudCode, cloudFinancing.getCloudCode()));
        if (cloudAssets.getStatus() != CloudConstant.CLOUD_ASSETS_STATUS_ZERO) {
            throw new ServiceException("资产状态必须为待签收");
        }

        //查询资产关联表。
        CloudTrackCode cloudTrackCode = cloudTrackCodeService.getOne(Wrappers.<CloudTrackCode>lambdaQuery().eq(CloudTrackCode::getCloudCode, cloudAssets.getCloudCode()));

        if (ObjectUtil.isEmpty(cloudTrackCode)) {
            throw new ServiceException("资产关联信息查询不到，请联系管理员！");
        }
        BigDecimal amount = null;
        /**
         * 签收状态，修改云信状态为持单中
         */
        if (CloudConstant.CLOUD_ASSETS_TYPE_ONE == type) {

            //为了添加开单付息功能，这里拆单时需要查询付息方式，来计算不同的拆单金额
            //拆单金额
            amount = cloudAssets.getAmount();
            if (cloudAssets.getFinancingModel() == CloudConstant.CLOUD_FINANCING_MODEL_ZERO) {
                CloudFinancingCoreEnterprisePayment cloudFinancingCoreEnterprisePayment = new CloudFinancingCoreEnterprisePayment();
                cloudFinancingCoreEnterprisePayment.setCloudCode(cloudFinancing.getCloudCode());
                cloudFinancingCoreEnterprisePayment.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
                cloudFinancingCoreEnterprisePayment.setFinancingMoney(cloudFinancing.getFinancingMoney());
                cloudFinancingCoreEnterprisePayment.setCloudBillAmount(cloudFinancing.getCloudFinanceRateInterestSum());
                cloudFinancingCoreEnterprisePayment.setFinancingNo(cloudFinancing.getFinancingNo());
                cloudFinancingCoreEnterprisePayment.setCloudProductName(cloudFinancing.getCloudProductName());
                BigDecimal costDiff = cloudFinancing.getCloudFinanceRateInterestSum().subtract(cloudAssets.getAmount());
                cloudFinancingCoreEnterprisePayment.setCostDifference(costDiff);
                cloudFinancingCoreEnterprisePaymentService.save(cloudFinancingCoreEnterprisePayment);
                //融资成功后对公共还款计划表进行操作
                CloudAssets cloudAssetsIouNo = getOne(Wrappers.<CloudAssets>lambdaQuery().eq(CloudAssets::getCloudCode, cloudTrackCode.getCloudCoreCode()));
                String iouNo = cloudAssetsIouNo.getIouNo();
                List<LoanManageRepaymentPlan> list = loanManageRepaymentPlanService.list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery().eq(LoanManageRepaymentPlan::getIouNo, iouNo));
                if (Objects.nonNull(list) && list.size() > 0) {
                    Long planId = list.get(0).getId();
                    repaymentBizImplService.addExInterest(planId, costDiff);
                }
            }
            //云信付款明细
            CloudPaymentDetail cloudPaymentDetail = new CloudPaymentDetail();
            //云信流转编号
            String circulationCode = CodeUtil.generateCode(CodeEnum.CLOUD_CIRCULATION_NO);
            cloudPaymentDetail.setCloudTransferCode(circulationCode);
            cloudPaymentDetail.setCloudCode(cloudAssets.getCloudCode());
            cloudPaymentDetail.setCompanyId(cloudAssets.getUserId());
            cloudPaymentDetail.setAmount(amount);
            cloudPaymentDetail.setType(CloudConstant.CLOUD_TYPE_ONE);
            cloudPaymentDetail.setStatus(CloudConstant.CLOUD_PAYMENT_DETAIL_STATUS_ZERO);
            cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_TWO);
            //云信 拆分状态 修改为已融资
            cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_FINANCING_COMPLETE.getCode());
            //签收日期
            cloudAssets.setReceiveDate(LocalDateTime.now());
            //当前用户名称
            cloudAssets.setUserName(AuthUtil.getUserName());
            //新增云信轨迹图
            CloudTrackRecord cloudTrackRecord = new CloudTrackRecord();
            cloudTrackRecord.setHoldName(cloudAssets.getName());
            cloudTrackRecord.setBillName(cloudAssets.getCompanyName());
            cloudTrackRecord.setAmount(amount);
            cloudTrackRecord.setUserId(AuthUtil.getUserId());

            //从关联表中获取核心企业云信编号
            cloudPaymentDetail.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());

            cloudTrackRecord.setCloudCoreCode(cloudTrackCode.getCloudCoreCode());
            cloudTrackRecord.setCloudCode(cloudTrackCode.getCloudParentCode());
            //如果不是第一次新增云信轨迹图，那么下一个云信编号则需要填写当前进来 的 云信编号
            cloudTrackRecord.setNextCloudCode(cloudAssets.getCloudCode());
            cloudTrackRecord.setType(cloudAssets.getType());
            /*
			  后续云信轨迹图会使用递归循环，所以需要创建一个 父级id，先获取该云信轨迹上一级id
			  核心企业云信编号是否跟云信编号一致，一致则根据云信编号 查询云信轨迹图，获取到id，如果不一致则根据下一级云信编号查询 云信轨迹图
			*/
            BlockChainCloudReceive blockChainCloudReceive = new BlockChainCloudReceive();
            assignTrackRecord(cloudAssets, cloudTrackCode, cloudTrackRecord, blockChainCloudReceive);

            //区块链上链 --->>云信签收流程
            String value = ParamCache.getValue(CommonConstant.BLOCK_ENABLED);
            if (StrUtil.isNotBlank(value) && CommonConstant.YES == Integer.parseInt(value)) {
                blockChainCloudReceive.setAmount(amount);
                blockChainCloudReceive.setStartDate(cloudAssets.getStartDate());
                blockChainCloudReceive.setEndDate(cloudAssets.getEndDate());
                blockChainCloudReceive.setCompanyId(cloudAssets.getCompanyId());
                blockChainCloudReceive.setHoldId(cloudAssets.getUserId());
                blockChainCloudReceive.setHoldName(cloudTrackRecord.getHoldName());
                blockChainCloudReceive.setBillName(cloudTrackRecord.getBillName());
                blockChainCloudReceive.setContent(cloudTrackRecord.getContent());
                blockChainCloudReceive.setType(CloudConstant.CLOUD_TYPE_ONE);
                blockChainCloudReceive.setCloudCode(cloudTrackRecord.getCloudCode());
                blockChainCloudReceive.setCloudCoreCode(cloudTrackRecord.getCloudCoreCode());
                blockChainCloudReceive.setNextCloudCode(cloudTrackRecord.getNextCloudCode());
                blockChainCloudReceive.setStartDate(cloudTrackRecord.getStartDate());
                blockChainCloudReceive.setUserId(cloudTrackRecord.getUserId());
                blockChainCloudReceive.setCoreCompanyId(cloudAssets.getCoreCompanyId());
                blockChainCloudReceive.setStatus(BlockChainEnum.CLOUD_SIGN.getCode());
                blockChainInformationService.saveBlockChain(blockChainCloudReceive);
            }

            //通用方法 -- 判断 资产下面的子级状态
            commonSaveTrackRecord(cloudAssets.getCloudCode(), type, cloudAssets.getAmount());
            cloudTrackRecord.setStartDate(cloudAssets.getStartDate());
            cloudTrackRecord.setEndDate(cloudAssets.getEndDate());
            //云信轨迹图
            cloudTrackRecordService.save(cloudTrackRecord);
            //云信付款明细
            cloudPaymentDetailService.save(cloudPaymentDetail);
            //插入付息明细表数据


        } else {
            cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_ONE);
            //云信 拆分状态 修改为已融资
            cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_FINANCING_FAILURE.getCode());
            commonSaveTrackRecord(cloudAssets.getCloudCode(), type, cloudAssets.getAmount());
        }

        return this.saveOrUpdate(cloudAssets);
    }

    @Override
    public List<CloudAssetsVO> selectTransferBeforeCloudCore(List<CloudAssetsVO> cloudAssetsVOList) {
        List<String> cloudCodeS = cloudAssetsVOList.stream().map(CloudAssets::getCloudCode).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(cloudCodeS)) {
            List<CloudTrackCode> cloudTrackCodes = cloudTrackCodeService.list(Wrappers.<CloudTrackCode>lambdaQuery().in(CloudTrackCode::getCloudCode, cloudCodeS));
            for (CloudAssetsVO cloudAssetsVO : cloudAssetsVOList) {
                for (CloudTrackCode cloudTrackCode : cloudTrackCodes) {
                    if (cloudAssetsVO.getCloudCode().equals(cloudTrackCode.getCloudCode())) {
                        cloudAssetsVO.setParentCloudNo(cloudTrackCode.getCloudParentCode());
                    }
                }
            }
        }
        return cloudAssetsVOList;
    }

    @Override
    public IPage<CloudAssetsVO> getYunxin(Long companyId, Query query) {
        List<CloudPay> list1 = SpringUtil.getBean(ICloudPayService.class).lambdaQuery()
                .eq(CloudPay::getFundId, companyId).list();
        if (ObjectUtil.isEmpty(list1)) {
            return org.springblade.common.utils.Condition.getPage(query);
        }
        List<String> map = StreamUtil.map(list1, CloudPay::getCloudCode);
        IPage<CloudAssets> page = SpringUtil.getBean(ICloudAssetsService.class).lambdaQuery().in(CloudAssets::getCloudCode, map)
                .orderByDesc(CloudAssets::getCreateTime).page(org.springblade.common.utils.Condition.getPage(query));
        Page<CloudAssetsVO> cloudAssetsVOPage = CloudAssetsWrapper.build().pageVO(page);

        return cloudAssetsVOPage;
    }

    private void saveCloudRepaymentPlan(CloudAssets cloudAssets) {
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(cloudAssets.getEnterpriseId()).getData();
        CustomerGoods customerGoods = customerGoodsService.getByGoodsIdAndCustomerId(enterpriseQuota.getGoodsId(), cloudAssets.getCoreCompanyId());
        LoanManageIou loanManageIou = new LoanManageIou();
        loanManageIou.setGoodsType(GoodsEnum.CLOUD_CREDIT.getCode());
        loanManageIou.setFinanceNo(cloudAssets.getCloudCode());
        loanManageIou.setUserId(cloudAssets.getUserId());
        loanManageIou.setIouAmount(cloudAssets.getCloudBillAmount());
        loanManageIou.setFinanceApplyId(cloudAssets.getId());
        int days = org.springblade.core.tool.utils.DateUtil.between(cloudAssets.getStartDate(), cloudAssets.getEndDate()).getDays();
        loanManageIou.setPeriod(days);
        loanManageIou.setPeriodUnit(GoodsEnum.DAY.getCode());
        loanManageIou.setRepaymentType(2);
        loanManageIou.setExpireTime(cloudAssets.getEndDate());
        loanManageIou.setIouType(IouStatusEnum.UN_SETTLE.getCode());
        loanManageIou.setIouNo(CodeUtil.generateCode(CodeEnum.IOU_STOCK_CODE));
        loanManageIou.setLoanTime(LocalDate.now());
        loanManageIou.setCapitalId(customerGoods.getCapitalId());
        loanManageIou.setGoodsId(customerGoods.getGoodsId());
        loanManageIouService.save(loanManageIou);
        RepaymentPlanCal repaymentPlanCal = new RepaymentPlanCal();
        List<StagRecordVO> stagRecordVOS = new ArrayList<>();
        StagRecordVO stagRecordVO = new StagRecordVO();
        stagRecordVO.setTerm("1");
        stagRecordVO.setMonthlyInterest(BigDecimal.ZERO);
        stagRecordVO.setMonthlySupply(cloudAssets.getCloudBillAmount());
        stagRecordVO.setDiscountAmount(BigDecimal.ZERO);
        stagRecordVO.setMonthlyPrincipal(cloudAssets.getCloudBillAmount());
        stagRecordVO.setStartTime(cloudAssets.getStartDate());
        stagRecordVO.setRefundTime(cloudAssets.getEndDate());
        stagRecordVOS.add(stagRecordVO);
        repaymentPlanCal.setStagRecords(stagRecordVOS);
        repaymentPlanCal.setTotal(cloudAssets.getCloudBillAmount());
        repaymentPlanCal.setDayRate(cloudAssets.getFinancingApr().divide(new BigDecimal(365), 2, RoundingMode.HALF_UP));
        repaymentPlanCal.setYearRate(cloudAssets.getFinancingApr());

        RepaymentPlanDTO repaymentPlanDTO = repaymentPlanFinanceApplyBizService.saveRepaymentPlan(loanManageIou, repaymentPlanCal, enterpriseQuota.getGoodsId(), cloudAssets.getId(), customerGoods.getGoodsId(), cloudAssets.getFinancingApr());
        String iouNo = repaymentPlanDTO.getLoanManageIou().getIouNo();
        cloudAssets.setIouNo(iouNo);
    }

}
