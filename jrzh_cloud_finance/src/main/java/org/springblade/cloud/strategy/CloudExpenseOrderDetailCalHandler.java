package org.springblade.cloud.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.expense.handler.ExpenseOrderDetailCalHandler;
import org.springblade.expense.handler.ProductExpenseOrderDetailUtils;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
@Service
@RequiredArgsConstructor
public class CloudExpenseOrderDetailCalHandler implements ExpenseOrderDetailCalHandler {
    private final ProductDirector productDirector;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    @Override
    public GoodsEnum support() {
        return GoodsEnum.CLOUD_CREDIT;
    }

    @Override
    public ExpenseRuleDTO reCalculate(String s) {
        return null;
    }

    @Override
    public ExpenseRuleDTO buildFinanceApply(String req) {
        CostCalculusDto costCalculusDto = JSONUtil.toBean(req, CostCalculusDto.class);
        LocalDate startTime = costCalculusDto.getStartTime();
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(costCalculusDto.getEnterpriseQuotaId());
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), startTime, costCalculusDto.getTotalTerm());
        Integer accumulateLoanDay = ProductExpenseOrderDetailUtils.getLoanDay(startTime, LocalDate.now());
//        BigDecimal interestDay = ProductExpenseOrderDetailUtils.getInterestDayByLoanDay(product, accumulateLoanDay);
        Integer loanPeriods = GoodsEnum.TERM.getCode().equals(costCalculusDto.getLoadTermUnit()) ? costCalculusDto.getTotalTerm() : 1;
        StagRecordVO stagRecordVO = costCalculusDto.getStagRecordVO();
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(BigDecimal.valueOf(loanDay))
                .loanDays(BigDecimal.valueOf(costCalculusDto.getLoanDay()))
                .loanPeriods(BigDecimal.valueOf(loanPeriods))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getServiceRate()))
                .dayRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getDailyInterestRate()))
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getAnnualInterestRate())).build();
        if (ObjectUtil.isNotEmpty(stagRecordVO)) {
            //填充本期还款参数
            expenseRuleDTO.setTermPrincipalRepayment(stagRecordVO.getMonthlySupply());
            expenseRuleDTO.setTermInterestAccrualDays(ProductExpenseOrderDetailUtils.getInterestDay(product, stagRecordVO.getStartTime()));
        }
        return expenseRuleDTO;
    }
    private Product getProductByGoodsId(Long goodsId) {
        return productDirector.detailBase(goodsId);
    }
    private EnterpriseQuota getEnterpriseQuota(Long enterpriseQuotaId) {
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(enterpriseQuotaId);
        return enterpriseQuota;

    }


}
