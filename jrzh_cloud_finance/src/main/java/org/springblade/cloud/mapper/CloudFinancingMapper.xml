<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.cloud.mapper.CloudFinancingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cloudFinancingResultMap" type="org.springblade.cloud.entity.CloudFinancing">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="financing_no" property="financingNo"/>
        <result column="assets_id" property="assetsId"/>
        <result column="financing_money" property="financingMoney"/>
        <result column="financing_purpose" property="financingPurpose"/>
        <result column="collection_account" property="collectionAccount"/>
        <result column="contract" property="contract"/>
        <result column="invoice" property="invoice"/>
        <result column="cloud_product_id" property="cloudProductId"/>
        <result column="cloud_product_name" property="cloudProductName"/>
        <result column="repayment_mode" property="repaymentMode"/>
        <result column="load_term" property="loadTerm"/>
        <result column="load_term_unit" property="loadTermUnit"/>
        <result column="expire_time" property="expireTime"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="bond_pay_proportion" property="bondPayProportion"/>
        <result column="daily_interest_rate" property="dailyInterestRate"/>
        <result column="annual_interest_rate" property="annualInterestRate"/>
        <result column="service_rate" property="serviceRate"/>
        <result column="capital_id" property="capitalId"/>
        <result column="apply_user" property="applyUser"/>
        <result column="pass_time" property="passTime"/>
        <result column="repayment_type" property="repaymentType"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="newLoansList" resultType="org.springblade.cloud.entity.CloudFinancing">
        select * from jrzh_cloud_financing
        where loan_time like concat(concat(#{dto.queryTime},'%'))
          and status in (4,9,10,11)
          and is_deleted = 0
        <if test="dto.userId != null and dto.userId !=0">
            and apply_user = #{dto.userId}
        </if>
        <if test="dto.tenantId != null and dto.tenantId != ''">
            and tenant_id = #{dto.tenantId}
        </if>
    </select>

    <select id="newCloudUserByName" resultType="org.springblade.cloud.entity.CloudFinancing">
        SELECT * FROM `jrzh_cloud_financing`
        where loan_time like concat(concat(#{time},'%'))
          and `status` in (4,9,10,11)
          and is_deleted = 0
        <if test="userId != null and userId != 0">
            and apply_user = #{userId}
        </if>
        <if test="tentId != null and tentId != ''">
            and tenant_id = #{tentId}
        </if>
    </select>

    <select id="newCloudUserList" resultType="org.springblade.cloud.entity.CloudFinancing">
        SELECT * FROM `jrzh_cloud_financing`
        where loan_time like concat(concat(#{time},'%'))
        <if test="tentId != null and tentId != ''">
            and tenant_id = #{tentId}
        </if>
          and `status` in (4,9,10,11)
          and is_deleted = 0;
    </select>


    <select id="selectCloudFinancingPage" resultMap="cloudFinancingResultMap">
        select * from jrzh_cloud_financing where is_deleted = 0
    </select>
    <select id="queryCloudFinancingShowChart" resultType="org.springblade.cloud.entity.CloudFinancing">
        SELECT DATE(create_time) as create_time,SUM(financing_money) as financing_money FROM jrzh_cloud_financing where DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[ <= ]]> date(create_time)
        and is_deleted = 0 and status in (9,10,11)
        GROUP BY DATE(create_time) order by create_time
    </select>
    <select id="yesTerDayCloudFinancingIncomeCount" resultType="java.lang.Integer">
        SELECT sum(financing_money) as financing_money FROM jrzh_cloud_financing WHERE TO_DAYS( NOW( ) ) - TO_DAYS( create_time) = 1  and is_deleted = 0 and status in (9,10,11)
    </select>
    <select id="queryCloudFinancingStrokeCountShowChart" resultType="org.springblade.cloud.vo.CloudStrokeCountVO">
        SELECT DATE(create_time) as create_time,count(1) as stroke FROM jrzh_cloud_financing where DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[ <= ]]> date(create_time)
        and is_deleted = 0 and status in (9,10,11)
        GROUP BY DATE(create_time) order by create_time
    </select>
    <select id="queryYesTerDayCloudStrokeCount" resultType="java.lang.Integer">
        SELECT count(1) as stroke FROM jrzh_cloud_financing WHERE TO_DAYS( NOW( ) ) - TO_DAYS( create_time) = 1  and is_deleted = 0 and status in (9,10,11)
    </select>


    <select id="payableCloudByDtoList" resultType="org.springblade.cloud.entity.CloudFinancing">
        select cloud.* from jrzh_cloud_financing as cloud
                    LEFT JOIN jrzh_cloud_assets  as assets
                            on cloud.assets_id = assets.id
        where assets.end_date like concat(concat(#{dto.queryTime},'%'))
          and cloud.`status`in(4,9,10,11)
          and cloud.is_deleted = 0
        <if test="dto.userId != null and dto.userId != 0">
            and cloud.apply_user = #{dto.userId}
        </if>
        <if test="dto.tenantId != null and dto.tenantId != ''">
            and cloud.tenant_id = #{dto.tenantId}
        </if>

    </select>

    <select id="listCloudByLtDateStr" resultType="org.springblade.cloud.entity.CloudFinancing">
        SELECT * FROM `jrzh_cloud_financing` WHERE loan_time &lt;  #{time}
        <if test="userId != null and userId != 0">
            and apply_user = #{userId}
        </if>
        <if test="tentId != null and tentId != ''">
            and tenant_id = #{tentId}
        </if>
        and status in(4,9,10,11)
        and is_deleted = 0


    </select>

    <update id="cloudUpdateBatch">
        update jrzh_cloud_financing set
        <trim prefix="status=case" suffix="end,">
            <foreach collection="cloudFinancingList" item="item" index="index">
                when id = #{item.id} then #{itme.status}
            </foreach>
        </trim>
        <where>
            <foreach collection="cloudFinancingList" item="item" separator="or">
                id=#{item.id}
            </foreach>
        </where>
    </update>


</mapper>
