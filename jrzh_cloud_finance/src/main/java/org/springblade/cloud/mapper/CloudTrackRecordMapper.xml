<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.cloud.mapper.CloudTrackRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cloudTrackRecordResultMap" type="org.springblade.cloud.entity.CloudTrackRecord">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="parent_id" property="parentId"/>
        <result column="hold_name" property="holdName"/>
        <result column="bill_name" property="billName"/>
        <result column="amount" property="amount"/>
        <result column="cloud_core_code" property="cloudCoreCode"/>
        <result column="cloud_code" property="cloudCode"/>
        <result column="next_cloud_code" property="nextCloudCode"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="user_id" property="userId"/>
        <result column="content" property="content"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.cloud.vo.CloudTrackRecordTreeVO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="hold_name" property="holdName"/>
        <result column="bill_name" property="billName"/>
        <result column="amount" property="amount"/>
        <result column="cloud_code" property="cloudCode"/>
        <result column="next_cloud_code" property="nextCloudCode"/>
        <result column="end_date" property="endDate"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
    </resultMap>


    <select id="selectCloudTrackRecordPage" resultMap="cloudTrackRecordResultMap">
        select * from jrzh_cloud_track_record where is_deleted = 0
    </select>

    <select id="selectTreeCloudTrack" resultMap="treeNodeResultMap">
        select id, parent_id,hold_name,bill_name,amount,cloud_code,next_cloud_code,end_date,content,type, hold_name as title, id as "value", id as "key" from jrzh_cloud_track_record where is_deleted = 0
           and cloud_core_code = #{param1}
    </select>

    <select id="selectTreeCloudTrackCode" resultMap="treeNodeResultMap">
        select cloud.id,block.transaction_address, cloud.parent_id,cloud.hold_name,cloud.bill_name,cloud.amount,cloud.cloud_code,cloud.next_cloud_code,cloud.end_date,cloud.content,cloud.type, cloud.hold_name as title, cloud.id as "value", cloud.id as "key" from jrzh_cloud_track_record cloud
        inner join jrzh_block_chain_information block on block.cloud_core_code = cloud.cloud_core_code
        where is_deleted = 0
          and cloud.cloud_core_code = #{param1}
    </select>

</mapper>
