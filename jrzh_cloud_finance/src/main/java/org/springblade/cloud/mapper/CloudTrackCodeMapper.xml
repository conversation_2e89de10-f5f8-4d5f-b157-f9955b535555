<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.cloud.mapper.CloudTrackCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cloudTrackCodeResultMap" type="org.springblade.cloud.entity.CloudTrackCode">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="cloud_core_code" property="cloudCoreCode"/>
        <result column="cloud_parent_code" property="cloudParentCode"/>
        <result column="cloud_code" property="cloudCode"/>
        <result column="assets_id" property="assetsId"/>
        <result column="user_id" property="userId"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectCloudTrackCodePage" resultMap="cloudTrackCodeResultMap">
        select * from jrzh_cloud_track_code where is_deleted = 0
    </select>

</mapper>
