<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.cloud.mapper.CloudAssetsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cloudAssetsResultMap" type="org.springblade.cloud.entity.CloudAssets">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="cloud_code" property="cloudCode"/>
        <result column="company_name" property="companyName"/>
        <result column="name" property="name"/>
        <result column="user_id" property="userId"/>
        <result column="financing_model" property="financingModel"/>
        <result column="cloud_bill_amount" property="cloudBillAmount"/>
        <result column="amount" property="amount"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="remark" property="remark"/>
        <result column="receive_date" property="receiveDate"/>
        <result column="user_name" property="userName"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="unCloudpay" resultType="org.springblade.cloud.entity.CloudAssets">
        select cloud.* from jrzh_cloud_assets as cloud
                                LEFT JOIN jrzh_customer_info as info
                                          on cloud.company_id = info.company_id
        where  cloud.`status` in(2, 4, 5, 6, 8)
        <if test="tentId != null and tentId != ''">
            and cloud.tenant_id = #{tentId}
        </if>
          and	cloud.end_date like concat(concat(#{time},'%'))
          and cloud.is_deleted = 0
          and info.ent_auth_type = 3
    </select>




    <select id="selectCloudAssetsPage" resultMap="cloudAssetsResultMap">
        select *
        from jrzh_cloud_assets
        where is_deleted = 0
    </select>

    <select id="payCloud" resultType="org.springblade.cloud.entity.CloudAssets">
        select cloud.* from jrzh_cloud_assets as cloud
                                LEFT JOIN jrzh_customer_info as info
                                          on cloud.company_id = info.company_id
        where  cloud.`status` in(7,9)
          and	cloud.end_date like concat(concat(#{time},'%'))
          and cloud.is_deleted = 0
          and info.ent_auth_type = 3

    </select>

    <select id="unCloudpayByNameList" resultType="org.springblade.cloud.entity.CloudAssets">
        select cloud.* from jrzh_cloud_assets as cloud
                                LEFT JOIN jrzh_customer_info as info
                                          on cloud.company_id = info.company_id
        where  cloud.`status` in(2, 4, 5, 6, 8)
          and	cloud.end_date like concat(concat(#{time},'%'))
          and cloud.is_deleted = 0
          and info.ent_auth_type = 3
        <if test="userId != null and userId != 0">
            and user_id = #{userId}
        </if>
        <if test="tentId != null and tentId != ''">
            and cloud.tenant_id = #{tentId}
        </if>

    </select>

    <select id="overdueList" resultType="org.springblade.cloud.entity.CloudAssets">
        select * from jrzh_cloud_assets
            where  status = 8
        and	end_date like concat(concat(#{time},'%'))
        and is_deleted = 0
        <if test="userId != null and userId != 0">
            and user_id = #{userId}
        </if>
        <if test="tentId != null and tentId != ''">
            and tenant_id = #{tentId}
        </if>

    </select>
    <select id="selectCloudAssetsStatusIsTwoOrFour" resultType="org.springblade.cloud.entity.CloudAssets">
        SELECT
            ca.*
        FROM
            jrzh_cloud_track_code tc
                LEFT JOIN
            jrzh_cloud_assets ca
            ON tc.cloud_code=ca.cloud_code
        WHERE
            tc.cloud_core_code=#{cloudCoreCode}
          and
            ca.`status` in (2,4)

        ORDER BY
            tc.create_time desc
            LIMIT 1
    </select>


</mapper>
