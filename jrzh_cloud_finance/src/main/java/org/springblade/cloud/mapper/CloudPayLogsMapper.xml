<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.cloud.mapper.CloudPayLogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cloudPayLogsResultMap" type="org.springblade.cloud.entity.CloudPayLogs">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="ip" property="ip"/>
        <result column="address" property="address"/>
        <result column="browser" property="browser"/>
        <result column="computer" property="computer"/>
        <result column="cloud_code" property="cloudCode"/>
        <result column="user_agent" property="userAgent"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectCloudPayLogsPage" resultMap="cloudPayLogsResultMap">
        select * from jrzh_cloud_pay_logs where is_deleted = 0
    </select>

</mapper>
