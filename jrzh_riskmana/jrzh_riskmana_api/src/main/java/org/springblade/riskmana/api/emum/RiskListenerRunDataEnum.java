package org.springblade.riskmana.api.emum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运算符号
 */
public interface RiskListenerRunDataEnum {
    /**
     * 授信数据服务
     */
    String RISK_QUOTA_CREDIT_SERVICE = "RISK_QUOTA_CREDIT_SERVICE";

    @AllArgsConstructor
    @Getter
    enum Type implements RiskListenerRunDataEnum {
        /**
         * 授信数据,补充资料,征信数据运行
         */
        RISK_QUOTA_CREDIT(RISK_QUOTA_CREDIT_SERVICE, "授信数据,补充资料,征信数据运行"),
        ;
        /**
         * 服务名
         */
        final private String serviceName;
        /**
         * 描述
         */
        final private String desc;
    }
}

