package org.springblade.riskmana.api.feign;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.riskmana.api.entity.RatingRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Description:
 * @date 2023年05月26日 16:27
 */
@FeignClient(name="remoteRatingRecordService",url = "${feign.url}")
public interface RemoteRatingRecordService {

    String PATH = CommonConstant.BLADE_FEIGN + CommonConstant.BLADE_RISKMANA + "/riskmana/ratingrecord";

    /**
     * @return org.springblade.core.tool.api.R<org.springblade.riskmana.api.entity.RatingRecord>
     * @Description: 根据用户及产品查询评级记录
     * @Author: wujm
     * @Date: 2023/5/26 16:32
     **/
    @GetMapping(PATH + "/userorgoods")
    R<RatingRecord> getRatingRecordByUserIdAndProductId(@RequestParam(value = "userId") Long userId, @RequestParam(value = "goodsId") Long goodsId);

}
