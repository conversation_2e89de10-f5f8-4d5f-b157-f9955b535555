/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评级记录实体类
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Data
@TableName("jrzh_riskmana_rating_record")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RatingRecord对象", description = "评级记录")
public class RatingRecord extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 评分编号
	 */
	@ApiModelProperty(value = "评分编号")
	private String recordNo;
	/**
	 * 评分模板
	 */
	@ApiModelProperty(value = "评分模板")
	private String ratingName;
	/**
	 * 客户id
	 */
	@ApiModelProperty(value = "客户id")
	private Long clientId;
	/**
	 * 最终得分
	 */
	@ApiModelProperty(value = "最终得分")
	private BigDecimal finalScore;
	/**
	 * 信用评级
	 */
	@ApiModelProperty(value = "信用评级")
	private String grade;
	/**
	 * 到期时间
	 */
	@ApiModelProperty(value = "到期时间")
	private LocalDateTime expireTime;
	/**
	 * 风险限额
	 */
	@ApiModelProperty(value = "风险限额")
	private BigDecimal limitAmount;
	/**
	 * 指标项总分
	 */
	@ApiModelProperty(value = "指标项得分")
	private BigDecimal totalScore;
	/**
	 * 指标项最高分数
	 */
	@ApiModelProperty(value = "指标项最高分数")
	private BigDecimal totalMaxScore;
	/**
	 * 评分模板id
	 */
	@ApiModelProperty(value = "评分模板id")
	private Long ratingId;

	/**
	 * 客户产品id
	 */
	@ApiModelProperty(value = "客户产品id")
	private Long customerGoodsId;
}
