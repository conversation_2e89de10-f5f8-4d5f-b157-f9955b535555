/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.riskmana.api.entity.RiskmanaTemplate;
import org.springblade.riskmana.api.vo.RiskmanaTemplateVO;
import org.springblade.riskmana.core.mapper.RiskmanaTemplateMapper;
import org.springblade.riskmana.core.service.IRiskmanaTemplateService;
import org.springframework.stereotype.Service;

/**
 * 风控模板表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
@RequiredArgsConstructor
public class RiskmanaTemplateServiceImpl extends BaseServiceImpl<RiskmanaTemplateMapper, RiskmanaTemplate> implements IRiskmanaTemplateService {
    @Override
    public IPage<RiskmanaTemplateVO> selectRiskmanaTemplatePage(IPage<RiskmanaTemplateVO> page, RiskmanaTemplateVO riskmanaTemplate) {
        return page.setRecords(baseMapper.selectRiskmanaTemplatePage(page, riskmanaTemplate));
    }

}
