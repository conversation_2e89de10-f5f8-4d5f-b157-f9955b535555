package org.springblade.riskmana.core.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.riskmana.api.entity.RatingRecord;
import org.springblade.riskmana.api.vo.RatingRecordVO;

import java.util.Objects;

/**
 * 评级记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public class RatingRecordWrapper extends BaseEntityWrapper<RatingRecord, RatingRecordVO> {

    public static RatingRecordWrapper build() {
        return new RatingRecordWrapper();
    }

    @Override
    public RatingRecordVO entityVO(RatingRecord RatingRecord) {
        RatingRecordVO RatingRecordVO = Objects.requireNonNull(BeanUtil.copy(RatingRecord, RatingRecordVO.class));

        //User createUser = UserCache.getUser(RatingRecord.getCreateUser());
        //User updateUser = UserCache.getUser(RatingRecord.getUpdateUser());
        //RatingRecordVO.setCreateUserName(createUser.getName());
        //RatingRecordVO.setUpdateUserName(updateUser.getName());

        return RatingRecordVO;
    }
}
