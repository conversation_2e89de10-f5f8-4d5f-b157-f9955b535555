/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.config.mybatis.batch.BatchMapper;
import org.springblade.riskmana.api.entity.RatingRule;
import org.springblade.riskmana.api.vo.RatingRuleVO;

import java.util.List;

/**
 * 评分规则 Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
public interface RatingRuleMapper extends BatchMapper<RatingRule> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param ratingRule
	 * @return
	 */
	List<RatingRuleVO> selectRatingRulePage(IPage page,@Param("ratingRule") RatingRuleVO ratingRule);


	@Delete("update jrzh_riskmana_rating_rule set is_deleted=1")
	boolean deleteRealAll();

	@Delete("update jrzh_riskmana_rating_rule set is_deleted=1 where id in(#{ratingId})")
	boolean deleteRealByIds(String ids);
}
