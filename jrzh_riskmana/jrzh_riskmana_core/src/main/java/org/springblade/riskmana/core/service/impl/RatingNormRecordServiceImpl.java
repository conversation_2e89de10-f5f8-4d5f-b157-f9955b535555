/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.riskmana.api.entity.RatingNormRecord;
import org.springblade.riskmana.api.vo.RatingNormRecordVO;
import org.springblade.riskmana.core.mapper.RatingNormRecordMapper;
import org.springblade.riskmana.core.service.IRatingNormRecordService;
import org.springblade.riskmana.core.wrapper.RatingNormRecordWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指标项分数明细 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
public class RatingNormRecordServiceImpl extends BaseServiceImpl<RatingNormRecordMapper, RatingNormRecord> implements IRatingNormRecordService {

	@Override
	public IPage<RatingNormRecordVO> selectRatingNormRecordPage(IPage<RatingNormRecordVO> page, RatingNormRecordVO ratingNormRecord) {
		return page.setRecords(baseMapper.selectRatingNormRecordPage(page, ratingNormRecord));
	}
	@Override
	public List<RatingNormRecord> listByRecordId(List<Long> recordId) {
		return list(Wrappers.<RatingNormRecord>lambdaQuery().in(RatingNormRecord::getRecoreId, recordId));
	}

	@Override
	public List<RatingNormRecordVO> listVOByRecordId(Long recordId) {
		List<RatingNormRecord> list = list(Wrappers.<RatingNormRecord>lambdaQuery().in(RatingNormRecord::getRecoreId, recordId));
		List<RatingNormRecordVO> ratingNormRecords = RatingNormRecordWrapper.build().listVO(list);
		return ratingNormRecords;
	}

}
