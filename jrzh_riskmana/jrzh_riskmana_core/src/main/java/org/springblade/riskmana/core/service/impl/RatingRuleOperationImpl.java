/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.riskmana.api.entity.RatingRule;
import org.springblade.riskmana.core.mapper.RatingRuleMapper;
import org.springblade.riskmana.core.service.TemplateOperationInterceptor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 评分规则 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Service
@AllArgsConstructor
public class RatingRuleOperationImpl extends BaseServiceImpl<RatingRuleMapper, RatingRule> implements TemplateOperationInterceptor {

    @Override
    public void offShelfTemplate(List<Long> templateIds) {
        List<Long> ruleIds = list(new LambdaQueryWrapper<RatingRule>().in(RatingRule::getTemplateId, templateIds))
                .stream().map(RatingRule::getId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(ruleIds)) {
            return;
        }
        boolean update = update(new UpdateWrapper<RatingRule>().lambda()
                .set(RatingRule::getStatus, CommonConstant.CLOSESTATUS)
                .set(RatingRule::getTemplateId, null)
                .in(RatingRule::getId, ruleIds));
    }
}
