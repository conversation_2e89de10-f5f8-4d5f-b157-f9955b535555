/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.controller.back;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.Condition;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.modules.othersapi.riskordertwo.connector.RiskTwoConnector;
import org.springblade.modules.othersapi.riskordertwo.dto.RiskTwoQuotaProcessDTO;
import org.springblade.othersapi.core.utils.HttpOtherApiUtils;
import org.springblade.riskmana.api.dto.RatingRecordDTO;
import org.springblade.riskmana.api.dto.RiskApplyProcessParam;
import org.springblade.riskmana.api.entity.RiskmanaApply;
import org.springblade.riskmana.api.vo.RiskmanaApplyVO;
import org.springblade.riskmana.core.service.IRiskmanaApplyService;
import org.springblade.riskmana.core.wrapper.RiskmanaApplyWrapper;
import org.springframework.web.bind.annotation.*;

/**
 * 风控审批 控制器
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("riskmana/riskmanaApply")
@Api(value = "风控审批", tags = "风控审批接口")
public class RiskmanaApplyController extends BladeController {

    private final IRiskmanaApplyService riskmanaApplyService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入riskmanaApply")
    public R<RiskmanaApplyVO> detail(RiskmanaApply riskmanaApply) {
        RiskmanaApply detail = riskmanaApplyService.getOne(Condition.getQueryWrapper(riskmanaApply).lambda().orderByDesc(RiskmanaApply::getCreateTime));
        return R.data(RiskmanaApplyWrapper.build().entityVO(detail));
    }

    /**
     * 分页 风控审批
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入riskmanaApply")
    public R<IPage<RiskmanaApplyVO>> list(RiskmanaApply riskmanaApply, Query query) {
        IPage<RiskmanaApply> pages = riskmanaApplyService.page(Condition.getPage(query), Condition.getQueryWrapper(riskmanaApply).lambda().orderByDesc(RiskmanaApply::getCreateTime));
        return R.data(RiskmanaApplyWrapper.build().pageVO(pages));
    }



    /**
     * 风控订单申请
     */
    @PostMapping("/riskApply")
    public R<RiskmanaApply> riskApply(@RequestBody RatingRecordDTO ratingRecord) {
        return R.data(riskmanaApplyService.riskApply(ratingRecord));
    }

    /**
     * 风控订单通过
     */
    @PostMapping("/passApply")
    public R passApply(@RequestBody RiskApplyProcessParam riskApplyProcessParam) {
        riskmanaApplyService.passApplyQuota(riskApplyProcessParam.getRiskApplyId(), riskApplyProcessParam.getFinalApproveAmount(), riskApplyProcessParam.getProcessInstanceId());
        return R.status(true);
    }

    /**
     * 风控订单终止
     */
    @PostMapping("/terminateApplyQuota")
    public R terminateApplyQuota(@RequestBody RiskApplyProcessParam riskApplyProcessParam) {
        riskmanaApplyService.terminateApplyQuota(riskApplyProcessParam.getRiskApplyId(), riskApplyProcessParam.getProcessInstanceId());
        return R.status(true);
    }
    /**
     * 下载报告
     */
    @GetMapping("/downLoadReport")
    public R downLoadReport(@RequestParam(required = true) Long riskId) {
        return riskmanaApplyService.downLoanReport(riskId);
    }
    /**
     * 风控系统2.0异步通知
     */
    @PostMapping("/risk-order-two/notify/{tenantId}")
    public R riskOrderTwoNotify(@RequestBody JSONObject object, @PathVariable(value = "tenantId") String tenantId) {
        RiskTwoQuotaProcessDTO riskTwoQuotaProcessDTO = JSONUtil.toBean(object, RiskTwoQuotaProcessDTO.class, true);
        TenantBroker.runAs(tenantId, e -> {
            HttpOtherApiUtils.callable(() -> {
                riskmanaApplyService.riskOrderTwoNotify(riskTwoQuotaProcessDTO);
                return "";
            }, "额度审批状态变更通知", RiskTwoConnector.INTER_FACE_NAME, "");
        });
        return R.data(true);
    }
}
