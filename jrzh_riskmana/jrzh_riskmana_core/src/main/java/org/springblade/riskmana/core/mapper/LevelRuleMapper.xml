<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.riskmana.core.mapper.LevelRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="levelRuleResultMap" type="org.springblade.riskmana.api.entity.LevelRule">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="rating_id" property="ratingId"/>
        <result column="grade" property="grade"/>
        <result column="compare_value" property="compareValue"/>
        <result column="score_type" property="scoreType"/>
        <result column="score_left" property="scoreLeft"/>
        <result column="score_right" property="scoreRight"/>
        <result column="limit_amount" property="limitAmount"/>
    </resultMap>


    <select id="selectLevelRulePage" resultMap="levelRuleResultMap">
        select * from jrzh_riskmana_level_rule where is_deleted = 0
    </select>

</mapper>
