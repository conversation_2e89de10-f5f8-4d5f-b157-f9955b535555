/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.riskmana.api.dto.RiskmanaTemplateDTO;
import org.springblade.riskmana.api.entity.RiskmanaNormTemplate;
import org.springblade.riskmana.api.vo.RiskmanaNormTemplateVO;

import java.util.List;

/**
 * 风控模板指标中间表 服务类
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
public interface IRiskmanaNormTemplateService extends BaseService<RiskmanaNormTemplate> {

    /**
     * 自定义分页
     *
     * @param page
     * @param riskmanaNormTemplate
     * @return
     */
    IPage<RiskmanaNormTemplateVO> selectRiskmanaNormTemplatePage(IPage<RiskmanaNormTemplateVO> page, RiskmanaNormTemplateVO riskmanaNormTemplate);

    List<RiskmanaNormTemplate> listByTemplateId(Long templateId);

    /**
     * 根据模板id进行物理删除
     *
     * @return
     */
    boolean realDeleteByTemplateId(Long templateId);

    /**
     * 保存指标模板
     *
     * @param template
     * @return
     */
    boolean saveTemplate(RiskmanaTemplateDTO template);

    boolean updateTemplate(RiskmanaTemplateDTO template);

    boolean deleteTemplate(List<Long> ids);

    /**
     * 批量启用
     *
     * @param ids
     */
    Boolean batchEnabledTemplate(String ids);

    void batchDisabledTemplate(List<Long> templateIds);
}
