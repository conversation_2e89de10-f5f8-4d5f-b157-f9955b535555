/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.riskmana.api.entity.ScoreRule;
import org.springblade.riskmana.api.vo.ScoreRuleVO;
import org.springblade.riskmana.core.mapper.ScoreRuleMapper;
import org.springblade.riskmana.core.service.IScoreRuleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 得分规则表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class ScoreRuleServiceImpl extends BaseServiceImpl<ScoreRuleMapper, ScoreRule> implements IScoreRuleService {

	@Override
	public IPage<ScoreRuleVO> selectScoreRulePage(IPage<ScoreRuleVO> page, ScoreRuleVO scoreRule) {
		return page.setRecords(baseMapper.selectScoreRulePage(page, scoreRule));
	}

	@Override
	public List<ScoreRule> listByNormIds(List<Long> normIds) {
		return list(Wrappers.<ScoreRule>lambdaQuery().in(ScoreRule::getNormId, normIds));
	}

	@Override
	public boolean realDeleteByNormId(Long normId) {
		return baseMapper.realDelete(normId);
	}

}
