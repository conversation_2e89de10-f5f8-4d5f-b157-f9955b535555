/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.riskmana.core.controller.back;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.Condition;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.riskmana.api.entity.LevelRule;
import org.springblade.riskmana.api.vo.LevelRuleVO;
import org.springblade.riskmana.core.service.ILevelRuleService;
import org.springblade.riskmana.core.wrapper.LevelRuleWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 级别规则 控制器
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_RISKMANA+CommonConstant.WEB_BACK+"/riskmana/levelrule")
@Api(value = "级别规则", tags = "级别规则接口")
public class LevelRuleController extends BladeController {

	private final ILevelRuleService levelRuleService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入levelRule")
   @PreAuth( "hasPermission('riskmana:levelrule:detail') or hasRole('administrator')")
	public R<LevelRuleVO> detail(LevelRule levelRule) {
		LevelRule detail = levelRuleService.getOne(Condition.getQueryWrapper(levelRule));
		return R.data(LevelRuleWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 级别规则
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入levelRule")
   @PreAuth( "hasPermission('riskmana:levelrule:list') or hasRole('administrator')")
	public R<IPage<LevelRuleVO>> list(LevelRule levelRule, Query query) {
		IPage<LevelRule> pages = levelRuleService.page(Condition.getPage(query), Condition.getQueryWrapper(levelRule));
		return R.data(LevelRuleWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 级别规则
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入levelRule")
   @PreAuth( "hasPermission('riskmana:levelrule:page') or hasRole('administrator')")
	public R<IPage<LevelRuleVO>> page(LevelRuleVO levelRule, Query query) {
		IPage<LevelRuleVO> pages = levelRuleService.selectLevelRulePage(Condition.getPage(query), levelRule);
		return R.data(pages);
	}

	/**
	 * 新增 级别规则
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入levelRule")
   @PreAuth( "hasPermission('riskmana:levelrule:save') or hasRole('administrator')")
	public R save(@Valid @RequestBody LevelRule levelRule) {
		return R.status(levelRuleService.save(levelRule));
	}

	/**
	 * 修改 级别规则
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入levelRule")
   @PreAuth( "hasPermission('riskmana:levelrule:update') or hasRole('administrator')")
	public R update(@Valid @RequestBody LevelRule levelRule) {
		return R.status(levelRuleService.updateById(levelRule));
	}

	/**
	 * 新增或修改 级别规则
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入levelRule")
   @PreAuth( "hasPermission('riskmana:levelrule:submit') or hasRole('administrator')")
	public R submit(@Valid @RequestBody LevelRule levelRule) {
		return R.status(levelRuleService.saveOrUpdate(levelRule));
	}


	/**
	 * 删除 级别规则
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
   @PreAuth( "hasPermission('riskmana:levelrule:remove') or hasRole('administrator')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(levelRuleService.deleteLogic(Func.toLongList(ids)));
	}


}
