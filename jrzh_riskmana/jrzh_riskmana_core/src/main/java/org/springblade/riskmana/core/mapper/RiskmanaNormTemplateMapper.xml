<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.riskmana.core.mapper.RiskmanaNormTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="riskmanaNormTemplateResultMap" type="org.springblade.riskmana.api.entity.RiskmanaNormTemplate">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="template_id" property="templateId"/>
        <result column="norm_id" property="normId"/>
    </resultMap>


    <select id="selectRiskmanaNormTemplatePage" resultMap="riskmanaNormTemplateResultMap">
        select * from jrzh_riskmana_norm_template where is_deleted = 0
    </select>

</mapper>
