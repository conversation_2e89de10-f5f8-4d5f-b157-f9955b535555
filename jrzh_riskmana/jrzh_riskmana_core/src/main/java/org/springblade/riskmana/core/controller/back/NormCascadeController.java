package org.springblade.riskmana.core.controller.back;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.riskmana.api.entity.RatingRule;
import org.springblade.riskmana.api.entity.RiskmanaNormTemplate;
import org.springblade.riskmana.api.entity.RiskmanaTemplate;
import org.springblade.riskmana.core.service.IRatingRuleService;
import org.springblade.riskmana.core.service.IRiskmanaNormTemplateService;
import org.springblade.riskmana.core.service.IRiskmanaTemplateService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Data 2022/2/12 0012 - 11:40
 * @Depiction:
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_RISKMANA + CommonConstant.WEB_BACK + "/riskmana/norm/cascade")
@Api(value = "指标级联信息", tags = "指标级联信息接口")
public class NormCascadeController {
	private final IRiskmanaNormTemplateService normTemplateService;
	private final RiskmanaTemplateController templateController;

	private final IRiskmanaTemplateService riskmanaTemplateService;

	/**
	 * 获取指标删除时受影响的关联信息
	 */
	@GetMapping("/cascadeInfo")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "获取指标删除时受影响的关联信息", notes = "")
	public R<StringBuilder> cascadeInfo(@RequestParam Long id) {
		//通过id在中间表中找 再对找到的templateId去重
		List<Long> collect = normTemplateService.list(new QueryWrapper<RiskmanaNormTemplate>().lambda().eq(RiskmanaNormTemplate::getNormId, id))
			.stream().map(RiskmanaNormTemplate::getTemplateId).distinct().collect(Collectors.toList());
		//关联ids不存在 直接返回
		if (ObjectUtil.isEmpty(collect)) {
			return R.status(true);
		}
		return R.data(getMsg(collect));
	}

	public StringBuilder getMsg(List<Long> ids) {
		StringBuilder msg = new StringBuilder();
		if (ObjectUtil.isEmpty(ids)) {
			return msg;
		}
		List<String> collect = riskmanaTemplateService.listByIds(ids).stream().map(RiskmanaTemplate::getName).collect(Collectors.toList());
		if (ObjectUtil.isNotEmpty(collect)) {
			//获取规则模板的级联信息
			RatingRuleController ruleController = SpringUtil.getBean(RatingRuleController.class);
			IRatingRuleService ruleService = SpringUtil.getBean(IRatingRuleService.class);
			List<Long> ruleIds = ruleService.list(new LambdaQueryWrapper<RatingRule>().in(RatingRule::getTemplateId, ids)).stream().map(RatingRule::getId).collect(Collectors.toList());

			//判断是否为空
			if (ObjectUtil.isNotEmpty(ruleIds)) {
				msg = ruleController.getMsg(ruleIds);
			}
			msg.append("将会受到影响的 风控模板名称 如下:\n");
			msg.append(collect.toString());
		}
		//查询受到影响的产品信息
		return msg;
	}

}
