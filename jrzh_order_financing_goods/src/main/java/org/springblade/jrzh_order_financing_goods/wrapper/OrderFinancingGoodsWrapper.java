package org.springblade.jrzh_order_financing_goods.wrapper;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springblade.jrzh_order_financing_goods_api.dto.OrderFinancingGoodsDTO;
import org.springblade.jrzh_order_financing_goods_api.entity.OrderFinancingGoods;
import org.springblade.jrzh_order_financing_goods_api.vo.OrderFinancingGoodsVo;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.vo.ProductVO;;


import java.util.List;

/**
 * 产品表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-12-24
 */
@Mapper
public interface OrderFinancingGoodsWrapper {

    /**
     * 获取mapper对象
     *
     * @return
     */
    static OrderFinancingGoodsWrapper build() {
        return Mappers.getMapper(OrderFinancingGoodsWrapper.class);
    }

    /**
     * 实体类转vo
     *
     * @param goods
     * @return
     */
    OrderFinancingGoodsVo entityVO(OrderFinancingGoods goods);

    /**
     * 实体类转dto
     *
     * @param goods
     * @return
     */
    OrderFinancingGoodsDTO entityDTO(OrderFinancingGoods goods);

    /**
     * 实体类List转VOList
     *
     * @param goodss
     * @return
     */
    List<OrderFinancingGoodsVo> listVO(List<OrderFinancingGoods> goodss);

    /**
     * 实体类Page转VOPage
     *
     * @param page
     * @return
     */
    default Page<OrderFinancingGoodsVo> pageVO(IPage<OrderFinancingGoods> page) {
        List<OrderFinancingGoodsVo> goodsVOList = this.listVO(page.getRecords());
        Page<OrderFinancingGoodsVo> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(goodsVOList);
        pageVO.setPages(page.getPages());
        return pageVO;
    }

    default Page<ProductVO> pageProductVO(IPage<OrderFinancingGoods> page) {
        List<ProductVO> voList = this.listProductVO(page.getRecords());
        Page<ProductVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(voList);
        pageVO.setPages(page.getPages());
        return pageVO;
    }

    default List<ProductVO> listProductVO(List<OrderFinancingGoods> records) {
        return BeanUtil.copyToList(records, ProductVO.class);
    }


    default Product entityProduct(OrderFinancingGoods goods) {
        return BeanUtil.copyProperties(goods, Product.class);
    }

    default ProductVO entityProductVO(OrderFinancingGoods goods) {
        return BeanUtil.copyProperties(goods, ProductVO.class);
    }


    default OrderFinancingGoodsDTO productDTOtoGoodsDTO(ProductDTO productDTO) {
        return BeanUtil.copyProperties(productDTO, OrderFinancingGoodsDTO.class);
    }

    default IPage<Product> pageProduct(IPage<OrderFinancingGoods> page) {
        List<Product> voList = BeanUtil.copyToList(page.getRecords(), Product.class);
        Page<Product> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(voList);
        pageVO.setPages(page.getPages());
        return pageVO;
    }
}
