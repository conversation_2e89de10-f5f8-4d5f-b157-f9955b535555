package org.springblade.finance.handler.impl;

import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.finance.dto.financeApplyHandler.FinanceApplyHandlerDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.external.dto.EnterpriseQuotaSerchDTO;
import org.springblade.finance.handler.FinanceApplyCoreHandler;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 融资抽象类
 *
 * <AUTHOR>
 * @since 2023-04-18
 **/
public abstract class AbstractFinanceHandler implements FinanceApplyCoreHandler {


    @Resource
    private FinanceApplyMapper financeApplyMapper;
    @Resource
    private ProductDirector productDirector;

    /**
     * 贷中-手动-融资申请
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long loanMiddle(FinanceApplyHandlerDTO financeApplyHandlerDTO) {
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        //判断是否已经存在融资金额
        BigDecimal amount = new BigDecimal(0);
        //判断是否能够执行贷中接口
        if (null != financeApply.getId()) {
            checkApplyFinanceApply(financeApply.getId());
            FinanceApply finance = financeApplyMapper.selectById(financeApply.getId());
            financeApply.setFinanceNo(finance.getFinanceNo());
            financeApply.setProcessInstanceId(finance.getProcessInstanceId());
            financeApply.setQuotaUseDetailsId(finance.getQuotaUseDetailsId());
            amount = new BigDecimal(finance.getAmount().toString());
        }
        //获取融资申请额度
        BigDecimal applyAmount = accessQuota(financeApplyHandlerDTO);
        //扣除授信额度
        limitDeduction(applyAmount, amount, financeApply.getGoodsId());
        //根据模板方法返回不同的值，来执行不同的方法
        if (templateMethod()) {
            //扣除销售合同额度
            subtractReceivableAmount(financeApply.getId(), financeApplyHandlerDTO.getReceivableFinanceDTO().getSalesContractDetails());
        }
        //查询企业额度信息
        EnterpriseQuotaSerchDTO enterpriseQuotaSerchDTO = selectEnterpriseQuota(financeApply.getGoodsId());
        //获取产品信息
        Product product = productDirector.detailBase(financeApply.getGoodsId());
        financeApply.setAmount(applyAmount);
        //判断是否已经存在融资数据，然后根据不同的放款方式走手动和自动融资申请
        if (null != financeApply.getId()) {
            if (GoodsEnum.AUTO_LENDING.getCode().equals(financeApplyHandlerDTO.getLendingMethod())) {
                //自动融资申请-修改
                saveFinanceApplyUpdate(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            } else {
                //手动融资申请-修改
                submitFinanceApplyUpdate(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            }
        } else {
            //生成融资编号
            String financeNo = CodeUtil.generateCode(CodeEnum.FINANCING_CODE);
            financeApply.setFinanceNo(financeNo);
            if (GoodsEnum.AUTO_LENDING.getCode().equals(financeApplyHandlerDTO.getLendingMethod())) {
                //自动融资申请-新增
                saveFinanceApply(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            } else {
                //手动融资申请-新增
                submitFinanceApplySave(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            }
        }
        return financeApply.getId();
    }


}
