package org.springblade.finance.handler;

import org.springblade.customer.entity.SalesContractDetail;
import org.springblade.finance.dto.financeApplyHandler.FinanceApplyHandlerDTO;
import org.springblade.finance.external.dto.EnterpriseQuotaSerchDTO;
import org.springblade.product.common.entity.Product;

import java.math.BigDecimal;
import java.util.List;

/**
 * 融资 核心接口
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface FinanceApplyCoreHandler {

    /**
     * 贷中
     *
     * @return
     */
    Long loanMiddle(FinanceApplyHandlerDTO financeApplyHandlerDTO);

    /**
     * 获取融资申请金额
     *
     * @param financeApplyHandlerDTO 融资申请条件
     * @return
     */
    BigDecimal accessQuota(FinanceApplyHandlerDTO financeApplyHandlerDTO);

    /**
     * 保存融资申请--新增
     */
    void saveFinanceApply(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product);

    /**
     * 保存融资申请 ---修改
     */
    void saveFinanceApplyUpdate(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product);

    /**
     * 提交融资申请-新增
     *
     * @param financeApplyHandlerDTO   融资申请数据
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     */
    void submitFinanceApplySave(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product);

    /**
     * 提交融资申请-修改
     *
     * @param financeApplyHandlerDTO   融资申请数据
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     */
    void submitFinanceApplyUpdate(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product);


    /**
     * 检查当前融资订单可否提交申请
     *
     * @param financeApplyId 融资申请id
     */
    void checkApplyFinanceApply(Long financeApplyId);

    /**
     * 模板方法-扣除销售合同额度
     *
     * @return 需要执行的方法
     */
    default boolean templateMethod() {
        return false;
    }

    /**
     * 额度扣减
     *
     * @param applyAmount 申请金额
     * @param amount      已经存在得额度（查询融资申请数据，如果不存在则没有额度:默认0）
     * @param goodsId     产品id
     */
    default void limitDeduction(BigDecimal applyAmount, BigDecimal amount, Long goodsId) {
    }

    /**
     * 扣除销售合同额度(应收账款核心)
     *
     * @param financeApplyId          融资申请id
     * @param salesContractDetailList 销售合同列表
     */
    default void subtractReceivableAmount(Long financeApplyId, List<SalesContractDetail> salesContractDetailList) {
    }

    /**
     * 获取企业额度
     *
     * @param goodsId
     * @return
     */
    EnterpriseQuotaSerchDTO selectEnterpriseQuota(Long goodsId);

}
