<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.finance.mapper.FinanceApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="financeApplyResultMap" type="org.springblade.finance.entity.FinanceApply">
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="amount" property="amount"/>
        <result column="loan_usage" property="loanUsage"/>
        <result column="customer_goods_id" property="customerGoodsId"/>
        <result column="customer_id" property="customerId"/>
        <result column="repayment_mode" property="repaymentMode"/>
        <result column="load_term" property="loadTerm"/>
        <result column="load_term_unit" property="loadTermUnit"/>
        <result column="finance_no" property="financeNo"/>
    </resultMap>

    <resultMap id="genderCount" type="java.util.HashMap">
        <result column="financeNo" property="key" javaType="java.lang.String"/>
        <result column="platformCostPayMode" property="value" javaType="java.lang.String"/>
    </resultMap>

<!--    <select id="newLoansDto" resultType="org.springblade.finance.entity.FinanceApply">-->
<!--        SELECT finance.* FROM-->
<!--        `jrzh_loan_manage_iou` as iou-->
<!--        LEFT JOIN jrzh_finance_apply as finance-->
<!--        ON iou.finance_apply_id = finance.id-->
<!--        WHERE iou.loan_time like concat(concat(#{dto.queryTime},'%'))-->
<!--        and iou.is_deleted = 0-->
<!--        <if test="dto.type != -1 and dto.type == 1 or dto.type == 2 and dto.type != -1">-->
<!--            and finance.goods_type = #{dto.type}-->
<!--        </if>-->
<!--        <if test="dto.userId != null and dto.userId != 0">-->
<!--            and iou.user_id = #{dto.userId}-->
<!--        </if>-->
<!--        <if test="dto.tenantId != null and dto.tenantId != ''">-->
<!--            and iou.tenant_id = #{dto.tenantId}-->
<!--        </if>-->
<!--    </select>-->


    <select id="selectFinanceApplyPage" resultMap="financeApplyResultMap">
        select * from jrzh_finance_apply where is_deleted = 0
    </select>
    <select id="queryFinanceApplyShowChart" resultType="org.springblade.finance.entity.FinanceApply">
        SELECT DATE(create_time) as create_time,SUM(amount) as amount FROM jrzh_finance_apply where DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[ <= ]]> date(create_time)
        and is_deleted = 0  and status in (8,9,11,12,10)
        GROUP BY DATE(create_time) order by create_time
    </select>
    <select id="yesTerDayFinancingIncomeCount" resultType="java.lang.Integer">
        SELECT sum(amount) as amount FROM jrzh_finance_apply WHERE TO_DAYS( NOW( ) ) - TO_DAYS( create_time) = 1  and is_deleted = 0  and status in (8,9,11,12,10)
    </select>
    <select id="queryFinanceStrokeCountShowChart" resultType="org.springblade.finance.vo.FinanceStrokeCountVO">
        SELECT DATE(create_time) as create_time,count(1) as stroke FROM jrzh_finance_apply where DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[ <= ]]> date(create_time)
        and is_deleted = 0  and status in (8,9,11,12,10)
        GROUP BY DATE(create_time) order by create_time
    </select>
    <select id="queryYesTerDayFinanceStrokeCount" resultType="java.lang.Integer">
        SELECT count(1) as stroke FROM jrzh_finance_apply WHERE TO_DAYS( NOW( ) ) - TO_DAYS( create_time) = 1  and is_deleted = 0  and status in (8,9,11,12,10)
    </select>

    <select id="selectFinance" resultType="org.springblade.finance.entity.FinanceApply">
        select * from jrzh_finance_apply where id = #{financeApplyId}
    </select>
    <select id="selectListIds" resultType="org.springblade.finance.entity.FinanceApply">
        select * from jrzh_finance_apply
        where( id IN
        <foreach collection="financeApplyIdList" item="financeApplyIdList" index="index" open="(" close=")" separator=",">
            #{financeApplyIdList}
        </foreach>)
    </select>
    <select id="getByFinanceNoApply" resultType="org.springblade.finance.entity.FinanceApply">
        select * from jrzh_finance_apply where finance_no = #{financeNo}
    </select>

    <select id="getByOldFinanceNo" resultType="org.springblade.finance.entity.FinanceApply">
        select * from jrzh_finance_apply where old_finance_no = #{oldNo}
    </select>

    <select id="selectPayModeFinanceNoMap" resultType="java.util.Map" resultMap="genderCount">
        select DISTINCT f.finance_no as financeNo ,b.platform_cost_pay_mode as platformCostPayMode
            from
        (SELECT finance_no,goods_id from jrzh_finance_apply
        where( finance_no IN
        <foreach collection="financeNoList" item="financeNoList" index="index" open="(" close=")" separator=",">
            #{financeNoList}
        </foreach>)
        ) as f
         left join jrzh_bill_bank_carda_relation as b on f.goods_id=b.goods_id AND b.account_type= 2 AND b.is_deleted = 0
    </select>

    <select id="selectExportList" parameterType="org.springblade.finance.dto.FinanceApplyDTO" resultType="org.springblade.finance.excel.FinanceApplyExcel">
        select jfa.finance_no, db.dept_name, bu.name as user_name, jfa.amount, jfa.lending_amount, jfa.loan_apply_no, jfa.status, jfa.create_time
        from jrzh_finance_apply jfa
        left join blade_user bu on jfa.user_id = bu.id
        left join blade_dept db on jfa.capital_id = db.id
        <where>
            jfa.is_deleted = 0 and bu.is_deleted = 0 and db.is_deleted = 0
            <if test="financeApplyDTO.financeNoEqual != null and financeApplyDTO.financeNoEqual != ''">
                and jfa.finance_no = #{financeApplyDTO.financeNoEqual}
            </if>
            <if test="financeApplyDTO.capitalId != null">
                and jfa.capital_id = #{financeApplyDTO.capitalId}
            </if>
            <if test="financeApplyDTO.userId != null">
                and jfa.user_id = #{financeApplyDTO.userId}
            </if>
            <if test="financeApplyDTO.statusIn != null and financeApplyDTO.statusIn != ''">
                and jfa.status = #{financeApplyDTO.statusIn}
            </if>
            <if test="financeApplyDTO.createTimeDateGe != null and financeApplyDTO.createTimeDateGe != ''">
                <![CDATA[ and DATE_FORMAT(jfa.create_time,'%Y-%m-%d') >= #{financeApplyDTO.createTimeDateGe} ]]>
            </if>
            <if test="financeApplyDTO.createTimeDateLe != null and financeApplyDTO.createTimeDateLe != ''">
                <![CDATA[ and DATE_FORMAT(jfa.create_time,'%Y-%m-%d') <= #{financeApplyDTO.createTimeDateLe} ]]>
            </if>
        </where>
    </select>

</mapper>
