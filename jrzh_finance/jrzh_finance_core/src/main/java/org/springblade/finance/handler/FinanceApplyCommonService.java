package org.springblade.finance.handler;

import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.vo.FinanceApplyVO;
import org.springblade.finance.vo.financeCommon.FinanceApplyCommonVo;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * 融资---通用接口
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface FinanceApplyCommonService {

    /**
     * 根据融资主数据，查询融资详情数据
     *
     * @param financeApply 融资主数据
     * @return 融资详情数据
     */
    FinanceApplyCommonVo financeDetail(FinanceApply financeApply);

    /**
     * 处理对应融资订单
     *
     * @param
     * @param
     * @return page
     */
    void dealFinanceApply(List<FinanceApplyVO> financeApplyVO,
                          Map<String, List<LoanManageRepaymentPlan>> allUnRepaymentMap,
                          Map<String, List<LoanManageRepaymentPlan>> overdueRepaymentMap,
                          Map<Long, User> userMap);

}
