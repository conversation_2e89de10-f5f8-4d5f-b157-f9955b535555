package org.springblade.finance.service.impl;

import org.springblade.finance.limit.enums.FinancingLimitEnum;
import org.springblade.finance.service.OrderLevelServerService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 默认的水位处理服务，不做任何处理
 * <AUTHOR>
 * @date 2025/1/8
 */
@Service
public class DefaultOrderLevelServerServiceImpl implements OrderLevelServerService {

    @Override
    public Integer support() {
        return -1;
    }

    @Override
    public Boolean useOrderLevel(Long limitId, BigDecimal useAmount, String businessNo) {
        return null;
    }

    @Override
    public Boolean updateUseOrderLevel(Long limitId, BigDecimal useAmount, String businessNo) {
        return null;
    }

    @Override
    public Boolean useOrderLevelRollBack(String businessNo) {
        return null;
    }

    @Override
    public Boolean lendingOrderLevel(String businessNo, BigDecimal lendingAmount) {
        return null;
    }

    @Override
    public Boolean repaymentOrderLevel(String businessNo, BigDecimal repaymentAmount, String repaymentNo) {
        return null;
    }

}
