package org.springblade.finance.factory;

import lombok.RequiredArgsConstructor;
import org.springblade.finance.service.OrderLevelServerService;
import org.springblade.workflow.core.utils.ObjectUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 获取水位工厂
 * <AUTHOR>
 * @date 2025/1/8
 */
@Service
@RequiredArgsConstructor
public class OrderLevelServerFactory {

    private final List<OrderLevelServerService> orderLevelServerServiceList;

    /**
     * 获取水位服务会返回默认的水位服务，不执行任何操作
     * @param goodsType
     * @return
     */
    public OrderLevelServerService getServer(Integer goodsType) {
        Map<Integer, OrderLevelServerService> orderLevelServerServiceMap = orderLevelServerServiceList.stream()
                .collect(Collectors.toMap(OrderLevelServerService::support, e -> e, (oldValue, newValue) -> oldValue));
        OrderLevelServerService orderLevelServerService = orderLevelServerServiceMap.get(goodsType);
        if (ObjectUtil.isNotEmpty(orderLevelServerService)) {
            return orderLevelServerService;
        }
        return orderLevelServerServiceMap.get(-1);
    }

}
