package org.springblade.finance.handler;

import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.expense.constant.ExpenseConstant;

/**
 * 费用状态变更处理器
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 费用状态变更处理器
 * @Version: 1.0
 */
public interface IFinanceRepaymentCalHandler {
    /**
     * 支持的费用计算类型
     *
     * @return PlatformExpensesEnum 费用计算类型
     */
    PlatformExpensesEnum supportType();

    /**
     * 支持的计算节点
     *
     * @return FeeNodeEnum 计算节点
     */
    ExpenseConstant.FeeNodeEnum supportFee();

    /**
     * 是否支持
     *
     * @param type     PlatformExpensesEnum 费用计算类型
     * @param feedNode FeeNodeEnum 计算节点
     * @return
     */
    default Boolean support(Integer type, Integer feedNode) {
        return supportType().getCode() == type && supportFee().getCode() == feedNode;
    }

    /**
     * 填充费用字段参数 expenseOrderDTO
     *
     * @param costCalculusDto 前端的条件参数
     * @param expenseRuleDTO  需要填充的费用字段参数
     */
    void fullFieldExpenseRuleDTO(CostCalculusDto costCalculusDto, ExpenseRuleDTO expenseRuleDTO);

    /**
     * 填充每一期的费用
     *
     * @param costCalculusDto 前端的条件参数
     * @param expenseRuleDTO  需要填充的费用字段参数
     * @param stagRecordVO    本期还款计划
     */
    void fullFieldExpenseRuleDTOTerm(CostCalculusDto costCalculusDto, ExpenseRuleDTO expenseRuleDTO, StagRecordVO stagRecordVO);
}
