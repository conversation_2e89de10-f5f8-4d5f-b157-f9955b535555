package org.springblade.finance.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.resource.vo.AttachInfoDTO;

import java.util.List;

/**
 * 融资费用订单详情--还款试算数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseOrderDetailFinanceVo {

    /**
     * 费用订单详情数据
     */
    private List<ExpenseOrderDetail> expenseOrderDetailList;

    @ApiModelProperty(value = "父级费用类型名称")
    private String parenExpenseName;
    /**
     * 开户银行
     */
    private String bankDeposit;
    /**
     * 银行账户号
     */
    private String bankCardNo;
    /**
     * 账户名称
     */
    private String enterpriseName;
    /**
     * 平台
     */
    private Integer accountType;
    /**
     * 开户名
     */
    private String openHouseName;
    /**
     * 子商户编号(线上账户必填)
     */
    private String merchantNo;
    /**
     * 支付模式
     */
    private Integer payMode;
    /**
     * 支付状态 （不一定有）
     */
    private Integer paymentStatus;
    /**
     * 附件信息（不一定有）
     */
    private List<AttachInfoDTO> attachInfoDTOList;
    /**
     * 费用单号（不一定有）
     */
    private String billExpenseNo;
}
