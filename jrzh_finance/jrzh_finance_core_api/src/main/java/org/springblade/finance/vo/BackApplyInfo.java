package org.springblade.finance.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.customer.vo.SalesContractDetailVO;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.product.common.vo.ProductVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BackApplyInfo {

    private FinanceApply financeApply;
    private ProductVO goodsVO;
    private List<SalesContractDetailVO> salesContractDetailVOList;
    private List<StagRecordVO> repaymentCalculation;
    private List<ExpenseOrderDetail> platformExpenses;

}
