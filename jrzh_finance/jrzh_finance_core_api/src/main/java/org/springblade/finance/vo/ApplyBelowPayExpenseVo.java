package org.springblade.finance.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 融资申请线下支付费用实体
 * <AUTHOR>
 */
@Data
public class ApplyBelowPayExpenseVo {


//	@ApiModelProperty(value = "线下支付的费用详情")
//	private List<PlatformExpensesVO> belowPlatformExpensesList;

	@ApiModelProperty("父级费用类型名称")
	private String parentExpenseName;

	@ApiModelProperty("费用单号")
	private String expenseOrderNo;

	@ApiModelProperty("金额")
	private BigDecimal amount;

//	@ApiModelProperty("费用订单")
//	private BillExpenseOrder expenseOrder;
//
//	@ApiModelProperty(value = "线下费用关联的账户")
//	private BillBankCarda billBankCarda;
}
