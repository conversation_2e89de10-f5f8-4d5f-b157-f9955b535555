package org.springblade.finance.external.handler.businessProcessProgress;

import lombok.AllArgsConstructor;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class FinanceBusinessProcessProgressServiceImpl implements FinanceBusinessProcessProgressService {

    private final IBusinessProcessProgressService businessProcessProgressService;

    /**
     * 保存流程进度
     *
     * @param businessId        业务id
     * @param progress          进度
     * @param type              流程类型
     * @param processInstanceId 流程实例id
     * @return boolean
     */
    @Override
    public BusinessProcessProgress saveBusinessProcessProgressUnContainInvalid(Long businessId, Integer progress, Integer type, String processInstanceId) {
        return businessProcessProgressService.saveBusinessProcessProgressUnContainInvalid(businessId, progress, type, processInstanceId);
    }

    @Override
    public boolean updateBusinessProcessProgressUnContainInvalid(Long businessId, Integer progress, Integer type, String processInstanceId, Long userId, Integer status,Integer frontOperateAbility, Boolean needCheckOperator) {
        return businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(businessId, progress, type, processInstanceId, userId, status,frontOperateAbility,needCheckOperator);
    }


}
