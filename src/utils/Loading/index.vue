<template>
  <div class="loading-body">
    <div class="loading-round"></div>
    <div class="loading-content">
      <n-space vertical>
        <n-spin stroke="#007fff">
          <template #description>
            <div class="doce">
              {{ title }}
            </div>
          </template>
        </n-spin>
      </n-space>
    </div>
  </div>
</template>

<script>
export default {
  name: 'isLoading',
}
</script>
<script setup>
import { NSpin, NSpace } from 'naive-ui'
// import { defineComponent } from 'vue'
import { ref } from 'vue'
// import { useRouter } from 'vue-router'
// import { useRoute } from 'vue-router'
// const router = useRouter()
// const route = useRoute()

const title = ref('正在载入...')
// const uprouter = () => {
//   router.push('/user')
// }
// const setTitle = title => {
//   this.title = title
// }
</script>

<style lang="scss" scoped>
.loading-body {
  position: fixed;
  z-index: 950;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.9);
  .loading-round {
    // background: radial-gradient(
    //   #0080ff6c,
    //   #0080ff38,
    //   #0080ff49,
    //   #0080ffb2,
    //   #0080ffde,
    //   #007fff
    // );
    position: absolute;
    border-radius: 150%;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    // animation: bagRound 0.4s ease 1;
  }

  @keyframes bagRound {
    0% {
      width: 50px;
      height: 50px;
    }
    5% {
      width: 100px;
      height: 100px;
    }
    10% {
      width: 150px;
      height: 150px;
    }
    15% {
      width: 200px;
      height: 200px;
    }
    20% {
      width: 250px;
      height: 250px;
    }
    25% {
      width: 300px;
      height: 300px;
    }
    30% {
      width: 350px;
      height: 350px;
    }
    35% {
      width: 400px;
      height: 400px;
    }
    40% {
      width: 450px;
      height: 450px;
    }
    45% {
      width: 500px;
      height: 500px;
    }
    50% {
      width: 650px;
      height: 650px;
    }
    55% {
      width: 750px;
      height: 750px;
    }
    60% {
      width: 900px;
      height: 900px;
    }
    65% {
      width: 1150px;
      height: 1150px;
    }
    70% {
      width: 1450px;
      height: 1450px;
    }
    75% {
      width: 1750px;
      height: 1750px;
    }
    80% {
      width: 2050px;
      height: 2050px;
    }
    85% {
      width: 2450px;
      height: 2450px;
    }
    90% {
      width: 2750px;
      height: 2750px;
    }
    95% {
      width: 2950px;
      height: 2950px;
    }
    100% {
      width: 3250px;
      height: 3250px;
    }
  }
  .loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    z-index: 2;

    .doce {
      // color: rgb(231, 227, 224);
      font-size: 18px;
      font-weight: 500;
    }
  }
}
</style>
