<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="50.00001%" y1="-0.000369550686%" x2="50.00001%" y2="100.000698%" id="linearGradient-1">
            <stop stop-color="#E6EEFF" offset="0%"></stop>
            <stop stop-color="#F6F6F6" stop-opacity="0" offset="99.9999904%"></stop>
        </linearGradient>
        <linearGradient x1="49.990907%" y1="77.3385076%" x2="49.990907%" y2="21.0747498%" id="linearGradient-2">
            <stop stop-color="#E0EBF3" offset="0%"></stop>
            <stop stop-color="#ECF5FB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.825%" y1="0.00291120815%" x2="49.825%" y2="81.799285%" id="linearGradient-3">
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.990907%" y1="77.3385076%" x2="49.990907%" y2="21.0747498%" id="linearGradient-4">
            <stop stop-color="#DFE4F6" offset="0%"></stop>
            <stop stop-color="#ECF5FB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.825%" y1="0.00291120815%" x2="49.825%" y2="81.799285%" id="linearGradient-5">
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="74.9567591%" y1="6.68877698%" x2="25.0560196%" y2="93.3018705%" id="linearGradient-6">
            <stop stop-color="#FFDB80" offset="0%"></stop>
            <stop stop-color="#FFBB24" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9995325%" y1="-0.00887573964%" x2="49.9995325%" y2="99.9805082%" id="linearGradient-7">
            <stop stop-color="#F6FAFF" offset="0%"></stop>
            <stop stop-color="#DFE5F6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9849029%" y1="77.3427285%" x2="49.9849029%" y2="21.0661457%" id="linearGradient-8">
            <stop stop-color="#E8F1FF" offset="0%"></stop>
            <stop stop-color="#ECF4FB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.825%" y1="0.00291120815%" x2="49.825%" y2="81.799285%" id="linearGradient-9">
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="71.8114986%" y1="84.3680777%" x2="26.0707945%" y2="11.2024285%" id="linearGradient-10">
            <stop stop-color="#D5E0F5" offset="0%"></stop>
            <stop stop-color="#EAF9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="65.5578555%" y1="90.3918789%" x2="27.7752914%" y2="-7.70278298%" id="linearGradient-11">
            <stop stop-color="#E3E5FF" offset="1.90412615%"></stop>
            <stop stop-color="#FFFFFF" offset="97.7387115%"></stop>
        </linearGradient>
        <linearGradient x1="49.9995616%" y1="89.7789454%" x2="49.9995616%" y2="54.5897961%" id="linearGradient-12">
            <stop stop-color="#F2FFFC" offset="0%"></stop>
            <stop stop-color="#DEFDF3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0002979%" y1="99.9985592%" x2="50.0002979%" y2="-0.000563129111%" id="linearGradient-13">
            <stop stop-color="#EBFAF8" offset="0%"></stop>
            <stop stop-color="#FDFFFF" offset="52.47%"></stop>
            <stop stop-color="#E9FBF6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="32.1765048%" y1="34.2273584%" x2="64.5965131%" y2="62.9259876%" id="linearGradient-14">
            <stop stop-color="#E8FAF7" offset="0%"></stop>
            <stop stop-color="#C6E0E4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.2361383%" y1="94.2747398%" x2="23.6491525%" y2="-13.1432491%" id="linearGradient-15">
            <stop stop-color="#BCC2E4" offset="0%"></stop>
            <stop stop-color="#E8F7FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="67.2765225%" y1="62.1785422%" x2="25.3220275%" y2="32.6023881%" id="linearGradient-16">
            <stop stop-color="#F1F3FF" offset="0%"></stop>
            <stop stop-color="#EAF3FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0068642%" y1="-0.00654893304%" x2="50.0068642%" y2="65.1942667%" id="linearGradient-17">
            <stop stop-color="#181E4B" offset="0%"></stop>
            <stop stop-color="#2D3A65" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9889085%" y1="100.021369%" x2="49.9889085%" y2="34.8251403%" id="linearGradient-18">
            <stop stop-color="#38C5AF" offset="0%"></stop>
            <stop stop-color="#31B69F" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0068642%" y1="-0.00654893304%" x2="50.0068642%" y2="65.1942667%" id="linearGradient-19">
            <stop stop-color="#294574" offset="0%"></stop>
            <stop stop-color="#2D3065" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0138783%" y1="100.038693%" x2="50.0138783%" y2="0.00479520535%" id="linearGradient-20">
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
        </linearGradient>
        <linearGradient x1="0.0029504698%" y1="50.0091196%" x2="100.026629%" y2="50.0091196%" id="linearGradient-21">
            <stop stop-color="#4F517C" offset="0%"></stop>
            <stop stop-color="#273B68" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0068642%" y1="-0.00654893304%" x2="50.0068642%" y2="65.1942667%" id="linearGradient-22">
            <stop stop-color="#181E4B" offset="0%"></stop>
            <stop stop-color="#2D3A65" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0.0119037199%" y1="50.0097148%" x2="99.9823195%" y2="50.0097148%" id="linearGradient-23">
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
        </linearGradient>
        <linearGradient x1="100.008727%" y1="49.9950998%" x2="-0.0116354234%" y2="49.9950998%" id="linearGradient-24">
            <stop stop-color="#FAD96E" offset="0%"></stop>
            <stop stop-color="#FFB32C" offset="54.39%"></stop>
        </linearGradient>
        <linearGradient x1="49.9860162%" y1="100.00835%" x2="49.9860162%" y2="0.000800732098%" id="linearGradient-25">
            <stop stop-color="#FAD96E" offset="0%"></stop>
            <stop stop-color="#FFB32C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-0.0396239087%" y1="50.0119125%" x2="100.028207%" y2="50.0119125%" id="linearGradient-26">
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
        </linearGradient>
        <linearGradient x1="49.9914854%" y1="99.9990204%" x2="49.9914854%" y2="-0.00131390832%" id="linearGradient-27">
            <stop stop-color="#FAD96E" offset="0%"></stop>
            <stop stop-color="#FFB32C" offset="54.39%"></stop>
        </linearGradient>
        <linearGradient x1="49.9889085%" y1="100.021369%" x2="49.9889085%" y2="34.8251403%" id="linearGradient-28">
            <stop stop-color="#50C6B3" offset="0%"></stop>
            <stop stop-color="#6AE0CC" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人中心/用户中心/贸易伙伴/无数据" transform="translate(-860.000000, -895.000000)">
            <g id="缺省页/暂无预约" transform="translate(860.000000, 895.000000)">
                <rect id="矩形" x="0" y="0" width="200" height="200"></rect>
                <g id="编组" transform="translate(0.000000, 22.000000)" fill-rule="nonzero">
                    <g>
                        <path d="M200,159.4386 C200,134.585786 155.228475,114.4386 100,114.4386 C44.771525,114.4386 0,134.585786 0,159.4386 L200,159.4386 Z" id="椭圆形" fill="url(#linearGradient-1)"></path>
                        <g transform="translate(16.000000, 104.000000)" id="路径">
                            <path d="M6.67494613,0.0546670673 C6.67494613,0.0546670673 6.62560421,-0.0741555655 6.55831978,0.0637072521 C5.95275987,1.30447261 0.469078469,14.7449673 0.0339724605,25.1185793 C0.0339724605,25.1185793 -0.881095847,31.0783211 6.78708686,30.9992195 C13.1880794,30.9336782 13.0176255,25.4395059 12.9951973,23.588528 C12.9077276,16.0806546 6.67494613,0.0546670673 6.67494613,0.0546670673 Z" fill="url(#linearGradient-2)"></path>
                            <path d="M6.45,37 C6.175,37 5.95,36.9017467 5.95,36.7816594 L5.95,4.21834061 C5.95,4.09825328 6.175,4 6.45,4 C6.725,4 6.95,4.09825328 6.95,4.21834061 L6.95,36.779476 C6.95,36.9017467 6.725,37 6.45,37 Z" fill="url(#linearGradient-3)" transform="translate(6.450000, 20.500000) scale(-1, 1) translate(-6.450000, -20.500000) "></path>
                        </g>
                        <g id="编组备份" transform="translate(165.000000, 79.000000)">
                            <path d="M10.2691479,0.0846457816 C10.2691479,0.0846457816 10.1932373,-0.114821521 10.0897227,0.098643487 C9.15809211,2.01982856 0.721659184,22.8309171 0.0522653238,38.8932841 C0.0522653238,38.8932841 -1.35553207,48.1212714 10.4416721,47.9987915 C20.2893529,47.8973081 20.0271161,39.3902026 19.9926113,36.5241724 C19.8580424,24.8990781 10.2691479,0.0846457816 10.2691479,0.0846457816 Z" id="路径" fill="url(#linearGradient-4)"></path>
                            <path d="M9.5,58 C9.225,58 9,57.8421993 9,57.6493317 L9,5.35066825 C9,5.15780071 9.225,5 9.5,5 C9.775,5 10,5.15780071 10,5.35066825 L10,57.6458251 C10,57.8421993 9.775,58 9.5,58 Z" id="路径" fill="url(#linearGradient-5)"></path>
                        </g>
                        <g id="编组-2" transform="translate(87.549967, 10.000000)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-6)" cx="25.002269" cy="9.75817733" rx="9.74788342" ry="9.75817733"></ellipse>
                            <path d="M30.5508484,15.0795431 L20.8562628,15.0795431 C20.2307152,12.1001399 17.5882673,9.86207735 14.4240623,9.86207735 C11.2598573,9.86207735 8.6202146,12.1001399 7.99186183,15.0795431 L5.45320442,15.0795431 C2.44328243,15.0795431 0,17.5225976 0,20.5385062 L0,20.5385062 C0,23.5516067 2.44047729,25.9974693 5.45320442,25.9974693 L30.5508484,25.9974693 C33.5607704,25.9974693 36.0040529,23.5544148 36.0040529,20.5385062 L36.0040529,20.5385062 C36.0040529,17.5225976 33.5635756,15.0795431 30.5508484,15.0795431 Z" id="路径" fill="url(#linearGradient-7)"></path>
                        </g>
                        <g transform="translate(181.554454, 141.375191)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-8)" cx="3.90195851" cy="3.90607904" rx="3.90195851" ry="3.90607904"></ellipse>
                            <path d="M3.94554557,15.6248093 C3.70554557,15.6248093 3.51554557,15.5739164 3.51554557,15.5096307 L3.51554557,3.73998785 C3.51554557,3.67570213 3.70554557,3.62480928 3.94554557,3.62480928 C4.18554557,3.62480928 4.37554557,3.67570213 4.37554557,3.73998785 L4.37554557,15.5096307 C4.37554557,15.5712378 4.18554557,15.6248093 3.94554557,15.6248093 Z" id="路径" fill="url(#linearGradient-9)"></path>
                        </g>
                    </g>
                </g>
                <g id="编组" transform="translate(38.000000, 74.000000)" fill-rule="nonzero">
                    <path d="M76.5350892,93 L23.2501387,93 C18.6333175,93 14.8926145,89.3053443 14.8926145,84.7504133 L14.8926145,12.2495867 C14.8926145,7.69239183 18.635611,4 23.2501387,4 L76.5350892,4 C81.1519104,4 84.8926145,7.69465571 84.8926145,12.2495867 L84.8926145,84.7504133 C84.8949069,89.3053443 81.1519104,93 76.5350892,93 Z" id="路径" fill="url(#linearGradient-10)"></path>
                    <path d="M73.2706914,92.9532865 L22.1695117,92.9532865 C18.0858803,92.9532865 14.7765769,89.6544202 14.7765769,85.5836682 L14.7765769,14.0385333 C14.7765769,9.96778129 18.0858803,6.66891504 22.1695117,6.66891504 L73.2706914,6.66891504 C77.3543228,6.66891504 80.6636263,9.96778129 80.6636263,14.0385333 L80.6636263,85.5836682 C80.6613948,89.6544202 77.3520913,92.9532865 73.2706914,92.9532865 Z" id="路径" fill="url(#linearGradient-11)"></path>
                    <path d="M72.2218899,84.0799369 L23.4035271,84.0799369 C22.827802,84.0799369 22.3614201,83.6150258 22.3614201,83.0411165 L22.3614201,13.3445034 C22.3614201,12.7705941 22.827802,12.3056831 23.4035271,12.3056831 L72.2218899,12.3056831 C72.797615,12.3056831 73.2639969,12.7705941 73.2639969,13.3445034 L73.2639969,83.0411165 C73.2617654,83.6150258 72.7953835,84.0799369 72.2218899,84.0799369 Z" id="路径" fill="url(#linearGradient-12)"></path>
                    <path d="M45.5756372,83.4153822 C41.0166979,83.1078502 15.941416,81.2192882 0.41469073,68.7378731 C-0.352942711,68.1194747 0.0063275914,66.8493111 0.990415811,66.760333 C25.088304,64.5937141 22.1538913,21.7729621 22.3234847,13.5625 C22.3368737,12.8662457 22.7831722,12.3123565 23.4816293,12.3123565 L72.1393247,12.3123565 C72.7596796,12.3123565 73.2617654,12.8128588 73.2617654,13.4312572 L70.7134009,59.4129592 C70.6196782,59.6665471 70.4411588,60.1581515 70.2648709,60.3628014 L51.79927,82.5183697 C51.5716577,82.7875287 45.9304445,83.4370695 45.5756372,83.4153822 Z" id="路径" fill="url(#linearGradient-13)"></path>
                    <path d="M52.3415227,67.2141217 C55.1509718,67.3898536 63.5213004,67.4543628 70.7156324,59.4129592 C69.0107721,67.8725603 58.0630696,83.1768083 43.8373045,83.2813576 C52.0112618,79.0860362 51.6988528,71.3182405 51.4399997,67.8347445 C51.4109903,67.4276693 51.930928,67.1896527 52.3415227,67.2141217 Z" id="路径" fill="url(#linearGradient-14)"></path>
                    <path d="M30.048912,26.8491676 C30.048912,26.8491676 30.5398403,29.0802956 30.048912,31.4270953 C34.0834505,31.4270953 34.0455151,31.4270953 34.0455151,31.4270953 C34.0455151,31.4270953 34.7372778,29.3494546 34.0276632,26.7624139 C31.244992,26.7401693 30.048912,26.8491676 30.048912,26.8491676 Z" id="路径" fill="#B1D8F2"></path>
                    <path d="M68.1717309,31.4070752 L38.7807424,31.4070752 C38.7807424,31.4070752 39.0150491,29.5229621 38.7807424,26.6756602 C41.9717767,26.6756602 68.1739624,26.6756602 68.1739624,26.6756602 L68.1739624,31.4070752 L68.1717309,31.4070752 Z" id="路径" fill="#B1D8F2"></path>
                    <path d="M29.5758355,40.5918485 C29.5758355,40.5918485 30.0667639,42.8229765 29.5758355,45.1697761 C33.6103741,45.1697761 33.5724387,45.1697761 33.5724387,45.1697761 C33.5724387,45.1697761 34.2642014,43.0921355 33.5545868,40.5050947 C30.7719156,40.4828502 29.5758355,40.5918485 29.5758355,40.5918485 Z" id="路径" fill="#B1D8F2"></path>
                    <path d="M67.700886,45.149756 L38.3076659,45.149756 C38.3076659,45.149756 38.5419727,43.2656429 38.3076659,40.418341 C41.4987003,40.418341 67.700886,40.418341 67.700886,40.418341 L67.700886,45.149756 L67.700886,45.149756 Z" id="路径" fill="#B1D8F2"></path>
                    <path d="M28.5895158,55.2398823 C28.5895158,55.2398823 28.4154594,57.6667623 26.8511831,59.7822187 C28.9733325,59.7822187 31.4078909,59.866748 31.4078909,59.866748 C31.4078909,59.866748 33.1819275,58.1183266 32.8338147,55.2020666 C30.048912,55.1820465 28.5895158,55.2398823 28.5895158,55.2398823 Z" id="路径" fill="#B1D8F2"></path>
                    <path d="M67.4643478,59.8467279 L36.6541299,59.8467279 C36.6541299,59.8467279 37.5980513,57.9626148 37.3637446,55.1153129 C40.5547789,55.1153129 67.4643478,55.1153129 67.4643478,55.1153129 L67.4643478,59.8467279 Z" id="路径" fill="#B1D8F2"></path>
                    <path d="M61.8856163,3.92838691 L57.4137053,3.92838691 C56.6996276,3.92838691 56.1194396,3.35892652 56.1194396,2.6537744 L56.1194396,2.6537744 C56.1194396,1.43254879 55.1130364,0.440442021 53.8723266,0.440442021 L42.1235182,0.440442021 C40.8828084,0.440442021 39.8764052,1.43032434 39.8764052,2.6537744 L39.8764052,2.6537744 C39.8764052,3.35670207 39.2962171,3.92838691 38.5821395,3.92838691 L34.1124599,3.92838691 C32.4968593,3.92838691 31.1847417,5.21857061 31.1847417,6.80905568 L31.1847417,14.7815011 C31.1847417,16.3719862 32.4946278,17.6621699 34.1124599,17.6621699 L61.8856163,17.6621699 C63.501217,17.6621699 64.8133346,16.3719862 64.8133346,14.7792767 L64.8133346,6.80683123 C64.8111031,5.21857061 63.501217,3.92838691 61.8856163,3.92838691 Z" id="路径" fill="url(#linearGradient-15)"></path>
                    <path d="M61.1112884,3.44568025 L56.746489,3.44568025 C56.0480318,3.44568025 55.4834642,2.88066877 55.4834642,2.18663892 L55.4834642,2.18663892 C55.4834642,0.978760046 54.5016075,0 53.289907,0 L41.8155722,0 C40.6038718,0 39.6220151,0.978760046 39.6220151,2.18663892 L39.6220151,2.18663892 C39.6220151,2.88289323 39.0552159,3.44568025 38.3589903,3.44568025 L33.9941908,3.44568025 C32.4165256,3.44568025 31.1356488,4.72029277 31.1356488,6.29520666 L31.1356488,14.1764495 C31.1356488,15.7491389 32.4142941,17.0259759 33.9941908,17.0259759 L61.1135199,17.0259759 C62.6911852,17.0259759 63.9720619,15.7513634 63.9720619,14.1764495 L63.9720619,6.29520666 C63.9698304,4.72029277 62.6911852,3.44568025 61.1112884,3.44568025 Z" id="路径" fill="url(#linearGradient-16)"></path>
                </g>
                <g id="编组" transform="translate(123.000000, 104.000000)" fill-rule="nonzero">
                    <path d="M9.18037025,30.2025135 C9.18037025,30.2025135 15.827684,39.7845356 12.6384068,43.3926781 C9.44912957,47.0008206 3.75591142,51.6042352 3.75591142,51.6042352 L1.8813152,49.8518372 C1.8813152,49.8518372 8.0650191,41.6110735 7.79849944,40.1574819 C7.52974013,38.7038902 3.71783718,31.0899458 3.71783718,31.0899458 L9.18037025,30.2025135 Z" id="路径" fill="url(#linearGradient-17)"></path>
                    <path d="M2.30461112,49.1329047 C2.30461112,49.1329047 2.2486196,50.5909897 4.27775242,51.2874556 C4.66969309,52.4916675 5.35278968,53.8194459 5.91718424,54.6259983 C6.48381846,55.4325507 5.87239102,56.2548298 5.58571442,56.3626696 C4.71448631,55.4100841 1.83876164,53.0870335 0.510642698,50.7527495 C1.75365453,49.5732509 2.30461112,49.1329047 2.30461112,49.1329047 Z" id="路径" fill="url(#linearGradient-18)"></path>
                    <path d="M0,51.0897491 L0.555435917,50.6067164 C0.555435917,50.6067164 2.29789214,53.0353602 6.12547271,56.08633 C5.9194239,56.4076029 5.83431679,56.5828427 5.83431679,56.5828427 C5.83431679,56.5828427 2.80853484,54.4058253 0,51.0897491 Z" id="路径" fill="#B1D8F2"></path>
                    <path d="M10.2442092,29.7127407 C10.2442092,29.7127407 10.0650363,36.8301724 9.2923533,43.5342179 C8.51967027,50.2382635 8.25091096,58.4026406 8.25091096,58.4026406 L5.75368899,58.4026406 C5.75368899,58.4026406 3.73127515,47.6231465 3.34381381,43.185985 C2.95635246,38.7488235 1.29228437,33.5882362 2.15455384,30.9933392 C3.01682331,28.3984422 10.2442092,29.7127407 10.2442092,29.7127407 Z" id="路径" fill="url(#linearGradient-19)"></path>
                    <path d="M11.3349241,3.71883767 C11.3349241,3.71883767 11.4782624,7.09557373 9.55887295,8.58286533 C9.35058449,8.75585846 9.0975028,8.82325838 8.46367875,8.52445207 C7.8298547,8.22564575 7.82313571,9.60734414 7.69323538,10.4880364 C7.56333504,11.3687288 4.24415751,9.57589084 4.25755478,9.53320422 C4.27103344,9.48827094 5.7357717,5.58132216 6.11651407,4.17041714 C6.49949609,2.76400545 11.3349241,3.71883767 11.3349241,3.71883767 Z" id="路径" fill="url(#linearGradient-20)"></path>
                    <path d="M10.1344658,0.699321186 C10.1344658,0.699321186 8.64285162,-0.817177047 7.61932657,0.616194616 C5.51404527,-0.0128713174 4.61594123,1.71256667 5.07283206,3.27624485 C5.15569952,3.60201114 4.97652664,4.01764399 4.78167614,4.42653684 C4.58682563,4.8354297 3.82534091,6.82372738 6.44574423,7.94930607 C7.29681539,8.28405901 7.92392046,7.12927369 7.70219402,6.33395462 C7.48046759,5.53863555 7.95975503,4.62873661 8.41440621,5.40832903 C8.87129704,6.18792146 8.60701705,6.27554135 8.73915704,6.30474799 C8.87129704,6.33395462 9.16693229,5.8037419 9.07510619,5.11401604 C8.98328009,4.42204351 9.15349432,4.06257727 9.84554956,4.79274308 C10.5376048,5.5229089 13.8254271,6.62152762 14.7929606,3.7480443 C15.7604941,0.874560982 13.0639423,-0.704843844 10.1344658,0.699321186 Z" id="路径" fill="url(#linearGradient-21)"></path>
                    <path d="M14.2957559,13.1840333 C14.2957559,13.1840333 18.0001551,11.0609358 18.2644351,11.0497025 C18.5309547,11.0362225 18.9430524,11.5102686 18.9721679,11.5933952 C19.0012835,11.6765217 19.1155062,11.7596483 18.5130374,12.1011412 C17.9105687,12.4448808 15.0505216,14.3208453 15.0505216,14.3208453 L14.2957559,13.1840333 Z" id="路径" fill="url(#linearGradient-22)"></path>
                    <path d="M11.4760227,15.2869109 C11.5364936,15.2307443 14.5309203,14.2579387 14.3920613,13.9389124 C14.2509627,13.6198861 14.0045999,12.6897672 14.2733593,12.6807806 C14.5421186,12.6717939 14.7952003,13.2109933 14.9497369,13.4716063 C15.1042735,13.7322193 15.4850158,14.269172 15.3080826,13.6243795 C15.1311494,12.9795869 15.1266701,12.6583139 15.2588101,12.5819273 C15.3909501,12.5077874 15.5253297,12.6133806 15.7201802,13.0514801 C15.9127911,13.4873329 16.0202948,13.7322193 15.9083118,13.3323131 C15.7940891,12.9301603 15.7045026,12.3909609 15.8747168,12.3190676 C16.1076416,12.2202144 16.3607233,12.7886204 16.5690117,13.2783932 C16.739226,13.1503333 16.7101104,13.2469399 16.864647,12.838047 C17.0191836,12.4291542 17.1647616,12.0449746 17.3260171,12.0202613 C17.4872727,11.995548 17.5343056,12.912187 17.4671158,13.1907733 C17.399926,13.4693596 16.8400107,14.055739 16.2353023,14.5949383 C15.6305938,15.1341377 15.1490667,15.0712311 14.4861271,15.5542639 C13.8231874,16.0372966 12.0807312,17.5493015 12.0807312,17.5493015 L11.4760227,15.2869109 Z" id="路径" fill="url(#linearGradient-23)"></path>
                    <path d="M13.4245278,14.3006253 L14.2823179,15.9519234 C14.2823179,15.9519234 11.6999888,18.9399866 8.77947094,20.7193445 C7.4625503,18.4704338 7.35280691,17.5088616 7.35280691,17.5088616 C7.35280691,17.5088616 10.7929261,15.5542639 13.4245278,14.3006253 Z" id="路径" fill="url(#linearGradient-24)"></path>
                    <path d="M4.50395818,7.79428625 C4.50395818,7.79428625 7.40431912,9.74663731 8.10309333,9.90390379 C8.02918452,10.7464028 7.89480486,11.3574954 7.89480486,11.3574954 C7.89480486,11.3574954 9.97544989,14.8870047 9.79851668,19.1668997 C9.6193438,23.4467947 11.0482475,30.2744067 11.0482475,30.2744067 C11.0482475,30.2744067 5.81415984,32.0402847 1.05711997,31.4449187 C0.819715909,24.9790196 -0.530799646,12.9998069 3.85221684,9.95108374 C4.05826565,8.48176545 4.50395818,7.79428625 4.50395818,7.79428625 Z" id="路径" fill="url(#linearGradient-25)"></path>
                    <path d="M5.57227645,30.8113594 C5.57227645,30.8113594 5.57451612,30.8248394 5.5812351,30.8517994 C5.62826798,31.0652325 5.84999441,32.051518 6.00229136,32.2492244 C6.17474525,32.4693975 6.56444626,32.6738439 6.92727133,33.3950231 C6.55996694,34.2734687 5.52300391,35.4574607 4.10753819,34.8036814 C3.97315853,33.5657695 3.95524125,33.17485 4.02243107,32.7614638 C4.10753819,32.3682976 3.59241617,31.1011791 3.59241617,31.1011791 L5.57227645,30.8113594 Z" id="路径" fill="url(#linearGradient-26)"></path>
                    <path d="M3.8947704,32.3975042 L5.88806865,31.8358382 C5.88806865,31.8358382 7.52302115,10.9980292 3.27214465,11.7776216 C0.72788981,12.3392876 -0.156776267,20.314945 3.8947704,32.3975042 Z" id="路径" fill="url(#linearGradient-27)"></path>
                    <path d="M5.58347476,57.7960413 C5.58347476,57.7960413 6.50173575,58.8924134 8.42784417,57.9263478 C9.50288143,58.5127271 10.8690746,58.9800333 11.8097322,59.1530264 C12.7503898,59.3260195 12.8489349,60.3639783 12.7100759,60.6493047 C11.4536261,60.5908914 7.84553232,60.9952909 5.35278968,60.2673718 C5.47149171,58.5172205 5.58347476,57.7960413 5.58347476,57.7960413 Z" id="路径" fill="url(#linearGradient-28)"></path>
                    <path d="M5.20049274,60.8852044 L5.28559985,60.1325719 C5.28559985,60.1325719 8.14116757,60.626838 12.9161247,60.0606787 C12.9765956,60.4426116 13.0325871,60.6313313 13.0325871,60.6313313 C13.0325871,60.6313313 9.41105533,61.249164 5.20049274,60.8852044 Z" id="路径" fill="#B1D8F2"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
