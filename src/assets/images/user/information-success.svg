<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="28.875%" y1="0%" x2="71.125%" y2="100%" id="linearGradient-1">
            <stop stop-color="#007FFF" offset="0%"></stop>
            <stop stop-color="#00C6FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="34.8366471%" y1="14.875141%" x2="64.3050181%" y2="86.5374396%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#007FFF" stop-opacity="0" offset="49.6120213%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M15.9,1.99877619 C16.58952,1.99877619 17.25083,2.26216369 17.73846,2.73101369 C18.22609,3.19985119 18.5,3.83573869 18.5,4.49877619 L18.5,4.49877619 L18.5,19.4987762 C18.5,20.1617762 18.22609,20.7976512 17.73846,21.2665262 C17.25083,21.7354012 16.58952,21.9987762 15.9,21.9987762 L15.9,21.9987762 L8.1,21.9987762 C7.410441,21.9987762 6.749118,21.7354012 6.261527,21.2665262 C5.773923,20.7976512 5.5,20.1617762 5.5,19.4987762 L5.5,19.4987762 L5.5,4.49877619 C5.5,3.83573869 5.773923,3.19985119 6.261527,2.73101369 C6.749118,2.26216369 7.410441,1.99877619 8.1,1.99877619 L8.1,1.99877619 Z M12,16.9987762 C11.655214,16.9987762 11.324559,17.1305262 11.080757,17.3649012 C10.836968,17.5992762 10.7,17.9172762 10.7,18.2487762 C10.7,18.5802762 10.836968,18.8982762 11.080757,19.1326512 C11.324559,19.3670262 11.655214,19.4987762 12,19.4987762 C12.34476,19.4987762 12.67548,19.3670262 12.91923,19.1326512 C13.16298,18.8982762 13.3,18.5802762 13.3,18.2487762 C13.3,17.9172762 13.16298,17.5992762 12.91923,17.3649012 C12.67548,17.1305262 12.34476,16.9987762 12,16.9987762 Z" id="path-3"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="服务中心/我的消息" transform="translate(-560.000000, -280.000000)">
            <g id="编组-3" transform="translate(524.000000, 251.000000)">
                <g id="编组-2" transform="translate(24.000000, 17.000000)">
                    <g id="形状结合" transform="translate(12.000000, 12.000000)">
                        <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                        <use fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>