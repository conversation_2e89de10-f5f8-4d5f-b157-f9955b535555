<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="50.00001%" y1="-0.000369550686%" x2="50.00001%" y2="100.000698%" id="linearGradient-1">
            <stop stop-color="#E6EEFF" offset="0%"></stop>
            <stop stop-color="#F6F6F6" stop-opacity="0" offset="99.9999904%"></stop>
        </linearGradient>
        <linearGradient x1="49.990907%" y1="77.3385076%" x2="49.990907%" y2="21.0747498%" id="linearGradient-2">
            <stop stop-color="#E0EBF3" offset="0%"></stop>
            <stop stop-color="#ECF5FB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.825%" y1="0.00291120815%" x2="49.825%" y2="81.799285%" id="linearGradient-3">
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.990907%" y1="77.3385076%" x2="49.990907%" y2="21.0747498%" id="linearGradient-4">
            <stop stop-color="#DFE4F6" offset="0%"></stop>
            <stop stop-color="#ECF5FB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.825%" y1="0.00291120815%" x2="49.825%" y2="81.799285%" id="linearGradient-5">
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="74.9567591%" y1="6.68877698%" x2="25.0560196%" y2="93.3018705%" id="linearGradient-6">
            <stop stop-color="#FFDB80" offset="0%"></stop>
            <stop stop-color="#FFBB24" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9995325%" y1="-0.00887573964%" x2="49.9995325%" y2="99.9805082%" id="linearGradient-7">
            <stop stop-color="#F6FAFF" offset="0%"></stop>
            <stop stop-color="#DFE5F6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9849029%" y1="77.3427285%" x2="49.9849029%" y2="21.0661457%" id="linearGradient-8">
            <stop stop-color="#E8F1FF" offset="0%"></stop>
            <stop stop-color="#ECF4FB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.825%" y1="0.00291120815%" x2="49.825%" y2="81.799285%" id="linearGradient-9">
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="85.2589552%" y1="65.1080571%" x2="11.3177068%" y2="32.9447567%" id="linearGradient-10">
            <stop stop-color="#D5E0F5" offset="0%"></stop>
            <stop stop-color="#EAF9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="67.2765225%" y1="69.7462341%" x2="25.3220275%" y2="21.7915895%" id="linearGradient-11">
            <stop stop-color="#F1F3FF" offset="0%"></stop>
            <stop stop-color="#EAF3FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="76.6817244%" y1="67.6074166%" x2="11.8846216%" y2="24.8465046%" id="linearGradient-12">
            <stop stop-color="#E3E5FF" offset="1.90412615%"></stop>
            <stop stop-color="#FFFFFF" offset="97.7387115%"></stop>
        </linearGradient>
        <linearGradient x1="68.2361383%" y1="54.0874139%" x2="23.6491525%" y2="44.1706581%" id="linearGradient-13">
            <stop stop-color="#C7CEEF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.2361383%" y1="51.9986468%" x2="23.6491525%" y2="47.1495924%" id="linearGradient-14">
            <stop stop-color="#C7CEEF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="63.2813257%" y1="218.818471%" x2="30.8087556%" y2="-190.763623%" id="linearGradient-15">
            <stop stop-color="#BCC2E4" offset="0%"></stop>
            <stop stop-color="#E8F7FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="84.5564551%" y1="84.3680777%" x2="12.0884135%" y2="11.2024285%" id="linearGradient-16">
            <stop stop-color="#D5E0F5" offset="0%"></stop>
            <stop stop-color="#EAF9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="55.2541912%" y1="63.4160543%" x2="41.8824179%" y2="46.8989927%" id="linearGradient-17">
            <stop stop-color="#46BCAA" offset="0%"></stop>
            <stop stop-color="#6AE0CC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9874382%" y1="0.0478930318%" x2="49.9874382%" y2="100.046281%" id="linearGradient-18">
            <stop stop-color="#F4AE98" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="99.9994663%" y1="50.0011591%" x2="0.00132931636%" y2="50.0011591%" id="linearGradient-19">
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
        </linearGradient>
        <linearGradient x1="0.0029504698%" y1="50.0091196%" x2="100.026629%" y2="50.0091196%" id="linearGradient-20">
            <stop stop-color="#4F517C" offset="0%"></stop>
            <stop stop-color="#273B68" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0068642%" y1="-0.00654893304%" x2="50.0068642%" y2="65.1942667%" id="linearGradient-21">
            <stop stop-color="#181E4B" offset="0%"></stop>
            <stop stop-color="#2D3A65" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0068642%" y1="-0.00654893304%" x2="50.0068642%" y2="65.1942667%" id="linearGradient-22">
            <stop stop-color="#181E4B" offset="0%"></stop>
            <stop stop-color="#2D3A65" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9860162%" y1="100.00835%" x2="49.9860162%" y2="0.000800732098%" id="linearGradient-23">
            <stop stop-color="#FAD96E" offset="0%"></stop>
            <stop stop-color="#FFB32C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9818286%" y1="99.9563334%" x2="49.9818286%" y2="-0.00219431594%" id="linearGradient-24">
            <stop stop-color="#F4AE98" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.015892%" y1="100.007182%" x2="50.015892%" y2="-0.00727798703%" id="linearGradient-25">
            <stop stop-color="#FAD96E" offset="0%"></stop>
            <stop stop-color="#FFB32C" offset="54.39%"></stop>
        </linearGradient>
        <linearGradient x1="50.015892%" y1="100.007182%" x2="50.015892%" y2="-0.00727798703%" id="linearGradient-26">
            <stop stop-color="#FAD96E" offset="0%"></stop>
            <stop stop-color="#FFB32C" offset="54.39%"></stop>
        </linearGradient>
        <linearGradient x1="49.9889085%" y1="100.021369%" x2="49.9889085%" y2="34.8251403%" id="linearGradient-27">
            <stop stop-color="#50C6B3" offset="0%"></stop>
            <stop stop-color="#6AE0CC" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人中心/用户中心/贸易伙伴/无数据" transform="translate(-1334.000000, -290.000000)">
            <g id="缺省页/暂无发票" transform="translate(1334.000000, 290.000000)">
                <rect id="矩形" x="0" y="0" width="200" height="200"></rect>
                <g id="编组" transform="translate(0.000000, 22.000000)" fill-rule="nonzero">
                    <g>
                        <path d="M200,159.4386 C200,134.585786 155.228475,114.4386 100,114.4386 C44.771525,114.4386 0,134.585786 0,159.4386 L200,159.4386 Z" id="椭圆形" fill="url(#linearGradient-1)"></path>
                        <g transform="translate(16.000000, 104.000000)" id="路径">
                            <path d="M6.67494613,0.0546670673 C6.67494613,0.0546670673 6.62560421,-0.0741555655 6.55831978,0.0637072521 C5.95275987,1.30447261 0.469078469,14.7449673 0.0339724605,25.1185793 C0.0339724605,25.1185793 -0.881095847,31.0783211 6.78708686,30.9992195 C13.1880794,30.9336782 13.0176255,25.4395059 12.9951973,23.588528 C12.9077276,16.0806546 6.67494613,0.0546670673 6.67494613,0.0546670673 Z" fill="url(#linearGradient-2)"></path>
                            <path d="M6.45,37 C6.175,37 5.95,36.9017467 5.95,36.7816594 L5.95,4.21834061 C5.95,4.09825328 6.175,4 6.45,4 C6.725,4 6.95,4.09825328 6.95,4.21834061 L6.95,36.779476 C6.95,36.9017467 6.725,37 6.45,37 Z" fill="url(#linearGradient-3)" transform="translate(6.450000, 20.500000) scale(-1, 1) translate(-6.450000, -20.500000) "></path>
                        </g>
                        <g id="编组备份" transform="translate(165.000000, 79.000000)">
                            <path d="M10.2691479,0.0846457816 C10.2691479,0.0846457816 10.1932373,-0.114821521 10.0897227,0.098643487 C9.15809211,2.01982856 0.721659184,22.8309171 0.0522653238,38.8932841 C0.0522653238,38.8932841 -1.35553207,48.1212714 10.4416721,47.9987915 C20.2893529,47.8973081 20.0271161,39.3902026 19.9926113,36.5241724 C19.8580424,24.8990781 10.2691479,0.0846457816 10.2691479,0.0846457816 Z" id="路径" fill="url(#linearGradient-4)"></path>
                            <path d="M9.5,58 C9.225,58 9,57.8421993 9,57.6493317 L9,5.35066825 C9,5.15780071 9.225,5 9.5,5 C9.775,5 10,5.15780071 10,5.35066825 L10,57.6458251 C10,57.8421993 9.775,58 9.5,58 Z" id="路径" fill="url(#linearGradient-5)"></path>
                        </g>
                        <g id="编组-2" transform="translate(87.549967, 10.000000)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-6)" cx="25.002269" cy="9.75817733" rx="9.74788342" ry="9.75817733"></ellipse>
                            <path d="M30.5508484,15.0795431 L20.8562628,15.0795431 C20.2307152,12.1001399 17.5882673,9.86207735 14.4240623,9.86207735 C11.2598573,9.86207735 8.6202146,12.1001399 7.99186183,15.0795431 L5.45320442,15.0795431 C2.44328243,15.0795431 0,17.5225976 0,20.5385062 L0,20.5385062 C0,23.5516067 2.44047729,25.9974693 5.45320442,25.9974693 L30.5508484,25.9974693 C33.5607704,25.9974693 36.0040529,23.5544148 36.0040529,20.5385062 L36.0040529,20.5385062 C36.0040529,17.5225976 33.5635756,15.0795431 30.5508484,15.0795431 Z" id="路径" fill="url(#linearGradient-7)"></path>
                        </g>
                        <g transform="translate(181.554454, 141.375191)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-8)" cx="3.90195851" cy="3.90607904" rx="3.90195851" ry="3.90607904"></ellipse>
                            <path d="M3.94554557,15.6248093 C3.70554557,15.6248093 3.51554557,15.5739164 3.51554557,15.5096307 L3.51554557,3.73998785 C3.51554557,3.67570213 3.70554557,3.62480928 3.94554557,3.62480928 C4.18554557,3.62480928 4.37554557,3.67570213 4.37554557,3.73998785 L4.37554557,15.5096307 C4.37554557,15.5712378 4.18554557,15.6248093 3.94554557,15.6248093 Z" id="路径" fill="url(#linearGradient-9)"></path>
                        </g>
                    </g>
                </g>
                <g id="编组-2" transform="translate(49.000000, 71.000000)" fill-rule="nonzero">
                    <g id="编组" transform="translate(0.000000, 15.278684)">
                        <path d="M77.6553177,59.2273124 L11.6742717,59.2273124 C5.22756523,59.2273124 0,54.0046062 0,47.5586688 L0,11.6686436 C0,5.22504505 5.22522523,0 11.6742717,0 L77.6553177,0 C84.1020241,0 89.3295893,5.22270618 89.3295893,11.6686436 L89.3295893,47.5586688 C89.3295893,54.0022673 84.1020241,59.2273124 77.6553177,59.2273124 Z" id="路径" fill="url(#linearGradient-10)"></path>
                        <path d="M76.967357,59.2273124 L10.0994501,59.2273124 C4.52088452,59.2273124 0,54.7086074 0,49.1327312 L0,11.8370425 C0,6.26116634 4.52088452,1.74246131 10.0994501,1.74246131 L76.967357,1.74246131 C82.5459225,1.74246131 87.0668071,6.26116634 87.0668071,11.8370425 L87.0668071,49.1303923 C87.0668071,54.7062685 82.5459225,59.2273124 76.967357,59.2273124 Z" id="路径" fill="url(#linearGradient-11)"></path>
                        <path d="M88.3257283,73.7213161 L22.3446823,73.7213161 C15.8979759,73.7213161 10.6704107,68.4986099 10.6704107,62.0526725 L10.6704107,26.1626473 C10.6704107,19.7190487 15.8956359,14.4940037 22.3446823,14.4940037 L88.3257283,14.4940037 C94.7724348,14.4940037 100,19.7167098 100,26.1626473 L100,62.0526725 C100,68.496271 94.7724348,73.7213161 88.3257283,73.7213161 Z" id="路径" fill="url(#linearGradient-10)"></path>
                        <path d="M87.6377676,73.7213161 L20.7698608,73.7213161 C15.1912952,73.7213161 10.6704107,69.202611 10.6704107,63.6267348 L10.6704107,26.3310462 C10.6704107,20.75517 15.1912952,16.236465 20.7698608,16.236465 L87.6377676,16.236465 C93.2163332,16.236465 97.7372177,20.75517 97.7372177,26.3310462 L97.7372177,63.624396 C97.7372177,69.2002721 93.2163332,73.7213161 87.6377676,73.7213161 Z" id="路径" fill="url(#linearGradient-12)"></path>
                        <path d="M62.3774424,61.6690971 L25.6440856,61.6690971 C24.1324441,61.6690971 22.9062829,60.1885897 22.9062829,58.3642678 C22.9062829,56.537607 24.1324441,55.0594385 25.6440856,55.0594385 L62.3774424,55.0594385 C63.8890839,55.0594385 65.3843454,56.5867234 65.3843454,58.4133841 C65.3843454,60.2400449 63.8890839,61.6690971 62.3774424,61.6690971 Z" id="路径" fill="url(#linearGradient-13)"></path>
                        <path d="M62.3774424,47.6381905 L25.6440856,47.6381905 C24.1324441,47.6381905 22.9062829,46.1576831 22.9062829,44.3333612 C22.9062829,42.5067004 24.1324441,41.0285319 25.6440856,41.0285319 L62.3774424,41.0285319 C63.8890839,41.0285319 65.3843454,42.5558168 65.3843454,44.3824776 C65.3843454,46.2067995 63.8890839,47.6381905 62.3774424,47.6381905 Z" id="路径" fill="url(#linearGradient-13)"></path>
                        <path d="M80.6458406,33.5955896 L25.6440856,33.5955896 C24.1324441,33.5955896 22.9062829,32.1150822 22.9062829,30.2907603 C22.9062829,28.4640995 24.1324441,26.985931 25.6440856,26.985931 L80.6458406,26.985931 C82.1574822,26.985931 83.6527437,28.5132159 83.6527437,30.3398766 C83.6527437,32.1641986 82.1598222,33.5955896 80.6458406,33.5955896 Z" id="路径" fill="url(#linearGradient-14)"></path>
                        <path d="M80.1310401,33.5838952 L25.6440856,33.5838952 C24.1324441,33.5838952 22.9062829,32.3583251 22.9062829,30.8474124 C22.9062829,29.3364996 24.1324441,28.1109295 25.6440856,28.1109295 L80.1310401,28.1109295 C81.6426816,28.1109295 82.8688429,29.3364996 82.8688429,30.8474124 C82.8688429,32.3583251 81.6450216,33.5838952 80.1310401,33.5838952 Z" id="路径" fill="#FFFFFF"></path>
                        <path d="M62.3774424,47.6615793 L25.6440856,47.6615793 C24.1324441,47.6615793 22.9062829,46.4360092 22.9062829,44.9250964 C22.9062829,43.4141836 24.1324441,42.1886135 25.6440856,42.1886135 L62.3774424,42.1886135 C63.8890839,42.1886135 65.1152451,43.4141836 65.1152451,44.9250964 C65.1152451,46.4360092 63.8890839,47.6615793 62.3774424,47.6615793 Z" id="路径" fill="#FFFFFF"></path>
                        <path d="M62.3774424,61.6690971 L25.6440856,61.6690971 C24.1324441,61.6690971 22.9062829,60.443527 22.9062829,58.9326142 C22.9062829,57.4217015 24.1324441,56.1961314 25.6440856,56.1961314 L62.3774424,56.1961314 C63.8890839,56.1961314 65.1152451,57.4217015 65.1152451,58.9326142 C65.1152451,60.443527 63.8890839,61.6690971 62.3774424,61.6690971 Z" id="路径" fill="#FFFFFF"></path>
                        <path d="M85.6651457,52.278517 L81.5444015,52.278517 L81.5444015,50.9664086 L85.6651457,50.9664086 C86.9170469,50.9664086 87.9326079,49.9606926 87.9326079,48.7210893 C87.9326079,47.4814859 86.9170469,46.47577 85.6651457,46.47577 L83.8025038,46.47577 L85.5434655,44.653787 C86.4045864,43.7533204 86.3648064,42.3312849 85.4545455,41.4775958 C84.5442845,40.6239067 83.1098631,40.6636675 82.2487422,41.566473 L79.2184392,44.7403253 L75.9494559,41.5828451 C75.0532351,40.7174616 73.6188136,40.7338338 72.7436527,41.622606 C71.8684919,42.5113782 71.8872119,43.9334137 72.7834328,44.7987972 L74.5197145,46.47577 L72.8887329,46.47577 C71.6368316,46.47577 70.6212706,47.4814859 70.6212706,48.7210893 C70.6212706,49.9606926 71.6368316,50.9664086 72.8887329,50.9664086 L77.009477,50.9664086 L77.009477,52.278517 L72.8887329,52.278517 C71.6368316,52.278517 70.6212706,53.2842329 70.6212706,54.5238363 C70.6212706,55.7634396 71.6368316,56.7691556 72.8887329,56.7691556 L77.009477,56.7691556 L77.009477,58.9022089 C77.009477,60.1418122 78.025038,61.1475281 79.2769393,61.1475281 C80.5288405,61.1475281 81.5444015,60.1418122 81.5444015,58.9022089 L81.5444015,56.7691556 L85.6651457,56.7691556 C86.9170469,56.7691556 87.9326079,55.7634396 87.9326079,54.5238363 C87.9326079,53.2842329 86.9170469,52.278517 85.6651457,52.278517 Z" id="路径" fill="url(#linearGradient-15)"></path>
                        <path d="M84.8695449,52.5170822 L80.968761,52.5170822 L80.968761,51.2634456 L84.8718849,51.2634456 C86.0582661,51.2634456 87.020007,50.3021683 87.020007,49.116359 C87.020007,47.9305498 86.0582661,46.9692725 84.8718849,46.9692725 L83.1075231,46.9692725 L84.7548848,45.2291501 C85.5692056,44.3684443 85.5317655,43.0095584 84.6706447,42.1956302 C83.8095238,41.3817019 82.4499824,41.4191239 81.6356616,42.2798296 L78.7644788,45.3110107 L75.6686557,42.2938629 C74.8192348,41.4659014 73.4596935,41.4822735 72.6336726,42.3312849 C71.8053118,43.1802962 71.8216918,44.5391822 72.6711127,45.3648048 L74.3161343,46.9669336 L72.7717328,46.9669336 C71.5853516,46.9669336 70.6236106,47.9282109 70.6236106,49.1140202 C70.6236106,50.2998294 71.5853516,51.2611067 72.7717328,51.2611067 L76.6748567,51.2611067 L76.6748567,52.5147433 L72.7717328,52.5147433 C71.5853516,52.5147433 70.6236106,53.4760206 70.6236106,54.6618299 C70.6236106,55.8476391 71.5853516,56.8089164 72.7717328,56.8089164 L76.6748567,56.8089164 L76.6748567,58.8460759 C76.6748567,60.0318851 77.6365976,60.9931624 78.8229788,60.9931624 C80.00936,60.9931624 80.971101,60.0318851 80.971101,58.8460759 L80.971101,56.8089164 L84.8742249,56.8089164 C86.0606061,56.8089164 87.022347,55.8476391 87.022347,54.6618299 C87.017667,53.4783595 86.0559261,52.5170822 84.8695449,52.5170822 Z" id="路径" fill="#FFFFFF"></path>
                        <path d="M47.1884872,43.9591413 L38.7317187,35.6748488 C38.0531181,35.0059308 37.1124371,34.7626878 36.2442962,34.9404423 L34.7279747,33.4178351 C37.1030771,30.6088472 38.5398385,26.9835921 38.5398385,23.0285558 C38.5398385,14.1408337 31.3068913,6.91137338 22.4148824,6.91137338 C13.5228735,6.91137338 6.28992629,14.1408337 6.28992629,23.0285558 C6.28992629,31.9162779 13.5228735,39.1457382 22.4148824,39.1457382 C26.2407862,39.1457382 29.7554698,37.8055632 32.5236925,35.5719383 L34.0914941,37.1460007 C33.9370539,38.0113842 34.1967942,38.9352395 34.8707149,39.5971409 L42.5623026,48.3047697 C43.0981631,48.8310164 43.7954838,49.0953092 44.4928045,49.0953092 C45.2041652,49.0953092 46.4092664,48.9643322 47.3288873,47.9586163 C48.3538084,46.8359567 48.2719083,45.0233291 47.1884872,43.9591413 Z M22.4125424,33.6400282 C16.5578566,33.6400282 11.7959518,28.8804191 11.7959518,23.0285558 C11.7959518,17.1766924 16.5578566,12.4170833 22.4125424,12.4170833 C28.2672283,12.4170833 33.0291339,17.1766924 33.0291339,23.0285558 C33.031473,28.8804191 28.2672283,33.6400282 22.4125424,33.6400282 Z" id="形状" fill="url(#linearGradient-16)"></path>
                        <path d="M46.2478062,44.4783714 L37.2949573,35.6771877 C36.6374166,35.0293195 35.7271557,34.7930932 34.8847549,34.9661699 L33.4152334,33.4903402 C35.7154557,30.7702295 37.1077571,27.2572404 37.1077571,23.4238255 C37.1077571,14.8144294 30.0994501,7.80950109 21.4859015,7.80950109 C12.8723529,7.80950109 5.86404586,14.8144294 5.86404586,23.4238255 C5.86404586,32.0332216 12.8723529,39.0381499 21.4859015,39.0381499 C25.1924652,39.0381499 28.5971686,37.7400747 31.2788113,35.5766161 L32.7974728,37.1015621 C32.6477126,37.938879 32.8980929,38.8346679 33.5509536,39.4755194 L42.5038025,48.2767032 C43.023283,48.7865778 43.6995437,49.0415151 44.3734644,49.0415151 C45.0637651,49.0415151 45.7540658,48.7748834 46.2758863,48.243959 C47.3125073,47.200821 47.2984673,45.5121538 46.2478062,44.4783714 Z M21.4835615,33.7078555 C15.8113958,33.7078555 11.1969112,29.0955955 11.1969112,23.4261644 C11.1969112,17.7567332 15.8113958,13.1444732 21.4835615,13.1444732 C27.1557272,13.1444732 31.7702118,17.7567332 31.7702118,23.4261644 C31.7702118,29.0955955 27.1557272,33.7078555 21.4835615,33.7078555 Z" id="形状" fill="#FFFFFF"></path>
                    </g>
                    <g id="编组" transform="translate(37.892690, 0.000000)">
                        <path d="M16.9195251,41.2121215 C16.9195251,41.2121215 17.1792654,42.6388348 15.2862035,43.5393014 C15.0709233,44.7648715 14.5865428,46.1401295 14.1442823,46.9938186 C13.7020219,47.8475077 14.4157226,48.5889309 14.7129029,48.661436 C15.4336236,47.6323313 17.9304061,45.0431975 18.9061871,42.6084294 C17.5232457,41.5863414 16.9195251,41.2121215 16.9195251,41.2121215 Z" id="路径" fill="url(#linearGradient-17)"></path>
                        <path d="M19.4537476,42.8867555 L18.840667,42.4704359 C18.840667,42.4704359 17.4717657,45.0408587 14.1466223,48.4462595 C14.3946626,48.7386188 14.4999627,48.90234 14.4999627,48.90234 C14.4999627,48.90234 17.1652253,46.4441832 19.4537476,42.8867555 Z" id="路径" fill="#B1D8F2"></path>
                        <path d="M17.6566258,6.87276993 L16.9288851,10.5681912 C16.8937851,10.7412679 16.9733452,10.9190224 17.1184253,10.9845108 C17.4951657,11.1599264 18.2463064,11.4546246 19.0208472,11.4546246 C19.823468,11.4546246 20.5816288,10.9541055 20.9302891,10.6874738 C21.0496292,10.5962577 21.1011093,10.4255199 21.0543092,10.2688153 L19.9404681,6.49855005 C19.8889881,6.32781223 19.7368879,6.22256289 19.5801078,6.25530713 L17.9116861,6.59210502 C17.785326,6.62017151 17.6847059,6.7300986 17.6566258,6.87276993 Z" id="路径" fill="url(#linearGradient-18)"></path>
                        <path d="M21.3959496,5.75244917 C21.5012497,5.59106685 21.5691098,5.17942498 21.4825297,5.06014239 C21.3257495,4.92682656 21.1759894,5.00400941 21.0613292,5.12095312 C21.0870693,4.13862594 21.0028292,3.32235883 20.9841092,3.11419903 C20.9256091,2.51310835 20.3452885,1.40915971 18.4475466,1.40915971 C16.5498047,1.40915971 16.1824244,2.85458399 16.1824244,2.85458399 C16.1824244,2.85458399 16.0584042,3.87667203 16.0958443,5.11627538 C15.9835242,5.00167054 15.836104,4.92916544 15.6793239,5.06014239 C15.5927438,5.17942498 15.6606038,5.59106685 15.765904,5.75244917 C15.8712041,5.91383149 15.9390641,6.29506799 15.9601241,6.50556668 C15.9788442,6.70670986 15.840784,7.23997319 16.3415445,7.17214583 C16.6387248,8.43046017 17.846166,9.48061471 18.5949668,9.48061471 C19.4139676,9.48061471 20.5020687,8.42344355 20.813289,7.16980696 C21.3234095,7.24465093 21.1853494,6.70670986 21.2040694,6.50556668 C21.2204494,6.29506799 21.2883095,5.91383149 21.3959496,5.75244917 Z" id="路径" fill="url(#linearGradient-19)"></path>
                        <path d="M19.816448,0.98114572 C19.816448,0.98114572 19.4467276,-0.249102131 18.0263462,0.0455960237 C16.6059648,0.340294178 16.5123647,1.04897307 15.9531041,1.07470069 C15.0007232,1.12147817 14.2425624,2.37277589 15.4617036,3.69657871 C16.0045842,4.28597502 15.5459437,4.53857344 15.803344,5.06014239 C16.0607442,5.58171135 16.0584042,6.08924706 16.0584042,6.08924706 C16.0584042,6.08924706 16.5123647,4.56196218 16.2128444,3.90941627 C16.0584042,3.57261838 16.7416849,3.48140228 17.5934458,3.56092401 C18.4452066,3.64044573 19.786028,3.3434087 19.8936681,2.73062365 C20.1463883,3.98426024 20.3967686,4.1152372 20.7173489,4.30702489 C21.0355892,4.49881258 21.0566492,6.01206421 21.0566492,6.01206421 C21.0566492,6.01206421 21.1128093,4.89876007 21.3351095,4.64850053 C21.5199697,4.23685866 21.8779901,2.62069656 21.4708297,2.23010456 C21.0660093,1.83951257 21.4099896,0.583537099 19.816448,0.98114572 Z" id="路径" fill="url(#linearGradient-20)"></path>
                        <path d="M14.3478625,25.2493048 C14.2566024,25.3568931 14.3478625,25.0084008 12.2512204,26.8537726 C10.1545783,28.6991443 7.15937534,30.787759 7.18263909,32.7126525 C7.20617539,34.6398849 15.5459437,43.7895609 15.5459437,43.7895609 L17.4343256,41.8366009 C17.4343256,41.8366009 13.9547421,35.5754346 13.2644414,34.6656125 C12.5741408,33.7557904 12.826861,33.1944606 13.6552218,32.684586 C14.4835827,32.1770503 17.2260654,31.34675 19.6690279,29.39379 C22.1119903,27.44083 21.9949902,24.3090774 21.9949902,24.3090774 L14.3478625,25.2493048 Z" id="路径" fill="url(#linearGradient-21)"></path>
                        <path d="M17.9889062,24.5242538 C18.0123062,24.6084533 17.797026,29.078042 18.7049469,31.3818331 C19.6128678,33.6856242 19.5403277,36.1835419 19.5403277,37.0442476 C19.5403277,37.9049533 19.5637277,38.6814596 19.851548,40.2929439 C20.1370283,41.9020894 21.0449492,54.0899631 21.0449492,54.0899631 L23.3124115,54.1460961 C23.3124115,54.1460961 24.4098726,42.8072338 24.4098726,41.0437226 C24.4098726,39.2404505 24.6485528,38.1973126 24.791293,36.3519409 C24.9293531,34.5603632 25.2686535,33.243577 25.5073337,29.1622414 C25.7460139,25.0809059 22.8818511,22.3607951 22.8818511,22.3607951 L17.9889062,24.5242538 Z" id="路径" fill="url(#linearGradient-22)"></path>
                        <path d="M26.3614345,15.6365317 C26.3614345,15.6365317 26.1461543,9.50400345 22.6314708,9.13446132 C21.2321494,9.03388973 20.5067487,8.99412887 20.5067487,8.99412887 C20.5067487,8.99412887 20.2470084,11.1833152 18.847687,11.0546771 C17.4460256,10.926039 17.3711456,9.13212245 17.3711456,9.13212245 C17.3711456,9.13212245 13.8798621,9.14849457 12.766021,10.982172 C11.6498398,12.8135105 11.3011795,16.9346069 11.3011795,16.9346069 L13.844762,15.6435483 L14.0764223,24.4049712 C13.9851622,24.7020083 13.842422,25.2609992 13.6622418,25.4621424 C12.9274811,26.2924427 11.9727602,26.8724836 11.9727602,26.8724836 C11.9727602,26.8724836 18.810247,27.8220665 20.0013082,27.8220665 C21.1923694,27.8220665 24.2928725,27.6349566 25.6758139,29.4405675 L25.7460139,26.6315795 C25.6594338,22.3163565 24.798313,19.7248839 24.6391928,19.0068495 L24.0167522,15.4751494 L26.3614345,15.6365317 Z" id="路径" fill="url(#linearGradient-23)"></path>
                        <path d="M33.5054617,12.8135105 C33.5054617,12.8135105 33.9500621,12.3176692 34.0319622,12.0299876 C34.1138623,11.7423061 34.4063626,11.6347179 34.7222629,11.5154353 C35.0381632,11.3961527 35.5974238,11.4359136 36.1098843,11.241787 C36.6223448,11.0476604 37.5349457,10.5401247 37.7127859,10.6196465 C37.8906261,10.6991682 37.808726,11.148232 37.5489857,11.4429302 C37.2892455,11.7376284 36.2947445,12.4860681 35.857164,12.6334172 C35.4195836,12.7807663 34.9258431,13.3888736 34.3806226,14.1794131 C33.835402,14.9699526 33.0491612,13.5970334 33.0491612,13.5970334 L33.5054617,12.8135105 Z" id="路径" fill="url(#linearGradient-24)"></path>
                        <path d="M4.30223249,12.9234376 C4.30223249,12.9234376 3.85763204,12.4275963 3.77573196,12.1399147 C3.69383188,11.8522332 3.40133159,11.744645 3.08543127,11.6253624 C2.76953095,11.5060798 2.21027039,11.5458407 1.69780988,11.3517141 C1.18534937,11.1575875 0.272748457,10.6500518 0.0949082796,10.7295736 C-0.0829318983,10.8090953 -0.00103181638,11.2581591 0.258708443,11.5528573 C0.518448703,11.8475554 1.5129497,12.5959952 1.95053014,12.7433443 C2.38811057,12.8906934 2.88185107,13.4988007 3.42707161,14.2893401 C3.97229216,15.0798796 4.75853294,13.7069605 4.75853294,13.7069605 L4.30223249,12.9234376 Z" id="路径" fill="url(#linearGradient-24)"></path>
                        <path d="M13.1427613,10.479314 C13.1427613,10.479314 10.821479,12.5655898 8.47211666,16.8737962 C6.78965497,14.8670421 4.00271219,12.19137 4.00271219,12.19137 L3.0128912,13.8496318 C3.0128912,13.8496318 6.56033474,20.6183338 8.65697684,20.1903199 C10.7536189,19.7623059 13.826042,16.3615827 14.2215024,15.6950036 C14.832243,14.6658989 14.6941829,13.2064414 14.3946626,11.9223994 C14.0951423,10.6406963 13.7605219,10.0513 13.1427613,10.479314 Z" id="路径" fill="url(#linearGradient-25)"></path>
                        <path d="M24.7444929,10.2992207 C24.7444929,10.2992207 27.0657753,12.3854965 29.4151376,16.6937029 C31.0975993,14.6869488 33.8845421,12.0112766 33.8845421,12.0112766 L34.8743631,13.6695385 C34.8743631,13.6695385 31.3269195,20.4382405 29.2302774,20.0102265 C27.1336353,19.5822126 24.0916323,15.7838808 23.7008519,15.1149628 C23.3100715,14.4460447 23.1930714,13.0286869 23.4925917,11.744645 C23.792112,10.460603 24.1290723,9.87120671 24.7444929,10.2992207 Z" id="路径" fill="url(#linearGradient-26)"></path>
                        <path d="M20.8694491,54.0502022 C20.8694491,54.0502022 21.1011093,53.1941743 21.9224501,53.3134568 C22.7414509,53.4327394 22.9988512,54.1835181 23.1954114,54.129724 C23.3919716,54.0759298 23.4645116,53.5683941 23.5627917,53.8350258 C23.6610718,54.1016575 23.7429719,54.6395985 23.7429719,54.6395985 C23.7429719,54.6395985 23.6517118,55.7879858 23.5861918,56.4802926 C23.5230117,57.1725993 23.5861918,57.6848128 22.3366305,57.9257168 C21.0870693,58.1666209 20.7196889,58.2975978 20.0972483,57.9654777 C19.4724677,57.6333576 19.858568,56.5598143 19.9872682,56.2908438 C20.1159683,56.0218732 20.8694491,54.0502022 20.8694491,54.0502022 Z" id="路径" fill="url(#linearGradient-27)"></path>
                        <path d="M23.7265919,54.4595052 C23.7265919,54.4595052 23.5113117,56.6323194 23.2796515,57.0205725 C23.0479912,57.4088256 21.5410297,58.2367871 20.5371687,57.9444278 C19.9989682,57.811112 19.771988,57.4602809 19.7205079,57.2497822 C19.7205079,57.6403742 19.7205079,57.9327335 19.7205079,57.9327335 C19.7205079,57.9327335 20.2446684,58.5806016 21.3631895,58.5010799 C22.4840507,58.4215582 23.5042917,57.7853844 23.6704319,57.217038 C23.836572,56.6486915 23.836572,55.0699514 23.836572,55.0699514 L23.7265919,54.4595052 Z" id="路径" fill="#B1D8F2"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
