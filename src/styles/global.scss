@import 'antd.scss';
@import 'naive.scss';

* {
  // 常规
  font-family: PingFangSC-Regular, PingFang SC, Arial, Microsoft YaHei;
}

body {
  min-width: 1440px;
  background-color: RGB(246, 246, 246);

  h1,
  h2,
  p {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  ul,
  ol {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }
}

// 解决弹窗内部封装的 n-scrollbar 未完全展示内部 DOM 的问题
.n-scrollbar {
  display: inline-block;
  // width: 100%;
}
.ant-select,.ant-select-selector,
.ant-select-selection-placeholder,
.ant-select-selection-item,
.ant-select-selection-placeholder {
  height: 48px !important;
  line-height: 48px !important;
}
