.borrow-wave {
  padding: 0 !important;
  margin: 0;

  .el-form-item__content {
    margin-left: 20px !important;
  }
  // .el-input--small {
  //   font-size: 20px;
  // }

  // .el-input.is-disabled .el-input__inner {
  //   color: #000000 !important;
  //   background-color: #f5f7fa00 !important;
  //   border-color: #fdfeff0d !important;
  // }
}

.borrow-right {
  left: -3.5%;
}

.annual {
  z-index: 1;

  .el-form-item {
    width: auto;
  }
}

.loanstime {
  .el-form-item {
    width: auto;
  }
}

.loansleft1 {
  padding-right: 0 !important;
}

.loansleft {
  padding: 0 !important;
}

.loansright {
  padding: 0 !important;
}

@media screen and (min-width: 1910px) {
  .loansleft {
    width: 10% !important;
  }

  .loansright {
    width: 10% !important;
    margin-right: 4% !important;
  }
}

@media screen and (min-width: 1700px) and (max-width: 1900px) {
  .loansleft {
    width: 10% !important;
  }

  .loansright {
    width: 10% !important;
    margin-right: 3.3% !important;
  }
}

@media screen and (max-width: 1699px) {
  .loansleft {
    width: 15% !important;
    // margin-right: 2% !important;
    margin-left: 4% !important;
  }

  .loansright {
    width: 15% !important;
  }
}

@media screen and (max-width: 1600px) {
  .annual .el-form-item {
    width: 222px !important;
  }
}

@media screen and (max-width: 1900px) {
  .loanstime .el-form-item {
    width: 185px !important;
  }
}
