@mixin clear-a {
  text-decoration: none;
  color: inherit;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-appearance: none;
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
  border-bottom: none;
}

@mixin family-PingFangSC-Semibold {
  font-family: PingFangSC-Semibold, PingFang SC, Arial, Microsoft YaHei;
}

@mixin family-PingFangSC-Semibold-SFProText {
  font-family: SFProText-Semibold, SFProText, Arial, Microsoft YaHei;
  // @include family-PingFangSC-Semibold-SFProText;
}

@mixin family-PingFangSC-Medium {
  font-family: PingFangSC-Medium, PingFang SC, Arial, Microsoft YaHei;
  // @include family-PingFangSC-Medium;
}

@mixin family-PingFangSC-Medium-SFProText {
  font-family: SFProText-Medium, SFProText, <PERSON><PERSON>, Microsoft YaHei;
  // @include family-PingFangSC-Medium-SFProText;
}

@mixin family-PingFangSC-Regular-PingFang {
  font-family: PingFangSC-Regular, PingFang SC, Arial, Microsoft YaHei;
  // @include family-PingFangSC-Regular-PingFang;
}

@mixin family-CoreSansD65Heavy {
  font-family: CoreSansD65Heavy, Arial, Microsoft YaHei;
  // @include family-CoreSansD65Heavy;
}

@mixin family-CoreSansD75Black {
  font-family: CoreSansD75Black, Arial, Microsoft YaHei;
}

@mixin family-SFProText {
  font-family: SFProText-Medium, SFProText, Arial, Microsoft YaHei;
}

@mixin family-SFProText-Regular{
  font-family: SFProText-Regular, SFProText, Arial, Microsoft YaHei;
  // @include family-SFProText-Regular;
}


@mixin family-SFProDisplay-Bold {
  font-family: SFProDisplay-Bold, SFProDisplay, Arial, Microsoft YaHei;
  // @include family-SFProDisplay-Bold;
}