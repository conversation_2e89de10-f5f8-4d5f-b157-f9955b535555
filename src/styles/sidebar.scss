.el-menu--popup {
  .el-menu-item {
    background-color: #20222a;
    i,
    span {
      color: hsla(0, 0%, 100%, 0.7);
    }
    &:hover {
      i,
      span {
        color: #fff;
      }
    }
    &.is-active {
      background-color: rgba(0, 0, 0, 0.8);
      &:before {
        content: '';
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: #409eff;
        position: absolute;
      }
      i,
      span {
        color: #fff;
      }
    }
  }
}
.avue-sidebar {
  user-select: none;
  position: relative;
  padding-top: 60px;
  height: 100%;
  position: relative;
  background-color: #20222a;
  transition: width 0.2s;
  box-sizing: border-box;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  &--tip {
    width: 90%;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    position: absolute;
    top: 5px;
    left: 5%;
    color: #ccc;
    z-index: 2;
    text-align: center;
    font-size: 14px;
    background-color: rgba(0, 0, 0, 0.4);
  }
  .el-menu-item,
  .el-submenu__title {
    i {
      margin-right: 10px;
    }
    i,
    span {
      color: hsla(0, 0%, 100%, 0.7);
    }
    &:hover {
      background: transparent;
      i,
      span {
        color: #fff;
      }
    }
    &.is-active {
      &:before {
        content: '';
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: #409eff;
        position: absolute;
      }
      background-color: rgba(0, 0, 0, 0.8);
      i,
      span {
        color: #fff;
      }
    }
  }
}
