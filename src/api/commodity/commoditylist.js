import request from '@/router/axios'
import { API, WEB_BACK, BLADE_COMMODITY } from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getDetail = id => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/detail',
    method: 'get',
    params: {
      id,
    },
  })
}

export const remove = ids => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/remove',
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/save',
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/update',
    method: 'post',
    data: row,
  })
}

export const putShelf = id => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/putShelf',
    method: 'post',
    data: id,
  })
}
export const offShelf = id => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/offShelf',
    method: 'post',
    data: id,
  })
}

export const batchShelf = (ids, status) => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/batchShelf',
    method: 'post',
    data: {
      ids,
      status,
    },
  })
}
export const getshuju = id => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditylist/detail',
    method: 'get',
    params: {
      id,
    },
  })
}

/********** 商品价格维护表 **********/
// 列表
export const getGoodPriceList = params => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditySpec/page',
    method: 'get',
    params,
  })
}

// 价格参数详情
export const getGoodPriceDetail = params => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditySpec/details',
    method: 'get',
    params,
  })
}

// 价格维护提交
export const submitGoodPrice = data => {
  return request({
    url: API + BLADE_COMMODITY + WEB_BACK + '/commoditySpec/updatePrice',
    method: 'post',
    data,
  })
}

// 根据id查询具体的某个分类 /blade-commodity/web-back/commoditycatalogue/enable/getCatalogueById
export const getCatalogueById = id => request({
  url:'/api/blade-commodity/web-back/commoditycatalogue/enable/getCatalogueById',
  method:'get',
  params:{
    id
  }
})
