import request from '@/router/axios'
import { BLADE_GOODS_PREFIX } from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: BLADE_GOODS_PREFIX + `/goods/goodstype/list`,
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getDetail = id => {
  return request({
    url: BLADE_GOODS_PREFIX + `/goods/goodstype/detail`,
    method: 'get',
    params: {
      id,
    },
  })
}

export const remove = ids => {
  return request({
    url: BLADE_GOODS_PREFIX + `/goods/goodstype/remove`,
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: BLADE_GOODS_PREFIX + `/goods/goodstype/save`,
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: BLADE_GOODS_PREFIX + `/goods/goodstype/update`,
    method: 'post',
    data: row,
  })
}

export const addPraent = row => {
  return request({
    url: BLADE_GOODS_PREFIX + `/goods/goodstype/saveParent`,
    method: 'post',
    data: row,
  })
}
