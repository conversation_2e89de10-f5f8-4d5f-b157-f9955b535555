import request from '@/router/axios'
import {BLADE_GOODS_PREFIX} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: BLADE_GOODS_PREFIX+`/goods/label/list`,
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getDetail = id => {
  return request({
    url: BLADE_GOODS_PREFIX+`/goods/label/detail`,
    method: 'get',
    params: {
      id,
    },
  })
}

export const remove = ids => {
  return request({
    url: BLADE_GOODS_PREFIX+`/goods/label/remove`,
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: BLADE_GOODS_PREFIX+`/goods/label/save`,
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: BLADE_GOODS_PREFIX+`/goods/label/update`,
    method: 'post',
    data: row,
  })
}
export const batchEnabled = (ids) => {
  return request({
    url: BLADE_GOODS_PREFIX+`/goods/label/batchEnabled`,
    method: 'post',
    params: {
      ids,
    }
  })
}
export const batchDisabled = (ids) => {
  return request({
    url: BLADE_GOODS_PREFIX+`/goods/label/batchDisabled`,
    method: 'post',
    params: {
      ids,
    }
  })
}
