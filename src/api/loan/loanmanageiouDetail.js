import request from '@/router/axios';
import { BLADE_LOAN_PREFIX } from '@/config/apiPrefix'

export const getDetail = (id) => {
  return request({
    url: BLADE_LOAN_PREFIX + '/v2/loan/loanManageIou/detail',
    method: 'get',
    params: {
      id
    }
  })
}
export const getRepaymentDetail = id => request({
  url:BLADE_LOAN_PREFIX +  '/v2/loan/loanManageIou/advanceSettledById',
  method: 'get',
  params: {
    id
  }
})
// /blade-loan/web-back/loan/advanceSettledApply/applyAdvanceSettled
export const applyAdvanceSettled = data => request({
  url:BLADE_LOAN_PREFIX + '/loan/advanceSettledApply/applyAdvanceSettled',
  method:"post",
  data
})

// /blade-loan/web-back/loan/loanManageIou/repaymentDetails 还款明细
export const repaymentDetails = financeNo => request({
  url:'/api/blade-loan/web-back/v2/loan/loanManageIou/repaymentDetails',
  method:'get',
  params:{
    financeNo
  }
})
