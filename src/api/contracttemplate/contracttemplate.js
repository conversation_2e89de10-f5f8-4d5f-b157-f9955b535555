import request from '@/router/axios'
import {API, BLADE_CONTRACT, WEB_BACK} from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

/**
 * 同步模板
 * @param current
 * @param size
 * @returns {AxiosPromise}
 */
export const syncTemplate = (current, size) => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + "/contractTemplate/syncContractTemplate",
    method: 'post',
    params: {
      current,
      size
    }
  })
}

export const getData = templateId => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/detail',
    method: 'get',
    params: {
      templateId,
    },
  })
}

export const remove = ids => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/remove',
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/submit',
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/submit',
    method: 'post',
    data: row,
  })
}
export const skipToTemplate = () => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/skipToTemplate',
    method: 'post',
  })
}

export const updateStatus = (data) => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/updateStatus',
    method: 'post',
    data: data
  })
}
export const skipToTemplateDetails = (row) => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/skipToTemplateDetails',
    method: 'post',
    data: row
  })
}
export const getdata = contractId => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractlist/getDetails',
    method: 'get',
    params: {contractId},
  })
}
// 提醒合同签署
export const getcontract = contractId => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractlist/contractRemind',
    method: 'post',
    data: {
      contractId,
    },
  })
}
// 下载合同
export const downloadContract = contractId => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractlist/contractDownload',
    method: 'post',
    params: {
      contractId,
    },
  })
}
// 下载子合同
export const downloadSubContract = subContractId => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractlist/getSubContractUrl',
    method: 'post',
    params: {
      subContractId,
    },
  })
}
// 撤回合同
export const revokecontract = contractId => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractlist/contractRevoke',
    method: 'post',
    data: {contractId},
  })
}

export const getcontractTemplate = templateId => {
  // 查询合同模板详情接口
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/getTemplatePicUrl',
    method: 'post',
    data: {
      templateId: templateId,
    }
  })
}

//重新发送合同
export const resendContract = contractId => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractlist/delay',
    method: 'post',
    params: {
      contractId
    }
  })
}
// 查询所有可作为子合同的合同模板
export const listAllNotContainSelf = excludeTemplateIds => {
  return request({
    url: API + BLADE_CONTRACT + WEB_BACK + '/contractTemplate/list_all_not_contain_self',
    method: 'get',
    params: {
      excludeTemplateIds
    }
  })
}


