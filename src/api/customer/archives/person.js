import request from '@/router/axios'
import { API, WEB_BACK, CUSTOMER_WEB_BACK } from '@/config/apiPrefix'

// 融资个体户 - 查看融资个体户信息
export const requestPersonArchiveDetail = id =>
  request({
    url:
      API +
      CUSTOMER_WEB_BACK +
      WEB_BACK +
      '/customer/customerFrontUserType/personalDetails',
    method: 'get',
    params: { id },
  })

// 融资个体户-联系人列表
export const requestPersonContactList = params => {
  return request({
    url:
      API +
      CUSTOMER_WEB_BACK +
      WEB_BACK +
      '/customer/financingContacts/getFinancingList',
    method: 'post',
    params,
  })
}

// 融资个体户-数据总览

export const getDataStatistics = params => {
  return request({
    url:
      API +
      CUSTOMER_WEB_BACK +
      WEB_BACK +
      '/data_statistics/customer/data_view',
    method: 'get',
    params,
  })
}
// 融资个体户-客户评分
export const getCustomerScore = params => {
  return request({
    url:
      API +
      CUSTOMER_WEB_BACK +
      WEB_BACK +
      '/data_statistics/customer/data_customer_score',
    method: 'get',
    params,
  })
}

// 融资个体户-风险指标
export const getDataRiskNorm = params => {
  return request({
    url:
      API +
      CUSTOMER_WEB_BACK +
      WEB_BACK +
      '/data_statistics/customer/data_risk_norm',
    method: 'get',
    params,
  })
}
// 融资个体户-贷款产品偏好
export const getDataLoanNorm = params => {
  return request({
    url:
      API +
      CUSTOMER_WEB_BACK +
      WEB_BACK +
      '/data_statistics/customer/data_loan_label_hobby',
    method: 'get',
    params,
  })
}
// 融资个体户-信贷记录
export const getDataLoanRecord = params => {
  return request({
    url:
      API +
      CUSTOMER_WEB_BACK +
      WEB_BACK +
      '/data_statistics/customer/data_loan_record',
    method: 'get',
    params,
  })
}
