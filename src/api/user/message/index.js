import request from '@/utils/request'

export default {
  // 消息列表
  messageList(params) {
    return request({
      url: '/api/blade-message/messageList/list',
      method: 'get',
      params,
    })
  },
  // 消息详情
  messageDetail(id) {
    return request({
      url: '/api/blade-message/messageList/detail',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 未读消息数量
  messageNum() {
    return request({
      url: '/api/blade-message/messageList/unreadQuantity',
      method: 'get',
    })
  },
  // 全部已读
  updateAllRead() {
    return request({
      url: '/api/blade-message/messageList/updateAllRead',
      method: 'post',
    })
  },
}
