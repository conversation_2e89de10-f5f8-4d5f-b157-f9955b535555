import request from '@/utils/request'

// 获取代采待赎列表数据
export const requestPendingTableList = params =>
  request({
    url: '/api/blade-redemption/web-front/redemption/warehouseDetails/list',
    method: 'get',
    params,
  })

// 获取动产质押待赎列表数据
export const pledgeFinanceTableList = params =>
  request({
    url: '/api/blade-pledge/web-front/pledgeFinance/pledgeList',
    method: 'get',
    params,
  })

// 获取赎货列表数据
export const requestForeclosureTableList = params =>
  request({
    url: '/api/blade-redeem/web-front/cargo/list',
    method: 'get',
    params,
  })

// 获取动产赎货列表数据
export const requestForeclosureTablePledgeList = params =>
  request({
    url: '/api/blade-redeem/web-front/pledgeRedeemCargo/list',
    method: 'get',
    params,
  })

// 赎货申请 - 发起 - 获取商品信息
export const requestApplyInfo = params =>
  request({
    url: '/api/blade-redeem/web-front/cargo/applyInfo',
    method: 'get',
    params,
  })

// 动产质押赎货申请 - 发起 - 获取商品信息
export const applyInfoByPledge = params =>
  request({
    url: '/api/blade-pledge/web-front/process/applyInfoByPledge',
    method: 'get',
    params,
  })

// 赎货申请 - 计算
export const requestCalculationRedeem = data =>
  request({
    url: '/api/blade-redeem/web-front/cargo/calculationRedeem',
    method: 'post',
    data,
  })

// 动产质押赎货申请 - 计算
export const calculationRedeemByPledge = data =>
  request({
    url: '/api/blade-pledge/web-front/process/calculationRedeemByPledge',
    method: 'post',
    data,
  })

// 赎货申请 - 提交
export const requestSubmitApply = data =>
  request({
    url: '/api/blade-redeem/web-front/cargo/save',
    method: 'post',
    data,
  })

// 动产质押赎货申请 - 提交
export const savePledge = data =>
  request({
    url: '/api/blade-pledge/web-front/process/savePledge',
    method: 'post',
    data,
  })

// 赎货详情信息
export const requestForeclosureDetail = params =>
  request({
    url: '/api/blade-redeem/web-front/cargo/detail',
    method: 'get',
    params,
  })

// 动产赎货详情信息
export const requestForeclosurePledgeDetail = params =>
  request({
    url: '/api/blade-redeem/web-front/pledgeRedeemCargo/detail',
    method: 'get',
    params,
  })

// 有异议 / 确认验收
export const requestChangeStatus = data =>
  request({
    url: '/api/blade-redeem/web-front/cargo/changeStatus',
    method: 'post',
    data,
  })

// 动产质押确认验收
export const requestChangeStatusD = data =>
  request({
    url: '/api/blade-redeem/web-front/pledgeRedeemCargo/changeStatus',
    method: 'post',
    data,
  })

// 赎货确认提交
export const requestSubmitRedeemConfirm = data =>
  request({
    url: '/api/blade-redeem/web-front/cargo/redeemConfirm',
    method: 'post',
    data,
  })

// 动产赎货确认提交
export const requestSubmitPledgeRedeemConfirm = data =>
  request({
    url: '/api/blade-redeem/web-front/pledgeRedeemCargo/redeemConfirm',
    method: 'post',
    data,
  })

// 服务器时间
export const serverTime = () =>
  request({
    url: '/api/blade-business/web-front/finance-apply/currentTime',
    method: 'get',
  })

// /blade-loan/web-back/loan/agentPurchaseChangeApply/changeApply 异议发起
export const changeApply = data =>
  request({
    url: '/api/blade-loan/web-back/loan/agentPurchaseChangeApply/changeApply',
    method: 'post',
    data,
  })
//  获取赎货信息，及关联账号
export const getRedeemExpenseAndAccount = redeemNo =>
  request({
    url: '/api/blade-redeem/web-front/cargo/getRedeemExpenseAndAccount',
    method: 'get',
    params: {
      redeemNo,
    },
  })

//  获取赎货信息，及关联账号
export const getExpenseAndAccount = redeemNo =>
  request({
    url: '/api/blade-redeem/web-front/pledgeRedeemCargo/getRedeemExpenseAndAccountByPledge',
    method: 'get',
    params: {
      redeemNo,
    },
  })

//  保存赎货费用信息
export const getSave = redeemNo =>
  request({
    url: '/api/blade-redeem/web-front/redemptionExpense/expense/submit',
    method: 'post',
    params: {
      redeemNo,
    },
  })
