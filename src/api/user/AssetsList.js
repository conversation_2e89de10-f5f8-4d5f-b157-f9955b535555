import request from '@/utils/request'

// 获取资产列表数据
export const requestTableData = params =>
  request({
    url: '/api/blade-customer/web-front/customer/assets/listByType',
    method: 'get',
    params,
  })

// 资产列表详情页数据
export const requestAssetsDetail = params =>
  request({
    url: '/api/blade-customer/web-front/customer/assets/details',
    method: 'get',
    params,
  })

// 填写收款单
export const requestFillInTheReceipt = data =>
  request({
    url: '/api/blade-customer/web-front/customer/salesContractRefundDetail/save',
    method: 'post',
    data,
  })
