import request from '@/utils/request'

// 获取核心企业 - 应付账款列表数据
export const requestTableData = params =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/list',
    method: 'get',
    params,
  })

// 核心企业 - 应付压缩包文件上传
export const requestPayableFileUpload = (formData, onUploadProgress) =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/zipUpload',
    method: 'post',
    headers: {
      timeout: 600000,
    },
    data: formData,
    onUploadProgress,
  })

// 核心企业 - 文件解析进度
export const requestPayableFileParsingProgress = () =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/getCurrentUploadTask',
    method: 'get',
  })

// 核心企业 - 文件上传管理页
export const requestInvoiceManager = () =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/invoiceManager',
    method: 'get',
  })

// 核心企业 - 文件上传管理页表格数据
export const requestInvoiceManagerTableData = params =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/invoiceManagerDataPage',
    method: 'get',
    params,
  })

// 核心企业 - 应付账款 - 授予额度
export const requestPayableGrant = data =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/batchUploadSaleContract',
    method: 'post',
    data,
  })

// 核心企业 - 应付 - 删除发票
export const requestDeletePayableInvoices = params =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/removeInvoice',
    method: 'get',
    params,
  })

// 核心企业 - 应付列表详情页数据
export const requestAssetsDetail = params =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/details',
    method: 'get',
    params,
  })

// 核心企业 - 处理应付账款 - 确权
export const requestProcessPayable = params =>
  request({
    url: '/api/blade-customer/web-core/customer/assets/operateSaleContract',
    method: 'get',
    params,
  })
