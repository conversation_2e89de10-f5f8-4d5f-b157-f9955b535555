import request from '@/utils/request'
import store from '@/store'

// 获取租户ID
export const getTenantId = customerDomain =>
  request({
    url: '/api/blade-system/tenant/customerInfo',
    method: 'get',
    params: {
      customerDomain,
    },
  })

/** 兼容拦截器获取租户 */
export const getTenantIdAPI = params =>
  request({
    url: '/api/blade-system/tenant/customerInfo',
    method: 'get',
    params,
  })

// 获取验证码
export const requestPhoneCode = params =>
  request({
    url: '/api/blade-customer/web-front/front/customer/send-validate',
    method: 'post',
    params,
  })

// 个人实名手机号获取验证码
export const getPersonPhoneInfo = params =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/commitPersonAuthInfoByType',
    method: 'post',
    params,
  })
// 个人实名人脸获取url
export const getPersonAuthInfo = ({ type, returnUrl, reGen }) =>
  request({
    url: `/api/blade-otherApi/web-front/authApi/endpoint/commitPersonAuthInfoByType?type=${type}`,
    method: 'post',
    data: { returnUrl, reGen },
  })

// 个人实名通过手机号认证提交
export const submitPhoneAuth = data =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/verifyPersonAuthInfoByType?type=1',
    method: 'post',
    data,
  })
// 个人人脸识别实名通过查询
export const submitPersonFaceAuth = params =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/verifyPersonAuthInfoByType',
    method: 'post',
    params,
  })
// 个人人脸识别实名通过查询(新)
export const submitPersonFaceAuthNew = params =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/verifyAuthInfoByScan',
    method: 'post',
    params,
  })

// 手机号登录
export const requestPhoneLogin = params =>
  request({
    url: '/api/blade-customer/web-front/front/customer/web/phoneLogin',
    method: 'post',
    headers: {
      Authorization: 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
    },
    params,
  })

// 账号登录
export const requestAccountLogin = params =>
  request({
    url: '/api/blade-customer/web-front/front/customer/web/login',
    method: 'post',
    headers: {
      Authorization: 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
      // 不要传递旧账户的 Token
      noToken: true,
    },
    params,
  })

// 退出登录
export const requestLogout = () =>
  request({
    url: '/api/blade-customer/web-front/front/customer/oauth/logout',
    method: 'get',
  })

// 注册账号
export const requestRegister = params =>
  request({
    url: '/api/blade-customer/web-front/front/customer/web/register',
    method: 'post',
    params,
  })

// 注册账号
export const requestSubRegister = params =>
  request({
    url: '/api/blade-customer/web-front/front/customerFrontUserType/web/register',
    method: 'post',
    headers: {
      Authorization: 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
    },
    params,
  })

// 换绑手机号
export const requestChangeBindPhone = formData =>
  request({
    url: '/api/blade-customer/web-front/front/customer/updatePhone',
    method: 'post',
    data: formData,
  })

// 忘记密码
export const requestForgetPassword = params =>
  request({
    url: '/api/blade-customer/web-front/front/customer/web/forgetPassword',
    method: 'post',
    params,
  })

// 实名认证及实名信息查看接口（个人）
export const requestPersonalRealNameAuth = data =>
  request({
    url: '/api/blade-customer/web-front/front/customer/customrRealNameAuth',
    method: 'post',
    data: {
      ...data,
      userType: 1,
    },
  })

// 实名认证接口（新 - 个人）
export const requestPersonalRealNameAuthV2 = (returnUrl, data) =>
  request({
    url: '/api/blade-customer/web-front/front/customer/postCustomerPersonInfo',
    method: 'post',
    headers: {
      timeout: 60000,
    },
    params: { returnUrl },
    data,
  })

// 实名认证及实名信息查看接口（企业）
export const requestEnterpriseRealNameAuth = data =>
  request({
    url: '/api/blade-customer/web-front/front/customer/customrRealNameAuth',
    method: 'post',
    data: {
      ...data,
      userType: 2,
    },
  })

// 主动更新（与上上签同步）客户实名状态（个人） - 返回 success=true 时，表示更新成功且已经实名认证
export const requestPersonalActiveUpdateRealStatus = data =>
  request({
    url: '/api/blade-customer/web-front/front/customer/activeUpdateRealStatus',
    method: 'post',
    data: {
      ...data,
      userType: 1,
    },
  })

// 主动更新（与上上签同步）客户实名状态（企业）- 返回 success=true 时，表示更新成功且已经实名认证
export const requestEnterpriseActiveUpdateRealStatus = data =>
  request({
    url: '/api/blade-customer/web-front/front/customer/activeUpdateRealStatus',
    method: 'post',
    data: {
      ...data,
      userType: 2,
    },
  })

// 主动查询上上签个人实名情况 (新)
export const requestPersonalActiveQueryRealStatus = params =>
  request({
    url: '/api/customer_person_info/customerPersonInfo/queryPersonAuthByAccount',
    method: 'get',
    params,
  })

// 查询当前账户用户（账户空间）
export const requestSubAccountList = params =>
  request({
    url: '/api/blade-customer/web-front/front/customerFrontUserType/selectCustomer',
    method: 'post',
    params,
  })

// 查询当前账户所有企业信息（不包括个人）
export const requestSubAccountEnterpriseList = params =>
  request({
    url: '/api/blade-customer/web-front/front/customerFrontUserType/selectEnterprise',
    method: 'post',
    params,
  })

// 子账户登录
export const requestSubAccountLogin = params =>
  request({
    url: '/api/blade-customer/web-front/front/customerFrontUserType/selecustomerSigninct',
    method: 'post',
    headers: {
      Authorization: 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
    },
    params,
  })

// 切换账号时的子账户登录
export const requestSubAccountLoginOnSwitch = params =>
  request({
    url: '/api/blade-customer/web-front/front/customerFrontUserType/selecustomerSigninct',
    method: 'post',
    headers: {
      Authorization: 'Basic c2FiZXI6c2FiZXJfc2VjcmV0',
      // 不要传递旧账户的 Token
      noToken: true,
    },
    params,
  })

// 企业实名：保存预实名企业
export const requestSavePreEnterpriseName = enterpriseName =>
  request({
    url: '/api/blade-customer/web-front/customer/customerTemporary/addEnerpriseName',
    method: 'post',
    params: {
      enterpriseName,
    },
  })

// 企业实名：读取预实名企业
export const requestReadPreEnterpriseName = () =>
  request({
    url: '/api/blade-customer/web-front/customer/customerTemporary/getEnerpriseName',
    method: 'get',
  })

// 企业实名（新）：开通企业
export const requestOpenCompanyAuth = (returnUrl, data) =>
  request({
    url: '/api/customerInfo/openCompany',
    method: 'post',
    headers: {
      timeout: 60000,
    },
    params: {
      returnUrl,
    },
    data,
  })

// 企业实名（新）：查询企业实名状态
export const requestCompanyRealNameStatus = params =>
  request({
    url: '/api/customerInfo/queryEntAuth',
    method: 'get',
    params,
  })

// 企业实名（新）：查询是否需要签署授权书
export const requestCompanyRealNameCheckNeedSign = params =>
  request({
    url: '/api/customerInfo/checkNeedSign',
    method: 'get',
    params,
  })

// 企业实名（新）：获取企业授权书合同
export const requestContractByBizNo = params =>
  request({
    url: '/api/customerInfo/getContractByBizNo',
    method: 'get',
    params,
  })

// 企业实名（新）：跳转去签署
export const requestSkipToSigning = params =>
  request({
    url: '/api/blade-customer/web-front/contractCustomer/signRegistContract',
    method: 'get',
    params,
  })

// 企业打款前一步的实名提交
export const submitAuthInfoByType = data =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/commitEntAuthInfoByType?type=3',
    method: 'post',
    data,
  })

// 企业打款的操作验证
export const submitAuthBefore = data =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/verifyEntAuthInfoByType?type=3',
    method: 'post',
    data,
  })

// 法人人脸识别实名
export const getAuthLink = data =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/commitEntAuthInfoByType?type=2',
    method: 'post',
    data,
  })

// 法人人脸识别结果反馈
export const getAuthStatus = data =>
  request({
    url: '/api/blade-otherApi/web-front/authApi/endpoint/verifyEntAuthInfoByType?type=2',
    method: 'post',
    data,
  })
