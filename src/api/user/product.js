import request from '@/utils/request'

export default {
  userGoodsList(params) {
    // 客户页我的产品list
    return request({
      url: '/api/blade-customer/web-front/customerGoods/list',
      method: 'get',
      params,
    })
  },
  userGoodsList2(params) {
    // 客户页我的产品list
    return request({
      url: '/api/blade-customer/web-front/multi-funding/product/get-my-multi-funding-product',
      method: 'get',
      params,
    })
  },
  // 产品组详情列表
  groupProductDetailList(params) {
    // 客户页我的产品list
    return request({
      url: '/api/blade-customer/web-front/multi-funding/product/get-my-multi-funding-product-detail',
      method: 'get',
      params,
    })
  },
  goodsClear(params) {
    // 清除对应id的开始进度
    return request({
      url: '/api/blade-customer/web-front/customerGoods/opening/business/clear',
      method: 'get',
      params,
    })
  },

  getOrderOne(params) {
    // 测试获取订单
    return request({
      url: '/api/blade-plan/web-front/orderData/getOrderOneToMany',
      method: 'get',
      params,
    })
  },

  planCalculateOne(params) {
    // 计算方案
    return request({
      url: '/api/blade-plan/web-back/financing-plan-basic/planCalculateOne',
      method: 'get',
      params,
    })
  },
  financeCheck(params) {
    // 融资校验
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/financeCheck',
      method: 'get',
      params,
    })
  },
}
