import request from '@/utils/request'

// 获取实名认证信息
export const getdata = customerId =>
  request({
    url: '/api/blade-customer/web-front/customerInfo/getCustomerInfo',
    method: 'get',
    params: { customerId },
  })

// 重新认证
export const postinfor = url =>
  request({
    url: '/api/blade-business/web-back/bestSign/skipToAuth',
    method: 'post',
    data: url,
  })

// 查看证件
export const getView = attachId =>
  request({
    url: '/api/blade-customer/web-front/customerInfo/getPicByAttachId',
    method: 'get',
    params: { attachId },
  })

// 去实名认证
export const postgoRealName = (returnUrl, isReAuth) =>
  request({
    url: '/api/blade-customer/web-front/customerAuth/skipToAuth',
    method: 'post',
    data: { returnUrl, isReAuth },
  })

// 实名认证绑定账户
export const postbindingUser = () =>
  request({
    url: '/api/blade-customer/web-front/customerAuth/isBind',
    method: 'post',
  })

// 查询是否实名
export const getRealNameStatus = params =>
  request({
    url: '/api/blade-customer/web-front/customerInfo/searchCompanyStatusInfo',
    method: 'get',
    params,
  })

// 档案查询
export const postarchivesInfo = returnUrl =>
  request({
    url: '/api/blade-customer/web-front/customerAuth/toSkipDocumentsAuth',
    method: 'post',
    params: { returnUrl },
  })

// 查询档案是否授权
export const getarchivesState = () =>
  request({
    url: '/api/blade-customer/web-front/customerInfo/searchIsFileAuth',
    method: 'get',
  })
