import request from '@/router/axios';
import {PAY_BACK} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: PAY_BACK+'/notify/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: PAY_BACK+'/notify/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: PAY_BACK+'/notify/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: PAY_BACK+'/notify/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: PAY_BACK+'/notify/submit',
    method: 'post',
    data: row
  })
}

