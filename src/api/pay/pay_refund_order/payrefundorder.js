import request from '@/router/axios';
import {PAY_BACK} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: PAY_BACK+'/pay_refund_order/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: PAY_BACK+'/pay_refund_order/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: PAY_BACK+'/pay_refund_order/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: PAY_BACK+'/pay_refund_order/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: PAY_BACK+'/pay_refund_order/submit',
    method: 'post',
    data: row
  })
}

