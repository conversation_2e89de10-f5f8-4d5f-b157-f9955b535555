import request from '@/router/axios'
import { BLADE_BILL_BACK } from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade_expense/web-back/expense/expenseOrder/list",
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getDetail = id => {
  return request({
    url: "/api/blade_expense/web-back/expense/expenseOrder/detail",
    method: 'get',
    params: {
      id,
    },
  })
}

export const remove = ids => {
  return request({
    url: BLADE_BILL_BACK + '/bill/expenseOrder/remove',
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: BLADE_BILL_BACK + '/bill/expenseOrder/submit',
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: BLADE_BILL_BACK + '/bill/expenseOrder/submit',
    method: 'post',
    data: row,
  })
}
