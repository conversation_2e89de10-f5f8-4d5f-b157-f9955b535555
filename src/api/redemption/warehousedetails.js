import request from '@/router/axios';
import {BLADE_REDEMPTION_PREFIX} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: BLADE_REDEMPTION_PREFIX+'/redemption/warehouseDetails/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: BLADE_REDEMPTION_PREFIX+'/redemption/warehouseDetails/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: BLADE_REDEMPTION_PREFIX+'/redemption/warehouseDetails/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: BLADE_REDEMPTION_PREFIX+'/redemption/warehouseDetails/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: BLADE_REDEMPTION_PREFIX+'/redemption/warehouseDetails/submit',
    method: 'post',
    data: row
  })
}

// 生成仓单
export const receipt = (params) => {
  return request({
    url: '/api/blade-redemption/web-back/redemption/warehouseDetails/receipt',
    method: 'get',
    params,
  })
}

