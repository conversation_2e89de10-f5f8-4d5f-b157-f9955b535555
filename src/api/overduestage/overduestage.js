import request from '@/router/axios';
import { BLADE_LOAN_PREFIX } from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: BLADE_LOAN_PREFIX + '/loan/overdue-stage/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: BLADE_LOAN_PREFIX + '/loan/overdue-stage/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: BLADE_LOAN_PREFIX + '/loan/overdue-stage/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: BLADE_LOAN_PREFIX + '/loan/overdue-stage/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: BLADE_LOAN_PREFIX + '/loan/overdue-stage/submit',
    method: 'post',
    data: row
  })
}

