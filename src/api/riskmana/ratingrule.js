import request from '@/router/axios';
import {BLADE_RISK_SYSTEM_PREFIX, BLADE_RISKMANA_PREFIX,Blade_} from "@/config/apiPrefix";

export const getList = (current, size, params) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/ratingrule/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/ratingrule/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/ratingrule/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/ratingrule/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/ratingrule/update',
    method: 'post',
    data: row
  })
}
export const batchEnabled = (ids) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/ratingrule/batchEnabled',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const batchDisabled = (ids) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/ratingrule/batchDisabled',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const mapSupplierByType = (typeCode) => {
  return request({
    url:  '/api/othersapi/mapSupplierByType',
    method: 'get',
    params: {
      typeCode,
    }
  })
}

/**
 * 同步模板
 * @param current
 * @param size
 * @returns {AxiosPromise}
 */
export const syncTemplate = (apiNo) => {
  return request({
    url: BLADE_RISK_SYSTEM_PREFIX + "/risksys/ratingRule/asyncTemplate",
    method: 'get',
    params: {
      apiNo,
    }
  })
}
