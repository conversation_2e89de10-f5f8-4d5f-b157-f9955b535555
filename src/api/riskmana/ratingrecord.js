import request from '@/router/axios'
import {BLADE_RISKMANA_PREFIX} from "@/config/apiPrefix";

export const getList = (current, size, params) => {
  return request({
    url: BLADE_RISKMANA_PREFIX+'/riskmana/ratingrecord/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getDetail = id => {
  return request({
    url: BLADE_RISKMANA_PREFIX+'/riskmana/ratingrecord/detail',
    method: 'get',
    params: {
      id,
    },
  })
}

export const remove = ids => {
  return request({
    url: BLADE_RISKMANA_PREFIX+'/riskmana/ratingrecord/remove',
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: BLADE_RISKMANA_PREFIX+'/riskmana/ratingrecord/save',
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: BLADE_RISKMANA_PREFIX+'/riskmana/ratingrecord/update',
    method: 'post',
    data: row,
  })
}

export const getdata = (id, size, current) => {
  return request({
    url: BLADE_RISKMANA_PREFIX+'/riskmana/ratingrecord/detail',
    method: 'get',
    params: {
      id,
      size,
      current,
    },
  })
}
