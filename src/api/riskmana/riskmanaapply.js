import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/riskmana/riskmanaApply/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/riskmana/riskmanaApply/detail',
    method: 'get',
    params: {
      id
    }
  })
}
export const check = (riskId) => {
  return request({
    url: '/api/riskmana/riskmanaApply/check',
    method: 'get',
    params: {
      riskId
    }
  })
}
export const downLoadReport = (riskId) => {
  return request({
    url: '/api/riskmana/riskmanaApply/downLoadReport',
    method: 'get',
    params: {
      riskId
    }
  })
}

