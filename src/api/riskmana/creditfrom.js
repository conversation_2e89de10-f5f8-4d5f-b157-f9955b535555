import request from '@/router/axios'
import {BLADE_RISKMANA_PREFIX,BLADE_RESOURCE} from "@/config/apiPrefix";

export const getBusinessList = () => {
  // 获取新增弹窗list
  return request({
    url:  BLADE_RESOURCE + '/resource/businessfield/listData',
    method: 'get',
  })
}

export const getDictionaryTwo = code => {
  // 获取字典接口
  return request({
    url: '/api/blade-system/dict-biz/dictionary-two',
    method: 'get',
    params: {
      code: code
    },
  })
}

export const getFromDetail = id => {
  // 获取表单详情
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/fromDetail',
    method: 'get',
    params: {
      fromId: id
    },
  })
}

export const creditFrom = params => {
  // 新增接口
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/save',
    method: 'post',
    data: params
  })
}

export const upFromData = params => {
  // 新增接口
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/update',
    method: 'post',
    data: params
  })
}

export const fromStopType = params => {
  // 开启禁用状态
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/updateStatus',
    method: 'post',
    data: params,
  })
}


export const getList = (current, size, params) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = ids => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/remove',
    method: 'post',
    params: {
      fromId: ids
    },
  })
}

export const add = (row) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/submit',
    method: 'post',
    data: row
  })
}
export const enable = (id) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/enable',
    method: 'get',
    params: {
      id
    }
  })
}
export const disable = (id) => {
  return request({
    url: BLADE_RISKMANA_PREFIX + '/riskmana/creditfrom/disable',
    method: 'get',
    params: {
      id
    }
  })
}
