import request from '@/router/axios';
import {BLADE_WAREHOUSE_PREFIX} from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: BLADE_WAREHOUSE_PREFIX + '/warehouse/warehouseinout/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: BLADE_WAREHOUSE_PREFIX + '/warehouse/warehouseinout/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: BLADE_WAREHOUSE_PREFIX + '/warehouse/warehouseinout/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: BLADE_WAREHOUSE_PREFIX + '/warehouse/warehouseinout/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: BLA<PERSON>_WAREHOUSE_PREFIX + '/warehouse/warehouseinout/submit',
    method: 'post',
    data: row
  })
}

