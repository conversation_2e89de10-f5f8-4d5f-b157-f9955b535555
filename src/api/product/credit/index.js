import request from '@/utils/request'

export default {
  // 产品解冻申请进度开启及获取
  receivableUnfrozen(data) {
    return request({
      url: '/api/blade-customer/web-front/customerGoods/receivable_unfrozen',
      method: 'post',
      data,
    })
  },
  // 保存冻结流程资料
  saveUnfrozenMaterial(data) {
    return request({
      url: '/api/blade-customer/web-front/customer/material/save_unfrozen_material',
      method: 'post',
      data,
    })
  },
  // 冻结流程资料详情
  detailWithBusiness(params) {
    return request({
      url: '/api/blade-customer/web-front/customer/material/detail_with_business',
      method: 'get',
      params,
    })
  },
  // 提交解冻申请流程
  submitLinesToThaw(data) {
    return request({
      url: '/api/blade-business/web-front/business/process/receive_unfreeze_apply',
      method: 'post',
      data,
    })
  },
  // 提交解冻申请流程
  loanManageOverdueConsultSave(data) {
    return request({
      url: '/api/blade-overdue-consult/web-front/loan/loanManageOverdueConsult/save',
      method: 'post',
      data,
    })
  },
  // 查看协商信息
  loanManageOverdueConsultDetail(params) {
    return request({
      url: '/api/blade-overdue-consult/web-front/loan/loanManageOverdueConsult/details',
      method: 'get',
      params,
    })
  },
  // 逾期协商计算信息
  overdueConsultDetail(params) {
    return request({
      url: '/api/blade-overdue-consult/web-front/loan/loanManageOverdueConsult/overdue_consult_detail',
      method: 'get',
      params,
    })
  },
  // 开启逾期协商流程
  openOverdueConsult(data) {
    return request({
      url: '/api/blade-overdue-consult/web-front/loan/loanManageOverdueConsult/open_overdue_consult',
      method: 'post',
      data,
    })
  },
  // 获取逾期协商流程进度
  getBusinessProcess(params) {
    return request({
      url: '/api/blade-business/web-front/business/businessProcessProgress/get_business_process',
      method: 'get',
      params,
    })
  },
  // 提交逾期协商审批
  commitOverdueConsultApply(data) {
    return request({
      url: '/api/blade-overdue-consult/web-front/loan/loanManageOverdueConsult/commit_overdue_consult_apply',
      method: 'post',
      data,
    })
  },
  // 给年利率返回日利率
  calculateDayRate(yearRate) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/calculate_day_rate',
      method: 'get',
      params: {
        yearRate,
      },
    })
  },
}
