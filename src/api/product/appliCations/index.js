import request from '@/utils/request'

export default {
  // 查询额度
  getByenterpriseQuotaId(id) {
    return request({
      url: '/api/blade-riskmana/web-front/quota/enterpriseQuota/getById',
      method: 'get',
      params: {
        id,
      },
    })
  },

  /**
   * 更新融资金额
   * @returns
   */
  planCalculateByAmount(params) {
    return request({
      url: '/api/blade-plan/web-front/financing-plan-basic/planCalculateByAmount',
      method: 'get',
      params,
    })
  },

  financeApplyDetailList(ids) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/detailList',
      method: 'get',
      params: { ids },
    })
  },

  // 多资方产品关联的产品列表
  getUseProduct(planId) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/detail-ids',
      method: 'get',
      params: {
        planId,
      },
    })
  },

  // 应收账款 查询该用户该产品融资时最大可用额度
  availableFinanceAmount(params) {
    return request({
      url: '/api/blade-business/web-back/receivable-finance-apply/availableFinanceAmount',
      method: 'get',
      params,
    })
  },
  getFinanceApplyByIds(financingIds) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/getByIds',
      method: 'get',
      params: {
        ids: financingIds,
      },
    })
  },

  // 查询产品接口
  getBygoodsId(goodsId) {
    return request({
      url: '/api/blade_product/web-back/product/getById',
      method: 'get',
      params: {
        goodsId,
      },
    })
  },
  // 查询动产质押产品接口
  getByagentGoodsId(id) {
    return request({
      url: '/api/blade-pledge/web-front/pledge-goods/detail',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 查询应收账款列表
  salesContractPage(params) {
    return request({
      url: '/api/blade-customer/web-front/customer/salesContract/page',
      method: 'get',
      params,
    })
  },
  // 查询方案列表
  salesContractPage2(params) {
    return request({
      url: '/api/blade-plan/web-front/financing-plan-basic/list',
      method: 'get',
      params,
    })
  },

  // 查询订单融资列表
  salesContractPageDingDan(params) {
    return request({
      // url: '/api/blade-customer/web-front/orderFinancing/financing/getFinancingOrderData',
      url: '/api/web-front/orderLevel/financingLimit/getFinancingLimit',
      method: 'get',
      params,
    })
  },
  // 评估记录列表
  pledgeQuotaList(params) {
    return request({
      url: '/api/blade-riskmana/web-back/riskmana/pledgeQuota/list',
      method: 'get',
      params,
    })
  },
  // 查询上游企业 与 下游企业列表
  selectByCustomerGoodsId(ids) {
    return request({
      url: '/api/blade-customer/web-front/customer/tradeBackground/selectByCustomerGoodsId',
      method: 'get',
      params: {
        customerGoodsId: ids,
      },
    })
  },
  // 还款试算
  repaymentCalculation(data) {
    return request({
      // url: '/api/blade-business/web-front/receivable-finance-repayment/repaymentCalculation',
      // url: '/api/blade-business/web-front/v2/finance-repayment/repaymentCalculation',
      url: '/api/blade-business/web-front/v2/finance-repayment/repaymentCalculation',
      // url: '/api/blade-business/web-front/finance-repayment/repaymentCalculation',
      method: 'post',
      data,
    })
  },

  // 还款试算
  repaymentCalculation2(data) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/planRepaymentCalculation',
      method: 'post',
      data,
    })
  },
  // 还款试算-逾期协商
  repaymentCalculationXuYangYang(data) {
    return request({
      url: '/api/blade-overdue-consult/web-back/loan/loanManageOverdueConsult/consult',
      method: 'post',
      data,
    })
  },
  // 动产质押融资申请还款试算
  pledgeRepaymentFinanceApplyCalculation(data) {
    return request({
      url: '/api/blade-pledge/web-front/process/repaymentFinanceApplyCalculation',
      method: 'post',
      data,
    })
  },
  // 云信的还款试算
  repaymentCalculationByYunXin(data) {
    return request({
      url: '/api/blade-cloud/web-front/process/repaymentCalculationByCloud',
      method: 'post',
      data,
    })
  },
  // 查询逾期协商平台费用
  cKexpenseList(params) {
    return request({
      url: '/api/blade-goods/web-front/goods/expense/overdue_expenseList',
      method: 'get',
      params,
    })
  },
  // 查询平台费用
  expenseList(params) {
    return request({
      url: '/api/blade-goods/web-front/goods/expense/expenseList',
      method: 'get',
      params,
    })
  },
  // 查询代采融资费用详情
  purchaseExpenseList(params) {
    return request({
      url: '/api/blade-purchase/web-front/purchase/purchaseInformation/expenseList',
      method: 'get',
      params,
    })
  },
  // 查询云信平台费用
  expenseCloudList(data) {
    return request({
      url: '/api/blade-cloud/web-front/process/expenseCloudList',
      method: 'post',
      data,
    })
  },
  // 融资申请详情
  detail(id) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/detail',
      method: 'get',
      params: {
        id,
      },
    })
  },
  orderFinancingDetail(id) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/detail',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 融资申请提交
  submit(data) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/submit',
      method: 'post',
      data,
    })
  },
  // 融资申请保存
  applySave(data) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/save',
      method: 'post',
      data,
    })
  },
  // 订单融资申请提交
  submitDingDan(data) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/submit',
      method: 'post',
      data,
    })
  },
  // 订单融资申请保存
  applySaveDingDan(data) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/save',
      method: 'post',
      data,
    })
  },
  // 详情申请信息
  applyInfo(id) {
    return request({
      url: '/api/blade-business/web-front/receivable-finance-apply/applyInfo',
      method: 'get',
      params: {
        id,
      },
    })
  },
  orderFinancingApplyInfo(id) {
    return request({
      url: '/api/blade-customer/web-front/orderFinancing/financing/applyInfo',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 详情状态信息
  statusMsg(id) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/statusMsg',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 旧
  // 详情还款试算
  // repaymentCalculationById(financeApplyId) {
  //   return request({
  //     url: '/api/blade-business/web-front/finance-apply/repaymentCalculationById',
  //     method: 'get',
  //     params: {
  //       financeApplyId,
  //     },
  //   })
  // },
  // 创楷加的
  // 详情还款试算
  repaymentCalculationById(financeApplyId) {
    return request({
      url: '/api/blade-business/web-front/receivable-finance-repayment/repaymentCalculationDetailById',
      method: 'get',
      params: {
        financeApplyId,
      },
    })
  },
  repaymentCalculationByIdNew(financeApplyId) {
    return request({
      url: '/api/blade-business/web-front/order-finance-repayment/repaymentCalculationDetailById',
      method: 'get',
      params: {
        financeApplyId,
      },
    })
  },
  // 详情平台费用
  expensesList(financeNo) {
    return request({
      url: '/api/blade-business/web-front/platform-expenses/list',
      method: 'get',
      params: {
        financeNo,
      },
    })
  },
  // 详情平台费用(liutao)
  expensesListFinance(financeNo) {
    return request({
      url: '/api/blade-business/web-front/platform-expenses/list',
      method: 'get',
      params: {
        financeNo,
      },
    })
  },
  // 详情超时未申请放款关闭
  timeout(id) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/timeout',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 详情缴费信息
  expenseInfo(id) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/expenseInfo',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 详情融资信息
  loanInfo(id) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/loanInfo',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 动产融资信息详情
  loanInfoByPledge(id) {
    return request({
      url: '/api/blade-pledge/web-front/process/loanInfoByPledge',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 放款申请查询融资申请信息
  applyGetById(id) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/getById',
      method: 'get',
      params: {
        id,
      },
    })
  },
  // 放款申请客服
  frontType(type) {
    return request({
      url: '/api/blade-front/web-front/front/cec/type/' + type,
      method: 'get',
    })
  },
  // 放款申请短信校验
  validateMessage(params) {
    return request({
      url: '/api/blade-resource/sms/endpoint/validate-message',
      method: 'post',
      params,
    })
  },
  // 放款申请提交
  loanSubmit(data) {
    return request({
      url: '/api/blade-business/web-front/loan-apply/submit',
      method: 'post',
      data,
    })
  },
  orderFinancingLoanSubmit(data) {
    return request({
      url: '/api/blade-business/web-front/orderFinancing_loan-apply/submit',
      method: 'post',
      data,
    })
  },
  // 动产质押放款申请提交
  loanApply(data) {
    return request({
      url: '/api/blade-pledge/web-front/process/loanApply',
      method: 'post',
      data,
    })
  },
  // 放款申请查询费用订单附件
  selectBillOrderAttach(financeApplyId) {
    return request({
      url: '/api/blade-bill/web-front/bill/expenseOrder/selectBillOrderAttach',
      method: 'get',
      params: {
        financeApplyId,
      },
    })
  },
  // 融资确认提交
  financeConfirm(data) {
    return request({
      url: '/api/blade-business/web-front/receivable-finance-apply/financeConfirm',
      method: 'post',
      data,
    })
  },
  financeConfirm1(data) {
    return request({
      url: '/api/blade-customer/web-front/orderFinancing/financing/financeConfirm',
      method: 'post',
      data,
    })
  },
  financeConfirm2(data) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/financeConfirm',
      method: 'post',
      data,
    })
  },

  // 缴纳费用提交
  submitAutoLoanProcess(data) {
    return request({
      url: '/api/blade-business/web-front/receivable-finance-apply/submitAutoLoanProcess',
      method: 'post',
      data,
    })
  },
  submitAutoLoanProcess1(data) {
    return request({
      url: '/api/blade-customer/web-front/orderFinancing/financing/submitAutoLoanProcess',
      method: 'post',
      data,
    })
  },
  submitAutoLoanProcess2(data) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/submitAutoLoanProcess',
      method: 'post',
      data,
    })
  },
  // 更新方案
  allPlanCalculate(params) {
    return request({
      url: '/api/blade-plan/web-front/financing-plan-basic/allPlanCalculate',
      method: 'get',
      params,
    })
  },

  // 融资申请通用接口
  financeApplySubmit(data) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/submit',
      method: 'post',
      data,
    })
  },
  // 根据方案查询产品
  pageWithProduct(params) {
    return request({
      url: '/api/blade-plan/web-front/financing-plan-quota/listWithProduct',
      method: 'get',
      params,
    })
  },
  // 融资申请
  financeApplySubmit2(data) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/submitConfirm',
      method: 'post',
      data,
    })
  },
  financeApplySubmit3(data) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/submit',
      method: 'post',
      data,
    })
  },
  // 获取产品组可融资的关联产品
  listByGroupId(params) {
    return request({
      url: '/api/blade-plan/web-front/plan-finance-apply/listByGroupId',
      method: 'get',
      params,
    })
  },

  // 融资申请详情通用接口
  financeApplyDetail(params) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/detail',
      method: 'get',
      params,
    })
  },
  // 融资申请根据融资编号 + 收费节点获取 费用展示数据
  financeExpenseCoreData(params) {
    return request({
      url: '/api/blade-bill/web-front/bill/expenseOrder/expenseCoreData',
      method: 'get',
      params,
    })
  },
  // 融资申请根据融资编号+收费节点获取 线上支付状态
  financeExpensePayStatus(params) {
    return request({
      url: '/api/blade-bill/web-front/bill/expenseOrder/payStatus',
      method: 'get',
      params,
    })
  },
  // 支付渠道查询
  payChannelChoose() {
    return request({
      url: '/api/blade-pay/web-back/pay_channel/choose',
      method: 'get',
    })
  },
  // 动产质押提交融资申请
  submitPledgeCommodity(data) {
    return request({
      url: '/api/blade-pledge/web-front/process/submit-finance-apply',
      method: 'post',
      data,
    })
  },
  // 动产质押绑定收款账户
  savePledgeBank(params) {
    return request({
      url: '/api/blade-riskmana/web-back/riskmana/pledgeQuota/savePledgeBank',
      method: 'get',
      params,
    })
  },
  // 动产质押融资确认详情
  pledgeFinanceDetail(financeNo) {
    return request({
      url: '/api/blade-pledge/web-front/process/pledge-wait-submit',
      method: 'get',
      params: { financeNo },
    })
  },
  // 货物处置列表
  cargoSolveList(financeNo) {
    return request({
      url: '/api/blade-loan/web-front/loan/cargoSolve/list',
      method: 'get',
      params: { financeNo },
    })
  },
  // 货物处置详情
  cargoSolveInfo(financeNo) {
    return request({
      url: '/api/blade-loan/web-front/loan/cargoSolve/info',
      method: 'get',
      params: { financeNo },
    })
  },

  // 刷新最新订单数据
  latestOrderData(data) {
    return request({
      url: `/api/blade-customer/web-front/customer/salesContract/auto_bind_height_salesContract?backId=${data}`,
      method: 'post',
    })
  },
  // 刷新最新订单数据
  latestOrderFinancedOrderData(params) {
    return request({
      url: '/api/blade-customer/web-front/orderFinancing/financing/auto_bind_height_salesContract',
      method: 'post',
      params,
    })
  },

  // 支付订单创建并返回二维码
  pmGetQrCode(params) {
    return request({
      url: '/api/pay/external/pay-counter/getQrCode',
      method: 'post',
      params,
    })
  },
  // 支付订单创建并返回二维码(动产业务)
  pmGetQrCodeYewu(params) {
    return request({
      url: '/api/pay/external/pay-counter/getQrCode/PLEDGE_REDEEM_CARGO_SERVICE',
      method: 'post',
      params,
    })
  },
  // 在应收账款-自动放款第一步提交后获取对应的融资编号
  getFinanceNo(params) {
    return request({
      url: '/api/blade-business/web-front/finance-apply/detail-id',
      method: 'get',
      params,
    })
  },
}
