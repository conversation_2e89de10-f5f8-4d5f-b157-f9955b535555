import request from '@/router/axios';
import {API, WEB_BACK, BLADE_CONTRACT} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: API+BLADE_CONTRACT+WEB_BACK+'/contractlist/getContractDetails',
    method: 'post',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export  const  contractRemind=(data)=>{
  return request({
    url:API+BLADE_CONTRACT+WEB_BACK+'/contractlist/contractRemind',
    method: 'post',
    data:data
  })
}

export  const  contractRevoke=(data)=>{
  return request({
    url:API+BLADE_CONTRACT+WEB_BACK+'/contractlist/contractRevoke',
    method: 'post',
    data:data
  })
}
export  const  contractDownload = (contractId) =>{
  return request({
    url:API+BLADE_CONTRACT+WEB_BACK+'/contractlist/contractDownload',
    method: 'post',
    params: {
     contractId
    }
  })
}

