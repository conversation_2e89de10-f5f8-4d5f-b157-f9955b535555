import request from '@/router/axios';
import {MESSAGE_BACK} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: MESSAGE_BACK + '/message/messagetemplate/field/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: MESSAGE_BACK + '/message/messagetemplate/field/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: MESSAGE_BACK + '/message/messagetemplate/field/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: MESSAGE_BACK + '/message/messagetemplate/field/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: MESSAGE_BACK + '/message/messagetemplate/field/submit',
    method: 'post',
    data: row
  })
}

