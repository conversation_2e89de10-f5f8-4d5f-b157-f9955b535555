import request from '@/router/axios'
import {BLADE_RESOURCE} from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: BLADE_RESOURCE+'/resource/bank/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getDetail = id => {
  return request({
    url: BLADE_RESOURCE+'/resource/bank/detail',
    method: 'get',
    params: {
      id,
    },
  })
}

export const remove = ids => {
  return request({
    url: BLADE_RESOURCE+'/resource/bank/remove',
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: BLADE_RESOURCE+'/resource/bank/submit',
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: BLADE_RESOURCE+'/resource/bank/submit',
    method: 'post',
    data: row,
  })
}
