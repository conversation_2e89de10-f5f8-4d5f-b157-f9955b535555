import request from '@/router/axios';
import {API,WEB_BACK,BLADE_FRONT} from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: API+BLADE_FRONT+WEB_BACK+'/front/protocol/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: API+BLADE_FRONT+WEB_BACK+'/front/protocol/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: API+BLADE_FRONT+WEB_BACK+'/front/protocol/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: API+BLADE_FRONT+WEB_BACK+'/front/protocol/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: API+BLADE_FRONT+WEB_BACK+'/front/protocol/submit',
    method: 'post',
    data: row
  })
}

