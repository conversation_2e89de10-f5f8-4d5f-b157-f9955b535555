import request from '@/router/axios';
import {BLADE_CONTRACT_PREFIX} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url:  BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url:  BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url:  BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url:  BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/updateById',
    method: 'post',
    data: row
  })
}
export const listSsqFields = (id) => {
  return request({
    url:  BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/listSsqFields',
    method: 'get',
    params: {
      id
    }
  })
}
export const listFieldConfig = (id) => {
  return request({
    url:  BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/listFieldConfig',
    method: 'get',
    params: {
      id
    }
  })
}
export const syncSSqDetails = (id) => {
  return request({
    url:  BLADE_CONTRACT_PREFIX+'/contract/contractTemplateConfig/syncSSqDetails',
    method: 'get',
    params: {
      id
    }
  })
}
