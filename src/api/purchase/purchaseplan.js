import request from '@/router/axios'

import { BLADE_PURCHASE_PREFIX, BLADE_BILL_BACK } from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchaseplan/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getDetail = id => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchaseplan/detail',
    method: 'get',
    params: {
      id,
    },
  })
}

export const remove = ids => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchaseplan/remove',
    method: 'post',
    params: {
      ids,
    },
  })
}

export const add = row => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchaseplan/submit',
    method: 'post',
    data: row,
  })
}

export const update = row => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchaseplan/submit',
    method: 'post',
    data: row,
  })
}

export const abolitionOrder = (id, status) => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchaseplan/abandoned',
    method: 'post',
    data: {
      id,
      status,
    },
  })
}

// 代采保证金信息
export const getPurchaseBondData = params => {
  return request({
    url:  '/api/blade_expense/web-back/expense/expenseDeposit/getCashDeposit',
    method: 'get',
    params,
  })
}

/**代采订单数据 **/

export const getPurchaseDetail = params => {
  return request({
    url: '/api/blade-purchase/web-front/purchase/purchaseInformation/purchase-wait-sumbit',
    method: 'get',
    params,
  })
}
// 获取还款方式， 平台费用
export const getFinanceApplyPlan = params => {
  return request({
    url: '/api/blade-business/web-back/finance-apply/purchase-applyInfo',
    method: 'get',
    params,
  })
}

export const getPledgeFinanceInfo = params => {
  return request({
    url: '/api/blade-pledge/web-back/pledgeFinance/pledge-applyInfo',
    method: 'get',
    params,
  })
}

// 获取代采货物动态待入库
export const getGoodsWaitWarehouse = params => {
  return request({
    url: '/api/blade-redemption/web-front/redemption/WarehouseEntering/listFinancingCode',
    method: 'get',
    params,
  })
}
// 获取代采货物入库信息
export const getGoodswarehouseDetails = params => {
  return request({
    url: '/api/blade-redemption/web-front/redemption/warehouseDetails/listFinancingCode',
    method: 'get',
    params,
  })
}

// 获取代采的赎货单
export const getCargoList = params => {
  return request({
    url: '/api/blade-redeem/web-back/cargo/list',
    method: 'get',
    params,
  })
}

// 获取动产质押的赎货单
export const getCargoPledgeList = params => {
  return request({
    url: '/api/blade-redeem/web-back/pledgeRedeemCargo/list',
    method: 'get',
    params,
  })
}

// 获取费用详情
export const getCostDetail = params => {
  return request({
    url: '/api/blade_expense/web-back/expense/expenseOrder/billExpenseOrderList-order',
    method: 'get',
    params,
  })
}

// 获取保证金余额信息
export const getBondAmountList = params => {
  return request({
    url: '/api/blade_expense/web-back/expense/expenseDeposit/getCashDepositGeneralDetailsVO',
    method: 'get',
    params,
  })
}

// 获取保证金账单
export const getBondOrderList = params => {
  return request({
    url: 'api/blade_expense/web-back/expense/expenseDepositBill/getCashDepositBilList',
    method: 'get',
    params,
  })
}

// 还款计划列表
export const getLoanInfo = params => {
  return request({
    url: '/api/blade-business/web-front/finance-apply/loanInfo',
    method: 'get',
    params,
  })
}

// 变更记录

export const getHistoryByFinanceId = id =>
  request({
    url: '/api/blade-loan/web-back/loan/agentPurchaseChange/getHistoryByFinanceId',
    method: 'get',
    params: {
      id,
    },
  })
