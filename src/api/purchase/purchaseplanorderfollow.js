import request from '@/router/axios';
import {BLADE_PURCHASE_PREFIX} from '@/config/apiPrefix'
export const getList = (current, size, params) => {
  return request({
    url: '/api/purchase/purchasePlanOrderFollow/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchasePlanOrderFollow/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchasePlanOrderFollow/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchasePlanOrderFollow/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchasePlanOrderFollow/submit',
    method: 'post',
    data: row
  })
}
export const saveFollow = (from) => {
  return request({
    url: BLADE_PURCHASE_PREFIX + '/purchase/purchasePlanOrderFollow/save',
    method: 'post',
    data: from
  })
}


