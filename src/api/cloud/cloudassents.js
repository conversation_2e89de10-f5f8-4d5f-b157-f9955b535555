import request from '@/router/axios'

import { BLADE_CLOUD_WEB_FRONT, BLADE_CLOUD_BACK } from '@/config/apiPrefix'

export const getList = (current, size, params) => {
  // 云信资产列表
  return request({
    url: '/api/blade-cloud/web-back/cloud/cloudAssets/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  })
}

export const getCloudWorkDetail = params => {
  // 融资详情
  return request({
    url: BLADE_CLOUD_WEB_FRONT + '/cloud/cloudFinancing/detail',
    method: 'get',
    params,
  })
}

export const getCloudAssetsDetail = params => {
  // 资产详情
  return request({
    url: BLADE_CLOUD_WEB_FRONT + '/cloud/cloudAssets/detail',
    method: 'get',
    params,
  })
}

export const getCloudLine = params => {
  // 资产云信轨迹图
  return request({
    url: BLADE_CLOUD_WEB_FRONT + '/cloud/cloudAssets/track-child',
    method: 'get',
    params,
  })
}

export const getCloudSplitList = params => {
  // 拆分列表
  return request({
    url: BLADE_CLOUD_WEB_FRONT + '/cloud/cloudAssets/parent-split-list',
    method: 'get',
    params,
  })
}

// 云信融资

export const getCloudLoanDetail = params => {
  return request({
    url: BLADE_CLOUD_BACK + '/cloud/cloudFinancing/cloudLoanApplyDetail',
    method: 'get',
    params,
  })
}

// 放款提交
export const updateCloudLoanData = params => {
  return request({
    url: BLADE_CLOUD_BACK + '/cloud/cloudFinancing/cloudLoanApply',
    method: 'post',
    params,
  })
}
