<template>
  <div class="header-container" id="main-header-content">
    <header>
      <div class="header-left">
        <router-link :to="{ path: '/' }" class="header-logo">
          <!-- <img src="@/assets/logo_default_black.svg" alt="LOGO" /> -->
          <img :src="logoBig" alt="LOGO" />
        </router-link>
        <template v-if="isEnterpriseAccount">
          <CommonTag
            style="border-radius: 3px; margin-left: 8px"
            padding="8px 4px"
            color="#0D55CF"
            :name="isCoreEnterpriseAccount ? '核心企业端' : '融资企业端'"
            backgroundColor="#CCE6FF"
          />
        </template>
      </div>

      <div class="tab-bar">
        <router-link :to="{ name: 'Home' }" v-slot="{ navigate }">
          <span @click="navigate" @keypress.enter="navigate" role="link"
            >首 页</span
          >
        </router-link>
        <router-link
          :to="{ name: 'accountsReceivableList' }"
          v-slot="{ navigate }"
          v-if="showProductType[1]"
        >
          <span @click="navigate" @keypress.enter="navigate" role="link">
            应收账款
          </span>
        </router-link>
        <template v-if="!isLogined || (isLogined && !isCoreEnterpriseAccount)">
          <router-link
            :to="{ name: 'generationOfMiningFinancing' }"
            v-slot="{ navigate }"
            v-if="showProductType[2]"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              代采融资
            </span>
          </router-link>
          <router-link
            :to="{ name: 'generationOfMiningMall' }"
            v-slot="{ navigate }"
            v-if="showProductType[2]"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              代采商城
            </span>
          </router-link>
          <router-link
            :to="{ name: 'appraiseMall' }"
            v-slot="{ navigate }"
            v-if="showProductType[4]"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              动产质押
            </span>
          </router-link>
          <router-link
            :to="{ name: 'financingProductList' }"
            v-slot="{ navigate }"
            v-if="showProductType[5]"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link"
              >订单融资</span
            >
          </router-link>
          <!-- <router-link
            :to="{ name: 'orderFinancing' }"
            v-slot="{ navigate }"
            v-if="showProductType[5]"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link"
              >订单融资</span
            >
          </router-link> -->
          <router-link
            :to="{ name: 'productGroupList' }"
            v-slot="{ navigate }"
            v-if="showProductType[99]"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link"
              >产品组</span
            >
          </router-link>
        </template>
        <a
          :class="{
            'router-link-active': route.name === 'UserAssetsListIndex',
          }"
          v-if="showProductType[3]"
        >
          <span @click="goAssets()"> 云 信 </span>
        </a>
        <router-link :to="{ name: 'AboutWe' }" v-slot="{ navigate }">
          <span @click="navigate" @keypress.enter="navigate" role="link"
            >关于我们</span
          >
        </router-link>
        <!-- <router-link :to="{ name: 'HelperCenter' }" v-slot="{ navigate }">
          <span @click="navigate" @keypress.enter="navigate" role="link"
            >帮助</span
          >
        </router-link> -->
        <!-- 帮助中心 -->
        <div class="menu-list-have-children">
          <a-dropdown placement="bottom" :trigger="['click']">
            <a
              class="ant-dropdown-link"
              :class="{
                'router-link-active': route.name === 'helpCenter',
              }"
              @click.prevent
            >
              帮助中心
              <MySvgIcon icon-class="icon-xiajiantou" style="font-size: 16px" />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item v-for="item in helpCenterArr" :key="item.id">
                  <router-link
                    :to="{
                      name: item.urlName,
                      query: {
                        name: item.name,
                      },
                    }"
                  >
                    {{ item.name }}
                  </router-link>
                </a-menu-item>
                <a-menu-item
                  style="width: 130px"
                >
                  <router-link :to="{ name: 'HelperCenter' }" v-slot="{ navigate }">
                    <span @click="navigate" @keypress.enter="navigate" role="link"
                      >其他帮助</span
                    >
                  </router-link>
                </a-menu-item>
                <a-sub-menu title="相关视频">
                  <a-menu-item
                    v-for="item in videoSrc"
                    :key="item.id"
                    style="width: 130px"
                  >
                    <a :href="item.url" target="_blank">
                      {{ item.name }}
                    </a>
                  </a-menu-item>
                </a-sub-menu>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <!-- <span>应收账款</span> -->
        <!-- <span>代采融资</span> -->
        <!-- <span>帮助中心</span> -->
      </div>
      <div class="menu-list">
        <div class="notice-container" @click="handleNoticeDialog">
          <a-badge :count="messageNums">
            <i class="notice">
              <MySvgIcon
                icon-class="icon-lingdang1"
                style="color: black; font-size: 20px"
              />
            </i>
          </a-badge>
        </div>

        <div class="avatar-container">
          <a-spin v-if="initLoading">
            <span style="display: inline-block; width: 40px; height: 40px" />
          </a-spin>
          <template v-else>
            <a-popover
              overlayClassName="avatar-menu-popover"
              placement="bottomRight"
              trigger="click"
              v-model:visible="avatarMenuData.visible"
              :getPopupContainer="getHeaderContainerDom"
            >
              <template #title />
              <template #content>
                <div class="avatar-menu-content">
                  <div
                    v-for="item of avatarMenuData.menuList"
                    :key="item.opcode"
                    @click="handleAvatarMenuClick(item.opcode)"
                    class="avatar-menu-item"
                  >
                    <MySvgIcon
                      :icon-class="item.iconClass"
                      style="font-size: 20px; fill: #788398"
                    />
                    <span class="menu-name">{{ item.name }}</span>
                  </div>
                </div>
              </template>
              <img :src="userInfo.logoSrc" alt="" @click="handleAvatar" />
            </a-popover>
          </template>
        </div>
      </div>
    </header>
    <UserLogin />
    <ConfirmDialog
      ref="confirmDialog"
      title="实名认证"
      content="请先完成账号实名后再进行操作！"
      cancelBtnName="退出登录"
      confirmBtnName="去实名"
      @cancel="handleExit"
      @confirm="handleToVerified"
    />
    <NoticeDialog
      @closeDialog="handleCloseDialog"
      :visible="visible"
      ref="noticeDialog"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'headerS',
}
</script>
<script lang="ts" setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import type { UserInfo } from '@/store/modules/auth'
import UserLogin from '../login/index.vue'
import ConfirmDialog from '@/businessComponents/ConfirmDialog/index.vue'
import NoticeDialog from '@/components/NoticeDialog/index.vue'
import MySvgIcon from '@/components/MySvgIcon/index.vue'
import CommonTag from '@/components/CommonTag/index.vue'
import { HOMEAPI } from '@/api/index'

const router = useRouter()
const route = useRoute()
const store = useStore()
const userInfo = computed<UserInfo>(() => store.getters['Auth/userInfo'])
const initLoading = computed(() => store.getters['Auth/initLoading'])
const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
const isVerified = computed<boolean>(() => store.getters['Auth/isVerified'])
const isEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isEnterpriseAccount']
)
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
const realNameVerificationTipDialogWatchedData = computed(
  () => store.getters['Auth/realNameVerificationTipDialogWatchedData']
)
const messageNums = computed(() => store.getters['Product/messageNums'])
const showProductType = computed<any>(
  () => store.getters['Role/showProductType']
)
const confirmDialog = ref<any>(null)
const noticeDialog = ref<any>(null)
const visible = ref<boolean>(false)
const avatarMenuData = reactive<any>({
  visible: false,
  menuList: [
    {
      iconClass: 'icon-wode',
      name: '服务中心',
      opcode: 'userCenter',
    },
    {
      iconClass: 'icon-zuoyoujiantou1',
      name: '切换账号',
      opcode: 'switchAccount',
    },
    {
      iconClass: 'icon-dengchu',
      name: '退出登录',
      opcode: 'logout',
    },
  ],
})

const helpCenterArr = [
  {
    id: 1,
    urlName: 'helpCenter',
    name: '产品配置',
  },
  {
    id: 2,
    urlName: 'helpCenter',
    name: '企业入驻',
  },
  {
    id: 3,
    urlName: 'helpCenter',
    name: '应收账款质押',
  },
  {
    id: 4,
    urlName: 'helpCenter',
    name: '代采融资',
  },
  {
    id: 5,
    urlName: 'helpCenter',
    name: '云信',
  },
  {
    id: 6,
    urlName: 'helpCenter',
    name: '应收初始化',
  },
  {
    id: 7,
    urlName: 'helpCenter',
    name: '代采初始化',
  },
  {
    id: 8,
    urlName: 'helpCenter',
    name: '云信初始化',
  },
]

const videoSrc = [
  {
    id: 1,
    url: 'https://www.yecong.com/share/1583038695630573568?stage=0',
    name: '入驻',
  },
  {
    id: 2,
    url: 'https://www.yecong.com/share/1583038762777186304?stage=0',
    name: '贷前授信',
  },
  {
    id: 3,
    url: 'https://www.yecong.com/share/1583038823162580992?stage=0',
    name: '应收账款业务',
  },
  {
    id: 4,
    url: 'https://www.yecong.com/share/1583038925340020736?stage=0',
    name: '代采业务',
  },
  {
    id: 5,
    url: 'https://www.yecong.com/share/1583039023679672320?stage=0',
    name: '云信业务',
  },
]

watch(realNameVerificationTipDialogWatchedData, val => {
  if (val) {
    confirmDialog.value.handleOpen()
  }
})
watch([initLoading, isLogined], ([initLoadingVal, isLoginedVal]) => {
  if (initLoadingVal || isLoginedVal) {
    // 获取未读消息数量-
    store.dispatch('Product/messageNumFun')
  }
})

const logoBig = ref<any>()
setTimeout(() => {
  const tenantId = computed(() => store.getters['Auth/tenantId'])
  // 获取LOGO
  HOMEAPI.getLogo(tenantId.value).then(res => {
    if (res.data.code === 200) {
      logoBig.value = res.data.data.logo
    }
  })
}, 500)
watch(logoBig, val => {
  if (val) {
    logoBig.value = val
  }
})

// 导航栏头像点击事件
const handleAvatar = () => {
  if (isLogined.value) {
    if (isVerified.value) {
      // router.push({ name: 'UserCenter' })
    } else {
      confirmDialog.value.handleOpen()
      nextTick(() => {
        avatarMenuData.visible = false
      })
    }
  } else {
    store.dispatch('Auth/openLoginDialog')
    nextTick(() => {
      avatarMenuData.visible = false
    })
  }
}

// 导航栏头像子菜单点击事件
const handleAvatarMenuClick = (opcode: string) => {
  switch (opcode) {
    case 'userCenter':
      router.push({ name: 'UserCenter' })
      avatarMenuData.visible = false
      break
    case 'switchAccount':
      store.dispatch('Auth/switchAccount')
      avatarMenuData.visible = false
      break
    case 'logout':
      handleExit()
      avatarMenuData.visible = false
      break
    default:
      break
  }
}

// 退出登录
const handleExit = () => {
  store.dispatch('Auth/exitLogin')
}

// 前往实名
const handleToVerified = () => {
  router.push({ name: 'UserRegister' })
}

// 前往云信
const goAssets = () => {
  if (isLogined.value) {
    if (isCoreEnterpriseAccount.value) {
      router.push({ name: 'cloudLetterList' })
    } else {
      router.push({ name: 'UserAssetsListIndex' })
    }
  } else {
    store.dispatch('Auth/openLoginDialog')
  }
}
// 打开消息弹窗
const handleNoticeDialog = () => {
  if (isLogined.value) {
    // noticeDialog.value.handleOpen()

    visible.value = true
  } else {
    store.dispatch('Auth/openLoginDialog')
  }
}
// 关闭消息弹窗
const handleCloseDialog = () => {
  visible.value = false
}

// 获取头部容器 DOM 节点
const getHeaderContainerDom = () =>
  document.getElementById('main-header-content')
</script>

<style lang="scss" scoped>
$height-header: 72px;
$width-header: 100%;

.header-container {
  width: $width-header;
  height: $height-header;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  backdrop-filter: saturate(90%) blur(20px);
  -webkit-backdrop-filter: saturate(90%) blur(20px);
  background-color: RGBA(255, 255, 255, 0.6);
  box-shadow: 0px 3px 4px 0px rgba(10, 31, 68, 0.1),
    0px 0px 1px 0px rgba(10, 31, 68, 0.08);

  header {
    width: $width-header;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 890;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-left {
      display: flex;
      align-items: center;
      .header-logo {
        margin-left: 24px;
      }
    }
    .tab-bar {
      & span {
        color: #0a1f44;
        display: inline-block;
        height: 40px;
        text-align: center;
        margin-right: 24px;
        box-sizing: border-box;
        padding: 10px 20px 10px 20px;
        font-weight: bold;
      }

      .menu-list-have-children {
        display: initial;

        .ant-dropdown-link {
          box-sizing: border-box;
          height: 40px;
          padding: 12px 20px 12px 20px;
          font-weight: bold;
          color: #0a1f44;
        }

        .router-link-active {
          border-radius: 25px;
          background: #ebf5ff;
          color: #0d55cf;
          cursor: pointer;
        }

        &:hover {
          .ant-dropdown-link {
            border-radius: 25px;
            background: #ebf5ff;
            color: #0d55cf;
            cursor: pointer;
          }
        }
      }

      & span:hover,
      & .router-link-active span {
        border-radius: 25px;
        background: #ebf5ff;
        color: #0d55cf;
        cursor: pointer;
      }
    }
    .menu-list {
      margin-right: 24px;
      display: flex;
      justify-content: center;
      align-items: center;

      .notice-container {
        display: inline-block;
        margin-right: 24px;
        .notice {
          display: inline-block;
          width: 40px;
          height: 40px;
          border-radius: 100px;
          border: 1px solid #e1e4e8;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
          &:hover {
            border: 1px solid #0d55cf;

            #my-svg-icons {
              color: #0d55cf !important;
            }
          }
        }
      }

      .avatar-container {
        position: relative;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        font-size: 0;

        img {
          display: inline-block;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: contain;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.avatar-menu-popover {
  padding: 0 !important;
  top: 72px !important;

  .ant-popover-inner-content {
    padding: 16px;
  }

  .ant-popover-arrow {
    display: none;
    overflow: hidden;
    opacity: 0;
  }

  .ant-popover-inner {
    background: #ffffff;
    box-shadow: 0px 26px 26px 0px rgba(10, 31, 68, 0.12),
      0px 0px 1px 0px rgba(10, 31, 68, 0.1);
    border-radius: 16px;
  }

  .avatar-menu-content {
    .avatar-menu-item {
      display: flex;
      align-items: center;
      padding: 14px 34px 14px 14px;
      border-radius: 8px;
      cursor: pointer;

      &:hover {
        background-color: #f1f2f4;
      }

      .menu-name {
        margin-left: 16px;
        font-size: 14px;
        font-family: SFProText-Medium, SFProText;
        font-weight: 500;
        color: #0a1f44;
        line-height: 20px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.header-left {
  height: 100%;
  a {
    width: 144px;
    height: 100%;
    padding: 10px 0;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
