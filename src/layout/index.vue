<template>
  <div class="layout" v-loading="loading">
    <Header v-show="!articleSetpsType" />
    <!-- <Toast /> -->
    <div class="app-content">
      <router-view />
    </div>
    <Aside />
    <Footer />
    <Im v-if="nEnv !== 'development'" />
  </div>
  <!-- 合同签署加载界面组件 -->
  <ContractLoad v-if="contractLoadType" />
</template>

<script lang="ts">
export default {
  name: 'isLayout',
}
</script>
<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'
import Header from './header/index.vue'
import Footer from './footer/index.vue'
import Aside from './aside/index.vue'
// import Toast from './toast/index.vue'
import Im from './im/index.vue'
import ContractLoad from '@/views/product/component/contractLoad.vue'

const store = useStore()
const loading = computed<boolean>(() => store.getters['Product/loadingType'])
const articleSetpsType = computed<boolean>(
  () => store.getters['Product/articleSetpsType']
)
// 合同签署完成状态检测loading
const contractLoadType = computed<boolean>(
  () => store.getters['Product/contractLoadType']
)
const nEnv = process.env.NODE_ENV
</script>

<style lang="scss" scoped>
.layout {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100vh;

  .app-content {
    position: relative;
    width: 100%;
    padding-top: 72px;
    // background-color: RGB(246, 246, 246);
    // background-color: RGB(239, 247, 255);
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
</style>
