<template>
  <div class="about-we3">
    <!-- <Header :title="menuName" /> -->
    <div class="card" v-for="item in listData" :key="item.id">
      <div class="image">
        <img
          style="width: 428.35px; height: 260px"
          :src="item.goodsImgUrl"
        />
      </div>
      <div class="text">
        <div class="title">{{ item.goodsTypeStr }}</div>
        <div class="content">
          <span class="content-item">
            <template v-for="subItem in item.goodsDesc" :key="item.id">
              <p>{{ subItem }}</p>
            </template>
          </span>
        </div>
      </div>
    </div>
    <!-- <div class="card">
      <div class="image">
        <img
          style="width: 428.35px; height: 260px"
          src="@/assets/images/home/<USER>/piaoju.png"
        />
      </div>
      <div class="text">
        <div class="title">票据贴现</div>
        <div class="content">
          <span class="content-item">
            便捷的银行承兑汇票与商业承兑汇票秒贴平台，提供纯线上流程、无需开户、低贴现利率的票据秒贴服务。
          </span>
        </div>
      </div>
    </div> -->
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Header from './aboutHeader.vue'
// import img from '@/assets/zhanwei1.png'
import { HOMEAPI } from '@/api/index.js'

defineProps({
  menuName: {
    type: String,
  },
})

const listData = ref([
  {
    goodsTypeStr: '',
    goodsDesc: '',
    goodsImgUrl: '',
  },
])
const webIntroduceFun = async () => {
  let params = {
    // 产品介绍
    introduceType: 'GOODS_INTRODUCE',
  }
  const { data } = await HOMEAPI.webIntroduce(params)

  if (data.success && data.data) {
    listData.value = data.data.listData.map((item, index) => {
      return {
        ...item,
        id: index + 1,
        goodsDesc: item.goodsDesc.split('\n'),
      }
    })
  }
}

onMounted(() => {
  webIntroduceFun()
})
</script>
<style lang="scss" scoped>
.about-we3 {
  margin-top: 31px;
}

.card {
  display: flex;
  border: 1px solid #ccc;
  padding: 30px;
  box-sizing: border-box;
  height: 320px;
  width: 1200px;
  margin: auto;
  margin-bottom: 18px;
  .text {
    margin-left: 53px;
    .title {
      margin-top: 30px;
      font-weight: 700;
      font-size: 30px;
      line-height: 32px;
      color: #000000;
      height: 32px;
      margin-bottom: 30px;
    }
    .content {
      .content-item {
        font-weight: 400;
        font-size: 16px;
        line-height: 30px;
        color: #666666;
        height: 133px;
        p {
          white-space: pre-wrap; /* 保留空格和换行符，超出容器自动换行 */
        }
      }
    }
  }
}
</style>
