<template>
  <div class="about-we5">
    <Header :title="menuName" />
    <div class="content">
      <div class="text">
        <!-- <template v-for="item in textList" :key="item.id">
          <p>{{ item.text }}</p>
        </template> -->
        <p>联系电话：{{ contactUS.phone }}</p>
        <p>公司地址：{{ contactUS.address }}</p>
      </div>
      <!-- <div class="image">
        <img
          style="width: 100%; height: 100%"
          src="../../../assets/images/map.png"
        />
      </div> -->
      <div id="map" style="width: 100%; height: 500px"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader'
import { ref, onMounted } from 'vue'
import Header from './aboutHeader.vue'
import { HOMEAPI } from '@/api/index.js'

defineProps({
  menuName: {
    type: String,
  },
})

// const textList = ref([
//   {
//     id: 1,
//     text: ' 联系电话：15311932910 / 15173136450',
//   },
//   {
//     id: 2,
//     text: '公司地址：山东省济南市历下区经十东路10567号成城大厦B座1613',
//   },
// ])
const contactUS = ref({
  phone: '',
  address: '',
  key: '',
  latitude: '',
  longitude: '',
})

const webIntroduceFun = async () => {
  let params = {
    // 联系我们
    introduceType: 'CONTACT_US',
  }
  const { data } = await HOMEAPI.webIntroduce(params)

  if (data.success && data.data) {
    contactUS.value = data.data.data
  }
}

const amapFun = () => {
  AMapLoader.load({
    // key: 'd4a75ebc8058e4b091dfee19ff29a905', // 高德地图API Key
    key: contactUS.value.key, // 高德地图API Key
    version: '2.0', // 指定API版本
    plugins: ['AMap.Geolocation', 'AMap.PlaceSearch', 'AMap.Scale'], // 需要使用的插件
  })
    .then(AMap => {
      map.value = new AMap.Map('map', {
        zoom: 23, // 初始缩放级别
        center: [contactUS.value.longitude, contactUS.value.latitude], // 初始中心点坐标
        resizeEnable: true,
      })

      // 添加标记点
      const marker = new AMap.Marker({
        position: new AMap.LngLat(contactUS.value.longitude, contactUS.value.latitude), // 标记点坐标
        title: '公司地址', // 鼠标悬停时显示的标题
      })
      map.value!.add(marker) // 将标记点添加到地图上
    })
    .catch(e => {
      console.error(e)
    })
}

// 高德地图
const map: any = ref(null)
// 高德key：d4a75ebc8058e4b091dfee19ff29a905
onMounted(async () => {
  await webIntroduceFun()
  amapFun()
})
</script>
<style lang="scss" scoped>
.about-we5 {
  // padding: 10px 20px;
  .content {
    // padding: 0 2em;
    padding-top: 10px;
    font-size: 18px;
    font-weight: 400;
    color: #666666;
    margin-bottom: 25px;
    .text {
      p {
        // text-indent: 2em; /* 设置首行缩进为2em */
        line-height: 32px;
        margin-bottom: 10px;
      }
    }
    .image {
      margin-top: 10px;
      height: 400px;
    }
  }
}
</style>
