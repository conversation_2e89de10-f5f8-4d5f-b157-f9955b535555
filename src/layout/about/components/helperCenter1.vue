<template>
  <div class="help-we1">
    <Header :title="menuName" />
    <div class="content">
      <template v-for="item in textList" :key="item.id">
        <div class="text">
          <div>
            <div class="border"></div>
            <div class="text-item">{{ item.question }}</div>
          </div>
          <p v-if="typeof item.angser == 'string'">{{ item.angser }}</p>
          <template v-else v-for="(subText, index) in item.angser" :key="index">
            <p>{{ subText }}</p>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import Header from './aboutHeader.vue'
import { ref } from 'vue'
defineProps({
  menuName: {
    type: String,
  },
})

const textList = ref([
  {
    id: 1,
    question: '如何申请成为平台用户？',
    angser:
      '用户首次使用平台，需完成个人账号注册与企业认证。按系统引导完成企业认证材料的在线填写和相关扫描件上传以及打款认证通过后，可正常使用时雨链金供应链金融服务平台所提供的相关功能。',
  },
  {
    id: 2,
    question: '注册时，提示手机号码已被绑定，该怎么办？',
    angser:
      '您可以在登录界面用手机号登录的方式进行登录。您可以通过登录界面的忘记密码功能获取短信验证码重新设置该账号的登录密码。',
  },
  {
    id: 3,
    question: '无法收到手机验证码，该怎么办？',
    angser:
      '短信收发过程中会存在延迟，收到的短信在1分钟内均有效，请耐心等待；请确认手机是否安装短信拦截或过滤软件；请确认手机是否能够正常接收短信（未处于停机或欠费状态）。',
  },
  {
    id: 4,
    question: '注册认证所需要的文档？',
    angser: [
      '1、营业执照复印件（盖公章）',
      '2、法定代表人身份证复印件（盖公章和法定代表人签章）',
      '3、管理员身份证复印件（盖公章）',
    ],
  },
])
</script>
<style lang="scss" scoped>
.help-we1 {
  // padding: 10px 20px;
  .content {
    padding-top: 10px;
    .text {
      font-size: 18px;
      font-weight: 400;
      line-height: 32px;
      margin-top: 4px;
      div {
        display: flex;
        align-items: center;
        .border {
          height: 18px;
          border-left: 5px solid #1b5bbf;
          margin-right: 12px;
        }
        .text-item {
          color: #666666;
          font-weight: 500;
        }
      }
      p {
        // text-indent: 2em; /* 设置首行缩进为2em */
        line-height: 32px;
        color: #999999;
        font-size: 17px;
      }
    }
    .image {
      margin-top: 10px;
      height: 400px;
    }
  }
}
</style>
