<template>
  <div class="aside">
    <n-card hoverable>
      <!-- <n-popover placement="left" trigger="hover" :x="125">
        <template #trigger>
          <div class="aside_right">
            <p>
              <img src="@/assets/images/home/<USER>/wx.svg" alt="xcx" />
            </p>
            <p>小程序</p>
          </div>
        </template>
        <div class="aside_right_large-text">
          <img
            src="@/assets/images/productOpen/jingruiitAccounts.jpg"
            alt="wx"
          />
        </div>
      </n-popover> -->
      <!-- <div class="aside_string-long" /> -->
      <n-popover placement="left" trigger="hover">
        <template #trigger>
          <div class="aside_right">
            <p>
              <img src="@/assets/images/home/<USER>/h5.svg" alt="h5" />
            </p>
            <p>H5</p>
          </div>
        </template>
        <div class="aside_right_large-text">
          <img src="@/assets/images/home/<USER>/h51.png" alt="h5" />
        </div>
      </n-popover>
    </n-card>
  </div>
</template>

<script>
export default {
  name: 'asideS',
}
</script>
<script setup>
import { NCard, NPopover } from 'naive-ui'
</script>

<style lang="scss">
.n-popover:not(.n-popover--raw):not(.n-popover--scrollable):not(.n-popover--show-header) {
  padding: 6px;
}
</style>
<style lang="scss" scoped>
.aside {
  position: fixed;
  right: 0;
  bottom: 25%;
  z-index: 300;

  &_right {
    font-size: 12px;
    color: #53627c;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    img {
      width: 34px;
    }

    &_large-text {
      img {
        width: 140px;
      }
    }
  }

  &_string-long {
    border: 1px solid #d7d7d7;
    border-bottom: none;
    margin: 5px 0;
  }

  :deep() {
    .n-card {
      width: 60px;

      .n-card__content {
        padding: 6px;
      }
    }
  }
}
</style>
