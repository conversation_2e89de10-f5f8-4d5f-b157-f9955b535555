<template>
  <div class="cloud-card-box" :style="{ height, width }">
    <div class="cloud-card-head">
      <span>{{ rowItem.name }}</span>
      <span
        v-if="![null].includes(rowItem.resolutionStatus)"
        class="cloud-status cloud-status-green"
        :class="getStatusColor(rowItem)"
        >{{ getStatusText(rowItem) }}</span
      >
    </div>
    <div class="cloud-card-body">
      <div class="cloud-body-item">
        <span>云信编号:</span>
        <span>{{ rowItem.parentCloudNo }}</span>
      </div>
      <div class="cloud-body-item">
        <span>{{ subtitle2 }}:</span>
        <span class="cloud-amout"
          >¥{{ formatMoney(rowItem.cloudBillAmount) }}</span
        >
      </div>
    </div>
    <div class="cloud-card-footer">
      <span @click="handleToDetail">查看详情</span>
    </div>
  </div>

  <GlobalDialog
    title="查看详情"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="cloud-list">
      <div class="cloud-item">
        <span>转让状态</span>
        <span class="cloud-status" :class="getStatusColor(rowItem)">{{
          getStatusText(rowItem)
        }}</span>
      </div>
      <div class="cloud-item">
        <span>收单用户</span>
        <span class="cloud-subtitle">{{ rowItem.name }}</span>
      </div>
      <div class="cloud-item">
        <span>收单金额</span>
        <span class="cloud-subtitle"
          >¥{{ formatMoney(rowItem.cloudBillAmount) }}元</span
        >
      </div>
      <div class="cloud-item">
        <span>云信编号</span>
        <span class="cloud-subtitle">{{ rowItem.parentCloudNo }}</span>
      </div>
      <div class="cloud-item">
        <span>转让编号</span>
        <span class="cloud-subtitle">{{ rowItem.cloudCode }}</span>
      </div>
      <div class="cloud-item">
        <span>创建时间</span>
        <span class="cloud-subtitle">{{ rowItem.createTime }}</span>
      </div>
      <div class="cloud-item">
        <span>签收时间</span>
        <span class="cloud-subtitle">{{ rowItem.updateTime }}</span>
      </div>
      <div class="cloud-item" v-if="rowItem.status === 3">
        <span>状态说明</span>
        <span class="cloud-subtitle"
          >转让被拒签，拒签原因：{{ rowItem.remark }}</span
        >
      </div>
    </div>
    <div class="cloud-dialog-contract" v-if="rowItem.resolutionStatus !== 6">
      <div class="contract-name">合同信息</div>
      <!-- <div class="contract-list">
        <div class="contract-item">
          <div class="contract-item-left">
            <span class="cloud-status cloud-status-green">已完成</span>
            <span class="contract-item-title">云信转让协议</span>
          </div>
          <div class="contract-item-right">
            <span>查看协议</span>
            <MySvgIcon
              icon-class="icon-youjiantou"
              style="fill: #0d55cf; font-size: 16px"
            ></MySvgIcon>
          </div>
        </div>
      </div> -->
      <template v-if="rowItem.cloudCode">
        <!-- 合同 -->
        <Contract
          style="margin: -80px 0 0"
          itemWidth="100%"
          :processIndex="[3, 4, 5].includes(rowItem.resolutionStatus) ? 16 : 15"
          :showContract="true"
          :receiveData="
            [3, 4, 5].includes(rowItem.resolutionStatus)
              ? route.query.cloudCode
              : rowItem.cloudCode
          "
        />
      </template>
    </div>
    <template #button>
      <div style="width: 100%; text-align: center">
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 12px"
          round
          :bordered="false"
          @click="handleClose"
        >
          关闭
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'CloudCard',
}
</script>

<script setup>
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { NButton } from 'naive-ui'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { formatMoney } from '@/utils/utils'
import Contract from '@/views/product/component/contract'

defineProps({
  subtitle2: {
    type: String,
    required: true,
  },
  height: {
    type: String,
    default: '174px',
  },
  width: {
    type: String,
    default: '312px',
  },
  rowItem: {
    type: Object,
    default: () => {},
  },
})

const route = useRoute()
const dialogRef = ref(null)
const handleToDetail = () => {
  handleOpen()
}

// 状态--颜色

const getStatusColor = ({ resolutionStatus }) => {
  let color = ''
  switch (resolutionStatus) {
    case 0: // 转让待收
      color = 'cloud-status-blue'
      break
    case 1: // 已转让
      color = 'cloud-status-green'
      break
    case 2: // 转让失败
      color = 'cloud-status-gray'
      break
    case 3: // 融资待审核
      color = 'cloud-status-blue'
      break
    case 4: // 已融资
      color = 'cloud-status-green'
      break
    case 5: // 融资失败
      color = 'cloud-status-gray'
      break
    case 6: //  剩余云信
      color = 'cloud-status-blue'
      break
  }
  return color
}

const getStatusText = ({ resolutionStatus }) => {
  let text = ''
  if (resolutionStatus === 0) {
    text = '转让待收'
  } else if (resolutionStatus == 1) {
    text = '已转让'
  } else if (resolutionStatus == 2) {
    text = '转让失败'
  } else if (resolutionStatus == 3) {
    text = '融资待审核'
  } else if (resolutionStatus == 4) {
    text = '已融资'
  } else if (resolutionStatus == 5) {
    text = '融资失败'
  } else if (resolutionStatus == 6) {
    text = '剩余云信'
  }
  return text
}

const handleOpen = () => {
  dialogRef.value.handleOpen()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.cloud-card-box {
  box-sizing: border-box;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  background-color: #fff;
  margin-right: 24px;
  .cloud-card-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    & span:nth-of-type(1) {
      display: block;
      color: #0a1f44;
      height: 24px;
      line-height: 24px;
      font-size: 16px;
      font-weight: 600;
      overflow: hidden;
      max-width: 204px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .cloud-card-body {
    margin-bottom: 16px;
    .cloud-body-item {
      font-size: 14px;
      font-weight: 500;
      color: #8a94a6;
      height: 20px;
      line-height: 20px;
      & span:first-child {
        margin-right: 6px;
      }
      .cloud-amout {
        color: #dd2727;
      }
    }
    .cloud-body-item + .cloud-body-item {
      margin-top: 8px;
    }
  }
  .cloud-card-footer {
    box-sizing: border-box;
    & span {
      cursor: pointer;
      display: inline-block;
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;
      padding: 10px 20px;
      border: 1px solid #e1e4e8;
      color: #0a1f44;
      border-radius: 4px;
    }
  }
}
.cloud-list {
  box-sizing: border-box;
  padding-bottom: 24px;
  border-bottom: 1px solid #f1f2f4;
  .cloud-item {
    display: flex;
    align-items: center;
    & span:nth-of-type(1) {
      min-width: 60px;
      margin-right: 20px;
      color: #8a94a6;
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;
    }
    .cloud-subtitle {
      color: #0a1f44;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }
  }
  .cloud-item:last-child {
    display: flex;
    align-items: flex-start;
  }
  .cloud-item + .cloud-item {
    margin-top: 24px;
  }
}

.cloud-dialog-contract {
  margin-top: 24px;
  .contract-name {
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    color: #8a94a6;
    margin-bottom: 12px;
  }
  .contract-list {
    .contract-item {
      box-sizing: border-box;
      border: 1px solid #e1e4e8;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .contract-item-left {
        .contract-item-title {
          margin-left: 8px;
          font-size: 14px;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
        }
      }
      .contract-item-right {
        display: flex;
        align-items: center;
        & span {
          display: block;
          line-height: 20px;
          color: #0d55cf;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    .contract-item + .contract-item {
      margin-top: 12px;
    }
  }
}
.cloud-status {
  display: inline-block;
  border-radius: 100px;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  padding: 4px 10px;
}
.cloud-status-green {
  color: #00865a;
  background-color: #cff8eb;
}
.cloud-status-blue {
  color: #0d55cf;
  background-color: #ebf5ff;
}
.cloud-status-gray {
  color: #8a94a6;
  background-color: #f8f9fb;
}
</style>
