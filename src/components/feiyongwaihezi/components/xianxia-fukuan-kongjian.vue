<template>
  <div class="xianxia-fukuan-kongjian-may-box">
    <div class="form-container">
      <avue-form
        ref="payFormAvueRef"
        :option="payOption"
        v-model="payForm"
      ></avue-form>
    </div>
    <div class="payInfo">
      <div class="pay-box">
        <div class="payImg">
          <div class="table-bottom">
            <el-button
              v-if="platformImg.length"
              @click="handlePreviewImage(platformImg)"
              >查看支付凭证</el-button
            >
            <span
              class="table-bottom-pdf"
              v-for="(item, index) in platformPdf"
              :key="item.url"
              @click="handlePreviewImage(item.url, 'pdf')"
            >
              附件{{ index + 1 }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <FilePreview :url="pdfSrc" />
  </div>
</template>
<script>
import FilePreview from '@/components/file-preview'
export default {
  components: {
    FilePreview,
  },
  props: {
    chuanruObj: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    isLook: {
      type: Boolean,
    },
  },
  data() {
    return {
      payOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [],
      },
      payForm: {},
      pdfSrc: '',
      platformImg: [],
      platformPdf: [],
    }
  },
  watch: {
    chuanruObj: {
      handler(val) {
        if (!val) return
        this.payForm.paymentStatus = val.paymentStatus
        this.payForm.ziAmount = val.totalAmount
        this.payForm.payOpenBank = val.payOpenBank
        this.payForm.payAccountNo = val.payAccountNo
        this.payForm.payTime = val.payTime
        this.payForm.failReason = val.failReason

        if (val.attachList && val.attachList.length) {
          for (const item of val.attachList) {
            if (item.url.indexOf('pdf') !== -1) {
              this.platformPdf.push({
                name: item.name,
                url: item.url,
              })
            } else {
              this.platformImg.push({
                name: item.name,
                url: item.url,
              })
            }
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },

  created() {
    this.payOption.column = [
      {
        label: '付款状态',
        prop: 'paymentStatus',
        type: 'select',
        clearable: false,
        span: 24,
        disabled: this.isLook,
        placeholder: '请选择付款状态',
        dicData: [
          {
            label: '待付款',
            value: '1',
          },
          {
            label: '已付款',
            value: '2',
          },
          {
            label: '支付失败',
            value: '3',
          },
        ],
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择付款状态',
            trigger: ['change'],
          },
        ],
        control: value => {
          if (value == 2) {
            return {
              ziAmount: {
                display: true,
              },
              payOpenBank: {
                display: true,
              },
              payAccountNo: {
                display: true,
              },
              failReason: {
                display: false,
              },
              payTime: {
                display: true,
              },
            }
          } else if (value == 3) {
            return {
              ziAmount: {
                display: false,
              },
              payOpenBank: {
                display: false,
              },
              payAccountNo: {
                display: false,
              },
              failReason: {
                display: true,
              },
              payTime: {
                display: false,
              },
            }
          } else if (value == 1) {
            return {
              ziAmount: {
                display: false,
              },
              payOpenBank: {
                display: false,
              },
              payAccountNo: {
                display: false,
              },
              failReason: {
                display: false,
              },
              payTime: {
                display: false,
              },
            }
          }
        },
      },
      {
        disabled: this.isLook,
        label: '付款金额',
        prop: 'ziAmount',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            validator: this.validategoodsNum,
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        disabled: this.isLook,
        label: '付款开户行',
        prop: 'payOpenBank',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入付款开户行',
            trigger: ['blur'],
          },
        ],
      },
      {
        disabled: this.isLook,
        label: '付款账号',
        prop: 'payAccountNo',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入付款账号',
            trigger: ['blur'],
          },
        ],
      },
      {
        disabled: this.isLook,
        label: '还款时间',
        prop: 'payTime',
        type: 'datetime',
        display: false,
        span: 24,
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now()
          },
        },
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        rules: [
          {
            required: true,
            message: '请选择还款时间',
            trigger: ['change'],
          },
        ],
      },
      {
        label: '失败原因',
        prop: 'failReason',
        span: 24,
        display: false,
        type: 'textarea',
        disabled: this.isLook,
        rules: [
          {
            required: true,
            message: '请输入失败原因',
            trigger: ['blur'],
          },
        ],
      },
    ]
  },
  methods: {
    // 预览图片
    handlePreviewImage(imgSrcArr = [], type = 'img') {
      if (type == 'img') {
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      } else {
        this.pdfSrc = imgSrcArr + '?time=' + new Date().getMilliseconds()
      }
    },
    validategoodsNum(rule, value, callback) {
      const nVal = Number(value)
      if (!nVal || isNaN(nVal)) {
        callback(new Error('请输入实还金额'))
      } else if (nVal < 0) {
        callback(new Error('实还金额要大于0'))
      } else if (nVal !== Number(this.chuanruObj.totalAmount)) {
        callback(new Error(`实还金额应等于${this.chuanruObj.totalAmount}元`))
      } else {
        callback()
      }
    },
    checkFun() {
      // if (this.payForm.paymentStatus === 1) {
      //   return false
      // }
      return {
        jiaoyanF:
          this.payForm.paymentStatus === 1 ? false : this.$refs.payFormAvueRef,
        shuju: this.payForm,
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.xianxia-fukuan-kongjian-may-box {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  .form-container {
    width: 50%;

    ::v-deep {
      .el-collapse-item__wrap {
        background-color: unset;
        border-bottom: unset;
      }

      .el-input.is-disabled .el-input__inner {
        color: #606266;
      }
    }
  }

  .pay-box {
    width: 100%;
    border-radius: 6px;
    box-sizing: border-box;
    line-height: 20px;
    & span:first-child {
      display: block;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      font-weight: 500;
      margin-right: 8px;
    }
    & span:last-child {
      color: #00072a;
      font-weight: 600;
      font-size: 14px;
    }

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 150px;
        // text-align: right;
      }
    }
  }
}
</style>
