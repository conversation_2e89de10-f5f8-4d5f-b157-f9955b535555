<template>
  <div class="completion-page-box">
    <!-- 申请成功 -->
    <div
      v-if="ProcessInfoGDatas?.flowStatus === 3"
      class="product-businessApproval"
    >
      <MySvgIcon
        icon-class="icon-chenggong"
        style="color: #0bb07b; font-size: 64px"
      ></MySvgIcon>
      <span class="under-review">申请成功</span>
      <span class="message-span">您提交的信息已通过审核</span>
      <div class="gohome">
        <NButton
          class="blue button-item primary"
          style="width: 148px; height: 48px"
          :bordered="true"
          round
          @click="applyForLoan"
        >
          <span class="desc">去申请放款</span>
        </NButton>
      </div>
    </div>
    <!-- 申请失败 -->
    <div
      v-else-if="ProcessInfoGDatas?.flowStatus === 2"
      class="product-businessApproval"
    >
      <MySvgIcon
        icon-class="icon-shibai"
        style="color: #f03d3d; font-size: 64px"
      ></MySvgIcon>
      <span class="under-review">申请失败</span>
      <span class="message-span">{{ ProcessInfoGDatas?.fullMessage }}</span>
      <div class="jingruiit-accounts">
        <img :src="wxImg" alt="二维码" />
      </div>
      <div class="gohome">
        <NButton
          class="blue button-item"
          style="width: 148px; height: 48px"
          :bordered="true"
          round
          @click="toRouterPath"
        >
          <span class="desc">返回首页</span>
        </NButton>
      </div>
    </div>
    <!-- 加载中 -->
    <div v-else>
      <a-spin tip="加载中...">
        <span style="display: inline-block; height: 240px" />
      </a-spin>
    </div>
  </div>
</template>

<script>
export default {
  name: 'completionPage',
}
</script>
<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { NButton } from 'naive-ui'
import { HOMEAPI } from '@/api/index'
const router = useRouter()
const route = useRoute()
const store = useStore()

// 微信公众号img数据
const wxImg = ref('')
// 流程信息
const ProcessInfoGDatas = computed(
  () => store.getters['Product/processInfoObj']
)
// 获取产品流程配置信息
const Gdata = {
  // 请求参数
  requiredParams: {
    goodId: route.query.goodId,
    processType: route.query.processType,
    flowOnlyId: route.query.flowOnlyId,
  },
  route,
}
store.dispatch('Product/processInfoGFuns', Gdata)

// 获取微信公众号img数据
HOMEAPI.getCecList().then(res => {
  const resData = res.data
  if (resData.code == 200) {
    for (const item of resData.data) {
      if (item.qrCode) {
        wxImg.value = item.qrCode
      }
    }
  }
})

// 去相应路由页面
const toRouterPath = () => {
  router.push({ name: 'Home' })
}

// 去申请放款
const applyForLoan = () => {
  router.replace({ path: '/user/financing' })
}
</script>

<style lang="scss" scoped>
.completion-page-box {
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  padding-top: 40px;
  flex: 1;
  .product-businessApproval {
    position: relative;
    max-width: 1400px;
    margin: auto;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .product-businessApproval-flagstaff {
      width: 100%;
      height: 2px;
      background: #f1f2f4;
      margin-top: 39px;
    }

    & > #my-svg-icons {
      margin-top: 60px;
    }

    .under-review {
      font-size: 24px;
      @include family-PingFangSC-Semibold;
      font-weight: 600;
      color: #0a1f44;
      line-height: 32px;
      margin-top: 12px;
    }

    .under-money {
      :deep(.ant-statistic-content-value > span) {
        font-size: 45px;
        @include family-CoreSansD65Heavy;
        color: #031222;
      }

      .money-prefix {
        font-size: 20px;
        @include family-CoreSansD65Heavy;
        color: #031222;
      }
    }

    .message-span {
      font-size: 14px;
      @include family-PingFangSC-Medium;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
      margin-top: 12px;
    }

    .capital-information {
      width: 400px;
      height: 240px;
      background: #f8f9fb;
      border-radius: 8px;
      margin-top: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .capital-information-item {
        margin-bottom: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        &:last-child {
          margin-bottom: 0;
        }

        & > span:first-child {
          width: 30%;
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #8a94a6;
          margin-right: 24px;
          display: inline-block;
          box-sizing: border-box;
          text-align: right;
        }
        & > span:last-child {
          width: 70%;
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #0a1f44;
          display: inline-block;
          box-sizing: border-box;
        }
      }
    }

    .jingruiit-accounts {
      width: 196px;
      height: 196px;
      border-radius: 8px;
      border: 1px solid #efefef;
      margin-top: 24px;
      overflow: hidden;

      & > img {
        width: 100%;
        object-fit: contain;
      }
    }

    .gohome {
      margin-top: 32px;
    }
  }
}
</style>
