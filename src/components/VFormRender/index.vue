<template>
  <div class="VForm-render-box">
    <div v-if="isLoading">
      <a-spin>
        <span style="display: inline-block; height: 240px" />
      </a-spin>
    </div>
    <div v-else-if="arrData.length" class="father-box">
      <div v-for="item in arrData" :key="item.id" class="my-vform-box">
        <div class="table-name-box">
          <MySvgIcon
            icon-class="icon-a-1"
            style="fill: #000; font-size: 20px"
          />
          {{ item.tableName }}
        </div>
        <v-form-render
          :ref="el => setvFormRef(el, item.id)"
          :form-json="item.formJson"
          :form-data="item.formData"
          :option-data="item.optionData"
        />
      </div>
    </div>
    <div v-else-if="notData" class="content-box-empty" style="height: 200px">
      <div class="receiving-empty">
        <div class="receiving-box">
          <img src="@/assets/images/empty_3.svg" />
          <span class="receiving-title">暂无表单数据</span>
          <!-- <span class="receiving-address" @click="handleOpen()">添加账户</span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { VFormRender } from 'vform3-builds/dist/render.umd.js' //引入VFormRender组件
// import 'vform3-builds/dist/render.style.css' //引入VFormRender样式
import { VFormRender } from 'hwp-form3/render.umd.js' //引入VFormRender组件
import 'hwp-form3/render.style.css' //引入VFormRender样式
import {
  processSaveGFun,
  getAtPresentObj,
} from '@/components/VFormRender/processG'
export default {
  name: 'VFormRender',
}
</script>
<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { removeLocal, getLocal } from '@/utils/localStorage'
const router = useRouter()
const route = useRoute()
const store = useStore()

const props = defineProps({
  // 是否固定页
  notSlot: {
    type: Boolean,
    required: false,
    default: true,
  },
  // 是否显示缺损页
  notData: {
    type: Boolean,
    required: false,
    default: false,
  },
  // onClick: {
  //   type: Function,
  //   default: () => {},
  // },
})

// 获取产品流程配置信息
const Gdata = {
  // 请求参数
  requiredParams: {
    goodId: route.query.goodId,
    processType: route.query.processType,
    flowOnlyId: route.query.flowOnlyId,
  },
}
store.dispatch('Product/processInfoGFuns', Gdata)
// 流程信息
const ProcessInfoGDatas = computed(
  () => store.getters['Product/processInfoObj']
)
// 是否登录（Boolean）
const isLogined = computed(() => store.getters['Auth/isLogined'])

const vFormRef = ref([])
const setvFormRef = (el, keys) => {
  if (el) {
    vFormRef.value.push({ el, keys })
  }
}
const myFormData = reactive({})
const arrData = ref([])
const atPObj = ref({})
const lock = ref(false)
const beforeLock = ref(false)
const isLoading = ref(true)

// 赋值前处理日期选择器YYYY、DD的兼容性问题(二开源码已经处理完成)
const dataTreating = atP => {
  const listD = atP.AtObj.vformArr
  // for (const item of listD) {
  //   const jsonList = item.formJson.widgetList
  //   for (const items of jsonList) {
  //     console.log('items', items)
  //     items.options.format
  //   }
  // }
  arrData.value = listD
  setTimeout(() => {
    // 循环处理vform响应式回显
    onFormSetFun()
  }, 300)
}

watch(
  () => [props.notSlot, ProcessInfoGDatas.value?.current, isLogined.value],
  ([n, p, l]) => {
    // 上一步的页面是基础页进行锁定不进入watch
    if (beforeLock.value) return
    // 当前进度不存在不进入watch
    if (!p) return
    // 判断是否登录
    if (!l) return
    // 基础页进入一次就进行锁定不进入watch
    if (!lock.value && n) {
      lock.value = true
    } else if (lock.value) {
      return
    }
    isLoading.value = true
    vFormRef.value = []
    const atP = getAtPresentObj('watch')
    // 给保存接口使用
    atPObj.value = atP
    // vform渲染的数组
    dataTreating(atP)
    isLoading.value = false
  },
  { deep: false, immediate: true }
)

// 循环处理vform响应式回显
const onFormSetFun = () => {
  for (const item of vFormRef.value) {
    // for (const [index, item] of vFormRef.value.entries()) {
    // const data = arrData.value[index]
    // item.el.setFormJson(data.formJson)
    item.el.setFormData({})
  }
}

// 提交校验
const submitForm = datas => {
  const { externalData = {}, schedule = 'later' } = datas

  const atP = getAtPresentObj(schedule, schedule)
  if (!atP.AtObj) {
    const backRouterObj = getLocal('backRouterObj')
    if (backRouterObj) {
      router.replace(backRouterObj)
      removeLocal('backRouterObj')
      return
    }
    message.warning('当前属于第一个节点,无法再回退')
    return
  }
  // 上一步\下一步的目标页面是基础页、审批页、完成页进行锁定不进入watch
  if ([1, 4, 5].includes(atP.AtObj.nodeType)) {
    beforeLock.value = true
  }
  const isValidArr = []
  // 循环处理vform校验，校验通过保存数据
  for (const item of vFormRef.value) {
    const result = new Promise((resolve, reject) => {
      if (schedule === 'later') {
        item.el
          .getFormData()
          .then(formData => {
            myFormData[`iId_${item.keys}`] = formData
            resolve()
          })
          .catch(error => {
            // message.warning(error)
            reject()
          })
      } else {
        // 上一步不校验必填项，进行保存
        const formData = item.el.getFormData(false)
        myFormData[`iId_${item.keys}`] = formData
        resolve()
      }
    })
    isValidArr.push(result)
  }

  return new Promise((resolve, reject) => {
    // 不存在vform表单直接调用保存接口
    if (!isValidArr.length) {
      processSaveGFuns(externalData, schedule, datas, {
        resolve,
        reject,
      })
      return
    }
    Promise.all(isValidArr)
      .then(() => {
        // 校验成功调用保存接口
        processSaveGFuns(externalData, schedule, datas, {
          resolve,
          reject,
        })
      })
      .catch(() => {
        message.warning('请完善必填信息')
      })
  })
}

// 返回上一步
const lastStep = () => {
  submitForm({ schedule: 'before' })
}

// 校验成功调用保存接口
const processSaveGFuns = (
  externalData,
  schedule,
  datas,
  { resolve, reject }
) => {
  const dataObj = {
    route,
    myFormData,
    atPObj: atPObj.value,
    externalData,
    schedule,
    allDatas: datas,
  }
  processSaveGFun(dataObj)
    .then(res => {
      resolve(res)
    })
    .catch(() => {
      reject()
    })
}

defineExpose({
  submitForm,
  lastStep,
})
</script>
<style lang="scss" scoped>
.VForm-render-box {
  .father-box {
    max-width: 1400px;
    margin: 40px auto 32px;
  }
  .my-vform-box {
    margin-top: 40px;
    padding: 40px;
    background-color: #fff;
    box-shadow: 0px 12px 24px 0px rgb(10 31 68 / 4%);
    border-radius: 16px;
    border: 1px solid #efefef;
    box-sizing: border-box;

    .table-name-box {
      font-size: 24px;
      line-height: 32px;
      color: #0a1f44;
      font-weight: 500;
      margin-bottom: 16px;
    }
  }

  .content-box-empty {
    display: flex;
    justify-content: center;
    align-items: center;

    .receiving-empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .receiving-box {
        display: flex;
        align-items: center;
        flex-direction: column;
        & > img {
          width: 200px;
          height: 200px;
        }
        .receiving-title {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #8a94a6;
          height: 20px;
          line-height: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
        }
        .receiving-address {
          margin-top: 12px;
          cursor: pointer;
          display: block;
          width: 96px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border: 1px solid #e1e4e8;
          color: #0a1f44;
          font-size: 14px;
          font-weight: 500;
          border-radius: 100px;
          background-color: #ffffff;
        }
      }
    }
  }
}
</style>
