<template>
  <div>
    <div class="infometion-box" style="margin-top: 30px">
      <div class="infometion-top-box">
        <div class="infometion-left">
          <div class="left-title">{{ chuanruObj.expenseTypeName }}(元)</div>
          <div class="left-money">
            <div class="num">
              <span class="jinbifuhao-sp"> ￥ </span>
              <span class="qianqianzhanshi-sp">{{
                chuanruObj.totalAmount
              }}</span>
              <template v-if="shifouzhanshibenjLixiC">
                <span class="baohanbenjinlixi-sp-t">(包含本金+利息)</span>
                <span class="baohanbenjinlixi-sp-j">{{
                  chuanruObj.exAmount
                }}</span>
              </template>
            </div>
            <div class="play-type">
              <MySvgIcon
                icon-class="icon-xiajiantou-yuan1"
                style="fill: #0d55cf; font-size: 20px"
              />
              <span class="play-text" style="">线下支付</span>
            </div>
          </div>
          <p class="left-tips">
            请将其他费用转账至下方收款账户，为了提升审批进度，请备注支付信息，方便平台工作人员进行审核～
          </p>
          <div class="management-accounts">
            <p class="accounts-p">
              <span>收款账户</span>
              <span>{{ chuanruObj.accountNo }}</span>
            </p>
            <p class="accounts-p">
              <span>收款公司名</span>
              <span>{{ chuanruObj.enterpriseName }}</span>
            </p>
            <p class="accounts-p">
              <span>开户银行</span>
              <span>{{ chuanruObj.openBank }}</span>
            </p>
            <p class="accounts-p">
              <span>备注</span>
              <span>企业名称+产品名称</span>
            </p>
          </div>
        </div>
        <div class="infometion-right">
          <UploadDragger
            @setfileList="setfileAttachList"
            :arrData="chuanruObj.attachList"
          />
        </div>
      </div>
      <div
        class="infometion-bottom-box"
        v-if="
          chuanruObj.expenseOrderDetail && chuanruObj.expenseOrderDetail.length
        "
      >
        <div class="bottom-title">费用明细</div>
        <div class="table-box">
          <a-table
            :columns="offlineFeecolumns"
            :data-source="chuanruObj.expenseOrderDetail"
            :pagination="false"
          >
            <template
              #summary
              v-if="
                chuanruObj.expenseOrderDetail &&
                chuanruObj.expenseOrderDetail.length
              "
            >
              <a-table-summary-row>
                <a-table-summary-cell
                  style="
                    font-size: 20px;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #0a1f44;
                    line-height: 28px;
                  "
                  >总计</a-table-summary-cell
                >
                <a-table-summary-cell />
                <a-table-summary-cell />
                <a-table-summary-cell
                  style="
                    font-size: 20px;
                    font-family: SFProDisplay-Semibold, SFProDisplay;
                    font-weight: 600;
                    color: #0a1f44;
                    line-height: 28px;
                  "
                >
                  <a-typography-text>
                    ￥{{ chuanruObj.totalAmount }}
                    <!-- <span
                      v-if="
                        showNowCom === 2 &&
                        item.redeemAccountVOS.expenseType === '资方费用'
                      "
                    >
                      (包含本金 ￥{{ totalPrincipal }})
                    </span> -->
                  </a-typography-text>
                </a-table-summary-cell>
              </a-table-summary-row>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'xianxiafeiyong',
}
</script>
<script setup>
import { computed } from 'vue'
import UploadDragger from '@/views/product/loanApplication/components/component/uploadDragger.vue'

const props = defineProps({
  chuanruObj: {
    type: Object,
    required: true,
    default: () => {},
  },
})

const offlineFeecolumns = [
  {
    title: '费用名称',
    dataIndex: 'name',
  },
  {
    title: '支付节点',
    dataIndex: 'feeNodeStr',
  },
  {
    title: '计费方式',
    dataIndex: 'feeFormulaName',
  },
  {
    title: '应付金额',
    dataIndex: 'amount',
  },
]

const shifouzhanshibenjLixiC = computed(
  () =>
    props.chuanruObj.expenseInfoType === 1 &&
    Number(props.chuanruObj.exAmount) > 0
)

// 获取上传附件
const setfileAttachList = fileList => {
  props.chuanruObj.attachList = fileList
}
</script>

<style lang="scss" scoped>
.infometion-box {
  background: #ffffff;
  box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
  border-radius: 16px;
  border: 1px solid #efefef;
  padding: 40px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .infometion-top-box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .infometion-left {
      .left-title {
        height: 28px;
        font-size: 20px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 28px;
      }
      .left-money {
        display: flex;
        align-items: center;

        .num {
          .jinbifuhao-sp {
            font-size: 24px;
            font-family: CoreSansD65Heavy;
            color: #031222;
            line-height: 30px;
            font-weight: 600;
          }

          .qianqianzhanshi-sp {
            font-size: 50px;
            color: #031222;
            font-weight: 600;
          }

          .baohanbenjinlixi-sp-t {
            font-size: 16px;
            font-weight: bold;
            margin-left: 8px;
          }

          .baohanbenjinlixi-sp-j {
            font-size: 25px;
            font-weight: bold;
            margin-left: 8px;
          }
        }
        .play-type {
          height: 40px;
          background: #ebf5ff;
          padding: 10px 20px;
          box-sizing: border-box;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          line-height: 20px;
          border-radius: 100px;
          display: flex;
          align-items: center;
          margin-left: 15px;

          .play-text {
            margin-left: 4px;
            color: #0d55cf;
          }
        }
      }
      .left-tips {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #8a94a6;
        line-height: 20px;
        margin-bottom: 24px;
      }
      .management-accounts {
        .accounts-p {
          margin-bottom: 32px;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            display: inline-block;
          }

          & span:first-child {
            width: 70px;
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
            margin-right: 10px;
          }
          & span:last-child {
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #0a1f44;
            line-height: 20px;
          }
        }
      }
    }

    .infometion-right {
      width: 394px;
    }
  }

  .infometion-bottom-box {
    overflow: hidden;
    margin-top: 40px;

    .bottom-title {
      height: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #0a1f44;
      line-height: 20px;
    }
    .table-box {
      margin-top: 12px;
      border: 1px solid #f1f2f4;
    }
  }

  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    width: 279px;
    height: 277px;
    top: -50px;
    right: -40px;
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.2;
    border-radius: 150px;
    filter: blur(49px);
  }
  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    width: 106px;
    height: 143px;
    top: -20px;
    left: 0px;
    border-radius: 150px;
    filter: blur(24px);
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.2;
  }
}

:deep(.ant-table) {
  background: transparent;

  .ant-table-thead {
    .ant-table-cell {
      background-color: #f8f9fb;
    }
  }

  .ant-table-thead {
    .ant-table-cell {
      padding: 10px 24px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }
  }

  .ant-table-tbody {
    .ant-table-cell {
      padding: 10px 24px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #0a1f44;
      line-height: 20px;
    }
  }

  .ant-table-summary {
    background-color: #f8f9fb;
  }
}
</style>
