<template>
  <div class="infometion-box">
    <div class="infometion-top-box">
      <div class="infometion-left">
        <div class="flex">
          <div class="left-title">需缴保证金(元)</div>
          <div class="left-subtitle">
            <MySvgIcon
              icon-class="icon-yirenzheng"
              style="fill: #0d55cf; font-size: 16px"
            ></MySvgIcon>
            <span
              >保证金将按照合规要求,存放与中国人民银行开立的监管账户,还款后将按照释放方式原路退回</span
            >
          </div>
        </div>
        <div class="left-money">
          <div class="left-flex">
            <span class="num-icon">￥</span>
            <span class="num">{{ chuanruObj.totalAmount }}</span>
          </div>
          <div class="play-type">
            <MySvgIcon
              :icon-class="'icon-xiajiantou-yuan1'"
              style="fill: #0d55cf; font-size: 20px"
            />
            <span class="play-text">线下支付</span>
          </div>
        </div>
        <div class="left-charge">
          <p class="charge-item">
            <span>收取比例</span>
            <span>融资金额*{{ chuanruObj.expenseDeposit.cashDepositRate || 0 }}%</span>
          </p>
          <p class="charge-item">
            <span>退还方式</span>
            <span>{{ chuanruObj.expenseDeposit.refundTypeStr }}</span>
          </p>
        </div>
        <p class="left-tips">
          请将其他费用转账至下方收款账户，为了提升审批进度，请备注支付信息，方便平台工作人员进行审核～
        </p>
        <div class="management-accounts">
          <p class="accounts-p">
            <span>收款账户</span>
            <span>{{ chuanruObj.accountNo }}</span>
          </p>
          <p class="accounts-p">
            <span>收款公司名</span>
            <span>{{ chuanruObj.enterpriseName }}</span>
          </p>
          <p class="accounts-p">
            <span>开户银行</span>
            <span>{{ chuanruObj.openBank }}</span>
          </p>
          <p class="accounts-p">
            <span>备注</span>
            <span>企业名称+产品名称</span>
          </p>
        </div>
      </div>
      <div class="infometion-right">
        <UploadDragger
          @setfileList="setfileAttachList"
          :arrData="chuanruObj.attachList"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'baozjxianxia',
}
</script>
<script setup>
import UploadDragger from '@/views/product/loanApplication/components/component/uploadDragger.vue'

const props = defineProps({
  chuanruObj: {
    type: Object,
    required: true,
    default: () => {},
  },
})

// 获取上传附件
const setfileAttachList = fileList => {
  props.chuanruObj.attachList = fileList
}
</script>

<style lang="scss" scoped>
.infometion-box {
  margin-top: 40px;
  background: #ffffff;
  box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
  border-radius: 16px;
  border: 1px solid #efefef;
  padding: 40px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 48px;

  .infometion-top-box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .infometion-left {
      .left-title {
        height: 28px;
        font-size: 20px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 28px;
      }
      .left-subtitle {
        display: flex;
        align-items: center;
        margin-left: 12px;
        background-color: #ebf5ff;
        padding: 8px 3px 8px 21px;
        box-sizing: border-box;
        border-radius: 8px;
        & span {
          margin-left: 2px;
          display: block;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #0d55cf;
          line-height: 16px;
        }
      }
      .left-money {
        display: flex;
        align-items: center;
        margin: 10px 0 12px;
        .left-flex {
          display: flex;
          align-items: baseline;
          line-height: 58px;

          .num-icon {
            font-size: 24px;
            color: #031222;
            font-weight: 600;
          }
          .num {
            font-size: 48px;
            color: #031222;
            font-weight: 600;
          }
        }

        .play-type {
          height: 40px;
          background: #ebf5ff;
          padding: 10px 20px;
          box-sizing: border-box;
          font-size: 14px;
          font-weight: 500;
          color: #0d55cf;
          line-height: 20px;
          border-radius: 100px;
          display: flex;
          align-items: center;
          margin-left: 15px;

          .play-text {
            margin-left: 4px;
          }
        }
      }
      .left-charge {
        .charge-item {
          display: flex;
          align-items: center;
          & span:first-child {
            display: block;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #0a1f44;
            line-height: 20px;
          }
          & span:last-child {
            display: block;
            font-size: 14px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #0a1f44;
            line-height: 20px;
            margin-left: 24px;
          }
        }
        .charge-item:first-child {
          margin-bottom: 20px;
        }
        .charge-item:last-child {
          margin-bottom: 24px;
        }
      }
      .left-tips {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #8a94a6;
        line-height: 20px;
        margin-bottom: 24px;
      }
      .management-accounts {
        .accounts-p {
          margin-bottom: 32px;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            display: inline-block;
          }

          & span:first-child {
            width: 70px;
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
            margin-right: 10px;
          }
          & span:last-child {
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #0a1f44;
            line-height: 20px;
          }
        }
      }
    }

    .infometion-right {
      width: 394px;
    }
  }

  .infometion-bottom-box {
    overflow: hidden;
    margin-top: 40px;

    .bottom-title {
      height: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #0a1f44;
      line-height: 20px;
    }
    .table-box {
      margin-top: 12px;
      border: 1px solid #f1f2f4;
    }
  }

  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    width: 279px;
    height: 277px;
    top: -50px;
    right: -40px;
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.2;
    border-radius: 150px;
    filter: blur(49px);
  }
  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    width: 106px;
    height: 143px;
    top: -20px;
    left: 0px;
    border-radius: 150px;
    filter: blur(24px);
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.2;
  }
}
</style>
