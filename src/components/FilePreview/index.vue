<template>
  <a-modal
    v-model:visible="dialogVisible"
    :modal="true"
    :wrapClassName="`pdf-pre-dialog ${isFullscreen ? 'full-modal' : ''}`"
    width=""
    centered
    destroyOnClose
    :closable="false"
    :footer="null"
  >
    <template v-slot:title>
      <span class="pdf-pre-title">文件预览</span>
      <div class="dialog__menu">
        <!-- <MySvgIcon
          class="icon-btn"
          icon-class="icon-quanping"
          @click="handleFullScreen"
        /> -->
        <fullscreen-exit-outlined
          v-if="isFullscreen"
          @click="handleFullScreen"
          class="icon-btn"
        />
        <fullscreen-outlined
          v-else
          class="icon-btn"
          @click="handleFullScreen"
        />
        <MySvgIcon
          class="icon-btn"
          icon-class="icon-guanbi"
          @click="dialogVisible = false"
        />
      </div>
    </template>

    <iframe
      v-if="dialogVisible && pdfUrl"
      :src="pdfUrl"
      frameborder="no"
      class="pdf-iframe"
    ></iframe>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'FilePreview',
})
</script>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons-vue'

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  watchUrl: {
    type: Boolean,
    default: true,
  },
})

const dialogVisible = ref(false)
const isFullscreen = ref(false)
const pdfUrl = ref<string | undefined>(undefined)

watch(
  () => props.url,
  newUrl => {
    pdfUrl.value = '/cdn/pdfjs/web/viewer.html?file=' + newUrl
    dialogVisible.value = true
  }
)

const handleFullScreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const handleOpen = (targetUrl: string) => {
  pdfUrl.value = '/cdn/pdfjs/web/viewer.html?file=' + (targetUrl || props.url)
  dialogVisible.value = true
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss">
.pdf-pre-dialog {
  .ant-modal {
    width: 800px;
    height: 84vh;
    border-radius: 4px;
    overflow: hidden;
  }

  &.full-modal {
    .ant-modal {
      width: 100vw;
      height: 100vh;
      border-radius: initial;
      max-width: initial;
    }
  }

  .ant-modal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon-btn {
      margin-right: 12px;
      font-size: 18px;
      cursor: pointer;
      vertical-align: middle;
      color: rgba(0, 0, 0, 0.45);

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        color: rgba(0, 0, 0, 0.75);
      }
    }
  }

  .ant-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-modal-body {
      height: 100%;
    }
  }

  .ant-modal-body {
    padding: 0;
  }
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

.pdf-iframe body,
.pdf-iframe .toolbarContainer {
  background-color: white;
}
</style>
