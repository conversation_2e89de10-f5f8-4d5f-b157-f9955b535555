<template>
  <div class="message-container" v-if="visible">
    <div class="dialog-container">
      <div class="dialog-header">
        <LabelBar :labelList="labelList" @switch="handleSwitch" />
        <span class="all-ready" @click="allReady">全部已读</span>
      </div>
      <div class="dialog-main" ref="dialogBox">
        <a-spin v-if="!messageArr.length && loadingType" tip="加载中...">
          <span style="display: inline-block; height: 300px" />
        </a-spin>
        <div v-else-if="messageArr.length">
          <div
            v-for="(item, index) in messageArr"
            :key="item.id"
            class="dialog-main-list-item"
            @click="handleDelete(item, index)"
          >
            <div class="dialog-main-list-item-left">
              <div class="dialog-main-icon">
                <img
                  :src="require('@/assets/images/user/information-success.svg')"
                />
              </div>
              <div class="dialog-main-content">
                <span>{{ item.title }}</span>
                <span>{{ item.content }}</span>
              </div>
            </div>
            <div class="dialog-main-list-item-right">{{ item.notifyTime }}</div>
          </div>
        </div>
        <div v-else-if="!loadingType" class="dialog-list-empty">
          <img src="@/assets/images/empty_2.svg" alt="" />
        </div>
      </div>
      <p
        v-if="messageArr.length && loadingType"
        style="display: inline-block; width: 100%; text-align: center"
      >
        加载中...
      </p>
      <div class="dialog-footer" @click="handleAllNotice">查看全部消息</div>
    </div>
  </div>
  <!-- 消息详情弹窗 -->
  <MessageDeatil ref="messageDeatil" />
</template>

<script>
export default {
  name: 'NotiveDialog',
}

// const labelList = ['通知', '预警', '推送']
const labelList = ['通知', '预警']
</script>

<script setup>
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import MessageDeatil from './Dialog/messageDetail'
import { ref, watch, onMounted, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import _ from 'lodash'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import { MESSAGE } from '@/api/index'
const store = useStore()

const emit = defineEmits(['closeDialog'])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const router = useRouter()
const dialogBox = ref(null)
const messageDeatil = ref(null)
const messageArr = ref([]) // 消息列表
const onOff = ref(false) // 节流标识
const loadingType = ref(false)
const messageNums = computed(() => store.getters['Product/messageNums'])
const inforefData = reactive({
  current: 1,
  size: 10,
  pages: 1,
  type: 1,
  genre: 2,
})

// 懒加载
const handleScroll = () => {
  const event = dialogBox.value
  if (!event) return
  // 标准浏览器中：定义一个形参event，但当事件触发的时候，并没有给event赋实际的值，
  // 则浏览器会把”事件“的对象赋给这个形参e，这时这个e是个系统级的对象：事件；
  const scrollDistance =
    // 正文全文高
    event.scrollHeight -
    // 被卷去的高
    event.scrollTop -
    // 可见区域的宽度
    event.clientHeight
  // 滚动条距离底部小于等于0证明已经到底了，可以请求接口了
  if (scrollDistance <= 400) {
    //这个开关是为了避免请求数据中 再次被请求
    if (onOff.value) return
    onOff.value = true
    //当前页数小于总页数 就请求
    if (inforefData.current < inforefData.pages) {
      loadingType.value = true
      inforefData.current += 1
      loadTaksData()
    }
  }
}

// 数据list请求
const loadTaksData = () => {
  if (!loadingType.value) {
    loadingType.value = true
  }
  let params = {
    current: inforefData.current,
    size: inforefData.size,
    type: inforefData.type,
    genre: inforefData.genre,
  }
  MESSAGE.messageList(params).then(({ data }) => {
    if (data.success) {
      const { data: resData } = data
      inforefData.pages = resData.pages
      if (resData.records.length) {
        for (const item of resData.records) {
          const [n, y] = item.notifyTime.split('年')
          messageArr.value.push({
            ...item,
            notifyTime: y,
          })
        }
      }
      onOff.value = false
      loadingType.value = false
    }
  })
}

// tag切换事件
const handleSwitch = async targetIndex => {
  inforefData.current = 1
  inforefData.type = targetIndex + 1
  messageArr.value = []
  await loadTaksData()
  dialogBox.value.scrollTop = 0
}

// 全部已读功能
const allReady = () => {
  MESSAGE.updateAllRead().then(({ data }) => {
    if (data.success) {
      store.commit('Product/setMessageNum', 0)
      messageArr.value = []
      message.success('操作成功')
    }
  })
}

// 消息点击事件
const handleDelete = (item, index) => {
  MESSAGE.messageDetail(item.id).then(({ data }) => {
    if (data.success) {
      store.commit('Product/setMessageNum', messageNums.value - 1)
      emit('closeDialog')
      messageDeatil.value.handleOpen(item)
      messageArr.value.splice(index, 1)
    }
  })
}

// 查看全部消息
const handleAllNotice = () => {
  emit('closeDialog')
  router.push('/user/information')
}

const throttle = _.throttle(handleScroll, 1000)

onMounted(() => {
  document.addEventListener('mouseup', e => {
    //获取弹窗对象
    const userCon = document.querySelector('.message-container')
    //判断弹窗对象中是否包含点击对象
    if (userCon && !userCon.contains(e.target)) {
      emit('closeDialog')
    }
  })
})
watch(
  () => props.visible,
  val => {
    if (val) {
      window.addEventListener('scroll', throttle, true)
      inforefData.current = 1
      messageArr.value = []
      loadTaksData()
    } else {
      window.removeEventListener('scroll', throttle, true)
    }
  }
)
</script>

<style lang="scss" scoped>
.message-container {
  position: absolute;
  top: 72px;
  right: 88px;
  width: 500px;
  height: 492px;
  background: #ffffff;
  box-shadow: 0px 26px 26px 0px rgba(10, 31, 68, 0.12),
    0px 0px 1px 0px rgba(10, 31, 68, 0.1);
  border-radius: 16px;
  z-index: 3000;
  padding: 32px;
}

.dialog-container {
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    .all-ready {
      cursor: pointer;
      color: #0d55cf;
      font-size: 14px;
      font-weight: 400;
      height: 20px;
      line-height: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
    }
  }
  .dialog-main {
    height: 312px;
    overflow: auto;
    position: relative;
    .dialog-main-list-item {
      cursor: pointer;
      padding: 8px 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:hover {
        background: #f8f9fb;
      }
      &:not(:last-child) {
        margin-bottom: 8px;
      }
      .dialog-main-list-item-left {
        display: flex;
        box-sizing: border-box;
        .dialog-main-icon {
          width: 40px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-radius: 50%;
          background-color: #ebf5ff;
          margin-right: 12px;
          & img {
            width: 20px;
            height: 20px;
          }
        }

        .dialog-main-content {
          span {
            width: 282px;
            display: block;
            overflow: hidden;
            white-space: nowrap;
            text-align: left;
            text-overflow: ellipsis;
            &:first-child {
              color: #0a1f44;
              font-size: 14px;
              font-weight: 500;
              margin-bottom: 2px;
              font-family: SFProText-Medium, SFProText;
            }
            &:last-child {
              font-size: 12px;
              font-weight: 400;
              height: 16px;
              line-height: 16px;
              color: #8a94a6;
              font-family: SFProText-Regular, SFProText;
            }
          }
        }
      }
      .dialog-main-list-item-right {
        color: #53627c;
        font-size: 12px;
        font-weight: 400;
        height: 16px;
        line-height: 16px;
      }
    }
    .dialog-list-empty {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      & > img {
        width: 200px;
        height: 200px;
      }
    }
  }
  .dialog-footer {
    cursor: pointer;
    padding-top: 24px;
    text-align: center;
    font-size: 14px;
    color: #8a94a6;
    font-weight: 400;
  }
}
</style>
