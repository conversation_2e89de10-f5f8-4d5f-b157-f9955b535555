<template>
  <div class="other-expenses">
    <div class="main-content">
      <div class="form-container">
        <!-- 线上支付 -->
        <div class="onLinePay" v-if="FeeObj.onlineFeeArr.length">
          <div class="infometion-top-box">
            <div class="infometion-left">
              <div class="left-title">线上支付费用(元)</div>
              <div class="left-money">
                <span class="num">{{ FeeObj.allMoney }}</span>
                <div
                  class="play-type"
                  :style="{ background: payType == 2 ? '#0BB07B' : '#0d55cf' }"
                >
                  <MySvgIcon
                    :icon-class="
                      payType == 2 ? 'icon-chenggong1' : 'icon-dengdai1'
                    "
                    style="fill: #fff; font-size: 20px"
                  />
                  <span class="play-text">{{ getPayType[payType] }}</span>
                  <!-- getPayType[Number(payType) - 1] -->
                </div>
                <!-- <div style="margin-left: 3px" v-if="payType == 2">
                  <a-button type="text" danger @click="cancelPayment">
                    <span style="font-weight: bold"> 取消支付 </span>
                  </a-button>
                </div> -->
              </div>
              <div class="online-payment-box">
                <p class="pay-text" v-if="Feedata.isContainCapitalCost == 2">
                  请选择支付方式
                </p>
                <LabelBar
                  v-if="Feedata.isContainCapitalCost == 2"
                  :labelList="labelData.labelList"
                  :state="labelData.currentIndex"
                  :disableM="payType == 2"
                  @switch="handleSwitch"
                />
                <div
                  v-if="
                    Feedata.isContainCapitalCost == 1 &&
                    FeeObj.offlinePayArr.length != 0 &&
                    payType != 2
                  "
                >
                  <n-button
                    :bordered="false"
                    :disabled="payType == 1"
                    round
                    type="info"
                    size="large"
                    @click="onlinePay"
                    >立即支付</n-button
                  >
                </div>
              </div>
            </div>
            <div
              v-if="Feedata.isContainCapitalCost == 2"
              class="infometion-right"
              :class="{
                'infometion-obscuration':
                  payLoading || payType || labelData.currentIndex !== 0,
              }"
            >
              <PaymentQRCode :financeNoV="2" />
            </div>
          </div>
          <div v-for="item in FeeObj.onlineFeeArr" :key="item.id">
            <div class="infometion-bottom-box">
              <div class="bottom-title">{{ item.parenName }}</div>
              <div class="table-box">
                <a-table
                  :columns="feecolumns"
                  :data-source="item.expenseOrderDetailList"
                  :pagination="false"
                  :loading="myLoading"
                >
                  <template #summary v-if="!myLoading">
                    <a-table-summary-row>
                      <a-table-summary-cell
                        style="
                          font-size: 20px;
                          font-family: PingFangSC-Semibold, PingFang SC;
                          font-weight: 600;
                          color: #0a1f44;
                          line-height: 28px;
                        "
                      >
                        总计
                      </a-table-summary-cell>
                      <a-table-summary-cell />
                      <a-table-summary-cell />
                      <a-table-summary-cell />
                      <a-table-summary-cell
                        style="
                          font-size: 20px;
                          font-family: SFProDisplay-Semibold, SFProDisplay;
                          font-weight: 600;
                          color: #0a1f44;
                          line-height: 28px;
                        "
                      >
                        <a-typography-text>
                          ￥{{ item.totalAmount }}
                        </a-typography-text>
                      </a-table-summary-cell>
                    </a-table-summary-row>
                  </template>
                </a-table>
              </div>
            </div>
          </div>
        </div>
        <!-- 动态循环费用(线下) -->
        <div
          class="infometion-box"
          v-for="(item, index) in FeeObj.offlinePayArr"
          :key="index"
          style="margin-top: 30px"
        >
          <div class="infometion-top-box">
            <div class="infometion-left">
              <div class="left-title">
                {{ item.redeemAccountVOS.parenName }}(元)
              </div>
              <div class="left-money">
                <span class="num"
                  ><span
                    style="
                      font-size: 24px;
                      font-family: CoreSansD65Heavy;
                      color: #031222;
                      line-height: 30px;
                    "
                    >￥</span
                  >{{
                    Number(item.redeemAccountVOS.totalAmount).toFixed(2)
                  }}</span
                >
                <div class="play-type">
                  <MySvgIcon
                    icon-class="icon-xiajiantou-yuan1"
                    style="fill: #0d55cf; font-size: 20px"
                  />
                  <span class="play-text" style="">线下支付</span>
                </div>
              </div>
              <p class="left-tips">
                请将其他费用转账至下方收款账户，为了提升审批进度，请备注支付信息，方便平台工作人员进行审核～
              </p>
              <div class="management-accounts">
                <p class="accounts-p">
                  <span>收款账户</span>
                  <span>{{ item.redeemAccountVOS.bankCardNo }}</span>
                </p>
                <p class="accounts-p">
                  <span>收款公司名</span>
                  <span>{{ item.redeemAccountVOS.enterpriseName }}</span>
                </p>
                <p class="accounts-p">
                  <span>开户银行</span>
                  <span>{{ item.redeemAccountVOS.bankDeposit }}</span>
                </p>
                <p class="accounts-p">
                  <span>备注</span>
                  <span>企业名称+产品名称</span>
                </p>
              </div>
            </div>
            <div class="infometion-right">
              <UploadDragger
                @setfileList="fileList => setfileAttachList(item, fileList)"
                :arrData="item.fileList"
              />
            </div>
          </div>
          <div class="infometion-bottom-box">
            <div class="bottom-title">费用明细</div>
            <div class="table-box">
              <a-table
                :columns="offlineFeecolumns"
                :data-source="item.redeemAccountVOS.expenseOrderDetailList"
                :pagination="false"
                :loading="myLoading"
              >
                <template #summary v-if="!myLoading">
                  <a-table-summary-row>
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                      >总计</a-table-summary-cell
                    >
                    <a-table-summary-cell />
                    <a-table-summary-cell />
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: SFProDisplay-Semibold, SFProDisplay;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                    >
                      <a-typography-text>
                        ￥{{
                          Number(item.redeemAccountVOS.totalAmount).toFixed(2)
                        }}
                      </a-typography-text>
                    </a-table-summary-cell>
                  </a-table-summary-row>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹出网银的弹窗 -->
    <DialoginternetBankSelect
      ref="dialoginternetBankSelectRef"
      :financeNoV="redeemNo"
      :allMonry="FeeObj.allMoney"
      @changeCurrentIndex="changeCurrentIndex"
    />
  </div>
</template>

<script>
export default {
  name: 'otherExpenses',
}
</script>
<script setup>
import payMoney from './payMoney/payMoney'
import DialoginternetBankSelect from './components/Dialog/dialoginternetBankSelect.vue'
import LabelBar from './components/LabelBar.vue'
import PaymentQRCode from './components/paymentQRCode.vue'
import UploadDragger from '@/views/product/loanApplication/components/component/uploadDragger.vue'
import { NButton } from 'naive-ui'
import { ref } from 'vue'
// import { submitPurchaseLast } from '@/api/user/generat/index'
// const emit = defineEmits(['goStepSecond', 'confirm'])

// const bail = ref([])
// const platform = ref([])

const {
  Feedata,
  FeeObj,
  payType,
  redeemNo,
  onlinePay,
  labelData,
  // stateData,
  getPayType,
  feecolumns,
  payLoading,
  handleSwitch,
  // cancelPayment,
  handleSubmitBtn,
  setfileAttachList,
  offlineFeecolumns,
  changeCurrentIndex,
  dialoginternetBankSelectRef,
} = payMoney()
const myLoading = ref(false)

defineProps({
  selectProduct: {
    type: Object,
    default: () => {},
  },
  financeNo: {
    type: String,
    default: null,
  },
})

// 暴露校验方法
defineExpose({
  handleSubmitBtn,
})
</script>

<style lang="scss" scoped>
.other-expenses {
  width: 1400px;
  margin: 40px auto 0;
  margin-bottom: 60px;

  .main-content {
    .form-container {
      padding-bottom: 24px;

      .onLinePay {
        margin-top: 30px;
        background: #ffffff;
        box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
        border-radius: 16px;
        border: 1px solid #efefef;
        padding: 40px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        margin-bottom: 48px;

        .infometion-top-box {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          z-index: 1;

          .infometion-left {
            .left-title {
              height: 28px;
              font-size: 20px;
              font-weight: 600;
              color: #0a1f44;
              line-height: 28px;
            }
            .left-money {
              display: flex;
              align-items: center;

              .num {
                font-size: 50px;
                color: #031222;
                font-weight: 600;
              }
              .play-type {
                height: 40px;
                padding: 10px 20px;
                box-sizing: border-box;
                font-size: 14px;
                font-weight: 500;
                color: #ffffff;
                line-height: 20px;
                border-radius: 100px;
                display: flex;
                align-items: center;
                margin-left: 15px;

                .play-text {
                  margin-left: 4px;
                }
              }
            }
            .online-payment-box {
              margin-top: 20px;

              .pay-text {
                margin-bottom: 12px;
                font-size: 14px;
                font-weight: 500;
                color: #0a1f44;
              }
            }
          }

          .infometion-right {
            position: relative;
            width: 330px;
          }

          .infometion-obscuration {
            :deep(.ant-spin-container) {
              filter: blur(4px);
            }
          }
        }

        .infometion-bottom-box {
          overflow: hidden;
          margin-top: 40px;

          .bottom-title {
            height: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #0a1f44;
            line-height: 20px;
          }
          .table-box {
            margin-top: 12px;
            border: 1px solid #f1f2f4;
          }
        }

        &::before {
          content: '';
          display: inline-block;
          position: absolute;
          z-index: 0;
          width: 279px;
          height: 277px;
          top: -50px;
          right: -40px;
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.2;
          border-radius: 150px;
          filter: blur(49px);
        }
        &::after {
          content: '';
          display: inline-block;
          position: absolute;
          z-index: 0;
          width: 106px;
          height: 143px;
          top: -20px;
          left: 0px;
          border-radius: 150px;
          filter: blur(24px);
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.2;
        }
      }

      .infometion-box {
        background: #ffffff;
        box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
        border-radius: 16px;
        border: 1px solid #efefef;
        padding: 40px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .infometion-top-box {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;

          .infometion-left {
            .left-title {
              height: 28px;
              font-size: 20px;
              font-weight: 600;
              color: #0a1f44;
              line-height: 28px;
            }
            .left-money {
              display: flex;
              align-items: center;

              .num {
                font-size: 50px;
                color: #031222;
                font-weight: 600;
              }
              .play-type {
                height: 40px;
                background: #ebf5ff;
                padding: 10px 20px;
                box-sizing: border-box;
                font-size: 14px;
                font-weight: 500;
                color: #ffffff;
                line-height: 20px;
                border-radius: 100px;
                display: flex;
                align-items: center;
                margin-left: 15px;

                .play-text {
                  margin-left: 4px;
                  color: #0d55cf;
                }
              }
            }
            .left-tips {
              height: 20px;
              font-size: 14px;
              font-weight: 400;
              color: #8a94a6;
              line-height: 20px;
              margin-bottom: 24px;
            }
            .management-accounts {
              .accounts-p {
                margin-bottom: 32px;

                &:last-child {
                  margin-bottom: 0;
                }

                span {
                  display: inline-block;
                }

                & span:first-child {
                  width: 70px;
                  height: 20px;
                  font-size: 14px;
                  font-weight: 400;
                  color: #8a94a6;
                  line-height: 20px;
                  margin-right: 10px;
                }
                & span:last-child {
                  height: 20px;
                  font-size: 14px;
                  font-weight: 400;
                  color: #0a1f44;
                  line-height: 20px;
                }
              }
            }
          }

          .infometion-right {
            width: 394px;
          }
        }

        .infometion-bottom-box {
          overflow: hidden;
          margin-top: 40px;

          .bottom-title {
            height: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #0a1f44;
            line-height: 20px;
          }
          .table-box {
            margin-top: 12px;
            border: 1px solid #f1f2f4;
          }
        }

        &::before {
          content: '';
          display: inline-block;
          position: absolute;
          z-index: 0;
          width: 279px;
          height: 277px;
          top: -50px;
          right: -40px;
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.2;
          border-radius: 150px;
          filter: blur(49px);
        }
        &::after {
          content: '';
          display: inline-block;
          position: absolute;
          z-index: 0;
          width: 106px;
          height: 143px;
          top: -20px;
          left: 0px;
          border-radius: 150px;
          filter: blur(24px);
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.2;
        }
      }

      // 重置默认样式,我也是复制的
      :deep(.ant-table) {
        background: transparent;

        .ant-table-thead {
          .ant-table-cell {
            background-color: #f8f9fb;
          }
        }

        .ant-table-thead {
          .ant-table-cell {
            padding: 10px 24px;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #8a94a6;
            line-height: 20px;
          }
        }

        .ant-table-tbody {
          .ant-table-cell {
            padding: 10px 24px;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #0a1f44;
            line-height: 20px;
          }
        }

        .ant-table-summary {
          background-color: #f8f9fb;
        }
      }
    }
  }
}
</style>
