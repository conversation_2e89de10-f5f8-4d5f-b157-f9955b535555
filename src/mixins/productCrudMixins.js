/**
 * apiPath: api路径，接收 - @/api/路径
 * crudOption: crud的配置项
 */
export default (options = {}) => {
  const defaultSort = { updateTimeDesc: 'updateTimeDesc' }
  return {
    data() {
      return {
        // 表单内容
        form: {},
        // 列表数据
        data: [],
        // 选中数据
        selectionList: [],
        // 加载状态
        loading: false,
        // 追加额外参数
        query: {},
        // 搜索参数
        search: {},
        // 排序列，默认按照更新时间排序
        sortColumn: defaultSort,
        // 基础分页
        page: {
          currentPage: 1,
          pageSize: 10,
          total: 0,
        },
        // api对象
        api: require(`@/api/${options.apiPath}`),
        // option crud配置
        option: options.crudOption,
      }
    },
    computed: {
      // crud基础绑定属性
      bindVal() {
        return {
          ref: 'crud',
          option: this.option,
          data: this.data,
          tableLoading: this.loading,
          // search与page要加.sync修饰，因此在标签内写
          // page: this.page,
          // search: this.search
        }
      },
      // crud基础事件
      onEvent() {
        return {
          'on-load': this.onLoad,
          'row-save': this.rowSave,
          'row-update': this.rowUpdate,
          'row-del': this.rowDel,
          'selection-change': this.selectionChange,
          'refresh-change': this.refreshChange,
          'search-change': this.searchChange,
          'search-reset': this.searchReset,
          'sort-change': this.sortChange,
          // 没用到
          'date-change': this.dateChange,
        }
      },
    },
    methods: {
      /** 获取参数 */
      getQueryParams() {
        const { currentPage, pageSize } = this.page
        return {
          current: currentPage,
          size: pageSize,
          ...this.query,
          ...this.sortColumn,
          ...this.search,
        }
      },

      /**
       * 查询列表
       */
      async onLoad() {
        this.loading = true
        // 获取参数
        const params = this.getQueryParams()
        const { data: resData } = await this.api.list(params)
        const { code, data, msg } = resData
        const { total, records } = data

        this.loading = false

        // 是否成功
        if (code !== 200) {
          this.$message.error(msg)
          return
        }

        // 赋值
        this.page.total = total
        this.data = records

        // 清除选中
        if (this.selectionList.length > 0) this.selectionClear()
      },
      rowSave() {},
      rowUpdate() {},

      /**
       * 删除产品
       * @param row
       */
      rowDel(row) {
        const msg = '是否确定将选择数据删除?'
        const api = this.api.remove
        this.handleMsgApi({ msg, api, param: row.id })
      },

      /**
       * 选中监听，存储选中数据
       * @param {*} list
       */
      selectionChange(list) {
        this.selectionList = list
      },

      /**
       * 刷新
       */
      refreshChange() {
        this.onLoad()
      },

      /**
       * 搜索
       */
      async searchChange(_, done) {
        await this.onLoad()
        done()
      },

      /**
       * 重置
       */
      searchReset() {
        this.onLoad()
      },

      // 清空选中
      selectionClear() {
        this.selectionList = []
        this.$refs.crud.toggleSelection()
      },

      /**
       * 公共消息接口方法
       * @param msg 提示消息
       * @param api 接口
       * @param param 参数
       * @param isBatch 是否批量
       * @param calSucMsg 如果批量的话需要回调成功消息 - 定制
       */
      async handleMsgApi({ msg, api, param, calSucMsg }) {
        // 消息提示
        await this.$confirm(msg, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        // 调用接口
        const { data: resData } = await api(param)

        const sucMsg = calSucMsg ? calSucMsg(resData.data) : '操作成功!'

        // 刷新数据并成功提示
        this.onLoad()
        this.$message({ type: 'success', message: sucMsg })
      },

      /**
       * 批量 - 上架/下架
       */
      batchShelf(isOn) {
        // 变量声明
        const { batchOnShelf, batchOffShelf } = this.api
        const api = isOn ? batchOnShelf : batchOffShelf
        const msg = `确定将选择产品${isOn ? '上' : '下'}架吗`
        const ids = this.selectionList.map(item => item.id).join()
        const selectSize = this.selectionList.length
        const calSucMsg = num => {
          // 成功提示内容
          let sucMsg = isOn ? `成功上架${num}个产品` : '操作成功'
          const remLength = selectSize - num
          isOn && remLength > 0 && (sucMsg += `还有${remLength}个产品数据未填写完整`)
          return sucMsg
        }

        // 必须选择数据
        if (selectSize === 0) {
          this.$message.warning('请选择至少一条数据')
          return
        }

        // 消息接口方法
        this.handleMsgApi({ msg, api, param: ids, calSucMsg })
      },

      /**
       * 单个上下架
       * @param id
       * @param isOn
       */
      rowShelf(id, isOn) {
        // 变量声明
        const { onShelf, offShelf } = this.api
        const msg = `确定${isOn ? '上' : '下'}架该商品吗？`
        const api = isOn ? onShelf : offShelf

        // 消息接口方法
        this.handleMsgApi({ msg, api, param: id })
      },

      /** 排序 */
      sortChange(sort) {
        const { order, prop } = sort
        const sortName = `${prop}${order === 'ascending' ? 'Asc' : 'Desc'}`
        // 如果存在排序则获取排序的名称，否则默认排序
        this.sortColumn = order ? { [sortName]: sortName } : defaultSort
        this.onLoad()
      },

      // 没用到
      dateChange() {},
    },
  }
}
