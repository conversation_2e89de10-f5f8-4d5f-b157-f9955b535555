import Vue from 'vue'
import axios from './router/axios'
import VueAxios from 'vue-axios'
import App from './App'
import router from './router/router'
import './permission' // 权限
import './error' // 日志
import './cache' //页面缓存
import store from './store'
import { loadStyle } from './util/util'
import * as urls from '@/config/env'
import Element from 'element-ui'
import VForm from 'vform-builds' //引入VForm库
import 'vform-builds/dist/VFormDesigner.css' //引入VForm样式

import { iconfontUrl, iconfontVersion } from '@/config/env'
import i18n from './lang' // Internationalization
import './styles/common.scss'
import basicBlock from './components/basic-block/main'
import basicContainer from './components/basic-container/main'
import thirdRegister from './components/third-register/main'
import avueUeditor from 'avue-plugin-ueditor'
import website from '@/config/website'
import crudCommon from '@/mixins/crud'
import '@/icons/iconfont'
import '@/icons/iconfont_2502968/iconfont'
// 过滤器
import { formatMoney } from '@/util/filter'
import { startLoading, endLoading } from '@/util/loading'

import SvgIcon from '@/components/svg-icon/index.vue'
import filePreview from '@/components/file-preview/index.vue'
import {
  numJiaFun,
  numJianFun,
  numChengFun,
  numChuFun,
  numBidaFun,
  numBixiaoFun,
} from '@/util/bigNum'
// 虚拟列表UI组件
import UmyUi from 'umy-ui'
// 组织结构
// import {
//   VueOkrTree
// } from 'vue-okr-tree';
// import 'vue-okr-tree/dist/vue-okr-tree.css'
// console.log(VueOkrTree)
// Vue.use(VueOkrTree)
// 注册虚拟列表组件
Vue.use(UmyUi)
// 注册全局crud驱动
window.$crudCommon = crudCommon
// 注册全局过滤器
Vue.filter('formatMoney', formatMoney)
// 加载Vue拓展
Vue.use(router)
Vue.use(VueAxios, axios)

Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value),
})
Vue.use(VForm)  //全局注册VForm(同时注册了v-form-designe、v-form-render等组件)
/* 注意：如果你的项目中有使用axios，请用下面一行代码将全局axios复位为你的axios！！ */
window.axios = axios
Vue.use(window.AVUE, {
  size: 'small',
  tableSize: 'small',
  calcHeight: 65,
  i18n: (key, value) => i18n.t(key, value),
})
// 注册全局容器
Vue.component('basicContainer', basicContainer)
Vue.component('basicBlock', basicBlock)
Vue.component('thirdRegister', thirdRegister)
Vue.component('SvgIcon', SvgIcon)
Vue.component('FilePreview', filePreview)
Vue.use(avueUeditor)
// 加载相关url地址
Object.keys(urls).forEach(key => {
  Vue.prototype[key] = urls[key]
})
// 加载website
Vue.prototype.website = website
// 加载loading
Vue.prototype.startLoading = startLoading
// 关闭loading
Vue.prototype.endLoading = endLoading
// 注册数字加方法
Vue.prototype.$numJiaFun = numJiaFun
// 注册数字减方法
Vue.prototype.$numJianFun = numJianFun
// 注册数字乘方法
Vue.prototype.$numChengFun = numChengFun
// 注册数字除方法
Vue.prototype.$numChuFun = numChuFun
// 注册数字判断左比右大方法
Vue.prototype.$numBidaFun = numBidaFun
// 注册数字判断左比右小方法
Vue.prototype.$numBixiaoFun = numBixiaoFun

// 动态加载阿里云字体库
iconfontVersion.forEach(ele => {
  loadStyle(iconfontUrl.replace('$key', ele))
})

Vue.use(window.AvueFormDesign)
Vue.use(window.WfDesign.default, {
  i18n: (key, value) => i18n.t(key, value),
})

import WfCustomFields from './views/plugin/workflow/components'
Vue.use(WfCustomFields)

Vue.config.productionTip = false

new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
}).$mount('#app')
