import Layout from '@/page/index/'

export default [
  {
    path: '/wel',
    component: Layout,
    redirect: '/wel/index',
    children: [
      {
        path: 'index',
        name: '首页',
        meta: {
          i18n: 'dashboard',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/wel/index'),
      },
      {
        path: 'transaction',
        name: '交易详情',
        meta: {
          title: '交易详情',
          i18n: 'dashboard',
          meun: false,
        },
        component: () => import('@/views/wel/transaction.vue'),
      },
      {
        path: 'subIndex',
        name: '报表统计',
        meta: {
          i18n: 'dashboard',
          menu: false,
        },
        component: () => import('@/views/wel/subIndex.vue'),
      },
      {
        path: 'dashboard',
        name: '控制台',
        meta: {
          i18n: 'dashboard',
          menu: false,
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/wel/dashboard'),
      },
    ],
  },

  {
    path: '/test',
    component: Layout,
    redirect: '/test/index',
    children: [
      {
        path: 'index',
        name: '测试页',
        meta: {
          i18n: 'test',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/util/test'),
      },
    ],
  },
  {
    path: '/dict-horizontal',
    component: Layout,
    redirect: '/dict-horizontal/index',
    children: [
      {
        path: 'index',
        name: '字典管理',
        meta: {
          i18n: 'dict',
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/util/demo/dict-horizontal'
          ),
      },
    ],
  },
  {
    path: '/dict-vertical',
    component: Layout,
    redirect: '/dict-vertical/index',
    children: [
      {
        path: 'index',
        name: '字典管理',
        meta: {
          i18n: 'dict',
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/util/demo/dict-vertical'
          ),
      },
    ],
  },
  {
    path: '/info',
    component: Layout,
    redirect: '/info/index',
    children: [
      {
        path: 'index',
        name: '个人信息',
        meta: {
          i18n: 'info',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/system/userinfo'),
      },
    ],
  },
  // {
  //   path: '/work/process/leave',
  //   component: Layout,
  //   redirect: '/work/process/leave/form',
  //   children: [
  //     {
  //       path: 'form/:processDefinitionId',
  //       name: '请假流程',
  //       meta: {
  //         i18n: 'work',
  //       },
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "views" */
  //           '@/views/work/process/leave/form'
  //         ),
  //     },
  //     {
  //       path: 'handle/:taskId/:processInstanceId/:businessId',
  //       name: '处理请假流程',
  //       meta: {
  //         i18n: 'work',
  //       },
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "views" */
  //           '@/views/work/process/leave/handle'
  //         ),
  //     },
  //     {
  //       path: 'detail/:processInstanceId/:businessId',
  //       name: '请假流程详情',
  //       meta: {
  //         i18n: 'work',
  //       },
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "views" */
  //           '@/views/work/process/leave/detail'
  //         ),
  //     },
  //   ],
  // },
  {
    path: '/workflow',
    component: Layout,
    children: [
      {
        path: 'design/process/:id',
        name: '流程设计',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/design'
          ),
      },
      {
        path: 'design/model/history/:id',
        name: '模型历史',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/design/model-history'
          ),
      },
      {
        path: 'design/form/history/:id',
        name: '表单历史',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/design/form-history'
          ),
      },
      {
        path: 'process/start/:params',
        name: '新建流程',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/process/components/form'
          ),
      },
      {
        path: 'process/detail/:params',
        name: '流程详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/process/components/detail'
          ),
      },

      {
        path: '/riskmana/add',
        name: '授信管理',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/riskmana/add'
          ),
      },
      {
        path: '/contract/contractDetail/:id',
        name: '合同详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/contract/contractDetail'
          ),
      },
      {
        path: '/pcontrol/pinformation',
        name: '产品管理-代采融资',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/pcontrol/pinformation'
          ),
      },
      {
        path: '/pcontrol/pledgeMovables',
        name: '产品管理-动产质押',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/pcontrol/pledgeMovables'
          ),
      },
      {
        path: '/pcontrol/purchasing',
        name: '产品管理-应收账款',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/pcontrol/purchasing'
          ),
      },
      {
        path: '/pcontrol/orderFinancing',
        name: '产品管理-订单融资',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/pcontrol/orderFinancing'
          ),
      },
      {
        path: '/pcontrol/manyCapitalProductsDetails',
        name: '产品管理-多资方产品',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/pcontrol/manyCapitalProductsDetails'
          ),
      },
      {
        path: '/pcontrol/productgroupdetails',
        name: '产品组管理',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/pcontrol/product-group-details'
          ),
      },
      // 注释该代码，因为下面已经有了 loanmanageiouDetail/:id 的路由配置
      // {
      //   path: '/loan',
      //   component: Layout,
      //   children: [{
      //     path: 'loanmanageiouDetail/:id',
      //     name: '借据详情',
      //     component: () =>
      //       import(
      //         '@/views/loan/loanmanageiouDetail.vue'
      //       ),
      //   }, ],
      // },
      {
        path: '/finance/receivable/recDetail',
        name: '应收账款质押-订单详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/finance/recDetail.vue'
          ),
      },
      {
        path: '/finance/orderFinancingReceivable/orderFinancingRecDetail',
        name: '订单融资-订单详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/finance/orderFinancingRecDetail.vue'
          ),
      },
      {
        path: '/finance/receivable/advancerepayapply/:id',
        name: '提前结清申请',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/finance/advancerepayapply.vue'
          ),
      },
      {
        path: '/pcontrol_cloud/purchasing',
        name: '产品管理-云信',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/pcontrol_cloud/purchasing'
          ),
      },
      {
        path: '/riskmana/applicationForAdjustmentOfQuota',
        name: '额度管理',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/riskmana/applicationForAdjustmentOfQuota'
          ),
      },
      {
        path: '/riskmana/applicationForAdjustmentOfFinacingQuota',
        name: '额度管理',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/riskmana/applicationForAdjustmentOfFinacingQuota'
          ),
      },
      {
        // limitHistory
        path: '/riskmana/finlimithistory',
        name: '额度历史',
        component: () => import('@/views/riskmana/finlimitHistory'),
      },
      {
        // corelimitHistory
        path: '/riskmana/corelimitHistory',
        name: '额度历史',
        component: () => import('@/views/riskmana/corelimitHistory'),
      },
      {
        path: '/riskmana/adjustmentOfFinacingQuota',
        name: '调整额度',
        component: () => import('@/views/riskmana/adjustmentOfFinacingQuota'),
      },
      {
        path: '/riskmana/adjustmentOfQuota',
        name: '调整额度',
        component: () => import('@/views/riskmana/adjustmentOfQuota'),
      },
      {
        path: '/commodity/newProducts',
        name: '新增商品',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/commodity/newProducts'
          ),
      },
      {
        path: '/commodity/lookProducts/:id',
        name: '查看商品',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/commodity/lookProducts'
          ),
      },
      {
        path: '/commodity/editProducts/:id',
        name: '编辑商品',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/commodity/editProducts'
          ),
      },
      {
        path: '/commodity/commoditywhiteDetail',
        name: '商品白名单管理',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/commodity/commoditywhiteDetail.vue'
          ),
      },
      {
        path: '/redemption/warehousing',
        name: '入库',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/redemption/warehousing.vue'
          ),
      },
      {
        path: '/cashdeposit/cashdepositDetail',
        name: '保证金详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/cashdeposit/cashdepositDetail.vue'
          ),
      },
      {
        path: '/redeemcargo/toShipments',
        name: '发货登记',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/redeemcargo/toShipments.vue'
          ),
      },

      {
        path: '/redeemcargo/toShipmentsPledge',
        name: '动产质押发货登记',
        component: () => import('@/views/redeemcargo/toShipmentsPledge.vue'),
      },
      {
        path: '/loan/regulatingbreathing',
        name: '调息申请',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/process/external/Product/regulatingBreathing/approve'
          ),
      },
      {
        path: '/loan/debtRelief',
        name: '减免申请',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/process/external/Product/debtRelief/approve'
          ),
      },
      {
        path: '/loan/overdueNegotiationApply',
        name: '逾期协商',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/process/external/Product/overdueNegotiationApply/approve'
          ),
      },
      {
        path: '/loan/changeHistory',
        name: '历史变更',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/loan/changeHistory'
          ),
      },
      {
        path: 'process/product/externalform/addform',
        name: '流程设计-外置表单',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/plugin/workflow/design/addform'
          ),
      },
      {
        path: '/loan/agentpurchasechange/agentpurchaseDetail',
        name: '代采变更详情',
        component: () => import('@/views/loan/agentpurchaseDetail'),
      },
      {
        path: '/loan/cargoSolve/cargoSolveDetialInfo',
        name: '货物处置详情',
        component: () => import('@/views/loan/cargoSolveDetialInfo'),
      },
      {
        path: '/loan/cargoSolve/cargoSolveStart',
        name: '发起货物处置',
        component: () => import('@/views/loan/cargoSolveStart'),
      },
      {
        path: '/loan/cargoSolve/cargoSolveExamineApprove',
        name: '货物处置审批',
        component: () => import('@/views/loan/cargoSolveExamineApprove'),
      },
    ],
  },
  // 客户管理
  {
    path: '/customer',
    component: Layout,
    children: [
      {
        path: 'newWhitelist',
        name: '新增白名单',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/customer/newWhitelist'
          ),
      },
      {
        path: 'lookWhitelist',
        name: '查看白名单',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/customer/lookWhitelist'
          ),
      },
      {
        path: 'editWhitelist',
        name: '编辑白名单',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/customer/editWhitelist'
          ),
      },
      {
        path: 'archives/:type/:id',
        name: '档案信息',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/customer/archives'
          ),
        meta: {
          enablePathKey: true,
          keepAlive: true,
        },
      },
      {
        path: 'customerInfo/:type',
        name: '客户画像',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/customer/archives/customerInfo'
          ),
      },
      {
        path: 'archives/:type/:id/trade/detail/:position',
        name: '贸易明细',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/customer/tradeDetail/index.vue'
          ),
      },
      {
        path: 'capitalDetail',
        name: '资金方详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/customer/capitalDetail.vue'
          ),
      },
      // TODO：路由需要整改重构
      {
        path: '/riskmana/ratingrecord/detail/:params',
        name: '评分记录详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/riskmana/components/detail'
          ),
      },
    ],
  },
  //账单管理
  {
    path: '/bill',
    component: Layout,
    children: [
      {
        path: 'expenseOrderDetail/:id',
        name: '费用订单详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/bill/expenseorder/detail'
          ),
      },
      {
        path: 'reconciliationmerge/:batchId',
        name: '对账详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/bill/reconciliation/reconciliationmerge'
          ),
      },
      {
        path: 'invoiceTypeDetail/:id',
        name: '发票详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/bill/components/custom'
          ),
      },
      {
        path: 'invoiceType/billingDetail/:params',
        name: '开票明细',
        component: () => import('@/views/bill/components/billingDetail'),
      },
      // 退款列表
      {
        path: 'drawListDetail/:id',
        name: '退款详情',
        component: () => import('@/views/bill/components/drawListDetail.vue'),
      },
    ],
  },

  //账单管理
  {
    path: '/loan',
    component: Layout,
    children: [
      {
        path: 'loanmanageiouDetail/:id',
        name: '借据详情',
        component: () => import('@/views/loan/loanmanageiouDetail.vue'),
      },
      // 逾期列表详情
      {
        path: 'loanmanageoverdueDetail/:id',
        name: '逾期详情',
        component: () => import('@/views/loan/loanmanageoverdueDetail.vue'),
      },
    ],
  },
  // 产品管理--费用规则
  {
    path: '/expense',
    component: Layout,
    children: [
      {
        path: 'costRulesDetails/:params',
        name: '费用规则详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/expense/components/costRulesDetails'
          ),
      },
    ],
  },
  // {
  //   path: '/finance/receivable/recDetail',
  //   name: '应收账款质押-订单详情',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "views" */
  //       '@/views/finance/recDetail.vue'
  //     ),
  // },

  // 消息管理
  {
    path: '/msgManager',
    component: Layout,
    children: [
      {
        path: 'informMsgStencil',
        name: '通知类模板',
        component: () =>
          import(
            /* webpackChunkName: "views" */
            '@/views/message/template/informMsgStencil'
          ),
      },
      {
        path: 'warningMsgStencil',
        name: '预警类模板',
        component: () => import('@/views/msgManager/warningMsgStencil'),
      },
      {
        path: 'pollingMsgStencil',
        name: '推送类模板',
        component: () => import('@/views/msgManager/pollingMsgStencil'),
      },
    ],
  },
  // 云信--放款
  {
    path: '/cloud',
    component: Layout,
    children: [
      {
        path: 'cloudLoad/:params',
        name: '放款详情',
        component: () => import('@/views/cloud/cloudLoad.vue'),
      },
    ],
  },
  // 系统管理--机构管理
  // 系统管理--机构管理
  {
    path: '/system',
    component: Layout,
    children: [
      {
        path: 'cloudBillingLetter/:params',
        name: '云信开单',
        component: () => import('@/views/system/components/cloudBillingLetter'),
      },
      {
        path: 'cloudBillCashingDetail/:params',
        name: '云信兑付详情',
        component: () => import('@/views/system/components/cloudBillCashingDetail'),
      },
    ],
  },
  // 已开通产品--云信
  {
    path: '/product',
    component: Layout,
    children: [
      {
        path: 'cloudlist',
        name: '云信',
        component: () => import('@/views/product/cloudlist'),
      },
      {
        path: 'financing/:params',
        name: '云信融资详情',
        component: () => import('@/views/product/financing'),
      },
      {
        path: 'cloudAssetsDetail/:params',
        name: '云信资产详情',
        component: () => import('@/views/cloudassets/components/cloudAssetsDetail'),
      },
      {
        path: 'cloudTreeOrg',
        name: '云信轨迹图',
        component: () => import('@/views/cloud/cloudTreeOrg'),
      },
      {
        path: 'miningOrderDetail',
        name: '代采融资详情',
        component: () => import('@/views/product/mining/miningOrderDetail'),
      },
    ],
  },
  {
    path: '/redeem',
    component: Layout,
    children: [
      {
        path: 'detail/:id/:redeemNo',
        name: '赎货订单详情',
        component: () => import('@/views/redeemcargo/components/detail/index.vue'),
      },
      {
        path: 'pledgeDetail/:id/:redeemNo',
        name: '赎货订单详情',
        component: () => import('@/views/redeemcargo/components/detailPledge/index.vue'),
      },
    ],
  },

  // 商品价格维护表详情
  {
    path: '/commodity',
    component: Layout,
    children: [
      {
        path: 'priceMaintainList/priceMaintainDetail/:id',
        name: '商品价格维护详情',
        component: () => import('@/views/commodity/components/priceMaintainDetail.vue'),
      },
    ],
  },

  // 产品管理--预警规则
  {
    path: '/message',
    component: Layout,
    children: [
      {
        path: 'messagetemplaterule/warnRulesDispose',
        name: '预警规则配置',
        component: () => import('@/views/message/components/warnRulesDispose.vue'),
      },
    ],
  },

  // 区块链管理
  {
    path: '/blockchain',
    component: Layout,
    children: [
      {
        path: 'information/blockChainTrackDetail',
        name: '上链轨迹图',
        component: () => import('@/views/blockchain/blockChainTrackDetail.vue'),
      },
    ],
  },
  {
    path: '/product',
    component: Layout,
    children: [
      {
        path: 'miningOrderPledgeDetail',
        name: '订单详情',
        component: () => import('@/views/product/mining/miningOrderPledgeDetail.vue'),
      },
    ],
  },
]
