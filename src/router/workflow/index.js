import Layout from '@/page/index/'
import viewContainer from '@/views/plugin/workflow/process/components/viewContainer'

export default [
  {
    path: '/work',
    component: Layout,
    children: [
      // 查看页面
      {
        path: 'mywork',
        component: viewContainer,
        children: [
          // 内置表单查看流程详情
          {
            path: 'process/detail/:params',
            name: '流程详情',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/components/detail'
              ),
          },
          // 贸易背景
          {
            path: 'process/trade/approve/:params',
            name: '审批贸易背景',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Trade/approve'
              ),
          },
          {
            path: 'process/trade/detail/:params',
            name: '查看贸易背景',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Trade/detail'
              ),
          },
          // 应收账款确权
          {
            path: 'process/receivableConfirm/approve/:params',
            name: '审批应收账款确权',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/ReceivableConfirm/approve'
              ),
          },
          {
            path: 'process/receivableConfirm/detail/:params',
            name: '查看应收账款确权',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/ReceivableConfirm/detail'
              ),
          },
          // 产品确认
          {
            path: 'process/product/productConfirmation/approve/:params',
            name: '审批产品确认',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/productConfirmation/approve'
              ),
          },
          {
            path: 'process/product/productConfirmation/detail/:params',
            name: '查看产品确认',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/productConfirmation/detail'
              ),
          },
          //
          {
            path: 'process/product/thawApplication/approve/:params',
            name: '审批冻结申请-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/thawApplication/approve'
              ),
          },
          {
            path: 'process/product/thawApplication/detail/:params',
            name: '查看冻结申请-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/thawApplication/detail'
              ),
          },
          {
            path: 'process/product/creditlimitfinancing/approve/:params',
            name: '审批申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/creditLimitFinancing/approve'
              ),
          },
          {
            path: 'process/product/creditlimitfinancing/detail/:params',
            name: '查看申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/creditLimitFinancing/detail'
              ),
          },
          {
            path: 'process/product/creditlimitfinancingAI/approve/:params',
            name: '审批申请额度-融资客户-AI',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/creditLimitFinancingAI/approve'
              ),
          },
          {
            path: 'process/product/creditlimitfinancingAI/detail/:params',
            name: '查看申请额度-融资客户-AI',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/creditLimitFinancingAI/detail'
              ),
          },
          {
            path: 'process/product/productgrouprouting/approve/:params',
            name: '审批产品组路由申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/productGroupRouting/approve'
              ),
          },
          {
            path: 'process/product/productgrouprouting/detail/:params',
            name: '查看产品组路由申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/productGroupRouting/detail'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/quotaapplication/approve/:params',
            name: '审批动产质押申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/quotaApplication/approve'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/quotaapplication/detail/:params',
            name: '查看动产质押申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/quotaApplication/detail'
              ),
          },
          {
            path: 'process/product/coreenterprisecreditlimit/approve/:params',
            name: '审批申请额度-核心客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/coreEnterpriseCreditLimit/approve'
              ),
          },
          {
            path: 'process/product/coreenterprisecreditlimit/detail/:params',
            name: '查看申请额度-核心客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/coreEnterpriseCreditLimit/detail'
              ),
          },
          {
            path: 'process/product/financingFlagenterprisecreditlimit/approve/:params',
            name: '审批平台申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/financingFlagEnterpriseCreditLimit/approve'
              ),
          },
          {
            path: 'process/product/financingFlagenterprisecreditlimit/detail/:params',
            name: '查看平台申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/financingFlagEnterpriseCreditLimit/detail'
              ),
          },
          {
            path: 'process/product/amountOfActivated/approve/:params',
            name: '审批额度激活',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/amountOfActivated/approve'
              ),
          },
          {
            path: 'process/product/amountOfActivated/detail/:params',
            name: '查看额度激活',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/amountOfActivated/detail'
              ),
          },
          {
            path: 'process/product/applicationsFor/approve/:params',
            name: '审批融资申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/applicationsFor/approve'
              ),
          },
          {
            path: 'process/product/applicationsFor/detail/:params',
            name: '查看融资申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/applicationsFor/detail'
              ),
          },
          {
            path: 'process/product/dingdanrongzhi-bigb/applicationsFor/approve/:params',
            name: '审批订单融资-融资申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/dingdanrongzhi-bigb/applicationsFor/approve'
              ),
          },
          {
            path: 'process/product/dingdanrongzhi-bigb/applicationsFor/detail/:params',
            name: '查看订单融资-融资申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/dingdanrongzhi-bigb/applicationsFor/detail'
              ),
          },
          {
            path: 'process/product/applicationsForVoluntarily/approve/:params',
            name: '审批融资自动放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/applicationsForVoluntarily/approve'
              ),
          },
          {
            path: 'process/product/applicationsForVoluntarily/detail/:params',
            name: '查看融资自动放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/applicationsForVoluntarily/detail'
              ),
          },
          {
            path: 'process/product/dingdanrongzhi-bigb/applicationsForVoluntarily/approve/:params',
            name: '审批订单融资-融资自动放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/dingdanrongzhi-bigb/applicationsForVoluntarily/approve'
              ),
          },
          {
            path: 'process/product/dingdanrongzhi-bigb/applicationsForVoluntarily/detail/:params',
            name: '查看订单融资-融资自动放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/dingdanrongzhi-bigb/applicationsForVoluntarily/detail'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/financingapplication/approve/:params',
            name: '审批动产质押申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/financingApplication/approve'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/financingapplication/detail/:params',
            name: '查看动产质押申请额度-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/financingApplication/detail'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/loanmovableproperty/approve/:params',
            name: '审批动产质押放款申请-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/loanMovableProperty/approve'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/loanmovableproperty/detail/:params',
            name: '查看动产质押放款申请-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/loanMovableProperty/detail'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/redeemapplypledgemovables/approve/:params',
            name: '审批动产质押赎货申请-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/redeemApplyPledgeMovables/approve'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/redeemapplypledgemovables/detail/:params',
            name: '查看动产质押赎货申请-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/redeemApplyPledgeMovables/detail'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/redeemconfirmpledegmovables/approve/:params',
            name: '审批动产质押赎货确认-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/redeemConfirmPledegMovables/approve'
              ),
          },
          {
            path: 'process/product/chattelmortgagep/redeemconfirmpledegmovables/detail/:params',
            name: '查看动产质押赎货确认-融资客户',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/chattelMortgageP/redeemConfirmPledegMovables/detail'
              ),
          },
          {
            path: 'process/goodsApplications/approve/:params',
            name: '审批查看货物处置',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/goodsApplications/approve'
              ),
          },
          {
            path: 'process/goodsApplications/detail/:params',
            name: '查看货物处置',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/goodsApplications/detail'
              ),
          },
          // {
          //   path: 'process/product/externalform/addform',
          //   name: '流程设计-外置表单',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "views" */
          //       '@/views/plugin/workflow/design/addform'
          //     ),
          // },
          // 未知
          // {
          //   path: 'process/product/cloudBillingLetter',
          //   name: '流程设计-外置表单',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "views" */
          //       '@/views/plugin/workflow/design/addform'
          //     ),
          // },
          {
            path: 'process/product/applyCloudBillingLetter/detail/:params',
            name: '查看云信开单',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/applyCloudBillingLetter/detail'
              ),
          },
          {
            path: 'process/product/applyCloudBillingLetter/approve/:params',
            name: '审批云信开单',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/applyCloudBillingLetter/approve'
              ),
          },
          {
            path: 'process/product/cloudFinancingApplication/detail/:params',
            name: '查看云信融资',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/cloudFinancingApplication/detail'
              ),
          },
          {
            path: 'process/product/cloudFinancingApplication/approve/:params',
            name: '审批云信融资',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/cloudFinancingApplication/approve'
              ),
          },
          {
            path: 'process/product/loanApplication/approve/:params',
            name: '审批放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/loanApplication/approve'
              ),
          },
          {
            path: 'process/product/loanApplication/detail/:params',
            name: '查看放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/loanApplication/detail'
              ),
          },
          {
            path: 'process/product/dingdanrongzhi-bigb/loanApplication/approve/:params',
            name: '审批订单融资-放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/dingdanrongzhi-bigb/loanApplication/approve'
              ),
          },
          {
            path: 'process/product/dingdanrongzhi-bigb/loanApplication/detail/:params',
            name: '查看订单融资-放款申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/dingdanrongzhi-bigb/loanApplication/detail'
              ),
          },
          {
            path: 'process/product/regulatingBreathing/approve/:params',
            name: '审批调息申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/regulatingBreathing/approve'
              ),
          },
          {
            path: 'process/product/regulatingBreathing/detail/:params',
            name: '查看调息申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/regulatingBreathing/detail'
              ),
          },
          {
            path: 'process/product/debtRelief/approve/:params',
            name: '审批减免申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/debtRelief/approve'
              ),
          },
          {
            path: 'process/product/debtRelief/detail/:params',
            name: '查看减免申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/debtRelief/detail'
              ),
          },

          {
            path: 'process/product/purchaseFinancing/detail/:params',
            name: '查看代采融资申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/purchaseFinancing/detail'
              ),
          },
          {
            path: 'process/product/purchaseFinancing/approve/:params',
            name: '审批代采融资申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/purchaseFinancing/approve'
              ),
          },

          // 代采确认审批
          {
            path: 'process/product/purchaseSubmit/detail/:params',
            name: '查看代采确认审批',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/purchaseSubmit/detail'
              ),
          },
          {
            path: 'process/product/purchaseSubmit/approve/:params',
            name: '代采确认审批',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/purchaseSubmit/approve'
              ),
          },
          // 核心企业入驻审批
          {
            path: 'process/coreEnterpriseAccess/approve/:params',
            name: '审批核心企业入驻',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/coreEnterpriseAccess/approve'
              ),
          },
          {
            path: 'process/coreEnterpriseAccess/detail/:params',
            name: '查看核心企业入驻',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/coreEnterpriseAccess/detail'
              ),
          },
          // 赎货申请
          {
            path: 'process/RedeemApply/approve/:params',
            name: '审批赎货申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/RedeemApply/approve'
              ),
          },
          {
            path: 'process/RedeemApply/detail/:params',
            name: '查看赎货申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/RedeemApply/detail'
              ),
          },
          // 赎货确认
          {
            path: 'process/RedeemConfirm/approve/:params',
            name: '审批赎货确认',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/RedeemConfirm/approve'
              ),
          },
          {
            path: 'process/RedeemConfirm/detail/:params',
            name: '查看赎货确认',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/RedeemConfirm/detail'
              ),
          },

          // 云信放款
          {
            path: 'process/CloudLoanConfirm/approve/:params',
            name: '审批云信放款',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/CloudLoanConfirm/approve'
              ),
          },
          {
            path: 'process/CloudLoanConfirm/detail/:params',
            name: '查看云信放款',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/CloudLoanConfirm/detail'
              ),
          },
          // 展期申请
          {
            path: 'process/RenewalApply/approve/:params',
            name: '审批展期申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/renewalApply/approve'
              ),
          },
          {
            path: 'process/RenewalApply/detail/:params',
            name: '查看展期申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/renewalApply/detail'
              ),
          },
          // 展期确认
          {
            path: 'process/RenewalConfirm/approve/:params',
            name: '审批展期确认',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/renewalConfirm/approve'
              ),
          },
          {
            path: 'process/RenewalConfirm/detail/:params',
            name: '查看展期确认',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/renewalConfirm/detail'
              ),
          },

          {
            path: 'process/product/overdueConsultationDisposable/detail/:params',
            name: '查看逾期协商',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/overdueConsultationDisposable/detail'
              ),
          },
          {
            path: 'process/product/overdueConsultationDisposable/approve/:params',
            name: '审批逾期协商申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/overdueConsultationDisposable/approve'
              ),
          },
          {
            path: 'process/coreEnpriseAdjustment/approve/:params',
            name: '审批核心额度调整',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/coreEnpriseAdjustment/approve'
              ),
          },
          {
            path: 'process/coreEnpriseAdjustment/detail/:params',
            name: '查看核心额度调整',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/coreEnpriseAdjustment/detail'
              ),
          },
          {
            path: 'process/financingFlagEnterpriseAdjustment/approve/:params',
            name: '查看核心额度调整',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/financingFlagEnterpriseAdjustment/approve'
              ),
          },
          {
            path: 'process/financingFlagEnterpriseAdjustment/detail/:params',
            name: '查看核心额度调整',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/financingFlagEnterpriseAdjustment/detail'
              ),
          },
          {
            path: 'process/advancerepayapply/approve/:params',
            name: '审批提前结清申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/advancerepayapply/approve'
              ),
          },
          {
            path: 'process/advancerepayapply/detail/:params',
            name: '查看提前结清申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/advancerepayapply/detail'
              ),
          },
          {
            path: 'process/purchaseRefund/approve/:params',
            name: '审批代采变更申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/purchaseRefund/approve'
              ),
          },
          {
            path: 'process/purchaseRefund/detail/:params',
            name: '查看代采变更申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/purchaseRefund/detail'
              ),
          },
          {
            path: 'process/product/applyConfirmMulti/approve/:params',
            name: '审批代采变更申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/applyConfirmMulti/approve'
              ),
          },
          {
            path: 'process/product/applyConfirmMulti/detail/:params',
            name: '查看代采变更申请',
            component: () =>
              import(
                /* webpackChunkName: "views" */
                '@/views/plugin/workflow/process/external/Product/applyConfirmMulti/detail'
              ),
          },
        ],
      },
    ],
  },
]
