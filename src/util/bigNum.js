import BigNumber from 'bignumber.js'

// DECIMAL_PLACES: 保留两位小数
BigNumber.config({ DECIMAL_PLACES: 2 })
// toFormat 输出为千分位值
// toString 输出为字符串值
// toNumber 输出为数字值

// 封装加法函数
function numJiaFun(a, b) {
  const result = new BigNumber(a).plus(b)
  return result.toString()
}

// 封装减法函数
function numJianFun(a, b) {
  const result = new BigNumber(a).minus(b)
  return result.toString()
}

// 封装乘法函数
function numChengFun(a, b) {
  const result = new BigNumber(a).times(b)
  return result.toString()
}

// 封装除法函数
function numChuFun(a, b, c) {
  let result
  if (c) {
    const cB = BigNumber.clone({ DECIMAL_PLACES: c })
    result = new cB(a).dividedBy(b)
    return result.toString()
  }
  result = new BigNumber(a).dividedBy(b)
  return result.toString()
}

// 封装左边是否比右边大的判断函数
function numBidaFun(a, b, c) {
  let result
  if (c) {
    result = new BigNumber(a).isGreaterThanOrEqualTo(b) // 大于等于
  } else {
    result = new BigNumber(a).isGreaterThan(b) // 大于
  }
  return result
}

// 封装左边是否比右边小的判断函数
function numBixiaoFun(a, b, c) {
  let result
  if (c) {
    result = new BigNumber(a).isLessThanOrEqualTo(b) // 小于等于
  } else {
    result = new BigNumber(a).isLessThan(b) // 小于
  }
  return result
}
export {
  numJiaFun,
  numJianFun,
  numChengFun,
  numChuFun,
  numBidaFun,
  numBixiaoFun,
}
