import { getToken } from "./auth";
import website from '@/config/website';

export const downloadXHR = (contractId, getToken, contractTitle) => {
    const xhr = new XMLHttpRequest()
    const apiUrl = `/api/blade-contract/web-back/contractlist/contractDownload?contractId=${contractId}` // 这里填写后端提供的 API 地址
    const token = getToken()

    xhr.open('POST', apiUrl, true)
    xhr.responseType = 'blob'

    xhr.setRequestHeader('Blade-Auth', `Bearer ${token}`)
    // JSON 参数
    // xhr.setRequestHeader('Content-Type', 'application/json')

    xhr.onload = () => {
        if (xhr.status === 200) {
            const blob = xhr.response
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', `${contractTitle}.pdf`) // 设置下载文件名
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) // 清理
            window.URL.revokeObjectURL(url) // 释放 URL 对象
        }
    }

    xhr.onerror = () => {
        this.$message.error("加载失败，请稍后再试")
    }

    // JSON 参数
    // const requestBody = JSON.stringify({ contractId })

    xhr.send()
}

/**
 * 页面导出
 * @param {*} baseUrl 基础url
 * @param {*} params 筛选条件
 *
 * 示例：pageExport('/blade-contract/web-back/contractlist/export', { contractName: '测试' })
**/
export const pageExport = (baseUrl, params) => {
    const searchParams = new URLSearchParams({
        [website.tokenHeader]: getToken()
    });

    // 筛选条件
    for(const key in params) {
        if (params[key]) {
            searchParams.append(key, params[key]);
        }
    }

    const url = `${baseUrl}?${searchParams.toString()}`;
    window.open(url);
}
