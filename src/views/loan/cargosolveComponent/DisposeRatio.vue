<template>
  <!-- 处置比例 -->
  <basic-container>
    <div class="cargo">
      <div class="cargo-to-apply-for-product">
        <h1 class="cargo-to-apply-for-product-h1">
          <span>处置比例</span>
        </h1>
      </div>
    </div>

    <div class="table-top refund">

      <el-table ref="table4" :data="dataInfo.cargoSolveExpenseList || dataInfo.cargoSolveExpenses" style="width: 100%; margin-top: 13px"
        class="table-border-style">
        <el-table-column prop="name" label="费用名称"></el-table-column>
        <el-table-column prop="payable" label="应付(元)"></el-table-column>
        <el-table-column prop="paymentRatio" label="分账比例" :formatter="formatterPaymentRatio"></el-table-column>
        <el-table-column prop="actualPayment" label="分账金额(元)"></el-table-column>
      </el-table>
      <div class="footer">
        <div class="l annex"></div>
        <div class="right">
          <div class="sp-text">处置总额 = 处置金额 + 保证金</div>
          处置总额: <span class="am">{{ dataInfo.cargoSolveTotal || 0 }}</span>元
        </div>
      </div>
    </div>
  </basic-container>
</template>

<script>
import { formatMoney } from '@/util/filter.js'

export default {
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
    }
  },
  methods: {
    // 待赎信息统计
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
  }
}

</script>

<style lang="scss" scoped>
.cargo {
  .cargo-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .cargo-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .space {
        display: inline-block;
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: #d7d7d7;
        text-align: center;
        margin: 0 8px;
      }

      .sub-title {
        line-height: 22px;
        color: #7d7d7d;
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;

        .code {
          color: #697cff;
        }
      }

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        &>span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        &>span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .cargo-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .cargo-to-apply-for-product-left-logo-box {
        margin-top: 17px;
        display: flex;
        align-items: center;

        .cargo-to-apply-for-product-left-logo-box-img {
          width: 48px;
          height: 48px;
          border-radius: 40px;
          border: 1px solid rgba(234, 234, 234, 100);
          overflow: hidden;
          margin-right: 7px;

          &>img {
            width: 100%;
            object-fit: cover;
          }
        }

        .cargo-to-apply-for-product-left-logo-box-goodname {
          display: flex;
          flex-direction: column;

          &>span:first-child {
            display: block;
            height: 24px;
            color: rgba(18, 119, 255, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
          }

          &>span:last-child {
            display: block;
            height: 18px;
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }

      .cargo-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .cargo-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover>td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }

    .fromLeft-title-user {
      line-height: 22px;
      color: #7d7d7d;
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .fromLeft-title-name {
      color: #697cff !important;
      line-height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}


.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      min-width: 135px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }

}

.descriptions-for-box2 {
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 200px;
      min-width: 200px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      // width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}


.footer {
  padding-top: 15px;
  display: flex;
  justify-content: space-between;

  .r {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;

    .am {
      padding-left: 5px;
      font-size: 22px;
    }
  }

  .right {

    .sp-text {
      color: #0087ff;
      font-weight: normal;
      padding-bottom: 5px;
    }

    font-size: 16px;
    font-weight: 600;

    .am {
      padding-left: 5px;
      font-size: 22px;
    }
  }
}

.repayment-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 5px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.confirm-form-wrap {
  .formula-wrap {
    padding: 20px 40px;
    color: #aaa;
  }
}

.pt20 {
  padding-top: 20px;
}



.header {
  .pic-wrap {
    .pic-img {
      width: 48px;
      height: 48px;
      border-radius: 40px;
      border: 1px solid #eaeaea;
      overflow: hidden;
      margin-right: 7px;
    }
  }

  .info {
    .name {
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.align-center {
  display: flex;
  align-items: center;
}

.annex {
  .fj-label {
    color: #7d7d7d;
    font-size: 14px;
  }

  .fj-item {
    a {
      color: #3894ff;
      /* line-height: 36px; */
      /* padding-top: 10px; */
      display: inline-block;
      background: #f8f8f8;
      width: 200px;
      border: 1px solid #ededed;
      font-size: 14px;
      /* line-break: anywhere; */
      line-height: 26px;
      padding-left: 10px;
      ;
    }
  }
}
</style>
