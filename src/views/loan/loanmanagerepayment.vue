<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row }" slot="status">
        <el-link :underline="false" type="info" v-if="row.status === 1">未支付</el-link>
        <el-link :underline="false" type="primary" v-else-if="row.status === 2">支付中</el-link>
        <el-link :underline="false" type="success" v-else-if="row.status === 3">已支付</el-link>
        <el-link :underline="false" type="danger" v-else-if="row.status === 4">支付失败</el-link>
        <el-link :underline="false" type="info" v-else-if="row.status === 5">已重提</el-link>
        <el-link :underline="false" type="info" v-else-if="row.status === 6">已撤销</el-link>
        <el-link :underline="false" type="info" v-else-if="row.status === 7">已失效</el-link>
      </template>
      <template slot="menu" slot-scope="{ row }">
        <el-button type="text" @click="viewVoucher(row)">明细</el-button>
        <el-button type="text" v-if="row.status === 2 && row.payMode != 2 && row.goodsType != 2" @click="openDialog(row)">
          修改状态
        </el-button>
      </template>
      <!-- <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="repaymentStatus" @change="handleTabButton">
            <el-radio-button :key="0" :label="0">全部</el-radio-button>
            <el-radio-button
              v-for="(item, key) in statusMap"
              :key="key"
              :label="key"
              >{{ item }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </template> -->
    </avue-crud>
    <el-dialog title="明细" append-to-body width="35%" :visible.sync="statusBox">
      <div class="detail-box">
        <div class="children-box" v-for="item in detailArr" :key="item.id">
          <span class="laber-box">{{ item.label }}</span>
          <span
            class="value-box"
            :style="[
              { color: item.id === '6' ? 'red' : '#00072A' },
              { 'font-weight': ['9', '10'].includes(item.id) ? '' : 'bold' },
            ]"
            v-if="!item.img"
            >{{ item.prop }}</span
          >
          <el-upload
            v-else-if="item.prop.length"
            ref="upload"
            class="file-upload"
            action="/api/blade-resource/oss/endpoint/put-file-kv"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :file-list="item.prop"
            :disabled="true"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i
          ></el-upload>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="statusBox = false">取 消</el-button>
      </template>
    </el-dialog>
    <el-dialog title="修改状态" append-to-body width="35%" :before-close="closeDialog" :visible.sync="statusBox1">
      <div class="my-el-dialog">
        <avue-form ref="form" v-model="statusForm" :option="statusOption" :upload-error="uploadError">
          <template slot-scope="{ value }" slot="moneyBox">
            <div class="money-box" v-if="value.moneyBox">
              <div class="chlidern-money">
                <span class="label-box">应还本金(元)：</span>
                <span class="value-box">{{ value.moneyBox.principal }}</span>
              </div>
              <div class="chlidern-money">
                <span class="label-box">应还利息(元)：</span>
                <span class="value-box">{{ value.moneyBox.interest }}</span>
              </div>
              <!-- <div class="chlidern-money">
                <span class="label-box">手续费(元)：</span>
                <span class="value-box">{{
                  value.moneyBox.serviceCharge
                }}</span>
              </div> -->
              <!-- <div class="chlidern-money">
                <span class="label-box">逾期利息(元)：</span>
                <span class="value-box">{{
                  value.moneyBox.penaltyInterest
                }}</span>
              </div> -->
              <div class="chlidern-money" v-for="item in value.moneyBox.repaymentFeeList" :key="item.id">
                <span class="label-box">{{ item.feeName }}(元)：</span>
                <span class="value-box" style="color: red">￥{{ item.shouldAmount }}</span>
              </div>
              <div class="chlidern-money">
                <span class="label-box">应还总额(元)：</span>
                <span class="value-box" style="color: red">{{ value.moneyBox.totalPrices }}</span>
              </div>
            </div>
          </template>
        </avue-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="statusBox1 = false">取 消</el-button>
        <el-button type="primary" @click="submit()" :loading="submitLoading">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove, updateStatus } from '@/api/loan/loanmanagerepayment'
import { getByGoodsIdAndUserId } from '@/api/goods/pcontrol/workflow/productConfirmation'
// import { getDictionary } from '@/api/system/dictbiz'
import { ids } from '@/api/resource/attach'
import { mapGetters } from 'vuex'
import { formatMoney } from '@/util/filter'
import dayjs from 'dayjs'
var DIC = {
  pay: [
    {
      label: '线下支付',
      value: 1,
    },
    {
      label: '线上支付',
      value: 2,
    },
  ],
}

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      repaymentStatus: 0,
      statusBox: false,
      statusBox1: false,
      selectionList: [],
      statusMap: {},
      statusForm: {
        voucher: [],
      },
      statusOption: {
        detail: false,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: '110',
        column: [
          {
            label: '状态类型',
            placeholder: '请选择状态类型',
            prop: 'status',
            span: 21,
            type: 'select',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=loan_repayment_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            row: true,
            rules: [
              {
                required: true,
                message: '请选择状态',
                trigger: 'blur',
              },
            ],
            control: val => {
              if (val === 3) {
                return {
                  moneyBox: {
                    display: true,
                  },
                  actualAmount: {
                    display: true,
                  },
                  bank: {
                    display: true,
                  },
                  bankCardNo: {
                    display: true,
                  },
                  repaymentTime: {
                    display: true,
                  },
                  voucherUrl: {
                    display: true,
                  },
                  failReason: {
                    display: false,
                  },
                }
              } else if (val === 4) {
                return {
                  moneyBox: {
                    display: false,
                  },
                  actualAmount: {
                    display: false,
                  },
                  bank: {
                    display: false,
                  },
                  bankCardNo: {
                    display: false,
                  },
                  repaymentTime: {
                    display: false,
                  },
                  voucherUrl: {
                    display: false,
                  },
                  failReason: {
                    display: true,
                  },
                }
              } else {
                return {
                  moneyBox: {
                    display: false,
                  },
                  actualAmount: {
                    display: false,
                  },
                  bank: {
                    display: false,
                  },
                  bankCardNo: {
                    display: false,
                  },
                  repaymentTime: {
                    display: false,
                  },
                  voucherUrl: {
                    display: false,
                  },
                  failReason: {
                    display: false,
                  },
                }
              }
            },
          },
          {
            label: '',
            prop: 'moneyBox',
            display: false,
            row: true,
          },
          {
            label: '实还金额',
            prop: 'actualAmount',
            span: 21,
            append: '元',
            placeholder: '请输入实还金额',
            display: false,
            row: true,
            rules: [
              {
                required: true,
                message: '请输入还款金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '付款开户行',
            prop: 'bank',
            span: 21,
            placeholder: '请输入付款开户行',
            display: false,
            row: true,
            rules: [
              {
                required: true,
                message: '请输入付款开户行',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '付款账号',
            prop: 'bankCardNo',
            span: 21,
            placeholder: '请输入付款账号',
            display: false,
            row: true,
            rules: [
              {
                required: true,
                message: '请输入付款账号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '还款时间',
            prop: 'repaymentTime',
            type: 'datetime',
            span: 21,
            placeholder: '请选择还款时间',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            disabled: false,
            display: false,
            rules: [
              {
                required: true,
                message: '请选择还款时间',
                trigger: 'change',
              },
            ],
            pickerOptions: {
              disabledDate: time => {
                // 修复禅道问题 #4628，如果拿了当前时间戳会使此刻的时间不能选择，因此获取23：59：59的时间戳
                return time.getTime() >= dayjs(dayjs().format('YYYY-MM-DD 23:59:59')).valueOf()
              },
            },
          },
          {
            label: '还款凭证',
            prop: 'voucherUrl',
            type: 'upload',
            listType: 'picture-card',
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            accept: '.jpg,.png,.jpeg',
            row: true,
            span: 24,
            display: false,
            fileSize: 5 * 1024,
            width: 200,
            tip: '只能上传jpg/png/jpeg文件，且不超过5M',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            uploadAfter: (res, done) => {
              this.statusForm.voucher.push({ id: res.attachId, name: res.name })
              done()
            },
            onRemove: file => {
              for (const item of this.statusForm.voucher) {
                if (item.name === file.name) {
                  this.statusForm.voucher.splice(this.statusForm.voucher.indexOf(item), 1)
                }
              }
            },
            rules: [
              {
                required: true,
                message: '请上传还款凭证',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '失败原因',
            prop: 'failReason',
            type: 'textarea',
            display: false,
            span: 21,
            rules: [
              {
                required: true,
                message: '请输入失败原因',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        addBtn: false,
        menu: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        headerAlign: 'center',
        align: 'center',
        column: [
          {
            label: '还款时间',
            prop: 'rangeTime',
            type: 'daterange',
            dataType: 'array',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            searchRange: true,
            search: true,
            hide: true,
            display: false,
          },
          {
            label: '借据号',
            prop: 'iouNoEqual',
            hide: true,
            search: true,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入借据号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资用户',
            prop: 'userIdEqual',
            type: 'select',
            hide: true,
            search: true,
            dicUrl: '/api/blade-user/user-all?userType=6',
            dicMethod: 'get',
            display: false,
            props: {
              value: 'id',
              label: 'name',
            },
          },
          {
            label: '还款类型',
            prop: 'repaymentTypeEqual',
            search: true,
            hide: true,
            type: 'select',
            dataType: 'number',
            display: false,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=loan_repayment_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '还款单号',
            prop: 'repaymentNo',
            search: true,
            display: false,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入还款单号',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '线上还款编号',
            prop: 'onlinePayRepaymentCodeEqual',
            hide: true,
            searchLabelWidth: 100,
            search: true,
            display: false,
          },

          {
            label: '借据号',
            prop: 'iouNo',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入借据号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '线上还款编号',
            prop: 'onlinePayRepaymentCode',
            width: 93,
            display: false,
          },
          {
            label: '融资用户',
            width: 150,
            prop: 'userId',
            type: 'select',
            dicUrl: '/api/blade-user/user-all?userType=6',
            dicMethod: 'get',
            display: false,
            props: {
              value: 'id',
              label: 'name',
            },
          },

          {
            label: '所属期数',
            prop: 'period',
            display: false,
            hide: true,
          },
          {
            label: '所属期数',
            display: false,
            prop: 'periodDes',
          },
          {
            label: '应还金额（元）',
            prop: 'totalAmount',
            display: false,
            width: 120,
            rules: [
              {
                required: true,
                message: '请输入应还金额（元）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '实还金额（元）',
            width: 120,
            display: false,
            prop: 'actualAmount',
          },
          {
            label: '创建日期',
            prop: 'createTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            display: false,
            valueFormat: 'yyyy-MM-dd',
          },
          {
            label: '还款日期',
            prop: 'repaymentTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            display: false,
            valueFormat: 'yyyy-MM-dd',
          },
          {
            label: '还款类型',
            prop: 'repaymentType',
            dataType: 'number',
            display: false,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=loan_repayment_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '状态',
            prop: 'status',
            slot: true,
          },
          {
            label: '支付方式',
            prop: 'payMode',
            dicData: DIC.pay,
            slot: true,
          },
          {
            label: '操作时间',
            display: false,
            prop: 'updateTime',
          },
          {
            label: '操作人',
            display: false,
            prop: 'userName',
            width: 150,
          },
        ],
      },
      data: [],
      detailDefaultArr: [
        {
          id: '1',
          label: '本金(元)：',
          prop: '',
        },
        {
          id: '2',
          label: '利息(元)：',
          prop: '',
        },
        // {
        //   id: '3',
        //   label: '手续费(元)：',
        //   prop: '',
        // },
        // {
        //   id: '4',
        //   label: '逾期利息(元)：',
        //   prop: '',
        // },
        // {
        //   id: '5',
        //   label: '应还总额(元)：',
        //   prop: '',
        // },
      ],
      detailArr: [],
      submitLoading: false,
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.loanmanagerepayment_add, false),
        viewBtn: this.vaildData(this.permission.loanmanagerepayment_view, false),
        delBtn: this.vaildData(this.permission.loanmanagerepayment_delete, false),
        editBtn: this.vaildData(this.permission.loanmanagerepayment_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
    uploadError(error) {
      this.$message.error(error)
    },
    // 查看明细
    viewVoucher(row) {
      this.detailArr = JSON.parse(JSON.stringify(this.detailDefaultArr))
      this.detailArr[0].prop = `￥${formatMoney(row.principal)}`
      this.detailArr[1].prop = `￥${formatMoney(row.interest)}`
      // this.detailArr[2].prop = `￥${formatMoney(row.serviceCharge)}`
      // this.detailArr[3].prop = `￥${formatMoney(row.penaltyInterest)}`
      // this.detailArr[2].prop = `￥${formatMoney(row.totalAmount)}`
      row.repaymentFeeList.forEach(item => {
        this.detailArr.push({
          id: this.detailArr.length,
          label: item.feeName,
          prop: `￥${formatMoney(item.shouldAmount)}`,
          img: false,
        })
      })
      this.detailArr.push({
        id: '5',
        label: '应还总额(元)：',
        prop: `￥${formatMoney(row.totalAmount)}`,
      })

      if (row.status === 1) {
        //
      } else if (row.status === 3) {
        this.detailArr.push({
          id: this.detailArr.length,
          label: '实还金额(元)：',
          prop: `￥${formatMoney(row.actualAmount)}`,
        })
        if (row.payMode != 2) {
          this.detailArr.push({
            id: this.detailArr.length,
            label: '付款开户行：',
            prop: row.bank,
          })
          this.detailArr.push({
            id: this.detailArr.length,
            label: '付款账号：',
            prop: row.bankCardNo,
          })
          this.detailArr.push({
            id: this.detailArr.length,
            label: '还款时间：',
            prop: row.repaymentTime,
          })
          this.detailArr.push({
            id: this.detailArr.length,
            img: true,
            label: '还款凭证：',
            prop: [],
          })
        }

        if (row.voucher) {
          ids(row.voucher).then(resp => {
            let arr = []
            let data = resp.data.data
            data.forEach(item => {
              arr.push({ name: item.name, url: item.link })
            })
            this.detailArr[this.detailArr.length - 1].prop = arr
          })
        }
      } else {
        this.detailArr.push({
          id: this.detailArr.length,
          label: '实还金额(元)：',
          prop: `￥${formatMoney(row.actualAmount)}`,
        })
        this.detailArr.push({
          id: this.detailArr.length,
          label: '修改时间：',
          prop: row.updateTime,
        })
        this.detailArr.push({
          id: this.detailArr.length,
          label: '说明：',
          prop: row.failReason,
        })
      }
      this.statusBox = true
    },
    // 修改状态
    openDialog(row) {
      const params = {
        goodId: row.goodsId,
        userId: row.userId,
      }
      // 查核心账户
      this.getByGoodsIdAndUserIdFun(params)
      const obj = {}
      obj.moneyBox = {}
      obj.moneyBox.principal = `￥${formatMoney(row.principal)}`
      obj.moneyBox.interest = `￥${formatMoney(row.interest)}`
      // obj.moneyBox.serviceCharge = `￥${formatMoney(row.serviceCharge)}`
      // obj.moneyBox.penaltyInterest = `￥${formatMoney(row.penaltyInterest)}`
      obj.moneyBox.repaymentFeeList = row.repaymentFeeList
      const all = row.totalAmount
      obj.moneyBox.totalPrices = `￥${formatMoney(all)}`
      this.statusForm.moneyBox = obj
      this.statusForm.id = row.id
      this.statusForm.iouNo = row.iouNo
      this.statusForm.actualAmount = all
      this.statusBox1 = true
    },
    // 图片查看
    handlePictureCardPreview(file) {
      const imgSrcArr = []
      imgSrcArr.push({ url: file.url })
      this.$ImagePreview(imgSrcArr, 0, {
        closeOnClickModal: true,
      })
    },
    closeDialog(done) {
      this.celarFun()
      done()
    },
    celarFun() {
      this.statusForm = Object.assign({}, this.$options.data().statusForm)
      this.statusForm.status = void 0
      this.statusForm.actualAmount = void 0
      this.statusForm.repaymentTime = void 0
      this.statusForm.voucherUrl = []
      this.statusForm.failReason = void 0
    },
    submit() {
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          let voucher = null
          for (const item of this.statusForm.voucher) {
            if (voucher) {
              voucher += `,${item.id}`
            } else {
              voucher = item.id
            }
          }
          // this.statusForm.voucher = voucher
          if (this.statusForm.status === 3) {
            this.statusForm.failReason = ''
          }
          this.submitLoading = true
          updateStatus({ ...this.statusForm, voucher })
            .then(() => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
              done()
              this.celarFun()
              this.submitLoading = false
              this.statusBox1 = false
            })
            .catch(() => {
              done()
              this.submitLoading = false
            })
        }
      })
    },
    rowSave(row, done) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        },
        error => {
          window.console.log(error)
          done()
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      if (params.rangeTime) {
        this.query.repaymentTimeDateGe = params.rangeTime[0]
        this.query.repaymentTimeDateLe = params.rangeTime[1]
      }
      delete params.rangeTime
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      this.query.status = null
      if (this.repaymentStatus != 0) {
        this.query.status = this.repaymentStatus
      }
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    // 查询核心账户
    getByGoodsIdAndUserIdFun(obj) {
      const params = {
        goodsId: obj.goodId,
        userId: obj.userId,
      }
      getByGoodsIdAndUserId(params).then(({ data }) => {
        const { data: resData } = data
        if (data.success && resData) {
          this.statusForm.bank = resData.bankDeposit
          this.statusForm.bankCardNo = resData.bankCardNo
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
// 覆盖弹窗默认样式
::v-deep {
  .avue-dialog .el-dialog__body {
    margin-bottom: 0;
  }
  .el-dialog__body {
    padding: 0px 20px;
  }
  .my-el-dialog .avue-form__menu {
    display: none;
  }
  .file-upload {
    .el-upload--picture-card {
      display: none;
    }
    .el-upload-list__item:hover .el-upload-list__item-status-label {
      display: none;
    }
    .el-upload-list__item-status-label {
      display: none;
    }
  }
}
.file-upload {
  transform: translateY(10px);
}
.detail-box {
  display: flex;
  flex-direction: column;
  margin-left: 17px;

  .children-box {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .laber-box {
      width: 95px;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
    .value-box {
      width: 370px;
      height: 20px;
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
  }
}
.money-box {
  width: 432px;
  margin: -10px 0;
  border-radius: 6px;
  background-color: rgba(247, 247, 247, 100);
  padding: 0 12px 20px;
  box-sizing: border-box;
  overflow: hidden;

  .chlidern-money {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .label-box {
      width: 350px;
      height: 20px;
      line-height: 20px;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      text-align: right;
      font-family: SourceHanSansSC-regular;
    }
    .value-box {
      width: 311px;
      height: 20px;
      line-height: 20px;
      color: rgba(0, 7, 42, 100);
      font-weight: bold;
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
  }
}
</style>
