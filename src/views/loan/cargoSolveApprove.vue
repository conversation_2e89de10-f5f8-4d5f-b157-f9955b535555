<template>
  <div class="recDetail-box">

    <!-- 货物处置协商审批 -->
    <basic-container>
      <div class="cargo">
        <div class="cargo-to-apply-for-product">
          <h1 class="cargo-to-apply-for-product-h1">
            <span>产品信息</span>
            <span class="space"></span>
            <span class="sub-title">融资编号：<span class="code">J212312312312312</span></span>
          </h1>
          <div class="cargo-to-apply-for-product-title">
            <div class="cargo-to-apply-for-product-left-logo-box">
              <div class="cargo-to-apply-for-product-left-logo-box-img">
                <img src="goodsDetail?.capitalAvatar" alt />
              </div>
              <div class="cargo-to-apply-for-product-left-logo-box-goodname">
                <span @click="viewGoods()">产品名称 </span>
                <span> 产品描述 </span>
              </div>
            </div>
            <span class="cargo-to-apply-for-product-right-goodtype">动产质押</span>
          </div>

        </div>
      </div>
    </basic-container>

    <!-- 订单信息 -->
    <basic-container>
      <el-collapse v-model="activeNames1" @change="handleChange1">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i class="el-icon-caret-bottom" :class="{
                  'i-active': change1Type,
                }"></i>
                <h1 class="fromLeft-title">
                  <span>订单信息</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top">
            <TableDialog page="chaseOrder" :column-options="columnOptions" :table-data="goodsDetail.tableData">
            </TableDialog>
          </div>
          <div class="descriptions-for-box">
            <el-descriptions title="" :column="3" border>
              <el-descriptions-item label="借款人名称">{{
                goodsDetail.pickUpManners
              }}</el-descriptions-item>
              <el-descriptions-item label="借款金额">{{
                goodsDetail.receiveCompanyName
              }}</el-descriptions-item>
              <el-descriptions-item label="放款日">{{
                goodsDetail.receiveAddress
              }}</el-descriptions-item>
              <el-descriptions-item label="赎货日">{{
                goodsDetail.receiveName
              }}</el-descriptions-item>
            </el-descriptions>
            <div class="footer">
              <div class="l"></div>
              <div class="r">保证金: <span class="am">3000</span>元</div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>


    <!-- 费用详情 -->
    <basic-container>
      <div class="cargo">
        <div class="cargo-to-apply-for-product">
          <h1 class="cargo-to-apply-for-product-h1">
            <span>费用详情</span>
          </h1>
        </div>
      </div>

      <div class="repayment-note-container">
        <div class="form-container">
          <div class="table-top refund">
            <div class="title-container">
              <span class="title">还款单</span>
            </div>
            <div class="table-title-box">
              <div class="title-left-box">
                <span>银行还款单</span>
                <span />
                <span>年化利率{{ '--' }}%</span>
              </div>
              <div class="title-right-box">
                应还本金(元)
                <span style="color: black">{{
                  moneyTotal | formatMoney
                }}</span>
              </div>
            </div>
            <el-table ref="table2" :data="tableData4" style="width: 100%; margin-top: 13px" class="table-border-style"
              :summary-method="getSummaries" show-summary>
              <el-table-column prop="name" label="费用名称"></el-table-column>
              <el-table-column prop="name" label="费用类型"></el-table-column>
              <el-table-column prop="name" label="计算方式"></el-table-column>
              <el-table-column prop="name" label="应付金额(元) "></el-table-column>
            </el-table>
          </div>

          <div class="table-top refund">
            <div class="table-title-box">
              <div class="title-left-box">
                <span>仓储费用</span>
              </div>
            </div>
            <el-table ref="table2" :data="tableData4" style="width: 100%; margin-top: 13px" class="table-border-style"
              :summary-method="getSummaries" show-summary>
              <el-table-column prop="name" label="费用名称"></el-table-column>
              <el-table-column prop="name" label="费用类型"></el-table-column>
              <el-table-column prop="name" label="计算方式"></el-table-column>
              <el-table-column prop="name" label="应付金额(元) "></el-table-column>
            </el-table>
          </div>

          <div class="footer">
            <div class="l"></div>
            <div class="r">应还总额: <span class="am red">3000</span>元</div>
          </div>

        </div>
      </div>

    </basic-container>

    <!-- 处置货物信息 -->
    <basic-container>
      <div class="cargo">
        <div class="cargo-to-apply-for-product">
          <h1 class="cargo-to-apply-for-product-h1">
            <span>处置货物信息</span>
          </h1>
        </div>
      </div>

      <div class="table-top refund">

        <div class="header">
          <div class="pic-wrap">
            <img class="pic-img" src="" alt="商品图片">
          </div>
          <div class="info">
            <div class="name">商品名称</div>
          </div>
        </div>

        <el-table ref="table4" :data="tableData4" style="width: 100%; margin-top: 13px" class="table-border-style"
          :summary-method="getSummaries" show-summary>
          <el-table-column prop="payNodes" label="规格"></el-table-column>
          <el-table-column prop="payNodes" label="数量"></el-table-column>
          <el-table-column prop="cashDepositTypes" label="融资单价"></el-table-column>
          <el-table-column prop="cashDepositTypes" label="处置单价"></el-table-column>
        </el-table>
        <div class="footer">
          <div class="l annex">评估附件</div>
          <div class="r">处置金额: <span class="am">3000</span>元</div>
        </div>
      </div>
    </basic-container>

    <!-- 处置比例 -->
    <basic-container>
      <div class="cargo">
        <div class="cargo-to-apply-for-product">
          <h1 class="cargo-to-apply-for-product-h1">
            <span>处置比例</span>
          </h1>
        </div>
      </div>

      <div class="table-top refund">

        <el-table ref="table4" :data="tableData4" style="width: 100%; margin-top: 13px" class="table-border-style"
          :summary-method="getSummaries" show-summary>
          <el-table-column prop="payNodes" label="费用名称"></el-table-column>
          <el-table-column prop="payNodes" label="应付"></el-table-column>
          <el-table-column prop="cashDepositTypes" label="分账比例"></el-table-column>
          <el-table-column prop="cashDepositTypes" label="分账金额"></el-table-column>
        </el-table>
        <div class="footer">
          <div class="l annex">处置总额 = 处置金额 + 保证金</div>
          <div class="r">处置总额: <span class="am">3000</span>元</div>
        </div>
      </div>
    </basic-container>


    <!-- 处置信息 -->
    <basic-container>
      <div class="cargo">
        <div class="cargo-to-apply-for-product">
          <h1 class="cargo-to-apply-for-product-h1">
            <span>处置信息</span>
          </h1>
        </div>
      </div>

      <div class="table-top">
        <TableDialog page="chaseOrder" :column-options="columnOptions" :table-data="goodsDetail.tableData">
        </TableDialog>
      </div>
      <div class="descriptions-for-box">
        <el-descriptions title="" :column="3" border>
          <el-descriptions-item label="处置公司">{{
            goodsDetail.pickUpManners
          }}</el-descriptions-item>
          <el-descriptions-item label="处置公司负责人">{{
            goodsDetail.receiveCompanyName
          }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{
            goodsDetail.receiveAddress
          }}</el-descriptions-item>
          <el-descriptions-item label="处置总额">{{
            goodsDetail.receiveName
          }}</el-descriptions-item>
          <el-descriptions-item label="应付金额">{{
            goodsDetail.receiveName
          }}</el-descriptions-item>
          <el-descriptions-item label="剩余退回">{{
            goodsDetail.receiveName
          }}</el-descriptions-item>
          <el-descriptions-item label="是否足价清算">{{
            goodsDetail.receiveName
          }}</el-descriptions-item>
        </el-descriptions>
      </div>


      <div class="cargo pt20">
        <div class="cargo-to-apply-for-product">
          <h1 class="cargo-to-apply-for-product-h1">
            <span>退回信息</span>
          </h1>
        </div>
      </div>

      <div class="refund table-top">
        <el-table ref="table4" :data="tableData4" style="width: 100%; margin-top: 13px" class="table-border-style"
          :summary-method="getSummaries" show-summary>
          <el-table-column prop="payNodes" label="开户银行"></el-table-column>
          <el-table-column prop="payNodes" label="开户名"></el-table-column>
          <el-table-column prop="cashDepositTypes" label="银行账号"></el-table-column>
          <el-table-column prop="cashDepositTypes" label="退回金额"></el-table-column>
        </el-table>
      </div>

    </basic-container>


  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'
import { getCountDownHtml  } from '@/util/util'
import BasicFooter from '@/components/basic-footer/index'

export default {
  components: {
    BasicFooter
  },
  data() {
    return {
      activeNames1: [],
      activeNames2: [],
      activeNames3: [],
      change1Type: false,
      change2Type: false,
      change3Type: false,
      goodsDetail: {},

      pageData: {},
      tableData4: [],

      form: {},

      option: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 120,
        gutter: 80,
        column: [
          {
            label: '处置总额',
            prop: 'name1',
            type: 'input',
            disabled: true,
            value: '3000元',
            span: 12,
          },
          {
            label: '应付金额',
            prop: 'name2',
            type: 'input',
            value: '3000元',
            disabled: true,
            span: 12,
          },
          {
            label: '足价清算',
            prop: 'name3',
            type: 'select',
            value: '是',
            disabled: true,
            span: 12,
          },
          {
            label: '发起协商审批',
            prop: 'supplier',
            span: 12,
            type: 'select',
            value: '1',
            dicData: [
              { label: '是', value: '1' },
              { label: '否', value: '0' },
            ],
          },

        ],
      },
      option3: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 120,
        gutter: 80,
        column: [
          {
            label: '评估附件',
            prop: 'imgUrl4',
            type: 'upload',
            span: 12,
            // listType: 'picture-img',
            // accept: 'image/png, image/jpeg, image/jpg',
            fileSize: 5000,
            // tip: '注:仅支持上传jpg、jpeg、png格式，大小不超过5Mb',
            propsHttp: {
              res: 'data',
            },
            rules: [
              {
                required: true,
                message: '请上传评估附件',
              },
            ],

            action: '/api/blade-resource/oss/endpoint/put-file-kv',
          },
        ],
      },
    }
  },
  methods: {
    viewGoods() { },
    // 待赎信息统计
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
  },
}



</script>

<style lang="scss" scoped>
.recDetail-box {
  padding-bottom: 168px !important;
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }

    .fromLeft-title-user {
      line-height: 22px;
      color: #7d7d7d;
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .fromLeft-title-name {
      color: #697cff !important;
      line-height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}


.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      min-width: 135px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }

}

.descriptions-for-box2 {
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 200px;
      min-width: 200px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      // width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}

::v-deep i.el-collapse-item__arrow {
  display: none;
}

::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}

::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}

::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}

::v-deep .el-card {
  border-radius: 8px;

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

// 更改表格组件样式
::v-deep {
  .table-top {
    margin-top: 23px;

    .table-title-box {
      height: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .table-title {
      line-height: 21px;
      color: #7d7d7d;
      font-size: 14px;
      font-weight: 400;
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell>.cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}

.cargo {
  .cargo-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .cargo-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .space {
        display: inline-block;
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: #d7d7d7;
        text-align: center;
        margin: 0 8px;
      }

      .sub-title {
        line-height: 22px;
        color: #7d7d7d;
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;

        .code {
          color: #697cff;
        }
      }

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        &>span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        &>span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .cargo-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .cargo-to-apply-for-product-left-logo-box {
        margin-top: 17px;
        display: flex;
        align-items: center;

        .cargo-to-apply-for-product-left-logo-box-img {
          width: 48px;
          height: 48px;
          border-radius: 40px;
          border: 1px solid rgba(234, 234, 234, 100);
          overflow: hidden;
          margin-right: 7px;

          &>img {
            width: 100%;
            object-fit: cover;
          }
        }

        .cargo-to-apply-for-product-left-logo-box-goodname {
          display: flex;
          flex-direction: column;

          &>span:first-child {
            display: block;
            height: 24px;
            color: rgba(18, 119, 255, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
          }

          &>span:last-child {
            display: block;
            height: 18px;
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }

      .cargo-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .cargo-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover>td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}


// 自定义主体样式 - 结束
// 更改表格组件样式
::v-deep {
  .table-top {
    margin-top: 23px;

    .table-title-box {
      height: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell>.cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}

::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  // justify-content: space-between;
  justify-content: right;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: #242424;
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      color: #ff2929;
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

::v-deep .account {
  display: flex;
  width: 400px;
  align-items: center;

  .accountText {
    margin-right: 20px;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: #fff;
    cursor: pointer;
  }
}



.footer {
  padding-top: 15px;
  display: flex;
  justify-content: space-between;

  .r {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;

    .am {
      padding-left: 5px;
      font-size: 22px;
    }
  }
}

.repayment-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 5px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.confirm-form-wrap {
  .formula-wrap {
    padding: 20px 40px;
    color: #aaa;
  }
}

.pt20 {
  padding-top: 20px;
}
</style>
