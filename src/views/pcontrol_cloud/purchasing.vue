<template>
  <basic-container>
    <div class="tageds">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane
          :key="item.name"
          :name="item.name"
          v-for="item in editableTabs"
        >
          <span slot="label">
            <el-button-group>
              <el-button
                type="primary"
                size="medium"
                :plain="item.name == activeName ? false : true"
                :class="{
                  'bord-radius-left': item.name == '1',
                  'bord-radius-right':
                    item.name == editableTabs[editableTabs.length - 1].name,
                }"
                >{{ item.title }}</el-button
              >
            </el-button-group>
          </span>
          <!-- 产品信息 -->
          <AgentProduct
            ref="product"
            v-if="item.name == '1'"
            :look="look"
            @processOnload="processOnloadP"
          />
          <!-- 相关费用 云信的 -->
          <AgentCorrelative
            ref="correlative"
            v-else-if="item.name == '2'"
            :look="look"
          />
          <!-- 数据收集 -->
          <AgentGather ref="gather" v-else-if="item.name == '3'" :look="look" />
          <!-- 风控规则 -->
          <AgentPreposition
            ref="preposition"
            v-else-if="item.name == '4'"
            :look="look"
          />
          <!-- 合同模板 -->
          <AgentContract
            ref="contract"
            v-else-if="item.name == '5'"
            :look="look"
          />
          <!-- 审批流程 -->
          <AgentApprovalProcess
            ref="approvalProcess"
            v-else-if="item.name == '6'"
            :look="look"
          />
        </el-tab-pane>
      </el-tabs>
      <!-- 操作按钮 -->
      <div class="updata" v-if="!look">
        <el-button @click="handleback">取消</el-button>
        <template v-if="zhanshiBtn">
          <el-button type="success" @click="handleSubmit('updata')"
            >上架</el-button
          >
          <el-button type="primary" @click="handleSubmit('save')">保存</el-button>
        </template>
      </div>
    </div>
  </basic-container>
</template>

<script>
import AgentProduct from './agentSubsections/product.vue'
import AgentCorrelative from './agentSubsections/correlative1.vue'
import AgentGather from './agentSubsections/gather.vue'
import AgentPreposition from './agentSubsections/preposition.vue'
import AgentContract from './agentSubsections/contract.vue'
import AgentApprovalProcess from './agentSubsections/approvalProcess.vue'
import { mapState } from 'vuex'
import {
  saveCloudPurchasing,
  updateCloudPurchasing,
  cloudProductShelf,
} from '@/api/goods/pcontrol/pinformation'

export default {
  data() {
    return {
      id: this.$route.query.id,
      activeName: '1',
      editableTabs: [
        {
          title: '产品信息',
          name: '1',
          paths: 'product',
        },
        {
          title: '相关费用',
          name: '2',
          paths: 'correlative',
        },
        {
          title: '数据收集',
          name: '3',
          paths: 'gather',
        },
        {
          title: '风控规则',
          name: '4',
          paths: 'preposition',
        },
        {
          title: '合同模板',
          name: '5',
          paths: 'contract',
        },
        {
          title: '审批流程',
          name: '6',
          paths: 'approvalProcess',
        },
      ],
      look: false,
      zhanshiBtn: false,
    }
  },
  components: {
    AgentProduct,
    AgentCorrelative,
    AgentGather,
    AgentPreposition,
    AgentContract,
    AgentApprovalProcess,
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
      valid: state => state.common.valid,
    }),
  },
  created() {
    // 因为产品放款方式已经隐藏,避免被其他产品污染，所以进入就改写成手动放款
    sessionStorage.setItem('lendingMethod', '2')
    if (sessionStorage.getItem('look') == 'true') {
      this.look = true
    }
    this.yanshiqidongBtnFun()
  },
  methods: {
    yanshiqidongBtnFun() {
      setTimeout(() => {
        this.zhanshiBtn = true
      }, 4000)
    },
    // 云信的取消
    handleback(){
      this.$router.$avueRouter.closeTag()
      this.$router.back()
    },
    handleSubmit(doing) {
      this.$refs.product[0].handleSubmit(doing == 'save' ? doing : '')
      setTimeout(() => {
        this.$refs.correlative[0].setData()
      }, 100)
      setTimeout(() => {
        this.$refs.gather[0].setData()
      }, 200)
      setTimeout(() => {
        this.$refs.preposition[0].setData()
      }, 300)
      setTimeout(() => {
        this.$refs.contract[0].setData()
      }, 400)
      setTimeout(() => {
        this.$refs.approvalProcess[0].setData()
        if (!this.valid) {
          this.$store.commit('SET_VALID_TYPE', true)
          return
        }
        if (!this.id) {
          saveCloudPurchasing(this.formParamsDataed)
            .then(res => {
              const resData = res.data
              this.$message.success('已保存')
              this.id = resData.data
              if (doing == 'updata') {
                this.onShelf(resData.data)
              }
            })
            .catch(() => {})
        } else {
          const parimsId = {
            id: this.id,
          }
          const objParams = { ...parimsId, ...this.formParamsDataed }
          updateCloudPurchasing(objParams)
            .then(res => {
              const resDate = res.data
              if (resDate.code == 200) {
                this.$message.success('已保存')
                if (doing == 'updata') {
                  this.onShelf(this.id)
                }
              }
            })
            .catch(() => {})
        }
      }, 500)
    },
    onShelf(idS) {
      cloudProductShelf(idS)
        .then(res => {
          // 上架
          const resData = res.data
          if (resData.code == 200) {
            this.$message.success('已上架')
            this.$router.$avueRouter.closeTag()
            this.$router.push({ path: '/goods/cloudproduct' })
          }
        })
        .catch(() => {})
    },
    processOnloadP(argVal) {
      // this.$refs.approvalProcess[0].onLoadData()
      if(argVal == 'bank'){
        this.$refs.correlative[0].changeCapital(sessionStorage.getItem('capitalType'))
      }else if(argVal == 'repaymentType'){
        this.$refs.correlative[0].changRepaymentType(sessionStorage.getItem('repaymentType'))
      }
      
    },
    // handleClick(tab, event) {
    //   console.log(tab, event)
    // },
    // checkrouter(item) {
    //   console.log(item)
    //   // this.$router.push({ path: `/pcontrol/pinformation/subsections/${item.paths}` });
    // },
  },
}
</script>

<style lang="scss">
.tageds {
  .bord-radius-left {
    border-radius: 20px 0 0 20px !important;
    border-left-color: #b3d8ff !important;
  }
  .bord-radius-right {
    border-radius: 0 20px 20px 0 !important;
    border-right-color: #b3d8ff !important;
  }
  .el-button--primary.is-plain {
    // background: #ffffff;
    border-color: #a3cdf8;
  }
  .el-tabs__item {
    padding: 0 !important;
  }
  .el-tabs--card > .el-tabs__header .el-tabs__item {
    border: none;
  }
  .el-tabs--card > .el-tabs__header {
    border-bottom: none;
  }
  // .el-button--primary.is-plain{
  //   border-color: #a3cdf8 !important;
  // }
  // .el-button--primary {
  //   background: #1684fc;
  // }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-bottom: 1.5%;
    margin-top: 1%;
  }
}
</style>
