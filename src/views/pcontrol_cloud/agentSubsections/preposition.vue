<template>
  <!-- <basic-container> -->
  <div class="guarantee-receivable">
    <!-- up -->
    <h1 class="titleTop">基本数据</h1>
    <div class="guaranteeSetTop">
      <avue-form
        ref="form"
        :option="option"
        v-model="form"
        :defaults.sync="defaults"
      >
      </avue-form>
    </div>
    <!-- middle -->
    <h1 class="titleBottom" style="display: none">其他资料</h1>
    <div class="guaranteeSetMiddle" style="display: none">
      <avue-form
        :option="subformOption"
        ref="froms"
        v-model="obj"
        :defaults.sync="defaultsed"
      >
        <template slot-scope="{ row }" slot="expense">
          <div>{{ row.expense }}</div>
        </template>
      </avue-form>
    </div>
    <!-- down -->
    <div style="display: none">
      <h1 class="titleBottom">定时器管理</h1>
      <div class="guaranteeSetBottom">
        <avue-crud
          ref="crud"
          :option="timerOption"
          :table-loading="timerLoading"
          :data="timerAvueData"
        >
          <template slot="timingTime" slot-scope="{ row }">
            <div class="timingTime-box">
              <el-input-number
                style="width: 80%"
                v-model="row.num"
                placeholder="输入时间，仅限数字"
                :controls="false"
                :disabled="!row.unit || look"
                :min="1"
                @blur="val => handleTimeBlur(val, row)"
              />
              <el-select
                style="width: 20% !important"
                v-model="row.unit"
                placeholder="单位"
                :disabled="look"
                @change="handleChange(row)"
                clearable
              >
                <el-option
                  v-for="item in timeSelectOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </template>
        </avue-crud>
      </div>
    </div>
    <!-- 操作按钮 -->
    <!-- <div class="updata" v-if="!look">
        <el-button>取消</el-button>
        <el-button type="success" @click="setData()">上架</el-button>
        <el-button type="primary" @click="setData('save')">保存</el-button>
      </div> -->
    <!-- 弹窗 -->
    <el-dialog
      title="选择资料"
      :visible.sync="type1"
      :modal-append-to-body="false"
      class="avue-dialog avue-dialog--top"
      width="50%"
    >
      <!--
        arrOption: 配置 arr
        data: 数据源 arr
        search.sync: 获取实时的搜索值 obj
        search-change: 搜索点击事件 fan
        current-change: 分页功能页码点击事件 fan
        on-load: 首次加载事件 fan
        size-change: 分页总页数事件 fan
        selection-change: checkbox事件 fan
        table-loading: 表单加载动画 boolean
        page: 配置分页信息 obj
        -->
      <avue-crud
        ref="crud"
        :option="arrOption"
        :data="arrS"
        :search.sync="dialogSearch"
        @search-change="searchChange"
        @current-change="currentChangeScope"
        @size-change="sizeChangeScope"
        @selection-change="selectionChange"
        @on-load="onLoad"
        :table-loading="loading"
        :page="page"
      >
        <template slot-scope="{ row }" slot="node">
          <el-tag type="info">{{ row.node }}</el-tag>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="cardfals">取 消</el-button>
        <el-button @click="cardEngth" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
  <!-- </basic-container> -->
</template>

<script>
// import { set } from 'vue/types/umd'
// import {getList, getDetail, add, update, remove, enable} from "@/api/resource/oss";
// import {mapGetters} from "vuex";
// import func from "@/util/func";
import {
  // saveandUpdateGoodsRiskControl,
  // getwhitelisttemplateList,
  // selectAllEnabledList,
  selectNewAllEnabledList,
  // setShelf,
  getCloudProductDetail,
  getGoodsTimingDetail,
} from '@/api/goods/pcontrol/pinformation'
import { mapState } from 'vuex'
import { getDictionary } from '@/api/system/dictbiz'

export default {
  props: ['look'],
  data() {
    return {
      id: this.$route.query.id,
      type1: false,
      dialogSearch: {},
      cardList: [],
      loading: true,
      form: {
        whiteList: '',
        management: '',
      },
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [
          // {
          //   label: '企业白名单',
          //   prop: 'whiteList',
          //   type: 'select',
          //   span: 12,
          //   placeholder: '请选择企业白名单',
          //   disabled: this.look,
          //   dataType: 'string',
          //   dicData: [],
          // },
          {
            label: '评分模板',
            prop: 'management',
            type: 'select',
            span: 12,
            placeholder: '请选择评分模板',
            disabled: this.look,
            dicData: [],
          },
        ],
      },
      obj: {
        dynamic: [
          // {
          //   expense: '首付款不低于20%',
          //   node: '',
          //   id: '1',
          // },
          // {
          //   expense: '分控评分不低于80%',
          //   node: '',
          //   id: '2',
          // },
          // {
          //   expense: '借款人年龄不低于20岁',
          //   node: '',
          //   id: '3',
          // },
        ],
      },
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 110,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            // disabled: true,
            children: {
              delBtn: !this.look,
              addBtn: !this.look,
              align: 'center',
              headerAlign: 'center',
              rowAdd: () => {
                if (this.look) return
                this.type1 = true
                const exception = setInterval(() => {
                  if (this) {
                    this.checkUpType()
                    clearInterval(exception)
                  }
                }, 100)
                // this.$message.success('新增回调')
                // done({
                //   input: '默认值',
                // })
              },
              rowDel: (row, done) => {
                // if (this.look) return
                // this.$message.success('删除回调' + JSON.stringify(row))
                this.checkUpType(row)
                done()
              },
              column: [
                // {
                //   width: 90,
                //   label: '序号',
                //   prop: 'le',
                //   disabled: true,
                //   // type: 'text',
                //   formslot: true,
                // },
                {
                  width: 750,
                  label: '条件名称',
                  prop: 'expense',
                  disabled: this.look,
                  align: 'left',
                  // formslot: true,
                  // type: 'select',
                },
                {
                  // width: 200,
                  label: '触发节点',
                  prop: 'node',
                  type: 'select',
                  dicData: [],
                  placeholder: '上传节点',
                  disabled: this.look,
                },
              ],
            },
          },
        ],
      },
      arrS: [],
      arr: [
        // {
        //   expense: '首付款不低于20%',
        //   id: '1',
        // },
        // {
        //   expense: '分控评分不低于80%',
        //   id: '2',
        // },
        // {
        //   expense: '借款人年龄不低于20岁',
        //   id: '3',
        // },
        // {
        //   expense: 'test1',
        //   id: '4',
        // },
        // {
        //   expense: 'test12',
        //   id: '5',
        // },
        // {
        //   expense: 'test13',
        //   id: '6',
        // },
      ],
      page: {
        // total: 27,
        // pageSize: 10,
      },
      arrOption: {
        selection: true,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: true,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchMenuSpan: 7, // 搜索菜单栏宽带
        maxHeight: 300,
        index: true,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [
          {
            label: '资料名称',
            prop: 'expense',
            search: true,
            searchSpan: 10,
            searchClearable: false,
            placeholder: '按名称搜索',
          },
        ],
      },
      checkif: false,
      timerOption: {
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        index: true,
        menu: false,
        refreshBtn: false,
        columnBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        align: 'left',
        dialogClickModal: false,
        column: [],
      },
      timerLoading: false,
      timerAvueData: [],
      timeSelectOptions: [],
    }
  },
  created() {
    this.onLoadData()
    this.timeOnLoad()
    if (this.id) {
      this.getData()
    }
  },
  mounted() {
    // const set = setInterval(() => {
    //   if (this.defaults && this.look) {
    //     this.defaults.whiteList.disabled = true
    //     this.defaults.management.disabled = true
    //     // 值得更改触发组件状态更新 绝绝子 希望有大佬优化一下 谢谢！！
    //     this.form.whiteList = 1
    //     this.form.whiteList = ''
    //   }
    //   if (this.defaultsed && this.look) {
    //     this.defaultsed.dynamic.children.column[1].disabled = true
    //     console.log(this.defaultsed.dynamic)
    //     // 值得更改触发组件状态更新 绝绝子 希望有大佬优化一下 谢谢！！
    //     this.obj.dynamic[0].node = 1
    //     this.obj.dynamic[0].node = ''
    //     clearInterval(set)
    //   }
    // }, 100)
  },
  watch: {
    dialogSearch: {
      handler(val) {
        // 搜索框被清空后重新返回列表数据
        if (!val.expense && !val.node && !val.way) {
          this.arrS = this.arr
          setTimeout(() => {
            this.checkUpType()
          }, 100)
        }
      },
      deep: true,
    },
    type1(val) {
      if (!val) {
        // 弹窗关闭清空搜索值
        this.$refs.crud.searchReset()
      }
    },
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
    }),
  },
  methods: {
    setData() {
      const timeParamsArr = []
      if (this.timerAvueData.length) {
        for (const item of this.timerAvueData) {
          if (item.num) {
            timeParamsArr.push({
              type: item.type,
              num: item.num,
              unit: item.unit,
            })
          }
        }
      }
      const params = {
        // id: this.id,
        scoreTemplateId: this.form.management,
        whiteListTemplateId: this.form.whiteList,
        goodsTimingList: timeParamsArr,
      }
      const objParams = { ...this.formParamsDataed, ...params }
      this.$store.commit('SET_FORM_PARAMS_DATAED', objParams)
      // saveandUpdateGoodsRiskControl(params).then(res => {
      //   // 风控规则基本数据保存与修改
      //   const resData = res.data
      //   if (resData.code == 200) {
      //     if (!save) {
      //       setShelf(this.id)
      //         .then(res => {
      //           // 上架
      //           const resData = res.data
      //           if (resData.code == 200) {
      //             this.$message.success('已上架')
      //             this.$router.$avueRouter.closeTag()
      //             this.$router.push({ path: '/goods/goods' })
      //           }
      //         })
      //         .catch(() => {
      //           this.$message.success('已保存,未上架')
      //         })
      //     } else {
      //       this.$message.success('已保存')
      //     }
      //   }
      // })
    },
    getData() {
      getCloudProductDetail(this.id).then(res => {
        // 查询form表单数据
        const resData = res.data
        if (resData.code == 200) {
          this.form.management = resData.data.scoreTemplateId
          this.form.whiteList = resData.data.whiteListTemplateId
        }
      })
      // 查询定时任务数据
      getGoodsTimingDetail(this.id).then(({ data }) => {
        const { data: resData } = data
        if (data.success && resData.length) {
          for (const item of this.timerAvueData) {
            for (const items of resData) {
              if (item.type === items.type) {
                item.num = items.num
                item.unit = String(items.unit)
                break
              }
            }
          }
        }
      })
    },
    timeOnLoad() {
      // avueCard表格数据
      const avueCarddata = [
        {
          label: '业务节点',
          prop: 'professionNode',
        },
        {
          label: '定时器用途',
          prop: 'timerUsage',
        },
        {
          label: '定时时间',
          prop: 'timingTime',
          width: 500,
        },
      ]
      this.timerOption.column = avueCarddata
      const avueData = [
        {
          id: 1,
          type: 6,
          professionNode: '云信待签署、云信清分期',
          timerUsage: '到期自动作废云信单、控制云信清分时间',
        },
      ]
      this.timerAvueData = avueData
      // 定时器单位字典
      getDictionary({
        code: 'goods_time_unit',
      }).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          for (const item of resData) {
            this.timeSelectOptions.push({
              id: item.id,
              label: item.dictValue,
              value: item.dictKey,
            })
          }
        }
      })
    },
    onLoadData() {
      // getwhitelisttemplateList().then(res => {
      //   // 获取白名单模板list接口
      //   const resData = res.data
      //   if (resData.code == 200) {
      //     const resArr = []
      //     for (const item of resData.data) {
      //       resArr.push({
      //         label: item.templateName,
      //         value: item.id,
      //       })
      //     }
      //     this.option.column[0].dicData = resArr
      //   }
      // })
      selectNewAllEnabledList({ type: 3 }).then(res => {
        // 获取评分模板list接口
        const resData = res.data
        if (resData.code == 200) {
          const resArr = []
          for (const item of resData.data) {
            resArr.push({
              label: item.name,
              value: item.id,
            })
          }
          this.option.column[0].dicData = resArr
        }
      })
    },
    searchChange(params, done) {
      // card搜索事件
      let ar = []
      this.arr.map(item => {
        const ex = item.expense.indexOf(params.expense) != -1
        if (ex) {
          ar.push(item)
        }
      })
      this.arrS = ar
      setTimeout(() => {
        this.checkUpType()
      }, 100)
      done()
    },
    onLoad() {
      // card首次加载事件
      this.loading = false
      this.arrS = this.arr
    },
    currentChangeScope(currentPage) {
      // 分页页码切换事件
      return console.log(currentPage)
    },
    sizeChangeScope(pageSize) {
      // 分页页数切换事件
      return console.log(pageSize)
    },
    selectionChange(cardList) {
      // checkbox事件
      this.cardList = cardList
      if (!this.checkif) {
        this.checkif = true
      }
    },
    cardEngth() {
      // 弹窗表格确认按钮
      this.type1 = false
      this.upBeforeCheck()
    },
    checkUpType(rows) {
      // 根据rows是否存在判断是选择操作还是删除操作
      if (!this.obj.dynamic.length) {
        // 表单无数据，用户并没有进行保存操作，对checkbox状态全部清除
        this.toggleSelection()
        return
      }
      let exclude = []
      if (!rows) {
        exclude = this.fun(this.obj.dynamic, this.cardList, 'id')
      } else {
        let arr = []
        let arrs = [rows]
        this.cardList.map(item => {
          arr.push(item.id)
        })
        exclude = arrs.filter(item => arr.indexOf(item.id))
      }
      if (exclude.length) {
        exclude.map(num => {
          const ind = this.getArrayIndex(this.arr, num.id)
          if (ind !== -1) {
            this.toggleSelection([this.arr[ind]])
          }
        })
      }
    },
    cardfals() {
      // 弹窗表格取消按钮
      this.type1 = false
      if (this.checkif) {
        // 当checkbox事件被触发，并且没有确定更改，对checkbox进行状态复原
        this.checkUpType()
      }
      this.checkif = false
    },
    toggleSelection(val) {
      // 弹窗取消后恢复之前checkbox状态
      this.$refs.crud.toggleSelection(val)
    },
    upBeforeCheck() {
      let compareArr = []
      let storeArr = []
      this.obj.dynamic.map(item => {
        compareArr.push(item.id)
      })
      if (compareArr.length < this.cardList.length) {
        storeArr = this.cardList.filter(item => !compareArr.includes(item.id))
      } else {
        // 如果数据list的length比checkbox的length小，进行一个重新赋值
        storeArr = this.cardList.filter(item => compareArr.includes(item.id))
        storeArr = storeArr.map((item, index) => {
          item.$index = index
          return item
        })
        this.obj.dynamic = JSON.parse(JSON.stringify(storeArr))
        return
      }
      storeArr &&
        storeArr.map(item => {
          // 筛选出checkbox为true并且dynamic 数据List没有的进行push操作
          this.obj.dynamic.push(item)
        })
    },
    getArrayIndex(arr, obj) {
      /*
       * 获取某个元素下标
       * arr: 传入的数组
       * obj: 需要获取下标的元素
       * */
      var i = arr.length
      while (i--) {
        if (arr[i].id === obj) {
          return i
        }
      }
      return -1
    },
    fun(fArr, cArr, field) {
      let diffRes = []
      let fDatas = []
      let cDatas = []
      for (let i in fArr) {
        let flg = false
        for (let j in cArr) {
          if (cArr[j][field] === fArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          fDatas.push(fArr[i])
        }
      }
      for (let i in cArr) {
        let flg = false
        for (let j in fArr) {
          if (fArr[j][field] === cArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          cDatas.push(cArr[i])
        }
      }
      diffRes.push(...cDatas.concat(fDatas))
      return diffRes
    },
    handleTimeBlur(val, row) {
      const value = val.target.value
      const nowRow = this.timerAvueData.filter(item => item.id === row.id)
      // if (Number(value) > 30 && row.unit === '1') {
      //   this.$message.error('时间不能大于30天')
      //   nowRow[0].num = 30
      // } else if (Number(value) > 720 && row.unit === '2') {
      //   this.$message.error('时间不能大于30天(720小时)')
      //   nowRow[0].num = 720
      // } else if (Number(value) > 43200 && row.unit === '3') {
      //   this.$message.error('时间不能大于30天(43200分)')
      //   nowRow[0].num = 43200
      // }
      if (Number(value) > 24 && row.unit === '1') {
        this.$message.error('时间不能大于24天')
        nowRow[0].num = 24
      } else if (Number(value) > 576 && row.unit === '2') {
        this.$message.error('时间不能大于24天(576小时)')
        nowRow[0].num = 576
      } else if (Number(value) > 34560 && row.unit === '3') {
        this.$message.error('时间不能大于24天(34560分)')
        nowRow[0].num = 34560
      }
    },
    handleChange(row) {
      row.num = void 0
      this.timerAvueData = [...this.timerAvueData]
    },
  },
}
</script>

<style lang="scss" scoped>
.guarantee-receivable {
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 40px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetMiddle {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding: 35px 30px 0 30px;
    box-sizing: border-box;
  }
  .guaranteeSetBottom {
    .timingTime-box {
      display: flex;
      align-items: center;

      & > * {
        margin-right: 8px;
      }

      & > *:last-child {
        margin-right: 0;
      }
    }

    ::v-deep {
      .avue-crud__menu {
        display: none;
      }
    }
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    color: #4c4b4b !important;
  }
}
</style>
