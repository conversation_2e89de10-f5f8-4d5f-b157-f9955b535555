<template>
  <div class="account-all-box">
    <!-- 空状态盒子 -->
    <template v-if="virtualAccountObj.accountNowType === 0">
      <EmptyAccount />
    </template>
    <!-- 开通中 -->
    <template v-else-if="virtualAccountObj.accountNowType === 1">
      <InService />
    </template>
    <!-- 提交完成 -->
    <template v-else-if="virtualAccountObj.accountNowType === 2">
      <SubmitInformation ref="submitInformationRef" />
    </template>
    <!-- 开户完成-我的账户 -->
    <template v-else-if="virtualAccountObj.accountNowType === 3">
      <MyAccount />
    </template>
    <!-- 取消开户弹窗组件 -->
    <SecondConfirmationDialog ref="secondConfirmationDialogRef" />
  </div>
</template>

<script>
import {
  hlbDetail,
  hlbRedisEchoPay,
  redisSubmitPay,
  attachmentRetransmission,
} from '@/api/openAccount'
import { mapGetters } from 'vuex'
import EmptyAccount from './components/emptyAccount/index.vue'
import InService from './components/inService/index.vue'
import SubmitInformation from './components/submitInformation/index.vue'
import MyAccount from './components/myAccount/index.vue'
import SecondConfirmationDialog from './components/dialog/secondConfirmationDialog.vue'

export default {
  components: {
    EmptyAccount,
    InService,
    SubmitInformation,
    MyAccount,
    SecondConfirmationDialog,
  },
  data() {
    return {
      arr1: [],
      arr2: [],
      arr3: [],
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  created() {
    this.getData()
    this.arr1 = [
      'signName',
      'address',
      'benefAddress',
      'benefIdCardEndDate',
      'benefIdCardStartDate',
      'benefLegalPerson',
      'benefLegalPersonID',
      'benefLegalPersonIdType',
      'businessLicense',
      'city',
      'enterpriseOwner',
      'idType',
      'industryTypeCode',
      'legalPerson',
      'legalPersonID',
      'legalPersonIdAddress',
      'merchantCategory',
      'orgNum',
      'regionCode',
      'showName',
    ]
    this.arr2 = [
      'email',
      'linkPhone',
      'linkman',
      'linkmanIdCardEndDate',
      'linkmanIdCardStartDate',
      'linkmanIdType',
      'linkmanType',
    ]
    this.arr3 = [
      'accountName',
      'accountNo',
      'bankAccountId',
      'bankCode',
      'oldpassword',
      'payPassword',
      'settleBankType',
      'settleMode',
      'settlementMode',
      'settlementPeriod',
    ]
  },
  methods: {
    // 取消开户二次确认弹窗
    secondConfirmationOpen() {
      this.$refs.secondConfirmationDialogRef.handleOpen()
    },
    // 设置开通状态进度
    setAccountNowType(num) {
      // 0：空状态、1：开通中、2：已开通
      this.$store.commit('setvirtualAccountObj', {
        key: 'accountNowType',
        value: num,
      })
    },
    // 获取已保存数据如果有的话
    getData() {
      // 先清空旧进度
      this.setAccountNowType(0)
      // 开通完成详情接口
      hlbDetail().then(({ data }) => {
        const { data: resData } = data
        if (data.success) {
          // if (!resData) {
          //   // 有保存过数据的回显接口
          //   this.hlbRedisEchoPayFun()
          // } else {
          //   this.disposeDetail(resData)
          // }
          if (!resData) {
            // 有保存过数据的回显接口
            this.hlbRedisEchoPayFun()
          } else if (resData.status !== 7) {
            this.disposeDetail(resData)
          } else {
            this.getMyAccount()
          }
        }
      })
    },
    // 有保存过数据的回显接口
    hlbRedisEchoPayFun(isRe) {
      hlbRedisEchoPay().then(({ data }) => {
        const { data: resData } = data
        if (data.success && resData) {
          // 是否保存过数据
          this.$store.commit('setvirtualAccountObj', {
            key: 'isSave',
            value: true,
          })
          const obj1 = {}
          const obj2 = {}
          const obj3 = {}
          for (const item of this.arr1) {
            if (item === 'enterpriseOwner') {
              // 法人是否受益人转译
              if (resData.enterpriseOwner) {
                obj1[item] = 1
              } else {
                obj1[item] = 0
              }
            } else {
              obj1[item] = resData[item]
            }
          }
          for (const item of this.arr2) {
            obj2[item] = resData[item]
          }
          for (const item of this.arr3) {
            obj3[item] = resData[item]
          }

          // 开通中进度
          if (resData.level) {
            this.$store.commit('setvirtualAccountObj', {
              key: 'accountActive',
              value: Number(resData.level - 1),
            })
          }
          // 基本信息
          this.$store.commit('setvirtualAccountObj', {
            key: 'basicInformation',
            value: obj1,
          })
          // 联系人信息
          this.$store.commit('setvirtualAccountObj', {
            key: 'contactInformation',
            value: obj2,
          })
          // 结算卡信息
          this.$store.commit('setvirtualAccountObj', {
            key: 'statementInformation',
            value: obj3,
          })
          // 资质补充
          if (resData.attachList && resData.attachList.length) {
            this.$store.commit('setvirtualAccountObj', {
              key: 'supplementObj',
              value: { sType: false, supplement: resData.attachList },
            })
          }
          if (isRe) {
            // 总账户流程进度
            this.setAccountNowType(1)
            this.$refs.submitInformationRef.resubmitType = false
          }
        } else if (isRe) {
          // 驳回重新回显数据
          this.redisSubmitPayGetDataFun()
        }
      })
    },
    // 处理提交成功的详情接口数据
    disposeDetail(datas) {
      if (datas) {
        const obj1 = {}
        const obj2 = {}
        const obj3 = {}
        for (const item of this.arr1) {
          if (item === 'enterpriseOwner') {
            // 法人是否受益人转译
            if (datas.enterpriseOwner) {
              obj1[item] = 1
            } else {
              obj1[item] = 0
            }
          } else {
            obj1[item] = datas[item]
          }
        }
        for (const item of this.arr2) {
          obj2[item] = datas[item]
        }
        for (const item of this.arr3) {
          obj3[item] = datas[item]
        }

        // 开通中进度
        if (datas.level) {
          this.$store.commit('setvirtualAccountObj', {
            key: 'accountActive',
            value: Number(datas.level - 1),
          })
        }
        // 基本信息
        this.$store.commit('setvirtualAccountObj', {
          key: 'basicInformation',
          value: obj1,
        })
        // 联系人信息
        this.$store.commit('setvirtualAccountObj', {
          key: 'contactInformation',
          value: obj2,
        })
        // 结算卡信息
        this.$store.commit('setvirtualAccountObj', {
          key: 'statementInformation',
          value: obj3,
        })
        // 处理已提交的附件信息
        if (datas.mergePayAttachList && datas.mergePayAttachList.length) {
          this.$store.commit('setvirtualAccountObj', {
            key: 'mergePayAttachList',
            value: datas.mergePayAttachList,
          })
        }
        // 保存提交成功详情数据
        this.$store.commit('setvirtualAccountObj', {
          key: 'subbmitDetail',
          value: datas,
        })
        // 总账户流程进度
        this.setAccountNowType(2)
      }
    },
    // 资料审批被驳回后重新提交操作
    redisSubmitPayFun() {
      // 有保存过数据的回显接口
      this.hlbRedisEchoPayFun('reSubmit')
    },
    // 驳回重新回显数据
    redisSubmitPayGetDataFun() {
      const orderNo = this.virtualAccountObj.subbmitDetail.orderNo
      redisSubmitPay(orderNo).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const obj1 = {}
          const obj2 = {}
          const obj3 = {}
          for (const item of this.arr1) {
            if (item === 'enterpriseOwner') {
              // 法人是否受益人转译
              if (resData.enterpriseOwner) {
                obj1[item] = 1
              } else {
                obj1[item] = 0
              }
            } else {
              obj1[item] = resData[item]
            }
          }
          for (const item of this.arr2) {
            obj2[item] = resData[item]
          }
          for (const item of this.arr3) {
            obj3[item] = resData[item]
          }

          // 开通中进度
          if (resData.level) {
            this.$store.commit('setvirtualAccountObj', {
              key: 'accountActive',
              value: Number(resData.level - 1),
            })
          }
          // 基本信息
          this.$store.commit('setvirtualAccountObj', {
            key: 'basicInformation',
            value: obj1,
          })
          // 联系人信息
          this.$store.commit('setvirtualAccountObj', {
            key: 'contactInformation',
            value: obj2,
          })
          // 结算卡信息
          this.$store.commit('setvirtualAccountObj', {
            key: 'statementInformation',
            value: obj3,
          })
          // 资质补充
          if (resData.attachList && resData.attachList.length) {
            this.$store.commit('setvirtualAccountObj', {
              key: 'supplementObj',
              value: { sType: false, supplement: resData.attachList },
            })
          }
          // 总账户流程进度
          this.setAccountNowType(1)
          this.$refs.submitInformationRef.resubmitType = false
        }
      })
    },
    // 附件上传失败回显所需重新上传信息
    attachmentRetransmissionFun() {
      const mergePayId = this.virtualAccountObj.subbmitDetail.id
      attachmentRetransmission(mergePayId).then(({ data }) => {
        const { data: resData } = data
        if (data.success && resData.length) {
          // 开通中进度
          this.$store.commit('setvirtualAccountObj', {
            key: 'accountActive',
            value: 1,
          })
          // 资质补充
          this.$store.commit('setvirtualAccountObj', {
            key: 'supplementObj',
            value: { sType: true, supplement: resData },
          })
          // 总账户流程进度
          this.setAccountNowType(1)
          this.$refs.submitInformationRef.resubmitType = false
        }
      })
    },
    // 获取我的账户数据
    getMyAccount() {
      // 总账户流程进度
      this.setAccountNowType(3)
    },
  },
}
</script>

<style lang="scss" scoped>
.account-all-box {
  width: 100%;
  height: 100%;
  // border-radius: 8px;
  // background-color: rgba(255, 255, 255, 1);
}
</style>
