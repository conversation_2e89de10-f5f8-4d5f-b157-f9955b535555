<template>
  <div class="progress-bar">
    <div class="header-title">开通电子账户</div>
    <div class="long-string" />
    <div class="article-steps-box">
      <el-steps
        :active="virtualAccountObj.accountActive"
        finish-status="success"
        align-center
      >
        <el-step title="完善开户信息"></el-step>
        <el-step title="上传资质图片"></el-step>
        <!-- <el-step title="提交"></el-step> -->
      </el-steps>
    </div>
  </div>
</template>

<script>
// import { getGoodPriceDetail } from '@/api/commodity/commoditylist'
import { mapGetters } from 'vuex'
export default {
  name: 'progressBar',
  data() {
    return {
      // isSave: true,
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.progress-bar {
  width: 100%;
  height: 167px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 1);
  padding: 20px;
  box-sizing: border-box;
  margin: 0 7px 0;

  .header-title {
    height: 22px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    font-family: SourceHanSansSC-regular;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .long-string {
    width: 100%;
    border: 1px solid rgba(233, 235, 239, 100);
    border-bottom: none;
    margin-bottom: 29px;
  }

  .article-steps-box {
    width: 95%;
    margin: 0 auto 8px;
  }
}
</style>
