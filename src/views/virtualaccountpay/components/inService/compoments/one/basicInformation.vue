<template>
  <div class="basic-information-box">
    <h1 class="title">基本信息</h1>
    <BasicFormInformation ref="basicFormInformationRef" />
  </div>
</template>

<script>
import BasicFormInformation from '../../../components/basicFormInformation.vue'
export default {
  name: 'basicInformation',
  components: { BasicFormInformation },
  methods: {
    setData() {
      this.$refs.basicFormInformationRef.setData()
    },
  },
}
</script>

<style lang="scss" scoped>
.basic-information-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 16px 7px 0;

  .title {
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    margin: 0 0 10px;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
}
</style>
