<template>
  <div class="supplement-box">
    <h1 class="title">资质补充</h1>
    <div class="account-for-details">
      <div
        class="file-upload-container"
        v-for="item in supplementArr"
        :key="item.id"
      >
        <span>{{ item.name }}:</span>
        <div
          :class="{
            'file-upload-box-container': !look,
            'file-upload-box-Unactiv-container': look,
          }"
        >
          <el-upload
            class="file-upload"
            drag
            :limit="1"
            action="/api/blade-resource/oss/endpoint/put-file-kv"
            :on-success="
              (response, file, fileList) => {
                return handleUpSuccess(response, file, fileList, item)
              }
            "
            :on-remove="
              (file, fileList) => {
                return handleFileRemove(file, fileList, item)
              }
            "
            :on-preview="handleFilePreview"
            :on-exceed="handleFileExceed"
            :file-list="item.fileListData"
            :disabled="look"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              只能上传jpg/jpeg/png/pdf文件，且不超过2M
            </div>
          </el-upload>
        </div>
      </div>
    </div>

    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import FilePreview from '@/components/file-preview'
import { mapGetters } from 'vuex'
export default {
  name: 'supplement',
  components: { FilePreview },
  data() {
    return {
      look: false,
      supplementArr: [],
      pdfSrc: '',
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  created() {
    const arr = [
      {
        id: 1,
        name: '营业执照',
        ctype: 'BUSINESS_LICENSE',
        fileList: [],
        fileListData: [],
      },
      {
        id: 2,
        name: '开户许可证（或对公户开户证明）',
        ctype: 'PERMIT_FOR_BANK_ACCOUNT',
        fileList: [],
        fileListData: [],
      },
      {
        id: 3,
        name: '法人身份证正面（国徽面）',
        ctype: 'FRONT_OF_ID_CARD',
        fileList: [],
        fileListData: [],
      },
      {
        id: 4,
        name: '法人身份证反面（人像面）',
        ctype: 'BACK_OF_ID_CARD',
        fileList: [],
        fileListData: [],
      },
      {
        id: 5,
        name: '门头照',
        ctype: 'SIGN_BOARD',
        fileList: [],
        fileListData: [],
      },
      {
        id: 6,
        name: '室内场景照',
        ctype: 'INTERIOR_PHOTO',
        fileList: [],
        fileListData: [],
      },
    ]
    this.supplementArr = arr
  },
  mounted() {
    this.getData()
  },
  methods: {
    setData() {
      const dObj = this.virtualAccountObj.supplementObj
      const arr = []
      if (!dObj.sType) {
        for (const item of this.supplementArr) {
          let urlD = ''
          let nameD = ''
          let fileArr = []
          if (item.fileList.length) {
            fileArr = item.fileList
          } else if (item.fileListData.length) {
            fileArr = item.fileListData
          }
          if (fileArr.length) {
            urlD = fileArr[0].url
            nameD = fileArr[0].name
          }
          arr.push({
            credentialType: item.ctype,
            credentialUrl: urlD,
            name: nameD,
          })
        }
      } else {
        for (const item of this.supplementArr) {
          let urlD = ''
          let nameD = ''
          let fileArr = []
          if (item.fileList.length) {
            fileArr = item.fileList
          } else if (item.fileListData.length) {
            fileArr = item.fileListData
          }
          if (fileArr.length) {
            urlD = fileArr[0].url
            nameD = fileArr[0].name
          }
          arr.push({
            key: item.ctype,
            url: urlD,
            name: nameD,
            credentialType: item.credentialType,
            id: item.id,
            mergePayId: item.mergePayId,
            value: item.name,
          })
        }
      }
      this.$store.commit('setvirtualAccountObj', {
        key: 'supplementObj',
        value: { sType: dObj.sType, supplement: arr },
      })
    },
    getData() {
      const dObj = this.virtualAccountObj.supplementObj
      // 正常回显
      if (!dObj.sType && dObj.supplement.length) {
        for (const item of this.supplementArr) {
          for (const childrenItem of dObj.supplement) {
            if (
              item.ctype === childrenItem.credentialType &&
              childrenItem.credentialUrl
            ) {
              item.fileListData = [
                {
                  name: childrenItem.name,
                  url: childrenItem.credentialUrl,
                },
              ]
              break
            }
          }
        }
      } else if (dObj.sType && dObj.supplement.length) {
        this.supplementArr = []
        // 附件上传失败回显
        for (const item of dObj.supplement) {
          this.supplementArr.push({
            id: item.id,
            name: item.value,
            ctype: item.key,
            mergePayId: item.mergePayId,
            credentialType: item.credentialType,
            fileList: [],
            fileListData: [
              {
                name: item.name,
                url: item.url,
              },
            ],
          })
        }
      }
    },
    handleUpSuccess(response, file, fileList, item) {
      const arr = []
      for (const itemed of fileList) {
        if (itemed.response) {
          const { data = {} } = itemed.response
          arr.push({
            name: data.name,
            url: data.url,
            // attachId: data.attachId,
          })
        } else {
          arr.push({
            name: itemed.name,
            url: itemed.url,
          })
        }
      }
      item.fileList = arr
    },
    handleFileRemove(file, fileList, item) {
      item.fileList = fileList
      item.fileListData = fileList
    },
    handleFilePreview(file) {
      let targetUrl = ''
      if (file.response) {
        targetUrl = file.response.data.url
      } else if (file.url) {
        targetUrl = file.url
      }
      if (targetUrl) {
        if (targetUrl.endsWith('.pdf')) {
          this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
        } else {
          const imgSrcArr = []
          imgSrcArr.push({ url: targetUrl })
          this.$ImagePreview(imgSrcArr, 0, {
            closeOnClickModal: true,
          })
        }
      }
    },
    handleFileExceed() {
      this.$message.warning('最多只能上传一份文件')
    },
  },
}
</script>

<style lang="scss" scoped>
.supplement-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 16px 7px 0;

  .title {
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    margin: 0 0 -20px;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }

  .account-for-details {
    padding: 10px;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(525px, auto));
    grid-template-rows: repeat(auto-fill, minmax(270px px, auto));

    .file-upload-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;

      & > span:first-child {
        width: 349px;
        height: 21px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .file-upload {
        width: 360px;
      }

      .view-demo {
        margin-left: 22px;
      }

      .file-upload-box-container {
        display: flex;

        ::v-deep {
          .el-upload-list__item {
            background-color: #f5f7fa;
          }
          .el-upload-list__item .el-progress {
            display: none;
          }
        }
      }

      .file-upload-box-Unactiv-container {
        display: flex;

        ::v-deep {
          .el-upload-list__item {
            background-color: #f5f7fa;
          }
          .el-upload-list__item .el-progress {
            display: none;
          }
          .el-upload-dragger {
            background-color: #ebebeb;
            cursor: not-allowed;
          }
          .el-upload__text {
            color: #a7a7a7;
          }
        }
      }
    }
  }
}
</style>
