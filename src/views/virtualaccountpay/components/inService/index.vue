<template>
  <div class="in-service-box">
    <!-- 进度条 -->
    <ProgressBar />
    <!-- 第一步 -->
    <template v-if="virtualAccountObj.accountActive === 0">
      <!-- 基本信息 -->
      <BasicInformation ref="basicInformationRef" />
      <!-- 联系人信息 -->
      <ContactInformation ref="contactInformationRef" />
      <!-- 结算卡信息 -->
      <StatementInformation ref="statementInformationRef" />
    </template>
    <!-- 第二步 -->
    <template v-else-if="virtualAccountObj.accountActive === 1">
      <!-- 资质补充 -->
      <Supplement ref="SupplementRef" />
    </template>
    <p style="padding-bottom: 116px" />
    <!-- 脚脚 -->
    <div class="footer-container">
      <template v-if="virtualAccountObj.accountActive === 0">
        <span class="backBtn" @click="saveBtnFun">保 存</span>
        <span class="nextBtn btn-bg-color-blue" @click="nextBtnFun">
          下一步
        </span>
      </template>
      <template v-if="virtualAccountObj.accountActive === 1">
        <span
          class="backBtn"
          @click="lastBtnFun"
          v-if="this.virtualAccountObj.subbmitDetail.status !== 3"
          >上一步</span
        >
        <span
          class="nextBtn btn-bg-color-blue"
          @click="saveBtnFun"
          v-if="this.virtualAccountObj.subbmitDetail.status !== 3"
          >保 存</span
        >
        <el-button
          type="success"
          class="nextBtn btn-bg-color-green"
          :loading="subbmitType"
          @click="nextBtnFun"
        >
          提 交
        </el-button>
      </template>
    </div>
  </div>
</template>

<script>
import ProgressBar from './compoments/progressBar.vue'
import BasicInformation from './compoments/one/basicInformation.vue'
import ContactInformation from './compoments/one/contactInformation.vue'
import StatementInformation from './compoments/one/statementInformation.vue'
import Supplement from './compoments/two/supplement.vue'
import { mapGetters } from 'vuex'
import { hlbSave, hlbSubmit, hlbSubmitAttachResubmit } from '@/api/openAccount'

export default {
  name: 'inService',
  components: {
    ProgressBar,
    BasicInformation,
    ContactInformation,
    StatementInformation,
    Supplement,
  },
  data() {
    return {
      // 第一步三个信息，用于校验
      formArr: [
        {
          o: 'basicInformationRef',
          two: '$refs',
          t: 'basicFormInformationRef',
          f: '$refs',
          five: 'form1',
        },
        {
          o: 'contactInformationRef',
          two: '$refs',
          t: 'contactFormInformationRef',
          f: '$refs',
          five: 'form2',
        },
        {
          o: 'statementInformationRef',
          two: '$refs',
          t: 'statementFormInformationRef',
          f: '$refs',
          five: 'form3',
        },
      ],
      // 提交按钮加载状态
      subbmitType: false,
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  methods: {
    // 设置开通中步骤条进度
    setAccountActive(num) {
      this.$store.commit('setvirtualAccountObj', {
        key: 'accountActive',
        value: num,
      })
    },
    // 三个信息填写表单校验
    threeValidate() {
      let resultArr = []
      let _this = this
      let result = null
      this.formArr.forEach(formName => {
        result = new Promise((resolve, reject) => {
          _this.$refs[formName.o][formName.two][formName.t][formName.f][
            formName.five
          ].validate((valid, done) => {
            if (valid) {
              done()
              resolve()
            } else {
              reject()
            }
          })
        })
        resultArr.push(result)
      })
      Promise.all(resultArr)
        .then(() => {
          // 调用子组件保存方法
          for (const item of this.formArr) {
            this.$refs[item.o].setData()
          }
          // 提交
          this.submit1()
        })
        .catch(() => {
          this.$message.warning('请完善信息！')
          return
        })
    },
    // 三个信息提交
    submit1() {
      this.oneSaveFun1()
    },
    // 提交上传补充资料校验
    datumValidate() {
      const sta = this.virtualAccountObj.subbmitDetail.status
      this.$refs.SupplementRef.setData()
      for (const item of this.virtualAccountObj.supplementObj.supplement) {
        if (!item.credentialUrl && !item.url) {
          this.$message.warning('请上传完成资质补充资料')
          return
        }
      }
      if (sta === 3) {
        // 商户进件-上传失败的附件 重新提交
        this.hlbSubmitAttachResubmitFun()
      } else {
        this.submit2()
      }
    },
    // 补充资料提交
    submit2() {
      this.oneSaveFun2()
    },
    // 第一步保存函数
    oneSaveFun1(types) {
      for (const item of this.formArr) {
        this.$refs[item.o].setData()
      }
      const data = Object.assign(
        {},
        this.virtualAccountObj.basicInformation,
        this.virtualAccountObj.contactInformation,
        this.virtualAccountObj.statementInformation,
        { attachList: this.virtualAccountObj.supplementObj.supplement },
        { level: types ? 1 : 2 } // 第一步保存
      )
      hlbSave(data).then(({ data }) => {
        if (data.success) {
          if (types && types === 1) {
            this.$message.success('保存成功')
          } else if (types && types === 2) {
            // 新增账户跳转前进行保存
            this.$router.push('/bill/billbankcarda')
          } else {
            // 进度加一
            this.setAccountActive(1)
          }
        }
      })
    },
    // 第二步保存函数
    oneSaveFun2(types) {
      if (types) {
        this.$refs.SupplementRef.setData()
      }
      const data = Object.assign(
        {},
        this.virtualAccountObj.basicInformation,
        this.virtualAccountObj.contactInformation,
        this.virtualAccountObj.statementInformation,
        { attachList: this.virtualAccountObj.supplementObj.supplement },
        { level: 2 } // 第二步保存
      )
      hlbSave(data).then(({ data }) => {
        if (data.success) {
          if (types && types === 1) {
            this.$message.success('保存成功')
          } else {
            this.subbmitType = true
            // 统合数据完全的提交
            this.allSubbmitFun()
          }
        }
      })
    },
    // 统合数据完全的提交
    allSubbmitFun() {
      const data = Object.assign(
        {},
        this.virtualAccountObj.basicInformation,
        this.virtualAccountObj.contactInformation,
        this.virtualAccountObj.statementInformation,
        { attachList: this.virtualAccountObj.supplementObj.supplement }
      )
      hlbSubmit(data)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('提交成功')
            this.subbmitType = false
            // 刷新详情接口回显数据
            this.$parent.getData()
            // 进度加一
            // this.$parent.setAccountNowType(2)
          }
        })
        .catch(() => {
          this.subbmitType = false
        })
    },
    // 商户进件-上传失败的附件 重新提交
    hlbSubmitAttachResubmitFun() {
      const data = this.virtualAccountObj.supplementObj.supplement
      hlbSubmitAttachResubmit(data)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('提交成功')
            this.subbmitType = false
            // 刷新详情接口回显数据
            this.$parent.getData()
          }
        })
        .catch(() => {
          this.subbmitType = false
        })
    },
    // 上一步
    lastBtnFun() {
      this.setAccountActive(0)
    },
    saveBtnFun() {
      // 调用子组件保存方法
      const plan = this.virtualAccountObj.accountActive
      switch (plan) {
        case 0:
          this.oneSaveFun1(1)
          break
        case 1:
          this.oneSaveFun2(1)
          break
      }
      // this.$message.success('保存成功')
    },
    nextBtnFun() {
      const plan = this.virtualAccountObj.accountActive
      switch (true) {
        // 第一步
        case plan === 0:
          this.threeValidate()
          break
        // 第二步
        case plan === 1:
          this.datumValidate()
          break
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.in-service-box {
  .footer-container {
    position: fixed;
    height: 68px;
    width: calc(100% - 267px);
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-left: 8px;
    bottom: 0;
    // right: 15px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    & > * {
      margin-right: 18px;

      &:last-child {
        margin-right: 0;
      }
    }

    .backBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      border: 1px solid rgba(187, 187, 187, 100);
      cursor: pointer;
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      padding: 0;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }

    .btn-bg-color-blue {
      background-color: rgba(18, 119, 255, 100);
    }

    .btn-bg-color-green {
      background-color: #1cc374;
    }
  }
}
</style>
