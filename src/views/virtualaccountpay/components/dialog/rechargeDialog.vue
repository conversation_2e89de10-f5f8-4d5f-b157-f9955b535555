<template>
  <div class="second-confirmation-dialog">
    <el-dialog
      title="充值"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      width="30%"
    >
      <div>
        <div class="tip">
          提示：对公金额少于10.01不支持网银支付，对私金额少于0.11不支持网银支付
        </div>
        <div class="tip">
          手续费（元）：{{Fee | formatMoney}}
        </div>
        <avue-form ref="form1" :option="option" v-model="form"></avue-form>
      </div>
      <!--  -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleCountersign">确 定</el-button>
      </span>
    </el-dialog>
    <InternetBankHLB ref="internetBankHLBRef" />
  </div>
</template>

<script>
// import { getGoodPriceDetail } from '@/api/commodity/commoditylist'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import {rechargePay,queryProductOnlinePayFee} from "@/api/openAccount/index"
import InternetBankHLB from '../internetBankHLB.vue'
export default {
  name: 'secondConfirmationDialog',
  data() {
    return {
      dialogVisible: false,
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: []
      },
      Fee:0,
      signType:true
    }
  },
  created(){
    this.option.column = [
          {
            label: '充值金额',
            prop: 'amount',
            type: 'number',
            span: 24,
            placeholder: '请输入充值金额',
            rules: [
              {
                required: true,
                validator: this.validateMoney,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '付款类型',
            prop: 'business',
            type: 'select',
            span: 24,
            placeholder: '请选择付款类型',
            control:async val =>{
                if(val){
                    const {data:{data}} = await getDictionary(val)
                    const selectTheManagementArr = data.map(item=>{
                        return {
                           label: item.dictValue,
                           value:item.dictKey
                        }
                    })
                     this.option.column[2].dicData = selectTheManagementArr
                }
            },
            rules: [
              {
                required: true,
                message: '请选择付款类型',
                trigger: 'change',
              },
            ],
            dicData: [
              {
                label: '对公',
                value: 'wangyin_pay_b2b',
              },
              {
                label: '对私',
                value: 'wangyin_pay_b2c',
              },
            ],
          },
          {
            label: '银行',
            prop: 'bank',
            type: 'select',
            span: 24,
            placeholder: '请选择银行',
            rules: [
              {
                required: true,
                message: '请选择银行',
                trigger: 'change',
              },
            ],
            dicData:[]
          },
        ]
  },
  components:{
    InternetBankHLB
  },
  watch:{
   form:{
    deep:true,//true为进行深度监听,false为不进行深度监听
		handler(newVal){
			if(newVal.amount && newVal.business){
        let dataForm ={}
         if(newVal.business == 'wangyin_pay_b2b'){
          dataForm.business = 'B2B'
        }else{
          dataForm.business = 'B2C'
        }
        queryProductOnlinePayFee({amount:newVal.amount,business:dataForm.business}).then(({data:{data}})=>{
          this.Fee = data.transferFee
        })
      }
		}
   }
  },
  methods: {
    handleCountersign() {
       this.$refs.form1.validate(async (valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0].message
          if (!errMsg) {
            errMsg = Object.values(msg)[0][0].message
            if (!errMsg) {
              errMsg = '必填项未填'
            }
          }
          this.$message.error(errMsg)
          return
        }
        done()
        let dataForm = JSON.parse(JSON.stringify(this.form))
        if(dataForm.business == 'wangyin_pay_b2b'){
          dataForm.business = 'B2B'
        }else{
          dataForm.business = 'B2C'
        }
        dataForm.callbackUrl  = window.location.href

        const {data:{data} } =  await rechargePay(dataForm)
        this.$refs.internetBankHLBRef.internetBankToPay(data)
        this.$refs.form1.resetFields()

      })
    },
    handleClose() {

      this.dialogVisible = false
    },
    handleOpen() {
      this.dialogVisible = true
      this.$refs.form1.resetFields()
    },
    validateMoney(rule, value, callback){
        if(value < 10.01 && this.form.business == 'wangyin_pay_b2b'){
          callback(new Error('对公金额不能小于10.01'))
        }else if (value < 0.11) {
          callback(new Error('最低金额不能小于0.11'))
        } else{
            callback()
        }
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top: 0px;
}
.second-confirmation-dialog {
  .tip {
    padding-bottom: 30px;
    color: red;
    font-size: 12px;
  }
  .text {
    color: rgba(16, 16, 16, 1);
    font-size: 18px;
    margin-left: 3px;
    font-family: SourceHanSansSC-regular;
  }
}
</style>
