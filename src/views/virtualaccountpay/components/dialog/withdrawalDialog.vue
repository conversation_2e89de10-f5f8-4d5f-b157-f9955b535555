<template>
  <div class="second-confirmation-dialog">
    <el-dialog
      title="提现"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      before-close="handleClose"
      width="30%"
    >
      <div>
        <div class="accountText">
          <div class="left">可提现金额:</div>
          <div class="right">￥{{ total }}</div>
        </div>
        <!-- <div class="accountText">
          <div class="left">提现银行卡:</div>
          <div class="right">￥{{ total }}</div>
        </div> -->
        <div class="accountText">
          <div class="left">手续费:</div>
          <div class="right">￥{{ Fee | formatMoney }}</div>
        </div>
        <avue-form
          ref="form1"
          :option="option"
          v-model="information"
        ></avue-form>
      </div>
      <!--  -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleCountersign">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  transferSettlement,
  queryProductSettlementFee,
} from '@/api/openAccount/index'
import md5 from 'js-md5'

export default {
  name: 'secondConfirmationDialog',
  data() {
    return {
      dialogVisible: false,
      information: {},
      Fee: 0,
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [
          {
            label: '提现金额',
            prop: 'settleAmount',
            span: 24,
            placeholder: '请输入提现金额',
            rules: [
              {
                required: true,
                validator: this.validateMoney,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '密码',
            prop: 'payPassword',
            type: 'password',
            showPassword: true,
            span: 24,
            placeholder: '请输入密码',
            rules: [
              {
                required: true,
                message: '请输入密码',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
    }
  },
  props: {
    total: {
      type: String,
      require: true,
    },
  },
  watch: {
    'information.settleAmount': {
      async handler(val) {
        const { data: { data }, } = await queryProductSettlementFee(val)
        this.Fee = data.transferFee
      }
    },
  },
  methods: {
    handleCountersign() {
      this.$refs.form1.validate(async (valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0].message
          if (!errMsg) {
            errMsg = Object.values(msg)[0][0].message
            if (!errMsg) {
              errMsg = '必填项未填'
            }
          }
          this.$message.error(errMsg)
          return
        }
        done()

        const {
          data: { code, data, msg: resMsg },
        } = await transferSettlement({
          settleAmount: this.information.settleAmount,
          payPassword: md5(this.information.payPassword),
        })
        if (code == 200) {
          this.$message.success('提现成功')
          this.$emit('transfer')
          this.dialogVisible = false
        } else {
          this.$message.error(resMsg)
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleOpen() {
      this.dialogVisible = true
      this.Fee = 0
      this.information.settleAmount = ''
      this.information.payPassword = ''
    },
    validateMoney(rule, value, callback) {
      if (!value) {
        callback(new Error('提现金额不能为空'))
      } else if (value > this.total) {
        callback(new Error('提现金额不能大于可提现金额'))
      } else if (value <= 0) {
        callback(new Error('提现金额不能为0'))
      } else {
        callback()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.accountText {
  margin-bottom: 10px;
  box-sizing: border-box;
  padding: 0 25px;
  font-size: 14px !important;
  color: #606266;
  display: flex;
  .left {
    width: 100px;
    text-align: right;
    margin-right: 12px;
  }
  .right {
    color: #333;
    font: bold;
  }
}
</style>
