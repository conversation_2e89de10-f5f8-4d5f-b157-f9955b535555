<template>
  <div class="second-confirmation-dialog">
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      width="30%"
    >
      <p>
        <svg-icon
          icon-class="icon-tixing1"
          style="fill: #e29836; font-size: 18px"
        />
        <span class="text">此操作将取消开户申请, 是否继续?</span>
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleCountersign">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { getGoodPriceDetail } from '@/api/commodity/commoditylist'
export default {
  name: 'secondConfirmationDialog',
  data() {
    return {
      dialogVisible: false,
    }
  },
  methods: {
    handleCountersign() {
      this.dialogVisible = false
      this.$store.commit('setvirtualAccountObj', {
        key: 'isSave',
        value: false,
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleOpen() {
      this.dialogVisible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.second-confirmation-dialog {
  .text {
    color: rgba(16, 16, 16, 1);
    font-size: 18px;
    margin-left: 3px;
    font-family: SourceHanSansSC-regular;
  }
}
</style>
