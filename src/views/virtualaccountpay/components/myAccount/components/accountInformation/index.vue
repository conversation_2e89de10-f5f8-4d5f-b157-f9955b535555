<template>
  <div class="account-information-box">
    <h1 class="title">账户信息</h1>
    <div class="tabBar-box" v-if="tabList.length">
      <div
        class="tabBar-for-box"
        :class="{ 'active-box': activeName === item.value }"
        v-for="item in tabList"
        :key="item.value"
        @click="activeName = item.value"
      >
        {{ item.laber }}
      </div>
    </div>
    <!-- 基本信息 -->
    <MyBasicInformation
      v-show="activeName === 'first'"
      ref="subbasicInformationRef"
    />
    <!-- 联系人信息 -->
    <MyContactInformation
      v-show="activeName === 'second'"
      ref="contactInformationRef"
    />
    <!-- 结算卡信息 -->
    <MyStatementInformation
      v-show="activeName === 'third'"
      ref="statementInformationRef"
    />
    <!-- 账户设置 -->
    <MyAccountSetting
      v-show="activeName === 'fourth'"
      ref="statementInformationRef"
    />
  </div>
</template>

<script>
import MyBasicInformation from './components/myBasicInformation.vue'
import MyContactInformation from './components/myContactInformation.vue'
import MyStatementInformation from './components/myStatementInformation.vue'
import MyAccountSetting from './components/myAccountSetting.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'accountOverview',

  components: {
    MyBasicInformation,
    MyContactInformation,
    MyStatementInformation,
    MyAccountSetting,
  },
  data() {
    return {
      tabList: [
        { laber: '基本信息', value: 'first' },
        { laber: '联系人信息', value: 'second' },
        { laber: '结算卡信息', value: 'third' },
        { laber: '账户设置', value: 'fourth' },
      ],
      activeName: 'fourth',
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.account-information-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 15px 7px 0;

  .title {
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    margin: 0 0 10px;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }

  .tabBar-box {
    width: 40%;
    margin-top: 20px;
    display: flex;
    border: 1px solid rgba(105, 124, 255, 100);
    border-radius: 6px;
    overflow: hidden;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border-right: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;
      font-size: 14px;
      color: #449bfc;

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
        border-right: none;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }
    .active-box {
      background-color: #449bfc;
      color: #fff;

      &:hover {
        background-color: #449bfc;
      }
    }
  }
}
</style>
