<template>
  <div class="my-basic-information-box">
    <BasicFormInformation ref="basicFormInformationRef" :myDisabled="true" />
  </div>
</template>

<script>
import BasicFormInformation from '../../../../components/basicFormInformation.vue'
export default {
  name: 'myBasicInformation',
  components: { BasicFormInformation },
  data() {
    return {}
  },
  methods: {
    setData() {
      this.$refs.basicFormInformationRef.setData()
    },
  },
}
</script>

<style lang="scss" scoped>
.my-basic-information-box {
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  margin-top: 18px;
}
</style>
