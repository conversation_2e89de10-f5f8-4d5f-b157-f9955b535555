<template>
  <div class="my-contact-information-box">
    <ContactFormInformation
      ref="contactFormInformationRef"
      :myDisabled="true"
    />
  </div>
</template>

<script>
import ContactFormInformation from '../../../../components/contactFormInformation.vue'
export default {
  name: 'myContactInformation',
  components: { ContactFormInformation },
  data() {
    return {}
  },
  methods: {
    setData() {
      this.$refs.contactFormInformationRef.setData()
    },
  },
}
</script>

<style lang="scss" scoped>
.my-contact-information-box {
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  margin-top: 18px;
}
</style>
