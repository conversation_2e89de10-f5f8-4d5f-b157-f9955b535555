<template>
  <div class="my-statement-information-box">
    <StatementFormInformation
      ref="statementFormInformationRef"
      :myDisabled="true"
      :nonApplication="true"
    />
  </div>
</template>

<script>
import StatementFormInformation from '../../../../components/statementFormInformation.vue'
export default {
  name: 'myStatementInformation',
  components: { StatementFormInformation },
  data() {
    return {
      activeNames: [],
      changeType: false,
    }
  },
  methods: {
    setData() {
      this.$refs.statementFormInformationRef.setData()
    },
  },
}
</script>

<style lang="scss" scoped>
.my-statement-information-box {
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  margin-top: 18px;
}
</style>
