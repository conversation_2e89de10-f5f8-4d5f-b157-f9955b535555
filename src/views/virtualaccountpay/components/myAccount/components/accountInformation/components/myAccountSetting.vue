<template>
  <div class="my-account-setting-box">
    <div class="card-change-box">
      <div
        class="card-change-box_left"
        :class="{ 'card-change-box_active': cardActive === 1 }"
        @click="cardActive = 1"
      >
        <img src="/img/bg/card.png" alt="card" />
        <span>银行卡</span>
      </div>
      <div
        class="card-change-box_right"
        :class="{ 'card-change-box_active': cardActive === 2 }"
        @click="cardActive = 2"
      >
        <img src="/img/bg/pay.png" alt="pay" />
        <span>支付安全</span>
      </div>
    </div>
    <template v-if="cardActive === 1">
      <div class="bank-card">
        <div class="bank-card_infore">
          <span class="bank-card_infore_bank-name">中国工商银行</span>
          <span class="bank-card_infore_bank-num">**** **** **** 9044</span>
        </div>
      </div>
      <div class="bank-change-card" @click="changeBank">更换银行卡</div>
    </template>
    <template v-else-if="cardActive === 2">
      <div class="payment-settings" @click="changePassword">
        <div class="payment-settings_top">
          <span class="payment-settings_text">修改支付密码</span>
          <span class="payment-settings_operate">
            去修改
            <svg-icon
              icon-class="icon-youjiantou"
              style="fill: #1277ff; font-size: 20px"
            />
          </span>
        </div>
      </div>
      <div class="payment-settings" @click="retrievePassword">
        <div class="payment-settings_bottom">
          <span class="payment-settings_text">忘记支付密码</span>
          <span class="payment-settings_operate">
            找回密码
            <svg-icon
              icon-class="icon-youjiantou"
              style="fill: #1277ff; font-size: 20px"
            />
          </span>
        </div>
      </div>
    </template>
    <!-- 修改支付密码弹窗 -->
    <ChangepasswordDialog ref="changepasswordDialogRef" />
  </div>
</template>

<script>
import ChangepasswordDialog from './dialog/changepasswordDialog.vue'
export default {
  name: 'myAccountSetting',
  components: { ChangepasswordDialog },
  data() {
    return {
      cardActive: 1,
    }
  },
  methods: {
    // 变更账号
    changeBank() {
      //
    },
    // 修改支付密码
    changePassword() {
      this.$refs.changepasswordDialogRef.handleOpen()
    },
    // 找回密码
    retrievePassword() {
      //
    },
  },
}
</script>

<style lang="scss" scoped>
.my-account-setting-box {
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  margin-top: 18px;

  .card-change-box {
    display: flex;
    justify-content: space-around;
    align-items: center;

    &_left,
    &_right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 506px;
      height: 195px;
      background-color: rgba(147, 210, 243, 0.12);
      color: rgba(16, 16, 16, 1);
      font-size: 14px;
      text-align: center;
      border: 1px solid transparent;
      cursor: pointer;

      &:hover {
        border: 1px solid rgba(18, 119, 255, 1);
      }

      img {
        object-fit: cover;
      }

      span {
        height: 30px;
        color: rgba(16, 16, 16, 1);
        font-size: 20px;
      }
    }

    &_left {
      margin-right: 2%;
    }

    &_left img {
      width: 105px;
      height: 87px;
      margin-bottom: 1px;
    }

    &_right img {
      width: 80px;
      height: 80px;
    }

    &_active {
      border: 1px solid rgba(18, 119, 255, 1);
    }
  }

  .bank-card {
    margin-top: 20px;

    &_infore {
      width: 100%;
      line-height: 80px;
      border-radius: 4px;
      background-color: rgba(153, 153, 153, 0.08);
      text-align: center;

      &_bank-name {
        color: rgba(16, 16, 16, 1);
        font-size: 18px;
        margin-right: 10%;
      }

      &_bank-num {
        color: rgba(16, 16, 16, 1);
        font-size: 18px;
      }
    }
  }

  .bank-change-card {
    width: 100%;
    line-height: 80px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.08);
    text-align: center;
    border: 1px solid rgba(187, 187, 187, 0.41);
    color: rgba(16, 16, 16, 1);
    font-size: 18px;
    margin-top: 20px;
    cursor: pointer;
  }

  .payment-settings {
    &_top,
    &_bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      line-height: 80px;
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 0.08);
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 0.41);
      margin-top: 20px;
      padding: 0 28px;
      box-sizing: border-box;
      cursor: pointer;
    }

    &_text {
      color: rgba(16, 16, 16, 1);
      font-size: 18px;
    }

    &_operate {
      color: rgba(18, 119, 255, 1);
      font-size: 18px;
    }
  }
}
</style>
