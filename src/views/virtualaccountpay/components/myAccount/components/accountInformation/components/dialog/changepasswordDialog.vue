<template>
  <div class="change-password-dialog">
    <el-dialog
      title="修改支付密码"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      width="30%"
    >
      <div class="av-form-box">
        <avue-form ref="form" :option="option" v-model="form"></avue-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="notarize">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'changepasswordDialog',
  data() {
    return {
      dialogVisible: false,
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        column: [],
      },
    }
  },
  created() {
    this.option.column = [
      {
        label: '输入原密码',
        prop: 'name',
        span: 24,
        row: true,
        rules: [
          {
            required: true,
            message: '请输入原密码',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '输入新密码',
        prop: 'payPassword',
        type: 'password',
        span: 24,
        row: true,
        placeholder: '请输入六位数密码',
        rules: [
          {
            required: true,
            validator: this.validateNum,
            trigger: 'blur',
          },
        ],
      },
      {
        label: '确认新密码',
        prop: 'oldpassword',
        type: 'password',
        span: 24,
        row: true,
        placeholder: '请重新输入支付密码',
        rules: [
          {
            required: true,
            validator: this.validateNum1,
            trigger: 'blur',
          },
        ],
      },
    ]
  },
  methods: {
    notarize() {
      // 提交校验
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          done()
          this.handleClose()
        } else {
          return false
        }
      })
    },
    // 密码输入框校验
    validateNum(rule, value, callback) {
      // let regName = /[^\d.]/g
      // 只输入数字
      let regName = /^(0|[1-9][0-9]*)$/
      // 最高只能输入6位数
      let regName1 = /^\d{6}$/
      // 最少输入6位数
      let regName2 = /^\d{6,}$/
      if (value === '') {
        callback(new Error('请输入六位数密码'))
      } else if (!regName.test(value)) {
        callback(new Error('请确认只输入了数字'))
      } else if (!regName1.test(value)) {
        callback(new Error('请确认输入了六位数'))
      } else if (!regName2.test(value)) {
        callback(new Error('请确认输入了六位数'))
      } else {
        callback()
      }
    },
    // 确认密码输入框校验
    validateNum1(rule, value, callback) {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.payPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    },
    validate() {},
    handleClose() {
      this.dialogVisible = false
    },
    handleOpen() {
      this.dialogVisible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.change-password-dialog {
  .av-form-box {
    padding: 0 40px;
    box-sizing: border-box;
  }
  ::v-deep {
    .avue-form__menu {
      display: none;
    }
  }
}
</style>
