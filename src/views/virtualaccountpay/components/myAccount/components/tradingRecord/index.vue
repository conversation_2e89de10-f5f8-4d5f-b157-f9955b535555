<template>
  <div class="trading-record-box">
    <h1 class="title">交易记录</h1>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="processStatus" @change="handleTabButton">
            <el-radio-button
              v-for="item in [
                { label: '全部', value: 0 },
                { label: '申请中', value: 2 },
                { label: '待确认', value: 3 },
                { label: '待出库', value: 4 },
                { label: '待收货', value: 5 },
                { label: '已作废', value: 6 },
              ]"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </template> -->
      <template slot-scope="{ row }" slot="tradeInOut">
        <span>{{ row.tradeInOut == 1 ? '收入' : '支出' }}</span>
      </template>
    </avue-crud>
  </div>
</template>

<script>
import { list } from '@/api/openAccount/index'
import { mapGetters } from 'vuex'

export default {
  name: 'tradingRecord',
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: '350px',
        menuWidth: 280,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        selection: true,
        dialogClickModal: false,
        column: [],
      },
      data: [],
      // 页面自定义数据
      processStatus: 0,
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  created() {
    this.option.column = [
      {
        label: '交易单号',
        prop: 'orderId',
        search: true,
      },
      {
        label: '融资编号',
        prop: 'financeNo',
      },
      {
        label: '支付流水号',
        prop: 'flowNo',
        search: true,
        searchLabelWidth:100,
        rules: [
          {
            required: true,
            message: '请输入支付流水号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '付款账户',
        prop: 'billAccountName',
      },
      {
        label: '付款账号',
        prop: 'billAccountNo',
      },
      {
        label: '收支金额',
        prop: 'amount',
      },
      {
        label: '实际收支金额',
        prop: 'realAmount',
      
      },
      {
        label: '手续费',
        prop: 'serviceFee',
       
      },
      {
        label: '交易类型',
        prop: 'tradeType',
        search: true,
        type:'select',
        dataType: 'string',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=transfer_trade_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '收支类型',
        prop: 'tradeInOut',
       
      },
      {
        label: '额度订单状态',
        prop: 'status',
        type:'select',
        dataType: 'string',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=transfer_quota_status',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },

      },
      {
        label: '失败理由',
        prop: 'failReason',
      },
      {
        label: '交易时间',
        prop: 'orderCreateTime',
      },
      
    ]
  },
  methods: {
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      list(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    //页面点击支付状态事件方法
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
  },
}
</script>

<style lang="scss" scoped>
.trading-record-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 14px 7px 0;

  .title {
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    margin: 0 0 10px;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }

  .order-header-container {
    border: none;
  }

  .goods-container {
    display: flex;
    align-items: center;

    .goods-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 6px 0 6px 12px;
      width: 100%;
      height: 72px;

      .goods-name {
        font-size: 14px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #0a1f44;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }

      .goods-type-container {
        .goods-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8a94a6;
          line-height: 20px;
        }

        .goods-type-value {
          font-size: 14px;
          font-family: SFProText-Medium, SFProText;
          font-weight: 500;
          color: #53627c;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
