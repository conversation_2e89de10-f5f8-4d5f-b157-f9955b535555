<template>
  <div>
    <div class="account-overview-box">
      <h1 class="title">账户总览</h1>
      <div class="account-box">
        <div class="account-left-box">
          <div class="left-account-box">
            <p class="left-account-title">账户总额（元）</p>
            <div class="left-account-amount">
              {{ account.total | formatMoney }}
            </div>
            <div class="operating-button">
              <span class="withdraw" @click="upAccount">更新余额</span>
              <span class="withdraw" @click="withdrawBtnFun">提 现</span>
              <span class="top-up btn-bg-color-blue" @click="topUpBtnFun">
                充 值
              </span>
            </div>
          </div>
          <span class="long-string" />
        </div>
        <div class="account-right-box">
          <div class="right-account-for-box">
            <p class="right-account-title">可用余额（元）</p>
            <div class="right-account-amount">
              {{ account.availableAmount | formatMoney }}
            </div>
          </div>
          <div class="right-account-for-box">
            <p class="right-account-title">累计充值（元）</p>
            <div class="right-account-amount">
              {{ account.rechargePayAmount | formatMoney }}
            </div>
          </div>
          <div class="right-account-for-box mt">
            <p class="right-account-title">冻结金额（元）</p>
            <div class="right-account-amount">
              {{ account.frozenAmount | formatMoney }}
            </div>
          </div>
          <div class="right-account-for-box mt">
            <p class="right-account-title">累计提现（元）</p>
            <div class="right-account-amount">
              {{ account.settleAmount | formatMoney }}
            </div>
          </div>
        </div>
      </div>
      <rechargeDialog ref="rechargeDialogRef"></rechargeDialog>
      <withdrawalDialog
        ref="withdrawalDialogRef"
        :total="account.total"
        @transfer="queryAccount"
      ></withdrawalDialog>
    </div>
    <div class="trading-record-box">
      <h1 class="title">交易记录</h1>
      <avue-crud
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <!-- <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="processStatus" @change="handleTabButton">
            <el-radio-button
              v-for="item in [
                { label: '全部', value: 0 },
                { label: '申请中', value: 2 },
                { label: '待确认', value: 3 },
                { label: '待出库', value: 4 },
                { label: '待收货', value: 5 },
                { label: '已作废', value: 6 },
              ]"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </template> -->
        <template slot-scope="{ row }" slot="tradeInOut">
          <span>{{ row.tradeInOut == 1 ? '收入' : '支出' }}</span>
        </template>
      </avue-crud>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import rechargeDialog from '../../../dialog/rechargeDialog.vue'
import withdrawalDialog from '../../../dialog/withdrawalDialog.vue'
import { queryAccount, list } from '@/api/openAccount/index'
export default {
  name: 'accountOverview',
  data() {
    return {
      account: {
        total: 0,
        availableAmount: 0,
        frozenAmount: 0,
        rechargePayAmount: 0,
        settleAmount: 0,
      },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: '350px',
        menuWidth: 280,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        selection: true,
        dialogClickModal: false,
        column: [],
      },
      data: [],
      // 页面自定义数据
      processStatus: 0,
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  components: {
    rechargeDialog,
    withdrawalDialog,
  },
  created() {
    this.queryAccount()
    this.option.column = [
      {
        label: '交易单号',
        prop: 'orderId',
        search: true,
      },
      {
        label: '融资编号',
        prop: 'financeNo',
      },
      {
        label: '支付流水号',
        prop: 'flowNo',
        search: true,
        searchLabelWidth: 100,
        rules: [
          {
            required: true,
            message: '请输入支付流水号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '付款账户',
        prop: 'billAccountName',
      },
      {
        label: '付款账号',
        prop: 'billAccountNo',
      },
      {
        label: '收支金额',
        prop: 'amount',
      },
      {
        label: '实际收支金额',
        prop: 'realAmount',
      },
      {
        label: '手续费',
        prop: 'serviceFee',
      },
      {
        label: '交易类型',
        prop: 'tradeType',
        search: true,
        type: 'select',
        dataType: 'string',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=transfer_trade_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '收支类型',
        prop: 'tradeInOut',
      },
      {
        label: '额度订单状态',
        prop: 'status',
        type: 'select',
        dataType: 'string',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=transfer_quota_status',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '失败理由',
        prop: 'failReason',
      },
      {
        label: '交易时间',
        prop: 'orderCreateTime',
      },
    ]
  },
  methods: {
    topUpBtnFun() {
      this.$refs.rechargeDialogRef.handleOpen()
    },
    async upAccount() {},
    async queryAccount() {
      this.handleTabButton()
      const {
        data: { code, data, msg },
      } = await queryAccount()
      if (code != 200) {
        this.$message({
          type: 'fail',
          message: msg,
        })
      } else {
        if (data) {
          this.account = data
        }
      }
    },
    withdrawBtnFun() {
      this.$refs.withdrawalDialogRef.handleOpen()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      list(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    //页面点击支付状态事件方法
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
  },
}
</script>

<style lang="scss" scoped>
.account-overview-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 2px 7px 0;

  .title {
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    margin: 0 0 10px;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }

  .account-box {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    height: 115px;

    .account-left-box {
      flex: 1;
      display: flex;
      align-items: center;
      height: 100%;

      .left-account-box {
        width: 100%;

        .left-account-title {
          color: rgba(125, 125, 125, 1);
          font-size: 14px;
          text-align: center;
        }

        .left-account-amount {
          color: rgba(240, 5, 5, 1);
          font-size: 36px;
          text-align: center;
          margin-top: -2px;
        }

        .operating-button {
          display: flex;
          justify-content: center;
          margin-top: 5px;

          .withdraw {
            width: 80px;
            height: 30px;
            line-height: 30px;
            border-radius: 4px;
            color: rgba(0, 7, 42, 100);
            font-size: 14px;
            text-align: center;
            font-family: Microsoft Yahei;
            border: 1px solid rgba(187, 187, 187, 100);
            cursor: pointer;
            margin-right: 15%;
          }

          .top-up {
            width: 80px;
            height: 30px;
            line-height: 30px;
            border-radius: 4px;
            color: rgba(255, 255, 255, 100);
            font-size: 14px;
            text-align: center;
            padding: 0;
            font-family: Microsoft Yahei;
            cursor: pointer;
          }

          .btn-bg-color-blue {
            background-color: rgba(18, 119, 255, 100);
          }
        }
      }
      .long-string {
        display: inline-block;
        height: 100%;
        border: 1px solid rgba(187, 187, 187, 0.23);
        border-right: none;
      }
    }

    .account-right-box {
      flex: 2;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;

      .right-account-for-box {
        width: 50%;
        text-align: center;

        .right-account-title {
          color: rgba(125, 125, 125, 1);
          font-size: 14px;
          text-align: center;
        }

        .right-account-amount {
          color: rgba(18, 119, 255, 1);
          font-size: 25px;
          text-align: center;
        }
      }

      .mt {
        margin-top: 25px;
      }
    }
  }
}
.trading-record-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 14px 7px 0;

  .title {
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    margin: 0 0 10px;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }

  .order-header-container {
    border: none;
  }

  .goods-container {
    display: flex;
    align-items: center;

    .goods-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 6px 0 6px 12px;
      width: 100%;
      height: 72px;

      .goods-name {
        font-size: 14px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #0a1f44;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }

      .goods-type-container {
        .goods-type {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8a94a6;
          line-height: 20px;
        }

        .goods-type-value {
          font-size: 14px;
          font-family: SFProText-Medium, SFProText;
          font-weight: 500;
          color: #53627c;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
