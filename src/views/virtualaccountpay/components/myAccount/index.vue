<template>
  <div class="my-information-box">
    <!-- 当前账户状态 -->
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title value="我的账户"></avue-title>
          </div>
        </avue-affix>
        <div class="apply-container">
          <div class="left">
            <div class="form-item">
              <span class="title">子商户ID：</span>
              <span class="value">{{
                virtualAccountObj.subbmitDetail.merchantNo
              }}</span>
            </div>
            <div class="form-item">
              <span class="title">账户名：</span>
              <span class="value">{{
                virtualAccountObj.subbmitDetail.accountName
              }}</span>
            </div>
            <div class="form-item">
              <span class="title">账户类型：</span>
              <span class="value">{{
                virtualAccountObj.subbmitDetail.merchantType
              }}</span>
            </div>
            <div class="form-item">
              <span class="title">开户时间：</span>
              <span class="value">{{
                virtualAccountObj.subbmitDetail.createTime && virtualAccountObj.subbmitDetail.createTime.split(' ')[0]
              }}</span>
            </div>
          </div>
          <div class="right">
            <div class="right-icon-box">
              <SvgIcon
                :icon-class="
                  getInvoiceIconClass(virtualAccountObj.myAccountData.status)
                "
                style="font-size: 40px"
                :style="`fill: ${getTitleStyle(
                  virtualAccountObj.myAccountData.status
                )}`"
              ></SvgIcon>
              <span class="status-text">{{
                getTitleText(virtualAccountObj.myAccountData.status)
              }}</span>
            </div>
            <div class="desc">
              <!-- <div>
                {{ virtualAccountObj.myAccountData.remark }}
              </div> -->
            </div>
          </div>
        </div>
      </template>
    </basic-container>
    <!-- 账户总览 -->
    <AccountOverview />
    <!-- 交易记录 -->
    <!-- <TradingRecord /> -->
    <!-- 账户信息 -->
    <AccountInformation />
    <!--  -->
    <p style="padding-bottom: 45px" />
    <!-- <p style="padding-bottom: 116px" /> -->
    <!-- 脚脚 -->
    <!-- <div class="footer-container">
      <template>
        <el-button class="backBtn"> 返回 </el-button>
      </template>
    </div> -->
  </div>
</template>

<script>
import AccountOverview from './components/accountOverview/index.vue'
// import TradingRecord from './components/tradingRecord/index.vue'
import AccountInformation from './components/accountInformation/index.vue'
import { getTitleText, getInvoiceIconClass, getTitleStyle } from './config'
import { mapGetters } from 'vuex'

export default {
  name: 'myAccount',
  components: {
    AccountOverview,
    // TradingRecord,
    AccountInformation,
  },
  data() {
    return {}
  },
  mounted(){
    // console.log(this.virtualAccountObj)
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  methods: {
    getTitleText,
    getInvoiceIconClass,
    getTitleStyle,
  },
}
</script>

<style lang="scss" scoped>
.my-information-box {
  .header {
    width: 100%;
    height: 50px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 10px;
    margin: 0 -20px;
  }

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 130px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .status-text {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-weight: bold;
        margin-top: 10px;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;

        .des-time {
          color: #ff5656;
        }
      }
    }
  }

  .footer-container {
    position: fixed;
    height: 68px;
    width: calc(100% - 267px);
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-left: 8px;
    bottom: 0;
    // right: 15px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    & > * {
      margin-right: 18px;

      &:last-child {
        margin-right: 0;
      }
    }

    .backBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: #1277ff;
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      border: 1px solid #1277ff;
      padding: 0;
      cursor: pointer;
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      padding: 0;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }

    .btn-bg-color-blue {
      background-color: rgba(18, 119, 255, 100);
    }

    .btn-bg-color-green {
      background-color: #1cc374;
    }
  }
}
</style>
