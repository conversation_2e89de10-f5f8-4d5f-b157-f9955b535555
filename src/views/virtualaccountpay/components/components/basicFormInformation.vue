<template>
  <div class="basic-form-information-box">
    <div class="border-boxs">
      <avue-form ref="form1" :option="option" v-model="form">
        <template slot="enterpriseOwner">
          <el-radio-group
            :disabled="myDisabled"
            v-model="form.enterpriseOwner"
            @change="handleRadioChange"
          >
            <el-radio-button :label="1">是</el-radio-button>
            <el-radio-button :label="0">否</el-radio-button>
          </el-radio-group>
        </template>
      </avue-form>
    </div>
  </div>
</template>

<script>
// import { getGoodPriceDetail } from '@/api/commodity/commoditylist'
import axios from '@/router/axios'
import { mapGetters } from 'vuex'
let DIC = {
  NOID: [
    {
      label: '居民身份证',
      value: 'IDCARD',
    },
    {
      label: '护照',
      value: 'PASSPORT',
    },
    {
      label: '士兵证',
      value: 'SOLDIERSCERTIFICATE',
    },
    {
      label: '军官证',
      value: 'OFFICERSCERTIFICATE',
    },
    {
      label: '香港居民来往内地通行证',
      value: 'GATXCERTIFICATE',
    },
    {
      label: '台湾同胞来往内地通行证',
      value: 'TWNDCERTIFICATE',
    },
    {
      label: '澳门来往内地通行证',
      value: 'MACAOCERTIFICATE',
    },
  ],
}
export default {
  name: 'basicFormInformation',
  data() {
    return {
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 200,
        gutter: 50,
        column: [],
      },
    }
  },
  props: {
    // 是否禁用编辑
    myDisabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    // 是否非申请阶段，控制差异字段显影
    nonApplication: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  created() {
    this.option.column = [
      {
        label: '企业全称',
        prop: 'signName',
        type: 'input',
        span: 12,
        placeholder: '请输入企业全称',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入企业全称',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '组织机构代码',
        prop: 'orgNum',
        type: 'input',
        span: 12,
        placeholder: '请输入组织机构代码',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入组织机构代码',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '营业执照号',
        prop: 'businessLicense',
        type: 'input',
        span: 12,
        placeholder: '请输入营业执照号',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入营业执照号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '收款账户展示名',
        prop: 'showName',
        type: 'input',
        span: 12,
        placeholder: '请输入收款账户展示名',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入收款账户展示名',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '行业编码',
        prop: 'industryTypeCode',
        type: 'select',
        span: 12,
        placeholder: '请选择行业编码',
        disabled: this.myDisabled,
        // display: !this.nonApplication,
        display: false,
        rules: [
          {
            required: false,
            message: '请选择行业编码',
            trigger: 'change',
          },
        ],
      },
      {
        label: '经营类别',
        prop: 'merchantCategory',
        type: 'select',
        span: 12,
        placeholder: '请选择经营类别',
        // disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: false,
            message: '请选择经营类别',
            trigger: 'change',
          },
        ],
      },
      {
        label: '经营类别',
        prop: 'merchantCategory',
        type: 'input',
        span: 12,
        //placeholder: '请选择经营类别',
        // disabled: this.myDisabled,
        //display: false,
      },
      {
        label: '行业类型编码',
        prop: 'industryTypeCode',
        type: 'input',
        span: 12,
      },
      {
        label: '所属地区',
        prop: 'city',
        type: 'cascader',
        span: 12,
        placeholder: '请选择所属地区',
        disabled: this.myDisabled,
        lazy: true,
        rules: [
          {
            required: true,
            validator: this.validateCity,
            trigger: 'change',
          },
        ],
        props: {
          label: 'title',
          value: 'value',
        },
        dataType: 'string',
        lazyLoad(node, resolve) {
          let stop_level = 2
          let level = node.level
          let data = node.data || {}
          let code = data.value
          let list = []
          let callback = () => {
            resolve(
              (list || []).map(ele => {
                return Object.assign(ele, {
                  leaf: level >= stop_level,
                })
              })
            )
          }
          if (level == 0) {
            axios
              .get(`/api/blade-system/region/lazy-tree?parentCode=00`)
              .then(res => {
                list = res.data.data
                callback()
              })
          }
          if (level == 1) {
            axios
              .get(`/api/blade-system/region/lazy-tree?parentCode=${code}`)
              .then(res => {
                list = res.data.data
                callback()
              })
          } else if (level == 2) {
            axios
              .get(`/api/blade-system/region/lazy-tree?parentCode=${code}`)
              .then(res => {
                list = res.data.data
                callback()
              })
          } else {
            callback()
          }
        },
      },
      {
        label: '区县编码',
        prop: 'regionCode',
        type: 'input',
        span: 12,
        placeholder: '无需输入，自动带入',
        disabled: true,
        // display: !this.nonApplication,
        display: false,
        // rules: [
        //   {
        //     required: true,
        //     message: '请输入区县编码',
        //     trigger: 'change',
        //   },
        // ],
      },
      {
        label: '通讯地址',
        prop: 'address',
        type: 'input',
        span: 12,
        placeholder: '请输入通讯地址',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            validator: this.validateAddress,
            trigger: 'blur',
          },
        ],
      },
      {
        label: '法人名称',
        prop: 'legalPerson',
        type: 'input',
        span: 12,
        placeholder: '请输入法人名称',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入法人名称',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '法人证件类型',
        prop: 'idType',
        type: 'select',
        span: 12,
        placeholder: '请选择法人证件类型',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请选择法人证件类型',
            trigger: 'change',
          },
        ],
        dicData: DIC.NOID,
        dataType: 'string',
      },
      {
        label: '法人证件号',
        prop: 'legalPersonID',
        type: 'input',
        span: 12,
        placeholder: '请输入法人证件号',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入法人证件号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '法人证件居住地址',
        prop: 'legalPersonIdAddress',
        type: 'input',
        span: 12,
        placeholder: '请输入法人证件居住地址',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入法人证件居住地址',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '法人是否为受益人',
        prop: 'enterpriseOwner',
        // type: 'input',
        span: 12,
        // placeholder: '请选择法人是否为受益人',
        disabled: this.myDisabled,
        display: !this.nonApplication,
        rules: [
          {
            required: true,
            message: '请选择法人是否为受益人',
            trigger: 'change',
          },
        ],
      },
      {
        label: '受益人证件类型',
        prop: 'benefLegalPersonIdType',
        type: 'select',
        span: 12,
        placeholder: '请选择受益人证件类型',
        disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: true,
            message: '请选择受益人证件类型',
            trigger: 'change',
          },
        ],
        dicData: DIC.NOID,
        dataType: 'string',
      },
      {
        label: '受益人姓名',
        prop: 'benefLegalPerson',
        type: 'input',
        span: 12,
        placeholder: '请输入受益人姓名',
        disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入受益人姓名',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '受益人证件号',
        prop: 'benefLegalPersonID',
        type: 'input',
        span: 12,
        placeholder: '请输入受益人证件号',
        disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入受益人证件号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '受益人证件居住地址',
        prop: 'benefAddress',
        type: 'input',
        span: 12,
        placeholder: '请输入受益人证件居住地址',
        disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入受益人证件居住地址',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '受益人证件有效期开始时间',
        prop: 'benefIdCardStartDate',
        type: 'datetime',
        format: 'yyyy年MM月dd日',
        valueFormat: 'yyyyMMdd',
        span: 12,
        placeholder: '请选择受益人证件有效期开始时间',
        disabled: this.myDisabled,
        display: !this.nonApplication,
        rules: [
          {
            required: true,
            message: '请选择受益人证件有效期开始时间',
            trigger: 'change',
          },
        ],
      },
      {
        label: '受益人证件有效期结束时间',
        prop: 'benefIdCardEndDate',
        type: 'datetime',
        format: 'yyyy年MM月dd日',
        valueFormat: 'yyyyMMdd',
        span: 12,
        placeholder: '请选择受益人证件有效期结束时间',
        disabled: this.myDisabled,
        display: !this.nonApplication,
        rules: [
          {
            required: true,
            message: '请选择受益人证件有效期结束时间',
            trigger: 'change',
          },
        ],
      },
    ]
  },
  mounted() {
    this.getData()
  },
  methods: {
    setData() {
      if (this.form.city && this.form.city.length) {
        // this.form.city =
        //   this.form.city[0] + ',' + this.form.city[1] + ',' + this.form.city[2]
      } else {
        this.form.city = ''
      }
      // this.form.industryTypeCode = '431'
      // this.form.merchantCategory = 'FINANCE_INSURANCE'
      this.$store.commit('setvirtualAccountObj', {
        key: 'basicInformation',
        value: this.form,
      })
    },
    getData() {
      if (JSON.stringify(this.virtualAccountObj.basicInformation) !== '{}') {
        this.form = this.virtualAccountObj.basicInformation
        // if (this.form.city) {
        //   // this.form.city = this.form.city.split(',')
        // }
        if (this.nonApplication) {
          this.handleRadioChange(1)
        } else {
          this.handleRadioChange(this.form.enterpriseOwner)
        }
      }
    },
    // 法人是否为受益人切换事件
    // 法人是受益人，则无需展示受益人证件相关信息 法人不是受益人，则展示受益人相关信息
    handleRadioChange(value) {
      const arr = [
        'benefLegalPersonIdType',
        'benefLegalPerson',
        'benefLegalPersonID',
        'benefAddress',
      ]
      if (value === 1) {
        for (const item of arr) {
          this.findObject(this.option.column, item).display = false
        }
      } else if (value === 0) {
        for (const item of arr) {
          this.findObject(this.option.column, item).display = true
        }
      }
    },
    // 所属地区校验
    validateCity(rule, value, callback) {
      setTimeout(() => {
        if (!this.form.city || !this.form.city.length) {
          callback(new Error('请选择所属地区'))
        } else {
          callback()
        }
      }, 200)
    },
    // 通讯地址校验
    validateAddress(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入通讯地址'))
      } else if (value.length < 5) {
        callback(new Error('请补充详细的通讯地址'))
      } else {
        callback()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.basic-form-information-box {
  .border-boxs {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 27px;
    box-sizing: border-box;

    ::v-deep {
      .avue-form__menu {
        display: none;
      }
      .el-input.is-disabled .el-input__inner {
        color: #000;
      }
      .el-radio-group {
        display: flex;
        align-items: center;
        flex-flow: row wrap;
        margin-bottom: 12px;
      }
      .el-radio-button {
        user-select: none;
      }
      .el-radio-button__inner {
        border-radius: 29px;
        margin-right: 23px;
        border: 1px solid #eee;
        padding: 9px 36px;
      }
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        border: 1px solid #697cff !important;
        color: #697cff !important;
        box-shadow: none;
        background-color: #fff;
      }
    }
  }
}
</style>
