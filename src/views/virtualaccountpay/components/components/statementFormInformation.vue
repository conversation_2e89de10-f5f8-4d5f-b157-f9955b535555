<template>
  <div class="statement-form-information-box">
    <div class="border-boxs">
      <avue-form ref="form3" :option="option" v-model="form">
        <template slot="settleBankType">
          <el-radio-group
            :disabled="myDisabled"
            v-model="form.settleBankType"
            class="settle-bank-type"
          >
            <el-radio-button label="TOPUBLIC">对公</el-radio-button>
            <el-radio-button label="TOPRIVATE">对私</el-radio-button>
          </el-radio-group>
        </template>
        <template slot="settleMode">
          <el-radio-group
            :disabled="myDisabled"
            v-model="form.settleMode"
            class="settle-mode"
          >
            <el-radio-button label="MERCHANT">按商户结算</el-radio-button>
            <el-radio-button label="MERGE">按结算人结算</el-radio-button>
          </el-radio-group>
        </template>
      </avue-form>
    </div>

    <!-- 账户弹窗 -->
    <el-dialog
      title="选择账户"
      :visible.sync="type2"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="avue-dialog"
      width="70%"
    >
      <avue-crud
        ref="crud"
        :option="arrOption2"
        :data="arr_2"
        :search.sync="dialogSearch2"
        @row-click="rowClick2"
        @search-reset="searchReset2"
        @search-change="searchChange2"
        @size-change="sizeChangeScope2"
        @on-load="dialogOnLoad2"
        :table-loading="tableLoading2"
        :page.sync="accountPagingObj"
        @current-change="currentChangeScope2"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow2" :label="row.$index">&nbsp;</el-radio>
        </template>
        <template slot="empty">
          <div class="my-empty-box">
            <p class="text">暂无账户</p>
            <el-button type="primary" round class="add-btn" @click="handleAdd">
              新增账户
            </el-button>
          </div>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="type2 = false">取 消</el-button>
        <el-button @click="cardEngth2" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deptIdselectListAccount } from '@/api/goods/pcontrol/pinformation'
import { mapGetters } from 'vuex'
export default {
  name: 'statementFormInformation',
  data() {
    return {
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 200,
        gutter: 50,
        column: [],
      },
      // 账户弹窗
      type2: false,
      rowsData: void 0,
      arr_2: [],
      selectRow2: '',
      tableLoading2: true,
      searchChangeData2: {},
      accountPagingObj: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 30, 40, 50, 100],
        layout: 'total,sizes,pager',
      },
      arrOption2: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },
    }
  },
  props: {
    // 是否禁用编辑
    myDisabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    // 是否非申请阶段，控制差异字段显影
    nonApplication: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  computed: {
    ...mapGetters(['virtualAccountObj', 'userInfo']),
  },
  created() {
    this.option.column = [
      {
        label: '结算卡联行号',
        prop: 'bankCode',
        type: 'input',
        span: 12,
        placeholder: '请输入结算卡银行号',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入结算卡银行号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '结算卡类型',
        prop: 'settleBankType',
        // type: 'input',
        span: 12,
        // placeholder: '请选择结算卡类型',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请选择结算卡类型',
            trigger: 'change',
          },
        ],
      },
      {
        label: '结算方式',
        prop: 'settlementMode',
        type: 'select',
        span: 12,
        placeholder: '请选择结算方式',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请选择结算方式',
            trigger: 'change',
          },
        ],
        dicData: [
          {
            label: '不开通',
            value: 'NOTOPEN',
          },
          {
            label: '自动结算',
            value: 'AUTO',
          },
          {
            label: '自主结算',
            value: 'SELF',
          },
        ],
        dataType: 'string',
      },
      {
        label: '结算类型',
        prop: 'settlementPeriod',
        type: 'select',
        span: 12,
        placeholder: '请选择结算类型',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请选择结算类型',
            trigger: 'change',
          },
        ],
        dicData: [
          {
            label: 'T1(工作日隔天结算)',
            value: 'T1',
          },
          {
            label: 'D1(自然日隔天结算)',
            value: 'D1',
          },
          {
            label: 'D0(当日结算)',
            value: 'D0',
          },
        ],
        dataType: 'string',
      },
      {
        label: '结算账户',
        prop: 'accountName',
        // type: 'select',
        span: 12,
        placeholder: '请选择结算账户',
        disabled: this.myDisabled,
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        rules: [
          {
            required: false,
            message: '请选择结算账户',
            trigger: 'change',
          },
        ],
        click: () => {
          if (this.myDisabled) return
          // 打开弹窗
          this.initialize2()
          this.selectedWarehouse()
        },
      },
      {
        label: '结算账号',
        prop: 'accountNo',
        type: 'input',
        span: 12,
        placeholder: '请输入结算账号',
        disabled: this.myDisabled,
        display: this.nonApplication,
        rules: [
          {
            required: true,
            message: '请输入结算账号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '结算模式',
        prop: 'settleMode',
        // type: 'select',
        span: 12,
        // placeholder: '请选择结算模式',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请选择结算模式',
            trigger: 'change',
          },
        ],
      },
      {
        label: '设置支付密码',
        prop: 'payPassword',
        type: 'password',
        span: 12,
        placeholder: '请输入六位数密码',
        disabled: this.myDisabled,
        display: !this.nonApplication,
        rules: [
          {
            required: true,
            validator: this.validateNum,
            trigger: 'blur',
          },
        ],
      },
      {
        label: '确认支付密码',
        prop: 'oldpassword',
        type: 'password',
        span: 12,
        placeholder: '请重新输入支付密码',
        disabled: this.myDisabled,
        display: !this.nonApplication,
        rules: [
          {
            required: true,
            validator: this.validateNum1,
            trigger: 'blur',
          },
        ],
      },
    ]
    this.arrOption2.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '账户类型',
        prop: 'type',
        width: 90,
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=account_type_status',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
      },
      {
        label: '开户银行',
        prop: 'bankDeposit',
      },
      {
        label: '开户名',
        prop: 'openHouseName',
      },
      {
        label: '银行账户',
        prop: 'bankCardNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'enterpriseName',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入账户名称',
      },
    ]
  },
  mounted() {
    this.getData()

    // 将input 变成只读
    setTimeout(() => {
      const domArr = document.querySelectorAll('.el-input__inner')
      for (const item of domArr) {
        if (item.placeholder === '请选择结算账户') {
          item.readOnly = true
          item.id = 'my-corre-input-select'
          break
        }
      }
    }, 100)
  },
  methods: {
    setData() {
      this.$store.commit('setvirtualAccountObj', {
        key: 'statementInformation',
        value: this.form,
      })
    },
    getData() {
      if (
        JSON.stringify(this.virtualAccountObj.statementInformation) !== '{}'
      ) {
        this.form = this.virtualAccountObj.statementInformation
        if (this.form.payPassword) {
          this.form.oldpassword = this.form.payPassword
        }
      }
    },
    // 密码输入框校验
    validateNum(rule, value, callback) {
      // let regName = /[^\d.]/g
      // 只输入数字
      let regName = /^(0|[1-9][0-9]*)$/
      // 最高只能输入6位数
      let regName1 = /^\d{6}$/
      // 最少输入6位数
      let regName2 = /^\d{6,}$/
      if (value === '') {
        callback(new Error('请输入六位数密码'))
      } else if (!regName.test(value)) {
        callback(new Error('请确认只输入了数字'))
      } else if (!regName1.test(value)) {
        callback(new Error('请确认输入了六位数'))
      } else if (!regName2.test(value)) {
        callback(new Error('请确认输入了六位数'))
      } else {
        callback()
      }
    },
    // 确认密码输入框校验
    validateNum1(rule, value, callback) {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.payPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    },
    // 账户弹窗--弹窗清空按钮事件
    searchReset2() {
      this.selectListAccountFun()
    },
    // 账户弹窗--分页页码切换事件
    currentChangeScope2(currentPage) {
      this.accountPagingObj.currentPage = currentPage
      this.selectListAccountFun(this.searchChangeData2)
    },
    // 账户弹窗--分页页数切换事件
    sizeChangeScope2(pageSize) {
      // 分页页数切换事件
      this.accountPagingObj.currentPage = 1
      this.accountPagingObj.pageSize = pageSize
      this.selectListAccountFun(this.searchChangeData2)
    },
    // 账户弹窗--弹窗表格确认按钮
    cardEngth2() {
      if (this.rowsData) {
        // 资方
        this.form.accountName = this.rowsData.openHouseName
        // 给后端的
        this.form.bankAccountId = this.rowsData.id
        this.form.accountNo = this.rowsData.bankCardNo
      }
      this.type2 = false
    },
    // 账户弹窗--打开弹窗 选择性回显已选账户
    selectedWarehouse() {
      this.selectListAccountFun()
      this.type2 = true
    },
    // 账户弹窗--弹窗单选事件
    rowClick2(row) {
      if (this.selectRow2 !== row.$index) {
        this.selectRow2 = row.$index
        this.rowsData = row // 点击当前行数据
      }
    },
    // 账户弹窗初始化
    initialize2() {
      this.accountPagingObj.total = 0
      this.accountPagingObj.pageSize = 10
      this.accountPagingObj.currentPage = 1
      this.searchChangeData2 = {}
      this.selectRow2 = false
      this.arr_2 = []
    },
    // 账户弹窗--弹窗首次加载事件
    dialogOnLoad2() {
      // this.tableLoading2 = false
    },
    // 账户弹窗--弹窗搜索事件
    searchChange2(params, done) {
      this.searchChangeData2 = params
      this.selectListAccountFun(params)
      done()
    },
    // 选择账户弹窗-list数据接口
    selectListAccountFun(filter = {}) {
      this.tableLoading2 = true
      const params = {
        enterpriseName: filter.enterpriseName,
        bankCardNo: filter.bankCardNo,
        deptId: this.userInfo.dept_id,
        current: this.accountPagingObj.currentPage,
        size: this.accountPagingObj.pageSize,
      }
      deptIdselectListAccount(params).then(({ data }) => {
        if (data.success) {
          this.tableLoading2 = false
          const { data: resData } = data
          this.accountPagingObj.total = resData.total || 0
          if (resData.records.length) {
            this.arr_2 = resData.records
            // 反选之前已选列
            let onlyId
            const platformA = this.form.accountName
            if (platformA) {
              onlyId = platformA // 资方
            }
            for (const [index, item] of resData.records.entries()) {
              if (onlyId && item.openHouseName === onlyId) {
                this.selectRow2 = index
                break
              } else if (
                resData.records.length - 1 == index &&
                (this.selectRow2 === 0 || this.selectRow2)
              ) {
                this.selectRow2 = false
              }
            }
          } else {
            this.arr_2 = []
          }
        }
      })
    },
    // 新增账户
    handleAdd() {
      this.$parent.$parent.oneSaveFun1(2)
    },
  },
}
</script>

<style lang="scss">
// 自定义空状态
.my-empty-box {
  .text {
    color: rgba(153, 153, 153, 1);
    font-size: 14px;
    font-family: SourceHanSansSC-regular;
  }
}
</style>
<style lang="scss" scoped>
.statement-form-information-box {
  .border-boxs {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 27px;
    box-sizing: border-box;

    ::v-deep {
      .avue-form__menu {
        display: none;
      }
      .el-input.is-disabled .el-input__inner {
        color: #000;
      }
      .el-radio-group {
        display: flex;
        align-items: center;
        flex-flow: row wrap;
        margin-bottom: 12px;
      }
      .el-radio-button {
        user-select: none;
      }
      .el-radio-button__inner {
        border-radius: 29px;
        margin-right: 23px;
        border: 1px solid #eee;
      }
      .settle-bank-type .el-radio-button__inner {
        padding: 9px 36px;
      }
      .settle-mode .el-radio-button__inner {
        padding: 9px 18px;
      }
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        border: 1px solid #697cff !important;
        color: #697cff !important;
        box-shadow: none;
        background-color: #fff;
      }

      // 选择资方账户样式修改
      #my-corre-input-select {
        cursor: pointer;
      }
      .el-input__suffix {
        font-size: 16px;
        color: rgba(112, 112, 112, 100);
      }
    }
  }
}
</style>
