<template>
  <div class="contact-form-information-box">
    <div class="border-boxs">
      <avue-form ref="form2" :option="option" v-model="form"> </avue-form>
    </div>
  </div>
</template>

<script>
// import { getGoodPriceDetail } from '@/api/commodity/commoditylist'
import { mapGetters } from 'vuex'
let DIC = {
  NOID: [
    {
      label: '居民身份证',
      value: 'IDCARD',
    },
    {
      label: '护照',
      value: 'PASSPORT',
    },
    {
      label: '士兵证',
      value: 'SOLDIERSCERTIFICATE',
    },
    {
      label: '军官证',
      value: 'OFFICERSCERTIFICATE',
    },
    {
      label: '香港居民来往内地通行证',
      value: 'GATXCERTIFICATE',
    },
    {
      label: '台湾同胞来往内地通行证',
      value: 'TWNDCERTIFICATE',
    },
    {
      label: '澳门来往内地通行证',
      value: 'MACAOCERTIFICATE',
    },
  ],
}
export default {
  name: 'contactFormInformation',
  data() {
    return {
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 200,
        gutter: 50,
        column: [],
      },
    }
  },
  props: {
    // 是否禁用编辑
    myDisabled: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  created() {
    this.option.column = [
      {
        label: '联系人姓名',
        prop: 'linkman',
        type: 'input',
        span: 12,
        placeholder: '请输入联系人姓名',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入联系人姓名',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '联系人类型',
        prop: 'linkmanType',
        type: 'select',
        span: 12,
        placeholder: '请选择联系人类型',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请选择联系人类型',
            trigger: 'change',
          },
        ],
        dicData: [
          {
            label: '法人',
            value: 'LEGAL',
          },
          {
            label: '经办人',
            value: 'SUPER',
          },
        ],
        dataType: 'string',
        change: ({ value }) => {
          // 联系人类型为经办人，则展示联系人证件类型等证件信息，为法人，则不展示
          const arr = [
            'linkmanIdType',
            'linkmanIdCardStartDate',
            'linkmanIdCardEndDate',
          ]
          if (value === 'LEGAL') {
            for (const item of arr) {
              this.findObject(this.option.column, item).display = false
            }
          } else if (value === 'SUPER') {
            for (const item of arr) {
              this.findObject(this.option.column, item).display = true
            }
          }
        },
      },
      {
        label: '联系人电话',
        prop: 'linkPhone',
        type: 'input',
        span: 12,
        placeholder: '请输入联系人电话',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入联系人电话',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '联系邮箱',
        prop: 'email',
        type: 'input',
        span: 12,
        placeholder: '请输入联系邮箱',
        disabled: this.myDisabled,
        rules: [
          {
            required: true,
            message: '请输入联系邮箱',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '联系人证件类型',
        prop: 'linkmanIdType',
        type: 'select',
        span: 12,
        placeholder: '请选择联系人证件类型',
        disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: true,
            message: '请选择联系人证件类型',
            trigger: 'change',
          },
        ],
        dicData: DIC.NOID,
        dataType: 'string',
      },
      {
        label: '联系人证件有效期开始时间',
        prop: 'linkmanIdCardStartDate',
        type: 'datetime',
        format: 'yyyy年MM月dd日',
        valueFormat: 'yyyyMMdd',
        span: 12,
        placeholder: '请选择联系人证件有效期开始时间',
        disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: true,
            message: '请选择联系人证件有效期开始时间',
            trigger: 'change',
          },
        ],
      },
      {
        label: '联系人证件有效期结束时间',
        prop: 'linkmanIdCardEndDate',
        type: 'datetime',
        format: 'yyyy年MM月dd日',
        valueFormat: 'yyyyMMdd',
        span: 12,
        placeholder: '请选择联系人证件有效期结束时间',
        disabled: this.myDisabled,
        display: false,
        rules: [
          {
            required: true,
            message: '请选择联系人证件有效期结束时间',
            trigger: 'change',
          },
        ],
      },
    ]
  },
  mounted() {
    this.getData()
  },
  methods: {
    setData() {
      this.$store.commit('setvirtualAccountObj', {
        key: 'contactInformation',
        value: this.form,
      })
    },
    getData() {
      if (JSON.stringify(this.virtualAccountObj.contactInformation) !== '{}') {
        this.form = this.virtualAccountObj.contactInformation
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.contact-form-information-box {
  .border-boxs {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 27px;
    box-sizing: border-box;

    ::v-deep {
      .avue-form__menu {
        display: none;
      }
      .el-input.is-disabled .el-input__inner {
        color: #000;
      }
      .el-radio-group {
        display: flex;
        align-items: center;
        flex-flow: row wrap;
        margin-bottom: 12px;
      }
      .el-radio-button {
        user-select: none;
      }
      .el-radio-button__inner {
        border-radius: 29px;
        margin-right: 23px;
        border: 1px solid #eee;
        padding: 9px 36px;
      }
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        border: 1px solid #697cff !important;
        color: #697cff !important;
        box-shadow: none;
        background-color: #fff;
      }
    }
  }
}
</style>
