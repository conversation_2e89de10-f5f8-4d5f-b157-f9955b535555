<template>
  <div class="empty-account-box">
    <div class="contain-box">
      <img class="empty-img" src="/img/emptyAccount.png" alt="空" />
      <!-- 未开通并且没申请记录 -->
      <template v-if="!virtualAccountObj.isSave">
        <div class="information-box">
          <p class="title-i">暂未开通电子资金账户</p>
          <p class="btn-primary">
            <el-button type="primary" @click="toOpen('')">去开通</el-button>
          </p>
        </div>
      </template>
      <!-- 未开通但是有申请记录 -->
      <template v-else>
        <div class="information-box">
          <p class="title-i">开户申请信息已为您保存</p>
          <p class="btn-primary btn-primary-save">
            <!-- <el-button @click="cancel">取消开户</el-button> -->
            <el-button type="primary" @click="toOpen()">去提交</el-button>
          </p>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
// import { getGoodPriceDetail } from '@/api/commodity/commoditylist'
import { mapGetters } from 'vuex'
export default {
  name: 'emptyAccount',
  data() {
    return {
      // isSave: true,
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  methods: {
    toOpen() {
      this.$parent.setAccountNowType(1)
    },
    cancel() {
      this.$parent.secondConfirmationOpen()
    },
  },
}
</script>

<style lang="scss" scoped>
.empty-account-box {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  .contain-box {
    width: 360px;
    position: absolute;
    left: 50%;
    top: 46%;
    transform: translate(-50%, -50%);

    .empty-img {
      width: 100%;
      height: 300px;
      object-fit: cover;
    }

    .information-box {
      margin-top: -33px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .title-i {
        height: 20px;
        color: rgba(153, 153, 153, 1);
        font-size: 14px;
        text-align: center;
        font-family: SourceHanSansSC-regular;
      }
      .btn-primary {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10px;

        // 变更按钮样式
        ::v-deep {
          .el-button {
            padding: 8px 16px;
          }
        }
      }
      .btn-primary-save {
        justify-content: space-between;
        // width: 225px;
      }
    }
  }
}
</style>
