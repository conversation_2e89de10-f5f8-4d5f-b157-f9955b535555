<template>
  <div class="internet-bank-HLB">
    <!-- target="targetIfr" -->
    <form id="myForm" method="POST" :action="internetBankObj.payUrl">
      <input
        type="hidden"
        name="P1_bizType"
        :value="internetBankObj.P1_bizType"
      />
      <input
        type="hidden"
        name="P2_orderId"
        :value="internetBankObj.P2_orderId"
      />
      <input
        type="hidden"
        name="P3_customerNumber"
        :value="internetBankObj.P3_customerNumber"
      />
      <input
        type="hidden"
        name="P4_orderAmount"
        :value="internetBankObj.P4_orderAmount"
      />
      <input
        type="hidden"
        name="P5_bankId"
        :value="internetBankObj.P5_bankId"
      />
      <input
        type="hidden"
        name="P6_business"
        :value="internetBankObj.P6_business"
      />
      <input
        type="hidden"
        name="P7_timestamp"
        :value="internetBankObj.P7_timestamp"
      />
      <!-- <input
        type="hidden"
        name="P8_goodsName"
        :value="internetBankObj.P8_goodsName"
      /> -->
      <input
        type="hidden"
        name="P9_period"
        :value="internetBankObj.P9_period"
      />
      <input
        type="hidden"
        name="P10_periodUnit"
        :value="internetBankObj.P10_periodUnit"
      />
      <input
        type="hidden"
        name="P11_callbackUrl"
        :value="internetBankObj.P11_callbackUrl"
      />
      <input
        type="hidden"
        name="P12_serverCallbackUrl"
        :value="internetBankObj.P12_serverCallbackUrl"
      />
      <input
        type="hidden"
        name="P13_orderIp"
        :value="internetBankObj.P13_orderIp"
      />
      <input
        type="hidden"
        name="P14_onlineCardType"
        :value="internetBankObj.P14_onlineCardType"
      />
      <input
        type="hidden"
        name="P17_ruleJson"
        :value="internetBankObj.P17_ruleJson"
      />
      <input
        type="hidden"
        name="signatureType"
        :value="internetBankObj.signatureType"
        v-if="internetBankObj.signatureType"
      />
      <input
        type="hidden"
        name="encryptionKey"
        :value="internetBankObj.encryptionKey"
        v-if="internetBankObj.encryptionKey"
      />
      <input type="hidden" name="sign" :value="internetBankObj.sign" />
      <!-- <input type="hidden" name="P15_desc" :value="internetBankObj.P15_desc" /> -->
    </form>
    <!-- <iframe name="targetIfr" width="900px" height="900px"></iframe> -->
  </div>
</template>

<script>
export default {
  name: 'internetBankHLB',
  data(){
    return {
      internetBankObj:{}
    }
  },
  methods: {
    internetBankToPay(resData){
      this.internetBankObj = resData
      setTimeout(() => {
        document.getElementById('myForm').submit()
      }, 150)
    },
  },
}
</script>
<style lang="scss" scoped>
.internet-bank-HLB {
  color: #000;
}
</style>
