<template>
  <div class="sub-contact-information-box">
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item name="2">
        <template slot="title">
          <div class="fromHeader">
            <div class="fromLeft">
              <i
                class="el-icon-caret-bottom"
                :class="{
                  'i-active': changeType,
                }"
              ></i>
              <h1 class="fromLeft-title">
                <span>联系人信息</span>
              </h1>
            </div>
          </div>
        </template>
        <ContactFormInformation
          ref="contactFormInformationRef"
          style="margin-top: 27px"
          :myDisabled="true"
        />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import ContactFormInformation from '../../components/contactFormInformation.vue'
export default {
  name: 'subContactInformation',
  components: { ContactFormInformation },
  data() {
    return {
      activeNames: [],
      changeType: false,
    }
  },
  methods: {
    setData() {
      this.$refs.contactFormInformationRef.setData()
    },
    // 折叠面板收缩控制
    handleChange() {
      this.changeType = !this.changeType
    },
  },
}
</script>

<style lang="scss" scoped>
.sub-contact-information-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 16px 7px 0;
  overflow: hidden;

  .fromHeader {
    width: 100%;
    font-size: 16px;
    background: #fff;
    color: rgba(16, 16, 16, 100);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .fromLeft {
      display: flex;
      align-items: center;
      width: 60%;

      .fromLeft-title {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        display: flex;
        align-items: center;

        .long-string {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        .interest-rate {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .i-active {
      transform: rotate(0deg) !important;
    }
  }

  // 更改折叠面板默认样式
  ::v-deep {
    i.el-icon-caret-bottom {
      font-size: 150%;
      transform: rotate(-90deg);
      transition: transform 0.4s;
    }
    i.el-collapse-item__arrow {
      display: none;
    }
    div.el-collapse-item__header {
      height: 15px;
      border-bottom: none;
    }
    div.el-collapse-item__content {
      padding-bottom: 0 !important;
    }
    .el-collapse {
      border-top: none;
      border-bottom: none;
    }
    .el-card {
      border-radius: 8px;
      .el-collapse-item__wrap {
        border-bottom: none;
      }
    }
  }
}
</style>
