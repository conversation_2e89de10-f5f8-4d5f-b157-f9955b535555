<template>
  <div class="sign-hlb-box">
    <!-- target="targetIfr" -->
    <form id="myForm" method="POST" :action="signObj.url">
      <input
        type="hidden"
        name="interfaceName"
        :value="signObj.interfaceName"
      />
      <input type="hidden" name="body" :value="signObj.body" />
      <input type="hidden" name="sign" :value="signObj.sign" />
      <input type="hidden" name="merchantNo" :value="signObj.merchantNo" />
    </form>
    <!-- <iframe name="targetIfr" width="900px" height="900px"></iframe> -->
  </div>
</template>

<script>
import { signContract } from '@/api/openAccount'

export default {
  name: 'signhlb',
  data() {
    return {
      signObj: {},
    }
  },
  mounted() {
    // setInterval(() => {
    // const iframe = document.getElementsByTagName('iframe')[0]
    // console.log('iframe',iframe)
    // if (iframe.attachEvent) {
    //   iframe.attachEvent('onload', function () {
    //     console.log('1', 1)
    //   })
    // } else {
    //   iframe.onload = function () {
    //     console.log('2', 2)
    //   }
    // }
    // }, 1000)
  },
  methods: {
    toSign() {
      signContract()
        .then(({ data }) => {
          if (data.success) {
            const { data: resData } = data
            this.signObj = {
              url: resData.url,
              interfaceName: resData.interfaceName,
              body: resData.body,
              sign: resData.sign,
              merchantNo: resData.merchantNo,
            }
            setTimeout(() => {
              document.getElementById('myForm').submit()
            }, 150)
          }
        })
        .catch(() => {
          this.$parent.signType = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.sign-hlb-box {
  color: #000;
}
</style>
