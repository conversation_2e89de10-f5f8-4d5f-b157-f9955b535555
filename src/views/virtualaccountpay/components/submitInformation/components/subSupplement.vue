<template>
  <div
    class="sub-supplement-box"
    v-if="
      filePreviewArr.length &&
      ![0, 1, 2, 3, 4, 5].includes(virtualAccountObj.subbmitDetail.status)
    "
  >
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item name="4">
        <template slot="title">
          <div class="fromHeader">
            <div class="fromLeft">
              <i
                class="el-icon-caret-bottom"
                :class="{
                  'i-active': changeType,
                }"
              ></i>
              <h1 class="fromLeft-title">
                <span>资质补充</span>
              </h1>
            </div>
          </div>
        </template>
        <div style="margin: 17px 0 5px 0">
          <!-- 展示附件 -->
          <FilePreview :formUpload="filePreviewArr" />
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import FilePreview from './filePreview.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'subSupplement',
  components: { FilePreview },
  data() {
    return {
      activeNames: [],
      changeType: false,
      filePreviewArr: [],
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  created() {
    // const arr = [
    //   {
    //     key: 'anim deserunt non id ea',
    //     value: 'pariatur tempor irure',
    //     mergePayId: '25',
    //     url: 'http://dyfqndx.cf/viucpp',
    //     name: '其验处级指非',
    //     credentialType: 81,
    //     id: '26',
    //   },
    //   {
    //     key: 'tempor Lorem eiusmod Excepteur commodo',
    //     value: 'aliquip nulla culpa ea adipisicing',
    //     mergePayId: '71',
    //     url: 'http://iqpxtpoe.cc/pnxhklc',
    //     name: '现温品同',
    //     credentialType: 4,
    //     id: '26',
    //   },
    //   {
    //     key: 'nisi',
    //     value: 'sunt magna',
    //     mergePayId: '12',
    //     url: 'http://sjcancie.gn/zekenvs',
    //     name: '建长查八京志',
    //     credentialType: 53,
    //     id: '26',
    //   },
    //   {
    //     key: 'magna ut',
    //     value: 'id consectetur',
    //     mergePayId: '4',
    //     url: 'http://zclthiy.ev/ojx',
    //     name: '厂置象志运至',
    //     credentialType: 33,
    //     id: '89',
    //   },
    // ]
    if (this.virtualAccountObj.mergePayAttachList.length) {
      this.filePreviewArr = this.virtualAccountObj.mergePayAttachList
    }
    // else {
    //   this.filePreviewArr = arr
    // }
  },
  methods: {
    // 折叠面板收缩控制
    handleChange() {
      this.changeType = !this.changeType
    },
  },
}
</script>

<style lang="scss" scoped>
.sub-supplement-box {
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  // margin-top: 16px;
  margin: 16px 7px 0;
  overflow: hidden;

  .fromHeader {
    width: 100%;
    font-size: 16px;
    background: #fff;
    color: rgba(16, 16, 16, 100);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .fromLeft {
      display: flex;
      align-items: center;
      width: 60%;

      .fromLeft-title {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        display: flex;
        align-items: center;

        .long-string {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        .interest-rate {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .i-active {
      transform: rotate(0deg) !important;
    }
  }

  // 更改折叠面板默认样式
  ::v-deep {
    i.el-icon-caret-bottom {
      font-size: 150%;
      transform: rotate(-90deg);
      transition: transform 0.4s;
    }
    i.el-collapse-item__arrow {
      display: none;
    }
    div.el-collapse-item__header {
      height: 15px;
      border-bottom: none;
    }
    div.el-collapse-item__content {
      padding-bottom: 0 !important;
    }
    .el-collapse {
      border-top: none;
      border-bottom: none;
    }
    .el-card {
      border-radius: 8px;
      .el-collapse-item__wrap {
        border-bottom: none;
      }
    }
  }
}
</style>
