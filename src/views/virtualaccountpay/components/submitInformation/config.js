// 对应的标题
function getTitleText(state) {
  let text = ''
  if (state === 0) {
    text = '商户进件申请被驳回'
  } else if (state === 1) {
    text = '商户进件待审核'
  } else if (state === 2) {
    text = '商户进件初审通过'
  } else if (state === 3) {
    text = '附件上传失败'
  } else if (state === 4) {
    text = '附件上传中'
  } else if (state === 5) {
    text = '附件已上传'
  } else if (state === 6) {
    text = '待签署合同'
  } else if (state === 7) {
    text = '商户进件完成'
  } else if (state === 8) {
    text = '签署合同失败'
  } else if (state === 9) {
    text = '签署合同中'
  } else if (state === 10) {
    text = '代付产品-开通失败'
  } else if (state === 11) {
    text = '结算产品-开通失败'
  } else if (state === 12) {
    text = '网银产品-开通失败'
  }
  return text
}

// 对应其相关的icon
function getInvoiceIconClass(state) {
  let iconClass = ''
  switch (state) {
    case 0: // 商户进件申请被驳回
      iconClass = 'icon-jinggao1'
      break
    case 1: // 商户进件待审核
      iconClass = 'icon-dengdai1'
      break
    case 2: // 商户进件初审通过
      iconClass = 'icon-chenggong1'
      break
    case 3: // 附件上传失败
      iconClass = 'icon-delete-filling'
      break
    case 4: // 附件上传中
      iconClass = 'icon-dengdai1'
      break
    case 5: // 附件已上传
      iconClass = 'icon-chenggong1'
      break
    case 6: // 待签署合同
      iconClass = 'icon-dengdai1'
      break
    case 7: // 商户进件完成
      iconClass = 'icon-chenggong1'
      break
    case 8: // 签署合同失败
      iconClass = 'icon-delete-filling'
      break
    case 9: // 签署合同中
      iconClass = 'icon-dengdai1'
      break
    case 10: // 代付产品-开通失败
      iconClass = 'icon-delete-filling'
      break
    case 11: // 结算产品-开通失败
      iconClass = 'icon-delete-filling'
      break
    case 12: // 网银产品-开通失败
      iconClass = 'icon-delete-filling'
      break
  }
  return iconClass
}

// 对应的颜色
function getTitleStyle(state) {
  let iconStyle = ''
  switch (state) {
    case 0: // 商户进件申请被驳回
      iconStyle = '#DF9935'
      break
    case 1: // 商户进件待审核
      iconStyle = '#697CFF'
      break
    case 2: // 商户进件初审通过
      iconStyle = '#00865A'
      break
    case 3: // 附件上传失败
      iconStyle = '#FF4D4D'
      break
    case 4: // 附件上传中
      iconStyle = '#697CFF'
      break
    case 5: // 附件已上传
      iconStyle = '#00865A'
      break
    case 6: // 待签署合同
      iconStyle = '#697CFF'
      break
    case 7: // 商户进件完成
      iconStyle = '#00865A'
      break
    case 8: // 签署合同失败
      iconStyle = '#FF4D4D'
      break
    case 9: // 签署合同中
      iconStyle = '#697CFF'
      break
    case 10: // 代付产品-开通失败
      iconStyle = '#FF4D4D'
      break
    case 11: // 结算产品-开通失败
      iconStyle = '#FF4D4D'
      break
    case 12: // 网银产品-开通失败
      iconStyle = '#FF4D4D'
      break
  }

  return iconStyle
}

export { getTitleText, getInvoiceIconClass, getTitleStyle }