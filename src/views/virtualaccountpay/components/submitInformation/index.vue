<template>
  <div class="submit-information-box">
    <!-- 状态 -->
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title value="开户申请"></avue-title>
          </div>
        </avue-affix>
        <!--  -->
        <div class="apply-container">
          <div class="left">
            <div class="form-item">
              <span class="title">订单编号：</span>
              <span class="value">{{
                virtualAccountObj.subbmitDetail.orderNo
              }}</span>
            </div>
            <div class="form-item">
              <span class="title">下单时间：</span>
              <span class="value">{{
                virtualAccountObj.subbmitDetail.createTime
              }}</span>
            </div>
          </div>
          <div class="right">
            <div class="right-icon-box">
              <SvgIcon
                :icon-class="
                  getInvoiceIconClass(virtualAccountObj.subbmitDetail.status)
                "
                style="font-size: 40px"
                :style="`fill: ${getTitleStyle(
                  virtualAccountObj.subbmitDetail.status
                )}`"
              ></SvgIcon>
              <!-- getTitleText(virtualAccountObj.subbmitDetail.status) -->
              <span class="status-text">{{
                virtualAccountObj.subbmitDetail.statusName
              }}</span>
            </div>
            <div class="desc">
              <!-- <div v-if="virtualAccountObj.subbmitDetail.status === 4">
                <span>融资申请已通过，用户在</span>
                <span class="des-time"> {{ countDown }} </span>
                <span>内未申请放款，订单将自动关闭</span>
              </div>
              <div
                v-else-if="
                  [2, 6].includes(virtualAccountObj.subbmitDetail.status)
                "
              >
                <span style="color: #000">
                  [ {{ virtualAccountObj.subbmitDetail.remark }} ]
                </span>
              </div>
              <div
                v-else-if="
                  [3, 7].includes(virtualAccountObj.subbmitDetail.status)
                "
              >
                拒绝原因：
                <span style="color: #000"
                  >[ {{ virtualAccountObj.subbmitDetail.remark }} ]</span
                >
              </div>
              <div v-else> -->
              <div>
                <span
                  v-if="
                    [0, 3, 8, 10, 11, 12].includes(
                      virtualAccountObj.subbmitDetail.status
                    )
                  "
                >
                  {{ virtualAccountObj.subbmitDetail.remark }}
                </span>
                <span v-else-if="virtualAccountObj.subbmitDetail.status === 6">
                  {{ tipText }}
                </span>
                <span
                  v-if="[6, 8].includes(virtualAccountObj.subbmitDetail.status)"
                >
                  <el-button type="text" :loading="signType" @click="toSigin">
                    去签署
                  </el-button>
                </span>
              </div>
            </div>
          </div>
        </div>
        <!--  -->
        <div class="tabBar-box" v-if="tabList.length">
          <div
            class="tabBar-for-box"
            :class="{ 'active-box': activeName === item.value }"
            v-for="item in tabList"
            :key="item.value"
            @click="activeName = item.value"
          >
            {{ item.laber }}
          </div>
        </div>
      </template>
    </basic-container>
    <!--  -->
    <template v-if="activeName === 'first'">
      <!-- 基本信息 -->
      <SubBasicInformation ref="subbasicInformationRef" />
      <!-- 联系人信息 -->
      <SubContactInformation ref="contactInformationRef" />
      <!-- 结算卡信息 -->
      <SubStatementInformation ref="statementInformationRef" />
      <!-- 资质补充 -->
      <SubSupplement ref="subSupplementRef" />
    </template>
    <!--  -->
    <p style="padding-bottom: 116px" />
    <!-- 脚脚 -->
    <div class="footer-container">
      <template v-if="[0, 3].includes(virtualAccountObj.subbmitDetail.status)">
        <el-button class="backBtn" :loading="resubmitType" @click="resubmitFun">
          重新提交
        </el-button>
      </template>
      <template v-else-if="virtualAccountObj.subbmitDetail.status === 1">
        <!-- <el-button class="backBtn" :loading="cancelType" @click="canceFun">
          取消开户
        </el-button> -->
      </template>
      <template
        v-else-if="
          [10, 11, 12].includes(virtualAccountObj.subbmitDetail.status)
        "
      >
        <el-button class="backBtn" :loading="reApplyType" @click="reApplyFun"> 重新申请 </el-button>
      </template>
      <template v-else>
        <el-button class="backBtn" @click="closeFun">
          关闭
        </el-button>
      </template>
    </div>

    <SignBackUp ref="signBackUpRef" />
  </div>
</template>

<script>
import SubBasicInformation from './components/subBasicInformation.vue'
import SubContactInformation from './components/subContactInformation.vue'
import SubStatementInformation from './components/subStatementInformation.vue'
import SubSupplement from './components/subSupplement.vue'
import SignBackUp from './components/signBackUp.vue'
import { getTitleText, getInvoiceIconClass, getTitleStyle } from './config'
import { mapGetters } from 'vuex'
import { hlbProductAgainOpen } from '@/api/openAccount'

export default {
  name: 'submitInformation',
  components: {
    SubBasicInformation,
    SubContactInformation,
    SubStatementInformation,
    SubSupplement,
    SignBackUp,
  },
  data() {
    return {
      tabList: [
        // { laber: '申请信息', value: 'first' },
        // { laber: '审批进度', value: 'second' },
        // { laber: '合同信息', value: 'third' },
      ],
      tipText: '恭喜您，开户申请审核通过，请完成电子合同签署',
      activeName: 'first',
      // 重新提交按钮加载状态
      resubmitType: false,
      // 取消按钮加载状态
      cancelType: false,
      // 重新申请按钮加载状态
      reApplyType: false,
      // 签署按钮加载状态
      signType: false,
    }
  },
  computed: {
    ...mapGetters(['virtualAccountObj']),
  },
  methods: {
    getTitleText,
    getInvoiceIconClass,
    getTitleStyle,
    // 重新提交
    resubmitFun() {
      this.resubmitType = true
      const sta = this.virtualAccountObj.subbmitDetail.status
      if (sta === 0) {
        // 驳回重新回显数据
        this.$parent.redisSubmitPayFun()
      } else if (sta === 3) {
        // 附件上传失败回显所需重新上传信息
        this.$parent.attachmentRetransmissionFun()
      }
    },
    // 重新申请
    reApplyFun() {
      this.reApplyType = true
      hlbProductAgainOpen().then(({ data }) => {
        if (data.success) {
          this.$parent.getData()
          this.$message.success('提交成功')
          this.reApplyType = false
        }
      })
    },
    // 合同签署
    toSigin() {
      this.signType = true
      this.$refs.signBackUpRef.toSign()
    },
    canceFun() {
      this.$parent.secondConfirmationOpen()
    },
    closeFun() {
      this.$router.$avueRouter.closeTag()
      this.$router.push('/wel/index')
    },
  },
}
</script>

<style lang="scss" scoped>
.submit-information-box {
  .header {
    width: 100%;
    height: 50px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 10px;
    margin: 0 -20px;
  }

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 105px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .status-text {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-weight: bold;
        margin-top: 2px;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;

        .des-time {
          color: #ff5656;
        }
      }
    }
  }

  .tabBar-box {
    margin-top: 20px;
    display: flex;
    border: 1px solid rgba(105, 124, 255, 100);
    border-radius: 6px;
    overflow: hidden;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border-right: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;
      font-size: 14px;
      color: #449bfc;

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
        border-right: none;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }
    .active-box {
      background-color: #449bfc;
      color: #fff;

      &:hover {
        background-color: #449bfc;
      }
    }
  }

  .footer-container {
    position: fixed;
    height: 68px;
    width: calc(100% - 267px);
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-left: 8px;
    bottom: 0;
    // right: 15px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    & > * {
      margin-right: 18px;

      &:last-child {
        margin-right: 0;
      }
    }

    .backBtn {
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: #1277ff;
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      border: 1px solid #1277ff;
      padding: 0 10px;
      cursor: pointer;
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      padding: 0;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }

    .btn-bg-color-blue {
      background-color: rgba(18, 119, 255, 100);
    }

    .btn-bg-color-green {
      background-color: #1cc374;
    }
  }
}
</style>
