<template>
  <a-popover
    v-model:visible="checkboxData.visible"
    placement="bottom"
    trigger="click"
    overlayClassName="form-filter-container"
    @visibleChange="handleVisibleChange"
  >
    <div class="filter-switch-container">
      <span>{{ title }}</span>
      <down-outlined
        style="margin-left: 4px"
        :style="{
          color:
            checkboxData.confirmList.length > 0
              ? 'rgb(13, 85, 207)'
              : undefined,
        }"
      />
    </div>
    <template #content>
      <div class="xianzhi-check-box-d">
        <a-checkbox-group
          v-model:value="checkboxData.checkedList"
          :options="options"
        />
      </div>
      <div class="filter-control-container">
        <NButton
          class="blue button-item"
          :bordered="false"
          :disabled="checkboxData.checkedList.length === 0"
          @click="filterReset()"
          >重置</NButton
        >
        <NButton
          class="blue button-item"
          :bordered="false"
          @click="filterConfirm()"
          >确定</NButton
        >
      </div>
    </template>
  </a-popover>
</template>

<script lang="ts">
export default {
  name: 'CustomTableFilter',
}
</script>
<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { NButton } from 'naive-ui'

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  options: {
    type: Array,
    required: true,
  },
  optionValueMap: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['filterChange'])

const checkboxData: any = ref({
  visible: false,
  checkedList: [],
  confirmList: [],
})

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    checkboxData.value.checkedList = checkboxData.value.confirmList
  }
}

const filterReset = () => {
  checkboxData.value.visible = false
  checkboxData.value.checkedList = []
  checkboxData.value.confirmList = []
  emit('filterChange', [])
}

const filterConfirm = () => {
  checkboxData.value.confirmList = checkboxData.value.checkedList
  checkboxData.value.visible = false
  const filterData = []
  for (const item of checkboxData.value.confirmList) {
    // 以下 +1 是后台以 1 开始，0 为查询全部
    const index = props.options.findIndex(option => option === item) + 1
    let value = ''
    if (props.optionValueMap.length > 0) {
      value = String(props.optionValueMap[index - 1])
    } else {
      value = String(index)
    }
    filterData.push(value)
  }
  emit('filterChange', filterData)
}
</script>

<style lang="scss" scoped>
.filter-switch-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  cursor: pointer;
}
</style>
<style lang="scss">
.xianzhi-check-box-d {
  max-height: 40vh;
  overflow: hidden auto;
}

.form-filter-container {
  .ant-popover-inner-content {
    padding: 0;
  }

  .ant-checkbox-group {
    min-width: 100px;
    display: flex;
    flex-direction: column;

    .ant-checkbox-wrapper-checked {
      color: #1890ff;
      background-color: #e6f7ff;
    }

    .ant-checkbox-group-item {
      width: 100%;
      margin: 0;
      padding: 4px 12px;
      // justify-content: space-between;

      &:hover {
        background-color: #f5f5f5;
      }

      // > span:last-child {
      //   width: 100%;
      //   text-align: right;
      // }
    }
  }

  .filter-control-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    margin-top: 4px;
    padding-top: 2px;
    border-top: 1px solid #f6f6f6;
    text-align: center;
  }
}
</style>
