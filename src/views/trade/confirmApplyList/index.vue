<template>
  <div class="trade-apply-list">
    <div class="header-box">
      <UserHeader headerName="确权申请列表" noIcon />
      <LabelBar :labelList="labelMap.list" @switch="handleSwitch" />
    </div>
    <div class="content-box">
      <div class="contract-custom-search">
        <div class="search-input-container">
          <a-input
            class="search-name"
            v-model:value="searchData.name"
            placeholder="输入贸易背景名称"
            allowClear
          />
          <a-input
            class="search-task"
            v-model:value="searchData.processNo"
            placeholder="输入申请编号"
            allowClear
          />
          <a-range-picker class="search-time" v-model:value="searchData.time" />
        </div>
        <div class="search-control-container">
          <n-button class="blue border" :bordered="false" @click="handleSearch"
            >搜索</n-button
          >
          <n-button
            class="blue border"
            :bordered="false"
            @click="handleSearchReset"
            >重置</n-button
          >
        </div>
      </div>
      <template v-if="loading">
        <UserLoading :loading="loading" />
      </template>
      <div v-else-if="cardList && cardList.length > 0" class="card-list">
        <TradeCard
          v-for="item of cardList"
          :key="item.id"
          :title="item.coreName"
          :no="item.processNo"
          :processId="item.processId"
          :date="item.processTime"
          :status="item.status"
          @handleDetail="handleDetail"
          @handleCloseCause="handleCloseCause"
        />
      </div>
      <NoData
        v-else
        title="暂无数据"
        noButton
        :imgSrc="require('@/assets/images/empty.svg')"
      />
      <a-pagination
        style="margin-top: 24px"
        :current="paginationData.currentPage"
        :pageSize="paginationData.pageSize"
        :total="paginationData.total"
        :itemRender="customPaginationRender"
        :showSizeChanger="false"
        :hideOnSinglePage="true"
        @change="handlePaginationChange"
      />
    </div>
  </div>
  <ViewCloseCauseDialog
    title="查看原因"
    titleIconClass="icon-xinxi1"
    titleIconColor="#0c66ff"
    ref="viewCloseCauseDialogRef"
  />
</template>

<script lang="ts">
import { h } from 'vue'
import MySvgIcon from '@/components/MySvgIcon/index.vue'
import { NButton } from 'naive-ui'

export default {
  name: 'TradeConfirmApplyList',
}

const labelMap = {
  list: ['全部', '审批中', '已完成', '已中止'],
  key: [0, 1, 3, 2],
}

const customPaginationRender = ({
  originalElement,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  page,
  type,
}: {
  originalElement: any
  page: any
  type: any
}) => {
  if (type === 'prev') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-left custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h(MySvgIcon, { 'icon-class': 'icon-jiantou-zuo', 'font-size': '16px' }),
        h('span', {}, '上一页'),
      ]
    )
  } else if (type === 'next') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-right custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h('span', {}, '下一页'),
        h(MySvgIcon, { 'icon-class': 'icon-youjiantou1', 'font-size': '16px' }),
      ]
    )
  }
  return originalElement
}

const createInitPagData = () => ({
  pageSize: 16,
  currentPage: 1,
  maxPage: 1,
  total: 0,
})

const createInitSearchData = () => ({
  name: undefined,
  processNo: undefined,
  time: undefined,
})
</script>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRoute } from 'vue-router'
import UserHeader from '@/views/user/components/Header/index.vue'
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import dayjs from 'dayjs'
import TradeCard from './components/card/index.vue'
import { requestConfirmApplyList } from '@/api/user/trade'
import NoData from '@/views/user/components/NoData/index.vue'
import UserLoading from '@/views/user/components/UserLoading/index.vue'
import ViewCloseCauseDialog from '@/views/user/Trade/ViewCloseCauseDialog.vue'

const route = useRoute()
let currentTagIndex = 0
let enableSearch = false
let formatSearchData: {
  name: string | undefined
  processNo: string | undefined
  start: string | undefined
  end: string | undefined
} = {
  name: undefined,
  processNo: undefined,
  start: undefined,
  end: undefined,
}
const loading = ref(true)
const viewCloseCauseDialogRef: any = ref(null)
const cardList: any = ref([])
const searchData: any = reactive(createInitSearchData())
const paginationData: any = reactive(createInitPagData())

const initTableData = () => {
  Object.assign(paginationData, createInitPagData())
  loadTableData(paginationData.currentPage, paginationData.pageSize)
}

const loadTableData = (current: number, pageSize: number | undefined) => {
  loading.value = true
  // 构建搜索请求参数
  let requestObj: any = {
    current,
    size: pageSize || 16,
    status: currentTagIndex,
  }
  // 处理搜索数据
  if (enableSearch) {
    requestObj = {
      ...requestObj,
      name: formatSearchData.name,
      processNo: formatSearchData.processNo,
      start: formatSearchData.start,
      end: formatSearchData.end,
    }
  }
  requestConfirmApplyList(requestObj)
    .then(({ data }: { data: any }) => {
      loading.value = false
      if (data.success) {
        data = data.data
        paginationData.currentPage = current
        paginationData.maxPage = data.pages
        paginationData.total = data.total
        cardList.value = data.records || []
      }
    })
    .catch(() => {
      loading.value = false
    })
}

const handlePaginationChange = (currentPage: any) => {
  paginationData.current = currentPage
  loadTableData(paginationData.current, paginationData.pageSize)
}

const handleSwitch = (index: number) => {
  currentTagIndex = labelMap.key[index]
  initTableData()
}

const handleSearch = () => {
  enableSearch = true
  formatSearchData = {
    name: searchData.name,
    processNo: searchData.processNo,
    start: searchData.time
      ? dayjs(searchData.time[0]).format('YYYY-MM-DD')
      : undefined,
    end: searchData.time
      ? dayjs(searchData.time[1]).format('YYYY-MM-DD')
      : undefined,
  }
  initTableData()
}

const handleSearchReset = () => {
  enableSearch = false
  const initSearchData: any = createInitSearchData()
  for (const key in searchData) {
    searchData[key] = initSearchData[key]
  }
  initTableData()
}

const handleDetail = (processNo: string) => {}
const handleCloseCause = (processId: string) => {
  viewCloseCauseDialogRef.value.handleOpen(processId)
}

if (route.query.name) {
  searchData.name = route.query.name
  handleSearch()
} else {
  initTableData()
}
</script>

<style lang="scss" scoped>
.trade-apply-list {
  width: 1400px;
  margin: 24px auto;
  background-color: #fff;
  border-radius: 16px;

  .header-box {
    padding: 24px 24px 20px;
    border-bottom: 1px solid #f1f2f4;
  }

  .content-box {
    padding: 24px;

    .contract-custom-search {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .search-input-container {
        display: inline-block;

        > * {
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }

        .ant-input,
        .ant-picker {
          height: 40px;
          line-height: 40px;
          border-radius: 4px;
        }

        .search-name,
        .search-task {
          width: 260px;
        }

        .search-time {
          width: 240px;

          :deep(.ant-picker-input) > input {
            text-align: center;
          }
        }
      }

      .search-control-container {
        display: inline-block;

        > * {
          margin-right: 20px;
          height: 40px;
          line-height: 40px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .card-list {
      display: inline-block;
      width: 100%;
      margin-top: 24px;
      margin-bottom: -24px;

      > * {
        margin-right: 24px;
        margin-bottom: 24px;

        &:nth-child(4n) {
          margin-right: 0;
        }
      }
    }
  }

  // 分页
  :deep(.ant-pagination) {
    display: flex;
    align-items: center;
    justify-content: center;
    // 去除分页下外边距
    margin-bottom: 0;

    .ant-pagination-disabled {
      .custom-pagination-item-link {
        color: #a6aebc !important;
        border: 1px solid transparent;
      }
    }

    .custom-pagination-item-link {
      background: #f8f9fb;
      color: #53627c;
    }

    .custom-pagination-left {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px 0 12px;
      border-radius: 100px 0px 0px 100px;
    }

    .custom-pagination-right {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px 0 16px;
      border-radius: 0px 100px 100px 0px;
    }
  }
}
</style>
