<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="businessCode">
        <span v-if="row.businessName" class="line-block">{{
          row.businessName
        }}</span>
      </template>
      <template slot-scope="{ row }" slot="status">
        <span
          class="basic-block"
          :class="row.status === 1 ? 'text-color-green' : 'text-color-gray'"
          >{{
            row.status === 1 ? '启用' : row.status === 0 ? '禁用' : ''
          }}</span
        >
      </template>
      <template slot-scope="{ row, index, type, size }" slot="menu">
        <el-button
          icon="el-icon-view"
          :size="size"
          :type="type"
          @click.stop="handleDialog(row, 'Get')"
          >查看</el-button
        >
        <el-button
          icon="el-icon-edit"
          v-if="row.status === 0"
          :size="size"
          :type="type"
          @click.stop="handleDialog(row, 'Edit')"
          >编辑</el-button
        >
        <el-button
          icon="el-icon-del"
          v-if="row.status === 0"
          :size="size"
          :type="type"
          @click.stop="$refs.crud.rowDel(row, index)"
          >删除</el-button
        >
        <el-button
          icon="el-icon-top"
          v-if="row.status === 0"
          :size="size"
          :type="type"
          @click="handleDialog(row, 'Start')"
          >启用</el-button
        >
        <el-button
          icon="el-icon-bottom"
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click="handleDialog(row, 'Disabled')"
          >禁用</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getBusinessDetail,
  enableOrDisable,
  deleteRulesList,
} from '@/api/message/messagetemplaterule'

import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      loading: true,
      query: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        align: 'center',
        dialogClickModal: false,
        column: [
          {
            label: '预警规则名称',
            prop: 'ruleName',
            rules: [
              {
                required: true,
                message: '请输入产品编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '业务流程',
            prop: 'businessCode',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=business_type_message',
            slot: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '上次修改时间',
            prop: 'updateTime',
            slot: true,
          },
          // {
          //   label: '操作人',
          //   prop: 'goodsName',
          // },

          {
            label: '状态',
            prop: 'status',
            slot: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.goods_add, false),
        viewBtn: this.vaildData(this.permission.goods_view, false),
        delBtn: this.vaildData(this.permission.goods_delete, false),
        editBtn: this.vaildData(this.permission.goods_edit, false),
      }
    },
  },
  methods: {
    // 新增
    handleAdd() {
      this.$router.push({
        path: '/message/messagetemplaterule/warnRulesDispose',
      })
      sessionStorage.setItem('look', 'false')
    },
    // 操作列
    handleDialog(row, name) {
      switch (name) {
        case 'Get':
          this.$router.push({
            path: '/message/messagetemplaterule/warnRulesDispose',
            query: { id: row.id },
          })
          sessionStorage.setItem('look', 'true')
          break
        case 'Edit':
          this.$router.push({
            path: '/message/messagetemplaterule/warnRulesDispose',
            query: { id: row.id },
          })
          sessionStorage.setItem('look', 'false')
          break
        case 'Start':
          this.$confirm('确定将选择数据启用', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              return enableOrDisable({ id: row.id })
            })
            .then(() => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
            })

          break
        case 'Disabled':
          this.$confirm('确定将选择数据禁用?', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              return enableOrDisable({ id: row.id })
            })
            .then(() => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
            })

          break
      }
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return deleteRulesList({ ids: row.id })
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.selectionClear()
        this.loading = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.line-block {
  display: inline-block;
  padding: 4px 12px;
  line-height: 24px;
  color: #00072a;
  border-radius: 43px;
  font-size: 12px;
  font-weight: 500;
  background-color: #eaecf1;
}
.basic-block {
  display: inline-block;
  border-radius: 2px;
  width: 46px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  box-sizing: border-box;
}
.text-color-green {
  color: #1ac475;
  border: 1px solid #1ac475;
  background-color: #f5f5f5;
}
.text-color-gray {
  color: #84868d;
  border: 1px solid #d9d9d9;
  background-color: #f5f5f5;
}
</style>
