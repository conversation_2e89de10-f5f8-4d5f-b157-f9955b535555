<template>
  <basic-container>
    <h3>新增预警规则</h3>
    <avue-form ref="form" v-model="form" :option="option">
      <template slot="businessCode">
        <avue-select
          :disabled="look"
          v-model="form.businessCode"
          placeholder="请选择业务流程"
          :dic="businessKeyOptions"
          @change="handleBusinessChange"
        ></avue-select>
      </template>
      <template slot-scope="{ row }" slot="businessKey">
        <avue-select
          :disabled="look"
          v-model="row.businessKey"
          placeholder="请选择参数值"
          :dic="selectParams"
          @change="handleChange(row)"
        ></avue-select>
      </template>
      <template slot-scope="{ row }" slot="businessVal">
        <el-date-picker
          v-if="row.unit && row.unit.includes('时间')"
          v-model="row.businessVal"
          type="datetime"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :disabled="look"
        >
        </el-date-picker>
        <avue-input
          :disabled="look"
          v-else
          v-model="row.businessVal"
          placeholder="请输入内容"
          :append="row.unit"
        ></avue-input>
      </template>
    </avue-form>
    <BasicFooter
      :btn-options="btnOptions"
      @click="handleClickEvent"
    ></BasicFooter>
  </basic-container>
</template>

<script>
import {
  getBusiness,
  submitForm,
  getBusinessDetail,
} from '@/api/message/messagetemplaterule'
import BasicFooter from '@/components/basic-footer/index'
import { getDictionary } from '@/api/system/dictbiz'

let btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
  {
    btnName: '确定',
    funName: 'Submit',
    type: 'primary',
  },
]
export default {
  components: { BasicFooter },
  data() {
    return {
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 120,
        column: [
          {
            label: '规则名称',
            prop: 'ruleName',
            disabled: this.look,
            rules: [
              {
                required: true,
                message: '请输入规则名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '业务流程',
            prop: 'businessCode',
            type: 'select',
            formlost: true,
            rules: [
              {
                required: true,
                message: '请选择业务流程',
                trigger: 'change',
              },
            ],
          },
          {
            label: '规则配置',
            prop: 'messageTemplateConditionList',
            type: 'dynamic',
            span: 24,
            rules: [
              {
                required: true,
                message: '请选择规则配置',
              },
            ],
            children: {
              delBtn: !this.look,
              addBtn: !this.look,
              align: 'center',
              rowAdd: done => {
                done({
                  input: '默认值',
                })
              },
              rowDel: (row, done) => {
                done()
              },
              column: [
                {
                  label: '业务字段',
                  prop: 'businessKey',
                  row: true,
                },
                {
                  label: '比较符号',
                  prop: 'symbol',
                  type: 'select',
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=compare_value',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  disabled: this.look,
                },
                // {
                //   label: '数据范围',
                //   prop: 'range',
                //   type: 'select',
                //   dicData: [],
                // },
                {
                  label: '值',
                  prop: 'businessVal',
                  row: true,
                  disabled: this.look,
                },
                {
                  label: '分组',
                  prop: 'ruleGrouping',
                  disabled: this.look,
                },
              ],
            },
          },
        ],
      },
      form: {},
      selectParams: [], // 业务字段数据
      btnOptions,
      look: false,
      businessKeyOptions: [], // 业务流程数据
    }
  },
  watch: {
    'form.businessCode': {
      handler(businessCode) {
        if (businessCode) {
          this.getBusiness({ businessCode })
        }
      },
      immediate: true,
      deep: true,
    },
    look: {
      handler(bool) {
        console.log(bool)
        let list = [...btnOptions]
        if (bool) {
          list = list.filter(item => item.funName === 'Revert')
          console.log(list)
        }
        this.btnOptions = list
        console.log(this.btnOptions, 'this.btnOptions')
      },
      // immediate: true,
      deep: false,
    },
  },
  beforeCreate() {
    // 初始化的表单禁止
    const bool = sessionStorage.getItem('look') === 'true'
    this.look = bool
  },
  created() {
    this.getDictionary()
    // 自定义表单插槽
    const bool = sessionStorage.getItem('look') === 'true'
    this.look = bool
    // 详情接口
    const id = this.$route.query && this.$route.query.id
    if (id) {
      this.getBusinessDetail({ id })
    }
  },

  methods: {
    handleBusinessChange() {
      if (this.form.businessCode) {
        let obj = this.businessKeyOptions.find(
          item => item.value === this.form.businessCode
        )
        if (obj) {
          this.form.businessName = obj.label
        }
      }
    },
    // 业务字段
    handleChange(row) {
      if (row.businessKey) {
        let obj = this.selectParams.find(
          item => item.businessKey === row.businessKey
        )
        if (obj) {
          row.unit = obj.unit
        }
      }
    },
    // 按钮操作
    handleClickEvent(name) {
      let params = {}
      switch (name) {
        case 'Revert':
          this.$router.$avueRouter.closeTag()
          this.$router.push('/message/messagetemplaterule')
          break
        case 'Submit':
          params = {
            ...this.form,
            ruleName: this.form.ruleName,
            businessName: this.form.businessName,
            businessCode: this.form.businessCode,
          }
          this.submitForm(params)
          break
      }
    },
    submitForm(params) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.startLoading()
          submitForm(params)
            .then(({ data }) => {
              if (data.code === 200) {
                this.$router.$avueRouter.closeTag()
                this.$router.push('/message/messagetemplaterule')
              }
              this.endLoading()
            })
            .catch(() => {
              this.endLoading()
            })
        } else {
          this.$message.warning('完善信息')
          return false
        }
      })
    },
    getBusiness(params) {
      let list = []
      getBusiness(params).then(({ data }) => {
        const records = data.data.records || []
        if (data.code === 200) {
          for (const item of records) {
            list.push({
              ...item,
              label: item.businessKeyName,
              value: item.businessKey,
            })
          }
        }
        this.selectParams = list
      })
    },

    // 获取业务流程
    getDictionary() {
      let list = []
      getDictionary({ code: 'business_type_message' }).then(({ data }) => {
        if (data.code === 200) {
          const resData = data.data || []
          for (const item of resData) {
            list.push({ ...item, label: item.dictValue, value: item.dictKey })
          }
        }
        this.businessKeyOptions = list
      })
    },

    // 获取详情
    getBusinessDetail(params) {
      getBusinessDetail(params).then(({ data }) => {
        if (data.code === 200) {
          this.form = { ...data.data }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
h3 {
  color: #101010;
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  font-family: SourceHanSansSC-bold;
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  &::before {
    display: block;
    content: '';
    width: 8px;
    height: 16px;
    border-radius: 15px;
    background-color: #1277ff;
    margin-right: 4px;
  }
}
</style>
