<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.messagetemplate_delete"
          @click="handleDelete"
          >删 除
        </el-button>

        <el-button
          type="danger"
          size="small"
          icon="el-icon-plus"
          v-if="permissionList.addBtn"
          @click="templateShow"
          >新增
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="isSms">
        <el-tag v-if="row.isSms == 0"> 否 </el-tag>

        <el-tag
          v-if="row.isSms == 1"
          :style="{
            color: '#3b7f10',
            border: '1px solid rgb(117 234 46 / 49%)',
            background: '#c5d7ae',
          }"
        >
          是
        </el-tag>
      </template>

      <template slot-scope="{ row }" slot="isMessage">
        <el-tag v-if="row.isMessage == 0"> 否 </el-tag>

        <el-tag
          v-if="row.isMessage == 1"
          :style="{
            color: '#3b7f10',
            border: '1px solid rgb(117 234 46 / 49%)',
            background: '#c5d7ae',
          }"
        >
          是
        </el-tag>
      </template>

      <template slot-scope="{ row }" slot="isEmail">
        <el-tag v-if="row.isEmail == 0"> 否 </el-tag>

        <el-tag
          v-if="row.isEmail == 1"
          :style="{
            color: '#3b7f10',
            border: '1px solid rgb(117 234 46 / 49%)',
            background: '#c5d7ae',
          }"
        >
          是
        </el-tag>
      </template>

      <template slot="menu" slot-scope="scope">
        <el-button
          v-if="permissionList.viewBtn"
          type="text"
          size="small"
          icon="el-icon-view"
          @click="viewMessageTemplateForm(scope.row, scope.index)"
          >查看
        </el-button>
        <el-button
          v-if="permissionList.editBtn && scope.row.status == 0"
          type="text"
          size="small"
          icon="el-icon-edit"
          @click="editMessageTemplateForm(scope.row, scope.index)"
          >编辑
        </el-button>

        <el-button
          v-if="permissionList.editBtn && scope.row.status == 1"
          type="text"
          size="small"
          icon="el-icon-edit"
          @click="disableHandle(scope.row, scope.index)"
          >禁用
        </el-button>
        <el-button
          v-if="permissionList.editBtn && scope.row.status == 0"
          type="text"
          size="small"
          icon="el-icon-edit"
          @click="enableHandle(scope.row, scope.index)"
          >启用
        </el-button>
        <el-button
          v-if="permissionList.delBtn"
          type="text"
          size="small"
          icon="el-icon-edit"
          @click="rowDel(scope.row)"
          >删除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, remove } from '@/api/message/messagetemplate'
import { mapGetters } from 'vuex'
import { MESSAGE_BACK } from '@/config/apiPrefix'

let DIC_DATA = {
  IS_OPEN: [
    {
      label: '是',
      value: 1,
    },
    {
      label: '否',
      value: 0,
    },
  ],
}
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        align: 'center',
        border: true,
        index: true,
        selection: true,
        dialogClickModal: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menuWidth: '280',
        column: [
          {
            label: 'id',
            prop: 'id',
            rules: [
              {
                required: true,
                message: '请输入id',
                trigger: 'blur',
              },
            ],
            hide: true,
          },
          // {
          //   label: '模版标题',
          //   prop: 'templateName',
          // },
          {
            label: '模版类型',
            prop: 'templateTypeId',
            rules: [
              {
                required: true,
                message: '请输入模版类型',
                trigger: 'blur',
              },
            ],
            dicData: [
              { label: '确权通过', value: 1 },
              { label: '确权未通过', value: 2 },
              { label: '提交应收账款', value: 3 },
              { label: '应收账款放款成功通知', value: 4 },
              { label: '应收账款放款审核未通过', value: 5 },
              { label: '代采放款成功', value: 6 },
              { label: '代采放款审核未通过', value: 7 },
              { label: '云信融资成功', value: 8 },
              { label: '云信融资未通过', value: 9 },
              { label: '核心企业自主申请授信成功', value: 10 },
              { label: '核心企业自主申请授信未通过', value: 11 },
              { label: '融资企业申请授信成功', value: 12 },
              { label: '融资企业申请授信未通过', value: 13 },
              { label: '应收账款还款成功', value: 14 },
              { label: '应收账款还款失败', value: 15 },
              { label: '代采还款成功', value: 16 },
              { label: '代采还款失败', value: 17 },
              { label: '云信还款成功', value: 18 },
              { label: '云信还款失败', value: 19 },
              { label: '调息审核通过', value: 20 },
              { label: '调息审核未通过', value: 21 },
              { label: '减免审核通过', value: 22 },
              { label: '减免审核未通过', value: 23 },
              { label: '提前结清审核通过', value: 24 },
              { label: '提前结清审核未通过', value: 25 },
              { label: '还款到期预警', value: 26 },
              { label: '评分下调预警', value: 27 },
              { label: '商品价格波动预警', value: 28 },
              { label: '提醒赎货消息', value: 29 },
              { label: '逾期未赎货预警', value: 30 },
            ],
          },
          {
            label: '模版分类',
            prop: 'templateClassify',
            rules: [
              {
                required: true,
                message: '请输入模版分类',
                trigger: 'blur',
              },
            ],
            dicData: [
              {
                label: '通知消息',
                value: '1',
              },
              {
                label: '预警消息',
                value: '2',
              },
              {
                label: '推送消息',
                value: '3',
              },
            ],
          },
          {
            label: '模版编号',
            prop: 'templateCode',
            rules: [
              {
                required: true,
                message: '请输入模版CODE',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '短信',
            type: 'switch',
            prop: 'isSms',
            width: 60,
            rules: [
              {
                required: true,
                message: '请输入是否开启短信',
                trigger: 'blur',
              },
            ],
            dicData: DIC_DATA.IS_OPEN,
          },
          {
            label: '微信',
            prop: 'isWx',
            width: 60,
            type: 'switch',
            rules: [
              {
                required: true,
                message: '请输入是否开启微信',
                trigger: 'blur',
              },
            ],
            dicData: DIC_DATA.IS_OPEN,
          },
          {
            label: '站内信',
            prop: 'isMessage',
            type: 'switch',
            width: 60,
            rules: [
              {
                required: true,
                message: '请输入是否开启站内信',
                trigger: 'blur',
              },
            ],
            dicData: DIC_DATA.IS_OPEN,
          },
          {
            label: '邮箱',
            prop: 'isEmail',
            type: 'switch',
            width: 60,
            rules: [
              {
                required: true,
                message: '请输入是否开启邮箱',
                trigger: 'blur',
              },
            ],
            dicData: DIC_DATA.IS_OPEN,
          },
          {
            label: '状态',
            prop: 'status',
            width: 80,
            rules: [
              {
                required: true,
                message: '请输入状态',
                trigger: 'blur',
              },
            ],
            dicData: [
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.messagetemplate_add, false),
        viewBtn: this.vaildData(this.permission.messagetemplate_view, false),
        delBtn: this.vaildData(this.permission.messagetemplate_del, false),
        editBtn: this.vaildData(this.permission.messagetemplate_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    templateShow() {
      this.$router.push({
        path: '/msgManager/informMsgStencil',
      })
    },
    editMessageTemplateForm(row) {
      this.$router.push({
        path: '/msgManager/informMsgStencil',
        query: { id: row.id, openType: 'edit' },
      })
    },
    viewMessageTemplateForm(row) {
      this.$router.push({
        path: '/msgManager/informMsgStencil',
        query: { id: row.id, openType: 'view' },
      })
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    disableHandle(row) {
      this.$confirm('确定将选择数据禁用?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    enableHandle(row) {
      this.$confirm('确定将选择数据启用?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
  },
}
</script>

<style></style>
