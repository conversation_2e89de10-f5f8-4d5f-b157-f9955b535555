<template>
  <el-dialog
    :visible.sync="visible"
    append-to-body
    @close="handleCloseDialog"
    width="1222px"
    :fullscreen="fullscreen"
  >
    <template slot="title">
      <div class="dis_flex">
        <div class="dialog-title">云信兑付</div>
        <i
          @click="handleFullscreen"
          class="el-icon-full-screen"
          style="
            font-size: 16px;
            color: rgba(0, 0, 0, 0.25);
            margin-right: 20px;
            cursor: pointer;
          "
        ></i>
      </div>
    </template>
    <div class="contract-table">
      <el-table
        ref="table"
        :data="tableData"
        style="width: 100%; margin-top: 13px"
        class="table-border-style"
        :row-class-name="tableRowClassName"
      >
        <el-table-column prop="index" label="#" width="60" align="center">
        </el-table-column>
        <el-table-column prop="cloudCode" label="云信编号"> </el-table-column>
        <el-table-column prop="companyName" label="应收方"></el-table-column>

        <el-table-column prop="bankName" label="收款账户"></el-table-column>
        <el-table-column prop="bankCard" label="收款账号">
          <template slot-scope="scope">
            <p class="table-text">{{ scope.row.card }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="持单金额(元)"> </el-table-column>
        <el-table-column prop="overdueInterest" label="逾期利息(元)">
        </el-table-column>

        <el-table-column prop="collectionTotal" label="应付总额(元)">
        </el-table-column>
        <el-table-column prop="statusText" label="付款状态"></el-table-column>
      </el-table>
    </div>

    <el-form :model="form" :rules="rules" style="margin-top: 20px" ref="form">
      <el-form-item label="付款凭证" prop="fileList">
        <el-upload
          :on-success="handleAvatarSuccess"
          class="upload-demo"
          action="/api/blade-resource/oss/endpoint/put-file-kv"
          multiple
          :limit="1"
          :file-list="form.fileList"
        >
          <div style="display: flex">
            <el-button size="small" style="margin-right: 6px"
              >选择文件</el-button
            >
            <span v-if="!form.fileList.length">未选择任何文件</span>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button
        class="dialog-footer-btn"
        style="margin-right: 10px"
        @click="handleCloseDialog"
        >取 消</el-button
      >
      <el-button
        class="dialog-footer-btn"
        type="primary"
        @click="handleSubmit('form')"
        >确 定</el-button
      >
    </template>
  </el-dialog>
</template>

<script>
import { getCloudPayDetail, cloudPaySubmit } from '@/api/cloud/cloudpay'
import { formatMoney } from '@/util/filter'

export default {
  data() {
    return {
      fullscreen: false, // 全屏
      dialogVisible: false,
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    cloudCode: {
      type: String,
      default: '',
    },
  },
  watch: {
    visible: {
      handler(bool) {
        this.dialogVisible = bool
        if (bool) {
          this.getCloudPayDetail({ cloudCode: this.cloudCode })
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      tableData: [],
      form: {
        fileList: [],
      },
      initData: [],
      rules: {
        fileList: [{ required: true, message: '请上传凭证' }],
      },
    }
  },
  methods: {
    // 关闭弹窗
    handleCloseDialog() {
      this.$emit('update:visible', false)
    },
    // 全屏事件
    handleFullscreen() {
      this.fullscreen = !this.fullscreen
    },
    // 已上传的文件
    handleAvatarSuccess(response, file, fileList) {
      if (fileList.length) {
        for (const item of fileList) {
          const { response } = item
          this.form.fileList.push({
            id: response.data.attachId,
            link: response.data.url,
            name: response.data.name,
          })
        }
      }
    },
    // 提交操作
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let parentCloudAttachId = ''
          for (const item of this.form.fileList) {
            parentCloudAttachId += item.id
          }
          const params = {
            cloudPaymentDetailList: this.initData,
            parentCloudAttachId,
          }
          cloudPaySubmit(params).then(({ data }) => {
            if (data.code === 200) {
              this.$emit('onload')
              this.handleCloseDialog()
              this.$message.success('付款成功')
            }
          })
        } else {
          this.$message.error('请选择凭证')
          return false
        }
      })
    },

    getCloudPayDetail(params) {
      this.form.fileList = [] // 先请空上传图片数据
      getCloudPayDetail(params).then(({ data }) => {
        let list = []
        let obj = {
          amount: 0,
          overdueInterest: 0,
          collectionTotal: 0,
        }
        if (data.code === 200) {
          this.initData = data.data.cloudPaymentList.map(item => {
            return {
              ...item,
              collectionTotal: (item.amount || 0) + (item.overdueInterest || 0),
            }
          })
          data.data &&
            data.data.cloudPaymentList.forEach((item, index) => {
              list.push({
                ...item,
                index: index + 1,
                collectionTotal:
                  (item.amount || 0) + (item.overdueInterest || 0),
                amount: '￥' + formatMoney(item.amount || 0),
                overdueInterest: '￥' + formatMoney(item.overdueInterest || 0),
                statusText: item.status == 0 ? '待付款' : '已付款',
              })
            })

          if (data.data.cloudPaymentList && data.data.cloudPaymentList.length) {
            for (let i = 0; i < data.data.cloudPaymentList.length; i++) {
              obj.amount = this.$numJiaFun(
                obj.amount,
                data.data.cloudPaymentList[i].amount || 0
              )
              obj.overdueInterest = this.$numJiaFun(
                obj.overdueInterest,
                data.data.cloudPaymentList[i].overdueInterest || 0
              )
            }
            list.push({
              index: '总计',
              amount: '￥' + formatMoney(obj.amount),
              overdueInterest: '￥' + formatMoney(obj.overdueInterest),
              collectionTotal:
                '￥' + formatMoney(obj.amount + obj.overdueInterest),
              statusText: '',
            })
          }
          if (data.data && data.data.attachCloud) {
            this.form.fileList = [
              {
                ...data.data.attachCloud,
                name:
                  data.data && data.data.attachCloud
                    ? data.data.attachCloud.originalName
                    : '',
                url:
                  data.data && data.data.attachCloud
                    ? data.data.attachCloud.link
                    : '',
              },
            ]
          }
        }
        this.tableData = list
      })
    },
    handleAvatarChange(file, item) {
      setTimeout(() => {
        const { response } = file
        item.cloudAttachId =
          response && response.data && response.data.attachId
            ? response.data.attachId
            : ''
        console.log(response)

        item.cloudAttachUrl =
          response && response.data && response.data.url
            ? response.data.url
            : ''
      }, 500)
    },
    beforeAvatarUpload(file) {
      const type = file.type
      const islt500kb = file.size / 1024 > 1024 * 2
      if (['image/png', 'image/jpeg', 'application/pdf'].includes(type)) {
        if (islt500kb) {
          this.$message.error('文件大小不能超过2M')
          return false
        }
      } else {
        this.$message.error('文件格式错误')
        return false
      }
    },
    tableRowClassName({ row }) {
      if (row.index === '总计') {
        return 'aggregate-row'
      }
      return ''
    },
  },
}
</script>

<style lang="scss" scoped>
.dis_flex {
  display: flex;
  align-content: flex-start;
  justify-content: space-between;
  margin-bottom: 4px;
  .dialog-title {
    height: 20px;
    line-height: 20px;
  }
}
.dialog-footer-btn {
  height: 32px !important;
  padding: 6px 16px;
}
/deep/ .el-dialog__body {
  padding: 12px 20px 0px 33px;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.contract-table {
  .table-border-style {
    .table-text {
      color: #3894ff;
      font-size: 14px;
      font-weight: 400;
    }
    .table-img {
      width: 48px;
      height: 48px;
      object-fit: cover;
    }
  }
}

// 更改表格组件样式

::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 16px;
  height: 51px;
  line-height: 51px;
  font-weight: 600;
  .el-table__cell {
    padding: 0 !important;
  }
}
::v-deep {
  .contract-table {
    .el-table {
      margin-top: 0 !important;
    }
  }

  .contract-table {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .avatar-uploader .el-upload {
    border: 1px solid #bbbbbb;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 48px;
    height: 48px;
    line-height: 48px;
    text-align: center;
  }
  .avatar {
    width: 48px;
    height: 48px;
    display: block;
  }
}
</style>
