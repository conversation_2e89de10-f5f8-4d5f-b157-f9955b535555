<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row }" slot="requestInfo">
        <div class="details-button-box">
          <el-button type="text" size="mini" @click="detailsd(row.requestInfo)"
          >详情</el-button
          >
        </div>
      </template>
      <template slot-scope="{ row }" slot="responseInfo">
        <div class="details-button-box">
          <el-button type="text" size="mini" @click="detailsd(row.responseInfo)"
          >详情</el-button
          >
        </div>
      </template>
    </avue-crud>

    <el-dialog
      title="详情"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      width="60%"
    >
      <div style="max-height: 450px; overflow-y: auto" v-if="dialogData">
        <pre v-html="dialogData" />
      </div>
      <el-empty description="无数据" v-else></el-empty>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false"
        >确 定</el-button
        >
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from '@/api/system/loginterface'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      hasMinMaxForm: 1,
      minRows: 0, // 最小值
      maxRows: 10, // 最大值
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        index: false,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '授信企业名称',
            prop: 'creditCompanyName',
            rules: [
              {
                required: true,
                message: '请输入授信企业名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '接口方',
            search: true,
            prop: 'interfaceParty',
            rules: [
              {
                required: true,
                message: '请输入接口方',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '接口名称',
            prop: 'name',
            rules: [
              {
                required: true,
                message: '请输入接口名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'URL地址',
            prop: 'requestUrl',
            rules: [
              {
                required: true,
                message: '请输入URL地址',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '请求信息',
            prop: 'requestInfo',
            rules: [
              {
                required: true,
                message: '请输入请求信息',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '响应信息',
            prop: 'responseInfo',
            rules: [
              {
                required: true,
                message: '请输入响应信息',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '调用结果',
            prop: 'result',
            search: true,
            dicData: [
              {
                label: '成功',
                value: 1,
              },
              {
                label: '失败',
                value: 0,
              },
            ],
            rules: [
              {
                required: true,
                message: '请输入调用结果',
                trigger: 'blur',
              },
            ],
          },
          {
            label:'操作时间',
            prop: 'updateTime'
          },
          {
            label: '操作人',
            prop: 'operateName'
          }
        ],
      },
      data: [],
      dialogVisible: false,
      dialogData: '',
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.loginterface_add, false),
        viewBtn: this.vaildData(this.permission.loginterface_view, false),
        delBtn: this.vaildData(this.permission.loginterface_delete, false),
        editBtn: this.vaildData(this.permission.loginterface_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    detailsd(data) {
      this.dialogVisible = true
      if (data) {
        try {
          if (typeof JSON.parse(data) == 'object') {
            this.dialogData = this.syntaxHighlight(JSON.parse(data))
          }
        } catch (e) {
          this.dialogData = null
        }
      }
    },
    syntaxHighlight(json) {
      if (typeof json != 'string') {
        json = JSON.stringify(json, undefined, 2)
      }
      json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>')
      return json.replace(
        /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+]?\d+)?)/g,
        function (match) {
          var cls = 'number'
          if (/^"/.test(match)) {
            if (/:$/.test(match)) {
              cls = 'key'
            } else {
              cls = 'string'
            }
          } else if (/true|false/.test(match)) {
            cls = 'boolean'
          } else if (/null/.test(match)) {
            cls = 'null'
          }
          return '<span class="' + cls + '">' + match + '</span>'
        }
      )
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.details-button-box {
  display: flex;
  justify-content: center;
}
pre {
  outline: 1px solid #ccc;
  padding: 5px;
  margin: 5px;
}
::v-deep {
  .string {
    color: green;
  }
  .number {
    color: darkorange;
  }
  .boolean {
    color: blue;
  }
  .null {
    color: magenta;
  }
  .key {
    color: red;
  }
}
</style>
