/**
 * 1-4  赎货申请阶段
 * 5-7  赎货确认阶段
 * 8    待发货阶段
 * 9-11 待收货阶段
 * 12   待提货
 * 13   收货阶段/已完成
 */

const statusArr = [
  {},
  // 1
  {
    iconClass: 'icon-dengdai1',
    iconColor: '#697CFF',
    title: '赎货申请审核中',
    desc: '用户发起赎货申请，等待审批中',
  },
  // 2
  {
    iconClass: 'icon-delete-filling',
    iconColor: '#BBBBBB',
    title: '赎货申请已中止',
    desc: '', // 原因
  },
  // 3
  {
    iconClass: 'icon-renminbi1',
    iconColor: '#697CFF',
    title: '赎货申请审核中',
    desc: '赎货申请已通过，等待确认', // 倒计时
  },
  // 4
  {
    iconClass: 'icon-delete-filling',
    iconColor: '#BBBBBB',
    title: '超时已关闭',
    desc: '用户超时未确认赎货，该订单已自动关闭',
  },
  // 5
  {
    iconClass: 'icon-dengdai1',
    iconColor: '#697CFF',
    title: '赎货确认审核中',
    desc: '用户发起赎货确认，等待审批中',
  },
  // 6
  {
    iconClass: 'icon-jinggao1',
    iconColor: '#DF9935',
    title: '赎货确认已驳回',
    desc: '', // 原因
  },
  // 7
  {
    iconClass: 'icon-delete-filling',
    iconColor: '#BBBBBB',
    title: '赎货确认已中止',
    desc: '工作人员审批中，请耐心等待～',
  },
  // 8
  {
    iconClass: 'icon-dengdai1',
    iconColor: '#0D55CF',
    title: '待发货',
    desc: '赎货确认已通过，请尽快安排发货',
  },
  // 9
  {
    iconClass: 'icon-dengdai1',
    iconColor: '#0D55CF',
    title: '已发货',
    desc: '已安排发货，等候货物抵达目的地',
  },
  // 10
  {
    iconClass: 'icon-dengdai1',
    iconColor: '#0D55CF',
    title: '待验收',
    desc: '货物已抵达目的地，等待用户签收',
  },
  // 11
  {
    iconClass: 'icon-jinggao1',
    iconColor: '#DF9935',
    title: '有异议',
    desc: '用户对此验收请求有异议，请尽快处理',
  },
  // 12
  {
    iconClass: 'icon-dengdai1',
    iconColor: '#0D55CF',
    title: '待提货',
    desc: '赎货确认已通过，等待用户完成提货',
  },
  // 13
  {
    iconClass: 'icon-chenggong1',
    iconColor: '#1FC374',
    title: '已完成',
    desc: '货物已验收', // 验收时间
  },
]

export { statusArr }
