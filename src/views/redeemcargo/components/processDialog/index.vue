<template>
  <Dialog
    title="处理"
    ref="DialogRef"
    center
    :enableFullScreenBtn="false"
    width="40%"
    :cancelDisable="dialogLoading"
    :confirmLoading="dialogLoading"
    @cancel="handleDialogCancel"
    @confirm="handleDialogConfirm"
  >
    <el-form :model="formList" :rules="rules" class="rule-form" ref="form">
      <el-form-item label="状态类型:" prop="status">
        <el-select
          style="width: 100%"
          v-model="formList.status"
          placeholder="请选择状态"
        >
          <el-option
            v-for="item in statusOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="事件备注:" prop="remark">
        <el-input
          type="textarea"
          v-model="formList.remark"
          maxlength="200"
          show-word-limit
          size="medium"
          :autosize="{ minRows: 2, maxRows: 6 }"
          placeholder="请输入事件备注"
        ></el-input>
      </el-form-item>
    </el-form>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { requestObjectionHandle } from '@/api/redeemcargo'

export default {
  name: 'RedeemcargoProcessDialogIndex',
  components: { Dialog },
  data() {
    const rules = {
      status: [{ required: true, message: '请选择状态', trigger: 'change' }],
      remark: [
        { required: true, message: '请输入事件备注', trigger: 'change' },
      ],
    }

    return {
      rules,
      formList: {},
      statusOption: [],
      dialogLoading: false,
      redeemNo: undefined,
    }
  },
  methods: {
    handleOpen(_redeemNo) {
      this.redeemNo = _redeemNo
      this.statusOption = [
        { value: 9, label: '已发货' },
        { value: 10, label: '待验收' },
        { value: 13, label: '已完成' },
      ]
      this.formList = {}
      this.$refs.DialogRef.handleOpen()
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
    handleDialogCancel() {
      this.handleClose()
    },
    handleDialogConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.dialogLoading = true
          const requestObj = {
            redeemNo: this.redeemNo,
            handleStatus: this.formList.status,
            remarks: this.formList.remark,
          }

          requestObjectionHandle(requestObj)
            .then(({ data }) => {
              if (data.success) {
                this.$message.success('操作成功')
                this.$emit('handleRefreshTable')
                this.handleClose()
              }
            })
            .catch(() => {})
            .finally(() => {
              this.dialogLoading = false
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.rule-form {
  padding: 24px;
  ::v-deep {
    .el-form-item {
      display: flex;
      .el-form-item__label {
        width: 110px;
        max-width: 110px;
        text-align: left;
      }
      .el-form-item__content {
        flex-grow: 1;
      }
    }
  }
  .pay-box {
    padding: 20px 12px;
    width: 100%;
    background-color: #f7f7f7;
    border-radius: 6px;
    box-sizing: border-box;
    margin-top: 9px;
    line-height: 20px;
    & span:first-child {
      display: block;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      font-weight: 500;
      margin-right: 8px;
    }
    & span:last-child {
      color: #00072a;
      font-weight: 600;
      font-size: 14px;
    }

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 100px;
        text-align: right;
      }
    }
  }
}
</style>
