<template>
  <Dialog
    title="签收登记"
    ref="DialogRef"
    center
    :enableFullScreenBtn="false"
    width="40%"
    :cancelDisable="dialogLoading"
    :confirmLoading="dialogLoading"
    @cancel="handleDialogCancel"
    @confirm="handleDialogConfirm"
  >
    <el-form :model="formList" :rules="rules" class="rule-form" ref="form">
      <el-form-item label="签收人:" prop="name">
        <el-input placeholder="请输入签收人" v-model="formList.name" />
      </el-form-item>
      <el-form-item label="签收时间:" prop="date">
        <el-date-picker
          :picker-options="pickerOptions"
          style="width: 100%"
          v-model="formList.date"
          type="datetime"
          placeholder="选择签收时间"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="签收凭证" prop="voucher">
        <BaseImageUpload
          :imgData.sync="formList.voucher"
          :disabled="false"
          :length="1"
        />
      </el-form-item>
    </el-form>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import BaseImageUpload from '@/components/BaseImageUpload/index.vue'
import { requestSignForRegister } from '@/api/redeemcargo'

export default {
  name: 'RedeemcargoSignRegistrationDialogIndex',
  components: { Dialog, BaseImageUpload },
  data() {
    const rules = {
      name: [{ required: true, message: '请输入签收人', trigger: 'change' }],
      date: [{ required: true, message: '请选择到货时间', trigger: 'change' }],
      voucher: [
        { required: true, message: '请上传签收凭证', trigger: 'change' },
      ],
    }

    return {
      formList: {},
      rules,
      dialogLoading: false,
      targetId: undefined,
    }
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      }
    },
  },
  methods: {
    handleOpen(_targetId) {
      this.targetId = _targetId
      this.formList = {}
      this.$refs.DialogRef.handleOpen()
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
    handleDialogCancel() {
      this.handleClose()
    },
    handleDialogConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.dialogLoading = true
          const requestObj = {
            id: this.targetId,
            receivedPeople: this.formList.name,
            receivedTime: this.formList.date,
          }
          const attachIdArr = []
          for (const item of this.formList.voucher) {
            if (item.id) attachIdArr.push(item.id)
          }
          requestObj.receivedDocument = attachIdArr.toString()

          requestSignForRegister(requestObj)
            .then(({ data }) => {
              if (data.success) {
                this.$message.success('操作成功')
                this.$emit('handleRefreshTable')
                this.handleClose()
              }
            })
            .catch(() => {})
            .finally(() => {
              this.dialogLoading = false
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.rule-form {
  padding: 24px;
  ::v-deep {
    .el-form-item {
      display: flex;
      .el-form-item__label {
        width: 110px;
        max-width: 110px;
        text-align: left;
      }
      .el-form-item__content {
        flex-grow: 1;
      }
    }
  }
  .pay-box {
    padding: 20px 12px;
    width: 100%;
    background-color: #f7f7f7;
    border-radius: 6px;
    box-sizing: border-box;
    margin-top: 9px;
    line-height: 20px;
    & span:first-child {
      display: block;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      font-weight: 500;
      margin-right: 8px;
    }
    & span:last-child {
      color: #00072a;
      font-weight: 600;
      font-size: 14px;
    }

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 100px;
        text-align: right;
      }
    }
  }
}
</style>
