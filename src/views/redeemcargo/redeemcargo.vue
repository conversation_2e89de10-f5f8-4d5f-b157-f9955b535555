<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="processStatus" @change="handleTabButton">
            <el-radio-button
              v-for="item in [
                { label: '全部', value: 0 },
                { label: '申请中', value: 2 },
                { label: '待确认', value: 3 },
                { label: '待出库', value: 4 },
                { label: '待收货', value: 5 },
                { label: '已作废', value: 6 },
              ]"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </template> -->
      <template slot-scope="{ row }" slot="goodsName">
        <div class="goods-container">
          <el-image
            style="
              width: 72px;
              height: 72px;
              flex-shrink: 0;
              border-radius: 4px;
            "
            :src="row.goodsUrl"
            fit="contain"
          />
          <div class="goods-content">
            <span class="goods-name" :title="row.goodsName">{{
              row.goodsName
            }}</span>
            <div class="goods-type-container">
              <span class="goods-type">规格型号：</span>
              <span class="goods-type-value">{{ row.goodsSpec }}</span>
            </div>
          </div>
        </div>
      </template>
      <template slot-scope="{ row }" slot="purchasePrice">
        <span>￥{{ row.purchasePrice | formatMoney }}</span>
      </template>
      <template slot-scope="{ row }" slot="financingPrice">
        <span>￥{{ row.financingPrice | formatMoney }}</span>
      </template>
      <template slot-scope="{ row }" slot="createUser">
        <span>{{ row.createUserStr }}</span>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button class="el-btn" type="text" @click="handleDetail(row)"
          >详情</el-button
        >
        <el-button
          v-if="row.status === 8 && row.extractType === 1"
          class="el-btn"
          type="text"
          @click="toShipments(row.redeemNo)"
        >
          发货登记
        </el-button>
        <el-button
          v-if="row.status === 9"
          class="el-btn"
          type="text"
          @click="handleArrivalRegistration(row.id)"
          >到货登记</el-button
        >
        <el-button
          v-if="row.status === 12"
          class="el-btn"
          type="text"
          @click="handleSignReceiptRegistration(row.id)"
          >签收登记</el-button
        >
        <!----> <el-button
          v-if="row.status === 11"
          class="el-btn"
          type="text"
          @click="handleProcess(row.redeemNo)"
          >处理</el-button
        >
      </template>
    </avue-crud>
    <ArrivalDialog ref="arrivalDialogRef" @handleRefreshTable="initTable" />
    <SignDialog ref="signDialogRef" @handleRefreshTable="initTable" />
    <ProcessDialog ref="processDialogRef" @handleRefreshTable="initTable" />
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from '@/api/redeemcargo/redeemcargo'
import { mapGetters } from 'vuex'
import ArrivalDialog from './components/arrivalRegistrationDialog/index.vue'
import SignDialog from './components/signReceiptRegistrationDialog/index.vue'
import ProcessDialog from './components/processDialog/index.vue'

export default {
  components: { ArrivalDialog, SignDialog, ProcessDialog },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        menuWidth: 280,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '赎货单号',
            prop: 'redeemNo',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入赎货单号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资编号',
            prop: 'financingNo',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '货品信息',
            prop: 'goodsName',
            width: '300',
            rules: [
              {
                required: true,
                message: '请输入提货信息Id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '单位',
            prop: 'goodsUnit',
            rules: [
              {
                required: true,
                message: '请输入商品单位',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '采购单价（元）',
            prop: 'purchasePrice',
            rules: [
              {
                required: true,
                message: '请输入采购单价',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资单价（元）',
            prop: 'financingPrice',
            rules: [
              {
                required: true,
                message: '请输入融资单价',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '赎货总数',
            prop: 'num',
            rules: [
              {
                required: true,
                message: '请输入赎货数量',
                trigger: 'blur',
              },
            ],
          },
         {
            label: '退货数量',
            prop: 'backGoodsNum',
            rules: [
              {
                required: true,
                message: '请输入赎货数量',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '换货数量',
            prop: 'exchangeGoods',
            rules: [
              {
                required: true,
                message: '请输入赎货数量',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '赎货方式',
            type: 'tree',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=extract_type',
            search: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            prop: 'extractType',
            formatter: (val, value, label) => {
              return `${label}`
            },
          },
          {
            label: '融资用户',
            prop: 'createUser',
          },
          {
            label: '创建时间',
            prop: 'createTime',
          },
          {
            label: '状态',
            prop: 'statusStr',
            type: 'tree',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=redeem_cargo_state',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [
              {
                required: true,
                message: '请输入状态',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'statusEqual',
            hide:true,
            display:false,
            type: 'tree',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=redeem_cargo_state',
            search: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
        ],
      },
      data: [],
      // 页面自定义数据
      processStatus: 0,
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.redeemcargo_add, false),
        viewBtn: this.vaildData(this.permission.redeemcargo_view, false),
        delBtn: this.vaildData(this.permission.redeemcargo_delete, false),
        editBtn: this.vaildData(this.permission.redeemcargo_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    toShipments(id) {
      this.$router.push(`/redeemcargo/toShipments?id=${id}`)
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    initTable() {
      this.page.currentPage = 1
      this.refreshChange()
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    //页面点击支付状态事件方法
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
    // 操作 - 详情
    handleDetail(row) {
      this.$router.push(`/redeem/detail/${row.id}/${row.redeemNo}`)
    },
    // 操作 - 到货登记
    handleArrivalRegistration(targetId) {
      this.$refs.arrivalDialogRef.handleOpen(targetId)
    },
    // 操作 - 签收登记
    handleSignReceiptRegistration(targetId) {
      this.$refs.signDialogRef.handleOpen(targetId)
    },
    // 操作 - 处理
    handleProcess(redeemNo) {
      this.$refs.processDialogRef.handleOpen(redeemNo)
    },
  },
}
</script>

<style lang="scss" scoped>
.el-btn {
  color: #1177ff;
  font-size: 12px;
}

.order-header-container {
  border: none;
}

.goods-container {
  display: flex;
  align-items: center;

  .goods-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 6px 0 6px 12px;
    width: 100%;
    height: 72px;

    .goods-name {
      font-size: 14px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #0a1f44;
      line-height: 20px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    .goods-type-container {
      .goods-type {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8a94a6;
        line-height: 20px;
      }

      .goods-type-value {
        font-size: 14px;
        font-family: SFProText-Medium, SFProText;
        font-weight: 500;
        color: #53627c;
        line-height: 20px;
      }
    }
  }
}
</style>

<style></style>
