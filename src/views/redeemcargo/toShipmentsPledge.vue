<template>
  <div class="warehou-sing">
    <!-- 头部-步骤条 -->
    <basic-container>
      <div class="header-box">
        <div class="header-title">发货</div>
        <div class="long-string" />
        <div class="article-steps-box">
          <el-steps
            :active="shipmentsActive"
            finish-status="success"
            align-center
          >
            <el-step title="填写发货信息"></el-step>
            <el-step title="信息确认"></el-step>
          </el-steps>
        </div>
      </div>
    </basic-container>
    <!-- 赎货信息 -->
    <basic-container>
      <div class="redeem-cargo-box">
        <h1 class="title-box">赎货信息</h1>
        <div
          v-for="item in tableData2"
          :key="item.id"
          class="cargo-information-forbox"
        >
          <p class="item-title">
            <span>
              商品规格:
              <span class="addBold">{{ item.goodsSpec }}</span>
            </span>
            <span>
              需出库数量:
              <span class="addBold">{{ item.needReleasedNum }}</span>
            </span>
          </p>
          <MyElTable
            ref="tableB"
            :tableData="item.arrData"
            :columns="columnList"
          >
            <template #sendNum="{ iScope: { row } }">
              <el-input-number
                v-model="row.sendNum"
                :disabled="!row.warehouseNum || shipmentsActive !== 0"
                :controls="false"
                placeholder="请输入数量"
                :min="1"
                :max="row.warehouseNum"
                style="width: 100%"
                @blur="monitorSendNum(row)"
              />
            </template>
          </MyElTable>
        </div>
        <!-- <div class="cargo-information-box">
          <div class="info-box">
            <div class="img-container">
              <img :src="cargoObjData.goodLogo" alt="" />
            </div>
            <div class="right-info-box">
              <span class="name-info">
                {{ cargoObjData.goodsName }}
              </span>
              <span class="specification-info">
                {{ cargoObjData.goodsSpec }}
              </span>
            </div>
          </div>
          <div class="child-information">
            <span>商品编号</span>
            <span>
              {{ cargoObjData.redeemNo }}
            </span>
          </div>
          <div class="child-information">
            <span>采购单价</span>
            <span> ¥{{ cargoObjData.purchasePrice | formatMoney }} </span>
          </div>
          <div class="child-information">
            <span>出库数量</span>
            <span>
              {{ cargoObjData.num }}
            </span>
          </div>
          <div class="child-information">
            <span>单位</span>
            <span>
              {{ cargoObjData.goodsUnitValue }}
            </span>
          </div>
        </div> -->
        <div class="descriptions-for-box">
          <el-descriptions title="" :column="3" border>
            <el-descriptions-item
              v-for="item in tableData"
              :key="item.id"
              :label="item.label"
            >
              <template v-if="item.label === '融资编号'">
                <span style="color: #1177ff">
                  {{ item.value }}
                </span>
              </template>
              <template v-else-if="item.label === '供应商'">
                <span style="color: #1177ff">
                  {{ item.value }}
                </span>
              </template>
              <template v-else-if="item.label === '仓储公司'">
                <span style="color: #1177ff">
                  {{ item.value }}
                </span>
              </template>
              <template v-else>
                {{ item.value }}
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </basic-container>
    <template v-if="shipmentsActive === 0">
      <!-- 出库信息 -->
      <basic-container>
        <h1 class="title-box">出库信息</h1>
        <div class="form-box">
          <avue-form
            v-model="avueFormData1"
            :option="stockOutOption"
            ref="stockOutFormAvue"
          >
            <template slot="deliveryDocument">
              <div class="file-upload-box-container">
                <el-upload
                  class="file-upload"
                  drag
                  action="/api/blade-resource/oss/endpoint/put-file-kv"
                  multiple
                  :headers="{ 'Blade-Auth': Basic }"
                  :on-success="
                    (response, file, fileList) => {
                      return handleUpSuccess(response, file, fileList, 1)
                    }
                  "
                  :on-remove="
                    (file, fileList) => {
                      return handleFileRemove(file, fileList, 1)
                    }
                  "
                  :on-preview="handleFilePreview"
                  :file-list="deliveryDocumentArr"
                  :disabled="false"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <div class="el-upload__tip" slot="tip">
                    只能上传jpg/jpeg/png/pdf文件，且不超过20M
                  </div>
                </el-upload>
              </div>
            </template>
          </avue-form>
        </div>
      </basic-container>
      <!-- 物流信息 -->
      <basic-container style="margin-bottom: 100px">
        <h1 class="title-box">物流信息</h1>
        <div class="form-box">
          <avue-form
            v-model="avueFormData2"
            :option="logisticsOption"
            ref="logisticsFormAvue"
          >
            <template slot="logisticsDocument">
              <div class="file-upload-box-container">
                <el-upload
                  class="file-upload"
                  drag
                  action="/api/blade-resource/oss/endpoint/put-file-kv"
                  multiple
                  :headers="{ 'Blade-Auth': Basic }"
                  :on-success="
                    (response, file, fileList) => {
                      return handleUpSuccess(response, file, fileList, 2)
                    }
                  "
                  :on-remove="
                    (file, fileList) => {
                      return handleFileRemove(file, fileList, 2)
                    }
                  "
                  :on-preview="handleFilePreview"
                  :file-list="logisticsDocumentArr"
                  :disabled="false"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <div class="el-upload__tip" slot="tip">
                    只能上传jpg/jpeg/png/pdf文件，且不超过20M
                  </div>
                </el-upload>
              </div>
            </template>
          </avue-form>
        </div>
      </basic-container>
    </template>
    <!-- 二次确认信息 -->
    <template v-else>
      <basic-container>
        <div class="redeem-cargo-box">
          <h1 class="title-box">出库信息</h1>
          <div class="descriptions-for-box">
            <el-descriptions title="" :column="3" border>
              <el-descriptions-item
                v-for="item in deliveryDocumentElArr"
                :key="item.id"
                :label="item.label"
              >
                <template v-if="item.label === '出库单'">
                  <div class="look-btn-menu">
                    <span
                      v-if="imgeArr1.length"
                      @click="handleFilePreviewOnceAgain('img', 1)"
                    >
                      查看图片
                    </span>
                    <template v-if="pdfArr1.length === 1">
                      <span @click="handleFilePreviewOnceAgain(0, 1)">
                        查看附件
                      </span>
                    </template>
                    <template v-else>
                      <span
                        v-for="(item, index) in pdfArr1"
                        :key="index"
                        @click="handleFilePreviewOnceAgain(index, 1)"
                      >
                        查看附件{{ index + 1 }}
                      </span>
                    </template>
                  </div>
                </template>
                <template v-else>
                  {{ item.value }}
                </template>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </basic-container>
      <basic-container style="margin-bottom: 100px">
        <div class="redeem-cargo-box">
          <h1 class="title-box">物流信息</h1>
          <div class="descriptions-for-box">
            <el-descriptions title="" :column="3" border>
              <el-descriptions-item
                v-for="item in logisticsDocumentElArr"
                :key="item.id"
                :label="item.label"
              >
                <template v-if="item.label === '物流单'">
                  <div class="look-btn-menu">
                    <span
                      v-if="imgeArr2.length"
                      @click="handleFilePreviewOnceAgain('img', 2)"
                    >
                      查看图片
                    </span>
                    <template v-if="pdfArr2.length === 1">
                      <span @click="handleFilePreviewOnceAgain(0, 2)">
                        查看附件
                      </span>
                    </template>
                    <template v-else>
                      <span
                        v-for="(item, index) in pdfArr2"
                        :key="index"
                        @click="handleFilePreviewOnceAgain(index, 2)"
                      >
                        查看附件{{ index + 1 }}
                      </span>
                    </template>
                  </div>
                </template>
                <template v-else>
                  {{ item.value }}
                </template>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </basic-container>
    </template>

    <div class="footer-container" v-if="shipmentsActive === 0">
      <span class="backBtn" @click="backBtnFun">返 回</span>
      <span class="nextBtn" @click="nextBtnFun">下一步</span>
    </div>
    <div class="footer-container" v-else>
      <span class="backBtn" @click="lastBtnFun">上一步</span>
      <span class="nextBtn" @click="confirmBtnFun">确认发货</span>
    </div>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import FilePreview from '@/components/file-preview'
import {
  cargoPledgeDetail,
  cargoPledgeSendSave,
  warehouseDetailsByFiannceNo,
} from '@/api/redeemcargo/redeemcargo'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import MyElTable from '@/components/myElTable/index.vue'
import workflowTool from '@/util/workflowTool.js'
import { getToken } from '@/util/auth'

export default {
  name: 'shipments',
  components: { FilePreview, MyElTable },
  data() {
    return {
      cargoObjData: {},
      tableData: [],
      avueFormData1: {},
      avueFormData2: {},
      shipmentsActive: 0,
      // 出库Option
      stockOutOption: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 90,
        gutter: 50,
        // size: 'medium',
        column: [],
      },
      // 物流Option
      logisticsOption: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 110,
        gutter: 50,
        column: [],
      },
      deliveryDocumentArr: [],
      logisticsDocumentArr: [],
      pdfSrc: '',
      pdfArr1: [],
      imgeArr1: [],
      pdfArr2: [],
      imgeArr2: [],
      deliveryDocumentElArr: [],
      logisticsDocumentElArr: [],
      logisticsCodeArr: [],
      // 赎货表格信息
      columnList: [
        {
          id: 1,
          iProp: 'index',
          iLabel: '#',
          iWidth: 70,
          iAlign: 'center',
        },
        {
          id: 2,
          iProp: 'warehouseNo',
          iLabel: '库存编号',
          iWidth: 150,
        },
        {
          id: 3,
          iProp: 'warehouseName',
          iLabel: '仓库',
          iWidth: 150,
        },
        {
          id: 4,
          iProp: 'warehouseInDate',
          iLabel: '入库日期',
          iWidth: 120,
        },
        {
          id: 5,
          iProp: 'inventoryAge',
          iLabel: '库龄',
        },
        {
          id: 6,
          iProp: 'warehouseNum',
          iLabel: '库存数量',
        },
        {
          id: 7,
          iProp: 'goodsUnitValue',
          iLabel: '单位',
        },
        {
          id: 8,
          iProp: 'sendNum',
          iLabel: '出库数量',
          iWidth: 230,
          isSlot: true,
        },
      ],
      tableData2: {},
      Basic: '',
    }
  },
  watch: {},
  created() {
    this.Basic = 'bearer ' + getToken()
    // 出库
    this.stockOutOption.column = [
      {
        label: '出库日期',
        prop: 'outWarehouseTime',
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd hh:mm:ss',
        type: 'date',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择出库日期',
            trigger: 'change',
          },
        ],
      },
      {
        label: '批号',
        prop: 'batchNumber',
        type: 'input',
        span: 12,
        labelWidth: 55,
        rules: [
          {
            required: true,
            message: '请输入批号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '出库单',
        prop: 'deliveryDocument',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请上传出库单',
            trigger: 'change',
          },
        ],
      },
    ]
    // 物流
    this.logisticsOption.column = [
      {
        label: '物流公司',
        prop: 'logisticsCompany',
        type: 'select',
        span: 12,
        dicData: [],
        rules: [
          {
            required: true,
            message: '请选择物流公司',
            trigger: 'change',
          },
        ],
      },
      {
        label: '运单编号',
        prop: 'transportNumber',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入运单编号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '发货时间',
        prop: 'sendGoodsTime',
        type: 'date',
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd hh:mm:ss',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择发货时间',
            trigger: 'change',
          },
        ],
      },
      {
        label: '送货人',
        prop: 'givePeople',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入送货人',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '送货人手机号',
        prop: 'givePeoplePhone',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入送货人手机号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '车牌号',
        prop: 'carNumber',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入车牌号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '物流单',
        prop: 'logisticsDocument',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请上传物流单',
            trigger: 'change',
          },
        ],
      },
    ]
    getDictionary('logistics_code').then(res => {
      const resData = res.data
      if (resData.code == 200) {
        // 处理字典数据
        const resList = []
        for (const item of resData.data) {
          resList.push({
            label: item.dictValue,
            value: item.dictKey,
          })
        }
        // this.logisticsOption.column[0].dicData = resList
        const logisticsCompany = this.findObject(
          this.logisticsOption.column,
          'logisticsCompany'
        )
        logisticsCompany.dicData = resList // 提前还款下来数组
        this.logisticsCodeArr = resList // 用于二次确认回显中文
      }
    })
    this.onLoad()
  },
  methods: {
    onLoad() {
      const data = [
        {
          id: 1,
          label: '融资编号',
          value: void 0,
        },
        {
          id: 2,
          label: '约定赎货日',
          value: void 0,
        },
        {
          id: 3,
          label: '提货方式',
          value: void 0,
        },
        {
          id: 4,
          label: '收货对象',
          value: void 0,
        },
        {
          id: 5,
          label: '联系人',
          value: void 0,
        },
        {
          id: 6,
          label: '联系方式',
          value: void 0,
        },
        {
          id: 7,
          label: '收货地址',
          value: void 0,
        },
      ]
      this.tableData = data
      // 获取详情
      cargoPledgeDetail(this.$route.query.id).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const dat = resData.pledgeRedeemDetailCargoCurrencyVO
          this.warehouseDetailsByFiannceNoFun(
            dat.redeemCommodityVOS,
            dat.financeNo
          )
          this.cargoObjData = { ...dat }
          //this.tableData[0].value = dat.warehouseNo // 库存编号
          //this.tableData[2].value = dat.supplierName // 供应商
          //this.tableData[3].value = resData.storageCompany // 仓储公司
          //this.tableData[4].value = resData.warehouseName // 仓库
          //this.tableData[5].value = dat.warehouseInDate // 	入库日期
          //this.tableData[6].value = dat.warehouseAge // 库龄(天)
          this.tableData[0].value = dat.financeNo // 	融资编号
          this.tableData[1].value = dat.redemptionDate // 约定赎货日
          this.tableData[2].value = dat.extractType // 	提货方式
          this.tableData[3].value = dat.financingAddress.enterpriseName // 	收货对象
          this.tableData[4].value = dat.financingAddress.contacts // 	联系人
          this.tableData[5].value = dat.financingAddress.addressPhone // 		联系方式
          this.tableData[6].value = dat.financingAddress.urbanAreas // 		收货地址
        }
      })
      // 如果已经有缓存，就回显数据
      const sessionWare1 = sessionStorage.getItem(
        `shipments${this.$route.query.id}1`
      )
      const sessionWare2 = sessionStorage.getItem(
        `shipments${this.$route.query.id}2`
      )
      if (sessionWare1) {
        const shipmentsSessions = JSON.parse(sessionWare1)
        for (const key in shipmentsSessions) {
          this.avueFormData1[key] = shipmentsSessions[key]
          if (key === 'deliveryDocument') {
            this.deliveryDocumentArr = shipmentsSessions[key]
          }
        }
      }
      if (sessionWare2) {
        const shipmentsSessions = JSON.parse(sessionWare2)
        for (const key in shipmentsSessions) {
          this.avueFormData2[key] = shipmentsSessions[key]
          if (key === 'logisticsDocument') {
            this.logisticsDocumentArr = shipmentsSessions[key]
          }
        }
      }
    },
    warehouseDetailsByFiannceNoFun(specArr, financeNo) {
      warehouseDetailsByFiannceNo({ financeNo }).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const objData = {}
          for (const key in resData) {
            if (resData[key]) {
              const needReleasedNum = workflowTool.firstObjectFilter(
                specArr,
                key,
                'spec'
              ).num

              if (needReleasedNum) {
                const arrData = []
                let indexN = 1
                for (const item of resData[key]) {
                  arrData.push(
                    Object.assign(item, {
                      index: indexN,
                      inventoryAge: item.inventoryAge + '天',
                    })
                  )
                  indexN++
                }
                objData[key] = {
                  arrData,
                  id: arrData[0].id,
                  goodsSpec: arrData[0].goodsSpec,
                  needReleasedNum,
                }
              }
            }
          }
          this.tableData2 = objData
        }
      })
    },
    monitorSendNum(row) {
      const fatherData = this.tableData2[row.goodsSpec]
      if (Number(row.sendNum) > Number(fatherData.needReleasedNum)) {
        row.sendNum = undefined
        this.$message.warning('该规格总出库数量不能大于需出库数量')
      }
    },
    handleUpSuccess(response, file, fileList, type) {
      const arr = []
      for (const itemed of fileList) {
        if (itemed.response) {
          const { data = {} } = itemed.response
          arr.push({
            name: data.name,
            url: data.url,
            attachId: data.attachId,
          })
        } else {
          arr.push({
            name: itemed.name,
            url: itemed.url,
            attachId: itemed.attachId,
          })
        }
      }
      if (type === 1) {
        this.avueFormData1.deliveryDocument = arr
        this.$refs.stockOutFormAvue.validateField('deliveryDocument')
        return
      }
      this.avueFormData2.logisticsDocument = arr
      this.$refs.logisticsFormAvue.validateField('logisticsDocument')
    },
    handleFileRemove(file, fileList, type) {
      if (type === 1) {
        this.avueFormData1.deliveryDocument = fileList
        return
      }
      this.avueFormData2.logisticsDocument = fileList
    },
    handleFilePreview(file) {
      let targetUrl = void 0
      if (file.response) {
        targetUrl = file.response.data.url
      } else {
        targetUrl = file.url
      }
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    handleFilePreviewOnceAgain(type, index) {
      if (type === 'img') {
        if (index === 1) {
          this.$ImagePreview(this.imgeArr1, 0, {
            closeOnClickModal: true,
          })
          return
        }
        this.$ImagePreview(this.imgeArr2, 0, {
          closeOnClickModal: true,
        })
        return
      }
      if (index === 1) {
        this.pdfSrc =
          this.pdfArr1[type].url + '?time=' + new Date().getMilliseconds()
        return
      }
      this.pdfSrc =
        this.pdfArr2[type].url + '?time=' + new Date().getMilliseconds()
    },
    nextBtnFun() {
      this.$refs.stockOutFormAvue.validate((vaild, done) => {
        if (vaild && this.shipmentsActive < 1) {
          done()
        }
        for (const key in this.tableData2) {
          const tData = this.tableData2[key]
          if (tData) {
            let summation = 0
            for (const item of tData.arrData) {
              summation += Number(item.sendNum)
            }
            if (summation !== Number(tData.needReleasedNum)) {
              return this.$message.warning('请完成出库数量填写')
            }
          }
        }
        this.$refs.logisticsFormAvue.validate((vaild, done) => {
          if (vaild && this.shipmentsActive < 1) {
            // 处理图片与pdf
            this.pdfArr1 = []
            this.imgeArr1 = []
            this.pdfArr2 = []
            this.imgeArr2 = []
            // 出库
            for (const item of this.avueFormData1.deliveryDocument) {
              if (item.url.endsWith('.pdf')) {
                this.pdfArr1.push(item)
              } else {
                this.imgeArr1.push(item)
              }
            }
            // 物流
            for (const item of this.avueFormData2.logisticsDocument) {
              if (item.url.endsWith('.pdf')) {
                this.pdfArr2.push(item)
              } else {
                this.imgeArr2.push(item)
              }
            }
            // 步骤条++
            this.shipmentsActive++
            // 存储form数据
            sessionStorage.setItem(
              `shipments${this.$route.query.id}1`,
              JSON.stringify(this.avueFormData1)
            )
            sessionStorage.setItem(
              `shipments${this.$route.query.id}2`,
              JSON.stringify(this.avueFormData2)
            )
            // 对象转数组用于下一步渲染
            // 出库
            this.deliveryDocumentElArr = []
            let ind1 = 0
            for (const key in this.avueFormData1) {
              for (const item of this.stockOutOption.column) {
                if (item.prop === key) {
                  this.deliveryDocumentElArr.push({
                    id: ind1++,
                    label: item.label,
                    value: this.avueFormData1[key],
                  })
                  break
                }
              }
            }
            // 物流
            this.logisticsDocumentElArr = []
            let ind2 = 0
            for (const key in this.avueFormData2) {
              for (const item of this.logisticsOption.column) {
                if (item.prop === key) {
                  if (key === 'logisticsCompany') {
                    // 过滤出物流信息的对应中文
                    const logisticsCodeIndex = this.logisticsCodeArr.filter(
                      item => item.value === this.avueFormData2[key]
                    )
                    this.logisticsDocumentElArr.push({
                      id: ind2++,
                      label: item.label,
                      value: logisticsCodeIndex[0].label,
                    })
                  } else {
                    this.logisticsDocumentElArr.push({
                      id: ind2++,
                      label: item.label,
                      value: this.avueFormData2[key],
                    })
                  }
                  break
                }
              }
            }
            done()
          }
        })
      })
    },
    backBtnFun() {
      this.$router.$avueRouter.closeTag()
      this.$router.push('/redeemcargo/redeemPledgeCargo')
    },
    lastBtnFun() {
      if (this.shipmentsActive === 1) {
        this.deliveryDocumentArr = this.avueFormData1.deliveryDocument
        this.logisticsDocumentArr = this.avueFormData2.logisticsDocument
        this.shipmentsActive--
      }
    },
    confirmBtnFun() {
      const deliveryArr = []
      for (const item of this.avueFormData1.deliveryDocument) {
        deliveryArr.push(item.attachId)
      }
      const logisticsArr = []
      for (const item of this.avueFormData2.logisticsDocument) {
        logisticsArr.push(item.attachId)
      }
      const warehouseDetailsList = []
      for (const key in this.tableData2) {
        if (this.tableData2[key]) {
          for (const item of this.tableData2[key].arrData) {
            warehouseDetailsList.push({
              id: item.id,
              sendNum: item.sendNum,
            })
          }
        }
      }
      const data = {
        redeemNo: this.cargoObjData.redeemNo,
        outWarehouseTime: this.avueFormData1.outWarehouseTime,
        batchNumber: this.avueFormData1.batchNumber,
        deliveryDocument: deliveryArr.join(','),
        logisticsCompany: this.avueFormData2.logisticsCompany,
        transportNumber: this.avueFormData2.transportNumber,
        sendGoodsTime: this.avueFormData2.sendGoodsTime,
        givePeople: this.avueFormData2.givePeople,
        givePeoplePhone: this.avueFormData2.givePeoplePhone,
        carNumber: this.avueFormData2.carNumber,
        logisticsDocument: logisticsArr.join(','),
        warehouseDetailsList,
      }
      cargoPledgeSendSave(data).then(({ data }) => {
        if (data.success) {
          this.$message.success('发货成功')
          this.backBtnFun()
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.warehou-sing {
  .header-box {
    .header-title {
      width: 64px;
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-family: SourceHanSansSC-regular;
      font-weight: bold;
      margin-bottom: 20px;
    }

    .long-string {
      width: 100%;
      height: 1px;
      border: 1px solid rgba(233, 235, 239, 100);
      margin-bottom: 29px;
    }

    .article-steps-box {
      width: 65%;
      margin: 0 auto 8px;
    }
  }
  .title-box {
    width: 319px;
    height: 24px;
    margin: 0 0 12px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .redeem-cargo-box {
    cursor: context-menu;

    .cargo-information-forbox {
      margin-top: 15px;

      .item-title {
        color: #303030;
        margin-bottom: 10px;

        & > span:first-child {
          margin-right: 20px;
        }
      }

      .addBold {
        font-weight: bold;
      }
    }

    // .cargo-information-box {
    //   display: flex;
    //   justify-content: space-between;
    //   align-items: flex-start;
    //   .info-box {
    //     display: flex;
    //     align-items: center;
    //     width: 100%;
    //     height: 72px;
    //     .img-container {
    //       width: 72px;
    //       height: 72px;
    //       border-radius: 4px;
    //       overflow: hidden;
    //       margin-right: 8px;
    //       box-sizing: border-box;
    //       border: 1px solid #dddddd;
    //       flex-shrink: 0;

    //       img {
    //         width: 100%;
    //         height: 100%;
    //         object-fit: cover;
    //       }
    //     }
    //     .right-info-box {
    //       height: 100%;
    //       display: flex;
    //       flex-direction: column;
    //       justify-content: space-between;
    //       .name-info {
    //         width: 100%;
    //         height: 40px;
    //         color: rgba(105, 124, 255, 100);
    //         font-size: 14px;
    //         font-family: SourceHanSansSC-regular;
    //         display: -webkit-box;
    //         overflow: hidden;
    //         text-overflow: ellipsis;
    //         -webkit-box-orient: vertical; //子元素应该被水平或垂直排列
    //         -webkit-line-clamp: 2; //3行后显示省略号
    //       }
    //       .specification-info {
    //         width: 100%;
    //         height: 20px;
    //         color: rgba(141, 141, 141, 100);
    //         font-size: 14px;
    //         font-family: SourceHanSansSC-regular;
    //         overflow: hidden;
    //         text-overflow: ellipsis;
    //         white-space: nowrap;
    //       }
    //     }
    //   }
    //   .child-information {
    //     width: 60%;
    //     margin-left: 40px;
    //     span {
    //       display: block;
    //     }
    //     & span:first-child {
    //       width: 160px;
    //       height: 20px;
    //       color: rgba(112, 112, 112, 100);
    //       font-size: 14px;
    //       font-family: SourceHanSansSC-regular;
    //       margin-bottom: 8px;
    //     }

    //     & span:last-child {
    //       width: 160px;
    //       height: 20px;
    //       color: rgba(16, 16, 16, 100);
    //       font-size: 14px;
    //       font-weight: bold;
    //       font-family: SourceHanSansSC-bold;
    //     }
    //   }
    // }
    .descriptions-for-box {
      margin-top: 20px;

      .look-btn-menu {
        & span {
          height: 21px;
          color: rgba(105, 124, 255, 100);
          font-size: 14px;
          font-family: SourceHanSansSC-regular;
          cursor: pointer;
          margin-right: 8px;
        }

        &:last-child {
          margin-right: 0;
        }
      }
      // el描述列表样式修改
      ::v-deep {
        .el-descriptions-item__label.is-bordered-label {
          width: 13.5%;
          height: 48px;
          line-height: 20px;
          background-color: rgba(247, 247, 247, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          color: #000;
          padding-left: 15px;
        }
        .el-descriptions-item__content {
          width: 360px;
          height: 48px;
          line-height: 20px;
          background-color: rgba(255, 255, 255, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          color: #000;
          padding-left: 15px;
        }
      }
    }
  }
  .form-box {
    .form-text-box {
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }

    .file-upload-box-container {
      width: 360px;

      ::v-deep {
        .el-upload-list__item {
          background-color: #f5f7fa;
        }
      }
    }

    ::v-deep {
      // 表格操作栏
      .avue-form__menu {
        display: none;
      }
    }
  }
  .footer-container {
    position: fixed;
    height: 68px;
    width: calc(100% - 272px);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    bottom: 0;
    right: 15px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    .backBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      border: 1px solid rgba(187, 187, 187, 100);
      margin-right: 18px;
      cursor: pointer;
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      background-color: rgba(18, 119, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }
  }
}
</style>
