<template>
  <div class="inform-msg-contaier">
    <div class="inform-msg-item">
      <h2 class="inform-title">新增模板</h2>
      <div class="inform-msg-item-form">
        <avue-form ref="form" :option="option" v-model="information">
        </avue-form>
      </div>
    </div>
    <!-- 预警规则 -->
    <div class="inform-msg-item-pack">
      <el-collapse v-model="colList.activeName2">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="fromHeader" @keyup.stop="prevent()">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': colList.activeName2 == 1,
                  }"
                ></i>
                <span class="formLeft-title">预警规则</span>
              </div>
            </div>
          </template>
          <div class="inform-msg-item-form">
            <avue-form ref="form2" :option="option2" v-model="information">
              <template slot="dataRange" slot-scope="scope">
                <avue-select
                  v-if="scope.row.word"
                  v-model="scope.row.dataRange"
                  placeholder="请选择内容"
                  type="tree"
                  :dic="dataRangeOption"
                ></avue-select>
              </template>
              <template slot="result" slot-scope="scope">
                <div>
                  <div v-if="false" style="display: flex; align-items: center">
                    <avue-input
                      v-model="scope.row.min"
                      style="margin-right: 10px"
                      placeholder="最小值"
                    ></avue-input>
                    <avue-input
                      v-model="scope.row.max"
                      placeholder="最大值"
                    ></avue-input>
                  </div>
                  <div v-if="false">
                    <avue-input
                      v-model="scope.row.count"
                      append="元"
                    ></avue-input>
                  </div>
                  <div style="display: flex; align-items: center">
                    <avue-select
                      v-model="form"
                      placeholder="请选择内容"
                      type="tree"
                      :dic="dic"
                      style="width: 85px !important; margin-right: 10px"
                    ></avue-select>
                    <avue-input
                      v-model="scope.row.max"
                      append="天"
                    ></avue-input>
                  </div>
                </div>
              </template>
            </avue-form>
            <div class="inform-msg-form-tips">
              默认【触发时】发送预警，可在【定时发送】模块单独设置。
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!-- 发送对象 -->
    <div class="inform-msg-item-pack">
      <el-collapse v-model="colList.activeName3">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="fromHeader" @keyup.stop="prevent()">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': colList.activeName3 == 1,
                  }"
                ></i>
                <span class="formLeft-title">发送对象</span>
              </div>
            </div>
          </template>
          <div class="inform-msg-item-form">
            <avue-form ref="form3" :option="option3" v-model="information">
            </avue-form>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!-- 定时发送 -->
    <div class="inform-msg-item-pack">
      <el-collapse v-model="colList.activeName4">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="fromHeader" @keyup.stop="prevent()">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': colList.activeName4 == 1,
                  }"
                ></i>
                <span class="formLeft-title">定时发送</span>
              </div>
              <div @click.stop>
                <el-switch
                  @change="handleSwitchChange($event, 'form4')"
                  v-model="swithcList.switchVal4"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </div>
            </div>
          </template>
          <div class="inform-msg-item-form">
            <avue-form ref="form4" :option="option4" v-model="information">
            </avue-form>
            <div
              class="inform-msg-form-mask"
              :style="swithcList.switchVal4 ? 'display: none;' : ''"
            ></div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!-- 循环发送 -->
    <div class="inform-msg-item-pack">
      <el-collapse v-model="colList.activeName5">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="fromHeader" @keyup.stop="prevent()">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': colList.activeName5 == 1,
                  }"
                ></i>
                <span class="formLeft-title">循环发送</span>
              </div>
              <div @click.stop>
                <el-switch
                  @change="handleSwitchChange($event, 'form5')"
                  v-model="swithcList.switchVal5"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </div>
            </div>
          </template>
          <div class="inform-msg-item-form">
            <avue-form ref="form5" :option="option5" v-model="information">
              <template slot="frequency">
                <avue-input
                  type="number"
                  style="width: 112px; margin-right: 6px"
                  v-model="information.frequency"
                  placeholder="请输入内容"
                ></avue-input>
                <avue-select
                  style="width: 70px !important"
                  v-model="form"
                  placeholder=""
                  type="tree"
                  :dic="dic"
                ></avue-select>
              </template>
            </avue-form>
            <div
              class="inform-msg-form-mask"
              :style="swithcList.switchVal5 ? 'display: none;' : ''"
            ></div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 站内信 -->
    <div class="inform-msg-item-pack">
      <el-collapse v-model="colList.activeName6">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="fromHeader" @keyup.stop="prevent()">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': colList.activeName6 == 1,
                  }"
                ></i>
                <span class="formLeft-title">站内信</span>
              </div>
              <div @click.stop>
                <el-switch
                  @change="handleSwitchChange($event, 'form6')"
                  v-model="swithcList.switchVal6"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </div>
            </div>
          </template>
          <div class="inform-msg-item-form">
            <avue-form ref="form6" :option="option6" v-model="information">
            </avue-form>
            <div class="inform-msg-form-tips">
              变量格式：${name}；例如，尊敬的
              ${name}，您的快递已飞奔在路上，将今天 ${time}
              送达您的手里，请留意查收。
            </div>
            <div
              class="inform-msg-form-mask"
              :style="swithcList.switchVal6 ? 'display: none;' : ''"
            ></div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!-- 短信 -->
    <div class="inform-msg-item-pack">
      <el-collapse v-model="colList.activeName7">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="fromHeader" @keyup.stop="prevent()">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': colList.activeName7 == 1,
                  }"
                ></i>
                <span class="formLeft-title">短信</span>
              </div>
              <div @click.stop>
                <el-switch
                  @change="handleSwitchChange($event, 'form7')"
                  v-model="swithcList.switchVal7"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </div>
            </div>
          </template>
          <div class="inform-msg-item-form">
            <avue-form ref="form7" :option="option7" v-model="information">
            </avue-form>
            <div
              class="inform-msg-form-mask"
              :style="swithcList.switchVal7 ? 'display: none;' : ''"
            ></div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!-- 邮箱 -->
    <div class="inform-msg-item-pack">
      <el-collapse v-model="colList.activeName8" accordion>
        <el-collapse-item name="1">
          <template slot="title">
            <div class="fromHeader" @keyup.stop="prevent()">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': colList.activeName8 == 1,
                  }"
                ></i>
                <span class="formLeft-title">邮箱</span>
              </div>
              <div @click.stop>
                <el-switch
                  @change="handleSwitchChange($event, 'form8')"
                  v-model="swithcList.switchVal8"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </div>
            </div>
          </template>
          <div class="inform-msg-item-form">
            <avue-form ref="form8" :option="option8" v-model="information">
            </avue-form>
            <div
              class="inform-msg-form-mask"
              :style="swithcList.switchVal8 ? 'display: none;' : ''"
            ></div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <BasicFooter
      :btn-options="btnOptions"
      @click="handleClickEvent"
    ></BasicFooter>
  </div>
</template>

<script>
import BasicFooter from '@/components/basic-footer/index'
const btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
  {
    btnName: '确定',
    funName: 'Submit',
    type: 'primary',
  },
  {
    btnName: '启用',
    funName: 'Start',
    type: 'success',
  },
]
export default {
  components: { BasicFooter },
  data() {
    return {
      btnOptions, // 底部按钮
      dataRangeOption: [
        {
          label: '范围值',
          value: 0,
        },
        {
          label: '固定值',
          value: 1,
        },
        {
          label: '关联值',
          value: 2,
        },
      ], // 数据范围
      information: {
        title: '',
        goodsType: '',
        userType: '',
        cascader: '',
        word: '',
      },
      swithcList: {
        switchVal4: true, // 定时发送
        switchVal5: true, // 循环发送
        switchVal6: true, // 站内信开关
        switchVal7: true, // 短信开关
        switchVal8: true, // 邮箱开关
      },
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 114,
        labelPosition: 'left',
        column: [
          {
            label: '模板标题',
            prop: 'title',
            span: 11,
            placeholder: '请输入模板标题',
            rules: [
              {
                required: true,
                message: '请输入模板标题',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '模板类型',
            prop: 'goodsType',
            span: 11,
            placeholder: '请选择模板类型',
            rules: [
              {
                required: true,
                message: '请选择模板类型',
                trigger: 'change',
              },
            ],
            type: 'select',
            dicData: [
              {
                label: '应收账款',
                value: 0,
              },
              {
                label: '代采融资',
                value: 1,
              },
            ],
          },
        ],
      },
      option2: {
        labelWidth: 114,
        labelPosition: 'left',
        tip: '213123',
        tipPlacement: 'bottom',
        column: [
          {
            label: '业务流程',
            prop: 'userType',
            span: 8,
            type: 'select',
            placeholder: '请选择业务流程',
            rules: [
              {
                required: true,
                message: '请选择业务流程',
                trigger: 'change',
              },
            ],
            dicData: [
              {
                label: '应收账款',
                value: 0,
              },
              {
                label: '代采融资',
                value: 1,
              },
            ],
          },
          {
            label: '规则配置',
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            rules: [
              {
                required: true,
                message: '请添加规则配置',
                trigger: 'change',
              },
            ],
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                this.$message.success('新增回调')
                done({
                  input: '默认值',
                })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                done()
              },
              column: [
                {
                  label: '逻辑',
                  prop: 'select',
                  type: 'select',
                  width: 105,
                  dicData: [
                    {
                      label: '测试1',
                      value: 1,
                    },
                    {
                      label: '测试2',
                      value: 2,
                    },
                  ],
                },
                {
                  label: '业务字段',
                  prop: 'word',
                  type: 'select',
                  dicData: [
                    {
                      label: '测试1',
                      value: 1,
                    },
                    {
                      label: '测试2',
                      value: 2,
                    },
                  ],
                },
                {
                  label: '比较符号',
                  prop: 'symbol',
                  type: 'select',
                  width: 90,
                  dicData: [
                    {
                      label: '=',
                      value: 1,
                    },
                    {
                      label: '>',
                      value: 2,
                    },
                    {
                      label: '<',
                      value: 3,
                    },
                    {
                      label: '>=',
                      value: 4,
                    },
                    {
                      label: '<=',
                      value: 5,
                    },
                  ],
                },
                {
                  label: '数据范围',
                  prop: 'dataRange',
                  slot: true,
                  width: 110,
                },
                {
                  label: '值',
                  prop: 'result',
                  slot: true,
                },
                {
                  label: '执行顺序',
                  prop: 'executeIndex',
                  type: 'input',
                  width: 90,
                },
              ],
            },
          },
        ],
      },
      option3: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 114,
        labelPosition: 'left',
        column: [
          {
            label: '用户类型',
            prop: 'userType',
            span: 8,
            placeholder: '请选择用户类型',
            rules: [
              {
                required: true,
                message: '请选择用户类型',
                trigger: 'change',
              },
            ],
            type: 'select',
            dicData: [
              {
                label: '应收账款',
                value: 0,
              },
              {
                label: '代采融资',
                value: 1,
              },
            ],
          },
          {
            label: '接收对象',
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            rules: [
              {
                required: true,
                message: '请添加接收对象',
                trigger: 'change',
              },
            ],
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                this.$message.success('新增回调')
                done({
                  input: '默认值',
                })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                done()
              },
              column: [
                {
                  label: '类型',
                  prop: 'select',
                  type: 'select',
                  dicData: [
                    {
                      label: '测试1',
                      value: 1,
                    },
                    {
                      label: '测试2',
                      value: 2,
                    },
                  ],
                },
                {
                  label: '值',
                  prop: 'cascader',
                  type: 'tree',
                  multiple: true,
                  dicData: [
                    {
                      value: 'shejiyuanze',
                      label: '设计原则',
                      children: [
                        {
                          value: 'yizhi',
                          label: '一致',
                        },
                        {
                          value: 'fankui',
                          label: '反馈',
                        },
                        {
                          value: 'xiaolv',
                          label: '效率',
                        },
                        {
                          value: 'kekong',
                          label: '可控',
                        },
                      ],
                    },
                    {
                      value: 'daohang',
                      label: '导航',
                      children: [
                        {
                          value: 'cexiangdaohang',
                          label: '侧向导航',
                        },
                        {
                          value: 'dingbudaohang',
                          label: '顶部导航',
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
      option4: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 114,
        labelPosition: 'left',
        column: [
          {
            label: '发送时间',
            prop: 'sendtiTime',
            span: 8,
            type: 'datetime',
            placeholder: '选择时间',
            rules: [
              {
                required: true,
                message: '请选择时间',
                trigger: 'change',
              },
            ],
          },
        ],
      },
      option5: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 114,
        labelPosition: 'left',
        column: [
          {
            label: '循环次数',
            prop: 'num',
            type: 'input',
            append: '次',
            placeholder: ' ',
            span: 6,
            rules: [
              {
                required: true,
                message: '请输入循环次数',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '循环频率',
            prop: 'frequency',
            formslot: true,
            rules: [
              {
                required: true,
                message: '请输入循环次数',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '排除双休日',
            prop: 'excludeWeekends',
            type: 'switch',
            activeColor: '#13ce66',
            rules: [
              {
                required: true,
                message: '请输入循环次数',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      option6: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 114,
        labelPosition: 'left',
        column: [
          {
            label: '内容模板',
            prop: 'content',
            type: 'textarea',
            span: 24,
            placeholder: '请输入内容模板',
            rules: [
              {
                required: true,
                message: '请输入内容模板',
                trigger: 'change',
              },
            ],
            hide: true,
          },
        ],
      },
      option7: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 114,
        labelPosition: 'left',
        column: [
          {
            label: '内容模板',
            prop: 'title',
            type: 'select',
            span: 8,
            placeholder: '请选择短信模板',
            rules: [
              {
                required: true,
                message: '请选择短信模板',
                trigger: 'change',
              },
            ],
          },
        ],
      },
      option8: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 114,
        labelPosition: 'left',
        column: [
          {
            label: '内容模板',
            prop: 'content2',
            type: 'textarea',
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入内容模板',
                trigger: 'change',
              },
            ],
            hide: true,
            component: 'AvueUeditor',
            options: {
              action: '/api/blade-resource/oss/endpoint/put-file',
              props: {
                res: 'data',
                url: 'link',
              },
            },
          },
        ],
      },
      // 折叠的开关
      colList: {
        activeName2: '1',
        activeName3: '1',
        activeName4: '1',
        activeName5: '1',
        activeName6: '1',
        activeName7: '1',
        activeName8: '1',
      },
      dic: [
        {
          label: '天',
          value: 0,
        },
        {
          label: '时',
          value: 1,
        },
        {
          label: '分',
          value: 2,
        },
        {
          label: '秒',
          value: 3,
        },
        {
          label: '周',
          value: 4,
        },
        {
          label: '月',
          value: 5,
        },
        {
          label: '年',
          value: 6,
        },
      ],
    }
  },
  methods: {
    // 底部按钮的操作
    handleClickEvent(name) {
      switch (name) {
        case 'Revert':
          this.$router.go(-1)
          break
        case 'Submit':
          this.submitForm()
          break
        case 'Start':
          break
      }
    },

    // 用一个按钮去控制多个表单的提交
    submitForm() {
      let formArr = [
        'form',
        'form2',
        'form3',
        'form4',
        'form5',
        'form6',
        'form7',
        'form8',
      ]
      let formList = [...formArr]
      // 当相对应的开关关闭时，对应的表单提示时，不做校验
      if (!this.swithcList.switchVal4) {
        formArr = formList.filter(item => item !== 'form4')
      } else if (!this.swithcList.switchVal5) {
        formArr = formList.filter(item => item !== 'form5')
      } else if (!this.swithcList.switchVal6) {
        formArr = formList.filter(item => item !== 'form6')
      } else if (!this.swithcList.switchVal7) {
        formArr = formList.filter(item => item !== 'form7')
      } else if (!this.swithcList.switchVal8) {
        formArr = formList.filter(item => item !== 'form8')
      }
      let resultArr = []
      const _this = this
      // 处理单个表单的校验
      function checkForm(formName) {
        let result = new Promise((resolve, reject) => {
          _this.$refs[formName].validate(valid => {
            if (valid) {
              resolve
            } else {
              reject()
            }
          })
        })
        resultArr.push(result)
      }
      formArr.forEach(item => {
        checkForm(item)
      })
      Promise.all(resultArr)
        .then(() => {
          console.log(23333333, '22222')
        })
        .catch(() => {
          this.$message.error('请完善表单！')
          return
        })
    },
    handleSwitchChange(val, formName) {
      this.$refs[formName] && this.$refs[formName].clearValidate()
    },
  },
}
</script>

<style lang="scss" scoped>
.inform-msg-contaier {
  padding-bottom: 128px !important;
  .inform-msg-item {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 20px;
    .inform-title {
      height: 24px;
      text-align: left;
      color: #101010;
      font-size: 16px;
      font-weight: bold;
      line-height: 24px;
      display: flex;
      align-items: center;
      margin-bottom: 34px;
      &::before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 18px;
        background-color: #1277ff;
        border-radius: 15px;
        margin-right: 4px;
      }
    }
  }
  .inform-msg-item-pack {
    padding: 24px 24px 24px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 20px;
    .fromHeader {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .formLeft-title {
        color: #000;
        font-size: 16px;
        font-weight: bold;
        margin-left: 4px;
      }
    }
    .inform-msg-item-form {
      position: relative;
      .inform-msg-form-tips {
        font-size: 14px;
        font-weight: 500;
        color: #999;
        margin-left: 114px;
      }
      .inform-msg-form-mask {
        cursor: not-allowed;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1000;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
      }
    }
    ::v-deep {
      .avue-form__row:nth-last-child(2) {
        .el-form-item {
          margin-bottom: 10px !important;
        }
      }
      i.el-icon-caret-bottom {
        font-size: 150%;
        transform: rotate(-90deg);
        transition: transform 0.4s;
      }
      .i-active {
        transform: rotate(0deg) !important;
      }
      .el-collapse-item__arrow {
        display: none;
      }
      .el-collapse {
        border-top: none;
      }
      .el-collapse-item__header {
        height: 22px;
        line-height: 22px;
      }
      .el-collapse-item__content {
        padding-bottom: 0px !important;
      }
      .el-collapse-item__wrap {
        margin-top: 11px !important;
      }
      .el-collapse-item__wrap,
      .el-collapse-item__header {
        border-bottom: none;
      }
    }
  }
  ::v-deep {
    .avue-form__group--flex {
      display: flex;
      justify-content: space-between;
    }
    .avue-form__group .avue-form__menu {
      display: none;
    }
  }
}
</style>
