<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row }" slot="companyName">
        <span style="color: #409eff">{{ row.companyName }}</span>
      </template>
      <template slot-scope="{ row }" slot="name">
        <span style="color: #409eff">{{ row.name }}</span>
      </template>
      <template slot-scope="{ row }" slot="financingModel">
        <Tag
          :name="row.financingModel == 0 ? '开单企业付息' : '融资用户付息'"
          color="#00072A"
          backgroundColor="#EAECF1"
          borderColor="transparent"
          minWidth="64px"
          radius
        />
      </template>
      <template slot-scope="{ row }" slot="cloudBillAmount">
        <span>{{ row.cloudBillAmount | formatMoney }}</span>
      </template>

      <template slot-scope="{ row }" slot="status">
        <span :class="getColorIcon(row.status)">{{
          stateConfig[row.status]
        }}</span>
      </template>
      <template slot-scope="{ row, type, size }" slot="menu">
        <div style="display: flex">
          <el-button @click="handleBtnUseDetail(row)" type="text" size="small"
            >详情</el-button
          >
          <el-button
          v-if="row.status === 5"
          :size="size"
          :type="type"
          @click="handleClickLine(row)"
          >轨迹</el-button
        >
        </div>
      </template>
    </avue-crud>
    <DialogPreviewProof
      ref="DialogPreviewProofRef"
      :invoiceTypeMap="invoiceTypeMap"
      :proofTypeMap="proofTypeMap"
    />
    <DialogUseDetail ref="DialogUseDetailRef" />
    <DialogReplaymentDetail ref="DialogReplaymentDetailRef" />
  </basic-container>
</template>

<script>
import { mapGetters } from 'vuex'
import { getList } from '@/api/cloud/cloudassents'
import DialogPreviewProof from '@/views/customer/archives/components/ModuleAssetInformation/components/DialogPreviewProof/index.vue'
import DialogUseDetail from '@/views/customer/archives/components/ModuleAssetInformation/components/DialogUseDetail/index.vue'
import DialogReplaymentDetail from '@/views/customer/archives/components/ModuleAssetInformation/components/DialogRepaymentDetail/index.vue'
import Tag from '@/views/customer/archives/components/Tag/index.vue'
import { requestProcessInfo } from '@/api/customer/archives/archive'
import { routerMapKeyToPath } from '@/views/business/config'
import { getDictionary } from '@/api/system/dictbiz'
import { proofStatusMap } from '@/views/customer/tradeDetail/config'

const stateConfig = {
  0: '待签收',
  1: '签收已拒绝',
  2: '持单中',
  3: '超时作废',
  4: '拆分中',
  5: '已拆分',
  6: '到期未收款',
  7: '到期已收款',
  8: '逾期未收款',
  9: '逾期已收款',
  10: '清分中',
}
export default {
  name: 'AssetsManagerSalesReceivable',
  components: {
    DialogPreviewProof,
    DialogUseDetail,
    DialogReplaymentDetail,
    Tag,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        dialogClickModal: false,
        menuWidth: 250,
        menuAlign: 'center',
        menuHeaderAlign: 'center',
        column: [
          {
            label: '云信编号',
            prop: 'cloudCode',
            search: true,
          },
          {
            label: '开单企业',
            prop: 'companyName',
            search: true,
          },
          {
            label: '收单企业',
            prop: 'name',
            search: true,
          },
          {
            label: '融资付息模式',
            prop: 'financingModel',
            minWidth: '100px',
            type: 'select',
          },
          {
            label: '云信金额(元)',
            prop: 'cloudBillAmount',
          },
          {
            label: '到期日期',
            prop: 'endDate',
          },
          {
            label: '上链哈希值',
            prop: 'transactionAddress',
          },
          {
            label: '状态',
            prop: 'status',
            search: true,
            type: 'select',
            dicData: [
              {
                label: '待签收',
                value: 0,
              },
              {
                label: '签收已拒绝',
                value: 1,
              },
              {
                label: '持单中',
                value: 2,
              },
              {
                label: '超时作废',
                value: 3,
              },
              {
                label: '拆分中',
                value: 4,
              },
              {
                label: '已拆分',
                value: 5,
              },
              {
                label: '到期未收款',
                value: 6,
              },
              {
                label: '到期已收款',
                value: 7,
              },
              {
                label: '逾期未收款',
                value: 8,
              },
              {
                label: '逾期已收款',
                value: 9,
              },
            ],
          },
        ],
      },
      data: [],
      // 字典
      proofStatusMap,
      invoiceTypeMap: {},
      proofTypeMap: {},
      stateConfig,
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.material_add, false),
        viewBtn: this.vaildData(this.permission.material_view, false),
        delBtn: this.vaildData(this.permission.material_delete, false),
        editBtn: this.vaildData(this.permission.material_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    getFormatItem(supportedFormat) {
      return supportedFormat.split(',')
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },

    getColorIcon(state) {
      let iconColor = ''
      if ([0, 4, 6].includes(state)) {
        iconColor = 'text-blue'
      } else if ([1, 3, 5, 7, 9].includes(state)) {
        iconColor = 'text-gray'
      } else if ([2].includes(state)) {
        iconColor = 'text-green'
      } else if ([8].includes(state)) {
        iconColor = 'text-red'
      }
      return iconColor
    },
    // 操作 - 使用明细
    handleBtnUseDetail(row) {
      this.$router.push(
        `/product/cloudAssetsDetail/${Buffer.from(
          JSON.stringify({ id: row.id })
        ).toString('base64')}`
      )
    },
    // 查看云信轨迹
    handleClickLine(row) {
      this.$router.push({
        path: '/product/cloudTreeOrg',
        query: { cloudCode: row.cloudCode },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-tabs__header {
    margin: 0 0 2px;
  }
}
.text-blue {
  color: #697cff;
}
.text-gray {
  color: #9d9e9d;
}
.text-green {
  color: #1fc374;
}
.text-red {
  color: #ff2929;
}
</style>
