<template>
  <div class="recDetail-box">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="'订单详情'"></avue-title>
          </div>
        </avue-affix>
        <!--  -->
        <div class="apply-container">
          <div class="left">
            <div class="form-item">
              <span class="title">云信编号：</span>
              <span class="value">{{ cloudAssentList.cloudCode }}</span>
            </div>
            <div class="form-item">
              <span class="title">云信金额：</span>
              <span class="value">
                <span style="color: rgba(105, 124, 255, 100)">
                  {{
                  cloudAssentList.name
                  }}
                </span>
              </span>
            </div>
            <div class="form-item">
              <span class="title">到期日：</span>
              <span class="value">{{ cloudAssentList.endDate }}</span>
            </div>
          </div>
          <div class="right">
            <div class="right-icon-box">
              <SvgIcon
                :icon-class="getInvoiceIconClass(cloudAssentList.status)"
                style="font-size: 40px"
                :style="`fill: ${getTitleStyle(cloudAssentList.status)}`"
              ></SvgIcon>
              <span class="status-text">
                {{
                getTitleText(cloudAssentList.status)
                }}
              </span>
            </div>
            <div class="desc">
              <div v-if="cloudAssentList.status == 0">
                <span>云信待签收,将在</span>
                <span class="des-time">{{ countDown }}</span>
                <span>后超时作废</span>
              </div>
              <div v-else-if="cloudAssentList.status == 1">
                <span style="color: #7d7d7d">云信被拒绝,拒绝原因: {{ cloudAssentList.remark }}</span>
              </div>
              <div v-else-if="cloudAssentList.status == 2">
                <span style="color: #7d7d7d">云信已签收</span>
              </div>
              <div v-else-if="cloudAssentList.status == 3">
                <span style="color: #7d7d7d">云信超时未签收,已作废</span>
              </div>
              <div v-else-if="cloudAssentList.status == 4">
                <span style="color: #7d7d7d">当前云信拆分中,等待收单企业签收</span>
              </div>
              <div v-else-if="cloudAssentList.status == 5">
                <span style="color: #7d7d7d">云信已拆分</span>
              </div>
              <div v-else-if="cloudAssentList.status == 6">
                <span style="color: #7d7d7d">该云信已到期,等待开单企业付款中</span>
              </div>
              <div v-else-if="cloudAssentList.status == 7">
                <span style="color: #7d7d7d">开单企业已准时付款</span>
              </div>
              <div v-else-if="cloudAssentList.status == 8">
                <span style="color: #7d7d7d">
                  该云信已到期,当前逾期{{
                  cloudAssentList0overDay
                  }}天,等待开单企业付款中
                </span>
              </div>
              <div v-else-if="cloudAssentList.status == 9">
                <span style="color: #7d7d7d">开单企业逾期付款,共逾期{{ cloudAssentList0overDay }}天</span>
              </div>
            </div>
          </div>
        </div>
        <div class="tabBar-box">
          <div
            class="tabBar-for-box"
            :class="{ 'active-box': activeName == item.value }"
            v-for="item in tabList"
            :key="item.value"
            @click="activeName = item.value"
          >{{ item.label }}</div>
        </div>
      </template>
    </basic-container>

    <div class="content-box">
      <div class="content-item" v-if="activeName == 0">
        <div class="item-content-title">云信信息</div>
        <el-descriptions title :column="3" border class="table-descriptions">
          <el-descriptions-item label="云信金额">
            {{
            cloudAssentList.amount
            }}
          </el-descriptions-item>
          <el-descriptions-item label="开单企业">
            {{
            cloudAssentList.companyName
            }}
          </el-descriptions-item>
          <el-descriptions-item label="开单日期">
            {{
            cloudAssentList.startDate
            }}
          </el-descriptions-item>
          <el-descriptions-item label="承诺付款日">
            {{
            cloudAssentList.endDate
            }}
          </el-descriptions-item>
          <el-descriptions-item label="账期">
            {{
            cloudAssentList.termsDays > 0 ? cloudAssentList.termsDays : 0
            }}天
          </el-descriptions-item>
          <el-descriptions-item label="融资付息模式">
            {{
            cloudAssentList.financingModel == 0
            ? '开单企业付息'
            : '融资用户付息'
            }}
          </el-descriptions-item>

          <el-descriptions-item label="融资年利率">{{ cloudAssentList.financingApr }}%</el-descriptions-item>
          <el-descriptions-item label="日利率">
            {{
            $numChuFun(cloudAssentList.financingApr || 0, 360, 3)
            }}%
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            {{
            cloudAssentList.remark
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="content-item" v-else-if="activeName == 1">
        <div class="item-content-title">拆分列表</div>

        <div class="split-list">
          <div class="split-item" v-for="item in splitList" :key="item.id">
            <div class="split-item-content">
              <span>云信编号:</span>
              <span class="split-blue">{{ item.cloudCode }}</span>
            </div>
            <div class="split-item-content">
              <span>转单方:</span>
              <span class="split-black">{{ item.companyName }}</span>
            </div>
            <div class="split-item-content">
              <span>收单方:</span>
              <span class="split-black">{{ item.name }}</span>
            </div>
            <div class="split-item-content">
              <span>金额</span>
              <span class="split-red">￥{{ item.cloudBillAmounts }}</span>
            </div>
            <span class="split-btn" @click="handleOpenDialog(item)">查看详情</span>
          </div>
        </div>
      </div>
      <div class="content-item" v-else>
        <div class="item-content-title">合同信息</div>
      </div>
    </div>

    <el-dialog title :visible.sync="visible" width="432px" append-to-body>
      <div class="dialog-main">
        <div class="cloud-list">
          <div class="cloud-item">
            <span>转让状态</span>
            <span
              class="cloud-status"
              :class="getStatusColor(splitConfig.status)"
            >{{ getStatusText(splitConfig.status) }}</span>
          </div>
          <div class="cloud-item">
            <span>收单用户</span>
            <span class="cloud-subtitle">{{ splitConfig.name }}</span>
          </div>
          <div class="cloud-item">
            <span>收单金额</span>
            <span class="cloud-subtitle">¥{{ splitConfig.cloudBillAmount }}元</span>
          </div>
          <div class="cloud-item">
            <span>云信编号</span>
            <span class="cloud-subtitle">{{ splitConfig.cloudCode }}</span>
          </div>
          <div class="cloud-item">
            <span>转让编号</span>
            <span class="cloud-subtitle">{{ splitConfig.parentCloudNo }}</span>
          </div>
          <div class="cloud-item">
            <span>创建时间</span>
            <span class="cloud-subtitle">{{ splitConfig.createTime }}</span>
          </div>
          <div class="cloud-item">
            <span>签收时间</span>
            <span class="cloud-subtitle">{{ splitConfig.updateTime }}</span>
          </div>
          <div class="cloud-item" v-if="splitConfig.status === 3">
            <span>状态说明</span>
            <span class="cloud-subtitle">转让被拒签，拒签原因：{{ splitConfig.remark }}</span>
          </div>
        </div>
        <div class="cloud-dialog-contract">
          <div class="contract-name">合同信息</div>
          <div class="contract-list">
            <div class="contract-item">
              <div class="contract-item-left">
                <span class="cloud-status cloud-status-green">已完成</span>
                <span class="contract-item-title">云信转让协议</span>
              </div>
              <div class="contract-item-right">
                <span>查看协议</span>
                <svg-icon icon-class="icon-youjiantou" style="fill: #0d55cf; font-size: 16px"></svg-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="visible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <BasicFooter :btn-options="btnOptions" @click="handleClickEvent"></BasicFooter>
  </div>
</template>

<script>
import BasicFooter from '@/components/basic-footer/index'
import ContractInformation from '@/views/product/components/contractInformation.vue'
import { formatMoney } from '@/util/filter.js'
import { getCountDownHtml } from '@/util/util'
import {
  getCloudAssetsDetail,
  getCloudSplitList,
} from '@/api/cloud/cloudassents'
import dayjs from 'dayjs'

let btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
  {
    btnName: '轨迹',
    funName: 'Line',
    type: 'primary',
  },
]
let tabList = [
  {
    label: '收单信息',
    value: '0',
  },
  {
    label: '拆分信息',
    value: '1',
  },
  {
    label: '合同信息',
    value: '2',
  },
]
export default {
  components: {
    ContractInformation,
    BasicFooter,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        const params = JSON.parse(Buffer.from(val, 'base64').toString())
        this.getCloudAssetsDetail({ id: params.id })
      },
      immediate: true,
    },
    activeName: {
      handler(val) {
        if (val == 1) {
          this.getCloudSplitList({ cloudCode: this.cloudAssentList.cloudCode })
        }
      },
    },
    'cloudAssentList.status': {
      handler(val) {
        let list = [...btnOptions]
        let list2 = [...tabList]
        if (val != 5) {
          this.btnOptions = list.filter(item => item.funName != 'Line')
        } else {
          this.btnOptions = list
        }
        if ([0, 1, 3].includes(val)) {
          this.tabList = list2.filter(item => item.value != 1)
        } else {
          this.tabList = list2
        }
      },
    },
  },
  data() {
    return {
      // 状态消息数据
      msgStatus: 1,
      mesageData: null,
      // 申请信息数据
      applicationInData: {},
      // 借款信息数据
      borrowingInformationData: [],
      // 费用信息数据
      costInformationData: [],
      // 合同信息数据
      getByContractIdData: [],
      // 审批信息数据
      approveData: [],

      activeName: '0',
      splitConfig: {},
      // form: {},
      iouId: null,
      rebuttomShow: false,
      waiting: false,
      variables: {},
      sum: 0,
      sum1: 0,
      countDown: '15天00时00分00秒',
      inTervalTime: null,
      cloudAssentList: {},
      splitList: [],
      tabList: [],
      visible: false,
      btnOptions: [],
    }
  },
  destroyed() {
    clearInterval(this.inTervalTime)
  },
  methods: {
    // 状态请求
    getCloudAssetsDetail(params) {
      this.waiting = true
      getCloudAssetsDetail(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          this.waiting = false
          this.cloudAssentList = {
            ...data.data,
            termsDays: dayjs(data.data.endDate).diff(
              data.data.startDate,
              'day'
            ),
            overDay: dayjs().diff(data.data.startDate, 'day'),
          }

          if (data.data.status === 0) {
            this.inTervalTime = setInterval(() => {
              const nowTime = new Date().valueOf() // 当前时间
              const dayTime = data.data.day * 24 * 60 * 60 * 1000 // 过期天数
              const createTime = new Date(data.data.updateTime).valueOf() // 创建时间

              if (dayTime + createTime <= nowTime) {
                clearInterval(this.inTervalTime)
              }
              this.countDown = getCountDownHtml(dayTime + createTime - nowTime) // 倒计时
            }, 1000)
          }
        }
      })
    },

    getCloudSplitList(params) {
      getCloudSplitList(params).then(({ data }) => {
        let list = []
        if (data.code === 200) {
          // (data?.data ?? []).
          // @Bug # 4464不应该展示转让失败数据
          list = data.data.reduce((pre, cur) => {
            if (cur.status === 1) return pre
            pre.push({
              ...cur,
              cloudBillAmounts: formatMoney(cur.cloudBillAmount),
            })
            return pre
          }, [])
          // for (const item of data.data) {
          //   list.push({
          //     ...item,
          //     cloudBillAmounts: formatMoney(item.cloudBillAmount),
          //   })
          // }
        }
        this.splitList = list
      })
    },
    handleOpenDialog(row) {
      this.visible = true
      this.splitConfig = {
        ...row,
        cloudBillAmount: formatMoney(row.cloudBillAmount),
      }
    },
    // 对应的标题
    getTitleText(state) {
      let text = ''
      if (state == 0) {
        text = '待签收'
      } else if (state === 1) {
        text = '签收拒绝'
      } else if (state === 2) {
        text = '持单中'
      } else if (state === 3) {
        text = '超时作废'
      } else if (state === 4) {
        text = '拆分中'
      } else if (state === 5) {
        text = '已拆分'
      } else if (state === 6) {
        text = '到期未收款'
      } else if (state === 7) {
        text = '到期已收款'
      } else if (state === 8) {
        text = '逾期未收款'
      } else if (state === 9) {
        text = '逾期已收款'
      }
      return text
    },
    // 对应其相关的icon
    getInvoiceIconClass(state) {
      let iconClass = ''
      switch (state) {
        case 0: //
          iconClass = 'icon-dengdai1'
          break
        case 1: //  icon-jinggao1
          iconClass = 'icon-delete-filling'
          break
        case 2:
          iconClass = 'icon-dengdai1'
          break
        case 3: //
          iconClass = 'icon-delete-filling'
          break
        case 4: //
          iconClass = 'icon-dengdai1'
          break
        case 5: //
          iconClass = 'icon-fenlei'
          break
        case 6: //
          iconClass = 'icon-dengdai1'
          break
        case 7:
          iconClass = 'icon-chenggong1'
          break
        case 8: //
          iconClass = 'icon-jinggao1'
          break
        case 9: //
          iconClass = 'icon-chenggong1'
          break
      }
      return iconClass
    },
    // 对应的颜色
    getTitleStyle(state) {
      let iconStyle = ''
      switch (state) {
        case 0: //
          iconStyle = '#697CFF'
          break
        case 1: //
          iconStyle = '#C1C1C1'
          break
        case 2: //
          iconStyle = '#1FC374'
          break
        case 3: //
          iconStyle = '#C1C1C1'
          break
        case 4: //
          iconStyle = '#697CFF'
          break
        case 5: //
          iconStyle = '#C1C1C1'
          break
        case 6: //
          iconStyle = '#697CFF'
          break
        case 7: //
          iconStyle = '#C1C1C1'
          break
        case 8: //
          iconStyle = '#FF2929'
          break
        case 9: //
          iconStyle = '#C1C1C1'
          break
      }

      return iconStyle
    },

    getStatusColor(state) {
      let color = ''
      switch (state) {
        case 1:
          color = 'cloud-status-gray'
          break
        case 2:
          color = 'cloud-status-green'
          break
        case 3:
          color = 'cloud-status-blue'
          break
        case 5:
          color = 'cloud-status-green'
          break
      }
      return color
    },

    getStatusText(state) {
      let text = ''
      if (
        this.splitConfig.name === this.splitConfig.companyName &&
        state == 2
      ) {
        text = '剩余云信'
      } else {
        if (state == 0) {
          text = '转让待收'
        } else if (state == 1) {
          text = '转让失败'
        } else if (state == 2) {
          text = '已转让'
        } else if (state == 3) {
          text = '转让失败'
        } else if (state == 4) {
          text = '转让待收'
        } else if (state == 5) {
          text = '已转让'
        }
      }
      return text
    },

    // 底部按钮操作
    handleClickEvent(name) {
      switch (name) {
        case 'Revert':
          this.$router.$avueRouter.closeTag()
          this.$router.replace('/cloudassets/cloudassentmanage')
          break
        case 'Line':
          this.$router.push({
            path: '/product/cloudTreeOrg',
            query: { cloudCode: this.cloudAssentList.cloudCode },
          })
          break
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.recDetail-box {
  padding-bottom: 168px !important;
  .header {
    width: 100%;
    height: 50px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 10px;
    margin: 0 -20px;
  }

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 157px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .status-text {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-weight: bold;
        margin-top: 2px;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;

        .des-time {
          color: #ff5656;
        }
      }
    }
  }

  .tabBar-box {
    margin-top: 20px;
    display: flex;
    border: 1px solid rgba(105, 124, 255, 100);
    border-radius: 6px;
    overflow: hidden;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border-right: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;
      font-size: 14px;
      color: #449bfc;

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
        border-right: none;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }
    .active-box {
      background-color: #449bfc;
      color: #fff;
      &:hover {
        background-color: #449bfc;
      }
    }
  }

  .menu-box {
    display: flex;
    justify-content: center;
    align-items: center;

    .go-back {
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      border: 1px solid rgba(187, 187, 187, 100);
      padding: 4px 10px;
      box-sizing: border-box;
      cursor: pointer;
      margin-right: 18px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.content-box {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  .content-item {
    .item-content-title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 16px;
      font-weight: 600;
    }
    .split-list {
      display: flex;
      align-items: center;
      margin-bottom: -23px;
      .split-item {
        padding: 20px 16px;
        border-radius: 8px;
        background-color: #f7f7f7;
        width: 350px;
        height: 210px;
        margin-bottom: 23px;
        box-sizing: border-box;
        &:not(:nth-of-type(3n)) {
          margin-right: 23px;
        }
        .split-item-content {
          display: flex;
          margin-bottom: 15px;
          & span:first-child {
            display: block;
            text-align: left;
            width: 75px;
            max-width: 75px;
            font-size: 14px;
            font-weight: 500;
            color: #7d7d7d;
            line-height: 20px;
            margin-right: 12px;
          }
          & span:last-child {
            display: block;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            width: 230px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .split-blue {
            color: #697cff;
          }
          .split-black {
            color: #00072a;
          }
          .split-red {
            color: #ff2929;
          }
        }
        .split-btn {
          cursor: pointer;
          display: block;
          width: 90px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          color: rgba(16, 16, 16, 100);
          font-size: 14px;
          border-radius: 4px;
          border: 1px solid #bbb;
        }
      }
    }
  }
}

.dialog-main {
  .cloud-list {
    box-sizing: border-box;
    padding-bottom: 24px;
    border-bottom: 1px solid #f1f2f4;
    .cloud-item {
      display: flex;
      align-items: center;
      & span:nth-of-type(1) {
        min-width: 60px;
        margin-right: 20px;
        color: #8a94a6;
        line-height: 20px;
        font-size: 14px;
        font-weight: 500;
      }
      .cloud-subtitle {
        color: #0a1f44;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
      }
    }
    .cloud-item:last-child {
      display: flex;
      align-items: flex-start;
    }
    .cloud-item + .cloud-item {
      margin-top: 24px;
    }
  }

  .cloud-dialog-contract {
    margin-top: 24px;
    .contract-name {
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #8a94a6;
      margin-bottom: 12px;
    }
    .contract-list {
      .contract-item {
        box-sizing: border-box;
        border: 1px solid #e1e4e8;
        border-radius: 8px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .contract-item-left {
          .contract-item-title {
            margin-left: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #0a1f44;
            line-height: 20px;
          }
        }
        .contract-item-right {
          display: flex;
          align-items: center;
          & span {
            display: block;
            line-height: 20px;
            color: #0d55cf;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
      .contract-item + .contract-item {
        margin-top: 12px;
      }
    }
  }
  .cloud-status {
    display: inline-block;
    border-radius: 100px;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    padding: 4px 10px;
  }
  .cloud-status-green {
    color: #00865a;
    background-color: #cff8eb;
  }
  .cloud-status-blue {
    color: #0d55cf;
    background-color: #ebf5ff;
  }
  .cloud-status-gray {
    color: #8a94a6;
    background-color: #f8f9fb;
  }
}
</style>
