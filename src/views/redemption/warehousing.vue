<template>
  <div class="warehou-sing">
    <!-- 头部-步骤条 -->
    <basic-container>
      <div class="header-box">
        <div class="header-title">货物入库</div>
        <div class="long-string" />
        <div class="article-steps-box">
          <el-steps
            :active="warehouSingActive"
            finish-status="success"
            align-center
          >
            <el-step title="填写入库信息"></el-step>
            <el-step title="信息确认"></el-step>
          </el-steps>
        </div>
      </div>
    </basic-container>
    <!-- 基本信息 -->
    <basic-container
      v-if="warehouSingActive === 0"
      style="margin-bottom: 100px"
    >
      <div class="form-box">
        <avue-form v-model="form" :option="option" ref="formAvue">
          <template slot-scope="{ value }" slot="financingNumber">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="financingUser">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ row }" slot="goodsSpec">
            <div class="form-text-box">{{ row.goodsSpec }}</div>
          </template>
          <template slot-scope="{ row }" slot="quantity">
            <el-input-number
              v-model="row.quantity"
              controls-position="right"
              :placeholder="row.maxNum ? '请输入 入库数量' : '无'"
              :min="0"
              :max="row.maxNum"
              :disabled="!row.maxNum"
              style="width: 100%"
            />
          </template>
          <template slot="warehouseInNum">
            <el-input-number
              v-model="form.warehouseInNum"
              controls-position="right"
              placeholder="请输入 入库数量"
              :min="1"
              :max="wareMaxNum"
              style="width: 100%"
            />
          </template>
          <template slot="testReportAttachId">
            <div class="file-upload-box-container">
              <el-upload
                class="file-upload"
                drag
                :headers="{ 'Blade-Auth': Basic }"
                action="/api/blade-resource/oss/endpoint/put-file-kv"
                multiple
                :on-success="
                  (response, file, fileList) => {
                    return handleUpSuccess(response, file, fileList, 1)
                  }
                "
                :on-remove="
                  (file, fileList) => {
                    return handleFileRemove(file, fileList, 1)
                  }
                "
                :on-preview="handleFilePreview"
                :file-list="testReportAttachIdArr"
                :disabled="false"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  只能上传jpg/jpeg/png/pdf文件，且不超过20M
                </div>
              </el-upload>
            </div>
          </template>
          <template slot="warehouseProof">
            <div class="file-upload-box-container">
              <el-upload
                class="file-upload"
                drag
                :headers="{ 'Blade-Auth': Basic }"
                action="/api/blade-resource/oss/endpoint/put-file-kv"
                multiple
                :on-success="
                  (response, file, fileList) => {
                    return handleUpSuccess(response, file, fileList, 2)
                  }
                "
                :on-remove="
                  (file, fileList) => {
                    return handleFileRemove(file, fileList, 2)
                  }
                "
                :on-preview="handleFilePreview"
                :file-list="warehouseProofArr"
                :disabled="false"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  只能上传jpg/jpeg/png/pdf文件，且不超过20M
                </div>
              </el-upload>
            </div>
          </template>
        </avue-form>
      </div>
    </basic-container>
    <!-- 二次确认信息 -->
    <basic-container v-else style="margin-bottom: 100px">
      <div class="form-box">
        <avue-form v-model="form" :option="option">
          <template slot-scope="{ value }" slot="financingNumber">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="financingUser">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="warehouseInDate">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="lotNo">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="warehouseId">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="warehouseInNum">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="productionDate">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="expirationDate">
            <div class="form-text-box">{{ value }} 天</div>
          </template>
          <template slot-scope="{ label }" slot="alertTemplateId">
            <div class="form-text-box">{{ label }}</div>
          </template>
          <template slot-scope="{ value }" slot="warehouseAddress">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ value }" slot="warehouseManager">
            <div class="form-text-box">{{ value }}</div>
          </template>
          <template slot-scope="{ row }" slot="goodsSpec">
            <div class="form-text-box">{{ row.goodsSpec }}</div>
          </template>
          <template slot-scope="{ row }" slot="quantity">
            <div class="form-text-box">{{ row.quantity }}</div>
          </template>
          <template slot="testReportAttachId">
            <div class="look-btn-menu">
              <span
                v-if="testReportAttachIdimageArr.length"
                @click="handleFilePreviewOnceAgain('img', 1)"
              >
                查看图片
              </span>
              <template v-if="testReportAttachIdpdfArr.length === 1">
                <span @click="handleFilePreviewOnceAgain(0, 1)">
                  查看附件
                </span>
              </template>
              <template v-else>
                <span
                  v-for="(item, index) in testReportAttachIdpdfArr"
                  :key="index"
                  @click="handleFilePreviewOnceAgain(index, 1)"
                >
                  查看附件{{ index + 1 }}
                </span>
              </template>
            </div>
          </template>
          <template slot="warehouseProof">
            <div class="look-btn-menu">
              <span
                v-if="imageArr.length"
                @click="handleFilePreviewOnceAgain('img', 2)"
              >
                查看图片
              </span>
              <template v-if="pdfArr.length === 1">
                <span @click="handleFilePreviewOnceAgain(0, 2)">
                  查看附件
                </span>
              </template>
              <template v-else>
                <span
                  v-for="(item, index) in pdfArr"
                  :key="index"
                  @click="handleFilePreviewOnceAgain(index, 2)"
                >
                  查看附件{{ index + 1 }}
                </span>
              </template>
            </div>
          </template>
        </avue-form>
      </div>
    </basic-container>
    <div class="footer-container" v-if="warehouSingActive === 0">
      <span class="backBtn" @click="backBtnFun">返 回</span>
      <span class="nextBtn" @click="nextBtnFun">下一步</span>
    </div>
    <div class="footer-container" v-else>
      <span class="backBtn" @click="lastBtnFun">上一步</span>
      <span class="nextBtn" @click="confirmBtnFun">确定入库</span>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      title="选择仓库"
      :visible.sync="showDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="avue-dialog"
      width="80%"
    >
      <avue-crud
        ref="crud"
        :option="arrOption"
        :data="arr"
        :search.sync="dialogSearch"
        @row-click="rowClick"
        @search-change="searchChange"
        @on-load="dialogOnLoad"
        :table-loading="tableLoading"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow" :label="row.$index">&nbsp;</el-radio>
        </template>
        <template slot-scope="{ row }" slot="regionCodeName">
          <el-tag type="info">{{ row.regionCodeName }}</el-tag>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="cardfals">取 消</el-button>
        <el-button @click="cardEngth" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import {
  warehouseAll,
  warehouseDetails,
  warehouseDetailsSave,
  detailsByFinanceNo,
  warehouseDetailsSaveS,
} from '@/api/redemption/warehouseentering'
import FilePreview from '@/components/file-preview'
import { getToken } from '@/util/auth'

export default {
  name: 'warehouSing',
  components: { FilePreview },
  data() {
    return {
      form: {
        warehouseInNum: 1,
      },
      warehouSingActive: 0,
      option: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 90,
        gutter: 50,
        // size: 'medium',
        column: [],
      },
      warehouseProofArr: [],
      testReportAttachIdArr: [],
      showDialog: false,
      arrOption: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchMenuSpan: 7, // 搜索菜单栏宽带
        maxHeight: 300,
        index: false,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [],
      },
      wareMaxNum: 1,
      dialogSearch: {},
      selectRow: '',
      selectIdBefore: void 0,
      selectIdAfter: void 0,
      selectRowData: void 0,
      tableLoading: true,
      arr: [],
      searchPart: [],
      searchName: [],
      pdfSrc: '',
      pdfArr: [],
      imageArr: [],
      testReportAttachIdimageArr: [],
      testReportAttachIdpdfArr: [],
      Basic: '',
      ispledgeMovables: false, // 是否动产质押
      JIQ: false, // 是否选择了生产日期
      lock: false,
    }
  },
  watch: {
    dialogSearch: {
      handler(val) {
        // 搜索框被清空后重新返回列表数据
        if (!val.regionCodeName && !val.storageName) {
          this.warehouseAllFun()
          // setTimeout(() => {
          //   this.checkUpType()
          // }, 100)
        }
      },
      deep: true,
      immediate: false,
    },
    'form.productionDate'(val) {
      if (val) {
        this.JIQ = true
      } else {
        this.JIQ = false
      }
    },
  },
  created() {
    // 获取当前用户token
    this.Basic = 'bearer ' + getToken()
    // avue表格配置
    this.option.column = [
      {
        label: '融资编号',
        prop: 'financingNumber',
        type: 'input',
        span: 12,
      },
      {
        label: '融资用户',
        prop: 'financingUser',
        type: 'input',
        span: 12,
      },
      {
        label: '入库日期',
        prop: 'warehouseInDate',
        valueFormat: 'yyyy-MM-dd',
        type: 'date',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择入库日期',
            trigger: 'change',
          },
        ],
      },
      {
        label: '批号',
        prop: 'lotNo',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入批号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '选择仓库',
        prop: 'warehouseId',
        type: 'input',
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择仓库',
            trigger: 'change',
          },
        ],
        click: () => {
          // 打开弹窗
          this.selectedWarehouse()
        },
      },
      {
        label: '生产日期',
        prop: 'productionDate',
        valueFormat: 'yyyy-MM-dd',
        type: 'date',
        span: 12,
        control: val => {
          if (val) {
            return {
              expirationDate: {
                display: true,
              },
              alertTemplateId: {
                display: true,
              },
            }
          } else {
            return {
              expirationDate: {
                display: false,
              },
              alertTemplateId: {
                display: false,
              },
            }
          }
        },
      },
      {
        label: '保质期',
        prop: 'expirationDate',
        type: 'input',
        span: 12,
        dataType: 'number',
        append: '天',
        display: false,
        rules: [
          {
            required: true,
            message: '请输入保质期',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '预警模板',
        prop: 'alertTemplateId',
        type: 'select',
        filterable: true,
        display: false,
        dicUrl:
          '/api/blade-message/web-back/message/messagetemplate/rule/list?current=1&size=300',
        dicFormatter: res => {
          if (res.success) {
            const { data: resData } = res
            return resData.records
          } else {
            return []
          }
        },
        props: {
          label: 'ruleName',
          value: 'id',
        },
        rules: [
          {
            required: true,
            message: '请选择预警模板',
            trigger: 'change',
          },
        ],
      },
      {
        label: '存储地址',
        prop: 'warehouseAddress',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入存储地址',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '仓管员',
        prop: 'warehouseManager',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入仓管员',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '入库数量',
        prop: 'warehouseInNum',
        type: 'number',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入入库数量',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '质检凭证',
        prop: 'testReportAttachId',
        type: 'input',
        display: false,
        span: 12,
        rules: [
          {
            required: true,
            message: '请上传质检凭证',
            trigger: 'change',
          },
        ],
      },
      {
        label: '入库单',
        prop: 'warehouseProof',
        type: 'input',
        span: 12,
        rules: [
          {
            required: true,
            message: '请上传入库单',
            trigger: 'change',
          },
        ],
      },
      {
        label: '入库数量',
        prop: 'warehouseInNums',
        type: 'dynamic',
        span: 12,
        display: false,
        rules: [
          {
            required: true,
            message: '请选择准入商品',
            trigger: 'change',
          },
        ],
        children: {
          delBtn: false,
          addBtn: false,
          align: 'center',
          headerAlign: 'center',
          column: [
            {
              label: '商品规格',
              prop: 'goodsSpec',
            },
            {
              label: '数量',
              prop: 'quantity',
            },
          ],
        },
      },
    ]
    // 弹窗表格数据
    this.arrOption.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '仓库编号',
        prop: 'warehouseNo',
      },
      {
        label: '仓储方',
        prop: 'storageName',
        search: true,
        searchSpan: 10,
        searchClearable: false,
        placeholder: '选择仓储方',
        type: 'tree',
        dicUrl:
          '/api/blade-customer/web-back/customer/customerSupervise/supper-all',
        props: {
          label: 'superviseName',
          value: 'id',
        },
      },
      {
        label: '仓库名称',
        prop: 'warehouseName',
      },
      {
        label: '仓库区域',
        prop: 'regionCodeName',
        search: true,
        searchSpan: 10,
        searchClearable: false,
        placeholder: '选择仓库区域',
        type: 'tree',
        dicUrl: '/api/blade-warehouse/web-back/warehouse/warehouse/region-all',
        props: {
          label: 'label',
          value: 'parentCode',
        },
      },
      {
        label: '联系人',
        prop: 'contacts',
      },
      {
        label: '电话',
        prop: 'phone',
      },
      {
        label: '地址',
        prop: 'address',
      },
    ]
    this.warehouseAllFun()
    this.onLoad()
  },
  mounted() {
    // 将input 变成只读
    const domArr = document.querySelectorAll('.el-input__inner')
    domArr[5].readOnly = true
    domArr[5].id = 'my-input-select'
  },
  methods: {
    onLoad() {
      if (!this.$route.query.id) return
      // 获取详情
      warehouseDetails(this.$route.query.id).then(async ({ data }) => {
        if (data.success) {
          const { data: resData } = data
          if (resData.testNode == 1) {
            this.findObject(
              this.option.column,
              'testReportAttachId'
            ).display = true
          }
          // 动产质押参数变更
          if (resData.businessType === 4) {
            this.ispledgeMovables = true
            this.findObject(
              this.option.column,
              'warehouseInNum'
            ).display = false
            this.findObject(
              this.option.column,
              'warehouseInNums'
            ).display = true
            // 根据融资单号获取此次需要入库的所有货物,动产质押业务需要
            await this.detailsByFinanceNoFun(resData.financeNo)
          } else {
            // 动产质押暂时不适用该功能
            // 如果已经有缓存，就回显数据
            // this.echoData()
          }
          this.form.financingNumber = resData.financeNo // 融资编号
          this.form.financingUser = resData.companyName // 融资用户
          this.wareMaxNum = resData.readyToStorage // 待入库数量
        }
      })
    },
    // 根据融资单号获取此次需要入库的所有货物,动产质押业务需要
    detailsByFinanceNoFun(financeNo) {
      if (!financeNo) return false
      const paramsP = {
        financeNo,
      }
      return detailsByFinanceNo(paramsP).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          this.form.warehouseInNums = []
          for (const item of resData) {
            this.form.warehouseInNums.push({
              id: item.id,
              goodsSpec: item.goodsSpec,
              maxNum: item.readyToStorage,
            })
          }
        }
      })
    },
    // 如果已经有缓存，就回显数据
    echoData() {
      const sessionWare = sessionStorage.getItem(
        `warehouSing${this.$route.query.id}`
      )
      if (sessionWare) {
        const warehouSingSessions = JSON.parse(sessionWare)
        for (const key in warehouSingSessions) {
          this.form[key] = warehouSingSessions[key]
          if (key === 'warehouseProof') {
            this.warehouseProofArr = warehouSingSessions[key]
          } else if (key === 'selectIdAfter') {
            this.selectIdAfter = warehouSingSessions[key]
          } else if (key == 'testReportAttachId') {
            this.testReportAttachIdArr = warehouSingSessions[key]
          }
        }
      }
    },
    // 弹窗搜索事件
    searchChange(params, done) {
      this.warehouseAllFun(params.storageName, params.regionCodeName)
      // setTimeout(() => {
      //   this.checkUpType()
      // }, 100)
      done()
    },
    // 弹窗单选事件
    rowClick(row) {
      if (this.selectRow !== row.$index) {
        this.selectRow = row.$index
        this.selectRowData = `${row.storageName}>${row.warehouseName}`
        this.selectIdBefore = row.id
      }
    },
    // 弹窗首次加载事件
    dialogOnLoad() {
      this.tableLoading = false
    },
    // 弹窗表格取消按钮
    cardfals() {
      this.showDialog = false
      // if (this.checkif) {
      //   // 当checkbox事件被触发，并且没有确定更改，对checkbox进行状态复原
      //   this.checkUpType()
      // }
      // this.checkif = false
    },
    // 弹窗表格确认按钮
    cardEngth() {
      this.form.warehouseId = this.selectRowData
      this.selectIdAfter = this.selectIdBefore
      this.showDialog = false
      // this.upBeforeCheck()
    },
    // 弹窗回显已选仓库
    selectedWarehouse() {
      if (this.selectIdAfter) {
        this.arr.forEach((item, index) => {
          if (item.id === this.selectIdAfter) {
            this.selectRow = index
          }
        })
      }
      this.showDialog = true
    },
    // 选择仓库弹窗-list数据接口
    warehouseAllFun(storageId, regionCode) {
      const params = {
        storageId,
        regionCode,
      }
      warehouseAll(params).then(res => {
        if (res.status == 200) {
          const resData = res.data.data
          const arr = []
          if (resData) {
            for (const item of resData) {
              arr.push({
                id: item.id,
                warehouseNo: item.warehouseNo, // 仓库编号
                storageName: item.storageName, // 仓储方
                warehouseName: item.warehouseName, // 仓库名称
                regionCodeName: item.regionCodeName, // 仓库区域
                contacts: item.contacts, // 联系人
                phone: item.phone, // 电话
                address: item.address, // 地址
              })
            }
          }
          this.arr = arr
        }
      })
    },
    handleUpSuccess(response, file, fileList, type) {
      const arr = []
      for (const itemed of fileList) {
        if (itemed.response) {
          const { data = {} } = itemed.response
          arr.push({
            name: data.name,
            url: data.url,
            attachId: data.attachId,
          })
        } else {
          arr.push({
            name: itemed.name,
            url: itemed.url,
          })
        }
      }
      if (type == 2) {
        this.form.warehouseProof = arr
        this.$refs.formAvue.validateField('warehouseProof')
      } else {
        this.form.testReportAttachId = arr
        this.$refs.formAvue.validateField('testReportAttachId')
      }
    },
    handleFileRemove(file, fileList, type) {
      if (type == 2) {
        this.form.warehouseProof = fileList
      } else {
        this.form.testReportAttachId = fileList
      }
    },
    handleFilePreview(file) {
      let targetUrl = void 0
      if (file.response) {
        targetUrl = file.response.data.url
      } else {
        targetUrl = file.url
      }
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    handleFilePreviewOnceAgain(type, type2) {
      if (type === 'img' && type2 == 2) {
        this.$ImagePreview(this.imageArr, 0, {
          closeOnClickModal: true,
        })
      } else if (type === 'img' && type2 == 1) {
        this.$ImagePreview(this.testReportAttachIdimageArr, 0, {
          closeOnClickModal: true,
        })
      } else if (type == 2) {
        this.pdfSrc =
          this.pdfArr[type].url + '?time=' + new Date().getMilliseconds()
      } else if (type == 1) {
        this.pdfSrc =
          this.testReportAttachIdpdfArr[type].url +
          '?time=' +
          new Date().getMilliseconds()
      }
    },
    nextBtnFun() {
      this.$refs.formAvue.validate((vaild, done) => {
        if (vaild && this.warehouSingActive < 1) {
          // 动产质押专属校验
          if (this.ispledgeMovables) {
            const cNumArr = this.form.warehouseInNums
            if (!cNumArr || !cNumArr.length) {
              done()
              return false
            }
            let noInput = true
            for (const item of cNumArr) {
              // 最少需要填写一项规格的数量
              if (noInput && (item.quantity || item.quantity === 0)) {
                noInput = false
              }
            }
            if (noInput) {
              this.$message.warning('最少需要填写一项规格的数量')
              done()
              return false
            }
          }
          this.pdfArr = []
          this.imageArr = []
          this.testReportAttachIdpdfArr = []
          this.testReportAttachIdimageArr = []
          for (const item of this.form.warehouseProof) {
            if (!item.url) {
              this.$message.warning('请等待附件上传完成')
              done()
              return
            }
            if (item.url.endsWith('.pdf')) {
              this.pdfArr.push(item)
            } else {
              this.imageArr.push(item)
            }
          }
          for (const item of this.form.testReportAttachId) {
            if (!item.url) {
              this.$message.warning('请等待附件上传完成')
              done()
              return
            }
            if (item.url.endsWith('.pdf')) {
              this.testReportAttachIdpdfArr.push(item)
            } else {
              this.testReportAttachIdimageArr.push(item)
            }
          }
          this.form.selectIdAfter = this.selectIdAfter
          // sessionStorage.setItem(
          //   `warehouSing${this.$route.query.id}`,
          //   JSON.stringify(this.form)
          // )
          // 解决动产质押入库数量子表单缓存问题&&生产日期的显影问题
          this.wipeCacheD()
          this.warehouSingActive++
          done()
        }
      })
    },
    backBtnFun() {
      this.$router.$avueRouter.closeTag()
      this.$router.push('/redemption/warehouseentering')
    },
    lastBtnFun() {
      if (this.warehouSingActive === 1) {
        this.warehouseProofArr = this.form.warehouseProof
        this.testReportAttachIdArr = this.form.testReportAttachId
        // 解决动产质押入库数量子表单缓存问题&&生产日期的显影问题
        this.wipeCacheD()
        this.warehouSingActive--
      }
    },
    confirmBtnFun() {
      if (this.lock) return
      this.lock = true
      const arr = []
      if (this.form.warehouseProof) {
        for (const item of this.form.warehouseProof) {
          arr.push(item.url)
        }
      }
      const testReportAttachIdArr = []
      if (this.form.testReportAttachId) {
        for (const item of this.form.testReportAttachId) {
          testReportAttachIdArr.push(item.attachId)
        }
      }
      const dataP = {
        warehouseInDate: this.form.warehouseInDate,
        lotNo: this.form.lotNo,
        warehouseId: this.selectIdAfter,
        warehouseProof: arr.join(','),
        testReportAttachId: testReportAttachIdArr.join(','),
        warehouseInNum: this.form.warehouseInNum,
        warehouseManager: this.form.warehouseManager,
        warehouseAddress: this.form.warehouseAddress,
        productionDate: this.form.productionDate,
        expirationDate: this.form.expirationDate,
        alertTemplateId: this.form.alertTemplateId,
        enteringId: this.$route.query.id,
      }
      // 非动产质押
      if (!this.ispledgeMovables) {
        warehouseDetailsSave(dataP)
          .then(({ data }) => {
            if (data.success) {
              this.$message.success('入库成功')
              this.backBtnFun()
            }
          })
          .finally(() => {
            this.lock = false
          })
      } else {
        const warehouseDetailsListStr = []
        for (const item of this.form.warehouseInNums) {
          if (item.quantity || item.quantity === 0) {
            warehouseDetailsListStr.push({
              ...dataP,
              warehouseInNum: item.quantity,
              enteringId: item.id,
            })
          }
        }
        warehouseDetailsSaveS(warehouseDetailsListStr)
          .then(({ data }) => {
            if (data.success) {
              this.$message.success('入库成功')
              this.backBtnFun()
            }
          })
          .finally(() => {
            this.lock = false
          })
      }
    },
    // 解决动产质押入库数量子表单缓存问题&&生产日期的显影问题
    wipeCacheD() {
      // 动产质押的
      const fArr1 = ['warehouseInNums']
      // 生产日期的
      const fArr2 = ['expirationDate', 'alertTemplateId']
      for (const [index, item] of fArr1.concat(fArr2).entries()) {
        this.findObject(this.option.column, item).display = false
        // 第一个并且业务类型属于动产质押
        if (!index && this.ispledgeMovables) {
          this.findObject(this.option.column, item).display = true
        } else if (index && this.JIQ) {
          // 不是第一个并且归属生产日期管理显影的属性
          this.findObject(this.option.column, item).display = true
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.warehou-sing {
  .header-box {
    .header-title {
      width: 64px;
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-family: SourceHanSansSC-regular;
      font-weight: bold;
      margin-bottom: 20px;
    }

    .long-string {
      width: 100%;
      height: 1px;
      border: 1px solid rgba(233, 235, 239, 100);
      margin-bottom: 29px;
    }

    .article-steps-box {
      width: 65%;
      margin: 0 auto 8px;
    }
  }

  .form-box {
    .form-text-box {
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }

    .file-upload-box-container {
      width: 360px;

      ::v-deep {
        .el-upload-list__item {
          background-color: #f5f7fa;
        }
      }
    }

    .look-btn-menu {
      & span {
        height: 21px;
        color: rgba(105, 124, 255, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
        cursor: pointer;
        margin-right: 8px;
      }

      &:last-child {
        margin-right: 0;
      }
    }

    // 选择仓库样式修改
    ::v-deep {
      #my-input-select {
        cursor: pointer;
      }
      .el-input__suffix {
        font-size: 16px;
        color: rgba(112, 112, 112, 100);
      }
      // 表格操作栏
      .avue-form__menu {
        display: none;
      }
    }
  }

  .footer-container {
    position: fixed;
    height: 68px;
    width: calc(100% - 272px);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    bottom: 0;
    right: 15px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    .backBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      border: 1px solid rgba(187, 187, 187, 100);
      margin-right: 18px;
      cursor: pointer;
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      background-color: rgba(18, 119, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }
  }
}
</style>
