<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row }" slot="menu">
        <el-button
          v-if="row.isOk"
          type="text"
          size="small"
          @click="handelSignAgreement(row)"
          >确认完成入库
        </el-button>
        <el-button
          v-else-if="!row.notButton"
          type="text"
          size="small"
          @click="openPreview(row)"
          >入库
        </el-button>
      </template>
      <template slot="goodsInfos" slot-scope="{ row }">
        <div class="goodsInfo-slot">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.logo"
            fit="contain"
          ></el-image>
          <span class="demonstration">{{ row.goodsName }}</span>
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  completeWarehousing,
} from '@/api/redemption/warehouseentering'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        menuWidth: 95,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '融资编号',
            width: 130,
            overHidden: true,
            prop: 'financeNo',
            search: true,
          },
          {
            label: '融资用户',
            prop: 'companyName',
            hide: true,
            search: true,
          },
          {
            label: '货品信息',
            prop: 'goodsInfos',
            width: 200,
            overHidden: true,
            slot: true,
          },
          {
            label: '业务类型',
            prop: 'businessType',
            width: 70,
            type: 'select',
            dataType: 'string',
            // multiple: true,
            search: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dicFormatter: res => {
              if (res.success) {
                for (const item of res.data) {
                  // 类型为代采和动产可选，其他禁用
                  if (!['2', '4'].includes(item.dictKey)) {
                    item.disabled = true
                  }
                }
                return res.data
              } else {
                this.$message.warning('业务类型字典接口请求错误')
                return []
              }
            },
          },
          {
            label: '供应商',
            overHidden: true,
            prop: 'supplierId',
            width: 150,
            type: 'select',
            dicUrl:
              '/api/blade-customer/web-back/customer/customersupplier/supper-all?status=' +
              1,
            props: {
              label: 'supperName',
              value: 'id',
            },
          },
          {
            label: '单位值',
            prop: 'goodsUnitValue',
          },
          {
            label: '待入库数量',
            width: 80,
            prop: 'readyToStorage',
          },
          {
            label: '采购单价(元)',
            width: 100,
            prop: 'purchasePrice',
          },
          {
            label: '融资单价(元)',
            width: 100,
            prop: 'financingPrice',
          },
          {
            label: '最迟交付日',
            width: 90,
            prop: 'latestDelivery',
          },
          {
            label: '逾期天数（天）',
            width: 70,
            prop: 'overDueDay',
          },
          {
            label: '创建日期',
            prop: 'createTimeRange',
            type: 'datetime',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd 00:00:00',
            searchRange: true,
            hide: true,
            display: false,
            search: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.redemptionwarehouseentering_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.redemptionwarehouseentering_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.redemptionwarehouseentering_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.redemptionwarehouseentering_edit,
          false
        ),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    // 点击入库已完成按钮请求
    handelSignAgreement(row) {
      completeWarehousing({ financeNo: row.financeNo }).then(({ data }) => {
        if (data.success) {
          row.isOk = false
          row.notButton = true
          this.$message.success('确认完成')
        }
      })
    },
    // 入库
    openPreview(row) {
      this.$router.push(`/redemption/warehousing?id=${row.id}`)
    },
    beforeOpen(done, type) {
      console.log(type)
      // if (['edit', 'view'].includes(type)) {
      //   getDetail(this.form.id).then(res => {
      //     this.form = res.data.data
      //   })
      // }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      const { createTimeRange } = this.query
      let values = {
        ...params,
      }
      if (createTimeRange) {
        values = {
          ...params,
          create_time_datege: createTimeRange[0],
          create_time_datele: createTimeRange[1],
          ...this.query,
        }
        values.createTimeRange = null
      }
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        let recordState = ''
        let ind = -2
        for (const [index, item] of data.records.entries()) {
          // 差异化处理动产质押数据的入库按钮显示
          if (item.businessType && item.businessType === 4) {
            if (recordState && item.financeNo === recordState) {
              item.notButton = true
              // 检查没入库完成，清除之前设的值
              if (ind !== -2 && Number(item.readyToStorage) !== 0) {
                data.records[ind].isOk = false
                ind = -2
              }
            } else {
              recordState = item.financeNo
              // 可能已入库完成，需要后续检查
              if (Number(item.readyToStorage) === 0) {
                item.isOk = true
                ind = index
              } else {
                ind = -2
              }
            }
          }
        }
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.goodsInfo-slot {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>
