<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!--      <template slot="menu">-->
      <!--        <el-button type="text" size="small" @click="listWarehouseInOut(row)"-->
      <!--        >出入库记录-->
      <!--        </el-button>-->
      <!--      </template>-->
      <template
        slot="goodsInfos"
        slot-scope="{ row }"
      >
        <div class="goodsInfo-slot">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.logo"
            fit="contain"
          ></el-image>
          <span class="demonstration">{{ row.goodsName }}</span>
        </div>
      </template>
      <!-- <template slot="testReportAttachId" slot-scope="{row}">
        <div class="see" @click="see(row)" style="color:#007fff;" v-if="row.testReportAttachId">
          查看凭证
        </div>
      </template> -->
      <template
        slot-scope="{ row }"
        slot="menu"
      >
        <el-button
          v-if="row.testReportAttachId"
          type="text"
          size="small"
          @click="see(row)"
        >查看质检报告
        </el-button>
        <el-button
          type="text"
          size="small"
          @click="viewWarehouse(row)"
        >查看仓单
        </el-button>
      </template>
    </avue-crud>
    <FilePreview :url="pdfSrc" />
    <el-dialog
      title="查看仓单"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :before-close="handleClose"
      @opened="handleOpened"
      width="60%"
    >
      <div style="position: relative">
        <el-descriptions style="margin-bottom: 10px">
          <el-descriptions-item label="仓单生成时间">{{
            dataObj.nowData
          }}</el-descriptions-item>
          <el-descriptions-item label="入库时间">{{
            dataObj.warehouseDate
          }}</el-descriptions-item>
          <el-descriptions-item label="库龄">{{ dataObj.warehouseDays }}天</el-descriptions-item>
        </el-descriptions>
        <el-descriptions border>
          <el-descriptions-item label="仓单编号">{{
            dataObj.warehouseNo
          }}</el-descriptions-item>
          <el-descriptions-item label="持有人">{{
            dataObj.holderName
          }}</el-descriptions-item>
          <el-descriptions-item label="保管人">
            {{ dataObj.custodianName }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库名称">
            {{ dataObj.warehouseName }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库地址">
            {{ dataObj.warehouseAddress }}
          </el-descriptions-item>
        </el-descriptions>
        <div style="margin-top: 20px">
          <p style="font-weight: bold; margin-bottom: 5px">商品规格：</p>
          <MyElTable
            :tableData="tableData"
            :columns="columnList"
            :isGetSummaries="getSummaries"
          />
        </div>
        <div style="margin-top: 20px">
          <p style="font-weight: bold; margin-bottom: 5px">仓单变更记录：</p>
          <MyElTable
            :tableData="tableData2"
            :columns="columnList2"
          />
        </div>
        <div class="common-seal">
          <CommonSeal :nameOfFirm="nameOfFirm" />
        </div>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        > -->
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  remove,
  update,
  receipt,
} from '@/api/redemption/warehousedetails'
import FilePreview from '@/components/file-preview'
import { mapGetters } from 'vuex'
import MyElTable from '@/components/myElTable/index.vue'
import CommonSeal from './components/commonSeal.vue'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        searchShow: true,
        // menu: false,
        menuWidth: 100,
        tip: false,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '库存编号',
            prop: 'warehouseNo',
            search: true,
            width: 100,
            rules: [
              {
                required: true,
                message: '请输入库存编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资编号',
            prop: 'financeNo',
            search: true,
            width: 100,
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '业务类型',
            prop: 'businessType',
            width: 70,
            type: 'select',
            dataType: 'string',
            // multiple: true,
            search: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dicFormatter: res => {
              if (res.success) {
                for (const item of res.data) {
                  // 类型为代采和动产可选，其他禁用
                  if (!['2', '4'].includes(item.dictKey)) {
                    item.disabled = true
                  }
                }
                return res.data
              } else {
                this.$message.warning('业务类型字典接口请求错误')
                return []
              }
            },
          },
          {
            label: '货品信息',
            prop: 'goodsInfos',
            width: 200,
            overHidden: true,
            slot: true,
          },
          {
            label: '仓库信息',
            prop: 'warehouseId',
            type: 'select',
            search: true,
            overHidden: true,
            width: 150,
            dicUrl:
              '/api/blade-warehouse/web-back/warehouse/warehouse/warehouse-all',
            props: {
              label: 'warehouseName',
              value: 'id',
            },
          },
          {
            label: '供应商',
            prop: 'supplierId',
            type: 'select',
            dicUrl:
              '/api/blade-customer/web-back/customer/customersupplier/supper-all',
            overHidden: true,
            width: 150,
            search: true,
            props: {
              label: 'supperName',
              value: 'id',
            },
          },
          {
            label: '单位',
            prop: 'goodsUnitValue',
            rules: [
              {
                required: true,
                message: '请输入单位',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '采购单价',
            prop: 'purchasePrice',
            rules: [
              {
                required: true,
                message: '请输入采购单价',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资单价',
            prop: 'financingPrice',
            rules: [
              {
                required: true,
                message: '请输入融资单价',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '入库数量',
            prop: 'warehouseInNum',
            rules: [
              {
                required: true,
                message: '请输入入库数量',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '库存数量',
            prop: 'warehouseNum',
            rules: [
              {
                required: true,
                message: '请输入库存数量',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '在途数量',
            prop: 'warehouseRedemptionNum',
            rules: [
              {
                required: true,
                message: '请输入在途数量',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '出库数量',
            prop: 'warehouseOutNum',
            rules: [
              {
                required: true,
                message: '请输入出库数量',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '入库时间',
            width: 100,
            prop: 'warehouseInDate',
            rules: [
              {
                required: true,
                message: '请输入入库时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '入库年龄（天）',
            width: 120,
            prop: 'inventoryAge',
          },
          {
            label: '约定赎货日',
            prop: 'redemptionDate',
            width: 100,
            rules: [
              {
                required: true,
                message: '请输入约定赎货日',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '是否逾期',
            prop: 'overdueStatus',
            dicData: [
              {
                label: '未逾期',
                value: 1,
              },
              {
                label: '逾期',
                value: 2,
              },
            ],
          },
          {
            label: '库存状态',
            prop: 'warehouseStatus',
            dicData: [
              {
                label: '可使用',
                value: 1,
              },
              {
                label: '在途',
                value: 2,
              },
              {
                label: '已出库',
                value: 3,
              },
            ],
          },
          {
            label: '上次更新时间',
            prop: 'updateTime',
            width: 100,
          },
          {
            label: '仓管员',
            prop: 'warehouseManager',
            rules: [
              {
                required: true,
                message: '请输入仓管员',
                trigger: 'blur',
              },
            ],
          },
          // {
          //   label:'质检报告',
          //   prop:'testReportAttachId',
          //   fixed:'right',
          // },
          {
            label: '商品名称',
            prop: 'goodsName',
            search: true,
            hide: true,
          },
          {
            label: '创建日期',
            prop: 'createTimeRange',
            type: 'datetime',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd 00:00:00',
            searchRange: true,
            hide: true,
            display: false,
            search: true,
          },
        ],
      },
      data: [],
      dialogVisible: false,
      dataObj: {},
      tableData: [],
      columnList: [
        {
          id: 1,
          iProp: 'cargoName',
          iLabel: '货物名称',
        },
        {
          id: 2,
          iProp: 'cargoSpec',
          iLabel: '规格',
        },
        {
          id: 2,
          iProp: 'warehouseInNum',
          iLabel: '入库数量',
        },
        {
          id: 3,
          iProp: 'cargoNum',
          iLabel: '当前库存',
        },
        {
          id: 4,
          iProp: 'unit',
          iLabel: '单位',
        },
      ],
      tableData2: [],
      columnList2: [
        {
          id: 1,
          iProp: 'inOutTime',
          iLabel: '时间',
        },
        {
          id: 2,
          iProp: 'warehouseTypeStr',
          iLabel: '类型',
        },
        {
          id: 3,
          iProp: 'goodsName',
          iLabel: '商品名',
        },
        {
          id: 3,
          iProp: 'goodsSpec',
          iLabel: '规格',
        },
        {
          id: 4,
          iProp: 'inOutQuantity',
          iLabel: '数量',
        },
        {
          id: 5,
          iProp: 'goodsUnitValue',
          iLabel: '单位',
        },
        {
          id: 6,
          iProp: 'operatorName',
          iLabel: '出库人',
          iWidth: 210,
        },
      ],
      nameOfFirmB: '', // 仓单电子章b
      nameOfFirm: '', // 仓单电子章
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.warehousedetails_add, false),
        viewBtn: this.vaildData(this.permission.warehousedetails_view, false),
        delBtn: this.vaildData(this.permission.warehousedetails_delete, false),
        editBtn: this.vaildData(this.permission.warehousedetails_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    viewWarehouse(row) {
      receipt({ id: row.id }).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          // 存储总数据
          this.dataObj = resData
          // 仓单电子章
          this.nameOfFirmB = resData.custodianName
          // 商品规格数据，不知道为什么返回的不是数组，先用着
          this.tableData = [
            {
              cargoName: resData.cargoName,
              cargoSpec: resData.cargoSpec,
              warehouseInNum: resData.warehouseInNum,
              cargoNum: resData.cargoNum,
              unit: resData.unit,
            },
          ]
          // 商品规格数据的单位
          // this.columnList[1].iLabel = `货物数量 (${resData.unit})`
          // 仓单变更记录
          const arrD = []
          for (const item of resData.warehouseInOutVOList) {
            arrD.push(item)
          }
          this.tableData2 = arrD
          this.dialogVisible = true
        }
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = money
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    handleOpened() {
      this.nameOfFirm = this.nameOfFirmB
    },
    handleClose(done) {
      this.nameOfFirm = ''
      done()
    },
    see(row) {
      this.handleViewBalanceProof(row.reportList)
    },
    handleViewBalanceProof(Arr) {
      const imgSrcArr = []
      const pdfSrcArr = []
      for (const item of Arr) {
        if (item.extension != 'pdf') {
          imgSrcArr.push({ name: item.name, url: item.link })
        } else {
          pdfSrcArr.push({ name: item.name, url: item.link })
        }
      }
      if (pdfSrcArr.length == 0) {
        this.handlePreviewImage(imgSrcArr)
      } else {
        this.handlePreviewImage(pdfSrcArr, 'pdf')
      }
    },
    handlePreviewImage(imgSrcArr = [], type = 'img') {
      if (type == 'img') {
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      } else {
        this.pdfSrc = imgSrcArr[0].url + '?time=' + new Date().getMilliseconds()
      }
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      const { createTimeRange } = this.query
      let values = {
        ...params,
      }
      if (createTimeRange) {
        values = {
          ...params,
          create_time_datege: createTimeRange[0],
          create_time_datele: createTimeRange[1],
          ...this.query,
        }
        values.createTimeRange = null
      }
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
  components: {
    FilePreview,
    MyElTable,
    CommonSeal,
  },
}
</script>

<style lang="scss" scoped>
.goodsInfo-slot {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.common-seal {
  position: absolute;
  height: 140px;
  width: 140px;
  right: 23px;
  bottom: 78px;
}
</style>
