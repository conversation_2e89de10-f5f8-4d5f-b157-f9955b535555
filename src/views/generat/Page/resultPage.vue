<template>
  <div class="result-container">
    <div class="result-text" v-if="true">
      <MySvgIcon
        icon-class="icon-chenggong"
        style="fill: #0bb07b; font-size: 64px; margin-bottom: 12px"
      />
      <span class="result-msg">恭喜您!绑定成功</span>
      <span class="result-title"
        >您与【深圳市精锐纵横网络技术】绑定为贸易关系,快去开通产品吧</span
      >
      <a-button>返回</a-button>
    </div>
    <div class="result-text" v-else>
      <MySvgIcon
        icon-class="icon-shibai"
        style="fill: #f03d3d; font-size: 64px"
      />
      <span class="result-msg">恭喜您!绑定成功</span>
      <span class="result-title"
        >您与【深圳市精锐纵横网络技术】绑定为贸易关系,快去开通产品吧</span
      >
      <a-button>返回</a-button>
    </div>
  </div>
</template>

<script>
export default {}
</script>

<style lang="scss" scoped>
.result-container {
  background: #fff;
  height: calc(100vh - 280px);
  display: flex;
  align-items: center;
  justify-content: center;
  .result-text {
    display: flex;
    align-items: center;
    flex-direction: column;
    .result-msg {
      font-size: 24px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #0a1f44;
      line-height: 32px;
      margin-bottom: 13px;
    }
    .result-title {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }
    .ant-btn {
      margin-top: 48px;
      width: 200px;
      height: 48px;
      background: #0c66ff;
      border-radius: 24px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 24px;
    }
  }
}
</style>
