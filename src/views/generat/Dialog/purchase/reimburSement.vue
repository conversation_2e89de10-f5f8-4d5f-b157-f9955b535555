<template>
  <GlobalDialog
    title="还款试算"
    width="1200px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="trade-space-box">
      <div class="label-tab-bar">
        <LabelBar
          :labelList="filterTabBar"
          :style="'display: inline-flex'"
          :state="tabBarObj.repaymentType"
          @switch="handleSwitch"
        />
        <div class="explain-box">
          <a-tooltip
            overlayClassName="billing-rules-tooltip"
            placement="bottom"
          >
            <div class="billing-text-box">
              <MySvgIcon
                icon-class="icon-xinxi"
                style="color: #8a94a6; font-size: 20px"
              />
              <span>说明</span>
            </div>
            <template #title>
              <div class="row-text">
                <div class="cont">
                  <span class="field">随借随还</span>
                </div>
                <p class="help">
                  借款人可随时归还所贷金额，按天计息，用多久付多久的利息
                </p>
              </div>
            </template>
          </a-tooltip>
        </div>
      </div>
      <div class="cost-box">
        <div class="table-father-box">
          <div class="table-box">
            <div class="table-title-box">
              <span class="table-bank">银行利息</span>
              <span class="long-string" />
              <span class="interest-rate"
                >日利率{{
                  dataAnnualInterestRateObj.dailyInterestRate
                }}%（年化利率{{
                  dataAnnualInterestRateObj.annualInterestRate
                }}%）</span
              >
            </div>
            <BaseTableDetailTotal
              :tableData="tableData1"
              :columns="columns1"
              :loading="tableDataLoad"
            />
          </div>
          <!-- 自动放款单独收取、手动收款展示 -->
          <div class="otherFee" v-if="otherFeeData.length > 0">
            <div
              v-for="item in otherFeeData"
              :key="item.id"
              class="table-box"
              style="margin-top: 40px"
            >
              <div class="table-title-box" style="display: block">
                <span class="table-bank">{{ item.name }}</span>
              </div>
              <BaseTableDetailTotal
                :tableData="item.expenseOrderDetailList"
                :columns="columns2"
                :loading="tableDataLoad"
              />
            </div>
          </div>
        </div>
        <div class="all-money-box">
          <div class="left-money-box">
            <span>
              <MySvgIcon
                icon-class="icon-xinxi"
                style="color: #8a94a6; font-size: 24px"
              ></MySvgIcon>
            </span>
            <span>以上仅为试算费用，以实际放款为准～</span>
          </div>
          <div class="right-money-box">
            <span>合计:</span>
            <span>{{ formatMoney(allMonry) }}</span>
          </div>
        </div>
      </div>
    </div>
    <template #button>
      <div style="width: 100%; text-align: right">
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 12px"
          round
          :bordered="false"
          @click="handleClose"
        >
          取消
        </n-button>
        <n-button
          class="border blue button-item primary"
          type="info"
          style="height: 40px"
          round
          :bordered="false"
          @click="handleConfirm"
        >
          确认
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'dialogReimburSement',
}
</script>
<script setup>
import { ref, watch } from 'vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import LabelBar from '@/components/BaseSwitchBar/index.vue'
import BaseTableDetailTotal from '@/components/BaseTableDetailTotal/index.vue'
import { formatMoney } from '@/utils/utils'
import { PRODUCT_APPLI_CATIONS_API } from '@/api/index'
import { requestDictMap } from '@/api/common/index'
import { NButton } from 'naive-ui'
import { getRepaymentCalculation, repaymentPurchaseCalculation } from "@/api/user/generat/index"
import dayjs from 'dayjs'
const props = defineProps({
  repaymentType: {
    type: Number,
    default: 0,
  },
  tabBarObj: {
    type: Object,
    default: () => {},
  },
  filterTabBar: {
    type: Array,
    default: () => ['随借随还'],
  },

  data: {
    type: Object,
    default: () => {},
  },
  filterArr: {
    type: Array,
    defalut: () => [],
  },
  financingTotal: {
    type: Number,
    defalut: 0,
  },
})

const emit = defineEmits(['setData'])
const columns1 = ref([
  // {
  //   title: '期数',
  //   dataIndex: 'name',
  // },
  {
    title: '还款日期',
    dataIndex: 'refundTime',
  },
  {
    title: '应还总额',
    dataIndex: 'monthlySupply',
  },
  {
    title: '应还本金',
    dataIndex: 'monthlyPrincipal',
  },
  {
    title: '应还利息',
    dataIndex: 'planInterest',
  },
])
const columns2 = [
  {
    title: '费用名称',
    dataIndex: 'expenseTypeStr',
  },
  // {
  //   title: '费用类型',
  //   dataIndex: 'feeNameType',
  // },
  // {
  //   title: '支付节点',
  //   dataIndex: 'feeNodeStr',
  // },
  {
    title: '期数',
    dataIndex: 'repaymentTerm',
  },
  {
    title: '收费节点',
    dataIndex: 'collectFeesNodeStr',
  },
  {
    title: '计费方式',
    dataIndex: 'feeFormulaName',
  },
  {
    title: '应付金额',
    dataIndex: 'amount',
  },
]
const initData = ref({})
const tableDataLoad = ref(false)
const tableDataLoad2 = ref(false)
const dialogRef = ref(null)
const handleSwitchIndex = ref(0)
const allMonry = ref(0)
const tableData1 = ref([])
const tableData2 = ref([])
const dataAnnualInterestRateObj = ref({})
const otherFeeData = ref([])
const tarGet = ref(false)
const changeSwitch = ref({
  repaymentType: 0,
  label: '',
}) // 当前选中的内容
watch(
  () => props.data,
  val => {
    initData.value = { ...val }
  },
  { immediate: true }
)
const handleOpen = () => {
  dialogRef.value.handleOpen()
  changeSwitch.value.repaymentType = props.tabBarObj.repaymentType
  changeSwitch.value.label = props.tabBarObj.label
  onload()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}

const handleConfirm = () => {
  emit('setData', changeSwitch.value)
  handleClose()
}
// 合计
const allMonrySum = item => {
  allMonry.value = 0
  allMonry.value += Number(item.monthlySupply)
  if (otherFeeData.value.length) {
    otherFeeData.value.forEach(item => {
      if (item.expenseOrderDetailList.length) {
        allMonry.value += Number(
          item.expenseOrderDetailList[
            item.expenseOrderDetailList.length - 1
          ].amount.replace(/,/g, '').split('￥')[1]
        )
      }
    })
  }
}

const handleSwitch = val => {
  changeSwitch.value.repaymentType = val
  changeSwitch.value.label = props.filterTabBar?.length
    ? props.filterTabBar.find(item => item.value === val).label
    : ''
  onload()
}
const getList = () => {
  tableDataLoad.value = true
  repaymentPurchaseCalculation({
    type: 1, //代采融资申请节点
    financeAmount: props.financingTotal,
    annualInterestRate: props.data.annualInterestRate,
    totalTerm: props.data.loanPeriod,
    startTime: dayjs().format('YYYY-MM-DD'),
    refundType: changeSwitch.value.repaymentType,
    loadTermUnit: props.data.loadTermUnit,
    goodsId: props.data.goodsId,
    chargePoint: "8,2,9,6", // 8 融资申请
    loanDay: props.data.loanPeriod, // 融资天数
    goodType: 2,
  }).then(({ data }) => {
    let list = []
    let Map = {}
    if (data.code === 200) {
      dataAnnualInterestRateObj.value = {
        dailyInterestRate: data.data?.showRepaymentPlan.dayRate || 0,
        annualInterestRate: data.data?.showRepaymentPlan.yearRate || 0,
      }
      if (initData.value.chargeMethod == 1) {
        // columns1.push
        data.data.showRepaymentPlan.stagRecords[0].planFeeList.forEach(
          (item, index) => {
            Map[item.feeName] = {
              index,
              collectFeeMethod: item.collectFeeMethod,
              amount: item.amount,
            }
            // 第一次加载添加，第二次不添加
            if (!tarGet.value) {
              columns1.value.push({
                title: item.feeName,
                dataIndex: 'amount' + index,
              })
            }
          }
        )
        tarGet.value = true
      } else {
        data.data.expenseOrderDetailFinanceVos.forEach(item => {
          let amount = item.expenseOrderDetailList.reduce((prve, next) => {
            next.repaymentTerm = next.repaymentTerm
              ? next.repaymentTerm + '期'
              : '--'
            const nAmo = Number(next.amount)
            if (next.feeFormulaName === '手填') {
              if (nAmo) {
                return prve + nAmo
              } else {
                next.amount = '人工计算'
                return prve
              }
            }
            return prve + nAmo
          }, 0)
          item.expenseOrderDetailList.push({
            name: '总计:',
            feeNameType: '',
            feeNodeStr: '',
            feeFormulaName: '',
            amount: `￥${formatMoney(amount)}`,
          })
        })
        otherFeeData.value = data.data.expenseOrderDetailFinanceVos
      }
      if (data.data?.showRepaymentPlan.stagRecords?.length) {
        for (const item of data.data.showRepaymentPlan.stagRecords) {
          allMonrySum(item)
          list.push({
            ...item,
            monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
            monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
            planInterest: `￥${formatMoney(item.planInterest)}`,
            // name: item.term + '期',
          })
          list.push({
            // name: '总计:',
            refundTime: '总计:',
            monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
            monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
            planInterest: `￥${formatMoney(item.planInterest)}`,
          })
          //  资方统一清分动态费用 随借随还的逻辑
          if (initData.value.chargeMethod === 1) {
            item.planFeeList.forEach((citem, cindex) => {
              list[0][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
              list[1][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
            })
          }
        }
      }
    }
    setTimeout(() => {
      tableDataLoad.value = false
    }, 200)
    tableData1.value = list
  })
}

const onload = () => {
  getList()
  // initData.chargeMethod
  if (initData.value.chargeMethod === 2 || initData.value.lendingMethod === 2) {
    requestDictMap('goods_expense_rule_fee_node').then(res => {
      const resData = res.data
      if (resData.code == 200) {
        // 处理字典数据
        const resList = []
        for (const item of resData.data) {
          resList.push({
            key: item.dictKey,
            value: item.dictValue,
            id: item.id,
          })
        }
        requestDictMap('goods_expense_rule_type').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList1 = []
            for (const item of resData.data) {
              resList1.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            // expenseList(resList, resList1)
          }
        })
      }
    })
  }
}

// 其他费用
const expenseList = (arr, arr1) => {
  const params = {
    goodsId: props.data.goodsId,
    chargePoint: 8, // 8 融资申请
    financeAmount: props.financingTotal, // 融资金额
    loanDay: props.data.loanPeriod, // 融资天数
  }
  tableDataLoad2.value = true
  PRODUCT_APPLI_CATIONS_API.expenseList(params).then(res => {
    const resData = res.data
    tableData2.value = []
    if (resData.code == 200) {
      const dat = resData.data

      let sum = 0
      for (const item of dat) {
        sum += Number(item.amount)
        // 过滤出当前的支付节点
        const chargePointFilter = arr.filter(
          itemed => itemed.key == item.feeNode
        )
        // 过滤出当前的费用类型
        const expenseTypeFilter = arr1.filter(
          itemed => itemed.key == item.expenseType
        )
        tableData2.value.push({
          name: item.name,
          feeNameType: expenseTypeFilter?.[0]?.value,
          chargePoint: chargePointFilter?.[0]?.value,
          chargeMethodStr:
            item.calculation != 1 ? item.feeFormulaName : '人工计算',
          amount:
            item.calculation != 1 ? `￥${formatMoney(item.amount)}` : '待计算',
        })
      }
      // 总计
      tableData2.value.push({
        name: '总计:',
        feeNameType: '',
        chargePoint: '',
        chargeMethodStr: '',
        amount: `￥${formatMoney(sum)}`,
      })
      allMonry.value += sum
    }
    setTimeout(() => {
      tableDataLoad2.value = false
    }, 200)
  })
}

defineExpose({
  handleOpen,
})
</script>
<style lang="scss">
.billing-rules-tooltip {
  max-width: none;
  .ant-tooltip-inner {
    padding: 16px;
    border-radius: 8px;
    // background: rgba(0, 0, 0, 0.6);
    box-shadow: 0px 4px 24px 0px rgba(10, 31, 68, 0.2),
      0px 0px 1px 0px rgba(10, 31, 68, 0.08);
    // border: 1px solid #9ba1a5;
    backdrop-filter: blur(4px);
  }
}
</style>
<style lang="scss" scoped>
.trade-space-box {
  .label-tab-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .explain-box {
      display: flex;
      align-items: flex-start;
      cursor: pointer;

      & > span:last-child {
        width: 28px;
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #8a94a6;
        line-height: 20px;
        margin-left: 4px;
      }
    }
  }
  .cost-box {
    border-radius: 8px;
    border: 1px solid #e1e4e8;

    .table-father-box {
      // max-height: calc(80vh - 250px);
      // overflow: auto;

      .table-box {
        margin: 24px 24px 0 24px;
        .table-title-box {
          display: flex;
          align-items: center;
          margin-bottom: 20px;

          .table-bank {
            width: 64px;
            height: 24px;
            font-size: 16px;
            font-weight: 500;
            color: #53627c;
            line-height: 24px;
          }
          .long-string {
            display: inline-block;
            width: 1px;
            height: 16px;
            background: #b5bbc6;
            margin: 0 12px 0 12px;
          }
          .interest-rate {
            height: 24px;
            font-size: 16px;
            font-weight: 500;
            color: #8a94a6;
            line-height: 24px;
          }
        }
      }
    }

    .all-money-box {
      height: 76px;
      background: #ebf5ff;
      border-radius: 0px 0px 8px 8px;
      border-top: 1px solid #e1e4e8;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24px;

      .left-money-box {
        display: flex;
        align-items: center;
        margin-left: 40px;

        & > span:last-child {
          height: 24px;
          font-size: 16px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 24px;
          margin-left: 4px;
        }
      }

      .right-money-box {
        display: flex;
        align-items: center;
        margin-right: 24px;

        & > span:first-child {
          width: 51px;
          height: 28px;
          font-size: 20px;
          font-weight: 600;
          color: #0a1f44;
          line-height: 28px;
        }

        & > span:last-child {
          height: 40px;
          font-size: 30px;
          color: #0a1f44;
          line-height: 38px;
          font-weight: 800;
        }
      }
    }
  }
}

.billing-text-box {
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  & > span:last-child {
    width: 28px;
    height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
    margin-left: 4px;
  }
}

.wrap {
  width: 300px;
  color: #ffffff;
}

.title {
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
}

.row-text {
  padding-bottom: 8px;
  &:last-child {
    padding-bottom: 0;
  }
  .cont {
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
    .field {
      font-size: 12px;
      font-family: PingFangSC-Semibold, PingFang SC;
    }
    .val {
      font-size: 12px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
    }
  }
  .help {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    opacity: 0.8;
  }
}
</style>
