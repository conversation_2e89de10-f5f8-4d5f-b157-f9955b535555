<template>
  <div class="financing-container">
    <div class="financing-total">
      <div class="financing-total-title">融资金额(元)</div>
      <div class="financing-total-detail">
        <div class="financing-total-num">
          <span>￥</span>
          <span>{{ formatMoney(detailData.amount || 0) }}</span>
        </div>
      </div>
    </div>

    <div class="pledged-goods">
      <a-collapse v-model:activeKey="activeKey1" ghost>
        <!-- 还款计划更多详情 -->
        <a-collapse-panel key="1" :forceRender="true">
          <template #header>
            <div class="pledged-text-box">
              <span class="pledged-goods-text">还款计划</span>
              <span class="long-string"></span>
              <span class="pledged-goods-money">￥{{ planTotalAmount }}</span>
            </div>
          </template>

          <div class="plan-list">
            <div
              v-for="item in planList.repaymentPlanList"
              :key="item.id"
              :class="[
                'plan-item',
                item.overdue === 1 ? 'plan-bg-repayable' : 'plan-bg-overdue',
              ]"
            >
              <div class="plan-item-left">
                <span
                  :style="{
                    backgroundColor: item?.statusText?.color,
                  }"
                  >{{ item.statusText?.text }}</span
                >
                <span
                  >{{ dayjs(item.repaymentTime).format('YYYY年MM月DD日')
                  }}{{ [2, 5].includes(item.status) ? '应还' : '还款' }}</span
                >
              </div>
              <div class="plan-item-right">
                <div class="plan-total-item">
                  <span>本金:￥{{ formatMoney(item.principal) }}</span>
                </div>
                <div class="plan-total-item">
                  <span>利息: ￥{{ formatMoney(item.planInterest) }} </span>
                </div>
                <div class="plan-total-item" v-if="item.penaltyInterest > 0">
                  <span
                    >逾期罚息: ￥{{
                      formatMoney(item.penaltyInterest || 0)
                    }}</span
                  >
                </div>
                <div
                  class="plan-total-item"
                  v-for="citem in item.repaymentPlanFeeVOList"
                  :key="citem.id"
                >
                  <span
                    >{{ citem.feeName }}: ￥{{
                      formatMoney(citem.needPayAmount || 0)
                    }}</span
                  >
                </div>
                <div class="plan-total-item">
                  <span>总额:￥{{ formatMoney(item.totalAmount || 0) }}</span>
                  <MySvgIcon
                    icon-class="icon-xinxi1"
                    style="fill: #0a1f44; font-size: 20px; margin-left: 8px"
                  ></MySvgIcon>
                </div>
              </div>
            </div>
          </div>
        </a-collapse-panel>

        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
      </a-collapse>
    </div>
    <div class="pledged-goods" v-if="planList?.repaymentList?.length">
      <a-collapse v-model:activeKey="activeKey2" ghost>
        <!-- 已还明细更多详情 -->
        <a-collapse-panel key="1" :forceRender="true">
          <template #header>
            <div class="pledged-text-box">
              <span class="pledged-goods-text">已还明细</span>
              <span class="long-string"></span>
              <span class="pledged-goods-money">￥{{ repaymentAmount }}</span>
            </div>
          </template>
          <div class="plan-list">
            <div
              v-for="item in planList.repaymentList"
              :key="item.id"
              class="plan-item plan-bg-repayable"
            >
              <div class="plan-item-left">
                <span
                  :style="{
                    backgroundColor: item?.statusText?.color,
                  }"
                  >{{ item.statusText?.text }}</span
                >
                <span
                  >{{ dayjs(item.repaymentTime).format('YYYY年MM月DD日')
                  }}{{ [2, 5].includes(item.status) ? '应还' : '还款' }}(第{{
                    item.period
                  }}期)</span
                >
              </div>
              <div class="plan-item-right">
                <div class="plan-total-item">
                  <span>本金:￥{{ formatMoney(item.principal) }}</span>
                </div>
                <div class="plan-total-item">
                  <span>利息: ￥{{ formatMoney(item.interest) }} </span>
                </div>
                <div
                  class="plan-total-item"
                  v-if="
                    item.repaymentType == 3 && Number(item.penaltyInterest) > 0
                  "
                >
                  <span
                    >逾期罚息: ￥{{
                      formatMoney(item.penaltyInterest || 0)
                    }}</span
                  >
                </div>
                <div
                  class="plan-total-item"
                  v-if="
                    item.repaymentType == 2 && Number(item.serviceCharge) > 0
                  "
                >
                  <span
                    >手续费: ￥{{ formatMoney(item.serviceCharge || 0) }}</span
                  >
                </div>
                <div
                  class="plan-total-item"
                  v-for="citem in item.repaymentFeeList"
                  :key="citem.id"
                >
                  <span
                    >{{ citem.feeName }}: ￥{{
                      formatMoney(citem.actualAmount || 0)
                    }}</span
                  >
                </div>
                <div class="plan-total-item">
                  <span>总额:￥{{ formatMoney(item.totalAmount || 0) }}</span>
                  <MySvgIcon
                    icon-class="icon-xinxi1"
                    style="fill: #0a1f44; font-size: 20px; margin-left: 8px"
                  ></MySvgIcon>
                </div>
              </div>
            </div>
          </div>
        </a-collapse-panel>

        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
      </a-collapse>
    </div>

    <div class="pledged-goods">
      <a-collapse v-model:activeKey="activeKey3" ghost>
        <!-- 更多详情 -->
        <a-collapse-panel key="1" :forceRender="true">
          <template #header>
            <div class="pledged-text-box">
              <span class="pledged-goods-text">更多详情</span>
            </div>
          </template>
          <div class="forMore-etails">
            <div class="tradefor-be-surrounded-box">
              <div class="tradefor-box">
                <span>融资金额</span>
                <span class="tradefor-text"
                  >{{ formatMoney(detailData.amount || 0) }}元</span
                >
              </div>
              <div class="tradefor-box">
                <span>收款账户</span>
                <span class="tradefor-text">{{
                  planList.repaymentAccount
                }}</span>
              </div>
              <div class="tradefor-box">
                <span>起止时间</span>
                <span class="tradefor-text"
                  >{{ planList?.startDate }} - {{ planList?.endDate }}</span
                >
              </div>
              <div class="tradefor-box">
                <span>年利率(单利)</span>
                <span class="tradefor-text"
                  >{{ planList.annualInterestRate }}%（日利率*360）</span
                >
              </div>
              <div class="tradefor-box">
                <span>日利率</span>
                <span class="tradefor-text"
                  >{{ planList.dailyInterestRate }}%</span
                >
              </div>

              <div class="tradefor-box">
                <span>还款方式</span>
                <span class="tradefor-text">随借随还</span>
              </div>
              <div class="tradefor-box">
                <span>还款日</span>
                <span class="tradefor-text" v-if="planList.dueDate">{{
                  planList.dueDate
                }}</span>
              </div>
              <div class="tradefor-box">
                <span>融资期限</span>
                <span class="tradefor-text" v-if="planList?.loanTermUnit === 1"
                  >{{ planList?.loanTerm }}天（天）</span
                >
                <span v-else>{{ planList?.loanTerm }}个月（期）</span>
              </div>
              <div class="tradefor-box">
                <span>还款账户</span>
                <span class="tradefor-text">{{
                  planList?.repaymentAccount
                }}</span>
              </div>
              <div class="tradefor-box">
                <span>融资用户</span>
                <span class="tradefor-text">{{ planList?.userName }}</span>
              </div>
              <div class="tradefor-box">
                <span>统一社会代码</span>
                <span class="tradefor-text">{{ planList?.creditCode }}</span>
              </div>
              <div class="tradefor-box">
                <span>融资产品</span>
                <span class="tradefor-text">{{ planList?.goodsName }}</span>
              </div>
              <div class="tradefor-box">
                <span>业务类型</span>
                <span class="tradefor-text">代采融资</span>
              </div>
              <div class="tradefor-box">
                <span>资金方</span>
                <span class="tradefor-text">{{ planList?.capitalName }}</span>
              </div>
              <div class="tradefor-box">
                <span>融资用途</span>
                <span class="tradefor-text">企业经营</span>
              </div>
            </div>
          </div>
        </a-collapse-panel>

        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
      </a-collapse>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubstituteFinancingDeatil',
}
</script>

<script setup>
import { formatMoney } from '@/utils/utils'
import { onMounted, ref, watch } from 'vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'
import { PRODUCT_APPLI_CATIONS_API } from '@/api/index'
import dayjs from 'dayjs'
const props = defineProps({
  detailData: {
    type: Object,
    default: () => {},
  },
  financeApplyId: {
    type: String,
    default: '',
  },
})
const detailList = ref({})
const planList = ref({})
const planTotalAmount = ref(0) // 计划总额
const repaymentAmount = ref(0) // 计划总额
watch(
  () => props.detailData,
  val => {
    if (val) {
      detailList.value = { ...val }
    }
  },
  { deep: true }
)

const getLoanInfoList = () => {
  if (props.financeApplyId) {
    let repaymentPlanList = []
    let repaymentList = []
    let sum = 0
    let sum1 = 0
    PRODUCT_APPLI_CATIONS_API.loanInfo(props.financeApplyId).then(
      ({ data }) => {
        if (data.code === 200) {
          for (const item of data.data.repaymentPlanList) {
            repaymentPlanList.push({
              ...item,
              statusText: getPlanStatus(item.status),
              penaltyInterest: formatMoney(item.penaltyInterest),
            })
            sum += Number(item.totalAmount || 0)
          }

          for (const item of data.data.repaymentList) {
            repaymentList.push({
              ...item,
              statusText: getRepaymentTypeText(item.repaymentType),
              penaltyInterest: formatMoney(item.penaltyInterest),
            })
            sum1 += Number(item.totalAmount || 0)
          }
          planList.value = {
            ...data.data,
            repaymentPlanList,
            repaymentList,
            startDate: dayjs(data.data.startDate).format('YYYY/MM/DD'),
            endDate: dayjs(data.data.endDate).format('YYYY/MM/DD'),
            dueDate: dayjs(data.data.firstRepaymentDate).format(
              'YYYY年MM月DD日'
            ),
          }
        }
        planTotalAmount.value = formatMoney(sum)
        repaymentAmount.value = formatMoney(sum1)
      }
    )
  }
}

const getRepaymentTypeText = state => {
  const arr = [
    {
      text: '正常还款',
      color: '#0BB07B',
    },
    {
      text: '提前还款',
      color: '#0BB07B',
    },
    {
      text: '逾期还款',
      color: '#f03d3d',
    },
    {
      text: '提前结清',
      color: '#0BB07B',
    },
  ]
  return arr[state - 1]
}

const getPlanStatus = state => {
  const obj = {}
  switch (state) {
    case 1:
      obj.text = '待还款'
      obj.color = '#a6aebc'
      break
    case 2:
      obj.text = '本期应还'
      obj.color = '#0d55cf'
      break
    case 3:
      obj.text = '正常还款'
      obj.color = '#0BB07B'
      break
    case 4:
      obj.text = '逾期还款'
      obj.color = '#f03d3d'
      break
    case 5:
      obj.text = '逾期待还'
      obj.color = '#f03d3d'
      break
    case 6:
      obj.text = '已作废'
      obj.color = '#a6aebc'
      break
    case 7:
      obj.text = '提前还款'
      obj.color = '#0BB07B'
      break
    case 8:
      obj.text = '提前结清'
      obj.color = '#0BB07B'
      break
  }
  return obj
}
onMounted(() => {
  getLoanInfoList()
})

const activeKey1 = ref([])
const activeKey2 = ref([])
const activeKey3 = ref([])
</script>

<style lang="scss" scoped>
.financing-container {
  position: relative;
  z-index: 99;
  .financing-total {
    .financing-total-title {
      font-size: 24px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #0a1f44;
      line-height: 32px;
      margin-bottom: 12px;
    }
    .financing-total-detail {
      display: flex;
      align-items: center;
      margin-bottom: 40px;
      .financing-total-num {
        display: flex;
        align-items: baseline;
        line-height: 77px;
        margin-right: 24px;
        & span {
          color: #031222;
          font-family: CoreSansD65Heavy;
          &:first-child {
            font-size: 24px;
          }
          &:last-child {
            font-size: 64px;
          }
        }
      }
    }
  }
}

:deep(.pledged-goods) {
  .pledged-text-box {
    display: flex;
    align-items: center;

    .pledged-goods-text {
      height: 24px;
      font-size: 16px;
      font-weight: 600;
      color: #0a1f44;
      line-height: 24px;
    }
    .pledged-goods-money {
      line-height: 24px;
      color: #0a1f44;
      font-size: 16px;
      font-weight: 500;
      font-family: CoreSansD55Bold;
      display: block;
    }
    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      background: #b5bbc6;
      margin: 0 12px;
    }
    .pledged-goods-num {
      .ant-statistic-content {
        height: 24px;
        line-height: 0;
      }

      .ant-statistic-content-value {
        font-size: 16px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 24px;
      }
      .ant-statistic-content-prefix {
        font-size: 16px;
        font-weight: 600;
        margin-right: -1px;
      }
    }
  }
  .plan-list {
    .plan-item {
      padding: 14px 20px;

      border-radius: 8px;

      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 8px;
      .plan-item-left {
        display: flex;
        width: 25%;
        align-items: center;
        & span:first-child {
          // display: block;
          // font-size: 11px;
          // font-family: SFProText-Semibold, SFProText;
          // font-weight: 600;
          // color: #ffffff;
          // line-height: 16px;
          // padding: 4px 10px;
          // border-radius: 100px;
          // margin-right: 12px;
          // display: inline-block;
          height: 24px;
          font-size: 11px;
          font-weight: 600;
          color: #ffffff;
          line-height: 16px;
          padding: 4px 10px;
          box-sizing: border-box;
          border-radius: 100px;
          margin-right: 12px;
        }
        & span:last-child {
          font-size: 14px;
          font-family: SFProText-Semibold, SFProText;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
        }

        .text-bg-blue {
          background-color: #0d55cf;
        }
        .text-bg-green {
          background-color: #0bb07b;
        }
        .text-bg-red {
          background-color: #f03d3d;
        }
      }
      .plan-item-right {
        display: flex;
        width: 75%;
        align-items: center;
        width: 75%;
        flex-wrap: wrap;
        .plan-total-item {
          display: flex;
          align-items: center;

          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #0a1f44;
          line-height: 20px;
          & span {
            display: inline-block;
            text-align: right;
            width: 200px;
          }
        }
        .plan-total-item + .plan-total-item {
          margin-left: 40px;
        }
      }
    }
    .plan-item + .plan-item {
      margin-top: 8px;
    }
    .plan-bg-overdue {
      background-color: #ffdc99;
      border: unset;
    }
    .plan-bg-repayable {
      background-color: #f8f9fb;
      border: 1px solid #efefef;
    }
  }
  .tradefor-information-box {
    display: flex;
    flex-direction: column;

    .tradefor-for {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 40px;
      }

      & > .base-card-item {
        margin-top: 0;
        margin-right: 0;
      }
    }
  }
  // 更多信息class
  .forMore-etails {
    .tradefor-be-surrounded-box {
      .tradefor-box {
        cursor: context-menu;
        margin-bottom: 20px;
        display: flex;

        & > span:first-child {
          width: 116px;
          height: 20px;
          display: inline-block;
          font-weight: 400;
          color: #8a94a6;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }
        .tradefor-contract {
          display: flex;
          align-items: center;
          .contract-link-item {
            display: block;
            cursor: pointer;
            color: #0d55cf;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }
          .contract-link-item + .contract-link-item {
            margin-left: 12px;
          }
        }
        .tradefor-text {
          height: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #0a1f44;
          line-height: 20px;
        }
      }
    }
  }
  // 折叠面板样式修改
  .ant-collapse-header {
    display: flex;
    padding: 12px 3px 8px;
    box-sizing: border-box;
  }
  .ant-collapse-arrow {
    font-size: 19px;
    vertical-align: -4px;
    margin-right: 6px;
    color: #758196;
  }
  .ant-collapse-content-box {
    padding: 12px 0 0;
  }
}
</style>
