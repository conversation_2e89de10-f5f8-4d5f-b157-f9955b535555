<template>
  <div class="newLoanDepositorCost-other-expenses">
    <Feiyongwaihezi ref="feiyongwaiheziRef" />
    <VFormRender ref="VFormRenderRef" />
    <!-- 合同 -->
    <Contract
      style="margin: -55px 0 30px"
      :processIndex="
        route.query.isAloneCollect === 'true'
          ? 9
          : route.query.isAloneCollect === 'false'
          ? 8
          : 10
      "
      :receiveData="financeNo"
      :flowingWave="true"
    />
    <div class="menu-bottom-box">
      <span>
        <NButton
          class="blue button-item"
          style="width: 188px; height: 48px"
          :bordered="true"
          round
          @click="previousToPape()"
        >
          <span class="desc">上一步</span>
        </NButton>
      </span>
      <span>
        <NButton
          class="blue button-item primary"
          style="width: 188px; height: 48px"
          :bordered="true"
          type="info"
          round
          @click="nextToPape()"
        >
          <span class="desc">下一步</span>
        </NButton>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'newLoanDepositorCost',
}
</script>
<script setup>
import Feiyongwaihezi from '@/components/feiyongxiaoheizi'
import Contract from '@/views/product/component/contract'
import VFormRender from '@/components/VFormRender'
import { NButton } from 'naive-ui'
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { contractListCheckId } from '@/views/product/component/contract/components/sign'
import { message } from 'ant-design-vue'
const store = useStore()
const route = useRoute()

// 判断是否有合同未签署（没配置合同就不会判断）
const signStatus = computed(() => store.getters['Product/signStatus'])
const demonstrator = computed(() => store.getters['Product/demonstrator'])
// 是否核心企业（Boolean）
const isCoreEnterpriseAccount = computed(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
const feiyongwaiheziRef = ref(null)
const VFormRenderRef = ref(null)

const props = defineProps({
  financeNo: {
    type: String,
    default: null,
  },
})

// 上一步
const previousToPape = () => {
  VFormRenderRef.value.lastStep()
  // emit('goStepSecond')
}

// 立即提交事件
const nextToPape = () => {
  if (!feiyongwaiheziRef.value.exposeFeiyongListFun()) {
    return
  }
  if (signStatus.value && !demonstrator.value) {
    message.error('还有授权书未签署,请先签署完成')
    return
  }

  let params = {
    financeNo: props.financeNo,
    expenseInfoExpenseList: feiyongwaiheziRef.value.exposeFeiyongListFun(),
  }
  // 获取合同签署的合同list的id
  const propss = {
    processIndex:
      route.query.isAloneCollect === 'true'
        ? 9
        : route.query.isAloneCollect === 'false'
        ? 8
        : 10,
    receiveData: props.financeNo,
  }
  const data = { props: propss, route, isC: isCoreEnterpriseAccount.value }
  contractListCheckId(data).then(({ idArr }) => {
    if (idArr) {
      VFormRenderRef.value
        .submitForm({ externalData: params, contractIds: idArr })
        .then(data => {
          // emit('confirm')
        })
    }
  })
}
</script>

<style lang="scss" scoped>
.newLoanDepositorCost-other-expenses {
  max-width: 1400px;
  position: relative;
  margin: auto;
  box-sizing: border-box;

  .flex {
    display: flex;
    align-items: center;
  }
  .infometion-box {
    margin-top: 40px;
    background: #ffffff;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 1px solid #efefef;
    padding: 40px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;

    .infometion-top-box {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .infometion-left {
        .left-title {
          height: 28px;
          font-size: 20px;
          font-weight: 600;
          color: #0a1f44;
          line-height: 28px;
        }
        .left-subtitle {
          display: flex;
          align-items: center;
          margin-left: 12px;
          background-color: #ebf5ff;
          padding: 8px 3px 8px 21px;
          box-sizing: border-box;
          border-radius: 8px;
          & span {
            margin-left: 2px;
            display: block;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #0d55cf;
            line-height: 16px;
          }
        }
        .left-money {
          display: flex;
          align-items: center;
          margin: 10px 0 12px;
          .left-flex {
            display: flex;
            align-items: baseline;
            line-height: 58px;

            .num-icon {
              font-size: 24px;
              color: #031222;
              font-weight: 600;
            }
            .num {
              font-size: 48px;
              color: #031222;
              font-weight: 600;
            }
          }

          .play-type {
            height: 40px;
            background: #ebf5ff;
            padding: 10px 20px;
            box-sizing: border-box;
            font-size: 14px;
            font-weight: 500;
            color: #0d55cf;
            line-height: 20px;
            border-radius: 100px;
            display: flex;
            align-items: center;
            margin-left: 15px;

            .play-text {
              margin-left: 4px;
            }
          }
        }
        .left-charge {
          .charge-item {
            display: flex;
            align-items: center;
            & span:first-child {
              display: block;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #0a1f44;
              line-height: 20px;
            }
            & span:last-child {
              display: block;
              font-size: 14px;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #0a1f44;
              line-height: 20px;
              margin-left: 24px;
            }
          }
          .charge-item:first-child {
            margin-bottom: 20px;
          }
          .charge-item:last-child {
            margin-bottom: 24px;
          }
        }
        .left-tips {
          height: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 20px;
          margin-bottom: 24px;
        }
        .management-accounts {
          .accounts-p {
            margin-bottom: 32px;

            &:last-child {
              margin-bottom: 0;
            }

            span {
              display: inline-block;
            }

            & span:first-child {
              width: 70px;
              height: 20px;
              font-size: 14px;
              font-weight: 400;
              color: #8a94a6;
              line-height: 20px;
              margin-right: 10px;
            }
            & span:last-child {
              height: 20px;
              font-size: 14px;
              font-weight: 400;
              color: #0a1f44;
              line-height: 20px;
            }
          }
        }
      }

      .infometion-right {
        width: 394px;
      }
    }

    .infometion-bottom-box {
      overflow: hidden;
      margin-top: 40px;

      .bottom-title {
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #0a1f44;
        line-height: 20px;
      }
      .table-box {
        margin-top: 12px;
        border: 1px solid #f1f2f4;
      }
    }

    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      z-index: 0;
      width: 279px;
      height: 277px;
      top: -50px;
      right: -40px;
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.2;
      border-radius: 150px;
      filter: blur(49px);
    }
    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      z-index: 0;
      width: 106px;
      height: 143px;
      top: -20px;
      left: 0px;
      border-radius: 150px;
      filter: blur(24px);
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.2;
    }
  }

  .menu-bottom-box {
    text-align: center;
    margin-bottom: 48px;

    & span {
      margin-right: 24px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
