<template>
  <div class="bond-container">
    <div class="bond-amount-list">
      <div class="bond-amount-item">
        <div class="bond-item-top">
          <span>￥</span>
          <span>{{ formatMoney(allAmount.balance || 0) }}</span>
        </div>
        <div class="bond-item-bottom">保证金余额(元)</div>
      </div>
      <div class="bond-line"></div>
      <div class="bond-amount-item">
        <div class="bond-item-top">
          <span>￥</span>
          <span>{{ formatMoney(allAmount.refundAmount || 0) }}</span>
        </div>
        <div class="bond-item-bottom">退款余额(元)</div>
      </div>
      <div class="bond-line"></div>
      <div class="bond-amount-item">
        <div class="bond-item-top">
          <span>￥</span>
          <span>{{ formatMoney(allAmount.payableAmount || 0) }}</span>
        </div>
        <div class="bond-item-bottom">缴纳总额(元)</div>
      </div>
    </div>
    <div class="bond-table">
      <div class="table-title">保证金账单</div>
      <a-table
        class="bond-custom-table"
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="false"
      >
        <template #headerCell="{ column, title }">
          <template v-if="column.dataIndex === 'type'">
            <TableHeadFilter
              ref="payFilterRef"
              :title="title"
              :options="['支付', '退款']"
              @filterChange="handleFilterChange('PayCustom', $event)"
            />
          </template>
          <template v-else-if="column.dataIndex === 'bondType'">
            <TableHeadFilter
              ref="bondFilterRef"
              :title="title"
              :options="['初始保证金', '追加保证金']"
              @filterChange="handleFilterChange('BondCustom', $event)"
            />
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'type'">
            <span
              :class="record.payRefundType === 1 ? 'text-init' : 'text-init'"
              >{{
                record.payRefundType === 1
                  ? '支付'
                  : record.payRefundType === 0
                  ? '退款'
                  : ''
              }}</span
            >
          </template>
          <template v-else-if="column.dataIndex === 'amount'">
            <span class="border-amount" :class="getAmountStatus(record)"
              >{{
                record.payRefundType === 1
                  ? '-'
                  : record.payRefundType === 0
                  ? '+'
                  : ''
              }}{{ formatMoney(record.amount) }}
            </span></template
          >
          <template v-else-if="column.dataIndex === 'bondType'">
            <span class="text-init">{{
              record.cashDepositBillType === 1
                ? '初始保证金'
                : record.cashDepositBillType === 2
                ? '追加保证金'
                : ''
            }}</span>
          </template>
        </template>

        <template #emptyText>
          <template v-if="pageInitLoading"></template>
          <template v-else>
            <div class="empty-container">
              <img src="@/assets/images/empty_2.svg" alt="" />
              <span class="desc">暂无数据</span>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BondTable',
}

const columns = [
  {
    title: '时间',
    dataIndex: 'createTime',
    width: 120,
  },
  {
    title: '交易流水',
    dataIndex: 'transactionNo',
    width: 140,
  },
  {
    title: '收支类型',
    dataIndex: 'type',
    width: 190,
  },
  {
    title: '金额(元)',
    dataIndex: 'amount',
    width: 190,
  },
  {
    title: '保证金类型',
    dataIndex: 'bondType',
  },
  {
    title: '付款账户',
    dataIndex: 'payCompanyAccountName',
  },
  {
    title: '收款账户',
    dataIndex: 'proceedsCompanyAccountName',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
]
</script>

<script setup>
import { formatMoney } from '@/utils/utils'
import {
  getBondDetail,
  getInitBondDeposit,
  getAllBondBilList,
} from '@/api/user/generat/index'
import { onMounted, ref } from 'vue'
import TableHeadFilter from '@/components/TableHeadFilter/index.vue'

const props = defineProps({
  financeNo: {
    type: String,
    default: '',
  },
})

const allAmount = ref({})
const tableData = ref([])
const loading = ref(false)

onMounted(() => {
  getAllBondDetails()
})

getBondDetail({ financeNo: props.financeNo }).then(({ data }) => {
  if (data.code === 200 && data.data) {
    //allAmount.value = { ...data.data }
  }
})

const getAllBondDetails = () => {
  loading.value = true
  getInitBondDeposit({ financeNo: props.financeNo })
    .then(({ data }) => {
      if (data.code === 200 && data.data) {
        allAmount.value = { ...data.data }
        const { id } = data.data
        if (id) {
          getAllBondBilListTable({ id })
        }
      }
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
const getAllBondBilListTable = params => {
  let list = []
  getAllBondBilList(params).then(({ data }) => {
    if (data.code === 200 && data.data) {
      data.data.forEach((item, index) => {
        list.push({
          ...item,
          index: index + 1,
        })
      })
    }
    tableData.value = list
  })
}

const getAmountStatus = ({ payRefundType }) => {
  let textColor = ''
  switch (payRefundType) {
    case 0:
      textColor = 'text-red'
      break
    case 1:
      textColor = 'text-black'
      break
  }
  return textColor
}
const handleFilterChange = (target, targetFilterData) => {
  // filterData[target] = targetFilterData
  // tablePaginationData.value = createInitPagData()
  // initTableData()
}
</script>

<style lang="scss" scoped>
.bond-container {
  width: 100%;
  position: relative;
  z-index: 99;
  .bond-amount-list {
    border: 1px solid #efefef;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 16px;
    padding: 26px 0 22px;
    margin-bottom: 40px;
    background-color: #fff;
    .bond-line {
      width: 1px;
      height: 64px;
      background-color: #e1e4e8;
    }
    .bond-amount-item {
      padding: 0 24px;
      width: 100%;
      text-align: center;
      box-sizing: border-box;
      .bond-item-top {
        font-size: 20px;
        font-family: SFProDisplay-Regular, SFProDisplay;
        font-weight: 400;
        color: #0a1f44;
        line-height: 40px;
        margin-bottom: 12px;
      }
      .bond-item-bottom {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8a94a6;
        line-height: 28px;
      }
    }
  }
  .bond-table {
    .table-title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #0a1f44;
      font-family: PingFangSC-Semibold, PingFang SC;
    }
  }
}
.bond-custom-table {
  margin-top: 20px;

  // 表格
  :deep(.ant-table) {
    // 去除表头分隔线
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      content: none;
    }

    .ant-table-thead {
      th {
        padding: 0 12px;
        height: 40px;
        line-height: 40px;
        background-color: #f8f9fb;
        font-size: 14px;
        @include family-PingFangSC-Medium;
        font-weight: 500;
        color: #8a94a6;
      }
    }

    .ant-table-tbody {
      // 鼠标停留背景色
      tr.ant-table-row:hover > td {
        background: #f5faff;
      }

      // 清除无数据时的边框
      .ant-table-placeholder > td {
        border: none;
      }

      td {
        padding: 6px 12px;
        height: 48px;
        line-height: 1.24;
        font-size: 14px;
        @include family-PingFangSC-Medium-SFProText;
        font-weight: 500;
        color: #0a1f44;

        .ant-statistic-content {
          line-height: 1.24;
          font-size: 14px;
          @include family-PingFangSC-Medium-SFProText;
          font-weight: 500;
          color: #0a1f44;

          .ant-statistic-content-prefix {
            margin-right: 0px;
          }
        }
      }
    }
  }

  // 表格自定义列
  .action-column-container {
    display: flex;

    & > span {
      margin-right: 8px;
      font-size: 14px;
      @include family-PingFangSC-Medium;
      font-weight: 500;
      color: #0d55cf;
      line-height: 20px;
      word-break: keep-all;
      cursor: pointer;

      a {
        color: #0d55cf;
      }

      &:hover {
        text-decoration: underline;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .single-line-text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }

  // 分页
  :deep(.ant-pagination) {
    display: flex;
    align-items: center;
    justify-content: center;
    // 去除分页下外边距
    margin-bottom: 0;

    .ant-pagination-disabled {
      .custom-pagination-item-link {
        color: #a6aebc !important;
        border: 1px solid transparent;
      }
    }

    .custom-pagination-item-link {
      background: #f8f9fb;
      color: #53627c;
    }

    .custom-pagination-left {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px 0 12px;
      border-radius: 100px 0px 0px 100px;
    }

    .custom-pagination-right {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px 0 16px;
      border-radius: 0px 100px 100px 0px;
    }
  }

  .empty-container {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 40px auto;
    text-align: center;

    img {
      width: 200px;
      height: 200px;
    }

    .desc {
      font-size: 14px;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }
  }
  .text-init {
    display: inline-block;
    padding: 6px 10px;
    background-color: #f1f2f4;
    color: #0a1f44;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    border-radius: 100px;
  }
  .text-green {
    color: #00865a;
    background-color: #cff8eb;
  }
  .text-red {
    color: #dd2727;
  }
  .text-black {
    color: #0a1f44;
  }
}
</style>
