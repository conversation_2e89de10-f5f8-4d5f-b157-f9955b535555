<template>
  <div class="tree-org-warpper">
    <BaseTreeOrg :cloudList="cloudList"></BaseTreeOrg>
    <BasicFooter
      :btn-options="btnOptions"
      @click="handleClickEvent"
    ></BasicFooter>
  </div>
</template>

<script>
import BasicFooter from '@/components/basic-footer/index'
import BaseTreeOrg from '@/components/BaseTreeOrg/index'
import { getTackRcordList } from '@/api/blockchain/information'
const btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
]
export default {
  components: { BaseTreeOrg, BasicFooter },
  data() {
    return {
      cloudList: [],
      btnOptions,
    }
  },
  created() {
    const params = this.$route.query
    if (params.cloudCoreCode) {
      this.getTackRcordList({ cloudCoreCode: params.cloudCoreCode })
    }
  },
  methods: {
    getTackRcordList(params) {
      let list = []
      getTackRcordList(params).then(({ data }) => {
        if (data.code === 200) {
          if (data.data && data.data.length) {
            list = this.handleChildData(data.data)
          }
        }
        this.cloudList = list
      })
    },

    // 底部按钮操作
    handleClickEvent(name) {
      switch (name) {
        case 'Revert':
          this.$router.$avueRouter.closeTag()
          this.$router.push('/blockchain/information')
          break
      }
    },
    handleChildData(data) {
      data.map(item => {
        if (item.children && item.children.length) {
          this.handleChildData(item.children)
        } else {
          item.children = []
        }
      })

      return data
    },
  },
}
</script>

<style lang="scss" scoped>
.tree-org-warpper {
  padding: 30px !important;
  background: #fff;
  border-radius: 4px;
  margin: 0 10px 99px;
  height: 800px !important;
  overflow: auto;
}
</style>
