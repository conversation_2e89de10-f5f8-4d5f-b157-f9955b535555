<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
<!--        <el-button-->
<!--          type="danger"-->
<!--          size="small"-->
<!--          icon="el-icon-delete"-->
<!--          plain-->
<!--          v-if="permission.blockchain_delete"-->
<!--          @click="handleDelete"-->
<!--          >删 除-->
<!--        </el-button>-->
      </template>
      <template slot="statusName" slot-scope="{ row }">
        <el-tag v-if="row.status == 0">申请云信额度</el-tag>
        <el-tag v-if="row.status == 1">云信开单</el-tag>
        <el-tag v-if="row.status == 2">云信融资</el-tag>
        <el-tag v-if="row.status == 3">核心企业自主申请额度</el-tag>
        <el-tag v-if="row.status == 4">资金方承兑额度</el-tag>
        <el-tag v-if="row.status == 5">核心企业自主激活额度</el-tag>
        <el-tag v-if="row.status == 6">贴现</el-tag>
        <el-tag v-if="row.status == 7">签收</el-tag>
        <el-tag v-if="row.status == 8">转让和流转</el-tag>
      </template>
      <template slot-scope="{ row, index, type, size }" slot="menu">
        <el-button
          icon="el-icon-top"
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click="rowOnShelf(row)"
          >上链轨迹</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from '@/api/blockchain/information'
import { mapGetters } from 'vuex'
var DIC = {
  STATUS: [
    {
      label: '已禁用',
      value: 0,
    },
    {
      label: '已启用',
      value: 1,
    },
  ],
}
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        editBtn: false,
        delBtn:false,
        searchMenuSpan: 6,
        addBtn:false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '区块链上链信息地址',
            prop: 'transactionAddress',
            rules: [
              {
                required: true,
                message: '请输入区块链上链信息地址',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '核心企业云信编号',
            prop: 'cloudCoreCode',
            searchLabelWidth: 130,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入产品id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '云信编号',
            prop: 'nextCloudCode',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入云信编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上链节点',
            prop: 'statusName',
            viewDisplay:false,
            slot: true,
            // search: true,
            // dicData: DIC.STATUS,
            // mock: {
            //   type: 'dic',
            // }
          },
          {
            label: '上链节点',
            prop: 'status',
            search: true,
            type: 'select',
            viewDisplay:false,
            dicUrl:
              '/api/blade-block-chain/web-back/blockChain/information/block-code',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            hide: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.blockchain_add, false),
        viewBtn: this.vaildData(this.permission.blockchain_view, false),
        delBtn: this.vaildData(this.permission.blockchain_delete, false),
        editBtn: this.vaildData(this.permission.blockchain_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    rowOnShelf(row) {
      this.$router.push({
        path: '/blockchain/information/blockChainTrackDetail',
        query: { cloudCoreCode: row.cloudCoreCode },
      })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
