<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row, index, type }" slot="menuForm">
        <el-button
          type="primary"
          size="small"
          plain
          v-if="
            (type == 'edit' || type == 'add')&& permission.expense_edit"
          @click="handleFormEnabled(row)"
        >启用
        </el-button>
      </template>
      <template slot="menuLeft">

      </template>
      <template slot-scope="{ row }" slot="menu">

        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.status == 0 && permission.expense_view"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.status == 0 && permission.expense_delete"
        >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          v-if="permission.expense_enable"
          @click="enabled(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
      <template slot-scope="{ scope, row }" slot="status">
        <el-tag type="success" v-if="row.status == 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>
      <template slot-scope="{ type, disabled }" slot="chargeMethodForm">
        <el-radio-group :disabled="disabled" v-model="form.chargeMethod">
          <el-radio :label="0">
            <el-select
              :disabled="disabled"
              v-model="clo1"
              placeholder="请选择"
              style="width: 200px !important"
            >
              <el-option
                v-for="item in cloOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            ×
            <el-input
              :disabled="disabled"
              v-model="clo1Text"
              style="width: 70px !important"
            ></el-input>
            %
          </el-radio>
          <el-row></el-row>
          <el-radio :label="1">
            <a style="width: 250px !important">固定费用</a>
            <el-input
              :disabled="disabled"
              v-model="fixFee"
              style="width: 145px !important"
            ></el-input>
            元
          </el-radio>
          <el-row></el-row>
          <el-radio :label="2">
            <a style="width: 250px !important">周期</a>
            <el-select
              :disabled="disabled"
              v-model="round1"
              style="width: 173px !important"
              placeholder="请选择"
            >
              <el-option
                v-for="item in roundOptions"
                :label="item.dictValue"
                :value="item.dictKey"
              >
              </el-option>
            </el-select>
            收取
            <el-select
              :disabled="disabled"
              v-model="round1Col"
              style="width: 150px !important"
              placeholder="请选择"
            >
              <el-option
                v-for="item in cloOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-input
              :disabled="disabled"
              v-model="round1ColText"
              style="width: 70px !important"
            ></el-input>
            %
          </el-radio>
          <el-row></el-row>

          <el-radio :label="3">
            <a style="width: 250px !important">周期</a>
            <el-select
              :disabled="disabled"
              v-model="round2"
              placeholder="请选择"
              style="width: 173px !important"
            >
              <el-option
                v-for="item in roundOptions"
                :label="item.dictValue"
                :value="item.dictKey"
              >
              </el-option>
            </el-select>
            收取固定费用
            <el-input
              :disabled="disabled"
              v-model="round2Fee"
              style="width: 95px !important"
            ></el-input>
            元
          </el-radio>
          <el-row></el-row>
          <el-radio :label="4">
            <a style="width: 250px !important">手填</a>
            <el-input
              :disabled="disabled"
              v-model="otherText"
              style="width: 173px !important"
            ></el-input>
          </el-radio>
          <el-row></el-row>
        </el-radio-group>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, enable, getDetail, getList, remove, update,} from '@/api/expense/expense'
import {mapGetters} from 'vuex'

export default {
  data() {
    return {
      cloOptions: [
        {
          value: 1,
          label: '借款本金',
        },
      ],
      roundOptions: [],
      clo1: '',
      clo1Text: '',

      fixFee: '',

      round1: '',
      round1Col: '',
      round1ColText: '',

      round2: '',
      round2Fee: '',

      otherText: '',

      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        delBtn: false,
        editBtn: false,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '费用名称',
            row: true,
            prop: 'name',
            rules: [
              {
                required: true,
                message: '请输入费用名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '收费节点',
            row: true,
            prop: 'chargePoint',
            type: 'select',
            dataType: "string",
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=jrzh_goods_expense_charge_point',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [
              {
                required: true,
                message: '请输入费用结点',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '计费方式',
            prop: 'chargeMethod',
            hide: true,
            formslot: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入计费方式',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '计费方式',
            display: false,
            prop: 'formulaShow',
          },
          {
            label: '最低金额(元)',
            prop: 'minRefund',
            row: true,
            type: "number",
            hide: true,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入最低金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '最高金额(元)',
            prop: 'maxRefund',
            type: "number",
            row: true,
            hide: true,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入最高金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '费用说明',
            hide: true,
            type: 'textarea',
            prop: 'remark',
          },
          {
            label: '上次修改时间',
            display: false,
            type: 'dateTime',
            prop: 'updateTime',
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '操作人',
            prop: 'updateUserName',
            display: false,
          },
          {
            label: '状态',
            prop: 'status',
            slot: true,
            display: false,
            dicData: [
              {
                label: '禁用',
                value: 0,
              },
              {
                label: '启用',
                value: 1,
              },
            ],
          },
          {
            label: '排序',
            prop: 'sort',
            type: 'number',
            minRows: 0,
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  watch: {
    'form.chargeMethod': {
      handler(newVal) {
        const column1 = this.findObject(this.option.column, 'minRefund')
        const column2 = this.findObject(this.option.column, 'maxRefund')
        if (newVal == 1 || newVal == 3) {
          column1.display = true
          column2.display = true
        } else {
          column1.display = false
          column2.display = false
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.expense_add, false),
        viewBtn: this.vaildData(this.permission.expense_view, false),
        delBtn: this.vaildData(this.permission.expense_delete, false),
        editBtn: this.vaildData(this.permission.expense_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    handleFormEnabled() {
      this.form.status = 1
      this.$refs.crud.rowUpdate()
    },
    resetFieldData() {
      this.clo1 = ''
      this.clo1Text = ''
      this.fixFee = ''
      this.round1 = ''
      this.round1Col = ''
      this.round1ColText = ''
      this.round2 = ''
      this.round2Fee = ''
      this.otherText = ''
    },
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    },
    enabled(row, done) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
      } else {
        msg = '确定禁用操作'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          enable(row.id, 0).then(
            () => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
              done()
            },
            error => {
              loading()
              window.console.log(error)
            }
          )
        } else {
          enable(row.id, 1).then(
            () => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
              done()
            },
            error => {
              loading()
              window.console.log(error)
            }
          )
        }
      })
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    //借款本金：clo1 --费率：clo1Text
    //固定 fixFee
    //每日 round1 借款本金 round1Col  费率round1ColText
    // 每日 round2  固定费用round2Fee
    // 其他otherText
    getFormDetails(row) {
      const chargeMethod = row.chargeMethod
      switch (chargeMethod) {
        case 0: {
          row.typeMoney = this.clo1;
          row.chargeRate = this.clo1Text;
          break
        }
        case 1: {
          row.chargeFee = this.fixFee
          break
        }
        case 2: {
          row.formula = this.round1;
          row.typeMoney = this.round1Col;
          row.chargeRate = this.round1ColText;
          break
        }
        case 3: {
          row.formula = this.round2;
          row.chargeFee = this.round2Fee;
          break
        }
        case 4: {
          row.formula = this.otherText
          break
        }
      }
    },
    rowSave(row, done, loading) {
      this.getFormDetails(row)
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      this.getFormDetails(row)
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
          let chargeMethod = this.form.chargeMethod
          switch (chargeMethod) {
            case 0:
              this.clo1 = this.form.typeMoney
              this.clo1Text = this.form.chargeRate
              break
            case 1:
              this.fixFee = this.form.chargeFee
              break
            case 2:
              this.round1 = this.form.formula
              this.round1Col = this.form.typeMoney
              this.round1ColText = this.form.chargeRate
              break
            case 3:
              this.round2 = this.form.formula
              this.round2Fee = this.form.chargeFee
              break
            case 4:
              this.otherText = this.form.formula
              break
          }
        })
      }
      done()
    },
    beforeClose(done) {
      this.resetFieldData()
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
      this.$axios.get("/api/blade-system/dict-biz/dictionary?code=expense_period").then(res => {
        this.roundOptions = res.data.data;
      })

    },
  },
}
</script>

<style></style>
