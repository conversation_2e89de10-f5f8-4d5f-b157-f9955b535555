<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{ row, index, type }" slot="menuForm">
        <el-button
          type="primary"
          size="small"
          plain
          v-if="
            (type == 'edit' || type == 'add') && permission.jobmgmt_enabled
          "
          @click="handleFormEnabled()"
        >启用
        </el-button>
      </template>
      <template slot="cornExpressionForm" slot-scope="scope">
        <div class="cron">
          <el-popover v-model="cronPopover">
            <cron i18n="cn" @change="changeCron" @close="cronPopover=false"/>
            <el-input
              slot="reference"
              v-model="form.cornExpression"
              placeholder="请输入定时策略"
              @click="cronPopover=true"/>
          </el-popover>
        </div>
      </template>


      <template slot-scope="{ scope, row }" slot="status">
        <el-tag type="success" v-if="row.status == 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          size="small"
          @click="handleTest(row)"
        >执行测试
        </el-button>
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.status == 0 && permission.jobmgmt_view"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.status == 0 && permission.jobmgmt_delete"
        >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          @click="enable(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update,test} from "@/api/timerjob/jobmgmt";
import {mapGetters} from "vuex";
import {cron} from 'vue-cron'

export default {
  components: {cron},
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        delBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "bean名称",
            prop: "beanName",
            rules: [{
              required: true,
              message: "请输入bean名称",
              trigger: "blur"
            }]
          },
          {
            label: "方法名称",
            prop: "methodName",
            rules: [{
              required: true,
              message: "请输入方法名称",
              trigger: "blur"
            }]
          },
          {
            label: "方法参数",
            prop: "methodParams",
          },
          {
            label: "corn表达式",
            prop: "cornExpression",
            rules: [{
              required: true,
              message: "请输入corn表达式",
              trigger: "blur"
            }]
          },
          {
            label: '状态',
            prop: 'status',
            display: false,
            formslot: true,
          },
          {
            label: '备注',
            prop: 'remark',
          },
        ]
      },
      data: [],
      cronPopover: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.jobmgmt_add, false),
        viewBtn: this.vaildData(this.permission.jobmgmt_view, false),
        delBtn: this.vaildData(this.permission.jobmgmt_delete, false),
        editBtn: this.vaildData(this.permission.jobmgmt_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    handleTest(row){
      this.$confirm("确定执行测试", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        test(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      })
    },
    changeCron(val) {
      this.form.cornExpression = val
    },
    handleFormEnabled() {
      this.form.status = 1
      this.$refs.crud.rowSave()
    },
    enable(row) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
      } else {
        msg = '确定禁用操作'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          row.status = 0
        } else {
          row.status = 1
        }
        this.rowUpdate(row)
      })
    },
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    handleEnable() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定启用操作?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return enabled(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleDisabled() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定禁用操作?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return disabled(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
