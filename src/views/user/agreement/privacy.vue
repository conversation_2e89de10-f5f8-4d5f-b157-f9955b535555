<template>
  <div class="news-detail-container">
    <div class="content-container">
      <div class="news-wrapper">
        <div class="news-header-container">
          <h2>《隐私协议》</h2>
          <div class="news-body-container">
            <div class="news-detail" v-html="userProtocolData?.content"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from 'vue'
import { userProtocol } from '@/api/user/agreement.js'

const userProtocolData = ref()

onBeforeMount(() => {
  document.body.scrollTop = 0
  document.documentElement.scrollTop = 0
})

userProtocol().then(res => {
  userProtocolData.value = res.data.data.records[0]
})
</script>

<style lang="scss" scoped>
.news-detail-container {
  .img-container {
    position: absolute;
    top: 0;
    width: 100%;
    height: 864px;
    z-index: -1;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .content-container {
    margin: 24px 0;

    .news-wrapper,
    .news-jump-container {
      max-width: 920px;
      margin: 0 auto;
      padding: 40px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
    }

    .news-header-container {
      padding-bottom: 40px;
      border-bottom: solid 1px #f1f2f4;

      & > h2 {
        font-size: 40px;
      }

      .newsNav {
        ::v-deep(.currentRoute) {
          width: auto !important;
        }
      }

      .news-title {
        margin-top: 20px;

        h1 {
          font-size: 24px;
          font-weight: 800;
          line-height: 32px;
          color: #0a1f44;
          margin: 0;
        }

        .desc {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #a6aebc;
          font-family: PingFangSC-Medium, PingFang SC;
          line-height: 20px;
          margin-top: 8px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          overflow-wrap: break-word;
        }
      }

      .news-info {
        margin-top: 20px;
        font-size: 0;

        .time,
        .views {
          span {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #a6aebc;
            line-height: 20px;
          }
        }

        .time {
          margin-right: 40px;
        }
      }
    }

    .news-body-container {
      margin-top: 40px;

      .news-detail {
        font-size: 14px;

        ::v-deep() {
          h1,
          h2,
          h3,
          h4,
          h5,
          h6 {
            color: #0a1f44;
          }

          p,
          span {
            color: #53627c;
          }

          img {
            width: 100%;
            height: auto;
            object-fit: contain;
          }
        }
      }
    }

    .news-jump-container {
      font-size: 0;
      margin-top: 24px;
      padding: 0;

      .news-jump-item {
        display: flex;
        padding: 20px 40px;

        border-bottom: solid 1px #e1e4e8;

        &:last-child {
          border-bottom: none;
        }

        .msg {
          display: inline-block;
          width: 100%;

          .caption {
            display: inline-block;
            width: 100%;
            font-size: 12px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #a6aebc;
            line-height: 16px;
          }

          .title {
            margin-top: 8px;
            display: inline-block;
            width: 100%;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #53627c;
            line-height: 24px;
          }
        }

        .icon {
          display: inline-block;
          width: 50px;
          flex-shrink: 0;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

// @media screen and (max-width: 1000px) {
//   .news-detail-container {
//     .img-container {
//       position: static;
//       height: 450px;
//     }

//     .content-container {
//       margin-top: 24px;

//       .news-wrapper {
//         padding: 24px !important;
//       }

//       .news-jump-container {
//         .news-jump-item {
//           padding: 20px 20px !important;
//         }
//       }
//     }
//   }
// }

// @media screen and (max-width: 770px) {
//   .news-detail-container {
//     .img-container {
//       position: static;
//       height: 347px;
//     }

//     .content-container {
//       .news-wrapper {
//         .newsNav {
//           display: none;
//         }

//         .news-title {
//           margin-top: 0;
//         }
//       }

//       .news-header-container {
//         padding-bottom: 2px;
//       }

//       .news-body-container {
//         margin-top: 2px;
//       }
//     }
//   }
// }

// @media screen and (max-width: 520px) {
//   .news-detail-container {
//     .content-container {
//       margin-top: 12px;
//       padding: 0 12px;

//       .news-wrapper {
//         padding: 12px !important;

//         .news-info {
//           margin-top: 12px;
//         }
//       }

//       .news-jump-container {
//         .news-jump-item {
//           padding: 12px !important;
//         }
//       }
//     }
//   }
// }
</style>
