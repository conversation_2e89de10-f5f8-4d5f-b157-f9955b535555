<template>
  <div class="cloud-wrapper">
    <div>
      <ArticleSetps
        :succerssType="false"
        :info="'云信付款'"
        :widths="'1000px'"
      />
    </div>
    <div>
      <div class="contractLoad-box" v-if="!errType">
        <div class="contractLoad-time">
          <img src="@/assets/images/productOpen/aperture.svg" alt="" />
          <div
            class="product-open-radius-plan"
            :class="{ 'err-product-open-radius-plan': errType }"
          >
            <div class="product-open-radius-plan-num">
              <span>{{ time }}</span>
              <span>s</span>
            </div>
          </div>
        </div>
      </div>
      <template v-if="errType">
        <div
          class="cloud-pay-type"
          v-if="cloudData.status == 1 || current == 0"
        >
          <MySvgIcon
            icon-class="icon-dengdai"
            style="font-size: 64px; fill: #0d55cf; margin-bottom: 12px"
          />
          <span class="pay-status">支付等待中...</span>
          <span class="pay-remark"
            >您的支付申请已提交,系统正在处理中,请耐心等待!</span
          >
          <a-button type="primary" ghost @click="handleGoIndex"
            >返回首页</a-button
          >
        </div>
        <div class="cloud-pay-type" v-if="cloudData.status == 3">
          <MySvgIcon
            icon-class="icon-chenggong"
            style="font-size: 64px; margin-bottom: 12px; fill: #00865a"
          />
          <div class="cloud-num">
            <span>￥</span>
            <span>{{ formatMoney(cloudData.amountDue || 0) }}</span>
          </div>
          <div class="cloud-success-status">支付成功</div>
          <div class="success-box">
            <div class="box-item">
              <span>支付账户</span>
              <span>中国人民银行福田支行 9481</span>
            </div>
            <div class="box-item">
              <span>订单号</span>
              <span>{{ cloudData.repaymentNumber }}</span>
            </div>
            <div class="box-item">
              <span>创建时间</span>
              <span>{{ cloudData.createTime }}</span>
            </div>
            <div class="box-item">
              <span>还款时间</span>
              <span>{{ cloudData.endDate }}</span>
            </div>
          </div>
          <a-button type="primary" ghost @click="handleGoIndex"
            >返回首页</a-button
          >
        </div>
        <div class="cloud-pay-type" v-if="cloudData.status == 4">
          <MySvgIcon
            icon-class="icon-delete-filling"
            style="font-size: 64px; fill: #dd2727; margin-bottom: 12px"
          />
          <span class="pay-status">支付失败</span>
          <span class="pay-remark">{{ cloudData.failReason }}</span>
          <a-button type="primary" ghost @click="handleGoIndex"
            >返回首页</a-button
          >
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'cloudLiabilitiesPay',
}
</script>
<script setup>
import ArticleSetps from '@/components/articleSetps/index.vue'
import { ref, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getCloudPayStatusDetail } from '@/api/user/cloud.js'
import { formatMoney } from '@/utils/utils'
const resultSteps = ref(true)
const cloudData = ref({})
const router = useRouter()
const route = useRoute()
const current = ref(null)
const time = ref(10)
const countdowns = ref(null)
const errType = ref(false)
const countdown = async () => {
  countdowns.value = setInterval(() => {
    if (time.value <= 0) {
      clearInterval(countdowns.value)
      errType.value = true
      current.value = 0
      return
    } else {
      getCloudPayStatus()
    }
    time.value--
  }, 1000)
}

countdown()

// 获取付款状态
const getCloudPayStatus = async () => {
  try {
    const { data } = await getCloudPayStatusDetail({ id: route.query.id })
    if (data.code === 200) {
      if (data.data.status != 1) {
        clearInterval(countdowns.value)
        errType.value = true
      }
      cloudData.value = { ...data.data }
    }
  } catch {
    clearInterval(countdowns.value)
  }
}

// 返回首页
const handleGoIndex = () => {
  router.push({ name: 'Home' })
}

onUnmounted(() => {
  clearInterval(countdowns.value)
})
</script>

<style lang="scss" scoped>
.cloud-wrapper {
  width: 100%;
  background: #fff;
  flex: 1;
  box-sizing: border-box;
  padding-top: 40px;
  width: 100%;
  min-height: 190px;

  .cloud-loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.cloud-container {
  max-width: 1400px;
  margin: 0 auto;
  box-sizing: border-box;
  .cloud-detail {
    padding: 44px 40px 32px;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    margin-top: 40px;
    background: #ffffff;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 1px solid #efefef;
    margin-bottom: 40px;
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      z-index: 0;
      width: 280px;
      height: 510px;
      top: -73px;
      right: -40px;
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.1;
      border-radius: 150px;
      filter: blur(49px);
    }
    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      z-index: 0;
      width: 106px;
      height: 264px;
      top: -20px;
      left: -22px;
      border-radius: 150px;
      filter: blur(24px);
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.1;
    }
  }
}

.contractLoad-box {
  width: 100vw;
  height: 100vh;
  background-color: rgba($color: #fff, $alpha: 0.8);
  z-index: 1000;
  position: fixed;
  top: 0;
  left: 0;

  .contractLoad-time {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;

    .product-open-radius-plan {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      transform: translateX(10px);

      .product-open-radius-plan-num {
        & > span:first-child {
          font-size: 64px;
          font-family: CoreSansD75Black;
          color: #031222;
          line-height: 79px;
          letter-spacing: 1px;
          font-weight: bold;
        }
        & > span:last-child {
          font-size: 32px;
          font-family: CoreSansD75Black;
          color: #031222;
          line-height: 79px;
          letter-spacing: 1px;
        }
      }

      .text-box {
        font-size: 16px;
        font-weight: 400;
        color: #8a94a6;
        line-height: 24px;
      }

      .text-err-box {
        font-size: 16px;
        font-weight: 400;
        color: #000;
        line-height: 24px;
      }
    }

    .err-product-open-radius-plan {
      transform: translate(10px, -10px);
    }
  }
}

.cloud-pay-type {
  width: 100vw;
  height: 100vh;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .pay-status {
    margin-bottom: 12px;
    font-size: 24px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #0a1f44;
    line-height: 32px;
  }
  .pay-remark {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
    margin-bottom: 48px;
  }
  .cloud-num {
    display: flex;
    align-items: center;

    font-family: CoreSansD65Heavy;
    color: #031222;
    line-height: 30px;
    margin-bottom: 12px;
    & span:first-child {
      font-size: 24px;
    }
    & span:last-child {
      font-size: 48px;
    }
  }
  .cloud-success-status {
    margin-bottom: 24px;
    font-size: 24px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #0a1f44;
    line-height: 32px;
  }
  .success-box {
    padding: 32px 60px;
    background-color: #f8f9fb;
    width: 400px;
    height: 240px;
    box-sizing: border-box;
    border-radius: 8px;
    margin-bottom: 32px;
    .box-item {
      display: flex;
      & span:first-child {
        display: block;
        width: 60px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #8a94a6;
        line-height: 20px;
        margin-right: 24px;
        text-align: right;
      }
      & span:last-child {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #0a1f44;
        line-height: 20px;
      }
    }
    .box-item + .box-item {
      margin-top: 32px;
    }
  }
  .ant-btn {
    width: 120px;
    height: 48px;
    border-radius: 100px;
    border: 1px solid #0c66ff;
    box-sizing: border-box;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #0d55cf;
  }
}
</style>
