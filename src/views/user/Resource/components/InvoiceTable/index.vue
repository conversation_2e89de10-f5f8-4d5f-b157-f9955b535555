<template>
  <div class="invoice-search">
    <div class="search-condition">
      <a-input
        class="search-item"
        v-model:value="searchParams.financeNoEqual"
        placeholder="输入融资编号"
      ></a-input>
      <a-range-picker
        class="search-item"
        v-model:value="searchParams.date"
        valueFormat="YYYY-MM-DD"
        :placeholder="['截止日期', '截止日期']"
      />
    </div>
    <div class="search-control">
      <a-button size="large" @click="handleSearch">搜索</a-button>
      <a-button size="large" @click="handleReset">重置</a-button>
    </div>
  </div>
  <a-table
    class="invoice-table"
    :columns="columns"
    :data-source="tableData"
    :tableLoading="tableLoading"
    :pagination="pagination"
    :scroll="{ x: 1200 }"
    @change="handleTableChange"
  >
    <template #headerCell="{ column, title }">
      <template v-if="column.key === 'costType'">
        <TableHeadFilter
          ref="number"
          :title="title"
          :options="goodsCostoptions"
          @filterChange="handleFilterChange('costType', $event)"
        >
        </TableHeadFilter>
      </template>
      <template v-else-if="column.key === 'paymentMethodName'">
        <TableHeadFilter
          ref="paymentMethodName"
          :title="title"
          :options="goodsBillPayOptions"
          @filterChange="handleFilterChange('paymentMethodName', $event)"
        >
        </TableHeadFilter>
      </template>
      <template v-else-if="column.key === 'invoiceStatusName'">
        <TableHeadFilter
          ref="invoiceStatusName"
          :title="title"
          :options="goodsBillInvoiceOptions"
          @filterChange="handleFilterChange('invoiceStatusName', $event)"
        >
        </TableHeadFilter>
      </template>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'financeNo'">
        <span class="single-line-text">{{ record.financeNo }}</span>
      </template>
      <template v-else-if="column.key === 'costType'">
        <div class="single-line">
          <span
            class="single-line-cost"
            v-for="(item, index) in record.costList"
            :key="index"
          >
            {{ item }}
          </span>
        </div>
      </template>
      <template v-else-if="column.key === 'amount'">
        <a-statistic :precision="2" :value="record.amount" />
      </template>
      <template v-else-if="column.key === 'payTime'">
        <span class="single-line-payTime">{{ record.payTime }}</span>
      </template>
      <template v-else-if="column.key === 'paymentMethodName'">
        <span class="single-line-payMent">{{ record.paymentMethodName }}</span>
      </template>
      <template v-else-if="column.key === 'invoiceStatusName'">
        <span
          ><a-badge
            :color="getBadgeColor(record.invoiceStatus)"
            :text="record.invoiceStatusName"
          ></a-badge
        ></span>
      </template>
      <template v-else-if="column.key === 'action'">
        <span class="btnName" @click="gotoInvoiceDetail(record)">详情</span>
        <span
          class="btnName"
          v-if="record.invoiceStatus === 0"
          @click="handleApplyInvoice(record)"
          >申请开票</span
        >
        <span
          class="btnName"
          v-else-if="record.invoiceStatus === 1"
          @click="handleCancelApply(record)"
          >取消申请</span
        >
        <span
          class="btnName"
          v-else-if="record.invoiceStatus === 2 || record.invoiceStatus === 3"
          @click="handleConfirmGetInvoice(record)"
          >确认收票</span
        >
        <span
          class="btnName"
          v-else-if="record.invoiceStatus === 5"
          @click="handleApplyInvoice(record)"
          >重新开票</span
        >
      </template>
    </template>
    <template #emptyText>
      <template v-if="pageInitLoading"></template>
      <template v-else>
        <div class="empty-container">
          <img src="@/assets/images/empty_2.svg" alt="" />
          <span class="desc">暂无数据</span>
        </div>
      </template>
    </template>
  </a-table>

  <!-- 申请开票 -->
  <ApplyInvoiceDialog
    ref="applyDialog"
    :financeNo="rowItem.financeNo"
    :billExpenseNo="rowItem.billExpenseNo"
    @refresh="initGetTableData"
  >
  </ApplyInvoiceDialog>

  <ConfirmDialog
    ref="confirmDialog"
    :title="titleList.title"
    cancelBtnName="取消"
    :confirmBtnName="titleList.btnName"
    @cancel="handleExit"
    @confirm="handleToVerified"
  />
</template>

<script>
import { h } from 'vue'
import { NButton } from 'naive-ui'
import MySvgIcon from '@/components/MySvgIcon/index.vue'
import ApplyInvoiceDialog from '../ApplyInvoiceDialog/index.vue'
import { formatMoney } from '@/utils/utils'
import { USER_CENTER } from '@/api/index'
import { setSession } from '@/utils/sessionStorage'
export default {
  name: 'InvoiceManage',
}

const columns = [
  {
    title: '费用编号',
    dataIndex: 'billExpenseNo',
    width: 170,
  },
  {
    title: '融资编号',
    key: 'financeNo',
    width: 170,
  },
  {
    title: '费用类型',
    key: 'costType',
    width: 180,
  },
  {
    title: '付款金额(元)',
    key: 'amount',
    width: 140,
  },
  {
    title: '付款时间',
    key: 'payTime',
    width: 193,
  },
  {
    title: '支付方式',
    key: 'paymentMethodName',
    width: 120,
  },
  {
    title: '状态',
    key: 'invoiceStatusName',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 128,
    fixed: 'right',
  },
]

const customPaginationRender = ({
  originalElement,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  page,
  type,
}) => {
  if (type === 'prev') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-left custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h(MySvgIcon, { 'icon-class': 'icon-jiantou-zuo', 'font-size': '16px' }),
        h('span', {}, '上一页'),
      ]
    )
  } else if (type === 'next') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-right custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h('span', {}, '下一页'),
        h(MySvgIcon, { 'icon-class': 'icon-youjiantou1', 'font-size': '16px' }),
      ]
    )
  }
  return originalElement
}

//分页
const initPagination = () => ({
  pageSize: 10,
  currentPage: 1,
  maxPage: 1,
  total: 0,
})
</script>

<script setup>
import { ref, reactive, computed } from 'vue'
import TableHeadFilter from '@/components/TableHeadFilter/index.vue'
import { useRouter } from 'vue-router'
import { INVOICE_MANAGE } from '@/api/index'
import { requestDictMap } from '@/api/common/index'
import ConfirmDialog from '@/businessComponents/ConfirmDialog/index.vue'
import { message } from 'ant-design-vue'

// 查询参数
const searchParams = reactive({
  date: [],
  financeNoEqual: '',
})
const confirmDialog = ref(null)

const pageInitLoading = ref(true) // 初始化加载
const tableLoading = ref(false) // 表格加载
const filterData = {
  // 表头可配置的复选参数数据
  costType: [],
  paymentMethodName: [],
  invoiceStatusName: [],
}
const router = useRouter()
const contorlBtnStatus = ref(4) // 4 确认收票 5.取消申请
const applyDialog = ref(null) // 申请弹窗事件
const rowItem = ref({
  financeNo: '',
  billExpenseNo: '',
})

const tableData = ref([])
const goodsCostoptions = ref([]) //费用类型
const goodsBillPayOptions = ref([]) // 付款方式
const goodsBillInvoiceOptions = ref([]) // 开票状态
const addressDetailList = ref({})
const titleList = ref({}) // 取消按钮标题

const paginationData = ref(initPagination())

const pagination = computed(() => ({
  total: paginationData.value.total,
  current: paginationData.value.currentPage,
  pageSize: paginationData.value.pageSize,
  showSizeChanger: false,
  itemRender: customPaginationRender,
}))

// 当页码发生变化触发事件
const handleTableChange = pagination => {
  if (!pagination.current) return
  paginationData.value.currentPage = pagination.current
  paginationData.value.pageSize = pagination.pageSize
  updateList({ current: pagination.current, size: pagination.pageSize })
}
// 表头复选框改变触发事件
const handleFilterChange = (target, targetFilterData) => {
  filterData[target] = targetFilterData
  paginationData.value = initPagination()
  initGetTableData()
}

const handleExit = () => {}
// 发票状态
const getBadgeColor = state => {
  let stateColor = ''
  switch (state) {
    case 0: // 可开票
      stateColor = '#0D55CF'
      break
    case 1: // 已申请
      stateColor = '#0D55CF'
      break
    case 2: // 已寄出
      stateColor = '#0D55CF'
      break
    case 3: // 待收票
      stateColor = '#0D55CF'
      break
    case 4: // 已收票
      stateColor = '#0BB07B'
      break
    case 5: // 已取消
      stateColor = '#B5BBC6'
      break
    case 6: // 已收票（自动收票的时候是该值）
      stateColor = '#0BB07B'
      break
  }
  return stateColor
}

// 跳转详情页
const gotoInvoiceDetail = ({ id, financeNo, invoiceStatus, billExpenseNo }) => {
  router.push({
    name: 'InvoiceDetail',
    query: { id, financeNo, invoiceStatus, billExpenseNo },
  })
}

USER_CENTER.getBasicAddress().then(({ data }) => {
  if (data.code === 200) {
    addressDetailList.value = {
      addressTarget: data.data.addressTarget,
      receiveAddress: `${data.data.urbanAreas}${data.data.addressDetailed}`,
      receiveName: data.data.contacts,
      receiveNumber: data.data.addressPhone,
    }
  }
  setSession('addressDetailList', addressDetailList.value)
})

// 申请开票操作
const handleApplyInvoice = row => {
  rowItem.value = {
    ...row,
    financeNo: row.financeNo || '',
    billExpenseNo: row.billExpenseNo || '',
  }
  applyDialog.value.handleOpen()
}

// 取消申请开票
const handleCancelApply = row => {
  handleConfirmOpen()
  titleList.value = {
    title: '是否取消申请开票？',
    btnName: '取消申请',
  }
  rowItem.value = { ...row }
  contorlBtnStatus.value = 5
}
// 确认收票
const handleConfirmGetInvoice = row => {
  handleConfirmOpen()
  titleList.value = {
    title: '是否确认收票？',
    btnName: '确认收票',
  }
  rowItem.value = { ...row }
  contorlBtnStatus.value = 4
}

const handleToVerified = () => {
  //  4 确认收票 5.取消申请
  let invoiceStatus = null
  switch (contorlBtnStatus.value) {
    case 4:
      invoiceStatus = 4
      break
    case 5:
      invoiceStatus = 5
      break
  }
  const params = {
    invoiceStatus,
    id: rowItem.value.id,
  }
  INVOICE_MANAGE.updateInvoiceStatus(params).then(({ data }) => {
    if (data.code === 200) {
      message.success('操作成功！')
      initGetTableData()
    }
  })
}

const handleConfirmOpen = () => {
  confirmDialog.value.handleOpen()
}

const initGetTableData = () => {
  tableLoading.value = true
  updateList({
    current: paginationData.value.currentPage,
    size: paginationData.value.pageSize,
  })
}
// 搜索条件
const handleSearch = () => {
  initGetTableData()
}
// 重置
const handleReset = () => {
  for (const key in searchParams) {
    if (typeof searchParams[key] === 'object') {
      searchParams[key] = []
    } else {
      searchParams[key] = ''
    }
  }
  paginationData.value = initPagination()
  initGetTableData()
}
// table数据调取操作
const updateList = ({ current, size }) => {
  let params = {
    current,
    size: size || 10,
    // eslint-disable-next-line camelcase
    createTimeDateGe: searchParams.date ? searchParams.date[0] : '',
    // eslint-disable-next-line camelcase
    createTimeDateLe: searchParams.date ? searchParams.date[1] : '',
    financeNoEqual: searchParams.financeNoEqual,
  }
  if (filterData.costType?.length) {
    params.platName = filterData.costType.join()
  }
  if (filterData.paymentMethodName?.length) {
    params.paymentMethodIn = filterData.paymentMethodName.join()
  }
  if (filterData.invoiceStatusName?.length) {
    params.invoiceStatusIn = filterData.invoiceStatusName.join()
  }
  // 清除查询条件空值的字段
  for (const key in params) {
    if (!params[key]) {
      delete params[key]
    }
  }
  INVOICE_MANAGE.getInvoiceList(params)
    .then(res => {
      const resData = res.data
      if (resData.code === 200) {
        const { data } = resData
        let _list = []
        data.records.forEach(item => {
          item.costList = item.platName ? item.platName.split(',') : []
          item.amount = item.amount ? `￥${formatMoney(item.amount)}` : 0
          item.paymentMethodName = item.paymentMethodName
            ? `${item.paymentMethodName}支付`
            : ''
          _list.push(item)
        })
        paginationData.value.currentPage = data.current
        paginationData.value.pageSize = data.size
        paginationData.value.total = data.total
        tableData.value = [..._list]
      }
    })
    .finally(() => {
      tableLoading.value = false
      pageInitLoading.value = false
    })
}

initGetTableData()

/***************关于数据字典取值*****************/

// 支付方式以及发票状态
const getDictionary = (params, dataS) => {
  requestDictMap(params).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      // 处理字典数据
      const resList = []
      for (const item of resData.data) {
        resList.push({
          value:
            params === 'goods_expense_rule_type'
              ? item.dictValue
              : item.dictKey,
          label:
            params === 'bill_pay' ? `${item.dictValue}支付` : item.dictValue,
          id: item.id,
        })
      }
      dataS.value =
        params === 'bill_invoice_status'
          ? resList.filter(item => !item.label.includes('超过时间'))
          : resList
    }
  })
}
getDictionary('bill_pay', goodsBillPayOptions) // 获取支付方式字典
getDictionary('bill_invoice_status', goodsBillInvoiceOptions) // 获取开票状态字典
getDictionary('goods_expense_rule_type', goodsCostoptions) // 获取费用类型
</script>

<style lang="scss" scoped>
.invoice-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .search-condition {
    .search-item {
      width: 260px;
      height: 40px;
      &:not(:last-child) {
        margin-right: 20px;
      }
    }
  }
  .search-control {
    & > .ant-btn {
      font-weight: 500;
      font-size: 14px;
      color: #0a1f44;
    }
    & > .ant-btn:first-child {
      margin-right: 20px;
    }
  }
}

.invoice-table {
  margin-top: 20px;
  // 表格
  :deep(.ant-table) {
    // 去除表头分隔线
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      content: none;
    }

    .ant-table-thead {
      th {
        padding: 0 12px;
        height: 40px;
        line-height: 40px;
        background-color: #f8f9fb;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #8a94a6;
      }
    }

    .ant-table-tbody {
      // 鼠标停留背景色
      tr.ant-table-row:hover > td {
        background: #f5faff;
      }

      // 清除无数据时的边框
      .ant-table-placeholder > td {
        border: none;
      }

      td {
        padding: 6px 12px;
        height: 48px;
        line-height: 1.24;
        font-size: 14px;
        font-family: SFProText-Medium, SFProText;
        font-weight: 500;
        color: #0a1f44;

        .ant-statistic-content {
          line-height: 1.24;
          font-size: 14px;
          font-family: SFProText-Medium, SFProText;
          font-weight: 500;
          color: #0a1f44;

          .ant-statistic-content-prefix {
            margin-right: 0px;
          }
        }
      }
    }
  }

  // 表格自定义列
  .action-column-container {
    > span {
      margin-right: 8px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #0d55cf;
      line-height: 20px;
      word-break: keep-all;
      cursor: pointer;

      a {
        color: #0d55cf;
      }

      &:hover {
        text-decoration: underline;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
  :deep(.ant-badge-status-dot) {
    width: 8px;
    height: 8px;
  }
  :deep(.ant-badge-status-text) {
    font-size: 14px;
    font-weight: 500;
    color: #8a94a6;
    margin-left: 6px;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .single-line-text {
    color: #0d55cf;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    font-weight: 500;
    font-family: SFProText-Medium, SFProText;
  }
  .single-line {
    display: flex;
    flex-flow: row nowrap;
    .single-line-cost {
      word-break: keep-all; /* 不换行 */
      white-space: nowrap; /* 不换行 */
      overflow: hidden;
      display: block;
      padding: 6px 10px;
      line-height: 16px;
      text-align: center;
      color: #0a1f44;
      background-color: #f1f2f4;
      border-radius: 100px;
      font-size: 12px;
      font-weight: 500;
      font-family: PingFangSC-Medium, PingFang SC;
      &:not(:last-child) {
        margin-right: 4px;
      }
    }
  }

  .single-line-payCount,
  .single-line-payTime,
  .single-line-payMent {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    font-weight: 500;
  }
  .single-line-payCount {
    color: #0a1f44;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .single-line-payTime {
    color: #0a1f44;
    font-family: SFProText-Medium, SFProText;
  }
  .single-line-payMent {
    color: #53627c;
  }
  .btnName {
    cursor: pointer;
    height: 20px;
    line-height: 20px;
    color: #0d55cf;
    font-size: 14px;
    font-weight: 500;
    font-family: PingFangSC-Medium, PingFang SC;
    &:not(:last-child) {
      margin-right: 6px;
    }
  }
  // 分页
  :deep(.ant-pagination) {
    display: flex;
    align-items: center;
    // 去除分页下外边距
    margin-bottom: 0;
    .ant-pagination-disabled {
      .custom-pagination-item-link {
        color: #a6aebc !important;
        border: 1px solid transparent;
      }
    }

    .custom-pagination-item-link {
      background: #f8f9fb;
      color: #53627c;
    }

    .custom-pagination-left {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px 0 12px;
      border-radius: 100px 0px 0px 100px;
    }

    .custom-pagination-right {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px 0 16px;
      border-radius: 0px 100px 100px 0px;
    }
  }

  .empty-container {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 40px auto;
    text-align: center;

    img {
      width: 200px;
      height: 200px;
    }

    .desc {
      font-size: 14px;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }
  }
}
</style>
