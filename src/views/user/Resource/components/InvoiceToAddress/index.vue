<template>
  <GlobalDialog
    title="选择地址"
    width="432px"
    ref="addressRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="invoice-address-manage-list" v-if="addressList.length > 0">
      <CheckCard
        v-for="(item, index) in addressList"
        :key="item.id"
        :active="activeIndex === index"
        @change="handleChangeIndex(index)"
      >
        <div class="invoice-address-manage-main">
          <div class="invoice-address-manage-main-title">
            <span class="invoice-address-default" v-if="item.type === 1"
              >默认</span
            >
            <span class="invoice-address-name">{{ item.addressTarget }}</span>
          </div>
          <div class="invoice-address-manage-main-subtitle">
            {{ item.address }}
          </div>
          <div class="invoice-address-manage-main-user">
            <span>{{ item.contacts }}</span>
            <span>{{ item.addressPhone }}</span>
          </div>
        </div>
      </CheckCard>
    </div>
    <div class="invoice-address-manage-list-empty" v-else>
      <div class="invoice-address-empty-img">
        <img src="@/assets/images/empty_3.svg" />
      </div>
      <div></div>
    </div>
    <template #button>
      <div class="invoice-address-btn" v-if="addressList.length > 0">
        <div class="invoice-address-btn-left">
          <a-button type="primary" @click="handleGoCreateAddress"
            >添加地址</a-button
          >
        </div>
        <div class="invoice-address-btn-right">
          <a-button size="large" @click="handleCloseDialog">取 消</a-button>
          <a-button size="large" type="primary" @click="handleSubmit"
            >确 定</a-button
          >
        </div>
      </div>
      <div v-else class="invoice-address-empty-btn">
        <a-button type="primary" @click="handleGoCreateAddress"
          >添加地址</a-button
        >
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import CheckCard from '@/components/BaseCard/checkCard/index.vue'
export default {
  name: 'InvoiceToAddress',
}
</script>

<script setup>
import { ref } from 'vue'
import { USER_CENTER } from '@/api/index'
import { useRouter, useRoute } from 'vue-router'

const addressRef = ref(null) // 调用子组件的弹窗方法
const activeIndex = ref(0)
const addressList = ref([]) // 地址信息
const router = useRouter()
const route = useRoute()
const emit = defineEmits(['select'])

const handleOpen = () => {
  // 打开弹窗
  addressRef.value.handleOpen()
  USER_CENTER.getAddressList().then(res => {
    const resData = res.data
    if (resData.code === 200) {
      const { data } = resData
      const _list = []
      data.forEach(item => {
        item.address = `${item.urbanAreas}${item.addressDetailed}`
        _list.push(item)
      })
      addressList.value = [..._list]
    }
  })
}

// 跳转路由页面
// UserResourceIndex
const handleGoCreateAddress = () => {
  const routePath = route.path
  if (routePath === '/user/resource') {
    router.go(0)
  } else {
    router.replace('/user/resource')
  }
}
const handleCloseDialog = () => {
  // 关闭弹窗
  addressRef.value.handleClose()
}
// 点击切换
const handleChangeIndex = index => {
  activeIndex.value = index // 选中地址的值
}

// 选择地址
const handleSubmit = () => {
  if (!addressList.value.length) return
  handleCloseDialog()
  const rowItem = { ...addressList.value[activeIndex.value] }
  emit('select', rowItem)
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.invoice-address-manage-list {
  :deep(.base-card-check-item) {
    &:not(:last-child) {
      margin-bottom: 6px;
    }
  }
  .invoice-address-manage-main {
    .invoice-address-manage-main-title {
      display: flex;
      align-items: center;
      & span {
        display: block;
      }
      .invoice-address-default {
        width: 44px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 100px;
        color: #0d55cf;
        font-weight: bold;
        font-size: 11px;
        font-family: SFProText-Semibold, SFProText;
        background-color: #ebf5ff;
        margin-right: 8px;
      }
      .invoice-address-name {
        flex-grow: 1;
        width: 100%;
        height: 24px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #0a1f44;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1; /* 限制在一个块元素显示的文本的行数 */
        -webkit-box-orient: vertical; /* 垂直排列 */
        word-break: break-all; /* 内容自动换行 */
      }
    }
    .invoice-address-manage-main-subtitle {
      margin: 4px 0 8px;
      width: 300px;
      height: 32px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8a94a6;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1; /* 限制在一个块元素显示的文本的行数 */
      -webkit-box-orient: vertical; /* 垂直排列 */
      word-break: break-all; /* 内容自动换行 */
    }
    .invoice-address-manage-main-user {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #53627c;
      line-height: 20px;
      & span:first-child {
        padding-right: 12px;
      }
    }
  }
}

.invoice-address-manage-list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  .invoice-address-empty-img {
    margin-top: 24px;
    margin-bottom: 12px;
    width: 140px;
    height: 140px;
    & > img {
      width: 100%;
      object-fit: cover;
    }
  }
}
.invoice-address-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.ant-btn) {
    border-radius: 100px;
  }
  .invoice-address-btn-left {
    :deep(.ant-btn) {
      height: 40px;
      border-radius: 100px;
      background-color: #0c66ff;
    }
  }
  .invoice-address-btn-right {
    :deep(.ant-btn) {
      height: 40px;
      border-radius: 100px;
      &:first-child {
        margin-right: 8px;
      }
      &:last-child {
        background-color: #0c66ff;
      }
    }
  }
}

.invoice-address-empty-btn {
  width: 100%;
  text-align: center;
  :deep(.ant-btn) {
    width: 124px;
    height: 40px;
    border-radius: 100px;
  }
}
</style>
