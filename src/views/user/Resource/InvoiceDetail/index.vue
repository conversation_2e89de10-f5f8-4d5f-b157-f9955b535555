<template>
  <div class="invoice-detail-container">
    <BaseDetailHeadCard
      :iconStyle="getInvoiceIconClass(costList.invoiceStatus)"
      :iconColor="getTitleStyle(costList.invoiceStatus)"
      :iconTitleColor="getTitleStyle(costList.invoiceStatus)"
      :title-name="costList.invoiceStatusName"
    >
      <template #subtitle>
        <div class="card-subtitle" v-if="costList.invoiceStatus == 0">
          可申请开具纸质发票
        </div>
        <div class="card-subtitle" v-else-if="costList.invoiceStatus == 1">
          开票申请已提交,工作人员正在处理中,请耐心等待~
        </div>
        <div class="card-subtitle" v-else-if="costList.invoiceStatus == 2">
          发票已寄出,请保持联系通畅~
        </div>
        <div class="card-subtitle" v-else-if="costList.invoiceStatus == 3">
          发票已达目的地, 请在
          <span class="invoice-received-time">{{ difDay }}</span>
          内确认,到期将自动确认~
        </div>
        <div class="card-subtitle" v-else-if="costList.invoiceStatus == 4">
          于{{ costList.updateTime }}确认收票
        </div>
        <div class="card-subtitle" v-else-if="costList.invoiceStatus == 5">
          发票申请已取消，如需发票可重新申请开票
        </div>
        <div class="card-subtitle" v-else-if="costList.invoiceStatus == 6">
          于{{ costList.updateTime }}超时后自动确认
        </div>
      </template>
      <template #btnName>
        <span
          style="color: #0d55cf"
          v-if="costList.invoiceStatus == 0"
          @click="handleApplyInvoice"
          >申请开票</span
        >
        <span
          style="color: #0d55cf"
          v-else-if="costList.invoiceStatus == 1"
          @click="handleCancelApply"
        >
          取消开票
        </span>
        <span
          style="color: #0d55cf"
          v-else-if="costList.invoiceStatus == 2 || costList.invoiceStatus == 3"
          @click="handleConfirmGetInvoice"
        >
          确认收票
        </span>
        <span
          style="color: #0d55cf"
          v-else-if="costList.invoiceStatus == 5"
          @click="handleApplyInvoice"
          >重新开票</span
        >
        <span
          style="color: #0d55cf; margin-left: 20px"
          @click="handleSeeDetail"
          v-if="isShowDetailDialog"
          >查看详情</span
        >
      </template>
    </BaseDetailHeadCard>
    <div class="invoice-detail-main">
      <div class="invoice-detail-left-mask"></div>
      <div class="invoice-detail-right-mask"></div>

      <div class="invoice-detail-main-box">
        <div class="invoice-detail-main-title">支付信息</div>
        <BaseTableDetailTotal :tableData="tableData" :columns="columns">
        </BaseTableDetailTotal>

        <div class="invoice-detail-introduce">
          <div class="invoice-detail-item">
            <span>融资编号</span>
            <span class="invoice-detail-number">{{ costList.financeNo }}</span>
          </div>
          <div class="invoice-detail-item">
            <span>付款金额</span>
            <span class="invoice-detail-price">{{ costList.amount }}</span>
          </div>
          <div class="invoice-detail-item">
            <span>支付时间</span>
            <span class="invoice-detail-pay-time">{{ costList.payTime }}</span>
          </div>
          <div class="invoice-detail-item">
            <span>支付方式</span>
            <span
              class="invoice-detail-offline-pay"
              v-if="costList.paymentMethodName"
              >{{ costList.paymentMethodName }}支付</span
            >
          </div>
          <div
            v-if="[1, 2].includes(costList.paymentMethod)"
            class="invoice-detail-item"
          >
            <span>支付凭证</span>
            <span
              class="invoice-detail-see-voucher"
              @click="handleViewImg(costList.imgUrl)"
              >查看凭证</span
            >
            <span
              class="invoice-detail-see-voucher"
              v-for="(item, index) in costList.pdfUrl"
              :key="item"
              @click="handleViewPdf(item)"
              >查看附件{{ index + 1 }}</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 申请开票 -->
  <ApplyInvoiceDialog
    ref="applyDialog"
    :financeNo="costList.financeNo"
    :billExpenseNo="costList.billExpenseNo"
    @refresh="handleRefresh"
  >
  </ApplyInvoiceDialog>

  <InvoiceDetailDialog
    ref="invoiceRef"
    :status="costList.invoiceStatus"
  ></InvoiceDetailDialog>
  <ConfirmDialog
    ref="confirmDialog"
    :title="titleList.title"
    cancelBtnName="取消"
    :confirmBtnName="titleList.btnName"
    @cancel="handleExit"
    @confirm="handleToVerified"
  />
  <PdfView ref="pdfView" />
</template>

<script>
const columns = [
  {
    title: '费用名称',
    dataIndex: 'name',
  },
  {
    title: '支付节点',
    dataIndex: 'computeName',
  },
  {
    title: '计费方式',
    dataIndex: 'feeFormulaName',
  },
  {
    title: '应付金额',
    dataIndex: 'amount',
  },
]

export default {
  name: 'InvoiceDetail',
}
</script>

<script setup>
import BaseDetailHeadCard from '@/components/BaseDetailHeadCard/index.vue'
import BaseTableDetailTotal from '@/components/BaseTableDetailTotal/index.vue'
import InvoiceDetailDialog from '../components/InvoiceDetailDialog/index.vue'
import { INVOICE_MANAGE } from '@/api/index'
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { formatMoney, getCountDownHtml } from '@/utils/utils'
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import PdfView from '@/components/FilePreview/index.vue'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
import ApplyInvoiceDialog from '../components/ApplyInvoiceDialog/index.vue'
import ConfirmDialog from '@/businessComponents/ConfirmDialog/index.vue'

const costList = ref({
  financeNo: '',
}) // 详情数据
const route = useRoute() // 获取路由参数
const tableData = ref([]) // 支付信息内容
const pdfView = ref(null)
const difDay = ref(null) // 天数展示
const applyDialog = ref(null) // 控制申请开票
const confirmDialog = ref(null) // 二次确认操作弹窗
const titleList = ref({}) // 二次操作弹窗内容
const invoiceStatus = ref(route.query.invoiceStatus)

// 如果是代收票状态才会有定时器
onMounted(async () => {
  await getInvoiceOrderDetail()
})
// 刷新
const handleRefresh = async () => {
  await getInvoiceOrderDetail()
}
// 定时器事件
const startInterval = () => {
  const updateTime = costList.value.updateTime // 开票时间
  const day = costList.value.day // 倒计时天数

  const countDownInterval = setInterval(() => {
    if (!updateTime || !day) {
      clearInterval(countDownInterval)
    } else {
      const nowTime = dayjs().valueOf()
      const dayTime = day * 24 * 60 * 60 * 1000
      const updateTimeDate = dayjs(updateTime).valueOf()
      if (nowTime - updateTimeDate > dayTime || updateTimeDate > nowTime) {
        clearInterval(countDownInterval)
      } else {
        difDay.value = getCountDownHtml(dayTime + updateTimeDate - nowTime)
      }
    }
  })
}
// 查看图片
const handleViewImg = imgUrl => {
  if (!imgUrl.length) return false
  viewerApi({
    options: {
      toolbar: false,
      // navbar: false,
      // title: false,
    },
    images: imgUrl,
  })
}
// 查看pdf文件
const handleViewPdf = pdfUrl => {
  pdfView.value.handleOpen(pdfUrl)
}

// 数据源处理
const getInvoiceOrderDetail = async () => {
  const { data } = await INVOICE_MANAGE.getInvoiceOrderDetail({
    id: route.query.id,
  })
  if (data.code === 200) {
    let imgUrl = [],
      pdfUrl = []
    // 把pdf文件和图片区分开
    if (data.data.attachList?.length) {
      data.data.attachList.forEach(item => {
        if (item.link.includes('pdf')) {
          pdfUrl.push(item.link)
        } else {
          imgUrl.push(item.link)
        }
      })
    }
    costList.value = {
      ...data.data,
      financeNo: data.data.financeNo || '',
      invoiceStatus: data.data.invoiceStatus || '',
      amount: `￥${formatMoney(data.data.amount)}`,
      imgUrl,
      pdfUrl,
    }

    if (data.data.invoiceStatus === 3) {
      // 待收票时，启动定时器
      startInterval()
    }

    const _list = []
    let total = data.data.expenseOrderDetailList
      .reduce((a, b) => a + Number(b.amount), 0)
      .toFixed(2)
    if (data.data.expenseOrderDetailList?.length) {
      data.data.expenseOrderDetailList.forEach(item => {
        item.amount = `￥${formatMoney(item.amount)}`
        _list.push(item)
      })

      // 生成总计一栏
      _list.push({
        name: '总计:',
        computeName: '',
        chargeMethod: '',
        amount: `￥${formatMoney(total)}`,
      })
    }
    tableData.value = [..._list]
  }
}

const handleApplyInvoice = () => {
  applyDialog.value.handleOpen()
}

// 取消开票
const handleCancelApply = () => {
  titleList.value = {
    title: '是否取消申请开票？',
    btnName: '取消申请',
    state: 5,
  }
  confirmDialog.value.handleOpen()
}
// 确认收票
const handleConfirmGetInvoice = row => {
  titleList.value = {
    title: '是否确认收票？',
    btnName: '确认收票',
    state: 4,
  }
  confirmDialog.value.handleOpen()
}

// 计算开票状态(可开票和已取消状态不存在查看详情)
const isShowDetailDialog = computed(() =>
  [1, 2, 3, 4, 6].includes(Number(invoiceStatus.value))
)

// 根据发票的类型对应其相关的icon
const getInvoiceIconClass = state => {
  let iconClass = 'icon-chenggong1'
  switch (state) {
    case 0: // 可开票
      iconClass = 'icon-dengdai1'
      break
    case 5: // 已取消
      iconClass = 'icon-tixing1'
      break
  }
  return iconClass
}

// 根据发票状态对应的发票类型和icon颜色
const getTitleStyle = state => {
  let iconStyle = '#0d55cf'
  switch (state) {
    case 1:
      break
    case 4: // 已收票
      iconStyle = '#00865a'
      break
    case 5: // 已取消
      iconStyle = '#8a94a6'
      break
    case 6: // 已收票(自动收票)
      iconStyle = '#00865a'
      break
  }

  return iconStyle
}

// 取消二次弹窗
const handleExit = () => {}

const handleToVerified = () => {
  let invoiceStatus = null
  switch (titleList.value.state) {
    case 4:
      invoiceStatus = 4
      break
    case 5:
      invoiceStatus = 5
      break
  }
  const params = {
    invoiceStatus,
    id: route.query.id,
  }
  INVOICE_MANAGE.updateInvoiceStatus(params).then(({ data }) => {
    if (data.code === 200) {
      message.success('操作成功！')
      handleRefresh()
    }
  })
}
// 查看详情事件
const invoiceRef = ref(null)
const handleSeeDetail = () => {
  invoiceRef.value.handleOpen()
}
</script>

<style lang="scss" scoped>
.invoice-detail-container {
  width: 1400px;
  margin: 40px auto 0;
  box-sizing: border-box;
  .invoice-received-time {
    color: #ff5d5d;
    font-size: 14px;
  }
  .invoice-detail-main {
    margin: 40px 0 40px;
    padding: 40px;
    width: 100%;
    overflow: hidden;
    position: relative;
    background: #ffffff;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 1px solid #efefef;
    .invoice-detail-left-mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 106px;
      height: 187px;
      background: #0c66ff 100%;
      opacity: 0.1;
      filter: blur(24px);
    }
    .invoice-detail-right-mask {
      position: absolute;
      right: 0;
      top: 0;
      width: 280px;
      height: 361px;
      background: #0c66ff 100%;
      opacity: 0.1;
      filter: blur(49px);
    }
    .invoice-detail-main-box {
      position: relative;
      background-color: rgba(255, 255, 255, 0.1);
      .invoice-detail-main-title {
        color: #0a1f44;
        height: 28px;
        line-height: 28px;
        font-size: 20px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: bold;
        margin-bottom: 24px;
      }
      .invoice-detail-introduce {
        margin-top: 24px;
        .invoice-detail-item {
          display: flex;
          align-items: center;
          & span {
            display: block;
          }
          & span:first-child {
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
            margin-right: 24px;
          }
          & span:last-child {
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            font-family: SFProText-Medium, SFProText;
          }
          &:not(:last-child) {
            margin-bottom: 24px;
          }
          .invoice-detail-number {
            font-weight: 500;
            color: #0d55cf;
          }
          .invoice-detail-price {
            font-weight: 400;
            color: #dd2727;
          }
          .invoice-detail-pay-time {
            font-weight: 400;
            color: #0a1f44;
          }
          .invoice-detail-offline-pay {
            font-weight: 400;
            color: #0a1f44;
          }
          .invoice-detail-see-voucher {
            cursor: pointer;
            font-weight: 400;
            color: #0d55cf;
          }
          .invoice-detail-see-voucher + .invoice-detail-see-voucher {
            margin-left: 24px;
          }
        }
      }
    }
  }
}
</style>
