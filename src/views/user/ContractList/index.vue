<template>
  <div class="contract-list-container">
    <div class="header-box">
      <UserHeader headerName="合同列表" iconClass="icon-wenjian" />
    </div>
    <div class="table-container">
      <ContractListTable ref="ContractTableRef" />
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'UserContractList',
}
</script>
<script lang="ts" setup>
import UserHeader from '@/views/user/components/Header/index.vue'
import ContractListTable from './components/table.vue'
</script>

<style lang="scss" scoped>
.contract-list-container {
  padding: 24px;

  // .header-box {
  // }

  // .content-box {
  // }
}
</style>
