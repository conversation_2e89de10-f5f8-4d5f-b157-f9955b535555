<template>
  <div class="empty-data-container">
    <div class="content">
      <img
        v-if="imgSrc !== ''"
        :src="imgSrc"
        alt=""
        style="width: 200px; height: 200px"
      />
      <svg
        v-else
        width="200px"
        height="200px"
        viewBox="0 0 200 200"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <title>切片</title>
        <defs>
          <linearGradient
            x1="50.00001%"
            y1="-0.000369550686%"
            x2="50.00001%"
            y2="100.000698%"
            id="linearGradient-1"
          >
            <stop stop-color="#E6EEFF" offset="0%"></stop>
            <stop
              stop-color="#F6F6F6"
              stop-opacity="0"
              offset="99.9999904%"
            ></stop>
          </linearGradient>
          <linearGradient
            x1="49.990907%"
            y1="77.3385076%"
            x2="49.990907%"
            y2="21.0747498%"
            id="linearGradient-2"
          >
            <stop stop-color="#E0EBF3" offset="0%"></stop>
            <stop stop-color="#ECF5FB" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.825%"
            y1="0.00291120815%"
            x2="49.825%"
            y2="81.799285%"
            id="linearGradient-3"
          >
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.990907%"
            y1="77.3385076%"
            x2="49.990907%"
            y2="21.0747498%"
            id="linearGradient-4"
          >
            <stop stop-color="#DFE4F6" offset="0%"></stop>
            <stop stop-color="#ECF5FB" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.825%"
            y1="0.00291120815%"
            x2="49.825%"
            y2="81.799285%"
            id="linearGradient-5"
          >
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="74.9567591%"
            y1="6.68877698%"
            x2="25.0560196%"
            y2="93.3018705%"
            id="linearGradient-6"
          >
            <stop stop-color="#FFDB80" offset="0%"></stop>
            <stop stop-color="#FFBB24" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.9995325%"
            y1="-0.00887573964%"
            x2="49.9995325%"
            y2="99.9805082%"
            id="linearGradient-7"
          >
            <stop stop-color="#F6FAFF" offset="0%"></stop>
            <stop stop-color="#DFE5F6" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.9849029%"
            y1="77.3427285%"
            x2="49.9849029%"
            y2="21.0661457%"
            id="linearGradient-8"
          >
            <stop stop-color="#E8F1FF" offset="0%"></stop>
            <stop stop-color="#ECF4FB" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.825%"
            y1="0.00291120815%"
            x2="49.825%"
            y2="81.799285%"
            id="linearGradient-9"
          >
            <stop stop-color="#ECF1FB" offset="0%"></stop>
            <stop stop-color="#D5E2F3" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="75.5325981%"
            y1="84.3680777%"
            x2="21.9884086%"
            y2="11.2024285%"
            id="linearGradient-10"
          >
            <stop stop-color="#D5E0F5" offset="0%"></stop>
            <stop stop-color="#EAF9FF" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="68.7090003%"
            y1="90.3918789%"
            x2="23.2738179%"
            y2="-7.70278298%"
            id="linearGradient-11"
          >
            <stop stop-color="#E3E5FF" offset="1.90412615%"></stop>
            <stop stop-color="#FFFFFF" offset="97.7387115%"></stop>
          </linearGradient>
          <linearGradient
            x1="75.9253011%"
            y1="90.3918789%"
            x2="12.9651874%"
            y2="-7.70278298%"
            id="linearGradient-12"
          >
            <stop stop-color="#E3E5FF" offset="1.90412615%"></stop>
            <stop stop-color="#FFFFFF" offset="97.7387115%"></stop>
          </linearGradient>
          <linearGradient
            x1="76.6817244%"
            y1="50.8189478%"
            x2="11.8846216%"
            y2="48.8300726%"
            id="linearGradient-13"
          >
            <stop stop-color="#E3E5FF" offset="1.90412615%"></stop>
            <stop stop-color="#FFFFFF" offset="97.7387115%"></stop>
          </linearGradient>
          <linearGradient
            x1="85.2589552%"
            y1="61.1386017%"
            x2="11.3177068%"
            y2="37.4258113%"
            id="linearGradient-14"
          >
            <stop stop-color="#D5E0F5" offset="0%"></stop>
            <stop stop-color="#EAF9FF" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="76.6817244%"
            y1="62.2771767%"
            x2="11.8846216%"
            y2="32.4611462%"
            id="linearGradient-15"
          >
            <stop stop-color="#E3E5FF" offset="1.90412615%"></stop>
            <stop stop-color="#FFFFFF" offset="97.7387115%"></stop>
          </linearGradient>
          <linearGradient
            x1="0.0029504698%"
            y1="50.0091196%"
            x2="100.026629%"
            y2="50.0091196%"
            id="linearGradient-16"
          >
            <stop stop-color="#4F517C" offset="0%"></stop>
            <stop stop-color="#273B68" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.965022%"
            y1="99.9801351%"
            x2="49.965022%"
            y2="-0.006782869%"
            id="linearGradient-17"
          >
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.9857084%"
            y1="-0.0020079371%"
            x2="49.9857084%"
            y2="99.9996782%"
            id="linearGradient-18"
          >
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.9987818%"
            y1="0.00530150379%"
            x2="49.9987818%"
            y2="100.000072%"
            id="linearGradient-19"
          >
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.990594%"
            y1="99.9908987%"
            x2="49.990594%"
            y2="0.0041714069%"
            id="linearGradient-20"
          >
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
          </linearGradient>
          <linearGradient
            x1="49.9860162%"
            y1="100.00835%"
            x2="49.9860162%"
            y2="0.000800732098%"
            id="linearGradient-21"
          >
            <stop stop-color="#FAD96E" offset="0%"></stop>
            <stop stop-color="#FFB32C" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="0.0029504698%"
            y1="50.0091196%"
            x2="100.026629%"
            y2="50.0091196%"
            id="linearGradient-22"
          >
            <stop stop-color="#4F517C" offset="0%"></stop>
            <stop stop-color="#273B68" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="50.0184826%"
            y1="100.001052%"
            x2="50.0184826%"
            y2="-0.0161551493%"
            id="linearGradient-23"
          >
            <stop stop-color="#F4B9A4" offset="0%"></stop>
            <stop stop-color="#FAD1BB" offset="65.2%"></stop>
          </linearGradient>
          <linearGradient
            x1="7.92517943%"
            y1="50%"
            x2="100%"
            y2="66.0571744%"
            id="linearGradient-24"
          >
            <stop stop-color="#50C6B3" offset="0%"></stop>
            <stop stop-color="#6AE0CC" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="68.0047605%"
            y1="65.4462324%"
            x2="41.316893%"
            y2="18.4290306%"
            id="linearGradient-25"
          >
            <stop stop-color="#50C6B3" offset="0%"></stop>
            <stop stop-color="#6AE0CC" offset="100%"></stop>
          </linearGradient>
        </defs>
        <g
          id="页面-1"
          stroke="none"
          stroke-width="1"
          fill="none"
          fill-rule="evenodd"
        >
          <g
            id="个人中心/用户中心/贸易伙伴/已实名认证"
            transform="translate(-992.000000, -371.000000)"
          >
            <g
              id="缺省页/暂无预约"
              transform="translate(992.000000, 371.000000)"
            >
              <rect id="矩形" x="0" y="0" width="200" height="200"></rect>
              <g
                id="编组"
                transform="translate(0.000000, 22.000000)"
                fill-rule="nonzero"
              >
                <g>
                  <path
                    d="M200,159.4386 C200,134.585786 155.228475,114.4386 100,114.4386 C44.771525,114.4386 0,134.585786 0,159.4386 L200,159.4386 Z"
                    id="椭圆形"
                    fill="url(#linearGradient-1)"
                  ></path>
                  <g transform="translate(16.000000, 104.000000)" id="路径">
                    <path
                      d="M6.67494613,0.0546670673 C6.67494613,0.0546670673 6.62560421,-0.0741555655 6.55831978,0.0637072521 C5.95275987,1.30447261 0.469078469,14.7449673 0.0339724605,25.1185793 C0.0339724605,25.1185793 -0.881095847,31.0783211 6.78708686,30.9992195 C13.1880794,30.9336782 13.0176255,25.4395059 12.9951973,23.588528 C12.9077276,16.0806546 6.67494613,0.0546670673 6.67494613,0.0546670673 Z"
                      fill="url(#linearGradient-2)"
                    ></path>
                    <path
                      d="M6.45,37 C6.175,37 5.95,36.9017467 5.95,36.7816594 L5.95,4.21834061 C5.95,4.09825328 6.175,4 6.45,4 C6.725,4 6.95,4.09825328 6.95,4.21834061 L6.95,36.779476 C6.95,36.9017467 6.725,37 6.45,37 Z"
                      fill="url(#linearGradient-3)"
                      transform="translate(6.450000, 20.500000) scale(-1, 1) translate(-6.450000, -20.500000) "
                    ></path>
                  </g>
                  <g id="编组备份" transform="translate(165.000000, 79.000000)">
                    <path
                      d="M10.2691479,0.0846457816 C10.2691479,0.0846457816 10.1932373,-0.114821521 10.0897227,0.098643487 C9.15809211,2.01982856 0.721659184,22.8309171 0.0522653238,38.8932841 C0.0522653238,38.8932841 -1.35553207,48.1212714 10.4416721,47.9987915 C20.2893529,47.8973081 20.0271161,39.3902026 19.9926113,36.5241724 C19.8580424,24.8990781 10.2691479,0.0846457816 10.2691479,0.0846457816 Z"
                      id="路径"
                      fill="url(#linearGradient-4)"
                    ></path>
                    <path
                      d="M9.5,58 C9.225,58 9,57.8421993 9,57.6493317 L9,5.35066825 C9,5.15780071 9.225,5 9.5,5 C9.775,5 10,5.15780071 10,5.35066825 L10,57.6458251 C10,57.8421993 9.775,58 9.5,58 Z"
                      id="路径"
                      fill="url(#linearGradient-5)"
                    ></path>
                  </g>
                  <g id="编组-2" transform="translate(87.549967, 10.000000)">
                    <ellipse
                      id="椭圆形"
                      fill="url(#linearGradient-6)"
                      cx="25.002269"
                      cy="9.75817733"
                      rx="9.74788342"
                      ry="9.75817733"
                    ></ellipse>
                    <path
                      d="M30.5508484,15.0795431 L20.8562628,15.0795431 C20.2307152,12.1001399 17.5882673,9.86207735 14.4240623,9.86207735 C11.2598573,9.86207735 8.6202146,12.1001399 7.99186183,15.0795431 L5.45320442,15.0795431 C2.44328243,15.0795431 0,17.5225976 0,20.5385062 L0,20.5385062 C0,23.5516067 2.44047729,25.9974693 5.45320442,25.9974693 L30.5508484,25.9974693 C33.5607704,25.9974693 36.0040529,23.5544148 36.0040529,20.5385062 L36.0040529,20.5385062 C36.0040529,17.5225976 33.5635756,15.0795431 30.5508484,15.0795431 Z"
                      id="路径"
                      fill="url(#linearGradient-7)"
                    ></path>
                  </g>
                  <g transform="translate(181.554454, 141.375191)">
                    <ellipse
                      id="椭圆形"
                      fill="url(#linearGradient-8)"
                      cx="3.90195851"
                      cy="3.90607904"
                      rx="3.90195851"
                      ry="3.90607904"
                    ></ellipse>
                    <path
                      d="M3.94554557,15.6248093 C3.70554557,15.6248093 3.51554557,15.5739164 3.51554557,15.5096307 L3.51554557,3.73998785 C3.51554557,3.67570213 3.70554557,3.62480928 3.94554557,3.62480928 C4.18554557,3.62480928 4.37554557,3.67570213 4.37554557,3.73998785 L4.37554557,15.5096307 C4.37554557,15.5712378 4.18554557,15.6248093 3.94554557,15.6248093 Z"
                      id="路径"
                      fill="url(#linearGradient-9)"
                    ></path>
                  </g>
                </g>
              </g>
              <g
                id="编组-2"
                transform="translate(38.000000, 78.000000)"
                fill-rule="nonzero"
              >
                <g id="编组" transform="translate(30.004154, 0.000000)">
                  <path
                    d="M11.7005938,86 L58.2976956,86 C64.7589257,86 69.9958457,80.8041162 69.9958457,74.3935156 L69.9958457,15.3548915 C69.9958457,8.94429095 64.7589257,3.7484071 58.2976956,3.7484071 L11.7005938,3.7484071 C5.23936365,3.7484071 0.00244373305,8.94429095 0.00244373305,15.3548915 L0.00244373305,74.3910911 C0.00244373305,80.8041162 5.23936365,86 11.7005938,86 Z"
                    id="路径"
                    fill="url(#linearGradient-10)"
                  ></path>
                  <path
                    d="M8.5481782,86 L57.7038684,86 C62.4251607,86 66.2520466,82.2031012 66.2520466,77.5188046 L66.2520466,15.3621652 C66.2520466,10.6778686 62.4251607,6.88096983 57.7038684,6.88096983 L8.5481782,6.88096983 C3.82688595,6.88096983 0,10.6778686 0,15.3621652 L0,77.5188046 C0.00244373305,82.2031012 3.82932968,86 8.5481782,86 Z"
                    id="路径"
                    fill="url(#linearGradient-11)"
                  ></path>
                  <path
                    d="M8.5481782,78.5977446 L57.7038684,78.5977446 C59.0014907,78.5977446 60.0522959,77.5551734 60.0522959,76.2677192 L60.0522959,16.6156752 C60.0522959,15.328221 59.0014907,14.2856498 57.7038684,14.2856498 L8.5481782,14.2856498 C7.25055595,14.2856498 6.19975074,15.328221 6.19975074,16.6156752 L6.19975074,76.2677192 C6.19975074,77.5551734 7.25055595,78.5977446 8.5481782,78.5977446 Z"
                    id="路径"
                    fill="#F9FAFC"
                  ></path>
                  <ellipse
                    id="椭圆形"
                    fill="url(#linearGradient-12)"
                    cx="33.1272452"
                    cy="55.4550888"
                    rx="16.0773197"
                    ry="16.3101776"
                  ></ellipse>
                  <path
                    d="M15.8720461,29.7860164 L50.3824442,29.7860164 C51.8413529,29.7860164 53.0241196,28.6125176 53.0241196,27.1650409 L53.0241196,26.7407387 C53.0241196,25.2932619 51.8413529,24.1197632 50.3824442,24.1197632 L15.8720461,24.1197632 C14.4131375,24.1197632 13.2303669,25.2932619 13.2303669,26.7407387 L13.2303669,27.1650409 C13.227927,28.6125176 14.4106938,29.7860164 15.8720461,29.7860164 Z"
                    id="路径"
                    fill="url(#linearGradient-13)"
                  ></path>
                  <path
                    d="M23.1470394,18.3347054 L46.5751081,18.3347054 C48.9992913,18.3347054 50.9640527,16.3853397 50.9640527,13.9801522 L50.9640527,4.35455314 C50.9640527,1.94936566 48.9992913,0 46.5751081,0 L23.1470394,0 C20.7228562,0 18.7580949,1.94936566 18.7580949,4.35455314 L18.7580949,13.9801522 C18.7580949,16.3853397 20.7228562,18.3347054 23.1470394,18.3347054 Z"
                    id="路径"
                    fill="url(#linearGradient-14)"
                  ></path>
                  <path
                    d="M22.0033724,18.3347054 L45.7564576,18.3347054 C47.5477139,18.3347054 49.0017351,16.8945024 49.0017351,15.1148576 L49.0017351,4.88068791 C49.0017351,3.10346772 47.5501576,1.66084015 45.7564576,1.66084015 L22.0033724,1.66084015 C20.212116,1.66084015 18.7580949,3.10104314 18.7580949,4.88068791 L18.7580949,15.1148576 C18.7580949,16.8920778 20.212116,18.3347054 22.0033724,18.3347054 Z"
                    id="路径"
                    fill="url(#linearGradient-15)"
                  ></path>
                  <path
                    d="M37.0396618,55.24415 L40.6001808,51.7115309 C41.650986,50.6689597 41.650986,48.9814491 40.6001808,47.9388779 C39.5493756,46.8963067 37.8485374,46.8987313 36.7977322,47.9388779 L33.2372132,51.471497 L29.6766941,47.9388779 C28.6258889,46.8987313 26.9250507,46.8987313 25.8742455,47.9388779 C24.8234403,48.9814491 24.8234403,50.6689597 25.8742455,51.7115309 L29.4347645,55.24415 L25.8742455,58.7767691 C24.8234403,59.8193403 24.8234403,61.5068509 25.8742455,62.549422 C26.3996481,63.0707076 27.0863371,63.3301381 27.7754698,63.3301381 C28.4621588,63.3301381 29.1512915,63.0707076 29.6766941,62.549422 L33.2372132,59.0168029 L36.7977322,62.549422 C37.3231348,63.0707076 38.0098238,63.3301381 38.6989565,63.3301381 C39.3856455,63.3301381 40.0747782,63.0707076 40.6001808,62.549422 C41.650986,61.5068509 41.650986,59.8193403 40.6001808,58.7767691 L37.0396618,55.24415 Z"
                    id="路径"
                    fill="#FFFFFF"
                  ></path>
                </g>
                <g id="编组" transform="translate(0.000000, 15.077685)">
                  <path
                    d="M21.6123751,0.223865208 C21.6123751,0.223865208 24.1563012,-0.093755316 25.0213827,1.80469408 C25.8864642,3.70314347 24.2320569,4.50083166 24.2320569,4.50083166 L25.0849197,8.20074708 C25.0849197,8.20074708 26.5780406,8.99601068 26.1968183,12.4752889 C25.8131522,15.9545672 22.0351409,18.7961798 22.0351409,18.7961798 L20.5029203,2.72361147 L21.6123751,0.223865208 Z"
                    id="路径"
                    fill="url(#linearGradient-16)"
                  ></path>
                  <path
                    d="M24.0463332,2.86423735 C24.0463332,2.86423735 26.9641504,8.94024525 25.3048557,9.8809839 C23.6431172,10.819298 22.8244667,10.1210177 22.8244667,10.1210177 L21.0478727,4.87906679 L24.0463332,2.86423735 Z"
                    id="路径"
                    fill="url(#linearGradient-17)"
                  ></path>
                  <path
                    d="M23.1519269,47.1298703 C23.1519269,47.1298703 22.5238875,64.4801945 22.5874245,65.0960389 C22.6509616,65.7118833 24.5033113,67.2830138 24.5033113,67.2830138 L23.388969,67.8188469 L21.1260722,65.9519171 C21.1260722,65.9519171 17.8294763,47.2026078 18.5161653,45.6557231 C19.2028543,44.1064138 23.1519269,47.1298703 23.1519269,47.1298703 Z"
                    id="路径"
                    fill="url(#linearGradient-18)"
                  ></path>
                  <path
                    d="M15.9160333,46.6982943 C15.9160333,46.6982943 16.8593143,48.3179165 14.8994404,49.0743868 C12.9395665,49.830857 2.41685198,54.1684381 2.41685198,54.1684381 L1.74971286,57.3810121 C1.74971286,57.3810121 3.03267271,58.5205666 2.97157938,58.2174936 C2.71498741,56.9785311 3.26727108,55.404976 3.6607121,55.3249647 C8.64104005,54.3017902 19.7673566,52.192402 20.9965543,50.2600084 C22.665624,47.6317592 19.4374527,45.0059346 19.4374527,45.0059346 L15.9160333,46.6982943 Z"
                    id="路径"
                    fill="url(#linearGradient-19)"
                  ></path>
                  <path
                    d="M13.1155153,23.1725542 C13.1155153,23.1725542 13.7142299,24.9667465 17.941888,29.5661827 C19.1270986,28.3781364 19.1002681,28.0871863 19.1002681,28.0871863 L16.0699885,23.3616718 L13.1155153,23.1725542 Z"
                    id="路径"
                    fill="url(#linearGradient-20)"
                  ></path>
                  <path
                    d="M17.8759072,10.9696222 C17.8759072,10.9696222 14.9385401,15.5035946 17.9858752,21.8196363 C18.9658122,23.6017056 18.9829183,25.5801663 17.2967425,28.6399915 C15.5054862,31.8889343 15.8085091,35.2178883 15.6472227,38.2389202 C15.4859363,41.259952 14.884778,46.1212433 12.4923633,46.227925 C13.6897925,48.116676 18.5308277,50.3303214 25.0971384,49.5399069 C24.5179737,48.3882295 25.7716087,40.4428672 25.6249847,36.088314 C25.4783607,31.7337609 24.9431832,30.3081054 25.6249847,26.0844798 C26.2163681,22.4282069 26.2212556,20.8304059 26.2212556,20.8304059 C26.2212556,20.8304059 28.9044745,18.6361573 26.6195841,15.3241753 C24.3322499,12.014618 21.4657511,9.27968703 17.8759072,10.9696222 Z"
                    id="路径"
                    fill="url(#linearGradient-21)"
                  ></path>
                  <path
                    d="M24.6597102,1.90167744 C24.6597102,1.90167744 24.8747587,3.73708765 22.9173285,5.68160414 C24.9334083,9.02753028 25.1484568,12.3031435 22.519,14.031872 C19.8895433,15.7581759 19.0660052,13.16872 19.0660052,13.16872 C19.0660052,13.16872 17.5997654,16.0830702 18.5454901,17.6323794 C19.4936585,19.1792641 20.5933384,22.8137158 20.6226632,24.2902875 C17.9932064,25.4783338 13.5627184,24.9376515 12.3408519,20.4036791 C12.0964786,19.0725824 12.0085042,17.9257541 12.4092764,16.5631378 C12.8100486,15.2005215 12.971335,15.1180857 12.5363505,13.5808993 C12.101366,12.0412884 11.424452,9.24816744 14.3887002,6.11802929 C14.2958383,2.95152238 16.3436866,0.684536198 19.9824051,1.11611218 C20.9916669,-0.401677504 24.359131,-0.581096731 24.6597102,1.90167744 Z"
                    id="路径"
                    fill="url(#linearGradient-22)"
                  ></path>
                  <path
                    d="M33.1370201,4.65842963 C33.1370201,4.65842963 31.4850566,5.3203411 31.2895579,6.19804057 C31.2504582,6.3774598 31.2113585,7.43700307 31.0305222,8.25166335 C30.5906503,10.2349732 29.9235112,13.3820834 29.7622248,14.1288553 C29.5374013,15.1835494 28.7920628,15.1350578 28.0564991,15.0914153 C27.105887,15.0356498 24.4275555,14.4537496 22.3894822,15.0817169 C20.7204125,15.6030025 20.8230493,18.3500564 23.8972655,18.1027488 C26.2579116,17.9112066 31.6610054,18.0615308 31.8662789,15.9739639 C32.0715525,13.8863969 32.3672442,8.55473637 32.3672442,8.55473637 C32.3672442,8.55473637 33.403387,7.2648576 33.3154126,6.90844373 C33.2274382,6.54960527 33.5744483,6.53990693 33.5402361,6.27077809 C33.5060238,6.00164925 33.6404291,5.90709047 33.5035801,5.66463205 C33.2030009,5.12394979 35.0846754,4.3408091 34.7743213,4.01106566 C34.6203661,3.84376935 33.1370201,4.65842963 33.1370201,4.65842963 Z"
                    id="路径"
                    fill="url(#linearGradient-23)"
                  ></path>
                  <path
                    d="M20.8865863,64.8996476 C20.8865863,64.8996476 20.4418269,65.573682 20.4784829,66.1749788 C20.5322451,66.4319847 20.7546248,66.4271356 20.8206056,66.8490132 C20.8865863,67.2684663 20.9036925,68.284367 20.9036925,68.284367 L21.1260722,68.284367 C21.1260722,68.284367 21.1260722,67.1423879 21.1431783,66.9993374 C21.1602845,66.856287 21.265365,66.6211023 21.5415068,66.8199182 C21.8176486,67.0187341 22.4530192,67.6030589 22.6387429,67.8309698 C22.8244667,68.0588807 23.132377,68.3037637 23.7579727,68.2504229 C24.3835683,68.197082 25.8180396,67.9691711 25.75939,67.7170143 C25.7007405,67.4648576 24.8674275,67.1181421 24.3982307,67.0914716 C24.2027321,67.188455 23.6186799,67.4648576 23.0761712,67.0187341 C22.5336624,66.5726106 21.2482588,65.2415139 21.1627282,65.1348322 C21.0747538,65.0257259 20.9134674,64.8535805 20.8865863,64.8996476 Z"
                    id="路径"
                    fill="url(#linearGradient-24)"
                  ></path>
                  <path
                    d="M2.76386208,53.8435438 C2.76386208,53.8435438 1.9525427,53.877488 1.48090223,54.2557231 C1.30250971,54.4472653 1.4344713,54.6242599 1.12900467,54.9224838 C0.823538037,55.2207076 0,55.8244291 0,55.8244291 L0.129517851,56.0038483 C0.129517851,56.0038483 1.06546761,55.3395122 1.19254173,55.2691993 C1.31961585,55.1988864 1.57620782,55.1479701 1.57132035,55.4874119 C1.56887662,55.8268536 1.46135236,56.6803073 1.38070917,56.961559 C1.30006598,57.2428108 1.28051612,57.6355934 1.68861954,58.1083873 C2.09672295,58.5811813 3.11820337,59.6067803 3.29170841,59.4128136 C3.46521346,59.2188469 3.26238362,58.343572 3.01312285,57.9507894 C2.82006794,57.8513814 2.25312187,57.5386101 2.30199653,56.8427544 C2.35087119,56.1444742 2.69543755,54.3333098 2.73209355,54.2023823 C2.76874954,54.0714547 2.8176242,53.8386946 2.76386208,53.8435438 Z"
                    id="路径"
                    fill="url(#linearGradient-25)"
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
      <span class="tip-title" v-html="title" />
      <template v-if="!noButton">
        <router-link v-if="linkTo" class="btn-wrapper" :to="{ name: linkTo }">
          <n-button class="blue border" :bordered="false" round>{{
            btnName
          }}</n-button>
        </router-link>
        <n-button
          v-else
          class="blue border"
          :bordered="false"
          round
          @click="handleEvent"
          >{{ btnName }}</n-button
        >
      </template>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import { NButton } from 'naive-ui'

export default defineComponent({
  name: 'NoData',
  components: {
    NButton,
  },
  props: {
    btnName: {
      type: String,
    },
    title: {
      type: String,
      required: true,
    },
    linkTo: {
      type: String,
    },
    noButton: {
      type: Boolean,
      default: false,
    },
    imgSrc: {
      type: String,
      default: '',
    },
  },
  methods: {
    handleEvent() {
      this.$emit('btnClick')
    },
  },
})
</script>
<style lang="scss" scoped>
.empty-data-container {
  width: 100%;
  padding: 40px 0;
  text-align: center;

  .content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      display: block;
      width: 200px;
      height: 200px;
    }

    .tip-title {
      display: inline-block;
      margin-bottom: 12px;
      line-height: 25px;
      font-size: 14px;
      color: #8a94a6;
    }

    .btn-wrapper {
      display: inline-block;
      // height: 40px;
      // border-radius: 20px;
      // border: 1px solid #e1e4e8;
      line-height: 38px;
      text-align: center;
      font-size: 14px;
      color: #0a1f44;
      cursor: pointer;

      span {
        padding: 0 24px;
      }
    }
  }
}
</style>
