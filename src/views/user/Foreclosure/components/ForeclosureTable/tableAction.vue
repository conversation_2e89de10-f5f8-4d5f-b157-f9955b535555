<template>
  <a-popover
    overlayClassName="custom-table-action"
    placement="bottomRight"
    :arrowPointAtCenter="true"
    v-model:visible="visible"
    trigger="click"
  >
    <template #content>
      <div class="action-btn" @click="handleViewProof">
        <span>查看凭证</span>
        <MySvgIcon
          icon-class="icon-youjiantou1"
          style="font-size: 20px; fill: #182c4f"
        />
      </div>
      <div
        v-if="record.proofStatus !== 0"
        class="action-btn"
        @click="handleViewValueDetail"
      >
        <span>价值明细</span>
        <MySvgIcon
          icon-class="icon-youjiantou1"
          style="font-size: 20px; fill: #182c4f"
        />
      </div>
      <div
        v-if="record.proofStatus !== 0"
        class="action-btn"
        @click="handleAddRepaymentInformation"
      >
        <span>填写回款信息</span>
        <MySvgIcon
          icon-class="icon-youjiantou1"
          style="font-size: 20px; fill: #182c4f"
        />
      </div>
    </template>
    <MySvgIcon
      icon-class="icon-gengduo-X"
      style="
        font-size: 24px;
        fill: #182c4f;
        display: inline-flex;
        outline: none;
      "
    />
  </a-popover>
</template>

<script lang="ts">
export default {
  name: 'CustomTableAction',
}
</script>
<script lang="ts" setup>
import { defineEmits, ref } from 'vue'
import { requestAttachDetail } from '@/api/common'

const props: any = defineProps({
  record: {
    type: Object,
    required: true,
    default: () => {},
  },
})
const visible = ref(false)

const emit = defineEmits([
  'handleViewImg',
  'handleViewValueDetail',
  'handleAddRepaymentInformation',
])

const handleViewProof = () => {
  visible.value = false
  requestAttachDetail({ id: props.record.proof })
    .then(({ data }) => {
      if (data.success) {
        data = data.data
        emit('handleViewImg', data.link)
      }
    })
    .catch(() => {})
}

const handleViewValueDetail = () => {
  visible.value = false
  emit('handleViewValueDetail', props.record.id)
}

const handleAddRepaymentInformation = () => {
  visible.value = false
  emit('handleAddRepaymentInformation', props.record.id)
}
</script>

<style lang="scss" scoped></style>

<style lang="scss">
.custom-table-action {
  padding: none;

  .ant-popover-inner {
    box-shadow: 0px 18px 18px 0px rgba(10, 31, 68, 0.12),
      0px 0px 1px 0px rgba(10, 31, 68, 0.1);
    border: 1px solid #efefef;
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-popover-inner-content {
    padding: 0 !important;
  }

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 140px;
    height: 40px;
    padding: 10px 14px 10px 16px;
    border-bottom: 1px solid #f1f2f4;
    font-size: 14px;
    @include family-PingFangSC-Medium-SFProText;
    font-weight: 500;
    color: #0a1f44;
    line-height: 40px;
    cursor: pointer;
    transition: all 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: aliceblue;
    }
  }
}
</style>
