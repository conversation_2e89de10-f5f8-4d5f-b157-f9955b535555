import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { api as viewerApi } from 'v-viewer'
import { RuleObject } from 'ant-design-vue/es/form/interface';
import { useStore } from 'vuex'
export default function () {
    const store = useStore()
    const loading: any = ref(false)
    const dialogRef: any = ref(null)
    const pdfView: any = ref(null)
    const formRef: any = ref(null)
    const previewVisible = ref<boolean>(false)
    const previewImage = ref<string | undefined>('')
    interface formState {
        changeType: string | undefined,
        goodsNum: number | string,
        reason: string,
        adjunctProof: any,
        linkPhone: string,
        linkMan: string
    }
    let formStateObj: formState = {
        changeType: undefined,
        goodsNum: 1,
        reason: '',
        adjunctProof: [],
        linkPhone: '',
        linkMan: ''
    }
    const progress = {
        strokeColor: {
            '0%': '#8F6BFF',
            '100%': '#4567FF',
        },
        strokeWidth: 3,
        format: (percent: any) => `${parseFloat(percent.toFixed(2))}%`,
        class: 'test',
    }
    const headersData = ref({
        'Blade-Auth': `bearer ${store.getters['Auth/token']}`,
    })
    const handleClose = () => {
        dialogRef.value.handleClose()
    }
    const formState = reactive({ ...formStateObj })
    const handleOpen = () => {
        dialogRef.value.handleOpen()
        formState.adjunctProof = []
        formState.changeType = undefined
        formState.reason = ''
        formState.goodsNum = 1
        formState.linkPhone = ''
        formRef.linkMan = ''
    }

    const selectOption = [{
        value: '1',
        label: "退货退款"
    }, {
        value: '2',
        label: '退款'
    }, {
        value: '3',
        label: '换货'
    }
    ]
    // 自定义检验手机号方式
    const phoneValidator = async (rule: RuleObject, value: string) => {
        if (value) {
            if (value.length != 11) {
                return Promise.reject('手机号码格式不对')
            }
            return Promise.resolve()
        } else {
            return Promise.reject('请输入手机号码')
        }

    }

    const rules = reactive({
        changeType: [{
            required: true,
            message: '请选择申请类型',
            trigger: 'blur',
        }],
        linkPhone: [{
            required: true,
            validator: phoneValidator,
            trigger: ['change', 'blur'],
        }],
        linkMan: [{
            required: true,
            message: '请输入联系人',
            trigger: 'blur',
        }],
        goodsNum: [{
            required: true,
            message: '请输入货物数量',
            trigger: 'blur',
        }],
        reason: [{
            required: true,
            message: '请输入申请原因',
            trigger: 'blur'
        }],
        adjunctProof: [{
            required: true,
            message: '请上传凭证',
            trigger: 'change'
        }]
    })

    const handlePreview = (file: any) => {
        const targetUrl = file.url
        if (!targetUrl) {
            message.warning('文件上传中')
            return
        }
        if (targetUrl.endsWith('.pdf')) {
            console.log(pdfView.value)
            pdfView.value.handleOpen(targetUrl)
        } else {
            viewerApi({
                options: {
                    toolbar: false,
                    navbar: false,
                    title: false,
                },
                images: [targetUrl],
            })
        }
    }
    const handleChange = (info: any) => {
        const status = info.file.status
        const res = info.file.response
        if (status === 'done' && res.code === 200) {
            const dat = res.data
            for (const item of formState.adjunctProof) {
                if (item.lastModified === info.file.lastModified) {
                    item.attachId = dat.attachId
                    item.url = dat.link
                }
            }

        } else if (status === 'error') {
            formState.adjunctProof.pop()
            message.error('文件上传失败')
        }
    }

    const changeSelect = (value: any) => {
        if (value == 1 || value == 3) {
            rules.goodsNum[0].required = true
        } else {
            rules.goodsNum[0].required = false
        }
    }



    return { handleClose, previewVisible, previewImage, formRef, dialogRef, loading, handleOpen, formState, selectOption, rules, handlePreview, handleChange, headersData, progress, changeSelect,pdfView }
}