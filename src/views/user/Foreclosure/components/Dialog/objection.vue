<template>
  <GlobalDialog
    title="发起异议"
    width="600px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
    :maskClosable="false"
    :onClose="handleClose"
  >
    <a-form :model="formState" :rules="rules" ref="formRef">
      <a-form-item
        name="changeType"
        label="申请类型"
        labelAlign="right"
        :labelCol="{ span: 4 }"
      >
        <a-select
          v-model:value="formState.changeType"
          size="small"
          :options="selectOption"
          @change="changeSelect"
          placeholder="请选择申请类型"
        ></a-select>
      </a-form-item>
      <a-form-item
        labelAlign="right"
        name="goodsNum"
        label="货物数量"
        :labelCol="{ span: 4 }"
        v-show="formState.changeType != 2"
      >
        <n-input-number
          placeholder="请输入退货数量"
          v-model:value="formState.goodsNum"
          :max="maxNum"
          :min="1"
          size="large"
          :show-button="false"
        >
          <template #suffix>{{ company }}</template></n-input-number
        >
      </a-form-item>
      <a-form-item
        labelAlign="right"
        name="linkMan"
        label="联系人"
        :labelCol="{ span: 4 }"
      >
        <a-input
          placeholder="请输入联系人"
          v-model:value="formState.linkMan"
          size="large"
          autocomplete="off"
        ></a-input>
      </a-form-item>
      <a-form-item
        labelAlign="right"
        name="linkPhone"
        label="联系方式"
        :labelCol="{ span: 4 }"
      >
        <a-input
          placeholder="请输入联系方式"
          v-model:value="formState.linkPhone"
          size="large"
          autocomplete="off"
        ></a-input>
      </a-form-item>
      <a-form-item
        name="reason"
        label="申请原因"
        labelAlign="right"
        :labelCol="{ span: 4 }"
      >
        <a-textarea
          placeholder="请输入申请原因"
          v-model:value="formState.reason"
          :autosize="{ minRows: 4, maxRows: 6 }"
          showCount
          :maxlength="150"
        ></a-textarea>
      </a-form-item>
      <a-form-item
        name="adjunctProof"
        label="凭证上传"
        labelAlign="right"
        :labelCol="{ span: 4 }"
      >
        <div class="clearfix">
          <a-upload
            :headers="headersData"
            action="/api/blade-resource/oss/endpoint/put-file-attach"
            list-type="picture-card"
            v-model:file-list="formState.adjunctProof"
            @preview="handlePreview"
            @change="handleChange"
            accept=".jpg,.jpeg,.png,.pdf"
          >
            <div v-if="formState.adjunctProof.length < 8">
              <!-- <plus-outlined /> -->
              <div class="ant-upload-text">上传凭证</div>
            </div>
          </a-upload>
        </div>

        <PdfView ref="pdfView" />
      </a-form-item>
    </a-form>
    <template #button>
      <div style="width: 100%; text-align: right">
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 12px"
          round
          :bordered="false"
          @click="handleClose"
        >
          取 消
        </n-button>
        <n-button
          class="border blue button-item primary"
          type="info"
          style="height: 40px"
          round
          :bordered="false"
          :loading="loading"
          @click="onFinish"
          html-type="submit"
        >
          申请
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>
<script setup lang="ts">
import { changeApply } from '@/api/user/Foreclosure'
import { ref, reactive } from 'vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import PdfView from '@/components/FilePreview/index.vue'
import { NButton, NInputNumber } from 'naive-ui'
import objection from '@/views/user/Foreclosure/components/Dialog/objection'
import { message } from 'ant-design-vue'
const emit = defineEmits(['refresh'])
const props = defineProps({
  maxNum: {
    type: Number,
  },
  company: {
    type: String,
  },
  redeemNo: {
    type: String,
  },
  financeNo: {
    type: String,
  },
})

const {
  dialogRef,
  formState,
  selectOption,
  rules,
  progress,
  headersData,
  loading,
  formRef,
  previewVisible,
  previewImage,
  handleOpen,
  handleClose,
  handlePreview,
  handleChange,
  changeSelect,
  pdfView,
} = objection()
const onFinish = async () => {
  try {
    await formRef.value.validateFields()
    loading.value = true
    const resArr = []
    const params = { ...JSON.parse(JSON.stringify(formState)) }
    for (const item of formState.adjunctProof) {
      resArr.push(item.attachId)
    }
    params.adjunctProof = resArr.join(',')
    params.redeemNo = props.redeemNo
    params.financeNo = props.financeNo
    params.goodsUnit = props.company
    if (formState.changeType === '2') {
      params.goodsNum = 0
    }
    const {
      data: { data },
    } = await changeApply(params)
    if (data) {
      message.success('提交成功')
      emit('refresh')
      handleClose()
    }
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
defineExpose({ handleOpen })
</script>
<style lang="scss" scoped>
.ant-input {
  height: 48px;
  line-height: 48px;
}
.upload-wrapper {
  width: 100px;
}
</style>
