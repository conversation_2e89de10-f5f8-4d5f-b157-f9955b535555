// 凭证状态
const foreclosureStatusMap: any = {
  null: {
    name: '--',
    color: '#84868D',
    backgroundColor: '#F5F5F5',
    borderColor: '#D9D9D9',
  },
  undefined: {
    name: '--',
    color: '#84868D',
    backgroundColor: '#F5F5F5',
    borderColor: '#D9D9D9',
  },
  0: {
    name: '暂无',
    color: '#84868D',
    backgroundColor: '#F5F5F5',
    borderColor: '#D9D9D9',
  },
  1: {
    name: '赎货申请审核中',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
  2: {
    name: '已作废',
    color: '#53627C',
    backgroundColor: '#fff',
    borderColor: '#53627C',
  },
  3: {
    name: '赎货申请待确认',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
  4: {
    name: '已作废',
    color: '#53627C',
    backgroundColor: '#fff',
    borderColor: '#53627C',
  },
  5: {
    name: '赎货确认审核中',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
  6: {
    name: '赎货确认已驳回',
    color: '#F07300',
    backgroundColor: '#fff',
    borderColor: '#F07300',
  },
  7: {
    name: '已作废',
    color: '#53627C',
    backgroundColor: '#fff',
    borderColor: '#53627C',
  },
  8: {
    name: '待发货',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
  9: {
    name: '已发货',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
  10: {
    name: '待验收',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
  11: {
    name: '异议处理中',
    color: '#F07300',
    backgroundColor: '#fff',
    borderColor: '#F07300',
  },
  12: {
    name: '待提货',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
  13: {
    name: '已完成',
    color: '#00865A',
    backgroundColor: '#fff',
    borderColor: '#00865A',
  },
  14: {
    name: '待解质押',
    color: '#0D55CF',
    backgroundColor: '#fff',
    borderColor: '#0D55CF',
  },
}

export { foreclosureStatusMap }
