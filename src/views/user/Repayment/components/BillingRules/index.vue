<template>
  <div class="wrap">
    <div class="title">计费规则</div>
    <div class="row-text">
      <div class="cont">
        <span class="field">剩余应还本金</span>
        <span class="val">¥{{ props.data.surplusPrincipal }}</span>
      </div>
      <div class="cont">
        <span class="field">应还利息</span>
        <span class="val">¥{{ props.data.shouldInterest }}</span>
      </div>
      <p class="help">
        <!-- 应还利息=剩余应还本金*日利率*计息天数 500.00*0.05%*1=0.25元 -->
        <!-- 应还利息=剩余应还本金*日利率*计息天数 -->
        {{ props.data.shouldInterestFormula || '' }}<br />{{
          props.data.shouldInterestValueFormula || ''
        }}
      </p>
    </div>

    <div class="row-text">
      <!--<div class="cont">
        <span class="field">提前结清手续费</span>
        <span class="val">
          <!~~ ¥{{ props.data.prepaymentServiceFee }} ~~>
          ¥{{ getFormulaByType('ServiceFee') }}
        </span>
      </div>-->
      <div class="help">
        <!-- 提前还款手续费=剩余应还本金*日利率*提前还款天数*5%
        500.00*0.05%*56*5%=0.70元 -->
        <!-- 提前还款手续费=剩余应还本金*日利率*提前还款天数*5%
        {{ props.data.prepaymentServiceFeeFormula || '' }}={{
          props.data.prepaymentServiceFeeValueFormula || ''
        }}元 -->
        <p>{{ getFormulaByType('ServiceFeeFormula') }}</p>
        <p>{{ getFormulaByType('ServiceFeeValueFormula') }}</p>
      </div>
    </div>
    <template v-if="addAmountVO.repaymentPlanFeeList && addAmountVO.repaymentPlanFeeList?.length">
      <div class="row-text"  v-for="item in addAmountVO.repaymentPlanFeeList" :key="item.id">
      <div class="cont">
        <span class="field">{{item.feeName}}</span>
        <span class="val">
          <!-- ¥{{ props.data.prepaymentServiceFee }} -->
          ¥{{ item.amount }}
        </span>
      </div>
    </div>
    </template>
    
    <div class="row-text">
      <div class="cont">
        <span class="field">计息天数</span>
        <span class="val">{{ props.data.interestAccrualDay }}天</span>
      </div>
      <p class="help">{{ props.data.interestAccrualDateRange }}</p>
    </div>
    <div class="row-text">
      <div class="cont">
        <span class="field">提前还款天数</span>
        <span class="val">{{ props.data.prepaymentDay }}天</span>
      </div>
      <p class="help">{{ props.data.prepaymentDayDateRange }}</p>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  type: {
    type: String,
    default: 'part', // 默认提前还款(part) ，settle：提前结清
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  addAmountVO: {
    type: Object,
    default: () => ({}),
  },
})

const getFormulaByType = field => {
  // ServiceFeeFormula
  const { type, data } = props
  let fieldFirst = ''
  if (type === 'part') {
    fieldFirst = 'prepayment'
  } else {
    fieldFirst = 'advanceSettle'
  }
  return data[`${fieldFirst}${field}`]
}
const helpTipsGS = () => {
  let result = ''
  const { type, data } = props
  console.log('data', data)
  if (type === 'part') {
    // prepaymentServiceFeeFormula
    result = data.prepaymentServiceFeeFormula
  } else {
    result = data.advanceSettleServiceFeeFormula
  }
  return result
}
const helpTipsVal = () => {
  let result = ''
  const { type, data } = props
  console.log('data', data)
  if (type === 'part') {
    result = data.prepaymentServiceFeeValueFormula
  } else {
    result = data.advanceSettleServiceFeeValueFormula
  }
  return result
}
</script>

<style lang="scss" scoped>
.wrap {
  width: 300px;
  color: #ffffff;
}

.title {
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
}

.row-text {
  padding-bottom: 8px;

  &:last-child {
    padding-bottom: 0;
  }

  .cont {
    padding: 8px 0;
    display: flex;
    justify-content: space-between;

    .field {
      font-size: 12px;
      font-family: PingFangSC-Semibold, PingFang SC;
    }

    .val {
      font-size: 12px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
    }
  }

  .help {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    opacity: 0.8;
  }
}
</style>
