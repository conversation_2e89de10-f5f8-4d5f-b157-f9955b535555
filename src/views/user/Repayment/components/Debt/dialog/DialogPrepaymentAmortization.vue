<template>
  <GlobalDialog
    title="提前还款"
    width="432px"
    ref="dialogRef"
    :enableFullscreen="false"
  >
    <div class="wrap dialog-prepayment-amortization-component">
      <a-form
        ref="formRef"
        :model="formState"
        @finish="onFinish"
        autocomplete="off"
        layout="vertical"
        @finishFailed="onFinishFailed"
      >
        <a-form-item
          name="amount"
          :rules="[{ required: true, message: '请选择还款期数' }]"
        >
          <a-checkbox-group v-model:value="amount" style="width: 100%"
            ><a-row
              class="amorization-item"
              justify="space-between"
              align="top"
            >
              <a-col :span="12">
                <a-checkbox value="A">2022/08/12(第2期)</a-checkbox>
              </a-col>
              <a-col :span="12">
                <div class="checkbox-tips-text">
                  <span class="val">应还¥3,004,012.00</span>
                  <BillingRulesToolTip />
                </div>
              </a-col> </a-row
            ><a-row
              class="amorization-item"
              justify="space-between"
              align="top"
            >
              <a-col :span="12">
                <a-checkbox value="A">2022/08/12(第2期)</a-checkbox>
              </a-col>
              <a-col :span="12">
                <div class="checkbox-tips-text">
                  <span class="val">应还¥3,004,012.00</span>
                  <BillingRulesToolTip />
                </div>
              </a-col> </a-row
            ><a-row
              class="amorization-item"
              justify="space-between"
              align="top"
            >
              <a-col :span="12">
                <a-checkbox value="A">2022/08/12(第2期)</a-checkbox>
              </a-col>
              <a-col :span="12">
                <div class="checkbox-tips-text">
                  <span class="val">应还¥3,004,012.00</span>
                  <BillingRulesToolTip />
                </div>
              </a-col> </a-row
            ><a-row
              class="amorization-item"
              justify="space-between"
              align="top"
            >
              <a-col :span="12">
                <a-checkbox value="A">2022/08/12(第2期)</a-checkbox>
              </a-col>
              <a-col :span="12">
                <div class="checkbox-tips-text">
                  <span class="val">应还¥3,004,012.00</span>
                  <BillingRulesToolTip />
                </div>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>

        <div class="footer">
          <div class="left">
            <a-checkbox
              v-model:checked="checkAll"
              :indeterminate="indeterminate"
              @change="onCheckAllChange"
            >
              全选
            </a-checkbox>
          </div>
          <div class="right">
            <div class="total">
              <span class="tips-text">总计</span>
              <span class="val">¥3,004,012.00</span>
            </div>
            <a-button
              class="btn"
              type="primary"
              shape="round"
              size="large"
              html-type="submit"
              >还款</a-button
            >
          </div>
        </div>
      </a-form>
    </div>
  </GlobalDialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import BillingRulesToolTip from '../../BillingRules/tooltip.vue'

const formState = reactive({
  amount: '',
})

const dialogRef = ref(null)
const formRef = ref(null)

const handleOpen = () => {
  dialogRef.value.handleOpen()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}
const onFinish = values => {
  console.log('Success:', values)
}

const onFinishFailed = errorInfo => {
  console.log('Failed:', errorInfo)
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.wrap {
  padding-top: 8px;
}
.amorization-item {
  padding: 12px;
  background: #f8f9fb;
  border-radius: 6px;
  border: 1px solid #efefef;
  margin-bottom: 8px;
  color: #0a1f44;

  &.checked {
    border: 1px solid #0c66ff;
  }

  .checkbox-tips-text {
    .val {
      padding-right: 2px;
    }
    display: flex;
    align-items: center;
    justify-content: end;
    padding-right: 4px;
  }
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  .left {
    padding-left: 13px;
  }
  .right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .total {
      padding-right: 8px;
      .tips-text {
        font-size: 14px;
        color: #9ba1a5;
        padding-right: 4px;
      }
      .val {
        font-size: 18px;
        font-family: CoreSansD55Bold;
        color: #0a1f44;
      }
    }
    .btn {
      height: 40px;
      &[disabled] {
        background: #0c66ff;
        opacity: 0.2;
        color: #ffffff;
      }
    }
  }
}
</style>
<style lang="scss">
.dialog-prepayment-amortization-component {
  .ant-form-item {
    margin-bottom: 0;
  }
  .ant-checkbox-wrapper {
    color: #0a1f44;
  }

  .ant-checkbox {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 0;
    .ant-checkbox-wrapper {
      display: flex;
      align-items: center;
    }
    .ant-checkbox-inner {
      width: 16px;
      height: 16px;
      border: 2px solid #c0bcc0;
      border-radius: 8px;
    }
    &.ant-checkbox-checked {
      .ant-checkbox-inner {
        width: 24px;
        height: 24px;
        background: #0c66ff;
        border-radius: 12px;
        border-width: 0;
        &::after {
          left: 34.5%;
        }
      }
      &::after {
        border-radius: 12px;
      }
    }
  }
}
</style>
