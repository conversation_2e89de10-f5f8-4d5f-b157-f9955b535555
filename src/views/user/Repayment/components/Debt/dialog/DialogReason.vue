<template>
  <GlobalDialog
    title="提前结清申请"
    width="600px"
    ref="dialogRef"
    :enableFullscreen="false"
    :onClose="onClose"
  >
    <a-form
      ref="formRef"
      :model="formState"
      @finish="onFinish"
      autocomplete="off"
      layout="vertical"
    >
      <a-form-item
        name="reason"
        :rules="[{ required: true, message: '请输入提前结清原因' }]"
      >
      <div class="title">提前结清原因</div>
        <a-textarea
          v-model:value="formState.reason"
          showCount
          placeholder="请输入提前结清原因"
          :maxlength="200"
          :rows="4"
        />
      </a-form-item>
      <div class="footer">
        <div class="left"></div>
        <div class="right">
          <a-button
            class="btn"
            type="primary"
            shape="round"
            size="large"
            html-type="submit"
            :loading="submitBtnLoading"
            >提交</a-button
          >
        </div>
      </div>
    </a-form>
  </GlobalDialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { LOAN_REPAYMENT } from '@/api/index.js'
import { message } from 'ant-design-vue'
const initFormData = {
  reason: '',
}
const formState = reactive({
  ...initFormData,
})
const submitBtnLoading = ref(false)
const dialogRef = ref(null)
const isVisiable = ref(false)
const formRef = ref(null)
const emit = defineEmits(['Close'])
const props = defineProps({
  iouNo: {
    type: String,
    default: '',
  },
  onClose: {
    type: Function,
    default: () => {},
  },
})

const handleOpen = () => {
  dialogRef.value.handleOpen()
  isVisiable.value = true
  formState.reason = ''
}

function onClose() {
  if (typeof props.onClose === 'function') {
    props.onClose()
  }
  isVisiable.value = false
}
const onFinish = async () => {
    await formRef.value.validate()
    const reason = formState.reason
    submitBtnLoading.value = true
    let param = {
        iouNo:props.iouNo,
        reason
    }
    LOAN_REPAYMENT.reqapplyAdvanceSettled(param)
      .then(({ data: res }) => {
        if (res.code === 200) {
          message.success('提交成功')
          
          setTimeout(() => {
            submitBtnLoading.value = false
            emit('Close')
            dialogRef.value.handleClose()
            
          }, 500)
        }
      })
      .catch(err => {
        submitBtnLoading.value = false
      })
  
}

defineExpose({
  handleOpen,
})
</script>
<style lang="scss" >
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;

  .left {
    padding-left: 13px;
  }

  .right {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .total {
      padding-right: 8px;

      .tips-text {
        font-size: 14px;
        color: #9ba1a5;
        padding-right: 4px;
      }

      .val {
        font-size: 18px;
        font-family: CoreSansD55Bold;
        color: #0a1f44;
      }
    }

    .btn {
      height: 40px;

      &[disabled] {
        background: #0c66ff;
        opacity: 0.2;
        color: #ffffff;
      }
    }
  }
}
</style>
