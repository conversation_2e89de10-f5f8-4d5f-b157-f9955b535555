<template>
  <div class="user-product-container">
    <div class="header-box">
      <UserHeader headerName="我的产品" iconClass="icon-fenlei" />
      <LabelBar
        :labelList="labelData.labelList"
        :state="labelData.currentIndex"
        @switch="handleSwitch"
      />
      <!-- 测试使用 -->
      <!-- <div style="display: flex; margin-top: 20px">
        <a-input
          class="search-name"
          style="width: 200px"
          v-model:value="demoPrice"
          placeholder="订单面额"
        />
        <n-button
          class="blue border"
          style="margin: 0 10px"
          :bordered="false"
          @click="demoGetOrder"
        >
          获取订单
        </n-button>
        <n-button
          class="blue border"
          :bordered="false"
          @click="handleFinancing"
        >
          立即融资
        </n-button> 
      </div> -->
    </div>
    <div class="content-box">
      <Whole
        v-if="!isEnterpriseAccount || roleMap.my_product_list"
        :indexNum="labelData.currentIndex"
      />
      <div v-else-if="isEnterpriseAccount" class="empty-content">
        <Empty text="暂无权限,请与管理员联系" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserProductIndex',
}
</script>

<script setup>
import { reactive, computed, ref } from 'vue'
import { useStore } from 'vuex'
import { USER_PRODUCT } from '@/api/index'
import Whole from './components/Whole/index.vue'
import UserHeader from '@/views/user/components/Header/index.vue'
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import Empty from '@/components/empty/index.vue'

import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { createVNode } from 'vue'
import { Modal } from 'ant-design-vue'

import { NButton } from 'naive-ui'

const store = useStore()
const router = useRouter()
const userInfo = computed(() => store.getters['Auth/userInfo'])
const demoPrice = ref('')
// 测试写死
const groupId = '1865290170307170306'
// 测试获取订单
const demoGetOrder = async () => {
  if (!demoPrice.value) {
    message.error('请先输入订单面额')
    return
  }

  Modal.confirm({
    title: '提示?',
    icon: createVNode(ExclamationCircleOutlined),
    content: '是否要获取订单？',
    async onOk() {
      try {
        const params = {
          groupId,
          orderAmount: demoPrice.value,
        }

        await USER_PRODUCT.getOrderOne(params)
        // const { data: orderRes } =
        // const orderData = orderRes.data
        // const params2 = {
        //   groupId,
        //   orderId: orderData.id,
        //   userId: userInfo.value.userId,
        // }

        // await USER_PRODUCT.planCalculateOne(params2)

        demoPrice.value = ''
        message.success('获取订单成功')
        return true
        // return await new Promise((resolve, reject) => {
        //   setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);
        // });
      } catch {
        return console.log('Oops errors!')
      }
    },
    onCancel() {},
  })
}
// 测试立即融资
const handleFinancing = async () => {
  // 融资校验
  await USER_PRODUCT.financeCheck({ groupId })

  router.push({
    name: 'appliCationsProductGroup',
    query: {
      goodId: groupId,
    },
  })
  // /blade-plan/web-front/plan-finance-apply/getByIds
}
// const storeCurrentLabelIndex = computed(() =>
//   store.getters['ServerCenter/currentActiveLabelIndex']('UserProductIndex')
// )
const roleMap = computed(() => store.getters['Role/roleMap'])
const isEnterpriseAccount = computed(
  () => store.getters['Auth/isEnterpriseAccount']
)
const labelData = reactive({
  labelList: [
    '全部',
    '待申请额度',
    '待激活',
    '可融资',
    '开通失败',
    '已到期',
    '已禁用',
    '已冻结',
  ],
  currentIndex: 0,
})

// 有bug先停用
// 将 Store 里面保存的 Label 菜单更新到当前页面
// watchEffect(() => {
//   labelData.currentIndex = storeCurrentLabelIndex.value || 0
// })

const handleSwitch = targetIndex => {
  labelData.currentIndex = targetIndex
  // store.commit('ServerCenter/setActiveLabelIndexMap', {
  //   UserProductIndex: targetIndex,
  // })
}
</script>

<style lang="scss" scoped>
.user-product-container {
  .header-box {
    padding: 24px 24px 20px;
    border-bottom: 1px solid #f1f2f4;
    box-sizing: border-box;
  }

  .content-box {
    padding: 0 24px 24px;

    .empty-content {
      display: flex;
      justify-content: center;
      height: 500px;
    }
  }
}
</style>
