<template>
  <div class="user-product">
    <div class="loading-box" v-if="loadingType">
      <a-spin :spinning="loadingType" />
    </div>
    <div
      class="user-product-flexbox"
      v-else-if="arrData.length && !loadingType"
    >
      <div
        class="user-product-childrenbox"
        v-for="item in arrData"
        :key="item.id"
      >
        <div class="user-product-childrenbox-title">
          <div class="user-product-childrenbox-title-left">
            <span
              :style="{ width: `${item.goodsType != 99 ? '28px' : '0px'}` }"
            >
              <img
                v-if="item.goodsType != 99"
                :src="item.capitalAvatar"
                alt=""
              />
            </span>
            <span @dblclick="showOrderModal(item)">{{ item.goodsName }}</span>
            <span>
              <span @dblclick="showModal(item)">{{ item.goodsTypeText }} </span>
            </span>
          </div>
          <div class="user-product-childrenbox-title-right">
            <span
              class="c8a94a6"
              :class="{
                c00865a: item.type == '可融资' || item.type == '可使用',
                c0d55cf:
                  item.type == '待申请额度' ||
                  item.type == '待激活' ||
                  item.type == '额度调整',
              }"
              >{{
                item.appendType
                  ? item.appendType
                  : item.type == '额度调整'
                  ? '待激活'
                  : item.type
              }}</span
            >
          </div>
        </div>
        <div
          class="user-product-childrenbox-content-num"
          v-if="
            item.type == '可融资' ||
            item.type == '可使用' ||
            item.type == '已到期' ||
            item.type == '已禁用' ||
            item.type == '额度调整' ||
            item.type == '已冻结' ||
            item.type == '待激活' ||
            item.goodsType == 99
          "
        >
          <div class="limit-lable">
            <span>当前可用额度(元)</span>
            <MySvgIcon
              icon-class="icon-tixing"
              style="color: #f07300; font-size: 20px"
              v-if="item.type == '额度调整' || item.type == '已冻结'"
            />
            <span v-if="item.type == '额度调整'">
              额度已重新评估，需要激活额度才能生效
            </span>
            <span v-else-if="item.type == '已冻结'"
              >请申请解冻，恢复正常使用</span
            >
          </div>
          <span>
            <a-statistic
              :class="{
                'moneyNum-in-active':
                  item.type == '待激活' || item.type == '额度调整',
                'moneyNum-decoration': item.type == '已到期',
                'moneyNum-disables':
                  item.type == '已禁用' || item.type == '已冻结',
                'product-group-size': item.goodsType == 99 || props.isDetail,
              }"
              :precision="2"
              :value="
                item.goodsType == 99
                  ? item.totalAvailable
                  : item.availableCredit
              "
            >
              <!-- <template #prefix>
                <span class="money-prefix">￥</span>
              </template> -->
            </a-statistic>
          </span>
          <span
            v-if="item.goodsType != 99"
            :class="{
              'text-disables':
                item.type == '已到期' ||
                item.type == '已禁用' ||
                item.type == '已冻结',
            }"
            >日利率{{ item.dailyInterestRate }}%起（年化利率{{
              item.annualInterestRate
            }}%）</span
          >
          <span class="text-item" v-if="item.capitalName && props.isDetail">
            资金方：{{ item.capitalName }}
          </span>
          <!-- <span v-else>注：当前额度为所有资金方可用额度汇总</span> -->
          <div class="products-item-time" v-if="item.goodsType == 99">
            申请时间：{{ item.createTime }}
          </div>
          <span v-else-if="item.goodsTypeText !== '动产质押'">
            到期日：{{ item.expireTime }}
          </span>
        </div>
        <div class="user-product-childrenbox-content-introduce" v-else>
          <div class="products-item-tag">
            <span
              v-for="items in item.itemLableDatas || 3"
              :key="items.id"
              class="tags"
              :style="{
                'border-color': items.labelColor,
                color: items.labelColor,
              }"
            >
              {{ items.name }}
            </span>
          </div>
          <div class="products-item-emphasis">
            <div
              class="products-item-emphasis-box"
              :style="{
                width: item.annualInterestRateType == 1 ? '230px' : '152px',
              }"
            >
              <div class="products-item-emphasis-children-top">
                <span>{{ item.loanAmountEnd }}</span>
                <span>万</span>
              </div>
              <div class="products-item-emphasis-children-bottom">最高可借</div>
            </div>
            <div
              class="products-item-emphasis-box"
              v-if="item.annualInterestRateType != 1"
              style="width: 152px"
            >
              <div class="products-item-emphasis-children-top">
                <span>{{ item.annualInterestRateStart }}</span>
                <span>%</span>
              </div>
              <div class="products-item-emphasis-children-bottom">
                年利率低至
              </div>
            </div>
            <div
              class="products-item-emphasis-box"
              :style="{
                width: item.annualInterestRateType == 1 ? '230px' : '152px',
              }"
            >
              <div class="products-item-emphasis-children-top">
                <span>{{ item.loadTermEnd }}</span>
                <span>{{ item.loadTermUnit }}</span>
              </div>
              <div class="products-item-emphasis-children-bottom">最长期限</div>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between">
            <div class="products-item-time">
              申请时间：{{ item.createTime }}
            </div>

            <div class="products-item-time" v-if="props.isDetail">
              资金方：{{ item.capitalName }}
            </div>
          </div>
        </div>
        <div
          class="user-product-childrenbox-menu"
          :class="{ fixedBtn: props.isDetail }"
        >
          <router-link
            v-if="props.isDetail && !item.type"
            :to="{
              name: 'processOpen',
              query: {
                goodId: item.goodsId,
                goodType: 5,
                capitalId: item.capitalId,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              立即开通
            </span>
          </router-link>
          <template
            v-if="
              !props.isDetail &&
              (!isEnterpriseAccount ||
                roleMap.my_product_applied_financing_button) &&
              item.type == '可融资' &&
              (item.goodsTypeText === '应收账款质押' ||
                item.goodsTypeText === '订单融资' ||
                item.goodsTypeText === '动产质押')
            "
          >
            <span
              v-if="item.goodsType == 99"
              @click="handleFinancing(item.groupId)"
              @keypress.enter="handleFinancing(item.groupId)"
              role="link"
              style="margin-right: 16px"
            >
              立即融资
            </span>
            <router-link
              v-else
              :to="{
                name:
                  item.goodsTypeText === '应收账款质押' ||
                  item.goodsTypeText === '订单融资'
                    ? 'appliCations'
                    : 'movablePropertyFinancing',
                query: {
                  goodId: item.goodsId,
                  goodType: item.goodsType,
                  customerGoodsId: item.id,
                  enterpriseQuotaId: item.enterpriseQuotaId,
                  lendingMethod: item.lendingMethod,
                  chargeMethod: item.chargeMethod,
                  capitalId: item.capitalId,
                },
              }"
              v-slot="{ navigate }"
            >
              <span @click="navigate" @keypress.enter="navigate" role="link">
                立即融资
              </span>
            </router-link>
          </template>

          <span
            v-else-if="
              !props.isDetail &&
              isEnterpriseAccount &&
              !roleMap.my_product_applied_financing_button &&
              item.type == '可融资' &&
              (item.goodsTypeText === '应收账款质押' ||
                item.goodsTypeText === '订单融资' ||
                item.goodsTypeText === '动产质押')
            "
            style="margin-right: 16px"
            @click="noRights"
          >
            立即融资
          </span>
          <router-link
            v-else-if="
              (!isEnterpriseAccount || roleMap.my_product_purchase_button) &&
              item.type == '可融资' &&
              item.goodsTypeText === '代采融资'
            "
            :to="{
              name: 'generationOfMiningMallDetail',
              query: {
                goodId: item.goodsId,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              立即代采
            </span>
          </router-link>
          <span
            v-else-if="
              isEnterpriseAccount &&
              !roleMap.my_product_purchase_button &&
              item.type == '可融资' &&
              item.goodsTypeText === '代采融资'
            "
            style="margin-right: 16px"
            @click="noRights"
          >
            立即代采
          </span>
          <router-link
            v-else-if="
              (!isEnterpriseAccount || roleMap.my_product_credit_button) &&
              item.type == '可使用' &&
              item.goodsTypeText === '云信'
            "
            :to="{
              name: 'cloudBillProcess',
              query: {
                cloudProductId: item.goodsId,
                cloudProductName: item.goodsName,
                fundName: item.capitalName,
                fundId: item.capitalId,
                overdueInterestRate: item.annualInterestRate,
                enterpriseId: item.enterpriseQuotaId,
                availableAmountStr: item.availableCredit,
                expireTime: item.expireTime,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              申请开单
            </span>
          </router-link>
          <span
            v-else-if="
              isEnterpriseAccount &&
              !roleMap.my_product_credit_button &&
              item.type == '可使用' &&
              item.goodsTypeText === '云信'
            "
            style="margin-right: 16px"
            @click="noRights"
          >
            申请开单
          </span>
          <!-- v-if="item.goodsType == 99" -->

          <router-link
            v-if="
              (!isEnterpriseAccount || roleMap.my_product_applied_quota) &&
              !item.isCompleted &&
              (item.type == '待申请额度' || item.type == '已到期') &&
              !props.isDetail
            "
            :to="{
              name:
                item.goodsType == 99
                  ? 'productGroupProcessOpen'
                  : 'processOpen',
              query: {
                goodId: item.goodsId,
                goodType: item.goodsType,
                capitalId: item.capitalId,
                isMulti: item.goodsType == 99 ? 1 : 0,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              申请额度
            </span>
          </router-link>

          <span
            v-else-if="
              isEnterpriseAccount &&
              !roleMap.my_product_applied_quota &&
              ['待申请额度', '已到期', '审批中', '已驳回'].includes(item.type)
            "
            style="margin-right: 16px"
            @click="noRights"
          >
            申请额度
          </span>
          <router-link
            v-else-if="
              props.isDetail &&
              props.isCompleted &&
              (item.type == '待申请额度' || item.type == '已到期')
            "
            :to="{
              name:
                item.goodsType == 99
                  ? 'productGroupProcessOpen'
                  : 'processOpen',
              query: {
                goodId: item.goodsId,
                goodType: item.goodsType,
                groupId: props.groupId,
                capitalId: item.capitalId,
                isMulti: item.goodsType == 99 ? 1 : 0,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              申请额度
            </span>
          </router-link>

          <span
            v-if="!props.isDetail && item.goodsType == 99"
            style="margin-right: 16px"
            @click="openBindProductModal(item)"
          >
            查看关联产品
          </span>
          <router-link
            v-if="item.type == '已禁用'"
            :to="{
              name: 'openTheDetails',
              query: { goodId: item.goodsId, customerGoodsId: item.id },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              查看禁用原因
            </span>
          </router-link>
          <router-link
            v-if="item.type == '已冻结'"
            :to="{
              name: 'linesThaw',
              query: {
                goodId: item.goodsId,
                goodType: item.goodsType,
                customerGoodsId: item.id,
                needOpen: 'true',
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              申请解冻
            </span>
          </router-link>
          <router-link
            v-if="
              (!isEnterpriseAccount || roleMap.my_product_activation_button) &&
              item.goodsTypeText !== '动产质押' &&
              (item.type == '额度调整' || item.type == '待激活')
            "
            :to="{
              name: 'processOpen',
              query: {
                goodId: item.goodsId,
                goodType: item.goodsType,
                capitalId: item.capitalId,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              激活额度
            </span>
          </router-link>
          <span
            v-else-if="
              isEnterpriseAccount &&
              !roleMap.my_product_activation_button &&
              item.goodsTypeText !== '动产质押' &&
              (item.type == '额度调整' || item.type == '待激活')
            "
            style="margin-right: 16px"
            @click="noRights"
          >
            激活额度
          </span>
          <!-- <router-link
            v-if="
              (!isEnterpriseAccount ||
                roleMap.my_product_product_details_button) &&
              (item.type == '可融资' ||
                item.type == '可使用' ||
                item.type == '已到期' ||2
                item.type == '已冻结' ||
                item.type == '额度调整')22
            :to="{
              name: 'openTheDetails',
              query: {
                goodId: item.goodsId,
                customerGoodsId: item.id,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              产品详情
            </span>
          </router-link>
          <span
            v-else-if="
              isEnterpriseAccount &&
              !roleMap.my_product_product_details_button &&
              (item.type == '可融资' ||
                item.type == '可使用' ||
                item.type == '已到期' ||
                item.type == '已禁用' ||
                item.type == '额度调整')
            "
            style="margin-right: 16px"
            @click="noRights"
          >
            产品详情
          </span> -->
          <router-link
            v-if="item.type == '开通失败'"
            :to="{
              name: 'processOpen',
              query: {
                goodId: item.goodsId,
                goodType: item.goodsType,
                capitalId: item.capitalId,
              },
            }"
            v-slot="{ navigate }"
          >
            <span @click="navigate" @keypress.enter="navigate" role="link">
              开通进度
            </span>
          </router-link>
        </div>
      </div>
    </div>
    <NoData v-else-if="!loadingType" :noButton="true" title="暂无产品" />
    <a-modal
      v-model:visible="visible"
      title="清除当前产品的开通进度"
      @ok="handleOk"
    >
      <p>确定清除当前产品的开通进度吗？</p>
    </a-modal>
    <a-modal v-model:visible="visibleTwo" title="请输入密码" @ok="handleOkTwo">
      <a-input-password v-model:value="mimavalue" placeholder="请输入密码" />
    </a-modal>

    <a-modal v-model:visible="visibleDemo" title="生成订单" @ok="handleOkDemo">
      <a-radio-group
        style="margin-bottom: 10px"
        v-model:value="scenarioType"
        name="radioGroup"
      >
        <a-radio value="1">一对多</a-radio>
        <a-radio value="2">多对一</a-radio>
      </a-radio-group>
      <a-input
        class="search-name"
        v-model:value="demoPrice"
        placeholder="订单面额"
      />
    </a-modal>
    <DialogAuthority ref="dialogAuthority" />

    <BindProductModal ref="bindProduct" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'UserCenterProduct',
}
</script>

<script setup lang="ts">
import { ref, watch, computed, watchEffect } from 'vue'
import { USER_PRODUCT } from '@/api/index'
import { requestDictMap } from '@/api/common/index'
import NoData from '@/views/user/components/NoData/index.vue'
import DialogAuthority from '@/views/user/components/Dialog/dialogAuthority.vue'
import dayjs from 'dayjs'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import BindProductModal from './BindProductModal.vue'

import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { createVNode } from 'vue'
import { Modal } from 'ant-design-vue'

const store = useStore()
const router = useRouter()

// 方案demo
const demoPrice = ref('')
const scenarioType = ref('1')
const visibleDemo = ref(false)
const groupId = ref('')
const userInfo = computed(() => store.getters['Auth/userInfo'])

const showOrderModal = item => {
  if (item.goodsType === 99) {
    visibleDemo.value = true
    groupId.value = item.groupId
  }
}

const handleOkDemo = async () => {
  if (!demoPrice.value) {
    message.error('请先输入订单面额')
    return
  }
  Modal.confirm({
    title: '提示?',
    icon: createVNode(ExclamationCircleOutlined),
    content: '是否要获取订单？',
    async onOk() {
      try {
        const params = {
          groupId: groupId.value,
          orderAmount: demoPrice.value,
          scenarioType: scenarioType.value,
        }

        const { data: orderRes } = await USER_PRODUCT.getOrderOne(params)
        const orderData = orderRes.data
        const params2 = {
          groupId: groupId.value,
          orderId: orderData.id,
          userId: userInfo.value.userId,
        }

        // await USER_PRODUCT.planCalculateOne(params2)

        demoPrice.value = ''
        groupId.value = ''
        visibleDemo.value = false
        message.success('获取订单成功')
        return true
        // return await new Promise((resolve, reject) => {
        //   setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);
        // });
      } catch {
        return console.log('Oops errors!')
      }
    },
    onCancel() {},
  })
}

const props = defineProps({
  indexNum: {
    type: [String, Number],
    default: '',
  },
  isDetail: Boolean,
  groupId: String,
  isCompleted: Boolean,
})
const loadingType = ref(true)
const visible = ref(false)
const visibleTwo = ref(false)
const mimavalue = ref(null)
const paramsObj = ref({})
const requestDictMapUnitData = ref(null)
const requestDictMapTypeData = ref(null)
const dialogAuthority = ref(null)
const arrData = ref([])

const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
const roleMap = computed<any>(() => store.getters['Role/roleMap'])
const isEnterpriseAccount = computed(
  () => store.getters['Auth/isEnterpriseAccount']
)
const showProductType = computed(() => store.getters['Role/showProductType'])

const bindProduct = ref()

/**
 * 产品组查看详情
 */
const openBindProductModal = item => {
  bindProduct.value.handleOpen(item.groupId, item.isCompleted)
}

const handleFinancing = async (groupId: string) => {
  // 融资校验
  await USER_PRODUCT.financeCheck({ groupId })

  router.push({
    name: 'appliCationsProductGroup',
    query: {
      goodId: groupId,
    },
  })
}

const showModal = item => {
  paramsObj.value = { goodsId: item.goodsId, customerGoodsId: item.id }
  visible.value = true
}

const handleOk = () => {
  visibleTwo.value = true
}

const handleOkTwo = () => {
  if (mimavalue.value === 'nsdsbxx') {
    visibleTwo.value = false
    visible.value = false
    clearData()
    return
  }
  message.warning('请输入密码')
}

const clearData = () => {
  USER_PRODUCT.goodsClear(paramsObj.value).then(() => {})
}

watch(
  () => props.indexNum,
  newData => {
    userGoodsListFun(newData)
  },
  { immediate: false }
)

const userGoodsListFun = async status => {
  const requestObj = {
    status,
    enterpriseType: isCoreEnterpriseAccount.value ? 2 : 1, // 企业类型 1 融资企业 2 核心企业
  }
  // 需求 删减合并状态，接口传参不动，暂时先这样
  if (status == 2) {
    requestObj.status = '2,7'
  } else if (status == 0) {
    requestObj.status = ''
  } else if (status == 7) {
    requestObj.status = 8
  }
  loadingType.value = true

  console.log(props.groupId)

  const { data } = !props.isDetail
    ? await USER_PRODUCT.userGoodsList2(requestObj)
    : await USER_PRODUCT.groupProductDetailList({ groupId: props.groupId })

  // .then(({ data }) => {
  arrData.value = []
  if (data.success) {
    const { data: resData } = data
    console.log(resData)
    for (const item of resData) {
      if (showProductType.value[item.goodsType]) {
        let type = ''
        let appendType = ''
        switch (item.status) {
          case 1:
            type = '待申请额度'
            break
          case 2:
            type = '待激活'
            break
          case 3:
            type = '可融资'
            break
          case 4:
            type = '开通失败'
            break
          case 5:
            type = '已到期'
            break
          case 6:
            type = '已禁用'
            break
          case 7:
            type = '额度调整'
            break
          case 8:
            type = '已冻结'
            break
          case 9:
            type = '待审批'
            break
          case 10:
            type = '已驳回'
            break
        }

        if (type === '可融资' && isCoreEnterpriseAccount.value) {
          type = '可使用'
        }
        if (props.isDetail && item.processStatus) {
          item.processStatus == 1 && (appendType = '审批中')
          item.processStatus == 4 && (appendType = '已驳回')
        }
        const itemLableData = []
        // 如果有标签数据，就进行处理
        if (item.labelList) {
          for (const itemLable of item.labelList) {
            itemLableData.push({
              labelColor: itemLable.labelColor,
              name: itemLable.name,
              id: itemLable.id,
            })
          }
        }
        let loadTermUnitData = ''
        if (item.loadTermUnit) {
          loadTermUnitData = requestDictMapUnitData.value.filter(
            // 过滤出当前的最长期限单位
            items => items.key == item.loadTermUnit
          )[0].value
        }
        let goodsTypeData = ''
        if (item.goodsType) {
          goodsTypeData = requestDictMapTypeData.value.filter(
            // 过滤出当前的产品类型
            items => items.key == item.goodsType
          )[0].value
        }
        arrData.value.push({
          ...item,
          type: type,
          appendType,
          goodsTypeText: goodsTypeData,
          // 开通中、已关闭 start
          itemLableDatas: itemLableData,
          createTime: dayjs(item.createTime).format('YYYY.MM.DD'),
          loadTermUnit: loadTermUnitData,
          // 开通中、已关闭 end
          // 已到期、已禁用、已开通、已开通未激活 start
          availableCredit: item.availableCredit ? item.availableCredit : 0,
          expireTime: dayjs(item.expireTime).format('YYYY.MM.DD'),
          // 已到期、已禁用、已开通、已开通未激活 end
        })
      }
    }
    if (arrData.value) {
      setTimeout(() => {
        loadingType.value = false
      }, 300)
    }
  }
  // })
}

// 最长期限单位字典
requestDictMap('goods_load_term_unit').then(res => {
  const resData = res.data
  if (resData.code == 200) {
    // 处理字典数据
    const resList = []
    for (const item of resData.data) {
      resList.push({
        key: item.dictKey,
        value: item.dictValue,
        id: item.id,
      })
    }
    requestDictMapUnitData.value = resList
  }
})

// 产品类型字典
requestDictMap('goods_type').then(res => {
  const resData = res.data
  if (resData.code == 200) {
    // 处理字典数据
    const resList = []
    for (const item of resData.data) {
      resList.push({
        key: item.dictKey,
        value: item.dictKey == 99 ? '订单融资' : item.dictValue,
        id: item.id,
      })
    }
    requestDictMapTypeData.value = resList
  }
})

const noRights = () => {
  dialogAuthority.value.handleOpen()
}

watchEffect(() => {
  if (
    isLogined.value &&
    requestDictMapUnitData.value &&
    requestDictMapTypeData.value &&
    showProductType.value.hasData
  ) {
    userGoodsListFun('')
  }
})
</script>

<style lang="scss" scoped>
.user-product {
  box-sizing: border-box;

  .loading-box {
    width: 100%;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .empty-data-container {
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .user-product-flexbox {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;

    .user-product-childrenbox {
      width: 532px;
      height: 296px;
      border-radius: 16px;
      border: 1px solid #e1e4e8;
      overflow: hidden;
      padding: 24px;
      box-sizing: border-box;
      position: relative;
      margin-top: 24px;
      cursor: context-menu;

      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        width: 79px;
        height: 76px;
        top: -25px;
        left: -28px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.14;
        border-radius: 150px;
        filter: blur(13px);
      }
      &::after {
        content: '';
        display: inline-block;
        position: absolute;
        width: 210px;
        height: 205px;
        bottom: -74px;
        right: -49px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.14;
        border-radius: 150px;
        filter: blur(13px);
      }

      .user-product-childrenbox-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .user-product-childrenbox-title-left {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          overflow: hidden;

          span {
            display: inline-block;
          }

          & > span:first-child {
            width: 28px;
            height: 28px;
            margin-right: 8px;

            img {
              width: 100%;
            }
          }

          & > span:nth-child(2) {
            height: 28px;
            font-size: 20px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            font-weight: 400;
            color: #0a1f44;
            margin-right: 8px;
            text-align: left;
            line-height: 28px;
          }

          & > span:last-child {
            background: #ebf5ff;
            border-radius: 13px;
            white-space: nowrap;
            margin-right: 8px;

            span {
              margin: 4px 12px;
              box-sizing: border-box;
              font-size: 14px;

              font-weight: 400;
              color: #0d55cf;
            }
          }
        }
        .user-product-childrenbox-title-right {
          height: 20px;
          font-size: 14px;
          white-space: nowrap;
          font-weight: 400;
          line-height: 20px;

          .c8a94a6 {
            color: #8a94a6;
          }
          .c00865a {
            color: #00865a;
          }
          .c0d55cf {
            color: #0d55cf;
          }
        }
      }

      .user-product-childrenbox-content-num {
        flex-direction: column;
        display: flex;
        margin-bottom: 20px;

        .limit-lable {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          & > span:last-child {
            height: 20px;
            font-size: 14px;

            font-weight: 400;
            color: #f07300;
            line-height: 20px;
            margin-left: 1px;
          }

          & > span:first-child {
            font-size: 14px;

            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
          }

          #my-svg-icons {
            margin-left: 16px;
          }
        }

        & > span:nth-child(2) {
          margin-bottom: 12px;

          :deep(.ant-statistic-content-value > span) {
            font-size: 48px;
            @include family-CoreSansD65Heavy;
            // color: #031222;
            line-height: 59px;
          }
          .moneyNum-in-active {
            :deep(.ant-statistic-content-value) {
              color: #8a94a6;
            }
          }
          .moneyNum-decoration {
            :deep(.ant-statistic-content-value) {
              display: flex;
              text-decoration: line-through;
            }
          }
          .product-group-size {
            :deep(.ant-statistic-content-value) {
              font-size: 56px;
              font-weight: bold;
            }
          }
          .moneyNum-disables {
            :deep(.ant-statistic-content-value) {
              display: flex;
              text-decoration: line-through;
              color: #8a94a6;
            }
          }
        }

        & > span:nth-child(3) {
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #8a94a6;
          line-height: 20px;
          margin-bottom: 16px;
        }

        & > .text-item {
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #8a94a6;
          line-height: 20px;
          margin-bottom: 16px;
        }

        .text-disables {
          text-decoration: line-through;
        }

        & > span:last-child {
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #8a94a6;
          line-height: 20px;
        }
      }

      .user-product-childrenbox-content-introduce {
        margin-bottom: 20px;

        .products-item-tag {
          width: 100%;
          margin-top: 12px;
          box-sizing: border-box;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          text-align: center;

          .tags {
            display: inline-block;
            height: 28px;
            border-radius: 14px;
            border: 1px solid;
            font-size: 13px;
            @include family-PingFangSC-Medium-SFProText;
            font-weight: 500;
            line-height: 25px;
            margin-right: 12px;
            padding: 0 9px;
          }

          // @mixin tags($border, $color) {
          //   display: inline-block;
          //   width: 66px;
          //   height: 28px;
          //   border-radius: 14px;
          //   border: 1px solid;
          //   border-color: $border;
          //   font-size: 13px;
          //   @include family-PingFangSC-Medium-SFProText;
          //   font-weight: 500;
          //   color: $color;
          //   line-height: 25px;
          //   margin-right: 12px;
          // }

          // & > span:first-child {
          //   @include tags(#0bb07b, #00865a);
          // }
          // & > span:nth-child(2) {
          //   @include tags(#0c66ff, #0d55cf);
          // }
          // & > span:last-child {
          //   @include tags(#ffad0d, #f07300);
          // }
        }

        .products-item-emphasis {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12px;

          .products-item-emphasis-box {
            // width: 126px;
            height: 74px;
            border-radius: 8px;
            border: 1px solid #e1e4e8;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .products-item-emphasis-children-top {
              & > span:first-child {
                width: 47px;
                height: 34px;
                font-size: 28px;
                @include family-CoreSansD65Heavy;
                color: #dd2727;
                line-height: 35px;
              }
              & > span:last-child {
                width: 14px;
                height: 20px;
                font-size: 14px;
                @include family-PingFangSC-Semibold;
                font-weight: 600;
                color: #dd2727;
                line-height: 20px;
              }
            }

            .products-item-emphasis-children-bottom {
              width: 126px;
              height: 20px;
              font-size: 14px;

              font-weight: 400;
              color: #8a94a6;
              line-height: 20px;
              text-align: center;
            }
          }
        }

        .products-item-time {
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #8a94a6;
          line-height: 20px;
          margin-top: 14px;
        }
      }

      .user-product-childrenbox-menu {
        &.fixedBtn {
          position: absolute;
          bottom: 24px;
          right: 24px;
          z-index: 1000;
        }
        a {
          margin-right: 16px;

          &:last-child {
            margin-right: 0;
          }
        }
        span {
          display: inline-block;
          height: 40px;
          border-radius: 4px;
          border: 1px solid #e1e4e8;
          line-height: 19px;
          padding: 10px 20px;
          box-sizing: border-box;
          cursor: pointer;

          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #0a1f44;
          text-align: center;
          transition: all 0.3s;

          &:hover {
            border: 1px solid #0c66ff;
            color: #0d55cf;
          }
        }
      }
    }
  }
}
</style>
