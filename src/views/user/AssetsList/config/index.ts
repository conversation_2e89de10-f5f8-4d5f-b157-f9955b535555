// 凭证状态
const proofStatusMap: any = {
  0: {
    name: '暂无',
    color: '#84868D',
    backgroundColor: '#F5F5F5',
    borderColor: '#D9D9D9',
  },
  1: {
    name: '待确权',
    color: '#697CFF',
    backgroundColor: '#fff',
    borderColor: '#697CFF',
  },
  2: {
    name: '已作废',
    color: '#7d7d7d',
    backgroundColor: '#fff',
    borderColor: '#979797',
  },
  3: {
    name: '可使用',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
  4: {
    name: '已使用',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
  5: {
    name: '到期未结清',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
  6: {
    name: '已结清',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
}

const coreProofStatusMap = {
  0: {
    name: '暂无',
    color: '#84868D',
    backgroundColor: '#F5F5F5',
    borderColor: '#D9D9D9',
  },
  1: {
    name: '待确权',
    color: '#697CFF',
    backgroundColor: '#fff',
    borderColor: '#697CFF',
  },
  2: {
    name: '已作废',
    color: '#7d7d7d',
    backgroundColor: '#fff',
    borderColor: '#979797',
  },
  3: {
    name: '可使用',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
  4: {
    name: '已使用',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
  5: {
    name: '到期未结清',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
  6: {
    name: '已结清',
    color: '#1AC475',
    backgroundColor: '#fff',
    borderColor: '#1AC475',
  },
}

export { proofStatusMap }
