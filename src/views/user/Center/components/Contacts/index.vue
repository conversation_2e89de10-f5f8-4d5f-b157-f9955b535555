<template>
  <div class="contacts-container">
    <div class="contacts-subtitle">
      <img
        class="contacts-safe-icon"
        src="@/assets/images/user/user_center_safe.png"
        alt
      />
      <div class="contacts-subtitle-describe">
        <span>请确保信息真实有效，已为您开启</span>
        <span>隐私保护</span>
      </div>
      <MySvgIcon
        icon-class="icon-youjiantou"
        style="fill: rgb(84, 84, 84); font-size: 16px; cursor: pointer"
      />
    </div>
    <div class="contacts-title">联系人信息</div>
    <div class="contacts-list">
      <BasicsCard
        v-for="item in listData"
        :key="item.id"
        width="252px"
        height="110px"
        @edit="handleEdit"
        @delete="handleDelete"
      >
        <div class="contacts-slot">
          <div class="contacts-slot-top">
            <span>{{ item.contactsName }}</span>
            <span>{{ item.contactsType }}</span>
          </div>
          <div class="contacts-slot-bottom">
            <span>{{ item.contactsPhone }}</span>
          </div>
        </div>
      </BasicsCard>
      <AddCard
        @create="handleCreate"
        width="252px"
        height="110px"
        title-name="添加联系人"
      >
      </AddCard>
    </div>

    <GlobalDialog
      :title="dialogTitle"
      width="432px"
      ref="formDialog"
      :enableFullscreen="false"
    >
      <a-form :model="searchParams" class="form" @finish="onFinish">
        <a-form-item label="" :wrapper-col="{ span: 24 }">
          <a-input v-model:value="searchParams.name" placeholder="请输入姓名" />
        </a-form-item>

        <a-form-item
          label=""
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
        >
          <a-select
            v-model:value="searchParams.type"
            placeholder="请选择类型"
            :options="typeOptions"
          />
        </a-form-item>
        <a-form-item
          label=""
          name="phoneNum"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
        >
          <a-input
            v-model:value="searchParams.phoneNum"
            placeholder="请输入手机号"
          />
        </a-form-item>
        <a-form-item>
          <a-button class="btn-cancel" @click="handleClose">取 消</a-button>
          <a-button type="primary" class="btn-submit" html-type="submit"
            >确 定</a-button
          >
        </a-form-item>
      </a-form>
    </GlobalDialog>
  </div>
</template>

<script>
export default {
  name: 'ContactsPage',
}
</script>

<script setup>
import { ref, reactive } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { createVNode } from 'vue'
import { Modal } from 'ant-design-vue'
import AddCard from '@/components/BaseCard/addCard/index.vue'
import BasicsCard from '@/components/BaseCard/basicsCard/index.vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
// import { getFinancingList, getContactOption } from '@/api/user/center/index'
import { USER_CENTER } from '@/api/index'

const formDialog = ref(null)

const searchParams = reactive({
  type: null,
  name: '',
  phoneNum: '',
})
const typeOptions = ref([
  {
    value: '1',
    label: '1',
  },
  {
    value: '2',
    label: '2',
  },
])
// 联系人信息
const listData = ref([])

const dialogTitle = ref('添加联系人')
// 所有联系人的信息
USER_CENTER.getFinancingList()
  .then(res => {
    const resData = res.data
    if (resData.code === 200) {
      const { data } = resData
      data.forEach(item => {
        listData.value.push(item)
      })
    }
  })
  .catch(error => console.log(error))

// 得到联系人类型
// http.getContactOption().then(res => {
//   const resData = res.data
//   if(resData.code === 200) {
//     const { data } = resData

//   }
// })
// //关闭弹窗
const handleClose = () => {
  formDialog.value.handleClose()
}
const handleEdit = () => {
  formDialog.value.handleOpen()
  dialogTitle.value = '编辑联系人'
}
const handleCreate = () => {
  formDialog.value.handleOpen()
  dialogTitle.value = '添加联系人'
}

// console.log(getContactOption())

// const getContactTypeConfig = () => {
//   getContactOption().then(res => {
//     console.log(res, 'res')
//   }).catch(err => console.log(err))

// }

const handleDelete = () => {
  Modal.confirm({
    title: '是否删除该联系人？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {},
    onCancel() {
      console.log('cancel')
    },
    class: 'test',
  })
}

const handleOpen = () => {
  formDialog.value.handleOpen()
}
const onFinish = value => {
  console.log(value, 'value')
}
defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.contacts-container {
  min-height: 531px;
  .contacts-subtitle {
    display: flex;
    align-items: center;
    .contacts-safe-icon {
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
    .contacts-subtitle-describe {
      font-size: 14px;
      font-weight: 400;
      height: 20px;
      line-height: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      & span:first-child {
        color: #53627c;
      }
      & span:last-child {
        color: #031222;
        cursor: pointer;
        font-weight: bold;
      }
    }
  }
  .contacts-title {
    margin-top: 24px;
    height: 28px;
    font-size: 20px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #0a1f44;
    line-height: 28px;
  }
  .contacts-list {
    display: flex;
    align-items: center;
    flex-flow: row wrap;
    & :deep(.base-card-item:nth-of-type(4n)) {
      margin-right: 0 !important;
    }
    .contacts-slot {
      .contacts-slot-top {
        display: flex;
        align-items: center;
        & span:first-child {
          height: 28px;
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #0a1f44;
          line-height: 28px;
        }
        & span:last-child {
          margin-left: 8px;
          display: inline-block;
          padding: 4px 10px;
          line-height: 24px;
          text-align: center;
          border-radius: 24px;
          color: #0d55cf;
          font-size: 11px;
          font-weight: 600;
          background-color: #ebf5ff;
        }
      }
      .contacts-slot-bottom {
        margin-top: 12px;
        height: 24px;
        font-size: 16px;
        font-family: SFProText-Semibold, SFProText;
        font-weight: 600;
        color: #53627c;
        line-height: 24px;
      }
    }
  }

  .contacts-dialog-bg {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.4);
  }
  .contacts-dialog {
    width: 432px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    border-radius: 16px;
    background-color: #ffffff;
    .contacts-dialog-main {
      width: 100%;
      height: 100%;
      position: relative;
      padding: 24px 32px 32px;
      .close-icon {
        position: absolute;
        right: 12px;
        top: 12px;
      }
      .contacts-dialog-title {
        margin-bottom: 24px;
        height: 28px;
        font-size: 20px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #0a1f44;
        line-height: 28px;
      }
      .contacts-box {
        margin-bottom: 12px;
      }
      .contacts-phone {
        margin-bottom: 24px;
      }
      .contacts-btn {
        text-align: right;
        & .ant-btn {
          width: 73px;
          height: 40px;
          border-radius: 100px;
          &:first-child {
            margin-right: 8px;
            color: #53627c;
            font-size: 500;
          }
          &:last-child {
            color: #ffffff;
            font-size: 500;
          }
        }
      }
    }
  }
}
.form {
  .btn-cancel {
    margin-right: 8px;
  }
  .btn-submit,
  .btn-cancel {
    width: 73px;
    height: 40px;
    border-radius: 100px;
  }
  & :deep(.ant-form-item:last-child) {
    margin-bottom: 0 !important;
    text-align: right;
  }
}
</style>
