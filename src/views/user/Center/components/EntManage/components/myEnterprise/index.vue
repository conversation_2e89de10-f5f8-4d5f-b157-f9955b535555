<template>
  <a-spin :spinning="loading">
    <div class="my-enterprise-container">
      <template v-if="!loading">
        <template v-if="isEnterpriseAccount">
          <!-- 企业账户显示我的企业 -->
          <div class="enterprise-info-container">
            <div class="ent-item-container">
              <div class="avatar-container">
                <img :src="enterpriseData.corporateAvatar" alt="" />
              </div>
              <div class="name-wrapper">
                <div class="name-container">
                  <span class="name">{{ enterpriseData.name }}</span>
                  <CommonTag
                    padding="4px 10px"
                    color="#53627C"
                    borderColor="#fff"
                    name="我的企业"
                    backgroundColor="#fff"
                  />
                </div>
                <span class="code">{{ enterpriseData.creditCode }}</span>
              </div>
            </div>
            <div class="right-info-container">
              <div class="button-container">
                <!-- <a-upload
                  accept=".jpg,.jpeg,.png"
                  :fileList="[]"
                  :before-upload="handleAvatarBeforeUpload"
                >
                  <n-button
                    class="border black"
                    size="tiny"
                    round
                    :bordered="false"
                    >更换logo</n-button
                  >
                </a-upload> -->
                <n-button
                  class="border black"
                  size="tiny"
                  round
                  :bordered="false"
                  @click="handleViewDetail"
                  >查看详情</n-button
                >
                <n-button
                  v-if="!isEnterpriseAccount"
                  class="border black"
                  size="tiny"
                  round
                  :bordered="false"
                  @click="handleEnterEnterprise"
                  >进入企业</n-button
                >
              </div>
              <span class="open-date">
                {{ enterpriseData.createTime || '-- ' }}开通</span
              >
            </div>
          </div>
        </template>
        <template v-else-if="enterpriseData.openStatus !== 0">
          <!-- 个人账户开通企业 -->
          <div class="enterprise-info-container">
            <div class="ent-item-container">
              <div class="avatar-container">
                <img :src="enterpriseData.corporateAvatar" alt="" />
              </div>
              <div class="name-wrapper">
                <div class="name-container">
                  <span class="name">{{ enterpriseData.name }}</span>
                </div>
                <span class="code">{{ enterpriseData.creditCode }}</span>
              </div>
            </div>
            <div class="right-info-container" style="align-items: center">
              <span class="status">
                {{ enterpriseData.openStatusMap[enterpriseData.openStatus] }}
              </span>
              <div class="button-container">
                <n-button
                  v-if="[1, 2].includes(enterpriseData.openStatus)"
                  class="blue border"
                  style="
                    font-size: 12px;
                    font-weight: 500;
                    color: #8a94a6;
                    height: auto;
                    padding: 4px 10px;
                    background-color: white;
                    margin-right: 12px;
                  "
                  round
                  :bordered="false"
                  @click="handleViewOpenEnterpriseDetail"
                  >查看详情</n-button
                >
                <n-button
                  class="blue border"
                  style="
                    font-size: 12px;
                    font-weight: 500;
                    color: #8a94a6;
                    height: auto;
                    padding: 4px 10px;
                    background-color: white;
                  "
                  round
                  :bordered="false"
                  @click="handleCancelOpenEnterprise"
                  >取消开通</n-button
                >
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="open-enterprise-container" @click="handleOpenEnterprise">
            <span class="btn-desc"
              ><loading-outlined
                v-if="openEnterpriseLoading"
                style="margin-right: 4px"
              />开通企业</span
            >
          </div>
        </template>
      </template>
    </div>
  </a-spin>
  <ConfirmOpenEntDialog
    ref="confirmOpenEntDialogRef"
    @updateRealNameStatus="handleUpdateRealNameStatus"
    @refresh="handleRefresh"
  />
  <ViewVerifyDataDialog ref="viewVerifyDataDialogRef" />
  <SelectOpenEntTypeDialog ref="selectOpenEntTypeDialog" />
  <ConfirmDialog
    ref="confirmDialog"
    title="确认取消开通"
    content="取消开通企业后，您的企业将不能使用，确定取消吗？"
    cancelBtnName="返回"
    confirmBtnName="确认取消"
    :confirmBtnLoading="cancelOpenEnterpriseBtnLoading"
    :confirmBtnCloseDialog="false"
    @confirm="handleCancelOpenEnterpriseSubmit"
    @refresh="handleRefresh"
  />
</template>

<script lang="ts">
export default {
  name: 'UserCenterEnterpriseManageMyEnterpriseIndex',
}
</script>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import {
  requestEnterpriseDetail,
  requestEnterpriseOpenStatusDetail,
} from '@/api/user/center/entManage'
import { NButton } from 'naive-ui'
import CommonTag from '@/components/CommonTag/index.vue'
import ConfirmOpenEntDialog from '../confirmOpenEntDialog/index.vue'
import ViewVerifyDataDialog from '../viewVerifyDataDialog/index.vue'
import type { UserInfo } from '@/store/modules/auth'
import { message } from 'ant-design-vue'
import { fileUpload } from '@/api/common'
import {
  requestChangeEnterpriseLogo,
  requestEnterpriseCancelOpen,
} from '@/api/user/center/entManage'
import {
  requestEnterpriseActiveUpdateRealStatus,
  requestReadPreEnterpriseName,
} from '@/api/user/auth'
import { LoadingOutlined } from '@ant-design/icons-vue'
import SelectOpenEntTypeDialog from '../selectOpenEntTypeDialog/index.vue'
import ConfirmDialog from '@/businessComponents/ConfirmDialog/index.vue'
import { removeLocal } from '@/utils/localStorage'

const store = useStore()
const router = useRouter()
const confirmOpenEntDialogRef = ref<any>(null)
const viewVerifyDataDialogRef = ref<any>(null)
const selectOpenEntTypeDialog = ref<any>(null)
const confirmDialog = ref<any>(null)
const isEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isEnterpriseAccount']
)
const userInfo = computed<UserInfo>(() => store.getters['Auth/userInfo'])
const loading = ref<boolean>(true)
const openEnterpriseLoading = ref<boolean>(false)
const cancelOpenEnterpriseBtnLoading = ref<boolean>(false)

const enterpriseData = ref<any>({
  corporateAvatar: require('@/assets/images/user/avatar_default_black.svg'),
  name: '',
  creditCode: '',
  createTime: '',
  // 开通的企业类型 【2-融资企业，3-核心企业】
  entAuthType: undefined,
  // 开通状态 【0-未开通，1-开通中, 2-已开通, 3-开通失败, 4-驳回】
  openStatus: 0,
  openStatusMap: {
    1: '开通中...',
    2: '已开通',
    3: '开通失败',
    4: '驳回',
  },
  id: '',
  // 取消开通时使用的参数
  processId: undefined,
})
const emit = defineEmits(['enterEnterprise', 'refresh'])

// 获取我的企业详情
const initMyEnterpriseData = () => {
  if (isEnterpriseAccount.value) {
    // 显示我的企业
    requestEnterpriseDetail()
      .then(({ data }) => {
        if (data.success) {
          data = data.data || {}
          data.corporateAvatar =
            data.corporateAvatar ||
            require('@/assets/images/user/avatar_default_black.svg')
          enterpriseData.value = data
          loading.value = false
        }
      })
      .catch(() => {})
  } else {
    // 开通企业状态
    requestEnterpriseOpenStatusDetail()
      .then(({ data }) => {
        data = data.data
        if (data == null) {
          // 没有开通企业
          enterpriseData.value.openStatus = 0
        } else {
          // 正在开通企业
          data = data.customerInfo || {}
          data.logo =
            data.logo ||
            require('@/assets/images/user/avatar_default_black.svg')
          enterpriseData.value.processId = data.processId
          enterpriseData.value.name = data.corpName
          enterpriseData.value.creditCode = data.businessLicenceNumber
          enterpriseData.value.corporateAvatar = data.logo
          enterpriseData.value.openStatus = data.status
          enterpriseData.value.entAuthType = data.entAuthType
          enterpriseData.value.id = data.id
        }
        loading.value = false
      })
      .catch(() => {})
  }
}

// 重载企业信息
const handleRefresh = () => {
  initMyEnterpriseData()
  emit('refresh')
}

// 开通企业 - old
// const handleOpenEnterprise = () => {
//   openEnterpriseLoading.value = true
//   requestReadPreEnterpriseName()
//     .then(({ data }) => {
//       openEnterpriseLoading.value = false
//       if (data.success) {
//         data = data.data || {}
//         const preAuthData = {
//           url: data.license,
//           enterpriseName: data.companyName,
//           creditCode: data.creditCode,
//           legalPersonName: data.legalPersonName,
//         }
//         // 含有预实名企业信息，回显信息开始实名
//         confirmOpenEntDialogRef.value.handleOpen(preAuthData)
//       } else {
//         // 无法获取预企业实名信息等，从初始状态开始实名
//         confirmOpenEntDialogRef.value.handleOpen()
//       }
//     })
//     .catch(({ msg, hideMsgFunc }) => {
//       openEnterpriseLoading.value = false
//       hideMsgFunc && hideMsgFunc()
//       // 无法获取预企业实名信息等，从初始状态开始实名
//       confirmOpenEntDialogRef.value.handleOpen()
//     })
// }
// 开通企业 - 新
const handleOpenEnterprise = () => {
  selectOpenEntTypeDialog.value.handleOpen()
}

// 开通企业中 查看详情按钮点击事件
const handleViewOpenEnterpriseDetail = () => {
  if (enterpriseData.value.entAuthType == 3) {
    // 核心企业
    router.push({
      path: '/user/enterprise/open/core',
      query: { id: enterpriseData.value.id },
    })
  } else if (enterpriseData.value.entAuthType == 2) {
    // 融资企业
    router.push({
      path: '/user/enterprise/open/financing',
      query: { id: enterpriseData.value.id },
    })
  } else {
    message.error('现在无法查看，请稍后再试')
  }
}

// 点击取消开通企业按钮事件
const handleCancelOpenEnterprise = () => {
  confirmDialog.value.handleOpen()
}

// 点击取消开通企业按钮事件后二次确认后的回调
const handleCancelOpenEnterpriseSubmit = () => {
  cancelOpenEnterpriseBtnLoading.value = true
  const requestObj = {
    processId: enterpriseData.value.processId,
  }
  requestEnterpriseCancelOpen(requestObj)
    .then(({ data }) => {
      cancelOpenEnterpriseBtnLoading.value = false
      message.success('取消开通当前企业成功')
      confirmDialog.value.handleClose()
      handleRefresh()
    })
    .catch(() => {
      cancelOpenEnterpriseBtnLoading.value = false
    })
}

// 同步实名状态 - 验证实名
const handleUpdateRealNameStatus = (enterpriseName: string | undefined) => {
  const requestObj = {
    companyName: enterpriseName,
  }
  requestEnterpriseActiveUpdateRealStatus(requestObj)
    .then(({ data }: { data: any }) => {
      if (data.success) {
        // 已完成实名
        initMyEnterpriseData()
      } else {
        // 未实名
      }
    })
    .catch(() => {})
}

// 更换 Logo
const handleAvatarBeforeUpload = (file: any) => {
  let passFlag = true

  // 检查文件格式
  const isAcceptFileTyle = ['image/jpg', 'image/jpeg', 'image/png'].includes(
    file.type
  )
  if (!isAcceptFileTyle) {
    message.error('不支持的文件格式! 请上传 jpg,png 图片格式')
    passFlag = false
  }

  // 检查文件大小
  const isLimit20M = file.size / 1024 / 1024 < 2
  if (!isLimit20M) {
    message.error('图片或文件过大! 请上传小于2M的图片或文件')
    passFlag = false
  }

  if (passFlag) {
    // 上传文件
    const uploadFileFormData = new FormData()
    uploadFileFormData.append('file', file)

    fileUpload(uploadFileFormData)
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          const requestObj = {
            typeId: enterpriseData.value.id,
            corporateAvatar: data.link,
          }
          requestChangeEnterpriseLogo(requestObj)
            .then(({ data }) => {
              if (data.success) {
                message.success('更换头像成功')
                store.dispatch('Auth/fetchUserInfo')
                initMyEnterpriseData()
              } else {
                message.success(data.msg || '更换头像失败')
              }
            })
            .catch(() => {})
        } else {
          message.success(data.msg || '上传失败')
        }
      })
      .catch(() => {})
  }

  return false
}

// 查看企业详情
const handleViewDetail = () => {
  removeLocal('stepId')
  viewVerifyDataDialogRef.value.handleOpen()
}

// 进入企业
const handleEnterEnterprise = () => {
  emit('enterEnterprise', enterpriseData.value.id)
}

initMyEnterpriseData()
</script>

<style lang="scss" scoped>
.my-enterprise-container {
  height: 80px;
  position: relative;
  background-color: #ebf5ff;
  background-image: url('@/assets/images/user/center/my_enterprise_bg.svg');
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;

  .open-enterprise-container {
    height: 100%;
    border-radius: 6px;
    cursor: pointer;

    .btn-desc {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 20px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #0d55cf;
    }
  }

  .enterprise-info-container {
    height: 100%;
    padding: 16px 20px 16px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ent-item-container {
      height: 100%;
      display: flex;
      align-items: center;

      .avatar-container {
        width: 48px;
        height: 48px;
        margin-right: 12px;
        border-radius: 50%;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: contain;
        }
      }

      .name-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;

        .name-container {
          display: flex;
          align-items: center;

          .name {
            margin-right: 8px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #0a1f44;
            line-height: 24px;
          }
        }

        .code {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8a94a6;
          line-height: 20px;
        }
      }

      .tag-container {
        margin-right: 20px;
        min-width: 80px;
        text-align: left;
      }
    }

    .right-info-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: space-between;

      .button-container {
        > * {
          margin-right: 12px;
          display: inline-block;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .open-date {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8a94a6;
        line-height: 1;
        text-align: right;
      }

      .status {
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #0d55cf;
        line-height: 24px;
      }
    }
  }
}
</style>
