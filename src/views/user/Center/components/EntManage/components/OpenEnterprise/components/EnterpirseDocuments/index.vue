<template>
  <div class="doc-container">
    <!-- 企业证件 -->
    <div class="ent-container">
      <div class="title-container">
        <span class="title">企业证件</span>
      </div>
      <div class="form-container">
        <a-form
          class="form"
          ref="enterpriseFormRef"
          :model="enterpriseData.formState"
          :rules="entpriseRules"
          :colon="false"
          autocomplete="off"
        >
          <a-form-item label="营业执照" name="cert">
            <div class="upload-container">
              <div
                class="img-preview"
                :style="{
                  backgroundImage: enterpriseData.uploadData.cert.url
                    ? `url(${enterpriseData.uploadData.cert.url})`
                    : `none`,
                }"
              />
              <div class="preview-wrapper">
                <div
                  class="preview-btn"
                  v-if="
                    enterpriseData.uploadData.cert.loading ||
                    enterpriseData.uploadData.cert.url !== undefined
                  "
                  @click="
                    handleViewImg(enterpriseData.uploadData.cert.url || '')
                  "
                >
                  <loading-outlined
                    v-if="enterpriseData.uploadData.cert.loading"
                  />
                  <MySvgIcon
                    v-else
                    icon-class="icon-search"
                    style="font-size: 24px; fill: #fff; display: inline-flex"
                  />
                </div>
              </div>
              <a-upload
                class="upload-wrapper"
                accept=".jpg,.jpeg,.png"
                :fileList="[]"
                :before-upload="(file: any) => beforeUpload(file, 'ent', 'cert')"
              >
                <div class="upload-btn" ref="uploadBtnRef">
                  <span>点击上传</span>
                </div>
              </a-upload>
            </div>
          </a-form-item>
          <div class="upload-desc">
            请上传凭证原件的<span>彩色照片或扫描件</span>，支持
            jpg/jpeg/png，最大15M
          </div>
          <a-form-item label="企业logo" name="logo">
            <div class="upload-container">
              <div
                class="img-preview"
                :style="{
                  backgroundImage: enterpriseData.uploadData.logo.url
                    ? `url(${enterpriseData.uploadData.logo.url})`
                    : 'none',
                }"
              />
              <div class="preview-wrapper">
                <div
                  class="preview-btn"
                  v-if="
                    enterpriseData.uploadData.logo.loading ||
                    enterpriseData.uploadData.logo.url !== undefined
                  "
                  @click="
                    handleViewImg(enterpriseData.uploadData.logo.url || '')
                  "
                >
                  <loading-outlined
                    v-if="enterpriseData.uploadData.logo.loading"
                  />
                  <MySvgIcon
                    v-else
                    icon-class="icon-search"
                    style="font-size: 24px; fill: #fff; display: inline-flex"
                  />
                </div>
              </div>
              <a-upload
                class="upload-wrapper"
                accept=".jpg,.jpeg,.png"
                :fileList="[]"
                :before-upload="(file: any) => beforeUpload(file, 'ent', 'logo')"
              >
                <div class="upload-btn" ref="uploadBtnRef">
                  <span>点击上传</span>
                </div>
              </a-upload>
            </div>
          </a-form-item>
          <div class="upload-desc">
            请上传凭证原件的<span>彩色照片或扫描件</span>，支持
            jpg/jpeg/png，最大15M
          </div>
          <div class="tip-container">
            <img src="@/assets/icons/icon_tip.svg" alt="" />
            <span class="tip-content"
              >以下信息自动识别，需仔细核对，如识别有误，请修正。</span
            >
          </div>
          <a-form-item label="企业名称" name="name">
            <a-input
              @change="inputFilter($event, 'name')"
              placeholder="请填写企业名称"
              v-model:value.trim="enterpriseData.formState.name"
            />
          </a-form-item>
          <a-form-item label="统一社会代码" name="code">
            <a-input
              @change="inputFilter($event, 'code')"
              placeholder="请填写统一社会信用代码"
              v-model:value.trim="enterpriseData.formState.code"
            />
          </a-form-item>
          <a-form-item label="法定代表人" name="legalPerson">
            <a-input
              @change="inputFilter($event, 'legalPerson')"
              placeholder="请填写法定代表人姓名"
              v-model:value.trim="enterpriseData.formState.legalPerson"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>
    <!-- 个人证件 -->
    <div class="personal-container">
      <div class="title-container">
        <span class="title">个人证件</span>
      </div>
      <div class="form-container">
        <a-form
          class="form"
          ref="personalFormRef"
          :model="personalData.formState"
          :rules="personalRules"
          :colon="false"
          autocomplete="off"
        >
          <a-form-item label="身份证类型" name="type">
            <LabelBar
              :labelList="['我是经办人', '我是法人']"
              :state="personalData.labelIndex"
              @switch="handlePersonalTypeSwitch"
            />
          </a-form-item>
          <template v-if="personalData.labelIndex === 0">
            <a-form-item label="法人身份证人像面" name="cardNumber">
              <div class="upload-container">
                <div
                  class="img-preview"
                  :style="{
                    backgroundImage: personalData.uploadData.cardNumber.url
                      ? `url(${personalData.uploadData.cardNumber.url})`
                      : `none`,
                  }"
                />
                <div class="preview-wrapper">
                  <div
                    class="preview-btn"
                    v-if="
                      personalData.uploadData.cardNumber.loading ||
                      personalData.uploadData.cardNumber.url !== undefined
                    "
                    @click="
                      handleViewImg(
                        personalData.uploadData.cardNumber.url || ''
                      )
                    "
                  >
                    <loading-outlined
                      v-if="personalData.uploadData.cardNumber.loading"
                    />
                    <MySvgIcon
                      v-else
                      icon-class="icon-search"
                      style="font-size: 24px; fill: #fff; display: inline-flex"
                    />
                  </div>
                </div>
                <a-upload
                  class="upload-wrapper"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :fileList="[]"
                  :before-upload="(file: any) => beforeUpload(file, 'person', 'cardNumber')"
                >
                  <div class="upload-btn" ref="uploadBtnRef">
                    <span>点击上传</span>
                  </div>
                </a-upload>
              </div>
            </a-form-item>
            <div class="upload-desc">
              请上传凭证原件的<span>彩色照片或扫描件</span>，支持
              jpg/jpeg/png，最大15M
            </div>
            <a-form-item label="法人身份证国徽面" name="cardPeriod">
              <div class="upload-container">
                <div
                  class="img-preview"
                  :style="{
                    backgroundImage: personalData.uploadData.cardPeriod.url
                      ? `url(${personalData.uploadData.cardPeriod.url})`
                      : 'none',
                  }"
                />
                <div class="preview-wrapper">
                  <div
                    class="preview-btn"
                    v-if="
                      personalData.uploadData.cardPeriod.loading ||
                      personalData.uploadData.cardPeriod.url !== undefined
                    "
                    @click="
                      handleViewImg(
                        personalData.uploadData.cardPeriod.url || ''
                      )
                    "
                  >
                    <loading-outlined
                      v-if="personalData.uploadData.cardPeriod.loading"
                    />
                    <MySvgIcon
                      v-else
                      icon-class="icon-search"
                      style="font-size: 24px; fill: #fff; display: inline-flex"
                    />
                  </div>
                </div>
                <a-upload
                  class="upload-wrapper"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :fileList="[]"
                  :before-upload="(file: any) => beforeUpload(file, 'person', 'cardPeriod')"
                >
                  <div class="upload-btn" ref="uploadBtnRef">
                    <span>点击上传</span>
                  </div>
                </a-upload>
              </div>
            </a-form-item>
            <div class="upload-desc">
              请上传凭证原件的<span>彩色照片或扫描件</span>，支持
              jpg/jpeg/png，最大15M
            </div>
            <div class="tip-container">
              <img src="@/assets/icons/icon_tip.svg" alt="" />
              <span class="tip-content"
                >以下信息自动识别，需仔细核对，如识别有误，请修正。</span
              >
            </div>
            <a-form-item label="姓名" name="name">
              <a-input
                :placeholder="
                  personalData.ocrNumberData.compelete
                    ? '请填写姓名'
                    : '上传证件自动识别'
                "
                v-model:value="personalData.formState.name"
                :disabled="!personalData.ocrNumberData.compelete"
              />
            </a-form-item>
            <a-form-item label="身份证号码" name="id">
              <a-input
                :placeholder="
                  personalData.ocrNumberData.compelete
                    ? '请填写身份证号码'
                    : '上传证件自动识别'
                "
                v-model:value="personalData.formState.id"
                :disabled="!personalData.ocrNumberData.compelete"
              />
            </a-form-item>
          </template>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'UserRegisterPersonalDocumentsIndex',
}
</script>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import { fileUploadAttach } from '@/api/common'
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import { requestOcrEnterpriseLicense, requestOcrIdCard } from '@/api/common/ocr'
import imageCompression from 'browser-image-compression'
import dayjs from 'dayjs'
import { VueNode } from 'ant-design-vue/lib/_util/type'

const enterpriseFormRef = ref<any>(null)
const personalFormRef = ref<any>(null)
const enterpriseData = reactive<any>({
  uploadData: {
    cert: {
      init: true,
    },
    logo: {
      init: true,
    },
  },
  ocrData: {},
  formState: {},
})
const personalData = reactive<any>({
  labelIndex: 0,
  uploadData: {
    cardNumber: {
      init: true,
    },
    cardPeriod: {
      init: true,
    },
  },
  ocrNumberData: {
    compelete: false,
  },
  ocrPeriodData: {
    compelete: false,
  },
  formState: {},
})
const emit = defineEmits(['handleSubmit'])

/**
 * @description 去除空格
 * <AUTHOR>
 * @Bug #4490 去除名称与编码的空格
 * @param param0 值
 * @param tagName 变量名
 */
const inputFilter = ({ target: { value } }, tagName: string) => {
  enterpriseData.formState[tagName] = value.replace(/\s+/g, '')
}

// 企业证件表单自定义校验 - 营业执照
const validateCert = async (rule: any, value: any, callback: any) => {
  if (enterpriseData.uploadData.cert.init) {
    return Promise.resolve()
  }
  if (enterpriseData.uploadData.cert.url === undefined) {
    return Promise.reject('请上传营业执照')
  }
  return Promise.resolve()
}
// 个人证件自定义校验 - 身份证人像面
const validateCardNumber = async (_rule: any, _value: string) => {
  if (personalData.uploadData.cardNumber.init) {
    return Promise.resolve()
  }
  if (personalData.uploadData.cardNumber.url === undefined) {
    return Promise.reject('请上传身份证人像面')
  }
  return Promise.resolve()
}
// 个人证件自定义校验 - 身份证国徽面
const validateCardPeriod = async (_rule: any, _value: string) => {
  if (personalData.uploadData.cardPeriod.init) {
    return Promise.resolve()
  }
  if (personalData.uploadData.cardPeriod.url === undefined) {
    return Promise.reject('请上传身份证国徽面')
  }
  return Promise.resolve()
}
// 企业表单校验规则
const entpriseRules = {
  cert: [
    {
      required: true,
      validator: validateCert,
    },
  ],
  name: [{ required: true, message: '请填写企业名称' }],
  code: [{ required: true, message: '请填写统一社会信用代码' }],
  legalPerson: [{ required: true, message: '请填写法定代表人姓名' }],
}
// 表单校验规则
const personalRules = {
  cardNumber: [
    {
      required: true,
      validator: validateCardNumber,
    },
  ],
  cardPeriod: [
    {
      required: true,
      validator: validateCardPeriod,
    },
  ],
  name: [{ required: true, message: '请填写您的姓名' }],
  id: [{ required: true, message: '请填写您的身份证号' }],
}

// 文件上传钩子
const beforeUpload = async (
  file: any,
  targetDataType: string,
  targetField: string
) => {
  let passFlag = true

  // 检查文件格式
  const isAcceptFileTyle = ['image/jpg', 'image/jpeg', 'image/png'].includes(
    file.type
  )
  if (!isAcceptFileTyle) {
    message.error('不支持的文件格式! 请上传支持的图片格式')
    passFlag = false
  }

  // 检查文件大小
  const isLimitFileSize = file.size / 1024 / 1024 < 15
  if (!isLimitFileSize) {
    message.error('图片过大! 请上传小于 15M 的图片')
    passFlag = false
  }

  if (passFlag) {
    // 上传文件
    if (targetDataType === 'ent') {
      enterpriseData.uploadData[targetField].loading = true
    } else {
      personalData.uploadData[targetField].loading = true
    }

    // 检查是否需要压缩图片
    if (file.size / 1024 > 512) {
      // 文件大于 512K，压缩
      try {
        const compressedFile = await imageCompression(file, {
          maxSizeMB: 0.512,
          maxWidthOrHeight: 1920,
          useWebWorker: true,
        })
        file = new File([compressedFile], file.name)
      } catch (error) {
        console.error(error)
      }
    }

    const formData = new FormData()
    formData.append('file', file)

    fileUploadAttach(formData)
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          message.success('上传成功')
          if (targetDataType === 'ent') {
            // 企业证件部分的上传
            if (targetField === 'cert') {
              // 营业执照
              message.loading({
                content: '正在识别营业执照信息，请稍后...',
                key: 'cert',
                duration: 0,
              })
              requestOcrEnterpriseLicense(data.link)
                .then(({ data }) => {
                  if (data.success) {
                    message.success({
                      content: '营业执照信息识别成功',
                      key: 'cert',
                      duration: 2,
                    })
                    data = data.data
                    enterpriseData.formState.name = data.name
                    enterpriseData.formState.code = data.registrationNumber
                    enterpriseData.formState.legalPerson =
                      data.legalRepresentative
                    enterpriseData.ocrData = { ...data, compelete: true }
                  } else {
                    message.error({
                      content: '无法识别的图像！请更换图像后重试',
                      key: targetField,
                      duration: 6,
                    })
                    enterpriseData.uploadData[targetField].url = undefined
                    enterpriseData.uploadData[targetField].id = undefined
                    enterpriseData.uploadData[targetField].loading = false
                  }
                })
                .catch(() => {
                  message.error({
                    content: '无法识别的图像！请更换图像后重试',
                    key: targetField,
                    duration: 6,
                  })
                  enterpriseData.uploadData[targetField].url = undefined
                  enterpriseData.uploadData[targetField].id = undefined
                  enterpriseData.uploadData[targetField].loading = false
                })
            } else {
              // logo
            }
            enterpriseData.uploadData[targetField].url = data.link
            enterpriseData.uploadData[targetField].id = data.attachId
            enterpriseData.uploadData[targetField].loading = false
            enterpriseData.formState[targetField] = data.attachId
            enterpriseFormRef.value.validateFields([targetField])
          } else {
            const clear = () => {
              personalData.uploadData[targetField].url = undefined
              personalData.uploadData[targetField].id = undefined
              personalData.uploadData[targetField].loading = false
            }
            const cardMessage = (
              content: VueNode,
              type?: 'error' | 'success',
              duration?: number
            ) => {
              message[type || 'success']({
                content,
                key: targetField,
                duration: duration || 2,
              })
            }
            // 个人证件部分的上传
            message.loading({
              content: '正在识别身份证信息，请稍后...',
              key: targetField,
              duration: 0,
            })
            requestOcrIdCard({ picUrl: data.link, processType: 2 })
              .then(({ data }) => {
                if (data.success) {
                  data = data.data
                  if (targetField === 'cardNumber') {
                    if (data.number) {
                      cardMessage('身份证信息识别成功')
                    } else {
                      cardMessage('请上传正确的身份证照片', 'error')
                      clear()
                      return
                    }
                    personalData.formState.name = data.name
                    personalData.formState.id = data.number
                    personalData.ocrNumberData = { ...data, compelete: true }
                  } else {
                    if (
                      data.valid_to &&
                      dayjs(data.valid_to).isBefore(dayjs())
                    ) {
                      cardMessage('身份证已过期，无法提交', 'error')
                      clear()
                      return
                    }
                    if (data.issue) {
                      cardMessage('身份证信息识别成功')
                    } else {
                      cardMessage('请上传正确的身份证照片', 'error')
                      clear()
                      return
                    }
                    personalData.ocrPeriodData = { ...data, compelete: true }
                  }
                } else {
                  cardMessage('无法识别的图像！请更换图像后重试', 'error', 6)
                  clear()
                }
              })
              .catch(() => {
                cardMessage('无法识别的图像！请更换图像后重试', 'error', 6)
                clear()
              })
            personalData.uploadData[targetField].url = data.link
            personalData.uploadData[targetField].id = data.attachId
            personalData.uploadData[targetField].loading = false
            personalData.formState[targetField] = data.attachId
            personalFormRef.value.validateFields([targetField])
          }
        } else {
          message.success(data.msg || '上传失败')
        }
      })
      .catch(() => {
        if (targetDataType === 'ent') {
          enterpriseData.uploadData[targetField].loading = false
        } else {
          personalData.uploadData[targetField].loading = false
        }
        message.error('上传失败!')
      })
  }

  return false
}

// 获取表单提交数据
const handlePreSubmit = async () => {
  for (const key in enterpriseData.uploadData) {
    enterpriseData.uploadData[key].init = false
  }
  for (const key in personalData.uploadData) {
    personalData.uploadData[key].init = false
  }

  Promise.all([
    enterpriseFormRef.value.validate(),
    personalFormRef.value.validate(),
  ])
    .then(() => {
      if (
        personalData.labelIndex === 0 &&
        enterpriseData.formState.legalPerson !== personalData.formState.name
      ) {
        message.error({
          content: '企业法人姓名与法人身份证姓名不一致，请检查',
          duration: 6,
        })
        return
      }
      emit('handleSubmit', enterpriseData, personalData)
    })
    .catch(() => {})
}

// 个人证件身份类型切换
const handlePersonalTypeSwitch = (index: number) => {
  if (index === 0) {
    personalData.labelIndex = 0
  } else {
    personalData.labelIndex = 1
  }
}

// 根据接口返回回显数据
const handleSetPageData = (data: any) => {
  // 企业部分信息回显
  if (data.businessLicenceAttachId) {
    enterpriseData.uploadData.cert = {
      init: false,
      id: data.businessLicenceAttachId,
      url: data.attachMap[data.businessLicenceAttachId],
    }
    enterpriseData.formState.cert = data.businessLicenceAttachId
  }
  if (data.logo) {
    enterpriseData.uploadData.logo = {
      init: false,
      url: data.logo,
    }
  }
  enterpriseData.formState.name = data.corpName
  enterpriseData.formState.code = data.businessLicenceNumber
  enterpriseData.formState.legalPerson = data.corporationName

  // 个人部分信息回显
  if (data.legalPersonFlag === 1) {
    // 法人
    personalData.labelIndex = 1
  } else {
    // 经办人
    personalData.labelIndex = 0
    if (data.corporationFaceAttachId) {
      personalData.uploadData.cardNumber = {
        init: false,
        id: data.corporationFaceAttachId,
        url: data.attachMap[data.corporationFaceAttachId],
      }

      enterpriseData.formState.cardNumber = data.corporationFaceAttachId
    }
    if (data.corporationBackAttachId) {
      personalData.uploadData.cardPeriod = {
        init: false,
        id: data.corporationBackAttachId,
        url: data.attachMap[data.corporationBackAttachId],
      }
      enterpriseData.formState.cardPeriod = data.corporationBackAttachId
    }
  }
}

defineExpose({ handlePreSubmit, handleSetPageData })
</script>

<style lang="scss" scoped>
.doc-container {
  width: 1000px;
  padding: 40px;
  margin: auto;
  background: #ffffff;
  box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
  border-radius: 16px;
  border: 1px solid #efefef;

  .ent-container {
    padding-bottom: 40px;
  }

  .personal-container {
    padding-top: 40px;
    border-top: 1px dashed #e1e4e8;
  }

  .title-container {
    .title {
      font-size: 24px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #0a1f44;
      line-height: 32px;
    }
  }

  .form-container {
    .form {
      position: relative;

      :deep(.ant-form-item) {
        &:last-child {
          margin-bottom: 0;
        }

        .ant-form-item-label {
          width: 300px;

          & > label {
            height: 48px;
          }
        }

        .ant-form-item-control {
          width: 400px;
          flex: none;

          > * {
            width: 100%;
          }
        }

        // 行高
        input,
        .ant-picker,
        .ant-select-selector,
        .ant-select-selection-item,
        .ant-select-selection-placeholder {
          height: 48px;
          line-height: 48px;
        }

        .ant-input-affix-wrapper {
          padding: 0 12px;
        }

        .ant-upload {
          width: 100%;
          height: 100%;
          margin: 0;
          padding: 0;
        }
      }

      .upload-desc,
      .invoice-btn,
      .tip-container {
        margin-left: 300px;
      }

      .invoice-btn {
        margin-top: 8px;
      }

      .delete {
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 24px;
        fill: #838fa6;
        cursor: pointer;
        outline: none;

        &:hover {
          fill: #6a7791;
        }
      }

      .ant-form-item-has-error .ant-picker.ant-picker-disabled {
        background-color: #f5f5f5;
      }
    }

    .upload-container {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 208px;
      height: 208px;
      border-radius: 8px;
      border: 1px solid #efefef;
      overflow: hidden;

      .img-preview {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        z-index: 1;
      }

      .preview-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        height: 100%;
        background-color: #fff;
      }

      .upload-wrapper {
        height: 48px;
        flex-shrink: 0;
        cursor: pointer;
        z-index: 2;

        .upload-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          text-align: center;
          background: rgba(24, 44, 79, 0.5);
          border-radius: 0px 0px 8px 8px;
          backdrop-filter: saturate(90%) blur(6px);
          -webkit-backdrop-filter: saturate(90%) blur(6px);
        }
      }

      .preview-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: rgba(24, 44, 79, 0.5);
        backdrop-filter: saturate(90%) blur(6px);
        -webkit-backdrop-filter: saturate(90%) blur(6px);
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;
        z-index: 2;

        span {
          font-size: 16px;
          @include family-PingFangSC-Semibold;
          font-weight: 600;
          color: #ffffff;
          line-height: 24px;
        }
      }
    }

    .tip-container {
      display: inline-block;
      margin-top: 16px;
      margin-bottom: 24px;
      padding: 8px 14px;
      background: #f1f2f4;
      border-radius: 4px;
    }

    .desc {
      margin-top: 16px;
    }
  }
}
</style>
