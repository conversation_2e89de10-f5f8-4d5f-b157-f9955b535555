<template>
  <GlobalDialog
    title="移交企业"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="content-container">
      <div class="tip-container">
        <span class="tip-item"
          >企业所有者为超级管理员且只有1位，移交后你将变为管理员</span
        >
      </div>
      <template v-if="listDataLoading">
        <a-spin>
          <div style="min-height: 60px" />
        </a-spin>
      </template>
      <div v-else class="user-list-container">
        <n-scrollbar
          :style="{
            maxHeight: '320px',
          }"
        >
          <div class="account-space-list">
            <CheckCard
              noBorder
              v-for="item of userListData"
              :key="item.id"
              :active="item.active"
              :style="{ backgroundColor: item.active ? '#EBF5FF' : '' }"
              @click="handleSubAccountCardClick(item)"
            >
              <div class="account-item">
                <div class="avatar-container">
                  <img :src="item.logoSrc" alt="" />
                </div>
                <div class="name-container">
                  <span class="name">{{ item.personalName }}</span>
                </div>
              </div>
            </CheckCard>
          </div>
        </n-scrollbar>
      </div>
    </div>
    <template #button>
      <div
        style="display: flex; align-items: center; justify-content: flex-end"
      >
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 8px"
          round
          :bordered="false"
          @click="handleCancel"
        >
          取消
        </n-button>
        <n-button
          class="blue border primary"
          style="height: 40px"
          round
          :bordered="false"
          :loading="btnLoading"
          @click="handleConfirm"
        >
          确定
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script lang="ts">
export default {
  name: 'UserCenterTransferEnterpriseDialogIndex',
}
</script>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { NButton, NScrollbar } from 'naive-ui'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import CheckCard from '@/components/BaseCard/checkCard/index.vue'
import {
  requestEntMemberList,
  requestTransferEnterprise,
} from '@/api/user/center/entManage'
import { message } from 'ant-design-vue'

const store = useStore()
const dialogRef = ref<any>(null)
const btnLoading = ref<any>(false)
const listDataLoading = ref<boolean>(true)
const subAccountId = computed(() => store.getters['Auth/subAccountId'])
const userListData = ref<any>([])

const handleCancel = () => {
  dialogRef.value.handleClose()
}

const handleOpen = () => {
  btnLoading.value = false
  listDataLoading.value = true
  requestEntMemberList()
    .then(({ data }) => {
      if (data.success) {
        listDataLoading.value = false
        data = data.data
        userListData.value = data
      }
    })
    .catch(() => {})
  dialogRef.value.handleOpen()
}

// 点击用户卡片选择用户
const handleSubAccountCardClick = (item: any) => {
  for (const userItem of userListData.value) {
    userItem.active = false
  }
  item.active = true
}

// 确认事件
const handleConfirm = () => {
  let targetId = undefined
  for (const item of userListData.value) {
    if (item.active) {
      targetId = item.customerFrontUserId
      break
    }
  }
  if (!targetId) {
    message.warn('请选择一个用户')
    return
  }
  btnLoading.value = true
  const requestObj = {
    typeId: subAccountId.value,
    customerId: targetId,
  }
  requestTransferEnterprise(requestObj)
    .then(({ data }) => {
      btnLoading.value = false
      if (data.success) {
        message.success('操作成功')
        // store.dispatch('Auth/fetchUserInfo')
        store.dispatch('Auth/exitLogin')
        handleCancel()
      }
    })
    .catch(() => {
      btnLoading.value = false
    })
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.content-container {
  .tip-container {
    margin-bottom: 24px;

    .tip-item {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #53627c;
      line-height: 20px;
    }
  }

  .user-list-container {
    > * {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .account-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .avatar-container {
        width: 40px;
        height: 40px;
        margin-right: 16px;
        border-radius: 50%;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .name-container {
        display: flex;
        flex-direction: column;
        flex: 1;

        .name {
          font-size: 14px;
          font-family: SFProText-Medium, SFProText;
          font-weight: 500;
          color: #0a1f44;
          line-height: 20px;
        }

        .account {
          font-size: 12px;
          font-family: SFProText-Medium, SFProText;
          font-weight: 500;
          color: #8a94a6;
          line-height: 16px;
        }
      }
    }
  }
}
</style>
