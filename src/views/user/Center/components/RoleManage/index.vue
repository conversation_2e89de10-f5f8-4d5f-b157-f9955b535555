<template>
  <div class="role-manage-container">
    <div class="title-container">
      <span class="title">角色列表</span>
      <div class="button-container" v-if="roleMap.user_center_role_add_button">
        <n-button text color="#182c4f" @click="handleAddRole">
          <template #icon>
            <MySvgIcon
              icon-class="icon-jianzhu"
              class="icon"
              style="font-size: 24px; color: #182c4f"
            />
          </template>
          添加角色
        </n-button>
      </div>
    </div>
    <div class="content-container">
      <a-spin :spinning="tableLoading" tip="加载中...">
        <!-- :pagination="{ hideOnSinglePage: true }" -->
        <a-table
          :columns="columns"
          :data-source="tableData"
          rowKey="id"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'roleName'">
              <span class="name-wrapper">{{ record.roleName }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'createTime'">
              <span class="date-wrapper">{{ record.createTime }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <div class="button-container">
                <span @click="handleView(record.roleName, record.parentId)"
                  >查看</span
                >
                <template
                  v-if="
                    !['admin', 'financing_admin'].includes(record.roleAlias)
                  "
                >
                  <span
                    @click="
                      handleEdit(record.roleName, record.parentId, record.id)
                    "
                    >编辑</span
                  >
                  <span @click="handleRole(record.id)">权限</span>
                  <span @click="handleDelete(record.id, record.children)"
                    >删除</span
                  ></template
                >
              </div>
            </template>
          </template>
          <template #emptyText>
            <template v-if="pageInitLoading">
              <div style="height: 100px"></div>
            </template>
            <template v-else>
              <div class="empty-container">
                <img src="@/assets/images/empty_2.svg" alt="" />
                <span class="desc">暂无数据</span>
              </div>
            </template>
          </template>
        </a-table>
      </a-spin>
    </div>
    <AddRole ref="addRoleRef" @reloadList="initTableData" />
    <RoleManage ref="roleManageRef" />
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      content="删除的记录无法恢复，请确认是否删除"
      cancelBtnName="取消"
      confirmBtnName="确认删除"
      isWarnBtn
      @confirm="handleConfirmDelete"
    />
    <DialogAuthority ref="dialogAuthority" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'UserCenterEnterpriseManageIndex',
}

const columns = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 280,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
]
</script>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { NButton } from 'naive-ui'
import AddRole from './components/addRole/index.vue'
import { requestFinancingRoleList } from '@/api/common'
import RoleManage from './components/roleManage/index.vue'
import ConfirmDialog from '@/businessComponents/ConfirmDialog/index.vue'
import { requestDeleteRole } from '@/api/user/center/roleManage'
import { message } from 'ant-design-vue'
import DialogAuthority from '@/views/user/components/Dialog/dialogAuthority.vue'

const store = useStore()
const dialogAuthority = ref(null)
const addRoleRef = ref<any>(null)
const roleManageRef = ref<any>(null)
const confirmDialog = ref<any>(null)
const tenantId = computed(() => store.getters['Auth/tenantId'])
const pageInitLoading = ref<boolean>(true)
const tableData = ref<any>([])
const tableLoading = ref<boolean>(true)
let deleteDataObj: any = {
  targetId: undefined,
}
const roleMap = computed<any>(() => store.getters['Role/roleMap'])

const initTableData = () => {
  tableLoading.value = true
  requestFinancingRoleList(tenantId.value)
    .then(({ data }) => {
      if (data.success) {
        tableLoading.value = false
        data = data.data

        tableData.value = data
      }
      pageInitLoading.value = false
    })
    .catch(() => {
      pageInitLoading.value = false
    })
}

// 查看按钮
const handleView = (roleName: string, parentId: string) => {
  addRoleRef.value.handleOpen(
    roleName,
    parentId === '0' ? undefined : parentId,
    { isView: true }
  )
}

// 编辑按钮
const handleEdit = (roleName: string, parentId: string, targetId: string) => {
  if (!roleMap.value.user_center_role_edit_button) {
    dialogAuthority.value.handleOpen()
    return
  }
  addRoleRef.value.handleOpen(
    roleName,
    parentId === '0' ? undefined : parentId,
    { isEdit: true, targetId }
  )
}

// 权限按钮
const handleRole = (targetId: string) => {
  if (!roleMap.value.user_center_role_jurisdiction_button) {
    dialogAuthority.value.handleOpen()
    return
  }
  roleManageRef.value.handleOpen(targetId)
}

// 删除按钮
const handleDelete = (targetId: string, hasChildren: any) => {
  if (!roleMap.value.user_center_role_delete_button) {
    dialogAuthority.value.handleOpen()
    return
  }
  if (hasChildren) {
    message.warn('只能删除不含子角色的角色')
    return
  }
  deleteDataObj.targetId = targetId
  confirmDialog.value.handleOpen()
}

const handleConfirmDelete = () => {
  requestDeleteRole(deleteDataObj.targetId)
    .then(({ data }: { data: any }) => {
      if (data.success) {
        message.success('删除成功')
        initTableData()
        confirmDialog.value.handleClose()
      }
    })
    .catch(() => {})
}

// 添加角色按钮
const handleAddRole = () => {
  addRoleRef.value.handleOpen()
}

initTableData()
</script>

<style lang="scss" scoped>
.role-manage-container {
  .title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 20px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #0a1f44;
      line-height: 28px;
    }

    .button-container {
      display: flex;
      align-items: center;

      > * {
        margin-right: 40px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    :deep(.n-button__icon) {
      margin: 0 6px 0 0;
      width: 24px;
      max-width: 24px !important;
      height: 24px;

      .n-icon-slot {
        width: 24px;
        height: 24px;
      }
    }
  }

  .content-container {
    :deep(.ant-table-container) {
      .ant-table-tbody > tr > td {
        border: unset;

        .name-wrapper {
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #0a1f44;
          line-height: 20px;
        }

        .date-wrapper {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8a94a6;
          line-height: 20px;
        }
      }
    }

    .button-container {
      span {
        margin-right: 8px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #0d55cf;
        line-height: 20px;
        cursor: pointer;

        :last-child {
          margin-right: 0;
        }

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .empty-container {
      width: 100%;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 40px auto;
      text-align: center;

      img {
        width: 200px;
        height: 200px;
      }

      .desc {
        font-size: 14px;
        font-weight: 500;
        color: #8a94a6;
        line-height: 20px;
      }
    }
  }

  :deep(.ant-table-thead) {
    > tr > th {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8a94a6;
      line-height: 20px;
      background: transparent;
      border-color: transparent;

      &::before {
        display: none;
        opacity: 0;
      }
    }
  }
}
</style>
