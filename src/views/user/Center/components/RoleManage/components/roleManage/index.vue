<template>
  <GlobalDialog
    title="权限配置"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="content-container">
      <template v-if="treeDataLoading">
        <a-spin style="display: block; min-height: 42px; line-height: 42px" />
      </template>
      <template v-else>
        <a-tree
          v-model:expandedKeys="treeState.expandedKeys"
          v-model:selectedKeys="treeState.selectedKeys"
          v-model:checkedKeys="treeState.checkedKeys"
          checkable
          :tree-data="roleTreeData"
          :fieldNames="{ children: 'children', title: 'title', key: 'id' }"
        >
        </a-tree>
      </template>
    </div>
    <template #button>
      <div
        style="display: flex; align-items: center; justify-content: flex-end"
      >
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 8px"
          round
          :bordered="false"
          @click="handleCancel"
        >
          取消
        </n-button>
        <n-button
          class="blue border primary"
          style="height: 40px"
          round
          :bordered="false"
          :loading="btnLoading"
          @click="handleConfirm"
        >
          确定
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script lang="ts">
export default {
  name: 'UserCenterRoleManageDialogIndex',
}
</script>
<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { useStore } from 'vuex'
import { NButton } from 'naive-ui'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { requestRoleMenuData } from '@/api/common'
import {
  requestRolePermissionsData,
  requestEditRole,
} from '@/api/user/center/roleManage'
import { message } from 'ant-design-vue'

const store = useStore()
const dialogRef = ref<any>(null)
const btnLoading = ref<any>(false)
const treeDataLoading = ref<boolean>(true)
const roleTreeData = ref<any>([])
const tenantId = computed(() => store.getters['Auth/tenantId'])
const treeState = reactive({
  expandedKeys: [],
  selectedKeys: [],
  checkedKeys: [],
})
let currentTargetId = ''
const emit = defineEmits(['reloadList'])

const handleCancel = () => {
  dialogRef.value.handleClose()
}

const handleOpen = (targetId: string) => {
  treeDataLoading.value = true
  currentTargetId = targetId
  roleTreeData.value = []
  treeState.expandedKeys = []
  treeState.selectedKeys = []
  treeState.checkedKeys = []
  requestRoleMenuData(tenantId.value)
    .then(({ data }) => {
      if (data.success) {
        treeDataLoading.value = false
        data = data.data
        roleTreeData.value = data.menu
      }
    })
    .catch(() => {})
  requestRolePermissionsData(targetId)
    .then(({ data }: { data: any }) => {
      if (data.success) {
        data = data.data
        treeState.checkedKeys = data.menu
      }
    })
    .catch(() => {})
  dialogRef.value.handleOpen()
}

// 确认事件
const handleConfirm = () => {
  btnLoading.value = true
  const requestObj = {
    roleIds: [currentTargetId],
    menuIds: treeState.checkedKeys,
    dataScopeIds: [],
    apiScopeIds: [],
  }
  requestEditRole(requestObj)
    .then(({ data }) => {
      btnLoading.value = false
      if (data.success) {
        message.success('操作成功')
        handleCancel()
        emit('reloadList')
      }
    })
    .catch(() => {
      btnLoading.value = false
    })
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped></style>
