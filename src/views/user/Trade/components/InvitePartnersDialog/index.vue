<template>
  <GlobalDialog
    :title="inviteResult.pending ? '邀请贸易伙伴' : '确定邀请'"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="content-container">
      <!-- 邀请贸易伙伴 -->
      <div v-if="inviteResult.pending" class="form-container" key="form-container">
        <LabelBar
          :labelList="labelData.labelList"
          @switch="handleLabelSwitch"
          :state="labelData.currentIndex"
        />
        <a-form
          ref="formRef"
          style="display: flex; flex-direction: column"
          :model="formState"
          :rules="formRules"
        >
          <a-form-item v-if="labelData.currentIndex === 0" name="enterpriseName">
            <a-input
              placeholder="请输入邀请企业名称"
              v-model:value="formState.enterpriseName"
              @change="valueFormat($event, 'enterpriseName')"
            />
          </a-form-item>
          <a-form-item v-else-if="labelData.currentIndex === 1" name="inviteeName">
            <a-input placeholder="请输入受邀人姓名" v-model:value="formState.inviteeName" />
          </a-form-item>
          <a-form-item v-if="labelData.currentIndex === 0" name="code">
            <a-input
              placeholder="请输入统一社会代码"
              v-model:value="formState.code"
              @change="valueFormat($event, 'code')"
            />
          </a-form-item>
          <a-form-item v-if="labelData.currentIndex === 0" name="contact">
            <a-input placeholder="请输入联系人" v-model:value="formState.contact" />
          </a-form-item>
          <a-form-item name="contactDetails">
            <a-input placeholder="请输入联系方式" v-model:value="formState.contactDetails" />
          </a-form-item>
          <a-form-item v-if="labelData.currentIndex === 0" name="targetType">
            <a-radio-group v-model:value="formState.targetType" :options="options" />
          </a-form-item>
        </a-form>
      </div>
      <!-- 确定邀请 -->
      <div v-else class="confirm-container" key="confirm-container">
        <div class="invite-container-form">
          <div class="form-item">
            <span class="value">
              {{
              inviteResult.name +
              '邀请您成为该企业' +
              (inviteResult.position == 1 ? '上游' : '下游') +
              '贸易伙伴'
              }}
            </span>
          </div>
          <div class="form-item">
            <span class="value">
              <a style="color: #697cff" href="https://sc.jingruiit.com">https://sc.jingruiit.com</a>
            </span>
          </div>
          <div class="form-item">
            <span class="label">有效期：</span>
            <span class="value">{{ inviteResult.expireDate }}</span>
          </div>
          <div class="form-item">
            <span class="label">绑定流程：</span>
            <span class="value">注册/登录-用户中心-贸易伙伴-接收邀请</span>
          </div>
        </div>
      </div>
    </div>
    <template #button>
      <div class="button-container">
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 8px"
          round
          :bordered="false"
          @click="handleCancel"
        >{{ inviteResult.pending ? '取消' : '复制邀请' }}</n-button>
        <n-button
          class="blue border primary"
          style="height: 40px"
          round
          :bordered="false"
          :loading="btnLoading"
          @click="handleConfirm"
        >{{ inviteResult.pending ? '确定' : '短信邀请' }}</n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script lang="ts">
export default {
  name: 'UserTradeInvitePartnersDialogIndex',
}

const options = [
  { label: '核心企业', value: 3 },
  { label: '非核心企业', value: 2 },
]

const formRules = {
  enterpriseName: [
    {
      required: true,
      message: '请输入邀请企业名称',
      trigger: 'blur',
    },
  ],
  inviteeName: [
    {
      required: true,
      message: '请输入受邀人姓名',
      trigger: 'blur',
    },
  ],
  code: [{ required: true, message: '请输入统一社会代码', trigger: 'blur' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactDetails: [
    { required: true, message: '请输入联系手机号', trigger: ['blur'] },
    {
      pattern: /^1\d{10}( 1\d{10})*$/,
      message: '请输入正确的手机号',
      trigger: 'blur',
    },
  ],
  targetType: [
    {
      required: true,
      message: '请选择企业类型',
      trigger: ['blur', 'change'],
    },
  ],
}
</script>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { NButton } from 'naive-ui'
import { message } from 'ant-design-vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import Clipboard from 'clipboard'
import { requestInvitePartner } from '@/api/user/trade'

const dialogRef = ref<any>(null)
const formRef = ref<any>(null)
const btnLoading = ref<any>(false)
const formState = ref<any>({
  enterpriseName: undefined,
  contact: undefined,
  contactDetails: undefined,
  targetType: undefined,
})
const labelData = reactive<any>({
  labelList: ['企业机构', '个体工商户'],
  currentIndex: 0,
})
const inviteResult = ref<any>({
  pending: true,
})
const emit = defineEmits(['initTradeList'])
let isUpStream: boolean | undefined = undefined

/**
 * @description 去除空格
 * <AUTHOR>
 * @Bug #4455 去除名称与编码的空格
 * @param param0 值
 * @param tagName 变量名
 */
const valueFormat = ({ target: { value } }, tagName: string) => {
  formState.value[tagName] = value.replace(/\s+/g, '')
}

const handleOpen = (currentIsUpStream: boolean) => {
  isUpStream = currentIsUpStream
  // 初始化数据·
  for (const key in formState.value) {
    formState.value[key] = undefined
  }
  labelData.currentIndex = 0
  inviteResult.value.pending = true
  btnLoading.value = false
  // 打开弹窗
  dialogRef.value.handleOpen()
}

// 确认事件
const handleConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
      btnLoading.value = true
      const requestObj: any = {
        invitedContactPhone: formState.value.contactDetails,
        invitedCompanyPosition: isUpStream ? 1 : 0,
      }
      if (labelData.currentIndex === 0) {
        requestObj.invitedCompanyName = formState.value.enterpriseName
        requestObj.invitedCompanyCode = formState.value.code
        requestObj.invitedContact = formState.value.contact
        requestObj.invitedCompanyType = formState.value.targetType
      } else if (labelData.currentIndex === 1) {
        requestObj.invitedContact = formState.value.inviteeName
        requestObj.invitedCompanyType = 1
      }
      requestInvitePartner(requestObj)
        .then(({ data }: { data: any }) => {
          if (data.success) {
            data = data.data
            inviteResult.value = data
            inviteResult.value.pending = false
          }
          emit('initTradeList')
          btnLoading.value = false
        })
        .catch(() => {
          btnLoading.value = false
        })
    })
    .catch(() => {})
}

// 头部 Label 栏切换
const handleLabelSwitch = (index: number) => {
  formRef.value.resetFields()
  labelData.currentIndex = index
}

// 确定邀请 - 复制邀请
const handleCopy = (event: any) => {
  const copyValue = `${inviteResult.value.name || '-- '}邀请您成为该企业${
    inviteResult.value.position == 1 ? '上游' : '下游'
  }贸易伙伴
https://sc.jingruiit.com
有效期：${inviteResult.value.expireDate || '--'}
绑定流程：注册/登录-用户中心-贸易伙伴-接收邀请`
  const clipboard = new Clipboard('body', {
    text: () => copyValue,
  })
  clipboard.on('success', () => {
    message.success('复制成功')
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    console.error(e)
    // 不支持复制
    message.warning('该浏览器不支持自动复制')
    clipboard.destroy()
  })
}

// 邀请伙伴弹窗取消按钮事件
const handleCancel = (event: any) => {
  if (inviteResult.value.pending) {
    // 取消按钮
    dialogRef.value.handleClose()
  } else {
    // 复制邀请按钮
    handleCopy(event)
  }
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.content-container {
  display: inline-block;
  width: 100%;
}

:deep(.ant-form) {
  margin-top: 12px;

  .ant-form-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;

      .ant-col ant-form-item-control {
        margin-bottom: 16px;
      }
    }
  }

  .ant-form-item-control {
    .ant-input {
      height: 48px;
    }

    .ant-input-password {
      padding: 0px;

      input {
        height: 46px;
        padding-left: 11px;
      }

      .ant-input-suffix {
        position: absolute;
        right: 11px;
        height: 100%;
      }
    }
  }

  .ant-select-selector {
    min-height: 48px;

    input {
      height: 100% !important;
    }

    .ant-select-selection-placeholder {
      height: 100%;
      line-height: 46px;
    }
  }
}

.invite-container-form {
  .form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }

    .value {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #0a1f44;
      line-height: 20px;
    }
  }
}

.button-container {
  display: flex;
  justify-content: flex-end;
}
</style>
