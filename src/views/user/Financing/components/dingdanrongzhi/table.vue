<template>
  <div class="financing-custom-search">
    <div class="search-input-container">
      <a-input
        class="search-number"
        v-model:value="searchData.number"
        placeholder="输入融资编号"
      />
      <a-input
        class="search-name"
        v-model:value="searchData.name"
        placeholder="输入产品名称"
      />
      <a-range-picker class="search-time" v-model:value="searchData.time" />
    </div>
    <div class="search-control-container">
      <n-button class="blue border" :bordered="false" @click="handleSearch"
        >搜索</n-button
      >
      <n-button class="blue border" :bordered="false" @click="handleSearchReset"
        >重置</n-button
      >
    </div>
  </div>
  <a-table
    class="financing-custom-table"
    :columns="columns"
    :data-source="tableData"
    :pagination="tablePagination"
    :loading="tableLoading"
    @change="handleTableChange"
  >
    <template #headerCell="{ column, title }">
      <template v-if="column.dataIndex === 'goodsType'">
        <CustomTableFilter
          ref="goodsTypeFilterRef"
          :title="title"
          :options="['应收账款质押', '代采融资']"
          @filterChange="handleFilterChange('processTypeCustom', $event)"
        />
      </template>
      <template v-else-if="column.dataIndex === 'status'">
        <CustomTableFilter
          ref="statusFilterRef"
          :title="title"
          :options="[
            '融资申请待提交',
            '融资申请审核中',
            '融资申请已驳回',
            '融资申请拒绝',
            '融资申请待放款',
            '放款申请审核中',
            '放款申请已驳回',
            '放款申请拒绝',
            '待结清',
            '逾期未结清',
            '提前结清',
            '到期已结清',
            '逾期已结清',
            '已作废',
            '坏账已核销',
            '逾期协商审核中',
            '逾期协商已驳回',
            '展期申请审核中',
            '展期申请已驳回',
            '展期申请待确认',
            '展期确认审核中',
            '展期确认已驳回',
            '调息申请审核中',
            '调息申请已驳回',
            '提前结清审核中',
            '提前结清已驳回',
            '提前结清待支付',
            '减免申请审核中',
            '减免申请已驳回',
          ]"
          :optionValueMap="[
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
            19, 20, 21,
          ]"
          @filterChange="handleFilterChange('statusCustom', $event)"
        />
      </template>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'contractTitle'">
        <span class="single-line-text">{{ record.contractTitle }}</span>
      </template>
      <template v-else-if="column.dataIndex === 'goodsType'">
        <CommonTag
          :name="goodsTypeMap[record.goodsType].name"
          backgroundColor="#f1f2f4"
          color="#0A1F44"
          padding="6px 10px"
        />
      </template>
      <template v-else-if="column.dataIndex === 'productName'">
        <div class="action-column-container">
          <span
            ><router-link
              :to="{
                name: 'ProductDetails',
                query: {
                  goodId: record.goodsId,
                  goodType: record.goodType,
                  capitalId: record.capitalId,
                },
              }"
              >{{ record.goodName }}</router-link
            ></span
          >
        </div>
      </template>
      <template v-else-if="column.dataIndex === 'amount'">
        <span>{{ formatMoney(record.amount) }}</span>
      </template>
      <template v-else-if="column.dataIndex === 'processTypeCustom'">
        {{ goodsTypeMap[record.processType].name }}
      </template>
      <template v-else-if="column.dataIndex === 'status'">
        <TableStatusTag
          :name="financingStatusMap[record.status]?.name || ''"
          :color="financingStatusMap[record.status]?.color || ''"
          :center="false"
        />
      </template>
      <template v-else-if="column.key === 'action'">
        <div class="action-column-container">
          <span @click="handleDetail(record)">详情</span>
          <span
            v-if="[2, 6, 16, 18, 21].includes(record.status)"
            @click="handleToModify(record)"
          >
            去修改
          </span>
          <span v-if="[4].includes(record.status)" @click="handleApply(record)">
            申请放款
          </span>
          <span
            v-if="[0].includes(record.status)"
            @click="handleProduct(record)"
          >
            去提交
          </span>
          <span v-if="record.canOpenDelayApply" @click="handlePeriod(record)"
            >申请展期</span
          >
          <span
            v-if="[19].includes(record.status)"
            @click="handlePeriodSubmit(record)"
            >展期确认</span
          >
          <span
            v-if="record.canOpenOverdueConsult"
            @click="handleNegotiate(record)"
          >
            逾期协商
          </span>
          <!-- <span
            v-else-if="[8, 9].includes(record.status)"
            @click="handleReimApply(record)"
          >
            申请还款
          </span> -->
        </div>
      </template>
    </template>
    <template #emptyText>
      <template v-if="pageInitLoading"></template>
      <template v-else>
        <div class="empty-container">
          <img src="@/assets/images/empty_2.svg" alt="" />
          <span class="desc">暂无数据</span>
        </div>
      </template>
    </template>
  </a-table>
  <DialogAuthority ref="dialogAuthority" />
  <!-- 展期还款计划 -->
  <RepayPlanDialog ref="repayPlanRef" :planData="iouNosDetail" />

  <!-- 展期确认 -->
  <RenewalSubmitDialog
    ref="renewalSubmitRef"
    :financeNo="financeNo"
    :dataObj="dataObj"
    @refesh="handleSearchReset"
  />

  <!-- 当期展期,剩余待还全部展期 -->
  <RenewalApplyDialog ref="renewalApplyRef" :financeNo="rowItem.financeNo" />
  <!-- 随借随还弹窗 -->
  <RenewalApplyPayDialog
    ref="renewalApplyPayRef"
    :financeNo="rowItem.financeNo"
  />
  <!-- 逾期协商弹窗 -->
  <DialogLimitNegotiate
    ref="dialogLimitNegotiate"
    @refreshState="refreshState"
  />
</template>

<script lang="ts">
import { h } from 'vue'
import { NButton } from 'naive-ui'
import MySvgIcon from '@/components/MySvgIcon/index.vue'

export default {
  name: 'TradeDetailTable',
}

const customPaginationRender = ({
  originalElement,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  page,
  type,
}: {
  originalElement: any
  page: any
  type: any
}) => {
  if (type === 'prev') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-left custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h(MySvgIcon, { 'icon-class': 'icon-jiantou-zuo', 'font-size': '16px' }),
        h('span', {}, '上一页'),
      ]
    )
  } else if (type === 'next') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-right custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h('span', {}, '下一页'),
        h(MySvgIcon, { 'icon-class': 'icon-youjiantou1', 'font-size': '16px' }),
      ]
    )
  }
  return originalElement
}

// 宽度需要加上左边距12，右边距12；共24px
const columns = [
  {
    title: '融资编号',
    dataIndex: 'financeNo',
    minWidth: 120,
  },
  // {
  //   title: '业务类型',
  //   dataIndex: 'goodsType',
  //   width: 140,
  // },
  {
    title: '申请产品',
    dataIndex: 'goodsName',
    width: 190,
  },
  {
    title: '融资金额(元)',
    dataIndex: 'amount',
    width: 190,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 190,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 176,
  },
  {
    title: '操作',
    key: 'action',
  },
]

const createInitPagData = () => ({
  pageSize: 10,
  currentPage: 1,
  maxPage: 1,
  total: 0,
})

const createInitSearchData = () => ({
  number: undefined,
  name: undefined,
  time: undefined,
})
</script>
<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { TablePaginationConfig } from 'ant-design-vue'
import TableStatusTag from '@/views/trade/components/status-tag/index.vue'
import CommonTag from '@/components/CommonTag/index.vue'
import { financingStatusMap, goodsTypeMap } from '../../config'
import { requestTableData } from '@/api/user/FinancingList'
import CustomTableFilter from './tableFilter.vue'
import dayjs from 'dayjs'
import { formatMoney } from '@/utils/utils'
import router from '@/router'
import { useStore } from 'vuex'
import DialogAuthority from '@/views/user/components/Dialog/dialogAuthority.vue'
import RepayPlanDialog from './Dialog/repayPlanDialog.vue'
import DialogLimitNegotiate from './Dialog/dialogLimitNegotiate.vue'
import { LOAN_REPAYMENT } from '@/api/index.js'
import RenewalSubmitDialog from './Dialog/renewalSubmitDialog.vue'
import RenewalApplyDialog from './Dialog/renewalApplyDialog.vue'
import RenewalApplyPayDialog from './Dialog/renewalApplyPayDialog.vue'

const store = useStore()
const dialogAuthority: any = ref(null)
const dialogLimitNegotiate = ref(null)
const goodsTypeFilterRef: any = ref(null)
const statusFilterRef: any = ref(null)
const pageInitLoading = ref<boolean>(true)
const tableLoading = ref<boolean>(true)
const tableData = ref<any>([])
const iouNosDetail = ref<any>({}) // 还款数据
const tablePaginationData: any = ref(createInitPagData())
const tablePagination = computed(() => ({
  total: tablePaginationData.value.total,
  current: tablePaginationData.value.currentPage,
  pageSize: tablePaginationData.value.pageSize,
  showSizeChanger: false,
  itemRender: customPaginationRender,
}))
const roleMap = computed<any>(() => store.getters['Role/roleMap'])
const isEnterpriseAccount = computed(
  () => store.getters['Auth/isEnterpriseAccount']
)
let enableSearch = false
const searchData: any = reactive(createInitSearchData())
const repayPlanRef: any = ref(null) // 申请展期弹窗
const financeNo: any = ref(null)
const dataObj: any = ref({})
const renewalSubmitRef: any = ref(null) // 展期确认弹窗
const renewalApplyRef: any = ref(null) // 当期展期,剩余待还全部展期内容弹窗
const renewalApplyPayRef: any = ref(null) // 随借随还
const rowItem: any = ref({
  financeNo: null,
}) // 单行数据
let formatSearchData: {
  financeNoEqual: string | undefined
  goodsName: string | undefined
  createTimeDateGe: string | undefined
  createTimeDateLe: string | undefined
} = {
  financeNoEqual: undefined,
  goodsName: undefined,
  createTimeDateGe: undefined,
  createTimeDateLe: undefined,
}
const filterData: any = {
  processTypeCustom: [],
  statusCustom: [],
}

const loadTableData = (current: number, pageSize: number | undefined) => {
  tableLoading.value = true
  // 处理过滤数据
  let processTypeCustom = undefined
  let statusCustom = undefined
  if (filterData.processTypeCustom) {
    if (filterData.processTypeCustom.length > 0) {
      for (const item of filterData.processTypeCustom) {
        if (!processTypeCustom) {
          processTypeCustom = String(item)
        } else {
          processTypeCustom += `,${item}`
        }
      }
    }
    if (filterData.statusCustom.length > 0) {
      for (const item of filterData.statusCustom) {
        if (!statusCustom) {
          statusCustom = String(item)
        } else {
          statusCustom += `,${item}`
        }
      }
    }
  }
  // 构建搜索请求参数
  let requestObj: any = {
    current,
    size: pageSize || 10,
    statusIn: statusCustom,
    processType: processTypeCustom,
    goodsTypeEqual: 5,
  }
  // 处理搜索数据
  if (enableSearch) {
    requestObj = {
      ...requestObj,
      financeNoEqual: formatSearchData.financeNoEqual,
      goodsName: formatSearchData.goodsName,
      createTimeDateGe: formatSearchData.createTimeDateGe,
      createTimeDateLe: formatSearchData.createTimeDateLe,
    }
  }
  requestTableData(requestObj)
    .then(({ data }) => {
      tableLoading.value = false
      pageInitLoading.value = false
      if (data.success) {
        data = data.data
        tablePaginationData.value.currentPage = current
        tablePaginationData.value.maxPage = data.pages
        tablePaginationData.value.total = data.total
        tableData.value = data.records || []
      }
    })
    .catch(() => {
      tableLoading.value = false
      pageInitLoading.value = false
    })
}

const handleTableChange: any = (
  pag: TablePaginationConfig,
  filters: any,
  sorter: any
) => {
  if (!pag.current) return
  tablePaginationData.value.current = pag.current
  tablePaginationData.value.pageSize = pag.pageSize
  loadTableData(pag.current, pag.pageSize)
}

const initTableData = () => {
  loadTableData(
    tablePaginationData.value.currentPage,
    tablePaginationData.value.pageSize
  )
}

const handleFilterChange = (target: string, targetFilterData: any) => {
  filterData[target] = targetFilterData
  tablePaginationData.value = createInitPagData()
  initTableData()
}

const handleSearch = () => {
  enableSearch = true
  formatSearchData = {
    financeNoEqual: searchData.number,
    goodsName: searchData.name ? searchData.name.trim() : undefined,
    createTimeDateGe: searchData.time
      ? dayjs(searchData.time[0]).format('YYYY-MM-DD')
      : undefined,
    createTimeDateLe: searchData.time
      ? dayjs(searchData.time[1]).format('YYYY-MM-DD')
      : undefined,
  }
  tablePaginationData.value = createInitPagData()
  initTableData()
}

const handleSearchReset = () => {
  // 重置列表过滤项
  // goodsTypeFilterRef.value.filterClear()
  statusFilterRef.value.filterClear()
  for (const key in filterData) {
    filterData[key] = []
  }
  // 重置搜索内容
  enableSearch = false
  const initSearchData: any = createInitSearchData()
  for (const key in searchData) {
    searchData[key] = initSearchData[key]
  }
  // 重置分页
  tablePaginationData.value = createInitPagData()
  initTableData()
}

// 逾期协商提交成功刷新list
const refreshState = () => {
  initTableData()
}

// 自定义操作-查看详情
const handleDetail = (record: any) => {
  router.push({
    name: 'financingDetails',
    query: {
      id: record.id,
      goodId: record.goodsId,
      goodType: record.goodsType,
      customerGoodsId: record.customerGoodsId,
      lendingMethod: record.lendingMethod,
      chargeMethod: record.chargeMethod,
      canOpenDelayApply: record.canOpenDelayApply ? 1 : 2,
    },
  })
}

// 自定义操作-去修改
const handleToModify = (record: any) => {
  rowItem.value = { ...record } // 单行的数据
  let status = record.status
  if (
    isEnterpriseAccount.value &&
    !roleMap.value.financing_list_query_discharge_modify
  ) {
    dialogAuthority.value.handleOpen()
    return
  }
  if (status === 2) {
    // 融资申请
    router.push({
      name: 'appliCations',
      query: {
        id: record.id,
        goodId: record.goodsId,
        goodType: record.goodsType,
        financeNo: record.financeNo,
        customerGoodsId: record.customerGoodsId,
        lendingMethod: record.lendingMethod,
        chargeMethod: record.chargeMethod,
        capitalId: record.capitalId,
      },
    })
  } else if (status === 6) {
    // 放款申请
    router.push({
      name: 'loanApplication',
      query: {
        id: record.id,
        goodId: record.goodsId,
        goodType: record.goodsType,
        financeNo: record.financeNo,
        customerGoodsId: record.customerGoodsId,
      },
    })
  } else if (status === 16) {
    // 逾期协商
    handleNegotiate(record)
  } else if (status == 18) {
    // 展期申请
    getIouNos({ financeNo: record.financeNo }, { ...record, isEdit: true })
    // if (record.repaymentType == 1) {
    //   // 当期展期
    //   renewalApplyRef.value.handleOpen({ ...record, isEdit: true })
    // } else if (record.repaymentType == 2) {
    //   //分期
    //   renewalApplyPayRef.value.handleOpen({ ...record, isEdit: true })
    // }
  } else if (status == 21) {
    // 展期确认
    financeNo.value = record.financeNo
    dataObj.value = record
    renewalSubmitRef.value.handleOpen()
  }
}

// 自定义操作-申请放款
const handleApply = (contractId: any) => {
  if (
    isEnterpriseAccount.value &&
    !roleMap.value.financing_list_query_discharge_loan
  ) {
    dialogAuthority.value.handleOpen()
    return
  }
  router.push({
    name: 'loanApplication',
    query: {
      id: contractId.id,
      goodId: contractId.goodsId,
      goodType: contractId.goodsType,
      financeNo: contractId.financeNo,
      customerGoodsId: contractId.customerGoodsId,
    },
  })
}

// 自定义操作-自动放款流程提交申请
const handleProduct = (contractId: any) => {
  if (
    isEnterpriseAccount.value &&
    !roleMap.value.financing_list_query_discharge_Submit
  ) {
    dialogAuthority.value.handleOpen()
    return
  }
  router.push({
    name: 'appliCations',
    query: {
      id: contractId.id,
      goodId: contractId.goodsId,
      goodType: contractId.goodsType,
      financeNo: contractId.financeNo,
      customerGoodsId: contractId.customerGoodsId,
      lendingMethod: contractId.lendingMethod,
      chargeMethod: contractId.chargeMethod,
      capitalId: contractId.capitalId,
    },
  })
}

// 自定义操作-申请展期
const handlePeriod = (record: any) => {
  repayPlanRef.value.handleOpen()
  getIouNos({ financeNo: record.financeNo })

  // renewalApplyRef.value.handleOpen()
  // renewalSubmitRef.value.handleOpen()
}

// 自定义操作-展期确认
const handlePeriodSubmit = (record: any) => {
  financeNo.value = record.financeNo
  dataObj.value = record
  renewalSubmitRef.value.handleOpen()
}

// 自定义操作-逾期协商
const handleNegotiate = (item: any) => {
  dialogLimitNegotiate.value.handleOpen(item)
  // router.push({
  //   name: 'loanApplication',
  //   query: {
  //     id: contractId.id,
  //     goodId: contractId.goodsId,
  //     customerGoodsId: contractId.customerGoodsId,
  //   },
  // })
}

// 自定义操作-申请还款
// const handleReimApply = (contractId: any) => {
//   //
//   // router.push({
//   //   name: 'loanApplication',
//   //   query: {
//   //     id: contractId.id,
//   //     goodId: contractId.goodsId,
//   //     customerGoodsId: contractId.customerGoodsId,
//   //   },
//   // })
// }
// 获取还款单号
const getIouNos = async (params: any, rowParams: any) => {
  const { data } = await LOAN_REPAYMENT.requestGetIouNos(params)
  if (data.code === 200) {
    iouNosDetail.value = { ...data.data }

    // 判断修改按钮进入则需要去获取
    if (rowParams?.isEdit) {
      if (!data.data.iouNo) return
      const resData = await LOAN_REPAYMENT.requestRepaymentIouType({
        iouNos: data.data.iouNo,
      })
      if (resData.data.code === 200) {
        if (resData.data.data?.repaymentType == 1) {
          renewalApplyRef.value.handleOpen({
            ...rowParams,
            delayMode: resData.data.data.delayMode,
          })
        } else if (resData.data.data?.repaymentType == 2) {
          renewalApplyPayRef.value.handleOpen({
            ...rowParams,
            delayMode: resData.data.data.delayMode,
          })
        }
      }
    }
  }
}

initTableData()
</script>

<style lang="scss" scoped>
.financing-custom-search {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .search-input-container {
    display: inline-block;

    > * {
      margin-right: 20px;

      &:last-child {
        margin-right: 0;
      }
    }

    .ant-input,
    .ant-picker {
      height: 40px;
      line-height: 40px;
      border-radius: 4px;
    }

    .search-number,
    .search-name {
      width: 260px;
    }

    .search-time {
      width: 240px;

      :deep(.ant-picker-input) > input {
        text-align: center;
      }
    }
  }

  .search-control-container {
    display: inline-block;

    > * {
      margin-right: 20px;
      height: 40px;
      line-height: 40px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.financing-custom-table {
  margin-top: 24px;

  // 表格
  :deep(.ant-table) {
    // 去除表头分隔线
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      content: none;
    }

    .ant-table-thead {
      th {
        padding: 0 12px;
        height: 40px;
        line-height: 40px;
        background-color: #f8f9fb;
        font-size: 14px;
        @include family-PingFangSC-Medium;
        font-weight: 500;
        color: #8a94a6;
      }
    }

    .ant-table-tbody {
      // 鼠标停留背景色
      tr.ant-table-row:hover > td {
        background: #f5faff;
      }

      // 清除无数据时的边框
      .ant-table-placeholder > td {
        border: none;
      }

      td {
        padding: 6px 12px;
        height: 48px;
        line-height: 1.24;
        font-size: 14px;
        @include family-PingFangSC-Medium-SFProText;
        font-weight: 500;
        color: #0a1f44;

        .ant-statistic-content {
          line-height: 1.24;
          font-size: 14px;
          @include family-PingFangSC-Medium-SFProText;
          font-weight: 500;
          color: #0a1f44;

          .ant-statistic-content-prefix {
            margin-right: 0px;
          }
        }
      }
    }
  }

  // 表格自定义列
  .action-column-container {
    display: flex;

    & > span {
      margin-right: 8px;
      font-size: 14px;
      @include family-PingFangSC-Medium;
      font-weight: 500;
      color: #0d55cf;
      line-height: 20px;
      word-break: keep-all;
      cursor: pointer;

      a {
        color: #0d55cf;
      }

      &:hover {
        text-decoration: underline;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .single-line-text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }

  // 分页
  :deep(.ant-pagination) {
    display: flex;
    align-items: center;
    justify-content: center;
    // 去除分页下外边距
    margin-bottom: 0;

    .ant-pagination-disabled {
      .custom-pagination-item-link {
        color: #a6aebc !important;
        border: 1px solid transparent;
      }
    }

    .custom-pagination-item-link {
      background: #f8f9fb;
      color: #53627c;
    }

    .custom-pagination-left {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px 0 12px;
      border-radius: 100px 0px 0px 100px;
    }

    .custom-pagination-right {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px 0 16px;
      border-radius: 0px 100px 100px 0px;
    }
  }

  .empty-container {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 40px auto;
    text-align: center;

    img {
      width: 200px;
      height: 200px;
    }

    .desc {
      font-size: 14px;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }
  }
}
</style>
