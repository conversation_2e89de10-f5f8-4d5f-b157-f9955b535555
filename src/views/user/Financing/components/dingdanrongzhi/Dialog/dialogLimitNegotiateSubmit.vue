<template>
  <GlobalDialog
    title="逾期协商"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
    :maskClosable="false"
    :closeIconCustom="false"
    :onCloseIcon="onCloseIcon"
  >
    <div>
      <TotalReimbursement :dataObj="dataObj" />
    </div>
    <template #button>
      <div style="width: 100%; text-align: center">
        <n-button
          v-if="!signLastStep"
          class="blue border"
          style="height: 48px; margin-right: 5px"
          round
          :bordered="false"
          @click="handleCancel"
        >
          上一步
        </n-button>
        <n-button
          class="border blue primary"
          style="height: 48px; width: 286px"
          :style="{ width: !signLastStep ? '286px' : '97%' }"
          round
          :bordered="false"
          :loading="submitLoading"
          @click="handleConfirm"
        >
          确认提交
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'dialogLimitNegotiate',
}
</script>
<script setup>
import { ref, inject, computed } from 'vue'
import { useStore } from 'vuex'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import TotalReimbursement from './components/totalReimbursement.vue'
import { NButton } from 'naive-ui'
import { message } from 'ant-design-vue'
import { CREDIT } from '@/api/index'
import { contractListCheckId } from '@/views/product/component/contract/components/sign'

const store = useStore()

// 判断是否有合同未签署（没配置合同就不会判断）
const signStatus = computed(() => store.getters['Product/signStatus'])
const demonstrator = computed(() => store.getters['Product/demonstrator'])
// 判断是否有合同已经签署，有就禁止上一步按钮
const signLastStep = computed(() => store.getters['Product/signLastStep'])

// defineProps({
//   purposeLoanList: {
//     type: Array,
//     required: false,
//     default: () => [],
//   },
// })

const dialogWriteLimitNegotiateOpen = inject('dialogWriteLimitNegotiateOpen')
const dialogConfirmationOpen = inject('dialogConfirmationOpen')
const refreshStateFun = inject('refreshStateFun')

const dialogRef = ref(null)
const submitLoading = ref(false)
const dataObj = ref({})

const handleOpen = objD => {
  dataObj.value = objD
  dialogRef.value.handleOpen()
}

const handleCancel = () => {
  handleClose()
  dialogWriteLimitNegotiateOpen(dataObj.value)
}

const handleClose = () => {
  dialogRef.value.handleClose()
}

const handleConfirm = () => {
  if (signStatus.value && !demonstrator.value) {
    message.error('还有授权书未签署,请先签署完成')
    return
  }
  submitLoading.value = true
  const props = {
    processIndex: 12,
    receiveData: dataObj.value.bId,
    goodDatas: { goodId: dataObj.value.itemData.goodsId },
  }
  const data = { props, isC: false }
  // 获取合同签署的合同list的id
  contractListCheckId(data).then(({ idArr }) => {
    if (idArr) {
      const requestData = {
        customerGoodsId: dataObj.value.itemData.customerGoodsId,
        contractIdList: idArr,
        goodsId: dataObj.value.itemData.goodsId,
        goodsType: dataObj.value.itemData.goodsType,
        processType: dataObj.value.processType,
        enterpriseType: 1,
        businessId: dataObj.value.itemData.id,
      }
      CREDIT.commitOverdueConsultApply(requestData)
        .then(({ data }) => {
          if (data.success) {
            message.success('提交成功')
            refreshStateFun()
            handleClose()
            submitLoading.value = false
          }
        })
        .catch(() => {
          submitLoading.value = false
        })
    }
  })
}

const onCloseIcon = () => {
  // 签署了才进入
  if (signLastStep.value) {
    dialogConfirmationOpen(dataObj.value)
  } else {
    handleClose()
  }
}

defineExpose({
  handleOpen,
  handleClose,
})
</script>

<style lang="scss" scoped></style>
