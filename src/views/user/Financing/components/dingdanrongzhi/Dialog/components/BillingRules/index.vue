<template>
  <div class="availabel-amount-container">
    <span class="title">计费规则</span>
    <div class="availabel-content-wrapper">
      <div class="availabel-content">
        <span class="label">{{ objValue[keys]?.title }}</span>
        <span class="value">
          ￥{{ formatMoney(objValue[keys]?.loanAmount) }}
        </span>
      </div>
      <div class="availabel-content">
        <span class="label">{{ objValue[keys]?.countingDays }}</span>
        <span class="value">{{ objValue[keys]?.countingDaysStr }}</span>
      </div>
      <div class="desc-container">
        <span class="desc">{{ objValue[keys]?.shouldFormulaStr }}</span>
        <!-- <span class="desc"
          >{{ formatMoney(objValue[keys]?.v2) }}*10%/360*30={{
            formatMoney(objValue[keys]?.v3)
          }}元</span
        > -->
      </div>
      <p style="margin-top: 16px" />
      <template v-for="item in objValue[keys]?.list" :key="item.index">
        <div class="availabel-content" style="margin-bottom: 0">
          <span class="label">{{ item.label }}</span>
          <span class="value">￥{{ formatMoney(item.value) }}</span>
        </div>
        <div class="desc-container" style="margin-bottom: 8px">
          <span class="desc">{{ item.shouldFormulaNum }}</span>
        </div>
      </template>
    </div>
    <!-- <div class="confirm-content-wrapper">
      <div class="confirm-content">
        <span class="label">{{ objValue[keys]?.name2 }}</span>
        <span class="value"
          >{{ objValue[keys]?.name3 }}{{ objValue[keys]?.name4 }}</span
        >
      </div>
      <div class="desc-container">
        <span class="desc">{{ objValue[keys]?.date }}</span>
      </div>
    </div> -->
  </div>
</template>

<script lang="ts">
export default {
  name: 'RepaymentBillingRulesIndex',
}
</script>
<script lang="ts" setup>
import { formatMoney } from '@/utils/utils'

defineProps({
  keys: {
    type: String,
    require: true,
  },
})
import { inject, ref } from 'vue'

const datilData = inject('datilData')

const datilDataV = ref({})
const objValue = ref({})

datilDataV.value = datilData.value

const keyObj = datilDataV.value.loanManageOverdueCalculationResultMap
for (const key in keyObj) {
  if (key === 'shouldOverdueInterest') {
    const listData = keyObj[key]
    const listObjData = {}
    const list = []
    let indexN = 1
    if (listData.length) {
      if (listData[0].repaymentType === 1) {
        listObjData.repaymentType = 1
        listObjData.repaymentTypeStr = '期'
        listObjData.countingStr = '逾期期数'
        const termList = []
        for (const item of listData) {
          termList.push(item.term)
        }
        listObjData.termstr = termList.join('、')
        for (const item of listData) {
          list.push({
            index: indexN,
            label: `${item.term}期逾期利息`,
            value: item.repaymentExpenseResp.shouldOverdueInterest,
            shouldFormulaNum:
              item.repaymentExpenseResp.shouldInterestValueFormula,
          })
          indexN++
        }
      } else {
        listObjData.repaymentType = 2
        listObjData.repaymentTypeStr = '天'
        listObjData.countingStr = '逾期天数'
        listObjData.termstr = listData[0].term
        for (const item of listData) {
          list.push({
            index: indexN,
            label: '逾期利息',
            value: item.repaymentExpenseResp.shouldOverdueInterest,
            shouldFormulaNum:
              item.repaymentExpenseResp.shouldInterestValueFormula,
          })
          indexN++
        }
      }
      listObjData.shouldFormulaStr =
        listData[0].repaymentExpenseResp.shouldInterestFormula

      objValue.value.overdue = {
        title: '总逾期利息',
        loanAmount: datilDataV.value.shouldOverdueInterest,
        countingDays: listObjData.countingStr,
        countingDaysStr: `${listObjData.termstr}${listObjData.repaymentTypeStr}`,
        shouldFormulaStr: listObjData.shouldFormulaStr,
        list,
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.availabel-amount-container {
  .title {
    display: inline-block;
    margin-bottom: 16px;
    font-size: 16px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #fefefe;
    line-height: 24px;
  }

  .availabel-content-wrapper,
  .confirm-content-wrapper {
    .availabel-content,
    .confirm-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .label {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #f3f3f3;
        line-height: 20px;
        margin-right: 25px;
      }

      .value {
        font-size: 16px;
        font-family: CoreSansD65Heavy;
        color: #fdfdfd;
        line-height: 20px;
      }
    }

    .desc-container {
      display: flex;
      flex-direction: column;

      .desc {
        display: block;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #d5d5d5;
        line-height: 16px;
      }
    }
  }

  .confirm-content-wrapper {
    margin-top: 16px;
  }
}
</style>
