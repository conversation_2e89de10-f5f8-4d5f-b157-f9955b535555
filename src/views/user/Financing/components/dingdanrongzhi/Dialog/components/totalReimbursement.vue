<template>
  <div class="negotiate-contract-show-card-box">
    <div class="info-box">
      <div class="title-type-box">
        <span>总计</span>
      </div>
      <a-spin :spinning="numLoad">
        <div class="num-box">
          <span class="monetary-unit">￥</span>
          <p class="amount-box" @click="handleExhibition">
            <span class="amount-num">
              {{ formatMoney(numData) }}
            </span>
            <MySvgIcon
              icon-class="icon-youjiantou"
              style="font-size: 23px; fill: #758196"
            />
          </p>
        </div>
      </a-spin>
    </div>
    <!-- 合同 -->
    <Contract
      style="margin-top: -66px"
      itemWidth="100%"
      :processIndex="12"
      :flowingWave="true"
      :noDataType="true"
      :receiveData="dataObj.bId"
      :goodDatas="{
        goodId: dataObj.itemData.goodsId,
        goodType: dataObj.itemData.goodsType,
      }"
    />
    <!-- 还款计试算弹窗 -->
    <DialogReimburSement
      ref="dialogReimburSement"
      :allNumMoney="dataObj.datilData.surplusPrincipal"
      :penaltyObj="{
        overdueInterest: loanMData.penaltyInterest, // 逾期罚息
        penaltyInterest: loanMData.breakOutAmount, // 违约金
        processInterest: loanMData.serviceFee, // 手续费
      }"
      :loanPeriod="loanMData.totalTerm"
      :startTime="loanMData.refundStartTime"
      :loadTermUnit="loanMData.repaymentType === 2 ? 1 : 2"
      :filterArr="filterArr"
      :tabBarObj="{
        tabBarIndex: 0,
        tabBarIndexVal: void 0,
      }"
      :annualInterestRateObj="annualInterestRateObj"
      :financeNoId="dataObj.itemData.id"
      :showExpenseList="showExpenseList"
      :isNegotiate="true"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'totalReimbursement',
}
</script>
<script lang="ts" setup>
import DialogReimburSement from '@/views/product/appliCations/components/financingDemand/components/Dialog/dialogReimburSement.vue'
import Contract from '@/views/product/component/contract'
import { formatMoney, calculateDayRateFun } from '@/utils/utils'
import { ref, reactive, provide } from 'vue'
import { CREDIT, PRODUCT_APPLI_CATIONS_API } from '@/api/index'
import { requestDictMap } from '@/api/common/index'

const props = defineProps({
  dataObj: {
    type: Object,
    required: true,
  },
})

const dialogReimburSement = ref(null)
const loanMData = ref({})
const showExpenseList = ref(true)
const filterArr = reactive({
  filterTabBar: [],
  filterTabBarVal: [],
})
const annualInterestRateObj = reactive({
  annualInterestRate: '',
  dailyInterestRate: '',
})
provide('labelListFunc', () => filterArr.filterTabBar) // 随借随还 等额本息等
// 总计数值
const numLoad = ref(true)
const numData = ref(0)

const repaymentCalculationFun = () => {
  const surplusPrincipalP = props.dataObj.datilData.surplusPrincipal
  const loadTermUnitD = loanMData.value.repaymentType === 2 ? 1 : 2
  if (!surplusPrincipalP) return // 金额不能为0
  const data = {
    financeAmount: surplusPrincipalP, // 融资金额
    overdueInterest: loanMData.value?.overdueInterest || 0, // 逾期罚息
    penaltyInterest: loanMData.value?.penaltyInterest || 0, // 违约金
    processInterest: loanMData.value?.processInterest || 0, // 手续费
    annualInterestRate: annualInterestRateObj.annualInterestRate, // 年利率
    totalTerm: loanMData.value.totalTerm, // 总期数
    startTime: loanMData.value.refundStartTime, // 开始时间
    refundType: filterArr.filterTabBarVal[0], // 还款类型 '1','2','3'
    loadTermUnit: loadTermUnitD, // 1-天,2-期
  }
  PRODUCT_APPLI_CATIONS_API.repaymentCalculation(data).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      const dat = resData.data
      if (loadTermUnitD == 1) {
        numData.value = dat.stagRecords?.[0].monthlySupply
      } else {
        numData.value =
          dat.stagRecords?.[dat.stagRecords.length - 1].monthlySupply
      }
      numData.value =
        Number(props.dataObj.datilData.platFromServiceFee) +
        Number(numData.value) // 加上平台服务费 有的话
      numLoad.value = false
    }
  })
}

const onLoad = async () => {
  // 费用是独立收取的话 不显示还款试算的其他费用
  if (props.dataObj.itemData.chargeMethod === 2) {
    showExpenseList.value = false
  }
  const resList = []
  await requestDictMap('goods_billing_method').then(res => {
    const resData = res.data
    if (resData.code == 200) {
      // 处理字典数据
      for (const item of resData.data) {
        resList.push({
          key: item.dictKey,
          value: item.dictValue,
          id: item.id,
        })
      }
    }
  })
  await CREDIT.loanManageOverdueConsultDetail({
    financeId: props.dataObj.itemData.id,
  }).then(async ({ data }) => {
    if (data.success) {
      const { data: resData } = data
      loanMData.value = resData
      // 还款方式
      if (resData.repaymentType !== 1) {
        filterArr.filterTabBar = ['随借随还']
        filterArr.filterTabBarVal = ['-1']
      } else {
        const filArr = resList.filter(
          itemed => itemed.key == resData.repaymentMode
        )
        filterArr.filterTabBar = [filArr[0]?.value]
        filterArr.filterTabBarVal = [filArr[0]?.key]
      }
      annualInterestRateObj.annualInterestRate = resData.interestRate
      // annualInterestRateObj.dailyInterestRate = (
      //   Number(resData.interestRate) / 360
      // ).toFixed(3)
      annualInterestRateObj.dailyInterestRate = await calculateDayRateFun(
        resData.interestRate
      )
    }
  })
  // 总计数值接口
  await repaymentCalculationFun()
}
onLoad()

// 还款试算弹窗
const handleExhibition = () => {
  dialogReimburSement.value.handleOpen() // 打开弹窗
  dialogReimburSement.value.onload() // 初始化数据
}
</script>

<style lang="scss" scoped>
.negotiate-contract-show-card-box {
  .info-box {
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 11px 11px 11px 11px;
    border: 1px solid #efefef;
    width: 100%;
    padding: 16px;
    box-sizing: border-box;

    .title-type-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      font-weight: 400;
      color: #0a1f44;
      line-height: 20px;

      .type1 {
        width: 56px;
        height: 24px;
        border-radius: 0px 0px 0px 0px;
        font-size: 12px;
        font-weight: 400;
        color: #dd2727;
        border: 1px solid #dd2727;
        text-align: center;
        line-height: 22px;
        border-radius: 12px;
      }
    }
    .num-box {
      .monetary-unit {
        font-size: 20px;
        font-weight: 400;
        color: #0a1f44;
      }
      .amount-box {
        display: inline-flex;
        align-items: center;
        cursor: pointer;

        .amount-num {
          font-size: 42px;
          font-weight: 400;
          color: #0a1f44;
        }
        .monetary-operate {
          font-size: 25px;
          font-weight: 400;
          color: #0a1f44;
          margin-left: 3px;
        }
      }
    }
  }
}
</style>
