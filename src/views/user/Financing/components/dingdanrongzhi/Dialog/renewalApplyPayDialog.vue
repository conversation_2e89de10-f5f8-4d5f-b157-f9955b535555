<template>
  <GlobalDialog
    title="展期申请"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
    :onClose="handleClose"
  >
    <a-form
      :model="formData"
      class="apply-form"
      ref="formRef"
      autocomplete="off"
      :label-col="{ span: 24 }"
      :wrapper-col="{ span: 24 }"
      :rules="rules"
    >
      <a-form-item label="展期方式" name="delayMode">
        <a-select
          @change="handleModeChange"
          style="height: 48px"
          v-model:value="formData.delayMode"
          :options="modeOptions"
          placeholder="请选择展期方式"
        ></a-select>
      </a-form-item>
      <a-form-item label="展期金额" name="amount">
        <a-input-number
          disabled
          class="input-number"
          :min="0"
          style="width: 100%; height: 48px"
          v-model:value="formData.amount"
          prefix="元"
          :controls="false"
          placeholder="请输入展期金额"
          :formatter="value => numMillimeter(value)"
          :parser="value => numFormat(value)"
        >
        </a-input-number>
      </a-form-item>

      <a-form-item
        label="展期期限"
        name="delayTerm"
        v-if="formData.delayMode == 1"
      >
        <a-input-number
          class="input-number"
          :min="0"
          style="width: 100%; height: 48px"
          v-model:value="formData.delayTerm"
          prefix="期"
          :controls="false"
          placeholder="请输入展期期限"
          :formatter="value => value.replace(/^(0+)|[^\d]+/g, '')"
          :parser="value => numFormat(value)"
        >
        </a-input-number>
      </a-form-item>
      <a-form-item
        label="计费方式"
        name="repaymentMode"
        v-if="formData.delayMode == 1"
      >
        <a-select
          style="height: 48px"
          v-model:value="formData.repaymentMode"
          :options="repaymentModeOptions"
          placeholder="请选择计费方式"
        ></a-select>
      </a-form-item>
      <a-form-item
        label="最低还款金额"
        name="repaymentMoneyMin"
        v-if="formData.delayMode == 2"
      >
        <a-input-number
          class="input-number"
          :min="0"
          style="width: 100%; height: 48px"
          v-model:value="formData.repaymentMoneyMin"
          prefix="元"
          :controls="false"
          placeholder="请输入最低还款金额"
          :formatter="value => numMillimeter(value)"
          :parser="value => numFormat(value)"
        >
        </a-input-number>
      </a-form-item>
      <a-form-item
        label="延迟还款天数"
        name="delayDays"
        v-else-if="formData.delayMode == 3"
      >
        <a-input-number
          class="input-number"
          :min="0"
          style="width: 100%; height: 48px"
          v-model:value="formData.delayDays"
          prefix="天"
          :controls="false"
          placeholder="请输入延迟还款天数"
          :formatter="value => value.replace(/^(0+)|[^\d]+/g, '')"
          :parser="value => numFormat(value)"
        >
        </a-input-number>
      </a-form-item>
      <!-- <a-form-item label="展期开始日" name="loanDelayStartDate">
        <a-date-picker
          style="width: 100%; height: 48px"
          v-model:value="formData.loanDelayStartDate"
          value-format="YYYY-MM-DD"
          placeholder="请选择展期开始日"
        />
      </a-form-item> -->
      <a-form-item label="展期理由" name="reason">
        <a-textarea
          style="height: 48px"
          v-model:value="formData.reason"
          show-count
          :maxlength="200"
          :auto-size="{ minRows: 4, maxRows: 10 }"
          placeholder="请输入申请展期理由并阐述还款计划"
        />
      </a-form-item>
    </a-form>
    <template #button>
      <n-button
        class="confirm-login-btn blue border primary"
        style="flex-grow: 1; height: 48px"
        :bordered="false"
        round
        :loading="loading"
        @click="handleSubmitForm"
        >确认提交</n-button
      >
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'RenewalApplyDialog',
  data() {
    return {}
  },
}
</script>

<script setup>
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { reactive, ref, watch } from 'vue'
import { NButton } from 'naive-ui'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { numFormat, numMillimeter } from '@/utils/utils'
import { LOAN_REPAYMENT } from '@/api/index.js'
import { requestDictMap } from '@/api/common/index'
const dialogRef = ref(null) // 调用子组件的弹窗方法

const modeOptions = ref([
  {
    label: '分期还款',
    value: 1,
  },
  // {
  //   label: '最低还款',
  //   value: 2,
  // },
  {
    label: '延迟还款',
    value: 3,
  },
])
const repaymentModeOptions = ref([]) // 计费方式
const formRef = ref(null)
const loading = ref(false)
const router = useRouter()
const emit = defineEmits(['refresh'])
const delayModeDispose = ref(null) // 记录产品配置的展期方式
const isEdit = ref(false) // 进入弹窗的入口，true是从修改进入
const loanDetail = reactive({
  shouldAmount: 0,
  isEdit: false, // 进入弹窗的入口，true是从修改进入
  financeNo: null,
})

const rules = {
  amount: [{ required: true, message: '请输入展期金额' }],
  delayMode: [{ required: true, message: '请选择展期方式', trigger: 'change' }],
  delayTerm: [{ required: true, message: '请输入展期金额' }],
  delayDays: [{ required: true, message: '请输入延迟还款天数' }],
  reason: [{ required: true, message: '请输入展期理由' }],
  repaymentMoneyMin: [{ required: true, message: '请输入最低还款金额' }],
  repaymentMode: [{ required: true, message: '请选择计费方式' }],
  loanDelayStartDate: [{ required: true, message: '请选择展期开始日' }],
}
// 表单的值
const formData = reactive({
  amount: null,
  delayMode: null,
  delayTerm: null,
  delayDays: null,
  reason: null,
  repaymentMoneyMin: null,
  repaymentMode: null,
  id: null,
  loanDelayStartDate: null,
})

// 展期方式切换
const handleModeChange = () => {
  /**
   * 分期还款清空最低还款金额、延迟还款天数
   * 最低还款清空展期期限、延迟还款天数
   *  延迟还款清空展期期限、最低还款金额
   */
  if (formData.delayMode == 1) {
    formData.delayDays = null
    formData.repaymentMoneyMin = null
    formData.repaymentMode = null
  } else if (formData.delayMode == 2) {
    formData.delayTerm = null
    formData.delayDays = null
  } else if (formData.delayMode == 3) {
    formData.delayTerm = null
    formData.repaymentMoneyMin = null
  }
}

const handleOpen = ({ isEdit, financeNo, shouldAmount, delayMode }) => {
  let list = [...modeOptions.value]
  loanDetail.isEdit = isEdit ? true : false
  loanDetail.shouldAmount = shouldAmount || null
  loanDetail.financeNo = financeNo || null
  delayModeDispose.value = delayMode
  let delayModess = delayMode.split(',')
  modeOptions.value = list.filter(
    item => delayModess.findIndex(child => child == item.value) > -1
  )
  // isEdit为true时，则说明从修改处来, 展期金额则从接口处获取
  if (isEdit) {
    getLoanDelayDetail({ financeNo })
  } else {
    formData.amount = shouldAmount || 0
  }
  getDictData()
  // 打开弹窗
  dialogRef.value.handleOpen()
}
const handleClose = () => {
  formData.amount = null
  formData.delayMode = null
  formData.delayTerm = null
  formData.delayDays = null
  formData.reason = null
  formData.repaymentMoneyMin = null
  formData.id = null
  formData.loanDelayStartDate = null
  formData.repaymentMode = null
}
const handleCloseDialog = () => {
  // 关闭弹窗
  dialogRef.value.handleClose()
}

// 提交表单的操作
const handleSubmitForm = () => {
  formRef.value
    .validate()
    .then(() => {
      let params = {
        financeNo: loanDetail.financeNo,
        amount: formData.amount,
        delayMode: formData.delayMode,
        reason: formData.reason,
        repaymentType: 2,
        loanDelayStartDate: formData.loanDelayStartDate,
      }
      if (loanDetail.isEdit) {
        Object.assign(params, { id: formData.id })
      }
      if (formData.delayMode == 1) {
        Object.assign(params, {
          delayTerm: formData.delayTerm,
          repaymentMode: formData.repaymentMode,
        })
      } else if (formData.delayDays == 2) {
        Object.assign(params, { repaymentMoneyMin: formData.repaymentMoneyMin })
      } else if (formData.delayMode == 3) {
        Object.assign(params, { delayDays: formData.delayDays })
      }
      submitApply(params)
    })
    .catch(() => {
      message.warning('请完善信息！')
    })
}

// 申请展期
const submitApply = async params => {
  try {
    loading.value = true
    const { data } = await LOAN_REPAYMENT.requestRepaymentSave(params)
    if (data.code === 200) {
      // 申请成功之后关闭弹窗
      message.success('成功申请!')
      dialogRef.value.handleClose()
      // 在当前路由下关闭弹窗，强制刷新
      if (router.currentRoute.value.path === '/user/financing') {
        location.reload()
      }
      router.push({ path: '/user/financing' })
    }
  } finally {
    loading.value = false
  }
}

// 展期申请数据回显
const getLoanDelayDetail = async params => {
  const { data } = await LOAN_REPAYMENT.requstDelayDetail(params)
  if (data.code === 200) {
    formData.amount = data.data?.amount
    formData.type = data.data?.type
    formData.reason = data.data?.reason
    formData.delayMode = data.data?.delayMode
    formData.delayDays = data.data?.delayDays
    formData.delayTerm = data.data?.delayTerm
    formData.id = data.data?.id
    formData.repaymentMode = data.data?.repaymentMode
    formData.loanDelayStartDate = data.data?.loanDelayStartDate
  }
}

// 获取计费方式
const getDictData = async () => {
  let list = []
  const { data } = await requestDictMap('goods_billing_method')
  let resData = data.data || []
  if (data.code === 200) {
    for (const item of resData) {
      list.push({
        label: item.dictValue,
        value: Number(item.dictKey),
      })
    }
  }
  repaymentModeOptions.value = list
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.apply-form {
  margin-top: 24px;
  & :deep(.ant-form-item:last-child) {
    margin-bottom: 0 !important;
    & .ant-btn {
      border-radius: 100px;
    }
    .btn-cancel {
      margin-right: 8px;
    }
  }
}
:deep() {
  .ant-input-number-input {
    height: 48px;
    position: relative;
  }
  .ant-input-number-prefix {
    order: 2;
    margin-inline-end: 11px !important;
    margin-inline-start: 4px;
  }
}
</style>
