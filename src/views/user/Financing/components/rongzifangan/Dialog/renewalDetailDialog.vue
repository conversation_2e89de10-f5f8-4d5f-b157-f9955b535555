<template>
  <GlobalDialog
    title="还款试算"
    width="439px"
    ref="dialogRef"
    :enableFooterSlot="false"
    :enableFullscreen="false"
    :maskClosable="false"
    :onClose="handleClose"
  >
    <div class="main-box">
      <div class="main-head">
        <p class="plan-repay-title">待还总额</p>
        <p class="plan-repay-amount">
          <span>￥</span><span>{{ amount }}</span>
        </p>
        <p class="plan-repay-subtitle">每月还款日从华夏银行宝安支行自动还款</p>
      </div>
      <div class="main-list">
        <DetailCard
          v-for="(item, index) in listData"
          :key="item.term + index"
          @click="handleClick(item)"
          :title="`第${item.term}期应还`"
          :subtitle="item.refundTime"
          :name="`￥${formatMoney(item.monthlySupply || 0)}`"
          card-style="padding: 14px 28px; margin-bottom: 10px"
        ></DetailCard>
      </div>
    </div>
  </GlobalDialog>

  <!-- 单期展期详情 -->
  <PayableDetailsDialog ref="payableDetailsRef" :row="planRow" :fee="fee"/>
</template>

<script>
export default {
  name: 'RepayPlanDialog',
}
</script>
<script setup>
import { ref, watch } from 'vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import DetailCard from '@/components/BaseCard/detailCard/index.vue'
import { NButton } from 'naive-ui'
import PayableDetailsDialog from './payableDetailsDialog.vue'
import RenewalApplyDialog from './renewalApplyDialog.vue'
import { LOAN_REPAYMENT } from '@/api/index.js'
import { formatMoney } from '@/utils/utils'
import dayjs from 'dayjs'
import BigNumber from 'bignumber.js'

const dialogRef = ref(null)
const payableDetailsRef = ref(null) // 还款明细弹窗
const renewalApplyRef = ref(null) // 展期申请弹窗
const listData = ref([]) // 计划的数据
const amount = ref(0) // 待还总额
const planRow = ref({}) // 单个的还款计划
const props = defineProps({
  fee:{
    type:Boolean,
    defaule:false
  }
})

// 打开弹窗
const handleOpen = ({ financeNo }) => {
  if (financeNo) {
    getRepaymentPlan({ financeNo })
    dialogRef.value.handleOpen()
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogRef.value.handleClose()
}
const handleClick = row => {
  planRow.value = {
    ...row,
    principal: row.monthlyPrincipal,
    interest: row.monthlyInterest,
    totalAmount: row.monthlySupply,
    shouldAmount: amount.value,
    repaymentAccount: '华夏银行宝安支行',
  }
  payableDetailsRef.value.handleOpen()
}

// 确认
const handleConfirm = () => {
  renewalApplyRef.value.handleOpen()
}

const getRepaymentPlan = async params => {
  const { data } = await LOAN_REPAYMENT.requestGetRepaymentPlan(params)
  let list = []
  let resData = data.data || []
  let resAmount = new BigNumber(0)
  if (data.code === 200) {
    for (const item of resData) {
      item.refundTime = item.refundTime
        ? dayjs(item.refundTime).format('YYYY/MM/DD')
        : ''
      list.push(item)
      resAmount = resAmount.plus(item.monthlySupply)
    }
  }
  amount.value = formatMoney(resAmount.toNumber())
  listData.value = list
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.main-box {
  .main-head {
    width: 100%;
    border: 1px solid #efefef;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 8px;
    .plan-repay-title {
      line-height: 20px;
      font-size: 14px;
      font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
      font-weight: 400;
      color: #0a1f44;
    }
    .plan-repay-amount {
      margin-top: 8px;
      line-height: 48px;
      font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
      font-weight: 400;
      color: #021222;
      span:nth-of-type(1) {
        font-size: 24px;
      }
      span:nth-of-type(2) {
        font-size: 40px;
      }
    }
    .plan-repay-subtitle {
      margin-top: 8px;
      line-height: 20px;
      font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
      font-weight: 400;
      color: #8a94a6;
    }
  }
}
</style>
