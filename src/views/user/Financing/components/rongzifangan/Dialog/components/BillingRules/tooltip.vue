<template>
  <a-tooltip overlayClassName="billing-rules-tooltip" placement="bottom">
    <div class="billing-text-box">
      <MySvgIcon
        icon-class="icon-xinxi"
        style="color: #8a94a6; font-size: 20px"
      />
    </div>
    <template #title>
      <BillingRules :keys="keys" />
    </template>
  </a-tooltip>
</template>

<script lang="ts" setup>
import BillingRules from './index.vue'
defineProps({
  keys: {
    type: String,
    require: true,
  },
})
</script>

<style lang="scss">
.billing-rules-tooltip {
  max-width: none;
  .ant-tooltip-inner {
    padding: 16px;
    border-radius: 8px;
    // background: rgba(0, 0, 0, 0.6);
    box-shadow: 0px 4px 24px 0px rgba(10, 31, 68, 0.2),
      0px 0px 1px 0px rgba(10, 31, 68, 0.08);
    // border: 1px solid #9ba1a5;
    backdrop-filter: blur(4px);
  }
}
</style>
<style lang="scss" scoped>
.billing-text-box {
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  & > span:last-child {
    width: 28px;
    height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
    margin-left: 4px;
  }
}
</style>
