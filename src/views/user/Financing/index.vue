<template>
  <div class="financing-list-container">
    <div class="header-box">
      <UserHeader headerName="融资列表" iconClass="icon-yinhang" />
      <div class="label-bar-container">
        <LabelBar
          v-if="labelData.labelList.length > 0"
          :labelList="labelData.labelList"
          :state="labelData.currentIndex"
          @switch="handleSwitch"
        />
      </div>
    </div>

    <div class="table-container">
      <FinancingListTable
        v-if="labelData.labelList[labelData.currentIndex] === '应收账款质押'"
        ref="FinancingTableRef"
      />
      <CloudListTable
        v-else-if="labelData.labelList[labelData.currentIndex] === '云信'"
      ></CloudListTable>
      <GenerationTable
        v-else-if="labelData.labelList[labelData.currentIndex] === '代采融资'"
      ></GenerationTable>
      <PledgeMovablesTable
        v-else-if="labelData.labelList[labelData.currentIndex] === '动产质押'"
      ></PledgeMovablesTable>
      <!-- <DingDanFinancingListTable
        v-if="labelData.labelList[labelData.currentIndex] === '订单融资'"
        ref="dingDanFinancingListTableRef"
      /> -->
      <!-- 融资方案 -->
      <FinancingPlanListTable
        v-if="labelData.labelList[labelData.currentIndex] === '订单融资'"
        ref="financingPlanListTableRef"
      />
    </div>
  </div>
</template>

<script lang="ts">
const ModuleName = 'UserFinancingListIndex'

export default {
  name: ModuleName,
}
</script>
<script lang="ts" setup>
import { reactive, computed, watchEffect, watch } from 'vue'
import { useStore } from 'vuex'
import UserHeader from '@/views/user/components/Header/index.vue'
import FinancingListTable from './components/table.vue'
import DingDanFinancingListTable from './components/dingdanrongzhi/table.vue'
import FinancingPlanListTable from './components/rongzifangan/table.vue'
import CloudListTable from './components/CloudListTable.vue'
import GenerationTable from '@/views/generat/generation/GenerationTable.vue'
import PledgeMovablesTable from '@/views/product/chattelMortgage/financingListTable/table.vue'
import LabelBar from '@/views/user/components/LabelBar/index.vue'

const store = useStore()
const storeCurrentLabelIndex = computed<number>(() =>
  store.getters['ServerCenter/currentActiveLabelIndex'](ModuleName)
)
const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
const isEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isEnterpriseAccount']
)
const roleMap = computed(() => store.getters['Role/roleMap'])
const showProductType = computed(() => store.getters['Role/showProductType'])
const labelData = reactive<any>({
  labelList: [],
  currentIndex: 0,
})

// 将 Store 里面保存的 Label 菜单更新到当前页面
watchEffect(() => {
  labelData.currentIndex = storeCurrentLabelIndex.value || 0
})

// 已登录后生成 Label 菜单
watch(
  () => [roleMap.value, isLogined.value],
  ([roleMap, isLogined]) => {
    if (!roleMap || !isLogined) return
    labelData.labelList = []
    if (
      (!isEnterpriseAccount.value || roleMap.financing_query) &&
      showProductType.value[1]
    )
      labelData.labelList.push('应收账款质押')
    if (
      (!isEnterpriseAccount.value || roleMap.financing_financing) &&
      showProductType.value[3]
    )
      labelData.labelList.push('云信')
    if (
      (!isEnterpriseAccount.value || roleMap.purchase_financing) &&
      showProductType.value[2]
    )
      labelData.labelList.push('代采融资')
    if (
      (!isEnterpriseAccount.value || roleMap.purchase_financing) &&
      showProductType.value[4]
    )
      labelData.labelList.push('动产质押')
    if (
      (!isEnterpriseAccount.value || roleMap.financing_query) &&
      showProductType.value[5]
    )
      labelData.labelList.push('订单融资')

    // labelData.labelList.push('融资方案')
  },
  { immediate: true }
)

// Label 栏切换
const handleSwitch = (targetIndex: number) => {
  const currentModuleIndexObj: any = {}
  currentModuleIndexObj[ModuleName] = targetIndex
  store.commit('ServerCenter/setActiveLabelIndexMap', currentModuleIndexObj)
}
</script>

<style lang="scss" scoped>
.financing-list-container {
  padding: 24px;
  .table-container {
    padding-top: 24px;
  }
}
</style>
