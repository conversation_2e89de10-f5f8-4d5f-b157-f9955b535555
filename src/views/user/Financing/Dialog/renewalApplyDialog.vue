<template>
  <GlobalDialog
    title="展期申请"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
    :onClose="handleClose"
  >
    <a-form
      :model="formData"
      class="apply-form"
      ref="formRef"
      autocomplete="off"
      :label-col="{ span: 24 }"
      :wrapper-col="{ span: 24 }"
      :rules="rules"
    >
      <a-form-item label="展期类型" name="type">
        <a-select
          @change="handleTypeChange"
          style="height: 48px"
          v-model:value="formData.type"
          :options="typeOptions"
          placeholder="请选择展期类型"
        ></a-select>
      </a-form-item>
      <a-form-item label="展期金额" name="amount">
        <a-input-number
          disabled
          class="input-number"
          :min="0"
          style="width: 100%; height: 48px"
          v-model:value="formData.amount"
          prefix="元"
          :controls="false"
          placeholder="请输入展期金额"
          :formatter="value => numMillimeter(value)"
          :parser="value => numFormat(value)"
        >
        </a-input-number>
      </a-form-item>
      <a-form-item label="展期方式" name="delayMode">
        <a-select
          style="height: 48px"
          v-model:value="formData.delayMode"
          :options="modeOptions"
          placeholder="请选择展期方式"
        ></a-select>
      </a-form-item>
      <a-form-item
        label="展期期限"
        name="delayTerm"
        v-if="formData.delayMode == 1"
      >
        <a-input-number
          class="input-number"
          :min="0"
          style="width: 100%; height: 48px"
          v-model:value="formData.delayTerm"
          prefix="期"
          :controls="false"
          placeholder="请输入展期期限"
          :formatter="value => value.replace(/^(0+)|[^\d]+/g, '')"
          :parser="value => numFormat(value)"
        >
        </a-input-number>
      </a-form-item>
      <a-form-item
        label="计费方式"
        name="repaymentMode"
        v-if="formData.delayMode == 1"
      >
        <a-select
          style="height: 48px"
          v-model:value="formData.repaymentMode"
          :options="repaymentModeOptions"
          placeholder="请选择展期方式"
        ></a-select>
      </a-form-item>
      <a-form-item
        label="延迟还款天数"
        name="delayDays"
        v-else-if="formData.delayMode == 3"
      >
        <a-input-number
          class="input-number"
          :min="0"
          style="width: 100%; height: 48px"
          v-model:value="formData.delayDays"
          prefix="天"
          :controls="false"
          placeholder="请输入延迟还款天数"
          :formatter="value => value.replace(/^(0+)|[^\d]+/g, '')"
          :parser="value => numFormat(value)"
        >
        </a-input-number>
      </a-form-item>
      <!-- <a-form-item label="展期开始日" name="loanDelayStartDate">
        <a-date-picker
          style="width: 100%; height: 48px"
          v-model:value="formData.loanDelayStartDate"
          value-format="YYYY-MM-DD"
          placeholder="请选择展期开始日"
        />
      </a-form-item> -->
      <a-form-item label="展期理由" name="reason">
        <a-textarea
          style="height: 48px"
          v-model:value="formData.reason"
          show-count
          :maxlength="200"
          :auto-size="{ minRows: 4, maxRows: 10 }"
          placeholder="请输入申请展期理由并阐述还款计划"
        />
      </a-form-item>
    </a-form>
    <template #button>
      <n-button
        class="confirm-login-btn blue border primary"
        style="flex-grow: 1; height: 40px"
        :bordered="false"
        round
        :loading="loading"
        @click="handleSubmitForm"
        >确认提交</n-button
      >
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'RenewalApplyDialog',
  data() {
    return {}
  },
}
</script>

<script setup>
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { reactive, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NButton } from 'naive-ui'
import { message } from 'ant-design-vue'
import { formatMoney, numFormat, numMillimeter } from '@/utils/utils'
import { LOAN_REPAYMENT } from '@/api/index.js'
import { requestDictMap } from '@/api/common/index'

const props = defineProps({
  rId: {
    type: String,
  },
})

const dialogRef = ref(null) // 调用子组件的弹窗方法
const typeOptions = ref([
  {
    label: '当期展期',
    value: 1,
  },
  {
    label: '剩余待还全部展期',
    value: 2,
  },
]) // 展期数据
const repaymentModeOptions = ref([]) // 计费方式
const formRef = ref(null)
const loading = ref(false)
const router = useRouter()
const route = useRoute()
const emit = defineEmits(['refresh'])
const delayModeDispose = ref(null) // 记录产品配置的展期方式
const modeOptions = ref([])
const loanDetail = reactive({
  rowAmount: 0,
  shouldAmount: 0,
  isEdit: false, // 进入弹窗的入口，true是从修改进入
  financeNo: null,
})

const rules = {
  type: [{ required: true, message: '请选择展期类型', trigger: 'change' }],
  amount: [{ required: true, message: '请输入展期金额' }],
  delayMode: [{ required: true, message: '请选择展期方式', trigger: 'change' }],
  delayTerm: [{ required: true, message: '请输入展期金额' }],
  delayDays: [{ required: true, message: '请输入延迟还款天数' }],
  reason: [{ required: true, message: '请输入展期理由' }],
  repaymentMode: [{ required: true, message: '请选择计费方式' }],
  loanDelayStartDate: [{ required: true, message: '请选择展期理由' }],
}
// 表单的值
const formData = reactive({
  type: null,
  amount: null,
  delayMode: null,
  delayTerm: null,
  delayDays: null,
  reason: null,
  repaymentMode: null,
  id: null,
  loanDelayStartDate: null,
})

watch(
  () => [formData.type, delayModeDispose.value],
  ([val, val2]) => {
    let list = []
    // 如果是当期展期取还款计划第一期数据，否则就是还款总额
    if (val == 1) {
      list = [
        // {
        //   label: '最低还款',
        //   value: 2,
        // },
        {
          label: '延迟还款',
          value: 3,
        },
      ]
    } else if (val == 2) {
      list = [
        {
          label: '分期还款',
          value: 1,
        },
      ]
    }
    val2 = val2.split(',')
    modeOptions.value = list.filter(
      item => val2.findIndex(child => child == item.value) > -1
    )
    if (!loanDetail.isEdit) {
      console.log(val,loanDetail)
      formData.amount =
        val == 1 ? loanDetail.rowAmount : val == 2 ? loanDetail.shouldAmount : 0
    }
  },
  { deep: true }
)
const handleClose = () => {
  formData.type = null
  formData.amount = null
  formData.delayMode = null
  formData.delayTerm = null
  formData.delayDays = null
  formData.reason = null
  formData.loanDelayStartDate = null
  formData.repaymentMode = null
  formData.id = null
  // 关闭弹窗
  dialogRef.value.handleClose()
}

const handleOpen = ({
  isEdit,
  financeNo,
  rowAmount,
  shouldAmount,
  delayMode,
}) => {
  loanDetail.isEdit = isEdit ? true : false
  loanDetail.rowAmount = rowAmount || 0
  loanDetail.shouldAmount = shouldAmount || null
  loanDetail.financeNo = financeNo || null
  delayModeDispose.value = delayMode
  // 编辑入口进入则调取回显
  if (isEdit) {
    getLoanDelayDetail({ financeNo })
  }
  getDictData()
  // 打开弹窗
  dialogRef.value.handleOpen()
}
const handleCloseDialog = () => {
  // 关闭弹窗
  dialogRef.value.handleClose()
}
// 展期类型
const handleTypeChange = () => {
  formData.delayMode = null
}

// 提交表单的操作
const handleSubmitForm = () => {
  formRef.value
    .validate()
    .then(() => {
      let params = {
        financeNo: loanDetail.financeNo,
        amount: formData.amount,
        delayMode: formData.delayMode,
        reason: formData.reason,
        type: formData.type,
        repaymentType: 1,
        repaymentId: props.rId,
      }
      if (loanDetail.isEdit) {
        Object.assign(params, { id: formData.id })
      }
      if (formData.delayMode == 1) {
        Object.assign(params, {
          delayTerm: formData.delayTerm,
          repaymentMode: formData.repaymentMode,
          loanDelayStartDate: formData.loanDelayStartDate,
        })
      } else if (formData.delayMode == 3) {
        Object.assign(params, {
          delayDays: formData.delayDays,
          loanDelayStartDate: formData.loanDelayStartDate,
        })
      }

      submitApply(params)
    })
    .catch(() => {
      message.warning('请完善信息！')
    })
}

// 申请展期
const submitApply = async params => {
  try {
    loading.value = true
    const { data } = await LOAN_REPAYMENT.requestRepaymentSave(params)
    if (data.code === 200) {
      // 申请成功之后关闭弹窗
      dialogRef.value.handleClose()
      message.success('成功申请!')
      // 在当前路由下关闭弹窗，强制刷新
      if (router.currentRoute.value.path === '/user/financing') {
        location.reload()
      }

      router.replace({ path: '/user/financing' })
    }
  } finally {
    loading.value = false
  }
}

// 展期申请数据回显
const getLoanDelayDetail = async params => {
  const { data } = await LOAN_REPAYMENT.requstDelayDetail(params)
  if (data.code === 200) {
    formData.amount = data.data?.amount
    formData.type = data.data?.type
    formData.repaymentType = data.data?.repaymentType
    formData.reason = data.data?.reason
    formData.delayMode = data.data?.delayMode
    formData.delayDays = data.data?.delayDays
    formData.delayTerm = data.data?.delayTerm
    formData.id = data.data?.id
    formData.repaymentMode = data.data?.repaymentMode
    formData.loanDelayStartDate = data.data?.loanDelayStartDate
  }
}
// 获取计费方式
const getDictData = async () => {
  let list = []
  const { data } = await requestDictMap('goods_billing_method')
  let resData = data.data || []
  if (data.code === 200) {
    for (const item of resData) {
      list.push({
        label: item.dictValue,
        value: Number(item.dictKey),
      })
    }
  }
  repaymentModeOptions.value = list
}
defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.apply-form {
  margin-top: 24px;
  & :deep(.ant-form-item:last-child) {
    margin-bottom: 0 !important;
    & .ant-btn {
      border-radius: 100px;
    }
    .btn-cancel {
      margin-right: 8px;
    }
  }
}
:deep() {
  .ant-input-number-input {
    height: 48px;
    position: relative;
  }
  .ant-input-number-prefix {
    order: 2;
    margin-inline-end: 11px !important;
    margin-inline-start: 4px;
  }
}
</style>
