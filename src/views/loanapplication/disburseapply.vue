<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.disburseapply_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/loanapplication/disburseapply";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "银行授信编号",
              prop: "credApprSerino",
              rules: [{
                required: true,
                message: "请输入银行授信编号",
                trigger: "blur"
              }]
            },
            {
              label: "携程支用申请流水号",
              prop: "sublnAplySerino",
              rules: [{
                required: true,
                message: "请输入携程支用申请流水号",
                trigger: "blur"
              }]
            },
            {
              label: "申请企业统一信用代码",
              prop: "unifiedCreditCode",
              rules: [{
                required: true,
                message: "请输入申请企业统一信用代码",
                trigger: "blur"
              }]
            },
            {
              label: "本次支用金额",
              prop: "expdtrAmt",
              rules: [{
                required: true,
                message: "请输入本次支用金额",
                trigger: "blur"
              }]
            },
            {
              label: "贷款截止有效期",
              prop: "loanExpirationDate",
              rules: [{
                required: true,
                message: "请输入贷款截止有效期",
                trigger: "blur"
              }]
            },
            {
              label: "还款方式",
              prop: "repaymentMethod",
              rules: [{
                required: true,
                message: "请输入还款方式",
                trigger: "blur"
              }]
            },
            {
              label: "出行前订单金额",
              prop: "orderAmountBeforeTravel",
              rules: [{
                required: true,
                message: "请输入出行前订单金额",
                trigger: "blur"
              }]
            },
            {
              label: "出行中订单金额",
              prop: "orderAmountDuringTravel",
              rules: [{
                required: true,
                message: "请输入出行中订单金额",
                trigger: "blur"
              }]
            },
            {
              label: "返程后订单金额",
              prop: "orderAmountAfterReturn",
              rules: [{
                required: true,
                message: "请输入返程后订单金额",
                trigger: "blur"
              }]
            },
            {
              label: "月均退单率",
              prop: "monthlyReturnRate",
              rules: [{
                required: true,
                message: "请输入月均退单率",
                trigger: "blur"
              }]
            },
            {
              label: "变异系数",
              prop: "coefficientOfVariation",
              rules: [{
                required: true,
                message: "请输入变异系数",
                trigger: "blur"
              }]
            },
            {
              label: "合作时长",
              prop: "partnershipDuration",
              rules: [{
                required: true,
                message: "请输入合作时长",
                trigger: "blur"
              }]
            },
            {
              label: "结算银行卡账号",
              prop: "bankAccountNumberForSettlement",
              rules: [{
                required: true,
                message: "请输入结算银行卡账号",
                trigger: "blur"
              }]
            },
            {
              label: "结算银行账户名称",
              prop: "bankNameForSettlement",
              rules: [{
                required: true,
                message: "请输入结算银行账户名称",
                trigger: "blur"
              }]
            },
            {
              label: "支用申请状态",
              prop: "loanStatus",
              rules: [{
                required: true,
                message: "请输入支用申请状态",
                trigger: "blur"
              }]
            },
            {
              label: "失败原因",
              prop: "failMsg",
              rules: [{
                required: true,
                message: "请输入失败原因",
                trigger: "blur"
              }]
            },
            {
              label: "计算后的支用金额",
              prop: "computeExpdtrAmt",
              rules: [{
                required: true,
                message: "请输入计算后的支用金额",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.disburseapply_add, false),
          viewBtn: this.vaildData(this.permission.disburseapply_view, false),
          delBtn: this.vaildData(this.permission.disburseapply_delete, false),
          editBtn: this.vaildData(this.permission.disburseapply_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
