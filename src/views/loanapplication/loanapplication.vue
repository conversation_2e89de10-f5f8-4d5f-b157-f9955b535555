<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.loanapplication_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/loanapplication/loanapplication";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "预授信审批编号",
              prop: "credApprSeriNo",
              rules: [{
                required: true,
                message: "请输入预授信审批编号",
                trigger: "blur"
              }]
            },
            {
              label: "携程贷款申请流水号",
              prop: "loanApplyNo",
              rules: [{
                required: true,
                message: "请输入携程贷款申请流水号",
                trigger: "blur"
              }]
            },
            {
              label: "手机号",
              prop: "mobNo",
              rules: [{
                required: true,
                message: "请输入手机号",
                trigger: "blur"
              }]
            },
            {
              label: "参考贷款金额",
              prop: "reftLoanAmt",
              rules: [{
                required: true,
                message: "请输入参考贷款金额",
                trigger: "blur"
              }]
            },
            {
              label: "参考贷款期限",
              prop: "refLoanTerm",
              rules: [{
                required: true,
                message: "请输入参考贷款期限",
                trigger: "blur"
              }]
            },
            {
              label: "收款账号",
              prop: "rcvAccNo",
              rules: [{
                required: true,
                message: "请输入收款账号",
                trigger: "blur"
              }]
            },
            {
              label: "收款账户户名",
              prop: "rcvAccNm",
              rules: [{
                required: true,
                message: "请输入收款账户户名",
                trigger: "blur"
              }]
            },
            {
              label: "贷款还款账号",
              prop: "repyAccNo",
              rules: [{
                required: true,
                message: "请输入贷款还款账号",
                trigger: "blur"
              }]
            },
            {
              label: "贷款受托支付号",
              prop: "repyAccNm",
              rules: [{
                required: true,
                message: "请输入贷款受托支付号",
                trigger: "blur"
              }]
            },
            {
              label: "月均退单率",
              prop: "monthlyReturnRate",
              rules: [{
                required: true,
                message: "请输入月均退单率",
                trigger: "blur"
              }]
            },
            {
              label: "变异系数",
              prop: "coefficientOfVariation",
              rules: [{
                required: true,
                message: "请输入变异系数",
                trigger: "blur"
              }]
            },
            {
              label: "合作时长",
              prop: "partnershipDuration",
              rules: [{
                required: true,
                message: "请输入合作时长",
                trigger: "blur"
              }]
            },
            {
              label: "预授信审核编号",
              prop: "creditPreApplyNo",
              rules: [{
                required: true,
                message: "请输入预授信审核编号",
                trigger: "blur"
              }]
            },
            {
              label: "失败原因",
              prop: "failureReason",
              rules: [{
                required: true,
                message: "请输入失败原因",
                trigger: "blur"
              }]
            },
            {
              label: "合同信息",
              prop: "contract",
              rules: [{
                required: true,
                message: "请输入合同信息",
                trigger: "blur"
              }]
            },
            {
              label: "融资申请id",
              prop: "financeApplyId",
              rules: [{
                required: true,
                message: "请输入融资申请id",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.loanapplication_add, false),
          viewBtn: this.vaildData(this.permission.loanapplication_view, false),
          delBtn: this.vaildData(this.permission.loanapplication_delete, false),
          editBtn: this.vaildData(this.permission.loanapplication_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
