<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button
          type="success"
          icon="el-icon-check"
          size="small"
          plain
          v-if="form.status === 1"
          @click="publish()"
        >发布</el-button
        >
      </template>

      <template slot-scope="{ row, index, type, size }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          size="small"
          v-if="row.status === 1 "
          @click="openEditDialog(row)"
        >编辑</el-button
        >
        <el-button
          icon="el-icon-delete"
          v-if="row.status === 1 "
          :size="size"
          :type="type"
          @click="rowDel(row)"
        >删除</el-button
        >
        <el-button
          type="text"
          icon="el-icon-video-play"
          v-if="row.status === 1 "
          @click="publish(row)"
          size="small"
        >发布
        </el-button>
        <el-button
          type="text"
          icon="el-icon-circle-check"
          v-if="row.status === 2 "
          @click="unPublish(row)"
          size="small"
        >取消发布
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove} from "@/api/front/protocol";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height:'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        delBtn: false,
        editBtn: false,
        addBtn:true,
        column: [
          {
            label: "协议名称",
            prop: "name",
            editDisabled: true,
            span: 24,
            rules: [{
              required: true,
              message: "请输入协议名称",
              trigger: "blur"
            }]
          },
          {
            label: "类别",
            prop: "type",
            dataType:'string',
            type:'select',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=frontProtocolType',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            span: 24,
            rules: [{
              required: true,
              message: "请输入协议名称",
              trigger: "blur"
            }]
          },
          {
            label: "排序",
            span: 24,
            prop: "sort",
            controlsPosition: '',
            type: 'number',
            minRows: 0,
            rules: [{
              required: true,
              message: "请输入排序",
              trigger: "blur"
            }]
          },
          {
            label: '内容',
            prop: 'content',
            hide: true,
            component: 'AvueUeditor',
            options: {
              action: '/api/blade-resource/oss/endpoint/put-file',
              props: {
                res: 'data',
                url: 'link',
              },
            },
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入内容',
                trigger: 'blur',
              },
            ],
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.protocol_add, false),
        viewBtn: this.vaildData(this.permission.protocol_view, false),
        delBtn: this.vaildData(this.permission.protocol_delete, false),
        editBtn: this.vaildData(this.permission.protocol_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    openEditDialog(row) {
      this.$refs.crud.rowEdit(row, row.index)
    },
    publish(row) {
      if (row) {
        this.$confirm('您确定要发布吗, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          row.status = 2
          update(row).then(
            () => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
            },
            (error) => {
              console.log(error)
            }
          )
        })
      } else {
        this.form.status = 2
        this.$refs.crud.rowSave()
      }
    },

    unPublish(row) {
      this.$confirm('您确定要取消发布吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        row.status = 1
        update(row).then(
          () => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
          },
          (error) => {
            console.log(error)
          }
        )
      })
    },

    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
