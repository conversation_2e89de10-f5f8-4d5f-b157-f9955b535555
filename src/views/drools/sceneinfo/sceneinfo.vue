<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.sceneinfo_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>

      <template slot="menu" slot-scope="scope">
        <el-button
          type="text"
          size="small"
          icon="el-icon-view"
          @click="ruleInitHandle(scope.row, scope.index)"
        >预览drl
        </el-button>
      </template>
    </avue-crud>

    <!-- 参数对话框-->
    <el-dialog title="请填写参数" class="dialog-expand" :visible.sync="dialogParamFormVisible" append-to-body="true">
      <el-form :model="paramForm">
        <el-form-item :key="index" v-for="(item,index) in paramForm.conList" :label="item.fieldName"
                      :label-width="formLabelWidth">
          <el-input :placeholder="item.field" v-model="item.fieldValue" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogParamFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="executeHandle()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 结果预览对话框-->
    <el-dialog title="结果预览" class="dialog-expand" :visible.sync="dialogFormVisible" append-to-body="true">
      <el-form :model="ruleForm" class="table-expand">
        <el-form-item label="计算结果" :label-width="formLabelWidth">
          <span v-html="ruleForm.data"></span>
        </el-form-item>
        <el-form-item label="规则串" :label-width="formLabelWidth">
          <span v-html="ruleForm.ruleStr"></span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
      </div>
    </el-dialog>

  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, executeDrools, execRuleParam} from "@/api/drools/sceneinfo";
  import {mapGetters} from "vuex";
  import {DROOLS_BACK} from '@/config/apiPrefix'

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          dialogClickModal: false,
          dialogWidth: '30%',
          "column": [
            {
              "type": "input",
              "label": "主键",
              "prop": "id",
              "span": 12,
              hide: true,
              editDisplay: false,
              addDisplay: false
            }, {
              "type": "input",
              "label": "名称",
              "prop": "sceneName",
              "span": 24,
              rules: [
                {
                  required: true,
                  message: '场景名称不能为空',
                  trigger: 'blur'
                },
              ],
            }, {
              "type": "input",
              "label": "标识",
              "prop": "sceneIdentify",
              "span": 24,
              rules: [
                {
                  required: true,
                  message: '场景标识不能为空',
                  trigger: 'blur'
                },
              ],
            }, {
              "type": "select",
              "label": "关联实体",
              "prop": "entityIds",
              "span": 24,
              hide: true,
              multiple: true,
              dicUrl: DROOLS_BACK + '/drools/entityinfo/select',
              rules: [
                {
                  required: true,
                  message: '场景关联实体不能为空',
                  trigger: 'blur'
                },
              ],
            }, {
              "type": "textarea",
              "label": "描述",
              "prop": "sceneDesc",
              minRows: 2,
              "span": 24
            }, {
              "type": "textarea",
              "label": "备注",
              "prop": "remark",
              minRows: 2,
              "span": 24
            }, {
              "type": "select",
              "label": "状态",
              "prop": "status",
              "span": 12,
              width: 80,
              editDisplay: false,
              addDisplay: false,
              value: 1,
              dicData: [{
                "label": "启用",
                "value": 1
              }, {
                "label": "停用",
                "value": 0
              }],
            }]
        },
        data: [],
        dialogParamFormVisible: false,
        dialogFormVisible: false,
        ruleForm: {
          ruleStr: '',
          request: '',
          data: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        paramForm: {
          conList: [],
          scene: ''
        },
        formLabelWidth: '120px'
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.sceneinfo_add, false),
          viewBtn: this.vaildData(this.permission.sceneinfo_view, false),
          delBtn: this.vaildData(this.permission.sceneinfo_del, false),
          editBtn: this.vaildData(this.permission.sceneinfo_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      executeHandle() {
        executeDrools({
          "sceneIdentify": this.paramForm.scene,
          "params": this.paramForm.conList
        }).then(res => {
          this.form.ruleStr = res.data.data.rulesStr.replace(/\r\n/g, "<br/>");
          this.form.data = res.data.data.globalMap;
          this.form.request = res.data.data.factObjectList;
          this.dialogFormVisible = true;
        })
      },
      ruleInitHandle(row) {
        execRuleParam({'scene': row.sceneIdentify}).then(res => {
          this.paramForm.conList = res.data.data;
          this.paramForm.scene = row.sceneIdentify;
          this.dialogParamFormVisible = true;
        })
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
