<template>
  <div class="cost-rules-container">
    <div class="cost-rules-main">
      <div class="cost-rules-form">
        <el-form
          ref="formRef"
          :model="costForm"
          :rules="costRules"
          class="cost-rules-detail-form"
          label-width="114px"
          label-position="left"
        >
          <el-form-item
            label="计算公式:"
            prop="feeFormulaName"
            class="cout-type"
            key="text"
          >
            <el-input
              disabled
              type="textarea"
              v-model="costForm.feeFormulaName"
              :rows="5"
            ></el-input>
          </el-form-item>
          <el-form-item label="数字符号:" class="cout-type">
            <span
              class="cost-number-tag"
              :class="
                disabled
                  ? 'cost-number-tag-disabled'
                  : 'cost-number-tag-allowed'
              "
              v-for="item in numberSignList"
              :key="item"
              @click="handleTagClick(item)"
              >{{ item.label }}</span
            >
          </el-form-item>
          <el-form-item label="计算符号:" class="cout-type">
            <span
              class="cost-number-tag cost-number-tag-allowed"
              v-for="item in numberCoutList"
              :key="item"
              @click="handleTagClick(item)"
              >{{ item.label }}</span
            >
          </el-form-item>
          <el-form-item label="比较符号:" class="cout-type">
            <span
              class="cost-number-tag"
              :class="
                disabled
                  ? 'cost-number-tag-disabled'
                  : 'cost-number-tag-allowed'
              "
              v-for="item in compareList"
              :key="item"
              @click="handleTagClick(item)"
              >{{ item.label }}</span
            >
          </el-form-item>
          <el-form-item label="时间比较" class="cout-type">
            <div class="cost-text-tag-list">
              <span
                :class="
                  disabled
                    ? 'cost-number-tag-disabled'
                    : 'cost-number-tag-allowed'
                "
                @click="handleOpenDialog(item)"
                class="cost-text-tag-item"
                v-for="item in dateOptions"
                :key="item.value"
                >{{ item.label }}</span
              >
            </div>
          </el-form-item>
          <el-form-item label="计算字段:" class="cout-type">
            <div class="cost-text-tag-list">
              <span
                :class="
                  disabled
                    ? 'cost-number-tag-disabled'
                    : 'cost-number-tag-allowed'
                "
                @click="handleTagClick(item)"
                class="cost-text-tag-item"
                v-for="item in textCoutList"
                :key="item.value"
                >{{ item.label }}</span
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="footer-button">
      <el-button type="primary" @click="cancelCostRule">返回</el-button>
      <el-button type="primary" @click="confirmCostRule">确认</el-button>
    </div>

    <!-- 日期配置 -->
    <DateRulesDialog
      ref="dateDailog"
      @getParams="handleChildParams"
      :openDialogBtn="openDialogBtn"
    />

    <!-- 区间配置 -->
    <IntervalRulesDialog ref="intervalDialog" @getParams="handleChildParams" />
  </div>
</template>

<script>
import {
  getExpenseRuleBusiness,
  saveExpenseRules,
  expenseRulesDetail,
} from '@/api/expense/expense'
import { getRuleParams } from '@/api/drools/info'

import { numberSignList, numberCoutList, compareList } from './costRule'
import DateRulesDialog from './Dialog/dateRulesDialog.vue'
import IntervalRulesDialog from './Dialog/intervalRulesDialog.vue'

export default {
  components: { DateRulesDialog, IntervalRulesDialog },
  data() {
    return {
      costForm: {
        feeFormula: '',
        feeFormulaName: '',
      },
      costRules: {
        feeFormulaName: [{ required: true, message: '请填写计算方式' }],
      },
      disabled: false,
      numberSignList,
      numberCoutList,
      compareList,
      textCoutList: [],
      dateOptions: [
        {
          label: '加减日期',
          value: 'time',
        },
        {
          label: '区间',
          value: 'intervalTime^(',
        },
        {
          label: '当前日期',
          value: '^nowDate^',
        },
      ],
      openDialogBtn: null, // 加减日期、区间
    }
  },
  provide() {
    return {
      getTextCoutList: () => this.textCoutList,
    }
  },
  watch: {
    tabDataAndFeeNode(obj) {
      // 当费用节点有值，并且在tab在公式计算下
      if (obj.costMode === 2 && obj.feeNode && obj.businessType) {
        this.getExpenseRuleBusiness({
          feeNode: obj.feeNode,
          businessType: obj.businessType,
        })
      }
    },
    '$route.params.params': {
      handler(val) {
        if (val) {
          let list = { ...this.costForm }
          let costModeList = [...this.costModeOptions] // 计算方式
          let btnFooter = [...this.btnOptions]
          const params = JSON.parse(Buffer.from(val, 'base64').toString())
          const { id, btnName } = params
          this.btnName = btnName
          if (btnName === 'Add') {
            this.disabled = false
            for (const key in list) {
              if (typeof list[key] === 'number') {
                list[key] = 1
              } else {
                list[key] = ''
              }
            }
            this.costModeOptions = costModeList
            this.btnOptions = btnFooter
          } else if (btnName === 'Get') {
            this.disabled = true
            this.btnOptions = btnFooter.filter(
              item => item.funName === 'Revert'
            )
            this.expenseRulesDetail({ id })
          } else if (btnName === 'Edit') {
            this.expenseRulesDetail({ id })
            this.disabled = false
            this.btnOptions = btnFooter
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    cancelCostRule() {
      this.$emit('close')
    },
    confirmCostRule() {
      this.$emit('callback', this.costForm)
    },
    // 公式计算下的计算公式点击赋值
    handleTagClick(item) {
      if (this.disabled) return
      if (item.label === 'C') {
        this.costForm.feeFormulaName = ''
        this.costForm.feeFormula = ''
      } else {
        this.costForm.feeFormulaName += item.label
        this.costForm.feeFormula += item.value
      }
    },
    // 时间比较参数的对应不同操作
    handleOpenDialog(item) {
      if (item.label === '加减日期') {
        this.openDialogBtn = 'date'
        this.$refs['dateDailog'].dialogVisible = true
      } else if (item.label === '区间') {
        this.$refs['intervalDialog'].dialogVisible = true
      } else {
        this.costForm.feeFormulaName = this.costForm.feeFormulaName + item.label
        this.costForm.feeFormula = this.costForm.feeFormula + item.value
      }
    },
    // 费用类型、计算节点、结果取值、业务类型数据
    addInitCostRule(ruleId, costName) {
      this.getRuleParams(ruleId)
      this.costForm.feeFormulaName = costName == undefined ? '' : costName
    },
    getRuleParams(ruleId) {
      let that = this
      getRuleParams(ruleId).then(res => {
        that.textCoutList = res.data.data
      })
    },
    // 获取子表单
    handleChildParams(data) {
      this.costForm.feeFormulaName = this.costForm.feeFormulaName + data.label
      this.costForm.feeFormula = this.costForm.feeFormula + data.value
    },
    // 获取计算字段
    getExpenseRuleBusiness(params) {
      getExpenseRuleBusiness(params)
        .then(({ data }) => {
          let list = []
          if (data.code === 200) {
            data.data.forEach(item => {
              list.push({
                label: item.name,
                value: item.nameField,
              })
            })
          }
          this.textCoutList = list
        })
        .catch(() => {})
    },
    // 新增费用规则
    saveExpenseRules(params) {
      this.startLoading()
      saveExpenseRules(params)
        .then(({ data }) => {
          if (data.code === 200) {
            this.$message.success('操作成功!')
            this.$router.replace('/goods/goodsexpenserule')
            this.$router.$avueRouter.closeTag()
          }
        })
        .catch(() => {})
        .finally(() => {
          this.endLoading()
        })
    },
    // 费用规则详情
    expenseRulesDetail(params) {
      expenseRulesDetail(params)
        .then(({ data }) => {
          if (data.code === 200) {
            this.costForm = {
              ...data.data,
            }
            const list = [...this.costModeOptions]
            this.costModeOptions = list.filter(
              item => item.value === data.data.calculation
            )
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.cost-rules-container {
  // padding-bottom: 120px !important;

  .cost-rules-main {
    padding: 24px 26px 20px 20px;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    box-sizing: border-box;

    .cost-rules-main-title {
      color: #101010;
      font-size: 16px;
      font-weight: bold;
      height: 24px;
      line-height: 24px;
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &::before {
        content: '';
        display: inline-block;
        background-color: #1277ff;
        width: 8px;
        height: 16px;
        border-radius: 15px;
        margin-right: 4px;
      }
    }

    .cost-rules-form {
      width: 100%;
      padding: 24px 24px 0;
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      box-sizing: border-box;

      .cost-rules-detail-form {
        display: flex;
        flex-flow: row wrap;
        align-items: center;

        .el-form-item {
          width: calc(50% - 33px);
          margin-bottom: 24px;

          &:nth-of-type(odd) {
            margin-right: 66px;
          }

          ::v-deep {
            .el-radio-group {
              display: flex;
              align-items: center;
              flex-flow: row wrap;
              margin-bottom: 12px;
            }

            .el-radio-button {
              user-select: none;
            }

            .el-radio-button__inner {
              border-radius: 29px;
              margin-right: 12px;
              border: 1px solid #eee;
            }

            .el-radio-button__orig-radio:checked + .el-radio-button__inner {
              border: 1px solid #697cff !important;
              color: #697cff !important;
              box-shadow: none;
              background-color: #fff;
            }

            .el-input.is-disabled .el-input__inner {
              color: #666 !important;
            }
          }
        }

        & ::v-deep .cout-type {
          width: 100% !important;
          margin-right: 0 !important;

          .limit-range {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .limit-range-item {
              display: flex;
              align-items: center;

              & > label {
                width: 60px;
                margin-right: 8px;
                color: #999;
                font-size: 14px;
              }

              .limit-input {
                width: 220px;
              }
            }
          }

          .cost-number-tag {
            cursor: pointer;
            user-select: none;
            display: inline-block;
            text-align: center;
            width: 34px;
            height: 34px;
            line-height: 34px;
            border-radius: 50%;
            background-color: #eeee;
            margin-right: 12px;
          }

          .cost-number-tag-disabled {
            cursor: not-allowed;
          }

          .cost-number-tag-allowed {
            cursor: pointer;
          }

          .cost-text-tag-list {
            display: flex;
            align-items: center;
            flex-flow: row wrap;

            .cost-text-tag-item {
              cursor: pointer;
              user-select: none;
              display: block;
              margin-right: 12px;
              margin-bottom: 12px;
              line-height: 24px;
              padding: 6px 12px;
              font-size: 14px;
              background-color: #eee;
              border-radius: 29px;
            }

            .cost-number-tag-disabled {
              cursor: not-allowed;
            }

            .cost-number-tag-allowed {
              cursor: pointer;
            }
          }

          .el-textarea__inner {
            color: #666;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .footer-button {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
  }
}
</style>
