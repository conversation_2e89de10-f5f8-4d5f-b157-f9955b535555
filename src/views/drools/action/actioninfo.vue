<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">

      <template slot="menuLeft">
        <el-button
          v-if="permissionList.addBtn"
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="showActionForm">新增
        </el-button>
      </template>

      <template slot="menu" slot-scope="scope">
        <el-button
          v-if="permissionList.viewBtn"
          type="text"
          size="small"
          icon="el-icon-view"
          @click="viewActionForm(scope.row, scope.index)"
        >查看
        </el-button>
        <el-button
          v-if="permissionList.editBtn"
          type="text"
          size="small"
          icon="el-icon-edit"
          @click="editActionForm(scope.row, scope.index)"
        >编辑
        </el-button>
        <el-button
          v-if="permissionList.delBtn"
          type="text"
          size="small"
          icon="el-icon-edit"
          @click="rowDel(scope.row)"
        >删除
        </el-button>

      </template>


    </avue-crud>

    <el-dialog
      :visible.sync="dialogActionFormVisible"
      append-to-body
      title="规则动作"
      width="55%"
      @close="closeActionForm">
      <ActionForm ref="actionRef"></ActionForm>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/drools/actioninfo";
  import {mapGetters} from "vuex";
  import ActionForm from './actionForm'

  export default {
    components: {ActionForm},
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        dialogActionFormVisible: false,
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          dialogClickModal: false,
          addBtn: false,
          delBtn: false,
          editBtn: false,
          column: [
            {
              label: "动作名称",
              prop: "actionName",
              rules: [{
                required: true,
                message: "请输入动作名称",
                trigger: "blur"
              }]
            },
            {
              label: "动作类型",
              prop: "actionType",
              rules: [{
                required: true,
                message: "请输入动作类型",
                trigger: "blur"
              }],
              dicData: [
                {label: '实现', value: "1"},
                {label: '自身', value: "2"}
              ]
            },
            {
              label: "包路径",
              prop: "actionClass",
              rules: [{
                required: true,
                message: "请输入动作实现类(包路径)",
                trigger: "blur"
              }],
              span: 24,
              overHidden: true,
            },
            {
              label: "动作描述",
              prop: "actionDesc",
              rules: [{
                required: true,
                message: "请输入动作描述",
                trigger: "blur"
              }],
              span: 24,
              overHidden: true,
            },

            {
              label: "备注",
              prop: "remark",
              rules: [{
                required: true,
                message: "请输入备注",
                trigger: "blur"
              }],
              span: 24,
              overHidden: true,
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.actioninfo_add, false),
          viewBtn: this.vaildData(this.permission.actioninfo_view, false),
          delBtn: this.vaildData(this.permission.actioninfo_del, false),
          editBtn: this.vaildData(this.permission.actioninfo_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      showActionForm() {
        this.dialogActionFormVisible = true;
        this.$nextTick(() => {
          this.$refs.actionRef.initActionForm();
        });
      },
      editActionForm(row) {
        this.dialogActionFormVisible = true;
        this.$nextTick(() => {
          this.$refs.actionRef.editActionForm(row.id);
        });
      },
      viewActionForm(row) {
        this.dialogActionFormVisible = true;
        this.$nextTick(() => {
          this.$refs.actionRef.viewActionForm(row.id);
        });
      },
      closeActionForm() {
        this.$refs.actionRef.resetForm();
        this.dialogActionFormVisible = false;
        this.onLoad(this.page);
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
