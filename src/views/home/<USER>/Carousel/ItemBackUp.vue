<template>
  <div
    class="item"
    :style="[
      backgroundStyle,
      { cursor: itemData?.goodsId ? 'pointer' : 'context-menu' },
    ]"
    @click="goDetail()"
  ></div>
</template>

<script>
export default {
  name: 'CarouselItem',
}
</script>
<script setup>
import { ref, watchEffect } from 'vue'
import router from '@/router'
import backgroundImg from './background_default.png'

const backgroundStyle = ref(null)

const props = defineProps({
  itemData: {
    type: Object,
  },
})

watchEffect(() => {
  if (props.itemData) {
    backgroundStyle.value = `background-image: url(${props.itemData.logoSrc})`
  } else {
    backgroundStyle.value = `background-image: url(${backgroundImg})`
  }
})

// 动态轮播图跳转详情页
const goDetail = () => {
  if (props.itemData.goodsId) {
    router.push({
      name: 'ProductDetails',
      query: {
        goodId: props.itemData.goodsId,
        goodType: props.itemData.goodType,
        capitalId: props.itemData.capitalId,
      },
    })
  }
}
</script>

<style lang="scss" scoped>
@import '@/views/home/<USER>';

.item {
  background: #f6f6f6;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: $nav-bar-height;
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
