<template>
  <TimeLineCommonCard :date="date" style="width: 420px">
    <div class="usage-record-timeline-content">
      <div class="form-item">
        <span class="form-title">质押/转让对象</span>
        <span class="form-value company-name">{{ targetName || '--' }}</span>
      </div>
      <div class="form-item">
        <span class="form-title">使用金额</span>
        <span class="form-value amount">{{ formatMoney(amount) }}</span>
      </div>
      <div class="form-item">
        <span class="form-title">融资编号</span>
        <span class="form-value number">{{ number || '--' }}</span>
      </div>
      <div class="form-item">
        <span class="form-title">使用方式</span>
        <span class="form-value">
          <CommonTag
            padding="4px 10px"
            color="#0A1F44"
            borderColor="transparent"
            :name="
              useType === null || useType === undefined
                ? '--'
                : useType == 0
                ? '质押'
                : '转让'
            "
            backgroundColor="#F1F2F4"
          />
        </span>
      </div>
    </div>
  </TimeLineCommonCard>
</template>

<script lang="ts">
export default {
  name: 'UseageReacordTimelineCardIndex',
}
</script>
<script lang="ts" setup>
import TimeLineCommonCard from '../TimeLineCommonCard/index.vue'
import CommonTag from '@/components/CommonTag/index.vue'
import { formatMoney } from '@/utils/utils'

defineProps({
  date: {
    type: String,
    required: true,
    default: '--',
  },
  targetName: {
    type: String,
    required: true,
    default: '--',
  },
  amount: {
    type: String,
    required: true,
    default: '--',
  },
  number: {
    type: String,
    required: true,
    default: '--',
  },
  useType: {
    type: [String, Number],
    required: true,
    default: undefined,
  },
})
</script>

<style lang="scss" scoped>
.usage-record-timeline-content {
  .form-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .form-title {
      flex-shrink: 0;
      width: 120px;
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8a94a6;
      text-align: left;
    }

    .form-value {
      color: rgba(77, 0, 0, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;

      &.company-name {
        font-size: 14px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #0a1f44;
      }

      &.amount {
        font-size: 14px;
        font-family: SFProText-Bold, SFProText;
        font-weight: bold;
        color: #dd2727;
      }

      &.number {
        font-size: 14px;
        font-family: SFProText-Regular, SFProText;
        font-weight: 400;
        color: #0d55cf;
      }
    }
  }
}
</style>
