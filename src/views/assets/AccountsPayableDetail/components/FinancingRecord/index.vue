<template>
  <div class="application-content">
    <div class="content-money-text">
      <span>剩余金额(元)</span>
      <a-popover
        placement="bottomLeft"
        trigger="hover"
        :arrowPointAtCenter="true"
        overlayClassName="usage-record-popover availabel-amount"
        color="rgba(0,0,0,.6)"
        :overlayStyle="{
          backdropFilter: 'saturate(90%) blur(6px)',
          '-webkit-backdrop-filter': 'saturate(90%) blur(6px)',
        }"
      >
        <template #title />
        <template #content>
          <div class="availabel-amount-container">
            <span class="title">计算方式</span>
            <div class="availabel-content-wrapper">
              <div class="availabel-content">
                <span class="label">剩余金额</span>
                <span class="value">￥{{ formatMoney(availabelAmount) }}</span>
              </div>
              <div class="desc-container">
                <span class="desc">剩余金额=确权金额-已用金额</span>
                <span class="desc"
                  >{{ formattedConfirmAmount }}-{{ formatMoney(usedAmount) }}={{
                    formatMoney(Number(confirmAmount) - Number(usedAmount))
                  }}元</span
                >
              </div>
            </div>
            <div class="confirm-content-wrapper">
              <div class="confirm-content">
                <span class="label">确权金额</span>
                <span class="value">￥{{ formattedConfirmAmount }}</span>
              </div>
              <div class="desc-container">
                <span class="desc">确权金额=账面金额*可贷成数</span>
                <span class="desc"
                  >{{ formatMoney(accountAmount) }}*{{ Number(loanable) }}%={{
                    formattedConfirmAmount
                  }}元</span
                >
              </div>
            </div>
          </div>
        </template>
        <MySvgIcon
          icon-class="icon-xinxi"
          style="
            fill: #758196;
            font-size: 24px;
            margin-left: 4px;
            outline: none;
          "
        />
      </a-popover>
    </div>
    <div class="content-botton-money-box">
      <span class="content-money">
        <a-statistic :precision="2" :value="availabelAmount">
          <template #prefix>
            <span>￥</span>
          </template>
        </a-statistic>
      </span>
    </div>
    <div class="pledged-goods">
      <a-collapse v-model:activeKey="activeKey" ghost>
        <!-- 更多详情 -->
        <a-collapse-panel key="1" :forceRender="true">
          <template #header>
            <div class="pledged-text-box">
              <span class="pledged-goods-text">融资记录</span>
            </div>
          </template>
          <div class="usage-record-container">
            <template v-if="UsageRecordArr && UsageRecordArr.length > 0">
              <TimelineCard
                v-for="item of UsageRecordArr"
                :key="item.id"
                :date="item.createTime"
                :targetName="item.enterpriseName"
                :amount="item.amount"
                :number="item.financeNo"
                :useType="item.useType"
              />
            </template>
            <template v-else>
              <NoData
                title="暂无数据"
                noButton
                :imgSrc="require('@/assets/images/empty.svg')"
              />
            </template>
          </div>
        </a-collapse-panel>

        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
      </a-collapse>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssetsDetailUsageRecordIndex',
}
</script>
<script setup>
import { ref, computed } from 'vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'
import TimelineCard from './TimelineCard.vue'
import NoData from '@/views/user/components/NoData/index.vue'
import { formatMoney } from '@/utils/utils'

const props = defineProps({
  availabelAmount: {
    type: String,
    required: true,
    default: '--',
  },
  accountAmount: {
    type: String,
    required: true,
    default: '--',
  },
  applyAmount: {
    type: String,
    required: true,
    default: '--',
  },
  usedAmount: {
    type: String,
    required: true,
    default: '--',
  },
  loanable: {
    type: [String, Number],
    required: true,
    default: '--',
  },
  UsageRecordArr: {
    type: Array,
    required: true,
    default: () => [],
  },
})
const activeKey = ref([])
const confirmAmount = computed(
  () => props.accountAmount * (Number(props.loanable) / 100)
)
const formattedConfirmAmount = computed(() => formatMoney(confirmAmount.value))
</script>

<style lang="scss" scoped>
.application-content {
  position: relative;
  z-index: 100;
  .content-money-text {
    height: 32px;
    font-size: 24px;
    font-weight: 600;
    color: #0a1f44;
    line-height: 32px;
    margin-bottom: 12px;
    cursor: context-menu;
  }
  .content-botton-money-box {
    display: flex;
    justify-items: flex-start;
    align-items: baseline;
    margin-bottom: 40px;

    .content-money {
      cursor: context-menu;
      :deep(.ant-statistic-content) {
        .ant-statistic-content-value {
          font-size: 64px;
          font-weight: 600;
          color: #031222;
          line-height: 64px;
        }
        .ant-statistic-content-prefix {
          font-size: 25px;
          font-weight: 600;
          margin-right: -1px;
        }
      }
    }
  }
  :deep(.pledged-goods) {
    .pledged-text-box {
      display: flex;
      align-items: center;

      .pledged-goods-text {
        height: 24px;
        font-size: 16px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 24px;
      }
      .long-string {
        display: inline-block;
        width: 1px;
        height: 16px;
        background: #b5bbc6;
        margin: 0 12px;
      }
      .pledged-goods-num {
        .ant-statistic-content {
          height: 24px;
          line-height: 0;
        }

        .ant-statistic-content-value {
          font-size: 16px;
          font-weight: 600;
          color: #0a1f44;
          line-height: 24px;
        }
        .ant-statistic-content-prefix {
          font-size: 16px;
          font-weight: 600;
          margin-right: -1px;
        }
      }
    }
    .tradefor-information-box {
      display: flex;
      flex-direction: column;

      .tradefor-for {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 40px;
        }

        & > .base-card-item {
          margin-top: 0;
          margin-right: 0;
        }
      }
    }
    // 更多信息class
    .forMore-etails {
      .tradefor-be-surrounded-box {
        .tradefor-box {
          cursor: context-menu;
          margin-bottom: 20px;
          display: flex;

          & > span:first-child {
            width: 116px;
            height: 20px;
            display: inline-block;
            font-weight: 400;
            color: #8a94a6;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }

          .tradefor-text {
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #0a1f44;
            line-height: 20px;
          }
        }
      }
    }
    // 折叠面板样式修改
    .ant-collapse-header {
      display: flex;
      padding: 12px 3px 8px;
      box-sizing: border-box;
    }
    .ant-collapse-arrow {
      font-size: 19px;
      vertical-align: -4px;
      margin-right: 6px;
      color: #758196;
    }
    .ant-collapse-content-box {
      padding: 12px 0 0;
    }
  }

  .usage-record-container {
    padding: 40px 0 40px 94px;
    background-color: #f8f9fb;
    border-radius: 8px;
    border: 1px solid #efefef;
  }
}
</style>

<style lang="scss">
@mixin backgroundMask {
}

.usage-record-popover {
  &.availabel-amount {
    .ant-popover-content {
      border-radius: 8px;
      overflow: hidden;

      .ant-popover-arrow {
        transform: scale(2) translateY(-20%);
      }
    }

    .availabel-amount-container {
      .title {
        display: inline-block;
        margin-bottom: 16px;
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #fefefe;
        line-height: 24px;
      }

      .availabel-content-wrapper,
      .confirm-content-wrapper {
        .availabel-content,
        .confirm-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .label {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #f3f3f3;
            line-height: 20px;
          }

          .value {
            font-size: 16px;
            font-family: CoreSansD65Heavy;
            color: #fdfdfd;
            line-height: 20px;
          }
        }

        .desc-container {
          display: flex;
          flex-direction: column;

          .desc {
            display: block;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #d5d5d5;
            line-height: 16px;
          }
        }
      }

      .confirm-content-wrapper {
        margin-top: 16px;
      }
    }
  }
}
</style>
