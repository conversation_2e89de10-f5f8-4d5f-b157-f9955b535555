<template>
  <GlobalDialog
    :title="isUploading ? '' : '操作须知'"
    width="432px"
    ref="dialogRef"
    :enableFullscreen="false"
  >
    <div class="content-container">
      <template v-if="!isUploading">
        <div class="form-container">
          <div class="form-item">
            <span class="label">文件属性：</span>
            <span class="value">发票文件</span>
          </div>
          <div class="form-item">
            <span class="label">文件格式：</span>
            <span class="value">zip、jpg、jpeg、png、pdf</span>
          </div>
          <div class="form-item">
            <span class="label">文件大小：</span>
            <span class="value">不超过500M</span>
          </div>
          <div class="form-item">
            <span class="label">温馨提示：</span>
            <span class="value"
              >已导入的发票请勿再次上传，上传过程中请勿轻易退出</span
            >
          </div>
        </div>
        <a-upload
          accept=".zip"
          :fileList="[]"
          :before-upload="handleAvatarBeforeUpload"
        >
          <n-button
            class="upload-btn blue border"
            style=""
            :bordered="false"
            round
            >上传文件</n-button
          >
        </a-upload>
      </template>
      <template v-else>
        <div class="upload-container">
          <div class="upload-process-wrapper">
            <div id="upload-process-bar" />
            <div class="upload-process-text">
              <template v-if="uploadProgressData.complete">
                <MySvgIcon
                  icon-class="icon-gouxuan1"
                  style="fill: #22c993; font-size: 80px"
                />
              </template>
              <template v-else-if="uploadProgressData.fail">
                <MySvgIcon
                  icon-class="icon-guanbi1"
                  style="fill: #f00; font-size: 80px"
                />
              </template>
              <template v-else>
                <span class="status-text">{{
                  uploadProgressData.statusText
                }}</span>
                <span class="status-number">{{
                  uploadProgressData.statusNumber
                }}</span>
              </template>
            </div>
          </div>
          <div class="upload-desc">
            {{ uploadProgressData.desc }}
          </div>
        </div>
      </template>
    </div>
  </GlobalDialog>
</template>

<script lang="ts">
export default {
  name: 'UserTradeInvitePartnersDialogIndex',
}
</script>
<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue'
import { NButton } from 'naive-ui'
import { message } from 'ant-design-vue'
import ProgressBar from 'progressbar.js'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import {
  requestPayableFileUpload,
  requestPayableFileParsingProgress,
} from '@/api/user/DebtList'

const dialogRef = ref<any>(null)
const btnLoading = ref<any>(false)
const isUploading = ref<boolean>(false)
const uploadProgressData = reactive<any>({
  complete: false,
  progressBar: undefined,
  statusText: '文件上传中',
  statusNumber: '--',
  desc: '上传过程中请勿退出',
})
const emit = defineEmits(['onComplete'])

// 打开函数
const handleOpen = () => {
  isUploading.value = false
  btnLoading.value = false
  uploadProgressData.complete = false
  uploadProgressData.progressBar = undefined
  uploadProgressData.fail = false
  // 打开弹窗
  dialogRef.value.handleOpen()
}

// 关闭函数
const handleClose = () => {
  // 关闭弹窗
  dialogRef.value.handleClose()
}

// 文件识别进度获取 （递归）
const handleReFetchParsingProgress = () => {
  requestPayableFileParsingProgress()
    .then(({ data }) => {
      if (data.code === 200) {
        // 解析完成
        uploadProgressData.complete = true
        uploadProgressData.desc = '任务完成，即将关闭...'
        uploadProgressData.progressBar?.animate(1, {
          from: { color: '#22C993' },
          to: { color: '#22C993' },
        })
        emit('onComplete')
        setTimeout(() => {
          handleClose()
        }, 3000)
        return
      } else if (data.code === 400) {
        // 未存在进行中的任务
        // emmmmmmmmm  后端说继续获取
        setTimeout(() => {
          handleReFetchParsingProgress()
        }, 1000)
      } else if (data.code === 201) {
        // 任务进行中
        data = data.data
        uploadProgressData.statusNumber = `${data.currentIndex}/${data.count}`
        uploadProgressData.progressBar?.animate(
          0.25 + (data.currentIndex / data.count / 4) * 3
        )
        setTimeout(() => {
          handleReFetchParsingProgress()
        }, 1000)
      }
    })
    .catch(() => {})
}

// 文件上传进度处理函数
const handleOnUploadProgress = (progressEvent: any) => {
  const uploadRatio = progressEvent.loaded / progressEvent.total

  // 上传百分比
  uploadProgressData.statusNumber = ((uploadRatio * 100) | 0) + '%'
  // 进度条进度（文件上传占总进度的 20%）
  uploadProgressData.progressBar?.animate(uploadRatio / 5)

  // 文件上传完成后的执行函数
  if (progressEvent.loaded === progressEvent.total) {
    uploadProgressData.statusText = '自动识别中'
    uploadProgressData.statusNumber = '正在解压文件'
    uploadProgressData.desc = '文件识别中请勿退出'
  }
}

// 文件上传钩子
const handleAvatarBeforeUpload = (file: any) => {
  let passFlag = true

  // 检查文件格式
  const isAcceptFileTyle = [
    'application/zip',
    'application/zip-compressed',
    'application/x-zip-compressed',
  ].includes(file.type)
  if (!isAcceptFileTyle) {
    message.error('不支持的文件格式! 请上传 zip 格式的压缩文件')
    passFlag = false
  }

  // 检查文件大小
  const isLimit20M = file.size / 1024 / 1024 < 500
  if (!isLimit20M) {
    message.error('图片或文件过大! 请上传小于500M的图片或文件')
    passFlag = false
  }

  if (passFlag) {
    uploadProgressData.statusText = '文件上传中'
    // 上传文件
    const uploadFileFormData = new FormData()
    uploadFileFormData.append('file', file)

    isUploading.value = true

    nextTick(() => {
      try {
        if (uploadProgressData.progressBar)
          uploadProgressData.progressBar.destroy()
        uploadProgressData.progressBar = new ProgressBar.Circle(
          document.getElementById('upload-process-bar'),
          {
            strokeWidth: 6,
            trailColor: '#DADADA',
            trailWidth: 6,
            duration: 200,
            easing: 'bounce',
            from: { color: '#0D55CF' },
            to: { color: '#0D55CF' },
            // Set default step function for all animate calls
            step: function (state: any, circle: any) {
              circle.path.setAttribute('stroke', state.color)
            },
          }
        )
      } catch (e) {
        console.error(e)
      }

      requestPayableFileUpload(uploadFileFormData, handleOnUploadProgress)
        .then(() => {
          // 文件上传结束，接口返回以后，后台开始解析，获取解析进度
          handleReFetchParsingProgress()
        })
        .catch(() => {
          uploadProgressData.fail = true
          uploadProgressData.desc = '文件上传失败，请重试'
          uploadProgressData.progressBar?.animate(1, {
            from: { color: '#f00' },
            to: { color: '#f00' },
          })
        })
    })
  }

  return false
}

defineExpose({ handleOpen })
</script>

<style lang="scss" scoped>
.content-container {
  display: inline-block;
  width: 100%;

  .form-container {
    .form-item {
      display: flex;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        flex-shrink: 0;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #8a94a6;
        line-height: 20px;
      }

      .value {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #8a94a6;
        line-height: 20px;
      }
    }
  }

  .upload-btn {
    width: 100%;
    height: 48px;
    margin-top: 20px;
    background: #0c66ff;
    border-radius: 24px;
    line-height: 48px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    box-sizing: border-box;
    cursor: pointer;
  }

  .upload-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 298px;

    .upload-process-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: 160px;
      height: 160px;
      color: #0d55cf;

      #upload-process-bar {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
      }

      .upload-process-text {
        display: flex;
        align-items: center;
        flex-direction: column;

        > span {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #0d55cf;
          line-height: 24px;
        }
      }
    }

    .upload-desc {
      margin-top: 24px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }
  }

  :deep(.ant-upload) {
    width: 100%;
  }
}
</style>
