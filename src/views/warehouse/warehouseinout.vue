<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{ row }" slot="warehouseType">
        <el-tag
          :style="{
            color: row.warehouseType == 1 ? '#67c23a' : '#A6AEBC',
            border: row.warehouseType == 1 ? '1px solid #67c23a' : '1px solid #C9CED6',
            background: row.warehouseType == 1 ? '#EAFCF7' : '#fff',
          }"
        >{{
            row.warehouseType == 1 ? '入库' : '出库'
          }}
        </el-tag>
      </template>
      <template slot="goodsInfos" slot-scope="{ row }">
        <div class="goodsInfo-slot">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.logo"
            fit="contain"
          ></el-image>
          <span class="demonstration">{{ row.goodsName }}</span>
        </div>
      </template>
      <template slot-scope="{ row }" slot="logo">
<!--        <div-->
<!--          style="color: #409eff; cursor: pointer"-->
<!--          type="info"-->
<!--          @click="openPreview(row)"-->
<!--        >-->
<!--          查看-->
<!--        </div>-->
      </template>
    </avue-crud>
    <FilePreview :url="pdfSrc"/>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/warehouse/warehouseinout";
import {mapGetters} from "vuex";
import FilePreview from '@/components/file-preview'

var DIC = {
  STATUS: [{
    label: '入库',
    value: 1
  }, {
    label: '出库',
    value: 2
  }]
}
export default {
  components: {FilePreview},
  data() {
    return {
      pdfSrc: '',
      dialogVisible: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        menu: false,
        calcHeight: 30,
        addBtn: false,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        dialogClickModal: false,
        headerAlign: 'center',
        align: 'center',
        column: [
          {
            label: "出入库单号",
            width: 120,
            overHidden: true,
            prop: "inventoryNo",
            search: true,
          },
          {
            label: "融资编号",
            width: 100,
            overHidden: true,
            prop: "financeNo",
            search: true,
          },
          {
            label: "出/入库",
            prop: "warehouseType",
            slot: true,
            search: true,
            dispiay: false,
            type: "select",
            dicData: DIC.STATUS
          },
          {
            label: '货品信息',
            prop: 'goodsInfos',
            width: 200,
            overHidden: true,
            slot: true,
          },
          {
            label: "仓库",
            prop: "warehouseId",
            type: "select",
            search: true,
            overHidden: true,
            width: 150,
            dicUrl: '/api/blade-warehouse/web-back/warehouse/warehouse/warehouse-all',
            props: {
              label: "warehouseName",
              value: "id"
            },
          },
          {
            label: "供应商",
            search: true,
            prop: "supplierName",
            width: 100,
          },
          {
            label: "商品名称",
            prop: "goodsName",
            search: true,
            hide: true,
            display: false,
          },
          {
            label: "单位",
            prop: "goodsUnitValue",
          },
          {
            label: "数量",
            prop: "inOutQuantity",
          },
          {
            label: "采购单价（元）",
            width: 120,
            prop: "purchasePrice",
          },
          {
            label: "融资单价（元）",
            width: 120,
            prop: "financingPrice"
          },
          {
            label: "仓管员",
            prop: "warehouseManager",
          },
          // {
          //   label: "操作",
          //   slot: true,
          //   prop: "logo",
          // },
          {
            label: '创建日期',
            prop: 'createTime',
            type: 'datetime',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd 00:00:00',
            display: false,
          },
          {
            label: '创建日期',
            prop: 'createTimeRange',
            type: 'datetime',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd 00:00:00',
            searchRange: true,
            hide: true,
            display: false,
            search: true,
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.warehouseinout_add, false),
        viewBtn: this.vaildData(this.permission.warehouseinout_view, false),
        delBtn: this.vaildData(this.permission.warehouseinout_delete, false),
        editBtn: this.vaildData(this.permission.warehouseinout_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    openPreview(row) {
      this.pdfSrc = row.logo + '?time=' + new Date().getMilliseconds();
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      const {createTimeRange} = this.query
      let values = {
        ...params,
      }
      if (createTimeRange) {
        values = {
          ...params,
          create_time_datege: createTimeRange[0],
          create_time_datele: createTimeRange[1],
          ...this.query,
        }
        values.createTimeRange = null
      }
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query,values)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.goodsInfo-slot {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>
