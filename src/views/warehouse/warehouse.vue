<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.warehouse_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="info"
                   size="small"
                   plain
                   @click="offBatchShelf()">禁用
        </el-button>
        <el-button type="success"
                   size="small"
                   plain
                   @click="putBatchShelf()">启用
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="status">
        <el-tag
          :style="{
            color: row.status == 0 ? '#67c23a' : '#A6AEBC',
            border: row.status == 0 ? '1px solid #67c23a' : '1px solid #C9CED6',
            background: row.status == 0 ? '#EAFCF7' : '#fff',
          }"
        >{{
            row.status == 0 ? '已启用' : '已禁用'
          }}
        </el-tag>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row, index)"
          v-if="scope.row.status != 0&&permission.warehouse_edit"
          size="small"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row, index)"
          v-if="scope.row.status != 0&&permission.warehouse_delete"
          size="small"
        >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-success"
          size="small"
          v-if="scope.row.status == 0"
          @click.stop="offShelf(scope.row, scope.index)"
        >禁用
        </el-button>

        <el-button
          type="text"
          icon="el-icon-warning"
          size="small"
          v-if="scope.row.status == 1"
          @click.stop="putShelf(scope.row, scope.index)"
        >
          启用
        </el-button>
      </template>

    </avue-crud>
  </basic-container>
</template>

<script>
import {batchShelf, getDetail, getList, regionList, remove, save, update} from "@/api/warehouse/warehouse";
import {mapGetters} from "vuex";

var DIC = {
  SEX: [{
    label: '禁用',
    value: 1
  }, {
    label: '启用',
    value: 0
  }]
}
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      regionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        indexLabel: '序号',
        height: 'auto',
        editBtn: false,
        delBtn: false,
        calcHeight: 30,
        tip: true,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        headerAlign: 'center',
        align: 'center',
        column: [
          {
            label: "仓库编号",
            prop: "warehouseNo",
            width: 130,
            search: true,
            display: false,
            rules: [{
              required: true,
              message: "请输入仓库编号",
              trigger: "blur"
            }]
          },
          {
            label: "仓储方",
            prop: 'storageId',
            width: 100,
            type: "select",
            search: true,
            dicUrl: '/api/blade-customer/web-back/customer/customerSupervise/supper-all',
            props: {
              label: "superviseName",
              value: "id"
            },
          },
          {
            label: "仓储区域",
            prop: "regionCode",
            type: "tree",
            search: true,
            hide: true,
            dataType: "string",
            dicData: [],
            rules: [{
              required: true,
              message: "请选择仓储区域",
              trigger: "blur"
            }]
          },
          {
            label: "仓储区域",
            prop: "regionCodeName",
            display: false
          },
          {
            label: "联系人",
            prop: "contacts",
            search: true,
            rules: [{
              required: true,
              message: "请输入联系人",
              trigger: "blur"
            }]
          },
          {
            label: "电话",
            prop: "phone",
            search: true,
            rules: [{
              required: true,
              message: "请输入电话",
              trigger: "blur"
            }]
          },
          {
            label: "地址",
            prop: "address",
            search: true,
            rules: [{
              required: true,
              message: "地址",
              trigger: "blur"
            }]
          },
          {
            label: "仓库名称",
            prop: "warehouseName",
            search: true,
            rules: [{
              required: true,
              message: "请输入仓库名称",
              trigger: "blur"
            }]
          },
          {
            label: '上次修改时间',
            display: false,
            width:120,
            type: 'dateTime',
            prop: 'updateTime',
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '操作人',
            prop: 'updateUserName',
            display: false,
          },
          {
            label: '状态',
            prop: 'status',
            slot: true,
            search: true,
            display: false,
            type: "select",
            dicData: [{
              label: '已启用',
              value: 0,
            }, {
              label: '已禁用',
              value: 1,
            }]
          },
          {
            label: "是否启用",
            prop: "status",
            span: 6,
            type: "switch",
            dicData: DIC.SEX,
            mock: {
              type: 'dic'
            },
            hide: true,
            row: true,
          }
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.warehouse_add, false),
        viewBtn: this.vaildData(this.permission.warehouse_view, false),
        delBtn: this.vaildData(this.permission.warehouse_delete, false),
        editBtn: this.vaildData(this.permission.warehouse_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    putBatchShelf() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      let msg = '确认将选择的数据启用?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let status = 0
          return batchShelf(this.ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    offBatchShelf() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      let msg = '确认将选择的数据禁用?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let status = 1
          return batchShelf(this.ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    putShelf(row) {
      if (row.status == 0) {
        this.$message({
          type: 'info',
          message: '已经是启用状态!',
        })
        return
      }
      let msg = '确认启用状态?'

      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids = row.id
          let status = 0
          return batchShelf(ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    offShelf(row) {
      if (row.status === 1) {
        this.$message({
          type: 'info',
          message: '已经是禁用状态，不可重复禁用!',
        })
        return
      }
      let msg = '确认进行禁用?'

      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids = row.id
          let status = 1
          return batchShelf(ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },

    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    rowSave(row, done, loading) {
      save(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      regionList().then(res => {
        const regionCode = this.findObject(
          this.option.column,
          'regionCode'
        )
        regionCode.dicData = res.data.data
      })
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });

    }
  }
};
</script>

<style>
</style>
