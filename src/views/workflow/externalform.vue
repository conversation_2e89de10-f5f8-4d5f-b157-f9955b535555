<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="toAddPage()"
          >新增
        </el-button>
      </template>
      <template slot="businessType" slot-scope="{ row }">
        <el-tag type="primary" v-if="row.businessType === 1" effect="plain"
          >应收账款质押</el-tag
        >
        <el-tag type="success" v-else-if="row.businessType === 2" effect="plain"
          >代采融资</el-tag
        >
        <el-tag type="success" v-else-if="row.businessType === 3" effect="plain"
          >云信</el-tag
        >
        <el-tag type="success" v-else-if="row.businessType === 4" effect="plain"
          >动产质押</el-tag
        >
      </template>
      <template slot="status" slot-scope="{ row }">
        <el-tag type="info" v-if="row.status === 1" effect="plain"
          >已禁用</el-tag
        >
        <el-tag type="success" v-if="row.status === 2" effect="plain"
          >已启用</el-tag
        >
      </template>
      <template slot-scope="{ row, index, type, size }" slot="menu">
        <el-button
          icon="el-icon-view"
          :size="size"
          :type="type"
          @click.stop="lookTo(row)"
          >查看</el-button
        >
        <el-button
          icon="el-icon-edit"
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click.stop="goTo(row)"
          >编辑</el-button
        >
        <el-button
          icon="el-icon-del"
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click.stop="$refs.crud.rowDel(row, index)"
          >删除</el-button
        >
        <el-button
          icon="el-icon-top"
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click="handlerEnable(row)"
          >启用</el-button
        >
        <el-button
          icon="el-icon-bottom"
          v-if="row.status === 2"
          :size="size"
          :type="type"
          @click="handlerDisable(row)"
          >禁用</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  enable,
  disable,
} from '@/api/workflow/externalform'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        selection: false,
        dialogClickModal: false,
        align: 'center',
        column: [
          {
            label: '流程key',
            prop: 'formKey',
            rules: [
              {
                required: true,
                message: '请输入流程key',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '表单名称',
            prop: 'name',
            rules: [
              {
                required: true,
                message: '请输入表单名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '业务类型',
            prop: 'businessType',
            slot: true,
            rules: [
              {
                required: true,
                message: '请输入业务类型',
                trigger: 'blur',
              },
            ],
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '上次修改时间',
            prop: 'updateTime',
            type: 'datetime',
          },
          {
            label: '操作人',
            prop: 'operator',
          },
          {
            label: '状态',
            prop: 'status',
            slot: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.externalform_add, false),
        viewBtn: this.vaildData(this.permission.externalform_view, false),
        delBtn: this.vaildData(this.permission.externalform_delete, false),
        editBtn: this.vaildData(this.permission.externalform_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    handlerEnable(row) {
      this.$confirm('确定将选择数据启用?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return enable(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handlerDisable(row) {
      this.$confirm('确定将选择数据禁用?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return disable(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    toAddPage() {
      this.$router.push({
        path: '/workflow/process/product/externalform/addform',
      })
      sessionStorage.setItem('lookCollect', '')
    },
    lookTo(row) {
      this.$router.push({
        path: '/workflow/process/product/externalform/addform',
        query: { id: row.id },
      })
      sessionStorage.setItem('lookCollect', 'true')
    },
    goTo(row) {
      this.$router.push({
        path: '/workflow/process/product/externalform/addform',
        query: { id: row.id },
      })
      sessionStorage.setItem('lookCollect', 'false')
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
