<template>
  <div class="goods-list-box">
    <div v-if="loadingType" class="goods-list-for-box">
      <div class="left-goods-box">
        <header>
          <div class="head-left-box">
            <span class="product-Picture">
              <img :src="goodData.capitalLogo" alt="" />
            </span>
            <span class="goods-name">{{ goodData.goodsName }}</span>
            <span class="long-string" />
            <span class="goods-info">
              {{ goodData.goodsExplain }}
            </span>
          </div>
          <div class="head-right-box">
            <div class="tag-box">
              <CommonTag
                padding="6px 12px"
                :color="items.labelColor"
                :borderColor="items.labelColor"
                backgroundColor="#fff"
                :name="items.name"
                v-for="items in goodData.labelList"
                :key="items.goodsId"
              />
            </div>
            <div class="highQuality-tag" v-if="goodData.isHighQuality === 1">
              <MySvgIcon
                icon-class="icon-hao"
                style="font-size: 20px; fill: #0d55cf"
              />
              <span>优质</span>
            </div>
            <div
              class="canBeOpened-tag highQuality-tag"
              v-if="!goodData.customerGoodsId"
            >
              <MySvgIcon
                icon-class="icon-chenggong"
                style="font-size: 20px; fill: #00865a"
              />
              <span>可开通</span>
            </div>
          </div>
        </header>
        <article style="margin-left: -24px">
          <div class="goods-info-for-box">
            <div class="info-children-box info-after">
              <div class="laber-box">
                <span>{{ goodData.loanAmountEnd }}</span>
                <span>元</span>
              </div>
              <span class="value-box">最高可借</span>
            </div>
            <div
              class="info-children-box info-after"
              v-if="goodData.annualInterestRateType !== 1"
            >
              <div class="laber-box">
                <span>{{ goodData.annualInterestRateStart }}</span>
                <span>%</span>
              </div>
              <span class="value-box">年利率低至</span>
            </div>
            <div class="info-children-box">
              <div class="laber-box">
                <span>{{ goodData.loadTermEnd }}</span>
                <span>{{ goodData.loadTermUnit }}</span>
              </div>
              <span class="value-box">最长期限</span>
            </div>
            <!-- <div class="info-children-box">
              <div class="laber-box">
                <span>{{ goodData.annualInterestRateEnd }}</span>
                <span>%</span>
              </div>
              <span class="value-box">最高可贷</span>
            </div> -->
          </div>
        </article>
      </div>
      <div class="right-goods-box">
        <span class="goods-right-long-string" />
        <div
          class="goods-right-item-financing"
          @click="handleImmediate(goodData)"
        >
          <span class="goods-right-item-financing-fiter">
            {{ goodData.status === 3 ? '立即开单' : '立即开通' }}
            <MySvgIcon
              icon-class="icon-youjiantou-chang"
              style="color: white; font-size: 18px"
            />
          </span>
        </div>
        <!-- <div>
        <NButton
          class="blue border primary"
          type="info"
          style="height: 40px; width: 116px"
          round
          :bordered="false"
          @click="handleImmediate()"
        >
          立即融资
          <MySvgIcon
            icon-class="icon-youjiantou-xichang1"
            style="font-size: 20px; fill: #fff; margin-left: 4px"
          />
        </NButton>
      </div> -->
      </div>
    </div>
    <a-skeleton v-else active avatar :paragraph="{ rows: 2 }" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'goodsBox',
}
</script>
<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const store = useStore()
import CommonTag from '@/components/CommonTag/index.vue'
const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
const isVerified = computed<boolean>(() => store.getters['Auth/isVerified'])
// import { NButton } from 'naive-ui'

const router = useRouter()

defineProps({
  goodData: {
    type: Object,
    required: true,
    default: () => {},
  },
  loadingType: {
    type: Boolean,
    required: false,
    default: false,
  },
})

const handleImmediate = data => {
  const {
    id,
    type,
    capitalId,
    customerGoodsId,
    goodsName,
    capitalName,
    overdueInterestRate,
    enterpriseId,
    availableAmountStr,
    expireTime,
    status,
  } = data
  switch (true) {
    case status !== 3:
      router.push({
        name: 'ProductDetails',
        query: { goodId: id, goodType: type, capitalId },
      })
      break
    case !isLogined.value:
      store.dispatch('Auth/openLoginDialog')
      break
    // 是否已实名
    case !isVerified.value:
      store.dispatch('Auth/openRealNameVerificationTipDialog')
      break
    case status === 3:
      // router.push({
      //   name: 'openTheDetails',
      //   query: { goodId: id, customerGoodsId },
      // })
      router.push({
        name: 'cloudBillProcess',
        query: {
          cloudProductId: id,
          cloudProductName: goodsName,
          fundName: capitalName,
          fundId: capitalId,
          overdueInterestRate,
          enterpriseId,
          availableAmountStr,
          expireTime,
        },
      })
      break
  }
}
</script>

<style lang="scss" scoped>
.goods-list-box {
  width: 100%;
  position: relative;
  background: #ffffff;
  box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
  border-radius: 16px;
  margin-top: 24px;
  overflow: hidden;
  padding: 24px;
  box-sizing: border-box;

  .goods-list-for-box {
    height: 100px;
    display: flex;
    align-items: center;

    .left-goods-box {
      header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 1195px;

        .head-left-box {
          display: flex;
          align-items: center;

          .product-Picture {
            width: 32px;
            height: 32px;
            border: 1px solid #ffffff;
            border-radius: 100px;
            overflow: hidden;
            display: block;
            margin-right: 8px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .goods-name {
            height: 28px;
            font-size: 20px;
            font-weight: 400;
            color: #0a1f44;
            line-height: 28px;
            margin-right: 12px;
          }

          .long-string {
            width: 1px;
            height: 20px;
            background: #b5bbc6;
            margin-right: 12px;
          }

          .goods-info {
            width: 500px;
            display: block;
            height: 21px;
            font-size: 14px;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .head-right-box {
          display: flex;
          align-items: center;

          > * {
            margin-right: 12px;

            &:last-child {
              margin-right: 24px;
            }
          }

          .tag-box {
            display: flex;
            align-items: center;

            > * {
              margin-right: 12px;

              &:last-child {
                margin-right: 0;
              }
            }
          }

          .highQuality-tag {
            height: 28px;
            background: #cce6ff;
            color: #0d55cf;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px 10px;
            box-sizing: border-box;
          }

          .canBeOpened-tag {
            background: #92eccf;
            color: #00865a;
          }
        }
      }

      .goods-info-for-box {
        display: flex;
        align-items: center;

        .info-children-box {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          height: 54px;
          width: 304px;
          margin-top: 26px;

          .laber-box {
            display: flex;
            align-items: baseline;

            & span:first-child {
              height: 34px;
              font-size: 28px;
              color: #031222;
              line-height: 35px;
            }

            & span:last-child {
              height: 20px;
              font-size: 14px;
              font-weight: 400;
              color: #53627c;
              line-height: 20px;
            }
          }

          .value-box {
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
          }
        }

        .info-after {
          &::after {
            content: '';
            display: block;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 28px;
            background: #e1e4e8;
          }
        }
      }
    }

    .right-goods-box {
      display: flex;
      align-items: center;

      .goods-right-long-string {
        width: 1px;
        height: 158px;
        margin-right: 32px;
        background: linear-gradient(
          180deg,
          rgba(225, 228, 232, 0) 0%,
          #e1e4e8 47%,
          rgba(225, 228, 232, 0) 100%
        );
      }

      .goods-right-item-financing {
        width: 116px;
        height: 40px;
        background: #0c66ff;
        padding: 10px 20px;
        box-sizing: border-box;
        border-radius: 25px;
        font-size: 14px;
        @include family-PingFangSC-Medium;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        white-space: nowrap;
        position: relative;
        cursor: pointer;
        overflow: hidden;

        .goods-right-item-financing-fiter {
          position: relative;
          z-index: 1;
        }

        @keyframes move {
          0% {
            transform: translate(0);
          }
          25% {
            transform: translate(50%);
          }
          50% {
            transform: translate(70%);
          }
        }

        & > #my-svg-icons {
          flex-shrink: 0;
          margin-left: 4px;
        }

        &::after {
          display: inline-block;
          width: 116px;
          height: 40px;
          border-radius: 25px;
          top: 0;
          bottom: 0;
          left: 0;
          transform: translate(-100%);
          position: absolute;
          background: rgba(255, 255, 255, 0.1);
          content: '';
          transition: all 0.3s;
        }

        &:hover::after {
          transform: translate(0);
        }

        &:hover #my-svg-icons {
          animation: move 0.6s linear 2;
        }
      }
    }
  }

  &:last-child {
    margin-bottom: 24px;
  }

  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    width: 161px;
    height: 143px;
    bottom: -18px;
    right: -20px;
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.09;
    border-radius: 150px;
    filter: blur(10px);
  }
  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    width: 137px;
    height: 136px;
    top: -35px;
    left: -79px;
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.09;
    border-radius: 150px;
    filter: blur(23px);
  }
}
</style>
