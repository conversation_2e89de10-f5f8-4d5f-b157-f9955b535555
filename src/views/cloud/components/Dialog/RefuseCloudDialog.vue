<template>
  <GlobalDialog
    title="拒绝签收"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <a-textarea
      v-model:value="remark"
      :autoSize="{ minRows: 6 }"
      placeholder="请输入拒绝原因,不超过300字"
      allow-clear
    />
    <template #button>
      <div style="width: 100%; text-align: right">
        <a-button
          style="
            width: 73px;
            height: 40px;
            margin-right: 12px;
            border-radius: 100px;
          "
          @click="handleClose"
        >
          取 消
        </a-button>
        <a-button
          style="width: 73px; height: 40px; border-radius: 100px"
          type="primary"
          @click="handleConfirm"
        >
          确 定
        </a-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'CloudCard',
}
</script>

<script setup>
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { updateCloudAssets } from '@/api/user/cloud'
import { ref } from 'vue'
import { message } from 'ant-design-vue'

const props = defineProps({
  rowId: {
    type: [String, Number],
    required: true,
    default: '',
  },
})

const remark = ref('')
const dialogRef = ref(null)
const handleToDetail = () => {
  handleOpen()
}
const emit = defineEmits(['refresh'])

const handleOpen = () => {
  remark.value = ''
  dialogRef.value.handleOpen()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}
const handleConfirm = () => {
  updateCloudAssets({
    id: props.rowId,
    type: 2,
    denialReason: remark.value,
  }).then(({ data }) => {
    if (data.code === 200) {
      message.success('拒绝签收成功')
      emit('refresh')
      dialogRef.value.handleClose()
    }
  })
}
defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.cloud-list {
  box-sizing: border-box;
  padding-bottom: 24px;
  border-bottom: 1px solid #f1f2f4;
  .cloud-item {
    display: flex;
    align-items: center;
    & span:nth-of-type(1) {
      min-width: 60px;
      margin-right: 20px;
      color: #8a94a6;
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;
    }
    .cloud-subtitle {
      color: #0a1f44;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }
  }
  .cloud-item:last-child {
    display: flex;
    align-items: flex-start;
  }
  .cloud-item + .cloud-item {
    margin-top: 24px;
  }
}

.cloud-dialog-contract {
  margin-top: 12px;
  .contract-name {
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    color: #8a94a6;
    margin-bottom: 12px;
  }
  .contract-list {
    .contract-item {
      box-sizing: border-box;
      border: 1px solid #e1e4e8;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .contract-item-left {
        .contract-item-title {
          margin-left: 8px;
          font-size: 14px;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
        }
      }
      .contract-item-right {
        display: flex;
        align-items: center;
        & span {
          display: block;
          line-height: 20px;
          color: #0d55cf;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    .contract-item + .contract-item {
      margin-top: 12px;
    }
  }
}
.cloud-status {
  display: inline-block;
  border-radius: 100px;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  padding: 4px 10px;
}
.cloud-status-green {
  color: #00865a;
  background-color: #cff8eb;
}
.cloud-status-blue {
  color: #0d55cf;
  background-color: #ebf5ff;
}
.cloud-status-gray {
  color: #8a94a6;
  background-color: #f8f9fb;
}
</style>
