<template>
  <GlobalDialog
    title="确认签收"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div>
      <div class="bankInput" @click="open">
        <div class="smallText">
          {{
            finance.openingBankChildren
              ? `${finance.openingBankChildren}(${finance.lastNum})`
              : '点击绑定对公账户'
          }}
        </div>
        <div class="imgbox">
          <MySvgIcon
            icon-class="icon-youjiantou1"
            style="color: #8a94a6; font-size: 20px"
          />
        </div>
      </div>
      <!-- 合同 -->
      <Contract
        v-if="finance.openingBankChildren"
        style="margin-top: -68px; overflow-y: clip"
        itemWidth="100%"
        :processIndex="route.query.businessType == 1 ? 14 : 15"
        :autoLock="true"
        :flowingWave="true"
        :receiveData="route.query.cloudCode"
      />
    </div>
    <template #button>
      <div style="width: 100%; text-align: right">
        <a-button
          style="
            width: 73px;
            height: 40px;
            margin-right: 12px;
            border-radius: 100px;
          "
          @click="handleClose"
        >
          取 消
        </a-button>
        <a-button
          style="width: 73px; height: 40px; border-radius: 100px"
          type="primary"
          :loading="loading"
          @click="handleConfirm"
          :disabled="savetype"
        >
          确 定
        </a-button>
      </div>
    </template>
  </GlobalDialog>
  <SelectAccountDialog
    ref="selectAccountDialog"
    :bankName="capitalName"
    :otherBackName="capitalName"
    capital="0"
    @select="handleSelect"
  ></SelectAccountDialog>
</template>

<script>
export default {
  name: 'CloudCard',
}
</script>

<script setup>
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import SelectAccountDialog from '@/components/selectAccountDialog/index.vue'
import { updateCloudAssets } from '@/api/user/cloud'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
const route = useRoute()
const store = useStore()
import Contract from '@/views/product/component/contract'
// 判断是否有合同未签署（没配置合同就不会判断）
const signStatus = computed(() => store.getters['Product/signStatus'])
const demonstrator = computed(() => store.getters['Product/demonstrator'])

const props = defineProps({
  rowId: {
    type: [String, Number],
    required: true,
    default: '',
  },
  capitalName: {
    type: String,
  },
})
const savetype = ref(true)
const finance = ref({})
const dialogRef = ref(null)
const selectAccountDialog = ref(null)
const loading = ref(false)
const emit = defineEmits(['refresh'])
const handleOpen = () => {
  dialogRef.value.handleOpen()
}
const open = () => {
  selectAccountDialog.value.handleOpen()
}
const handleSelect = row => {
  finance.value.bankAccountNum = row.bankCardNo
  finance.value.lastNum = row.bankCardNoCutOut
  finance.value.openingBankChildren = row.bankDeposit
  savetype.value = false
}
const handleClose = () => {
  dialogRef.value.handleClose()
}

const handleConfirm = () => {
  if (signStatus.value && !demonstrator.value) {
    message.error('还有授权书未签署,请先签署完成')
    return
  }
  loading.value = true
  updateCloudAssets({
    id: props.rowId || route.query.id,
    type: 1,
    bankDeposit: finance.value.openingBankChildren,
    backName: props.capitalName,
    bankCardNo: finance.value.bankAccountNum,
  })
    .then(({ data }) => {
      if (data.code === 200) {
        message.success('签收成功')
        emit('refresh')
        dialogRef.value.handleClose()
      }
      loading.value = false
    })
    .catch(() => (loading.value = false))
}

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
.bankInput {
  padding: 9px 20px;
  border-radius: 4px;
  border: 1px solid #e1e4e8;
  display: flex;
  align-items: center;
  .smallText {
    width: 344px;
    line-height: 20px;
    font-size: 14px;
    font-family: SFProText-Medium, SFProText;
    font-weight: 500;
    color: #0a1f44;
    line-height: 20px;
    margin-left: 16px;
    margin-right: 8px;
  }
}
.cloud-list {
  box-sizing: border-box;
  padding-bottom: 24px;
  border-bottom: 1px solid #f1f2f4;
  .cloud-item {
    display: flex;
    align-items: center;
    & span:nth-of-type(1) {
      min-width: 60px;
      margin-right: 20px;
      color: #8a94a6;
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;
    }
    .cloud-subtitle {
      color: #0a1f44;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }
  }
  .cloud-item:last-child {
    display: flex;
    align-items: flex-start;
  }
  .cloud-item + .cloud-item {
    margin-top: 24px;
  }
}

.cloud-status {
  display: inline-block;
  border-radius: 100px;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  padding: 4px 10px;
}
.cloud-status-green {
  color: #00865a;
  background-color: #cff8eb;
}
.cloud-status-blue {
  color: #0d55cf;
  background-color: #ebf5ff;
}
.cloud-status-gray {
  color: #8a94a6;
  background-color: #f8f9fb;
}
</style>
