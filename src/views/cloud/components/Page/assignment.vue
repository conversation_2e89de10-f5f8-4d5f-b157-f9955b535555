<template>
  <div class="trade-account-wrapper">
    <div class="trade-account-container">
      <ArticleSetps
        info="转让云信"
        :arrData="setpHeaderData"
        :current="currentNode"
        :widths="'1000px'"
      />
      <div class="content-Tips">
        <MySvgIcon
          icon-class="icon-xinxi"
          style="fill: #8a94a6; font-size: 20px"
        ></MySvgIcon>
        <span>您当前正在转让云信单，云信额度只能转让您的上游贸易伙伴</span>
      </div>
      <!-- 临时用的调用合同，后续自己写合同签署可以删除 -->
      <!-- <Contract v-show="false" :processIndex="1" receiveData="001" /> -->
      <div class="cloud-amout">
        <span class="cloud-name">持单金额(元)</span>
        <span class="cloud-num">{{ formatMoney(cloudConfig.amount) }}</span>
        <span class="cloud-payday">承诺付款日: {{ cloudConfig.endDate }}</span>
        <span class="cloud-rotatable"
          >可转余额(元): {{ formatMoney(usableAmout || 0) }}</span
        >
      </div>
      <div class="main-content">
        <div class="form-container">
          <template v-for="(item, index) of allFormState" :key="item.key">
            <FormItem
              :formData="item"
              :index="index"
              :checkAccumulator="formCheckAccumulator"
              :formDisabled="formDisabled"
              :list="companyOption"
              :amout="usableAmout"
              @updateFormData="handleUpdateFormData"
              @deleteForm="handleDeleteForm"
              @validatePass="validatePass"
            />
            <!-- 合同 -->
            <template v-if="item.cloudCode && currentNode != 0">
              <Contract
                style="margin-top: -60px"
                itemWidth="1000px"
                :processIndex="15"
                :flowingWave="true"
                :externalParameterj="item"
                :receiveData="item.cloudCode"
              />
            </template>
          </template>
          <div
            class="add-more"
            @click="handleAddMoreForm"
            v-if="currentNode == 0"
          >
            <MySvgIcon
              icon-class="icon-add-circle"
              style="fill: #0a1f44; font-size: 24px"
            />
            <span class="desc">添加</span>
          </div>
        </div>

        <div class="button-wrapper" v-if="currentNode == 0">
          <span class="ant-col ant-form-item-label" />
          <a-button class="submit-btn" type="primary" @click="handleNext"
            >下一步</a-button
          >
        </div>
        <template v-else>
          <div class="button-wrapper2">
            <a-button type="primary" class="submit-btn" ghost @click="preStep"
              >上一步</a-button
            >
            <a-button
              type="primary"
              class="submit-btn"
              @click="handleConfirmAssign"
              >确定转让</a-button
            >
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CloudAssignment',
}

const initFormData = () => ({
  userId: null,
  amount: '',
  remark: '',
})
</script>
<script setup>
import { ref, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import FormItem from '@/views/cloud/components/Page/FormItem'
import { formatMoney } from '@/utils/utils'
import ArticleSetps from '@/components/articleSetps/index.vue'
import {
  getCloudAssetsDetail,
  saveNextTransfer,
  getCompanyList,
  submitTransfer,
  getCloudredisData,
} from '@/api/user/cloud'
import { message } from 'ant-design-vue'
import router from '@/router'
import Contract from '@/views/product/component/contract'

const route = useRoute()
const store = useStore()
const allFormState = ref([initFormData()])
const formCheckAccumulator = ref(0)
const currentNode = ref(0)
const formDisabled = ref(false)
const cloudConfig = ref({})
const companyOption = ref([]) // 收单企业数据
const nextStepData = ref({}) // 接收下一步数据

const usableAmout = ref(0)

// 判断是否有合同未签署（没配置合同就不会判断）
const signStatus = computed(() => store.getters['Product/signStatus'])
const demonstrator = computed(() => store.getters['Product/demonstrator'])

let passCount = 0
const setpHeaderData = [
  { id: 1, name: '填写转让信息' },
  { id: 2, name: '转让确认' },
]
watch(
  allFormState,
  val => {
    let amountAll = 0
    for (const index in val) {
      let a = isNaN(Number(val[index].amount)) ? 0 : Number(val[index].amount)
      amountAll += a
    }
    usableAmout.value = Number(cloudConfig.value.amount) - amountAll
  },
  { deep: true }
)
// 收单企业下拉列表数据
const getCloudAssetsDetailFun = () => {
  if (!route.query.id) {
    setTimeout(() => {
      getCloudAssetsDetailFun()
    }, 200)
    return
  }
  getCloudAssetsDetail({ id: route.query.id }).then(({ data }) => {
    if (data.code === 200) {
      cloudConfig.value = { ...data.data }
      usableAmout.value = data.data.amount || 0
      getCompanyList({ ability: 2 }).then(({ data }) => {
        let list = []
        if (data.code === 200) {
          for (const item of data.data) {
            list.push({
              ...item,
              label: item.companyHeightName,
              value: item.companyHeightId,
            })
          }
        }
        companyOption.value = list
        // 刷新回显之前填写的
        if (currentNode.value == 1) {
          getCloudredisDataFun()
        }
      })
    }
  })
}
getCloudAssetsDetailFun()

const getCloudredisDataFun = () => {
  if (!route.query.cloudRedisCode) {
    setTimeout(() => {
      getCloudredisDataFun()
    }, 200)
    return
  }
  // 刷新回显之前填写的
  getCloudredisData({ cloudRedisCode: route.query.cloudRedisCode }).then(
    ({ data }) => {
      if (data.success) {
        const { data: resData } = data
        allFormState.value = resData.cloudAssets
        nextStepData.value = {
          ...resData,
          cloudAssets:
            resData.cloudAssets && resData.cloudAssets.length
              ? resData.cloudAssets
              : [],
        }
        currentNode.value++
        formDisabled.value = true
      }
    }
  )
}

const getCompanyName = id =>
  companyOption.value.find(item => item.value === id).label

const handleUpdateFormData = (value, index) => {
  allFormState.value[index] = value
}

const handleDeleteForm = index => {
  allFormState.value.splice(index, 1)
}

const handleAddMoreForm = () => {
  allFormState.value.push(initFormData())
}

const handleNext = () => {
  if (allFormState.value.length === 0) return
  passCount = 0
  formCheckAccumulator.value++
  let isContinue = true
  for (let i = 0; i < allFormState.value.length; i++) {
    for (let j = i + 1; j < allFormState.value.length; j++) {
      if (allFormState.value[i].userId === allFormState.value[j].userId) {
        isContinue = false
      }
    }
  }
  if (!isContinue) {
    message.error('当前存在相同的收单企业！')
    return
  }

  let cloudAssets = []

  allFormState.value.forEach(item => {
    cloudAssets.push({
      name: getCompanyName(item.userId),
      userId: item.userId,
      companyName: cloudConfig.value.name,
      companyId: cloudConfig.value.userId,
      amount: item.amount,
      cloudBillAmount: item.amount,
      remark: item.remark,
    })
  })

  const params = {
    cloudParentCode: cloudConfig.value.cloudCode,
    cloudAssets,
  }
  saveNextTransfer(params).then(({ data }) => {
    if (data.success) {
      const { data: resData } = data
      nextStepData.value = {
        ...resData,
        cloudAssets:
          resData.cloudAssets && resData.cloudAssets.length
            ? resData.cloudAssets
            : [],
      }
      router.replace({
        query: {
          goodId: resData.cloudProductId,
          goodType: 3,
          cloudCode: route.query.cloudCode,
          businessType: route.query.businessType,
          cloudRedisCode: resData.cloudRedisCode,
          id: route.query.id,
        },
      })
      currentNode.value++
      formDisabled.value = true
      // 避免重复性 调取合同
      if (currentNode.value == 1) {
        getCloudredisDataFun()
      }
    }
  })
}

// 确定转让
const handleConfirmAssign = () => {
  if (signStatus.value && !demonstrator.value) {
    message.error('还有授权书未签署,请先签署完成')
    return
  }

  let cloudAssets = []

  for (const obj of nextStepData.value.cloudAssets) {
    cloudAssets.push({
      name: obj.name,
      userId: obj.userId,
      companyName: obj.companyName,
      companyId: obj.companyId,
      amount: obj.amount,
      cloudBillAmount: obj.cloudBillAmount,
      cloudCode: obj.cloudCode,
      remark: obj.remark,
      financingModel: cloudConfig.value.financingModel,
    })
  }
  const params = {
    cloudAssets,
    cloudParentCode: nextStepData.value.cloudParentCode,
    cloudCoreCode: nextStepData.value.cloudCoreCode,
    cloudRedisCode: nextStepData.value.cloudRedisCode,
  }

  submitTransfer(params).then(({ data }) => {
    if (data.code === 200) {
      message.success('提交成功!')
      router.push({
        //跳转详情拆分
        name: 'CloudDetail',
        query: {
          id: cloudConfig.value.id,
          currentIndex: 1,
          goodId: route.query.goodId,
          goodType: 3,
          cloudCode: route.query.cloudCode,
          businessType: route.query.businessType,
        },
      })
    }
  })
}

const nextStep = () => {
  if (currentNode.value > 1) return
}

const validatePass = () => {
  passCount++
  if (passCount === allFormState.value.length) {
    nextStep()
  }
}

const preStep = () => {
  currentNode.value = 0
  formDisabled.value = false
}
</script>

<style lang="scss" scoped>
.trade-account-wrapper {
  background-color: #fff;
}
.trade-account-container {
  max-width: 1400px;
  min-width: 1000px;
  margin: 40px auto 60px;

  .title-container {
    margin: 40px 0;
    text-align: center;

    .title-wrapper {
      display: inline-block;
      position: relative;

      h2 {
        margin: 0 !important;
        font-size: 24px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 32px;
      }

      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        display: inline-block;
        width: 48px;
        height: 7px;
        margin: auto;
        content: '';
        text-align: center;
        background: #0c66ff;
        transform: translateY(100%);
      }
    }
  }

  .steps-container {
    max-width: 1000px;
    margin: 0 auto 40px;

    :deep(.ant-steps-item-finish) {
      .ant-steps-item-icon {
        background: #0d55cf;
        border-color: #0d55cf;

        svg {
          fill: #fff;
        }
      }

      .ant-steps-item-tail::after {
        background: #0d55cf;
      }
    }

    :deep(.ant-steps-item-active) {
      .ant-steps-item-icon {
        background: #0d55cf;
        border-color: #0d55cf;
      }

      .ant-steps-item-tail::after {
        background: #e1e4e8;
      }
    }

    :deep(.ant-steps-item-wait) {
      .ant-steps-item-tail::after {
        background: #e1e4e8;
      }
    }

    :deep(.ant-steps-item-icon) {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    :deep(.ant-steps-item-content) {
      .ant-steps-item-title {
        font-size: 16px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 24px;
      }
    }

    :deep(.ant-steps-item-disabled) {
      cursor: auto;
    }
  }
  .content-Tips {
    width: 1400px;
    background-color: #f8f9fb;
    border-radius: 6px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    margin-top: 40px;

    & span {
      color: #8a94a6;
      line-height: 20px;
      font-size: 14px;
      font-weight: 400;
      margin-left: 4px;
    }
  }
  .cloud-amout {
    min-width: 1000px;
    margin: 58px auto 24px;
    display: flex;
    flex-direction: column;
    text-align: center;
    .cloud-name {
      line-height: 32px;
      font-size: 24px;
      font-weight: 500;
      color: #0a1f44;
      font-family: PingFangSC-Medium, PingFang SC;
      margin-bottom: 16px;
    }
    .cloud-num {
      line-height: 77px;
      font-size: 64px;
      font-weight: bold;
      color: #031222;
      margin-bottom: 16px;
    }
    .cloud-payday {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 500;
      color: #8a94a6;
      line-height: 24px;
    }
    .cloud-rotatable {
      font-size: 20px;
      font-weight: 600;
      color: #0d55cf;
      line-height: 28px;
    }
  }
  .main-content {
    max-width: 1400px;
    margin: auto;

    .height-container,
    .lower-container {
      padding: 32px 0;
      background: #f8f9fb;
      border-radius: 16px;
      border: 1px solid #efefef;
    }

    .lower-container {
      margin-top: 32px;
    }
  }

  .form-container {
    .add-more {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1000px;
      max-width: 1000px;
      margin: 24px auto 0;
      height: 64px;
      line-height: 64px;
      background: #fff;
      border-radius: 16px;
      border: 1px solid #efefef;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      text-align: center;
      cursor: pointer;

      .desc {
        margin-left: 4px;
        font-size: 16px;
        @include family-PingFangSC-Semibold;
        font-weight: 600;
        color: #0a1f44;
        line-height: 24px;
      }
    }
  }

  .button-wrapper {
    display: block;
    width: 400px;
    margin: 40px auto 0;

    .submit-btn {
      flex: 1;
      width: 100%;
      height: 48px;
      background: #0c66ff;
      border-color: #0c66ff;
      border-radius: 24px;
      overflow: hidden;

      :deep(span) {
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
  .button-wrapper2 {
    margin-top: 26px;
    text-align: center;
    .submit-btn {
      width: 188px;
      height: 48px;
      border-radius: 24px;
    }
    .submit-btn:nth-of-type(1) {
      background: #fff;
      border-color: #0c66ff;
      color: #0c66ff;
    }
    .submit-btn:nth-of-type(2) {
      background: #0c66ff;
      border-color: #0c66ff;
    }
    .submit-btn + .submit-btn {
      margin-left: 24px;
    }
  }

  .approval-container,
  .pass-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 100px;

    .title {
      margin-top: 12px;
      font-size: 24px;
      font-weight: 600;
      color: #0a1f44;
      line-height: 32px;
    }

    .desc {
      margin-top: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }

    .redir-btn {
      margin-top: 48px;
      display: inline-block;
      height: 48px;
      line-height: 48px;
      padding: 0 28px;
      border-radius: 100px;
      border: 1px solid #0c66ff;

      span {
        font-size: 16px;
        font-weight: 500;
        color: #0d55cf;
        line-height: 24px;
      }
    }
  }
}

:deep(.cloud-contract) {
  width: 1000px;
}
.background-white {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #fff;
  z-index: -1024;
}
</style>
