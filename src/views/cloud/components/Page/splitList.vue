<template>
  <div class="cloud-split-container">
    <div class="split-header">
      <div class="split-name">云信拆分列表</div>
      <BaseSwitchBar
        :labelList="labelList"
        @switch="handleSwitch"
        :state="currentIndex"
      />
    </div>
    <div class="split-main">
      <div class="cloud-search">
        <div class="cloud-search-left">
          <a-input
            class="search-item"
            v-model:value="searchData.cloudCode"
            placeholder="输入云信编号"
          />
          <a-input
            class="search-item"
            v-model:value="searchData.name"
            placeholder="输入收单企业名称"
          />
          <a-range-picker
            class="search-item"
            v-model:value="searchData.date"
            value-format="YYYY-MM-dd"
          />
        </div>
        <div class="cloud-search-right">
          <a-button class="search-btn" @click="handleSearch">搜索</a-button>
          <a-button class="search-btn" @click="handleSearchReset"
            >重置</a-button
          >
        </div>
      </div>

      <div class="cloud-contact-list" v-if="cloudData.length">
        <CloudCard
          width="320px"
          height="188px"
          subtitle2="金额"
          v-for="item in cloudData"
          :key="item"
          :rowItem="item"
        ></CloudCard>
      </div>
      <!-- <div></div> -->
      <div class="empty-container" v-else>
        <img src="@/assets/images/empty_2.svg" alt="" />
        <span class="desc">暂无数据</span>
      </div>

      <div class="cloud-pagination" v-if="cloudData.length > 0">
        <!-- <span>总2页</span> -->
        <a-pagination
          v-model:current="tablePaginationData.current"
          :total="tablePaginationData.total"
        >
          <template #itemRender="{ type, originalElement }">
            <div v-if="type === 'prev'" class="pagination-item prev">
              <MySvgIcon
                icon-class="icon-jiantou-zuo"
                style="font-size: 16px; margin-right: 2px"
                class="icon-class"
              ></MySvgIcon>
              <span>上一页</span>
            </div>
            <div v-else-if="type === 'next'" class="pagination-item next">
              <span>下一页</span>
              <MySvgIcon
                icon-class="icon-youjiantou1"
                style="font-size: 16px; margin-left: 2px"
                class="icon-class"
              ></MySvgIcon>
            </div>
            <component :is="originalElement" v-else></component>
          </template>
        </a-pagination>
      </div>
    </div>
  </div>
</template>

<script>
const labelList = [
  {
    label: '全部',
    value: 0,
  },
  {
    label: '转让待收',
    value: 1,
  },
  {
    label: '已转让',
    value: 2,
  },
  {
    label: '转让失败',
    value: 3,
  },
  {
    label: '融资待审',
    value: 4,
  },
  {
    label: '已融资',
    value: 5,
  },
  {
    label: '融资失败',
    value: 6,
  },
]
const createInitPagData = () => ({
  pageSize: 10,
  currentPage: 1,
  maxPage: 1,
  total: 0,
})
const createInitSearchData = () => ({
  cloudCode: undefined,
  name: '',
})

export default {
  name: 'SplitList',
}
</script>

<script setup>
import BaseSwitchBar from '@/components/BaseSwitchBar/index.vue'
import CloudCard from '@/businessComponents/CloudCard/index'
import { ref, reactive } from 'vue'
import { getCompanySplitList } from '@/api/user/cloud'
const tableLoading = ref(false)
const currentIndex = ref(0)

const cloudData = ref([])
const tablePaginationData = ref(createInitPagData())

let enableSearch = false
const searchData = reactive(createInitSearchData())
const handleSwitch = targetIndex => {
  currentIndex.value = targetIndex
  handleSearchReset()
}

const loadTableData = (current, pageSize) => {
  tableLoading.value = true
  // 构建搜索请求参数
  let requestObj = {
    current: current || 1,
    size: pageSize || 10,
  }

  if (enableSearch) {
    requestObj = {
      ...requestObj,
      cloudCode: formatSearchData.cloudCode,
      name: formatSearchData.name,
    }
  }
  requestObj.resolutionStatus =
    currentIndex.value === 0 ? null : currentIndex.value - 1

  getCompanySplitList(requestObj).then(({ data }) => {
    if (data.code === 200) {
      const resData = data.data?.length ? data.data : []
      let _list = []
      resData.forEach(item => {
        _list.push({
          ...item,
        })
      })
      tablePaginationData.value.currentPage = data.data.current
      tablePaginationData.value.pageSize = data.data.size
      tablePaginationData.value.total = data.data.total
      cloudData.value = _list
    }
  })
}
let formatSearchData = {
  cloudCode: undefined,
  name: '',
  state: '',
}
const handleSearchReset = () => {
  // 重置列表过滤项
  // 重置搜索内容
  enableSearch = false
  const initSearchData = createInitSearchData()
  for (const key in searchData) {
    searchData[key] = initSearchData[key]
  }
  initTableData()
}
// 页码发生变化
const handleTableChange = (pag, filters, sorter) => {
  if (!pag.current) return
  tablePaginationData.value.current = pag.current
  tablePaginationData.value.pageSize = pag.pageSize
  loadTableData(pag.current, pag.pageSize)
}

const initTableData = () => {
  tablePaginationData.value = createInitPagData()
  loadTableData(
    tablePaginationData.value.currentPage,
    tablePaginationData.value.pageSize
  )
}

// 搜索
const handleSearch = () => {
  enableSearch = true
  formatSearchData = {
    cloudCode: searchData.cloudCode,
    name: searchData.name,
    state: searchData.state,
  }
  initTableData()
}

initTableData()

defineExpose({ initTableData })
</script>
<style lang="scss" scoped>
.cloud-split-container {
  width: 1400px;
  background: #fff;
  margin: 24px auto;
  border-radius: 16px;
  box-sizing: border-box;

  .split-header {
    box-sizing: border-box;
    padding: 24px;
    border-bottom: 1px solid #f1f2f4;
    .split-name {
      color: #0a1f44;
      font-size: 24px;
      line-height: 32px;
      font-weight: bold;
      margin-bottom: 20px;
    }
  }
  .split-main {
    box-sizing: border-box;
    padding: 24px;
    .cloud-search {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .cloud-search-left {
        display: flex;
        align-items: center;
        .search-item {
          width: 260px;
          height: 40px;
          border-radius: 4px;
        }
        .search-item + .search-item {
          margin-left: 20px;
        }
      }
      .cloud-search-right {
        .search-btn {
          width: 68px;
          height: 40px;
          border-radius: 4px;
        }
        .search-btn + .search-btn {
          margin-left: 20px;
        }
      }
    }
    .cloud-contact-list {
      display: flex;
      align-items: center;
      flex-flow: row wrap;
      margin: 24px 0;

      :deep() {
        .cloud-card-box:nth-of-type(4n) {
          margin-right: 0;
        }
      }
    }
    .empty-container {
      width: 100%;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 40px auto;
      text-align: center;

      img {
        width: 200px;
        height: 200px;
      }

      .desc {
        font-size: 14px;
        font-weight: 500;
        color: #8a94a6;
        line-height: 20px;
      }
    }
    .cloud-pagination {
      text-align: right;
      // display: flex;1
      // align-items: center;
      // justify-content: space-between;
      & span {
        font-size: 14px;
        font-weight: 500;
        color: #8a94a6;
        line-height: 20px;
      }
      .pagination-item {
        background: #f8f9fb;
        width: 82px;
        line-height: 32px;
        &:hover {
          border: 1px solid #1890ff;
        }
        .icon-class {
          fill: #53627c;
        }
      }
      .prev {
        border-radius: 100px 0 0 100px;
      }
      .next {
        border-radius: 0px 100px 100px 0;
      }
    }
  }
}
:deep() {
  .cloud-pagination span {
    color: #53627c !important;
  }
  .ant-pagination-disabled {
    &:hover {
      .pagination-item {
        border-color: transparent !important;
      }
    }
    span {
      color: #a6aebc !important;
    }

    .icon-class {
      fill: #a6aebc !important;
    }
  }
}
</style>
