<template>
  <div class="financing-feedback">
    <div class="feedback-result">
      <MySvgIcon
        icon-class="icon-dengdai1"
        style="fill: #0d55cf; font-size: 64px; margin-bottom: 12px"
      />
      <span class="result-status">审批中</span>
      <span class="result-remark"
        >您的云信信息已提交,我们将会在1~3小时内进行审核,请耐心等待!</span
      >
      <div class="result-btn">
        <a-button type="primary" ghost @click="handleGoBack">返回首页</a-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BusinessApproval',
}
</script>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()

const handleGoBack = () => {
  router.push({ name: 'Home' })
}
</script>

<style lang="scss" scoped>
.financing-feedback {
  position: relative;
  background-color: #fff;
  box-sizing: border-box;
  max-width: 1400px;
  margin: auto;
  padding-top: 40px;

  .feedback-result {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .result-status {
    height: 32px;
    line-height: 32px;
    font-size: 24px;
    font-weight: 600;
    color: #0a1f44;
    margin-bottom: 12px;
  }
  .result-remark {
    font-size: 14px;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
    margin-bottom: 48px;
  }
  .result-btn {
    .ant-btn {
      width: 120px;
      height: 48px;
      border-radius: 100px;
      border: 1px solid #0c66ff;
      color: #0d55cf;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
