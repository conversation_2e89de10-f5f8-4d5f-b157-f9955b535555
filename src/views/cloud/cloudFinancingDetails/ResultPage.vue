<template>
  <div class="result-warp">
    <div class="result-content" v-if="false">
      <MySvgIcon
        icon-class="icon-chenggong"
        style="fill: #0bb07b; font-size: 64px"
      />
      <span class="result-title">恭喜您!绑定成功</span>
      <span class="result-remark">已成功申请云信融资</span>
      <div class="result-btn">
        <a-button type="primary">返回云信融资</a-button>
      </div>
    </div>

    <div class="result-content" v-else>
      <MySvgIcon
        icon-class="icon-shibai"
        style="fill: #f03d3d; font-size: 64px"
      />
      <span class="result-title">绑定失败</span>
      <span class="result-remark">已成功申请云信融资</span>
      <div class="result-btn">
        <a-button type="primary" ghost>返回云信融资</a-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {}
</script>

<style lang="scss" scoped>
.result-warp {
  position: relative;
  max-width: 1400px;
  margin: auto;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .result-content {
    margin-top: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .result-title {
      height: 32px;
      line-height: 32px;
      font-size: 24px;
      font-weight: 600;
      color: #0a1f44;
      margin: 12px 0;
    }
    .result-remark {
      font-size: 14px;
      font-weight: 500;
      height: 20px;
      line-height: 20px;
      color: #8a94a6;
      margin-bottom: 48px;
    }
    .result-btn {
      .ant-btn {
        width: 200px;
        height: 48px;
        background-color: #0c66ff;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
      }
    }
  }
}
</style>
