<template>
  <div class="financing-demand">
    <div class="cloud-step1-header">
      <div class="cloud-header-left">
        <div class="cloud-info-title">
          <span>云信信息</span>
          <span></span>
          <span>可转让金额: ￥{{ formatMoney(data.amount) }}</span>
        </div>
        <div class="cloud-info-item">
          <span>云信编号</span>
          <span>{{ data.cloudCode }}</span>
        </div>
        <div class="cloud-info-item">
          <span>开单企业</span>
          <span>{{ data.companyName }}</span>
        </div>
        <div class="cloud-info-item">
          <span>持单日期</span>
          <span>{{ data.startDate }}</span>
        </div>
        <div class="cloud-info-item">
          <span>承诺付款日</span>
          <span>{{ data.endDate }}</span>
        </div>
        <div class="cloud-info-item">
          <span>账期</span>
          <span>{{ data.termsDays < 0 ? 0 : data.termsDays }}天</span>
        </div>
        <div class="cloud-info-item">
          <span>融资付息模式</span>
          <span>{{
            data.financingModel == 0 ? '开单企业付息' : '融资用户付息'
          }}</span>
        </div>
        <div class="cloud-info-item">
          <span>融资年利率</span>
          <span>{{ data.financingApr }}%（日利率*360）</span>
        </div>
      </div>
      <div class="cloud-header-right">
        <div class="cloud-info-title">
          <span>云信信息</span>
          <span></span>
          <span>可融资金额: ￥{{ formatMoney(data.amount) }}</span>
          <MySvgIcon
            icon-class="icon-xinxi"
            style="fill: #8a94a6; font-size: 24px; margin-left: 6px"
          ></MySvgIcon>
        </div>
        <div class="cloud-financing-amout">
          <a-input-number
            prefix="￥"
            :min="0"
            :max="Number(data.amount)"
            v-model:value="cloudInputAmount"
            :controls="false"
            :formatter="
              value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            "
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
          />
        </div>

        <div class="cloud-financing-info">
          <div class="financing-left">
            需转让云信金额:
            <span class="finacing-subtitle"
              >¥{{ formatMoney(cloudAmount) }}</span
            >元
          </div>
          <div class="financing-right" @click="handleFreeDetail">
            查看费用试算
          </div>
        </div>
        <div class="cloud-financing-info">
          <div class="financing-left">
            融资到期日:
            <span class="finacing-subtitle">{{ data.endDate }}</span>
          </div>
        </div>
        <div class="cloud-financing-info">
          <div class="financing-left">
            融资期限:
            <span class="finacing-subtitle">{{ data.timeTerm }}</span
            >天
          </div>
        </div>
        <div class="cloud-financing-product">
          <div class="product-cont-title">
            <img :src="data.avatar" alt />
            <span>{{ data.goodsName }}</span>
          </div>
          <div class="product-cont-subtitle">
            该产品由【{{ data.capitalName }}】提供
          </div>
        </div>
      </div>
    </div>
    <div class="cloud-step1-main">
      <div class="main-item" @click="purposeLoan">
        <div class="main-item-left">融资用途</div>
        <div class="main-item-right">
          <span>{{ purposeLoanData.text || '请选择' }}</span>
          <MySvgIcon
            icon-class="icon-youjiantou"
            style="fill: #8a94a6; font-size: 28px; margin-left: 4px"
          ></MySvgIcon>
        </div>
      </div>
      <div class="main-item">
        <div class="main-item-left">收款账户</div>
        <div class="main-item-right">
          <span>中国人民银行宝安支行 8941</span>
          <MySvgIcon
            icon-class="icon-youjiantou"
            style="fill: #8a94a6; font-size: 28px; margin-left: 4px"
          ></MySvgIcon>
        </div>
      </div>
    </div>
    <div class="cloud-step1-footer">
      <div class="footer-title">
        <span>补充资料</span>
        <span>可增加融资成功率</span>
      </div>
      <div class="footer-upload">
        <div class="footer-upload-item">
          <div class="upload-title">合同</div>
          <div class="upload-subtitle">
            请上传合同原件的<span>彩色照片或扫描件</span>, 支持jpg/jpeg/png/pdf,
            最大20M
          </div>
          <div class="upload-container-box">
            <template v-if="contractList.length">
              <div
                class="upload-container-item"
                v-for="(item, index) in contractList"
                :key="item.id"
              >
                <MySvgIcon
                  class="delete"
                  icon-class="icon-delete-filling"
                  v-if="contractList.length > 1"
                  @click="deleteUploadData(index, contractList)"
                />
                <img
                  v-if="item.isPdf"
                  class="img-preview"
                  src="@/views/trade/pdf_default.png"
                />
                <div
                  v-else
                  class="img-preview"
                  :style="{
                    backgroundImage: item.url ? `url(${item.url})` : none,
                  }"
                />
                <div class="preview-wrapper">
                  <div class="preview-btn" @click="handleViewImg(item.url)">
                    <loading-outlined v-if="item.loading" />
                    <MySvgIcon
                      v-else
                      icon-class="icon-search"
                      style="font-size: 24px; fill: #fff; display: inline-flex"
                    />
                  </div>
                </div>
                <a-upload
                  class="upload-wrapper"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :fileList="[]"
                  :before-upload="
                    file => beforeUpload(file, item, contractList)
                  "
                >
                  <div class="upload-btn"><span>点击上传</span></div>
                </a-upload>
              </div>
            </template>
            <template v-else>
              <div class="upload-container-item">
                <div class="img-preview" style="backgroundimage: none" />
                <div class="preview-wrapper"></div>
                <a-upload
                  class="upload-wrapper"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :fileList="[]"
                  :before-upload="file => beforeUpload(file, {}, contractList)"
                >
                  <div class="upload-btn"><span>点击上传</span></div>
                </a-upload>
              </div>
            </template>
          </div>
        </div>

        <div class="upload-line"></div>
        <div class="footer-upload-item">
          <div class="upload-title">发票</div>
          <div class="upload-subtitle">
            请上传发票原件的<span>彩色照片或扫描件</span>, 支持jpg/jpeg/png/pdf,
            最大20M
          </div>
          <div class="upload-container-box">
            <template v-if="invoiceList.length">
              <div
                class="upload-invoice"
                v-for="item in invoiceList"
                :key="item.id"
              >
                <div class="upload-container-item">
                  <MySvgIcon
                    class="delete"
                    v-if="invoiceList.length > 1"
                    icon-class="icon-delete-filling"
                    @click="deleteUploadData(index, invoiceList)"
                  />
                  {{ item.url ? 1 : 2 }}
                  <img
                    v-if="item.isPdf"
                    class="img-preview"
                    src="@/views/trade/pdf_default.png"
                  />
                  <div
                    v-else
                    class="img-preview"
                    :style="{
                      backgroundImage: item.url ? `url(${item.url})` : 'none',
                    }"
                  />
                  <div class="preview-wrapper">
                    <div class="preview-btn" @click="handleViewImg(item.url)">
                      <loading-outlined v-if="item.loading" />
                      <MySvgIcon
                        v-else
                        icon-class="icon-search"
                        style="
                          font-size: 24px;
                          fill: #fff;
                          display: inline-flex;
                        "
                      />
                    </div>
                  </div>
                  <a-upload
                    class="upload-wrapper"
                    accept=".jpg,.jpeg,.png,.pdf"
                    :fileList="[]"
                    :before-upload="file => beforeInoviceUpload(file, item)"
                  >
                    <div class="upload-btn" :ref="setRef">
                      <span>点击上传</span>
                    </div>
                  </a-upload>
                </div>
                <div
                  v-if="item.isShow"
                  class="getInvoiceDetail"
                  @click="handleViewInvoiceDetail"
                >
                  查看发票信息
                </div>
              </div>
            </template>
            <template v-else>
              <div class="upload-container-item">
                <div class="img-preview" style="backgroundimage: none" />
                <div class="preview-wrapper"></div>
                <a-upload
                  class="upload-wrapper"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :fileList="[]"
                  :before-upload="file => beforeInoviceUpload(file, {})"
                >
                  <div class="upload-btn" ref="initBtnUpload">
                    <span>点击上传</span>
                  </div>
                </a-upload>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <PdfView ref="pdfView" />
    <div class="cloud-step1-btn">
      <a-button type="primary" @click="handleNextStep">下一步</a-button>
    </div>
  </div>

  <!-- 融资用途 -->
  <DialogPurposeLoan
    ref="dialogPurposeLoan"
    :purposeLoanList="purposeLoanList"
    :setpurposeLoanIndex="setpurposeLoanIndex"
    @setData="setPurposeLoanData"
  />
  <ReimburSement
    :coreCompanyId="data.coreCompanyId"
    :cloudInputAmount="cloudInputAmount"
    :id="data.goodsId"
    :startDate="data.startDate"
    :timeTerm="data.timeTerm"
    :financingApr="data.financingApr"
    ref="dialogRef"
  />

  <!-- 发票ocr识别 -->
  <InvoiceOcrDialog
    ref="ocrDialog"
    :invoiceType="invoiceType"
    @handleViewFile="handleViewImg(invoiceCurrent.url)"
    @handleReUploadInvoice="handleReUploadInvoice"
    @handleSaveInvoice="handleSaveInvoice"
  />

  <InvoiceDetailDialog
    ref="invoiceDetailDialog"
    :invoiceTypeMap="invoiceTypeMap"
    :invoiceData="invoiceDetailData"
  />
</template>

<script>
export default {}
</script>
<script setup>
import { reactive, ref, watch } from 'vue'
import { api as viewerApi } from 'v-viewer'
import PdfView from '@/components/FilePreview/index.vue'
import 'viewerjs/dist/viewer.css'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { formatMoney } from '@/utils/utils'
import DialogPurposeLoan from './Dialog/cloudPurposeLoan.vue'
import { requestDictMap, fileUploadAttach } from '@/api/common'
import { message } from 'ant-design-vue'
import ReimburSement from './Dialog/reimburSement.vue'
import InvoiceOcrDialog from './Dialog/FormItemInvoiceOcrDialog.vue'
import InvoiceDetailDialog from '@/views/trade/receivableAccount/FormItemInvoiceDetailDialog.vue'
import dayjs from 'dayjs'
const cloudInputAmount = ref(0)

const cloudAmount = ref(0)
let contractList = ref([]) // 合同列表
let invoiceList = ref([]) // 发票列表
const emit = defineEmits(['nextStep'])
const dialogPurposeLoan = ref(null) // 融资用途
const purposeLoanList = ref([])
const setpurposeLoanIndex = ref(undefined)
const purposeLoanData = ref({})
const pdfView = ref(null)
const dialogRef = ref(null)
const ocrDialog = ref(null) // ocr
const invoiceDetailDialog = ref(null)
const invoiceCurrent = ref({}) // 记录点击上传当前的数据

const refList = reactive([])
const invoiceDetailData = ref({}) // 查看详情
const invoiceType = ref([])
const invoiceTypeMap = ref({})
const initBtnUpload = ref(null) //

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})

watch(
  () => cloudInputAmount.value,
  val => {
    cloudAmount.value = Number(props.data.amount) - Number(val)
  },
  { immediate: true }
)

// 融资用途
const purposeLoan = () => {
  dialogPurposeLoan.value.handleOpen() // 打开弹窗
}

requestDictMap('finance_apply_loan_usage').then(res => {
  const resData = res.data
  if (resData.code == 200) {
    // 处理字典数据
    const resList = []
    for (const item of resData.data) {
      resList.push({
        key: item.dictKey,
        value: item.dictValue,
        id: item.id,
      })
    }
    purposeLoanList.value = resList
  }
})

// 设置融资用途数据
const setPurposeLoanData = val => {
  purposeLoanData.value = val
}
// 查看费用详情
const handleFreeDetail = () => {
  if (!cloudInputAmount.value) {
    message.error('请输入融资金额！')
    return
  }

  dialogRef.value.handleOpen()
}

const deleteUploadData = (index, list) => {
  list.splice(index, 1)
}

const handleViewImg = targetUrl => {
  if (targetUrl.includes('pdf')) {
    pdfView.value.handleOpen(targetUrl)
  } else {
    viewerApi({
      options: {
        toolbar: false,
        navbar: false,
        title: false,
      },
      images: [targetUrl],
    })
  }
}

// 重新上传
const handleReUploadInvoice = () => {
  const index = invoiceList.value.findIndex(
    item => item.id === invoiceCurrent.value.id
  )
  if (invoiceList.value.length) {
    refList[index + 1].click()
  } else {
    initBtnUpload.value.click()
  }
}

// 保存图片
const handleSaveInvoice = data => {
  invoiceDetailData.value = {
    ...data,
    formatOpeningDate: dayjs(data.openingDate).format('YYYY-MM-DD'),
    formatTotalAmount: data.totalAmount.replace('￥', '') + '元',
    formatAmount: data.amount.replace('￥', '') + '元',
  }
  invoiceCurrent.value.id = data.id
  invoiceCurrent.value.isShow = true
  invoiceList.value.push(invoiceCurrent.value)
}

const beforeUpload = (file, target, parentData) => {
  let passFlag = true

  // 检查文件格式
  const isAcceptFileTyle = [
    'image/jpg',
    'image/jpeg',
    'image/png',
    'application/pdf',
  ].includes(file.type)
  if (!isAcceptFileTyle) {
    message.error('不支持的文件格式! 请上传支持的图片格式或者pdf文件 ')
    passFlag = false
  }

  // 检查文件大小
  const isLimit20M = file.size / 1024 / 1024 < 20
  if (!isLimit20M) {
    message.error('图片或文件过大! 请上传小于20M的图片或文件')
    passFlag = false
  }

  const isPdf = file.type === 'application/pdf'

  if (passFlag) {
    // 上传文件
    target.loading = true

    const formData = new FormData()
    formData.append('file', file)

    fileUploadAttach(formData)
      .then(({ data }) => {
        target.loading = false
        if (data.success) {
          data = data.data
          message.success('上传成功')

          parentData.push({
            url: data.link,
            isPdf,
            attachId: data.attachId,
          })
        } else {
          message.success(data.msg || '上传失败12')
        }
      })
      .catch(() => {
        target.loading = false
        message.error('上传失败1!')
      })
  }

  return false
}
// 发票上传之前
const beforeInoviceUpload = (file, target) => {
  let passFlag = true

  // 检查文件格式
  const isAcceptFileTyle = [
    'image/jpg',
    'image/jpeg',
    'image/png',
    'application/pdf',
  ].includes(file.type)
  if (!isAcceptFileTyle) {
    message.error('不支持的文件格式! 请上传支持的图片格式或者pdf文件 ')
    passFlag = false
  }
  // 检查文件大小
  const isLimit20M = file.size / 1024 / 1024 < 20
  if (!isLimit20M) {
    message.error('图片或文件过大! 请上传小于20M的图片或文件')
    passFlag = false
  }

  const isPdf = file.type === 'application/pdf'

  if (passFlag) {
    // 上传文件
    target.loading = true

    const formData = new FormData()
    formData.append('file', file)

    fileUploadAttach(formData)
      .then(({ data }) => {
        target.loading = false
        if (data.success) {
          data = data.data
          invoiceCurrent.value = {
            url: data.link,
            id: data.attachId,
            isPdf,
            loading: false,
            isShow: false,
          }
          ocrDialog.value.handleOpen(data.link)
        } else {
          message.success(data.msg || '上传失败12')
        }
      })
      .catch(() => {
        target.loading = false
        message.error('上传失败1!')
      })
  }

  return false
}

// ref数组
const setRef = el => {
  if (el) {
    refList.push(el)
  }
}

const handleNextStep = () => {
  if (!purposeLoanData.value.id) return
  if (!contractList.value.length) {
    return
  }
  if (!invoiceList.value.length) return
  let contract = undefined
  for (const item of contractList.value) {
    if (contract === undefined) {
      contract = item.attachId
    } else {
      contract += `,${item.attachId}`
    }
  }

  let invoice = undefined
  for (const item of invoiceList.value) {
    if (invoice === undefined) {
      invoice = item.id
    } else {
      invoice += `,${item.id}`
    }
  }
  const params = {
    ...props.data,
    cloudInputAmount: cloudInputAmount.value, // 输入的金额
    cloudAmounts: cloudAmount.value, // 需转让金额
    financingPurpose: purposeLoanData.value.index,
    contract,
    invoice,
  }

  emit('nextStep', params)
}

// 查看发票信息
const handleViewInvoiceDetail = () => {
  invoiceDetailDialog.value.handleOpen()
}

requestDictMap('customer_invoice_type')
  .then(({ data }) => {
    data = data.data || []
    for (const item of data) {
      invoiceTypeMap.value[item.dictKey] = item.dictValue
    }
    invoiceType.value = data
  })
  .catch(() => {})
</script>
<style lang="scss" scoped>
.financing-demand {
  max-width: 1400px;
  position: relative;
  margin: auto;
  box-sizing: border-box;
  margin-top: 40px;

  .cloud-step1-header {
    display: flex;
    justify-content: space-between;
    .cloud-header-left {
      padding: 40px;
      width: 100%;
      margin-right: 40px;
      box-sizing: border-box;
      border: 1px solid #efefef;
      border-radius: 16px;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);

      .cloud-info-item {
        margin-top: 20px;
        display: flex;
        align-items: flex-start;
        & span:nth-of-type(1) {
          width: 120px;
          display: block;
          color: #8a94a6;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          text-align: left;
        }
        & span:nth-of-type(2) {
          display: block;
          color: #0a1f44;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
    .cloud-header-right {
      padding: 40px;
      width: 100%;
      box-sizing: border-box;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      border-radius: 16px;
      border: 1px solid #efefef;
      position: relative;
      &::before {
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        content: '';
        width: 105px;
        height: 145px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.2;
        filter: blur(24px);
        z-index: 1;
      }
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        display: block;
        content: '';
        width: 219px;
        height: 314px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.2;
        filter: blur(24px);
      }
      .cloud-financing-amout {
        position: relative;
        box-sizing: border-box;
        margin-top: 4px;
        margin-bottom: 24px;
        z-index: 2;
        :deep() {
          .ant-input-number-affix-wrapper {
            width: 100%;
            border-radius: unset;
            padding: 20px 0;
            border: none;
            outline: none !important;
            border-bottom: 1px solid #182c4f;
            background-color: transparent;
          }
          .ant-input-number-affix-wrapper:focus,
          .ant-input-number-affix-wrapper-focused {
            box-shadow: unset !important;
          }
          .ant-input-number-prefix {
            color: #031222;
            font-size: 40px;
            line-height: 49px;
            font-weight: bold;
            font-family: CoreSansD45Medium;
          }
          .ant-input-number {
            background-color: unset;
          }
          .ant-input-number-input-wrap,
          .ant-input-number-input {
            color: #031222;
            font-size: 64px;
            font-weight: bold;
            font-family: CoreSansD65Heavy;
            height: 79px;
            line-height: 79px;
            background-color: unset;
            background-color: transparent;
          }
        }
      }
      .cloud-financing-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        .financing-left {
          color: #8a94a6;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          .finacing-subtitle {
            color: #031222;
            padding: 0 6px;
            font-weight: bold;
          }
        }
        .financing-right {
          position: relative;
          z-index: 99;
          cursor: pointer;
          color: #0d55cf;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          &:hover {
            text-decoration: underline;
          }
        }
      }

      .cloud-financing-product {
        display: flex;
        align-items: center;
        .product-cont-title {
          display: flex;
          align-items: center;
          margin-right: 12px;
          & img {
            width: 24px;
            height: 24px;
            object-fit: cover;
            margin-right: 4px;
          }
          & span {
            font-size: 20px;
            font-weight: bold;
            color: #0a1f44;
            line-height: 28px;
          }
        }
        .product-cont-subtitle {
          padding: 0 12px;
          border-left: 1px solid #b5bbc6;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          color: #53627c;
        }
      }
    }
    .cloud-info-title {
      display: flex;
      align-items: center;
      & span:nth-of-type(1) {
        display: block;
        color: #0a1f44;
        font-size: 24px;
        font-weight: 500;
        line-height: 32px;
      }
      & span:nth-of-type(2) {
        display: block;
        height: 22px;
        width: 1px;
        background-color: #b5bbc6;
        margin: 0 12px;
      }
      & span:nth-of-type(3) {
        display: block;
        color: #8a94a6;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }
    }
  }
  .cloud-step1-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .main-item {
      cursor: pointer;
      padding: 40px;
      width: 100%;
      height: 112px;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      border-radius: 16px;
      border: 1px solid #efefef;
      box-sizing: border-box;
      margin: 40px auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:first-child {
        margin-right: 40px;
      }
      .main-item-left {
        color: #0a1f44;
        font-size: 24px;
        font-weight: 500;
        line-height: 32px;
      }
      .main-item-right {
        display: flex;
        align-items: center;
        & span {
          display: block;
          line-height: 32px;
          color: #8a94a6;
          font-size: 24px;
          font-weight: 500;
        }
      }
    }
  }
  .cloud-step1-footer {
    padding: 40px;
    box-sizing: border-box;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 1px solid #efefef;
    margin-bottom: 40px;
    .footer-title {
      display: flex;
      align-items: center;
      margin-bottom: 40px;
      & span:nth-of-type(1) {
        display: block;
        font-size: 24px;
        line-height: 32px;
        color: #0a1f44;
        font-weight: 500;
        margin-right: 12px;
      }
      & span:nth-of-type(2) {
        display: block;
        padding-left: 12px;
        border-left: 1px solid #b5bbc6;
        font-size: 16px;
        line-height: 24px;
        color: #8a94a6;
        font-weight: 500;
      }
    }
    .footer-upload {
      .footer-upload-item {
        .upload-title {
          font-size: 16px;
          font-weight: bold;
          line-height: 24px;
          color: #0a1f44;
          margin-bottom: 8px;
        }
        .upload-subtitle {
          font-size: 14px;
          font-weight: 400;
          color: #53627c;
          line-height: 20px;
          margin-bottom: 24px;
          & span {
            color: #007fff;
          }
        }
        .upload-container-box {
          position: relative;
          display: flex;
          flex-wrap: wrap;
          overflow: hidden;

          .upload-container-item {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 208px;
            height: 208px;
            border-radius: 8px;
            border: 1px solid #efefef;
            overflow: hidden;
            margin-right: 20px;
            margin-top: 20px;

            &:last-child {
              margin-right: 0;
            }

            .delete {
              position: absolute;
              top: 4px;
              right: 4px;
              font-size: 20px;
              fill: rgba(24, 44, 79, 0.5);
              cursor: pointer;
              outline: none;
              z-index: 2;
              backdrop-filter: saturate(90%) blur(6px);
              -webkit-backdrop-filter: saturate(90%) blur(6px);
              transition: all 0.3s;

              &:hover {
                fill: rgba(24, 44, 79, 0.6);
              }
            }

            .img-preview {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-repeat: no-repeat;
              background-position: center;
              background-size: cover;
              z-index: 1;
            }

            .preview-wrapper {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;
              height: 100%;
              background-color: #fff;
            }

            .upload-wrapper {
              height: 48px;
              flex-shrink: 0;
              cursor: pointer;
              z-index: 2;

              :deep(.ant-upload-select) {
                width: 100%;
                height: 100%;
              }

              .upload-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                text-align: center;
                background: rgba(24, 44, 79, 0.5);
                border-radius: 0px 0px 8px 8px;
                backdrop-filter: saturate(90%) blur(6px);
                -webkit-backdrop-filter: saturate(90%) blur(6px);
                transition: all 0.3s;

                &:hover {
                  background: rgba(24, 44, 79, 0.6);
                }

                & > span {
                  font-size: 16px;
                  @include family-PingFangSC-Semibold;
                  font-weight: 600;
                  color: #ffffff;
                }
              }
            }

            .preview-btn {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 48px;
              height: 48px;
              background: rgba(24, 44, 79, 0.5);
              backdrop-filter: saturate(90%) blur(6px);
              -webkit-backdrop-filter: saturate(90%) blur(6px);
              border-radius: 50%;
              overflow: hidden;
              cursor: pointer;
              z-index: 2;
              transition: all 0.3s;

              &:hover {
                background: rgba(24, 44, 79, 0.6);
              }

              span {
                font-size: 16px;
                @include family-PingFangSC-Semibold;
                font-weight: 600;
                color: #ffffff;
                line-height: 24px;
              }
            }
          }
          .getInvoiceDetail {
            width: 152px;
            height: 48px;
            line-height: 48px;
            border-radius: 4px;
            border: 1px solid #0c66ff;
            box-sizing: border-box;
            text-align: center;
            color: #0d55cf;
            font-size: 16px;
            font-weight: 500;
            margin-left: 28px;
            cursor: pointer;
            margin-top: 8px;
          }
        }
      }
      .upload-line {
        margin: 32px auto;
        width: 100%;
        border-top: 1px dashed #e1e4e8;
      }
    }
  }

  .cloud-step1-btn {
    margin: 0 auto 40px;
    text-align: center;
    :deep() {
      .ant-btn {
        width: 400px;
        height: 48px;
        border-radius: 24px;
      }
    }
  }
}
</style>
