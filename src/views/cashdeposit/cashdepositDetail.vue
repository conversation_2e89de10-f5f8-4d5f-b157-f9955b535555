<template>
  <div class="cashdeposit-detail">
    <!-- 头部详情信息 -->
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <h1 class="title-box">保证金详情</h1>
            <div class="margin-number">
              <span>保证金编号：</span>
              <span>
                {{ headerData.cashDepositNo }}
              </span>
            </div>
          </div>
        </avue-affix>
        <div class="apply-container">
          <div class="left">
            <div class="form-item">
              <span class="title">融资编号：</span>
              <span class="value" style="color: rgba(105, 124, 255, 100)">
                {{ headerData.financingNo }}
              </span>
            </div>
            <div class="form-item">
              <span class="title">融资用户：</span>
              <span class="value">
                <span style="color: rgba(105, 124, 255, 100)">
                  {{ headerData.customerName }}
                </span>
              </span>
            </div>
            <div class="form-item">
              <span class="title">创建时间：</span>
              <span class="value">
                {{ headerData.createTime }}
              </span>
            </div>
          </div>
          <div class="right">
            <div class="right-icon-box">
              <svg-icon
                :icon-class="headerStatusArr[headerStatusIndex].iconCass"
                :style="{ fill: headerStatusArr[headerStatusIndex].iconColor }"
                style="font-size: 40px"
              />
              <span class="status">
                {{ headerStatusArr[headerStatusIndex].text }}
              </span>
            </div>
          </div>
        </div>
      </template>
    </basic-container>
    <!-- 金额信息 -->
    <div class="amount-detail">
      <div class="amount-for-box" v-for="item in amountArr" :key="item.id">
        <div>{{ item.value | formatMoney }}</div>
        <div>{{ item.label }}</div>
      </div>
    </div>
    <!-- 缴纳明细 -->
    <basic-container v-if="tableData.length">
      <div class="payment-details">
        <h1 class="title-box">缴纳明细</h1>
        <div class="descriptions-for-box">
          <el-descriptions title="" :column="3" border>
            <el-descriptions-item
              v-for="item in tableData"
              :key="item.id"
              :label="item.label"
            >
              <template v-if="item.label === '保证金类型'">
                <span class="radius-box">
                  {{ item.value }}
                </span>
              </template>
              <template v-else-if="item.label === '支付方式'">
                <span class="radius-box">
                  {{ item.value }}
                </span>
              </template>
              <template v-else-if="item.label === '应缴金额(元)'">
                <div style="text-align: right">
                  ¥{{ item.value | formatMoney }}
                </div>
              </template>
              <template v-else-if="item.label === '实缴金额(元)'">
                <div style="color: #ff2929; text-align: right">
                  ¥{{ item.value | formatMoney }}
                </div>
              </template>
              <template v-else-if="item.label === '支付凭证'">
                <div class="look-btn-menu">
                  <span @click="handleFilePreviewOnceAgain('img')">
                    查看图片
                  </span>
                  <template v-if="pdfArr.length === 1">
                    <span @click="handleFilePreviewOnceAgain(0)">
                      查看附件
                    </span>
                  </template>
                  <template v-else>
                    <span
                      v-for="(item, index) in pdfArr"
                      :key="index"
                      @click="handleFilePreviewOnceAgain(index)"
                    >
                      查看附件{{ index + 1 }}
                    </span>
                  </template>
                </div>
              </template>
              <template v-else>
                {{ item.value }}
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </basic-container>
    <!-- 保证金账单 -->
    <basic-container v-if="!loading" style="margin-bottom: 100px">
      <div class="margin-statement">
        <h1 class="title-box">保证金账单</h1>
        <!-- avue -->
        <avue-crud
          ref="crud"
          :option="option"
          :table-loading="loading"
          :data="avueData"
          @on-load="onLoad"
        >
          <template slot="payRefundType" slot-scope="{ row }">
            <span class="radius-box">
              {{ row.payRefundTypeStr }}
            </span>
          </template>
          <template slot="amount" slot-scope="{ row }">
            <span v-if="row.payRefundType === 0" style="color: #ff2929">
              -{{ row.amount | formatMoney }}
            </span>
            <span v-else style="color: #1fc374">
              +{{ row.amount | formatMoney }}
            </span>
          </template>
          <template slot="cashDepositBillType" slot-scope="{ row }">
            <span class="radius-box">
              {{ row.cashDepositBillTypeStr }}
            </span>
          </template>
        </avue-crud>
      </div>
    </basic-container>
    <!-- 操作menu -->
    <div class="footer-container">
      <span class="backBtn" @click="backBtnFun">返 回</span>
    </div>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import {
  cashdepositDetail,
  getCashDepositGeneralDetailsVO,
  getCashDepositBilList,
} from '@/api/cashdeposit/cashdeposit'
import { getDictionary } from '@/api/system/dictbiz'
import FilePreview from '@/components/file-preview'

export default {
  name: 'cashdepositDetail',
  components: { FilePreview },
  data() {
    return {
      ids: this.$route.query.id,
      waiting: true,
      headerStatusIndex: 0,
      headerStatusArr: [
        {
          iconCass: 'icon-dengdai1',
          iconColor: '#4e9bfc',
          text: '待缴纳',
        },
        {
          iconCass: 'icon-chenggong1',
          iconColor: '#1FC374',
          text: '担保中',
        },
        {
          iconCass: 'icon-chenggong1',
          iconColor: '#1FC374',
          text: '已释放',
        },
        {
          iconCass: 'icon-shibai',
          iconColor: '#A6A3A3',
          text: '已关闭',
        },
      ],
      amountArr: [],
      tableData: [],
      pdfSrc: '',
      pdfArr: [],
      imageArr: [],
      avueData: [],
      loading: true,
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        index: true,
        menu: false,
        refreshBtn: false,
        columnBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        align: 'center',
        dialogClickModal: false,
        column: [],
      },
      headerData: {},
    }
  },
  created() {
    // avueCard表格数据
    const avueCarddata = [
      {
        label: '时间',
        prop: 'createTime',
        width: 150,
      },
      {
        label: '交易流水号',
        prop: 'transactionNo',
        width: 140,
      },
      {
        label: '缴/退类型',
        prop: 'payRefundType',
        width: 90,
      },
      {
        label: '金额(元)',
        prop: 'amount',
        align: 'right',
        width: 170,
      },
      {
        label: '保证金类型',
        prop: 'cashDepositBillType',
        width: 120,
      },
      {
        label: '付款公司',
        prop: 'payCompanyName',
        width: 220,
      },
      {
        label: '开户行',
        prop: 'payCompanyBankName',
        width: 220,
      },
      {
        label: '付款账号',
        prop: 'payCompanyAccount',
        width: 220,
      },
      {
        label: '收款公司',
        prop: 'proceedsCompanyName',
        width: 220,
      },
      {
        label: '开户行',
        prop: 'proceedsCompanyBankName',
        width: 220,
      },
      {
        label: '收款账号',
        prop: 'proceedsCompanyAccount',
        width: 220,
      },
      {
        label: '备注',
        prop: 'remark',
        width: 220,
      },
    ]
    this.option.column = avueCarddata
    this.cashdepositDetailFun(this.ids)
  },
  methods: {
    onLoad() {},
    // 详情接口
    cashdepositDetailFun(id) {
      getDictionary({
        code: 'goods_bond_pay_type',
      }).then(({ data }) => {
        if (data.success) {
          const { data: resData2 } = data
          const arr2 = []
          for (const item of resData2) {
            arr2.push({
              id: item.id,
              name: item.dictValue,
              key: item.dictKey,
            })
          }
          getDictionary({
            code: 'goods_bond_release_mode',
          }).then(({ data }) => {
            if (data.success) {
              const { data: resData1 } = data
              const arr1 = []
              for (const item of resData1) {
                arr1.push({
                  id: item.id,
                  name: item.dictValue,
                  key: item.dictKey,
                })
              }
              //
              cashdepositDetail(id).then(({ data }) => {
                if (data.success) {
                  const { data: resData } = data
                  this.headerStatusIndex = resData.status - 1 // 当前状态
                  // 头部信息
                  const headerDataRoot = {
                    financingNo: resData.financingNo, // 融资编号
                    customerName: resData.customerName, // 融资用户
                    createTime: resData.createTime, // 创建时间
                    cashDepositNo: resData.cashDepositNo, // 保证金编号
                  }
                  this.headerData = headerDataRoot
                  // 详情信息
                  const data1 = [
                    {
                      id: 1,
                      label: '保证金类型',
                      value:
                        resData.cashDepositType === 1
                          ? '初始保证金'
                          : '追加保证金',
                    },
                    {
                      id: 2,
                      label: '缴纳比例',
                      value: `${resData.cashDepositRate}%`,
                    },
                    {
                      id: 3,
                      label: '释放方式',
                      value: arr1.length
                        ? arr1.filter(item => item.key == resData.refundType)[0]
                            .name
                        : '..',
                    },
                    {
                      id: 4,
                      label: '应缴金额(元)',
                      value: resData.payableAmount,
                    },
                    {
                      id: 5,
                      label: '支付方式',
                      value: arr2.length
                        ? arr2.filter(item => item.key == resData.payType)[0]
                            .name
                        : '..',
                    },
                  ]
                  if ([1, 4].includes(resData.status)) {
                    this.tableData = data1
                    this.waiting = false
                    return
                  }
                  const data2 = [
                    {
                      id: 6,
                      label: '支付流水号',
                      value: resData.paymentSerialNumber,
                    },
                    {
                      id: 7,
                      label: '实缴金额(元)',
                      value: resData.payedAmount,
                    },
                    {
                      id: 8,
                      label: '支付时间',
                      value: '2021-12-02 12:34:20',
                    },
                    {
                      id: 9,
                      label: '开户行',
                      value: resData.bankName,
                    },
                    {
                      id: 10,
                      label: '开户账号',
                      value: resData.bankAccount,
                    },
                    {
                      id: 11,
                      label: '支付凭证',
                      value: '查看',
                    },
                  ]
                  this.tableData = data1.concat(data2)
                  // 获取保证金金额信息
                  this.getCashDepositGeneralDetailsVOFun(resData.financingNo)
                  // 查询所有保证金账单
                  this.getCashDepositBilListFun(this.ids)
                  // 获取支付凭证
                  this.payOrder(resData.attachPaymentList)
                }
              })
            }
          })
        }
      })
    },
    // 获取保证金金额信息
    getCashDepositGeneralDetailsVOFun(id) {
      getCashDepositGeneralDetailsVO(id).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const amountArrRoot = [
            {
              id: 1,
              label: '缴纳金额（元）',
              value: resData.payableAmount,
            },
            {
              id: 2,
              label: '保证金余额（元）',
              value: resData.balance,
            },
            {
              id: 3,
              label: '退款金额（元）',
              value: resData.refundAmount,
            },
          ]
          this.amountArr = amountArrRoot
          this.waiting = false
        }
      })
    },
    // 查询所有保证金账单
    getCashDepositBilListFun(id) {
      getCashDepositBilList(id).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const billistRoot = []
          for (const item of resData) {
            const dataRoot = {
              ...item,
            }
            if (item.payRefundType === 1) {
              dataRoot.payRefundTypeStr = '缴纳'
            } else {
              dataRoot.payRefundTypeStr = '退还'
            }
            if (item.cashDepositBillType === 1) {
              dataRoot.cashDepositBillTypeStr = '履约保证金'
            } else {
              dataRoot.cashDepositBillTypeStr = '..'
            }
            billistRoot.push({
              ...dataRoot,
            })
          }
          this.avueData = billistRoot
          this.loading = false
        }
      })
    },
    // 获取支付凭证
    payOrder(arr) {
      for (const item of arr) {
        if (item.link.endsWith('.pdf')) {
          this.pdfArr.push({
            url: item.link,
          })
        } else {
          this.imageArr.push({
            url: item.link,
          })
        }
      }
    },
    // 返回Fun
    backBtnFun() {
      this.$router.$avueRouter.closeTag()
      this.$router.push('/cashdeposit/cashdeposit')
    },
    // 查看支付凭证
    handleFilePreviewOnceAgain(type) {
      if (type === 'img') {
        this.$ImagePreview(this.imageArr, 0, {
          closeOnClickModal: true,
        })
      } else {
        this.pdfSrc =
          this.pdfArr[type].url + '?time=' + new Date().getMilliseconds()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.cashdeposit-detail {
  .header {
    width: 100%;
    // height: 50px;
    background: #fff;
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    // padding: 0 10px 10px 0;

    .title-box {
      margin: 0 0 12px;
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-family: SourceHanSansSC-regular;
    }

    .margin-number {
      height: 21px;
      font-size: 14px;
      font-family: SourceHanSansSC-regular;

      & span:first-child {
        color: rgba(125, 125, 125, 100);
      }
      & span:last-child {
        color: #101010;
      }
    }
  }

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 157px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        font-weight: bold;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .status {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-family: SourceHanSansSC-bold;
        font-weight: bold;
      }
    }
  }

  .amount-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 6px;

    .amount-for-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: calc((100% - 24px) / 3);
      height: 100px;
      line-height: 20px;
      border-radius: 6px;
      background-color: #fff;
      margin-right: 24px;
      cursor: context-menu;

      &:last-child {
        margin-right: 0;
      }

      & div:first-child {
        height: 23px;
        color: rgba(18, 119, 255, 100);
        font-size: 20px;
        text-align: center;
        font-family: SourceHanSansSC-bold;
        font-weight: bold;
        margin-bottom: 12px;
      }

      & div:last-child {
        height: 20px;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
        text-align: center;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .payment-details {
    .title-box {
      margin: 0 0 12px;
      width: 64px;
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-family: SourceHanSansSC-regular;
    }

    .descriptions-for-box {
      margin-top: 15px;
      position: relative;
      z-index: 1;

      .radius-box {
        border-radius: 43px;
        background-color: #eaecf1;
        color: rgba(0, 7, 42, 100);
        font-size: 12px;
        text-align: center;
        font-family: Microsoft Yahei;
        padding: 4px 8px;
        box-sizing: border-box;
      }

      .look-btn-menu {
        & span {
          height: 21px;
          color: rgba(105, 124, 255, 100);
          font-size: 14px;
          font-family: SourceHanSansSC-regular;
          cursor: pointer;
          margin-right: 8px;
        }

        &:last-child {
          margin-right: 0;
        }
      }

      ::v-deep {
        .el-descriptions-item__label.is-bordered-label {
          width: 13.5%;
          height: 48px;
          line-height: 20px;
          background-color: rgba(247, 247, 247, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          text-align: left;
          color: #000;
          padding-left: 15px;
        }

        .el-descriptions-item__content {
          width: 360px;
          height: 48px;
          line-height: 20px;
          background-color: rgba(255, 255, 255, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          text-align: left;
          color: #000;
          padding-left: 15px;
        }
      }
    }
  }

  .margin-statement {
    .title-box {
      margin: 0 0 12px;
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-family: SourceHanSansSC-regular;
    }

    .radius-box {
      border-radius: 43px;
      background-color: #eaecf1;
      color: rgba(0, 7, 42, 100);
      font-size: 12px;
      text-align: center;
      font-family: Microsoft Yahei;
      padding: 4px 8px;
      box-sizing: border-box;
    }

    // 覆盖avue样式
    ::v-deep {
      .avue-crud__menu {
        display: none;
      }

      .el-table th.el-table__cell > .cell {
        text-align: center;
      }
    }
  }

  .footer-container {
    position: fixed;
    z-index: 3;
    height: 68px;
    width: calc(100% - 278px);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    bottom: 0;
    right: 21px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    .backBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      border: 1px solid rgba(187, 187, 187, 100);
      margin-right: 18px;
      cursor: pointer;
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      background-color: rgba(18, 119, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }
  }
}
</style>
