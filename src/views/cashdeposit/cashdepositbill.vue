<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.cashdepositbill_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/cashdeposit/cashdepositbill";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "交易流水号",
              prop: "transactionNo",
              rules: [{
                required: true,
                message: "请输入交易流水号",
                trigger: "blur"
              }]
            },
            {
              label: "缴/退类型，0-退款，1-缴费",
              prop: "payRefundType",
              rules: [{
                required: true,
                message: "请输入缴/退类型，0-退款，1-缴费",
                trigger: "blur"
              }]
            },
            {
              label: "金额，缴纳/退款金额",
              prop: "amount",
              rules: [{
                required: true,
                message: "请输入金额，缴纳/退款金额",
                trigger: "blur"
              }]
            },
            {
              label: "保证金类型，1-履约保证金",
              prop: "cashDepositBillType",
              rules: [{
                required: true,
                message: "请输入保证金类型，1-履约保证金",
                trigger: "blur"
              }]
            },
            {
              label: "付款公司id",
              prop: "payCompanyId",
              rules: [{
                required: true,
                message: "请输入付款公司id",
                trigger: "blur"
              }]
            },
            {
              label: "付款公司的开户行id",
              prop: "payCompanyBankId",
              rules: [{
                required: true,
                message: "请输入付款公司的开户行id",
                trigger: "blur"
              }]
            },
            {
              label: "付款公司的账号",
              prop: "payCompanyAccount",
              rules: [{
                required: true,
                message: "请输入付款公司的账号",
                trigger: "blur"
              }]
            },
            {
              label: "收款公司id",
              prop: "proceedsCompanyId",
              rules: [{
                required: true,
                message: "请输入收款公司id",
                trigger: "blur"
              }]
            },
            {
              label: "收款公司的开户行id",
              prop: "proceedsCompanyBankId",
              rules: [{
                required: true,
                message: "请输入收款公司的开户行id",
                trigger: "blur"
              }]
            },
            {
              label: "收款公司的账号",
              prop: "proceedsCompanyAccount",
              rules: [{
                required: true,
                message: "请输入收款公司的账号",
                trigger: "blur"
              }]
            },
            {
              label: "备注",
              prop: "remark",
              rules: [{
                required: true,
                message: "请输入备注",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.cashdepositbill_add, false),
          viewBtn: this.vaildData(this.permission.cashdepositbill_view, false),
          delBtn: this.vaildData(this.permission.cashdepositbill_delete, false),
          editBtn: this.vaildData(this.permission.cashdepositbill_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
