<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <!-- #4684 -->
        <!-- <el-button type="primary" size="small" icon="el-icon-plus" @click="addTo"> 追加 </el-button> -->
      </template>

      <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="status" @change="handleTabButton">
            <el-radio-button v-for="(item, key) in statusMap" :key="key" :label="key">{{ item }}</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button type="text" @click="deleteLock(row.id)"> 详情 </el-button>
      </template>

      <template slot="status" slot-scope="{ row }">
        <el-tag type="info" v-if="row.status === 1">待缴纳</el-tag>
        <el-tag type="success" v-if="row.status === 2">担保中</el-tag>
        <el-tag type="success" v-if="row.status === 3">已释放</el-tag>
        <el-tag type="success" v-if="row.status === 4">已关闭</el-tag>
        <el-tag type="success" v-if="row.status === 5">货物已处置</el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/cashdeposit/cashdeposit'
import { mapGetters } from 'vuex'
import { getDictionary } from '@/api/system/dictbiz'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      statusMap: {},
      status: '0',
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '保证金编号',
            prop: 'cashDepositNo',
            rules: [
              {
                required: true,
                message: '请输入保证金编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资编号',
            prop: 'financingNo',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '保证金类型',
            prop: 'cashDepositType',
            dicData: [
              {
                label: '初始保证金',
                value: 1,
              },
              {
                label: '追加保证金',
                value: 2,
              },
            ],
          },
          {
            label: '融资用户',
            prop: 'customerName',
            rules: [
              {
                required: true,
                message: '请输入融资用户id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '应缴金额（元）',
            prop: 'payableAmount',
            rules: [
              {
                required: true,
                message: '请输入应缴金额（元），追加应缴金额（元）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '实缴金额（元）',
            prop: 'payedAmount',
            rules: [
              {
                required: true,
                message: '请输入实缴金额（元），已缴保证金（元）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '退款金额（元）',
            prop: 'refundAmount',
            rules: [
              {
                required: true,
                message: '请输入退款金额（元）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '当前余额（元）',
            prop: 'balance',
            rules: [
              {
                required: true,
                message: '请输入当前余额（元）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '创建时间',
            prop: 'createTime',
            rules: [
              {
                required: true,
                message: '请输入创建时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '创建时间',
            prop: 'createTimeRange',
            type: 'datetime',
            format: 'yyyy-MM-dd hh:mm:ss',
            valueFormat: 'yyyy-MM-dd hh:mm:ss',
            searchRange: true,
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入创建时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资用户',
            prop: 'customerId',
            search: true,
            type: 'tree',
            dicUrl: '/api/blade-customer/web-back/customer/frontfinancinglist/all',
            dictMethod: 'get',
            props: {
              label: 'name',
              value: 'companyId',
            },
            hide: true,
            display: false,
          },
          {
            label: '费用单号',
            prop: 'expenseNo',
            hide: true,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入费用单号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '支付方式',
            prop: 'payType',
            hide: true,
            dicData: [
              {
                label: '线下',
                value: 1,
              },
              {
                label: '银联',
                value: 2,
              },
              {
                label: '微信',
                value: 3,
              },
              {
                label: '支付宝',
                value: 4,
              },
            ],
            rules: [
              {
                required: true,
                message: '请输入支付方式，1-线下，2-银联，3-微信，4-支付宝',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '支付方式',
            prop: 'paymentMethod',
            search: true,
            display: false,
            hide: true,
            type: 'select',
            dataType: 'number',
            searchFilterable: true,
            searchMultiple: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=bill_pay',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [
              {
                required: true,
                message: '请输入支付方式;',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资金额',
            prop: 'financingAmount',
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入融资金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '追加原因',
            prop: 'appendReason',
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入追加原因',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '追加实缴金额（元）',
            prop: 'appendPayedAmount',
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入追加实缴金额（元）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '开户行',
            hide: true,
            prop: 'bankId',
            rules: [
              {
                required: true,
                message: '请输入开户行',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '开户账号',
            prop: 'bankAccount',
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入开户账号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '缴纳凭证',
            hide: true,
            prop: 'payedVoucher',
            rules: [
              {
                required: true,
                message: '请输入缴纳凭证',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '保证金比例（%）',
            prop: 'cashDepositRate',
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入保证金比例（%），追加保证金比例（%）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '退款方式',
            prop: 'refundType',
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入退款方式，1-同还款比例等比释放',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '支付节点',
            hide: true,
            prop: 'payNode',
            rules: [
              {
                required: true,
                message: '请输入支付节点，1-放款申请',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',

            dicUrl: '/api/blade-system/dict-biz/dictionary?code=cashde_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.cashdeposit_add, false),
        viewBtn: this.vaildData(this.permission.cashdeposit_view, false),
        delBtn: this.vaildData(this.permission.cashdeposit_delete, false),
        editBtn: this.vaildData(this.permission.cashdeposit_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },

  methods: {
    addTo() {
      console.log('1', 1)
    },
    deleteLock(id) {
      this.$router.push(`/cashdeposit/cashdepositDetail?id=${id}`)
    },
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      const { createTimeRange } = this.query

      if (this.status !== '0') {
        this.query.status = this.status
      } else {
        this.query = {}
      }
      let values = {
        ...params,
      }
      if (createTimeRange) {
        values = {
          ...params,
          create_time_datege: createTimeRange[0],
          create_time_datelt: createTimeRange[1],
          ...this.query,
        }
        values.createTimeRange = null
      }

      this.loading = true
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query, values)).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },

  created() {
    //页面初始化调用字典接口获取支付状态数据
    getDictionary({ code: 'cashde_status' }).then(resp => {
      let data = resp.data.data
      this.statusMap[0] = '全部'
      data.forEach(status1 => {
        this.statusMap[status1.dictKey] = status1.dictValue
      })
    })
  },
}
</script>

<style></style>
