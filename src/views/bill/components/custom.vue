<template>
  <div class="invoice-details-container">
    <div class="invoice-apply-detail">
      <div class="invoice-apply-head">
        <span>申请信息</span>
        <div class="invoice-apply-head-right">
          <span>发票编号: </span>
          <span>{{ detailList.invoiceNo }}</span>
        </div>
      </div>
      <div class="invoice-apply-box">
        <div class="invoice-apply-left">
          <div class="apply-item">
            <span>融资编号：</span>
            <span class="apply-item-blue">{{ detailList.financeNo }}</span>
          </div>
          <div class="apply-item">
            <span>费用编号：</span>
            <span class="apply-item-blue">{{ detailList.billExpenseNo }}</span>
          </div>
          <div class="apply-item">
            <span>申请人：</span>
            <span class="apply-item-blue">{{ detailList.customerName }}</span>
          </div>
          <div class="apply-item">
            <span>发票金额：</span>
            <span class="apply-item-basic">{{ detailList.amount }}</span>
          </div>
          <div class="apply-item">
            <span>申请时间：</span>
            <span class="apply-item-basic">{{ detailList.createTime }}</span>
          </div>
        </div>
        <div class="invoice-apply-right">
          <SvgIcon
            :icon-class="getInoviceIcon(detailList.deliveryStatus)"
            class="invoice-apply-right-icon"
            :style="getInoviceIconColor(detailList.deliveryStatus)"
          ></SvgIcon>
          <span class="invoice-apply-status-name">{{
            detailList.deliveryStatusName
          }}</span>
          <span
            v-if="detailList.deliveryStatus == 1"
            class="invoice-apply-status-desc"
            >用户申请开具发票,请尽快处理</span
          >
          <span
            v-else-if="detailList.deliveryStatus == 2"
            class="invoice-apply-status-desc"
            >发票已开具并邮寄,等待用户收票确认</span
          >
          <span
            v-else-if="detailList.deliveryStatus == 3"
            class="invoice-apply-status-desc"
            >发票已开具并邮寄,等待用户收件确认</span
          >
          <span
            v-else-if="detailList.deliveryStatus == 4"
            class="invoice-apply-status-desc"
            >用户已收到发票</span
          >
          <span
            v-else-if="detailList.deliveryStatus == 5"
            class="invoice-apply-status-desc"
            >用户已取消开票申请</span
          >
        </div>
      </div>
    </div>

    <div class="invoice-apply-detail">
      <el-descriptions title="发票抬头" :column="3" border>
        <el-descriptions-item label="单位名称">{{
          detailList.billInvoiceDetail.companyName
        }}</el-descriptions-item>
        <el-descriptions-item label="单位税号">{{
          detailList.billInvoiceDetail.unitTax
        }}</el-descriptions-item>
        <el-descriptions-item label="注册地址">{{
          detailList.billInvoiceDetail.registeredAddress
        }}</el-descriptions-item>
        <el-descriptions-item label="单位号码">{{
          detailList.billInvoiceDetail.unitNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="开户银行">{{
          detailList.billInvoiceDetail.depositaryBank
        }}</el-descriptions-item>
        <el-descriptions-item label="银行账户">{{
          detailList.billInvoiceDetail.bankAccount
        }}</el-descriptions-item>
        <el-descriptions-item v-if="sendType === 2" label="邮箱">{{
          detailList.billInvoiceDetail.email
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="invoice-apply-detail" v-if="sendType === 1">
      <el-descriptions title="邮寄地址" :column="3" border>
        <el-descriptions-item label="收件人">{{
          detailList.billInvoiceDetail.receiveName
        }}</el-descriptions-item>
        <el-descriptions-item label="联系号码">{{
          detailList.billInvoiceDetail.receiveNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="收件地址">{{
          detailList.billInvoiceDetail.receiveAddress
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="invoice-apply-detail" v-if="isShowGoodsDetil && sendType === 1">
      <el-descriptions title="物流信息" :column="3" border>
        <el-descriptions-item label="物流公司">{{
          detailList.billInvoiceDetail.logisticsCompanies
        }}</el-descriptions-item>
        <el-descriptions-item label="运单编号">{{
          detailList.billInvoiceDetail.waybillNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="物流费">{{
          detailList.billInvoiceDetail.freight
        }}</el-descriptions-item>
        <el-descriptions-item label="寄件时间">{{
          detailList.billInvoiceDetail.sendTime
        }}</el-descriptions-item>
        <el-descriptions-item
          :label="detailList.deliveryStatus != 2 ? '派件员姓名' : ''"
          >{{
            detailList.deliveryStatus != 2
              ? detailList.billInvoiceDetail.dispatcherName
              : ''
          }}</el-descriptions-item
        >
        <el-descriptions-item
          :label="detailList.deliveryStatus != 2 ? '派件人手机' : ''"
          >{{
            detailList.deliveryStatus != 2
              ? detailList.billInvoiceDetail.dispatcherNumber
              : ''
          }}</el-descriptions-item
        >
      </el-descriptions>
    </div>

    <InvoiceReachTarget
      :visible.sync="visible"
      :financeNo="financeNo"
      :billExpenseNo="billExpenseNo"
      @onLoad="getInvoiceDetail(invoiceId)"
    ></InvoiceReachTarget>

    <BasicFooter
      :btn-options="btnOptions"
      @click="handleClickEvent"
    ></BasicFooter>
  </div>
</template>

<script>
import BasicFooter from '@/components/basic-footer/index'
import { getDetail } from '@/api/bill/billinvoice'
import { formatMoney } from '@/util/filter'
import InvoiceReachTarget from './invoiceReachTarget'

const btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
  {
    btnName: '开票明细',
    funName: 'Detail',
    type: 'primary',
  },
  {
    btnName: '开票并寄出',
    funName: 'DetailAndSend',
    type: 'primary',
  },
  {
    btnName: '已到达目的地',
    funName: 'Reach',
    type: 'primary',
  },
]

export default {
  components: { BasicFooter, InvoiceReachTarget },
  data() {
    return {
      btnOptions: [],
      detailList: {
        billInvoiceDetail: {},
      }, //详情数据
      visible: false,
      financeNo: '', // 融资编号
      billExpenseNo: '', // 费用编号
      sendType: 1, // 邮寄or邮件
    }
  },
  watch: {
    'detailList.deliveryStatus': {
      handler(val) {
        let list = [...btnOptions]
        if (val == 1) {
          // 待开票
          this.btnOptions = list.filter(
            item => item.funName !== 'Reach' && item.funName !== 'Detail'
          )
        } else if (val == 2) {
          // 已寄出
          this.btnOptions = list.filter(
            item => item.funName !== 'DetailAndSend'
          )
        } else if (val == 3) {
          // 代收票
          this.btnOptions = list.filter(
            item => item.funName !== 'Reach' && item.funName !== 'DetailAndSend'
          )
        } else if (val == 4) {
          // 已收票
          this.btnOptions = list.filter(
            item => item.funName !== 'Reach' && item.funName !== 'DetailAndSend'
          )
        } else if (val === 5) {
          // 已取消
          this.btnOptions = list.filter(item => item.funName === 'Revert')
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 物流信息只有已寄出、代收票、已收票才展示
    isShowGoodsDetil() {
      let bool = false
      if ([2, 3, 4].includes(this.detailList.deliveryStatus)) {
        bool = true
      }
      return bool
    },
  },
  mounted() {
    let id = JSON.parse(Buffer.from(this.$route.params.id, 'base64').toString())
    this.invoiceId = id
    this.getInvoiceDetail(id)
  },
  methods: {
    // 查看详情
    getInvoiceDetail(id) {
      let list = {}
      getDetail(id)
        .then(({ data }) => {
          if (data.code === 200) {
            list = {
              ...data.data,
              billInvoiceDetail: {
                ...data.data.billInvoiceDetail,
                receiveAddress: data.data.billInvoiceDetail.receiveAddress
                  ? data.data.billInvoiceDetail.receiveAddress.split(',')[0]
                  : '',
                freight:
                  data.data &&
                  data.data.billInvoiceDetail &&
                  data.data.billInvoiceDetail.freight
                    ? formatMoney(data.data.billInvoiceDetail.freight)
                    : 0,
              },
              amount: '￥' + formatMoney(data.data.amount), // 金额样式转换
            }
            this.financeNo = data.data.financeNo
          }
          this.detailList = list
          if (data.data.billInvoiceDetail) {
            if (data.data.billInvoiceDetail.sendType) {
              this.sendType = data.data.billInvoiceDetail.sendType
            }
          }
          if (list.billExpenseNo) {
            this.billExpenseNo = list.billExpenseNo
          }
        })
        .catch(() => {})
    },
    // 处理图标
    getInoviceIcon(state) {
      let iconClass = null
      // 1、待开票 2、已寄出 3、待收票 4、已收票 5、已取消
      switch (state) {
        case 1: //
          iconClass = 'icon-line-dengdai'
          break
        case 2:
          iconClass = 'icon-line-chenggong'
          break
        case 3:
          iconClass = 'icon-line-dengdai'
          break
        case 4:
          iconClass = 'icon-line-chenggong'
          break
        case 5:
          iconClass = 'icon-line-zuojiantou-yuan'
          break
      }
      return iconClass
    },
    // 处理图标颜色
    getInoviceIconColor(state) {
      let iconColor = 'fill: #697cff'
      // 1、待开票 2、已寄出 3、待收票 4、已收票 5、已取消
      switch (state) {
        case 1:
          iconColor = 'fill: #697cff'
          break
        case 2:
          iconColor = 'fill: #697cff'
          break
        case 3:
          iconColor = 'fill: #697cff'
          break
        case 4:
          iconColor = 'fill: #3DC861'
          break
        case 5:
          iconColor = 'fill: #FB3030'
          break
      }
      return iconColor
    },
    // 底部栏按钮操作
    handleClickEvent(name) {
      switch (name) {
        case 'Revert': // 返回
          this.$router.go(-1)
          break
        case 'Detail': // 开票明细
          this.$router.push(
            '/bill/invoiceType/billingDetail/' +
              Buffer.from(
                JSON.stringify({
                  state: this.detailList.deliveryStatus,
                  billExpenseNo: this.detailList.billExpenseNo,
                  financeNo: this.detailList.financeNo,
                  type: this.detailList.type,
                })
              ).toString('base64')
          )
          break
        case 'DetailAndSend':
          this.$router.push(
            '/bill/invoiceType/billingDetail/' +
              Buffer.from(
                JSON.stringify({
                  state: this.detailList.deliveryStatus,
                  billExpenseNo: this.detailList.billExpenseNo,
                  financeNo: this.detailList.financeNo,
                  type: this.detailList.type,
                })
              ).toString('base64')
          )
          break
        case 'Reach': // 已到达目的地
          this.visible = true
          break
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.invoice-details-container {
  padding-bottom: 88px !important;
  .invoice-apply-detail {
    padding: 18px 20px 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    background-color: #fff;
    ::v-deep {
      .el-descriptions__title {
        display: flex;
        align-items: center;
        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 15px;
          background-color: #697cff;
          border-radius: 46px;
          margin-right: 8px;
        }
      }
      .el-descriptions-item__label {
        width: 135px;
      }
      .el-descriptions-item__content {
        width: 230px;
      }
    }
    .invoice-apply-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      & span {
        height: 22px;
        line-height: 22px;
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.85);
      }
      .invoice-apply-head-right {
        display: flex;
        align-items: center;
        height: 20px;
        line-height: 20px;

        & span:first-child {
          color: #7d7d7d;
          font-size: 14px;
        }
        & span:last-child {
          color: #101010;
          margin-left: 10px;
          font-size: 14px;
        }
      }
    }
    .invoice-apply-box {
      display: flex;
      .invoice-apply-left {
        width: calc(50% - 12.5px);
        background-color: #f6f6f6;
        border-radius: 8px;
        padding: 24px 0 20px 20px;
        margin-right: 25px;
        .apply-item {
          display: flex;
          align-items: center;
          height: 20px;
          line-height: 20px;
          &:not(:last-child) {
            margin-bottom: 24px;
          }
          & span:first-child {
            display: block;
            width: 130px;
            margin-right: 4px;
            text-align: right;
            color: #7d7d7d;
            font-size: 14px;
            font-weight: 500;
          }
          & span:last-child {
            font-size: 14px;
            font-weight: 500;
          }
          .apply-item-blue {
            cursor: pointer;
            color: #697cff;
          }
          .apply-item-basic {
            color: #101010;
          }
        }
      }
      .invoice-apply-right {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: calc(50% - 12.5px);
        background-color: #f6f6f6;
        border-radius: 8px;
        .invoice-apply-right-icon {
          font-size: 40px !important;
        }
        .invoice-apply-status-name {
          display: block;
          height: 24px;
          line-height: 24px;
          color: #101010;
          font-size: 16px;
          font-weight: bold;
          margin: 5px 0 7px;
          font-family: SourceHanSansSC-bold;
        }
        .invoice-apply-status-desc {
          display: block;
          color: #7d7d7d;
          height: 20px;
          line-height: 20px;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
