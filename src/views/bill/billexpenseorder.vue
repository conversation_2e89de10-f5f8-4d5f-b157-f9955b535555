<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >

<!--      <template slot="menuLeft"> </template>-->
      <!--      列表支付状态方法-->
      <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="paymentStatus" @change="handleTabButton">
            <el-radio-button
              v-for="(item, key) in paymentStatusMap"
              :key="key"
              :label="key"
              >{{ item }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </template>
      <!--    支付状态颜色加深-->
      <template slot-scope="{ row }" slot="paymentStatusName">
        <el-tag
          :type="
            row.paymentStatus === 1
              ? 'primary'
              : row.paymentStatus === 2
              ? 'success'
              : 'info'
          "
          >{{ row.paymentStatusName }}</el-tag
        >
      </template>
      <!--    支付方式颜色加深-->
      <template slot-scope="{ row }" slot="paymentMethodName">
        <el-tag v-if="row.paymentMethod === 1"
                :style="{
            color: '#101010',
            border: '1px solid #909399',
            background:'#f7f7f700'
          } ">

          {{row.paymentMethodName}}
        </el-tag>

        <el-tag v-if="row.paymentMethod !== 1"
                :style="{
            color: 'rgb(18 119 255)',
            border: '1px solid rgb(18 119 255 / 49%)',
            background:'#f7f7f700'
          } ">

          {{row.paymentMethodName}}
        </el-tag>

      </template>
      <!--   详情按钮-->
      <template slot="menu" slot-scope="{ row }">
        <el-button type="text" size="small" @click="toDetail(row)"
          >详情
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
} from '@/api/bill/billexpenseorder'
import { mapGetters } from 'vuex'
import { getDictionary } from '@/api/system/dictbiz'
import {formatMoney} from "@/util/filter";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      paymentStatusMap: {},
      paymentStatus: '0',
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: false,
        selection: false,
        dialogClickModal: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,

        align: 'center',
        column: [
          {
            label: '融资编号',
            prop: 'financeNo',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '费用单号',
            prop: 'billExpenseNo',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入费用单号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '支付单号',
            prop: 'payOrderNo',
            search: true,
          },
          {
            label: '融资用户',
            prop: 'customerName',
            rules: [
              {
                required: true,
                message: '请输入用户id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资用户',
            prop: 'customerId',
            search: true,
            type: 'tree',
            dicUrl:
              '/api/blade-customer/web-back/customer/frontfinancinglist/all',
            dictMethod: 'get',
            props: {
              label: 'name',
              value: 'companyId',
            },
            hide: true,
            display: false,
          },
          {
            label: '应付金额',
            prop: 'copeAmount',
            rules: [
              {
                required: true,
                message: '请输入应付金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '支付方式',
            prop: 'paymentMethodName',
            slot: true,
            rules: [
              {
                required: true,
                message: '请输入支付方式;',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '支付方式',
            prop: 'paymentMethod',
            search: true,
            display: false,
            hide: true,
            type: 'select',
            dataType: 'number',
            searchFilterable: true,
            searchMultiple: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=bill_pay',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [
              {
                required: true,
                message: '请输入支付方式;',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '创建时间',
            prop: 'createTime',
            rules: [
              {
                required: true,
                message: '请输入创建时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '创建时间',
            prop: 'createTimeRange',
            type: 'datetime',
            format: 'yyyy-MM-dd hh:mm:ss',
            valueFormat: 'yyyy-MM-dd hh:mm:ss',
            searchRange: true,
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入创建时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '支付状态',
            prop: 'paymentStatusName',
            slot: true,
            rules: [
              {
                required: true,
                message: '请输入支付状态;',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'paymentStatus',
            search: true,
            display: false,
            hide: true,
            type: 'select',
            dataType: 'number',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=bill_pay_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [
              {
                required: true,
                message: '请输入支付状态;',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.billexpenseorder_add, false),
        viewBtn: this.vaildData(this.permission.billexpenseorder_view, false),
        delBtn: this.vaildData(this.permission.billexpenseorder_delete, false),
        editBtn: this.vaildData(this.permission.billexpenseorder_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },

    //详情页面事件
    toDetail(row) {
      this.$router.push(
        '/bill/expenseOrderDetail/' +
          Buffer.from(JSON.stringify(row.id)).toString('base64')
      )
    },

    //页面点击支付状态事件方法
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      const { createTimeRange } = this.query
      let values = {
        ...params,
      }
      if (createTimeRange) {
        values = {
          ...params,
          create_time_datege: createTimeRange[0],
          create_time_datelt: createTimeRange[1],
          ...this.query,
        }
        values.createTimeRange = null
      }
      //判断如果传入的paymentMethod的值是0那么就不进行支付状态条件筛选
      if (this.paymentStatus !== '0') {
        this.query.paymentStatus = this.paymentStatus
      } else {
        this.query = {}
      }
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query, values)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        data.records.forEach(amount =>{
          amount.amount = "￥"+formatMoney(amount.amount)
        })
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
  created() {
    //页面初始化调用字典接口获取支付状态数据
    getDictionary({ code: 'bill_pay_status' }).then(resp => {
      let data = resp.data.data
      this.paymentStatusMap[0] = '全部'
      data.forEach(status => {
        this.paymentStatusMap[status.dictKey] = status.dictValue
      })
      // console.log(this.paymentStatusMap)
    })
  },
}
</script>

<style></style>
