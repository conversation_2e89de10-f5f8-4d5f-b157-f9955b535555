<template>
  <div class="foreclosure-confirm-container">
    <ArticleSetps
      :info="'赎货确认'"
      :arrData="stateData.setpHeaderData"
      :current="stateData.currentNodeIndex"
      :widths="'1000px'"
    />
    <div class="main-content">
      <div v-if="stateData.currentNodeIndex === 0" class="form-container">
        <!-- 赎货单 -->
        <div class="note-container">
          <div class="title-container">
            <span class="title">赎货单</span>
          </div>
          <GoodsTable
            :loading="loading"
            :pageData="pageData"
            :tableData="goodsTableData"
          />
          <div class="form-container">
            <div class="form-item">
              <span class="label">供应商</span>
              <span class="value light">
                {{ pageData.supplierName || '--' }}</span
              >
            </div>
            <div class="form-item">
              <span class="label">采购单价(元)</span>
              <span class="value">
                <span class="value sign">￥</span
                >{{ pageData.purchasePrice }}</span
              >
            </div>
            <div class="form-item">
              <span class="label">融资单价(元)</span>
              <span class="value red">
                <span class="value sign">￥</span
                >{{ pageData.financingPrice }}</span
              >
            </div>
            <div class="form-item">
              <span class="label">入库日期</span>
              <span class="value light">
                {{ pageData.warehouseInDate || '--' }}</span
              >
            </div>
            <div class="form-item">
              <span class="label">库龄(天)</span>
              <span class="value light"> {{ pageData.warehouseAge }}</span>
            </div>
            <div class="form-item">
              <span class="label">提货方式</span>
              <span class="value light"> {{ pageData.extractType }}</span>
            </div>
            <template v-if="pageData.extractType === '第三方物流'">
              <div class="form-item">
                <span class="label">收货地址</span>
                <span class="value light">
                  {{
                    `${pageData.financingAddress.contacts}，${
                      pageData.financingAddress.addressPhone
                    }，${
                      pageData.financingAddress.urbanAreas ||
                      pageData.financingAddress.addressTarget
                    }`
                  }}</span
                >
              </div>
            </template>
            <template v-else-if="pageData.redeemUser">
              <div class="form-item">
                <span class="label">提货人</span>
                <span class="value light">{{
                  `${pageData.redeemUser.username} | ${pageData.redeemUser.idCard} | ${pageData.redeemUser.phone}`
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">车牌号</span>
                <span class="value light">{{
                  pageData.redeemUser.licensePlate
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">提货日期</span>
                <span class="value light">{{
                  pageData.redeemUser.arriveTime || '--'
                }}</span>
              </div>
            </template>
          </div>
        </div>
        <Feiyongwaihezi ref="feiyongwaiheziRef" feeNode="9" />
        <!-- 线上支付 -->
        <!--<div class="onLinePay" v-if="FeeObj.onlineFeeArr.length">
          <div class="infometion-top-box">
            <div class="infometion-left">
              <div class="left-title">线上支付费用(元)</div>
              <div class="left-money">
                <span class="num">{{ FeeObj.allMoney }}</span>
                <div
                  class="play-type"
                  :style="{ background: payType == 3 ? '#0BB07B' : '#0d55cf' }"
                >
                  <MySvgIcon
                    :icon-class="
                      payType == 3 ? 'icon-chenggong1' : 'icon-dengdai1'
                    "
                    style="fill: #fff; font-size: 20px"
                  />
                  <span class="play-text">{{
                    getPayType[Number(payType) - 1]
                  }}</span>
                </div>
                <div style="margin-left: 3px" v-if="payType == 2">
                  <a-button type="text" danger @click="cancelPayment">
                    <span style="font-weight: bold"> 取消支付 </span>
                  </a-button>
                </div>
              </div>
              <div class="online-payment-box">
                <p class="pay-text" v-if="Feedata.isContainCapitalCost == 2">
                  请选择支付方式
                </p>
                <LabelBar
                  v-if="Feedata.isContainCapitalCost == 2"
                  :labelList="labelData.labelList"
                  :state="labelData.currentIndex"
                  :disableM="payType == 3"
                  @switch="handleSwitch"
                />
                <div
                  v-if="
                    Feedata.isContainCapitalCost == 1 &&
                    FeeObj.offlinePayArr.length != 0 &&
                    payType != 3
                  "
                >
                  <n-button
                    :bordered="false"
                    :disabled="!(payType == 1 || payType == 4)"
                    round
                    type="info"
                    size="large"
                    @click="onlinePay"
                    >立即支付</n-button
                  >
                </div>
              </div>
            </div>
            <div
              v-if="Feedata.isContainCapitalCost == 2"
              class="infometion-right"
              :class="{
                'infometion-obscuration':
                  payLoading || payType || labelData.currentIndex !== 0,
              }"
            >
              <PaymentQRCode :financeNoV="2" />
            </div>
          </div>
          <div class="infometion-bottom-box">
            <div class="bottom-title">费用明细</div>
            <div class="table-box">
              <a-table
                :columns="feecolumns"
                :data-source="FeeObj.onlineFeeArr"
                :pagination="false"
                :loading="loading"
              >
                <template #summary v-if="!loading">
                  <a-table-summary-row>
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                    >
                      总计
                    </a-table-summary-cell>
                    <a-table-summary-cell />
                    <a-table-summary-cell />
                    <a-table-summary-cell />
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: SFProDisplay-Semibold, SFProDisplay;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                    >
                      <a-typography-text>
                        ￥{{ FeeObj.allMoney }}
                        <span v-if="showNowCom === 1">
                          (包含本金 ￥{{ totalPrincipal }})
                        </span>
                      </a-typography-text>
                    </a-table-summary-cell>
                  </a-table-summary-row>
                </template>
              </a-table>
            </div>
          </div>
        </div>
        <div
          class="infometion-box"
          v-for="(item, index) in FeeObj.offlinePayArr"
          :key="index"
          style="margin-top: 30px"
        >
          <div class="infometion-top-box">
            <div class="infometion-left">
              <div class="left-title">
                {{ item.redeemAccountVOS.expenseType }}(元)
              </div>
              <div class="left-money">
                <span class="num"
                  ><span
                    style="
                      font-size: 24px;
                      font-family: CoreSansD65Heavy;
                      color: #031222;
                      line-height: 30px;
                    "
                    >￥</span
                  >{{
                    Number(item.redeemAccountVOS.belowAmount).toFixed(2)
                  }}</span
                >
                <div class="play-type">
                  <MySvgIcon
                    icon-class="icon-xiajiantou-yuan1"
                    style="fill: #0d55cf; font-size: 20px"
                  />
                  <span class="play-text" style="">线下支付</span>
                </div>
              </div>
              <p class="left-tips">
                请将其他费用转账至下方收款账户，为了提升审批进度，请备注支付信息，方便平台工作人员进行审核～
              </p>
              <div class="management-accounts">
                <p class="accounts-p">
                  <span>收款账户</span>
                  <span>{{
                    item.redeemAccountVOS.billBankCarda?.bankCardNo
                  }}</span>
                </p>
                <p class="accounts-p">
                  <span>收款公司名</span>
                  <span>{{
                    item.redeemAccountVOS.billBankCarda?.openHouseName
                  }}</span>
                </p>
                <p class="accounts-p">
                  <span>开户银行</span>
                  <span>{{
                    item.redeemAccountVOS.billBankCarda?.bankDeposit
                  }}</span>
                </p>
                <p class="accounts-p">
                  <span>备注</span>
                  <span>企业名称+产品名称</span>
                </p>
              </div>
            </div>
            <div class="infometion-right">
              <UploadDragger
                @setfileList="fileList => setfileAttachList(item, fileList)"
                :arrData="item.fileList"
              />
            </div>
          </div>
          <div class="infometion-bottom-box">
            <div class="bottom-title">费用明细</div>
            <div class="table-box">
              <a-table
                :columns="offlineFeecolumns"
                :data-source="item.redeemAccountVOS.belowExpenseVoList"
                :pagination="false"
                :loading="loading"
              >
                <template #summary v-if="!loading">
                  <a-table-summary-row>
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                      >总计</a-table-summary-cell
                    >
                    <a-table-summary-cell />
                    <a-table-summary-cell />
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: SFProDisplay-Semibold, SFProDisplay;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                    >
                      <a-typography-text>
                        ￥{{
                          Number(item.redeemAccountVOS.belowAmount).toFixed(2)
                        }}
                        <span
                          v-if="
                            showNowCom === 2 &&
                            item.redeemAccountVOS.expenseType === '资方费用'
                          "
                        >
                          (包含本金 ￥{{ totalPrincipal }})
                        </span>
                      </a-typography-text>
                    </a-table-summary-cell>
                  </a-table-summary-row>
                </template>
              </a-table>
            </div>
          </div>
        </div>-->
        <div class="button-container">
          <a-button
            class="submit-btn"
            type="primary"
            :loading="submitBtnLoading"
            @click="handleSubmitBtn"
            >提交申请</a-button
          >
        </div>
      </div>
      <div
        v-else-if="stateData.currentNodeIndex === 1"
        class="approval-container"
      >
        <MySvgIcon
          icon-class="icon-shijian"
          style="fill: #0d55cf; font-size: 64px"
        />
        <span class="title">审批中</span>
        <span class="desc"
          >您的赎货确认信息已提交，我们将会尽快进行审核，请耐心等待！</span
        >
        <router-link
          class="redir-btn"
          :to="{ name: 'UserForeclosureListIndex' }"
        >
          <span>返回列表</span>
        </router-link>
      </div>
      <template v-else-if="stateData.currentNodeIndex === 2">
        <div class="pass-container">
          <MySvgIcon
            icon-class="icon-renzheng"
            style="fill: #0bb07b; font-size: 64px"
          />
          <span class="title">恭喜您！申请成功</span>
          <span class="desc">请点击下方按钮前往首页</span>
          <router-link :to="{ name: 'Home' }">
            <n-button class="login-btn blue border" :bordered="false" round
              >去首页</n-button
            >
          </router-link>
        </div>
      </template>
    </div>
  </div>
  <div class="background-white" />
  <!-- 弹出网银的弹窗 -->
  <!-- <DialoginternetBankSelect
    ref="dialoginternetBankSelectRef"
    :financeNoV="redeemNo"
    :allMonry="FeeObj.allMoney"
    @changeCurrentIndex="changeCurrentIndex"
  /> -->
</template>

<script lang="ts">
export default {
  name: 'UserForeclosureConfirmIndex',
}

// const columns = [
//   {
//     title: '费用名称',
//     dataIndex: 'name',
//   },
//   {
//     title: '支付节点',
//     dataIndex: 'chargePoint',
//   },
//   {
//     title: '计费方式',
//     dataIndex: 'calculation',
//   },
//   {
//     title: '应付金额',
//     dataIndex: 'money',
//   },
// ]
</script>
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { NButton } from 'naive-ui'
import { useRoute } from 'vue-router'
import { formatMoney } from '@/utils/utils'
// import LabelBar from './component/LabelBar.vue'
// import PaymentQRCode from './component/paymentQRCode.vue'
import ArticleSetps from '@/components/articleSetps/index.vue'
import GoodsTable from '../detail/components/GoodsTable/index.vue'
import Feiyongwaihezi from '@/components/feiyongxiaoheizi'
// import UploadDragger from '@/views/product/loanApplication/components/component/uploadDragger.vue'
// import DialoginternetBankSelect from './component/Dialog/dialoginternetBankSelect.vue'
import {
  requestForeclosureDetail,
  requestSubmitRedeemConfirm,
} from '@/api/user/Foreclosure'
// import { PRODUCT_VERIFY_API } from '@/api/index'
import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
// import { Modal, message } from 'ant-design-vue'
// import payMoney from './payMoney/payMoney'

// const {
//   Feedata,
//   FeeObj,
//   payType,
//   redeemNo,
//   onlinePay,
//   labelData,
//   stateData,
//   getPayType,
//   feecolumns,
//   payLoading,
//   handleSwitch,
//   handleSubmitBtn,
//   offlineFeecolumns,
//   changeCurrentIndex,
//   dialoginternetBankSelectRef,
// } = payMoney()
const stateData = reactive({
  setpHeaderData: [
    { id: 1, name: '确认信息' },
    { id: 2, name: '审批确认' },
    { id: 3, name: '申请成功' },
  ],
  currentNodeIndex: 0,
})
const route = useRoute()

const feiyongwaiheziRef = ref(null)
// pengData
const loading = ref(true)
const pageData = ref<any>({
  id: route.params.id,
  redeemNo: route.params.redeemNo,
})
const respOriginData = ref<any>({})
const goodsTableData = ref<any>([])
const totalPrincipal = ref<string>('0')
// const bankAccountInformation = ref<any>({})
const repaymentData = reactive<any>({
  allMoney: '0',
  fileAttachData: [],
})
const platformFeeData = reactive<any>({
  tableData: [],
  allMoney: '0',
  fileAttachData: [],
})
const submitBtnLoading = ref<boolean>(false)
// 在什么地方显示本金金额
// const showNowCom = computed<number>(() => {
//   let showNow = 1
//   if (FeeObj?.offlinePayArr && FeeObj?.offlinePayArr.length) {
//     for (const item of FeeObj.offlinePayArr) {
//       if (item.redeemAccountVOS.expenseType === '资方费用') {
//         showNow = 2
//         break
//       }
//     }
//   }
//   return showNow
// })

// 获取上传附件
// const setfileAttachList = (isRepayment: any, fileList: any) => {
//   isRepayment.fileList = fileList
// }

// 查询平台账号
// const getAccountByFinanceNoFun = (INo: any) => {
//   PRODUCT_VERIFY_API.getAccountByFinanceNo(INo).then(({ data }) => {
//     const { data: resData } = data
//     if (data.success && resData) {
//       bankAccountInformation.value = resData
//     }
//   })
// }

// 给公用缴费组件的请求参数
sessionStorage.setItem('financingDemandNo', route.params.redeemNo)
sessionStorage.setItem('ggggoodsId', route.params.id)

// 请求页面数据
const requestData = () => {
  loading.value = true
  const requestObj = {
    redeemNo: pageData.value.redeemNo,
  }
  requestForeclosureDetail(requestObj)
    .then(({ data }) => {
      if (data.success) {
        loading.value = false
        data = data.data || {}
        const _respOriginData = cloneDeep(data)
        data = data.redeemDetailCargoCurrencyVO || {}
        // 查询平台账号
        // getAccountByFinanceNoFun(data.financeNo)
        data.manExpenseCulationVoList = data.manExpenseCulationVoList || []
        data.plaExpenseCulationVoList = data.plaExpenseCulationVoList || []
        data.manAttachList = data.manAttachList || []
        data.plaAttachList = data.plaAttachList || []

        // 数据格式化
        data.redeemUser = data.redeemUser || {}
        data.financingAddress = data.financingAddress || {}
        if (data.redeemUser.arriveTime) {
          data.redeemUser.arriveTime = dayjs(
            data.redeemUser.arriveTime,
            'YYYY-MM-DD'
          ).format('YYYY.MM.DD')
        }
        if (data.warehouseInDate) {
          data.warehouseInDate = dayjs(
            data.warehouseInDate,
            'YYYY-MM-DD'
          ).format('YYYY.MM.DD')
        }
        data.originPurchasePrice = data.purchasePrice
        data.purchasePrice = formatMoney(data.purchasePrice)
        data.originFinancingPrice = data.financingPrice
        data.financingPrice = formatMoney(data.financingPrice)
        data.originRepayFunds = data.originFinancingPrice * data.num
        data.repayFunds = formatMoney(data.originRepayFunds)

        // 计算银行还款单
        let repaymentTotalMoney = data.originRepayFunds
        for (const item of data.manExpenseCulationVoList) {
          repaymentTotalMoney += Number(item.money)
        }
        repaymentData.allMoney = formatMoney(repaymentTotalMoney)
        // 计算平台费用单
        let platformFeeTotalMoney = 0
        for (const item of data.plaExpenseCulationVoList) {
          platformFeeTotalMoney += Number(item.money)
        }
        platformFeeData.allMoney = formatMoney(platformFeeTotalMoney)
        // 处理费用明细表格
        const _platformFeeTableData = []
        for (const item of data.plaExpenseCulationVoList) {
          _platformFeeTableData.push({
            name: item.name,
            chargePoint: '赎货确认',
            // calculation: item.calculation,
            calculation: item.feeFormula,
            money: '￥' + formatMoney(item.money),
          })
        }
        platformFeeData.tableData = _platformFeeTableData

        // 货品表格数据
        goodsTableData.value = [
          {
            purchaseUnitPrice: '￥' + data.purchasePrice,
            financingUnitPrice: '￥' + data.financingPrice,
            quantity: data.num,
            unit: data.goodsUnitValue,
          },
        ]
        // 总计本金计算
        totalPrincipal.value = (
          Number(data.financingPrice.replace(/,/g, '')) * Number(data.num)
        ).toFixed(2)

        // 处理银行还款单已上传的凭证
        const _bankRepaymentFileArr = []
        for (const item of data.manAttachList) {
          _bankRepaymentFileArr.push({ name: item.name, url: item.link })
        }
        repaymentData.fileAttachData = _bankRepaymentFileArr

        // 处理平台费用单已上传的凭证
        const _platformFeeFileArr = []
        for (const item of data.manAttachList) {
          _platformFeeFileArr.push({ name: item.name, url: item.link })
        }
        platformFeeData.fileAttachData = _platformFeeFileArr

        // 头部步骤条
        if (data.status === 5) {
          stateData.currentNodeIndex = 1
        } else if (data.status >= 8) {
          stateData.currentNodeIndex = 2
        }

        respOriginData.value = _respOriginData
        pageData.value = { ...pageData.value, ...data }
      }
    })
    .catch(() => {})
}

// const cancelPayment = () => {
//   Modal.confirm({
//     title: () => '是否取消支付？',
//     onOk() {
//       const dataP = {
//         redeemNo: redeemNo,
//       }
//       PRODUCT_VERIFY_API.expenseCancelPay(dataP)
//         .then(({ data }) => {
//           message.success('取消成功')
//           // 刷新当前页面
//           setTimeout(() => {
//             location.reload()
//           }, 400)
//         })
//         .catch(() => {})
//     },
//     onCancel() {},
//   })
// }

// 提交按钮
const handleSubmitBtn = () => {
  if (!feiyongwaiheziRef.value.exposeFeiyongListFun()) {
    return
  }
  // if (repaymentData.fileAttachData.length == 0) {
  //   return message.error('请上传银行还款单')
  // }
  // if (platformFeeData.fileAttachData.length == 0) {
  //   return message.error('请上传平台费用单')
  // }
  submitBtnLoading.value = true
  const requestObj = {
    redeemNo: pageData.value.redeemNo,
    // bankRepaymentVoucher: '',
    // platformFeeVoucher: '',
    expenseInfoExpenseList: feiyongwaiheziRef.value.exposeFeiyongListFun(),
  }
  // for (const item of repaymentData.fileAttachData) {
  //   requestObj.bankRepaymentVoucher += ',' + item.attachId
  // }
  // requestObj.bankRepaymentVoucher = requestObj.bankRepaymentVoucher.substring(1)
  // for (const item of platformFeeData.fileAttachData) {
  //   requestObj.platformFeeVoucher += ',' + item.attachId
  // }
  // requestObj.platformFeeVoucher = requestObj.platformFeeVoucher.substring(1)
  requestSubmitRedeemConfirm(requestObj)
    .then(() => {
      message.success('提交成功')
      stateData.currentNodeIndex = 1
    })
    .catch(() => {})
    .finally(() => {
      submitBtnLoading.value = false
    })
}

requestData()
</script>

<style lang="scss" scoped>
.foreclosure-confirm-container {
  width: 1400px;
  margin: 40px auto 0;
  margin-bottom: 60px;

  .main-content {
    max-width: 1400px;
    margin: auto;
    padding-top: 40px;
  }

  .form-container {
    padding-bottom: 24px;

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .label {
        width: 140px;
        flex-shrink: 0;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #0a1f44;
        line-height: 20px;
      }

      .value {
        max-width: 75%;
        font-size: 14px;
        font-family: SFProText-Semibold, SFProText;
        font-weight: 600;
        color: #0a1f44;
        line-height: 20px;

        &.sign {
          font-family: CoreSansD55Bold;
          font-size: 12px;
          color: inherit;
        }

        &.light {
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8a94a6;
        }

        &.red {
          color: #dd2727;
        }

        &.tip {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #dd2727;
        }
      }

      .btn {
        margin-left: 8px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #0d55cf;
        line-height: 20px;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .note-container {
    padding: 40px;
    background: #ffffff;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 1px solid #efefef;
    margin-bottom: 20px;
    .title-container {
      margin-bottom: 20px;

      .title {
        font-size: 20px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #0a1f44;
        line-height: 28px;
      }
    }

    .form-container {
      margin-top: 32px;
    }
  }

  .repayment-container {
    margin: 40px 0;
  }

  .infometion-box {
    background: #ffffff;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 1px solid #efefef;
    padding: 40px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .infometion-top-box {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .infometion-left {
        .left-title {
          height: 28px;
          font-size: 20px;
          font-weight: 600;
          color: #0a1f44;
          line-height: 28px;
        }
        .left-money {
          display: flex;
          align-items: center;

          .num {
            font-size: 50px;
            color: #031222;
            font-weight: 600;
          }
          .play-type {
            height: 40px;
            background: #ebf5ff;
            padding: 10px 20px;
            box-sizing: border-box;
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            line-height: 20px;
            border-radius: 100px;
            display: flex;
            align-items: center;
            margin-left: 15px;

            .play-text {
              margin-left: 4px;
              color: #0d55cf;
            }
          }
        }
        .left-tips {
          height: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 20px;
          margin-bottom: 24px;
        }
        .management-accounts {
          .accounts-p {
            margin-bottom: 32px;

            &:last-child {
              margin-bottom: 0;
            }

            span {
              display: inline-block;
            }

            & span:first-child {
              width: 70px;
              height: 20px;
              font-size: 14px;
              font-weight: 400;
              color: #8a94a6;
              line-height: 20px;
              margin-right: 10px;
            }
            & span:last-child {
              height: 20px;
              font-size: 14px;
              font-weight: 400;
              color: #0a1f44;
              line-height: 20px;
            }
          }
        }
      }

      .infometion-right {
        width: 394px;
      }
    }

    .infometion-bottom-box {
      overflow: hidden;
      margin-top: 40px;

      .bottom-title {
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #0a1f44;
        line-height: 20px;
      }
      .table-box {
        margin-top: 12px;
        border: 1px solid #f1f2f4;
      }
    }

    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      z-index: 0;
      width: 279px;
      height: 277px;
      top: -50px;
      right: -40px;
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.2;
      border-radius: 150px;
      filter: blur(49px);
    }
    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      z-index: 0;
      width: 106px;
      height: 143px;
      top: -20px;
      left: 0px;
      border-radius: 150px;
      filter: blur(24px);
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.2;
    }
  }

  .approval-container,
  .pass-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 120px;
    font-size: 24px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #0a1f44;

    .title {
      margin-top: 12px;
      font-size: 24px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #0a1f44;
    }

    .desc {
      margin-top: 12px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #8a94a6;
    }

    .login-btn {
      width: 200px;
      height: 48px;
      margin-top: 20px;
      background: #0c66ff;
      border-radius: 24px;
      line-height: 48px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #ffffff;
      box-sizing: border-box;
      cursor: pointer;
    }

    .redir-btn {
      margin-top: 48px;
      display: inline-flex;
      align-items: center;
      height: 48px;
      padding: 0 28px;
      border-radius: 100px;
      border: 1px solid #0c66ff;

      span {
        font-size: 16px;
        font-weight: 500;
        color: #0d55cf;
      }
    }
  }

  .loading-container {
    width: 100%;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 24px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #0a1f44;

    > span {
      margin-top: 12px;
    }
  }

  .button-container {
    width: 100%;
    margin-top: 40px;
    text-align: center;

    .submit-btn {
      width: 400px;
      height: 48px;
      background: #0c66ff;
      border-color: #0c66ff;
      border-radius: 24px;
      overflow: hidden;

      &[disabled] {
        color: rgba(0, 0, 0, 0.25);
        background: #f5f5f5;
        border-color: #d9d9d9;
        text-shadow: none;
        box-shadow: none;

        :deep(span) {
          color: inherit;
        }
      }

      :deep(span) {
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
}

:deep(.ant-table) {
  background: transparent;

  .ant-table-thead {
    .ant-table-cell {
      background-color: #f8f9fb;
    }
  }

  .ant-table-thead {
    .ant-table-cell {
      padding: 10px 24px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
    }
  }

  .ant-table-tbody {
    .ant-table-cell {
      padding: 10px 24px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #0a1f44;
      line-height: 20px;
    }
  }

  .ant-table-summary {
    background-color: #f8f9fb;
  }
}

.background-white {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #fff;
  z-index: -1024;
}
</style>
<style lang="scss">
@mixin backgroundMask {
}

.usage-record-popover {
  &.availabel-amount {
    .ant-popover-content {
      border-radius: 8px;
      overflow: hidden;

      .ant-popover-arrow {
        transform: scale(2) translateY(-20%);
      }
    }

    .availabel-amount-container {
      .title {
        display: inline-block;
        margin-bottom: 16px;
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #fefefe;
        line-height: 24px;
      }

      .availabel-content-wrapper,
      .confirm-content-wrapper {
        .availabel-content,
        .confirm-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .label {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #f3f3f3;
            line-height: 20px;
          }

          .value {
            font-size: 16px;
            font-family: CoreSansD65Heavy;
            color: #fdfdfd;
            line-height: 20px;
          }
        }

        .desc-container {
          display: flex;
          flex-direction: column;

          .desc {
            display: block;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #d5d5d5;
            line-height: 16px;
          }
        }
      }

      .confirm-content-wrapper {
        margin-top: 16px;
      }
    }
  }
}

.onLinePay {
  margin-top: 30px;
  background: #ffffff;
  box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
  border-radius: 16px;
  border: 1px solid #efefef;
  padding: 40px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 48px;

  .infometion-top-box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    z-index: 1;

    .infometion-left {
      .left-title {
        height: 28px;
        font-size: 20px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 28px;
      }
      .left-money {
        display: flex;
        align-items: center;

        .num {
          font-size: 50px;
          color: #031222;
          font-weight: 600;
        }
        .play-type {
          height: 40px;
          padding: 10px 20px;
          box-sizing: border-box;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          line-height: 20px;
          border-radius: 100px;
          display: flex;
          align-items: center;
          margin-left: 15px;

          .play-text {
            margin-left: 4px;
          }
        }
      }
      .online-payment-box {
        margin-top: 20px;

        .pay-text {
          margin-bottom: 12px;
          font-size: 14px;
          font-weight: 500;
          color: #0a1f44;
        }
      }
    }

    .infometion-right {
      position: relative;
      width: 330px;
    }

    .infometion-obscuration {
      :deep(.ant-spin-container) {
        filter: blur(4px);
      }
    }
  }

  .infometion-bottom-box {
    overflow: hidden;
    margin-top: 40px;

    .bottom-title {
      height: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #0a1f44;
      line-height: 20px;
    }
    .table-box {
      margin-top: 12px;
      border: 1px solid #f1f2f4;
    }
  }

  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    width: 279px;
    height: 277px;
    top: -50px;
    right: -40px;
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.2;
    border-radius: 150px;
    filter: blur(49px);
  }
  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    width: 106px;
    height: 143px;
    top: -20px;
    left: 0px;
    border-radius: 150px;
    filter: blur(24px);
    background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
    opacity: 0.2;
  }
}
</style>
<!-- 银行还款单 -->
<!-- <div class="repayment-container">
          <div class="infometion-box">
            <div class="infometion-top-box">
              <div class="infometion-left">
                <div class="left-title">银行还款单(元)</div>
                <div class="left-money">
                  <span class="num"
                    ><span
                      style="
                        font-size: 24px;
                        font-family: CoreSansD65Heavy;
                        color: #031222;
                        line-height: 30px;
                      "
                      >￥</span
                    >{{ repaymentData.allMoney }}</span
                  >
                  <div class="play-type">
                    <MySvgIcon
                      icon-class="icon-shangjiantou-yuan1"
                      style="fill: #0d55cf; font-size: 20px"
                    />
                    <span class="play-text" style="">线上支付</span>
                  </div>
                </div>
                <p class="left-tips">
                  还款金额将从中国人民银行宝安支行(8941)扣除，请保证账户余额充足
                </p>
                <div class="form-container">
                  <div class="form-item">
                    <span class="label">应还本金(元)</span>
                    <span class="value">
                      <span class="value sign">￥</span
                      >{{ pageData.repayFunds }}</span
                    >
                  </div>
                  <div
                    class="form-item"
                    v-for="item of pageData.manExpenseCulationVoList"
                    :key="item.expenseType"
                  >
                    <span class="label">{{ item.expenseType }}</span>
                    <template v-if="item.money === null">
                      <span class="value tip">人工核算</span>
                    </template>
                    <template v-else>
                      <span class="value red">
                        <span class="value sign">￥</span
                        >{{ formatMoney(item.money) }}</span
                      >
                    </template>
                  </div>
                 <a-popover
                      placement="bottomLeft"
                      trigger="hover"
                      :arrowPointAtCenter="true"
                      overlayClassName="usage-record-popover availabel-amount"
                      color="rgba(0,0,0,.6)"
                      :overlayStyle="{
                        backdropFilter: 'saturate(90%) blur(6px)',
                        '-webkit-backdrop-filter': 'saturate(90%) blur(6px)',
                      }"
                    >
                      <template #title />
                      <template #content>
                        <div class="availabel-amount-container">
                          <span class="title">计费规则</span>
                          <div class="availabel-content-wrapper">
                            <div class="availabel-content">
                              <span class="label">应还利息</span>
                              <span class="value"
                                >￥{{ formatMoney(4931.51) }}</span
                              >
                            </div>
                            <div class="desc-container">
                              <span class="desc"
                                >应还利息=应还本金*年利率/360*计息天数</span
                              >
                              <span class="desc"
                                >{{ formatMoney(600000.0) }}*10%/360*30={{
                                  formatMoney(4931.51)
                                }}元</span
                              >
                            </div>
                          </div>
                          <div class="confirm-content-wrapper">
                            <div class="confirm-content">
                              <span class="label">计息天数</span>
                              <span class="value">30天</span>
                            </div>
                            <div class="desc-container">
                              <span class="desc">2022/04/14~2022/05/14</span>
                            </div>
                          </div>
                        </div>
                      </template>
                      <MySvgIcon
                        icon-class="icon-xinxi"
                        style="
                          fill: #758196;
                          font-size: 24px;
                          margin-left: 4px;
                          outline: none;
                        "
                      />
                    </a-popover>
                     </div>
              </div>
              <div class="infometion-right">
                <UploadDragger
                  @setfileList="fileList => setfileAttachList(true, fileList)"
                  :arrData="repaymentData.fileAttachData"
                />
              </div>
            </div>
          </div>
        </div> -->
<!-- 平台费用单 -->
<!-- <div class="infometion-box">
          <div class="infometion-top-box">
            <div class="infometion-left">
              <div class="left-title">平台费用单(元)</div>
              <div class="left-money">
                <span class="num"
                  ><span
                    style="
                      font-size: 24px;
                      font-family: CoreSansD65Heavy;
                      color: #031222;
                      line-height: 30px;
                    "
                    >￥</span
                  >{{ platformFeeData.allMoney }}</span
                >
                <div class="play-type">
                  <MySvgIcon
                    icon-class="icon-xiajiantou-yuan1"
                    style="fill: #0d55cf; font-size: 20px"
                  />
                  <span class="play-text" style="">线下支付</span>
                </div>
              </div>
              <p class="left-tips">
                请将其他费用转账至下方收款账户，为了提升审批进度，请备注支付信息，方便平台工作人员进行审核～
              </p>
              <div class="management-accounts">
                <p class="accounts-p">
                  <span>收款账户</span>
                  <span>{{ bankAccountInformation?.bankCardNo }}</span>
                </p>
                <p class="accounts-p">
                  <span>收款公司名</span>
                  <span>{{ bankAccountInformation?.openHouseName }}</span>
                </p>
                <p class="accounts-p">
                  <span>开户银行</span>
                  <span>{{ bankAccountInformation?.bankDeposit }}</span>
                </p>
                <p class="accounts-p">
                  <span>备注</span>
                  <span>企业名称+产品名称</span>
                </p>
              </div>
            </div>
            <div class="infometion-right">
              <UploadDragger
                @setfileList="fileList => setfileAttachList(false, fileList)"
                :arrData="platformFeeData.fileAttachData"
              />
            </div>
          </div>
          <div class="infometion-bottom-box">
            <div class="bottom-title">费用明细</div>
            <div class="table-box">
              <a-table
                :columns="columns"
                :data-source="platformFeeData.tableData"
                :pagination="false"
                :loading="loading"
              >
                <template #summary v-if="!loading">
                  <a-table-summary-row>
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                      >总计</a-table-summary-cell
                    >
                    <a-table-summary-cell />
                    <a-table-summary-cell />
                    <a-table-summary-cell
                      style="
                        font-size: 20px;
                        font-family: SFProDisplay-Semibold, SFProDisplay;
                        font-weight: 600;
                        color: #0a1f44;
                        line-height: 28px;
                      "
                    >
                      <a-typography-text
                        >￥{{ platformFeeData.allMoney }}</a-typography-text
                      >
                    </a-table-summary-cell>
                  </a-table-summary-row>
                </template>
              </a-table>
            </div>
          </div>
        </div> -->
