<template>
  <div class="table-container">
    <a-table :columns="columns" :data-source="tableData" :pagination="false">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'custom-goods'">
          <!-- 货品信息 -->
          <template v-if="loading">
            <a-spin>
              <div style="width: 88px; height: 88px" />
            </a-spin>
          </template>
          <template v-else>
            <GoodsContainer
              :url="pageData.goodLogo"
              :name="pageData.goodsName"
              :value="pageData.goodsSpec"
            />
          </template>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ForeclosureGoodsTable',
}

// 宽度需要加上左边距12，右边距12；共24px
const columns = [
  {
    title: '货品信息',
    key: 'custom-goods',
    width: 300,
  },
  {
    title: '采购单价(元)',
    dataIndex: 'purchaseUnitPrice',
  },
  {
    title: '融资单价(元)',
    dataIndex: 'financingUnitPrice',
  },
  {
    title: '赎货数量',
    dataIndex: 'quantity',
  },
  {
    title: '单位',
    dataIndex: 'unit',
  },
]
</script>
<script lang="ts" setup>
import GoodsContainer from '@/views/user/Foreclosure/components/GoodsContainer/index.vue'

defineProps({
  loading: {
    type: Boolean,
    default: true,
  },
  pageData: {
    type: Object,
    default: () => {},
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
:deep(.ant-table) {
  background: transparent;

  .ant-table-thead {
    .ant-table-cell {
      background-color: #f8f9fb;
    }
  }

  .ant-table-cell {
    padding: 10px 24px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
  }
}
</style>
