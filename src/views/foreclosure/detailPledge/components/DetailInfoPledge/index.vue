<template>
  <div v-if="pageData.ready" class="detail-info-container">
    <!-- 货品表格 -->
    <div class="table-container">
      <a-table :columns="columns" :data-source="tableData" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'custom-goods'">
            <!-- 货品信息 -->
            <GoodsContainer
              :url="pageData.goodLogo"
              :name="record.goodsName"
              :value="record.spec"
            />
          </template>
          <!-- <template v-else-if="column.key === 'unit'">
            <span>台</span>
          </template> -->
        </template>
      </a-table>
    </div>
    <div class="detail-container">
      <div style="display: none" class="form-container">
        <!--<div class="form-item">
          <span class="label">提货方式</span>
          <span class="value light"> {{ pageData.extractType }}</span>
        </div>
        <template v-if="pageData.extractType === '第三方物流'">
          <div class="form-item">
            <span class="label">收货地址</span>
            <span class="value light">
              {{
                `${pageData.financingAddress.contacts}，${
                  pageData.financingAddress.addressPhone
                }，${
                  pageData.financingAddress.urbanAreas ||
                  pageData.financingAddress.addressTarget
                }`
              }}</span
            >
          </div>
        </template>-->
        <div class="form-item">
          <span class="label">创建时间</span>
          <span class="value light"> {{ respData.createTime || '--' }}</span>
        </div>
        <div class="form-item">
          <span class="label">融资编号</span>
          <span class="value link"> {{ pageData.financeNo }}</span>
        </div>
        <div class="form-item">
          <span class="label">申请人</span>
          <span class="value light"> {{ respData.createUser || '--' }}</span>
        </div>
        <!-- 赎货确认阶段 -->
        <!-- <template v-if="pageData.status >= 5"> </template> -->
        <!-- 发货阶段 - 待收货阶段 -->
        <template
          v-if="
            (pageData.status >= 9 && pageData.status <= 11) ||
            (pageData.status === 13 && pageData.extractType !== '自提')
          "
        >
          <div class="form-item">
            <span class="label">发货时间</span>
            <span class="value light">{{
              redeemSendData.sendGoodsTime || '--'
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">物流公司</span>
            <span class="value light"
              >{{
                dicMap.logisticsTypeMap[redeemSendData.logisticsCompany] || '--'
              }}
            </span>
          </div>
          <div class="form-item">
            <span class="label">运单编号</span>
            <span
              class="value light"
              style="display: inline-flex; align-items: center"
              >{{ redeemSendData.transportNumber || '--'
              }}<MySvgIcon
                icon-class="icon-fuzhi"
                style="
                  margin-left: 4px;
                  color: #8a94a6;
                  font-size: 20px;
                  cursor: pointer;
                "
                @click="handleCopy(redeemSendData.transportNumber || '--')"
            /></span>
          </div>
          <div class="form-item">
            <span class="label">车牌号</span>
            <span class="value light">{{
              redeemSendData.carNumber || '--'
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">送货人</span>
            <span class="value light">{{
              `${redeemSendData.givePeople}|${redeemSendData.givePeoplePhone}`
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">发货凭证</span>
            <span class="value">
              <n-button
                type="info"
                text
                @click="handleViewDeliveryProof"
                :loading="viewDeliveryProofBtnLoading"
                >查看凭证</n-button
              >
            </span>
          </div>
        </template>
        <!-- 待验收 -->
        <template
          v-if="
            pageData.status === 10 ||
            (pageData.status === 13 && pageData.extractType !== '自提')
          "
        >
          <div class="form-item">
            <span class="label">到货地址</span>
            <span class="value light">{{
              redeemSendData.arrivalAddress || '--'
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">到货时间</span>
            <span class="value light">{{
              redeemSendData.arrivalTime || '--'
            }}</span>
          </div>
        </template>
        <!-- 待提货 -->
        <template v-if="pageData.status === 12">
          <div class="form-item">
            <span class="label">提货人</span>
            <span class="value light">{{
              `${pageData.redeemUser.username} | ${pageData.redeemUser.idCard} | ${pageData.redeemUser.phone}`
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">车牌号</span>
            <span class="value light">{{
              pageData.redeemUser.licensePlate
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">提货日期</span>
            <span class="value light">{{
              pageData.redeemUser.arriveTime || '--'
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">提货单</span>
            <span class="value">
              <n-button type="info" text @click="handleViewProof"
                >查看</n-button
              >
              <n-button
                type="info"
                text
                @click="handleViewProof"
                style="margin-left: 8px"
                >下载</n-button
              ></span
            >
          </div>
        </template>
        <!-- 收货 -->
        <template
          v-if="pageData.status === 13 && pageData.extractType === '自提'"
        >
          <div class="form-item">
            <span class="label">签收人</span>
            <span class="value light">{{
              respData.receivedPeople || '--'
            }}</span>
          </div>
          <div class="form-item">
            <span class="label">签收时间</span>
            <span class="value light">{{ respData.receivedTime || '--' }}</span>
          </div>
        </template>
      </div>
      <!-- 赎货申请审核中不展示 -->
      <PaymentDetail
        class="payment-detail-container"
        v-if="chuanruStatus !== 1 && pageData.ready"
        :data="pageData"
        :respData="respData"
        @handleViewPayProof="handleViewPayProof"
      />
    </div>
  </div>
  <PdfView ref="pdfView" />
</template>

<script lang="ts">
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'

export default {
  name: 'financingDemand',
}

// 宽度需要加上左边距12，右边距12；共24px
const columns = [
  {
    title: '货品信息',
    key: 'custom-goods',
    width: 300,
  },
  {
    title: '采购单价',
    dataIndex: 'purchasePrice',
  },
  {
    title: '融资单价',
    dataIndex: 'financingPrice',
  },
  {
    title: '赎货数量',
    dataIndex: 'num',
  },
  {
    title: '单位',
    dataIndex: 'unit',
  },
]
</script>
<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { NButton } from 'naive-ui'
import dayjs from 'dayjs'
import PaymentDetail from './PaymentDetail.vue'
import { formatMoney } from '@/utils/utils'
import GoodsContainer from '@/views/user/Foreclosure/components/GoodsContainer/index.vue'
import PdfView from '@/components/FilePreview/index.vue'
import Clipboard from 'clipboard'
import { requestDictMap, requestAttachDetail } from '@/api/common'

const props = defineProps({
  detailData: {
    type: Object,
    default: () => {},
  },
  respOriginData: {
    type: Object,
    default: () => {},
  },
  chuanruStatus: {
    type: Number,
  },
})
const pdfView = ref<any>(null)
const pageData = ref<any>({
  ready: false,
})
const respData = ref<any>({
  ready: false,
})
const redeemSendData = ref<any>({})
const tableData = ref<any>([])
const dicMap = reactive<any>({
  logisticsTypeOrigin: {},
  logisticsTypeMap: {},
})
const viewDeliveryProofBtnLoading = ref<boolean>(false)

watch(
  () => props.detailData,
  (data: any) => {
    // 数据格式化
    data.redeemUser = data.redeemUser || {}
    data.financingAddress = data.financingAddress || {}
    if (data.redeemUser.arriveTime) {
      data.redeemUser.arriveTime = dayjs(
        data.redeemUser.arriveTime,
        'YYYY-MM-DD'
      ).format('YYYY.MM.DD')
    }
    if (data.warehouseInDate) {
      data.warehouseInDate = dayjs(data.warehouseInDate, 'YYYY-MM-DD').format(
        'YYYY.MM.DD'
      )
    }
    data.originPurchasePrice = data.purchasePrice
    data.purchasePrice = formatMoney(data.purchasePrice)
    data.originFinancingPrice = data.financingPrice
    data.financingPrice = formatMoney(data.financingPrice)

    // 货品表格数据
    // tableData.value = [
    //   {
    //     purchaseUnitPrice: '￥' + data.purchasePrice,
    //     financingUnitPrice: '￥' + data.financingPrice,
    //     quantity: data.num,
    //     unit: data.goodsUnitValue || '未设置单位',
    //   },
    // ]
    tableData.value = data.redeemCommodityVOS
    data.ready = true
    pageData.value = data
  }
)
watch(
  () => props.respOriginData,
  (data: any) => {
    data.redeemDetailCargoCurrencyVO = data.redeemDetailCargoCurrencyVO || {}
    const respRedeemSendData = data.redeemDetailCargoCurrencyVO.redeemSend || {}

    // 数据格式化
    if (data.createTime) {
      data.createTime = dayjs(data.createTime, 'YYYY-MM-DD HH:mm:ss').format(
        'YYYY.MM.DD HH:mm:ss'
      )
    }
    if (respRedeemSendData.sendGoodsTime) {
      respRedeemSendData.sendGoodsTime = dayjs(
        respRedeemSendData.sendGoodsTime,
        'YYYY-MM-DD HH:mm:ss'
      ).format('YYYY.MM.DD HH:mm:ss')
    }
    if (data.receivedTime) {
      data.receivedTime = dayjs(
        data.receivedTime,
        'YYYY-MM-DD HH:mm:ss'
      ).format('YYYY.MM.DD HH:mm:ss')
    }
    data.actualAmount = formatMoney(data.actualAmount)

    data.ready = true
    respData.value = data
    redeemSendData.value = respRedeemSendData
  }
)

// 初始化字典数据
requestDictMap('logistics_code')
  .then(({ data }) => {
    if (data.success) {
      data = data.data
      const logisticsTypeMap: any = {}
      for (const item of data) {
        logisticsTypeMap[item.dictKey] = item.dictValue
      }
      logisticsTypeMap.ready = true
      dicMap.logisticsTypeOrigin = data
      dicMap.logisticsTypeMap = logisticsTypeMap
    }
  })
  .catch(() => {})

// 查看图片
const handleViewImg = (imgArr: string[] = []) => {
  // if (imgUrl.toLowerCase().includes('pdf')) {
  //   pdfView.value.handleOpen(imgUrl)
  // } else {
  viewerApi({
    options: {
      toolbar: false,
      navbar: false,
      title: false,
    },
    // images: [imgUrl],
    images: imgArr,
  })
  // }
}

// 查看付款凭证
const handleViewPayProof = () => {
  const imgArr = []
  for (const item of pageData.value.manAttachList || []) {
    imgArr.push(item.link)
  }
  for (const item of pageData.value.plaAttachList || []) {
    imgArr.push(item.link)
  }
  handleViewImg(imgArr)
}

// 查看发货凭证
const handleViewDeliveryProof = () => {
  viewDeliveryProofBtnLoading.value = true
  requestAttachDetail({ id: pageData.value.redeemSend?.deliveryDocument })
    .then(({ data }) => {
      if (data.success) {
        data = data.data
        handleViewImg([data.link])
      }
    })
    .catch(() => {})
    .finally(() => {
      viewDeliveryProofBtnLoading.value = false
    })
}

// 复制融资编号
const handleCopy = (copyValue: any) => {
  const clipboard = new Clipboard('body', {
    text: () => copyValue,
  })
  clipboard.on('success', () => {
    message.success('复制成功')
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    console.error(e)
    // 不支持复制
    message.warning('该浏览器不支持自动复制')
    clipboard.destroy()
  })
}
</script>

<style lang="scss" scoped>
.detail-info-container {
  .table-container {
    margin-bottom: 32px;
  }

  .detail-container {
    display: flex;
    justify-content: space-between;

    .form-container {
      padding-bottom: 24px;

      .form-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .label {
          width: 140px;
          flex-shrink: 0;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #0a1f44;
          line-height: 20px;
        }

        .value {
          max-width: 75%;
          font-size: 14px;
          font-family: SFProText-Semibold, SFProText;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;

          &.sign {
            font-family: CoreSansD55Bold;
            font-size: 12px;
            color: inherit;
          }

          &.light {
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
          }

          &.red {
            color: #dd2727;
          }

          &.link {
            font-family: SFProText-Regular, SFProText;
            font-weight: 400;
            color: #0d55cf;
          }

          &.tip {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #dd2727;
          }
        }

        .btn {
          margin-left: 8px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #0d55cf;
          line-height: 20px;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    // .payment-detail-container {
    // }
  }
}

:deep(.ant-table) {
  background: transparent;

  .ant-table-cell {
    padding: 10px 24px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td,
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    background: transparent;
  }

  .ant-table-thead > tr > th {
    background: #f8f9fb;
  }
}
</style>
