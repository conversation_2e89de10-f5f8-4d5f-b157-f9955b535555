<template>
  <div class="base-detail-card">
    <div class="base-detail-card-left-mask"></div>
    <div class="base-detail-card-right-mask"></div>

    <div class="base-detail-card-main">
      <div class="base-detail-card-main-left">
        <span class="base-detail-card-status">
          <MySvgIcon
            :icon-class="iconClassArr[activeIndex]"
            class="base-detail-card-icon"
            :style="{ fill: iconColorArr[activeIndex] }"
          ></MySvgIcon>
          <span
            class="base-detail-card-name"
            :style="{ color: titleColorArr[activeIndex] }"
          >
            {{ titleNameArr[activeIndex] }}
          </span>
        </span>
        <span class="base-detail-card-subtitle">
          <slot name="subTitle" />
        </span>
      </div>

      <div class="base-detail-card-main-right">
        <slot name="button" />
      </div>
    </div>
  </div>
</template>

<script>
import { defineProps } from '@vue/runtime-core'
export default {
  name: 'BaseDetailHeadCard',
}
</script>

<script setup>
defineProps({
  // 图标类型数组
  iconClassArr: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 图标颜色数组
  iconColorArr: {
    type: Array,
    required: true,
    default: () => [],
  },
  titleNameArr: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 标题颜色数组
  titleColorArr: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 当前激活状态
  activeIndex: {
    type: Number,
    required: true,
    default: 0,
  },
})
</script>

<style lang="scss" scoped>
.base-detail-card {
  position: relative;
  width: 100%;
  padding: 28px 40px;
  border: 1px solid #efefef;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
  .base-detail-card-left-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 212px;
    height: 80px;
    background: #0c66ff 100%;
    opacity: 0.1;
    filter: blur(24px);
  }
  .base-detail-card-right-mask {
    position: absolute;
    right: 0;
    top: 0;
    width: 502px;
    height: 80px;
    background: #0c66ff 100%;
    opacity: 0.1;
    filter: blur(49px);
  }
  .base-detail-card-main {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.1);
    .base-detail-card-main-left {
      display: flex;
      align-items: center;
      flex-flow: row nowrap;
      .base-detail-card-status {
        display: flex;
        align-items: center;
        margin-right: 12px;
        flex-shrink: 0;
        .base-detail-card-icon {
          font-size: 20px;
          margin-right: 2px;
        }
        .base-detail-card-name {
          display: block;
          height: 24px;
          line-height: 24px;
          font-size: 16px;
          font-weight: 600;
          font-family: PingFangSC-Semibold, PingFang SC;
        }
      }
      .base-detail-card-subtitle {
        padding-left: 12px;
        display: block;
        color: #8a94a6;
        border-left: 1px solid #b5bbc6;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }
    .base-detail-card-main-right {
      display: flex;
      align-items: center;
      & :deep(span) {
        cursor: pointer;
        display: block;
        height: 24px;
        line-height: 24px;
        font-size: 16px;
        font-weight: 500;
        font-family: PingFangSC-Medium, PingFang SC;
      }
    }
  }
}
</style>
