<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="list => (selectionList = list)"
      @sort-change="sortChange"
      @refresh-change="onLoad(page, query)"
      @on-load="onLoad"
      v-model="form"
      ref="crud"
    >
      <!-- 操作菜单 -->
      <template slot="menuLeft">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="openContent()">新增</el-button>
        <el-button type="success" size="small" icon="el-icon-top" plain @click="batchShelf(true)">上架 </el-button>
        <el-button type="warning" size="small" icon="el-icon-bottom" plain @click="batchShelf(false)">下架 </el-button>
      </template>

      <!-- 筛选Tab -->
      <template slot="header">
        <div class="order-header-container">
          <el-radio-group v-model="productType" size="small" @change="handleChange">
            <el-radio-button v-for="item of productTypeMap" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </template>

      <!-- 状态 -->
      <template slot="status" slot-scope="{ row }">
        <el-tag :type="row.status === 2 ? 'success' : 'info'">{{ row.$status }}</el-tag>
      </template>

      <!-- 产品类型 -->
      <template slot="type" slot-scope="{ row }">
        <el-tag :type="[1, 5].includes(row.type) ? 'danger' : ''" effect="plain">
          {{ defProductTypeMap[row.type - 1].label }}
        </el-tag>
      </template>

      <!-- 是否优质产品 -->
      <template slot="isHighQuality" slot-scope="{ row }">
        <el-tag :type="getDictObj('is_dict', row.isHighQuality).type" effect="plain">
          {{ getDictObj('is_dict', row.isHighQuality).label }}
        </el-tag>
      </template>

      <!-- 资金方 -->
      <template slot="capital" slot-scope="{ row }">
        <div class="capital-slot">
          <el-image style="width: 50px; height: 50px" :src="row.capitalLogo" fit="contain" />
          <span class="demonstration">{{ row.capitalName }}</span>
        </div>
      </template>

      <!-- 借款金融 -->
      <template slot="loanAmountStr" slot-scope="{ row }">
        <div>{{ row.loanAmountStart | formatMoney }} ~ {{ row.loanAmountEnd | formatMoney }}万</div>
      </template>

      <!-- 操作栏 -->
      <template slot-scope="{ row, index, type, size }" slot="menu">
        <template v-for="(item, i) of getButtons(row, index)">
          <el-button v-if="item.vIf" :key="i" :icon="item.icon" :size="size" :type="type" @click.stop="item.click">
            {{ item.label }}
          </el-button>
        </template>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import DataContent, { tableColumn, appendColumn, defSort, buttons } from './capitalProduct.data'
import { requestListSystemProductType } from '@/api/wei/wei.js'
import { mapGetters } from 'vuex'
import { getSortColumnObj } from '@/util/util'

export default {
  data() {
    return {
      ...DataContent,
    }
  },
  computed: {
    // 权限
    ...mapGetters(['permission']),
    /**
     * 权限列表 - 按钮权限
     * TODO: 目前统一使用的应收帐按钮权限，待优化
     */
    permissionList() {
      // vaildData为AVue中全局API方法，验证是否为空，为空则拿参数二（默认值）
      const auth = key => this.vaildData(this.permission[`goods_${key}`], false)
      // 对“添加，查看，删除，编辑”按钮进行权限校验
      return ['add', 'view', 'delete', 'edit'].reduce((obj, key) => ((obj[`${key}Btn`] = auth(key)), obj), {})
    },
    /**
     * 获取产品类型对象
     */
    productTypeObj() {
      return this.productTypeMap.find(item => item.value === this.productType)
    },
  },
  created() {
    // 初始化产品权限
    this.productTypeAuth()
  },
  methods: {
    /**
     * 产品类型权限，根据参数管理中的GOODS_TYPE_LIST配置来显示对应的权限
     */
    async productTypeAuth() {
      // 获取权限数值
      const { data: resData } = await requestListSystemProductType()
      const auth = resData.data
      // 将对应存在的数值加进
      this.productTypeMap = this.defProductTypeMap.reduce((_p, _c) => {
        if (auth.includes(_c.key)) _p.push(_c)
        return _p
      }, [])
    },
    // 获取操作按钮
    getButtons(row, index) {
      // 根据索引追加事件
      const btnClicks = [
        () => this.openContent(row, 'true'),
        () => this.openContent(row, 'false'),
        () => this.$refs.crud.rowDel(row, index),
        () => this.rowShelf(row.id, true),
        () => this.rowShelf(row.id, false),
        () => this.copyGoods(row.id),
      ]
      return buttons(row, this).map((item, i) => ({
        click: btnClicks[i],
        vIf: true,
        ...item,
      }))
    },
    /**
     * 获取字典值
     * @param code 字典code
     * @param value 字典值
     */
    getDictObj(code, value) {
      return this.dict[code].find(item => item.value === value)
    },

    /**
     * 打开弹窗（新增，查看，编辑）
     */
    openContent(row = {}, look = '') {
      const { id } = row
      sessionStorage.setItem('look', look)
      this.$router.push({ path: this.productTypeObj.path, query: id ? { id } : {} })
    },

    /**
     * 产品类型切换功能，直接使用重置方法就行了
     */
    handleChange() {
      this.$refs.crud.searchReset()
    },

    /**
     * 加载数据
     * @param page 分页
     * @param params 参数
     */
    async onLoad(page, params = {}) {
      this.loading = true

      /**
       * 设置查询条件，根据产品类型获取索引并 + 1得到产品类型值
       * （产品列表是根据值排序的，如出现其他产品需要增加产品类型值字段）
       */
      this.query.typeEqual = this.productTypeObj.key

      // 获取API接口
      const { getList, canOperator } = this.api[this.productType]

      // 获取分页数据
      const { currentPage, pageSize } = page
      const param = Object.assign(params, this.query, this.sortColumn)
      const { data: resData } = await getList(currentPage, pageSize, param)
      const { data } = resData

      // 设置分页数据
      this.page.total = data.total
      this.data = data.records
      this.loading = false

      // 设置表格列内容, 当为云信的时候修改内容
      if (this.query.typeEqual === 3) {
        // 深拷贝
        const deepColumn = this.deepClone(tableColumn)
        // 追加列
        deepColumn.splice(7, 1, appendColumn)
        // 设置表格列内容
        this.option.column = deepColumn
      } else this.option.column = tableColumn

      // 清空选择
      this.selectionList.length > 0 && this.selectionClear()

      // 获取编辑显示权限
      const { data: operatorRes } = await canOperator()
      this.operator = operatorRes.data
    },

    /**
     * 初始化内容
     */
    initLoad() {
      this.page.currentPage = 1
      this.onLoad(this.page, this.query)
    },

    // 清空选中
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },

    // 查询
    searchChange(params, done) {
      this.query = params
      this.initLoad()
      done()
    },

    // 重置
    searchReset() {
      this.query = {}
      this.initLoad()
    },

    // 排序
    sortChange(val) {
      this.sortColumn = val.order ? getSortColumnObj(val) : defSort
      this.onLoad(this.page)
    },

    /**
     * 批量 - 上架/下架
     */
    batchShelf(isOn) {
      // 变量声明
      const msg = isOn ? '确定将选择产品上架吗' : '确定下架该商品吗？此操作会下架其关联的轮播图'
      const ids = this.selectionList.map(item => item.id).join()
      const { batchOnShelf, offShelf } = this.api[this.productType]
      const selectSize = this.selectionList.length
      const api = isOn ? batchOnShelf : offShelf

      // 必须选择数据
      if (selectSize === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }

      // 调用上下架方法
      this.handleMsgApi({
        msg,
        api,
        param: ids,
        isBatch: true,
        calSucMsg: num => {
          // 成功提示内容
          let sucMsg = isOn ? `成功上架${num}个产品` : '操作成功'
          const remLength = selectSize - num
          isOn && remLength > 0 && (sucMsg += `还有${remLength}个产品数据未填写完整`)
          return sucMsg
        },
      })
    },

    /**
     * 单个上下架
     * @param id
     * @param isOn
     */
    rowShelf(id, isOn) {
      // 变量声明
      const msg = isOn ? '确定将选择产品上架吗' : '确定下架该商品吗？此操作会下架其关联的轮播图'
      const { onShelf, offShelf } = this.api[this.productType]
      const api = isOn ? onShelf : offShelf

      // 调用上下架方法
      this.handleMsgApi({ msg, api, param: id })
    },

    /**
     * 公共消息接口方法
     * @param msg 提示消息
     * @param api 接口
     * @param param 参数
     * @param isBatch 是否批量
     * @param calSucMsg 如果批量的话需要回调成功消息 - 定制
     */
    async handleMsgApi({ msg, api, param, isBatch, calSucMsg }) {
      // 消息提示
      await this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      // 调用接口
      const { data: resData } = await api(param)

      const sucMsg = calSucMsg ? calSucMsg(resData.data) : '操作成功!'

      // 刷新数据并成功提示
      this.onLoad(this.page)
      this.$message({ type: 'success', message: sucMsg })
      isBatch && this.selectionClear()
    },

    /**
     * 复制产品
     * @param id
     */
    copyGoods(id) {
      const msg = '请问是否确认复制该产品?'
      const api = this.api[this.productType].copy
      const calSucMsg = () => '复制成功！'
      this.handleMsgApi({ msg, api, param: id, calSucMsg })
    },

    /**
     * 删除产品
     * @param row
     */
    rowDel(row) {
      const msg = '是否确定将选择数据删除?'
      const api = this.api[this.productType].remove
      this.handleMsgApi({ msg, api, param: row.id })
    },
  },
}
</script>

<style lang="scss" scoped>
.order-header-container {
  width: 100%;
  height: 32px;
  border: 1px solid #f3f3f3;
  background-color: #ffff;

  .el-radio-group {
    height: 100%;

    .el-radio-button {
      height: 100%;
    }

    .el-radio-button__inner {
      border: none !important;
      height: 100%;
    }
  }
}

.capital-slot {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>
