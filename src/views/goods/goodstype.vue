<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="addPraent()"
          >新增
        </el-button>
      </template> -->
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click.stop="handleAdd(scope.row, scope.index)"
          v-if="scope.row.parentId == 0 && permission.goodstype_add"
          >新增子项
        </el-button>
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row, index)"
          size="small"
          >编辑</el-button
        >
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="deletePraent(scope.row, index)"
          size="small"
          >删除</el-button
        >
        <!-- <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row, index)"
          v-if="permission.goodstype_delete && scope.row.parentId != 0"
          size="small"
          >删除
        </el-button> -->
      </template>
      <template slot="menuLeft"></template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  remove,
  update,
  addParentApi,
  addPraent,
} from '@/api/goods/goodstype'
import { mapGetters } from 'vuex'
import { API, BLADE_GOODS, WEB_BACK } from '@/config/apiPrefix'
import { Loading } from 'element-ui';

export default {
  data() {
    return {
      apiPrefix: API + BLADE_GOODS + WEB_BACK,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        addBtn: true,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        selection: false,
        menuWidth: 260,
        dialogClickModal: false,
        column: [
          {
            label: '分类名称',
            prop: 'name',
            rules: [
              {
                required: true,
                message: '请输入分类名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上级分类',
            prop: 'parentId',
            type: 'select',
            dicUrl: `/api/blade-goods/web-back/goods/goodstype/listDetails`,
            hide: true,
            display: false,
            editDisabled: true,
            addDisabled: true,
            props: {
              label: 'name',
              value: 'id',
            },
            rules: [
              {
                required: true,
                message: '请输入分类名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '排序',
            prop: 'sort',
            type: 'number',
            minRows: 0,
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '产品序号',
            prop: 'typeNo',
            type: 'number',
            minRows: 0,
            display: true,
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '关联类型',
            prop: 'goodsType',
            type: 'select',
            minRows: 0,
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=product_group_related',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.goodstype_add, false),
        viewBtn: this.vaildData(this.permission.goodstype_view, false),
        delBtn: this.vaildData(this.permission.goodstype_delete, false),
        editBtn: this.vaildData(this.permission.goodstype_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    deletePraent(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleAdd(row) {
      const column = this.findObject(this.option.column, 'parentId')
      const typeNo = this.findObject(this.option.column, 'typeNo')
      this.form.parentId = row.id
      column.addDisabled = true
      typeNo.display = false
      this.$refs.crud.rowAdd()
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    rowSave(row, done, loading) {
      console.log('row', row)
      if (
        row.typeNo === undefined ||
        row.typeNo === null ||
        row.typeNo.length === 0
      ) {
        //增加子类
        add(row).then(
          () => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          },
          error => {
            loading()
            window.console.log(error)
          }
        )
      } else {
        //增加父类
        row.parentId = 0
        addPraent(row).then(
          () => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          },
          error => {
            loading()
            window.console.log(error)
          }
        )
      }
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        const loading = Loading.service({
          lock: true,
          text: '加载中，请稍等...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
          // 产品组关联类型
          const goodsType = this.findObject(this.option.column, 'goodsType')
          goodsType.display = false
          goodsType.viewDisplay = false
          if (this.form.typeNo == 99 && this.form.parentId != "0") {
            goodsType.display = true
            goodsType.viewDisplay = true
          }
          this.form.goodsType += ''

          // 延迟打开，防止屏闪
          setTimeout(() => {
            done()
            loading.close()
          }, 500)
        }).catch(error => {
          loading.close()
          window.console.log(error)
        })
      } else {
        done()
      }
    },
    beforeClose(done, type) {
      const typeNo = this.findObject(this.option.column, 'typeNo')
      typeNo.display = true
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        console.log(res.data.data)
        const data = res.data.data
        this.data = data
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
