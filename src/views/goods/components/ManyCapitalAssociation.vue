<template>
  <el-dialog title="关联产品" append-to-body :visible.sync="visible" width="60%" class="avue-dialog avue-dialog--top">
    <el-table
      ref="table"
      row-key="goodsId"
      height="300"
      v-loading="tableLoading"
      :data="dataSource"
      :row-style="{ cursor: 'pointer' }"
      @selection-change="handleSelectionChange"
      @row-click="rowClick"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="goodsCode" label="产品编号" align="center" />
      <el-table-column prop="goodsName" label="产品名称" align="center" />
      <el-table-column label="资金方" align="center">
        <!-- 资金方 -->
        <template slot-scope="{ row }">
          <div class="capital-slot">
            <el-image style="width: 40px; height: 40px; margin-right: 10px" :src="row.capitalLogo" fit="contain" />
            <span class="demonstration">{{ row.capitalName }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部按钮 -->
    <div class="avue-dialog__footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button @click="handleSubmit" type="primary" :loading="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getRelationById, getAffiliatedProduct, commit } from '@/api/goods/manyCapitalProducts'
import { goodstypeDetail } from '@/api/goods/product-group'
export default {
  data() {
    return {
      visible: false,
      tableLoading: false,
      submitLoading: false,
      dataSource: [],
      selectList: [],
      id: null,
    }
  },
  methods: {
    /**
     * 打开前回调
     * @param {Object} record 当前行数据
     */
    async beforeOpen(record) {
      const { id, repaymentType, goodsTypeId } = record

      // 赋值
      this.visible = true
      this.tableLoading = true
      this.id = id
      this.selectList = []

      // 获取产品类型
      const { data: goodsTypeRes } = await goodstypeDetail({ id: goodsTypeId })
      const { goodsType } = goodsTypeRes.data

      // 获取未关联数据
      const affiliatedParams = { goodsType, repaymentType }
      const { data: affiliatedRes } = await getAffiliatedProduct(affiliatedParams)
      this.dataSource = affiliatedRes.data

      // 获取已关联数据并勾选 @returns Promise<{ data: string[] }>
      const { data: relationRes } = await getRelationById(id)
      // 获取关联的ids
      const relationsIds = relationRes.data

      this.handleSelectByIds(relationsIds)

      this.tableLoading = false
    },

    handleSelectByIds(ids) {
      // 过滤已经关联的数据内容，在将其存储
      // 注：必须拿源数据过滤，因为toggleRowSelection拿的是源数据的内存地址指向
      const defSelectList = this.dataSource.filter(item => {
        return ids.includes(item.id)
      })

      // 勾选已关联数据，将源数据中过滤出来的数据进行勾选
      defSelectList.forEach(item => {
        this.$refs.table.toggleRowSelection(item, true)
      })
    },

    // 监听选择
    handleSelectionChange(rows) {
      this.selectList = rows
    },

    // 点击行选择
    rowClick(row) {
      this.$refs.table.toggleRowSelection(row)
    },

    /**
     * 提交关联
     */
    async handleSubmit() {
      try {
        if (!this.selectList.length) {
          this.$message.warning('请选择关联产品！')
          return
        }

        this.submitLoading = true

        const multiFundingAffiliatedProducts = this.selectList.map(({ id, goodsName }) => ({
          goodsId: id,
          groupId: this.id,
          goodsName,
        }))

        const { data: resData } = await commit({ multiFundingAffiliatedProducts })

        if (resData.data) {
          throw new Error(JSON.stringify(resData))
        }

        this.visible = false
        this.$message.success('关联成功！')
      } catch (error) {
        /**
         * 如果出现已经被客户开通了的产品进行解绑，后台会进行报错
         * 我们需要拿到后台返回的ids，进行重新勾选
         */
        const { data, msg } = JSON.parse(error.message)
        const ids = JSON.parse(data)
        if (ids && ids instanceof Array) {
          this.$message.warning(`${msg}已重新勾选！`)
          this.handleSelectByIds(data)
        }
      } finally {
        this.submitLoading = false
      }
    },
  },
}
</script>

<style scoped>
.capital-slot {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>
