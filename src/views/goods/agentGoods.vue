<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @sort-change="sortChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="toAddPage()"
        >新增
        </el-button>
        <el-button
          type="success"
          size="small"
          icon="el-icon-top"
          plain
          @click="batchOnShelf()"
        >上架
        </el-button>
        <el-button
          type="warning"
          size="small"
          icon="el-icon-bottom"
          plain
          @click="batchOffShelf()"
        >下架
        </el-button>
      </template>
      <template slot="status" slot-scope="{ row }">
        <el-tag type="info" v-if="row.status === 1">未上架</el-tag>
        <el-tag type="success" v-if="row.status === 2">已上架</el-tag>
        <el-tag type="info" v-if="row.status === 3">已下架</el-tag>
      </template>
      <template slot="type" slot-scope="{ row }">
        <el-tag type="danger" v-if="row.type === 1" effect="plain"
        >应收账款质押</el-tag
        >
        <el-tag type="" v-if="row.type === 2" effect="plain">代采融资</el-tag>
      </template>
      <template slot="isHighQuality" slot-scope="{ row }">
        <el-tag type="success" v-if="row.isHighQuality == 1" effect="plain"
        >是</el-tag
        >
        <el-tag type="danger" v-if="row.isHighQuality == 2" effect="plain">否</el-tag>
      </template>
      <template slot="capital" slot-scope="{ row }">
        <div class="capital-slot">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.capitalLogo"
            fit="contain"
          ></el-image>
          <span class="demonstration">{{ row.capitalName }}</span>
        </div>
      </template>
      <template slot="loanAmountStr" slot-scope="{ row }">
        <div>{{ row.loanAmountStart | formatMoney }} ~ {{ row.loanAmountEnd | formatMoney }}万</div>
      </template>
      <template slot-scope="{ row, index, type, size }" slot="menu">
        <el-button
          icon="el-icon-view"
          :size="size"
          :type="type"
          @click.stop="lookTo(row)"
        >查看</el-button
        >
        <el-button
          icon="el-icon-edit"
          v-if="row.status === 1 "
          :size="size"
          :type="type"
          @click.stop="goTo(row)"
        >编辑</el-button
        >
        <el-button
          icon="el-icon-edit"
          v-if="
            !operator
          "
          :size="size"
          :type="type"
          @click.stop="goTo(row)"
        >编辑
        </el-button>
        <el-button
          icon="el-icon-del"
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click.stop="$refs.crud.rowDel(row, index)"
        >删除</el-button
        >
        <el-button
          icon="el-icon-top"
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click="rowOnShelf(row)"
        >上架</el-button
        >
        <el-button
          icon="el-icon-bottom"
          v-if="row.status === 2"
          :size="size"
          :type="type"
          @click="rowOffShelf(row)"
        >下架</el-button
        >
        <el-button
          :size="size"
          :type="type"
          @click="copyGoods(row)"
        >复制
        </el-button>
        <!-- <el-button
          icon="el-icon-star-off"
          v-if="row.isHighQuality === 2 && row.status === 2"
          :size="size"
          :type="type"
          @click="setIsHighQuality(row,1)"
        >设置优质产品</el-button>
        <el-button
          icon="el-icon-star-on"
          v-if="row.isHighQuality === 1"
          :size="size"
          :type="type"
          @click="setIsHighQuality(row,2)"
        >取消优质产品</el-button> -->
      </template>
    </avue-crud>
    <el-dialog
      title="选择新增类型"
      :visible.sync="dialogDataType"
      :append-to-body="true"
    >
      <div class="dialog-body">
        <div class="dialog-chasing" @click="chasing()">
          <i class="el-icon-s-order"></i>
          <div>应收账款</div>
        </div>
        <div class="dialog-mation" @click="mation()">
          <i class="el-icon-shopping-cart-full"></i>
          <div>代采融资</div>
        </div>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail,  update, remove,batchOnShelf, onShelf, offShelf, setIsHighQuality,canOperator,
  copy} from "@/api/goods/agentgoods";
import { getList as getProcessList } from "@/api/plugin/workflow/deployment";
import {getList as getGoodsProcessList,add as saveGoodsProcess} from "@/api/goods/goodsprocess";
import {all} from "@/api/plugin/workflow/category"
import {mapGetters} from "vuex";
import {getSortColumnObj} from "@/util/util";

export default {
  data() {
    return {
      dialogDataType: false,
      form: {},
      processQuery: {},
      loading: true,
      query: {},
      processLoading: true,
      sortColumn: {updateTimeDesc:'updateTimeDesc'},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      processPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      processListVisible: false,
      processDialogFormVisible: false,
      processList: [],
      processForm: {},
      processCategoryMap: {},
      processSelectionList: [],
      currentGoodsId: '',
      selectionList: [],
      goodsStatusList: [],
      processOption: {
        menuBtn: false,
        column: [
          {
            label: '',
            prop: 'processList',
            type: 'dynamic',
            span:24,
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd:(done)=>{
                this.openProcessListWindow();
              },
              rowDel:(row,done)=>{
                done()
              },
              column: [
                {
                  label: '流程名称',
                  prop: "name",
                  slot: true
                },
                {
                  label: "分类",
                  prop: "category",
                  overHidden: true,
                  type: 'select',
                  slot: true,
                  props: {
                    label: 'name',
                    value: 'id'
                  },
                },
                {
                  label: '流程标识',
                  prop: "processType",
                  dicUrl: '/api/blade-system/dict-biz/dictionary?code=business_process_type',
                  type: "select",
                  dataType: "number",
                  props: {
                    label: 'dictValue',
                    value: 'dictKey'
                  },
                },
              ]
            }
          },
        ]
      },
      processListOption: {
        size: 'mini',
        searchSize: 'mini',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        selection: true,
        dialogType: 'drawer',
        addBtn: false,
        editBtn: false,
        align: 'center',
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        column: [
          {
            label: "流程名称",
            prop: "name",
            overHidden: true,
            search: true
          },
          {
            label: "分类",
            prop: "category",
            overHidden: true,
            type: 'select',
            //dicUrl: '/api/blade-workflow/design/category/all',
            dicData: [],
            props: {
              label: 'name',
              value: 'id'
            },
          },
        ]
      },
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        align: 'center',
        dialogClickModal: false,
        selectionFixed: false,
        indexFixed: false,
        menuFixed: 'right',
        column: [
          {
            label: '产品编号',
            prop: 'goodsCode',
            width: '125',
            search: true,
            rules: [
              {
                required: true,
                message: "请输入产品编号",
                trigger: "blur"
              }]
          },
          {
            label: "产品类型",
            prop: "type",
            width: "115",
            slot: true,
            rules: [{
              required: true,
              message: "请输入产品类型",
              trigger: "blur"
            }]
          },
          {
            label: "产品名称",
            prop: "goodsName",
            width: '125',
            search: true,
            rules: [{
              required: true,
              message: "请输入产品名称",
              trigger: "blur"
            }]
          },

          {
            label: "所属资方",
            prop: "capital",
            width: '150',
            slot: true,
            rules: [{
              required: true,
              message: "请输入资金方id",
              trigger: "blur"
            }]
          },
          {
            label: "借款金额(万)",
            prop: "loanAmountStr",
            width: '140',
            rules: [{
              required: true,
              message: "请输入借款金额",
              trigger: "blur"
            }]
          },
          {
            label: "借款期限",
            prop: "loadTermStr",
            rules: [{
              required: true,
              message: "请输入借款期限",
              trigger: "blur"
            }]
          },
          {
            label: "优质产品",
            prop: "isHighQuality",
            slot: true,
          },
          {
            label: "上次修改时间",
            width: '135',
            prop: "updateTime",
            sortable: true,
            format: 'yyyy-MM-dd HH-mm-ss',
            display: false
          },
          {
            label: "操作人",
            prop: "operator",
            width: '125',
          },
          {
            label: "状态",
            prop: "status",
            fixed: 'right',
            sortable: true,
            search: true,
            display: false,
            type: "select",
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_status',
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          }
        ]
      },
      data: [],
      processData: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.goods_add, false),
        viewBtn: this.vaildData(this.permission.goods_view, false),
        delBtn: this.vaildData(this.permission.goods_delete, false),
        editBtn: this.vaildData(this.permission.goods_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    copyGoods(row) {
      this.$confirm('确定操作?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return copy(row.id)
        })
        .then(resp => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功,复制成功',
          })
        })
    },
    sortChange(val) {
      this.sortColumn= getSortColumnObj(val)
      this.onLoad(this.page)
    },
    processSearchReset() {
      this.processPage = {};
      this.processOnLoad(this.processPage);
    },
    processSearchChange(params, done) {
      this.processQuery = params;
      this.processOnLoad(this.processPage, params);
      done()
    },
    processSelectionChange(list) {
      this.processSelectionList = list;
    },
    processCurrentChange(currentPage) {
      this.processPage.currentPage = currentPage;
    },
    processSizeChange(pageSize) {
      this.processPage.pageSize = pageSize;
    },
    processOnLoad(page, params = {}) {
      this.processLoading = true;

      if (this.categoryId) params['category'] = this.categoryId
      else delete params['category']

      getProcessList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.processPage.total = data.total;
        this.processData = data.records;
        this.processLoading = false;
      });
    },
    chasing() {
      this.$router.push({
        path: '/pcontrol/purchasing',
      })
    },
    mation() {
      this.$router.push({
        path: '/pcontrol/pinformation',
      })
    },
    lookTo(row) {
      if (row.type == 2) {
        this.$router.push({
          path: '/pcontrol/pinformation',
          query: { id: row.id },
        })
        sessionStorage.setItem('look', 'true')
      } else {
        this.$router.push({
          path: '/pcontrol/purchasing',
          query: { id: row.id },
        })
        sessionStorage.setItem('look', 'true')
      }
    },
    goTo(row) {
      if (row.type == 2) {
        this.$router.push({
          path: '/pcontrol/pinformation',
          query: { id: row.id },
        })
        sessionStorage.setItem('look', 'false')
      } else {
        this.$router.push({
          path: '/pcontrol/purchasing',
          query: { id: row.id },
        })
        sessionStorage.setItem('look', 'false')
      }
    },
    toAddPage() {
      sessionStorage.setItem('look', '')
      this.$router.push({
        path: '/pcontrol/pinformation',
      })
    },
    rowOnShelf(row) {
      this.$confirm('确定将选择产品上架吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return onShelf(row.id)
        })
        .then(resp => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功',
          })
        })
    },
    openBindProcessWindow(row) {
      this.goodsProcessList(row.id);
      all().then(resp => {
        let arr = resp.data.data;
        arr.forEach(item => {
          this.processCategoryMap[item.id] = item.name
        })
        this.findObject(this.processListOption.column,'category').dicData = arr;
      })
      this.processDialogFormVisible = true;
      this.currentGoodsId = row.id;
    },
    openProcessListWindow() {
      this.goodsProcessList(this.currentGoodsId);
      this.processListVisible = true;
    },
    goodsProcessList(goodsId) {
      getGoodsProcessList({goodsId: goodsId}).then(resp => {
        this.processForm.processList = resp.data.data;
      })
    },
    selectProcess() {
      this.processSelectionList.forEach(ele => {
        this.processForm.processList.push({name: ele.name,category: ele.category,processKey: ele.key})
      });
      this.processListVisible = false;
    },

    bindProcess() {
      let goodsProcessList = [];
      this.processForm.processList.forEach(ele => {
        goodsProcessList.push({processKey: ele.processKey,processType: ele.processType});
      });
      saveGoodsProcess({goodsId: this.currentGoodsId,goodsProcessList: goodsProcessList}).then(() => {
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.processListVisible = false;
          this.processDialogFormVisible = false;
          this.goodsProcessList(this.currentGoodsId)
        },
        error => {
          loading()
          window.console.log(error)
        })
    },
    setIsHighQuality(row, isHighQuality) {
      this.$confirm("确定要将该产品设置成优质产品名?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let param = {id: row.id, isHighQuality: isHighQuality}
          return setIsHighQuality(param);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功"
          });
        });
    },
    rowOffShelf(row) {
      this.$confirm('确定下架该商品吗？此操作会下架其关联的轮播图', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return offShelf(row.id)
        })
        .then(resp => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功',
          })
        })
    },
    batchOffShelf() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定下架该商品吗？此操作会下架其关联的轮播图', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return offShelf(this.ids)
        })
        .then(resp => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    batchOnShelf() {
      let size = this.selectionList.length
      if (size === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择产品上架吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return batchOnShelf(this.ids)
        })
        .then(resp => {
          let msg = `成功上架${resp.data.data}个产品`
          if (size - resp.data.data > 0) {
            msg = `${msg}，还有${size - resp.data.data}个产品数据未填写完整`
          }

          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: msg,
          })
          this.$refs.crud.toggleSelection()
        })
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      this.query.typeEqual = 2;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query,this.sortColumn)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
      canOperator().then(res => {
        this.operator = res.data.data;
        console.log(this.operator)
      })
    },
  },

}
</script>

<style lang="scss" scoped>
.capital-slot {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.dialog-body {
  display: flex;

  .dialog-chasing {
    width: 140px;
    height: 140px;
    margin-left: 1%;
    border-radius: 10px;
    background-color: rgba(255, 105, 105, 100);
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    i.el-icon-s-order {
      font-size: 785%;
    }
  }

  .dialog-mation {
    width: 140px;
    height: 140px;
    margin-left: 2.5%;
    border-radius: 10px;
    background-color: rgba(105, 124, 255, 100);
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    i.el-icon-shopping-cart-full {
      font-size: 785%;
    }
  }
}
</style>
