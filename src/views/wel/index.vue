<template>
  <div class="home-page">
    <header>
      <div class="top-left-box">
        <div class="switchover-box">
          <el-tabs
            type="border-card"
            v-model="tagChangeObj.editableTabsValue"
            @tab-click="handleClick"
          >
            <el-tab-pane label="天" name="1" />
            <el-tab-pane label="月" name="2" />
            <el-tab-pane label="年" name="3" />
          </el-tabs>
          <div class="switch-title">信贷报表</div>
          <div class="more-box" @click="handleLookReport">
            <span> 查看报表 </span>
            <svg-icon
              icon-class="icon-youjiantou"
              style="fill: #ffffff; font-size: 16px"
            />
          </div>
        </div>
        <div class="amount-box">
          <div id="echart-number"></div>
          <div id="echart-amount"></div>
        </div>
      </div>

      <div class="top-right-box">
        <div class="userinfo-box">
          <div class="img-userinfo">
            <img :src="userObj.avatar" alt="" />
          </div>
          <div class="name-userinfo">
            <div class="top-name">{{ userObj.name }}</div>
            <div class="bottom-name">{{ userObj.realName }}</div>
          </div>
        </div>
        <div class="institution-box">
          <span class="label-institution">机构</span>
          <span class="value-institution">{{ userObj.deptName }}</span>
        </div>
        <div class="orientation-box">
          <span class="label-orientation">职位</span>
          <span class="value-orientation">
            {{ userObj.postName }}
          </span>
        </div>
        <div class="orientation-box">
          <span class="label-orientation">角色</span>
          <span class="value-orientation">
            {{ userObj.roleName }}
          </span>
        </div>
        <div class="right-transverse-line" />
        <div class="right-menu-box">
          <span class="visiting-card">生成名片</span>
          <span class="enterprise-fun">邀请企业</span>
        </div>
      </div>
    </header>

    <main>
      <!-- 代办任务 -->
      <TaskAgents />
      <!-- 预警通知 -->
      <WarningNotice />
      <!-- 平台公告 -->
      <Announcement />
      <!-- 核心企业评分排行 -->
      <CoreEnterpriseRanking />
      <!-- 常用功能 -->
      <CommonFunction />
      <!-- 融资企业评分排行 -->
      <EnterpriseRanking />
      <!-- 融资个体户评分排行 -->
      <IndividualRanking />
    </main>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import TaskAgents from './components/taskAgents.vue'
import CommonFunction from './components/commonFunction.vue'
import WarningNotice from './components/warningNotice.vue'
import EnterpriseRanking from './components/enterpriseRanking.vue'
import IndividualRanking from './components/individualRanking.vue'
import CoreEnterpriseRanking from './components/coreEnterpriseRanking.vue'
import Announcement from './components/announcement.vue'

import { incomeMoney, userCentre, getCreditReport } from '@/api/wei/wei.js'
import { formatMoney } from '@/util/filter.js'
export default {
  components: {
    TaskAgents,
    CommonFunction,
    WarningNotice,
    EnterpriseRanking,
    IndividualRanking,
    CoreEnterpriseRanking,
    Announcement,
  },
  data() {
    return {
      // x:[],
      // y:[],
      radio: 1,
      tagChangeObj: {
        editableTabsValue: '1',
        text: void 0,
        text1: '更多收支明细',
        text2: '更多交易',
        name: void 0,
        name1: '收入（万）',
        name2: '融资金额（万）',
        amount: 0,
        financingAmount: void 0,
        income: void 0,
        detailedArr: [],
        detailedArr1: [
          {
            id: 1,
            label: '支出（万）',
            value: '0',
          },
        ],
        detailedArr2: [
          {
            id: 1,
            label: '融资笔数（笔）',
            value: '0',
          },
          {
            id: 2,
            label: '还款金额（万）',
            value: '0',
          },
          {
            id: 3,
            label: '还款笔数（笔）',
            value: '0',
          },
        ],
        radioArr: [],
        radioArr1: [
          {
            id: 4,
            label: '收入金额',
          },
          {
            id: 5,
            label: '支出金额',
          },
        ],
        radioArr2: [
          {
            id: 1,
            label: '融资金额',
          },
          {
            id: 2,
            label: '融资笔数',
          },
          {
            id: 3,
            label: '还款金额',
          },
        ],
      },
      userObj: {
        name: '等待数据',
        realName: '等待数据',
        deptName: '等待数据',
        postName: '等待数据',
        roleName: '等待数据',
        avatar: '/img/bg/img-logo.png',
      },
      tabOptions: [
        {
          label: '周',
          value: 1,
        },
        {
          label: '月',
          value: 2,
        },
        {
          label: '年',
          value: 3,
        },
      ],
    }
  },
  created() {
    this.incomeMoneyFun() // 金额
    this.userCentreFun()
  },
  mounted() {
    const params = {
      date: 1,
    }
    this.getCreditReport(params)
  },
  methods: {
    // 交易与收支tag切换
    handleClick(tab) {
      let params = {}
      const name = tab.name
      if (name == 1) {
        params = {
          date: 1,
        }
      } else if (name == 2) {
        params = {
          date: 2,
        }
      } else {
        params = {
          date: 3,
        }
      }
      this.getCreditReport(params)
    },

    // 查看报表
    handleLookReport() {
      // const type = this.tagChangeObj.editableTabsValue
      // let date = type
      this.$router.push({
        //path: '/report/creditReport',
        path: '/report/jimu/jimuCreditReport',
        // query: { date },
      })
    },
    // 头部金额
    incomeMoneyFun() {
      incomeMoney().then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          this.tagChangeObj.amount = this.$numChuFun(resData.financing, 10000)
          this.tagChangeObj.financingAmount = resData.financing
          this.tagChangeObj.income = resData.income
          const arrs = [
            resData.financingCount,
            formatMoney(this.$numChuFun(resData.repaymentCount, 10000)),
            resData.repaymentStrokeCount,
          ]
          const arrs1 = [formatMoney(this.$numChuFun(resData.dayexpend, 10000))]
          this.tagChangeObj.detailedArr2.forEach((item, index) => {
            item.value = arrs[index]
          })
          this.tagChangeObj.detailedArr1.forEach((item, index) => {
            item.value = arrs1[index]
          })
        }
      })
    },

    // 用户个人信息
    userCentreFun() {
      userCentre().then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          this.userObj.name = resData.name // 用户名
          this.userObj.realName = resData.realName // 真实姓名
          this.userObj.deptName = resData.deptName // 机构名
          this.userObj.postName = resData.postName // 职位
          this.userObj.roleName = resData.roleName // 角色
          if (resData.avatar) {
            this.userObj.avatar = resData.avatar // 头像
          }
        }
      })
    },
    async getCreditReport(params) {
      const { data } = await getCreditReport(params)
      // 笔数情况走势图数据
      let numberData = {
        xData: [],
        newCustomersData: [], // 新增客户数
        numberOfLoansData: [], // 新增放款笔数
        numberOfUnPayData: [], // 待还款笔数
        numberOfPayData: [], // 已还完笔数
      }
      let amountData = {
        xData: [],
        loanAmountData: [], // 放款金额(元)
        payAmountData: [], // 还款金额(元)
        interestData: [], // 已付利息(元)
      }
      for (const item of data.data) {
        numberData.xData.push(item.queryTime)
        numberData.newCustomersData.push(item.newCustomers)
        numberData.numberOfLoansData.push(item.numberOfLoans)
        numberData.numberOfUnPayData.push(item.numberOfUnPay)
        numberData.numberOfPayData.push(item.numberOfPay)

        // 金额
        amountData.xData.push(item.queryTime)
        amountData.loanAmountData.push(item.loanAmount)
        amountData.payAmountData.push(item.payAmount)
        amountData.interestData.push(item.payInterest)
      }
      // 处理笔数情况走势图数据
      this.initNumberEchart(numberData)
      // 处理金额情况走势图数据
      this.initAmountEchart(amountData)
    },
    // 笔数情况走势图
    initNumberEchart(data) {
      let options = {
        title: {
          x: 'center',
          text: '笔数情况走势图',
          y: '10',
        },
        legend: {
          data: ['新增客户', '新增放款笔数', '待还款笔数', '已还完笔数'],
          bottom: '8',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
          },
          formatter: function (params) {
            let list = params && params.length ? [...params] : []
            params = list.filter(item => item.seriesName != 'Placeholder')
            let res = `<div style="margin-bottom: 10px;">${params[0].name}</div>`
            for (let i = 0; i < params.length; i++) {
              res += `<div style="display:flex; align-items: center; justify-content:space-between; width: 200px;">
          <div>
            <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${[
              params[i].color,
            ]};"></span>
            <span>${
              params[i].seriesName == '-' ? 0 : params[i].seriesName
            }</span>
          </div>
          <span style="fontSize:14px;fontWeight:bold;">${
            params[i].value == '-' ? 0 : params[i].value
          }<span>
        </div>`
            }
            return res
          },
        },
        toolbox: {
          feature: {
            saveAsImage: { show: true },
            magicType: { show: true, type: ['line', 'bar'] },
          },
          y: '8',
        },
        xAxis: {
          type: 'category',
          splitLine: { show: false },
          data: data.xData,
          axisLabel: {
            rotate: 25,
          },
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          name: '笔数(笔)',
        },
        series: [
          {
            name: 'Placeholder',
            type: 'bar',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
            },
            data: [0, 1, 2, 3, 4, 5, 6, 7, 8],
          },
          {
            name: '新增客户',
            type: 'bar',
            data: data.newCustomersData,
          },
          {
            name: '新增放款笔数',
            type: 'bar',
            data: data.numberOfLoansData,
          },
          {
            name: '待还款笔数',
            type: 'bar',
            data: data.numberOfUnPayData,
          },
          {
            name: '已还完笔数',
            type: 'bar',
            data: data.numberOfPayData,
          },
        ],
      }
      let initEchart = echarts.init(document.getElementById('echart-number'))
      initEchart && initEchart.setOption(options)
      initEchart.on('click', () => {
        this.$router.push('/wel/subIndex')
      })
    },
    // 金额情况走势图
    initAmountEchart(data) {
      let options = {
        title: {
          x: 'center',
          text: '金额情况走势图',
          y: '10',
        },
        legend: {
          data: ['放款金额(元)', '实还金额(元)', '已付利息(元)'],
          bottom: '8',
        },
        grid: {
          left: '100',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
          },
          formatter: function (params) {
            let list = params && params.length ? [...params] : []
            params = list.filter(item => item.seriesName != 'Placeholder')
            let res = `<div style="margin-bottom: 10px;">${params[0].name}</div>`
            for (let i = 0; i < params.length; i++) {
              res += `<div style="display:flex; align-items: center; justify-content:space-between; width: 200px;">
          <div>
            <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${[
              params[i].color,
            ]};"></span>
            <span>${
              params[i].seriesName == '-' ? 0 : params[i].seriesName
            }</span>
          </div>
          <span style="fontSize:14px;fontWeight:bold;">${
            params[i].value == '-' ? 0 : params[i].value
          }<span>
        </div>`
            }

            return res
          },
        },
        toolbox: {
          feature: {
            saveAsImage: { show: true },
            magicType: { show: true, type: ['line', 'bar'] },
          },
          y: '8',
        },
        xAxis: {
          type: 'category',
          splitLine: { show: false },
          data: data.xData,
          axisLabel: {
            rotate: 25,
          },
        },
        yAxis: {
          type: 'value',
          name: '金额(元)',
        },
        series: [
          {
            name: 'Placeholder',
            type: 'bar',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
            },
            data: [0, 1, 2, 3, 4, 5, 6, 7, 8],
          },
          {
            name: '放款金额(元)',
            type: 'bar',
            data: data.loanAmountData,
          },
          {
            name: '实还金额(元)',
            type: 'bar',
            data: data.payAmountData,
          },
          {
            name: '已付利息(元)',
            type: 'bar',
            data: data.interestData,
          },
        ],
      }
      let initEchart = echarts.init(document.getElementById('echart-amount'))
      initEchart && initEchart.setOption(options)

      initEchart.on('click', () => {
        this.$router.push('/wel/subIndex')
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dis_flex {
  display: flex;
}
.home-page {
  $a_color: var(--global-theme-color);

  header {
    width: 100%;
    // height: 337px;
    display: flex;
    justify-content: space-between;
    // align-items: center;

    .top-left-box {
      flex: 3;
      height: 100%;
      border-radius: 16px;
      background-color: $a_color;
      padding: 24px;
      box-sizing: border-box;
      cursor: context-menu;

      .switchover-box {
        height: 40px;
        width: 100%;
        margin-bottom: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
        .tab-item {
          cursor: pointer;
          display: block;
        }
        .more-box {
          display: flex;
          align-items: center;
          cursor: pointer;
        }

        // el-tabs组件默认样式覆盖
        ::v-deep {
          .el-tabs--border-card > .el-tabs__content {
            padding: unset;
          }

          .el-tabs--border-card {
            background: transparent;
            border-radius: 6px;
            overflow: hidden;

            & > .el-tabs__header {
              border-bottom: unset;
              background-color: transparent;

              & .el-tabs__item {
                color: #fff;
                font-size: 16px;
                font-weight: bold;
                &:not(:last-child) {
                  border-right: 1px solid #fff;
                }
              }

              & .el-tabs__item.is-active {
                color: #409eff;
              }
            }
          }
        }
      }

      .amount-box {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .left-amount-box {
          flex: 1;

          .title-left {
            height: 20px;
            color: rgba(255, 255, 255, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
          }

          .amount-num {
            height: 64px;
            line-height: 64px;
            color: rgba(255, 255, 255, 100);
            font-size: 56px;
            font-family: Helvetica-bold;
            margin-bottom: 12px;
          }

          .transverse-line {
            width: 100%;
            height: 2px;
            background-color: rgba(255, 255, 255, 40);
            margin-bottom: 18px;
          }

          .detailed-for-box {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .children-detailed-box {
              display: flex;
              flex-direction: column;
              flex: 1;

              .children-detailed-label {
                width: 120px;
                height: 20px;
                color: rgba(255, 255, 255, 100);
                font-size: 14px;
                margin-bottom: 8px;
              }

              .children-detailed-value {
                width: 120px;
                height: 23px;
                color: rgba(255, 255, 255, 100);
                font-size: 20px;
                font-family: Helvetica-bold;
              }
            }
          }
        }

        .right-amount-box {
          flex: 1;
          margin-left: 21px;

          .day-switchover-box {
            display: flex;
            justify-content: flex-start;
            align-items: baseline;
            height: 20px;
            color: rgba(255, 255, 255, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
            position: relative;
            z-index: 1;

            .more-box {
              cursor: pointer;
              position: relative;
              z-index: 1;
            }

            .children-el-radio {
              height: 19px;
              color: rgba(255, 255, 255, 100);
              font-size: 14px;
              font-family: SourceHanSansSC-regular;

              // 单选框组默认样式覆盖
              ::v-deep {
                .el-radio__input.is-checked + .el-radio__label {
                  color: rgba(255, 255, 255, 100);
                }

                .el-radio__input.is-checked .el-radio__inner {
                  border-color: rgba(255, 255, 255, 100);
                  background: rgba(255, 255, 255, 100);
                }

                .el-radio__inner {
                  background-color: transparent;
                }

                .el-radio__inner::after {
                  background-color: $a_color;
                }
              }
            }
          }

          #echier-box {
            width: 100%;
            height: 230px;
            overflow: hidden;
            // transform: translateY(-75px);
          }
        }
      }
    }

    .top-right-box {
      flex: 1;
      border-radius: 16px;
      background-color: rgba(64, 64, 64, 100);
      margin-left: 19px;
      padding: 20px;
      box-sizing: border-box;
      color: rgba(255, 255, 255, 100);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      cursor: context-menu;

      & span {
        display: inline-block;
      }

      .userinfo-box {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 40px;

        .img-userinfo {
          width: 48px;
          height: 48px;
          border-radius: 4px;
          background-color: rgba(255, 255, 255, 100);
          border: 1px solid rgba(187, 187, 187, 100);

          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .name-userinfo {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          margin-left: 8px;

          .top-name {
            height: 24px;
            color: rgba(255, 255, 255, 100);
            font-size: 16px;
            font-family: SourceHanSansSC-bold;
            margin-bottom: 4px;
          }

          .bottom-name {
            height: 20px;
            color: rgba(255, 255, 255, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
          }
        }
      }

      .institution-box {
        display: flex;
        align-items: center;
        margin-bottom: 40px;

        .label-institution {
          width: 48px;
          height: 20px;
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          font-family: SourceHanSansSC-regular;
        }

        .value-institution {
          width: 345px;
          height: 17px;
          color: rgba(255, 255, 255, 80);
          font-size: 12px;
          font-family: SourceHanSansSC-regular;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .orientation-box {
        display: flex;
        align-items: center;
        margin-bottom: 40px;

        .label-orientation {
          width: 48px;
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          font-family: SourceHanSansSC-regular;
          flex-shrink: 0;
        }

        .value-orientation {
          border-radius: 3px;
          background-color: #666666;
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 6px;
          box-sizing: border-box;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .right-transverse-line {
        width: 100%;
        height: 2px;
        background-color: #fff1f1;
        margin-bottom: 40px;
        margin-top: 4px;
      }

      .right-menu-box {
        .visiting-card {
          border-radius: 4px;
          background-color: rgba(255, 255, 255, 100);
          color: rgba(105, 124, 255, 100);
          font-size: 14px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 6px;
          box-sizing: border-box;
          cursor: pointer;
        }

        .enterprise-fun {
          border-radius: 4px;
          background-color: rgba(255, 255, 255, 100);
          color: rgba(105, 124, 255, 100);
          font-size: 14px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 9px;
          box-sizing: border-box;
          margin-left: 12px;
          cursor: pointer;
        }
      }
    }
  }

  main {
    display: flex;
    flex-flow: column wrap;
    max-height: 146vh;
    padding-bottom: 40px;
    margin-left: -20px;
    box-sizing: border-box;
    // flex-wrap: wrap;
    // column-count: 2;
    // column-gap: 10px;

    & > * {
      width: calc(50% - 20px);
      margin-left: 20px;
      // height: 100%;
    }

    // & > *:nth-child(2n) {
    //   margin-left: 20px;
    // }
  }
}

#echart-number,
#echart-amount {
  width: 100%;
  height: 300px;
  background: #fff;
}
.switch-title {
  font-size: 18px;
  font-weight: bold;
}
</style>
