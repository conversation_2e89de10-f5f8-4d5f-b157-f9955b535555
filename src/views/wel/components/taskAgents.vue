<template>
  <!-- 代办任务 -->
  <div class="task-agents">
    <div class="title-box">
      <span class="title-text"> 待办任务（{{ total }}） </span>
      <span class="more-box" @click="moreTask">
        更多任务>
        <!-- <svg-icon
          icon-class="icon-youjiantou"
          style="fill: #5a5a5a; font-size: 16px"
        /> -->
      </span>
    </div>
    <div class="content-for-box" v-if="agency.length">
      <div
        class="children-box"
        v-for="item in agency"
        :key="item.id"
        @click="handleBtnApprove(item)"
      >
        <!-- <div class="img-box">
          <img :src="item.img" alt="" />
        </div> -->
        <div class="content-box">
          <div class="top">
            <span>{{ item.name }}</span>
            &nbsp;
            <span>{{ item.processDefinitionName }}</span>
          </div>
          <div class="bottom-time">{{ item.time }}</div>
        </div>
        <div class="icon-box">
          <svg-icon
            icon-class="icon-youjiantou"
            style="fill: #037efd; font-size: 21px"
          />
        </div>
      </div>
    </div>
    <el-empty
      v-else
      style="margin: 0 24px 24px 24px"
      description="暂时无数据"
      :image-size="116"
    />
  </div>
</template>

<script>
import { processList } from '@/api/wei/wei.js'
import { routerMapKeyToPath } from '@/views/business/config'

export default {
  name: 'taskAgents',
  data() {
    return {
      agency: [],
      total: 0,
    }
  },
  created() {
    this.processListFUn()
  },
  methods: {
    processListFUn() {
      const params = {
        size: 5,
        current: 1,
        status: 1,
      }
      processList(params).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const arrs = []
          this.total = resData.total
          for (const item of resData.records) {
            arrs.push({
              ...item,
              id: item.taskId,
              img: '',
              name: item.startUsername,
              processDefinitionName: item.processDefinitionName,
              time: item.createTime,
            })
          }
          this.agency = arrs
        }
      })
    },
    moreTask() {
      this.$router.push('/business/businessprocess')
    },
    // 审批跳转事件
    handleBtnApprove(row) {
      const { taskId, processInstanceId, processDefinitionKey } = row
      const target = routerMapKeyToPath[processDefinitionKey]
      if (!target) return
      this.$router.push(
        `${target}/approve/${Buffer.from(
          JSON.stringify({
            taskId,
            processInsId: processInstanceId,
          })
        ).toString('base64')}`
      )
    },
  },
}
</script>

<style lang="scss" scoped>
$toomuchtime: 0.2s; // 动画过度时间

.task-agents {
  overflow: hidden;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 100);
  color: rgba(16, 16, 16, 100);
  font-size: 14px;
  font-family: Roboto;
  margin-top: 20px;

  .title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 24px 24px 12px 24px;
    height: 30px;

    .title-text {
      // width: 132px;
      color: rgba(36, 36, 36, 100);
      font-size: 20px;
      font-family: SourceHanSansSC-bold;
    }

    .more-box {
      color: #5a5a5a;
      font-size: 16px;
      cursor: pointer;
    }
  }

  .content-for-box {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .children-box {
      display: flex;
      align-items: center;
      padding: 6px 25px;
      box-sizing: border-box;
      cursor: pointer;
      transition: background-color $toomuchtime ease-in-out;

      &:hover {
        background-color: #f0f7ff;

        .icon-box {
          opacity: 1;
        }
      }

      &:last-child {
        margin-bottom: 20px;
      }

      .img-box {
        width: 32px;
        height: 32px;
        border-radius: 26px;
        overflow: hidden;
        margin-right: 9px;
        flex-shrink: 0;

        & img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .content-box {
        width: calc(100% - 21px);
        // width: calc(100% - 41px - 21px);
        text-align: left;

        .top {
          display: flex;
          align-items: center;

          & span:first-child {
            height: 20px;
            color: rgba(44, 146, 246, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
            white-space: nowrap;
          }

          & span:last-child {
            display: block;
            height: 20px;
            color: #000;
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
            word-wrap: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .bottom-time {
          height: 18px;
          color: rgba(121, 121, 121, 100);
          font-size: 12px;
          font-family: SourceHanSansSC-regular;
        }
      }

      .icon-box {
        opacity: 0;
        transition: opacity $toomuchtime ease-in-out;
      }
    }
  }
}
</style>
