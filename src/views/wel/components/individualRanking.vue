<template>
  <!-- 融资个体户评分排行 -->
  <div class="enterprise-ranking">
    <div class="title-box">
      <span class="title-text"> 融资个体户评分排行 </span>
      <span class="more-box">
        更多排行>
        <!-- <svg-icon
          icon-class="icon-youjiantou"
          style="fill: #5a5a5a; font-size: 16px"
        /> -->
      </span>
    </div>
    <div class="content-for-box">
      <div
        class="children-box"
        v-for="(item, index) in scorelist"
        :key="item.id"
      >
        <div
          class="img-box"
          :class="{
            first: index === 0,
            second: index === 1,
            thirdly: index === 2,
          }"
        >
          {{ index + 1 }}
        </div>
        <div class="content-box">
          {{ item.name }}
        </div>
        <div class="icon-box">{{ item.score }}分</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'enterpriseRanking',
  data() {
    return {
      scorelist: [
        {
          id: '1',
          name: '张小虎',
          score: '98',
        },
        {
          id: '2',
          name: '吴青峰',
          score: '98',
        },
        {
          id: '3',
          name: '李德',
          score: '98',
        },
        {
          id: '4',
          name: '王峰',
          score: '98',
        },
        {
          id: '5',
          name: '胡力勇',
          score: '98',
        },
      ],
    }
  },
  mounted() {},
  methods: {},
}
</script>

<style lang="scss" scoped>
$toomuchtime: 0.2s; // 动画过度时间

.enterprise-ranking {
  overflow: hidden;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 100);
  color: rgba(16, 16, 16, 100);
  font-size: 14px;
  font-family: Roboto;
  margin-top: 20px;

  .title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 24px 24px 12px 24px;
    height: 30px;

    .title-text {
      // width: 132px;
      color: rgba(36, 36, 36, 100);
      font-size: 20px;
      font-family: SourceHanSansSC-bold;
    }

    .more-box {
      color: #5a5a5a;
      font-size: 16px;
      cursor: pointer;
    }
  }

  .content-for-box {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .children-box {
      display: flex;
      align-items: center;
      padding: 6px 25px;
      box-sizing: border-box;
      cursor: context-menu;
      // transition: background-color $toomuchtime ease-in-out;

      // &:hover {
      //   background-color: #f0f7ff;
      // }

      &:last-child {
        margin-bottom: 20px;
      }

      .img-box {
        width: 32px;
        height: 32px;
        line-height: 34px;
        font-size: 14px;
        font-family: Roboto;
        text-align: center;
        overflow: hidden;
        border-radius: 26px;
        margin-right: 24px;
        flex-shrink: 0;
        background-color: transparent;
        color: rgba(125, 125, 125, 100);
      }

      .first {
        background-color: #fff4b2;
        color: rgba(130, 111, 0, 100);
      }

      .second {
        background-color: #e7f1ff;
        color: rgba(18, 119, 255, 100);
      }

      .thirdly {
        background-color: #ffebe7;
        color: rgba(255, 140, 146, 100);
      }

      .content-box {
        width: calc(100% - 41px - 21px);
        text-align: left;
        height: 20px;
        line-height: 20px;
        color: rgba(44, 146, 246, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
        white-space: nowrap;
      }

      .icon-box {
        height: 20px;
        color: rgba(245, 34, 45, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-bold;
        white-space: nowrap;
      }
    }
  }
}
</style>
