<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template
        slot="menu"
        slot-scope="{ row }"
      >
        <el-button
          type="text"
          size="small"
          @click="handleDetail(row)"
          v-if="![null, 1].includes(row.status) && row.processInstanceId"
        >详情
        </el-button>
        <el-button
          type="text"
          size="small"
          v-if="row.status === 6 ||
            row.status === 7 ||
            row.status === 8 ||
            row.status === 9
          "
          @click="goRepayment(row)"
        >还款计划
        </el-button>
      </template>
      <template
        slot="goodsType"
        slot-scope="{ row }"
      >
        <el-tag
          type="danger"
          v-if="row.goodsType === 1"
          effect="plain"
        >应收账款质押</el-tag>
        <el-tag
          type=""
          v-if="row.goodsType === 2"
          effect="plain"
        >代采融资</el-tag>
      </template>
      <template
        slot="amount"
        slot-scope="{ row }"
      >
        <span>
          {{ row.amount | formatMoney }}
        </span>
      </template>
      <template
        slot-scope="{ row }"
        slot="status"
      >
        <el-tag type="primary">
          {{ row.$status }}
        </el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from '@/api/finance/financeapply'
import { mapGetters } from 'vuex'

const statusTextConfig = {
  1: '融资申请待提交',
  2: '融资申请审核中',
  3: '融资申请待确认',
  4: '放款审核中',

  7: '待赎货',
  8: '提前已赎货',
  9: '到期已赎货',
  10: '逾期未赎货',
  11: '逾期已赎货',
  12: '已作废',
}
// purchasing_status
export default {
  data() {
    return {
      form: {},
      query: {
        goodsTypeEqual: 4,
      },
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        dialogClickModal: false,
        column: [
          {
            label: '融资编号',
            prop: 'financeNo',
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资编号',
            prop: 'financeNoEqual',
            hide: true,
            display: false,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资用户',
            prop: 'customerName',
          },

          {
            label: '产品名称',
            prop: 'goodsName',
            search: true,
            type: 'tree',
            dicUrl: '/api/blade_product/web-back/product/selectProductList?type=4',
            props: {
              label: 'goodsName',
              value: 'goodsName',
            },
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请选择产品',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资金额(元)',
            prop: 'amount',
          },
          {
            label: '状态',
            prop: 'status',
            align: 'center',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=pledge_finance_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
          },
          {
            label: '创建时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },
          {
            label: '创建时间',
            prop: 'createTimeDateEq',
            hide: true,
            display: false,
            search: true,
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            searchSpan: 6,
            searchRange: true,
          },
        ],
      },
      data: [],
      statusTextConfig,
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.financeapply_add, false),
        viewBtn: this.vaildData(this.permission.financeapply_view, false),
        delBtn: this.vaildData(this.permission.financeapply_delete, false),
        editBtn: this.vaildData(this.permission.financeapply_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDetail(row) {
      console.log('---row', row)
      // if (row.goodsType == 4) {
      //   // 动产质押
      //   // console.log(row)
      //   this.$router.push({
      //     path: '/loan/cargoSolve/cargoSolveDetialInfo',
      //     query: {
      //       id: row.id,
      //       financeNo: row.financeNo,
      //       state: row.status,
      //       rong: true,
      //     },
      //   })
      //   return
      // }
      this.$router.push({
        path: '/product/miningOrderPledgeDetail',
        query: {
          financeNo: row.financeNo,
          state: row.status,
          id: row.id,
          processId: row.processInstanceId,
          iouId: row.iouId,
          // 区分代采和动产
          goodsType: row.goodsType
        },
      })
    },
    goRepayment(row) {
      this.$router.push(
        '/loan/loanmanageiouDetail/' +
        Buffer.from(JSON.stringify(row.iouId)).toString('base64')
      )
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      if (params.createTimeDateEq) {
        params.createTimeDateGe = params.createTimeDateEq[0] // 开始日期
        params.createTimeDateLe = params.createTimeDateEq[1] // 截至日期
        delete params.createTimeDateEq
      }
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      params.goodsTypeEqual = 4
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
