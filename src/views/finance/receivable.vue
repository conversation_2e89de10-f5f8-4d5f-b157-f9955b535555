<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :search.sync="search"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menu" slot-scope="{ row }">
        <el-button type="text" size="small" @click="details(row)"
          >详情
        </el-button>
        <el-button
          type="text"
          size="small"
          v-if="[6, 7, 8, 9, 10].includes(row.status)"
          @click="goRepayment(row)"
          >还款计划
        </el-button>
      </template>
      <template slot="amount" slot-scope="{ row }">
        <span>
          {{ row.amount | formatMoney }}
        </span>
      </template>
      <template slot-scope="{ row }" slot="status">
        <el-tag type="primary">
          {{ row.$status }}
        </el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from '@/api/finance/financeapply'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {
        goodsTypeEqual: 1,
      },
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      search: {
        // goodsName: 1,
      },
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        dialogClickModal: false,
        column: [
          {
            label: '融资编号',
            prop: 'financeNo',
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资编号',
            prop: 'financeNoEqual',
            hide: true,
            display: false,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入融资编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资用户',
            prop: 'customerName',
          },
          {
            label: '产品名称',
            prop: 'goodsName',
            search: true,
            type: 'tree',
            dicUrl: '/api/blade-goods/web-back/goods/getGoodsList',
            props: {
              label: 'goodsName',
              value: 'goodsName',
            },
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请选择产品',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资金额(元)',
            prop: 'amount',
          },
          {
            label: '状态',
            prop: 'status',
            align: 'center',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=finance_apply_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
          },
          {
            label: '创建时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },
          {
            label: '创建时间',
            prop: 'createTimeDateEq',
            hide: true,
            display: false,
            search: true,
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            searchSpan: 6,
            searchRange: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.financeapply_add, false),
        viewBtn: this.vaildData(this.permission.financeapply_view, false),
        delBtn: this.vaildData(this.permission.financeapply_delete, false),
        editBtn: this.vaildData(this.permission.financeapply_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    details(row) {
      this.$router.push({
        path: '/finance/receivable/recDetail',
        query: { id: row.id },
      })
    },
    // 去还款计划
    goRepayment(row) {
      console.log(row)
      this.$router.push(
        '/loan/loanmanageiouDetail/' +
          Buffer.from(JSON.stringify(row.iouId)).toString('base64')
      )
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      if (params.createTimeDateEq) {
        params.createTimeDateGe = params.createTimeDateEq[0] // 开始日期
        params.createTimeDateLe = params.createTimeDateEq[1] // 截至日期
        delete params.createTimeDateEq
      }
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      params.goodsTypeEqual = 1
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
