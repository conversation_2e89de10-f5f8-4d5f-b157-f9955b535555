<template>
  <div class="borrowingInformation-box">
    <!-- 费用信息 -->
    <basic-container v-if="costInformationData.length">
      <div class="header-box">
        <span class="name">费用及开票</span>
      </div>
      <!-- 表格 -->
      <el-table
        ref="table"
        :data="costInformationData"
        :summary-method="getSummaries"
        show-summary
        style="width: 100%; margin-top: 13px"
        class="table-border-style"
      >
        <el-table-column type="index" label="#" width="80" align="center">
        </el-table-column>
        <el-table-column prop="expenseTypeStr" label="费用名称"> </el-table-column>
        <!-- <el-table-column prop="expenseTypeStr" label="费用类型">
          <template slot-scope="scope">
            <span class="border-box">
              {{ scope.row.expenseTypeStr }}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column
          prop="repaymentTerm"
          label="期数"
        >
        </el-table-column>
        <el-table-column prop="feeNodeStr" label="计算节点">
          <template slot-scope="scope">
            <span class="border-box">
              {{ scope.row.feeNodeStr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="collectFeesNodeStr" label="收费节点">
          <template slot-scope="scope">
            <span class="border-box">
              {{ scope.row.collectFeesNodeStr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="calculationStr" label="收费节点">
        </el-table-column>
        <el-table-column prop="amount" label="应付金额">
          <template slot-scope="scope">
            <span v-if="scope.row.amount !== '待录入'">
              ￥{{ scope.row.amount | formatMoney }}
            </span>
            <span v-else>{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="支付状态">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.status.color }">
              {{ scope.row.status.text }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </basic-container>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'

export default {
  props: {
    costInformationData: {
      type: Array,
      default: () => [],
    },
  },
  watch: {},
  data() {
    return {
      financeNo: '',
      tableData: [],
    }
  },
  mounted() {},
  methods: {
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 2) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
  },
}
</script>

<style lang="scss" scoped>
.borrowingInformation-box {
  margin-top: 10px;

  .header-box {
    display: flex;
    align-items: center;

    .name {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
    }
  }

  .table-border-style {
    border-top: 1px solid #e9ebf0;
    border-left: 1px solid #e9ebf0;
    border-right: 1px solid #e9ebf0;
  }

  .border-box {
    color: #000000b5;
    background-color: #e9ebf0;
    border-radius: 100px;
    font-size: 14px;
    padding: 3px 12px;
    box-sizing: border-box;
  }

  // 更改表格组件样式
  ::v-deep {
    .el-table__header-wrapper .has-gutter {
      .el-table__cell {
        background-color: #fff1f1;

        .cell {
          font-size: 14px;
          color: #000;
        }
      }
    }

    .el-table__body tr:hover > td.el-table__cell {
      background-color: #fff;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
  }
}
</style>
