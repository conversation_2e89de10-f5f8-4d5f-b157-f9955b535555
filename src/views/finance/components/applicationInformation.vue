<template>
  <div class="applicationInformation-box">
    <!-- 申请产品 -->
    <basic-container v-if="applicationInData.goodsVO">
      <div class="boxs">
        <div class="boxs-to-apply-for-product">
          <h1 class="boxs-to-apply-for-product-h1">
            <span>融资需求</span>
            <!-- <span class="long-string" /> -->
            <!-- <div class="serial-number">
              <span>融资编号：</span>
              <span>{{ financeNo }}</span>
            </div> -->
          </h1>
          <div class="boxs-to-apply-for-product-title">
            <div class="boxs-to-apply-for-product-left-logo">
              <div class="boxs-to-apply-for-product-left-logo-box">
                <div class="boxs-to-apply-for-product-left-logo-box-img">
                  <img :src="applicationInData.goodsVO.capitalLogo" alt="" />
                </div>
                <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                  <span @click="viewGoods()">{{
                    applicationInData.goodsVO.goodsName
                  }}</span>
                  <span>{{ applicationInData.goodsVO.capitalName }}</span>
                </div>
              </div>
            </div>
            <span class="boxs-to-apply-for-product-right-goodtype">{{
              applicationInData.goodsVO.goodTypeStr[0].value
            }}</span>
          </div>
          <div class="descriptions-for-box" v-if="applicationInData.arr">
            <el-descriptions title="" :column="3" border>
              <el-descriptions-item
                v-for="item in applicationInData.arr.tableData1"
                :key="item.id"
                :label="item.label"
                >{{ item.value }}</el-descriptions-item
              >
            </el-descriptions>
          </div>
        </div>
      </div>
    </basic-container>

    <!-- 质押品 -->
    <basic-container v-if="applicationInData.arr">
      <el-collapse v-model="activeNames1" @change="handleChange1">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change1Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">质押品</h1>
              </div>
            </div>
          </template>
          <div class="table-top">
            <el-table
              ref="table1"
              :data="applicationInData.arr.tableData2"
              :summary-method="getSummaries"
              show-summary
              style="width: 100%; margin-top: 20px"
              class="table-border-style"
            >
              <el-table-column prop="index" label="#" width="70" align="center">
              </el-table-column>
              <el-table-column prop="contractNo" label="资产编号">
                <template slot-scope="scope">
                  <span class="contract-no">{{ scope.row.contractNo }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="!isDingDan" prop="enterpriseName" label="贸易背景">
              </el-table-column>
              <el-table-column v-if="!isDingDan" prop="proofType" label="凭证类型">
                <template slot-scope="scope">
                  <span class="tag-box">{{ scope.row.proofType }} </span>
                </template>
              </el-table-column>
              <el-table-column v-if="!isDingDan" prop="expireTime" label="到期日期">
              </el-table-column>
              <el-table-column prop="amount" label="本次使用价值（元）">
                <template slot-scope="scope">
                  <span>￥{{ scope.row.amount | formatMoney }} </span>
                </template>
              </el-table-column>
              <template slot="header" slot-scope="scope">
                {{ scope }}
              </template>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>

    <!-- 还款试算 -->
    <basic-container v-if="applicationInData.annualIn">
      <el-collapse v-model="activeNames2" @change="handleChange2">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change2Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>还款试算</span>
                  <!-- <span class="long-string" />
                  <span class="interest-rate"
                    >日利率{{
                      applicationInData.annualIn.allDailyInterestRate
                    }}%（年化利率{{
                      applicationInData.annualIn.allAnnualInterestRate
                    }}%）</span
                  > -->
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top refund">
            <div class="table-title-box">
              <div class="title-left-box">
                <span>资方费用</span>
                <span />
                <span
                  >日利率{{
                    applicationInData.annualIn.dailyInterestRate
                  }}%（年化利率{{
                    applicationInData.annualIn.annualInterestRate
                  }}%）</span
                >
              </div>
              <div class="title-right-box">
                计费方式：{{ applicationInData.annualIn.chargeMode }}
              </div>
            </div>
            <el-table
                ref="table2"
                :data="tableData2"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  prop="refundTime"
                  label="还款日期"
                >
                </el-table-column>
                <el-table-column
                  prop="monthlySupply"
                  label="应还总额"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="monthlyPrincipal"
                  label="还款本金"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.monthlyPrincipal | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="planInterest"
                  label="应还利息"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.planInterest | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  v-for="(item, index) in feeData.showRepaymentPlan
                    .stagRecords[0].planFeeList"
                  :key="item.id"
                  :prop="`amount${index}`"
                  :label="item.feeName"
                >
                </el-table-column> -->
              </el-table>
            </div>
            <div v-if="platformFeeList.length">
              <div
                class="table-top refund"
                v-for="item in platformFeeList"
                :key="item.id"
              >
                <div class="chain-line" />
                <div class="table-title-box" style="margin-top: -10px">
                  <div class="title-left-box">
                    <span>{{ item.parenExpenseName }}</span>
                  </div>
                </div>
                <el-table
                  ref="table3"
                  :data="item.expenseOrderDetailList"
                  :summary-method="getSummaries"
                  show-summary
                  style="width: 100%; margin-top: 13px"
                  class="table-border-style"
                >
                  <el-table-column prop="expenseTypeStr" label="费用名称">
                  </el-table-column>
                  <!-- <el-table-column prop="expenseTypeStr" label="费用类型">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.expenseTypeStr }}
                      </span>
                    </template>
                  </el-table-column> -->
                  <el-table-column
                    prop="repaymentTerm"
                    label="期数"
                  >
                  </el-table-column>
                  <el-table-column prop="feeNodeStr" label="计算节点">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="collectFeesNodeStr" label="收费节点">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.collectFeesNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeFormulaName" label="计费方式">
                  </el-table-column>
                  <el-table-column prop="amount" label="应付金额">
                    <template slot-scope="scope">
                      <span>￥{{ scope.row.amount | formatMoney }} </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <div
              class="chain-line"
              v-if="
                applicationInData.financeApply.chargeMethod === 2
              "
            />

            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥ {{ sum1 | formatMoney }} </span>
              </div>
            </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'

export default {
  props: {
    applicationInData: {
      type: Object,
      default: () => {},
    },
    repaymentCalculationDetail: {
      type: Object,
      default: () => {},
    },
    // 是否订单融资
    isDingDan: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    activeNames1() {
      setTimeout(() => {
        this.$refs.table1.$ready = false
      }, 50)
    },
    activeNames2() {
      setTimeout(() => {
        this.$refs.table2.$ready = false
        // this.$refs.table3.$ready = false
      }, 50)
    },
    repaymentCalculationDetail: {
      deep: true,
      immediate: true,
      handler(val) {
        const arr = []
        this.dailyInterestRate = val.showRepaymentPlan.dayRate // 银行日利率
        this.annualInterestRate = val.showRepaymentPlan.yearRate // 银行年利率
        const loanTermUnitChun = val.showRepaymentPlan.loanTermUnit // 期还是天
        // this.feeData = val
        //  资方统一清分需要处理的逻辑
        // let index = 0
        // let Map = {}
        // const { chargeMethod } = this.applicationInData.financeApply
        // if (chargeMethod == 1) {
          // val.showRepaymentPlan.stagRecords[0].planFeeList.forEach(
          //   (item, index) => {
          //     Map[item.feeName] = {
          //       index,
          //       collectFeeMethod: item.collectFeeMethod,
          //       amount: item.amount,
          //     }
          //   }
          // )
        // }
        const yuanArr = val.expenseOrderDetailFinanceVos
        if (yuanArr && yuanArr.length) {
          const copyArr = JSON.parse(JSON.stringify(yuanArr))
          for (const item of copyArr) {
            const amount = item.expenseOrderDetailList.reduce((prve, next) => {
              next.repaymentTerm = next.repaymentTerm
                ? next.repaymentTerm + '期'
                : '--'
              return this.$numJiaFun(prve, next.amount)
            }, 0)
            this.allMonrySum2(amount)
          }
          this.platformFeeList = copyArr
        }
        for (const item of val.showRepaymentPlan.stagRecords) {
          // if (this.applicationInData.financeApply.repaymentType === 1) {
          if (loanTermUnitChun === 2) {
            if (item.term) {
              arr.push({
                term: `${item.term}期`,
                refundTime: item.refundTime,
                monthlySupply: item.monthlySupply,
                monthlyPrincipal: item.monthlyPrincipal,
                planInterest: item.planInterest,
              })
              // if (
              //   index != val.showRepaymentPlan.stagRecords.length - 1 &&
              //   chargeMethod == 1
              // ) {
              //   if (
              //     index == 0 ||
              //     item.planFeeList.length ==
              //       val.showRepaymentPlan.stagRecords[0].length
              //   ) {
              //     item.planFeeList.forEach((citem, cindex) => {
              //       arr[index][`amount${cindex}`] = `￥${formatMoney(
              //         citem.amount
              //       )}`
              //     })
              //   } else {
              //     for (const key in Map) {
              //       arr[index][`amount${Map[key].index}`] = `￥${formatMoney(
              //         0
              //       )}`
              //     }
              //     item.planFeeList.forEach(citem => {
              //       if (Map[citem.feeName]) {
              //         let data = Map[citem.feeName]
              //         arr[index][`amount${data.index}`] = `￥${formatMoney(
              //           citem.amount
              //         )}`
              //       }
              //     })
              //   }
              // }
            } else {
              this.allMonrySum(item)
              arr.push({
                term: '',
                refundTime: '总计:',
                monthlySupply: item.monthlySupply,
                monthlyPrincipal: item.monthlyPrincipal,
                planInterest: item.planInterest,
              })

              // if (chargeMethod == 1) {
              //   for (const key in Map) {
              //     // 一次性付清
              //     if (Map[key].collectFeeMethod == 1) {
              //       arr[index][`amount${Map[key].index}`] = `￥${formatMoney(
              //         Map[key].amount
              //       )}`
              //     } else {
              //       // 分期
              //       arr[index][`amount${Map[key].index}`] = `￥${(
              //         formatMoney(Map[key].amount) * index
              //       ).toFixed(2)}`
              //     }
              //   }
              // }
            }
            // index++
          } else {
            // 这是随借随还的
            arr.push({
              // term: '1期',
              refundTime: item.refundTime,
              monthlySupply: item.monthlySupply,
              monthlyPrincipal: item.monthlyPrincipal,
              planInterest: item.planInterest,
            })
            arr.push({
              // term: '总计:',
              refundTime: '总计:',
              monthlySupply: item.monthlySupply,
              monthlyPrincipal: item.monthlyPrincipal,
              planInterest: item.planInterest,
            })
            // if (chargeMethod == 1) {
            //   item.planFeeList.forEach((citem, cindex) => {
            //     arr[0][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
            //     arr[1][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
            //   })
            // }

            this.allMonrySum(item)
          }
        }

        this.tableData2 = arr
      },
    },
  },
  data() {
    return {
      activeNames1: [],
      activeNames2: [],
      change1Type: false,
      change2Type: false,
      dailyInterestRate: '',
      annualInterestRate: '',
      platformFeeList: [],
      tableData2:[],
      sum1: 0
    }
  },
  methods: {
    // 合计
    allMonrySum(item) {
      // this.sum1 = 0
      this.sum1 = this.$numJiaFun(this.sum1, item.monthlySupply)
      // if (this.platformFeeList.length) {
      //   this.platformFeeList.forEach(item => {
      //     if (item.platformExpensesVOS.length) {
      //       this.sum1 += Number(
      //         item.platformExpensesVOS[item.platformExpensesVOS.length - 1]
      //           .amount
      //       )
      //     }
      //   })
      // }
    },
    // 合计2
    allMonrySum2(num) {
      this.sum1 = this.$numJiaFun(this.sum1, num)
    },
    handleChange1() {
      // 质押品折叠面板收缩控制
      this.change1Type = !this.change1Type
    },
    handleChange2() {
      // 还款试算折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    tableRowClassName({ row }) {
      if (!row.refundTime || row.refundTime === '总计:') {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      if (this.applicationInData.goodsVO.type == 2) {
        this.$router.push({
          path: '/pcontrol/pinformation',
          query: { id: this.applicationInData.goodsVO.id },
        })
        sessionStorage.setItem('look', 'true')
      } else {
        this.$router.push({
          path: '/pcontrol/purchasing',
          query: { id: this.applicationInData.goodsVO.id },
        })
        sessionStorage.setItem('look', 'true')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.applicationInformation-box {
  margin-top: 10px;

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 157px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
      }

      .status {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-family: SourceHanSansSC-bold;
        font-weight: blod;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .tabBar-box {
    margin-top: 20px;
    display: flex;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;

      .tabBar-children {
        font-size: 14px;
        color: #449bfc;
      }

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }
    .active-box {
      background-color: #449bfc;

      .tabBar-children {
        color: #fff;
      }

      &:hover {
        background-color: #449bfc;
      }
    }
  }
}
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        & > span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
        & > span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
