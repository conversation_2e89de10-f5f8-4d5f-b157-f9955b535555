<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="success"
          size="small"
          icon="el-icon-success"
          plain
          @click="putBatchShelf()"
          >上架
        </el-button>
        <el-button
          type="info"
          size="small"
          icon="el-icon-info"
          plain
          @click="offBatchShelf()"
          >下架
        </el-button>
      </template>
<!--      <template slot-scope="{ row }" slot="type">-->
<!--        <span>{{ row.typeStr }}</span>-->
<!--      </template>-->
      <template slot-scope="{ row }" slot="status">
        <el-tag
          :style="{
            color: row.status == 1 ? '#67c23a' : '#A6AEBC',
            border: row.status == 1 ? '1px solid #67c23a' : '1px solid #C9CED6',
            background: row.status == 1 ? '#EAFCF7' : '#fff',
          }"
          >{{
            row.status == 1 ? '已上架' : row.status == 0 ? '已下架' : '未上架'
          }}
        </el-tag>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row, index)"
          v-if="scope.row.status != 1"
          size="small"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row, index)"
          v-if="scope.row.status != 1"
          size="small"
          >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-success"
          size="small"
          v-if="scope.row.status == 0"
          @click.stop="putShelf(scope.row, scope.index)"
          >上架
        </el-button>

        <el-button
          type="text"
          icon="el-icon-warning"
          size="small"
          v-if="scope.row.status == 1"
          @click.stop="offShelf(scope.row, scope.index)"
        >
          下架
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  // getDetail,
  add,
  update,
  remove,
  // putShelf,
  // offShelf,
  batchShelf,
} from '@/api/commodity/commoditylist'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      hasMinMaxForm: 1,
      minRows: 0, // 最小值
      maxRows: 10, // 最大值
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        indexLabel: '序号',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        editBtn: false,
        delBtn: false,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '商品编号',
            display: false,
            prop: 'no',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入商品编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '商品名称',
            prop: 'name',
            maxRow: 1,
            type: 'textarea',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入商品名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '商品分类',
            prop: 'commodityCatalogueName',
            type: 'tree',
            dicUrl:
              '/api/blade-commodity/web-back/commoditycatalogue/enable/tree',
            search: true,
            props: {
              label: 'title',
              value: 'id',
            },
          },
          {
            label: '业务类型',
            prop: 'type',
            type: 'tree',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dicFormatter: res => {
              if (res.success) {
                for (const item of res.data) {
                  // 类型为代采和动产可选，其他禁用
                  if (!['2', '4'].includes(item.dictKey)) {
                    item.disabled = true
                  }
                }
                return res.data
              } else {
                this.$message.warning('业务类型字典接口请求错误')
                return []
              }
            },
            search: true,
          },
          {
            label: '质检节点',
            prop: 'testNode',
            dicData: [
              {
                label: '不检测',
                value: 0,
              },
              {
                label: '入库前检测',
                value: 1,
              },
            ],
          },
          {
            label: '商品图片',
            prop: 'img',
            type: 'upload',
            display: false,
            align: 'center',
            width: 70,
          },
          {
            label: '所属供应商',
            prop: 'supplierId',
            type: 'tree',
            maxRow: 1,
            search: true,
            dicUrl:
              '/api/blade-customer/web-back/customer/customersupplier/supperAll',
            props: {
              label: 'supperName',
              value: 'id',
            },
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请选择供应商',
                trigger: 'blur',
              },
            ],
          },
          // {
          //   label: '规格型号',
          //   search: true,
          //   prop: 'spec',
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入规格型号',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '计量单位',
            prop: 'unitId',
            search: false,
            type: 'select',
            dicUrl: '/api/blade-commodity/web-back/commodityunit/all',
            props: {
              label: 'unitName',
              value: 'id',
            },
          },
          // {
          //   label: '单价',
          //   prop: 'unitPriceSection',
          //   type: 'number',
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入单价(区间值)',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '上次修改时间',
            prop: 'updateTime',
          },
          {
            label: '操作人',
            prop: 'operatorName',
          },
          {
            label: '状态',
            prop: 'status',
            search: true,
            display: false,
            hide: false,
            type: 'select',
            dataType: 'number',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=commodity_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.commoditylist_add, true),
        viewBtn: this.vaildData(this.permission.commoditylist_view, true),
        delBtn: this.vaildData(this.permission.commoditylist_delete, true),
        editBtn: this.vaildData(this.permission.commoditylist_edit, true),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    cedit(row) {
      this.$router.push(
        `/commodity/editProducts/` +
          Buffer.from(JSON.stringify(row.id)).toString('base64')
      )
    },

    putBatchShelf() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      let msg = '确认将选择的数据上架?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let status = 1
          return batchShelf(this.ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    offBatchShelf() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      let msg = '确认将选择的数据下架?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let status = 0
          return batchShelf(this.ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    putShelf(row) {
      if (row.status == 1) {
        this.$message({
          type: 'info',
          message: '商品已进行上架!',
        })
        return
      }
      let msg = '确认进行上架?'

      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids = row.id
          let status = 1
          return batchShelf(ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    offShelf(row) {
      if (row.status == 0) {
        this.$message({
          type: 'info',
          message: '商品已下架!请勿重复操作!',
        })
        return
      }
      let msg = '确认进行下架?'

      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids = row.id
          let status = 0

          return batchShelf(ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '删除成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (type == 'add') {
        this.$router.push('/commodity/newProducts')
      }
      if (type == 'view') {
        this.$router.push(
          `/commodity/lookProducts/` +
            Buffer.from(JSON.stringify(this.form.id)).toString('base64')
        )
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    cdelete(id) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        remove(id.id).then(res => {
          if (res.data.code == '200') {
            this.$message({
              type: 'success',
              message: '删除成功!',
            })
            this.onLoad(this.page)
          }
        })
      })
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        // console.log(res.data.data)
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
