<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button
          type="success"
          size="small"
          icon="el-icon-success"
          plain
          @click="handleEnableAndDisAbleList(1)"
          >启用
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button
          type="warning"
          size="small"
          icon="el-icon-warning"
          plain
          @click="handleEnableAndDisAbleList(0)"
          >禁用
        </el-button>
      </template>

      <template slot-scope="{ row }" slot="status">
        <el-tag
          :style="{
            color: row.status == 1 ? '#67c23a' : '#A6AEBC',
            border: row.status == 1 ? '1px solid #67c23a' : '1px solid #C9CED6',
            background: row.status == 1 ? '#EAFCF7' : '#fff',
          }"
          >{{ row.status == 1 ? '已启用' : '已禁用' }}
        </el-tag>
      </template>

      <template slot-scope="{ row, index, type }" slot="menuForm">
        <el-button
          type="primary"
          size="small"
          plain
          v-if="type == 'edit' || type == 'add'"
          @click="enable()"
          >启用
        </el-button>
      </template>

      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row, index)"
          v-if="scope.row.status != 1"
          size="small"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row, index)"
          v-if="scope.row.status != 1"
          size="small"
          >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-success"
          size="small"
          v-if="scope.row.status == 0"
          @click.stop="handleDisable(scope.row, scope.index)"
          >启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-warning"
          size="small"
          v-if="scope.row.status == 1"
          @click.stop="handleDisable(scope.row, scope.index)"
          >禁用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click.stop="handleAdd(scope.row, scope.index)"
          v-if="scope.row.addChildren"
          >新增子项
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  changeStatus,
} from '@/api/commodity/commoditycatalogue'
import { mapGetters } from 'vuex'
export default {
  name: 'refresh',
  inject: ['reload'],
  data() {
    var validateName = (rule, value, callback) => {
      if (value.trim().length == 0 || value == null || value == '') {
        callback(new Error('分类名称不能为空'))
      }
      if (value.trim().length > 10) {
        callback(new Error('分类名称不能超过10个字'))
      } else {
        callback()
      }
    }
    return {
      form: {},
      query: {},
      icon: {
        type: String,
        default: '',
      },
      loading: true,
      parentId: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        indexLabel: '序号',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        tree: true,
        delBtn: false,
        editBtn: false,
        viewBtn: true,
        selection: true,
        menuWidth: 300,
        dialogClickModal: false,
        column: [
          {
            label: '目录名称',
            search: true,
            prop: 'directName',
            rules: [
              {
                required: true,
                validator: validateName,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '父级目录',
            prop: 'parentId',
            hide: true,
            type: 'tree',
            dicUrl: '/api/blade-commodity/web-back/commoditycatalogue/tree',
            search: true,
            props: {
              label: 'title',
              value: 'id',
            },
            rules: [
              {
                required: false,
                message: '请输入商品分类id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '质检',
            prop: 'testing',
            type: 'select',
            dataType: 'number',
            dicData: [
              {
                label:'否',
                value:'1',
              },{
                label:'是',
                value:'2'
              }
            ],
            rules: [
              {
                required: true,
                message: '请选择是否需要质检',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '操作时间',
            prop: 'updateTime',
            addDisabled: true,
            editDisabled: true,
            editDisplay: false,
            addDisplay: false,
          },
          {
            label: '操作人',
            prop: 'operatorName',
            addDisabled: true,
            editDisabled: true,
            editDisplay: false,
            addDisplay: false,
          },
          {
            label: '排序',
            prop: 'sort',
            type: 'number',
            sortable: true,
            minRows: 0,
            precision: 0,
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '状态',
            prop: 'status',
            display: false,
            formslot: true,
            hide: false,
            type: 'select',
            dataType: 'number',
            span: 25,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=api_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.commoditycatalogue_add, true),
        viewBtn: this.vaildData(this.permission.commoditycatalogue_view, true),
        delBtn: this.vaildData(
          this.permission.commoditycatalogue_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.commoditycatalogue_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    refresh() {
      this.reload()
    },
    init(page, params = {}) {
      this.onLoad(page, (params = {}))
    },

    enable() {
      this.form.status = 1
      this.$refs.crud.rowSave()
    },
    handleEnableAndDisAbleList(status) {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      let msg = status === 1 ? '确定将选择数据启用' : '确定将选择数据禁用'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return changeStatus(this.ids, status)
        })
        .then(() => {
          this.refresh()
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleDisable(row) {
      if (row.status === 1) {
        var msg = '确定将选择数据禁用?'
        var realStatus = 0
      } else {
        msg = '确定将选择数据启用?'
        realStatus = 1
      }

      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return changeStatus(row.id, realStatus)
        })
        .then(() => {
          this.refresh()
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },

    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    handleAdd(row, index) {
      // this.refresh()
      const column = this.findObject(this.option.column, 'parentId')
      this.form.parentId = row.id
      // column.addDisabled = true
      this.$refs.crud.rowAdd()
    },

    beforeClose(done, type) {
      if (['edit', 'view'].includes(type)) {
        if (this.form.parentId == 0) {
          const column = this.findObject(this.option.column, 'parentId')
          column.editDisabled = false
        }
      }
      if ('add' === type) {
        const column = this.findObject(this.option.column, 'parentId')
        column.addDisabled = false
      }
      done()
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.refresh()
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
        if (this.form.parentId == 0) {
          const column = this.findObject(this.option.column, 'parentId')
          column.editDisabled = true
        }
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      // this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        this.data = res.data.data
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style>
</style>
