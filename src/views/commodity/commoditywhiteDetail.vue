<template>
  <basic-container>
    <div class='commodity-whitelist'>
      <!-- up -->
      <h1 class='titleTop'>商品白名单</h1>
      <div class='guaranteeSetTop'>
        <avue-form ref='form' :option='option' v-model='form'></avue-form>
        <!-- line -->
        <div class='children-box'>
          <avue-form :option='subformOption' v-model='obj'>
            <template slot-scope='{ row }' slot='no'>
              <div>{{ row.no }}</div>
            </template>
            <template slot-scope='{ row }' slot='commodityCatalogueName'>
              <div>{{ row.commodityCatalogueName }}</div>
            </template>
            <template slot-scope='{ row }' slot='img'>
              <img
                :src='row.img'
                alt=''
                width='48px'
                height='48px'
                style='cursor: pointer'
                @click='handleImg(row.img)'
              />
            </template>
            <template slot-scope='{ row }' slot='name'>
              <div>{{ row.name }}</div>
            </template>
            <template slot-scope='{ row }' slot='supplierName'>
              <div>{{ row.supplierName }}</div>
            </template>
          </avue-form>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class='updata'>
        <el-button @click='handlClose'>取消</el-button>
        <el-button v-if='!look' type='success' @click='setData()'
        >启用
        </el-button
        >
        <el-button v-if='!look' type='primary' @click="setData('save')"
        >保存
        </el-button
        >
      </div>
      <!-- 弹窗 -->
      <el-dialog
        title='选择资料'
        :visible.sync='type1'
        :modal-append-to-body='false'
        class='avue-dialog'
        width='50%'
      >
        <!--
        arrOption: 配置 arr
        data: 数据源 arr
        search.sync: 获取实时的搜索值 obj
        search-change: 搜索点击事件 fan
        current-change: 分页功能页码点击事件 fan
        on-load: 首次加载事件 fan
        size-change: 分页总页数事件 fan
        selection-change: checkbox事件 fan
        table-loading: 表单加载动画 boolean
        page: 配置分页信息 obj
        -->
        <!-- @on-load="onLoad" -->
        <avue-crud
          ref='crud'
          :option='arrOption'
          :data='arr'
          :search.sync='dialogSearch'
          @search-reset='searchReset'
          @search-change='searchChange'
          @current-change='currentChangeScope'
          @size-change='sizeChangeScope'
          @select='selectionChange'
          :table-loading='loading'
          @select-all='selectAll'
          :page.sync='page'
        >
          <template slot='img' slot-scope='scope'>
            <div style='width: 48px; height: 48px'>
              <img
                :src='scope.row.img'
                style='width: 100%; height: 100%; cursor: pointer'
                @click='handleImg(scope.row.img)'
              />
            </div>
          </template>
        </avue-crud>
        <div class='avue-dialog__footer'>
          <el-button @click='cardfals'>取 消</el-button>
          <el-button @click='cardEngth' type='primary'>确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </basic-container>
</template>

<script>
import { commoditylist, commodityWhiteListDetail, commodityWhiteListSubmit } from '@/api/commodity/commoditywhitelist'
import { mapState } from 'vuex'

export default {
  data() {
    return {
      idn: this.$route.query.id,
      look: this.$route.query.look || false,
      type1: false,
      dialogSearch: {},
      Cbackup: [],
      loading: true,
      cTimeout: void 0,
      form: {
        sortValue: 1,
      },
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 95,
        gutter: 50,
        column: [
          {
            label: '模板名称',
            prop: 'templateName',
            type: 'input',
            size: 'medium',
            span: 8,
            placeholder: '请输入模板名称',
            disabled: false,
            rules: [
              {
                required: true,
                message: `请输入模板名称`,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '排序',
            prop: 'sortValue',
            type: 'number',
            minRows: 1,
            size: 'medium',
            span: 5,
            placeholder: '',
            disabled: false,
          },
        ],
      },
      obj: {
        dynamic: [],
      },
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '准入商品',
            labelWidth: 100,
            labelPosition: 'top',
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            rules: [
              {
                required: true,
                message: `请选择准入商品`,
                trigger: 'change',
              },
            ],
            // disabled: true,
            children: {
              delBtn: true,
              addBtn: true,
              align: 'center',
              headerAlign: 'center',
              rowAdd: () => {
                this.type1 = true
                this.page.currentPage = 1
                this.onLoad()
              },
              rowDel: (row, done) => {
                done()
              },
              column: [
                {
                  // width: 750,
                  label: '商品编号',
                  prop: 'no',
                  // disabled: true,
                  align: 'left',
                  // formslot: true,
                  // type: 'select',
                },
                {
                  // width: 200,
                  label: '商品分类',
                  prop: 'commodityCatalogueName',
                  align: 'left',
                  // type: 'input',
                  // dicData: DIC.NODE,
                },
                {
                  width: 170,
                  label: '商品图片',
                  prop: 'img',
                },
                {
                  label: '商品名称',
                  align: 'left',
                  prop: 'name',
                },
                {
                  label: '所属供应商',
                  align: 'left',
                  prop: 'supplierName',
                },
              ],
            },
          },
        ],
      },
      arr: [],
      page: {
        total: 0,
        pageSize: 5,
        currentPage: 1,
        pageSizes: [5, 10, 30, 40, 50, 100],
        layout: 'total,sizes,pager',
      },
      arrOption: {
        selection: true,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        maxHeight: 300,
        index: true,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [
          {
            label: '商品编号',
            prop: 'no',
          },
          {
            label: '商品分类',
            prop: 'commodityCatalogueName',
            type: 'tree',
            dicUrl:
              '/api/blade-commodity/web-back/commoditycatalogue/enable/tree',
            search: true,
            searchSpan: 12,
            searchLabelWidth: 100,
            props: {
              label: 'title',
              value: 'id',
            },
            placeholder: '请选择商品分类',
          },
          {
            width: 70,
            label: '商品图片',
            prop: 'img',
          },
          {
            label: '商品名称',
            prop: 'name',
            search: true,
            searchSpan: 12,
            searchLabelWidth: 100,
            searchClearable: false,
            placeholder: '请输入商品名称',
          },
          {
            label: '所属供应商',
            prop: 'supplierName',
            type: 'tree',
            search: true,
            searchSpan: 12,
            searchLabelWidth: 100,
            dicUrl:
              '/api/blade-customer/web-back/customer/customersupplier/supperAll',
            props: {
              label: 'supperName',
              value: 'id',
            },
            placeholder: '请选择所属供应商',
          },
        ],
      },
      checkif: false,
    }
  },
  watch: {
    type1(val) {
      if (!val) {
        // 弹窗关闭清空搜索值
        this.$refs.crud.searchReset()
      }
    },
  },
  computed: {
    ...mapState({
      // formParamsDataed: state => state.common.formParamsDataed,
    }),
  },
  created() {
    if (this.idn) {
      this.getData()
    }
    if (this.look) {
      for (const item of this.option.column) {
        item.disabled = true
      }
      this.$set(this.subformOption.column[0].children, 'delBtn', false)
      this.$set(this.subformOption.column[0].children, 'addBtn', false)
      // let voucherUrl = this.findObject(this.statusOption.column, 'voucherUrl')
    }
  },
  methods: {
    setData(type) {
      const goodArr = []
      this.obj.dynamic.map(item => {
        goodArr.push(item.id)
      })
      const params = {
        id: this.idn,
        status: type ? 0 : 1, // 0:保存 1:提交
        templateName: this.form.templateName,
        sortValue: this.form.sortValue,
        commodityIds: goodArr.join(','),
      }
      commodityWhiteListSubmit(params).then(({ data }) => {
        if (data.success) {
          if (type) {
            this.$message.success('保存成功')
            if (data.data) {
              this.idn = data.data
            }
          } else {
            this.$message.success('提交成功')
            this.handlClose()
          }
        }
      })
      // const objParams = { ...this.formParamsDataed, ...params }
      // this.$store.commit('SET_FORM_PARAMS_DATAED', objParams)
    },
    getData() {
      commodityWhiteListDetail({ id: this.idn }).then(({ data }) => {
        const { data: resData } = data
        if (data.success) {
          this.form.templateName = resData.templateName
          this.form.sortValue = resData.sortValue

          const resArr = []
          for (const item of resData.commodityLists) {
            resArr.push({
              id: item.id,
              no: item.no, // 商品编号
              commodityCatalogueName: item.commodityCatalogueName, // 商品分类
              img: item.img, // 商品图片
              name: item.name, // 商品名称
              supplierName: item.supplierName, // 所属供应商
            })
          }
          this.obj.dynamic = resArr
        }
      })
    },
    searchChange(params, done) {
      this.page.currentPage = 1
      this.onLoad(params)
      done()
    },
    onLoad(filter = {}) {
      this.loading = true
      const filterData = {
        current: this.page.currentPage,
        size: this.page.pageSize,
      }
      const type = JSON.stringify(filter) !== '{}'
      if (type) {
        filterData['commodityCatalogueName'] = filter.commodityCatalogueName
          ? filter.commodityCatalogueName
          : void 0
        filterData['name'] = filter.name ? filter.name : void 0
        filterData['supplierId'] = filter.supplierName
          ? filter.supplierName
          : void 0
      }
      commoditylist(filterData).then(({ data }) => {
        // 其他资料-子表单弹窗list数据接口
        const { data: resData } = data
        if (data.success) {
          this.page.total = resData.total || 0
          this.arr = []
          for (const item of resData.records) {
            this.arr.push({
              id: item.id,
              no: item.no, // 商品编号
              commodityCatalogueName: item.commodityCatalogueName, // 商品分类
              img: item.img, // 商品图片
              name: item.name, // 商品名称
              supplierName: item.supplierName, // 所属供应商
            })
          }
          setTimeout(() => {
            this.checkUpType()
            this.loading = false
          }, 100)
        }
      })
    },
    currentChangeScope(currentPage) {
      // 分页页码切换事件
      this.page.currentPage = currentPage
      this.onLoad()
    },
    searchReset() {
      this.onLoad()
    },
    sizeChangeScope(pageSize) {
      // 分页页数切换事件
      this.page.pageSize = pageSize
      // 备份已选checkbox
      this.onLoad()
    },
    selectionChange(cardList, ite) {
      // checkbox事件
      let indexD = -1
      if (this.Cbackup.length) {
        indexD = this.Cbackup.map(item => item.id).indexOf(ite.id)
      }
      if (indexD > -1) {
        this.Cbackup.splice(indexD, 1)
      } else {
        this.Cbackup.push(ite)
      }
    },
    selectAll(cardList, ite) {
      this.Cbackup = cardList
    },
    cardEngth() {
      // 弹窗表格确认按钮
      this.type1 = false
      this.obj.dynamic = [...this.fun(this.obj.dynamic, this.Cbackup, 'id')]
      this.Cbackup = []
    },
    checkUpType() {
      if (!this.type1) return
      let exclude = this.fun(this.obj.dynamic, this.Cbackup, 'id')
      if (exclude.length) {
        for (const item of exclude) {
          const index = this.getArrayIndex(this.arr, item.id)
          if (index !== -1) this.toggleSelection([this.arr[index]])
        }
      }
    },
    cardfals() {
      // 弹窗表格取消按钮
      this.type1 = false
      this.Cbackup = []
    },
    toggleSelection(val) {
      // 修改checkbox状态
      if (this.$refs.crud) {
        this.$refs.crud.toggleSelection(val)
      }
    },
    upBeforeCheck() {
      let compareArr = []
      let storeArr = []
      this.obj.dynamic.map(item => {
        compareArr.push(item.id)
      })
      const a = this.Cbackup
      if (!a.length) return
      if (compareArr.length < a.length) {
        storeArr = a.filter(item => !compareArr.includes(item.id))
      } else {
        // 如果数据list的length比checkbox的length小，进行一个重新赋值
        storeArr = a.filter(item => compareArr.includes(item.id))
        storeArr = storeArr.map((item, index) => {
          item.$index = index
          return item
        })
        this.obj.dynamic = JSON.parse(JSON.stringify(storeArr))
        return
      }
      storeArr &&
      storeArr.map(item => {
        // 筛选出checkbox为true并且dynamic 数据List没有的进行push操作
        this.obj.dynamic.push(item)
      })
    },
    getArrayIndex(arr, obj) {
      /*
       * 获取某个元素下标
       * arr: 传入的数组
       * obj: 需要获取下标的元素
       * */
      var i = arr.length
      while (i--) {
        if (arr[i].id === obj) {
          return i
        }
      }
      return -1
    },
    // 对比两个数组的不同，和成一个数组
    fun(fArr, cArr, field) {
      let diffRes = []
      let fDatas = []
      let cDatas = []
      for (let i in fArr) {
        let flg = false
        for (let j in cArr) {
          if (cArr[j][field] === fArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          fDatas.push(fArr[i])
        }
      }
      for (let i in cArr) {
        let flg = false
        for (let j in fArr) {
          if (fArr[j][field] === cArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          cDatas.push(cArr[i])
        }
      }
      diffRes.push(...cDatas.concat(fDatas))
      return diffRes
    },
    handleImg(url) {
      const imgSrcArr = []
      imgSrcArr.push({
        url: url,
      })
      this.$ImagePreview(imgSrcArr, 0, {
        closeOnClickModal: true,
      })
    },
    handlClose() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({
        path: '/commodity/commoditywhitelist',
      })
    },
  },
}
</script>

<style lang='scss' scoped>
.commodity-whitelist {
  // background-color: #fff;
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }

  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 40px;
    box-sizing: border-box;

    ::v-deep .avue-form__menu {
      display: none;
    }
  }

  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }

  .guaranteeSetBottom {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding: 35px 30px 0 30px;
    box-sizing: border-box;
  }

  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }

  .children-box {
    padding: 0 22px;
    box-sizing: border-box;
  }

  ::v-deep {
    .el-input.is-disabled .el-input__inner {
      color: #4c4b4b;
    }

    .el-form-item__label {
      color: rgba(153, 153, 153, 100);
      font-size: 16px;
      font-family: SourceHanSansSC-regular;
    }
  }
}
</style>
