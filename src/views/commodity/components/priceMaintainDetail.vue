<template>
  <div class="price-detail-container">
    <basic-container>
      <h3>基本信息</h3>
      <div class="price-form">
        <avue-form ref="form" v-model="form" :option="option"></avue-form>
      </div>
    </basic-container>
    <basic-container>
      <h3>价格趋势</h3>
    </basic-container>
  </div>
</template>

<script>
import { getGoodPriceDetail } from '@/api/commodity/commoditylist'
import { formatMoney } from '@/util/filter'
export default {
  name: 'priceMaintainDetail',
  data() {
    return {
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 120,
        column: [
          {
            label: '商品编号',
            prop: 'no',
            disabled: true,
          },
          {
            label: '商品分类',
            prop: 'catalogueId',
            dicUrl: '/api/blade-system/dict/dictionary?code=commodity_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            disabled: true,
          },
          {
            label: '商品名称',
            prop: 'name',
            disabled: true,
          },
          {
            label: '规格型号',
            prop: 'commoditySpec',
            disabled: true,
          },
          {
            label: '控货单价(元)',
            prop: 'commodityPriceStr',
            disabled: true,
          },
          {
            label: '市场单价(元)',
            prop: 'marketPrice',
            disabled: true,
          },
          {
            label: '价格日期',
            prop: 'priceDate',
            disabled: true,
          },
          {
            label: '维护人',
            prop: 'reviser',
            disabled: true,
          },
          {
            label: '价格日期',
            prop: 'priceSource',
            disabled: true,
          },
        ],
      },
      form: {},
    }
  },
  mounted() {
    let id = JSON.parse(Buffer.from(this.$route.params.id, 'base64').toString())
    console.log(id, '1111')
    this.getGoodPriceDetail({ commoditySpecId: id })
  },
  methods: {
    getGoodPriceDetail(params) {
      getGoodPriceDetail(params).then(({ data }) => {
        if (data.code === 200) {
          this.form = {
            ...data.data,
            marketPrice: formatMoney(data.data.marketPrice || 0),
            commodityPriceStr: `${formatMoney(
              data.data.commodityMinPrice || 0
            )} ~ ${formatMoney(data.data.commodityMaxPrice || 0)}`,
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.price-detail-container {
  h3 {
    color: #101010;
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    font-family: SourceHanSansSC-bold;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    &::before {
      display: block;
      content: '';
      width: 8px;
      height: 16px;
      border-radius: 15px;
      background-color: #1277ff;
      margin-right: 4px;
    }
  }
  .price-form {
    box-sizing: border-box;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 24px 24px 0;
    ::v-deep {
      .is-disabled input {
        color: #666;
      }
    }
  }
}
</style>
