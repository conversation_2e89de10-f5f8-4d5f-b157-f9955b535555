<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      :search.sync="search"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-download"
          @click="handleExport"
          >导出
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="commodityMinAndMaxPriceStr">
        <div class="text-color-blue">
          <span>{{ row.commodityMinPrice | formatMoney }}</span>
          <span style="padding: 0 4px">~</span>
          <span>{{ row.commodityMaxPrice | formatMoney }}</span>
        </div>
      </template>
      <template slot-scope="{ row }" slot="marketPrice">
        <span :class="dongtaiClassColorFun(row)"
          >{{ row.marketPrice | formatMoney }}
          <SvgIcon
            v-if="row.abnormal === 1"
            :iconClass="
              row.comparePrices === 7
                ? 'icon-shangjiantou-xichang'
                : row.comparePrices === 8
                ? 'icon-xiajiantou-xichang'
                : ''
            "
            style="font-size: 19px"
            :style="{ fill: row.comparePrices === 8? '#0cf970':'#f90c0c' }"
          />
        </span>
      </template>
      <template slot-scope="scope" slot="menu">
        <!-- <el-button
          type="text"
          size="small"
          @click.stop="handleDetail(scope.row)"
        >
          详情
        </el-button> -->
        <el-button
          type="text"
          size="small"
          @click.stop="handleDefendPirce(scope.row, scope.index)"
        >
          价格维护
        </el-button>
      </template>

      <template slot="nameForm">
        <div>{{ form.name }}</div>
      </template>
      <template slot="noForm">
        <div>{{ form.no }}</div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getGoodPriceList,
  getGoodPriceDetail,
  submitGoodPrice,
} from '@/api/commodity/commoditylist'
import { mapGetters } from 'vuex'
import { getToken } from '@/util/auth'

export default {
  data() {
    return {
      hasMinMaxForm: 1,
      minRows: 0,
      maxRows: 10,
      form: {},
      query: {},
      loading: true,
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        indexLabel: '序号',
        editBtn: false,
        delBtn: false,
        addBtn: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '商品编号',
            display: false,
            prop: 'no',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入商品编号',
                trigger: 'blur',
              },
            ],
          },
          /*{
            label: '商品分类',
            prop: 'catalogueId',
            type: 'tree',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=commodity_status',
            search: false,
            display: false,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },*/
          {
            label: '商品分类',
            prop: 'catalogueName',
            maxRow: 1,
            type: 'textarea',
            search: false,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入商品分类',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '商品名称',
            prop: 'name',
            maxRow: 1,
            type: 'textarea',
            search: true,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入商品名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '规格型号',
            prop: 'commoditySpec',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入规格型号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '控货单价(元)',
            prop: 'commodityMinAndMaxPriceStr',
            display: false,
          },
          {
            label: '市场单价(元)',
            prop: 'marketPrice',
            display: false,
          },
          {
            label: '价格日期',
            prop: 'priceDate',
            display: false,
          },
          {
            label: '维护人',
            prop: 'reviser',
            display: false,
          },
          /*{
            label: '状态',
            prop: 'abnormal',
            search: true,
            display: false,
            type: 'select',
            dataType: 'number',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=abnormal',
            props: {
              label: 'dictKey',
              value: 'dictValue',
            },
          },*/
        ],
        group: [
          {
            display: true,
            labelWidth: 120,
            column: [
              {
                label: '商品名称',
                prop: 'name',
                span: 24,
                formslot: true,
                rules: [
                  {
                    required: true,
                    message: '请输入商品名称',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '商品编号',
                prop: 'no',
                span: 24,
                formslot: true,
              },
              {
                label: '最低价(元)',
                prop: 'commodityMinPrice',
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '请输入最低价',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '最高价(元)',
                prop: 'commodityMaxPrice',
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '请输入最高价',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '市场单价(元)',
                prop: 'marketPrice',
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '请输入市场单价',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '市价来源',
                prop: 'priceSource',
                span: 24,
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.commoditylist_add, true),
        viewBtn: this.vaildData(this.permission.commoditylist_view, true),
        delBtn: this.vaildData(this.permission.commoditylist_delete, true),
        editBtn: this.vaildData(this.permission.commoditylist_edit, true),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    beforeOpen(done, type) {
      if (type === 'edit') {
        getGoodPriceDetail({ commoditySpecId: this.form.id }).then(
          ({ data }) => {
            if (data.code === 200) {
              this.form = data.data
            }
          }
        )
      }

      done()
    },
    rowUpdate(row, index, done, loading) {
      const params = {
        id: row.id,
        commodityMinPrice: row.commodityMinPrice,
        commodityMaxPrice: row.commodityMaxPrice,
        priceSource: row.priceSource,
        marketPrice: row.marketPrice,
        commodityListId: row.commodityListId,
      }
      submitGoodPrice(params)
        .then(({ data }) => {
          if (data.code === 200) {
            this.$message.success('操作成功')
            this.onLoad(this.page)
          }

          done()
        })
        .catch(() => {
          loading()
        })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    // 导出
    handleExport() {
      this.$confirm('是否导出所有数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        window.open(
          `/api/blade-commodity/web-back/commoditySpec/export-Commodity?${
            this.search.no ? `no=${this.search.no}` : ''
          }&& ${this.search.name ? `name=${this.search.name}` : ''}&& ${
            this.search.catalogueId
              ? `catalogueId=${this.search.catalogueId}`
              : ''
          }&& ${
            this.search.abnormal ? `abnormal=${this.search.abnormal}` : ''
          }&&${this.website.tokenHeader}=${getToken()}`
        )
      })
    },
    // 详情
    handleDetail(row) {
      this.$router.push(
        '/commodity/priceMaintainList/priceMaintainDetail/' +
          Buffer.from(JSON.stringify(row.id)).toString('base64')
      )
    },
    // 维护价格
    handleDefendPirce(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    dongtaiClassColorFun(val) {
      let textColorSet = 'text-color-blue'
      if (val.abnormal === 1) {
        if (val.comparePrices === 7) {
          textColorSet = 'text-color-red'
        } else {
          textColorSet = 'text-color-green'
        }
      }
      return textColorSet
    },
    onLoad(page, params = {}) {
      this.loading = true
      getGoodPriceList(
        Object.assign(params, this.query, {
          current: page.currentPage,
          size: page.pageSize,
        })
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records || []
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.text-color-blue {
  color: #1277ff;
}
.text-color-red {
  color: #f90c0c;
}
.text-color-green {
  color: #0cf970;
}
</style>
