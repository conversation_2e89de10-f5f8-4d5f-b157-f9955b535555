<template>
  <div class="newProducts">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">查看商品</div>
      </div>
      <div class="products-box">
        <div class="input-box">
          <avue-form v-model="form" :option="option" />
        </div>
        <div class="upImg">
          <avue-form
            :option="option3"
            v-model="form"
            :upload-preview="uploadPreview"
            :upload-error="uploadError"
            :upload-exceed="uploadExceed"
            :upload-delete="uploadDelete"
            :upload-before="uploadBefore"
            :upload-after="uploadAfter"
          ></avue-form>
        </div>
      </div>
    </div>
    <div class="specification-box">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">规格型号</div>
      </div>
      <div class="the-subform">
        <avue-form :option="aoption" v-model="aForm">
          <template slot-scope="{ row }" slot="unitPrice">
            <div class="min">
              <el-input
                placeholder="请输入最低价"
                v-model="row.min"
                :disabled="true"
              >
                <template slot="append">元</template>
              </el-input>

              <span>~</span>

              <el-input
                v-model="row.max"
                placeholder="请输入最高价"
                :disabled="true"
              >
                <template slot="append">元</template>
              </el-input>
            </div>
          </template>
        </avue-form>
      </div>
    </div>
    <div class="btn-box">
      <div class="no" @click="returnup">取消</div>
    </div>
  </div>
</template>
<script>
import { getshuju, getCatalogueById } from '../../api/commodity/commoditylist'
export default {
  data() {
    return {
      xxxxxx: {},
      title: '',
      min: '',
      max: '',
      id: 0,
      form: {},
      imgidlist: [],
      option: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 120,
        gutter: 80,
        column: [
          {
            label: '商品名称',
            prop: 'name',
            disabled: true,
            type: 'input',
            span: 12,
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请输入商品名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '商品分类',
            prop: 'goodsCategory',
            type: 'tree',
            disabled: true,
            span: 12,
            dicUrl:
              '/api/blade-commodity/web-back/commoditycatalogue/enable/tree',
            errorslot: true,
            props: {
              label: 'title',
              value: 'id',
            },
            rules: [
              {
                required: true,
                message: '选择商品分类',
                trigger: 'blur',
              },
            ],
            change: ({ value }) => {
              getCatalogueById(value).then(({ data: { data } }) => {
                const testNode = this.findObject(this.option.column, 'testNode')
                if (data) {
                  if (data.testing == 2) {
                    testNode.display = true
                  } else {
                    testNode.display = false
                  }
                } else {
                  testNode.display = false
                }
              })
            },
          },
          {
            label: '所属供应商',
            prop: 'supplier',
            span: 12,
            type: 'select',
            disabled: true,
            dicUrl:
              '/api/blade-customer/web-back/customer/customersupplier/supperAll',
            props: {
              label: 'supperName',
              value: 'id',
            },
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请选择供应商',
                trigger: 'blur',
              },
            ],
            // dicData: [
            //   { label: 'asdas', value: '312' },
            //   { label: 'cao', value: '3' },
            // ],
          },
          // {
          //   label: '规格型号',
          //   prop: 'model',
          //   disabled: true,
          //   type: 'input',
          //   span: 12,
          //   errorslot: true,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入规格型号',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '计量单位',
            prop: 'company',
            disabled: true,
            type: 'tree',
            span: 12,
            dicUrl: '/api/blade-commodity/web-back/commodityunit/all',
            props: {
              label: 'unitName',
              value: 'id',
            },
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请选择计量单位',
                trigger: 'blur',
              },
            ],
            // dicData: [
            //   { label: 'sb', value: 'sb' },
            //   { label: 'lj', value: 'lj' },
            // ],
          },
          {
            label: '业务类型',
            prop: 'businessType',
            type: 'select',
            dataType: 'string',
            multiple: true,
            disabled: true,
            span: 12,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dicFormatter: res => {
              if (res.success) {
                for (const item of res.data) {
                  // 类型为代采和动产可选，其他禁用
                  if (!['2', '4'].includes(item.dictKey)) {
                    item.disabled = true
                  }
                }
                return res.data
              } else {
                this.$message.warning('业务类型字典接口请求错误')
                return []
              }
            },
            rules: [
              {
                required: true,
                message: '请选择业务类型',
                trigger: 'change',
              },
            ],
          },
          {
            label: '质检节点',
            prop: 'testNode',
            type: 'select',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=test_node',
            display: false,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            disabled: true,
          },
          {
            label: '市场价预警模板',
            prop: 'alertTemplateId',
            type: 'tree',
            disabled: true,
            span: 12,
            dicUrl:
              '/api/blade-message/web-back/message/messagetemplate/rule/list?current=1&size=100',
            props: {
              label: 'ruleName',
              value: 'id',
            },
            dicFormatter: ({ data }) => {
              const resData = data.records || []
              return resData
            },
          },
          // {
          //   label: '单价',
          //   prop: 'min',
          //   disabled: true,
          //   type: 'numder',
          //   span: 12,
          //   errorslot: true,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入详细的单价区域',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
        ],
      },
      option3: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 120,
        gutter: 80,
        column: [
          {
            label: '商品图片',
            prop: 'imgUrl4',
            disabled: true,
            type: 'upload',
            span: 12,
            listType: 'picture-img',
            accept: 'image/png, image/jpeg, image/jpg',
            fileSize: 5000,
            tip: '注:仅支持上传jpg、jpeg、png格式，大小不超过5Mb',
            propsHttp: {
              res: 'data',
            },
            rules: [
              {
                required: true,
                message: '请上传商品图片',
              },
            ],

            action: '/api/blade-resource/oss/endpoint/put-file-kv',
          },
        ],
      },
      aForm: {},
      aoption: {
        submitBtn: false,
        emptyBtn: false,
        // labelWidth: 110,
        typeslot: true,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamicList',
            type: 'dynamic',
            span: 24,
            children: {
              delBtn: false,
              addBtn: false,
              align: 'center',
              headerAlign: 'left',
              rowAdd: done => {
                // this.$message.success('新增回调')
                done({
                  specificationsAndModels: '',
                })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                done()
              },
              column: [
                {
                  label: '规格型号',
                  prop: 'specificationsAndModels',
                  disabled: true,
                  clearable: false,
                  placeholder: '请输入规格型号',
                  width: 500,
                  // type: 'select',
                  // focus: () => {
                  //   this.draggStop()
                  // },
                  // blur: () => {
                  //   this.draggStopFlip()
                  // },
                },
                {
                  label: '单价',
                  prop: 'unitPrice',
                  disabled: true,
                  clearable: false,
                  placeholder: '',
                },
              ],
            },
          },
        ],
      },
    }
  },
  methods: {
    returnup() {
      this.$router.$avueRouter.closeTag()
      this.$router.replace('/commodity/commoditylist')
    },
    uploadDelete(column, file) {
      // console.log(column, file)
      return this.$confirm(`是否删除？`)
    },
    uploadBefore(file, done, loading) {
      var first = file.name.lastIndexOf('.')
      const type = file.name.substring(first + 1, file.length)
      // //如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
      if (['jpg', 'jpeg', 'png'].includes(type)) {
        // const isLt20M = file.size / 1024 > 500
        // if (isLt20M) {
        //   loading()
        //   this.$message.error('文件大小不能超过500KB')
        //   return
        // }
        done()
      } else {
        loading()
        this.$message.error('文件格式错误')
        return
      }
    },
    uploadError(error, column) {
      this.$message.error(error)
      // console.log(error, column)
    },
    uploadAfter(res, done, loading, column) {
      // console.log(res, 1, column)
      // this.imgidlist.push({ attachId: res.attachId })
      this.imgidlist = [{ attachId: res.attachId }]
      // console.log(this.imgidlist, 111111)
      done()
      this.$message.success('上传成功')
    },
    uploadPreview(file, column, done) {
      // console.log(file, column)
      done() //默认执行打开方法
      // this.$message.success('自定义查看方法,查看控制台')
    },
    uploadExceed(limit, files, fileList, column) {
      // console.log(limit, files, fileList, column)
      // this.$message.success('自定义查看方法,查看控制台')
    },
    submit() {
      // this.$message.success('当前数据' + JSON.stringify(this.form))
    },
  },
  mounted() {
    this.id = JSON.parse(
      Buffer.from(this.$route.params.id, 'base64').toString()
    )
    getshuju(this.id).then(({ data }) => {
      if (data.success) {
        const { data: resData } = data
        this.form.name = resData.name
        this.form.goodsCategory = resData.catalogueId
        this.form.supplier = resData.supplierId
        this.form.company = resData.unitId
        this.form.imgUrl4 = resData.attachList[0].link
        this.form.alertTemplateId = resData.alertTemplateId
        this.form.testNode = resData.testNode
        this.form.businessType = resData.type // 业务类型
        const arr = []
        for (const item of resData.commoditySpecList) {
          arr.push({
            specificationsAndModels: item.commoditySpec,
            min: item.commodityMinPrice,
            max: item.commodityMaxPrice,
            id: item.id,
          })
        }
        this.aForm.dynamicList = arr
      }
    })
  },
}
</script>
<style lang="scss" scoped>
.newProducts {
  ::v-deep {
    .avue-form__menu {
      display: none;
    }
    .el-input.is-disabled .el-input__inner {
      color: #000;
    }
  }

  .details-content1 {
    background-color: #ffffff;
    border-radius: 8px;
    margin-top: 16px;
    // margin-bottom: 50px;
    padding: 20px;
    box-sizing: border-box;

    .details-content1-tit {
      display: flex;
      align-items: center;
      height: 24px;

      .title-dot {
        width: 8px;
        height: 16px;
        background-color: #1277ff;
        border-radius: 4px;
        margin-right: 4px;
      }

      .title {
        font-size: 16px;
        height: 24px;
        line-height: 24px;
        color: #101010;
        font-weight: 600;
        margin: 0;
      }
    }

    .products-box {
      margin-top: 20px;
      width: 100%;
      // height: 361px;
      padding: 24px;
      box-sizing: border-box;
      border-radius: 16px;
      border: 1px solid rgba(232, 232, 232, 100);

      .input-box {
        width: 100%;
      }

      .upImg {
        position: relative;
        z-index: 10;

        ::v-deep {
          .avue-upload__icon {
            width: 120px;
            height: 120px;
            line-height: 120px;
          }
          .avue-upload__avatar {
            width: 120px;
            height: 120px;
          }
        }
      }

      .min {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
      }

      .min span {
        flex: 1;
        text-align: center;
      }
    }
  }

  .specification-box {
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 100);
    margin-top: 21px;
    padding: 20px 20px 0;
    box-sizing: border-box;

    .details-content1-tit {
      display: flex;
      align-items: center;
      height: 24px;

      .title-dot {
        width: 8px;
        height: 16px;
        background-color: #1277ff;
        border-radius: 4px;
        margin-right: 4px;
      }

      .title {
        font-size: 16px;
        height: 24px;
        line-height: 24px;
        color: #101010;
        font-weight: 600;
        margin: 0;
      }
    }

    .the-subform {
      margin-top: 14px;

      .min {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
      }

      .min span {
        flex: 1;
        text-align: center;
        margin: 0 28px;
        width: 14px;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .btn-box {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    margin-top: 12px;
    margin-bottom: 40px;
    justify-content: flex-end;

    & * {
      width: 60px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      border-radius: 4px;
    }

    .no {
      border: 1px solid #bbbbbb;
      margin-right: 12px;
      cursor: pointer;
    }

    .yes {
      background-color: #2dad28;
      color: #ffffff;
      margin-right: 12px;
      cursor: pointer;
    }

    .up {
      background-color: #1277ff;
      color: #ffffff;
      cursor: pointer;
    }
  }

  // .price {
  //   display: flex;
  //   align-items: center;
  //   justify-content: flex-start;
  // }
}
</style>
