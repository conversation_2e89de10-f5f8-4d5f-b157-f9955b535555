<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd()"
          >新 增
        </el-button>
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.commoditywhitelist_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="success"
          size="small"
          icon="el-icon-top"
          plain
          @click="handlerOnShelf"
          >启 用
        </el-button>
        <el-button
          type="warning"
          size="small"
          icon="el-icon-bottom"
          plain
          @click="conlistoff"
          >禁 用
        </el-button>
      </template>

      <template slot-scope="{ row }" slot="status">
        <div style="text-align: center">
          <el-tag
            :style="{
              color: row.status == 1 ? '#67c23a' : '#A6AEBC',
              border:
                row.status == 1 ? '1px solid #67c23a' : '1px solid #C9CED6',
              background: row.status == 1 ? '#EAFCF7' : '#fff',
            }"
            >{{ row.status == 1 ? '已启用' : '已禁用' }}
          </el-tag>
        </div>
      </template>

      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-view"
          @click="handleAdd(scope.row, 'look')"
          size="small"
          >查看
        </el-button>

        <el-button
          type="text"
          icon="el-icon-edit"
          @click="handleAdd(scope.row)"
          v-if="permission.commodityunit_edit && scope.row.status == 0"
          size="small"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row)"
          v-if="permission.commodityunit_delete && scope.row.status == 0"
          size="small"
          >删除
        </el-button>

        <el-button
          type="text"
          icon="el-icon-success"
          size="small"
          v-if="scope.row.status == 0"
          @click.stop="handleDisable(scope.row, scope.index)"
          >启用
        </el-button>

        <el-button
          type="text"
          icon="el-icon-warning"
          size="small"
          v-if="scope.row.status == 1"
          @click.stop="handleDisable(scope.row, scope.index)"
          >禁用
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  handlerOnShelf,
  conlistoff,
  changeStatus,
} from '@/api/commodity/commoditywhitelist'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        addBtn: false,
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        delBtn: false,
        editBtn: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        menuAlign: 'left',
        menuWidth: 250,
        column: [
          {
            label: '模板编号',
            prop: 'templateNo',
            rules: [
              {
                required: true,
                message: '请输入模板编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '模板名称',
            prop: 'templateName',
            rules: [
              {
                required: true,
                message: '请输入模板名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上次修改时间',
            prop: 'updateTime',
            rules: [
              {
                required: true,
                message: '请输入上次修改时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '操作人',
            prop: 'operatorName',
            rules: [
              {
                required: true,
                message: '请输入操作人',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'status',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=commodity_white_list_status',
            dataType: 'number',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [
              {
                required: true,
                message: '请输入状态',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '排序',
            prop: 'sortValue',
            rules: [
              {
                required: true,
                message: '请输入排序值，值越小越靠前',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.commoditywhitelist_add, false),
        viewBtn: this.vaildData(this.permission.commoditywhitelist_view, false),
        delBtn: this.vaildData(
          this.permission.commoditywhitelist_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.commoditywhitelist_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    handleAdd(look, type) {
      if (look) {
        this.$router.push({
          path: '/commodity/commoditywhiteDetail',
          query: {
            id: look.id,
            look: type ? true : false,
          },
        })
        return
      }
      this.$router.push({
        path: '/commodity/commoditywhiteDetail',
      })
    },
    cdelete(row) {
      this.$refs.crud.rowDel(row)
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },

    handlerOnShelf() {
      var length = this.selectionList.length
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定启用选择的商品白名单模板吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return handlerOnShelf(this.ids)
        })
        .then(resp => {
          // console.log(resp)
          let num = length - resp.data.data
          let msg = ''
          let val = resp.data.data;
          val = val === null ? 0 : val;

          if (num > 0) {
            msg = `成功启用${num}条数据,有${val}条数据未填写完整`
          } else {
            msg = '操作成功'
          }

          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: msg,
          })
        })
    },
    conlistoff() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择取消吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return conlistoff(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },

    handleDisable(row) {
      if (row.status === 1) {
        var msg = '确定将选择数据禁用?'
        var realStatus = 0
      } else {
        msg = '确定将选择数据启用?'
        realStatus = 1
      }

      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return changeStatus(row.id, realStatus)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index);
    },
  },
}
</script>

<style></style>
