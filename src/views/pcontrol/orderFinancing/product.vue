<template>
  <!-- <basic-container> -->
  <div class="guarantee">
    <!-- up -->
    <h1 class="titleTop">基本信息</h1>
    <div class="guaranteeSetTop">
      <avue-form
        v-model="form"
        ref="information"
        :option="option"
        :defaults.sync="defaults"
      >
        <template slot="wave">
          <div>
            <div style="text-align: center">~</div>
          </div>
        </template>
        <template slot="wave2">
          <div>
            <div style="text-align: center">~</div>
          </div>
        </template>
        <template slot="wave3">
          <div>
            <div style="text-align: center">~</div>
          </div>
        </template>
        <template slot="waveA">
          <div>
            <div style="text-align: center">~</div>
          </div>
        </template>
        <template slot="goodsLabelIds">
          <div>
            <el-select
              v-model="form.goodsLabelIds"
              placeholder="请选择产品标签（最多3个）"
              multiple
              :multiple-limit="3"
              :disabled="lookRepaymentType"
            >
              <el-option
                v-for="item in option.column[19].dicData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </template>
      </avue-form>
    </div>
    <!-- down -->
    <div class="title-bottom">
      <h1 class="titleBottom">开通流程</h1>
      <!-- <avue-ueditor v-model="text" :options="options"></avue-ueditor> -->
      <avue-form :option="optionS" v-model="obj">
        <!-- <template slot="procIcon">
          <div>1</div>
        </template> -->
      </avue-form>
    </div>
    <div class="title-bottom">
      <h1 class="titleBottom">常见问题</h1>
      <!-- <avue-ueditor v-model="text" :options="options"></avue-ueditor> -->
      <avue-form :option="optionQ" v-model="objQ">
        <template slot-scope="{ row }" slot="answer">
          <div>
            <el-input
              v-model="row.answer"
              size="small"
              class="answer-input"
              placeholder="请输入答案"
              :disabled="elDisabl"
            ></el-input>
          </div>
        </template>
      </avue-form>
    </div>
    <h1 class="titleBottom">产品背景</h1>
    <avue-form
      :option="optionI"
      v-model="objI"
      :upload-before="uploadBeforeImg"
    >
    </avue-form>
    <!-- 操作按钮 -->
    <!-- <div class="updata" v-if="!look">
        <el-button @click="handleback()">取消</el-button>
        <el-button type="success" @click="handleSubmit()">上架</el-button>
        <el-button type="primary" @click="handleSubmit('save')">保存</el-button>
      </div> -->
  </div>
  <!-- </basic-container> -->
</template>

<script>
import {
  // serveDataPurchasing,
  getListByType,
  getcapitalList,
  getTagList,
  setOrderFinancingShelf,
  getOrderFinancingGoodsDetail,
  deptExistBank,
  // upDataPurchasing,
} from '@/api/goods/pcontrol/pinformation'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import { mapGetters, mapState } from 'vuex'

export default {
  props: ['look'],
  data() {
    return {
      id: this.$route.query.id,
      lookRepaymentType: false,
      elDisabl: false,
      form: {
        isDelay: 1,
      },
      // text: '',
      obj: {},
      objQ: {},
      objI: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 85,
        gutter: 80,
        column: [
          {
            label: '产品名称',
            prop: 'goodsName',
            span: 12,
            placeholder: '请输入产品名称',
            // disabled: true,
            rules: [
              {
                required: true,
                message: '请填写产品名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '产品分类',
            prop: 'goodsTypeId',
            type: 'select',
            span: 12,
            placeholder: '请选择产品分类',
            // disabled: true,
            dicData: [],
            rules: [
              {
                required: true,
                message: '请选择产品分类',
                trigger: 'change',
              },
            ],
          },
          {
            label: '所属资方',
            prop: 'capitalId',
            type: 'select',
            span: 12,
            placeholder: '请选择资金方',
            // disabled: true,
            dicData: [],
            rules: [
              {
                required: true,
                message: '请选择资金方',
                trigger: 'change',
              },
            ],
            change: ({ value }) => {
              if (value) {
                this.capitalIdChangeFun(value)
              }
            },
          },
          {
            label: '借款金额',
            prop: 'loanAmount',
            span: 6,
            append: '万元',
            placeholder: false,
            className: 'loansleft1',
            // labelWidth: 82,
            // disabled: true,
            rules: [
              {
                required: true,
                message: '请填写借款金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave',
            // disabled: true,
            // minRows: 10,
            className: 'borrow-wave',
          },
          {
            label: '',
            prop: 'loanAmounted',
            span: 5,
            append: '万元',
            placeholder: false,
            labelWidth: 0,
            // className: 'loansright1',
            // disabled: true,
            rules: [
              {
                required: true,
                message: '请填写借款金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '年利率',
            prop: 'annualInterestRateType',
            type: 'select',
            span: 5,
            clearable: false,
            placeholder: '请选择年利率',
            className: 'annual',
            labelPosition: 'left',
            // value: '1',
            // disabled: true,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_annual_interest_rate_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            control: val => {
              if (val && val == 2 && !this.lookRepaymentType) {
                return {
                  annualInterestRateStart: {
                    disabled: false,
                  },
                  annualInterestRateEnd: {
                    disabled: false,
                  },
                }
              } else {
                return {
                  annualInterestRateStart: {
                    disabled: true,
                  },
                  annualInterestRateEnd: {
                    disabled: true,
                  },
                }
              }
            },
            rules: [
              {
                required: true,
                message: '请选择年利率',
                trigger: 'change',
              },
            ],
          },
          {
            label: '',
            prop: 'annualInterestRateStart',
            span: 4,
            placeholder: false,
            className: 'loansleft',
            labelWidth: 0,
            append: '%',
            // disabled: true,
            rules: [
              {
                required: true,
                message: '请填写年利率',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'waveA',
            // disabled: true,
            // minRows: 10,
            // className: 'borrow-wave',
          },
          {
            label: '',
            prop: 'annualInterestRateEnd',
            span: 4,
            placeholder: false,
            className: 'loansright',
            labelWidth: 0,
            append: '%',
            // disabled: true,
            rules: [
              {
                required: true,
                message: '请填写年利率',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '还款类型',
            span: 12,
            prop: 'repaymentType',
            placeholder: '请选择还款类型',
            type: 'select',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_repayment_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择还款类型',
                trigger: 'change',
              },
            ],
            control: val => {
              if (val) {
                sessionStorage.setItem('repaymentType', val)
                this.triggerBrother('repaymentType')
              }
            },
            // control: val => {
            //   if (val == 0) {
            //     return {
            //       loadTermUnit: {
            //         disabled: true,
            //       },
            //     }
            //   } else {
            //     return {
            //       loadTermUnit: {
            //         disabled: false,
            //       },
            //     }
            //   }
            // },
            // append: '%',
            // className: 'annual-percent',
            // labelWidth: 0,
          },
          {
            label: '计费方式',
            prop: 'billingMethod',
            type: 'tree',
            span: 12,
            multiple: true,
            parent: true,
            placeholder: '选择计费方式（多选）',
            // disabled: true,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_billing_method',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'string',
            rules: [
              {
                required: true,
                message: '请选择计费方式',
                trigger: 'change',
              },
            ],
          },

          {
            label: '借款期限',
            prop: 'loadTermUnit',
            type: 'select',
            span: 5,
            placeholder: '选择单位',
            className: 'loanstime',
            // disabled: false,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_load_term_unit',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择单位',
                trigger: 'change',
              },
            ],
          },
          {
            label: '',
            prop: 'loadTerm',
            span: 4,
            placeholder: false,
            className: 'loansleft',
            labelWidth: 0,
            // disabled: true,
            rules: [
              {
                required: true,
                message: '请填写期限',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave',
          },
          {
            label: '',
            prop: 'loadTermed',
            span: 4,
            placeholder: false,
            className: 'loansright',
            labelWidth: 0,
            // disabled: true,
            rules: [
              {
                required: true,
                message: '请填写期限',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '放款方式',
            prop: 'lendingMethod',
            type: 'select',
            span: 12,
            placeholder: '选择放款方式',
            // disabled: true,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_lending_method',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择放款方式',
                trigger: 'change',
              },
            ],
            control: val => {
              if (val) {
                sessionStorage.setItem('lendingMethod', val)
                // this.triggerBrother()
                // if (this.look) return
                // if (val && val == 2) {
                //   this.form.chargeMethod = 2
                //   sessionStorage.setItem('chargeMethod', false)
                // } else {
                //   sessionStorage.setItem('chargeMethod', true)
                // }
                // if (val == 2) {
                //   return {
                //     chargeMethod: {
                //       disabled: true,
                //     },
                //   }
                // } else {
                //   return {
                //     chargeMethod: {
                //       disabled: false,
                //     },
                //   }
                // }
              }
            },
          },
          {
            label: '收费方式',
            prop: 'chargeMethod',
            type: 'select',
            span: 12,
            placeholder: '选择收费方式',
            disabled: true,
            value: 2,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_charge_method',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择收费方式',
                trigger: 'change',
              },
            ],
            control: val => {
              if (val) {
                sessionStorage.setItem('chargeMethod', val)
                this.triggerBrother('chargeMethod')
              }
            },
          },
          {
            label: '提前还款',
            prop: 'prepaymentType',
            type: 'select',
            span: 12,
            placeholder: '请设置提前还款',
            // disabled: true,
            // dicUrl:
            //   '/api/blade-system/dict-biz/dictionary?code=goods_prepayment_type',
            // props: {
            //   label: 'dictValue',
            //   value: 'dictKey',
            // },
            dicData: [],
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择提前还款',
                trigger: 'change',
              },
            ],
            // control: val => {
            //   if (val) {
            //     sessionStorage.setItem('prepaymentType', val)
            //     this.triggerBrother()
            //   }
            // },
          },
          {
            label: '产品标签',
            prop: 'goodsLabelIds',
            type: 'tree',
            span: 12,
            placeholder: '选择产品标签（最多3个）',
            multiple: true,
            parent: true,
            // disabled: true,
            dicData: [],
            rules: [
              {
                required: true,
                message: '请选择产品标签',
                trigger: 'change',
              },
            ],
          },
          // {
          //   label: '可贷成数',
          //   prop: 'loanableStart',
          //   span: 6,
          //   append: '%',
          //   placeholder: false,
          //   className: 'loansleft1',
          //   // labelWidth: 155,
          //   // disabled: true,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请填写可贷成数',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          // {
          //   label: '',
          //   labelWidth: 0,
          //   span: 1,
          //   prop: 'wave',
          //   // disabled: true,
          //   // minRows: 10,
          //   className: 'borrow-wave',
          // },
          // {
          //   label: '',
          //   prop: 'loanableEnd',
          //   span: 5,
          //   append: '%',
          //   placeholder: false,
          //   labelWidth: 0,
          //   // className: 'loansright1',
          //   // disabled: true,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请填写可贷成数',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '代收代付类型',
            prop: 'bankCardCollectionType',
            type: 'select',
            span: 12,
            labelWidth: 110,
            placeholder: '代收代付类型',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_bank_card_collection_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '代收代付类型',
                trigger: 'change',
              },
            ],

            // multiple: true,
            // parent: true,
            // disabled: false,
            // dicData: [
            //   {
            //     label: '受托支付',
            //     value: 4,
            //   },
            // ],
          },
          {
            label: '展期申请',
            prop: 'isDelay',
            placeholder: '请设置展期申请',
            span: 12,
            type: 'select',
            dicData: [
              {
                label: '支持展期申请',
                value: 1,
              },
              {
                label: '不支持展期申请',
                value: 2,
              },
            ],
            rules: [
              {
                required: true,
                message: '请选择产品标签',
                trigger: 'change',
              },
            ],
          },
          {
            label: '展期方式',
            prop: 'delayType',
            type: 'tree',
            display: true,
            span: 12,
            placeholder: '选择展期方式(可多选)',
            multiple: true,
            parent: true,
            dicData: [
              {
                label: '分期还款',
                value: 1,
              },
              {
                label: '延迟还款',
                value: 3,
              },
            ],
            rules: [
              {
                required: true,
                message: '选择展期方式',
                trigger: 'change',
              },
            ],
          },
          {
            label: '展期年利率',
            prop: 'delayInterestRateMax',
            display: true,
            span: 6,
            append: '%',
            placeholder: false,
            className: 'loansleft1',
            rules: [
              {
                required: true,
                message: '请填写展期年利率',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave2',
            display: true,
            className: 'borrow-wave',
          },
          {
            label: '',
            prop: 'delayInterestRateMin',
            display: true,
            span: 5,
            append: '%',
            placeholder: false,
            labelWidth: 0,
            rules: [
              {
                required: true,
                message: '请填写展期年利率',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '展期申请时间',
            prop: 'delayBeforeDayMin',
            span: 6,
            placeholder: false,
            display: true,
            prepend: '还款日前',
            append: '天',
            rules: [
              {
                required: true,
                message: '请填写展期申请时间',
                trigger: 'blur',
              },
            ],
          },
          // {
          //   label: '',
          //   prop: 'delayBeforeDayMin',
          //   span: 4,
          //   append: '天',
          //   placeholder: false,
          //   display: true,
          //   labelWidth: 0,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请填写展期申请时间',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave3',
            display: true,
            className: 'borrow-wave',
          },
          {
            label: '',
            prop: 'delayBeforeDayMax',
            span: 5,
            prepend: '还款日前',
            append: '天',
            placeholder: false,
            display: true,
            labelWidth: 0,
            rules: [
              {
                required: true,
                message: '请填写展期申请时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '计息天数',
            prop: 'interestDay',
            span: 12,
            prepend: 'D+',
            append: '天',
            display: false,
            rules: [
              {
                required: true,
                message: '请填写计息天数',
                trigger: 'blur',
              },
            ],
          },
          {
            label:"分账场景",
            prop:"ruleSceneId",
            type: 'select',
            dicUrl:
              '/api/blade-drools/web-back/drools/sceneInfo/select',
            props: {
              label: 'label',
              value: 'value',
            },
          },
          {
            label: '产品说明',
            prop: 'goodsExplain',
            type: 'textarea',
            span: 24,
            placeholder: '请输入产品说明',
            minRows: 3,
            // maxRows: 10,
            // multiple: true,
            // parent: true,
            rules: [
              {
                required: true,
                message: '请填写产品说明',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      // options: {
      //   span: 24,
      //   hide: true,
      //   maxlength: 500,
      //   prop: 'companyInto',
      //   component: 'AvueUeditor',
      //   showWordLimit: true,
      //   rules: [
      //     {
      //       required: true,
      //       message: '详情',
      //       trigger: 'blur',
      //     },
      //   ],
      //   options: {
      //     className: 'niuniuniu',
      //     action: '/api/blade-resource/oss/endpoint/put-file',
      //     dataType: 'string',
      //     props: {
      //       res: 'data',
      //       url: 'link',
      //     },
      //   },
      // },
      optionS: {
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        column: [
          // {
          //   label: '输入框',
          //   prop: 'input1',
          //   span: 12,
          //   row: true,
          // },
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            children: {
              addBtn: true,
              delBtn: true,
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                // this.$message.success('新增回调')
                done({
                  procTheTitle: '',
                })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                done()
              },
              column: [
                {
                  width: 200,
                  label: '流程标题',
                  prop: 'procTheTitle',
                  placeholder: '请输入流程标题',
                  // formslot: true,
                },
                {
                  width: 500,
                  label: '流程描述',
                  prop: 'theProcDescription',
                  type: 'input',
                  placeholder: '请输入流程描述',
                },
                // {
                //   width: 200,
                //   label: '流程图标',
                //   prop: 'procIcon',
                //   placeholder: false,
                // },
                {
                  label: '流程图标',
                  prop: 'procIcon',
                  type: 'upload',
                  span: 24,
                  listType: 'picture-img',
                  // tip: '只能上传jpg/png文件，且不超过500kb',
                  propsHttp: {
                    res: 'data',
                  },
                  action: '/api/blade-resource/oss/endpoint/put-file-kv',
                  uploadBefore: (file, done, loading) => {
                    this.uploadBeforeIcon(file, done, loading)
                  },
                },
                {
                  // width: 200,
                  label: '排序',
                  prop: 'sort',
                  placeholder: false,
                },
              ],
            },
          },
        ],
      },
      optionQ: {
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        column: [
          // {
          //   label: '输入框',
          //   prop: 'input1',
          //   span: 12,
          //   row: true,
          // },
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamicQ',
            type: 'dynamic',
            span: 24,
            children: {
              addBtn: true,
              delBtn: true,
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                // this.$message.success('新增回调')
                done({
                  issue: '',
                })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                done()
              },
              column: [
                {
                  width: 300,
                  label: '问题',
                  prop: 'issue',
                  placeholder: '请输入问题',
                  // formslot: true,
                },
                {
                  width: 600,
                  label: '答案',
                  prop: 'answer',
                  placeholder: '请输入答案',
                  headerAlign: 'left',
                  // type: 'input',
                  // span: 24,
                  // row: true,
                },
                {
                  // width: 200,
                  label: '排序',
                  prop: 'sortQ',
                  placeholder: false,
                },
              ],
            },
          },
        ],
      },
      optionI: {
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: false,
            labelWidth: 0,
            disabled: false,
            prop: 'backgroundImg',
            type: 'upload',
            span: 3,
            listType: 'picture-img',
            tip: '仅支持格式：jpg、png，大小不超过2M',
            // loadText: '附件上传中，请稍等',
            propsHttp: {
              res: 'data',
            },
            action: '/api/blade-resource/oss/endpoint/put-file-kv',
          },
        ],
      },
    }
  },
  watch: {
    'form.repaymentType'(val) {
      // 还款类型选分期就只能以期为单位
      if (this.lookRepaymentType) return
      if (val && val == '1') {
        const loadTermUnit = this.findObject(this.option.column, 'loadTermUnit')
        const billingMethod = this.findObject(
          this.option.column,
          'billingMethod'
        )
        const prepaymentType = this.findObject(
          this.option.column,
          'prepaymentType'
        )
        const interestDay = this.findObject(this.option.column, 'interestDay')
        this.defaults.loadTermUnit.disabled = true // 借款期限
        this.defaults.billingMethod.disabled = false // 计费方式
        this.defaults.prepaymentType.disabled = true // 提前还款
        loadTermUnit.disabled = true // 借款期限
        billingMethod.disabled = false // 计费方式
        prepaymentType.disabled = true // 提前还款
        interestDay.display = false // 计息天数
        billingMethod.rules[0].required = true

        prepaymentType.rules[0].required = true

        this.form.interestDay = undefined
        this.form.loadTermUnit = '2'
        this.form.prepaymentType = '2' // 提前还款
      } else if (val && val == '2') {
        const loadTermUnit1 = this.findObject(
          this.option.column,
          'loadTermUnit'
        )
        const billingMethod1 = this.findObject(
          this.option.column,
          'billingMethod'
        )
        const prepaymentType1 = this.findObject(
          this.option.column,
          'prepaymentType'
        )
        const interestDay = this.findObject(this.option.column, 'interestDay')
        this.defaults.loadTermUnit.disabled = true // 借款期限
        this.defaults.billingMethod.disabled = true // 计费方式
        this.defaults.prepaymentType.disabled = false // 提前还款
        loadTermUnit1.disabled = true // 借款期限
        billingMethod1.disabled = true // 计费方式
        prepaymentType1.disabled = false // 提前还款
        interestDay.display = true // 计息天数
        billingMethod1.rules[0].required = false
        prepaymentType1.rules[0].required = false

        this.form.loadTermUnit = '1'
        this.form.billingMethod = null // 计费方式
        if (this.form.prepaymentType == '2') {
          this.form.prepaymentType = null // 提前还款
          setTimeout(() => {
            prepaymentType1.rules[0].required = true
            if (this.form.annualInterestRateType == '1') {
              this.form.annualInterestRateType = '2'
              this.form.annualInterestRateType = '1'
            }
          }, 100)
        }
      }
    },
    // 年利率为客户利率时，清空之前输入的值
    'form.annualInterestRateType'(val) {
      if (this.lookRepaymentType) return
      const annualInterestRateStart = this.findObject(
        this.option.column,
        'annualInterestRateStart'
      )
      const annualInterestRateEnd = this.findObject(
        this.option.column,
        'annualInterestRateEnd'
      )
      if (val && Number(val) === 1) {
        this.form.annualInterestRateStart = void 0
        this.form.annualInterestRateEnd = void 0
        annualInterestRateStart.rules[0].required = false
        annualInterestRateEnd.rules[0].required = false
        setTimeout(() => {
          this.$refs.information.validateField('annualInterestRateStart')
          this.$refs.information.validateField('annualInterestRateEnd')
        }, 100)
      } else {
        annualInterestRateStart.rules[0].required = true
        annualInterestRateEnd.rules[0].required = true
      }
    },
    // 当选择不支持展期,需要隐藏一些表单内容
    'form.isDelay'(val) {
      // 控制需要操作的表单字段,进行控制显隐
      let arr = [
        'delayType',
        'delayInterestRateMax',
        'delayInterestRateMin',
        'delayBeforeDayMin',
        'delayBeforeDayMax',
        'wave2',
        'wave3',
      ]
      let list = {}
      for (const index in arr) {
        list[arr[index]] = this.findObject(this.option.column, arr[index])
      }
      if (val == 2) {
        for (const props in list) {
          list[props].display = false
        }
      } else {
        for (const props in list) {
          list[props].display = true
        }
      }
    },
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
    }),
    ...mapGetters(['userInfo']),
  },
  created() {
    this.onLoadStart() // 加载对应选择框选项
    if (this.id) {
      if (this.look) {
        this.lookRepaymentType = true
      }
      this.getGoodsDetailed()
    } else {
      // this.lookRepaymentType = false
    }
  },
  mounted() {
    const set = setInterval(() => {
      if (this.option.column && this.look) {
        for (const item of this.option.column) {
          item.disabled = true
        }
        for (const item of this.optionS.column[0].children.column) {
          item.disabled = true
        }
        for (const item of this.optionQ.column[0].children.column) {
          item.disabled = true
        }
        this.optionS.column[0].children.addBtn = false
        this.optionS.column[0].children.delBtn = false
        this.optionQ.column[0].children.addBtn = false
        this.optionQ.column[0].children.delBtn = false
        this.elDisabl = true
        this.optionI.column[0].disabled = true
        clearInterval(set)
      }
    }, 100)
  },
  methods: {
    handleback() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({ path: '/goods/goods' })
    },
    handleSubmit(save) {
      if (!save) {
        this.$refs.information.validate((valid, done, msg) => {
          this.$store.commit('SET_VALID_TYPE', valid)
          if (!valid) {
            let errMsg = Object.values(msg)[0].message
            if (!errMsg) {
              errMsg = Object.values(msg)[0][0].message
              if (!errMsg) {
                errMsg = '必填项未填'
              }
            }
            this.$message.error(errMsg)
            return
          }
          done()
          this.formSubmit()
        })
      } else {
        if (!this.form.goodsName) {
          this.$store.commit('SET_VALID_TYPE', false)
          return this.$message.error('请先填写产品名称再进行保存')
        }
        if (!this.form.goodsTypeId) {
          this.$store.commit('SET_VALID_TYPE', false)
          return this.$message.error('请先选择产品类型再进行保存')
        }
        this.formSubmit()
      }
    },
    // 新增
    formSubmit() {
      // if (!this.checkNum()) return
      if (
        Number(this.form.loadTerm) &&
        Number(this.form.loadTermed) &&
        Number(this.form.loadTerm) >= Number(this.form.loadTermed)
      ) {
        this.$store.commit('SET_VALID_TYPE', false)
        this.$message.error('请确认借款期限范围是否正常')
        return
      }
      if (
        Number(this.form.loanAmount) &&
        Number(this.form.loanAmounted) &&
        Number(this.form.loanAmount) >= Number(this.form.loanAmounted)
      ) {
        this.$store.commit('SET_VALID_TYPE', false)
        this.$message.error('请确认借款金额范围是否正常')
        return
      }
      if (this.form.goodsLabelIds && this.form.goodsLabelIds.length > 3) {
        this.$store.commit('SET_VALID_TYPE', false)
        this.$message.error('产品标签最多只能选择3个')
        return
      }
      const goodsQuestionsed = []
      const goodsOpeningProcessesed = []
      for (const item of this.objQ.dynamicQ) {
        // 处理常见问题
        goodsQuestionsed.push({
          answer: item.answer,
          name: item.issue,
          sort: item.sortQ,
        })
      }
      for (const item of this.obj.dynamic) {
        // 处理开通流程
        goodsOpeningProcessesed.push({
          description: item.theProcDescription,
          logo: item.procIcon,
          sort: item.sort,
          title: item.procTheTitle,
        })
      }
      const fromData = this.form
      // let fromloadTerm = {}
      // let fromloadAmount = {}
      // if (!fromData.loadTerm || !fromData.loadTermed) {
      //   // 处理贷款期限只填写一半的情况
      //   fromloadTerm = fromData.loadTerm || fromData.loadTermed
      // } else {
      //   fromloadTerm = `${fromData.loadTerm}~${fromData.loadTermed}`
      // }
      // if (!fromData.loanAmount || !fromData.loanAmounted) {
      //   // 处理贷款金额只填写一半的情况
      //   fromloadAmount = fromData.loanAmount || fromData.loanAmounted
      // } else {
      //   fromloadAmount = `${fromData.loanAmount}~${fromData.loanAmounted}`
      // }
      let params = {
        annualInterestRateStart:
          fromData.annualInterestRateType == 2
            ? fromData.annualInterestRateStart
            : '0',
        annualInterestRateEnd:
          fromData.annualInterestRateType == 2
            ? fromData.annualInterestRateEnd
            : '0',
        annualInterestRateType: fromData.annualInterestRateType,
        bankCardCollectionType: fromData.bankCardCollectionType, // ?
        billingMethod: fromData.billingMethod ? fromData.billingMethod : '0',
        capitalId: fromData.capitalId,
        goodsLabelIds: fromData.goodsLabelIds,
        goodsName: fromData.goodsName,
        goodsTypeId: fromData.goodsTypeId,
        lendingMethod: fromData.lendingMethod,
        loadTermUnit: fromData.loadTermUnit,
        prepaymentType: fromData.prepaymentType,
        repaymentType: fromData.repaymentType,
        loanableStart: fromData.loanableStart, // 可贷成数
        loanableEnd: fromData.loanableEnd, // 可贷成数
        type: 5, // 类型
        background: this.objI.backgroundImg, // 背景图
        goodsExplain: fromData.goodsExplain, // 产品说明
        goodsOpeningProcesses: goodsOpeningProcessesed, // 开通流程
        goodsQuestions: goodsQuestionsed, // 常见问题
        // loadTerm: `${fromData.loadTerm}~${fromData.loadTermed}`,
        // loanAmount: `${fromData.loanAmount}~${fromData.loanAmounted}`,
        loanAmountStart: fromData.loanAmount,
        loanAmountEnd: fromData.loanAmounted,
        loadTermStart: fromData.loadTerm,
        loadTermEnd: fromData.loadTermed,
        chargeMethod: fromData.chargeMethod,
        interestDay: fromData.repaymentType == 2 ? fromData.interestDay : null,
        // 展期参数
        delayType: fromData.delayType.join(),
        isDelay: fromData.isDelay,
        delayBeforeDayMin:
          fromData.isDelay == 1 ? fromData.delayBeforeDayMin : null,
        delayBeforeDayMax:
          fromData.isDelay == 1 ? fromData.delayBeforeDayMax : null,
        delayInterestRateMax:
          fromData.isDelay == 1 ? fromData.delayInterestRateMax : null,
        delayInterestRateMin:
          fromData.isDelay == 1 ? fromData.delayInterestRateMin : null,
        ruleSceneId:fromData.ruleSceneId
      }

      this.$store.commit('SET_FORM_PARAMS_DATAED', params)
      // if (save) {
      //   serveDataPurchasing(params)
      //     .then(res => {
      //       const resData = res.data
      //       this.onShelf(resData.data, save)
      //     })
      //     .catch(() => {})
      // } else {
      //   upDataPurchasing(params).then(res => {
      //     const resDate = res.data
      //     if (resDate.code == 200) {
      //       this.onShelf(this.id, save)
      //     }
      //   })
      // }
    },
    // 查看
    getGoodsDetailed() {
      getOrderFinancingGoodsDetail(this.id).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // setTimeout(() => {
          //   this.lookRepaymentType = false
          // }, 1000)
          const chlok = resData.data
          this.form.goodsName = chlok.goodsName
          this.form.goodsTypeId = chlok.goodsTypeId
          this.form.capitalId = chlok.capitalId
          this.form.loanAmount = chlok.loanAmountStart
          this.form.loanAmounted = chlok.loanAmountEnd
          this.form.annualInterestRateType = chlok.annualInterestRateType
          if (Number(chlok.annualInterestRateStart) !== Number('0')) {
            this.form.annualInterestRateStart = chlok.annualInterestRateStart
          }
          if (Number(chlok.annualInterestRateEnd) !== Number('0')) {
            this.form.annualInterestRateEnd = chlok.annualInterestRateEnd
          }
          this.form.repaymentType = chlok.repaymentType
          this.form.billingMethod = chlok.billingMethod
            ? chlok.billingMethod
            : null
          this.form.ruleSceneId = chlok.ruleSceneId
          this.form.loadTermUnit = chlok.loadTermUnit
          this.form.loadTerm = chlok.loadTermStart
          this.form.loadTermed = chlok.loadTermEnd
          this.form.lendingMethod = chlok.lendingMethod
          this.form.prepaymentType = chlok.prepaymentType
          this.form.loanableStart = chlok.loanableStart
          this.form.loanableEnd = chlok.loanableEnd
          this.form.bankCardCollectionType = chlok.bankCardCollectionType
          this.form.goodsLabelIds = chlok.labelIds
          this.form.goodsExplain = chlok.goodsExplain
          this.form.chargeMethod = chlok.chargeMethod

          this.form.isDelay = chlok.isDelay
          this.form.delayBeforeDayMin = chlok.delayBeforeDayMin
          this.form.interestDay = chlok.interestDay
          this.form.delayBeforeDayMax = chlok.delayBeforeDayMax
          this.form.delayInterestRateMax = chlok.delayInterestRateMax
          this.form.delayInterestRateMin = chlok.delayInterestRateMin
          this.form.delayType = chlok.delayType
            ? chlok.delayType.split(',')
            : []

          for (const item of chlok.goodsOpeningProcesses) {
            this.obj['dynamic'].push({
              procTheTitle: item.title,
              theProcDescription: item.description,
              procIcon: item.logo,
              sort: item.sort,
            })
          }
          for (const item of chlok.goodsQuestions) {
            this.objQ['dynamicQ'].push({
              issue: item.name,
              answer: item.answer,
              sortQ: item.sort,
            })
          }
          this.obj['dynamic'] = chlok.goodsOpeningProcesses
          this.objQ['dynamicQ'] = chlok.goodsQuestions
          this.objI['backgroundImg'] = chlok.background
        }
      })
    },
    onLoadStart() {
      // 1: 应收账款 2: 代采融资
      getListByType(5).then(res => {
        // 获取产品分类字段
        const resData = res.data
        if (resData.code == 200) {
          for (const item of resData.data) {
            this.option.column[1].dicData.push({
              label: item.name,
              value: item.id,
            })
          }
        }
      })
      getcapitalList().then(res => {
        // 获取所属资方字段
        const resData = res.data
        if (resData.code == 200) {
          for (const item of resData.data) {
            this.option.column[2].dicData.push({
              label: item.name,
              value: item.companyId,
            })
          }
        }
      })
      getTagList().then(res => {
        // 获取产品标签list
        const resData = res.data
        if (resData.code == 200) {
          for (const item of resData.data) {
            this.option.column[19].dicData.push({
              label: item.name,
              value: item.id,
            })
          }
        }
      })
      getDictionary('goods_prepayment_type').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            if (item.dictKey === '2') {
              resList.push({
                label: item.dictValue,
                value: item.dictKey,
                disabled: true,
              })
            } else {
              resList.push({
                label: item.dictValue,
                value: item.dictKey,
              })
            }
          }
          const prepaymentType = this.findObject(
            this.option.column,
            'prepaymentType'
          )
          prepaymentType.dicData = resList // 提前还款下来数组
        }
      })
    },
    onShelf(idS, save) {
      if (save) {
        return this.$message.success('已保存')
      }
      setOrderFinancingShelf(idS)
        .then(res => {
          // 上架
          const resData = res.data
          if (resData.code == 200) {
            this.$message.success('已上架')
            this.$router.$avueRouter.closeTag()
            this.$router.push({ path: '/goods/goods' })
          }
        })
        .catch(() => {})
    },
    uploadBeforeIcon(file, done, loading) {
      var first = file.name.lastIndexOf('.')
      const type = file.name.substring(first + 1, file.length)
      //如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
      if (['jpg', 'jpeg', 'png'].includes(type)) {
        const isLt20M = file.size / 1024 > 100
        if (isLt20M) {
          loading()
          this.$message.error('文件大小不能超过100KB')
          return
        }
        done()
      } else {
        loading()
        this.$message.error('文件格式错误')
        return
      }
    },
    uploadBeforeImg(file, done, loading) {
      var first = file.name.lastIndexOf('.')
      const type = file.name.substring(first + 1, file.length)
      //如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
      if (['jpg', 'jpeg', 'png'].includes(type)) {
        const isLt20M = file.size / 1024 > 2000
        if (isLt20M) {
          loading()
          this.$message.error('文件大小不能超过2MB')
          return
        }
        done()
      } else {
        loading()
        this.$message.error('文件格式错误')
        return
      }
    },
    checkNum() {
      if (
        this.form.loadTerm &&
        this.form.loadTermed &&
        this.form.loadTerm >= this.form.loadTermed
      ) {
        this.$message.error('请确认借款期限范围是否正常')
        return false
      }
      if (
        this.form.loanAmount &&
        this.form.loanAmounted &&
        this.form.loanAmount >= this.form.loanAmounted
      ) {
        this.$message.error('请确认借款金额范围是否正常')
        return false
      }
      return true
    },
    triggerBrother(argVal) {
      this.$emit('processOnload', argVal)
    },
    capitalIdChangeFun(val) {
      deptExistBank(val).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          // 1.银行，否则非银行
          sessionStorage.setItem('capitalType', resData.capital)
          sessionStorage.setItem('companyId', val)
          this.triggerBrother('bank')
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.guarantee {
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 40px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetBottom {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding: 35px 30px 0 30px;
    box-sizing: border-box;
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
  .title-bottom {
    ::v-deep .avue-upload__icon {
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    ::v-deep .avue-upload__avatar {
      width: 35px;
      height: 35px;
    }
    ::v-deep i.el-icon-zoom-in {
      display: none;
    }
    ::v-deep .el-table td div {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    ::v-deep .answer-input {
      width: 575px;
    }
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    color: #4c4b4b !important;
  }
  ::v-deep .el-tag.el-tag--info {
    color: #4c4b4b !important;
  }
  ::v-deep .el-textarea.is-disabled .el-textarea__inner {
    color: #4c4b4b !important;
  }
}
</style>
