<template>
  <div class="guarantee">
    <h1 class="titleTop">保证金设置</h1>
    <div class="guaranteeSetTop">
      <avue-form
        ref="form"
        :option="option"
        @reset-change="emptytChange"
        @submit="submit"
        v-model="form"
      >
        <template slot="wave">
          <div>
            <div style="text-align: center">~</div>
          </div>
        </template>
      </avue-form>
    </div>
    <div v-for="item in costList" :key="item.id">
      <!-- 1应收 2代采 3云信 -->
      <template v-if="item.children.length > 0">
        <temp
          :costObj="item"
          :look="look"
          goodsType="2"
          :capital="capital"
          :repaymentType="2"
          :ref="item.id"
          :Method="Method"
          :zujianzidianArr="zujianzidianArr"
          @chargeMethodEmit="chargeMethodEmit"
        ></temp>
      </template>
    </div>
    <el-dialog
      title="选择账户"
      :visible.sync="type2"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="avue-dialog"
      width="70%"
    >
      <avue-crud
        ref="crud"
        :option="payMode == 1 ? arrOption2 : arrOption3"
        :data="arr_2"
        :search.sync="dialogSearch2"
        @row-click="rowClick2"
        @search-reset="searchReset2"
        @search-change="searchChange2"
        @size-change="sizeChangeScope2"
        @on-load="dialogOnLoad2"
        :table-loading="tableLoading2"
        :page.sync="accountPagingObj"
        @current-change="currentChangeScope2"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow2" :label="row.$index">&nbsp;</el-radio>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="type2 = false">取 消</el-button>
        <el-button @click="cardEngth2" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDetail } from '@/api/goods/agentgoods'
import {
  expenseTypeList,
  goodsExpenseId,
  selectListAccount,
  getbillBankCardaRelation,
} from '@/api/goods/pcontrol/pinformation'
import { mapState } from 'vuex'
import temp from './components/index.vue'
// import { getDictionary } from '@/api/system/dictbiz'
const DIC = {
  payment: [
    {
      value: 1,
    },
    {
      value: 2,
    },
  ],
}
export default {
  props: {
    look: Boolean,
  },
  data() {
    return {
      // 账户弹窗
      payMode: null,
      type2: false,
      rowsData: void 0,
      arr_2: [],
      selectRow2: '',
      tableLoading2: true,
      accountType: 0,
      searchChangeData2: {},
      // 普通账户
      arrOption2: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },
      // 虚拟账户
      arrOption3: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },

      id: this.$route.query.id,
      costList: [],
      capital: null,
      repaymentType: null,
      form: {
        switch: 2,
      },
      cashDepositBillBankObj: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 130,
        gutter: 50,
        column: [
          {
            label: '缴纳保证金',
            activeColor: '#13ce66',
            type: 'switch',
            placeholder: false,
            prop: 'switch',
            dicData: DIC.payment,
            disabled: this.look,
            control: val => {
              if (this.look) return
              if (val == 1) {
                return {
                  bondPayProportionStart: {
                    disabled: true,
                  },
                  bondPayProportionEnd: {
                    disabled: true,
                  },
                  // payWay: {
                  //   disabled: true,
                  // },
                  releaseWay: {
                    disabled: true,
                  },
                  cashDepositTakeBillBankCardas: {
                    disabled: true,
                  },
                }
              } else {
                return {
                  bondPayProportionStart: {
                    disabled: false,
                  },
                  bondPayProportionEnd: {
                    disabled: false,
                  },
                  // payWay: {
                  //   disabled: false,
                  // },
                  releaseWay: {
                    disabled: false,
                  },
                  cashDepositTakeBillBankCardas: {
                    disabled: false,
                  },
                }
              }
            },
            // disabled: true,
          },
          {
            label: '缴纳比例',
            prop: 'bondPayProportionStart',
            span: 6,
            append: '%',
            placeholder: false,
            className: 'loansleft1',
            disabled: this.look,
            // labelWidth: 82,
            rules: [
              {
                required: true,
                message: '请输入缴纳比例',
                trigger: 'change',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave',
            // disabled: true,
            // minRows: 10,
            className: 'borrow-wave',
          },
          {
            label: '',
            prop: 'bondPayProportionEnd',
            span: 5,
            append: '%',
            placeholder: false,
            labelWidth: 0,
            disabled: this.look,
            // className: 'loansright1',
            rules: [
              {
                required: true,
                message: '请输入缴纳比例',
                trigger: 'change',
              },
            ],
          },
          {
            label: '支付方式',
            prop: 'payWay',
            type: 'select',
            span: 12,
            placeholder: '请选择支付方式',
            disabled: true,
            // disabled: this.look,
            // dicData: [],
            value: 1,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_bond_pay_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择支付方式',
                trigger: 'change',
              },
            ],
            change: ({ value }) => {
              if (value) {
                this.payMode = value
                if (value === 1) {
                  this.option.column[6].display = true
                  this.option.column[7].display = false
                  this.form.cashDepositTakeBillBank
                  if (this.look) return
                  // 将input 变成只读
                  setTimeout(() => {
                    const domArr = document.querySelectorAll('.el-input__inner')
                    for (const item of domArr) {
                      if (item.placeholder === '请选择保证金监管账户') {
                        item.readOnly = true
                        item.id = 'my-corre-input-select'
                        break
                      }
                    }
                  }, 100)
                } else {
                  this.option.column[6].display = false
                  this.option.column[7].display = true
                  this.form.cashDepositTakeBillBankCardas = ''
                  this.cashDepositBillBankObj = {}
                }
              }
            },
          },
          {
            label: '释放方式',
            prop: 'releaseWay',
            type: 'select',
            span: 12,
            placeholder: '请选择释放方式',
            disabled: this.look,
            // dicData: [],
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_bond_release_mode',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择释放方式',
                trigger: 'change',
              },
            ],
          },
          {
            label: '账户',
            placeholder: '请选择账户',
            prop: 'cashDepositTakeBillBankCardas',
            disabled: this.look,
            display: false,
            type: 'input',
            clearable: false,
            suffixIcon: 'el-icon-arrow-right',
            span: 12,
            rules: [
              {
                required: true,
                message: '请选择账户',
                trigger: 'change',
              },
            ],
            click: () => {
              if (this.look) return
              // 打开弹窗
              this.initialize2()
              this.accountType = 3
              this.selectedWarehouse(2)
            },
          },
          {
            label: '账户',
            placeholder: '请选择账户',
            prop: 'cashDepositTakeBillBank',
            disabled: this.look,
            display: false,
            type: 'input',
            clearable: false,
            suffixIcon: 'el-icon-arrow-right',
            span: 12,
            rules: [
              {
                required: true,
                message: '请选择账户',
                trigger: 'change',
              },
            ],
            click: () => {
              if (this.look) return
              // 打开弹窗
              this.initialize2()
              this.accountType = 3
              this.selectedWarehouse(1)
            },
          },
        ],
      },
      accountPagingObj: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 30, 40, 50, 100],
        layout: 'total,sizes,pager',
      },
      Method: null,
      zujianzidianArr: [
        {
          label: '线下支付',
          value: '1',
        },
        {
          label: '线上支付',
          value: '2',
        },
      ],
    }
  },
  created() {
    this.arrOption2.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '账户类型',
        prop: 'type',
        width: 90,
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=account_type_status',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
      },
      {
        label: '开户银行',
        prop: 'bankDeposit',
      },
      {
        label: '开户名',
        prop: 'openHouseName',
      },
      {
        label: '银行账户',
        prop: 'bankCardNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'enterpriseName',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入账户名称',
      },
    ]
    this.expenseTypeList()
    // this.qingqiuzidianFun()
    // 放款方式
    this.repaymentType = 2
    // 资方是不是银行
    this.capital = sessionStorage.getItem('capitalType')
    this.arrOption3.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '账户类型',
        prop: 'merchantType',
        width: 90,
      },
      {
        label: '开户名',
        prop: 'accountName',
      },
      {
        label: '银行账户',
        prop: 'accountNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'signName',
      },
    ]
  },
  methods: {
    // 账户弹窗--弹窗清空按钮事件
    searchReset2() {
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun({}, type)
    },
    // 账户弹窗--分页页码切换事件
    currentChangeScope2(currentPage) {
      this.accountPagingObj.currentPage = currentPage
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(this.searchChangeData2, type)
    },
    // 账户弹窗--分页页数切换事件
    sizeChangeScope2(pageSize) {
      // 分页页数切换事件
      this.accountPagingObj.currentPage = 1
      this.accountPagingObj.pageSize = pageSize
      let type = this.payMode == 1 ? 2 : 1

      this.selectListAccountFun(this.searchChangeData2, type)
    },
    // 账户弹窗--弹窗表格确认按钮
    cardEngth2() {
      // 保证金监管账户
      if (this.payMode == 1) {
        this.form.cashDepositTakeBillBankCardas = this.rowsData.openHouseName
      } else {
        this.form.cashDepositTakeBillBank = this.rowsData.accountName
      }

      this.cashDepositBillBankObj = {
        // 给后端的
        billBankCardaId: this.rowsData.id,
        accountType: this.rowsData.type,
      }
      this.type2 = false
    },
    // 账户弹窗--打开弹窗 选择性回显已选账户
    selectedWarehouse(type) {
      this.selectListAccountFun({}, type)
      this.type2 = true
    },
    // 账户弹窗--弹窗单选事件
    rowClick2(row) {
      if (this.selectRow2 !== row.$index) {
        this.selectRow2 = row.$index
        this.rowsData = row // 点击当前行数据
      }
    },
    // 账户弹窗初始化
    initialize2() {
      this.accountPagingObj.total = 0
      this.accountPagingObj.pageSize = 10
      this.accountPagingObj.currentPage = 1
      this.searchChangeData2 = {}
      this.selectRow2 = false
      this.arr_2 = []
    },
    searchChange2(params, done) {
      this.searchChangeData2 = params
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(params, type)
      done()
    },
    selectListAccountFun(filter = {}, type = 2) {
      this.tableLoading2 = true
      const params = {
        enterpriseName: filter.enterpriseName,
        bankCardNo: filter.bankCardNo,
        accountType: this.accountType,
        current: this.accountPagingObj.currentPage,
        size: this.accountPagingObj.pageSize,
      }
      if (this.accountType !== 1) {
        params.enterpriseId = sessionStorage.getItem('companyId')
      }
      params.type = type
      selectListAccount(params).then(({ data }) => {
        if (data.success) {
          this.tableLoading2 = false
          const { data: resData } = data
          this.accountPagingObj.total = resData.total || 0
          if (resData.records.length) {
            this.arr_2 = resData.records
            // 反选之前已选列
            let onlyId
            let platformA
            if (this.payMode == 1) {
              platformA = this.form.cashDepositTakeBillBankCardas
            } else if (this.payMode == 2) {
              platformA = this.form.cashDepositTakeBillBank
            }
            onlyId = platformA // 不是资方
            for (const [index, item] of resData.records.entries()) {
              if (
                (onlyId && item.openHouseName === onlyId) ||
                (onlyId && item.accountName === onlyId)
              ) {
                this.selectRow2 = index
                break
              } else if (
                resData.records.length - 1 == index &&
                (this.selectRow2 === 0 || this.selectRow2)
              ) {
                this.selectRow2 = false
              }
            }
          } else {
            this.arr_2 = []
          }
        }
      })
    },
    async expenseTypeList() {
      const {
        data: { code, data },
      } = await expenseTypeList('2')
      if (this.id) {
        getDetail({ id: this.id }).then(res => {
          // 查询form表单数据
          const resData = res.data
          if (resData.code == 200) {
            const formData = this.form
            const paramsData = resData.data
            formData.switch = paramsData.isPayBond
            formData.bondPayProportionStart = paramsData.bondPayProportionStart
            formData.bondPayProportionEnd = paramsData.bondPayProportionEnd
            formData.payWay = paramsData.bondPayType
            formData.releaseWay = paramsData.bondReleaseMode
          }
        })
      }
      getbillBankCardaRelation(this.id).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          for (const key in resData) {
            if (key == '3' && resData[key].length) {
              // 保证金监管账户
              const data3 = resData[key][0]
              if (data3.platformCostPayMode == 1) {
                this.form.cashDepositTakeBillBankCardas = data3.accountName
              } else {
                this.form.cashDepositTakeBillBank = data3.accountName
              }
              this.cashDepositBillBankObj = {
                // 给后端的
                billBankCardaId: data3.billBankCardaId,
                accountType: data3.accountType,
              }
            }
          }
        }
      })
      let map = {}
      if (code == 200) {
        data.forEach((item, index) => {
          map[`${item.id}`] = index
          if (item.children) {
            item.keyMap = {}
            item.children = item.children.map((citem, cindex) => {
              item.keyMap[`${citem.expenseKey}`] = cindex
              return {
                id: citem.id,
                val: false,
                expenseName: citem.expenseName,
                dialogVal: void 0,
                repaymentType: citem.repaymentType,
                goodsType: citem.goodsType,
                expenseKey: citem.expenseKey,
              }
            })
          } else {
            item.children = []
          }
        })

        if (this.id) {
          const {
            data: { data: resDataTemp },
          } = await goodsExpenseId(this.id)
          const resData = resDataTemp || []
          resData.forEach(item => {
            let index = map[`${item.expenseId}`]
            if (index || index === 0) {
              data[index].accountFrom = item.billBankCardaRelation
              // 存在费用信息
              if (item.goodsExpenseRelations) {
                item.goodsExpenseRelations.forEach(citem => {
                  let cindex = data[index].keyMap[`${citem.expenseType}`]
                  if (cindex || cindex === 0) {
                    data[index].children[cindex].val =
                      citem.status === 1 ? true : false
                    data[index].children[cindex].dialogVal = {
                      expense: citem.name,
                      typeExpense: citem.expenseType,
                      node: citem.feeNode,
                      way:
                        citem.calculation == 1
                          ? '人工核算'
                          : citem.feeFormulaName,
                      calculation: citem.calculation,
                      id: citem.id,
                      collectFeesNode: citem.collectFeesNode,
                    }
                  }
                })
              }
            }
          })
        }
        this.costList = data
        this.changRepaymentType(2)
      }
    },
    changeCapital(capital) {
      this.capital = capital
    },
    changRepaymentType(repaymentType) {
      this.repaymentType = repaymentType
      this.costList.forEach(item => {
        if (item.children && item.children.length) {
          for (const citem of item.children) {
            if (
              citem.repaymentType &&
              citem.repaymentType.indexOf(repaymentType) == -1
            ) {
              citem.val = false
            }
          }
        }
      })
    },
    chargeMethod(Method) {
      this.Method = Method
    },
    setData() {
      let resultArr = []
      this.costList.forEach(item => {
        if (item.children.length != 0) {
          let result = this.$refs[`${item.id}`][0].setData()
          resultArr.push(result)
        }
      })
      const params = {}
      const formData = this.form
      if (formData.switch != 2) {
        params.isPayBond = 1
      } else {
        params.isPayBond = 2
        params.bondPayProportionStart = formData.bondPayProportionStart
        params.bondPayProportionEnd = formData.bondPayProportionEnd
        params.bondPayType = formData.payWay
        params.bondReleaseMode = formData.releaseWay
      }
      params.goodsExpense = resultArr
      // 保证金监管账户
      params.cashDepositTakeBillBankCardas = {
        billBankCardaId: this.cashDepositBillBankObj.billBankCardaId,
        accountType: 3,
        platformCostPayMode: this.payMode,
      }
      const objParams = { ...this.formParamsData, ...params }
      this.$store.commit('SET_FORM_PARAMS_DATA', objParams)
    },
    chargeMethodEmit(val) {
      this.$emit('chargeMethodEmit', val)
    },
    qingqiuzidianFun() {
      // getDictionary({ code: 'goods_bond_pay_type' })
      //   .then(({ data }) => {
      //     if (data.success) {
      //       const { data: resData } = data
      //       const arr = []
      //       for (const item of resData) {
      //         arr.push({
      //           label: item.dictValue,
      //           value: item.dictKey,
      //         })
      //       }
      //       this.zujianzidianArr = arr
      //     }
      //   })
      //   .catch(() => {})
    },
  },
  components: {
    temp,
  },
  computed: {
    ...mapState({
      formParamsData: state => state.common.formParamsData,
    }),
  },
}
</script>
<style lang="scss" scoped>
.guarantee {
  // 选择资方账户样式修改
  ::v-deep {
    #my-corre-input-select {
      cursor: pointer;
    }
    .el-input__suffix {
      font-size: 16px;
      color: rgba(112, 112, 112, 100);
    }
    // 表格操作栏
    .management-accounts .avue-form__menu {
      display: none;
    }
  }
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 35px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetBottom {
    // border: 2px solid RGB(245, 245, 245);
    // border-radius: 10px;
    // padding: 35px 30px 0 30px;
    // box-sizing: border-box;

    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
      .avue-form__menu {
        display: none;
      }
      .avue-form__row {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
    }
  }
  .guaranteeSetBottom-switch {
    .capital-box {
      .chaldren-for-box {
        display: flex;
        align-items: center;
        height: 50px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 100);
        text-align: center;
        border: 1px solid rgba(228, 228, 228, 100);
        padding: 10px 20px;
        box-sizing: border-box;
        margin-bottom: 12px;
        transition: border-color 0.3s;
        cursor: pointer;

        &::last-child {
          margin-bottom: 0;
        }

        &:hover {
          border-color: #1877f5;
        }
        .switch-container {
          // switch组件样式修改
          ::v-deep {
            .el-switch__core {
              height: 30px;
              border-radius: 20px;

              &::after {
                width: 22px;
                height: 22px;
                top: 3px;
                left: 3px;
              }
            }
            .el-switch.is-checked .el-switch__core::after {
              margin-left: -25px;
              left: 100%;
            }
          }
        }
        .type-container {
          border-radius: 43px;
          background-color: rgba(234, 236, 241, 100);
          color: rgba(0, 7, 42, 100);
          font-size: 12px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 10px;
          box-sizing: border-box;
          display: inline;
          margin-left: 20px;
        }

        .Interest-rules-container {
          flex: 1;
          text-align: right;
          .rules-info {
            height: 20px;
            width: 90%;
            line-height: 20px;
            color: rgba(16, 16, 16, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
            display: inline-block;
          }
          .tip-container {
            width: 84px;
            height: 20px;
            line-height: 20px;
            color: rgba(154, 154, 154, 100);
            font-size: 14px;
            text-align: right;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
    }
  }
  .choose-cost {
    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
    }
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
}
</style>
