<template>
  <basic-container>
    <div class="tageds">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane :key="item.name" :name="item.name" v-for="item in editableTabs">
          <span slot="label">
            <el-button-group>
              <el-button
                type="primary"
                size="medium"
                :plain="item.name == activeName ? false : true"
                :class="{
                  'bord-radius-left': item.name == '1',
                  'bord-radius-right': item.name == editableTabs[editableTabs.length - 1].name,
                }"
              >
                {{ item.title }}
              </el-button>
            </el-button-group>
          </span>
          <!-- 产品信息 -->
          <ProProduct ref="product" v-if="item.name == '1'" :look="look" />
          <!-- 数据收集 -->
          <ProGather ref="gather" v-else-if="item.name == '2'" :look="look" />
          <!-- 审批流程 -->
          <PrdApprovalProcess ref="approvalProcess" v-else-if="item.name == '3'" :look="look" />
        </el-tab-pane>
      </el-tabs>
      <!-- 操作按钮 -->
      <div class="updata" v-if="!look">
        <el-button @click="handleback">取消</el-button>
        <template v-if="zhanshiBtn">
          <el-button type="success" @click="handleSubmit('updata')">上架</el-button>
          <el-button type="primary" @click="handleSubmit('save')">保存</el-button>
        </template>
      </div>
    </div>
  </basic-container>
</template>

<script>
import ProProduct from './manyCapitalProductsDetailsCom/product.vue'
import ProGather from './manyCapitalProductsDetailsCom/gather.vue'
import PrdApprovalProcess from './manyCapitalProductsDetailsCom/approvalProcess.vue'
import { mapState } from 'vuex'
// import { serveDataPurchasing, upDataPurchasing, onShelf } from '@/api/goods/product-group'
import { save, update, onShelf } from '@/api/goods/manyCapitalProducts.js'

export default {
  name: 'productGroupDetails',
  components: {
    ProProduct,
    ProGather,
    PrdApprovalProcess,
  },
  data() {
    return {
      id: this.$route.query.id,
      activeName: '1',
      editableTabs: [
        {
          title: '产品信息',
          name: '1',
          paths: 'product',
        },
        {
          title: '数据收集',
          name: '2',
          paths: 'gather',
        },
        {
          title: '审批流程',
          name: '3',
          paths: 'approvalProcess',
        },
      ],
      look: false,
      zhanshiBtn: false,
    }
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
      cpzvalidChunType: state => state.common.cpzvalidChunType,
    }),
  },
  created() {
    if (sessionStorage.getItem('look') == 'true') {
      this.look = true
    }
    this.yanshiqidongBtnFun()
  },
  methods: {
    yanshiqidongBtnFun() {
      setTimeout(() => {
        this.zhanshiBtn = true
      }, 4000)
    },
    // 应收的取消
    handleback() {
      this.$router.$avueRouter.closeTag()
      this.$router.back()
    },
    handleSubmit(doing) {
      this.$refs.product[0].handleSubmit(doing == 'save' ? doing : '')
      setTimeout(() => {
        this.$refs.gather[0].setData()
      }, 100)
      setTimeout(() => {
        this.$refs.approvalProcess[0].setData()
        let jiaoyanSuccess = true
        for (const key in this.cpzvalidChunType) {
          if (!this.cpzvalidChunType[key]) {
            jiaoyanSuccess = false
            break
          }
        }
        // 重置校验状态
        this.$store.commit('RESET_CPZ_VALID_CHUN_TYPE')
        if (!jiaoyanSuccess) return
        if (!this.id) {
          // serveDataPurchasing(this.formParamsDataed)
          save(this.formParamsDataed)
            .then(res => {
              const resData = res.data
              this.$message.success('已保存')
              this.id = resData.data
              if (doing == 'updata') {
                this.onShelf(resData.data)
              }
            })
            .catch(() => {})
        } else {
          const parimsId = {
            id: this.id,
          }
          const objParams = { ...parimsId, ...this.formParamsDataed }
          // upDataPurchasing(objParams)
          update(objParams)
            .then(res => {
              const resDate = res.data
              if (resDate.code == 200) {
                this.$message.success('已保存')
                if (doing == 'updata') {
                  this.onShelf(this.id)
                }
              }
            })
            .catch(() => {})
        }
      }, 200)
    },
    onShelf(idS) {
      onShelf(idS)
        .then(res => {
          // 上架
          const resData = res.data
          if (resData.code == 200) {
            this.$message.success('已上架')
            this.$router.$avueRouter.closeTag()
            this.$router.push({ path: '/goods/manyCapitalProducts' })
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss">
.tageds {
  .bord-radius-left {
    border-radius: 20px 0 0 20px !important;
    border-left-color: #b3d8ff !important;
  }
  .bord-radius-right {
    border-radius: 0 20px 20px 0 !important;
    border-right-color: #b3d8ff !important;
  }
  .el-button--primary.is-plain {
    // background: #ffffff;
    border-color: #a3cdf8;
  }
  .el-tabs__item {
    padding: 0 !important;
  }
  .el-tabs--card > .el-tabs__header .el-tabs__item {
    border: none;
  }
  .el-tabs--card > .el-tabs__header {
    border-bottom: none;
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-bottom: 1.5%;
    margin-top: 1%;
  }
}
</style>
