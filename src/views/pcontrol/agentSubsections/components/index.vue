<template>
  <div class="guarantee">
    <div class="guarantee-top">
      <h1 class="titleBottom">{{ costObj.expenseName }}</h1>
      <div class="item" v-if="costObj.enterpriseTypeStr == '资金方'">
        <avue-form
          class="management-accounts"
          v-model="managementAccountsForm"
          :option="managementAccountsOption"
        />
      </div>
      <div
        class="item"
        v-else-if="costObj.enterpriseTypeStr != '资金方' && Method != 1"
      >
        <!-- 平台账号的 -->

        <avue-form
          class="management-accounts"
          v-model="platformAccountsForm"
          :option="platformAccountsOption"
        />
      </div>

      <div class="guaranteeSetBottom-switch" @click="location(1)">
        <div class="capital-box">
          <div
            class="chaldren-for-box"
            v-for="(item, index) in costObj.children"
            :key="item.id"
            @click="costDialog(item, index)"
            v-show="item.goodsType.indexOf(goodsType) != -1"
          >
            <div class="switch-container" @click.stop="">
              <el-switch
                v-model="item.val"
                :disabled="isDisabled(item.repaymentType)"
                active-color="#13ce66"
                inactive-color="#E2E2E3"
                :active-value="true"
                :inactive-value="false"
                :width="50"
              >
              </el-switch>
            </div>
            <div class="type-container">{{ item.expenseName }}</div>
            <div class="Interest-rules-container">
              <span
                class="rules-info"
                v-if="item.dialogVal && item.val == true"
              >
                {{ item.dialogVal.expense }}：{{ item.dialogVal.way }}
              </span>
              <span class="tip-container" v-else>选择费用规则</span>
              <svg-icon
                icon-class="icon-youjiantou"
                style="fill: #cccccc; font-size: 16px"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 费用弹窗 -->
    <el-dialog
      title="选择费用"
      :visible.sync="type1"
      :modal-append-to-body="false"
      class="avue-dialog choose-cost"
      width="70%"
    >
      <!--
        arrOption: 配置 arr
        data: 数据源 arr
        search.sync: 获取实时的搜索值 obj
        search-change: 搜索点击事件 fan
        current-change: 分页功能页码点击事件 fan
        on-load: 首次加载事件 fan
        size-change: 分页总页数事件 fan
        selection-change: checkbox事件 fan
        table-loading: 表单加载动画 boolean
        page: 配置分页信息 obj
        -->
      <avue-crud
        ref="crud1"
        :option="arrOption1"
        :data="arrS"
        @row-click="rowClick"
        @search-change="searchChange1"
        @search-reset="searchReset"
        :search.sync="dialogSearch1"
        :table-loading="loading"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow" :label="row.$index">&nbsp;</el-radio>
        </template>
        <template slot-scope="{ row }" slot="typeExpense">
          <el-tag type="info">{{ row.$typeExpense }}</el-tag>
        </template>
        <template slot-scope="{ row }" slot="node">
          <el-tag type="info">{{ row.$node }}</el-tag>
        </template>
        <template slot-scope="{ row }" slot="collectFeesNode">
          <el-tag type="info">{{ row.$collectFeesNode }}</el-tag>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="cardfals">取 消</el-button>
        <el-button @click="radioCardEngth" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 账户弹窗 -->
    <el-dialog
      title="选择账户"
      :visible.sync="type2"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="avue-dialog"
      width="70%"
    >
      <avue-crud
        ref="crud"
        :option="payMode == 1 ? arrOption2 : arrOption3"
        :data="arr_2"
        :search.sync="dialogSearch2"
        @row-click="rowClick2"
        @search-reset="searchReset2"
        @search-change="searchChange2"
        @size-change="sizeChangeScope2"
        @on-load="dialogOnLoad2"
        :table-loading="tableLoading2"
        :page.sync="accountPagingObj"
        @current-change="currentChangeScope2"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow2" :label="row.$index">&nbsp;</el-radio>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="type2 = false">取 消</el-button>
        <el-button @click="cardEngth2" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  // serveExpense,
  // setShelf,
  getExpenseList,
  selectListByExpenseType,
  // getExpenseDetail,
  // getDictionary,
  selectListAccount,
  // getbillBankCardaRelation,
} from '@/api/goods/pcontrol/pinformation'
import { mapState } from 'vuex'
import { tree } from '@/api/plugin/workflow/category'
export default {
  name: '',
  created() {
    this.arrOption1.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '费用名称',
        prop: 'expense',
        search: true,
        placeholder: '按名称搜索',
      },
      {
        label: '费用类型',
        prop: 'typeExpense',
        search: false,
        placeholder: '按类型搜索',
        dataType: 'number',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '计算节点',
        prop: 'node',
        search: true,
        placeholder: '按节点搜索',
        dataType: 'number',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_fee_node',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '收费节点',
        prop: 'collectFeesNode',
        search: true,
        placeholder: '按节点搜索',
        dataType: 'number',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_fee_node',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '计费方式',
        prop: 'way',
        search: true,
        placeholder: '按方式搜索',
      },
    ]

    this.platformAccountsOption.column = [
      {
        label: '支付方式',
        prop: 'platformCostPayMode',
        type: 'select',
        span: 12,
        labelWidth: 85,
        placeholder: '请选择支付方式',
        disabled: this.look,
        display: true,
        dicData: this.zujianzidianArr,
        // dicUrl:
        //   '/api/blade-system/dict-biz/dictionary?code=goods_bond_pay_type',
        // props: {
        //   label: 'dictValue',
        //   value: 'dictKey',
        // },
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择支付方式',
            trigger: 'change',
          },
        ],
        change: ({ value }) => {
          if (value) {
            this.payMode = value
            if (value === 1) {
              this.platformAccountsOption.column[1].display = true
              this.platformAccountsOption.column[2].display = false
              this.platformAccountsForm.platformBillBank = ''
              if (this.look) return
              // 将input 变成只读
              setTimeout(() => {
                const domArr = document.querySelectorAll('.el-input__inner')
                for (const item of domArr) {
                  if (item.placeholder === '请选择平台账号') {
                    item.readOnly = true
                    item.id = 'my-corre-input-select'
                    break
                  }
                }
              }, 100)
            } else {
              this.platformAccountsOption.column[1].display = false
              this.platformAccountsForm.platformBillBankCardas = ''
              if (this.costObj.enterpriseTypeStr != '平台方') {
                this.platformAccountsOption.column[2].display = true
              }
              this.platformBillBankObj.accountType = this.costObj.accountType
            }
          } else {
            this.platformAccountsOption.column[1].display = false
            this.platformAccountsOption.column[2].display = false
          }
        },
      },
      {
        label: '账户',
        placeholder: '请选择账户',
        prop: 'platformBillBankCardas',
        disabled: this.look,
        display: false,
        type: 'input',
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择账户',
            trigger: 'change',
          },
        ],
        click: () => {
          if (this.look) return
          // 打开弹窗
          if (this.platformAccountsForm.platformCostPayMode) {
            this.initialize2()
            this.accountType = this.costObj.accountType
            this.selectedWarehouse(2)
          } else {
            this.$message.warning('请选择支付方式')
          }
        },
      },
      {
        label: '账户',
        placeholder: '请选择账户',
        prop: 'platformBillBank',
        disabled: this.look,
        display: false,
        type: 'input',
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择账户',
            trigger: 'change',
          },
        ],
        click: () => {
          if (this.look) return
          // 打开弹窗
          this.initialize2()
          this.accountType = this.costObj.accountType
          this.selectedWarehouse(1)
        },
      },
    ]
    this.arrOption2.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '账户类型',
        prop: 'type',
        width: 90,
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=account_type_status',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
      },
      {
        label: '开户银行',
        prop: 'bankDeposit',
      },
      {
        label: '开户名',
        prop: 'openHouseName',
      },
      {
        label: '银行账户',
        prop: 'bankCardNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'enterpriseName',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入账户名称',
      },
    ]

    // 资方账号的配置
    this.managementAccountsOption.column = [
      {
        label: '支付方式',
        prop: 'capitalCostPayMode',
        type: 'select',
        span: 12,
        labelWidth: 85,
        placeholder: '请选择支付方式',
        disabled: this.look,
        display: true,
        dicData: this.zujianzidianArr,
        // dicUrl:
        //   '/api/blade-system/dict-biz/dictionary?code=goods_bond_pay_type',
        // props: {
        //   label: 'dictValue',
        //   value: 'dictKey',
        // },
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择支付方式',
            trigger: 'change',
          },
        ],
        change: ({ value }) => {
          if (value) {
            this.payMode = value

            if (value === 1) {
              this.managementAccountsOption.column[1].display = true
              this.managementAccountsOption.column[2].display = false
              this.managementAccountsForm.capitalBillBank = ''
              if (this.look) return
              // 将input 变成只读
              setTimeout(() => {
                const domArr = document.querySelectorAll('.el-input__inner')
                for (const item of domArr) {
                  if (item.placeholder === '请选择账户') {
                    item.readOnly = true
                    item.id = 'my-corre-input-select'
                    break
                  }
                }
              }, 100)
            } else if (value === 2 && this.capital != 1) {
              this.managementAccountsOption.column[1].display = false
              this.managementAccountsForm.capitalBillBankCardas = ''
              this.managementAccountsOption.column[2].display = true
              this.platformBillBankObj.accountType = this.costObj.accountType
            } else {
              this.managementAccountsForm.capitalBillBank = ''
              this.managementAccountsForm.capitalBillBankCardas = ''
              this.managementAccountsOption.column[1].display = false
              this.managementAccountsOption.column[2].display = false
            }
          } else {
            this.managementAccountsForm.capitalBillBank = ''
            this.managementAccountsForm.capitalBillBankCardas = ''
            this.managementAccountsOption.column[1].display = false
            this.managementAccountsOption.column[2].display = false
          }
        },
      },
      {
        label: '账户',
        placeholder: '请选择账户',
        prop: 'capitalBillBankCardas',
        disabled: this.look,
        display: false,
        type: 'input',
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择账户',
            trigger: 'change',
          },
        ],
        click: () => {
          if (this.look) return
          // 打开弹窗
          if (this.managementAccountsForm.capitalCostPayMode) {
            this.initialize2()
            this.accountType = this.costObj.accountType
            this.selectedWarehouse(2)
          } else {
            this.$message.warning('请选择支付方式')
          }
        },
      },
      {
        label: '账户',
        placeholder: '请选择账户',
        prop: 'capitalBillBank',
        disabled: this.look,
        display: false,
        type: 'input',
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择账户',
            trigger: 'change',
          },
        ],
        click: () => {
          if (this.look) return
          // 打开弹窗
          this.initialize2()
          this.accountType = this.costObj.accountType
          this.selectedWarehouse(1)
        },
      },
      // {
      //   label: '虚拟账号',
      //   placeholder: '请选择虚拟账号',
      //   prop: 'capitalBillBankCardas',
      //   type: 'input',
      //   disabled: this.look,
      //   display: false,
      //   clearable: false,
      //   suffixIcon: 'el-icon-arrow-right',
      //   span: 12,
      //   rules: [
      //     {
      //       required: true,
      //       message: '请选择虚拟账号',
      //       trigger: 'change',
      //     },
      //   ],
      //   click: () => {
      //     if (this.look) return
      //     // 打开弹窗
      //     this.initialize2()
      //     this.accountType = 2
      //     this.selectedWarehouse()
      //   },
      // },
    ]

    this.arrOption3.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '商户号',
        prop: 'merchantNo',
        width: 100,
      },
      {
        label: '账户类型',
        prop: 'merchantType',
        width: 90,
      },
      {
        label: '开户名',
        prop: 'accountName',
      },
      {
        label: '银行账户',
        prop: 'accountNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'signName',
      },
    ]

    // this.onLoadData()
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
    }),
  },
  props: {
    costObj: {
      type: Object,
      require: true,
    },
    look: Boolean,
    goodsType: String,
    capital: String,
    repaymentType: {
      type: String | Number,
    },
    Method: {
      type: String | Number,
    },
    zujianzidianArr: {
      type: Array,
      require: true,
    },
  },
  data() {
    return {
      id: this.$route.query.id,
      type1: false,
      type2: false,
      dialogSearch1: {},
      payMode: null,
      // 各种费用的费用规则
      arrOption1: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchMenuSpan: 7, // 搜索菜单栏宽带
        maxHeight: 300,
        index: false,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [],
      },
      costIndex: 0,
      selectRow: '',
      selectIdBefore: void 0,
      selectIdAfter: void 0,
      selectRowData: void 0,
      arrS: [], // 弹窗显示规则数组
      arr: [], // 平台费用规则
      arr2: [], // 资方费用规则
      //   页数
      page: {
        // total: 27,
        // pageSize: 10,
      },
      selStatus: false,
      // 资方账户配置
      platformBillBankObj: {},
      // 平台站账户配置
      platformAccountsForm: {
        platformBillBankCardas: '',
        platformBillBank: '',
      },
      platformAccountsOption: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 110,
        gutter: 100,
        column: [],
      },
      rowsData: void 0,
      arr_2: [],
      selectRow2: '',
      tableLoading2: true,
      accountType: 0,
      searchChangeData2: {},
      accountPagingObj: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 30, 40, 50, 100],
        layout: 'total,sizes,pager',
      },
      // 普通 账户
      arrOption2: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },

      // 虚拟账户
      arrOption3: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },

      // 资方账户
      capitalLockNum: 0, // 避免初次加载资方账户显示异常
      capitalBillBankObj: {},
      cashDepositBillBankObj: {},
      // 资方账户配置
      managementAccountsForm: {
        capitalBillBankCardas: '',
      },
      managementAccountsOption: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 110,
        gutter: 100,
        column: [],
      },
    }
  },
  methods: {
    setData() {
      const params = {}
      params.goodsExpenseRelations = []
      const costObj = this.costObj
      for (const item of this.costObj.children) {
        // 如果属于自动放款不能存在人工核算
        if (item.dialogVal) {
          const lendingMethodSess = sessionStorage.getItem('lendingMethod')
          if (item.dialogVal.calculation == 1 && lendingMethodSess === '1') {
            this.$emit('chargeMethodEmit', true)
          }
          if (item.val) {
            params.goodsExpenseRelations.push({
              goodsExpenseRuleId: item.dialogVal.id,
              feeNode: item.dialogVal.node,
              calculation: item.dialogVal.calculation,
              status: item.val ? 1 : 0,
              type: 1,
              expenseTypeId: item.id,
              collectFeesNode: item.dialogVal.collectFeesNode,
            })
          }
        }
      }
      // 账户信息
      const accountInfo = {}
      if (costObj.enterpriseType == 2) {
        accountInfo.billBankCardaId = this.capitalBillBankObj.billBankCardaId
        accountInfo.accountType = this.capitalBillBankObj.accountType
        accountInfo.platformCostPayMode =
          this.managementAccountsForm.capitalCostPayMode
        accountInfo.expenseKey = costObj.expenseKey
      }
      if (costObj.enterpriseType != 2 && this.Method != 1) {
        accountInfo.billBankCardaId = this.platformBillBankObj.billBankCardaId
        accountInfo.platformCostPayMode =
          this.platformAccountsForm.platformCostPayMode
        accountInfo.accountType = this.platformBillBankObj.accountType
        accountInfo.expenseKey = costObj.expenseKey
      }

      params.billBankCardaRelation = accountInfo
      params.enterpriseType = costObj.enterpriseType
      params.enterpriseTypeStr = costObj.enterpriseTypeStr
      params.expenseKey = costObj.expenseKey
      params.expenseName = costObj.expenseName
      params.expenseId = costObj.id
      return params
    },
    searchChange1(params, done) {
      // card搜索事件
      let ar = []
      this.arr2.map(item => {
        const ex = item.expense.indexOf(params.expense) != -1
        const ty = item.$typeExpense.indexOf(params.typeExpense) != -1
        const no = item.$node.indexOf(params.node) != -1
        const wa = item.way ? item.way.indexOf(params.way) != -1 : null
        if (ex || ty || no || wa) {
          ar.push(item)
        }
      })
      this.arrS = ar
      done()
    },
    rowClick(row) {
      if (this.selectRow !== row.$index) {
        this.selectRow = row.$index
        this.selectRowData = row
        this.selectIdBefore = row.id
      }
      this.selStatus = true
    },
    searchReset() {
      this.arrS = this.arr2
    },
    //选择费用取消事件
    cardfals() {
      this.type1 = false
    },
    // 点击选择按钮
    radioCardEngth() {
      if (this.selStatus == true) {
        if (sessionStorage.getItem('lendingMethod') === '1') {
          if (
            this.selectRowData.way == '人工核算' &&
            this.selectRowData.collectFeesNode == 8
          ) {
            return this.$message.warning('自动放款融资申请不能采用人工核算规则')
          }
        }
        this.costObj.children[this.costIndex].dialogVal = this.selectRowData
        this.selectIdAfter = this.selectIdBefore
      }
      this.selStatus = false
      this.type1 = false
    },
    location(inx) {
      this.crudIndex = inx
    },
    // 计算每个item 是否可被选
    isDisabled(str) {
      if (str) {
        return str.indexOf(this.repaymentType) == -1
      } else {
        return false
      }
    },
    costDialog(type, index) {
      if (type.repaymentType.indexOf(this.repaymentType) == -1) {
        this.$message.warning('该还款类型不支持该账户类型')
        return
      }
      if (!type.val) {
        this.$message.warning('请启用该费用')
        return
      }
      this.arrS = []
      this.loading = true
      this.costIndex = index // 赋值时的依据下标
      this.selectListByFun(type)
      this.type1 = true
      // const exception = setInterval(() => {
      //   if (this) {
      //     clearInterval(exception)
      //     this.checkUpType()
      //   }
      // }, 100)
    },

    // 获取弹窗的数据
    selectListByFun(type) {
      const params = {
        expenseType: type.expenseKey, // 启用否
        type: +this.goodsType, // 产品类型
      }
      selectListByExpenseType(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          this.arr2 = []
          for (const item of resData.data) {
            this.arr2.push({
              expense: item.name,
              typeExpense: item.expenseType,
              node: item.feeNode,
              way: item.calculation == 1 ? '人工核算' : item.feeFormulaName,
              calculation: item.calculation,
              id: item.id,
              collectFeesNode: item.collectFeesNode,
            })
          }
          this.arrS = this.arr2
          if (type.dialogVal) {
            const selectRowIndex = this.arr2.findIndex(
              item => item.id === type.dialogVal.id
            )
            // 过滤后无数据 ，说明该规则不存在，属于旧数据，无法在弹窗回显
            if (selectRowIndex !== -1) {
              this.selectRow = type.dialogVal.$index || selectRowIndex
            } else {
              this.selectRow = 10000
            }
          } else {
            this.selectRow = 10000
          }
          this.loading = false
        }
      })
    },
    // 账户弹窗初始化
    initialize2() {
      this.accountPagingObj.total = 0
      this.accountPagingObj.pageSize = 10
      this.accountPagingObj.currentPage = 1
      this.searchChangeData2 = {}
      this.selectRow2 = false
      this.arr_2 = []
    },

    // selectedWarehouse() {
    //   this.selectListAccountFun()
    //   this.type2 = true
    // },

    // 账户数据
    selectListAccountFun(filter = {}, type) {
      this.tableLoading2 = true
      const params = {
        enterpriseName: filter.enterpriseName,
        bankCardNo: filter.bankCardNo,
        accountType: this.accountType,
        current: this.accountPagingObj.currentPage,
        size: this.accountPagingObj.pageSize,
      }
      if (this.accountType !== 1) {
        params.enterpriseId = sessionStorage.getItem('companyId')
      }
      params.type = type
      selectListAccount(params).then(({ data }) => {
        if (data.success) {
          this.tableLoading2 = false
          const { data: resData } = data
          this.accountPagingObj.total = resData.total || 0
          if (resData.records.length) {
            this.arr_2 = resData.records
            // 反选之前已选列
            let onlyId
            let platformA
            if (this.payMode == 1 && this.costObj.enterpriseType != 2) {
              platformA = this.platformAccountsForm.platformBillBankCardas
            } else if (this.payMode == 2 && this.costObj.enterpriseType != 2) {
              platformA = this.platformAccountsForm.platformBillBank
            } else if (this.payMode == 1 && this.costObj.enterpriseType == 2) {
              platformA = this.managementAccountsForm.capitalBillBankCardas
            } else if (this.payMode == 2 && this.costObj.enterpriseType == 2) {
              platformA = this.managementAccountsForm.capitalBillBank
            }
            if (this.costObj.enterpriseType != 2 && platformA) {
              onlyId = platformA // 不是资方
            } else if (this.costObj.enterpriseType == 2 && platformA) {
              onlyId = platformA // 资方
            }
            for (const [index, item] of resData.records.entries()) {
              if (
                (onlyId && item.openHouseName === onlyId) ||
                (onlyId && item.accountName === onlyId)
              ) {
                this.selectRow2 = index
                break
              } else if (
                resData.records.length - 1 == index &&
                (this.selectRow2 === 0 || this.selectRow2)
              ) {
                this.selectRow2 = false
              }
            }
          } else {
            this.arr_2 = []
          }
        }
      })
    },
    // 账户的
    rowClick2(row) {
      if (this.selectRow2 !== row.$index) {
        this.selectRow2 = row.$index
        this.rowsData = row // 点击当前行数据
      }
    },
    // 账户弹窗--弹窗搜索事件
    searchChange2(params, done) {
      this.searchChangeData2 = params
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(params, type)
      done()
    },
    // 账户弹窗--弹窗清空按钮事件
    searchReset2() {
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun({}, type)
    },
    // 账户弹窗--分页页码切换事件
    currentChangeScope2(currentPage) {
      this.accountPagingObj.currentPage = currentPage
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(this.searchChangeData2, type)
    },
    // 账户弹窗--分页页数切换事件
    sizeChangeScope2(pageSize) {
      // 分页页数切换事件
      this.accountPagingObj.currentPage = 1
      this.accountPagingObj.pageSize = pageSize
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(this.searchChangeData2, type)
    },
    // 账户弹窗--弹窗表格确认按钮
    cardEngth2() {
      // f (this.accountType === 2 && this.rowsData) {
      //   this.managementAccountsForm.capitalBillBankCardas =
      //     this.rowsData.openHouseName
      //   this.capitalBillBankObj = {
      //     // 给后端的
      //     billBankCardaId: this.rowsData.id,
      //     accountType: this.rowsData.type,
      //   }
      // } else if (this.accountType === 1 && this.rowsData) {
      //   // 平台
      //   this.platformAccountsForm.platformBillBankCardas =
      //     this.rowsData.openHouseName
      //   this.platformBillBankObj = {
      //     // 给后端的
      //     billBankCardaId: this.rowsData.id,
      //     accountType: this.rowsData.type,
      //   }
      // }
      if (this.rowsData && this.costObj.enterpriseType != 2) {
        // 平台
        if (this.payMode == 1) {
          this.platformAccountsForm.platformBillBankCardas =
            this.rowsData.openHouseName
        } else {
          this.platformAccountsForm.platformBillBank = this.rowsData.accountName
        }
        this.platformBillBankObj = {
          // 给后端的
          billBankCardaId: this.rowsData.id,
          accountType: this.costObj.accountType,
        }
      } else if (this.rowsData && this.costObj.enterpriseType == 2) {
        if (this.managementAccountsForm.capitalCostPayMode == 1) {
          this.managementAccountsForm.capitalBillBankCardas =
            this.rowsData.openHouseName
        } else {
          this.managementAccountsForm.capitalBillBank =
            this.rowsData.accountName
        }
        this.capitalBillBankObj = {
          // 给后端的
          billBankCardaId: this.rowsData.id,
          accountType: this.costObj.accountType,
        }
      }
      this.type2 = false
    },
    // 账户弹窗--打开弹窗 选择性回显已选账户
    selectedWarehouse(type) {
      this.selectListAccountFun({}, type)
      this.type2 = true
    },
    onLoadData() {
      const params = {
        status: 1, // 启用否
        type: +this.goodsType, // 产品类型
      }
      getExpenseList(params).then(res => {
        // 选择费用list数据
        const resData = res.data
        if (resData.code == 200) {
          this.arr = []
          for (const item of resData.data) {
            this.arr.push({
              expense: item.name,
              typeExpense: item.expenseType,
              node: item.feeNode,
              way: item.calculation == 1 ? '人工核算' : item.feeFormulaName,
              calculation: item.calculation,
              expenseTypeId: item.id,
            })
          }
          // if (this.id) {
          //   this.getData()
          // }
        }
      })
    },
    // 根据所属资方进行是否选择资方账户开启与关闭
    earlySettcapitalDisabledFun() {
      this.capitalLockNum++
      const capitalBillBankCardas = this.findObject(
        this.managementAccountsOption.column,
        'capitalBillBankCardas'
      )
      if (sessionStorage.getItem('capitalType') !== '1') {
        // 避免初次加载的数据被清除
        if (this.capitalLockNum > 1) {
          this.managementAccountsForm.capitalBillBankCardas = ''
          this.capitalBillBankObj = {}
        }
        capitalBillBankCardas.display = true
        if (this.look) return
        setTimeout(() => {
          // 将input 变成只读
          const domArr = document.querySelectorAll('.el-input__inner')
          for (const item of domArr) {
            if (item.placeholder === '请选择资方账号') {
              item.readOnly = true
              item.id = 'my-corre-input-select'
              break
            }
          }
        }, 100)
      } else {
        capitalBillBankCardas.display = false
        this.managementAccountsForm.capitalBillBankCardas = ''
        this.capitalBillBankObj = {}
      }
    },
    getData() {
      //   getExpenseDetail(this.id).then(res => {
      //     // 获取已选择的其他费用子表单数据
      //     const resData = res.data
      //     if (resData.code == 200) {
      //       const dynArr2 = []
      //       const arrFData = this.arr.map(item => item.id) // 启动中的费用
      //       for (const key in resData.data) {
      //         if (key === '1') {
      //           for (const item of resData.data[key].filter(
      //             iteme => arrFData.includes(iteme.id) // 过滤掉禁用的费用
      //           )) {
      //             for (const items of this.obj) {
      //               if (items.costTypeKey == item.expenseType) {
      //                 items.val = item.status === 1 ? true : false
      //                 items.dialogVal = {
      //                   expense: item.name,
      //                   typeExpense: item.expenseType,
      //                   node: item.feeNode,
      //                   way:
      //                     item.calculation == 1
      //                       ? '人工核算'
      //                       : item.feeFormulaName,
      //                   calculation: item.calculation,
      //                   id: item.id,
      //                 }
      //               }
      //             }
      //           }
      //         } else if (key === '2') {
      //           for (const item of resData.data[key].filter(
      //             iteme => arrFData.includes(iteme.id) // 过滤掉禁用的费用
      //           )) {
      //             dynArr2.push({
      //               expense: item.name,
      //               typeExpense: item.expenseType,
      //               node: item.feeNode,
      //               way: item.calculation == 1 ? '人工核算' : item.feeFormulaName,
      //               calculation: item.calculation,
      //               id: item.id,
      //             })
      //           }
      //           this.obj1.dynamic = dynArr2
      //         }
      //       }
      //     }
      //   })
      //   // 查询产品关联账户
      //   getbillBankCardaRelation(this.id).then(({ data }) => {
      //     if (data.success) {
      //       const { data: resData } = data
      //       for (const key in resData) {
      //         if (key === '1' && resData[key].length) {
      //           // 平台
      //           const data1 = resData[key][0]
      //           this.platformAccountsForm.platformBillBankCardas =
      //             data1.accountName
      //           this.platformBillBankObj = {
      //             // 给后端的
      //             billBankCardaId: data1.billBankCardaId,
      //             accountType: data1.accountType,
      //           }
      //         }
      //       }
      //     }
      //   })
    },
  },
  watch: {
    capital: {
      handler(val) {
        this.$nextTick(() => {
          this.earlySettcapitalDisabledFun()
        })
        if (val == 1 && this.Method == 1) {
          this.managementAccountsForm.capitalBillBank = ''
          this.managementAccountsForm.capitalBillBankCardas = ''
          this.managementAccountsOption.column[1].display = false
          this.managementAccountsOption.column[2].display = false
        }
      },

    },
    Method: {
      handler(val) {
        if (val == 1 && this.capital == 1) {
          this.managementAccountsForm.capitalBillBank = ''
          this.managementAccountsForm.capitalBillBankCardas = ''
          this.managementAccountsOption.column[1].display = false
          this.managementAccountsOption.column[2].display = false
        }
      },
    },
    'costObj.accountFrom': {
      handler(val) {
        if (this.id) {
          if (val) {
            if (this.costObj.enterpriseTypeStr == '资金方') {
              this.managementAccountsForm.capitalCostPayMode =
                val.platformCostPayMode
              if (val.platformCostPayMode == 1) {
                this.managementAccountsForm.capitalBillBankCardas =
                  val.accountName
              } else {
                this.managementAccountsForm.capitalBillBank = val.accountName
              }
              this.capitalBillBankObj = {
                // 给后端的
                billBankCardaId: val.billBankCardaId,
                accountType: val.accountType,
              }
            } else {
              this.platformAccountsForm.platformCostPayMode =
                val.platformCostPayMode
              if (val.platformCostPayMode == 1) {
                this.platformAccountsForm.platformBillBankCardas =
                  val.accountName
              } else {
                this.platformAccountsForm.platformBillBank = val.accountName
              }
              this.platformBillBankObj = {
                // 给后端的
                billBankCardaId: val.billBankCardaId,
                accountType: val.accountType,
              }
            }
          }
        }
      },
      deep: tree,
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
.guarantee {
  // 选择资方账户样式修改
  ::v-deep {
    #my-corre-input-select {
      cursor: pointer;
    }
    .el-input__suffix {
      font-size: 16px;
      color: rgba(112, 112, 112, 100);
    }
    // 表格操作栏
    .management-accounts .avue-form__menu {
      display: none;
    }
  }
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 35px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetBottom {
    // border: 2px solid RGB(245, 245, 245);
    // border-radius: 10px;
    // padding: 35px 30px 0 30px;
    // box-sizing: border-box;

    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
      .avue-form__menu {
        display: none;
      }
      .avue-form__row {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
    }
  }
  .guaranteeSetBottom-switch {
    .capital-box {
      .chaldren-for-box {
        display: flex;
        align-items: center;
        height: 50px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 100);
        text-align: center;
        border: 1px solid rgba(228, 228, 228, 100);
        padding: 10px 20px;
        box-sizing: border-box;
        margin-bottom: 12px;
        transition: border-color 0.3s;
        cursor: pointer;

        &::last-child {
          margin-bottom: 0;
        }

        &:hover {
          border-color: #1877f5;
        }
        .switch-container {
          // switch组件样式修改
          ::v-deep {
            .el-switch__core {
              height: 30px;
              border-radius: 20px;

              &::after {
                width: 22px;
                height: 22px;
                top: 3px;
                left: 3px;
              }
            }
            .el-switch.is-checked .el-switch__core::after {
              margin-left: -25px;
              left: 100%;
            }
          }
        }
        .type-container {
          border-radius: 43px;
          background-color: rgba(234, 236, 241, 100);
          color: rgba(0, 7, 42, 100);
          font-size: 12px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 10px;
          box-sizing: border-box;
          display: inline;
          margin-left: 20px;
        }

        .Interest-rules-container {
          flex: 1;
          text-align: right;
          .rules-info {
            height: 20px;
            width: 90%;
            line-height: 20px;
            color: rgba(16, 16, 16, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
            display: inline-block;
          }
          .tip-container {
            width: 84px;
            height: 20px;
            line-height: 20px;
            color: rgba(154, 154, 154, 100);
            font-size: 14px;
            text-align: right;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
    }
  }
  .choose-cost {
    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
    }
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
}
</style>
