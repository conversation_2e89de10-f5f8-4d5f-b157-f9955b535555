<template>
  <div class="guarantee">
    <h1 class="titleBottom">合同模板</h1>
    <div class="guaranteeSetBottom">
      <avue-form :option="subformOption" v-model="obj">
        <template slot-scope="{ row }" slot="expense">
          <div>{{ row.expense }}</div>
        </template>
        <template slot-scope="{ row }" slot="do">
          <div style="color: #409eff; cursor: pointer" type="info" @click="openPreview(row)">预览</div>
        </template>
        <template slot-scope="{ row, index }" slot="signSlot">
          <div>
            <div
              class="form-slot-login"
              v-for="(item, childIndex) in row.contractGroupList"
              :key="childIndex"
            >
              <el-select
                :value="item.signUser"
                placeholder="请选择签署用户"
                :disabled="look"
                @change="val => changeNode(val, index, childIndex, 'signUser')"
              >
                <el-option
                  v-for="citem in goodsUploadUser"
                  :key="citem.id"
                  :label="citem.label"
                  :value="citem.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </template>
        <template slot-scope="{ row, index }" slot="nodeSlot">
          <div>
            <div
              class="form-slot-login"
              v-for="(item, childIndex) in row.contractGroupList"
              :key="childIndex"
            >
              <el-select
                :value="item.node"
                placeholder="请选择签署节点"
                multiple
                :disabled="look"
                @change="val => changeNode(val, index, childIndex, 'node')"
              >
                <el-option
                  v-for="citem in row.nodeArr"
                  :key="citem.id"
                  :label="citem.label"
                  :value="citem.value"
                ></el-option>
              </el-select>

              <el-button type="text" v-if="childIndex === 0 && !look" @click="handleAdd(index)">添加</el-button>
              <el-button
                type="text"
                v-if="childIndex > 0 && !look"
                @click="handleSettingDelete(index, childIndex)"
              >删除</el-button>
            </div>
          </div>
        </template>
      </avue-form>
    </div>
    <!-- 操作按钮 -->
    <!-- <div class="updata" v-if="!look">
        <el-button>取消</el-button>
        <el-button type="success" @click="setData">上架</el-button>
        <el-button type="primary">保存</el-button>
    </div>-->
    <!-- 弹窗 -->
    <el-dialog
      title="选择合同"
      :visible.sync="type1"
      :modal-append-to-body="false"
      class="avue-dialog avue-dialog--top"
      width="50%"
    >
      <!--
        arrOption: 配置 arr
        data: 数据源 arr
        search.sync: 获取实时的搜索值 obj
        search-change: 搜索点击事件 fan
        current-change: 分页功能页码点击事件 fan
        on-load: 首次加载事件 fan
        size-change: 分页总页数事件 fan
        selection-change: checkbox事件 fan
        table-loading: 表单加载动画 boolean
        page: 配置分页信息 obj
      -->
      <avue-crud
        ref="crud"
        :option="arrOption"
        :data="arrS"
        :search.sync="dialogSearch"
        @search-change="searchChange"
        @current-change="currentChangeScope"
        @size-change="sizeChangeScope"
        @selection-change="selectionChange"
        @on-load="onLoad"
        :table-loading="loading"
        :page="page"
      >
        <template slot-scope="{ row }" slot="node">
          <el-tag type="info">{{ row.node }}</el-tag>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="cardfals">取 消</el-button>
        <el-button @click="cardEngth" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import {
  getContractTemplateList,
  getgoodsContractTemplate,
  getcontractTemplate,
} from '@/api/goods/pcontrol/pinformation'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import { mapState } from 'vuex'
import FilePreview from '@/components/file-preview'

export default {
  props: ['look'],
  components: { FilePreview },
  data() {
    return {
      pdfSrc: '',
      id: this.$route.query.id,
      type1: false,
      dialogSearch: {},
      cardList: [],
      loading: true,
      obj: {
        dynamic: [],
      },
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 110,
        column: [
          {
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            children: {
              delBtn: !this.look,
              addBtn: !this.look,
              align: 'center',
              headerAlign: 'center',
              rowAdd: () => {
                this.type1 = true
                const exception = setInterval(() => {
                  if (this) {
                    this.checkUpType()
                    clearInterval(exception)
                  }
                }, 100)
              },
              rowDel: (row, done) => {
                this.checkUpType(row)
                done()
              },
              column: [
                {
                  // width: 330,
                  label: '合同模板',
                  prop: 'expense',
                  disabled: true,
                  align: 'left',
                  // formslot: true,
                  // type: 'select',
                },
                {
                  width: 100,
                  label: '排序',
                  prop: 'slot',
                  type: 'input',
                  placeholder: false,
                  clearable: false,
                  disabled: this.look,
                },
                {
                  width: 175,
                  label: '签署用户',
                  prop: 'signSlot',
                  // formslot: true,
                },
                {
                  width: 320,
                  label: '签署节点',
                  prop: 'nodeSlot',
                  // formslot: true,
                },
                {
                  width: 200,
                  label: '操作',
                  prop: 'do',
                  disabled: this.look,
                },
              ],
            },
          },
        ],
      },
      arrS: [],
      arr: [],
      page: {
        // total: 27,
        // pageSize: 10,
      },
      arrOption: {
        selection: true,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: true,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchMenuSpan: 7, // 搜索菜单栏宽带
        maxHeight: 300,
        index: true,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [
          {
            label: '资料名称',
            prop: 'expense',
            search: true,
            searchSpan: 10,
            searchClearable: false,
            placeholder: '按名称搜索',
          },
        ],
      },
      checkif: false,
      goodsUploadUser: [],
      goodsSignNode: [],
    }
  },
  watch: {
    dialogSearch: {
      handler(val) {
        // 搜索框被清空后重新返回列表数据
        if (!val.expense && !val.node && !val.way) {
          this.arrS = this.arr
          setTimeout(() => {
            this.checkUpType()
          }, 100)
        }
      },
      deep: true,
    },
    type1(val) {
      if (!val) {
        // 弹窗关闭清空搜索值
        this.$refs.crud.searchReset()
      }
    },
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
    }),
  },
  created() {
    this.getDictionaryFun()
    this.onLoadData()
  },
  methods: {
    setData() {
      const params = {
        goodsContractTemplates: [],
      }
      for (const item of this.obj.dynamic) {
        const userNodeList = []
        for (const citem of item.contractGroupList) {
          userNodeList.push({
            signUser: citem.signUser,
            signNode: citem.node.join(','),
          })
        }

        params.goodsContractTemplates.push({
          contractTemplateId: item.templateId,
          sort: item.slot,
          templateId: item.templateId,
          userNodeList,
        })
      }
      const objParams = { ...this.formParamsDataed, ...params }
      this.$store.commit('SET_FORM_PARAMS_DATAED', objParams)
    },
    getData() {
      getgoodsContractTemplate(this.id).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          this.obj.dynamic = []
          for (const item of resData.data) {
            const contractGroupList = []
            for (const citem of item.userNodeList) {
              const cObj = {}
              if (citem.signUser) {
                cObj.signUser = Number(citem.signUser)
              }
              if (citem.signNode) {
                cObj.node = citem.signNode.split(',')
              }
              contractGroupList.push(cObj)
            }
            if (item.signNode) {
              item.nodeArr = this.goodsSignNode.filter(itemF =>
                item.signNode.split(',').includes(itemF.value)
              )
            } else {
              item.nodeArr = []
            }
            this.obj.dynamic.push({
              expense: item.contractTemplateName,
              slot: item.sort,
              id: String(item.contractTemplateId),
              templateId: String(item.templateId),
              templateUrl: item.templateUrl,
              contractGenType: item.contractGenType,
              nodeArr: item.nodeArr,
              contractGroupList,
            })
          }
        }
      })
    },
    onLoadData() {
      getContractTemplateList().then(res => {
        const resData = res.data
        if (resData.code == 200) {
          const arrData = []
          for (const item of resData.data) {
            arrData.push({
              expense: item.templateName,
              id: item.templateId,
              templateId: item.templateId,
              templateUrl: item.templateUrl,
              contractGenType: item.contractGenType,
              signNode: item.signNode,
            })
          }
          this.arr = arrData
        }
      })
    },
    openPreview(row) {
      if (row.contractGenType === 2) {
        this.pdfSrc = row.templateUrl + '?time=' + new Date().getMilliseconds()
        return
      }
      getcontractTemplate(row.templateId).then(res => {
        let data = res.data.data
        let genType = data.contractGenType
        let url = data.url
        if (genType == 1) {
          window.open(url)
        } else {
          this.$router.push({
            path: url,
          })
        }
      })
    },
    searchChange(params, done) {
      // card搜索事件
      let ar = []
      this.arr.map(item => {
        const ex = item.expense.indexOf(params.expense) != -1
        if (ex) {
          ar.push(item)
        }
      })
      this.arrS = ar
      setTimeout(() => {
        this.checkUpType()
      }, 100)
      done()
    },
    onLoad() {
      // card首次加载事件
      this.loading = false
      this.arrS = this.arr
    },
    currentChangeScope(currentPage) {
      // 分页页码切换事件
      return console.log(currentPage)
    },
    sizeChangeScope(pageSize) {
      // 分页页数切换事件
      return console.log(pageSize)
    },
    selectionChange(cardList) {
      // checkbox事件
      this.cardList = cardList
      if (!this.checkif) {
        this.checkif = true
      }
    },
    cardEngth() {
      // 弹窗表格确认按钮
      this.type1 = false
      this.upBeforeCheck()
    },
    checkUpType(rows) {
      // 根据rows是否存在判断是选择操作还是删除操作
      if (!this.obj.dynamic.length) {
        // 表单无数据，用户并没有进行保存操作，对checkbox状态全部清除
        this.toggleSelection()
        return
      }
      let exclude = []
      if (!rows) {
        exclude = this.fun(this.obj.dynamic, this.cardList, 'id')
      } else {
        // 删除处理
        let arr = []
        // let arrs = [rows]
        this.cardList.map(item => {
          arr.push(item.id)
        })
        // exclude = arrs.filter(item => arr.indexOf(item.id))
      }
      if (exclude.length) {
        exclude.map(num => {
          const ind = this.getArrayIndex(this.arr, num.id)
          if (ind !== -1) {
            this.toggleSelection([this.arr[ind]])
          }
        })
      }
    },
    cardfals() {
      // 弹窗表格取消按钮
      this.type1 = false
      if (this.checkif) {
        // 当checkbox事件被触发，并且没有确定更改，对checkbox进行状态复原
        this.checkUpType()
      }
      this.checkif = false
    },
    toggleSelection(val) {
      // 弹窗取消后恢复之前checkbox状态
      this.$refs.crud.toggleSelection(val)
    },
    upBeforeCheck() {
      let compareArr = []
      let storeArr = []
      this.obj.dynamic.map(item => {
        compareArr.push(item.id)
      })
      if (compareArr.length < this.cardList.length) {
        storeArr = this.cardList.filter(item => !compareArr.includes(item.id))
      } else {
        // 如果数据list的length比checkbox的length小，进行一个重新赋值
        storeArr = this.cardList.filter(item => compareArr.includes(item.id))
        storeArr = storeArr.map((item, index) => {
          item.$index = index
          return item
        })
        this.obj.dynamic = JSON.parse(JSON.stringify(storeArr))
        return
      }
      storeArr &&
        storeArr.map(item => {
          item.contractGroupList = [
            {
              signUser: '',
              node: '',
            },
          ]
          if (item.signNode) {
            item.nodeArr = this.goodsSignNode.filter(itemF =>
              item.signNode.split(',').includes(itemF.value)
            )
          } else {
            item.nodeArr = []
          }
          // 筛选出checkbox为true并且dynamic 数据List没有的进行push操作
          this.obj.dynamic.push(item)
        })
    },
    getArrayIndex(arr, obj) {
      /*
       * 获取某个元素下标
       * arr: 传入的数组
       * obj: 需要获取下标的元素
       * */
      var i = arr.length
      while (i--) {
        if (arr[i].id === obj) {
          return i
        }
      }
      return -1
    },
    fun(fArr, cArr, field) {
      let diffRes = []
      let fDatas = []
      let cDatas = []
      for (let i in fArr) {
        let flg = false
        for (let j in cArr) {
          if (cArr[j][field] === fArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          fDatas.push(fArr[i])
        }
      }
      for (let i in cArr) {
        let flg = false
        for (let j in fArr) {
          if (fArr[j][field] === cArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          cDatas.push(cArr[i])
        }
      }
      diffRes.push(...cDatas.concat(fDatas))
      return diffRes
    },
    // 字典数据
    async getDictionaryFun() {
      // 签署用户
      await getDictionary('goods_upload_user').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            resList.push({
              label: item.dictValue,
              value: Number(item.dictKey),
              id: item.id,
            })
          }
          this.goodsUploadUser = resList
        }
      })
      // 签署节点
      await getDictionary('goods_sign_node').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            resList.push({
              label: item.dictValue,
              value: item.dictKey,
              id: item.id,
            })
          }
          this.goodsSignNode = resList
        }
      })

      if (this.id) {
        this.getData()
      }
    },
    // 单列内添加合同配置
    handleAdd(index) {
      const lengthNum = this.obj.dynamic[index].contractGroupList.length
      if (lengthNum > 2) {
        this.$message.warning('超过最大上限')
        return
      }
      const dyn = JSON.parse(JSON.stringify(this.obj.dynamic))
      dyn[index].contractGroupList.push({
        signUser: '',
        node: '',
      })
      this.obj.dynamic = dyn
    },
    // 单列内删除合同配置
    handleSettingDelete(index, childIndex) {
      this.obj.dynamic[index].contractGroupList.splice(childIndex, 1)
    },
    // 选择器事件
    changeNode(val, index, childIndex, taged) {
      if (taged === 'signUser') {
        const cList = this.obj.dynamic[index].contractGroupList.filter(
          item => item.signUser === val
        )
        if (cList.length) {
          this.$message.warning('同一合同签署用户已存在')
          return
        }
      }
      const dyn = JSON.parse(JSON.stringify(this.obj.dynamic))
      dyn[index].contractGroupList[childIndex][taged] = val
      this.obj.dynamic = dyn
    },
  },
}
</script>

<style lang="scss" scoped>
.guarantee {
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 40px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetBottom {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding: 35px 30px 0 30px;
    box-sizing: border-box;
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
  .form-slot-login {
    display: flex;
    align-items: center;
    ::v-deep {
      .el-select {
        margin-right: 8px;
      }
    }
    &:not(:nth-of-type(1)) {
      margin-top: 8px;
    }
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    color: #4c4b4b !important;
  }
}
</style>
