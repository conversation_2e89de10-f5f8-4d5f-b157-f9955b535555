<template>
  <div class="guarantee">
    <div v-for="item in costList" :key="item.id">
      <!-- 1应收 2代采 3云信 -->
      <template v-if="item.children.length > 0">
        <temp
          :costObj="item"
          :look="look"
          goodsType="1"
          :capital="capital"
          :repaymentType="repaymentType"
          :ref="item.id"
          :Method="Method"
          :zujianzidianArr="zujianzidianArr"
          @chargeMethodEmit="chargeMethodEmit"
        ></temp>
      </template>
    </div>
  </div>
</template>

<script>
import {
  expenseTypeList,
  goodsExpenseId,
} from '@/api/goods/pcontrol/pinformation'
import { mapState } from 'vuex'
import temp from './components/index.vue'
// import { getDictionary } from '@/api/system/dictbiz'

export default {
  props: {
    look: Boolean,
  },
  data() {
    return {
      id: this.$route.query.id,
      costList: [],
      capital: null,
      repaymentType: null,
      Method: null,
      zujianzidianArr: [
        {
          label: '线下支付',
          value: '1',
        },
        {
          label: '线上支付',
          value: '2',
        },
      ],
    }
  },
  created() {
    this.expenseTypeList()
    // this.qingqiuzidianFun()
    // 放款方式
    this.repaymentType = sessionStorage.getItem('repaymentType')
    // 资方是不是银行
    this.capital = sessionStorage.getItem('capitalType')
    // 收费方式
    this.Method = sessionStorage.getItem('chargeMethod')
  },
  methods: {
    async expenseTypeList() {
      const {
        data: { code, data },
      } = await expenseTypeList('1')
      let map = {}
      // 创建一个map 映射每一个值是什么样的

      if (code == 200) {
        data.forEach((item, index) => {
          map[`${item.id}`] = index
          if (item.children) {
            item.keyMap = {}
            item.children = item.children.map((citem, cindex) => {
              item.keyMap[`${citem.expenseKey}`] = cindex
              return {
                id: citem.id,
                val: false,
                expenseName: citem.expenseName,
                dialogVal: void 0,
                repaymentType: citem.repaymentType,
                goodsType: citem.goodsType,
                expenseKey: citem.expenseKey,
              }
            })
          } else {
            item.children = []
          }
        })

        if (this.id) {
          const {
            data: { data: resDataTemp },
          } = await goodsExpenseId(this.id)
          const resData = resDataTemp || []
          resData.forEach(item => {
            // 根据匹配值去赋值
            let index = map[`${item.expenseId}`]
            if (index || index === 0) {
              data[index].accountFrom = item.billBankCardaRelation
              // 存在费用信息
              if (item.goodsExpenseRelations) {
                item.goodsExpenseRelations.forEach(citem => {
                  let cindex = data[index].keyMap[`${citem.expenseType}`]
                  if (cindex || cindex === 0) {
                    data[index].children[cindex].val =
                      citem.status === 1 ? true : false
                    data[index].children[cindex].dialogVal = {
                      expense: citem.name,
                      typeExpense: citem.expenseType,
                      node: citem.feeNode,
                      way:
                        citem.calculation == 1
                          ? '人工核算'
                          : citem.feeFormulaName,
                      calculation: citem.calculation,
                      id: citem.id,
                      collectFeesNode: citem.collectFeesNode,
                    }
                  }
                })
              }
            }
          })
        }
        this.costList = data
      }
    },
    changeCapital(capital) {
      this.capital = capital
    },
    changRepaymentType(repaymentType) {
      this.repaymentType = repaymentType
      for (const item of this.costList) {
        if (item.children) {
          item.children.forEach(citem => {
            if (citem.repaymentType.indexOf(repaymentType) == -1) {
              citem.val = false
            }
          })
        }
      }
    },
    chargeMethod(Method) {
      this.Method = Method
    },
    setData() {
      let zifangshuyuxianshang = false
      const jiaoyanxianshangfeiyongFun = tItem => {
        const tBillBankCardaRelation =
          tItem.billBankCardaRelation.platformCostPayMode
        if (
          !zifangshuyuxianshang &&
          tItem.enterpriseType === 2 &&
          tBillBankCardaRelation &&
          tBillBankCardaRelation === 2
        ) {
          zifangshuyuxianshang = true
        }
        if (
          zifangshuyuxianshang &&
          tItem.enterpriseType !== 2 &&
          tBillBankCardaRelation &&
          tBillBankCardaRelation === 1
        ) {
          this.$emit('xianshangfeiyongbuxingEmit', true)
        }
      }
      let resultArr = []
      this.costList.forEach(item => {
        if (item.children.length != 0) {
          let result = this.$refs[`${item.id}`][0].setData()
          jiaoyanxianshangfeiyongFun(result)
          resultArr.push(result)
        }
      })
      const params = {}
      params.goodsExpense = resultArr
      const objParams = { ...this.formParamsDataed, ...params }
      this.$store.commit('SET_FORM_PARAMS_DATAED', objParams)
    },
    chargeMethodEmit(val) {
      this.$emit('chargeMethodEmit', val)
    },
    qingqiuzidianFun() {
      // getDictionary({ code: 'goods_bond_pay_type' })
      //   .then(({ data }) => {
      //     if (data.success) {
      //       const { data: resData } = data
      //       const arr = []
      //       for (const item of resData) {
      //         arr.push({
      //           label: item.dictValue,
      //           value: item.dictKey,
      //         })
      //       }
      //       this.zujianzidianArr = arr
      //     }
      //   })
      //   .catch(() => {})
    },
  },
  components: {
    temp,
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
    }),
  },
}
</script>
<style lang="scss" scoped>
.guarantee {
  // 选择资方账户样式修改
  ::v-deep {
    #my-corre-input-select {
      cursor: pointer;
    }
    .el-input__suffix {
      font-size: 16px;
      color: rgba(112, 112, 112, 100);
    }
    // 表格操作栏
    .management-accounts .avue-form__menu {
      display: none;
    }
  }
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 35px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetBottom {
    // border: 2px solid RGB(245, 245, 245);
    // border-radius: 10px;
    // padding: 35px 30px 0 30px;
    // box-sizing: border-box;

    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
      .avue-form__menu {
        display: none;
      }
      .avue-form__row {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
    }
  }
  .guaranteeSetBottom-switch {
    .capital-box {
      .chaldren-for-box {
        display: flex;
        align-items: center;
        height: 50px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 100);
        text-align: center;
        border: 1px solid rgba(228, 228, 228, 100);
        padding: 10px 20px;
        box-sizing: border-box;
        margin-bottom: 12px;
        transition: border-color 0.3s;
        cursor: pointer;

        &::last-child {
          margin-bottom: 0;
        }

        &:hover {
          border-color: #1877f5;
        }
        .switch-container {
          // switch组件样式修改
          ::v-deep {
            .el-switch__core {
              height: 30px;
              border-radius: 20px;

              &::after {
                width: 22px;
                height: 22px;
                top: 3px;
                left: 3px;
              }
            }
            .el-switch.is-checked .el-switch__core::after {
              margin-left: -25px;
              left: 100%;
            }
          }
        }
        .type-container {
          border-radius: 43px;
          background-color: rgba(234, 236, 241, 100);
          color: rgba(0, 7, 42, 100);
          font-size: 12px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 10px;
          box-sizing: border-box;
          display: inline;
          margin-left: 20px;
        }

        .Interest-rules-container {
          flex: 1;
          text-align: right;
          .rules-info {
            height: 20px;
            width: 90%;
            line-height: 20px;
            color: rgba(16, 16, 16, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
            display: inline-block;
          }
          .tip-container {
            width: 84px;
            height: 20px;
            line-height: 20px;
            color: rgba(154, 154, 154, 100);
            font-size: 14px;
            text-align: right;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
    }
  }
  .choose-cost {
    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
    }
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
}
</style>
