<template>
  <!-- <basic-container> -->
  <div class="guarantee">
    <!-- up -->
    <h1 class="titleTop">基本数据</h1>
    <div class="guaranteeSetTop">
      <avue-form ref="form" :option="option" v-model="form"> </avue-form>
    </div>
    <!-- down -->
    <h1 class="titleBottom">其他资料</h1>
    <div class="guaranteeSetBottom">
      <!-- <avue-form :option="subformOption" v-model="obj">
        <template slot-scope="{ row }" slot="expense">
          <div>{{ row.expense }}</div>
        </template>
      </avue-form> -->
      <u-table
        ref="plTable"
        :data="tableData"
        :height="height"
        use-virtual
        showBodyOverflow="title"
        showHeaderOverflow="title"
        :row-height="rowHeight"
        :row-class-name="tableRowClassName"
        :data-changes-scroll-top="false"
        :show-header-overflow="false"
        :show-body-overflow="false"
        :highlight-current-row="false"
        header-row-class-name="table-head-class"
        @cell-mouse-enter="handleCellMouseEnter"
        @cell-mouse-leave="handleCellMouseLeave"
        border
      >
        <u-table-column width="60" align="center">
          <template slot="header" v-if="!look">
            <el-button
              @click="handleAddT"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              circle
            ></el-button>
          </template>
          <template slot-scope="{ row, $index }">
            <span v-if="!row.isDel">{{ $index + 1 }}</span>
            <el-button
              v-else
              type="danger"
              icon="el-icon-delete"
              size="mini"
              circle
              @click="handleRowDel(row)"
            ></el-button>
          </template>
        </u-table-column>
        <u-table-column label="资料名称" prop="expense"></u-table-column>
        <u-table-column label="上传用户" prop="uploadUser">
          <template slot-scope="{ row, $index }">
            <div>
              <div
                class="form-slot-login"
                v-for="(item, childIndex) in row.contractGroupList"
                :key="childIndex"
              >
                <el-select
                  :value="item.uploadUser"
                  placeholder="请选择上传用户"
                  style="width: 100%"
                  :disabled="look"
                  @change="
                    val => changeNode(val, $index, childIndex, 'uploadUser')
                  "
                >
                  <el-option
                    v-for="citem in uploadOptions"
                    :key="citem.id"
                    :label="citem.label"
                    :value="citem.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
          </template>
        </u-table-column>
        <u-table-column label="上传节点" prop="node">
          <template slot-scope="{ row, $index }">
            <div>
              <div
                class="form-slot-login"
                v-for="(item, childIndex) in row.contractGroupList"
                :key="childIndex"
              >
                <el-select
                  :value="item.node"
                  placeholder="请选择上传节点"
                  style="width: 100%"
                  multiple
                  :disabled="look"
                  @change="val => changeNode(val, $index, childIndex, 'node')"
                >
                  <el-option
                    v-for="citem in row.nodeArr"
                    :key="citem.id"
                    :label="citem.label"
                    :value="citem.value"
                  >
                  </el-option>
                </el-select>

                <el-button
                  type="text"
                  v-if="childIndex === 0 && !look"
                  @click="handleAdd($index)"
                >
                  添加
                </el-button>
                <el-button
                  type="text"
                  v-if="childIndex > 0 && !look"
                  @click="handleSettingDelete($index, childIndex)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </template>
        </u-table-column>
      </u-table>
    </div>
    <!-- 操作按钮 -->
    <!-- <div class="updata" v-if="!look">
        <el-button>取消</el-button>
        <el-button type="success" @click="setData()">上架</el-button>
        <el-button type="primary" @click="setData('save')">保存</el-button>
      </div> -->
    <!-- 弹窗 -->
    <el-dialog
      title="选择资料"
      :visible.sync="type1"
      :modal-append-to-body="false"
      class="avue-dialog avue-dialog--top"
      width="50%"
    >
      <!--
        arrOption: 配置 arr
        data: 数据源 arr
        search.sync: 获取实时的搜索值 obj
        search-change: 搜索点击事件 fan
        current-change: 分页功能页码点击事件 fan
        on-load: 首次加载事件 fan
        size-change: 分页总页数事件 fan
        selection-change: checkbox事件 fan
        table-loading: 表单加载动画 boolean
        page: 配置分页信息 obj
        -->
      <avue-crud
        ref="crud"
        :option="arrOption"
        :data="arrS"
        :search.sync="dialogSearch"
        @search-change="searchChange"
        @current-change="currentChangeScope"
        @size-change="sizeChangeScope"
        @selection-change="selectionChange"
        @on-load="onLoad"
        :table-loading="loading"
        :page="page"
      >
        <template slot-scope="{ row }" slot="node">
          <el-tag type="info">{{ row.node }}</el-tag>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="cardfals">取 消</el-button>
        <el-button @click="cardEngth" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
  <!-- </basic-container> -->
</template>

<script>
// import { set } from 'vue/types/umd'
// import {getList, getDetail, add, update, remove, enable} from "@/api/resource/oss";
// import {mapGetters} from "vuex";
// import func from "@/util/func";
import {
  getmaterialDetail,
  getExpenseDataList,
  // servegoodsMaterial,
  // setShelf,
  getgoodsMaterialList,
  getGoodsDetail,
} from '@/api/goods/pcontrol/pinformation'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import { mapState } from 'vuex'
import { UTable, UTableColumn } from 'umy-ui'

export default {
  props: {
    look: Boolean,
  },
  components: { UTable, UTableColumn },
  data() {
    return {
      id: this.$route.query.id,
      type1: false,
      dialogSearch: {},
      cardList: [],
      loading: true,
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [
          {
            label: '核心企业表单',
            prop: 'financingEnterprises',
            type: 'select',
            span: 12,
            placeholder: '请选择授信表单',
            disabled: this.look,
            dicData: [],
          },
          {
            label: '融资企业表单',
            prop: 'enterpriseForm',
            type: 'select',
            span: 12,
            placeholder: '请选择授信表单',
            disabled: this.look,
            dicData: [],
          },
        ],
      },
      obj: {
        dynamic: [],
      },
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 110,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            // disabled: true,
            children: {
              delBtn: !this.look,
              addBtn: !this.look,
              align: 'center',
              headerAlign: 'center',
              rowAdd: () => {
                this.type1 = true
                const exception = setInterval(() => {
                  if (this) {
                    this.checkUpType()
                    clearInterval(exception)
                  }
                }, 100)
                // this.$message.success('新增回调')
                // done({
                //   input: '默认值',
                // })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                this.checkUpType(row)
                done()
              },
              column: [
                // {
                //   width: 90,
                //   label: '序号',
                //   prop: 'le',
                //   disabled: true,
                //   // type: 'text',
                //   formslot: true,
                // },
                {
                  width: 750,
                  label: '资料名称',
                  prop: 'expense',
                  disabled: true,
                  align: 'left',
                  // formslot: true,
                  // type: 'select',
                },
                {
                  // width: 200,
                  label: '上传用户',
                  prop: 'uploadUser',
                  type: 'select',
                  // dicData: DIC.NODE,
                  placeholder: '请选择上传用户',
                  disabled: this.look,
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=goods_upload_user',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  dataType: 'string',
                },
                {
                  // width: 200,
                  label: '上传节点',
                  prop: 'node',
                  type: 'select',
                  // dicData: DIC.NODE,
                  placeholder: '上传节点',
                  disabled: this.look,
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=goods_upload_node',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  dataType: 'string',
                },
              ],
            },
          },
        ],
      },
      arrS: [],
      arr: [],
      page: {
        // total: 27,
        // pageSize: 10,
      },
      arrOption: {
        selection: true,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: true,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchMenuSpan: 7, // 搜索菜单栏宽带
        maxHeight: 300,
        index: true,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [
          {
            label: '资料名称',
            prop: 'expense',
            search: true,
            searchSpan: 10,
            searchClearable: false,
            placeholder: '按名称搜索',
          },
        ],
      },
      checkif: false,
      height: 400,
      rowHeight: 55, // 如果你这里给行高为50，那么你表格行会出现错乱，不要问为啥，因为你可以看看控制台看节点的高是多少，是55，而你这里给50就有问题！
      tableData: [],
      uploadOptions: [], // 上传用户数据
      nodeOptions: [], // 上传节点数据
      timer: null,
    }
  },
  watch: {
    dialogSearch: {
      handler(val) {
        // 搜索框被清空后重新返回列表数据
        if (!val.expense && !val.node && !val.way) {
          this.arrS = this.arr
          setTimeout(() => {
            this.checkUpType()
          }, 100)
        }
      },
      deep: true,
    },
    type1(val) {
      if (!val) {
        // 弹窗关闭清空搜索值
        this.$refs.crud.searchReset()
      }
    },
  },
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
    }),
  },
  created() {
    this.getDictionaryFun()
    this.onLodaData()
  },
  methods: {
    setData() {
      const goodArr = []
      this.tableData.map((item, index) => {
        const userNodeList = []
        for (const citem of item.contractGroupList) {
          userNodeList.push({
            uploadUser: citem.uploadUser,
            uploadNode: citem.node.join(','),
          })
        }
        goodArr.push({
          materialId: item.id,
          sort: index + 1,
          userNodeList,
        })
      })
      const params = {
        // goodsId: this.id,
        creditFormId: this.form.enterpriseForm,
        coreCreditFormId: this.form.financingEnterprises,
        goodsMaterials: goodArr,
      }
      const objParams = { ...this.formParamsDataed, ...params }
      this.$store.commit('SET_FORM_PARAMS_DATAED', objParams)
      // servegoodsMaterial(params).then(res => {
      //   const resData = res.data
      //   if (resData.code == 200) {
      //     if (!save) {
      //       setShelf(this.id)
      //         .then(res => {
      //           // 上架
      //           const resData = res.data
      //           if (resData.code == 200) {
      //             this.$message.success('已上架')
      //             this.$router.$avueRouter.closeTag()
      //             this.$router.push({ path: '/goods/goods' })
      //           }
      //         })
      //         .catch(() => {
      //           this.$message.success('已保存,未上架')
      //         })
      //     } else {
      //       this.$message.success('已保存')
      //     }
      //   }
      // })
    },
    getData() {
      getGoodsDetail(this.id).then(res => {
        // 查询form表单数据
        const resData = res.data
        if (resData.code == 200) {
          this.form.enterpriseForm = resData.data.creditFormId
          this.form.financingEnterprises = resData.data.coreCreditFormId
        }
      })
      getgoodsMaterialList(this.id).then(res => {
        // 获取其他资料-子表单数据
        const resData = res.data
        if (resData.code == 200) {
          const resArr = []
          for (const item of resData.data) {
            const contractGroupList = []
            for (const citem of item.userNodeList) {
              const cObj = {}
              if (citem.uploadUser) {
                cObj.uploadUser = citem.uploadUser
              }
              if (citem.uploadNode) {
                cObj.node = citem.uploadNode.split(',')
              }
              contractGroupList.push(cObj)
            }
            if (item.collectionNode) {
              item.nodeArr = this.nodeOptions.filter(itemF =>
                item.collectionNode.split(',').includes(itemF.value)
              )
            } else {
              item.nodeArr = []
            }
            resArr.push({
              expense: item.materialName,
              id: item.materialId,
              isDel: false,
              nodeArr: item.nodeArr,
              contractGroupList,
            })
          }
          this.tableData = resArr
        }
      })
    },
    onLodaData() {
      getExpenseDataList(1).then(res => {
        // 基本数据-授信表单list接口
        if (res.status == 200) {
          const resData = res.data
          const resArr = []
          for (const item of resData.data) {
            resArr.push({
              label: item.fromName,
              value: item.id,
            })
          }
          this.option.column[0].dicData = resArr
          this.option.column[1].dicData = resArr
        }
      })
      getmaterialDetail().then(res => {
        // 其他资料-子表单弹窗list数据接口
        if (res.status == 200) {
          const resData = res.data
          const resArr = []
          for (const item of resData) {
            resArr.push({
              expense: item.name,
              id: item.id,
              collectionNode: item.collectionNode,
            })
          }
          this.arr = resArr
        }
      })
    },
    searchChange(params, done) {
      // 限制空值搜索
      if (JSON.stringify(params) == "{}") return done()
      // card搜索事件
      let ar = []
      this.arr.map(item => {
        const ex = item.expense.indexOf(params.expense) != -1
        if (ex) {
          ar.push(item)
        }
      })
      this.arrS = ar
      setTimeout(() => {
        this.checkUpType()
      }, 100)
      done()
    },
    onLoad() {
      // card首次加载事件
      this.loading = false
      this.arrS = this.arr
    },
    currentChangeScope(currentPage) {
      // 分页页码切换事件
      return console.log(currentPage)
    },
    sizeChangeScope(pageSize) {
      // 分页页数切换事件
      return console.log(pageSize)
    },
    selectionChange(cardList) {
      // checkbox事件
      this.cardList = cardList
      if (!this.checkif) {
        this.checkif = true
      }
    },
    cardEngth() {
      // 弹窗表格确认按钮
      this.type1 = false
      this.upBeforeCheck()
    },
    checkUpType(rows) {
      // 根据rows是否存在判断是选择操作还是删除操作
      if (!this.tableData.length) {
        // 表单无数据，用户并没有进行保存操作，对checkbox状态全部清除
        this.toggleSelection()
        return
      }
      let exclude = []
      if (!rows) {
        exclude = this.fun(this.tableData, this.cardList, 'id')
      } else {
        // 删除处理
        let arr = []
        // let arrs = [rows]
        this.cardList.map(item => {
          arr.push(item.id)
        })
        // exclude = arrs.filter(item => arr.indexOf(item.id))
      }
      if (exclude.length) {
        exclude.map(num => {
          const ind = this.getArrayIndex(this.arr, num.id)
          if (ind !== -1) {
            this.toggleSelection([this.arr[ind]])
          }
        })
      }
    },
    cardfals() {
      // 弹窗表格取消按钮
      this.type1 = false
      if (this.checkif) {
        // 当checkbox事件被触发，并且没有确定更改，对checkbox进行状态复原
        this.checkUpType()
      }
      this.checkif = false
    },
    toggleSelection(val) {
      // 弹窗取消后恢复之前checkbox状态
      this.$refs.crud.toggleSelection(val)
    },
    upBeforeCheck() {
      let compareArr = []
      let storeArr = []
      this.tableData.map(item => {
        compareArr.push(item.id)
      })
      if (compareArr.length < this.cardList.length) {
        storeArr = this.cardList.filter(item => !compareArr.includes(item.id))
      } else {
        // 如果数据list的length比checkbox的length小，进行一个重新赋值
        storeArr = this.cardList.filter(item => compareArr.includes(item.id))
        storeArr = storeArr.map((item, index) => {
          item.$index = index
          item.isDel = false
          return item
        })
        this.tableData = JSON.parse(JSON.stringify(storeArr))
        return
      }
      storeArr &&
        storeArr.map(item => {
          item.contractGroupList = [
            {
              uploadUser: '',
              node: '',
            },
          ]
          if (item.collectionNode) {
            item.nodeArr = this.nodeOptions.filter(itemF =>
              item.collectionNode.split(',').includes(itemF.value)
            )
          } else {
            item.nodeArr = []
          }
          // 筛选出checkbox为true并且dynamic 数据List没有的进行push操作
          this.tableData.push({ ...item, isDel: false })
        })
    },
    getArrayIndex(arr, obj) {
      /*
       * 获取某个元素下标
       * arr: 传入的数组
       * obj: 需要获取下标的元素
       * */
      var i = arr.length
      while (i--) {
        if (arr[i].id === obj) {
          return i
        }
      }
      return -1
    },
    fun(fArr, cArr, field) {
      let diffRes = []
      let fDatas = []
      let cDatas = []
      for (let i in fArr) {
        let flg = false
        for (let j in cArr) {
          if (cArr[j][field] === fArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          fDatas.push(fArr[i])
        }
      }
      for (let i in cArr) {
        let flg = false
        for (let j in fArr) {
          if (fArr[j][field] === cArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          cDatas.push(cArr[i])
        }
      }
      diffRes.push(...cDatas.concat(fDatas))
      return diffRes
    },

    // 其他资料虚拟列表的处理
    // 添加其他资料
    handleAddT() {
      this.type1 = true
      const exception = setInterval(() => {
        if (this) {
          // 在添加的弹窗内 用于回显勾选的数据
          this.checkUpType()
          clearInterval(exception)
        }
      }, 100)
    },
    // 查看时，不触发鼠标移入表格行
    handleCellMouseEnter(row) {
      // 表格删除事件打开
      if (!this.look) {
        clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          row.isDel = true
          this.timer = null
        }, 100)
      }
    },
    // 查看时，不触发鼠标移出表格行
    handleCellMouseLeave(row) {
      // 表格删除事件关闭
      if (!this.look) {
        clearTimeout(this.timer)
        row.isDel = false
      }
    },
    // 鼠标停留时,找到下标
    tableRowClassName({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    // 列表删除
    handleRowDel(row) {
      this.tableData.splice(row.index - 1, 1)
    },

    // 字典数据
    async getDictionaryFun() {
      // 上传用户
      await getDictionary('goods_upload_user').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            resList.push({
              label: item.dictValue,
              value: Number(item.dictKey),
              id: item.id,
            })
          }
          this.uploadOptions = resList
        }
      })
      // 签署节点
      await getDictionary('goods_upload_node').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            resList.push({
              label: item.dictValue,
              value: item.dictKey,
              id: item.id,
            })
          }
          this.nodeOptions = resList
        }
      })

      if (this.id) {
        this.getData()
      }
    },
    // 单列内添加合同配置
    handleAdd(index) {
      const lengthNum = this.tableData[index].contractGroupList.length
      if (lengthNum > 2) {
        this.$message.warning('超过最大上限')
        return
      }
      const dyn = JSON.parse(JSON.stringify(this.tableData))
      dyn[index].contractGroupList.push({
        uploadUser: '',
        node: '',
      })
      this.tableData = dyn
    },
    // 单列内删除合同配置
    handleSettingDelete(index, childIndex) {
      this.tableData[index].contractGroupList.splice(childIndex, 1)
    },
    // 选择器事件
    changeNode(val, index, childIndex, taged) {
      if (taged === 'uploadUser') {
        const cList = this.tableData[index].contractGroupList.filter(
          item => item.uploadUser === val
        )
        if (cList.length) {
          this.$message.warning('同一合同签署用户已存在')
          return
        }
      }
      const dyn = JSON.parse(JSON.stringify(this.tableData))
      dyn[index].contractGroupList[childIndex][taged] = val
      this.tableData = dyn
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .table-head-class th {
    background-color: #fafafa !important;
  }
}

.guarantee {
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 40px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetBottom {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding: 35px 30px;
    box-sizing: border-box;
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
  .form-slot-login {
    display: flex;
    align-items: center;
    ::v-deep {
      .el-select {
        margin-right: 8px;
      }
    }
    &:not(:nth-of-type(1)) {
      margin-top: 8px;
    }
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    color: #4c4b4b !important;
  }
}
</style>
