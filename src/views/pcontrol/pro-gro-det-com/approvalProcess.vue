<template>
  <!-- <basic-container> -->
  <div class="approvalProcess">
    <!-- up -->
    <h1 class="titleTop">审批流程</h1>
    <div class="crud-box">
      <avue-form :option="subformOption" ref="from" v-model="obj">
        <template slot-scope="{ row }" slot="name">
          <div>{{ row.name }}</div>
        </template>
        <template slot-scope="{ row }" slot="flow">
          <el-select
            v-model="row.flow"
            placeholder="请选择工作流程"
            :disabled="look"
          >
            <el-option
              v-for="item in row.selectArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </avue-form>
    </div>
    <!-- 操作按钮 -->
    <!-- <div class="updata" v-if="!look">
        <el-button>取消</el-button>
        <el-button type="success" @click="setData()">上架</el-button>
        <el-button type="primary" @click="setData('save')">保存</el-button>
      </div> -->
  </div>
  <!-- </basic-container> -->
</template>

<script>
import {
  getProcessList,
  getDictionary,
  getByBusinessType,
} from '@/api/goods/pcontrol/pinformation'
import { mapState } from 'vuex'

export default {
  props: ['look'],
  data() {
    return {
      id: this.$route.query.id,
      obj: {
        dynamic: [],
      },
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 110,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            children: {
              delBtn: false,
              addBtn: false,
              align: 'center',
              headerAlign: 'center',
              column: [
                {
                  label: '流程名称',
                  prop: 'name',
                  disabled: this.look,
                  align: 'left',
                },
                {
                  label: '绑定流程',
                  prop: 'flow',
                  type: 'select',
                  placeholder: '选择工作流程',
                  dicData: [],
                  dataType: 'string',
                  // disabled: this.look,
                },
              ],
            },
          },
        ],
      },
      lock: false,
    }
  },
  created() {
    this.onLoadData()
  },
  mounted() {},
  computed: {
    ...mapState({
      formParamsDataed: state => state.common.formParamsDataed,
    }),
  },
  methods: {
    // 流程审批数据校验
    liuchengshenpijiaoyanFun() {
      const objDy = this.obj.dynamic
      for (const item of objDy) {
        if (!item.flow) {
          this.$message.warning('请完善审批流程-流程的选定')
          return
        }
      }
      this.formSubmit()
    },
    setData() {
      // 流程审批数据校验
      this.liuchengshenpijiaoyanFun()
    },
    formSubmit() {
      this.$store.commit('SET_CPZ_VALID_CHUN_TYPE', ['three', true])

      const goodsProcessList = []
      for (const item of this.obj.dynamic) {
        goodsProcessList.push({ processType: item.key, processKey: item.flow })
      }
      const params = {
        goodsProcessList,
      }
      const objParams = { ...this.formParamsDataed, ...params }
      this.$store.commit('SET_FORM_PARAMS_DATAED', objParams)
    },
    getData() {
      getProcessList(this.id).then(res => {
        // 获取审批流程已填信息
        const resData = res.data
        if (resData.code == 200) {
          for (const item of this.obj.dynamic) {
            for (const itemed of resData.data) {
              if (item.key == itemed.processType) {
                item.flow = itemed.processKey
                break
              }
            }
          }
        }
      })
    },
    onLoadData() {
      if (this.lock) return
      this.lock = true
      getDictionary({ code: 'productGroup_process_type' }).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            // if (
            //   item.dictKey == 6 &&
            //   sessionStorage.getItem('lendingMethod') == 2
            // ) {
            //   resList.push({
            //     name: item.dictValue,
            //     flow: '',
            //     id: item.id,
            //     key: item.dictKey,
            //   })
            // } else if (item.dictKey != 6) {
              //   resList.push({
                //     name: item.dictValue,
            //     flow: '',
            //     id: item.id,
            //     key: item.dictKey,
            //   })
            // }
            resList.push({
              name: item.dictValue,
              flow: '',
              id: item.id,
              key: item.dictKey,
            })
          }
          // 获取工作流程list
          getByBusinessType({ businessType: 99 }).then(({ data }) => {
            if (data.success) {
              const { data: resData } = data
              for (const item of resList) {
                for (const key in resData) {
                  if (item.key === key) {
                    item.selectArr = []
                    for (const items of resData[key]) {
                      item.selectArr.push({
                        label: items.name,
                        value: items.key,
                      })
                    }
                    break
                  }
                }
              }
              this.obj.dynamic = resList
              if (this.id) {
                this.getData()
              }
              this.lock = false
            }
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.approvalProcess {
  ::v-deep {
    .el-input.is-disabled .el-input__inner {
      color: #000;
    }
  }
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .crud-box {
    ::v-deep {
      .avue-crud__menu {
        display: none;
      }
    }
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
}
</style>
