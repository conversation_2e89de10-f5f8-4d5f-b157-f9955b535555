<template>
  <basic-container>
    <div class="tageds">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane
          :key="item.name"
          :name="item.name"
          v-for="item in editableTabs"
        >
          <span slot="label">
            <el-button-group>
              <el-button
                type="primary"
                size="medium"
                :plain="item.name == activeName ? false : true"
                :class="{
                  'bord-radius-left': item.name == '1',
                  'bord-radius-right':
                    item.name == editableTabs[editableTabs.length - 1].name,
                }"
                >{{ item.title }}</el-button
              >
            </el-button-group>
          </span>
          <!-- 产品信息 -->
          <Product
            ref="product"
            v-if="item.name == '1'"
            :look="look"
            @processOnload="processOnloadP"
          />
          <!-- 相关费用 -->
          <Correlative
            ref="correlative"
            v-else-if="item.name == '2'"
            :look="look"
            @chargeMethodEmit="chargeMethodEmit"
          />
          <!-- 数据收集 -->
          <Gather ref="gather" v-else-if="item.name == '3'" :look="look" />
          <!-- 风控规则 -->
          <Preposition
            ref="preposition"
            v-else-if="item.name == '4'"
            :look="look"
          />
          <!-- 合同模板 -->
          <Contract ref="contract" v-else-if="item.name == '5'" :look="look" />
          <!-- 审批流程 -->
          <ApprovalProcess
            ref="approvalProcess"
            v-else-if="item.name == '6'"
            :look="look"
          />
        </el-tab-pane>
      </el-tabs>
      <!-- 操作按钮 -->
      <div class="updata" v-if="!look">
        <el-button @click="handleback">取消</el-button>
        <template v-if="zhanshiBtn">
          <el-button type="success" @click="handleSubmit('updata')"
            >上架</el-button
          >
          <el-button type="primary" @click="handleSubmit('save')">保存</el-button>
        </template>
      </div>
    </div>
  </basic-container>
</template>

<script>
import Product from './subsections/product.vue'
import Correlative from './subsections/correlative1.vue'
import Gather from './subsections/gather.vue'
import Preposition from './subsections/preposition.vue'
import Contract from './subsections/contract.vue'
import ApprovalProcess from './subsections/approvalProcess.vue'
import { mapState } from 'vuex'
import { save, update, onShelf } from '@/api/goods/agentgoods'

export default {
  data() {
    return {
      id: this.$route.query.id,
      activeName: '1',
      editableTabs: [
        {
          title: '产品信息',
          name: '1',
          paths: 'product',
        },
        {
          title: '相关费用',
          name: '2',
          paths: 'correlative',
        },
        {
          title: '数据收集',
          name: '3',
          paths: 'gather',
        },
        {
          title: '风控规则',
          name: '4',
          paths: 'preposition',
        },
        {
          title: '合同模板',
          name: '5',
          paths: 'contract',
        },
        {
          title: '审批流程',
          name: '6',
          paths: 'approvalProcess',
        },
      ],
      look: false,
      chargeMethodType: false,
      xianshangfeiyongbuxingType: false,
      zhanshiBtn: false,
    }
  },
  components: {
    Product,
    Correlative,
    Gather,
    Preposition,
    Contract,
    ApprovalProcess,
  },
  computed: {
    ...mapState({
      formParamsData: state => state.common.formParamsData,
      valid: state => state.common.valid,
    }),
  },
  created() {
    if (sessionStorage.getItem('look') == 'true') {
      this.look = true
    }
    this.yanshiqidongBtnFun()
  },
  methods: {
    yanshiqidongBtnFun() {
      setTimeout(() => {
        this.zhanshiBtn = true
      }, 4000)
    },
    // 代采的产品 的取消
    handleback() {
      this.$router.$avueRouter.closeTag()
      this.$router.back()
    },
    handleSubmit(doing) {
      this.$refs.product[0].handleSubmit(doing == 'save' ? doing : '')
      setTimeout(() => {
        this.$refs.correlative[0].setData()
      }, 100)
      setTimeout(() => {
        this.$refs.gather[0].setData()
      }, 200)
      setTimeout(() => {
        this.$refs.preposition[0].setData()
      }, 300)
      setTimeout(() => {
        this.$refs.contract[0].setData()
      }, 400)
      setTimeout(() => {
        if (this.chargeMethodType) {
          this.$message.warning('自动放款时,费用规则不能存在人工核算')
          this.chargeMethodType = false
          return
        }
        this.$refs.approvalProcess[0].setData()
        if (!this.valid) {
          this.$store.commit('SET_VALID_TYPE', true)
          return
        }
        if (!this.id) {
          const params = {}
          const empty = {}
          for (const i in this.formParamsData) {
            // 排除空对象属性
            if (this.formParamsData[i]) {
              params[i] = this.formParamsData[i]
            } else {
              empty[i] = this.formParamsData[i]
            }
          }
          Object.assign(params, { type: 2 })
          save(params)
            .then(res => {
              const resData = res.data
              this.$message.success('已保存')
              this.id = resData.data
              if (doing == 'updata') {
                this.onShelfFun(resData.data)
              }
            })
            .catch(() => {})
        } else {
          const parimsId = {
            id: this.id,
          }
          const objParams = { ...parimsId, ...this.formParamsData }
          const params = {}
          const empty = {}
          for (const i in objParams) {
            // 排除空对象属性
            if (objParams[i]) {
              params[i] = objParams[i]
            } else {
              empty[i] = objParams[i]
            }
          }
          Object.assign(params, { type: 2 })
          update(params)
            .then(res => {
              const resDate = res.data
              if (resDate.code == 200) {
                this.$message.success('已保存')
                if (doing == 'updata') {
                  this.onShelfFun(this.id)
                }
              }
            })
            .catch(() => {})
        }
      }, 500)
    },
    onShelfFun(idS) {
      onShelf(idS)
        .then(res => {
          // 上架
          const resData = res.data
          if (resData.code == 200) {
            this.$message.success('已上架')
            this.$router.$avueRouter.closeTag()
            this.$router.push({ path: '/goods/agentGoods' })
          }
        })
        .catch(() => {})
    },
    processOnloadP(argVal) {
      // this.$refs.approvalProcess[0].onLoadData()
      // this.$refs.correlative[0].earlySettlementDisabledFun()
      // this.$refs.correlative[0].earlySettprepaymentDisabledFun()
      // else if(argVal == 'repaymentType'){
      //   this.$refs.correlative[0].changRepaymentType(sessionStorage.getItem('repaymentType'))
      // }
      if(argVal == 'bank'){
        this.$refs.correlative[0].changeCapital(sessionStorage.getItem('capitalType'))
      }else if (argVal == 'chargeMethod'){
        this.$refs.correlative[0].chargeMethod(sessionStorage.getItem('chargeMethod'))
      }
    },
    chargeMethodEmit(val) {
      this.chargeMethodType = val
    },
    xianshangfeiyongbuxingEmit(val) {
      this.xianshangfeiyongbuxingType = val
    },
    // handleClick(tab, event) {
    //   console.log(tab, event)
    // },
    // checkrouter(item) {
    //   console.log(item)
    //   // this.$router.push({ path: `/pcontrol/pinformation/subsections/${item.paths}` });
    // },
  },
}
</script>

<style lang="scss">
.tageds {
  .bord-radius-left {
    border-radius: 20px 0 0 20px !important;
    border-left-color: #b3d8ff !important;
  }
  .bord-radius-right {
    border-radius: 0 20px 20px 0 !important;
    border-right-color: #b3d8ff !important;
  }
  .el-button--primary.is-plain {
    // background: #ffffff;
    border-color: #a3cdf8;
  }
  .el-tabs__item {
    padding: 0 !important;
  }
  .el-tabs--card > .el-tabs__header .el-tabs__item {
    border: none;
  }
  .el-tabs--card > .el-tabs__header {
    border-bottom: none;
  }
  // .el-button--primary.is-plain{
  //   border-color: #a3cdf8 !important;
  // }
  // .el-button--primary {
  //   background: #1684fc;
  // }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-bottom: 1.5%;
    margin-top: 1%;
  }
}
</style>
