<template>
  <!-- <basic-container> -->
  <div class="guarantee">
    <div class="guarantee-top">
      <!-- up -->
      <h1 class="titleTop">保证金设置</h1>
      <div class="guaranteeSetTop">
        <avue-form
          ref="form"
          :option="option"
          @reset-change="emptytChange"
          @submit="submit"
          v-model="form"
        >
          <template slot="wave">
            <div>
              <div style="text-align: center">~</div>
            </div>
          </template>
        </avue-form>
      </div>
      <!-- down -->
      <h1 class="titleBottom">资方费用</h1>
      <avue-form
        class="management-accounts"
        v-model="managementAccountsForm"
        :option="managementAccountsOption"
      />
      <div class="guaranteeSetBottom-switch" @click="location(1)">
        <div class="capital-box">
          <div
            class="chaldren-for-box"
            v-for="(item, index) in obj"
            :key="item.id"
            @click="
              item.disabled && item.id !== 1
                ? costDialog(false)
                : costDialog(item, index)
            "
          >
            <div class="switch-container" @click.stop="">
              <el-switch
                v-model="item.val"
                :disabled="item.disabled"
                active-color="#13ce66"
                inactive-color="#E2E2E3"
                :active-value="true"
                :inactive-value="false"
                :width="50"
              >
              </el-switch>
            </div>
            <div class="type-container">{{ item.costType }}</div>
            <div class="Interest-rules-container">
              <span class="rules-info" v-if="item.dialogVal">
                {{ item.dialogVal.expense }}：{{ item.dialogVal.way }}
              </span>
              <span class="tip-container" v-else>选择费用规则</span>
              <svg-icon
                icon-class="icon-youjiantou"
                style="fill: #cccccc; font-size: 16px"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- f -->
    <div class="guarantee-bottom">
      <h1 class="titleBottom">平台费用</h1>
      <avue-form
        class="management-accounts"
        v-model="platformAccountsForm"
        :option="platformAccountsOption"
      />
      <div class="guaranteeSetBottom" @click="location(2)">
        <avue-form :option="subformOption" v-model="obj1">
          <template slot-scope="{ row }" slot="expense">
            <div>{{ row.expense }}</div>
          </template>
          <template slot-scope="{ row }" slot="typeExpense">
            <el-tag type="info">{{ row.$typeExpense }}</el-tag>
          </template>
          <template slot-scope="{ row }" slot="node">
            <el-tag type="info">{{ row.$node }}</el-tag>
          </template>
          <template slot-scope="{ row }" slot="way">
            <div>{{ row.way }}</div>
          </template>
        </avue-form>
      </div>
    </div>
    <!-- 操作按钮 -->
    <!-- <div class="updata" v-if="!look">
        <el-button>取消</el-button>
        <el-button type="success" @click="setData()">上架</el-button>
        <el-button type="primary" @click="getData()">保存</el-button>
        <el-button type="primary" @click="setData('save')">保存</el-button>
      </div> -->
    <!-- 费用弹窗 -->
    <el-dialog
      title="选择费用"
      :visible.sync="type1"
      :modal-append-to-body="false"
      class="avue-dialog choose-cost"
      width="70%"
    >
      <!--
        arrOption: 配置 arr
        data: 数据源 arr
        search.sync: 获取实时的搜索值 obj
        search-change: 搜索点击事件 fan
        current-change: 分页功能页码点击事件 fan
        on-load: 首次加载事件 fan
        size-change: 分页总页数事件 fan
        selection-change: checkbox事件 fan
        table-loading: 表单加载动画 boolean
        page: 配置分页信息 obj
        -->
      <avue-crud
        v-if="crudIndex === 2"
        ref="crud"
        :option="arrOption"
        :data="arrS"
        :search.sync="dialogSearch"
        @search-change="searchChange"
        @current-change="currentChangeScope"
        @size-change="sizeChangeScope"
        @selection-change="selectionChange"
        @on-load="onLoad"
        :page="page"
      >
        <template slot-scope="{ row }" slot="typeExpense">
          <el-tag type="info">{{ row.$typeExpense }}</el-tag>
        </template>
        <template slot-scope="{ row }" slot="node">
          <el-tag type="info">{{ row.$node }}</el-tag>
        </template>
      </avue-crud>

      <avue-crud
        v-else
        ref="crud1"
        :option="arrOption1"
        :data="arrS"
        :search.sync="dialogSearch1"
        @row-click="rowClick"
        @search-change="searchChange1"
        @search-reset="searchReset"
        :table-loading="loading"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow" :label="row.$index">&nbsp;</el-radio>
        </template>
        <template slot-scope="{ row }" slot="typeExpense">
          <el-tag type="info">{{ row.$typeExpense }}</el-tag>
        </template>
        <template slot-scope="{ row }" slot="node">
          <el-tag type="info">{{ row.$node }}</el-tag>
        </template>
      </avue-crud>

      <div class="avue-dialog__footer">
        <el-button @click="cardfals">取 消</el-button>
        <el-button
          @click="crudIndex === 1 ? radioCardEngth() : cardEngth()"
          type="primary"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 账户弹窗 -->
    <el-dialog
      title="选择账户"
      :visible.sync="type2"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="avue-dialog"
      width="70%"
    >
      <avue-crud
        ref="crud"
        :option="arrOption2"
        :data="arr_2"
        :search.sync="dialogSearch2"
        @row-click="rowClick2"
        @search-reset="searchReset2"
        @search-change="searchChange2"
        @size-change="sizeChangeScope2"
        @on-load="dialogOnLoad2"
        :table-loading="tableLoading2"
        :page.sync="accountPagingObj"
        @current-change="currentChangeScope2"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow2" :label="row.$index">&nbsp;</el-radio>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="type2 = false">取 消</el-button>
        <el-button @click="cardEngth2" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
  <!-- </basic-container> -->
</template>

<script>
// import {getList, getDetail, add, update, remove, enable} from "@/api/resource/oss";
// import {mapGetters} from "vuex";
// import func from "@/util/func";
import {
  // serveExpense,
  // setShelf,
  getExpenseList,
  selectListByExpenseType,
  getExpenseDetail,
  getDictionary,
  selectListAccount,
  getbillBankCardaRelation,
} from '@/api/goods/pcontrol/pinformation'
import { getDetail } from '@/api/goods/pledgegoods'
import { mapState } from 'vuex'

const DIC = {
  payment: [
    {
      value: 1,
    },
    {
      value: 2,
    },
  ],
}
export default {
  props: {
    look: Boolean,
  },
  data() {
    return {
      id: this.$route.query.id,
      type1: false,
      dialogSearch: {},
      dialogSearch1: {},
      cardList: [],
      loading: true,
      crudIndex: 1,
      form: {
        switch: 2,
      },
      // 标志是否勾选的
      selStatus: false,
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 130,
        gutter: 50,
        column: [
          {
            label: '缴纳保证金',
            activeColor: '#13ce66',
            type: 'switch',
            placeholder: false,
            prop: 'switch',
            dicData: DIC.payment,
            disabled: this.look,
            control: val => {
              if (this.look) return
              if (val == 1) {
                return {
                  bondPayProportionStart: {
                    disabled: true,
                  },
                  bondPayProportionEnd: {
                    disabled: true,
                  },
                  payWay: {
                    disabled: true,
                  },
                  releaseWay: {
                    disabled: true,
                  },
                }
              } else {
                return {
                  bondPayProportionStart: {
                    disabled: false,
                  },
                  bondPayProportionEnd: {
                    disabled: false,
                  },
                  payWay: {
                    disabled: false,
                  },
                  releaseWay: {
                    disabled: false,
                  },
                }
              }
            },
            // disabled: true,
          },
          {
            label: '缴纳比例',
            prop: 'bondPayProportionStart',
            span: 6,
            append: '%',
            placeholder: false,
            className: 'loansleft1',
            disabled: this.look,
            // labelWidth: 82,
            rules: [
              {
                required: true,
                message: '请输入缴纳比例',
                trigger: 'change',
              },
            ],
          },
          {
            label: '',
            labelWidth: 0,
            span: 1,
            prop: 'wave',
            // disabled: true,
            // minRows: 10,
            className: 'borrow-wave',
          },
          {
            label: '',
            prop: 'bondPayProportionEnd',
            span: 5,
            append: '%',
            placeholder: false,
            labelWidth: 0,
            disabled: this.look,
            // className: 'loansright1',
            rules: [
              {
                required: true,
                message: '请输入缴纳比例',
                trigger: 'change',
              },
            ],
          },
          {
            label: '支付方式',
            prop: 'payWay',
            type: 'select',
            span: 12,
            placeholder: '请选择支付方式',
            disabled: this.look,
            // dicData: [],
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_bond_pay_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择支付方式',
                trigger: 'change',
              },
            ],
            change: ({ value }) => {
              if (value) {
                if (value === 1) {
                  this.option.column[6].display = true
                  if (this.look) return
                  // 将input 变成只读
                  setTimeout(() => {
                    const domArr = document.querySelectorAll('.el-input__inner')
                    for (const item of domArr) {
                      if (item.placeholder === '请选择保证金监管账户') {
                        item.readOnly = true
                        item.id = 'my-corre-input-select'
                        break
                      }
                    }
                  }, 100)
                } else {
                  this.option.column[6].display = false
                  this.form.cashDepositTakeBillBankCardas = ''
                  this.cashDepositBillBankObj = {}
                }
              }
            },
          },
          {
            label: '释放方式',
            prop: 'releaseWay',
            type: 'select',
            span: 12,
            placeholder: '请选择释放方式',
            disabled: this.look,
            // dicData: [],
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_bond_release_mode',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择释放方式',
                trigger: 'change',
              },
            ],
          },
          {
            label: '保证金监管账户',
            placeholder: '请选择保证金监管账户',
            prop: 'cashDepositTakeBillBankCardas',
            disabled: this.look,
            display: false,
            type: 'input',
            clearable: false,
            suffixIcon: 'el-icon-arrow-right',
            span: 12,
            rules: [
              {
                required: true,
                message: '请选择保证金监管账户',
                trigger: 'change',
              },
            ],
            click: () => {
              if (this.look) return
              // 打开弹窗
              this.initialize2()
              this.accountType = 3
              this.selectedWarehouse()
            },
          },
        ],
      },
      obj: [
        {
          id: 1,
          val: false,
          disabled: false,
          costType: '利息',
          dialogVal: void 0,
        },
        {
          id: 2,
          val: false,
          disabled: false,
          costType: '提前结清服务费',
          dialogVal: void 0,
        },
        {
          id: 3,
          val: false,
          disabled: false,
          costType: '提前还款服务费',
          dialogVal: void 0,
        },
        {
          id: 4,
          val: false,
          disabled: true,
          costType: '展期利息',
          dialogVal: void 0,
        },
        {
          id: 5,
          val: false,
          disabled: false,
          costType: '逾期利息',
          dialogVal: void 0,
        },
      ],
      obj1: {
        dynamic: [],
      },
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 110,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            // disabled: true,
            children: {
              delBtn: !this.look,
              addBtn: !this.look,
              align: 'center',
              headerAlign: 'center',
              rowAdd: () => {
                this.type1 = true
                this.selStatus = false
                const exception = setInterval(() => {
                  if (this) {
                    clearInterval(exception)
                    this.checkUpType()
                  }
                }, 100)
                // this.$message.success('新增回调')
                // done({
                //   input: '默认值',
                // })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                this.checkUpType(row)
                done()
              },
              column: [
                // {
                //   width: 90,
                //   label: '序号',
                //   prop: 'le',
                //   disabled: true,
                //   // type: 'text',
                //   formslot: true,
                // },
                {
                  width: 550,
                  label: '费用名称',
                  prop: 'expense',
                  disabled: true,
                  searchClearable: false,
                  align: 'left',
                  // formslot: true,
                  // type: 'select',
                },
                {
                  // width: 200,
                  label: '费用类型',
                  searchClearable: false,
                  prop: 'typeExpense',
                  disabled: true,
                  dataType: 'number',
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_type',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                },
                {
                  // width: 200,
                  label: '计算节点',
                  searchClearable: false,
                  prop: 'node',
                  disabled: true,
                  dataType: 'number',
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_fee_node',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                },
                {
                  // width: 300,
                  label: '计费方式',
                  searchClearable: false,
                  prop: 'way',
                  disabled: true,
                },
              ],
            },
          },
        ],
      },
      arrS: [], // 弹窗显示规则数组
      arr: [], // 平台费用规则
      arr2: [], // 资方费用规则
      page: {
        // total: 27,
        // pageSize: 10,
      },
      arrOption: {
        selection: true,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: true,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchMenuSpan: 4,
        maxHeight: 300,
        index: true,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [
          {
            label: '费用名称',
            prop: 'expense',
            search: true,
            placeholder: '按名称搜索',
          },
          {
            label: '费用类型',
            prop: 'typeExpense',
            search: true,
            placeholder: '按类型搜索',
            dataType: 'number',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '计算节点',
            prop: 'node',
            search: true,
            placeholder: '按节点搜索',
            dataType: 'number',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_fee_node',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '计费方式',
            prop: 'way',
            search: true,
            placeholder: '按方式搜索',
          },
        ],
      },
      arrOption1: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchMenuSpan: 7, // 搜索菜单栏宽带
        maxHeight: 300,
        index: false,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [],
      },
      costIndex: 0,
      selectRow: '',
      selectIdBefore: void 0,
      selectIdAfter: void 0,
      selectRowData: void 0,
      checkif: false,
      // 用户选择的账户存储对象
      capitalLockNum: 0, // 避免初次加载资方账户显示异常
      capitalBillBankObj: {},
      platformBillBankObj: {},
      cashDepositBillBankObj: {},
      // 资方账户配置
      managementAccountsForm: {
        capitalBillBankCardas: '',
      },
      managementAccountsOption: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 110,
        gutter: 100,
        column: [],
      },
      // 平台站账户配置
      platformAccountsForm: {
        platformCostPayMode: '',
        platformBillBankCardas: '',
      },
      platformAccountsOption: {
        emptyBtn: false,
        submitBtn: false,
        labelPosition: 'left',
        labelWidth: 110,
        gutter: 100,
        column: [],
      },
      // 账户弹窗
      type2: false,
      rowsData: void 0,
      arr_2: [],
      selectRow2: '',
      tableLoading2: true,
      accountType: 0,
      searchChangeData2: {},
      accountPagingObj: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 30, 40, 50, 100],
        layout: 'total,sizes,pager',
      },
      arrOption2: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },
    }
  },
  watch: {
    dialogSearch: {
      handler(val) {
        // 搜索框被清空后重新返回列表数据
        if (!val.expense && !val.node && !val.way) {
          this.arrS = this.arr
          setTimeout(() => {
            this.checkUpType()
          }, 100)
        }
      },
      deep: true,
    },
    type1(val) {
      if (!val && this.crudIndex === 2) {
        // 弹窗关闭清空搜索值
        this.$refs.crud.searchReset()
      }
    },
  },
  computed: {
    ...mapState({
      formParamsData: state => state.common.formParamsData,
    }),
  },
  created() {
    this.arrOption1.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '费用名称',
        prop: 'expense',
        search: true,
        placeholder: '按名称搜索',
      },
      {
        label: '费用类型',
        prop: 'typeExpense',
        search: false,
        placeholder: '按类型搜索',
        dataType: 'number',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '计算节点',
        prop: 'node',
        search: true,
        placeholder: '按节点搜索',
        dataType: 'number',
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_fee_node',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
      },
      {
        label: '计费方式',
        prop: 'way',
        search: true,
        placeholder: '按方式搜索',
      },
    ]
    this.arrOption2.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '账户类型',
        prop: 'type',
        width: 90,
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=account_type_status',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
      },
      {
        label: '开户银行',
        prop: 'bankDeposit',
      },
      {
        label: '开户名',
        prop: 'openHouseName',
      },
      {
        label: '银行账户',
        prop: 'bankCardNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'enterpriseName',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入账户名称',
      },
    ]
    // 资方账户
    this.managementAccountsOption.column = [
      {
        label: '选择资方账号',
        placeholder: '请选择资方账号',
        prop: 'capitalBillBankCardas',
        type: 'input',
        disabled: this.look,
        display: false,
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择资方账号',
            trigger: 'change',
          },
        ],
        click: () => {
          if (this.look) return
          // 打开弹窗
          this.initialize2()
          this.accountType = 2
          this.selectedWarehouse()
        },
      },
    ]
    // 平台账户
    this.platformAccountsOption.column = [
      {
        label: '支付方式',
        prop: 'platformCostPayMode',
        type: 'select',
        span: 12,
        labelWidth: 85,
        placeholder: '请选择支付方式',
        disabled: this.look,
        display: false,
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=goods_bond_pay_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择支付方式',
            trigger: 'change',
          },
        ],
        change: ({ value }) => {
          if (value) {
            if (value === 1) {
              this.platformAccountsOption.column[1].display = true
              if (this.look) return
              // 将input 变成只读
              setTimeout(() => {
                const domArr = document.querySelectorAll('.el-input__inner')
                for (const item of domArr) {
                  if (item.placeholder === '请选择平台账号') {
                    item.readOnly = true
                    item.id = 'my-corre-input-select'
                    break
                  }
                }
              }, 100)
            } else {
              this.platformAccountsOption.column[1].display = false
              this.platformAccountsForm.platformBillBankCardas = ''
              this.platformBillBankObj = {
                accountType: 1,
              }
            }
          }
        },
      },
      {
        label: '平台账号',
        placeholder: '请选择平台账号',
        prop: 'platformBillBankCardas',
        disabled: this.look,
        display: false,
        type: 'input',
        clearable: false,
        suffixIcon: 'el-icon-arrow-right',
        span: 12,
        rules: [
          {
            required: true,
            message: '请选择平台账号',
            trigger: 'change',
          },
        ],
        click: () => {
          if (this.look) return
          // 打开弹窗
          this.initialize2()
          this.accountType = 1
          this.selectedWarehouse()
        },
      },
    ]
    this.onLoadData()
    // 查看对资方费用全部禁用
    if (this.look) {
      for (const item of this.obj) {
        item.disabled = true
      }
    }
  },
  methods: {
    setData() {
      const formData = this.form
      const params = {}
      if (formData.switch != 2) {
        params.isPayBond = 1
      } else {
        params.isPayBond = 2
        params.bondPayProportionStart = formData.bondPayProportionStart
        params.bondPayProportionEnd = formData.bondPayProportionEnd
        params.bondPayType = formData.payWay
        params.bondReleaseMode = formData.releaseWay
      }
      params.capitalExpenses = []
      params.platformExpenses = []
      for (const item of this.obj) {
        // 如果属于自动放款不能存在人工核算
        if (item.dialogVal) {
          const chargeMethodSess = sessionStorage.getItem('chargeMethod')
          if (item.dialogVal.calculation == 1 && chargeMethodSess == 'true') {
            this.$emit('chargeMethodEmit', true)
          }
          params.capitalExpenses.push({
            goodsExpenseRuleId: item.dialogVal.id,
            feeNode: item.dialogVal.node,
            calculation: item.dialogVal.calculation,
            status: item.val ? 1 : 0,
            type: 1,
          })
        }
      }
      for (const item of this.obj1.dynamic) {
        // 如果属于自动放款不能存在人工核算
        const chargeMethodSess = sessionStorage.getItem('chargeMethod')
        if (item.calculation == 1 && chargeMethodSess == 'true') {
          this.$emit('chargeMethodEmit', true)
        }
        params.platformExpenses.push({
          goodsExpenseRuleId: item.id,
          feeNode: item.node,
          calculation: item.calculation,
          type: 2,
        })
      }
      // 资方账户设置
      params.capitalBillBankCardas = {
        billBankCardaId: this.capitalBillBankObj.billBankCardaId,
        accountType: this.capitalBillBankObj.accountType,
      }
      // 平台账户设置
      params.platformBillBankCardas = {
        billBankCardaId: this.platformBillBankObj.billBankCardaId,
        accountType: this.platformBillBankObj.accountType,
        platformCostPayMode: this.platformAccountsForm.platformCostPayMode, // 平台费用支付方式
      }
      // 保证金监管账户
      params.cashDepositTakeBillBankCardas = {
        billBankCardaId: this.cashDepositBillBankObj.billBankCardaId,
        accountType: this.cashDepositBillBankObj.accountType,
      }
      const objParams = { ...this.formParamsData, ...params }
      this.$store.commit('SET_FORM_PARAMS_DATA', objParams)
      // this.$store.commit('SET_FORM_PARAMS_DATA', params)
      // serveExpense(params).then(res => {
      //   const resData = res.data
      //   if (resData.code == 200) {
      //     this.$message.success('保存成功')
      //     if (!save) {
      //       setShelf(this.id)
      //         .then(res => {
      //           // 上架
      //           const resData = res.data
      //           if (resData.code == 200) {
      //             this.$message.success('已上架')
      //             this.$router.$avueRouter.closeTag()
      //             this.$router.push({ path: '/goods/goods' })
      //           }
      //         })
      //         .catch(() => {})
      //     } else {
      //       this.$router.$avueRouter.closeTag()
      //       this.$router.push({ path: '/goods/goods' })
      //     }
      //   }
      // })
    },
    getData() {
      getDetail(this.id).then(res => {
        // 查询form表单数据
        const resData = res.data
        if (resData.code == 200) {
          const formData = this.form
          const paramsData = resData.data
          formData.switch = paramsData.isPayBond
          formData.bondPayProportionStart = paramsData.bondPayProportionStart
          formData.bondPayProportionEnd = paramsData.bondPayProportionEnd
          formData.payWay = paramsData.bondPayType
          formData.releaseWay = paramsData.bondReleaseMode
        }
      })
      getExpenseDetail(this.id).then(res => {
        // 获取已选择的其他费用子表单数据
        const resData = res.data
        if (resData.code == 200) {
          const dynArr2 = []
          const arrFData = this.arr.map(item => item.id) // 启动中的费用
          for (const key in resData.data) {
            if (key === '1') {
              for (const item of resData.data[key].filter(
                iteme => arrFData.includes(iteme.id) // 过滤掉禁用的费用
              )) {
                for (const items of this.obj) {
                  if (items.costTypeKey == item.expenseType) {
                    items.val = item.status === 1 ? true : false
                    items.dialogVal = {
                      expense: item.name,
                      typeExpense: item.expenseType,
                      node: item.feeNode,
                      way:
                        item.calculation == 1
                          ? '人工核算'
                          : item.feeFormulaName,
                      calculation: item.calculation,
                      id: item.id,
                    }
                  }
                }
              }
            } else if (key === '2') {
              for (const item of resData.data[key].filter(
                iteme => arrFData.includes(iteme.id) // 过滤掉禁用的费用
              )) {
                dynArr2.push({
                  expense: item.name,
                  typeExpense: item.expenseType,
                  node: item.feeNode,
                  way: item.calculation == 1 ? '人工核算' : item.feeFormulaName,
                  calculation: item.calculation,
                  id: item.id,
                })
              }
              this.obj1.dynamic = dynArr2
            }
          }
        }
      })
      // 查询产品关联账户
      getbillBankCardaRelation(this.id).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          for (const key in resData) {
            if (key === '2' && resData[key].length) {
              // 资方
              const data2 = resData[key][0]
              this.managementAccountsForm.capitalBillBankCardas =
                data2.accountName
              this.capitalBillBankObj = {
                // 给后端的
                billBankCardaId: data2.billBankCardaId,
                accountType: data2.accountType,
              }
            } else if (key === '1' && resData[key].length) {
              // 平台
              const data1 = resData[key][0]
              this.platformAccountsForm.platformCostPayMode =
                data1.platformCostPayMode
              this.platformAccountsForm.platformBillBankCardas =
                data1.accountName
              this.platformBillBankObj = {
                // 给后端的
                billBankCardaId: data1.billBankCardaId,
                accountType: data1.accountType,
              }
            } else if (key === '3' && resData[key].length) {
              // 保证金监管账户
              const data3 = resData[key][0]
              this.form.cashDepositTakeBillBankCardas = data3.accountName
              this.cashDepositBillBankObj = {
                // 给后端的
                billBankCardaId: data3.billBankCardaId,
                accountType: data3.accountType,
              }
            }
          }
        }
      })
    },
    onLoadData() {
      const params = {
        status: 1, // 启用否
        type: 2, // 产品类型
      }
      getExpenseList(params).then(res => {
        // 选择费用list数据
        const resData = res.data
        if (resData.code == 200) {
          this.arr = []
          for (const item of resData.data) {
            this.arr.push({
              expense: item.name,
              typeExpense: item.expenseType,
              node: item.feeNode,
              way: item.calculation == 1 ? '人工核算' : item.feeFormulaName,
              calculation: item.calculation,
              id: item.id,
            })
          }
          if (this.id) {
            this.getData()
          }
        }
      })
      // 费用规则数据请求
      getDictionary({ code: 'goods_expense_rule_type' }).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          for (const item of resData) {
            for (const items of this.obj) {
              if (item.dictValue === items.costType) {
                items.costTypeKey = item.dictKey
                break
              }
            }
          }
        }
      })
    },
    searchChange(params, done) {
      // card搜索事件
      let ar = []
      this.arr.map(item => {
        const ex = item.expense.indexOf(params.expense) != -1
        const ty = item.$typeExpense.indexOf(params.typeExpense) != -1
        const no = item.$node.indexOf(params.node) != -1
        const wa = item.way ? item.way.indexOf(params.way) != -1 : null
        if (ex || ty || no || wa) {
          ar.push(item)
        }
      })
      this.arrS = ar
      setTimeout(() => {
        this.checkUpType()
      }, 100)
      done()
    },
    searchChange1(params, done) {
      // card搜索事件
      let ar = []
      this.arr2.map(item => {
        const ex = item.expense.indexOf(params.expense) != -1
        const ty = item.$typeExpense.indexOf(params.typeExpense) != -1
        const no = item.$node.indexOf(params.node) != -1
        const wa = item.way ? item.way.indexOf(params.way) != -1 : null
        if (ex || ty || no || wa) {
          ar.push(item)
        }
      })
      this.arrS = ar
      // setTimeout(() => {
      //   this.checkUpType()
      // }, 100)
      done()
    },
    searchReset() {
      this.arrS = this.arr2
    },
    onLoad() {
      // card首次加载事件
      this.loading = false
      this.arrS = this.arr
    },
    currentChangeScope(currentPage) {
      // 分页页码切换事件
      return console.log(currentPage)
    },
    sizeChangeScope(pageSize) {
      // 分页页数切换事件
      return console.log(pageSize)
    },
    selectionChange(cardList) {
      // checkbox事件
      this.cardList = cardList
      if (!this.checkif) {
        this.checkif = true
      }
    },
    cardEngth() {
      // 弹窗表格确认按钮
      this.type1 = false
      this.upBeforeCheck()
    },
    // 处理反选
    checkUpType(rows) {
      if (!this.$refs.crud) return
      // 根据rows是否存在判断是选择操作还是删除操作
      // if (!this.obj.dynamic.length && this.crudIndex === 1) {
      //   // 表单无数据，用户并没有进行保存操作，对checkbox状态全部清除
      //   this.toggleSelection()
      //   return
      // } else
      if (!this.obj1.dynamic.length && this.crudIndex === 2) {
        // 表单无数据，用户并没有进行保存操作，对checkbox状态全部清除
        this.toggleSelection()
        return
      }
      let exclude = []
      if (!rows) {
        if (this.crudIndex === 1) {
          exclude = this.fun(this.obj.dynamic, this.cardList, 'id')
        } else {
          exclude = this.fun(this.obj1.dynamic, this.cardList, 'id')
        }
      } else {
        let arr = []
        let arrs = [rows]
        this.cardList.map(item => {
          arr.push(item.id)
        })
        exclude = arrs.filter(item => arr.indexOf(item.id))
      }
      if (exclude.length) {
        exclude.map(num => {
          const ind = this.getArrayIndex(this.arr, num.id)
          if (ind !== -1) {
            this.toggleSelection([this.arr[ind]])
          }
        })
      }
    },
    // 弹窗表格取消按钮
    cardfals() {
      this.type1 = false
      if (this.checkif) {
        // 当checkbox事件被触发，并且没有确定更改，对checkbox进行状态复原
        this.checkUpType()
      }
      this.checkif = false
    },
    toggleSelection(val) {
      // 弹窗取消后恢复之前checkbox状态
      this.$refs.crud.toggleSelection(val)
    },
    upBeforeCheck() {
      let compareArr = []
      let storeArr = []
      if (this.crudIndex === 1) {
        this.obj.dynamic.map(item => {
          compareArr.push(item.id)
        })
      } else {
        this.obj1.dynamic.map(item => {
          compareArr.push(item.id)
        })
      }
      if (compareArr.length < this.cardList.length) {
        storeArr = this.cardList.filter(item => !compareArr.includes(item.id))
      } else {
        // 如果数据list的length比checkbox的length小，进行一个重新赋值
        storeArr = this.cardList.filter(item => compareArr.includes(item.id))
        storeArr = storeArr.map((item, index) => {
          item.$index = index
          return item
        })
        if (this.crudIndex === 1) {
          this.obj.dynamic = JSON.parse(JSON.stringify(storeArr))
        } else {
          this.obj1.dynamic = JSON.parse(JSON.stringify(storeArr))
        }
        return
      }
      if (this.crudIndex === 1) {
        storeArr &&
          storeArr.map(item => {
            // 筛选出checkbox为true并且dynamic 数据List没有的进行push操作
            this.obj.dynamic.push(item)
          })
      } else {
        storeArr &&
          storeArr.map(item => {
            // 筛选出checkbox为true并且dynamic 数据List没有的进行push操作
            this.obj1.dynamic.push(item)
          })
      }
    },
    // 获取添加的表格index
    location(inx) {
      this.crudIndex = inx
    },
    getArrayIndex(arr, obj) {
      /*
       * 获取某个元素下标
       * arr: 传入的数组
       * obj: 需要获取下标的元素
       * */
      var i = arr.length
      while (i--) {
        if (arr[i].id === obj) {
          return i
        }
      }
      return -1
    },
    // 比较并获取相同条件下的数组
    fun(fArr, cArr, field) {
      let diffRes = []
      let fDatas = []
      let cDatas = []
      for (let i in fArr) {
        let flg = false
        for (let j in cArr) {
          if (cArr[j][field] === fArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          fDatas.push(fArr[i])
        }
      }
      for (let i in cArr) {
        let flg = false
        for (let j in fArr) {
          if (fArr[j][field] === cArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          cDatas.push(cArr[i])
        }
      }
      diffRes.push(...cDatas.concat(fDatas))
      return diffRes
    },
    /**
     * 资方费用弹窗
     * @description:
     * @param {*} type 资方类型（来自字典）
     * @param {*} index 资方费用对应数据下标
     * @return {*}
     * @author: 95hwp27
     */
    costDialog(type, index) {
      if (type === false) {
        this.$message.error('该费用暂时无法使用')
        return
      }
      this.arrS = []
      this.loading = true
      this.costIndex = index // 赋值时的依据下标
      this.selectListByFun(type)
      this.type1 = true
      // const exception = setInterval(() => {
      //   if (this) {
      //     clearInterval(exception)
      //     this.checkUpType()
      //   }
      // }, 100)
    },
    // 根据是否可以提前还款进行开关禁用
    earlySettlementDisabledFun() {
      if (this.look) return
      const prepaySession = sessionStorage.getItem('prepaymentType')
      if (prepaySession === '1') {
        this.obj[1].val = false
        this.obj[1].disabled = true
        this.obj[2].val = false
        this.obj[2].disabled = true
      } else if (prepaySession === '2') {
        this.obj[1].disabled = false
        this.obj[2].val = false
        this.obj[2].disabled = true
      } else {
        this.obj[1].val = false
        this.obj[1].disabled = true
        this.obj[2].disabled = false
      }
    },
    // 根据所属资方进行是否选择资方账户开启与关闭
    earlySettcapitalDisabledFun() {
      this.capitalLockNum++
      const capitalBillBankCardas = this.findObject(
        this.managementAccountsOption.column,
        'capitalBillBankCardas'
      )
      if (sessionStorage.getItem('capitalType') !== '1') {
        // 避免初次加载的数据被清除
        if (this.capitalLockNum > 1) {
          this.managementAccountsForm.capitalBillBankCardas = ''
          this.capitalBillBankObj = {}
        }
        capitalBillBankCardas.display = true
        if (this.look) return
        setTimeout(() => {
          // 将input 变成只读
          const domArr = document.querySelectorAll('.el-input__inner')
          for (const item of domArr) {
            if (item.placeholder === '请选择资方账号') {
              item.readOnly = true
              item.id = 'my-corre-input-select'
              break
            }
          }
        }, 100)
      } else {
        capitalBillBankCardas.display = false
        this.managementAccountsForm.capitalBillBankCardas = ''
        this.capitalBillBankObj = {}
      }
    },
    // 根据收费方式进行是否选择平台账户开启与关闭
    earlySettprepaymentDisabledFun() {
      const platformCostPayMode = this.findObject(
        this.platformAccountsOption.column,
        'platformCostPayMode'
      )
      const platformBillBankCardas = this.findObject(
        this.platformAccountsOption.column,
        'platformBillBankCardas'
      )
      if (sessionStorage.getItem('chargeMethod') === '2') {
        platformCostPayMode.display = true
        if (this.platformAccountsForm.platformCostPayMode === 1) {
          platformBillBankCardas.display = true
        }
      } else {
        platformCostPayMode.display = false
        platformBillBankCardas.display = false
        this.platformAccountsForm.platformCostPayMode = ''
        this.platformAccountsForm.platformBillBankCardas = ''
        this.platformBillBankObj = {}
      }
    },
    /**
     * 弹窗单选事件
     * @description:
     * @param {*} row 单击选中的数据
     * @return {*}
     * @author: 95hwp27
     */
    rowClick(row) {
      if (this.selectRow !== row.$index) {
        this.selectRow = row.$index
        this.selectRowData = row
        this.selectIdBefore = row.id
      }
      this.selStatus = true
    },
    // 单选弹窗表格确认按钮
    radioCardEngth() {
      if (this.selStatus == true) {
        this.obj[this.costIndex].dialogVal = this.selectRowData
        this.selectIdAfter = this.selectIdBefore
      }
      this.selStatus = false
      this.type1 = false
    },
    /**
     * 根据类型请求费用规则数据
     * @description:
     * @param {*} type 费用类型
     * @return {*}
     * @author: 95hwp27
     */
    selectListByFun(type) {
      const params = {
        expenseType: type.costTypeKey, // 启用否
        type: 2, // 产品类型
      }
      selectListByExpenseType(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          this.arr2 = []
          for (const item of resData.data) {
            this.arr2.push({
              expense: item.name,
              typeExpense: item.expenseType,
              node: item.feeNode,
              way: item.calculation == 1 ? '人工核算' : item.feeFormulaName,
              calculation: item.calculation,
              id: item.id,
            })
          }
          this.arrS = this.arr2
          if (type.dialogVal) {
            const selectRowIndex = this.arr2.findIndex(
              item => item.id === type.dialogVal.id
            )
            // 过滤后无数据 ，说明该规则不存在，属于旧数据，无法在弹窗回显
            if (selectRowIndex !== -1) {
              this.selectRow = type.dialogVal.$index || selectRowIndex
            } else {
              this.selectRow = 10000
            }
          } else {
            this.selectRow = 10000
          }
          this.loading = false
        }
      })
    },
    // 账户弹窗--弹窗清空按钮事件
    searchReset2() {
      this.selectListAccountFun()
    },
    // 账户弹窗--分页页码切换事件
    currentChangeScope2(currentPage) {
      this.accountPagingObj.currentPage = currentPage
      this.selectListAccountFun(this.searchChangeData2)
    },
    // 账户弹窗--分页页数切换事件
    sizeChangeScope2(pageSize) {
      // 分页页数切换事件
      this.accountPagingObj.currentPage = 1
      this.accountPagingObj.pageSize = pageSize
      this.selectListAccountFun(this.searchChangeData2)
    },
    // 账户弹窗--弹窗表格确认按钮
    cardEngth2() {
      // 资方
      if (this.accountType === 2 && this.rowsData) {
        this.managementAccountsForm.capitalBillBankCardas =
          this.rowsData.openHouseName
        this.capitalBillBankObj = {
          // 给后端的
          billBankCardaId: this.rowsData.id,
          accountType: this.rowsData.type,
        }
      } else if (this.accountType === 1 && this.rowsData) {
        // 平台
        this.platformAccountsForm.platformBillBankCardas =
          this.rowsData.openHouseName
        this.platformBillBankObj = {
          // 给后端的
          billBankCardaId: this.rowsData.id,
          accountType: this.rowsData.type,
        }
      } else if (this.accountType === 3 && this.rowsData) {
        // 保证金监管账户
        this.form.cashDepositTakeBillBankCardas = this.rowsData.openHouseName
        this.cashDepositBillBankObj = {
          // 给后端的
          billBankCardaId: this.rowsData.id,
          accountType: this.rowsData.type,
        }
      }
      this.type2 = false
    },
    // 账户弹窗--打开弹窗 选择性回显已选账户
    selectedWarehouse() {
      this.selectListAccountFun()
      this.type2 = true
    },
    // 账户弹窗--弹窗单选事件
    rowClick2(row) {
      if (this.selectRow2 !== row.$index) {
        this.selectRow2 = row.$index
        this.rowsData = row // 点击当前行数据
      }
    },
    // 账户弹窗初始化
    initialize2() {
      this.accountPagingObj.total = 0
      this.accountPagingObj.pageSize = 10
      this.accountPagingObj.currentPage = 1
      this.searchChangeData2 = {}
      this.selectRow2 = false
      this.arr_2 = []
    },
    // 账户弹窗--弹窗首次加载事件
    dialogOnLoad2() {
      // this.tableLoading2 = false
    },
    // 账户弹窗--弹窗搜索事件
    searchChange2(params, done) {
      this.searchChangeData2 = params
      this.selectListAccountFun(params)
      done()
    },
    // 选择仓库弹窗-list数据接口
    selectListAccountFun(filter = {}) {
      this.tableLoading2 = true
      const params = {
        enterpriseName: filter.enterpriseName,
        bankCardNo: filter.bankCardNo,
        accountType: this.accountType,
        current: this.accountPagingObj.currentPage,
        size: this.accountPagingObj.pageSize,
      }
      if (this.accountType !== 1) {
        params.enterpriseId = sessionStorage.getItem('companyId')
      }
      selectListAccount(params).then(({ data }) => {
        if (data.success) {
          this.tableLoading2 = false
          const { data: resData } = data
          this.accountPagingObj.total = resData.total || 0
          if (resData.records.length) {
            this.arr_2 = resData.records
            // 反选之前已选列
            let onlyId
            const managementA =
              this.managementAccountsForm.capitalBillBankCardas
            const platformA = this.platformAccountsForm.platformBillBankCardas
            const cashDepositA = this.form.cashDepositTakeBillBankCardas
            if (this.accountType === 2 && managementA) {
              onlyId = managementA // 资方
            } else if (this.accountType === 1 && platformA) {
              onlyId = platformA // 平台
            } else if (this.accountType === 3 && cashDepositA) {
              onlyId = cashDepositA // 保证金监管账户
            }
            for (const [index, item] of resData.records.entries()) {
              if (onlyId && item.openHouseName === onlyId) {
                this.selectRow2 = index
                break
              } else if (
                resData.records.length - 1 == index &&
                (this.selectRow2 === 0 || this.selectRow2)
              ) {
                this.selectRow2 = false
              }
            }
          } else {
            this.arr_2 = []
          }
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.guarantee {
  // 选择资方账户样式修改
  ::v-deep {
    #my-corre-input-select {
      cursor: pointer;
    }
    .el-input__suffix {
      font-size: 16px;
      color: rgba(112, 112, 112, 100);
    }
    // 表格操作栏
    .management-accounts .avue-form__menu {
      display: none;
    }
  }
  .titleTop {
    width: 319px;
    height: 24px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetTop {
    border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding-top: 35px;
    box-sizing: border-box;
  }
  .titleBottom {
    width: 319px;
    height: 24px;
    margin-top: 19px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-bold;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 16px;
      line-height: 20px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
      text-align: center;
      transform: translateY(2px);
      box-sizing: border-box;
      margin-right: 4px;
    }
  }
  .guaranteeSetBottom {
    // border: 2px solid RGB(245, 245, 245);
    // border-radius: 10px;
    // padding: 35px 30px 0 30px;
    // box-sizing: border-box;

    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
      .avue-form__menu {
        display: none;
      }
      .avue-form__row {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
    }
  }
  .guaranteeSetBottom-switch {
    .capital-box {
      .chaldren-for-box {
        display: flex;
        align-items: center;
        height: 50px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 100);
        text-align: center;
        border: 1px solid rgba(228, 228, 228, 100);
        padding: 10px 20px;
        box-sizing: border-box;
        margin-bottom: 12px;
        transition: border-color 0.3s;
        cursor: pointer;

        &::last-child {
          margin-bottom: 0;
        }

        &:hover {
          border-color: #1877f5;
        }
        .switch-container {
          // switch组件样式修改
          ::v-deep {
            .el-switch__core {
              height: 30px;
              border-radius: 20px;

              &::after {
                width: 22px;
                height: 22px;
                top: 3px;
                left: 3px;
              }
            }
            .el-switch.is-checked .el-switch__core::after {
              margin-left: -25px;
              left: 100%;
            }
          }
        }
        .type-container {
          border-radius: 43px;
          background-color: rgba(234, 236, 241, 100);
          color: rgba(0, 7, 42, 100);
          font-size: 12px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 10px;
          box-sizing: border-box;
          display: inline;
          margin-left: 20px;
        }

        .Interest-rules-container {
          flex: 1;
          text-align: right;
          .rules-info {
            height: 20px;
            width: 90%;
            line-height: 20px;
            color: rgba(16, 16, 16, 100);
            font-size: 14px;
            font-family: SourceHanSansSC-regular;
            display: inline-block;
          }
          .tip-container {
            width: 84px;
            height: 20px;
            line-height: 20px;
            color: rgba(154, 154, 154, 100);
            font-size: 14px;
            text-align: right;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
    }
  }
  .choose-cost {
    ::v-deep {
      .el-tag.el-tag--info {
        border-radius: 100px;
        color: #000;
      }
    }
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
  }
  /deep/ .el-input.is-disabled .el-input__inner {
    color: #4c4b4b !important;
  }
}
</style>
