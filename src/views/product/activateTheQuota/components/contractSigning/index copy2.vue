<template>
  <div class="contractSigning-box">
    <div class="authorization-children">
      <template v-if="getGoodsContractTemplateList.length">
        <div
          class="authorization-item"
          v-for="item in getGoodsContractTemplateList"
          :key="item.id"
        >
          <a-spin :spinning="item.loadingType">
            <div class="authorization-item-box">
              <div
                :class="{
                  'authorization-item-left': item.completeSignStatus,
                  'authorization-item-left-success': !item.completeSignStatus,
                }"
              >
                <span>{{ item.statusType }}</span>
                <span>{{ item.contractTemplateName }}</span>
              </div>
              <div
                class="authorization-item-right"
                @click="immediatelySignedClick(item)"
                v-if="item.completeSignStatus"
              >
                <span>立即签署</span>
                <span>
                  <MySvgIcon
                    icon-class="icon-arrow-right1"
                    style="color: #0d55cf; font-size: 20px"
                  />
                </span>
              </div>
              <div
                class="authorization-item-right"
                @click="getContractDownLoadUrl(item)"
                v-else
              >
                <span>查看合同</span>
                <span>
                  <MySvgIcon
                    icon-class="icon-arrow-right1"
                    style="color: #0d55cf; font-size: 20px"
                  />
                </span>
              </div>
            </div>
          </a-spin>
        </div>
      </template>
      <div v-else-if="!unLength" class="spin-box">
        <a-spin />
      </div>
      <div v-else class="content-box-empty" style="margin-bottom: 20px">
        <div class="receiving-empty">
          <div class="receiving-box">
            <img src="@/assets/images/empty_3.svg" />
            <span class="receiving-title">暂无合同</span>
            <!-- <span class="receiving-address" @click="handleOpen()">添加账户</span> -->
          </div>
        </div>
      </div>
    </div>
    <!-- 操作menu -->
    <div class="authorization-menu-box">
      <span>
        <a-button
          :block="true"
          :ghost="false"
          shape="round"
          size="large"
          type="primary"
          @click="nextToPape()"
        >
          下一步
        </a-button>
      </span>
    </div>
    <DialogAuthority ref="dialogAuthority" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'AuthorizationS',
}
</script>
<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const store = useStore()
import { message } from 'ant-design-vue'
import { PRODUCT_CREDIT_LIMIT_API, PRODUCT_VERIFY_API } from '@/api/index.js'
import { requestDictMap } from '@/api/common'
import DialogAuthority from '@/views/user/components/Dialog/dialogAuthority.vue'

// 合同列表
const getGoodsContractTemplateList = ref([])
// 判断是否有合同未签署（没配置合同就不会判断）
const signStatus = ref(false)
// 合同生成或查看事件防重提
const lock = ref(true)
// 已签署或已完成的合同id保存数组
const contractIdList = ref([])
// list接口无数据记录状态
const unLength = ref(false)
// 是否登录（Boolean）
const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
// 是否核心企业（Boolean）
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
// 是否签署合同并回来（Boolean）
const contractLoadType = computed<boolean>(
  () => store.getters['Product/contractLoadType']
)
// 下一页返回上一步切换状态（Boolean）
const goBackPapeLock = computed<boolean>(
  () => store.getters['Product/goBackPapeLock']
)
// 是否演示服（Boolean）
const demonstrator = computed<boolean>(
  () => store.getters['Product/demonstrator']
)
// 演示服IP地址（String）
const demonstratorPath = computed<string>(
  () => store.getters['Product/demonstratorPath']
)
// 用于开发跳过合同签署（Boolean）
const demonstratorTest = computed<boolean>(
  () => store.getters['Product/demonstratorTest']
)
const roleMap = computed<any>(() => store.getters['Role/roleMap'])
const dialogAuthority = ref(null)

// 判断是否演示服
const determine = () => {
  const host = window.location?.host.split(':')[0]
  if (host === demonstratorPath.value || demonstratorTest.value) {
    store.commit('Product/setDemonstrator', true)
  } else {
    store.commit('Product/setDemonstrator', false)
  }
}

// 获取浏览器url参数并整理上上签处理过的url
const getParams = key => {
  const search = window.location.search.replace(/^\?/, '')
  const pairs = search.split('&')
  const paramsMap = pairs
    .map(pair => {
      const [key, value] = pair.split('=')
      if (key.includes('amp;')) {
        const [a, b] = key.split(';')
        return [decodeURIComponent(b), decodeURIComponent(value)]
      }
      return [decodeURIComponent(key), decodeURIComponent(value)]
    })
    .reduce((res, [key, value]) => Object.assign(res, { [key]: value }), {})
  if (paramsMap.contractId) {
    sessionStorage.setItem('contractIdSession', paramsMap.contractId)
  }
  router.replace({
    query: {
      goodId: paramsMap.goodId,
      goodType: paramsMap.goodType,
      flowType: paramsMap.flowType,
    },
  })
  return paramsMap[key] || ''
}

// 获取合同模板
const getGoodsContractTemplateFun = dictionaries => {
  const params = {
    goodsId: route.query.goodId,
    signNode: '3-0',
    signUser: isCoreEnterpriseAccount.value ? 2 : 1, // 企业类型 1 融资企业 2 核心企业 3 所有人
    processType: isCoreEnterpriseAccount.value ? 8 : 3, // 流程类型 3 融资企业 8 核心企业
  }
  PRODUCT_CREDIT_LIMIT_API.getGoodsContractTemplate(params).then(({ data }) => {
    const { data: resData } = data
    if (data.success && resData.length) {
      for (const item of resData) {
        // 签署完成提示用户
        if (
          item.contractId === sessionStorage.getItem('contractIdSession') &&
          [3, 5].includes(item.status)
        ) {
          message.success(`${item.contractTemplateName}已签署`)
          sessionStorage.removeItem('contractIdSession')
        }
        if ([1, 2, 4, 6].includes(item.status)) {
          // 禁止提交
          signStatusSwitch()
        } else {
          // 保存已签署合同id用于提交给后端
          contractIdList.value.push(item.contractId)
        }
        let statusType = ''
        statusType = dictionaries[item.status]
        getGoodsContractTemplateList.value.push({
          id: item.id,
          status: item.status,
          templateId: item.templateId,
          contractTemplateName: item.contractTemplateName,
          statusType: statusType,
          loadingType: false,
          contractId: item.contractId,
          completeSignStatus:
            item.status == 5 || item.status == 3 ? false : true,
        })
        if (contractLoadType.value) {
          store.commit('Product/setContractLoadType', false) // 合同签署完成状态检测loading
        }
      }
      return
    }
    unLength.value = true
  })
}

// 刷新后请求兼容
// 下一个页面进行上一步操作不进入
if (!goBackPapeLock.value) {
  watchEffect(() => {
    if (isLogined.value && route.query.goodId) {
      if (getParams('contractId')) {
        store.commit('Product/setContractLoadType', true) // 合同签署完成状态检测loading
      } else {
        requestDictMap('contract_status').then(({ data }) => {
          if (data.success) {
            const { data: resData } = data
            const contractMap = {}
            for (const item of resData) {
              contractMap[item.dictKey] = item.dictValue
            }
            getGoodsContractTemplateFun(contractMap) // 获取合同模板list数据方法
          }
        })
      }

      const params = {
        businessId: route.query.goodId,
        type: isCoreEnterpriseAccount.value ? 8 : 3, // 流程类型 3 融资企业 8 核心企业
      }
      // 查询申请额度流程进度
      PRODUCT_VERIFY_API.getByBusinessIdAndType(params)
        .then(res => {
          const resData = res.data
          if (resData.code == 200 && resData.data) {
            store.commit('Product/setActivateTheQuota', resData.data.progress)
            // store.commit('Product/setActivateTheQuota', 1)
          } else if (resData.code == 200 && !resData.data) {
            store.commit('Product/setActivateTheQuota', 0)
          }
        })
        .catch(({ msg, hideMsgFunc }) => {
          // console.error(data)
          // func()
        })
    }
  })
} else {
  if (isLogined.value && route.query.goodId) {
    if (getParams('contractId')) {
      store.commit('Product/setContractLoadType', true) // 合同签署完成状态检测loading
    } else {
      requestDictMap('contract_status').then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const contractMap = {}
          for (const item of resData) {
            contractMap[item.dictKey] = item.dictValue
          }
          getGoodsContractTemplateFun(contractMap) // 获取合同模板list数据方法
        }
      })
    }
  }
}

determine() //检查是否是测试服

/**
 * 发送合同
 * @description:
 * @param {*} item 用户点击的合同信息
 * @return {*}
 * @author: 95hwp27
 */
const immediatelySignedClick = item => {
  if (!roleMap.value.my_product_activation_button_signing) {
    dialogAuthority.value.handleOpen()
    return
  }
  if (lock.value) {
    lock.value = false
    item.loadingType = true // 启动loading

    const data = {
      templateId: item.templateId, // 模板id
      goodId: route.query.goodId,
      signNode: '3-0', // 签署节点
      processType: isCoreEnterpriseAccount.value ? 8 : 3, // 流程类型 3 融资企业 8 核心企业
      goodsType: route.query.goodType, // 产品类型
      // businessId: '81', //先不传 流程ID
    }
    // 生成合同
    PRODUCT_CREDIT_LIMIT_API.generateContractWithBusiness(data)
      .then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          const paramsDataed = {
            contractId: resData,
            returnUrl: `${window.location.href}&contractId=${resData}`,
          }
          // 签署合同
          PRODUCT_CREDIT_LIMIT_API.skipToSign(paramsDataed)
            .then(({ data }) => {
              if (data.success) {
                const { data: resDataed } = data
                window.open(resDataed)
                lock.value = true
                item.loadingType = false // 关闭loading
                item.statusType = '签署中' // 修改文案
              }
            })
            .catch(({ msg, hideMsgFunc }) => {
              lock.value = true
              item.loadingType = false // 关闭loading
            })
        }
      })
      .catch(({ msg, hideMsgFunc }) => {
        lock.value = true
        item.loadingType = false // 关闭loading
        // console.error(data)
        // func()
      })
  }
}

/**
 * 查看合同
 * @description:
 * @param {*} item 用户点击的合同信息
 * @return {*}
 * @author: 95hwp27
 */
const getContractDownLoadUrl = item => {
  if (!roleMap.value.my_product_activation_button_view) {
    dialogAuthority.value.handleOpen()
    return
  }
  if (lock.value) {
    lock.value = false
    item.loadingType = true // 启动loading
    // 查看合同
    PRODUCT_CREDIT_LIMIT_API.getContractDownLoadUrl({
      contractId: item.contractId,
    })
      .then(resed => {
        const resDataed = resed.data
        if (resDataed.code == 200 && resDataed.data) {
          window.open(resDataed.data)
          lock.value = true
          item.loadingType = false // 关闭loading
        }
      })
      .catch(({ msg, hideMsgFunc }) => {
        lock.value = true
        item.loadingType = false // 关闭loading
      })
  }
}

// 下一步事件
const nextToPape = () => {
  if (signStatus.value && !demonstrator.value) {
    message.error('还有授权书未签署,请先签署完成')
    return
  }

  const params = {
    businessId: route.query.goodId,
    type: isCoreEnterpriseAccount.value ? 8 : 3, // 流程类型 3 融资企业 8 核心企业
  }
  // 获取流程进度
  PRODUCT_VERIFY_API.getByBusinessIdAndType(params).then(res => {
    const resData = res.data
    if (resData.code == 200 && !resData.data) {
      const paramsSave = {
        businessId: route.query.goodId,
        progress: 1,
        type: isCoreEnterpriseAccount.value ? 8 : 3, // 流程类型 3 融资企业 8 核心企业
      }
      // 保存申请进度
      PRODUCT_VERIFY_API.businessProcessProgressSave(paramsSave).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          store.commit('Product/setActivateTheQuota', 1)
        }
      })
    } else if (resData.code == 200 && resData.data) {
      store.commit('Product/setActivateTheQuota', 1)
    }
  })
}

// 切换允许提交状态
const signStatusSwitch = () => {
  signStatus.value = true
}
</script>

<style lang="scss" scoped>
.contractSigning-box {
  position: relative;
  max-width: 1400px;
  margin: auto;
  box-sizing: border-box;

  .authorization-children {
    margin-top: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .authorization-item {
      width: 700px;
      height: 64px;
      border-radius: 8px;
      border: 1px solid #e1e4e8;
      transition: border 0.3s;
      box-sizing: border-box;
      margin-bottom: 20px;
      cursor: context-menu;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        border: 1px solid #0c66ff;
      }

      :deep(.ant-spin-nested-loading) {
        height: 100%;
      }

      .authorization-item-box {
        height: 64px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
      }

      .authorization-item-left {
        & > span:first-child {
          box-sizing: border-box;
          padding: 4px 10px;
          background: #ebf5ff;
          border-radius: 12px;
          margin-right: 8px;

          font-size: 11px;
          @include family-PingFangSC-Semibold-SFProText;
          font-weight: 600;
          color: #0d55cf;
        }

        & > span:last-child {
          font-size: 14px;
          @include family-PingFangSC-Semibold;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
        }
      }

      .authorization-item-left-success {
        & > span:first-child {
          box-sizing: border-box;
          padding: 4px 10px;
          background: #cff8eb;
          border-radius: 12px;
          margin-right: 8px;

          font-size: 11px;
          @include family-PingFangSC-Semibold-SFProText;
          font-weight: 600;
          color: #00865a;
        }

        & > span:last-child {
          font-size: 14px;
          @include family-PingFangSC-Semibold;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
        }
      }

      .authorization-item-right {
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover span:first-child {
          text-decoration: underline;
        }

        & > span:first-child {
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #0d55cf;
        }
      }
    }

    .spin-box {
      display: flex;
      justify-content: center;
    }

    .content-box-empty {
      display: flex;
      justify-content: center;
      align-items: center;

      .receiving-empty {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .receiving-box {
          display: flex;
          align-items: center;
          flex-direction: column;
          & > img {
            width: 200px;
            height: 200px;
          }
          .receiving-title {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #8a94a6;
            height: 20px;
            line-height: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
          }
          .receiving-address {
            margin-top: 12px;
            cursor: pointer;
            display: block;
            width: 96px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border: 1px solid #e1e4e8;
            color: #0a1f44;
            font-size: 14px;
            font-weight: 500;
            border-radius: 100px;
            background-color: #ffffff;
          }
        }
      }
    }
  }

  .authorization-menu-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    margin-top: 47px;

    & > span {
      width: 188px;
      height: 48px;
      margin-right: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      .ant-btn-round.ant-btn-lg {
        height: 100%;
      }
    }

    & > span:last-child {
      margin-right: 0;
    }
  }
}
</style>
