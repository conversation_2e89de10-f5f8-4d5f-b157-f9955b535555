<template>
  <div class="recDetail-box">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="'订单详情'"></avue-title>
          </div>
        </avue-affix>
        <!--  -->
        <div class="apply-container">
          <div class="left">
            <div class="form-item">
              <span class="title">融资编号：</span>
              <span class="value">{{ cloudPayConfig.financingNo }}</span>
            </div>
            <div class="form-item">
              <span class="title">融资用户：</span>
              <span class="value">
                <span style="color: rgba(105, 124, 255, 100)">{{
                  cloudPayConfig.companyName
                }}</span>
              </span>
            </div>
            <div class="form-item">
              <span class="title">创建时间：</span>
              <span class="value">{{ cloudPayConfig.createTime }}</span>
            </div>
          </div>
          <div class="right">
            <div class="right-icon-box">
              <SvgIcon
                :icon-class="getInvoiceIconClass(cloudPayConfig.status)"
                style="font-size: 40px"
                :style="`fill: ${getTitleStyle(cloudPayConfig.status)}`"
              ></SvgIcon>
              <span class="status-text">{{
                getTitleText(cloudPayConfig.status)
              }}</span>
            </div>
            <div class="desc">
              <div v-if="cloudPayConfig.status === 1">
                <span>用户已申请融资,工作流程待审核</span>
              </div>
              <div v-if="cloudPayConfig.status === 2">
                <span>【上传的发票不符合,需要重新上传】</span>
              </div>
              <div v-if="cloudPayConfig.status === 3">
                <span></span>
              </div>
              <div v-if="cloudPayConfig.status === 4"></div>
              <div v-if="cloudPayConfig.status === 5">
                <span>资金方超时未放款,该订单已自动关闭</span>
              </div>
            </div>
          </div>
        </div>
        <!--  -->
        <div class="tabBar-box">
          <div
            class="tabBar-for-box"
            :class="{ 'active-box': activeName == item.value }"
            v-for="item in tabList"
            :key="item.value"
            @click="activeName = item.value"
          >
            {{ item.label }}
          </div>
        </div>
      </template>
    </basic-container>

    <!-- <div v-if="!waiting"> -->
    <!-- 申请信息 -->
    <ApplicationInformation
      v-if="activeName == '0'"
      :cloudPayConfig="cloudPayConfig"
      :tableData2="tableData2"
      :tableData3="tableData3"
      :formUpload="customerMaterialFormUpload"
    />
    <!-- 云信轨迹 -->
    <CloudLine
      :cloudPayConfig="cloudPayConfig"
      v-else-if="activeName == 1"
    ></CloudLine>
    <!-- 审批信息 -->
    <Approval v-else-if="activeName == '2'" />
    <!-- 放款信息 -->
    <RepaymentInformation v-else-if="activeName == '3'" />
    <!-- 合同信息 -->
    <ContractInformation v-else-if="activeName == '4'" />
    <!-- </div> -->
    <BasicFooter
      :btn-options="btnOptions"
      @click="handleClickEvent"
    ></BasicFooter>
  </div>
</template>

<script>
import BasicFooter from '@/components/basic-footer/index'
import ApplicationInformation from './components/applicationInformation.vue'
import RepaymentInformation from './components/repaymentInformation.vue'
import CloudLine from './components/cloudLine.vue'
import ContractInformation from './components/contractInformation.vue'
import Approval from './components/approval.vue'
import { formatMoney } from '@/util/filter.js'
import { getCountDownHtml } from '@/util/util'
import { getCloudWorkDetail } from '@/api/cloud/cloudassents'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'

const btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
  {
    btnName: '放款',
    funName: 'Load',
    type: 'primary',
  },
]

export default {
  components: {
    ApplicationInformation,
    RepaymentInformation,
    ContractInformation,
    Approval,
    CloudLine,
    BasicFooter,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          this.financingId = param.id
          this.getCloudWorkDetail({ id: param.id })
        }
      },
      immediate: true,
    },
    'cloudPayConfig.status': {
      handler(val) {
        let list = [...btnOptions]
        if (val !== 3) {
          this.btnOptions = list.filter(item => item.funName === 'Revert')
        } else {
          this.btnOptions = list
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      // 状态消息数据
      msgStatus: 1,
      mesageData: null,
      // 申请信息数据
      applicationInData: {},
      // 借款信息数据
      borrowingInformationData: [],
      // 费用信息数据
      costInformationData: [],
      // 合同信息数据
      getByContractIdData: [],
      // 审批信息数据
      approveData: [],

      activeName: '0',
      // form: {},
      iouId: null,
      rebuttomShow: false,
      waiting: false,
      variables: {},
      sum: 0,
      sum1: 0,
      tabList: [
        {
          label: '申请信息',
          value: '0',
        },
        {
          label: '云信轨迹',
          value: '1',
        },
        // {
        //   label: '审批信息',
        //   value: '2',
        // },
        // {
        //   label: '还款信息',
        //   value: '3',
        // },
        // {
        //   label: '合同信息',
        //   value: '4',
        // },
      ],

      cloudPayConfig: {},
      tableData2: [],
      tableData3: [],
      customerMaterialFormUpload: [],
      btnOptions: [],
      financingId: null,
    }
  },

  methods: {
    // 按钮操作
    handleClickEvent(name) {
      switch (name) {
        case 'Revert':
          this.$router.$avueRouter.closeTag()
          this.$router.push('/cloud/cloudfinancing')
          break
        case 'Load':
          this.$router.push(
            `/cloud/cloudLoad/${Buffer.from(
              JSON.stringify({ id: this.financingId })
            ).toString('base64')}`
          )
          break
      }
    },
    // 对应的标题
    getTitleText(state) {
      let text = ''
      if (state === 1) {
        text = '融资申请审核中'
      } else if (state === 2) {
        text = '融资申请已驳回'
      } else if (state === 3) {
        text = '融资申请待放款'
      } else if (state === 4) {
        text = '已放款'
      } else if (state === 5) {
        text = '已作废'
      }
      return text
    },
    // 对应其相关的icon
    getInvoiceIconClass(state) {
      let iconClass = ''
      switch (state) {
        case 1: // 融资申请审核中
          iconClass = 'icon-dengdai1'
          break
        case 2: // 融资申请已驳回
          iconClass = 'icon-jinggao1'
          break
        case 3: // 融资申请待放款
          iconClass = 'icon-renminbi1'
          break
        case 4: //已放款
          iconClass = 'icon-chenggong1'
          break
        case 5: // 已作废
          iconClass = 'icon-delete-filling'
          break
      }
      return iconClass
    },
    // 对应的颜色
    getTitleStyle(state) {
      let iconStyle = ''
      switch (state) {
        case 1: // 融资申请审核中
          iconStyle = '#697CFF'
          break
        case 2: // 融资申请已驳回
          iconStyle = '#DF9935'
          break
        case 3: // 融资申请待放款
          iconStyle = '#697CFF'
          break
        case 4: //已放款
          iconStyle = '#1FC374'
          break
        case 5: // 已作废
          iconStyle = '#BBBBBB'
          break
      }

      return iconStyle
    },

    // 详情数据
    getCloudWorkDetail(params) {
      getCloudWorkDetail(params).then(({ data }) => {
        let uploadArr = []
        if (data.code === 200) {
          this.cloudPayConfig = {
            ...data.data,
            financingMoney: formatMoney(data.data.financingMoney),
          }
          this.tableData2 = data.data.platformExpensesList.map(item => {
            return {
              ...item,
              repaymentTerm: item.repaymentTerm
                ? item.repaymentTerm + '期'
                : '--'
            }
          })
          this.tableData3 = data.data.invoice.map((item, index) => {
            return {
              ...item,
              index: index + 1,
              total: item.total ? item.total.split('￥') : 0,
              subtotalAmount: item.subtotalAmount
                ? item.subtotalAmount.split('￥')
                : 0,
              startDay: item.issueDate
                ? item.issueDate
                    .replace('年', '-')
                    .replace('月', '-')
                    .replace('日', '')
                : '',
            }
          })
          for (const item of data.data.contract) {
            uploadArr.push({
              ...item,
              url: item.link,
              fileType: item.link.includes('pdf') ? 'pdf' : '',
            })
          }
        }
        this.customerMaterialFormUpload.push({
          materialName: '合同',
          uploadArr,
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.recDetail-box {
  padding-bottom: 168px !important;
  .header {
    width: 100%;
    height: 50px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 10px;
    margin: 0 -20px;
  }

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 157px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .status-text {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-weight: bold;
        margin-top: 2px;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;

        .des-time {
          color: #ff5656;
        }
      }
    }
  }

  .tabBar-box {
    margin-top: 20px;
    display: flex;
    border: 1px solid rgba(105, 124, 255, 100);
    border-radius: 6px;
    overflow: hidden;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border-right: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;
      font-size: 14px;
      color: #449bfc;

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
        border-right: none;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }
    .active-box {
      background-color: #449bfc;
      color: #fff;
      &:hover {
        background-color: #449bfc;
      }
    }
  }

  .menu-box {
    display: flex;
    justify-content: center;
    align-items: center;

    .go-back {
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      border: 1px solid rgba(187, 187, 187, 100);
      padding: 4px 10px;
      box-sizing: border-box;
      cursor: pointer;
      margin-right: 18px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
