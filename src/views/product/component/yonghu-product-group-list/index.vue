<template>
  <div class="accounts-receivable-list">
    <div class="accounts-box">
      <header>
        <h1 class="he-title">订单融资产品选择</h1>
        <h4 class="he-info">用户可自行选择一个产品</h4>
      </header>
      <article>
        <div v-if="loadingType && goodListArr.length">
          <GoodsBox
            :loadingType="loadingType"
            :goodData="item"
            :nowSKey="radioValue"
            v-for="item in goodListArr"
            :key="item.id"
            @selectFun="selectFun"
          />
        </div>
        <div v-else-if="!loadingType">
          <GoodsBox
            :loadingType="loadingType"
            :goodData="{}"
            v-for="item in 5"
            :key="item"
          />
        </div>
        <div v-else class="content-box-empty" style="height: 610px">
          <div class="receiving-empty">
            <div class="receiving-box">
              <img src="@/assets/images/empty_3.svg" />
              <span class="receiving-title">暂无数据</span>
              <!-- <span class="receiving-address" @click="handleOpen()">添加账户</span> -->
            </div>
          </div>
        </div>
        <div class="loading-text-box" v-show="loadingTypeText">
          正在努力加载中…
        </div>
      </article>
      <footer class="menu-btn-dd">
        <a-button
          :block="true"
          :ghost="false"
          shape="round"
          size="large"
          type="primary"
          :loading="confirmSelectionLoadType"
          @click="confirmSelectionFun"
        >
          确认选择
        </a-button>
      </footer>
    </div>
    <!-- 选择贸易伙伴弹窗 -->
    <TradeSpace
      ref="tradeSpace"
      :goodsDetailList="goodsDetailList"
      :goodsId="radioValue"
      :grObj="grObj"
      :confirmSelectionLoadType="confirmSelectionLoadType"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'productGroupList',
}
</script>
<script setup lang="ts">
import GoodsBox from './components/goodsBox/index.vue'

import { ref, reactive } from 'vue'
// import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { formatMoney } from '@/utils/utils'
import { requestDictMap } from '@/api/common/index'
import { HOMEAPI, PRODUCT_VERIFY_API } from '@/api/index'
import { message } from 'ant-design-vue'
import TradeSpace from './components/dialogTradeSpace.vue'

const route = useRoute()
// const store = useStore()
// const tenantId = computed(() => store.getters['Auth/tenantId'])
// const paging = reactive({
//   size: 10,
//   current: 1,
// })
const goodListArr: any = ref([])
const loadingType = ref(false)
const loadingTypeText = ref(false)
const radioValue = ref(void 0)
const goodsTypeReceivable = ref(void 0)
const capitalIdReceivable = ref('')
const confirmSelectionLoadType = ref(false)
const grObj = reactive({
  groupId: '',
  groupIdcustomerMaterialId: '',
})

// 无限滚动
// const intersectionObserver = new IntersectionObserver(entries => {
//   // 如果不可见，就返回
//   if (entries[0].intersectionRatio <= 0) return
//   setTimeout(() => {
//     paging.current++
//     goodsList(true)
//   }, 500)
// })

// 开始观察
// onMounted(() => {
//   intersectionObserver.observe(document.querySelector('.loading-text-box'))
// })

// onUnmounted(() => {
//   // 关闭观察
//   intersectionObserver.disconnect()
// })

const selectFun = val => {
  radioValue.value = val
}

const confirmSelectionFun = () => {
  if (!radioValue.value) {
    message.warning('请选择一个产品，再进行确认')
    return
  }
  // 判断是否是产品组下的应收账款
  const temp = goodListArr.value.filter(item => item.id === radioValue.value)
  goodsTypeReceivable.value = temp[0].type
  capitalIdReceivable.value = temp[0].capitalId
  if (temp[0].type == 1) {
    const params = {
      goodsId: radioValue.value,
      status: 1,
    }
    PRODUCT_VERIFY_API.customerGoods(params).then(res => {
      const { data: resData, code } = res.data
      if (code === 200) {
        // 是否已经绑定贸易伙伴
        if (resData) {
          confirmSelectionLoadType.value = true
          const dataD = {
            groupId: grObj.groupId,
            customerMaterialId: grObj.customerMaterialId,
            productId: radioValue.value,
          }
          HOMEAPI.confirmProduct(dataD)
            .then(({ data }) => {
              if (data.success) {
                location.reload()
              }
            })
            .finally(() => {
              confirmSelectionLoadType.value = false
            })
        } else {
          getGoodsDetail()
          handleOpenTradeSpace()
        }
      }
    })
  } else {
    confirmSelectionLoadType.value = true
    const dataD = {
      groupId: grObj.groupId,
      customerMaterialId: grObj.customerMaterialId,
      productId: radioValue.value,
    }
    HOMEAPI.confirmProduct(dataD)
      .then(({ data }) => {
        if (data.success) {
          location.reload()
        }
      })
      .finally(() => {
        confirmSelectionLoadType.value = false
      })
  }
  // confirmSelectionLoadType.value = true
  // const dataD = {
  //   groupId: grObj.groupId,
  //   customerMaterialId: grObj.customerMaterialId,
  //   productId: radioValue.value,
  // }
  // HOMEAPI.confirmProduct(dataD)
  //   .then(({ data }) => {
  //     if (data.success) {
  //       location.reload()
  //     }
  //   })
  //   .finally(() => {
  //     confirmSelectionLoadType.value = false
  //   })
}

// list请求
const goodsList = type => {
  // if (!tenantId.value) {
  //   setTimeout(() => {
  //     goodsList()
  //   }, 200)
  //   return
  // }
  requestDictMap('goods_load_term_unit').then(res => {
    const resData = res.data
    if (resData.code == 200) {
      // 处理字典数据
      const resList = []
      for (const item of resData.data) {
        resList.push({
          key: item.dictKey,
          value: item.dictValue,
          id: item.id,
        })
      }
      if (!type) {
        goodListArr.value = []
        // loadingTypeText.value = false
        loadingType.value = false
        // paging.current = 1
      }
      const params = {
        groupId: route.query.groupId,
        // size: paging.size, // 多少条
        // current: paging.current, // 第几页
      }
      HOMEAPI.confirmProductList(params).then(({ data }) => {
        // 获取应收账款产品list数据
        const { data: resData } = data
        if (data.success && resData) {
          Object.assign(grObj, {
            customerMaterialId: resData.customerMaterialId,
            groupId: resData.groupId,
          })
          // paging.current = resData.current
          // if (resData.current < resData.pages) {
          //   loadingTypeText.value = true
          // } else {
          //   loadingTypeText.value = false
          // }

          for (const item of resData.products) {
            // 过滤出当前的最长期限单位
            const loadTer = resList.filter(
              itemS => itemS.key == item.loadTermUnit
            )
            // const labelListData = []
            // if (item.labelList) {
            //   for (const items of item.labelList) {
            //     labelListData.push({
            //       name: items.name,
            //       labelColor: items.labelColor,
            //       id: items.id,
            //     })
            //   }
            // }
            const itemObj = {
              ...item,
              loanAmountEnd: formatMoney(item.loanAmountEnd * 10000),
              loadTermUnit: loadTer.length ? loadTer[0]?.value : '',
              labelList: [],
              isHighQuality: item.id === resData.recommendGoodsId ? 1 : 2, // 是否优质产品 1 是 2 否
            }
            goodListArr.value.push(itemObj)
          }
          setTimeout(() => {
            loadingType.value = true
          }, 200)
          return
        }
        loadingType.value = true
      })
    }
  })
}

goodsList()

const goodsDetailList: any = ref([])
// 查询未被使用的贸易背景
const getGoodsDetail = () => {
  const params = {
    // goodsId: route.query.goodId,
    // goodsType: route.query.goodType,
    // capitalId: route.query.capitalId,
    goodsId: radioValue.value,
    goodsType: goodsTypeReceivable.value,
    capitalId: capitalIdReceivable.value,
  }
  PRODUCT_VERIFY_API.getGoodsDetail(params).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      goodsDetailList.value = []
      const dat = resData.data
      for (const item of dat) {
        goodsDetailList.value.push({
          label: item.companyLowerName,
          value: item.id,
        })
      }
    }
  })
}
const tradeSpace = ref(null)
const handleOpenTradeSpace = () => {
  tradeSpace.value.handleOpen()
}
</script>

<style lang="scss" scoped>
@import '@/views/product/antdStyle.scss';

.accounts-receivable-list {
  // position: relative;
  // box-sizing: border-box;
  // margin-bottom: -72px;
  // top: -72px;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   z-index: 0;
  //   width: 100%;
  //   height: 300px;
  //   background: linear-gradient(180deg, #cce6ff 0%, #f6f6f6 100%);
  // }

  .accounts-box {
    max-width: 1400px;
    // margin: 112px auto 0;
    margin: 12px auto 0;
    position: relative;
    z-index: 1;

    header {
      .he-title {
        height: 56px;
        font-size: 40px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 56px;
        text-align: center;
      }
      .he-info {
        height: 24px;
        font-size: 16px;
        font-weight: 400;
        color: #53627c;
        line-height: 24px;
        margin-top: 8px;
        text-align: center;
      }
    }

    .loading-text-box {
      height: 24px;
      font-size: 16px;
      font-weight: 400;
      color: #8a94a6;
      line-height: 24px;
      text-align: center;
      margin-bottom: 24px;
    }
  }

  .content-box-empty {
    display: flex;
    justify-content: center;
    align-items: center;

    .receiving-empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .receiving-box {
        display: flex;
        align-items: center;
        flex-direction: column;
        & > img {
          width: 200px;
          height: 200px;
        }
        .receiving-title {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #8a94a6;
          height: 20px;
          line-height: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
        }
        .receiving-address {
          margin-top: 12px;
          cursor: pointer;
          display: block;
          width: 96px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border: 1px solid #e1e4e8;
          color: #0a1f44;
          font-size: 14px;
          font-weight: 500;
          border-radius: 100px;
          background-color: #ffffff;
        }
      }
    }
  }

  .menu-btn-dd {
    width: 140px;
    text-align: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 30px;
  }
}
</style>
