<template>
  <div>
    <div v-if="processStatusType == '审批中'" class="product-businessApproval">
      <div class="product-businessApproval-flagstaff" />
      <MySvgIcon
        icon-class="icon-shijian"
        style="color: #0d55cf; font-size: 64px"
      ></MySvgIcon>
      <span class="under-review">审核中</span>
      <span class="message-span"
        >您的申请已提交，我们将会在1～7小时内进行审核，请耐心等待！</span
      >
      <div class="gohome">
        <a-button
          size="large"
          type="primary"
          ghost
          :block="true"
          shape="round"
          @click="
            type === 5 || type === 6
              ? toDetile()
              : router.push({ name: 'Home' })
          "
          >{{ type === 5 || type === 6 ? '查看详情' : '返回首页' }}</a-button
        >
      </div>
    </div>
    <div v-else class="product-businessApproval">
      <div class="product-businessApproval-flagstaff" />
      <MySvgIcon
        icon-class="icon-jinggao"
        style="color: #ff8f00; font-size: 64px"
      ></MySvgIcon>
      <span class="under-review">{{ processStatusType }}</span>
      <span class="message-span">{{ fullMessage }}</span>
      <div class="gohome">
        <a-button
          size="large"
          type="primary"
          ghost
          :block="true"
          shape="round"
          @click="goBackPape()"
          >去修改</a-button
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'businessApproval',
}
</script>
<script setup>
import { ref } from 'vue'
// import { getdata, postinfor, getview } from '@/api/user/conter'
// import NoData from '@/views/user/components/noData.vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { PRODUCT_VERIFY_API, CREDIT } from '@/api/index.js'

const router = useRouter()
const route = useRoute()
const store = useStore()

const emit = defineEmits(['goBack'])

const processStatusType = ref('审批中')
const fullMessage = ref()

const props = defineProps({
  type: {
    type: Number,
    default: 1,
  },
  bsId: {
    type: String,
  },
  // 应收融资申请自动放款两种方式的返回兼容
  backIndex: {
    type: Boolean,
  },
})

const params = {
  businessId: props.bsId || route.query.goodId,
  type: props.type,
}
const datas = {
  customerGoodsId: route.query.customerGoodsId,
}
// 兼容解冻流程进度查询
const requestApi =
  props.type == 11
    ? CREDIT.receivableUnfrozen(datas)
    : PRODUCT_VERIFY_API.getByBusinessIdAndType(params)
requestApi.then(res => {
  const resData = res.data
  if (resData.code == 200 && resData.data) {
    switch (resData.data.status) {
      case 1:
        processStatusType.value = '审批中'
        break
      case 4:
        processStatusType.value = '已驳回'
        break
    }
    if (resData.data.processInstanceId && resData.data.status == 4) {
      PRODUCT_VERIFY_API.selectProcessStatus({
        processInstanceId: resData.data.processInstanceId,
      }).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          fullMessage.value = resData.data.commentList[0].fullMessage
        }
      })
    }
  }
})

// 去修改
const goBackPape = () => {
  const paramsSave = {
    progress: 0,
    type: props.type, // 流程类型
  }
  if (props.type != 11) {
    paramsSave.businessId = props.bsId || route.query.goodId
  } else {
    paramsSave.businessId = route.query.customerGoodsId
  }
  // 修改申请进度
  PRODUCT_VERIFY_API.businessProcessProgressUpdate(paramsSave).then(
    ({ data }) => {
      // const { data: resData } = data
      if (data.success) {
        store.commit('Product/setGOBackPapeLock')
        emit('goBack', 0)
      }
    }
  )
  // if (props.type === 2 || props.type === 7) {
  // emit('goBack', 1)
  //   store.commit('Product/setCreditLimitForApplication', 1)
  // } else if (props.type === 3 || props.type === 8) {
  //   store.commit('Product/setActivateTheQuota', 1)
  // } else if (props.type === 5) {
  // emit('goBack', 0)
  // } else if (props.type === 6) {
  //   emit('goBack', 1)
  // } else if (props.type === 9) {
  //   if (props.backIndex) {
  //     emit('goBack', 1)
  //   } else {
  //     emit('goBack', 2)
  //   }
  // }
}

// 查看详情
const toDetile = () => {
  router.replace({
    name: 'financingDetails',
    query: {
      id:
        JSON.parse(sessionStorage.getItem('financingDemandId')) ||
        route.query.id,
      goodId: route.query.goodId,
      customerGoodsId: route.query.customerGoodsId,
      goodType: route.query.goodType,
      lendingMethod: route.query.lendingMethod,
      chargeMethod: route.query.chargeMethod,
    },
  })
}
</script>

<style lang="scss" scoped>
.product-businessApproval {
  position: relative;
  max-width: 1400px;
  margin: auto;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  .product-businessApproval-flagstaff {
    width: 100%;
    // height: 2px;
    background: #f1f2f4;
    margin-top: 39px;
  }

  & > #my-svg-icons {
    margin-top: 60px;
  }

  .under-review {
    font-size: 24px;
    @include family-PingFangSC-Semibold;
    font-weight: 600;
    color: #0a1f44;
    line-height: 32px;
    margin-top: 12px;
  }

  .message-span {
    font-size: 14px;
    @include family-PingFangSC-Medium;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
    margin-top: 12px;
  }
  .gohome {
    margin-top: 48px;

    .ant-btn-round.ant-btn-lg {
      padding: 12px 28px;
      height: 48px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      border-color: #0c66ff;

      font-size: 16px;
      @include family-PingFangSC-Medium;
      font-weight: 500;
      color: #0d55cf;
      line-height: 24px;
    }
  }
}
</style>
