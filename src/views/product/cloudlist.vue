<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="status" slot-scope="{ row }">
        <el-tag type="success" v-if="row.status === 2">生效中</el-tag>
        <el-tag type="primary" v-if="row.status === 3">已冻结</el-tag>
        <el-tag type="info" v-if="row.status === 4">已禁用</el-tag>
        <el-tag type="info" v-if="row.status === 5">已过期</el-tag>
        <el-tag type="warning" v-if="row.status === 6">调整中</el-tag>
        <el-tag type="primary" v-if="row.status === 7">续期中</el-tag>
      </template>
      <template slot="taskNo" slot-scope="{ row }">
        <el-link
          type="primary"
          :underline="false"
          @click="handleBtnView(row)"
          >{{ row.taskNo }}</el-link
        >
      </template>
      <template slot="menu" slot-scope="{ row }">
        <!-- <el-button
          type="text"
          size="small"
          v-if="row.status === 4"
          @click="enableAmount(row.id)"
          >启用
        </el-button> -->
        <el-button type="text" size="small" @click="applyClound(row)">
          云信开单
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="查看"
      :visible.sync="viewFrozenReasonVisible"
      :modal-append-to-body="false"
    >
      <avue-form
        ref="form"
        v-model="viewFrozenReasonForm"
        :option="frozenReason"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getCloudList,
  getDetail,
  frozenAmount,
} from '@/api/riskmana/enterprisequota'
import { mapGetters } from 'vuex'
import { routerMapKeyToPath } from '@/views/business/config'
import { getSingleProcess } from '@/api/business/businessprocess'

export default {
  data() {
    return {
      form: {},
      frozenForm: {},
      thawForm: {},
      disableForm: {},
      query: {
        enterpriseTypeEqual: 2,
      },
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      viewFrozenReasonForm: {},
      frozenReason: {
        detail: true,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '冻结原因',
            prop: 'frozenReason',
            type: 'textarea',
            span: 24,
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入冻结原因',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '解冻原因',
            prop: 'thawReason',
            type: 'textarea',
            display: false,
            hide: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入解冻原因',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '禁用原因',
            prop: 'disableReason',
            type: 'textarea',
            display: false,
            hide: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入禁用原因',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      frozenVisible: false,
      thawVisible: false,
      disableVisible: false,
      viewFrozenReasonVisible: false,
      selectionList: [],
      frozenOption: {
        column: [
          {
            label: '冻结原因',
            prop: 'frozenReason',
            type: 'textarea',
            span: 24,
            hide: true,
            rules: [
              {
                required: true,
                message: '请输入冻结原因',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      thawOption: {
        column: [
          {
            label: '解冻原因',
            prop: 'thawReason',
            type: 'textarea',
            hide: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入解冻原因',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      disableOption: {
        column: [
          {
            label: '禁用原因',
            prop: 'disableReason',
            type: 'textarea',
            hide: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入禁用原因',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        dialogWidth: '45%',
        selection: false,
        dialogClickModal: false,
        column: [
          // {
          //   label: '额度编号',
          //   prop: 'quotaNo',
          //   width: 130,
          //   display: false,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入额度编号',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          // {
          //   label: '任务编号',
          //   prop: 'taskNo',
          //   display: false,
          //   width: 150,
          //   slot: true,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入任务编号',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          // {
          //   label: '任务编号',
          //   prop: 'taskNoEqual',

          //   display: false,
          //   hide: true,
          // },
          // {
          //   label: 'Logo',
          //   prop: 'avatar',
          //   type: 'upload',
          //   display: false,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入头像',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '产品名称',
            prop: 'goodsName',
            width: 100,
            display: false,
            search: true,
            rules: [
              {
                required: true,
                message: '产品名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '资金方',
            prop: 'capitalName',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入资方名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '开通企业',
            prop: 'enterpriseName',
            width: 170,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入企业名称',
                trigger: 'blur',
              },
            ],
          },
          // {
          //   label: '产品类型',
          //   prop: 'productTypeStr',
          //   width: 100,
          //   display: false,
          //   rules: [
          //     {
          //       required: true,
          //       message: '产品类型',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '授信总额',
            prop: 'creditAmountStr',
            width: 120,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入授信额度',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '可用额度',
            prop: 'availableAmountStr',
            width: 120,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入已使用额度',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '已用额度',
            prop: 'usedAmountStr',
            width: 120,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入已使用额度',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '冻结额度',
            prop: 'frozenAmountStr',
            width: 120,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入已使用额度',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '年利率',
            prop: 'annualInterestRateStr',
            width: 90,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入年利率',
                trigger: 'blur',
              },
            ],
          },
          // {
          //   label: '服务利率',
          //   prop: 'serviceRateStr',
          //   display: false,
          //   width: 90,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入服务利率',
          //       trigger: 'blur',
          //     },
          //   ],
          // },

          {
            label: '生效日',
            prop: 'effectiveTime',
            display: false,
            type: 'datetime',
            format: 'yyyy-MM-dd',
            width: 100,
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            rules: [
              {
                required: true,
                message: '请输入生效时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '到期日',
            prop: 'expireTime',
            type: 'datetime',
            format: 'yyyy-MM-dd',
            width: 100,
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入过期时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '额度状态',
            prop: 'status',
            display: false,
            slot: true,
            type: 'select',
            fixed: 'right',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=core_enterprise_quota_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
          },
          {
            label: '额度状态',
            prop: 'statusEqual',
            display: false,
            hide: true,
            slot: true,
            search: true,
            type: 'select',
            fixed: 'right',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=core_enterprise_quota_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.coreenterprisequota_add, false),
        viewBtn: this.vaildData(
          this.permission.coreenterprisequota_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.coreenterprisequota_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.coreenterprisequota_edit,
          false
        ),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    handleBtnView(row) {
      getSingleProcess(row.processInstanceId).then(resp => {
        let data = resp.data.data
        let taskId = data.taskId
        let processDefinitionKey = data.processDefinitionKey
        const target = routerMapKeyToPath[processDefinitionKey]
        if (!target) return
        this.$router.push(
          `${target}/detail/${Buffer.from(
            JSON.stringify({
              taskId,
              processInsId: row.processInstanceId,
            })
          ).toString('base64')}`
        )
      })
    },
    viewFrozenReason(row) {
      this.viewFrozenReasonForm.frozenReason = row.frozenReason
      this.viewFrozenReasonVisible = true
    },
    openDisableDialog(row) {
      this.disableForm.id = row.id
      this.disableVisible = true
    },
    applyClound(row) {
      const params = {
        id: row.id,
        btnState: 0, // 申请开单
      }
      this.$router.push(
        `/system/cloudBillingLetter/${Buffer.from(
          JSON.stringify(params)
        ).toString('base64')}`
      )
    },
    openThawDialog(row) {
      this.thawForm.id = row.id
      this.thawVisible = true
    },
    openFrozenDialog(row) {
      this.frozenForm.id = row.id
      this.frozenVisible = true
    },
    frozenSubmit(row, done) {
      frozenAmount(this.frozenForm).then(() => {
        this.onLoad(this.page)
        this.$message({
          type: 'success',
          message: '操作成功!',
        })
        done()
      })
      this.frozenVisible = false
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getCloudList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
