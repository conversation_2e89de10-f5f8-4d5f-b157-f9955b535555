<template>
  <div class="good-table-container">
    <div class="table-top">
      <el-table
        ref="table1"
        :data="tableData"
        style="width: 100%; margin-top: 13px"
        class="table-border-style"
      > 
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" inline class="demo-table-expand">
              <div class="goodscontent">
                <div class="goodsgg">
                  <span>商品规格</span>
                  <ul v-for="item in props.row.redeemCommodityList" :key="item.index">     
                    <li>
                      {{item.spec}}
                    </li> 
                  </ul>
                </div>
                <div class="goodsgg">
                  <span>数量</span>
                  <ul v-for="item in props.row.redeemCommodityList" :key="item.index">     
                    <li>
                      {{item.num}}
                    </li> 
                  </ul>
                </div>
                <div class="goodsgg">
                  <span>融资单价</span>
                  <ul v-for="item in props.row.redeemCommodityList" :key="item.index">     
                    <li>
                      {{item.financingPrice}}
                    </li> 
                  </ul>
                </div>
                </div>
              </el-form>         
          </template>
      </el-table-column>
        <el-table-column
          v-for="item in columnOptions"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width || ''"
        >
          <template slot-scope="scope">
            <div class="table-goodsInfo" v-if="item.prop === 'goodInfo'">
              <img :src="scope.row.commodityUrl" alt />
              <div class="table-goodsInfo-right">
                <div
                  class="goodsInfo-right-top"
                  @click="handleLink(scope.row.commodityId)"
                >
                  {{ scope.row.name }}
                </div>
                <div class="goodsInfo-right-bottom">
                  {{ scope.row.spec }}
                </div>
              </div>
            </div>
            <div v-else-if="item.prop === 'unitPrice'">
              <div class="table-prices">
                ￥{{ scope.row.purchasePrice | formatMoney }}
              </div>
            </div>
            <div v-else-if="item.prop === 'financingPrice'">
              <span
                class="table-text-red"
                v-if="['chaseOrder', 'goodsTable3'].includes(page)"
                >￥{{ scope.row.financingPrice | formatMoney }}</span
              >
              <span v-else class="table-text-black"
                >￥{{ scope.row.financingPrice | formatMoney }}</span
              >
            </div>
            <div
              v-else-if="
                ['chaseOrder', 'goodsTable3'].includes(page) &&
                item.prop === 'financingTotal'
              "
            >
              <span class="table-text-red"
                >￥{{ scope.row.financingTotal | formatMoney }}</span
              >
            </div>
            <div v-else-if="item.prop === 'storageMsg'">
              <div>
                <div>{{ scope.row.storageName }}</div>
                <div>{{ scope.row.warehouseName }}</div>
              </div>
            </div>
            <div v-else-if="page === 'goodsTable3' && item.prop === 'redeemNo'">
              <span class="text-blue">{{ scope.row.redeemNo }}</span>
            </div>
            <div v-else-if="page === 'goodsTable3' && item.prop === 'status'">
              <span
                class="table-status"
                :class="getTextColor(scope.row.status)"
                >{{ scope.row.statusStr }}</span
              >
            </div>
            <div v-else-if="item.prop === 'action'">
              <el-button type="text" class="table-action">出入库记录</el-button>
            </div>
            <div v-else>
              <span>{{ scope.row[item.prop] }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'
export default {
  name: 'good-table-container',
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    columnOptions: {
      type: Array,
      default: () => [],
    },
    page: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  methods: {
    // 跳转到商品详情
    handleLink(id) {
      if (id) {
        this.$router.push(
          `/commodity/lookProducts/` +
            Buffer.from(JSON.stringify(id)).toString('base64')
        )
      }
    },
    getTextColor(state) {
      let textColor = ''
      switch (state) {
        case 0:
          textColor = 'text-blue'
          break
        case 1:
          textColor = 'text-green'
          break
      }
      return textColor
    },
  },
}
</script>

<style lang="scss" scoped>
.good-table-container {
  .table-border-style {
    border-top: 1px solid #e9ebf0;
    border-left: 1px solid #e9ebf0;
    border-right: 1px solid #e9ebf0;
    .table-goodsInfo {
      display: flex;
      align-items: center;
      & img {
        width: 72px;
        height: 72px;
        object-fit: cover;
        margin-right: 8px;
      }
      .table-goodsInfo-right {
        .goodsInfo-right-top {
          cursor: pointer;
          width: 196px;
          color: #697cff;
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 12px;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .goodsInfo-right-bottom {
          width: 196px;
          line-height: 20px;
          color: rgba(141, 141, 141, 100);
          font-size: 14px;
          text-align: left;
          font-family: SourceHanSansSC-regular;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }
    .table-text-red {
      color: #ff2929;
      font-weight: 500;
      font-size: 14px;
    }
    .table-text-black {
      color: #444444;
      font-weight: 500;
      font-size: 14px;
    }

    .table-status {
      line-height: 20px;
      font-size: 14px;
      font-family: Microsoft Yahei;
      font-weight: 500;
    }
    .table-action {
      color: #697cff;
    }
  }
}

.text-blue {
  color: #697cff;
}
.text-green {
  color: #1fc374;
}
li{
  list-style-type:none;
}
.goodscontent{
  position: relative;
  left:25%;
  display:grid;
  grid-template-columns:100px 100px 100px;
}
</style>
