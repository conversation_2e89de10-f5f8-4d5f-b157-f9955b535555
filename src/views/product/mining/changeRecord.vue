<template>
  <div class="creditLimitFinancing-box">
    <template v-if="!noData">
      <basic-container>
        <div class="order-header-container">
          <!--   -->
          <el-radio-group v-model="tragetId" @change="handleTabButton">
            <el-radio-button
              v-for="item in listRecords"
              :key="item.id"
              :label="item.label"
            >
              {{ item.value }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <!--  -->
        <div v-for="item in tragetArr" :key="item.mName">
          <div class="table-top refund">
            <h3 style="margin: 10px 0">变更信息</h3>
            <el-table
              ref="table5"
              :data="item.listData"
              style="width: 100%; margin-top: 13px"
              class="table-border-style"
            >
              <!-- <el-table-column prop="userName" label="融资用户">
              </el-table-column> -->
              <!-- <el-table-column
                label="融资编号"
                prop="financeNo"
              ></el-table-column> -->
              <!-- <el-table-column prop="iouNo" label="借据单号"></el-table-column> -->
              <el-table-column prop="redemptionAmount" label="货物数量">
              </el-table-column>
              <el-table-column prop="goodsUnit" label="单位"> </el-table-column>
              <el-table-column prop="goodsNum" label="退货数量">
              </el-table-column>
              <el-table-column
                prop="refundAmount"
                label="退款金额"
              ></el-table-column>
              <el-table-column label="联系人" prop="linkMan"></el-table-column>
              <el-table-column
                label="联系方式"
                prop="linkPhone"
              ></el-table-column>
            </el-table>
          </div>
          <div v-if="item.appendObj.length">
            <div class="chain-line" />
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item
                  v-for="citem in item.appendObj"
                  :key="citem.id"
                  :label="citem.label"
                >
                  <span v-if="citem.label !== '附件信息'">
                    {{ citem.value }}
                  </span>
                  <div v-else class="my-basebox">
                    <BaseImageUpload
                      :imgData.sync="citem.value"
                      :disabled="true"
                    />
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>
    </template>
    <el-empty
      v-else
      style="margin: 0 24px 24px 24px"
      description="暂无数据"
      :image-size="116"
    />

    <!-- 脚脚 -->
    <!-- <div v-if="$route.query.fId" class="occupied-box" style="height: 405px" /> -->
    <!-- <template>
      <div class="occupied-box" />
      <div class="footer-container">
        <el-button
          type="success"
          class="nextBtn btn-bg-color-blue"
          :loading="subbmitType"
          @click="backBtnFun"
        >
          返 回
        </el-button>
      </div>
    </template> -->
  </div>
</template>

<script>
import BaseImageUpload from '@/components/BaseImageUpload'
import { getHistoryByFinanceId } from '@/api/purchase/purchaseplan'

export default {
  components: {
    BaseImageUpload,
  },
  watch: {
    activeNames2() {
      setTimeout(() => {
        this.$refs.table2.$ready = false // 解决element组件折叠面板与表单冲突bug
      }, 50)
    },
  },
  data() {
    return {
      objData: {},
      tragetArr: [],
      tragetId: '',
      listRecords: [],
      noData: false,
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    // 获取详情 
    getDetail() {
      const paramsD = this.$route.query.fId || this.$route.query.id

      getHistoryByFinanceId(paramsD).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          if (JSON.stringify(resData) === '{}') {
            this.noData = true
            return
          }
          const arr = []
          const objData = {}
          for (const key in resData) {
            arr.unshift(resData[key])
            // arr.push(resData[key])
          }

          let indexN = arr.length
          for (const itemD of arr) {
            let textType = ''
            switch (itemD.changeType) {
              case 1:
                textType = '退款退货'
                break
              case 2:
                textType = '退款'
                break
              case 3:
                textType = '退货'
                break
            }
            // 切换tab列表数据

            let textL = ''
            indexN--
            if (!indexN) {
              textL = `变更前`
            } else {
              textL = `第${indexN}次变更`
            }
            this.listRecords.push({
              id: itemD.id,
              label: itemD.id,
              value: `${textL} ${textType} ${itemD.createTime}`,
            })
            let obj = {
              mName: itemD.id,
              listData: [],
              appendObj: this.appendPretreatment(itemD),
            }
            obj.listData.push(itemD)

            objData[`${itemD.id}`] = obj
          }

          this.tragetId = arr[0].id
          this.tragetArr = [objData[this.tragetId]]
          this.objData = objData
        }
      })
    },
    // 变更记录切换
    handleTabButton(acceptVal) {
      this.tragetArr = [this.objData[acceptVal]]
    },
    // 返回事件
    backBtnFun() {
      this.$router.$avueRouter.closeTag()
      this.handleCloseTag('/loan/financingorder')
    },
    appendPretreatment(datas) {
      let newArr = []
      newArr = [
        {
          id: 1,
          label: '变更日期',
          value: datas.createTime,
        },
        {
          id: 2,
          label: '附件信息',
          value: datas.attachList,
        },
        {
          id: 3,
          label: '变更原因',
          value: datas.reason,
        },
      ]
      return newArr
    },
  },
}
</script>

<style lang="scss" scoped>
.creditLimitFinancing-box {
  margin-top: 10px;

  ::v-deep .el-table .aggregate-row {
    background-color: #f7f7f7;
    font-size: 20px;
    font-weight: 600;
  }

  .order-header-container {
    width: 100%;
    // height: 41px;
    // height: 37px;
    // border: 1px solid #f3f3f3;
    background-color: #ffff;
    // margin-top: 15px;

    .el-radio-group {
      height: 100%;
      // height: 49px;
      display: flex;
      overflow-x: auto;

      .el-radio-button {
        height: 100%;

        &.is-active {
          border: 1px sloid gray;
        }
      }

      .el-radio-button__inner {
        border: none !important;
        height: 100%;
      }
    }
  }

  .my-basebox {
    ::v-deep {
      .base-image-item {
        margin-bottom: 0;

        .base-image-item-disabled {
          display: none;
        }
      }
    }
  }

  .header {
    width: 100%;
    height: 50px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 0 10px 10px 0;
    padding: 0 20px 10px;
    margin: 0 -20px;
  }

  .chain-line {
    width: 100%;
    height: 1px;
    border-top: 1px dashed #000;
    margin: 20px 0;
  }

  .table-border-style {
    border-top: 1px solid #e9ebf0;
    border-left: 1px solid #e9ebf0;
    border-right: 1px solid #e9ebf0;
  }

  .fees-at-box {
    margin-top: 23px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .fees-left-at {
      height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }

    .fees-right-at {
      height: 29px;
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: right;
      font-weight: 600;
      display: flex;
      align-items: center;

      & span {
        font-size: 22px;
      }
    }
  }

  .fromHeader {
    width: 100%;
    font-size: 16px;
    font-family: SourceHanSansSC-bold;
    background: #fff;
    color: rgba(16, 16, 16, 100);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .fromLeft {
    display: flex;
    align-items: center;
    width: 60%;

    .fromLeft-title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      display: flex;
      align-items: center;

      .long-string {
        display: inline-block;
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        margin: 0 8px;
      }

      .interest-rate {
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: left;
      }
    }
  }

  .i-active {
    transform: rotate(0deg) !important;
  }

  .occupied-box {
    height: 104px;
    width: 1px;
  }

  .footer-container {
    position: fixed;
    z-index: 1;
    height: 68px;
    width: calc(100% - 267px);
    display: flex;
    justify-content: center;
    align-items: center;
    bottom: 0;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    & > * {
      margin-right: 18px;

      &:last-child {
        margin-right: 0;
      }
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      padding: 0;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }

    .btn-bg-color-blue {
      background-color: #579eff;
    }
  }

  .descriptions-for-box {
    margin-top: 15px;
    position: relative;
    z-index: 1;
    ::v-deep {
      .el-descriptions-item__label.is-bordered-label {
        width: 13.5%;
        height: 48px;
        line-height: 20px;
        background-color: rgba(247, 247, 247, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: left;
        color: #000;
        padding-left: 15px;
      }
      .el-descriptions-item__content {
        width: 360px;
        height: 48px;
        line-height: 20px;
        background-color: rgba(255, 255, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: left;
        color: #000;
        padding-left: 15px;
      }
    }
  }

  // 更改折叠组件样式
  ::v-deep i.el-icon-caret-bottom {
    font-size: 150%;
    transform: rotate(-90deg);
    transition: transform 0.4s;
  }
  ::v-deep i.el-collapse-item__arrow {
    display: none;
  }
  ::v-deep div.el-collapse-item__header {
    height: 15px;
    border-bottom: none;
  }
  ::v-deep div.el-collapse-item__content {
    padding-bottom: 0 !important;
  }
  ::v-deep .el-collapse {
    border-top: none;
    border-bottom: none;
  }
  ::v-deep .el-card {
    border-radius: 8px;
    .el-collapse-item__wrap {
      border-bottom: none;
    }
  }
  // 更改表格组件样式
  ::v-deep {
    .table-top {
      .table-title-box {
        height: 22px;
        // margin-top: 23px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-left-box {
          display: flex;
          align-items: center;

          & > span:first-child {
            color: rgba(125, 125, 125, 100);
            font-size: 14px;
            text-align: left;
            font-weight: 600;
          }
          & > span:nth-child(2) {
            display: inline-block;
            width: 1px;
            height: 16px;
            line-height: 20px;
            background-color: rgba(215, 215, 215, 100);
            text-align: center;
            margin: 0 8px;
          }
          & > span:nth-child(3) {
            height: 22px;
            color: rgba(125, 125, 125, 100);
            font-size: 14px;
            text-align: left;
          }
          .change-trend {
            height: 22px;
            color: #605e5e;
            font-size: 14px;
            transform: translateX(-11px);
          }
        }

        .title-right-box {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: right;
        }
      }
      .contract-no {
        color: #697cff;
      }
      .tag-box {
        height: 30px;
        padding: 3px 6px;
        box-sizing: border-box;
        border-radius: 4px;
        color: rgba(16, 16, 16, 100);
        font-size: 13px;
        text-align: center;
        border: 1px solid rgba(187, 187, 187, 100);
      }
      .el-table th.el-table__cell {
        background-color: #fff1f1;
      }
      .el-table th.el-table__cell > .cell {
        color: rgba(0, 0, 0, 100);
        font-size: 14px;
      }
      .el-table__footer-wrapper tbody td.el-table__cell {
        font-size: 20px;
        background-color: #f7f7f7;
        font-weight: 600;
      }
      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background-color: transparent;
      }
    }

    .el-input-group__append {
      color: #000;
    }
  }
}
</style>
