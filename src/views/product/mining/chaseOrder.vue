<template>
  <div class="chaseOrder-box">
    <!-- 申请产品 -->
    <basic-container>
      <div class="boxs">
        <div class="boxs-to-apply-for-product">
          <h1 class="boxs-to-apply-for-product-h1">
            <span>融资需求</span>
          </h1>
          <div class="boxs-to-apply-for-product-title">
            <div class="boxs-to-apply-for-product-left-logo-box">
              <div class="boxs-to-apply-for-product-left-logo-box-img">
                <img
                  :src="goodsDetail.capitalAvatar"
                  alt
                />
              </div>
              <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                <span @click="viewGoods()">{{ goodsDetail.goodsName }}</span>
                <span>{{ goodsDetail.capitalName }}</span>
              </div>
            </div>
            <span class="boxs-to-apply-for-product-right-goodtype">应收账款质押</span>
          </div>
          <div class="descriptions-for-box">
            <el-descriptions
              title=""
              :column="2"
              border
            >
              <el-descriptions-item label="融资金额">{{
                $numChuFun(goodsDetail.amount || 0, 10000) | formatMoney
                }}万元</el-descriptions-item>
              <el-descriptions-item label="借款期限">{{ goodsDetail.loadTerm || 0
                }}{{
                  goodsDetail.loadTermUnit === 1 ? '天' : '个月'
                }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>
    </basic-container>

    <!-- 代采订单 -->
    <basic-container>
      <el-collapse
        v-model="activeNames1"
        @change="handleChange1"
      >
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change1Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>{{ goodsType == 4 ? '动产质押' : '代采订单' }}</span>
                  <span class="long-string" />
                  <span class="fromLeft-title-user">供应商:&nbsp;&nbsp;</span>
                  <span class="fromLeft-title-name">{{
                    goodsDetail.supplierName
                  }}</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top">
            <TableDialog
              page="chaseOrder"
              :column-options="columnOptions"
              :table-data="goodsDetail.tableData"
            ></TableDialog>
          </div>
          <div class="descriptions-for-box">
            <el-descriptions
              title=""
              :column="3"
              border
            >
              <el-descriptions-item label="提货方式">{{
                goodsDetail.pickUpManners
              }}</el-descriptions-item>
              <el-descriptions-item label="收货对象">{{
                goodsDetail.receiveCompanyName
              }}</el-descriptions-item>
              <el-descriptions-item label="联系地址">{{
                goodsDetail.receiveAddress
              }}</el-descriptions-item>
              <el-descriptions-item label="联系人">{{
                goodsDetail.receiveName
              }}</el-descriptions-item>
              <el-descriptions-item label="联系方式">{{
                goodsDetail.receiveNumber
              }}</el-descriptions-item>
              <el-descriptions-item label="交付时间">{{
                goodsDetail.deliverTime
              }}</el-descriptions-item>
              <el-descriptions-item label="收货标准">{{
                goodsDetail.receiptStandard
              }}</el-descriptions-item>
              <el-descriptions-item label="代收代付类型">{{
                getPaymentText(goodsDetail.paymentType)
              }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{
                goodsDetail.remark || '--'
              }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>

    <!-- 还款试算 -->
    <basic-container>
      <el-collapse
        v-model="activeNames2"
        @change="handleChange2"
      >
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change2Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>还款试算</span>
                  <!-- <span class="long-string" />
                  <span class="interest-rate"
                    >日利率{{
                      goodsDetail.allDailyInterestRate
                    }}%&nbsp;&nbsp;(年化利率{{
                      goodsDetail.allAnnualInterestRate
                    }}%)</span
                  > -->
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top refund">
            <div class="table-title-box">
              <div class="title-left-box">
                <span>资方费用</span>
                <span />
                <span>日利率{{ goodsDetail.dailyInterestRate }}%&nbsp;(年化利率{{
                  goodsDetail.annualInterestRate
                  }}%)</span>
              </div>
              <div class="title-right-box">计费方式: 随借随还</div>
            </div>
            <el-table
              ref="table2"
              :data="tableData2"
              style="width: 100%; margin-top: 13px"
              class="table-border-style"
              :row-class-name="tableRowClassName"
              :summary-method="getSummaries2"
              show-summary
            >
              <!-- <el-table-column
                prop="term"
                label="期数"
                width="110"
                align="center"
              >
              </el-table-column> -->
              <el-table-column
                prop="refundTime"
                label="还款日期"
              >
              </el-table-column>
              <el-table-column
                prop="monthlySupply"
                label="应还总额"
              >
                <template slot-scope="scope">
                  <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="monthlyPrincipal"
                label="还款本金"
              >
                <template slot-scope="scope">
                  <span>￥{{ scope.row.monthlyPrincipal | formatMoney }} </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="planInterest"
                label="应还利息"
              >
                <template slot-scope="scope">
                  <span>￥{{ scope.row.planInterest | formatMoney }} </span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="chain-line" />

          <div class="table-top refund">
            <div
              class="table-title-box"
              style="margin-top: -10px"
            >
              <div class="title-left-box">
                <span>平台费用</span>
              </div>
            </div>
            <el-table
              ref="table3"
              :data="tableData3"
              :summary-method="getSummaries"
              show-summary
              style="width: 100%; margin-top: 13px"
              class="table-border-style"
            >
              <el-table-column
                prop="expenseTypeStr"
                label="费用名称"
              > </el-table-column>
              <!-- <el-table-column prop="expenseTypeStr" label="费用类型">
                <template slot-scope="scope">
                  <span class="border-box">
                    {{ scope.row.expenseTypeStr }}
                  </span>
                </template>
              </el-table-column> -->
              <el-table-column
                prop="repaymentTerm"
                label="期数"
              >
              </el-table-column>
              <el-table-column
                prop="feeNodeStr"
                label="计算节点"
              >
                <template slot-scope="scope">
                  <span class="border-box">
                    {{ scope.row.feeNodeStr }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="collectFeesNodeStr"
                label="收费节点"
              >
                <template slot-scope="scope">
                  <span class="border-box">
                    {{ scope.row.collectFeesNodeStr }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="feeFormulaNames"
                label="计费方式"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.feeFormulaName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="amount"
                label="应付金额"
              >
                <template slot-scope="scope">
                  <span>￥{{ scope.row.amount | formatMoney }} </span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="chain-line" />

          <div class="fees-at-box">
            <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
            <div class="fees-right-at">
              应还总额：
              <span> ￥{{ sum | formatMoney }}</span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>

    <!-- 保证金 -->
    <basic-container>
      <el-collapse
        v-model="activeNames3"
        @change="handleChange3"
      >
        <el-collapse-item name="furtherInformation">
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change3Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>保证金</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top refund">
            <el-table
              ref="table4"
              :data="tableData4"
              style="width: 100%; margin-top: 13px"
              class="table-border-style"
              :summary-method="getSummaries"
              show-summary
            >
              <el-table-column
                prop="cashDepositTypes"
                label="保证金类型"
              >
                <template slot-scope="scope">
                  <span
                    class="border-box"
                    v-if="scope.row.cashDepositType == 1"
                  >初始保证金</span>
                  <span
                    class="border-box"
                    v-else-if="scope.row.cashDepositType == 2"
                  >追加保证金</span>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="payNodes" label="支付节点">
                <template slot-scope="scope">
                  <span class="border-box">{{
                    scope.row.payNode === 1 ? '放款申请' : ''
                  }}</span>
                </template>
              </el-table-column> -->
              <el-table-column
                prop="cashDepositRates"
                label="计算方式"
              >
                <template slot-scope="scope">
                  <span>融资金额*保证金比例({{ scope.row.cashDepositRate }}%)</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="refundTypes"
                label="退还方式"
              ><template slot-scope="scope">
                  <span>{{
                    scope.row.refundType === 1 ? '同还款比例等比释放' : '信贷结清后退还'
                  }}</span>
                </template></el-table-column>
              <el-table-column
                prop="payableAmount"
                label="应缴金额(元)"
              >
                <template slot-scope="scope">
                  <span>￥{{ scope.row.payableAmount | formatMoney }} </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'
import TableDialog from './Dialog/tableDialog'
import { getDictionary } from '@/api/system/dictbiz'
import {
  getPurchaseBondData,
  getFinanceApplyPlan,
} from '@/api/purchase/purchaseplan'
const columnOptions = [
  {
    label: '#',
    prop: 'index',
    width: '80',
  },
  {
    label: '货品信息',
    prop: 'goodInfo',
    width: '300',
  },
  {
    label: '发布单价(元)',
    prop: 'unitPrice',
  },
  {
    label: '待入库数量(台)',
    prop: 'quantity',
  },
  {
    label: '采购单价(元)',
    prop: 'purchasePrice',
  },
  {
    label: '融资单价(元)',
    prop: 'financingPrice',
  },
  {
    label: '采购总额(元)',
    prop: 'purchaseTotal',
  },
  {
    label: '融资总额(元)',
    prop: 'financingTotal',
  },
]

export default {
  props: {
    goodsType: {
      type: Number,
      default: 2
    },
    goodsDetail: {
      type: Object,
      default: () => { },
    },
    sum: {
      type: [Number, String],
      default: 0,
    },
    tableData2: {
      type: Array,
      default: () => [],
    },
    tableData3: {
      type: Array,
      default: () => { },
    },
  },
  mounted() {
    this.getStatusList()
  },
  watch: {
    goodsDetail: {
      handler(val) {
        if (val) {
          this.tableData = val.tableData
          this.getPurchaseBondData({ financeNo: val.financeNo })
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: { TableDialog },
  data() {
    return {
      activeNames1: [],
      activeNames2: [],
      activeNames3: [],
      change1Type: false,
      change2Type: false,
      change3Type: false,
      tableData: [],
      tableData4: [],
      columnOptions,
      collectTypeOption: [], // 代收代付类型
    }
  },
  methods: {
    getStatusList() {
      let resList = []
      getDictionary({ code: 'goods_bank_card_collection_type' }).then(
        ({ data }) => {
          if (data.code === 200 && data.data) {
            for (const item of data.data) {
              resList.push({
                value: Number(item.dictKey),
                label: item.dictValue,
                id: item.id,
              })
            }
          }
          this.collectTypeOption = resList
        }
      )
    },
    getPaymentText(type) {
      let obj = this.collectTypeOption.find(item => item.value === Number(type))
      return obj ? obj.label : ''
    },

    // 获取保证金数据
    getPurchaseBondData(params) {
      getPurchaseBondData(params).then(({ data }) => {
        let list = []
        if (data.code === 200 && data.data) {
          list.push(data.data)
        }
        this.tableData4 = list
      })
    },

    handleChange1() {
      // 代采订单折叠面板收缩控制
      this.change1Type = !this.change1Type
    },
    handleChange2() {
      // 还款试算折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
    handleChange3() {
      // 保证金折叠面板收缩控制
      this.change3Type = !this.change3Type
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    getSummaries2(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    tableRowClassName({ row }) {
      if (!row.refundTime) {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      this.$router.push({
        path: '/pcontrol/pinformation',
        query: { id: this.goodsDetail.goodsId },
      })
      sessionStorage.setItem('look', 'true')
    },
  },
}
</script>

<style lang="scss" scoped>
.chaseOrder-box {
  margin-top: 10px;

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 157px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      >* {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
      }

      .status {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-family: SourceHanSansSC-bold;
        font-weight: blod;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .tabBar-box {
    margin-top: 20px;
    display: flex;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;

      .tabBar-children {
        font-size: 14px;
        color: #449bfc;
      }

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }

    .active-box {
      background-color: #449bfc;

      .tabBar-children {
        color: #fff;
      }

      &:hover {
        background-color: #449bfc;
      }
    }
  }
}

::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}

.cloud-detail-title {
  line-height: 21px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 12px;
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    >div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  display: inline-block;
  color: #00072a;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }

    .fromLeft-title-user {
      line-height: 22px;
      color: #7d7d7d;
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .fromLeft-title-name {
      color: #697cff !important;
      line-height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        &>span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        &>span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo-box {
        margin-top: 17px;
        display: flex;
        align-items: center;

        .boxs-to-apply-for-product-left-logo-box-img {
          width: 48px;
          height: 48px;
          border-radius: 40px;
          border: 1px solid rgba(234, 234, 234, 100);
          overflow: hidden;
          margin-right: 7px;

          &>img {
            width: 100%;
            object-fit: cover;
          }
        }

        .boxs-to-apply-for-product-left-logo-box-goodname {
          display: flex;
          flex-direction: column;

          &>span:first-child {
            display: block;
            height: 24px;
            color: rgba(18, 119, 255, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
          }

          &>span:last-child {
            display: block;
            height: 18px;
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }

      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover>td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      min-width: 135px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

.descriptions-for-box2 {
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 200px;
      min-width: 200px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      // width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}

::v-deep i.el-collapse-item__arrow {
  display: none;
}

::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}

::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}

::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}

::v-deep .el-card {
  border-radius: 8px;

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .table-title {
      line-height: 21px;
      color: #7d7d7d;
      font-size: 14px;
      font-weight: 400;
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell>.cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
