<template>
  <div class="applicationInformation-box">
    <!-- 待入库 -->
    <basic-container>
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': changeType,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>待入库</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top">
            <TableDialog
              page="goodsTable1"
              :column-options="columnOptions"
              :table-data="tableData"
            ></TableDialog>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
    <!-- 库存 -->
    <basic-container>
      <el-collapse v-model="activeNames2" @change="handleChange2">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change2Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>库存</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top">
            <TableDialog
              page="goodsTable2"
              :column-options="columnOptions2"
              :table-data="tableData2"
            ></TableDialog>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
    <!-- 赎货单 -->
    <basic-container>
      <el-collapse v-model="activeNames3" @change="handleChange3">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change3Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>赎货单</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top">
            <TableDialog
              page="goodsTable3"
              :column-options="columnOptions3"
              :table-data="tableData3"
            ></TableDialog>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
  </div>
</template>

<script>
import {
  getGoodsWaitWarehouse,
  getGoodswarehouseDetails,
  getCargoList,
} from '@/api/purchase/purchaseplan'
import TableDialog from './Dialog/tableDialog'
const columnOptions = [
  {
    label: '#',
    prop: 'index',
    width: '80',
  },
  {
    label: '货品信息',
    prop: 'goodInfo',
    width: '300',
  },
  {
    label: '供应商',
    prop: 'supplierName',
    width: '200',
  },
  {
    label: '单位',
    prop: 'goodsUnitValue',
    width: '80',
  },
  {
    label: '待入库数量',
    prop: 'readyToStorage',
    width: '120',
  },
  {
    label: '采购单价(元)',
    prop: 'unitPrice',
  },
  {
    label: '融资单价(元)',
    prop: 'financingPrice',
  },
  {
    label: '最迟交付日',
    prop: 'latestDelivery',
    width: '100',
  },
  {
    label: '逾期天数(天)',
    prop: 'overDueDay',
    width: '130',
  },
]

const columnOptions2 = [
  {
    label: '#',
    prop: 'index',
    width: '80',
  },
  {
    label: '库存编号',
    prop: 'warehouseNo',
    width: '160',
  },
  {
    label: '货品信息',
    prop: 'goodInfo',
    width: '300',
  },
  {
    label: '仓库信息',
    prop: 'storageMsg',
    width: '220',
  },
  {
    label: '供应商',
    prop: 'supplierName',
    width: '220',
  },
  {
    label: '单位',
    prop: 'goodsUnitValue',
    width: '100',
  },
  {
    label: '采购单价(元)',
    prop: 'unitPrice',
    width: '180',
  },
  {
    label: '融资单价(元)',
    prop: 'financingPrice',
    width: '180',
  },
  {
    label: '入库数量',
    prop: 'warehouseNum',
    width: '120',
  },
  {
    label: '在途数量',
    prop: 'warehouseRedemptionNum',
    width: '120',
  },
  {
    label: '库存数量',
    prop: 'warehouseNum',
    width: '120',
  },
  {
    label: '入库时间',
    prop: 'warehouseInDate',
    width: '160',
  },
  {
    label: '库龄',
    prop: 'inventoryAge',
    width: '120',
  },
  {
    label: '上次更新时间',
    prop: 'updateTime',
    width: '160',
  },
  {
    label: '仓管员',
    prop: 'warehouseManager',
    width: '120',
  },
  {
    label: '操作',
    prop: 'action',
    width: '140',
  },
]
const columnOptions3 = [
  {
    label: '#',
    prop: 'index',
    width: '80',
  },
  {
    label: '赎货编号',
    prop: 'redeemNo',
  },
  {
    label: '货品信息',
    prop: 'goodInfo',
    width: '300',
  },
  {
    label: '赎货数量',
    prop: 'num',
  },
  {
    label: '融资单价(元)',
    prop: 'financingPrice',
  },
  {
    label: '融资总额(元)',
    prop: 'financingTotal',
  },
  {
    label: '创建时间',
    prop: 'createTime',
  },
  {
    label: '赎货状态',
    prop: 'status',
  },
]

export default {
  props: {
    financeNo: {
      type: String,
      default: '',
    },
  },
  watch: {
    financeNo: {
      handler(val) {
        this.getGoodsWaitWarehouse({ financeNo: val })
        this.getGoodswarehouseDetails({ financingCode: val })
        this.getCargoList({ financingNo: val })
      },
      immediate: true,
    },
  },
  components: { TableDialog },
  data() {
    return {
      activeNames: [],
      activeNames2: [],
      activeNames3: [],
      changeType: false,
      change2Type: false,
      change3Type: false,
      tableData: [],
      tableData2: [],
      tableData3: [],
      columnOptions,
      columnOptions2,
      columnOptions3,
    }
  },
  methods: {
    // 待入库
    getGoodsWaitWarehouse(params) {
      let list = []
      getGoodsWaitWarehouse(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          data.data.forEach((item, index) => {
            list.push({
              ...item,
              commodityId: item.goodsId,
              index: index + 1,
              commodityUrl: item.logo,
              name: item.goodsName,
              spec: item.goodsSpec,
            })
          })
        }
        this.tableData = list
      })
    },
    // 库存明细
    getGoodswarehouseDetails(params) {
      let list = []
      getGoodswarehouseDetails(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          data.data.forEach((item, index) => {
            list.push({
              ...item,
              commodityId: item.goodsId,
              index: index + 1,
              commodityUrl: item.logo,
              name: item.goodsName,
              spec: item.goodsSpec,
            })
          })
        }
        this.tableData2 = list
      })
    },
    // 赎货单
    getCargoList(params) {
      let list = []
      getCargoList(params).then(({ data }) => {
        if (data.code === 200) {
          data.data.records.forEach((item, index) => {
            list.push({
              ...item,
              commodityId: item.goodsId,
              index: index + 1,
              commodityUrl: item.goodsUrl,
              spec: item.goodsSpec,
              name: item.goodsName,
              financingTotal: item.financingPriceSum,
            })
          })
        }
        this.tableData3 = list
      })
    },
    handleChange() {
      // 还款试算折叠面板收缩控制
      this.changeType = !this.changeType
    },
    handleChange2() {
      // 还款试算折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
    handleChange3() {
      // 还款试算折叠面板收缩控制
      this.change3Type = !this.change3Type
    },
    getStateColor(state) {
      let color = ''
      switch (state) {
        case 0:
          color = 'state-blue'
          break
        case 1:
          color = 'state-blue'
          break
        case 2:
          color = 'state-green'
          break
      }
      return color
    },
  },
}
</script>

<style lang="scss" scoped>
.applicationInformation-box {
  margin-top: 10px;

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 157px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
      }

      .status {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-family: SourceHanSansSC-bold;
        font-weight: blod;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .tabBar-box {
    margin-top: 20px;
    display: flex;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;

      .tabBar-children {
        font-size: 14px;
        color: #449bfc;
      }

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }
    .active-box {
      background-color: #449bfc;

      .tabBar-children {
        color: #fff;
      }

      &:hover {
        background-color: #449bfc;
      }
    }
  }
}

.init-state {
  display: inline-block;
  line-height: 30px;
  text-align: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}
.state-gray {
  color: #7d7d7d;
}
.state-blue {
  color: #697cff;
}

.state-green {
  color: #3dc861;
}
.state-red {
  color: #fb3030;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .table-title {
      line-height: 21px;
      color: #7d7d7d;
      font-size: 14px;
      font-weight: 400;
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
    .text-blue {
      color: #697cff;
    }
    .fee-list {
      display: flex;
      align-items: center;
      flex-flow: row nowrap;
    }
    .fee-item {
      display: inline-block;
      color: #00072a;
      background-color: #e9ebf0;
      border-radius: 100px;
      font-size: 14px;
      padding: 3px 12px;
      box-sizing: border-box;
    }
    .fee-item + .fee-item {
      margin-left: 4px;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
