<template>
  <div class="repaymentInformation-box">
    <basic-container>
      <div class="repayment-title">
        <div class="repayment-name">还款信息</div>
        <div class="repayment-code">
          <span
            >计划还款单号:
            {{
              borrowingInformationData[0]
                ? borrowingInformationData[0].iouNo
                : '暂无数据'
            }}
          </span>
          <span></span>
        </div>
      </div>
      <div class="table-top refund">
        <el-table
          ref="table"
          :data="borrowingInformationData"
          style="width: 100%; margin-top: 13px"
          class="table-border-style"
          :row-class-name="tableRowClassName"
        >
          <el-table-column type="index" label="#" width="80" align="center">
          </el-table-column>
          <el-table-column prop="repaymentNo" label="还款单号">
            <template slot-scope="scope">
              <span class="text-blue">{{ scope.row.repaymentNo }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="repaymentTime" label="还款时间">
          </el-table-column>
          <el-table-column prop="totalAmount" label="应还金额">
            <template slot-scope="scope">
              <span>￥{{ scope.row.totalAmount | formatMoney }} </span>
            </template>
          </el-table-column>
          <el-table-column prop="actualAmount" label="实还金额">
            <template slot-scope="scope">
              <span>{{ scope.row.actualAmount | formatMoney }} </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="repaymentType"
            label="还款类型"
          ></el-table-column>
          <el-table-column
            prop="status"
            label="状态"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span
                class="status-box"
                :style="{
                  'border-color': scope.row.status.color,
                  color: scope.row.status.color,
                }"
              >
                {{ scope.row.status.text }}
              </span>
              <!-- <div style="text-align: center"> -->
              <!-- <span class="init-state" :class="getStateColor(scope.row.status)"
                >支付</span
              > -->
              <!-- </div> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
    </basic-container>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'
import { listByFinanceApplyId } from '@/api/finance/financeapply'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'

export default {
  data() {
    return {
      // tableData: [],
      // 借款信息数据
      borrowingInformationData: [],
    }
  },
  mounted() {
    this.borrowingInformationFun()
  },
  methods: {
    tableRowClassName({ row }) {
      if (!row.refundTime) {
        return 'aggregate-row'
      }
      return ''
    },
    getStateColor(state) {
      let color = ''
      switch (state) {
        case 0:
          color = 'state-gray'
          break
        case 1:
          color = 'state-blue'
          break
        case 2:
          color = 'state-green'
          break
        case 3:
          color = 'state-red'
          break
      }
      return color
    },
    // 借款状态对应颜色文字
    borrowingStatus(state) {
      const obj = {}
      switch (state) {
        case 1:
          obj.text = '未支付'
          obj.color = '#a6aebc'
          break
        case 2:
          obj.text = '支付中'
          obj.color = '#0d55cf'
          break
        case 3:
          obj.text = '已支付'
          obj.color = '#5DD17B'
          break
        case 4:
          obj.text = '支付失败'
          obj.color = '#f03d3d'
          break
        case 5:
          obj.text = '已重提'
          obj.color = '#0d55cf'
          break
        case 6:
          obj.text = '已撤销'
          obj.color = '#a6aebc'
          break
        case 7:
          obj.text = '已失效'
          obj.color = '#a6aebc'
          break
      }
      return obj
    },
    borrowingInformationFun() {
      getDictionary('loan_repayment_type').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            resList.push({
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            })
          }
          //
          const params = {
            financeApplyId: this.$route.query.id,
          }
          listByFinanceApplyId(params).then(({ data }) => {
            const { data: resData } = data
            if (data.success) {
              const arr = []
              for (const item of resData) {
                // 过滤出当前的还款类型
                const repaymentFilter = resList.filter(
                  itemed => itemed.key == item.repaymentType
                )
                arr.push({
                  id: item.id,
                  repaymentNo: item.repaymentNo,
                  repaymentTime: item.repaymentTime,
                  totalAmount: item.totalAmount,
                  actualAmount: item.actualAmount,
                  repaymentType: repaymentFilter[0]
                    ? repaymentFilter[0].value
                    : '..',
                  status: this.borrowingStatus(item.status),
                  iouNo: item.iouNo,
                })
              }
              this.borrowingInformationData = arr
            }
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.repaymentInformation-box {
  margin-top: 10px;
  .repayment-title {
    display: flex;
    align-items: center;

    .repayment-name {
      padding-right: 8px;
      border-right: 1px solid #d7d7d7;
      line-height: 22px;
      color: rgab(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
    }
    .repayment-code {
      display: flex;
      align-items: center;
      margin-left: 8px;
      & span {
        display: block;
        line-height: 22px;
        font-size: 14px;
        font-weight: 500;
      }
      & span:first-child {
        color: #7d7d7d;
        margin-right: 6px;
      }
      & span:last-child {
        color: #697cff;
      }
    }
  }
}
.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}
.init-state {
  display: inline-block;
  width: 76px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #d7d7d7;
}
.state-gray {
  border-color: #d7d7d7;
  color: #7d7d7d;
}
.state-blue {
  border-color: #697cff;
  color: #697cff;
}

.state-green {
  border-color: #3dc861;
  color: #3dc861;
}
.state-red {
  border-color: #fb3030;
  color: #fb3030;
}

// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
    .text-blue {
      color: #697cff;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
