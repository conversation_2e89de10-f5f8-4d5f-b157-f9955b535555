<template>
  <div class="applicationInformation-box">
    <!-- 补充资料 -->
    <basic-container>
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': changeType,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>合同信息</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="table-top refund">
            <el-table
              ref="table"
              :data="tableData"
              style="width: 100%; margin-top: 13px"
              class="table-border-style"
              :row-class-name="tableRowClassName"
            >
              <el-table-column
                prop="index"
                label="序号"
                width="110"
                align="center"
              ></el-table-column>
              <el-table-column prop="invoiceType" label="合同编号">
              </el-table-column>
              <el-table-column prop="invoiceCode" label="合同标题">
              </el-table-column>
              <el-table-column
                prop="invoiceNum"
                label="创建时间"
              ></el-table-column>
              <el-table-column
                prop="startDay"
                label="签署状态"
                align="center"
                width="120"
              >
                <template slot-scope="scope">
                  <!-- <div style="text-align: center"> -->
                  <span
                    class="init-state"
                    :class="getStateColor(scope.row.status)"
                    >支付</span
                  >
                  <!-- </div> -->
                </template>
              </el-table-column>
              <el-table-column prop="action" label="操作">
                <template slot-scope="scope">
                  <el-button type="text" class="btn-name">详情</el-button>
                  <el-button type="text" class="btn-name">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
  </div>
</template>

<script>
export default {
  props: {},
  watch: {
    activeNames() {
      setTimeout(() => {
        this.$refs.table.$ready = false
      }, 50)
    },
  },
  components: {},
  data() {
    return {
      activeNames: [],
      changeType: false,
      tableData: [],
    }
  },
  methods: {
    handleChange() {
      // 还款试算折叠面板收缩控制
      this.changeType = !this.changeType
    },
    getStateColor(state) {
      let color = ''
      switch (state) {
        case 0:
          color = 'state-gray'
          break
        case 1:
          color = 'state-blue'
          break
        case 2:
          color = 'state-green'
          break
        case 3:
          color = 'state-red'
          break
      }
      return color
    },
  },
}
</script>

<style lang="scss" scoped>
.applicationInformation-box {
  margin-top: 10px;

  .apply-container {
    display: flex;
    flex-wrap: nowrap;

    .left,
    .right {
      width: 100%;
      height: 157px;
      padding: 20px;
      line-height: 20px;
      border-radius: 8px;
      background-color: rgba(246, 246, 246, 100);
      text-align: center;
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 12px;

      .form-item {
        width: 100%;
      }

      .title {
        display: inline-block;
        width: 130px;
        text-align: right;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
      }

      .value {
        display: inline-block;
        width: calc(100% - 130px);
        text-align: left;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      > * {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .right-icon-box {
        display: flex;
        flex-direction: column;
      }

      .status {
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: center;
        font-family: SourceHanSansSC-bold;
        font-weight: blod;
      }

      .desc {
        color: rgba(132, 134, 141, 100);
        font-size: 14px;
        text-align: center;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .tabBar-box {
    margin-top: 20px;
    display: flex;

    .tabBar-for-box {
      height: 40px;
      width: 100%;
      background-color: rgba(255, 255, 255, 100);
      border: 1px solid rgba(105, 124, 255, 100);
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      transition: background-color 0.3s;

      .tabBar-children {
        font-size: 14px;
        color: #449bfc;
      }

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
      }

      &:hover {
        background-color: #697cff2b;
      }
    }
    .active-box {
      background-color: #449bfc;

      .tabBar-children {
        color: #fff;
      }

      &:hover {
        background-color: #449bfc;
      }
    }
  }
}

.init-state {
  display: inline-block;
  width: 76px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #d7d7d7;
}
.state-gray {
  border-color: #d7d7d7;
  color: #7d7d7d;
}
.state-blue {
  border-color: #697cff;
  color: #697cff;
}

.state-green {
  border-color: #3dc861;
  color: #3dc861;
}
.state-red {
  border-color: #fb3030;
  color: #fb3030;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .table-title {
      line-height: 21px;
      color: #7d7d7d;
      font-size: 14px;
      font-weight: 400;
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
