<template>
  <div class="good-bond-box">
    <div class="bond-card-list">
      <div class="bond-card-item">
        <span>{{ (allAmountConfig.payableAmount || 0) | formatMoney }}</span>
        <span>缴纳总额(元)</span>
      </div>
      <div class="bond-card-item">
        <span>{{ (allAmountConfig.balance || 0) | formatMoney }}</span>
        <span>保证金余额(元)</span>
      </div>
      <div class="bond-card-item">
        <span>{{ (allAmountConfig.refundAmount || 0) | formatMoney }}</span>
        <span>退还金额(元)</span>
      </div>
      <div class="bond-card-item">
        <span>{{ (allAmountConfig.paymentAmount || 0) | formatMoney }}</span>
        <span>待缴金额(元)</span>
      </div>
    </div>
    <basic-container>
      <div class="bond-title">
        <div class="bond-name">保证金账单</div>
      </div>
      <div class="table-top refund">
        <el-table
          ref="table"
          :data="tableData"
          style="width: 100%; margin-top: 13px"
          class="table-border-style"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            prop="index"
            label="#"
            width="110"
            align="center"
          ></el-table-column>
          <el-table-column prop="createTime" label="时间"> </el-table-column>
          <el-table-column prop="transactionNo" label="交易流水号">
          </el-table-column>
          <el-table-column prop="type" label="缴/退类型">
            <template slot-scope="scope">
              <span class="border-box">{{
                scope.row.payRefundType === 1
                  ? '缴纳'
                  : scope.row.payRefundType === 0
                  ? '退还'
                  : ''
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额">
            <template slot-scope="scope">
              <span class="border-amount" :class="getAmountStatus(scope.row)"
                >{{
                  scope.row.payRefundType === 1
                    ? '+'
                    : scope.row.payRefundType === 0
                    ? '-'
                    : ''
                }}{{ scope.row.amount | formatMoney }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="cashDepositBillType" label="保证金类型">
            <template slot-scope="scope">
              <span class="border-box">{{
                scope.row.cashDepositBillType === 1
                  ? '初始保证金'
                  : scope.row.cashDepositBillType === 2
                  ? '追加保证金'
                  : ''
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payCompanyAccount" label="付款账户">
          </el-table-column>
          <el-table-column
            prop="proceedsCompanyAccount"
            label="收款账户"
          ></el-table-column>
          <el-table-column prop="remark" label="备注"></el-table-column>
        </el-table>
      </div>
    </basic-container>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'
import {
  getBondAmountList,
  getPurchaseBondData,
  getBondOrderList,
} from '@/api/purchase/purchaseplan'

export default {
  props: {
    financeNo: {
      type: String,
      default: '',
    },
  },
  watch: {
    financeNo: {
      handler(val) {
        if (val) {
          this.getBondAmountList({ financeNo: val })
          this.getPurchaseBondData({ financeNo: val })
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      tableData: [],
      allAmountConfig: {},
    }
  },
  methods: {
    // 获取保证金的余额
    getBondAmountList(params) {
      getBondAmountList(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          this.allAmountConfig = { ...data.data }
        }
      })
    },
    // 获取保证金信息
    getPurchaseBondData(params) {
      getPurchaseBondData(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          const { id } = data.data
          if (id) {
            this.getBondOrderList({ id })
          }
        }
      })
    },

    // 获取保证金账单
    getBondOrderList(params) {
      let list = []
      getBondOrderList(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          data.data.forEach((item, index) => {
            list.push({
              ...item,
              index: index + 1,
            })
          })
        }
        this.tableData = list
      })
    },

    // 金额是正还是负
    getAmountStatus({ payRefundType }) {
      let textColor = ''
      switch (payRefundType) {
        case 0:
          textColor = 'text-red'
          break
        case 1:
          textColor = 'text-green'
          break
      }
      return textColor
    },
    tableRowClassName({ row }) {
      if (!row.refundTime) {
        return 'aggregate-row'
      }
      return ''
    },
  },
}
</script>

<style lang="scss" scoped>
.good-bond-box {
  margin-top: 10px;
  .bond-card-list {
    display: flex;
    align-items: center;
    flex-flow: row nowrap;
    padding: 0 6px;
    margin-bottom: 10px;
    .bond-card-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      background-color: #fff;
      border-radius: 6px;
      padding: 22px 0 23px;
      & span:first-child {
        line-height: 23px;
        color: rgba(18, 119, 255, 100);
        margin-bottom: 12px;
        font-size: 20px;
        font-weight: 600;
        text-align: center;
        font-family: SourceHanSansSC-bold;
      }
      & span:last-child {
        line-height: 20px;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        font-family: SourceHanSansSC-regular;
      }
    }
    .bond-card-item + .bond-card-item {
      margin-left: 25px;
    }
  }
  .bond-title {
    display: flex;
    align-items: center;

    .bond-name {
      line-height: 22px;
      color: rgab(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
    }
  }
}
.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}
.border-amount {
  line-height: 20px;
  font-size: 14px;
  text-align: right;
  font-family: Roboto;
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
    .text-blue {
      color: #697cff;
    }
    .text-red {
      color: #ff2929;
    }
    .text-green {
      color: #1fc374;
    }
    .border-box {
      display: inline-block;
      color: #00072a;
      background-color: #e9ebf0;
      border-radius: 100px;
      font-size: 14px;
      padding: 3px 12px;
      box-sizing: border-box;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
