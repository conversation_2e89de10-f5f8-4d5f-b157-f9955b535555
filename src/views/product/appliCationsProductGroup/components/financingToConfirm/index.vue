<template>
  <div class="financing-confirm">
    <span class="top-line" />
    <div class="infometion-box">
      <div class="borrowing-balance">融资金额(元)</div>
      <!--  -->
      <div class="borrowing-figures">
        <div class="figures-left">{{ pageData[0].amount }}</div>
        <div class="figures-right" @click="handleExhibition()">
          <span>查看还款计划</span>
          <span>
            <MySvgIcon
              icon-class="icon-youjiantou1"
              style="fill: #0d55cf; font-size: 16px"
            />
          </span>
        </div>
      </div>
      <!--  -->
      <div class="positive-box">
        <div class="positive-for" v-for="item in positveArr" :key="item.id">
          <div class="positive-title">{{ item.title }}</div>
          <div class="positive-info">{{ item.value }}</div>
        </div>
      </div>
      <!--  -->
      <div class="loaninformation-box">
        <div class="loaninformation-title">放款信息</div>
        <div class="loanchildren-box">
          <div
            class="loanchildren-for"
            v-for="item in loanchildrenArr"
            :key="item.id"
          >
            <div class="loanchildren-title">{{ item.title }}</div>
            <div
              class="loanchildren-info"
              v-for="(cItem, index) of item.value"
              :key="index"
            >
              {{ cItem }}
            </div>
          </div>
        </div>
        <n-collapse-transition :show="show">
          <div class="termsOfCredit-box">
            <!-- 1 -->
            <div style="margin-bottom: 40px">
              <div class="termsOfCredit-title">贷款条件</div>
              <p class="termsOfCredit-p">
                该项贷款只能用于生产经营且需提供证明材料（包括但不限于发票、银行流水、采购合同等），若放款一个月内未提供将影响您下一笔支用。
              </p>
              <p class="termsOfCredit-p">禁止用于以下用途：</p>
              <p class="termsOfCredit-p">（一）购房及偿还住房抵押贷款；</p>
              <p class="termsOfCredit-p">
                （二）股票、债券、期货、金融衍生产品和资产管理产品等投资；
              </p>
              <p class="termsOfCredit-p">（三）固定资产、股本权益性投资；</p>
              <p class="termsOfCredit-p">（四）法律法规禁止的其他用途。</p>
            </div>
            <!-- 2 -->
            <div style="margin-bottom: 40px">
              <div class="termsOfCredit-title">逾期清收</div>
              <p class="termsOfCredit-p">
                由{{
                  pageData[2].capitalNameP
                }}，及委托精锐纵横网络技术有限公司对通期客户催收道期贷款本息及其他应付款项。
              </p>
            </div>
            <!-- 3 -->
            <div>
              <div class="termsOfCredit-title">咨询投诉渠道和违约责任</div>
              <p class="termsOfCredit-p">
                精锐纵横供应链客服电话：{{ pageData[1].jrzhPhone }}
              </p>
              <p class="termsOfCredit-p">
                {{ pageData[2].capitalNameP }}客服电话：4000-520-888
              </p>
              <p class="termsOfCredit-p">
                在业务存续期间，按业务各项合同约定若出现违约情况，需由违约方承担相应责任。
              </p>
            </div>
            <!-- 4 -->
          </div>
        </n-collapse-transition>
        <div class="fold-box" @click="show = !show">
          <span>{{ show ? '收起' : '展开' }}</span>
          <span>
            <MySvgIcon
              :icon-class="show ? 'icon-shangjiantou' : 'icon-xiajiantou'"
              style="fill: #0d55cf; font-size: 16px"
            />
          </span>
        </div>
      </div>
      <div class="loaninformation-menu-box" v-if="!finaContractSigningType">
        <span>
          <NButton
            class="blue button-item"
            style="width: 188px; height: 48px"
            :bordered="true"
            round
            @click="lastStep()"
          >
            <span class="desc">上一步</span>
          </NButton>
        </span>
        <span>
          <NButton
            class="blue button-item primary"
            style="width: 188px; height: 48px"
            :bordered="true"
            type="info"
            round
            @click="nextToPape"
          >
            <span class="desc">下一步</span>
          </NButton>
        </span>
      </div>
    </div>
    <!-- 还款计划弹窗 -->
    <DialogReimburSement
      ref="dialogReimburSement"
      :loadTermUnit="loadTermUnit"
      :filterArr="filterArr"
      :tabBarObj="{
        tabBarIndex: 0,
        tabBarIndexVal: void 0,
      }"
      :queryParams="ReimburSementQueryParams"
      text="还款计划"
      isReadOnly
    />
  </div>
</template>

<script>
export default {
  name: 'financingToConfirm',
}
</script>
<script setup>
import DialogReimburSement from '../financingDemand/components/Dialog/dialogReimburSement.vue'
import { NCollapseTransition } from 'naive-ui'
import { ref, reactive, provide, inject, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { formatMoney } from '@/utils/utils'
import { NButton } from 'naive-ui'
import dayjs from 'dayjs'

import { PRODUCT_APPLI_CATIONS_API, PRODUCT_VERIFY_API } from '@/api/index'
import { requestDictMap } from '@/api/common/index'

import { fill, min } from 'lodash'

const route = useRoute()
const store = useStore()

const emit = defineEmits(['setCurrentUpFun'])

const setRepaymentContractDataFun = inject('setRepaymentContractDataFun')

defineProps({
  finaContractSigningType: {
    type: Boolean,
    required: true,
    defalut: false,
  },
})

const dialogReimburSement = ref(null)
const detail = ref({})

const loadTermUnit = ref(2)
const filterArr = reactive({
  filterTabBar: [],
  filterTabBarVal: [],
})
const annualInterestRateObj = ref({
  annualInterestRate: '0.333',
  dailyInterestRate: '0.3',
})
const financeNoId = ref('J20566224724029') // 融资编号
const finacCinId = ref(
  JSON.parse(sessionStorage.getItem('financingDemandId')) ||
    route.query.id ||
    route.query.goodId
)

const financingIds = computed(() => sessionStorage.getItem('financingIds'))

const show = ref(false)
const positveArr = ref([
  {
    id: 1,
    title: '融资期限',
    value: '',
  },
  {
    id: 2,
    title: '还款方式',
    value: '',
  },
  // {
  //   id: 3,
  //   title: '日利率',
  //   value: '',
  // },
  // {
  //   id: 4,
  //   title: '年利率',
  //   value: '',
  // },
])
const loanchildrenArr = ref([
  {
    id: 1,
    title: '贷款主体',
    value: [],
  },
  {
    id: 2,
    title: '银行实际年利率',
    value: [],
  },
  {
    id: 3,
    title: '年化综合资金成本',
    value: [],
  },
  {
    id: 4,
    title: '利息计算方式',
    value: [],
  },
])
const pageData = reactive([
  { dalogAmount: 0 },
  { loanPeriod: null },
  { amount: '0.00' },
  { jrzhPhone: null },
  { capitalNameP: '' },
])

provide('labelListFunc', () => filterArr.filterTabBar) // 随借随还 等额本息等

// 还款试算弹窗
const handleExhibition = () => {
  dialogReimburSement.value.handleOpen() // 打开弹窗
  dialogReimburSement.value.onload() // 初始化数据
}

const planDetail = ref([])
const financingApplyInfo = ref([])
const listProduct = ref([])
// 还款试算参数
const ReimburSementQueryParams = computed(() => {
  console.log(financingApplyInfo.value)
  return financingApplyInfo.value.map(product => {
    const { financeApply, receivableFinanceDTO } = product
    // 如果产品的最大期限小于输入期限则拿产品最大期限
    const loanTerm = financeApply.loadTerm
    console.log('---------方案产品---------', product)

    const curProduct = listProduct.value.find(item => {
      return item.enterpriseQuota.goodsId === financeApply.goodsId
    })

    let annualInterestRate = ''

    if (curProduct && Object.keys(curProduct).length) {
      annualInterestRate = curProduct.enterpriseQuota.annualInterestRate
    }

    return {
      financeAmount: receivableFinanceDTO.salesContractDetails[0].amount,
      totalTerm: loanTerm,
      loanDay: loanTerm,
      goodsId: financeApply.goodsId,
      goodType: financeApply.goodsType,
      loadTermUnit: financeApply.loadTermUnit,
      annualInterestRate,
    }
  })
})

// 详情
const applyGetByIdFun = (arr, arr1) => {
  PRODUCT_APPLI_CATIONS_API.getFinanceApplyByIds(financingIds.value).then(
    ({ data }) => {
      const { data: resData } = data
      if (data.success && resData) {
        planDetail.value = resData
        // 融资期限
        if (resData.loadTermUnit) {
          const _a = arr1.filter(
            itemed => itemed.key == resData.loadTermUnit
          )[0]?.value
          if (_a !== '天') {
            positveArr.value[0].value = `${resData.loadTerm}个月`
          } else {
            positveArr.value[0].value = `${resData.loadTerm}天`
          }
          pageData[0].loanPeriod = resData.loadTerm //还款计划请求需要
        } else {
          positveArr.value[0].value = '未选择'
        }

        // 日利率
        positveArr.value[2].value = `${resData.dailyInterestRate}%`
        // 年利率
        positveArr.value[3].value = `${resData.annualInterestRate}%`

        // 贷款主体
        loanchildrenArr.value[0].value = resData.map(item => item.capitalName)
        // pageData[2].capitalNameP = resData.capitalName
        // 银行实际年利率
        loanchildrenArr.value[1].value = resData.map(
          item => `${item.annualInterestRate}%`
        )
        // 年化综合资金成本
        loanchildrenArr.value[2].value = resData.map(
          item =>
            `${formatMoney(
              Number(item.annualInterestRate) + Number(item.serviceRate)
            )}%`
        )
        loanchildrenArr.value[3].value = fill(
          Array(resData.length),
          '单利，详见【还款计划】'
        )

        // 融资金额
        // pageData[0].amount = formatMoney(resData.amount)
        // pageData[0].dalogAmount = resData.amount //还款计划请求需要

        // 弹窗数据处理
        filterArr.filterTabBar = [positveArr.value[1].value]
        // financeNoId.value = resData.financeNo
        // annualInterestRateObj.value.annualInterestRate =
        //   resData.annualInterestRate
        // annualInterestRateObj.value.dailyInterestRate =
        //   resData.dailyInterestRate

        // 合同模板生成数据处理
        const dialogdatas = {
          financeAmount: resData.amount, // 融资金额
          annualInterestRate: resData.annualInterestRate, // 年利率
          totalTerm: pageData[0].loanPeriod, // 总期数
          startTime: dayjs().format('YYYY-MM-DD'), // 开始时间
          refundType: filterArr.filterTabBarVal[0] || -1, // 还款类型 '1','2','3'
          loadTermUnit: resData.loadTermUnit, // 1-天,2-期
        }
        setRepaymentContractDataFun(dialogdatas)
      }
    }
  )
}

// 客服电话
const frontType = () => {
  PRODUCT_APPLI_CATIONS_API.frontType(1).then(res => {
    const { data: resData, code } = res.data
    if (code === 200 && resData.length) {
      pageData[1].jrzhPhone = resData[0].description
    } else {
      pageData[1].jrzhPhone = '000-000-000'
    }
  })
}

// 避免签署合同的多余参数污染下一个节点
const routerQueryDelte = page => {
  emit('setCurrentUpFun', page)
  if (page == 0) {
    // 回退页面的事件执行
    store.commit('Product/setGOBackPapeLock')
  }
}

// 自动放款-收平台费-提交流程
const financeConfirmFun = async () => {
  const params = {
    id: sessionStorage.getItem('planId') || route.query.planId,
    financeApplyHandlerDTOList: financingApplyInfo.value.map(item => {
      // 后端返回的时间戳，需要转换为年月日
      item.costCalculusDto.startTime = dayjs(
        item.costCalculusDto.startTime
      ).format('YYYY-MM-DD')
      return item
    }),
  }

  const { data: resData } = await PRODUCT_APPLI_CATIONS_API.financeApplySubmit3(
    params
  )

  sessionStorage.setItem('financingIds', resData.data)

  const data = {
    planId: sessionStorage.getItem('planId') || route.query.planId,
    financeConfirmDTOs: resData.data.map(item => {
      return {
        financeApplyId: item,
        contractIdList: [],
      }
    }),
  }

  PRODUCT_APPLI_CATIONS_API.financeConfirm2(data).then(({ data }) => {
    if (data.success) {
      store.commit('Product/setGOBackPapeLock') // 触发获取上传附件请求
      routerQueryDelte(2)
    }
  })
}

// 上一步事件
const lastStep = () => {
  routerQueryDelte(0)
}

// 下一步事件
const nextToPape = () => {
  financeConfirmFun()
}

// 查询流程进度
const getByBusinessIdAndTypeApi = arr => {
  const planId = sessionStorage.getItem('planId') || route.query.planId
  PRODUCT_VERIFY_API.getByBusinessIdAndType3(planId)
    .then(res => {
      const resData = res.data
      if (resData.code == 200 && resData.data) {
        sessionStorage.setItem('financeApplyIds', resData.data.financeApplyIds)
        detail.value = resData.data

        const data = resData.data

        console.log(arr, data.repaymentMode)
        // 还款方式
        // if (!~data.repaymentMode) {
        //   positveArr.value[1].value = '随借随还'
        // } else {
        //   const filArr = arr.filter(itemed => itemed.key == data.repaymentMode)
        //   positveArr.value[1].value = filArr[0]?.value
        //   // 弹窗数据处理
        //   filterArr.filterTabBarVal = [filArr[0]?.key]
        // }

        // 解决JSON内数字格式过长导致精度丢失问题
        financingApplyInfo.value = JSON.parse(
          data.financingApplyInfo.replace(
            /:\s*([-+]?\d+(\.\d+)?([eE][-+]?\d+)?)/g,
            (match, p1) => {
              if (Math.abs(p1) > Number.MAX_SAFE_INTEGER) {
                return `:"${p1}"`
              }
              return `:${p1}`
            }
          )
        )

        positveArr.value[0].value =
          data.loadTerm + `${data.loadTermUnit === 1 ? '天' : '个月'}`
        pageData[0].loanPeriod = resData.loadTerm
        pageData[0].amount = formatMoney(data.actualAmount)

        loadTermUnit.value = data.loadTermUnit
        if (data.loadTermUnit == 1) {
          positveArr.value[1].value = '随借随还'

          // 随借随还
          filterArr.filterTabBarVal = ['-1']
        } else {
          const filArr = arr.filter(itemed => itemed.key == data.repaymentMode)
          positveArr.value[1].value = filArr[0]?.value
          // 弹窗数据处理
          filterArr.filterTabBarVal = [filArr[0]?.key]
        }

        filterArr.filterTabBar = [positveArr.value[1].value]
      }
    })
    .catch(({ msg, hideMsgFunc }) => {})
}

const getListProduct = async () => {
  const planNo = sessionStorage.getItem('planNo') || route.query.planNo
  const {
    data: { data: resData },
  } = await PRODUCT_APPLI_CATIONS_API.pageWithProduct({ planNo })

  listProduct.value = resData.filter(item => +item.quotaActual)

  // 贷款主体
  loanchildrenArr.value[0].value = listProduct.value.map(
    item => item.enterpriseQuota.bank
  )
  // pageData[2].capitalNameP = listProduct.value.capitalName
  // 银行实际年利率
  loanchildrenArr.value[1].value = listProduct.value.map(
    item => `${item.enterpriseQuota.annualInterestRate}%`
  )
  // 年化综合资金成本
  loanchildrenArr.value[2].value = listProduct.value.map(
    item =>
      `${formatMoney(
        Number(item.enterpriseQuota.annualInterestRate) +
          Number(item.enterpriseQuota.serviceRate)
      )}%`
  )
  loanchildrenArr.value[3].value = fill(
    Array(listProduct.value.length),
    '单利，详见【还款计划】'
  )
}

const onLoad = () => {
  requestDictMap('goods_billing_method').then(res => {
    const resData = res.data
    if (resData.code == 200) {
      // 处理字典数据
      const resList = []
      for (const item of resData.data) {
        resList.push({
          key: item.dictKey,
          value: item.dictValue,
          id: item.id,
        })
      }
      requestDictMap('goods_load_term_unit').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList1 = []
          for (const item of resData.data) {
            resList1.push({
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            })
          }
          getByBusinessIdAndTypeApi(resList)
          getListProduct()
          // applyGetByIdFun(resList, resList1) // 放款查询相关信息
          frontType() // 放款申请客服
        }
      })
    }
  })
}
onLoad()
</script>

<style lang="scss" scoped>
@import '@/views/product/antdStyle.scss';

.financing-confirm {
  max-width: 1400px;
  position: relative;
  margin: auto;
  box-sizing: border-box;

  .top-line {
    width: 100%;
    // height: 1px;
    display: block;
    background: #f1f2f4;
    margin-top: 40px;
    // margin-bottom: 48px;
  }

  .infometion-box {
    overflow: hidden;

    .borrowing-balance {
      height: 32px;
      font-size: 24px;
      font-weight: 500;
      color: #0a1f44;
      line-height: 32px;
      text-align: center;
      margin-bottom: 20px;
      cursor: context-menu;
    }

    .borrowing-figures {
      display: flex;
      justify-content: center;
      align-items: baseline;
      cursor: context-menu;
      transform: translateX(5%);

      .figures-left {
        height: 77px;
        font-size: 64px;
        font-weight: 700;
        color: #031222;
        line-height: 79px;
        margin-right: 32px;
      }
      .figures-right {
        font-size: 16px;
        font-weight: 500;
        color: #0d55cf;
        line-height: 24px;
        cursor: pointer;
      }
    }

    .positive-box {
      display: flex;
      justify-content: center;
      margin-top: 40px;
      margin-bottom: 40px;

      .positive-for {
        width: 320px;
        height: 160px;
        background: #ffffff;
        box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
        border-radius: 16px;
        border: 1px solid #efefef;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin-right: 40px;
        &:last-child {
          margin-right: 0;
        }

        .positive-title {
          height: 28px;
          font-size: 20px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 28px;
        }
        .positive-info {
          height: 40px;
          font-size: 28px;
          font-weight: bold;
          color: #0a1f44;
          line-height: 40px;
          margin-top: 12px;
        }
      }
    }

    .loaninformation-box {
      background: #ffffff;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      border-radius: 16px;
      border: 1px solid #efefef;
      padding: 40px;
      box-sizing: border-box;
      margin-bottom: 40px;

      .loaninformation-title {
        height: 32px;
        font-size: 24px;
        font-weight: 500;
        color: #0a1f44;
        line-height: 32px;
        margin-bottom: 40px;
      }

      .loanchildren-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .loanchildren-for {
          width: 300px;

          .loanchildren-title {
            height: 24px;
            font-size: 16px;
            font-weight: 600;
            color: #8a94a6;
            line-height: 24px;
          }
          .loanchildren-info {
            height: 24px;
            font-size: 16px;
            font-weight: 500;
            color: #53627c;
            line-height: 24px;
            margin-top: 8px;
          }
        }
      }

      .termsOfCredit-box {
        margin-top: 40px;
        .termsOfCredit-title {
          height: 24px;
          font-size: 16px;
          font-weight: 600;
          color: #8a94a6;
          line-height: 24px;
          margin-bottom: 6px;
        }
        .termsOfCredit-p {
          font-size: 16px;
          font-weight: 400;
          color: #53627c;
          line-height: 26px;
        }
      }

      .fold-box {
        height: 24px;
        font-size: 16px;
        font-weight: 500;
        color: #0d55cf;
        line-height: 24px;
        margin-top: 25px;
        cursor: pointer;
      }
    }

    .loaninformation-menu-box {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      margin: 40px 0;

      & > span {
        width: 188px;
        height: 48px;
        margin-right: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;

        .ant-btn-round.ant-btn-lg {
          height: 100%;
        }
      }

      & > span:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
