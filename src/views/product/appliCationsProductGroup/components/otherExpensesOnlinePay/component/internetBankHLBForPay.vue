<template>
  <div class="internet-bank-HLB">
    <!-- target="targetIfr" -->
    <template v-if="internetBankObj?.payUrl">
      <form id="myForm" method="POST" :action="internetBankObj.payUrl">
        <input
          type="hidden"
          name="P1_bizType"
          :value="internetBankObj.P1_bizType"
          v-if="internetBankObj.P1_bizType"
        />
        <input
          type="hidden"
          name="P2_orderId"
          :value="internetBankObj.P2_orderId"
          v-if="internetBankObj.P2_orderId"
        />
        <input
          type="hidden"
          name="P3_customerNumber"
          :value="internetBankObj.P3_customerNumber"
          v-if="internetBankObj.P3_customerNumber"
        />
        <input
          type="hidden"
          name="P4_orderAmount"
          :value="internetBankObj.P4_orderAmount"
          v-if="internetBankObj.P4_orderAmount"
        />
        <input
          type="hidden"
          name="P5_bankId"
          :value="internetBankObj.P5_bankId"
          v-if="internetBankObj.P5_bankId"
        />
        <input
          type="hidden"
          name="P6_business"
          :value="internetBankObj.P6_business"
          v-if="internetBankObj.P6_business"
        />
        <input
          type="hidden"
          name="P7_timestamp"
          :value="internetBankObj.P7_timestamp"
          v-if="internetBankObj.P7_timestamp"
        />
        <!-- <input
        type="hidden"
        name="P8_goodsName"
        :value="internetBankObj.P8_goodsName"
      /> -->
        <input
          type="hidden"
          name="P9_period"
          :value="internetBankObj.P9_period"
          v-if="internetBankObj.P9_period"
        />
        <input
          type="hidden"
          name="P10_periodUnit"
          :value="internetBankObj.P10_periodUnit"
          v-if="internetBankObj.P10_periodUnit"
        />
        <input
          type="hidden"
          name="P11_callbackUrl"
          :value="internetBankObj.P11_callbackUrl"
          v-if="internetBankObj.P11_callbackUrl"
        />
        <input
          type="hidden"
          name="P12_serverCallbackUrl"
          :value="internetBankObj.P12_serverCallbackUrl"
          v-if="internetBankObj.P12_serverCallbackUrl"
        />
        <input
          type="hidden"
          name="P13_orderIp"
          v-model="internetBankObj.P13_orderIp"
          v-if="internetBankObj.P13_orderIp"
        />
        <input
          type="hidden"
          name="P14_onlineCardType"
          :value="internetBankObj.P14_onlineCardType"
          v-if="internetBankObj.P14_onlineCardType"
        />
        <input
          type="hidden"
          name="P16_splitBillType"
          :value="internetBankObj.P16_splitBillType"
          v-if="internetBankObj.P16_splitBillType"
        />
        <input
          type="hidden"
          name="P17_ruleJson"
          :value="internetBankObj.P17_ruleJson"
          v-if="internetBankObj.P17_ruleJson"
        />
        <input
          type="hidden"
          name="signatureType"
          :value="internetBankObj.signatureType"
          v-if="internetBankObj.signatureType"
        />
        <input
          type="hidden"
          name="encryptionKey"
          :value="internetBankObj.encryptionKey"
          v-if="internetBankObj.encryptionKey"
        />
        <input type="hidden" name="sign" :value="internetBankObj.sign" />
        <!-- <input type="hidden" name="P15_desc" :value="internetBankObj.P15_desc" /> -->
      </form>
    </template>
    <!-- <iframe name="targetIfr" width="900px" height="900px"></iframe> -->
  </div>
</template>

<script>
export default {
  name: 'internetBankHLB',
}
</script>
<script setup>
import { ref } from '@vue/reactivity'
const internetBankObj = ref({})

// setInterval(() => {
// const iframe = document.getElementsByTagName('iframe')[0]
// console.log('iframe',iframe)
// if (iframe.attachEvent) {
//   iframe.attachEvent('onload', function () {
//     console.log('1', 1)
//   })
// } else {
//   iframe.onload = function () {
//     console.log('2', 2)
//   }
// }
// }, 1000)

const internetBankToPay = resData => {
  internetBankObj.value = resData
  setTimeout(() => {
    // const form = document.getElementById('myForm')
    // form.action = resData.payUrl
    // const P1_bizType = document.getElementsByName('P1_bizType')[0]
    // P1_bizType.value = resData.P1_bizType
    // const P2_orderId = document.getElementsByName('P2_orderId')[0]
    // P2_orderId.value = resData.P2_orderId
    // const P3_customerNumber = document.getElementsByName('P3_customerNumber')[0]
    // P3_customerNumber.value = resData.P3_customerNumber
    // const P4_orderAmount = document.getElementsByName('P4_orderAmount')[0]
    // P4_orderAmount.value = resData.P4_orderAmount
    // const P5_bankId = document.getElementsByName('P5_bankId')[0]
    // P5_bankId.value = resData.P5_bankId
    // const P6_business = document.getElementsByName('P6_business')[0]
    // P6_business.value = resData.P6_business
    // const P7_timestamp = document.getElementsByName('P7_timestamp')[0]
    // P7_timestamp.value = resData.P7_timestamp
    // const P11_callbackUrl = document.getElementsByName('P11_callbackUrl')[0]
    // P11_callbackUrl.value = resData.P11_callbackUrl
    // const P12_serverCallbackUrl = document.getElementsByName('P12_serverCallbackUrl')[0]
    // P12_serverCallbackUrl.value = resData.P12_serverCallbackUrl
    // const P13_orderIp = document.getElementsByName('P13_orderIp')[0]
    // P13_orderIp.value = resData.P13_orderIp
    // const P14_onlineCardType = document.getElementsByName('P14_onlineCardType')[0]
    // P14_onlineCardType.value = resData.P14_onlineCardType
    // const P17_signatureType = document.getElementsByName('P17_signatureType')[0]
    // P17_signatureType.value = resData.P17_signatureType
    // const sign = document.getElementsByName('sign')[0]
    // sign.value = resData.sign
    // console.log(resData)
    // console.log(document.getElementById('myForm'))
    document.getElementById('myForm').submit()
  }, 150)
}

defineExpose({
  internetBankToPay,
})
</script>

<style lang="scss" scoped>
.internet-bank-HLB {
  color: #000;
}
</style>
