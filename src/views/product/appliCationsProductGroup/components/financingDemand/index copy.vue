<template>
  <div class="financing-demand">
    <span class="top-line" />
    <!-- 质押品 -->
    <div class="pledge-box">
      <div class="pledge-header">
        <span class="pledge-text-title">{{
          route.query.goodType === '5' ? '融资订单' : '质押品'
        }}</span>
        <template v-if="route.query.goodType !== '5'">
          <span class="pledge-Vertical" />
          <div class="pledge-price-box">
            <span class="pledge-text-price">当前总价值：</span>
            <span class="pledge-num-price">
              <a-statistic :precision="2" :value="appliCationsAllNumData">
                <template #prefix>
                  <span>￥</span>
                </template>
              </a-statistic>
            </span>
          </div>
        </template>
      </div>
      <div class="tradefor-information">
        <div
          class="tradefor-for"
          v-for="(item, index) in appliCationsData"
          :key="item.key"
        >
          <TradeforBox
            :ref="`tradeforBox${index}`"
            :items="item"
            :indexs="index"
            @deletes="deleteAppliCationsData"
            @allNum="allNumMoneyFun"
          />
        </div>
        <div class="tradefor-for" v-if="panduanshifouchuAddC">
          <AddCard
            @create="handleCreate()"
            width="414px"
            height="325px"
            :title-name="
              route.query.goodType === '5' ? '选择订单记录' : '添加应收账款'
            "
          ></AddCard>
        </div>
      </div>
    </div>
    <!-- 融资金额 -->
    <div class="financing-amount">
      <div class="financing-top-amount">
        <div class="financing-left-amount">
          <div class="pledge-header">
            <span class="pledge-text-title">融资金额</span>
            <template v-if="route.query.goodType !== '5'">
              <span class="pledge-Vertical" />
              <div class="pledge-price-box">
                <span class="pledge-text-price">最多可融资金额：</span>
                <span class="pledge-num-price">
                  <a-statistic :precision="2" :value="loanAmount">
                    <template #prefix>
                      <span>￥</span>
                    </template>
                  </a-statistic>
                </span>
              </div>
            </template>
          </div>
          <div class="financing-money-all">
            <a-statistic :precision="2" :value="allNumMoney">
              <template #prefix>
                <span>￥</span>
              </template>
            </a-statistic>
          </div>
          <span class="line" />
          <div class="financing-hint">
            <MySvgIcon
              icon-class="icon-xinxi"
              style="fill: #8a94a6; font-size: 24px"
              targerUri
            ></MySvgIcon>
            <span class="financing-text-hint">按天算利息，可提前还款</span>
          </div>
        </div>
        <div class="financing-right-amount">
          <div class="exhibition-box" @click="monthFun()">
            <span class="exhibition-text">借多久？</span>
            <div class="exhibition-menu">
              <div v-if="loanPeriod">
                <span class="exhibition-text-menu" v-if="loadTermUnit === 2"
                  >{{ loanPeriod }}个月</span
                >
                <span
                  class="exhibition-text-menu"
                  v-else-if="loadTermUnit === 1"
                  >{{ loanPeriod }}天</span
                >
              </div>
              <span v-else class="exhibition-text-menu">请选择</span>

              <span class="exhibition-do-menu">
                <MySvgIcon
                  icon-class="icon-youjiantou1"
                  style="fill: #8a94a6; font-size: 28px"
                  targerUri
                ></MySvgIcon>
              </span>
            </div>
          </div>
          <div class="exhibition-box" @click="purposeLoan()">
            <span class="exhibition-text">融资用途</span>
            <div class="exhibition-menu">
              <span class="exhibition-text-menu">
                {{ purposeLoanData.text || '请选择' }}
              </span>
              <span class="exhibition-do-menu">
                <MySvgIcon
                  icon-class="icon-youjiantou1"
                  style="fill: #8a94a6; font-size: 28px"
                  targerUri
                ></MySvgIcon>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="financing-bottom-amount" @click="handleExhibition()">
        <div class="exhibition-box">
          <span class="exhibition-text">怎么还？</span>
          <div class="exhibition-menu">
            <div class="exhibition-text-menu">
              <span v-if="filterNullStr" class="null-box">{{
                filterNullStr
              }}</span>
              <span v-else class="text-box">{{
                filterArr.filterTabBar[tabBarObj.tabBarIndex]
              }}</span>
              <span class="blue-text-box">查看还款试算</span>
            </div>
            <div class="exhibition-do-menu">
              <MySvgIcon
                icon-class="icon-youjiantou1"
                style="fill: #8a94a6; font-size: 28px"
                targerUri
              ></MySvgIcon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="financing-menu-box">
      <NButton
        class="blue button-item primary"
        :bordered="true"
        type="info"
        round
        @click="nextToPape"
      >
        <span class="desc">{{
          lendingMethodDataType === '2' ? '立即提交' : '下一步'
        }}</span>
      </NButton>
    </div>
    <!-- 借多久弹窗（期） -->
    <DialogTimeMonth
      ref="dialogTimeMonth"
      :loadTermObj="loadTermObj"
      :loanPeriod="loanPeriod"
      @setLoanPeriod="alterationLoanPeriod"
    />
    <!-- 借多久弹窗（天） -->
    <DialogTimeDays
      ref="dialogTimeDays"
      :loadTermObj="loadTermObj"
      :loanPeriod="loanPeriod"
      @setLoanPeriod="alterationLoanPeriod"
    />
    <!-- 融资用途 -->
    <DialogPurposeLoan
      ref="dialogPurposeLoan"
      :purposeLoanList="purposeLoanList"
      :setpurposeLoanObj="purposeLoanData"
      @setData="setPurposeLoanData"
    />
    <!-- 应收展账款弹窗 -->
    <DialogReceivables ref="dialogReceivables" @allRecount="allRecount" />
    <!-- 订单融资选择弹窗 -->
    <DialogReceivablesDingDan
      ref="dialogReceivablesDingDanRef"
      @allRecount="allRecount"
    />
    <!-- 还款试算弹窗 -->
    <DialogReimburSement
      ref="dialogReimburSement"
      :allNumMoney="allNumMoney"
      :loanPeriod="loanPeriod"
      :loadTermUnit="loadTermUnit"
      :filterArr="filterArr"
      :tabBarObj="tabBarObj"
      :annualInterestRateObj="annualInterestRateObj"
      :chargeMethod="chargeMethod"
      @saveRepaymentType="saveRepaymentType"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'financingDemand',
}
</script>
<script setup lang="ts">
import TradeforBox from './components/tradeforBox.vue'
import DialogTimeMonth from './components/Dialog/dialogTimeMonth.vue'
import DialogTimeDays from './components/Dialog/dialogTimeDays.vue'
import DialogReceivables from './components/Dialog/dialogReceivables.vue'
import DialogReceivablesDingDan from './components/Dialog/dialogReceivablesDingDan.vue'
import DialogReimburSement from './components/Dialog/dialogReimburSement.vue'
import DialogPurposeLoan from './components/Dialog/dialogPurposeLoan.vue'
import AddCard from '@/components/BaseCard/addCard/index.vue'
import { NButton } from 'naive-ui'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { PRODUCT_APPLI_CATIONS_API, PRODUCT_VERIFY_API } from '@/api/index.js'
import { requestDictMap } from '@/api/common/index'
const route = useRoute()
const store = useStore()
import {
  ref,
  computed,
  getCurrentInstance,
  watch,
  provide,
  reactive,
} from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

const emit = defineEmits(['setCurrentUpFun'])

const props = defineProps({
  type: {
    type: Number,
    required: true,
    defalut: 0,
  },
})

const instance = getCurrentInstance() // this

const dialogTimeMonth = ref(null)
const dialogTimeDays = ref(null)
const dialogPurposeLoan = ref(null)
const dialogReceivables = ref(null)
const dialogReceivablesDingDanRef = ref(null)
const dialogReimburSement = ref(null)
const chargeMethod = ref<any>('')
const loanAmount = ref(0)
const loadTermUnit = ref(2)
const loanPeriod = ref(0)
const allNumMoney = ref(0)
const allNumMoneyArr = ref([])
const filterArr = reactive({
  filterTabBar: [],
  filterTabBarVal: [],
})
const filterNullStr = ref('请选择')
const tabBarObj = reactive({
  tabBarIndex: 0,
  tabBarIndexVal: void 0,
})
const annualInterestRateObj = ref({})
const appliCationsData = computed<Array<any>>(
  () => store.getters['Product/appliCations']
)
chargeMethod.value = route.query.chargeMethod
const appliCationsAllNumData = ref(
  computed(() => {
    let num = 0
    for (const item of appliCationsData.value) {
      if (item.financingAvailableAmount) {
        num += Number(item.financingAvailableAmount)
      }
    }
    return num
  })
)
const panduanshifouchuAddC = computed(() => {
  let kaiqi = false
  if (route.query.goodType === '5') {
    if (appliCationsData.value.length < 1) {
      kaiqi = true
    }
  } else {
    if (appliCationsData.value.length < 9) {
      kaiqi = true
    }
  }
  return kaiqi
})
const loadTermObj = ref({})
const purposeLoanList = ref([])
const purposeLoanData = ref({})
const financeNoId = ref(null)
const finacCinId = ref(
  JSON.parse(sessionStorage.getItem('financingDemandId')) || route.query.id
)
const idObj = ref({})

const nextLock = ref(false)

const lendingMethodDataType = ref(route.query.lendingMethod)

provide('labelListFunc', () => filterArr.filterTabBar) // 随借随还 等额本息等

// 比较两个数组差异
const getArrDifference = (arr1, arr2) =>
  arr1.concat(arr2).filter(function (v, i, arr) {
    return arr.indexOf(v) === arr.lastIndexOf(v)
  })

// 两个数字的大小
const numberCompare = (number1, number2) => {
  if (parseInt(number1) >= parseInt(number2)) {
    return number2
  } else if (parseInt(number1) <= parseInt(number2)) {
    return number1
  }
}

// 融资用途
const purposeLoan = () => {
  if (JSON.stringify(purposeLoanData.value) !== '{}') {
    dialogPurposeLoan.value.handleReset(purposeLoanData.value) // 设置之前已选index
  }
  dialogPurposeLoan.value.handleOpen() // 打开弹窗
}

// 设置融资用途数据
const setPurposeLoanData = val => {
  purposeLoanData.value = val
}

// 借多久的fun
const monthFun = () => {
  if (loadTermUnit.value == 2) {
    dialogTimeMonth.value.handleOpen() // 打开弹窗
  } else {
    dialogTimeDays.value.handleOpen() // 打开弹窗
  }
}

// 设置借多久数量
const alterationLoanPeriod = num => {
  if (loadTermUnit.value == 2) {
    loanPeriod.value = num
  } else {
    loanPeriod.value = num.day
  }
}

// 还款试算弹窗
const handleExhibition = () => {
  if (verifyCheck()) {
    dialogReimburSement.value.handleOpen() // 打开弹窗
    dialogReimburSement.value.onload() // 初始化数据
  }
}

// 设置还款试算参数
const saveRepaymentType = obj => {
  tabBarObj.tabBarIndex = obj.tabBarIndex
  tabBarObj.tabBarIndexVal = obj.filtArrKey
  filterNullStr.value = null // 清除请选择文案
}

// 添加应收账款弹窗
const handleCreate = () => {
  if (route.query.goodType === '5') {
    dialogReceivablesDingDanRef.value.handleOpen() // 打开弹窗
    setTimeout(() => {
      dialogReceivablesDingDanRef.value.handreFresh() // 初始化数据
    }, 100)
    return
  }
  dialogReceivables.value.handleOpen() // 打开弹窗
  setTimeout(() => {
    dialogReceivables.value.handreFresh() // 初始化数据
  }, 100)
}

// 应收账款弹窗内增删数据触发事件 重新计算融资金额
const allRecount = () => {
  if (route.query.goodType === '5') {
    // allNumMoney.value = appliCationsData.value[0]?.financingAvailableAmount
    allNumMoney.value = appliCationsData.value[0]?.limitAmount // 水位金额
    return
  }

  watch(
    () => appliCationsData.value,
    (N, O) => {
      if (N.length && O.length) {
        const newIndexArr = N.map(item => item.key)
        const oldIndexArr = O.map(item => item.key)
        const differenceArr = getArrDifference(newIndexArr, oldIndexArr).filter(
          item => oldIndexArr.includes(item)
        ) // 过滤出旧数据中的差异值 并返回数组
        for (let index = oldIndexArr.length; index >= 0; index--) {
          // 选出旧数组中包含的差异值进行删除
          if (differenceArr.includes(oldIndexArr[index])) {
            allNumMoneyArr.value.splice(index, 1) // 删除金额的数据
          }
        }
        // 重新计算金额
        allNumMoney.value = 0
        for (const item of allNumMoneyArr.value) {
          allNumMoney.value += Number(item)
        }
      } else if (!N.length) {
        // 新数据lengt不存在 清空金额
        // 重新计算金额
        allNumMoney.value = 0
      }
    }
  )
}e

// 应收账款删除数据
const deleteAppliCationsData = index => {
  appliCationsData.value.splice(index, 1) // 删除arr
  allNumMoneyArr.value.splice(index, 1) // 删除金额的数据
  // 重新计算金额
  allNumMoney.value = 0
  for (const item of allNumMoneyArr.value) {
    allNumMoney.value += Number(item)
  }
  store.commit('Product/setAppliCations', appliCationsData.value) // 保存修改
  allRecount()
}

// 算出应收账款总价值
const allNumMoneyFun = (num, index) => {
  if (isNaN(num)) return
  allNumMoneyArr.value[index] = Number(num)
  allNumMoney.value = 0
  for (const item of allNumMoneyArr.value) {
    allNumMoney.value += Number(item)
  }
  // 使用价值不能大于可融资金额
  if (Number(loanAmount.value)) {
    if (Number(allNumMoney.value) > Number(loanAmount.value)) {
      message.error('不能大于可融资金额')
      instance.refs[`tradeforBox${index}`][0].clearMatch()
    }
  }
}

// 请求
store.commit('Product/setAppliCations', [])
// 借款用途字典
requestDictMap('finance_apply_loan_usage').then(res => {
  const resData = res.data
  if (resData.code == 200) {
    // 处理字典数据
    const resList = []
    for (const item of resData.data) {
      resList.push({
        key: item.dictKey,
        value: item.dictValue,
        id: item.id,
      })
    }
    // 设置默认值
    // let keyf = []
    // // 过滤出目标的key
    // if (resList.length) {
    //   keyf = resList.filter(item => item.key === '1')
    // }
    // if (keyf.length) {
    //   purposeLoanData.value['text'] = keyf[0].value
    //   purposeLoanData.value['index'] = keyf[0].key
    // }
    // 融资用途是否设置默认值
    // purposeLoanData.value['text'] = resList[0].value
    // purposeLoanData.value['index'] = resList[0].key
    purposeLoanList.value = resList
  }
})
if (!finacCinId.value) {
  requestDictMap('goods_billing_method').then(res => {
    const resData = res.data
    if (resData.code == 200) {
      // 处理字典数据
      const resList = []
      for (const item of resData.data) {
        resList.push({
          key: item.dictKey,
          value: item.dictValue,
          id: item.id,
        })
      }
      // 查询额度
      PRODUCT_APPLI_CATIONS_API.getByenterpriseQuotaId(
        route.query.enterpriseQuotaId
      ).then(res => {
        const resData = res.data
        if (resData.code == 200 && resData.data) {
          const dates = resData.data
          const availableAmount = dates.availableAmount
          annualInterestRateObj.value = {
            annualInterestRate: dates.annualInterestRate,
            dailyInterestRate: dates.dailyInterestRate,
          }
          // 查询产品接口
          PRODUCT_APPLI_CATIONS_API.getBygoodsId(route.query.goodId).then(
            resed => {
              const resDatas = resed.data
              if (resDatas.code == 200) {
                const dat = resDatas.data
                const params = {
                  customerGoodsId: route.query.customerGoodsId,
                  enterpriseQuotaId: route.query.enterpriseQuotaId,
                }
                if (route.query.goodType === '5') {
                  const num = numberCompare(availableAmount, dat.loanAmountEnd)
                  loanAmount.value = Number(JSON.stringify(num * 10000))

                  // 借多久是否设置默认值
                  // loanPeriod.value = dat.loadTermStart
                  // DialogTimeMonth弹窗参数 start
                  loadTermObj.value['loadTermStart'] = dat.loadTermStart
                  loadTermObj.value['loadTermEnd'] = dat.loadTermEnd
                  // DialogTimeMonth弹窗参数 end
                  // DialogReimburSement弹窗参数 start
                  loadTermUnit.value = dat.loadTermUnit // 1:天；2:期
                  if (dat.repaymentType !== 1) {
                    filterArr.filterTabBar = ['随借随还']
                    filterArr.filterTabBarVal = ['-1']
                    return
                  }
                  dat.billingMethod.split(',').forEach((item, index) => {
                    // 过滤出当前的计费方式
                    const filtResList = resList.filter(
                      itemed => itemed.key == item
                    )
                    filterArr.filterTabBar[index] = filtResList[0]?.value
                    filterArr.filterTabBarVal[index] = filtResList[0]?.key
                  })
                  // DialogReimburSement弹窗参数 end
                  return
                }
                PRODUCT_APPLI_CATIONS_API.availableFinanceAmount(params).then(
                  resed => {
                    const resDatas = resed.data
                    if (resDatas.code == 200) {
                      const dat1 = resDatas.data
                      const num = dat1
                      loanAmount.value = num
                      // 借多久是否设置默认值
                      // loanPeriod.value = dat.loadTermStart
                      // DialogTimeMonth弹窗参数 start
                      loadTermObj.value['loadTermStart'] = dat.loadTermStart
                      loadTermObj.value['loadTermEnd'] = dat.loadTermEnd
                      // DialogTimeMonth弹窗参数 end
                      // DialogReimburSement弹窗参数 start
                      loadTermUnit.value = dat.loadTermUnit // 1:天；2:期
                      if (dat.repaymentType !== 1) {
                        filterArr.filterTabBar = ['随借随还']
                        filterArr.filterTabBarVal = ['-1']
                        return
                      }
                      dat.billingMethod.split(',').forEach((item, index) => {
                        // 过滤出当前的计费方式
                        const filtResList = resList.filter(
                          itemed => itemed.key == item
                        )
                        filterArr.filterTabBar[index] = filtResList[0]?.value
                        filterArr.filterTabBarVal[index] = filtResList[0]?.key
                      })
                      // DialogReimburSement弹窗参数 end
                    }
                  }
                )
              }
            }
          )
        }
      })
    }
  })
} else {
  // 查询流程
  const params = {
    businessId: finacCinId.value || route.query.goodId,
    type: props.type,
  }
  PRODUCT_VERIFY_API.getByBusinessIdAndType(params).then(res => {
    const resData = res.data
    const dat = res.data.data
    if (resData.code == 200 && dat) {
      idObj.value.pId = dat.processInstanceId
      idObj.value.bId = dat.businessId
      if (dat.status !== 2) {
        requestDictMap('goods_billing_method').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList1 = []
            for (const item of resData.data) {
              resList1.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            requestDictMap(
              'jrzh_customer_front_sales_contract_proof_type'
            ).then(res => {
              const resData = res.data
              if (resData.code == 200) {
                // 处理字典数据
                const resList2 = []
                for (const item of resData.data) {
                  resList2.push({
                    label: item.dictValue,
                    value: item.dictKey,
                    id: item.id,
                  })
                }

                // 查询详情
                PRODUCT_APPLI_CATIONS_API.detail(finacCinId.value).then(res => {
                  const resData = res.data
                  if (resData.code == 200) {
                    const dat = resData.data
                    let salesContracts = null
                    let salesContractDetails = null
                    let maxAmount = null
                    let loadTermStart = null
                    let loadTermEnd = null
                    let billingMethod = null
                    if (dat.receivableFinanceCommonVo) {
                      salesContracts =
                        dat.receivableFinanceCommonVo.salesContracts
                      salesContractDetails =
                        dat.receivableFinanceCommonVo.salesContractDetails
                      maxAmount = dat.receivableFinanceCommonVo.maxAmount
                      loadTermStart =
                        dat.receivableFinanceCommonVo.loadTermStart
                      loadTermEnd = dat.receivableFinanceCommonVo.loadTermEnd
                      billingMethod =
                        dat.receivableFinanceCommonVo.billingMethod
                    } else if (dat.orderFinancingFinanceCommonVo) {
                      // salesContracts =
                      //   dat.orderFinancingFinanceCommonVo.orderDatas
                      salesContracts =
                        dat.orderFinancingFinanceCommonVo.financingLimits
                      salesContractDetails =
                        dat.orderFinancingFinanceCommonVo.salesContractDetails
                      maxAmount = dat.orderFinancingFinanceCommonVo.maxAmount
                      loadTermStart =
                        dat.orderFinancingFinanceCommonVo.loadTermStart
                      loadTermEnd =
                        dat.orderFinancingFinanceCommonVo.loadTermEnd
                      billingMethod =
                        dat.orderFinancingFinanceCommonVo.billingMethod
                    }
                    const {
                      repaymentMode,
                      repaymentType,
                      loadTerm,
                      loanUsage,
                      financeNo,
                      annualInterestRate,
                      dailyInterestRate,
                      loadTermUnit: _a,
                    } = dat.financeApply

                    financeNoId.value = financeNo
                    // 质押品数据处理 start
                    const arrData = []
                    let index = 1
                    for (const item of salesContracts) {
                      // 过滤出当前的凭证类型
                      const proofTypeFilter = resList2?.filter(
                        items => items.value == item.proofType
                      )
                      arrData.push({
                        id: item.id,
                        indexs: index,
                        key: index,
                        // contractNo: item.contractNo || item.orderNo,
                        contractNo: item.id || item.orderNo,
                        enterpriseName: item.enterpriseName || '无',
                        proofType: proofTypeFilter?.[0]?.label,
                        // financingAvailableAmount:
                        //   item.financingAvailableAmount || item.orderAmount,
                        financingAvailableAmount:
                          item.limitAmount || item.orderAmount,
                        expireTime: item.expireTime,
                      })
                      index++
                    }
                    // 回显金额
                    if (route.query.goodType === '1') {
                      salesContractDetails.map((item, index) => {
                        arrData[index].amount = item.amount
                        arrData[index].financingAvailableAmount = String(
                          Number(item.amount) +
                            Number(arrData[index].financingAvailableAmount)
                        )
                      })
                    } else {
                      // allNumMoney.value = salesContracts[0]?.orderAmount
                      allNumMoney.value = salesContractDetails[0]?.amount
                    }
                    store.commit('Product/setAppliCations', arrData)
                    // 质押品数据处理 end
                    // 最高融资金额处理 start
                    loanAmount.value = maxAmount
                    // 最高融资金额处理 end
                    // 借多久数据处理 start
                    loanPeriod.value = loadTerm
                    // 借多久数据处理 end
                    // DialogTimeMonth弹窗参数 start
                    loadTermObj.value['loadTermStart'] = loadTermStart
                    loadTermObj.value['loadTermEnd'] = loadTermEnd
                    // DialogTimeMonth弹窗参数 end
                    // 融资用途数据处理 start
                    const purposeLoanFilter = purposeLoanList.value.filter(
                      itemed => itemed.key == loanUsage
                    )[0]
                    purposeLoanData.value['text'] = purposeLoanFilter.value
                    purposeLoanData.value['index'] = purposeLoanFilter.key
                    // 融资用途数据处理 end
                    // DialogReimburSement弹窗参数 start
                    annualInterestRateObj.value = {
                      annualInterestRate: annualInterestRate,
                      dailyInterestRate: dailyInterestRate,
                    }
                    loadTermUnit.value = _a // 1:天；2:期
                    tabBarObj.tabBarIndexVal = repaymentMode // 还款方式key
                    if (repaymentType !== 1) {
                      filterArr.filterTabBar = ['随借随还']
                      filterArr.filterTabBarVal = ['-1']
                    } else {
                      const arrs = []
                      billingMethod.split(',').forEach((item, index) => {
                        // 过滤出当前的计费方式
                        const filtResList = resList1.filter(
                          itemed => itemed.key == item
                        )
                        filterArr.filterTabBar[index] = filtResList[0]?.value
                        filterArr.filterTabBarVal[index] = filtResList[0]?.key
                        arrs.push(filtResList)
                      })
                      const arrsKey = arrs.map(item => item[0]?.key)
                      tabBarObj.tabBarIndex = arrsKey.indexOf(
                        String(repaymentMode)
                      ) // 还款方式index
                    }
                    // DialogReimburSement弹窗参数 end
                    filterNullStr.value = null // 清除请选择文案
                  }
                })
              }
            })
          }
        })
      }
    }
  })
}

// 提交校验
const verifyCheck = type => {
  // 校验必填信息
  if (!appliCationsData.value.length) {
    message.error('未选择质押品')
    return false
  } else if (!allNumMoney.value) {
    message.error('未填写融资金额')
    return false
  } else if (!loanPeriod.value) {
    message.error('未选择融资期限')
    return false
  } else if (!purposeLoanData.value.index) {
    message.error('未选择融资用途')
    return false
  } else if (filterNullStr.value && type) {
    message.error('未选择还款方式')
    return false
  } else {
    return true
  }
}

// 提交成功后的回调事件
const successCalBackFun = (id, msg) => {
  const dataP = {
    id,
  }
  PRODUCT_APPLI_CATIONS_API.getFinanceNo(dataP)
    .then(({ data }) => {
      if (data.success) {
        const { data: resDataC } = data
        if (resDataC.financeNo) {
          message.success(msg)
          sessionStorage.setItem(
            'financingDemandId',
            JSON.stringify(resDataC.id)
          )
          sessionStorage.setItem('financingDemandNo', resDataC.financeNo)
          emit('setCurrentUpFun', 1)
        }
      }
    })
    .catch(() => {
      message.warning('获取融资编号接口异常')
    })
}

// 下一步事件
const nextToPape = () => {
  if (route.query.goodType === '5') {
    nextToPapeDingDan()
    return
  }
  nextToPapeYinShou()
}

// 应收账款下一步事件
const nextToPapeYinShou = () => {
  if (!verifyCheck(true)) return
  if (nextLock.value) return
  nextLock.value = true
  const arrData = []
  appliCationsData.value.map((item, index) => {
    arrData.push({
      amount: allNumMoneyArr.value[index],
      saleContractId: item.id,
    })
  })
  const data = {
    // 应收账款-融资数据
    receivableFinanceDTO: {
      salesContractDetails: arrData, // 销售合同 列表 ----- 手动放款存金额
    },
    // 融资主数据
    financeApply: {
      customerGoodsId: route.query.customerGoodsId,
      goodsId: route.query.goodId,
      loadTerm: loanPeriod.value, // 融资期限
      loadTermUnit: loadTermUnit.value,
      loanUsage: purposeLoanData.value.index, // 融资用途
      repaymentMode: tabBarObj.tabBarIndexVal, // 还款方式 1起步 木有0 ok?
      processInstanceId: idObj.value.pId,
      id: idObj.value.bId,
      financeNo: financeNoId.value,
      goodsType: route.query.goodType,
      lendingMethod: route.query.lendingMethod, //放款方式 自动/手动
      chargeMethod: route.query.chargeMethod, //收款方式 统一清分/独立收取
    },
    goodsType: route.query.goodType,
    lendingMethod: route.query.lendingMethod, //放款方式 自动/手动
    chargeMethod: route.query.chargeMethod, //收款方式 统一清分/独立收取
    costCalculusDto: {
      enterpriseQuotaId: route.query.enterpriseQuotaId,
      chargePoint: 8,
      customerGoodsId: route.query.customerGoodsId,
      totalTerm: loanPeriod.value,
      loanDay: loanPeriod.value,
      startTime: dayjs().format('YYYY-MM-DD'),
    },
  }
  if (lendingMethodDataType.value === '2') {
    // PRODUCT_APPLI_CATIONS_API.submit(data)
    PRODUCT_APPLI_CATIONS_API.financeApplySubmit(data)
      .then(res => {
        const resData = res.data
        if (resData.code == 200) {
          successCalBackFun(resData.data, '提交成功')
          // message.success('提交成功')
          // sessionStorage.setItem(
          //   'financingDemandId',
          //   JSON.stringify(resData.data)
          // )
          // emit('setCurrentUpFun', 1)
        }
      })
      .catch(() => {
        nextLock.value = false
      })
  } else {
    // PRODUCT_APPLI_CATIONS_API.applySave(data)
    PRODUCT_APPLI_CATIONS_API.financeApplySubmit(data)
      .then(res => {
        const resData = res.data
        if (resData.code == 200) {
          successCalBackFun(resData.data, '保存成功')
        }
      })
      .catch(() => {
        nextLock.value = false
      })
  }
}

// 订单融资下一步事件
const nextToPapeDingDan = () => {
  if (!verifyCheck(true)) return
  if (nextLock.value) return
  nextLock.value = true
  const arrData = []
  appliCationsData.value.map((item, index) => {
    arrData.push({
      amount: allNumMoney.value,
      saleContractId: item.id,
    })
  })
  const data = {
    // 应收账款-融资数据
    receivableFinanceDTO: {
      salesContractDetails: arrData, // 销售合同 列表 ----- 手动放款存金额
    },
    // 融资主数据
    financeApply: {
      customerGoodsId: route.query.customerGoodsId,
      goodsId: route.query.goodId,
      loadTerm: loanPeriod.value, // 融资期限
      loadTermUnit: loadTermUnit.value,
      loanUsage: purposeLoanData.value.index, // 融资用途
      repaymentMode: tabBarObj.tabBarIndexVal, // 还款方式 1起步 木有0 ok?
      processInstanceId: idObj.value.pId,
      id: idObj.value.bId,
      financeNo: financeNoId.value,
      goodsType: route.query.goodType,
      lendingMethod: route.query.lendingMethod, //放款方式 自动/手动
      chargeMethod: route.query.chargeMethod, //收款方式 统一清分/独立收取
    },
    goodsType: route.query.goodType,
    lendingMethod: route.query.lendingMethod, //放款方式 自动/手动
    chargeMethod: route.query.chargeMethod, //收款方式 统一清分/独立收取
    costCalculusDto: {
      enterpriseQuotaId: route.query.enterpriseQuotaId,
      chargePoint: 8,
      customerGoodsId: route.query.customerGoodsId,
      totalTerm: loanPeriod.value,
      loanDay: loanPeriod.value,
      startTime: dayjs().format('YYYY-MM-DD'),
    },
  }
  if (lendingMethodDataType.value === '2') {
    // PRODUCT_APPLI_CATIONS_API.submit(data)
    PRODUCT_APPLI_CATIONS_API.financeApplySubmit(data)
      .then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // message.success('提交成功')
          // sessionStorage.setItem(
          //   'financingDemandId',
          //   JSON.stringify(resData.data)
          // )
          // emit('setCurrentUpFun', 1)
          successCalBackFun(resData.data, '提交成功')
        }
      })
      .catch(() => {
        nextLock.value = false
      })
  } else {
    // PRODUCT_APPLI_CATIONS_API.applySave(data)
    PRODUCT_APPLI_CATIONS_API.financeApplySubmit(data)
      .then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // message.success('保存成功')
          // sessionStorage.setItem(
          //   'financingDemandId',
          //   JSON.stringify(resData.data)
          // )
          // emit('setCurrentUpFun', 1)
          successCalBackFun(resData.data, '保存成功')
        }
      })
      .catch(() => {
        nextLock.value = false
      })
  }
}
</script>

<style lang="scss" scoped>
.financing-demand {
  max-width: 1400px;
  position: relative;
  margin: auto;
  box-sizing: border-box;

  .top-line {
    width: 100%;
    // height: 1px;
    display: block;
    background: #f1f2f4;
    margin-top: 40px;
    // margin-bottom: 48px;
  }

  .pledge-box {
    width: 1400px;
    // height: 464px;
    background: #ffffff;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 1px solid #efefef;
    padding: 40px 40px 5px;
    box-sizing: border-box;

    .pledge-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 28px;

      .pledge-text-title {
        height: 32px;
        font-size: 24px;
        @include family-PingFangSC-Semibold;
        font-weight: 500;
        color: #0a1f44;
        line-height: 32px;
      }

      .pledge-Vertical {
        width: 1px;
        height: 22px;
        background: #b5bbc6;
        display: inline-block;
        margin-left: 12px;
        margin-right: 12px;
      }

      .pledge-price-box {
        height: 24px;
        font-size: 16px;
        @include family-PingFangSC-Semibold;
        font-weight: 500;
        color: #8a94a6;
        line-height: 24px;
        display: flex;

        .pledge-num-price {
          :deep(.ant-statistic-content) {
            font-size: 16px;
            font-weight: 500;
            color: #8a94a6;
            line-height: 24px;

            .ant-statistic-content-prefix {
              margin-right: -1px;
            }
          }
        }
      }
    }

    .tradefor-information {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;

      .tradefor-for {
        margin-bottom: 38px;
        margin-right: 38px;

        & > .base-card-item {
          margin-top: 0;
          margin-right: 0;
        }

        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }

  .financing-amount {
    display: flex;
    flex-direction: column;

    .financing-top-amount {
      margin-top: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: context-menu;

      .financing-left-amount {
        width: 680px;
        height: 262px;
        background: #ffffff;
        box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
        border-radius: 16px;
        border: 1px solid #efefef;
        padding: 40px 40px 24px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
        cursor: context-menu;

        .pledge-header {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 5px;

          .pledge-text-title {
            height: 32px;
            font-size: 24px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #0a1f44;
            line-height: 32px;
          }

          .pledge-Vertical {
            width: 1px;
            height: 22px;
            background: #b5bbc6;
            display: inline-block;
            margin-left: 12px;
            margin-right: 12px;
          }

          .pledge-price-box {
            height: 24px;
            font-size: 16px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #8a94a6;
            line-height: 24px;
            display: flex;

            .pledge-num-price {
              :deep(.ant-statistic-content) {
                font-size: 16px;
                font-weight: 500;
                color: #8a94a6;
                line-height: 24px;

                .ant-statistic-content-prefix {
                  margin-right: -1px;
                }
              }
            }
          }
        }

        .financing-money-all {
          :deep(.ant-statistic-content) {
            .ant-statistic-content-prefix {
              font-size: 40px;
            }

            .ant-statistic-content-value {
              font-size: 64px;
            }
          }
        }

        .line {
          width: 100%;
          height: 1px;
          display: block;
          background: #f1f2f4;
          margin-top: 10px;
        }

        .financing-hint {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin-top: 24px;

          .financing-text-hint {
            height: 24px;
            font-size: 16px;
            @include family-PingFangSC-Semibold;
            font-weight: 400;
            color: #8a94a6;
            line-height: 24px;
            margin-left: 4px;
          }
        }

        &::before {
          content: '';
          display: inline-block;
          position: absolute;
          width: 105px;
          height: 145px;
          top: -25px;
          left: -28px;
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.14;
          border-radius: 150px;
          filter: blur(30px);
        }

        &::after {
          content: '';
          display: inline-block;
          position: absolute;
          width: 210px;
          height: 205px;
          bottom: -74px;
          right: -49px;
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.14;
          border-radius: 150px;
          filter: blur(13px);
        }
      }

      .financing-right-amount {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;
        height: 262px;

        .exhibition-box {
          width: 680px;
          height: 112px;
          background: #ffffff;
          box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
          border-radius: 16px;
          border: 1px solid #efefef;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 40px;
          box-sizing: border-box;
          cursor: pointer;

          .exhibition-text {
            height: 32px;
            font-size: 24px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #0a1f44;
            line-height: 32px;
          }

          .exhibition-menu {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .exhibition-text-menu {
              height: 32px;
              font-size: 24px;
              @include family-PingFangSC-Semibold;
              font-weight: 500;
              color: #8a94a6;
              line-height: 32px;
            }
          }
        }
      }
    }

    .financing-bottom-amount {
      .exhibition-box {
        width: 100%;
        height: 112px;
        background: #ffffff;
        box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
        border-radius: 16px;
        border: 1px solid #efefef;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        box-sizing: border-box;
        margin-top: 40px;
        cursor: pointer;

        .exhibition-text {
          height: 32px;
          font-size: 24px;
          @include family-PingFangSC-Semibold;
          font-weight: 500;
          color: #0a1f44;
          line-height: 32px;
        }

        .exhibition-menu {
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .exhibition-text-menu {
            font-size: 24px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #8a94a6;
            line-height: 32px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-end;

            .null-box {
              text-align: right;
              color: #8a94a6;
              font-weight: 500;
            }

            .text-box {
              width: 82px;
              height: 28px;
              font-size: 20px;
              font-weight: 400;
              color: #0a1f44;
              line-height: 28px;
            }

            .blue-text-box {
              width: 96px;
              height: 24px;
              font-size: 16px;
              font-weight: 400;
              color: #0d55cf;
              line-height: 24px;
            }
          }
        }
      }
    }
  }

  .financing-menu-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    margin-top: 48px;
    margin-bottom: 48px;

    :deep(.n-button) {
      width: 400px;
      height: 40px;
    }
  }
}
</style>
