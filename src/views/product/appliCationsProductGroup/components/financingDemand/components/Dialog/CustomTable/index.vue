<template>
  <div class="product-detail-table">
    <Table ref="ProductDetailTableRef" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'CustomTableIndex',
}
</script>
<script lang="ts" setup>
import { ref } from 'vue'
import Table from './table-ding-dan.vue'

const ProductDetailTableRef: any = ref(null)

const onLoad = () => {
  ProductDetailTableRef.value.initTableData()
}

const setSelectedDataInStore = () => {
  ProductDetailTableRef.value.setSelectedDataInStore()
}

defineExpose({
  onLoad,
  setSelectedDataInStore,
})
</script>

<style lang="scss" scoped>
.product-detail-table {
  width: 100%;
  position: relative;
  margin: 2px auto;
  max-width: 1400px;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 16px;
}
</style>
