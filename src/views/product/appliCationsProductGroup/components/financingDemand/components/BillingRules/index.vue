<template>
  <div class="wrap">
    <div class="title">说明</div>
    <div class="row-text" v-if="labelList.includes('等额本息')">
      <div class="cont">
        <span class="field">等额本息</span>
      </div>
      <p class="help">
        借款人每月归还的金额相同，其中包含了每月全部的贷款利率和部分本金，随着本金的归还，每月还款额中的本金比重逐月递增，利率比重逐月递减。
      </p>
    </div>

    <div class="row-text" v-if="labelList.includes('等额本金')">
      <div class="cont">
        <span class="field">等额本金</span>
      </div>
      <p class="help">
        借款人将本金平均分摊到每个月内归还，同时付清上一还款日至本次还款日之间的利息，此还款方式相对等额本息而言，总利率支出较低，但前期支付的本金和利率较多，还款负担逐月递减。
      </p>
    </div>

    <div class="row-text" v-if="labelList.includes('先息后本')">
      <div class="cont">
        <span class="field">先息后本</span>
      </div>
      <p class="help">
        借款人在贷款期限内分期归还利率，但不归还本金。在到期后，一次性归还本金。
      </p>
    </div>

    <div class="row-text" v-if="labelList.includes('随借随还')">
      <div class="cont">
        <span class="field">随借随还</span>
      </div>
      <p class="help">借款人可随时归还所贷金额，按天计息，用多久付多久的利息</p>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'RepaymentBillingRulesIndex',
}
</script>
<script lang="ts" setup>
import { inject, ref } from 'vue'

const labelList = ref([])

const labelListFunc = inject('labelListFunc')

labelList.value = labelListFunc()
</script>

<style lang="scss" scoped>
.wrap {
  width: 300px;
  color: #ffffff;
}

.title {
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
}

.row-text {
  padding-bottom: 8px;
  &:last-child {
    padding-bottom: 0;
  }
  .cont {
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
    .field {
      font-size: 12px;
      font-family: PingFangSC-Semibold, PingFang SC;
    }
    .val {
      font-size: 12px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
    }
  }
  .help {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    opacity: 0.8;
  }
}
</style>
