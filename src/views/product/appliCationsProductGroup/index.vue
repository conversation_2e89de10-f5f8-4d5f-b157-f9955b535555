<template>
  <div class="appli-cations">
    <ArticleSetps
      :info="'融资申请'"
      :arrData="setpHeaderData"
      :current="current"
      :widths="'1000px'"
    />
    <!-- 填写融资需求 -->
    <FinancingDemand
      v-if="financingDemand"
      :type="businessApprovalNum"
      @setCurrentUpFun="getByBusinessIdAndTypeApi"
    />
    <!-- 调整方案审批 -->
    <planBusinessApproval
      v-else-if="planAdjustPageType"
      :status="status"
      :message="message"
      @goBack="currentUpFun"
    />
    <!-- 融资确认 -->
    <div v-else-if="financingToConfirm">
      <FinancingToConfirm
        :finaContractSigningType="finaContractSigningType"
        @setCurrentUpFun="currentUpFun"
      />
      <ContractSigning
        v-if="finaContractSigningType"
        signNodeProps="9-1"
        @setCurrentUpFun="currentUpFun"
      />
    </div>
    <!-- 缴纳费用 -->
    <div v-else-if="otherExpenses">
      <ZiDongNewotherExpenses
        ref="ziDongNewotherExpensesRef"
        @setCurrentUpFun="currentUpFun"
      />
      <ContractSigning
        v-if="otherContractSigningType"
        signNodeProps="9-2"
        @setCurrentUpFun="currentUpFun"
        @previousToPape="previousToPape"
        @nextToPape="nextToPape"
      />
    </div>
    <!-- 业务审批 -->
    <!-- backIndex \ true:1 ; false:2  -->
    <BusinessApproval v-else-if="businessApprovalType" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'appliCationsProductGroup',
}
</script>
<script setup lang="ts">
import ArticleSetps from '@/components/articleSetps/index.vue'
import FinancingDemand from './components/financingDemand/index.vue'
import FinancingToConfirm from './components/financingToConfirm/index.vue'
import ContractSigning from './components/contractSigning/index.vue'
import ZiDongNewotherExpenses from './components/ziDongNewotherExpenses/index.vue'
import BusinessApproval from './components/businessApproval.vue'
import planBusinessApproval from './components/planBusinessApproval.vue'

import {
  ref,
  onMounted,
  watch,
  onUnmounted,
  computed,
  reactive,
  provide,
  readonly,
} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { PRODUCT_VERIFY_API } from '@/api/index.js'
const route = useRoute()
const router = useRouter();

const ziDongNewotherExpensesRef = ref()

const financingDemand = ref(true)
const financingToConfirm = ref(false)
const otherExpenses = ref(false)
const businessApprovalType = ref(false)
const planAdjustPageType = ref(false)
const finaContractSigningType = ref(false)
const otherContractSigningType = ref(false)

const setpHeaderData = ref<{ id: number; name: string }[]>([])
const current = ref(0) // 进度
const businessApprovalNum = ref(5)
// const onlineAndOfflinePayment = ref(1) // 1:线下，2：线上

// 给合同生成模板用的数据
const repaymentContractData = reactive<any>({
  financeAmount: 0, // 融资金额
  annualInterestRate: 0, // 年利率
  totalTerm: 0, // 总期数
  startTime: '', // 开始时间
  refundType: 0, // 还款类型 '1','2','3'
  loadTermUnit: 0, // 1-天,2-期
  initial: true, // 是否初始数据
})
provide('repaymentContractData', readonly(repaymentContractData))

// 修改合同生成模板数据
const setRepaymentContractDataFun = (datas: any) => {
  for (const key in repaymentContractData) {
    if (datas[key]) {
      repaymentContractData[key] = datas[key] || void 0
    }
  }
  delete repaymentContractData.initial
}
provide('setRepaymentContractDataFun', setRepaymentContractDataFun)

// 变更流程进度
const currentUpFun = (val: number) => {
  current.value = val

  financingDemand.value = val == 0
  financingToConfirm.value = val == 1 && ![5, 6, 7].includes(node.value)
  otherExpenses.value = val == 2
  businessApprovalType.value = val == 3
  planAdjustPageType.value = val == 1 && [5, 6, 7].includes(node.value)
}

const status = ref(0)
const message = ref('')

const node = ref(1)

// 查询流程进度
const getByBusinessIdAndTypeApi = async (planIdPar?: string) => {
  console.log('--------planId', planIdPar)
  const planId =
    planIdPar || sessionStorage.getItem('planId') || route.query.planId

  // 初始化
  if (!planId) {
    currentUpFun(0)
    return
  }
  // 获取进度
  const { data: resData } = await PRODUCT_VERIFY_API.getByBusinessIdAndType3(
    planId
  )
  if (resData.code != 200) return

  const data = resData.data

  if (planIdPar) {
    // 同时拥有planNo和planId
    // 更新方法自调
    await router.replace({ name: 'appliCationsProductGroup', query: { goodId: route.query.goodId, planId: planIdPar, planNo: data.planNo } })
  }

  // 获取不为空的融资申请id
  const financeApplyIds = (data.financeApplyIds || []).reduce(
    (pre: string[], cur: string) => (cur && pre.push(cur), pre),
    []
  )

  sessionStorage.setItem('financingIds', financeApplyIds)
  sessionStorage.setItem('planNo', data.planNo)

  node.value = data.node

  currentUpFun(data.node - 1)

  status.value = data.applyStatus
  message.value = data.rejectReason

  console.log('-----------', planIdPar, data.node, current.value)

  // 方案调整审批
  if ([5, 6, 7].includes(data.node)) {
    console.log('我进来了')
    setpHeaderData.value = [
      { id: 1, name: '填写融资需求' },
      { id: 5, name: '方案调整审批' },
      { id: 2, name: '融资确认' },
      { id: 3, name: '缴纳费用' },
      { id: 4, name: '业务审批' },
    ]
    currentUpFun(1)
  } else {
    console.log('我出来了')
    setpHeaderData.value = setpHeaderData.value.filter(item => item.id != 5)
    console.log('setpHeaderData.value', setpHeaderData.value)
  }

  // 更新方案，状态为废弃的时候调用
  if (resData.data.status == 3) {
    PRODUCT_VERIFY_API.getNewPlan(planId).then(({ data: resData }) => {
      sessionStorage.setItem('planId', resData.data)

      // 。。。更新路由
      // 由于刷新页面的时候跟初始化的接口会一起调用，导致更改node被覆盖，因此延迟调用
      getByBusinessIdAndTypeApi(resData.data)
    })
  }
}

// 根据放款方式变化流程
const setlendingMethodType = () => {
  setpHeaderData.value = [
    { id: 1, name: '填写融资需求' },
    { id: 2, name: '融资确认' },
    { id: 3, name: '缴纳费用' },
    { id: 4, name: '业务审批' },
  ]
  finaContractSigningType.value = false
  otherContractSigningType.value = true
  getByBusinessIdAndTypeApi()
}

// watch(
//   () => current.value,
//   val => {
//     console.log('----------current______', current.value, val)
//     // 自动放款-收平台费用
//     financingDemand.value = val == 0
//     financingToConfirm.value = val == 1 && ![5, 6, 7].includes(node.value)
//     otherExpenses.value = val == 2
//     businessApprovalType.value = val == 3
//     planAdjustPageType.value = val == 1 && [5, 6, 7].includes(node.value)
//   },
//   { immediate: true }
// )

onMounted(() => {
  setlendingMethodType()
})

// 缴纳费用上一步
const previousToPape = () => {
  ziDongNewotherExpensesRef.value.previousToPape()
}

// 缴纳费用下一步
const nextToPape = () => {
  ziDongNewotherExpensesRef.value.nextToPape()
}

onUnmounted(() => {
  sessionStorage.removeItem('financingDemandId')
  sessionStorage.removeItem('planId')
  sessionStorage.removeItem('planNo')
  sessionStorage.removeItem('financingIds')
  sessionStorage.removeItem('financeApplyIds')
})
</script>

<style lang="scss" scoped>
.appli-cations {
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  padding-top: 40px;
  flex: 1;
}
</style>
