<template>
  <div class="appli-cations">
    <ArticleSetps
      :succerssType="succerssPageType"
      info="动产质押放款申请"
      :arrData="setpHeaderData"
      :current="current"
      :widths="'1000px'"
    />
    <div v-if="financingToConfirm">
      <FinancingToConfirm @setCurrentUpFun="currentUpFun" />
      <!-- <ContractSigning @setCurrentUpFun="currentUpFun" /> -->
    </div>
    <template v-else-if="otherExpenses">
      <DongChanNewOtherExpenses @setCurrentUpFun="currentUpFun" />
      <!-- <OtherExpenses
        v-if="onlineAndOfflinePayment === 1"
        @setCurrentUpFun="currentUpFun"
      />
      <OtherExpensesOnlinePay v-else @setCurrentUpFun="currentUpFun" /> -->
    </template>
    <BusinessApproval
      :type="23"
      :bsId="route.query.id"
      v-else-if="businessApprovalType"
      @goBack="currentUpFun"
    />
    <SuccerssPage v-else-if="succerssPageType" :bsId="route.query.id" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'appliCations',
}
</script>
<script setup lang="ts">
import ArticleSetps from '@/components/articleSetps/index.vue'
import FinancingToConfirm from './components/financingToConfirm/index.vue'
// import ContractSigning from '@/views/product/loanApplication/components/contractSigning/index.vue'
// import OtherExpenses from './components/otherExpenses/index.vue'
// import OtherExpensesOnlinePay from './components/otherExpensesOnlinePay/index.vue'
import DongChanNewOtherExpenses from './components/dongChanNewOtherExpenses'
import BusinessApproval from '@/views/product/component/businessApproval.vue'
import SuccerssPage from './components/succerssPage/index.vue'

import {
  ref,
  onMounted,
  watch,
  onUnmounted,
  provide,
  reactive,
  readonly,
} from 'vue'
import { useRoute } from 'vue-router'
import { PRODUCT_VERIFY_API } from '@/api/index.js'
const route = useRoute()
const financingToConfirm = ref(true)
const otherExpenses = ref(false)
const businessApprovalType = ref(false)
const succerssPageType = ref(false)
const setpHeaderData = [
  { id: 1, name: '融资确认' },
  { id: 2, name: '缴纳费用' },
  { id: 3, name: '业务审批' },
  { id: 4, name: '放款成功' },
]
// 给合同生成模板用的数据
const repaymentContractData = reactive({
  financeAmount: 0, // 融资金额
  annualInterestRate: 0, // 年利率
  totalTerm: 0, // 总期数
  startTime: '', // 开始时间
  refundType: 0, // 还款类型 '1','2','3'
  loadTermUnit: 0, // 1-天,2-期
  initial: true, // 是否初始数据
})
provide('repaymentContractData', readonly(repaymentContractData))

// 修改合同生成模板数据
const setRepaymentContractDataFun = datas => {
  for (const key in repaymentContractData) {
    if (datas[key]) {
      repaymentContractData[key] = datas[key] || void 0
    }
  }
  delete repaymentContractData.initial
}
provide('setRepaymentContractDataFun', setRepaymentContractDataFun)
const current = ref(0) // 进度
const onlineAndOfflinePayment = ref(1) // 1:线下，2：线上

// 变更流程进度
const currentUpFun = val => {
  current.value = val
}

// 查线下还是线上支付
;(() => {
  const paramsD = {
    goodsId: route.query.goodId,
    accountType: 1, // 账户类型 1.平台账户、2.资方账户
  }
  PRODUCT_VERIFY_API.getGoodsRelationAccount(paramsD).then(({ data }) => {
    const { data: resData } = data
    if (data.success && resData) {
      onlineAndOfflinePayment.value = resData.platformCostPayMode
    }
  })
})()

// 查询流程进度
const getByBusinessIdAndTypeApi = () => {
  const params = {
    businessId: route.query.id,
    type: 23,
  }
  PRODUCT_VERIFY_API.getByBusinessIdAndType(params)
    .then(res => {
      const resData = res.data
      if (resData.code == 200 && !resData.data) {
        currentUpFun(0)
      } else if (resData.code == 200 && resData.data) {
        currentUpFun(resData.data.progress)
      }
    })
    .catch(({ msg, hideMsgFunc }) => {})
}
getByBusinessIdAndTypeApi()

onMounted(() => {
  watch(
    () => current.value,
    val => {
      if (val == 0) {
        financingToConfirm.value = true
        otherExpenses.value = false
        businessApprovalType.value = false
        succerssPageType.value = false
      } else if (val == 1) {
        financingToConfirm.value = false
        otherExpenses.value = true
        businessApprovalType.value = false
        succerssPageType.value = false
      } else if (val == 2) {
        financingToConfirm.value = false
        otherExpenses.value = false
        businessApprovalType.value = true
        succerssPageType.value = false
      } else if (val == 3) {
        financingToConfirm.value = false
        otherExpenses.value = false
        businessApprovalType.value = false
        succerssPageType.value = true
      }
    },
    { immediate: true }
  )
})

onUnmounted(() => {
  sessionStorage.removeItem('financingDemandId')
})
</script>

<style lang="scss" scoped>
.appli-cations {
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  padding-top: 40px;
  flex: 1;
}
</style>
