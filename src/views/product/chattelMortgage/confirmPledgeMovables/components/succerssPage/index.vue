<template>
  <div>
    <!-- 申请成功提示 -->
    <div v-if="successType" class="product-businessApproval">
      <MySvgIcon
        icon-class="icon-chenggong"
        style="color: #0bb07b; font-size: 64px"
      ></MySvgIcon>
      <span class="under-review">放款成功</span>
      <span class="message-span">您提交的信息已通过审核，放款申请成功！</span>
      <div class="gohome">
        <a-button
          size="large"
          type="primary"
          :block="true"
          shape="round"
          @click="goToCred()"
          >查看详情</a-button
        >
      </div>
    </div>
    <!-- 申请失败提示 -->
    <div v-else class="product-businessApproval">
      <MySvgIcon
        icon-class="icon-shibai"
        style="color: #f03d3d; font-size: 64px"
      ></MySvgIcon>
      <span class="under-review">放款失败</span>
      <span class="message-span">{{ fullMessage }}</span>
      <div class="jingruiit-accounts">
        <img :src="wxImg" alt="" />
      </div>
      <div class="gohome2">
        <a-button
          size="large"
          type="primary"
          :block="true"
          shape="round"
          :ghost="true"
          @click="goHome()"
          >返回首页</a-button
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SetpsHeader',
}
</script>
<script setup>
// import NoData from '@/views/user/components/noData.vue'
import { ref } from 'vue'
import { HOMEAPI, PRODUCT_VERIFY_API } from '@/api/index'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()

const props = defineProps({
  bsId: {
    type: String,
  },
})

const customerGoodsId = ref(null)

const goToCred = () => {
  router.push({
    name: 'financingDetails',
    query: {
      id: props.bsId,
      goodId: route.query.goodId,
      goodType: route.query.goodType,
      customerGoodsId: customerGoodsId.value,
      lendingMethod: 2, // 手动放款写死
      chargeMethod: 2, // 手动放款写死
    },
  })
}
const wxImg = ref('')
const successType = ref(true)
const fullMessage = ref()

HOMEAPI.getCecList().then(res => {
  // 获取微信公众号img数据
  const resData = res.data
  if (resData.code == 200) {
    for (const item of resData.data) {
      if (item.qrCode) {
        wxImg.value = item.qrCode
      }
    }
  }
})

const params = {
  businessId: props.bsId || route.query.goodId,
  type: 6,
}
// 获取流程状态是否通过
PRODUCT_VERIFY_API.getByBusinessIdAndType(params)
  .then(res => {
    const resData = res.data
    if (resData.code == 200 && resData.data) {
      const variablesParams = {
        processInstanceId: resData.data.processInstanceId,
      }
      PRODUCT_VERIFY_API.variables(variablesParams).then(resed => {
        const resedData = resed.data
        if (resedData.code == 200) {
          customerGoodsId.value = resedData.data.financeApply.customerGoodsId
        }
      })
      switch (resData.data.status) {
        case 3:
          successType.value = true
          break
        case 2:
          successType.value = false
          break
      }
      // 如果时被驳回就查找驳回原因
      if (resData.data.processInstanceId && resData.data.status == 2) {
        PRODUCT_VERIFY_API.selectProcessStatus({
          processInstanceId: resData.data.processInstanceId,
        }).then(res => {
          const resData = res.data
          if (resData.code == 200) {
            fullMessage.value = resData.data.commentList[0].fullMessage
          }
        })
      }
    }
  })
  .catch(({ msg, hideMsgFunc }) => {
    // console.log(data)
  })

const goHome = () => {
  router.push({ name: 'Home' })
}
</script>

<style lang="scss" scoped>
.product-businessApproval {
  position: relative;
  max-width: 1400px;
  margin: auto;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  .product-businessApproval-flagstaff {
    width: 100%;
    height: 2px;
    background: #f1f2f4;
    margin-top: 39px;
  }

  & > #my-svg-icons {
    margin-top: 60px;
  }

  .under-review {
    font-size: 24px;
    @include family-PingFangSC-Semibold;
    font-weight: 600;
    color: #0a1f44;
    line-height: 32px;
    margin-top: 12px;
  }

  .under-money {
    :deep(.ant-statistic-content-value > span) {
      font-size: 45px;
      @include family-CoreSansD65Heavy;
      color: #031222;
    }

    .money-prefix {
      font-size: 20px;
      @include family-CoreSansD65Heavy;
      color: #031222;
    }
  }

  .message-span {
    font-size: 14px;
    @include family-PingFangSC-Medium;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
    margin-top: 12px;
  }

  .capital-information {
    width: 400px;
    height: 240px;
    background: #f8f9fb;
    border-radius: 8px;
    margin-top: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .capital-information-item {
      margin-bottom: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      &:last-child {
        margin-bottom: 0;
      }

      & > span:first-child {
        width: 30%;
        font-size: 14px;
        @include family-PingFangSC-Medium;
        font-weight: 500;
        color: #8a94a6;
        margin-right: 24px;
        display: inline-block;
        box-sizing: border-box;
        text-align: right;
      }
      & > span:last-child {
        width: 70%;
        font-size: 14px;
        @include family-PingFangSC-Medium;
        font-weight: 500;
        color: #0a1f44;
        display: inline-block;
        box-sizing: border-box;
      }
    }
  }

  .jingruiit-accounts {
    width: 196px;
    height: 196px;
    border-radius: 8px;
    border: 1px solid #efefef;
    margin-top: 24px;
    overflow: hidden;

    & > img {
      width: 100%;
      object-fit: contain;
    }
  }

  .gohome {
    margin-top: 32px;

    .ant-btn-round.ant-btn-lg {
      padding: 12px 49px;
      height: 48px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      border-color: #0c66ff;

      font-size: 16px;
      @include family-PingFangSC-Medium;
      font-weight: 500;
      color: #fff;
      line-height: 24px;
    }

    .ant-btn-primary {
      background-color: #0c66ff;
    }
  }

  .gohome2 {
    margin-top: 32px;

    .ant-btn-round.ant-btn-lg {
      padding: 12px 49px;
      height: 48px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      border-color: #0c66ff;

      font-size: 16px;
      @include family-PingFangSC-Medium;
      font-weight: 500;
      color: #0d55cf;
      line-height: 24px;
    }

    // .ant-btn-primary {
    //   background-color: #0c66ff;
    // }
  }
}
</style>
