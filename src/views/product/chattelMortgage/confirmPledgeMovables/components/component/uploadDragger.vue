<template>
  <a-upload-dragger
    class="upload-wrapper"
    accept=".jpg,.jpeg,.png,.pdf"
    action="/api/blade-resource/oss/endpoint/put-file-attach"
    :headers="headersData"
    :progress="progress"
    v-model:fileList="fileList"
    @reject="message.error('不支持的文件格式! 请上传支持的图片格式或者pdf文件')"
    @change="handleChange"
    @preview="handlePreview"
  >
    <div class="text-box">
      <div>
        <MySvgIcon
          :icon-class="'icon-dakaiwenjianjia'"
          style="fill: #0d55cf; font-size: 55px"
        />
      </div>
      <p class="drop-text">上传支付凭证：可点击、可拖拽</p>
      <p class="drop-extension">支持扩展名：.jpg .jpeg .png .pdf</p>
    </div>
    <template #removeIcon>
      <MySvgIcon
        :icon-class="'icon-guanbi'"
        style="fill: #758196; font-size: 20px"
      />
    </template>
  </a-upload-dragger>
  <PdfView ref="pdfView" />
</template>

<script>
export default {
  name: 'uploadDragger',
}
</script>
<script setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import PdfView from '@/components/FilePreview/index.vue'
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import { useStore } from 'vuex'
const store = useStore()

const emit = defineEmits(['setfileList'])

const props = defineProps({
  arrData: {
    type: Array,
    required: false,
  },
})

watch(
  () => props.arrData,
  val => {
    fileList.value = val
  },
  { immediate: false, deep: true }
)

const fileList = ref([])
const pdfView = ref(null)
const headersData = ref({
  'Blade-Auth': `bearer ${store.getters['Auth/token']}`,
})

const handlePreview = file => {
  const targetUrl = file.url
  if (targetUrl.endsWith('.pdf')) {
    pdfView.value.handleOpen(targetUrl)
  } else {
    viewerApi({
      options: {
        toolbar: false,
        navbar: false,
        title: false,
      },
      images: [targetUrl],
    })
  }
}

const handleChange = info => {
  const status = info.file.status
  const res = info.file.response
  if (status === 'done' && res.code === 200) {
    const dat = res.data
    for (const item of fileList.value) {
      if (item.lastModified === info.file.lastModified) {
        item.attachId = dat.attachId
        item.url = dat.link
      }
    }
    emit('setfileList', fileList.value)
  } else if (status === 'error') {
    message.error('文件上传失败')
  } else if (status === 'removed') {
    emit('setfileList', fileList.value)
  }
}

const progress = {
  strokeColor: {
    '0%': '#8F6BFF',
    '100%': '#4567FF',
  },
  strokeWidth: 3,
  format: percent => `${parseFloat(percent.toFixed(2))}%`,
  class: 'test',
}
</script>

<style lang="scss" scoped>
.upload-wrapper {
  .text-box {
    padding: 32px 80px;
    box-sizing: border-box;

    .drop-text {
      height: 24px;
      font-size: 16px;
      font-weight: 500;
      color: #53627c;
      line-height: 24px;
      margin-top: 16px;
    }

    .drop-extension {
      height: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #8a94a6;
      line-height: 20px;
      margin-top: 4px;
    }
  }
}
</style>
