<template>
  <div class="dongChanNew-other-expenses">
    <span class="top-line" />
    <Feiyongwaihezi ref="feiyongwaiheziRef" />
    <div class="menu-bottom-box">
      <span>
        <NButton
          class="blue button-item"
          style="width: 188px; height: 48px"
          :bordered="true"
          round
          @click="previousToPape"
        >
          <span class="desc">上一步</span>
        </NButton>
      </span>
      <span>
        <NButton
          class="blue button-item primary"
          style="width: 188px; height: 48px"
          :bordered="true"
          type="info"
          round
          @click="nextToPape"
        >
          <span class="desc">立即提交</span>
        </NButton>
      </span>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'dongChanNewOtherExpenses',
}
</script>
<script setup lang="ts">
import Feiyongwaihezi from '@/components/feiyongxiaoheizi'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { NButton } from 'naive-ui'
import { PRODUCT_APPLI_CATIONS_API } from '@/api/index'

const route = useRoute()

const emit = defineEmits(['setCurrentUpFun'])

const feiyongwaiheziRef = ref(null)
const lockSubmit = ref(true)

// 上一步
const previousToPape = () => {
  emit('setCurrentUpFun', 0)
}

// 立即提交事件
const nextToPape = () => {
  if (!feiyongwaiheziRef.value.exposeFeiyongListFun()) {
    return
  }
  if (!lockSubmit.value) return
  lockSubmit.value = false
  const dataP = {
    financeNo: route.query.financeNo,
    expenseInfoExpenseList: feiyongwaiheziRef.value.exposeFeiyongListFun(),
  }
  PRODUCT_APPLI_CATIONS_API.loanApply(dataP)
    .then(({ data }) => {
      if (data.success) {
        // const { data: resData } = data
        emit('setCurrentUpFun', 2)
      }
    })
    .catch(() => {
      lockSubmit.value = true
    })
}
</script>

<style lang="scss" scoped>
.dongChanNew-other-expenses {
  max-width: 1400px;
  position: relative;
  margin: auto;
  box-sizing: border-box;

  .top-line {
    width: 100%;
    // height: 1px;
    display: block;
    background: #f1f2f4;
    margin-top: 40px;
    // margin-bottom: 40px;
  }

  .menu-bottom-box {
    text-align: center;
    margin-bottom: 48px;

    & span {
      margin-right: 24px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
