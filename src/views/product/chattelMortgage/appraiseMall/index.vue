<template>
  <div class="accounts-receivable-list">
    <div class="accounts-box">
      <header>
        <h1 class="he-title">动产质押</h1>
        <h4 class="he-info">汇聚多个资方，多种融资选择，融资方案多样化！</h4>
      </header>
      <article>
        <div class="screen-box">
          <!-- filterTabBar -->
          <div class="screen-left-box">
            <LabelBar
              :labelList="labelList"
              :state="tagIndex"
              @switch="handleSwitch"
            />
            <div class="screen-bottom-left-box">
              共查询到 {{ totalNum }} 个产品
            </div>
          </div>
          <div class="screen-right-box">
            <!-- 选择还款方式
            <div class="modeOfRepayment-select">
              <a-select
                v-model:value="filterBox.modeOfRepayment"
                show-search
                placeholder="选择还款方式"
                :options="filterBox.modeselectOptions"
                size="large"
                :filter-option="filterOption"
              >
                <template #suffixIcon>
                  <MySvgIcon
                    icon-class="icon-xiajiantou"
                    style="font-size: 20px; fill: #344564"
                  />
                </template>
              </a-select>
            </div>
            选择资金方
            <div class="theFundingParty-select">
              <a-select
                v-model:value="filterBox.theFundingParty"
                show-search
                placeholder="选择资金方"
                :options="filterBox.theFunselectOptions"
                size="large"
                :filter-option="filterOption"
              >
                <template #suffixIcon>
                  <MySvgIcon
                    icon-class="icon-xiajiantou"
                    style="font-size: 20px; fill: #344564"
                  />
                </template>
              </a-select>
            </div> -->
            <!-- 输入供应商 -->
            <div class="keywordQuery-input" style="margin-right: 24px">
              <a-input
                class="large"
                style="border-radius: 4px"
                placeholder="输入供应商"
                v-model:value="filterBox.supplier"
                @change="handleChange"
              >
                <!-- <template #suffix>
                  <MySvgIcon
                    icon-class="icon-sousuo1"
                    style="font-size: 20px; fill: #344564; cursor: pointer"
                  />
                </template> -->
              </a-input>
            </div>
            <!-- 输入商品名称 -->
            <div class="keywordQuery-input">
              <a-input
                class="large"
                style="border-radius: 4px"
                placeholder="输入商品名称"
                v-model:value="filterBox.productName"
                @change="handleChange"
              >
              </a-input>
            </div>
            <!-- buttom menu -->
            <div class="bottom-menu">
              <!-- <NButton class="blue button-item">搜索</NButton> -->
              <NButton class="blue button-item" @click="handleReset">
                重置
              </NButton>
            </div>
          </div>
        </div>
      </article>
      <article>
        <ProductsMining
          :ProductsMiningListData="ProductsMiningListData"
          :boxLoaing="boxLoaing"
        />
        <div class="loading-text-box" v-show="loadingTypeText">
          正在努力加载中…
        </div>
      </article>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'accountsReceivableList',
}
</script>
<script setup lang="ts">
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import ProductsMining from './components/productsMining'
import { NButton } from 'naive-ui'

import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { requestDictMap } from '@/api/common/index'
import { HOMEAPI } from '@/api/index'
import { useStore } from 'vuex'
const store = useStore()

// taberBar筛选数据
const labelList = ['全部', '可评估', '热门', '新品']
// 筛选box参数
const filterBox = reactive({
  // modeOfRepayment: null,
  // modeselectOptions: [],
  // theFundingParty: null,
  // theFunselectOptions: [],
  tag: 0,
  supplier: void 0,
  productName: void 0,
})
const pageObj = reactive({
  current: 1, // 当前页码
  size: 12, // 每页条数
})
const totalNum = ref(0)
const tagIndex = ref(void 0)
const ProductsMiningListData = ref([])

const loadingTypeText = ref(false)
const boxLoaing = ref(true)

const tenantId = computed(() => store.getters['Auth/tenantId'])

// 无限滚动
const intersectionObserver = new IntersectionObserver(entries => {
  // 如果不可见，就返回
  if (entries[0].intersectionRatio <= 0) return
  setTimeout(() => {
    pageObj.current++
    purchaseCommodityListFun(true)
  }, 500)
})

// 开始观察
onMounted(() => {
  intersectionObserver.observe(document.querySelector('.loading-text-box'))
})

onUnmounted(() => {
  // 关闭观察
  intersectionObserver.disconnect()
})

// taberBar选择事件
const handleSwitch = targetIndex => {
  filterBox.tag = targetIndex
  tagIndex.value = targetIndex
  pageObj.current = 1
  purchaseCommodityListFun()
}

// 输入框change事件
const handleChange = () => {
  pageObj.current = 1
  purchaseCommodityListFun()
}

// 重置按钮事件
const handleReset = () => {
  tagIndex.value = 0
  pageObj.current = 1
  filterBox.tag = 0
  filterBox.supplier = void 0
  filterBox.productName = void 0
  purchaseCommodityListFun()
}
// 选择框筛选事件
// const filterOption = (input: string, option: any) =>
//   option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0

// 请求
const purchaseCommodityListFun = type => {
  if (!tenantId.value) {
    setTimeout(() => {
      purchaseCommodityListFun()
    }, 200)
    return
  }
  if (!type) {
    boxLoaing.value = true
  }
  const params = {
    type: 4, // 查询动产业务
    status: 1, // 上架状态，值固定为1
    current: pageObj.current, // 当前页码
    size: pageObj.size, // 每页条数
    tag: filterBox.tag + 1, // 1-查全部，2-可代采，3-热门，4-新品
    supplierName: filterBox.supplier, // 供应商名称模糊查询字段
    name: filterBox.productName, // 商品名称模糊查询字段
    tenantId: tenantId.value,
  }
  HOMEAPI.purchaseCommodityList(params).then(({ data }) => {
    if (data.success) {
      const { data: resData } = data
      totalNum.value = resData.total
      pageObj.current = resData.current
      if (resData.current < resData.pages) {
        loadingTypeText.value = true
      } else {
        loadingTypeText.value = false
      }
      if (!type) {
        const arrData = []
        if (resData.records) {
          for (const item of resData.records) {
            arrData.push({
              id: item.id,
              img: item.img,
              name: item.name,
              num: item.minPrice.split('.')[0],
              decimals: item.minPrice.split('.')[1],
              supplierLogo: item.supplierLogo,
              supplierName: item.supplierName,
            })
          }
        }
        ProductsMiningListData.value = arrData
      } else {
        if (resData.records) {
          for (const item of resData.records) {
            ProductsMiningListData.value.push({
              id: item.id,
              img: item.img,
              name: item.name,
              num: item.minPrice.split('.')[0],
              decimals: item.minPrice.split('.')[1],
              supplierLogo: item.supplierLogo,
              supplierName: item.supplierName,
            })
          }
        }
      }
      boxLoaing.value = false
    }
  })
}

purchaseCommodityListFun()

const goodsLoadTermUnit = ref([])
// 公用字典请求
const getDictionary = (params, dataS) => {
  requestDictMap(params).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      // 处理字典数据
      const resList = dataS
      for (const item of resData.data) {
        resList.push({
          label: item.dictValue,
          value: item.dictKey,
          id: item.id,
        })
      }
    }
  })
}
// 字典请求
getDictionary('goods_load_term_unit', goodsLoadTermUnit.value) // 获取最长融资单位字典
</script>

<style lang="scss" scoped>
@import '@/views/product/antdStyle.scss';

.accounts-receivable-list {
  position: relative;
  box-sizing: border-box;
  margin-bottom: -72px;
  top: -72px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 300px;
    background: linear-gradient(180deg, #cce6ff 0%, #f6f6f6 100%);
  }

  .accounts-box {
    max-width: 1400px;
    margin: 112px auto 0;
    position: relative;
    z-index: 1;

    header {
      .he-title {
        height: 56px;
        font-size: 40px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 56px;
        text-align: center;
      }
      .he-info {
        height: 24px;
        font-size: 16px;
        font-weight: 400;
        color: #53627c;
        line-height: 24px;
        margin-top: 8px;
        text-align: center;
      }
    }

    .screen-box {
      height: 132px;
      background: #ffffff;
      box-shadow: 0px 20px 60px 0px rgba(10, 31, 68, 0.04),
        0px 0px 1px 0px rgba(10, 31, 68, 0.1);
      border-radius: 16px;
      display: flex;
      justify-content: space-between;
      margin-top: 40px;
      padding: 24px;
      box-sizing: border-box;

      .screen-left-box {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .screen-bottom-left-box {
          height: 24px;
          font-size: 16px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 24px;
          margin-top: 12px;
        }
      }
      :deep(.screen-right-box) {
        display: flex;
        justify-content: flex-end;

        .modeOfRepayment-select {
          margin-right: 20px;

          .ant-select-selector {
            padding: 0 25px 0 12px;
          }
        }

        .theFundingParty-select {
          margin-right: 20px;

          .ant-select-selector {
            padding: 0 165px 0 12px;
          }
        }

        .keywordQuery-input {
          width: 260px;
        }

        .bottom-menu {
          margin-left: 24px;

          .button-item {
            height: 48px;
            width: 88px;
            margin-right: 12px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        // 覆盖antd选择器组件样式
        .ant-select-arrow {
          width: unset;
          height: unset;
          top: 40%;
          right: 9px;
        }
        .ant-select-selector {
          height: 48px;
          border-radius: 4px;
        }
        .ant-select-selection-search-input {
          height: 48px;
        }
        .ant-select-selection-placeholder {
          height: 48px;
          line-height: 48px;
          font-size: 14px;
        }
      }
    }

    .loading-text-box {
      height: 24px;
      font-size: 16px;
      font-weight: 400;
      color: #8a94a6;
      line-height: 24px;
      text-align: center;
      margin-bottom: 24px;
    }
  }
}
</style>
