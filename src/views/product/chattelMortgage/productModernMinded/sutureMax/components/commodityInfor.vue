<template>
  <div class="product-mining-warpper">
    <!-- 代采需求 -->
    <div class="product-mining-container">
      <div class="product-mining-box">
        <div class="product-left">
          <div class="product-img">
            <img :src="productList.img" alt />
          </div>
          <div class="product-introduce">
            <h1 class="product-title">
              {{ productList.name }}
            </h1>
            <div class="product-price">
              <div class="product-price-range">
                <span>￥</span>
                <span>{{
                    formatMoney(productList.allCommoditySpecMinPrice || 0)
                  }}</span>
              </div>
              <div class="product-price-icon">~</div>
              <div class="product-price-range">
                <span>￥</span>
                <span>{{
                    formatMoney(productList.allCommoditySpecMaxPrice || 0)
                  }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="product-right">
          <div class="product-right-top">
            <div class="top-title">填写评估需求</div>
            <div class="top-main">
              <div class="top-left" @click="handleOpenList">
                <span>规格型号</span>
                <MySvgIcon
                  :icon-class="
                    !isOpen ? 'icon-xiajiantou' : 'icon-shangjiantou'
                  "
                  style="fill: #8a94a6; font-size: 16px"
                  class="top-left-icon"
                />
              </div>
              <div class="top-right">
                <div
                  class="product-size-item"
                  v-for="(item, index) in productSizeList"
                  :key="item"
                  v-show="!isOpen ? index < 2 : index < productSizeList.length"
                >
                  <div class="product-size-item-title">
                    <div class="size-title-left">{{ item.commoditySpec }}</div>
                    <div class="size-title-right">
                      <span>{{ formatMoney(item.commodityMinPrice) }}</span>
                      <span>~</span>
                      <span>{{ formatMoney(item.commodityMaxPrice) }}元</span>
                      <span>
                        &nbsp; / &nbsp;
                        {{ productList?.commodityUnit || "未设置单位" }}
                      </span>
                    </div>
                  </div>
                  <div class="size-form">
                    <n-input-number
                      @update:value="handlePriceChange(item)"
                      :show-button="false"
                      style="height: 40px"
                      :min="1"
                      placeholder="请输入数量"
                      v-model:value="item.quantity"
                      button-placement="both"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="product-right-bottom">
      <a-button type="primary" class="bottom-btn" @click="handleApply">
        申请代采
      </a-button>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "commodityInfor"
}
</script>

<script setup>
// import SupportFundProduct from './SupportFundProduct'
// import ProductMining from './ProductMining'
import { ref, watch, computed } from "vue"
import { NInputNumber } from "naive-ui"
import { useRoute } from "vue-router"
import { useStore } from "vuex"
import { getProductMiningDetail } from "@/api/product/productMining/index"
import { formatMoney } from "@/utils/utils"
import { message } from "ant-design-vue"
// import DialogAuthority from '@/views/user/components/Dialog/dialogAuthority.vue'
import { PRODUCT_CREDIT_LIMIT_API } from "@/api/index.js"
// import {
//   getProductCardDetail,
//   submitGoodsApply,
// } from '@/api/product/productMining/index'
import { getPurchaseHistoryDetail } from "@/api/user/generat/index"

const store = useStore()
const route = useRoute()
// const router = useRouter()
const dialogAuthority = ref(null) // 申请代采无权限弹窗
const isOpen = ref(false)
const productSizeList = ref([])
const productList = ref({})
// const purchaseTotal = ref(0)
const financingTotal = ref(0)
const proportion = ref(0) // 融资占比
const isCoreEnterpriseAccount = computed(
  () => store.getters["Auth/isCoreEnterpriseAccount"]
) // 企业身份
const isEnterpriseAccount = computed(
  () => store.getters["Auth/isEnterpriseAccount"]
)
const roleMap = computed(() => store.getters["Role/roleMap"])
// const isLoading = ref(true)
const productSupper = ref([]) // 可支持的资金产品
// const productPropor = ref(0) // 获取产品中最高的融资占比用于限制融资单价的输入
const selectProduct = ref({
  // 选择产品
  id: null,
  financingProportion: 0
})
const formUploadList = ref([]) // 补充资料数据 接口需求
// const goodId = computed(() => route.query.goodId)
const lock = ref(false) // 延迟赋值
const initData = ref([]) // 初始化资金产品数据
// 输入框限制条件，小数位只能两位
// const onlyAllowNumber = value => {
//   const bool = !value || /^\d+(\.\d{0,2})?$/.test(value)
//   return !!bool
// }

//点击展开
const handleOpenList = () => {
  isOpen.value = !isOpen.value
}

// 去绑定供应商
// const handleGoBindProduct = () => {
//   router.push({
//     name: 'TradeNew',
//     query: {
//       isUpperReaches: true,
//       supplierName: productList.value.supplierName,
//     },
//   })
// }

// 申请代采
const handleApply = () => {
  if (isEnterpriseAccount.value && !roleMap.value.my_product_purchase_button) {
    dialogAuthority.value.handleOpen()
    return false
  }
  // 未绑定供应商
  // if (productList.value.bindUpStreamSupperStatus !== 1) {
  //   message.warning('请先绑定供应商')
  //   return
  // }

  // 先判断是否选择了产品
  // if (!selectProduct.value.id) {
  //   message.warning('请选择一项产品！')
  //   return
  // }
  // 当选择的产品未开通时，跳转到开通流程页面
  // if (selectProduct.value.customerGoodsStatusStr !== '可融资') {
  //   const query = {
  //     goodId: selectProduct.value.id,
  //     goodType: selectProduct.value.type,
  //     capitalId: selectProduct.value.capitalId,
  //   }
  //   router.push({
  //     name: 'ProductDetails',
  //     query,
  //   })
  //   return
  // }
  // 当融资金额与货品金额为0时,说明没有输入一项
  // if (!(purchaseTotal.value && financingTotal.value)) {
  //   message.warning('至少填写一项商品数量')
  //   return
  // }
  // 要么一列是否全部输入，要么全部未输入,否则其它就是输入不规范
  // let bool = productSizeList.value.every(
  //   item =>
  //     (item.purchasePrice && item.quantity && item.financingPrice) ||
  //     (!item.purchasePrice && !item.quantity && !item.financingPrice)
  // )
  // if (!bool) {
  //   message.warn('当前存在未完成的商品规格！')
  //   return
  // }
  // 融资总额不能超过产品可用额度
  // if (
  //   Number(financingTotal.value || 0) >
  //   Number(selectProduct.value.currentAvailableAmount || 0)
  // ) {
  //   message.warn(
  //     `商品融资总额已超出产品可用额度: ${selectProduct.value.currentAvailableAmount}元,请重新填写`
  //   )
  //   return
  // }
  // 代采的申请数据
  // let recordState = 0
  let purchaseCommodityList = []
  let hasOneSelect = false
  // 商品数据的处理
  for (const item of productSizeList.value) {
    // if (item.purchasePrice && item.quantity && item.financingPrice) {
    if (item.quantity) {
      hasOneSelect = true

      // recordState = 1
      purchaseCommodityList.push({
        supplierName: productList.value.supplierName, // 供应商名称
        supplierId: productList.value.supplierId, // 供应商id
        name: productList.value.name, //
        commodityId: productList.value.id,
        spec: item.commoditySpec, //商品规格型号
        unitName: productList.value.commodityUnit, // 计量单位
        unitPrice: item.commodityMinAndMaxPriceStr || item.unitPrice, // 发布单价
        quantity: item.quantity, // 数量
        purchasePrice: item.purchasePrice || 0, //采购单价
        financingPrice: item.financingPrice || 0, // 融资单价
        purchaseTotal: item.purchaseTotal || 0, // 采购总价
        financingTotal: item.financingTotal || 0, // 融资总价
        commodityUrl: productList.value.img // 第二步展示需要
      })
    }
  }
  if (!hasOneSelect) {
    message.warning("至少填写一项商品数量")
    return false
  }
  // recordState = 0
  return purchaseCommodityList

  // 补充资料数据处理，主要用于代采申请中补充资料的回显
  // let supplementMaterial = []
  // let uploadArr = []
  // for (const item of formUploadList.value) {
  //   uploadArr = item.uploadArr.filter(child => child.attachId)
  //   supplementMaterial.push({
  //     ...item,
  //     uploadArr,
  //   })
  // }
  // let customerMaterial = {
  //   goodsId: selectProduct.value.id,
  //   supplementMaterial: JSON.stringify(supplementMaterial),
  // }
  // const params = {
  //   purchaseCommodityList,
  //   productId: selectProduct.value.id,
  //   goodsName: selectProduct.value.goodsName,
  //   amount: financingTotal.value,
  //   // customerMaterial,
  //   financeNo: route.query.financeNo || null,
  // }
  // submitGoodsApply(params).then(({ data }) => {
  //   if (data.code === 200 && data.data) {
  //     // 为1则是自动放款 手动是4 自动是20
  //     let processType = 4
  //     let isAloneCollect = void 0
  //     if (selectProduct.value.lendingMethod === 1) {
  //       processType = 20
  //       if (selectProduct.value.chargeMethod === 1) {
  //         isAloneCollect = false // 自动里的一起清分
  //       } else {
  //         isAloneCollect = true // 自动里的独立清分
  //       }
  //     }

  //     const Gdata = {
  //       // 请求参数
  //       requiredParams: {
  //         goodId: selectProduct.value.id,
  //         processType,
  //         flowOnlyId: route.query.flowOnlyId,
  //       },
  //       // 开启流程路由跳转参数
  //       routePrams: {
  //         goodId: selectProduct.value.id,
  //         goodType: 2,
  //         financeNo: data.data.financeNo,
  //         businessId: data.data.financeId,
  //         processType,
  //         isAloneCollect,
  //       },
  //       // 当前路由信息
  //       route,
  //       // 是否初始化
  //       isInitialize: true,
  //     }
  //     // 获取产品流程配置信息
  //     store.dispatch('Product/processInfoGFuns', Gdata)
  //   }
  // })
}

// 计算货品总额和融资总额
const handlePriceChange = row => {
  // row.purchaseTotal = Number(row.purchasePrice || 0) * Number(row.quantity || 0) // 单行的货品金额
  // row.financingTotal =
  //   Number(row.financingPrice || 0) * Number(row.quantity || 0) // 单行的融资金额
}

//获取商品规格数据
getProductMiningDetail({ id: route.query.id })
  .then(({ data }) => {
    let list = []
    const resData = data.data
    if (data.code === 200) {
      productList.value = resData
      if (
        resData &&
        resData.commoditySpecList &&
        resData.commoditySpecList.length
      ) {
        for (const item of resData.commoditySpecList) {
          list.push({
            ...item,
            purchasePrice: null,
            financingPrice: null,
            quantity: null,
            purchaseTotal: 0,
            financingTotal: 0
          })
        }
      }
    }
    productSizeList.value = list
  })
  .catch(() => {
  })

// 计算需缴纳保证金
watch(
  () => financingTotal.value,
  val => {
    selectProduct.value.financingTotal = val || 0
    selectProduct.value.total =
      ((selectProduct.value.financingProportion || 0) / 100) * (val || 0)
  },
  { immediate: true }
)
// 获取产品数据
// const paramsP = {
//   id: route.query.id,
//   type: 2,
// }
// getProductCardDetail(paramsP).then(({ data }) => {
//   let list = []
//   let proporTotal = 0
//   if (data.code === 200) {
//     if (data.data && data.data.length) {
//       for (const item of data.data) {
//         item.financingProportion =
//           item.financingProportion || item.financeProportionEnd
//         proporTotal =
//           Number(proporTotal) < Number(item.financingProportion)
//             ? item.financingProportion
//             : proporTotal
//         list.push(item)
//       }
//     }
//   }
//   if (goodId.value) {
//     selectProduct.value = list.find(item => item.id == goodId.value) || {}
//   }
//   productPropor.value = (proporTotal || 0) / 100 // 取出产品中最高的融资比例
//   productSupper.value = list // 初始化产品数据
//   initData.value = list // 保存产品数据用于商品融资占比发生变化时，进行操作
//   isLoading.value = false
// })

// const getClassColor = index => {
//   let styleColor = ''
//   switch (index) {
//     case 0:
//       styleColor = 'tag-green'
//       break
//     case 1:
//       styleColor = 'tag-blue'
//       break
//     case 2:
//       styleColor = 'tag-red'
//       break
//   }
//   return styleColor
// }

// 选择产品进行代采申请，row.disabled 表示不可选
// const handleSelectClick = row => {
//   if (row.disabled) {
//     message.warning(
//       `当前填写的融资占比（${proportion.value}%）已经超过所选产品的最高融资占比，请重新选择`
//     )
//     return
//   }
//   const newObj = {
//     ...row,
//     financingProportion: row.financingProportion || row.financeProportionEnd,
//     financingTotal: financingTotal.value || 0,
//     total: ((row.financingProportion || 0) / 100) * (financingTotal.value || 0),
//   }
//   selectProduct.value = newObj
// }

// 跳转代采产品列表
// const handleGoProduct = () => {
//   router.push({ name: 'generationOfMiningFinancing' })
// }

// 获取补充资料数据
const getGoodsMaterialList = params => {
  PRODUCT_CREDIT_LIMIT_API.getGoodsMaterialList(params).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      const arrData = []
      for (const item of resData.data) {
        const uploadArr = []
        uploadArr.push(uploadArrPushData(1, item.example))
        arrData.push({
          id: item.id,
          materialId: item.materialId,
          materialName: item.materialName,
          multipleType: item.multipleType,
          maxNum: item.num,
          uploadArr: uploadArr,
          example: item.example
        })
      }
      formUploadList.value = arrData
    }
  })
}
// 补充资料默认添加格式封装
const uploadArrPushData = (idS, example) => {
  const examplePath = example.split(".")
  return {
    id: idS,
    loading: false,
    url: undefined,
    attachId: undefined,
    defaultUrl: example,
    isPdf: examplePath[examplePath.length - 1] == "pdf" ? true : false,
    init: true
  }
}

// 商品数据回显
const goodDetail = async params => {
  lock.value = true
  let list = []
  const { data } = await getPurchaseHistoryDetail(params)
  if (data.code === 200) {
    let dataList = data.data.purchaseCommodityList || []
    for (const item of dataList) {
      list.push({
        ...item,
        commoditySpec: item.spec,
        purchasePrice: Number(item.purchasePrice),
        financingPrice: Number(item.financingPrice),
        commodityMinPrice: item.unitPrice.replace(/\s*/g, "").split("~")[0],
        commodityMaxPrice: item.unitPrice.replace(/\s*/g, "").split("~")[1]
      })
    }
  }
  productSizeList.value = list

  setTimeout(() => {
    lock.value = false
  }, 500)
}

// 监听融资占比来控制产品是否可选
watch(
  () => proportion.value,
  val => {
    let list = [...initData.value]
    // 当规格数据发生变化时,清空选中
    if (!lock.value) {
      selectProduct.value = {}
    }
    // 当产品数据的融资比例小于商品的融资比例，不可选择
    productSupper.value = list.map(item => ({
      ...item,
      disabled: Number(item.financingProportion) < Number(val || 0)
    }))
  },
  { deep: true }
)

// 获取补充资料，用于代采申请初始化显示补充资料
watch(
  () => selectProduct.value.id,
  goodsId => {
    if (goodsId) {
      getGoodsMaterialList({
        goodsId: goodsId,
        uploadNode: "7-0",
        uploadUser: isCoreEnterpriseAccount.value ? 2 : 1 // true 为核心企业
      })
    }
  },
  { deep: true }
)

watch(
  () => route.query.financeNo,
  financeNo => {
    if (financeNo) {
      goodDetail({ financeNo })
    }
  },
  { immediate: true, deep: true }
)

// 货品总额\融资总额\融资占比 计算
// watch(
//   () => productSizeList.value,
//   val => {
//     let pTotal = 0
//     let fTotal = 0
//     for (const item of val) {
//       pTotal += Number(item.purchasePrice || 0) * Number(item.quantity || 0)
//       fTotal += Number(item.financingPrice || 0) * Number(item.quantity || 0)
//     }
//     purchaseTotal.value = pTotal
//     financingTotal.value = fTotal
//     // 融资占比计算
//     let prop = Number((financingTotal.value / purchaseTotal.value) * 100 || 0)
//     proportion.value = prop.toFixed(2)
//   },
//   { deep: true }
// )
defineExpose({ handleApply })
</script>

<style lang="scss" scoped>
.product-mining-warpper {
  background: #fff;
  // padding-bottom: 50px;
}

.product-mining-container {
  background: #fff;
  padding: 40px 0;

  .product-mining-box {
    display: flex;
    align-items: flex-start;
    box-sizing: border-box;
    max-width: 1400px;
    margin: auto;
    box-sizing: border-box;

    .product-left {
      max-width: 400px;
      background: #ffffff;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      border-radius: 16px;
      border: 1px solid #efefef;
      box-sizing: border-box;
      margin-right: 40px;
      position: relative;

      &::after {
        display: block;
        content: '';
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 0;
        width: 234px;
        height: 166px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.07;
        filter: blur(45px);
      }

      &::before {
        display: block;
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 137px;
        height: 181px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.24;
        filter: blur(30px);
        z-index: 0;
      }

      .product-img {
        padding: 28px;

        & img {
          width: 345px;
          height: 242px;
          object-fit: cover;
        }
      }

      .product-introduce {
        padding: 24px;

        .product-title {
          font-size: 24px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: bold;
          color: #0a1f44;
          line-height: 32px;
          margin-bottom: 20px;
        }

        .product-price {
          display: flex;
          align-items: center;

          .product-price-range {
            line-height: 39px;

            & span {
              font-family: CoreSansD55Bold;
              color: #dd2727;
            }

            & span:first-child {
              font-size: 16px;
            }

            & span:last-child {
              font-size: 32px;
            }
          }

          .product-price-icon {
            margin: 0 8px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #dd2727;
            line-height: 22px;
          }
        }

        .product-line {
          margin: 24px 0;
          width: 100%;
          height: 1px;
          background-color: #f1f2f4;
        }

        .product-bottom {
          display: flex;
          align-items: center;

          .product-name {
            display: flex;
            align-items: center;
            margin-right: 4px;

            & img {
              width: 20px;
              height: 20px;
              margin-right: 4px;
              object-fit: cover;
            }

            & span {
              max-width: 165px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 16px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #8a94a6;
              line-height: 28px;
            }
          }

          .product-status {
            position: relative;
            z-index: 10;
            display: flex;
            align-items: center;
            padding: 4px 10px;
            border-radius: 14px;

            & span {
              font-size: 13px;
              font-weight: 500;
              line-height: 16px;
            }
          }

          .text-green {
            color: #00865a;
            background-color: #cff8eb;
          }

          .text-blue {
            cursor: pointer;
            color: #0d55cf;
            background-color: #ebf5ff;
          }
        }
      }
    }

    .product-right {
      flex-grow: 1;
      background: #ffffff;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      border-radius: 16px;
      border: 1px solid #efefef;
      position: relative;
      padding-bottom: 24px;
      z-index: 20;

      &::before {
        position: absolute;
        display: block;
        content: '';
        left: 0;
        top: 0;
        z-index: 0;
        width: 321px;
        height: 283px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.09;
        filter: blur(30px);
      }

      &::after {
        position: absolute;
        display: block;
        content: '';
        right: 0;
        bottom: 0;
        z-index: 0;
        width: 258px;
        height: 304px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.09;
        filter: blur(45px);
      }

      .product-right-top {
        position: relative;
        z-index: 10;

        .top-title {
          padding: 24px 32px;
          border-bottom: 1px solid #f1f2f4;
          font-size: 32px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: bold;
          color: #031222;
          line-height: 45px;
        }

        .top-main {
          padding: 0 32px;
          display: flex;
          align-items: flex-start;

          .top-left {
            cursor: pointer;
            user-select: none;
            margin-top: 24px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
            line-height: 24px;
            margin-right: 28px;
            display: flex;
            align-items: center;
            flex-flow: row nowrap;

            & span {
              width: 64px;
              display: inline-block;
            }

            .top-left-icon {
              transition: all 0.35s ease;
            }
          }

          .top-right {
            flex-grow: 1;
            min-height: 257px;
            max-height: 642px;
            overflow: auto;

            .product-size-item {
              // margin-bottom: 24px;
              padding: 24px 0;
              box-sizing: border-box;

              .product-size-item-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 16px;
                font-family: SFProText-Semibold, SFProText;
                font-weight: 600;
                color: #0a1f44;
                line-height: 24px;
                margin-bottom: 16px;
              }

              .size-form {
                display: flex;
                flex-flow: row nowrap;

                :deep(.n-input-wrapper) {
                  height: 40px !important;
                  line-height: 40px;
                }
              }
            }

            .product-size-item:not(:nth-of-type(1)) {
              border-top: 1px dashed #e1e4e8;
              padding-top: 24px;
            }
          }
        }
      }

      .product-right-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 32px;
        border-top: 1px solid #efefef;
        border-bottom: 1px solid #efefef;
        background-color: #fff8f8;
        position: relative;
        z-index: 10;

        .main-left {
          box-sizing: border-box;

          .main-left-item {
            display: flex;
            align-items: baseline;

            &:not(:last-child) {
              margin-bottom: 13px;
            }

            & > span {
              display: block;
              width: 96px;
              font-size: 16px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #53627c;
              line-height: 24px;
              margin-right: 12px;
            }

            .main-left-num {
              display: flex;
              align-items: baseline;
              line-height: 48px;
              font-family: CoreSansD35Regular;

              & span:first-child {
                font-size: 20px;
              }

              & span:last-child {
                font-size: 24px;
              }
            }

            .text-black {
              color: #0a1f44;
            }

            .text-red {
              color: #dd2727;
            }
          }
        }

        .main-right {
          .billing-rules-tooltip {
            .ant-tooltip-inner {
              color: red !important;
              width: 360px;
            }
          }

          .main-right-tooltip {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
            line-height: 24px;
          }
        }
      }
    }
  }
}

.product-right-bottom {
  margin-bottom: 32px;
  display: flex;
  justify-content: center;

  .bottom-btn {
    margin-top: 20px;
    width: 400px;
    height: 48px;
    background-color: #0d55cf;
    color: #fff;
    box-sizing: border-box;
    border-radius: 32px;
    text-align: center;
  }
}

.support-product-warpper {
  margin: auto;
  max-width: 1400px;
  box-sizing: border-box;
  margin-top: 20px;

  .support-tips {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .support-title {
      font-size: 40px;
      font-weight: bold;
      color: #0a1f44;
      line-height: 56px;
    }

    .support-value {
      display: flex;
      align-items: center;
      margin-left: 14px;
      border-left: 1px solid #b5bbc6;
      line-height: 24px;
      color: #8a94a6;
      padding: 0 10px;
    }
  }

  .support-subtitle-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .support-subtitle {
      font-size: 16px;
      font-weight: 400;
      color: #53627c;
      line-height: 24px;
      margin-bottom: 32px;
    }

    .subtitle-right {
      cursor: pointer;
      display: flex;
      align-items: center;

      & span {
        display: block;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8a94a6;
        line-height: 20px;
      }
    }
  }

  .support-card {
    display: flex;
    align-items: center;
    flex-flow: row wrap;
    margin-bottom: -24px;

    .support-card-item {
      position: relative;
      width: 450px;
      min-height: 234px;
      padding: 24px;
      box-sizing: border-box;
      background-color: #fff;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      margin-right: 24px;
      margin-bottom: 24px;
      border-radius: 16px;
      cursor: pointer;

      .support-card-select {
        position: absolute;
        right: -1px;
        bottom: -1px;
        width: 32px;
        height: 32px;
        padding: 4px;
        z-index: 10;
        box-sizing: border-box;
        background-color: #0d55cf;
        border-radius: 8px 0px 8px 0;
      }

      &::before {
        display: block;
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        z-index: 0;
        width: 79px;
        height: 76px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.14;
        filter: blur(13px);
      }

      &::after {
        display: block;
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 0;
        width: 164px;
        height: 143px;
        background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
        opacity: 0.2;
        filter: blur(24px);
      }

      &:nth-of-type(3n) {
        margin-right: 0;
      }

      .card-main {
        position: relative;
        z-index: 2;

        .card-main-title {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .card-title-left {
            display: flex;
            align-items: center;

            & img {
              width: 28px;
              height: 28px;
              object-fit: cover;
              margin-right: 8px;
            }

            & span {
              display: block;
              font-size: 20px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #0a1f44;
              line-height: 28px;
            }
          }

          .card-title-right {
            .title-right-status {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              line-height: 20px;
            }

            .text-green {
              color: #00865a;
            }

            .text-blue {
              color: #0d55cf;
            }
          }
        }

        .card-content {
          .card-subtitle {
            margin-top: 12px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .card-total {
            margin-top: 2px;
            margin-bottom: 12px;
            font-size: 48px;
            font-family: CoreSansD65Heavy;
            color: #031222;
            line-height: 59px;
          }

          .card-detail {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #8a94a6;
            line-height: 20px;
          }

          .card-detail + .card-detail {
            margin-top: 16px;
          }

          .card-tag-list {
            display: flex;
            align-items: center;
            flex-flow: row nowrap;
            margin-top: 12px;

            .card-tag-item {
              box-sizing: border-box;
              display: block;
              padding: 6px 12px;
              border-radius: 14px;
              border: 1px solid;
              font-size: 13px;
              font-family: SFProText-Medium, SFProText;
              line-height: 16px;
              margin-right: 12px;

              &:last-child {
                margin-right: 0;
              }
            }

            .card-tag2-item {
              box-sizing: border-box;
              width: 126px;
              height: 74px;
              border-radius: 8px;
              border: 1px solid #e1e4e8;
              border-radius: 8px;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              margin-right: 12px;

              &:last-child {
                margin-right: 0;
              }

              .tag2-top {
                display: flex;
                align-items: baseline;

                & span:nth-of-type(1) {
                  font-size: 28px;
                  font-family: CoreSansD65Heavy;
                  color: #dd2727;
                  line-height: 35px;
                }

                & span:nth-of-type(2) {
                  font-size: 14px;
                  font-family: PingFangSC-Semibold, PingFang SC;
                  font-weight: 600;
                  color: #dd2727;
                  line-height: 20px;
                }
              }

              .tag2-bottom {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #8a94a6;
                line-height: 20px;
              }
            }

            .tag-green {
              color: #00865a;
              border-color: #0bb07b;
            }

            .tag-blue {
              color: #0d55cf;
              border-color: #0c66ff;
            }

            .tag-yellow {
              color: #f07300;
              border-color: #ffad0d;
            }
          }
        }
      }
    }

    .active-class {
      border: 1px solid #0d55cf;
      background: #f5faff;
      border-radius: 8px;
    }

    .select-disabled {
      background: #f8f9fb;
      // cursor: not-allowed;
      &::before {
        z-index: -1;
      }

      &::after {
        z-index: -1;
      }
    }
  }
}

:deep() {
  .explain-box {
    display: flex;
    align-items: flex-start;

    .explain-wrap {
      .explain-introduce {
        .explain-introduce-item {
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.main-right-tooltip {
  .ant-tooltip-content {
    width: 360px;

    .ant-tooltip-inner {
      padding: 24px 16px;
      border-radius: 8px;
      box-shadow: 0px 4px 24px 0px rgba(10, 31, 68, 0.2),
      0px 0px 1px 0px rgba(10, 31, 68, 0.08);
      backdrop-filter: blur(4px);

      .tooltip-box {
        .tooltip-title {
          font-size: 16px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #ffffff;
          line-height: 24px;
        }

        .tooltip-price-list {
          .tooltip-price-item {
            margin-top: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            & span:first-child {
              font-size: 14px;
              font-family: SFProText-Medium, SFProText;
              font-weight: 500;
              color: #ffffff;
              line-height: 20px;
            }

            & span:last-child {
              font-size: 16px;
              font-family: CoreSansD65Heavy;
              color: #ffffff;
              line-height: 20px;
            }
          }
        }
      }
    }
  }
}
</style>
