<template>
  <div class="appli-cations">
    <ArticleSetps
      :succerssType="succerssPageType"
      :info="'融资申请'"
      :arrData="setpHeaderData"
      :current="current"
      :widths="'1000px'"
    />
    <!-- 填写融资需求 -->
    <!-- v-if="financingDemand" -->
    <FinancingDemand
      v-if="current === 0"
      :type="businessApprovalNum"
      @setCurrentUpFun="currentUpFun"
    />
    <!-- 融资确认 -->
    <!-- <div v-else-if="financingToConfirm">
      <FinancingToConfirm
        :finaContractSigningType="finaContractSigningType"
        @setCurrentUpFun="currentUpFun"
      />
      <ContractSigning
        v-if="finaContractSigningType"
        signNodeProps="9-1"
        @setCurrentUpFun="currentUpFun"
      />
    </div> -->
    <!-- 缴纳费用 -->
    <!-- <div v-else-if="otherExpenses">
      <OtherExpenses
        v-if="onlineAndOfflinePayment === 1"
        ref="otherExpensesRef"
        @setCurrentUpFun="currentUpFun"
      />
      <OtherExpensesOnlinePay
        v-else
        ref="otherExpensesOnlinePayRef"
        @setCurrentUpFun="currentUpFun"
      />
      <ContractSigning
        v-if="otherContractSigningType"
        signNodeProps="9-2"
        @setCurrentUpFun="currentUpFun"
        @previousToPape="previousToPape"
        @nextToPape="nextToPape"
      />
    </div> -->
    <!-- 业务审批 -->
    <!-- backIndex \ true:1 ; false:2  -->
    <!-- v-else-if="businessApprovalType" -->
    <BusinessApproval
      v-else-if="current === 1"
      :type="businessApprovalNum"
      :bsId="finacCinId"
      @goBack="currentUpFun"
    />
    <!-- :backIndex="finaContractSigningType" -->
    <!-- 审批成功 -->
    <SuccerssPage
      v-else-if="succerssPageType"
      :type="businessApprovalNum"
      :bsId="finacCinId"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'appliCations',
}
</script>
<script setup lang="ts">
import ArticleSetps from '@/components/articleSetps/index.vue'
// src\views\product\chattelMortgage/movablePropertyFinancing\index.vue
import FinancingDemand from '@/views/product/chattelMortgage/movablePropertyFinancing/components/financingDemand/index.vue'
// import FinancingToConfirm from '@/views/product/chattelMortgage/movablePropertyFinancing/components/financingToConfirm/index.vue'
// import ContractSigning from '@/views/product/chattelMortgage/movablePropertyFinancing/components/contractSigning/index.vue'
// import OtherExpenses from '@/views/product/chattelMortgage/movablePropertyFinancing/components/otherExpenses/index.vue'
// import OtherExpensesOnlinePay from '@/views/product/chattelMortgage/movablePropertyFinancing/components/otherExpensesOnlinePay/index.vue'
import BusinessApproval from '@/views/product/component/businessApproval.vue'
import SuccerssPage from '@/views/product/chattelMortgage/movablePropertyFinancing/components/succerssPage/index.vue'

import {
  ref,
  // onMounted,
  // watch,
  onUnmounted,
  // computed,
  reactive,
  provide,
  readonly,
} from 'vue'
import { useRoute } from 'vue-router'
// import { useStore } from 'vuex'
import { PRODUCT_VERIFY_API } from '@/api/index.js'
const route = useRoute()
// const store = useStore()

// const otherExpensesRef = ref(null)
// const otherExpensesOnlinePayRef = ref(null)

// const financingDemand = ref(true)
// const financingToConfirm = ref(false)
// const otherExpenses = ref(false)
// const businessApprovalType = ref(false)
const succerssPageType = ref(false)
// const finaContractSigningType = ref(false)
// const otherContractSigningType = ref(false)
// const goBackPapeLock = computed<boolean>(
//   () => store.getters['Product/goBackPapeLock']
// )
const setpHeaderData = ref([
  { id: 1, name: '填写融资需求' },
  { id: 2, name: '审批确认' },
  { id: 3, name: '申请成功' },
])
const current = ref(0) // 进度
const finacCinId = ref(
  JSON.parse(sessionStorage.getItem('financingDemandId')) ||
    route.query.id ||
    route.query.goodId
)
const businessApprovalNum = ref(5)
const onlineAndOfflinePayment = ref(1) // 1:线下，2：线上

// 给合同生成模板用的数据
const repaymentContractData = reactive({
  financeAmount: 0, // 融资金额
  annualInterestRate: 0, // 年利率
  totalTerm: 0, // 总期数
  startTime: '', // 开始时间
  refundType: 0, // 还款类型 '1','2','3'
  loadTermUnit: 0, // 1-天,2-期
  initial: true, // 是否初始数据
})
provide('repaymentContractData', readonly(repaymentContractData))

// 修改合同生成模板数据
const setRepaymentContractDataFun = datas => {
  for (const key in repaymentContractData) {
    if (datas[key]) {
      repaymentContractData[key] = datas[key] || void 0
    }
  }
  delete repaymentContractData.initial
}
provide('setRepaymentContractDataFun', setRepaymentContractDataFun)

// 变更流程进度
const currentUpFun = val => {
  current.value = val
}

// 查线下还是线上支付
;(() => {
  const paramsD = {
    goodsId: route.query.goodId,
    accountType: 1, // 账户类型 1.平台账户、2.资方账户
  }
  PRODUCT_VERIFY_API.getGoodsRelationAccount(paramsD).then(({ data }) => {
    const { data: resData } = data
    if (data.success && resData) {
      onlineAndOfflinePayment.value = resData.platformCostPayMode
    }
  })
})()

// 查询流程进度
// const getByBusinessIdAndTypeApi = () => {
//   const params = {
//     businessId: finacCinId.value,
//     type: businessApprovalNum.value,
//   }
//   PRODUCT_VERIFY_API.getByBusinessIdAndType(params)
//     .then(res => {
//       const resData = res.data
//       if (resData.code == 200 && !resData.data) {
//         currentUpFun(0)
//       } else if (resData.code == 200 && resData.data) {
//         currentUpFun(resData.data.progress)
//       }
//     })
//     .catch(({ msg, hideMsgFunc }) => {})
// }

// 根据放款方式变化流程
// const setlendingMethodType = val => {
//   if (val.len === '2') {
//     setpHeaderData.value = [
//       { id: 1, name: '填写融资需求' },
//       { id: 2, name: '审批确认' },
//       { id: 3, name: '申请成功' },
//     ]
//     businessApprovalNum.value = 5
//     getByBusinessIdAndTypeApi()
//   } else {
//     businessApprovalNum.value = 9
//     if (val.cha === '1') {
//       setpHeaderData.value = [
//         { id: 1, name: '填写融资需求' },
//         { id: 2, name: '融资确认' },
//         { id: 3, name: '业务审批' },
//         { id: 4, name: '审批成功' },
//       ]
//       finaContractSigningType.value = true
//       otherContractSigningType.value = false
//       getByBusinessIdAndTypeApi()
//     } else {
//       setpHeaderData.value = [
//         { id: 1, name: '填写融资需求' },
//         { id: 2, name: '融资确认' },
//         { id: 3, name: '缴纳费用' },
//         { id: 4, name: '业务审批' },
//         { id: 5, name: '审批成功' },
//       ]
//       finaContractSigningType.value = false
//       otherContractSigningType.value = true
//       getByBusinessIdAndTypeApi()
//     }
//   }
// }
// const propsData = {
//   len: route.query.lendingMethod,
//   cha: route.query.chargeMethod,
// }
// setlendingMethodType(propsData)

// 兼容手动合同签署
// watch(
//   () => route.query.chargeMethod,
//   newVal => {
//     if (newVal && !goBackPapeLock.value) {
//       const propsData = {
//         len: route.query.lendingMethod,
//         cha: route.query.chargeMethod,
//       }
//       setlendingMethodType(propsData)
//     }
//   }
// )

// watchEffect(() => {
//   if (route.query.chargeMethod && !goBackPapeLock.value) {
//     const propsData = {
//       len: route.query.lendingMethod,
//       cha: route.query.chargeMethod,
//     }
//     setlendingMethodType(propsData)
//   }
// })

// onMounted(() => {
//   watch(
//     () => current.value,
//     val => {
//       // 自动放款-收平台费用
//       if (otherContractSigningType.value) {
//         if (val == 0) {
//           financingDemand.value = true
//           financingToConfirm.value = false
//           otherExpenses.value = false
//           businessApprovalType.value = false
//           succerssPageType.value = false
//         } else if (val == 1) {
//           financingDemand.value = false
//           financingToConfirm.value = true
//           otherExpenses.value = false
//           businessApprovalType.value = false
//           succerssPageType.value = false
//         } else if (val == 2) {
//           financingDemand.value = false
//           financingToConfirm.value = false
//           otherExpenses.value = true
//           businessApprovalType.value = false
//           succerssPageType.value = false
//         } else if (val == 3) {
//           financingDemand.value = false
//           financingToConfirm.value = false
//           otherExpenses.value = false
//           businessApprovalType.value = true
//           succerssPageType.value = false
//         } else if (val == 4) {
//           financingDemand.value = false
//           financingToConfirm.value = false
//           otherExpenses.value = false
//           businessApprovalType.value = false
//           succerssPageType.value = true
//         }
//       } else if (finaContractSigningType.value) {
//         // 自动放款-不收平台费用
//         if (val == 0) {
//           financingDemand.value = true
//           financingToConfirm.value = false
//           businessApprovalType.value = false
//           succerssPageType.value = false
//         } else if (val == 1) {
//           financingDemand.value = false
//           financingToConfirm.value = true
//           businessApprovalType.value = false
//           succerssPageType.value = false
//         } else if (val == 2) {
//           financingDemand.value = false
//           financingToConfirm.value = false
//           businessApprovalType.value = true
//           succerssPageType.value = false
//         } else if (val == 3) {
//           financingDemand.value = false
//           financingToConfirm.value = false
//           businessApprovalType.value = false
//           succerssPageType.value = true
//         }
//       } else if (businessApprovalNum.value == 5) {
//         // 手动放款-收平台费用
//         if (val == 0) {
//           financingDemand.value = true
//           businessApprovalType.value = false
//           succerssPageType.value = false
//         } else if (val == 1) {
//           financingDemand.value = false
//           businessApprovalType.value = true
//           succerssPageType.value = false
//         } else if (val == 2) {
//           financingDemand.value = false
//           businessApprovalType.value = false
//           succerssPageType.value = true
//         }
//       }
//     },
//     { immediate: true }
//   )
// })

// 缴纳费用上一步
// const previousToPape = () => {
//   if (onlineAndOfflinePayment.value === 1) {
//     otherExpensesRef.value.previousToPape()
//     return
//   }
//   otherExpensesOnlinePayRef.value.previousToPape()
// }

// 缴纳费用下一步
// const nextToPape = () => {
//   if (onlineAndOfflinePayment.value === 1) {
//     otherExpensesRef.value.nextToPape()
//     return
//   }
//   otherExpensesOnlinePayRef.value.nextToPape()
// }

onUnmounted(() => {
  sessionStorage.removeItem('financingDemandId')
})
</script>

<style lang="scss" scoped>
.appli-cations {
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  padding-top: 40px;
  flex: 1;
}
</style>
