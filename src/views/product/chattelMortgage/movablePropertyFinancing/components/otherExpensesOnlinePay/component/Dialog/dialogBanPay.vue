<template>
  <GlobalDialog
    title="通知"
    width="432px"
    ref="formDialog"
    enableFooterSlot
    :maskClosable="false"
    :enableFullscreen="false"
    :onClose="changeCurrentIndexFun"
  >
    <div class="stop-pay">
      <p v-show="isEnterprise === 'wt' || isEnterprise">
        企业:金额少于<span class="stop-pay_red">10.01</span>不支持网银支付
      </p>
      <p v-show="isEnterprise === 'wt' || !isEnterprise">
        个人:金额少于<span class="stop-pay_red">0.11</span>不支持网银支付
      </p>
    </div>

    <template #button>
      <!-- <a-button class="btn-cancel" @click="handleClose">取 消</a-button> -->
      <a-button
        type="primary"
        class="btn-submit"
        html-type="submit"
        @click="handleSubmitForm"
      >
        确 定
      </a-button>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'dialogBanPay',
}
</script>

<script setup>
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { ref } from 'vue'

const formDialog = ref(null)
const isEnterprise = ref(false)
const emit = defineEmits(['changeCurrentIndex'])

const handleOpen = isEnterprises => {
  isEnterprise.value = isEnterprises
  formDialog.value.handleOpen()
}
const handleClose = () => {
  formDialog.value.handleClose()
}

// 确认操作
const handleSubmitForm = () => {
  emit('changeCurrentIndex', 0)
  handleClose()
}

const changeCurrentIndexFun = () => {
  emit('changeCurrentIndex', 0)
}

defineExpose({
  handleOpen,
  handleClose,
})
</script>
<style lang="scss" scoped>
.stop-pay {
  font-size: 16px;
  padding: 0 10px;

  &_red {
    color: red;
  }
}
.btn-cancel {
  margin-right: 8px;
}
.btn-submit,
.btn-cancel {
  height: 40px;
  line-height: 20px;
  padding: 10px 20px;
  border-radius: 100px;
}
</style>
