<template>
  <GlobalDialog
    title="选择银行"
    width="800px"
    ref="formDialog"
    enableFooterSlot
    :maskClosable="false"
    :enableFullscreen="false"
    :onClose="changeCurrentIndexFun"
  >
    <div class="type-switching-box">
      <template v-if="!firstLoading">
        <a-tabs
          v-model:activeKey="activeKey"
          type="card"
          @change="handleChange"
        >
          <a-tab-pane key="wangyin_pay_b2c" tab="个人网银">
            <template v-if="bankSelectList.B2CBankCodeTypes.length">
              <a-radio-group v-model:value="radioGValue">
                <a-radio
                  v-for="item of bankSelectList.B2CBankCodeTypes"
                  :key="item.id"
                  :value="item.code"
                >
                  <div class="img-box">
                    <img :src="item.pic" :alt="item.name" :title="item.name" />
                  </div>
                </a-radio>
              </a-radio-group>
            </template>
            <a-empty
              v-else
              :image="EmptyImg"
              :imageStyle="{ height: '170px' }"
            />
          </a-tab-pane>
          <a-tab-pane key="wangyin_pay_b2b" tab="企业网银">
            <template v-if="bankSelectList.B2BBankCodeTypes.length">
              <a-radio-group v-model:value="radioGValue">
                <a-radio
                  v-for="item of bankSelectList.B2BBankCodeTypes"
                  :key="item.id"
                  :value="item.code"
                >
                  <div class="img-box">
                    <img :src="item.pic" :alt="item.name" :title="item.name" />
                  </div>
                </a-radio>
              </a-radio-group>
            </template>
            <a-empty
              v-else
              :image="EmptyImg"
              :imageStyle="{ height: '170px' }"
            />
          </a-tab-pane>
        </a-tabs>
      </template>
      <div v-else class="example">
        <a-spin />
      </div>
    </div>

    <template #button>
      <a-button class="btn-cancel" @click="handleClose">取 消</a-button>
      <a-button
        type="primary"
        class="btn-submit"
        html-type="submit"
        :loading="loading"
        @click="handleSubmitForm"
      >
        确 定
      </a-button>
    </template>
  </GlobalDialog>
  <InternetBankHLB ref="internetBankHLBRef" />
  <InternetBankHLBForPay ref="internetBankHLBForPayRef"></InternetBankHLBForPay>
  <DialogBanPay ref="DialogBanPayRef" />
</template>

<script>
export default {
  name: 'dialoginternetBankSelect',
}
</script>

<script setup>
import { PRODUCT_VERIFY_API } from '@/api/index'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import InternetBankHLB from '../internetBankHLB.vue'
import InternetBankHLBForPay from '../internetBankHLBForPay.vue'
import DialogBanPay from './dialogBanPay.vue'
import { message } from 'ant-design-vue'
import { ref, reactive } from 'vue'
import EmptyImg from '@/assets/images/empty_3.svg'
import { useRoute } from 'vue-router'
const route = useRoute()

const activeKey = ref(null)
const radioGValue = ref(null)
const formDialog = ref(null)
const DialogBanPayRef = ref(null)
const internetBankHLBRef = ref(null)
const internetBankHLBForPayRef = ref(null)
const firstLoading = ref(true)
const loading = ref(false)
const bankSelectList = reactive({
  B2BBankCodeTypes: [],
  B2CBankCodeTypes: [],
}) // 银行列表数据
const finacCinId = ref(
  JSON.parse(sessionStorage.getItem('financingDemandId')) || route.query.id
)

const emit = defineEmits(['changeCurrentIndex'])

const props = defineProps({
  financeNoV: {
    required: true,
  },
  allMonry: {
    required: true,
  },
  // 为了兼容还款的参数
  isReplayment: {
    type: Boolean,
  },
  // 1 本期应还 2 可提前还款 3 提前结清
  isPayType: {
    type: Number,
  },
  iouNo: {
    type: String,
  },
  advanceRepaymentId: {
    type: String,
  },
  repaymentIds: {
    type: String,
  },
  principalAmount: {
    type: String,
  },
})

// 获取银行列表
const listGroupCodeFun = () => {
  PRODUCT_VERIFY_API.listGroupCode().then(({ data }) => {
    if (data.success) {
      const { data: resData } = data
      bankSelectList.B2CBankCodeTypes = resData.B2CBankCodeTypes
      bankSelectList.B2BBankCodeTypes = resData.B2BBankCodeTypes
      firstLoading.value = false
    }
  })
}

// 初始化数据
const initialize = () => {
  activeKey.value = 'wangyin_pay_b2c'
  radioGValue.value = null
}

// 标签页切换事件
const handleChange = val => {
  if (val === 'wangyin_pay_b2b' && Number(props.allMonry) < 10.01) {
    DialogBanPayRef.value.handleOpen(true)
    activeKey.value = 'wangyin_pay_b2c'
    return
  }
  // DialogBanPayRef.value.handleOpen(false)
  radioGValue.value = null
}

// 打开弹窗
const handleOpen = () => {
  if (Number(props.allMonry) < 0.11) {
    DialogBanPayRef.value.handleOpen('wt')
    setTimeout(() => {
      changeCurrentIndexFun()
    }, 66)
    return
  }
  // console.log(props.financeNoV,props.allMonry)

  listGroupCodeFun()
  initialize()
  formDialog.value.handleOpen()
}
// 关闭弹窗
const handleClose = () => {
  firstLoading.value = true
  formDialog.value.handleClose()
}

// 确认操作
const handleSubmitForm = () => {
  if (!radioGValue.value) {
    message.warning('请选择银行')
    return
  }
  loading.value = true
  // const callUrl = 'https://www.baidu.com/'
  // const callUrl = `https://501xc90628.zicp.fun${route.fullPath}&id=${finacCinId.value}`
  const fId = `&id=${finacCinId.value}`
  const callUrl = `${window.location.protocol}//${window.location.host}${
    route.fullPath
  }${route.query.id ? '' : fId}`
  let dataP = {
    // bank: 'CCB',
    bank: radioGValue.value,
    financeNo: props.financeNoV,
    type: 0,
    business: activeKey.value === 'wangyin_pay_b2b' ? 'B2B' : 'B2C', // 业务类型 B2C:个人支付 B2B:企业支付
    callbackUrl: callUrl,
  }
  if (props.isReplayment) {
    let callUrl = window.location.href
    dataP = {
      financeNo: props.financeNoV,
      bank: radioGValue.value,
      business: activeKey.value === 'wangyin_pay_b2b' ? 'B2B' : 'B2C', // 业务类型 B2C:个人支付 B2B:企业支付
      callbackUrl: callUrl,
      amount: props.allMonry,
      // 提前还款
      advanceRepaymentId: props.advanceRepaymentId,
      principalAmount: props.principalAmount,
      // 正常还款
      repaymentIds: props.repaymentIds,
      // 提前结清
      iouNo: props.iouNo,
      repaymentType: props.isPayType,
      payMode: 2,
    }
    loading.value = true
    PRODUCT_VERIFY_API.rechargePay(dataP)
      .then(({ data }) => {
        const { data: resData } = data
        if (data.success) {
          setTimeout(() => {
            internetBankHLBForPayRef.value.internetBankToPay(resData)
          }, 500)
          setTimeout(() => {
            loading.value = false
          }, 2500)
        }
      })
      .catch(() => {
        loading.value = false
      })
  } else {
    PRODUCT_VERIFY_API.onlinePayHLB(dataP)
      .then(({ data }) => {
        const { data: resData } = data
        if (data.success) {
          // handleClose()
          // 网银支付
          internetBankHLBRef.value.internetBankToPay(resData)
          setTimeout(() => {
            loading.value = false
          }, 2500)
        }
      })
      .catch(() => {
        loading.value = false
      })
  }
}

// 切换父组件按钮index
const changeCurrentIndexFun = () => {
  emit('changeCurrentIndex', 0)
}

defineExpose({
  handleOpen,
  handleClose,
})
</script>
<style lang="scss" scoped>
.type-switching-box {
  .img-box {
    width: 199px;
    height: 75px;
    margin-top: 15px;
    border: 1px solid #e1e4e8;
    box-shadow: 1px 2px 2px 1px #f0f3f7;
    border-radius: 10px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .example {
    width: 100%;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 27px;
  }
}
.btn-cancel {
  margin-right: 8px;
}
.btn-submit,
.btn-cancel {
  height: 40px;
  line-height: 20px;
  padding: 10px 20px;
  border-radius: 100px;
}
</style>
