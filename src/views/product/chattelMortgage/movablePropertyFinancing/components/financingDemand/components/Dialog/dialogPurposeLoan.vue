<template>
  <GlobalDialog
    title="融资用途"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div>
      <CheckCard
        v-for="item of purposeLoanList"
        :key="item.key"
        :active="activeIndex === item.key"
        @click="handleChangeIndex(item)"
      >
        <div class="account-item">
          {{ item.value }}
        </div>
      </CheckCard>
    </div>
    <template #button>
      <div style="width: 100%; text-align: right">
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 12px"
          round
          :bordered="false"
          @click="handleClose"
        >
          取消
        </n-button>
        <n-button
          class="border blue button-item primary"
          type="info"
          style="height: 40px"
          round
          :bordered="false"
          @click="handleConfirm"
        >
          确认
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'dialogPurposeLoan',
}
</script>
<script setup>
import { ref, watch } from 'vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import CheckCard from '@/components/BaseCard/checkCard/index.vue'
import { NButton } from 'naive-ui'
import { message } from 'ant-design-vue'

const props = defineProps({
  purposeLoanList: {
    type: Array,
    required: true,
    default: () => [],
  },
  setpurposeLoanObj: {
    type: Object,
    required: false,
    default: () => {},
  },
})

const emit = defineEmits(['setData'])

const dialogRef = ref(null)
const activeIndex = ref(null)
const purposeLoanData = ref({ index: null, text: '' })

// 可以获取已选的下游企业
const handleChangeIndex = item => {
  activeIndex.value = item.key
  purposeLoanData.value.index = item.key
  purposeLoanData.value.text = item.value
}

const handleOpen = () => {
  dialogRef.value.handleOpen()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}

const handleConfirm = () => {
  if (!activeIndex.value) {
    message.warn('请选择融资用途')
    return
  }
  emit('setData', JSON.parse(JSON.stringify(purposeLoanData.value)))
  handleClose()
}

const handleReset = val => {
  activeIndex.value = val.index
  purposeLoanData.value.index = val.index
  purposeLoanData.value.text = val.text
}

watch(
  () => props.setpurposeLoanObj,
  val => {
    if (!val) return
    activeIndex.value = val.index
    purposeLoanData.value.index = val.index
    purposeLoanData.value.text = val.text
  },
  { deep: true, immediate: true }
)

defineExpose({
  handleOpen,
  handleReset,
})
</script>

<style lang="scss" scoped>
.base-card-check-item {
  margin-bottom: 8px;

  .account-item {
    height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #0a1f44;
    line-height: 24px;
  }

  :last-child {
    margin-bottom: 0;
  }
}
</style>
