<template>
  <GlobalDialog
    :title="text ? text : '还款试算'"
    width="1200px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="trade-space-box">
      <div class="label-tab-bar">
        <LabelBar
          :labelList="filterArr.filterTabBar"
          :style="'display: inline-flex'"
          :state="tabBarObj.tabBarIndex"
          @switch="handleSwitch"
        />
        <div>
          <BillingRulesToolTip />
        </div>
      </div>
      <div class="cost-box">
        <div class="table-father-box">
          <div class="table-box">
            <div class="table-title-box">
              <span class="table-bank">银行利息</span>
              <span class="long-string" />
              <span class="interest-rate"
                >日利率{{
                  dataAnnualInterestRateObj.dailyInterestRate
                }}%（年化利率{{
                  dataAnnualInterestRateObj.annualInterestRate
                }}%）</span
              >
            </div>
            <BaseTableDetailTotal
              :tableData="tableData1"
              :columns="columns1"
              :loading="tableDataLoad"
            />
          </div>
          <div class="otherFee" v-if="otherFeeData.length">
            <div
              v-for="item in otherFeeData"
              :key="item.id"
              class="table-box"
              style="margin-top: 40px"
              :style="{ display: showExpenseList ? 'block' : 'none' }"
            >
              <div class="table-title-box" style="display: block">
                <span class="table-bank">{{ item.parenExpenseName }}</span>
              </div>
              <BaseTableDetailTotal
                :tableData="item.expenseOrderDetailList"
                :columns="columns2"
                :loading="tableDataLoad"
              />
            </div>
          </div>
        </div>
        <div class="all-money-box">
          <div class="left-money-box">
            <span>
              <MySvgIcon
                icon-class="icon-xinxi"
                style="color: #8a94a6; font-size: 24px"
              ></MySvgIcon>
            </span>
            <span>以上仅为试算费用，以实际放款为准～</span>
          </div>
          <div class="right-money-box">
            <span>合计:</span>
            <span>{{ `￥${formatMoney(allMonry)}` }}</span>
          </div>
        </div>
      </div>
    </div>
    <template #button>
      <div style="width: 100%; text-align: right">
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 12px"
          round
          :bordered="false"
          @click="handleClose"
        >
          取消
        </n-button>
        <n-button
          class="border blue button-item primary"
          type="info"
          style="height: 40px"
          round
          :bordered="false"
          @click="handleConfirm"
        >
          确认
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'dialogReimburSement',
}
</script>
<script setup>
import { reactive, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import BaseTableDetailTotal from '@/components/BaseTableDetailTotal/index.vue'
import BillingRulesToolTip from '../BillingRules/tooltip.vue'
import { formatMoney } from '@/utils/utils'
import { PRODUCT_APPLI_CATIONS_API } from '@/api/index'
// import { requestDictMap } from '@/api/common/index'
import { NButton } from 'naive-ui'
import dayjs from 'dayjs'
const route = useRoute()
const store = useStore()

const props = defineProps({
  allNumMoney: {
    type: [Number, String],
    required: true,
    default: 0,
  },
  loanPeriod: {
    type: Number,
    required: true,
    default: 0,
  },
  loadTermUnit: {
    type: Number,
    required: true,
    default: 1,
  },
  tabBarObj: {
    type: Object,
    required: true,
    default: () => {},
  },
  filterArr: {
    type: Object,
    required: true,
    default: () => {},
  },
  annualInterestRateObj: {
    type: Object,
    required: true,
    default: () => {},
  },
  text: {
    type: String,
    required: false,
  },
  showAmount: {
    type: Boolean,
    required: false,
    default: false,
  },
  financeNoId: {
    required: false,
  },
  showExpenseList: {
    type: Boolean,
    required: false,
    default: true,
  },
  startTime: {
    type: String,
    required: false,
    default: '',
  },
  // 是否逾期协商，要变更其他费用接口
  isNegotiate: {
    type: Boolean,
    required: false,
    default: false,
  },
  chargeMethod: {
    type: String,
  },
  // 逾期协商罚款对象
  penaltyObj: {
    type: Object,
    required: false,
    default: () => {},
  },
  // 产品类型默认应收账款
  gooDType: {
    type: Number,
    required: false,
    default: 1,
  },
})
//  这个是其他费用的 资方分别收取用的
const otherFeeData = ref([])
const emit = defineEmits(['saveRepaymentType'])
const columns1 = ref([
  {
    title: '还款日期',
    dataIndex: 'refundTime',
  },
  {
    title: '应还总额',
    dataIndex: 'monthlySupply',
  },
  {
    title: '应还本金',
    dataIndex: 'monthlyPrincipal',
  },
  {
    title: '应还利息',
    dataIndex: 'planInterest',
  },
])
const columns2 = [
  {
    title: '费用名称',
    dataIndex: 'expenseTypeStr',
  },
  // {
  //   title: '费用类型',
  //   dataIndex: 'feeNameType',
  // },
  // {
  //   title: '支付节点',
  //   dataIndex: 'feeNodeStr',
  // },
  {
    title: '期数',
    dataIndex: 'repaymentTerm',
  },
  {
    title: '收费节点',
    dataIndex: 'collectFeesNodeStr',
  },
  {
    title: '计费方式',
    dataIndex: 'feeFormulaName',
  },
  {
    title: '应付金额',
    dataIndex: 'amount',
  },
]
const tableDataLoad = ref(false)
const dialogRef = ref(null)
const handleSwitchObj = reactive({
  tabBarIndex: 0,
  filtArrKey: void 0,
})
const allMonry = ref(0)
const tableData1 = ref([])
// const tableData2 = ref([])
const dataAnnualInterestRateObj = ref({})
const appliCationsData = computed(
  () => store.getters['Product/appliCations']
)

const handleOpen = () => {
  handleSwitchObj.tabBarIndex = props.tabBarObj.tabBarIndex
  handleSwitchObj.filtArrKey = props.tabBarObj.tabBarIndexVal
    ? props.tabBarObj.tabBarIndexVal
    : props.filterArr.filterTabBarVal[0]
  if (props.loadTermUnit == 2) {
    if (columns1.value.length == 4) {
      columns1.value.unshift({
        title: '期数',
        dataIndex: 'name',
      })
    }
  }
  dialogRef.value.handleOpen()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}

const handleConfirm = () => {
  emit('saveRepaymentType', handleSwitchObj)
  handleClose()
}

const handleSwitch = val => {
  handleSwitchObj.tabBarIndex = val
  handleSwitchObj.filtArrKey = props.filterArr.filterTabBarVal[val]
  onload()
  // ProductDetailTableRef.value.handleSwitch(value)
}

// 合计
const allMonrySum = item => {
  allMonry.value = 0
  allMonry.value += Number(item.monthlySupply)
  if (otherFeeData.value.length) {
    for (const item of otherFeeData.value) {
      if (item.expenseOrderDetailList.length) {
        allMonry.value += Number(
          item.expenseOrderDetailList[item.expenseOrderDetailList.length - 1]
            .countAmount
        )
      }
    }
  }
  // otherFeeData.value.forEach(item => {
  //   if (item.platformExpensesVOS.length) {
  //     allMonry.value += Number(
  //       item.platformExpensesVOS[
  //         item.platformExpensesVOS.length - 1
  //       ].amount.split('￥')[1]
  //     )
  //   }
  // })
}
const tarGet = ref(false)
// 银行利息
const repaymentCalculation = () => {
  tableDataLoad.value = true
  if (!props.allNumMoney) return // 金额不能为0
  const data = {
    financeAmount: props.allNumMoney, // 融资金额
    overdueInterest: props.penaltyObj?.overdueInterest || 0, // 逾期罚息
    penaltyInterest: props.penaltyObj?.penaltyInterest || 0, // 违约金
    processInterest: props.penaltyObj?.processInterest || 0, // 手续费
    annualInterestRate: props.annualInterestRateObj?.annualInterestRate, // 年利率
    totalTerm: props.loanPeriod, // 总期数
    startTime: props.startTime || dayjs().format('YYYY-MM-DD'), // 开始时间
    refundType: handleSwitchObj.filtArrKey, // 还款类型 '1','2','3'
    loadTermUnit: props.loadTermUnit, // 1-天,2-期
    goodsId: route.query.goodId,
    chargePoint: 8, // 8 融资申请
    loanDay: props.loanPeriod, // 融资天数
    goodType: props.gooDType,
    pledgeQuotaNo:appliCationsData.value[0].quotaNo,
  }
  PRODUCT_APPLI_CATIONS_API.pledgeRepaymentFinanceApplyCalculation(data).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      const dat = resData.data
      dataAnnualInterestRateObj.value = {
        dailyInterestRate: dat?.showRepaymentPlan.dayRate || 0,
        annualInterestRate: dat?.showRepaymentPlan.yearRate || 0,
      }
      // 记录那些是一次性付清的
      let Map = {}
      const chargeMethod = route.query.chargeMethod
      if (chargeMethod == 1) {
        // columns1.push
        dat.showRepaymentPlan.stagRecords[0]?.planFeeList?.forEach(
          (item, index) => {
            Map[item.feeName] = {
              index,
              collectFeeMethod: item.collectFeeMethod,
              amount: item.amount,
            }
            // 第一次加载添加，第二次不添加
            if (!tarGet.value) {
              columns1.value.push({
                title: item.feeName,
                dataIndex: 'amount' + index,
              })
            }
          }
        )
        tarGet.value = true
      } else {
        dat.expenseOrderDetailFinanceVos.forEach(item => {
          let amount = item.expenseOrderDetailList.reduce((prve, next) => {
            next.repaymentTerm = next.repaymentTerm
              ? next.repaymentTerm + '期'
              : '--'
            const nAmo = Number(next.amount)
            if (next.feeFormulaName === '手填') {
              if (nAmo) {
                return prve + nAmo
              } else {
                next.amount = '人工计算'
                return prve
              }
            }
            return prve + nAmo
          }, 0)
          item.expenseOrderDetailList.push({
            name: '总计:',
            expenseTypeStr: '',
            feeNodeStr: '',
            feeFormulaName: '',
            amount: `￥${formatMoney(amount)}`,
            countAmount: amount,
          })
        })
        otherFeeData.value = dat.expenseOrderDetailFinanceVos
      }
      tableData1.value = []
      let index = 0
      for (const item of dat.showRepaymentPlan.stagRecords) {
        if (props.loadTermUnit == 1) {
          allMonrySum(item)
          tableData1.value.push({
            // name: '1期',
            refundTime: item.refundTime,
            monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
            monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
            planInterest: `￥${formatMoney(item.planInterest)}`,
          })
          tableData1.value.push({
            // name: '总计:',
            refundTime: '总计:',
            monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
            monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
            planInterest: `￥${formatMoney(item.planInterest)}`,
          })
          //  资方统一清分动态费用 随借随还的逻辑
          if (chargeMethod == 1&&item.planFeeList!=null) {
            item.planFeeList.forEach((citem, cindex) => {
              tableData1.value[0][`amount${cindex}`] = `￥${formatMoney(
                citem.amount
              )}`
              tableData1.value[1][`amount${cindex}`] = `￥${formatMoney(
                citem.amount
              )}`
            })
          }
        } else {
          if (!item.term) {
            allMonrySum(item)
          }
          tableData1.value.push({
            name: item.term ? `${item.term}期` : '总计:',
            refundTime: item.refundTime,
            monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
            monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
            planInterest: `￥${formatMoney(item.planInterest)}`,
          })
          if (
            index != dat.showRepaymentPlan.stagRecords.length - 1 &&
            chargeMethod == 1
          ) {
            if (
              index == 0 ||
              item?.planFeeList.length ==
                dat.showRepaymentPlan.stagRecords[0].length
            ) {
              item?.planFeeList?.forEach((citem, cindex) => {
                tableData1.value[index][`amount${cindex}`] = `￥${formatMoney(
                  citem.amount
                )}`
              })
            } else {
              for (const key in Map) {
                tableData1.value[index][
                  `amount${Map[key].index}`
                ] = `￥${formatMoney(0)}`
              }
              item?.planFeeList?.forEach(citem => {
                if (Map[citem.feeName]) {
                  let data = Map[citem.feeName]
                  tableData1.value[index][
                    `amount${data.index}`
                  ] = `￥${formatMoney(citem.amount)}`
                }
              })
            }
          } else if (
            index == dat.showRepaymentPlan.stagRecords.length - 1 &&
            chargeMethod == 1
          ) {
            for (const key in Map) {
              // 一次性付清
              if (Map[key].collectFeeMethod == 1) {
                tableData1.value[index][
                  `amount${Map[key].index}`
                ] = `￥${formatMoney(Map[key].amount)}`
              } else {
                // 分期
                tableData1.value[index][`amount${Map[key].index}`] = `￥${(
                  formatMoney(Map[key].amount) * index
                ).toFixed(2)}`
              }
            }
          }
          index++
        }
      }
      tableDataLoad.value = false
    }
  })
}

// 逾期协商其他费用
// const cKexpenseList = (arr, arr1) => {
//   const params = {
//     businessId: props.financeNoId,
//   }
//   PRODUCT_APPLI_CATIONS_API.cKexpenseList(params).then(res => {
//     const resData = res.data
//     if (resData.code == 200) {
//       const dat = resData.data
//       tableData2.value = []
//       let sum = 0
//       for (const item of dat) {
//         sum += Number(item.amount)
//         // 过滤出当前的支付节点
//         const chargePointFilter = arr.filter(
//           itemed => itemed.key == item.feeNode
//         )
//         // 过滤出当前的费用类型
//         const expenseTypeFilter = arr1.filter(
//           itemed => itemed.key == item.expenseType
//         )

//         tableData2.value.push({
//           name: item.name,
//           expenseTypeStr: expenseTypeFilter?.[0]?.value,
//           chargePoint: chargePointFilter?.[0]?.value,
//           chargeMethodStr:
//             item.calculation != 1 ? item.feeFormulaName : '人工计算',
//           amount:
//             item.calculation != 1 ? `￥${formatMoney(item.amount)}` : '待计算',
//         })
//       }
//       // 总计
//       if (tableData2.value.length) {
//         tableData2.value.push({
//           name: '总计:',
//           expenseTypeStr: '',
//           chargePoint: '',
//           chargeMethodStr: '',
//           amount: `￥${formatMoney(sum)}`,
//         })
//         allMonry.value += sum
//       }
//       setTimeout(() => {
//         tableDataLoad.value = false
//       }, 200)
//     }
//   })
// }

// 其他费用
// const expenseList = (arr, arr1) => {
//   const params = {
//     goodsId: route.query.goodId,
//     chargePoint: 8, // 8 融资申请
//     financeAmount: props.allNumMoney, // 融资金额
//     loanDay: props.loanPeriod, // 融资天数
//   }
//   PRODUCT_APPLI_CATIONS_API.expenseList(params).then(res => {
//     const resData = res.data
//     if (resData.code == 200) {
//       const dat = resData.data
//       tableData2.value = []
//       let sum = 0
//       for (const item of dat) {
//         sum += Number(item.amount)
//         // 过滤出当前的支付节点
//         const chargePointFilter = arr.filter(
//           itemed => itemed.key == item.feeNode
//         )
//         // 过滤出当前的费用类型
//         const expenseTypeFilter = arr1.filter(
//           itemed => itemed.key == item.expenseType
//         )

//         tableData2.value.push({
//           name: item.name,
//           expenseTypeStr: expenseTypeFilter?.[0]?.value,
//           chargePoint: chargePointFilter?.[0]?.value,
//           chargeMethodStr:
//             item.calculation != 1 ? item.feeFormulaName : '人工计算',
//           amount:
//             item.calculation != 1 ? `￥${formatMoney(item.amount)}` : '待计算',
//         })
//       }
//       // 总计
//       if (tableData2.value.length) {
//         tableData2.value.push({
//           name: '总计:',
//           expenseTypeStr: '',
//           chargePoint: '',
//           chargeMethodStr: '',
//           amount: `￥${formatMoney(sum)}`,
//         })
//         allMonry.value += sum
//       }
//       setTimeout(() => {
//         tableDataLoad.value = false
//       }, 200)
//     }
//   })
// }

// 融资申请完成后的放款中的其他费用
// const loanExpenseList = (arr, arr1, ids) => {
//   PRODUCT_APPLI_CATIONS_API.expensesList(ids).then(res => {
//     const resData = res.data
//     if (resData.code == 200) {
//       const dat = resData.data
//       tableData2.value = []
//       let sum = 0
//       for (const item of dat) {
//         sum += Number(item.amount)
//         // 过滤出当前的支付节点
//         const chargePointFilter = arr.filter(
//           itemed => itemed.key == item.feeNode
//         )
//         // 过滤出当前的费用类型
//         const expenseTypeFilter = arr1.filter(
//           itemed => itemed.key == item.expenseType
//         )
//         tableData2.value.push({
//           name: item.name,
//           expenseTypeStr: expenseTypeFilter?.[0]?.value,
//           chargePoint: chargePointFilter?.[0].value,
//           chargeMethodStr:
//             item.calculation != 1 ? item.feeFormulaName : '人工计算',
//           amount: `￥${formatMoney(item.amount)}`,
//         })
//       }
//       // 总计
//       if (tableData2.value.length) {
//         tableData2.value.push({
//           name: '总计:',
//           expenseTypeStr: '',
//           chargePoint: '',
//           chargeMethodStr: '',
//           amount: `￥${formatMoney(sum)}`,
//         })
//         allMonry.value += sum
//       }
//       setTimeout(() => {
//         tableDataLoad.value = false
//       }, 200)
//     }
//   })
// }

const onload = () => {
  repaymentCalculation()

  // requestDictMap('goods_expense_rule_fee_node').then(res => {
  //   const resData = res.data
  //   if (resData.code == 200) {
  //     // 处理字典数据
  //     const resList = []
  //     for (const item of resData.data) {
  //       resList.push({
  //         key: item.dictKey,
  //         value: item.dictValue,
  //         id: item.id,
  //       })
  //     }
  //     requestDictMap('goods_expense_rule_type').then(res => {
  //       const resData = res.data
  //       if (resData.code == 200) {
  //         // 处理字典数据
  //         const resList1 = []
  //         for (const item of resData.data) {
  //           resList1.push({
  //             key: item.dictKey,
  //             value: item.dictValue,
  //             id: item.id,
  //           })
  //         }
  //         // 还款计划需要展示金额，融资申请时不需要
  //         if (props.showAmount) {
  //           // loanExpenseList(resList, resList1, props.financeNoId)
  //         } else if (props.isNegotiate) {
  //           // 是否展示其他费用
  //           if (props.showExpenseList) {
  //             // cKexpenseList(resList, resList1)
  //           } else {
  //             tableDataLoad.value = false
  //           }
  //         } else {
  //           // expenseList(resList, resList1)
  //         }
  //       }
  //     })
  //   }
  // })
}

defineExpose({
  handleOpen,
  onload,
})
</script>

<style lang="scss" scoped>
.trade-space-box {
  .label-tab-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  .cost-box {
    border-radius: 8px;
    border: 1px solid #e1e4e8;

    .table-father-box {
      // max-height: calc(80vh - 250px);
      // overflow: auto;

      .table-box {
        margin: 24px 24px 0 24px;
        .table-title-box {
          display: flex;
          align-items: center;
          margin-bottom: 20px;

          .table-bank {
            width: 64px;
            height: 24px;
            font-size: 16px;
            font-weight: 500;
            color: #53627c;
            line-height: 24px;
          }
          .long-string {
            display: inline-block;
            width: 1px;
            height: 16px;
            background: #b5bbc6;
            margin: 0 12px 0 12px;
          }
          .interest-rate {
            height: 24px;
            font-size: 16px;
            font-weight: 500;
            color: #8a94a6;
            line-height: 24px;
          }
        }
      }
    }

    .all-money-box {
      height: 76px;
      background: #ebf5ff;
      border-radius: 0px 0px 8px 8px;
      border-top: 1px solid #e1e4e8;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24px;

      .left-money-box {
        display: flex;
        align-items: center;
        margin-left: 40px;

        & > span:last-child {
          height: 24px;
          font-size: 16px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 24px;
          margin-left: 4px;
        }
      }

      .right-money-box {
        display: flex;
        align-items: center;
        margin-right: 24px;

        & > span:first-child {
          width: 51px;
          height: 28px;
          font-size: 20px;
          font-weight: 600;
          color: #0a1f44;
          line-height: 28px;
        }

        & > span:last-child {
          height: 40px;
          font-size: 30px;
          color: #0a1f44;
          line-height: 38px;
          font-weight: 800;
        }
      }
    }
  }
}
</style>
