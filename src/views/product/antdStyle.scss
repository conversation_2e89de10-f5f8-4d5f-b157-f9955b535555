// 使用尾标
:deep(.ant-input-affix-wrapper) {
  padding: 0 11px;

  &.radius {
    border-radius: 100px;
    .ant-input {
      padding-left: 2px;
      box-sizing: border-box;
      border-radius: 100px;
    }
  }

  &.large {
    height: 48px;
    .ant-input {
      height: 46px;
    }
  }

  &.small {
    height: 32px;
    .ant-input {
      height: 30px;
    }
  }
}

// 正常情况
:deep(.ant-input) {
  height: 40px;
  box-sizing: border-box;
  margin: 0;
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: 'tnum';
  position: relative;
  display: inline-block;
  width: 100%;
  min-width: 0;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  line-height: 1.5715;
  // background-color: #fff;
  background-image: none;
  // border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.3s;

  &.large {
    height: 48px;
  }

  &.small {
    height: 32px;
  }

  &.radius {
    border-radius: 100px;
  }
}

:deep(.ant-input:hover) {
  border-color: #40a9ff;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}
