<template>
  <div class="repaymentInformation-box">
    <basic-container>
      <div class="repayment-title">
        <div class="repayment-name">还款信息</div>
        <div class="repayment-code">
          <span>计划还款单号:</span>
          <span>2974300920123</span>
        </div>
      </div>
      <div class="table-top refund">
        <el-table
          ref="table3"
          :data="tableData"
          style="width: 100%; margin-top: 13px"
          class="table-border-style"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            prop="index"
            label="#"
            width="110"
            align="center"
          ></el-table-column>
          <el-table-column prop="invoiceType" label="还款单号">
          </el-table-column>
          <el-table-column prop="invoiceCode" label="还款时间">
          </el-table-column>
          <el-table-column prop="invoiceNum" label="应还金额"></el-table-column>
          <el-table-column prop="monthlyPrincipal" label="实还金额">
            <template slot-scope="scope">
              <span>{{ scope.row.monthlyPrincipal | formatMoney }} </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="monthlyInterest"
            label="还款类型"
          ></el-table-column>
          <el-table-column
            prop="startDay"
            label="状态"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <!-- <div style="text-align: center"> -->
              <span class="init-state" :class="getStateColor(scope.row.status)"
                >支付</span
              >
              <!-- </div> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
    </basic-container>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'

export default {
  data() {
    return {
      tableData: [
        {
          status: 1,
        },
      ],
    }
  },
  methods: {
    tableRowClassName({ row }) {
      if (!row.refundTime) {
        return 'aggregate-row'
      }
      return ''
    },
    getStateColor(state) {
      let color = ''
      switch (state) {
        case 0:
          color = 'state-gray'
          break
        case 1:
          color = 'state-blue'
          break
        case 2:
          color = 'state-green'
          break
        case 3:
          color = 'state-red'
          break
      }
      return color
    },
  },
}
</script>

<style lang="scss" scoped>
.repaymentInformation-box {
  margin-top: 10px;
  .repayment-title {
    display: flex;
    align-items: center;

    .repayment-name {
      padding-right: 8px;
      border-right: 1px solid #d7d7d7;
      line-height: 22px;
      color: rgab(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
    }
    .repayment-code {
      display: flex;
      align-items: center;
      margin-left: 8px;
      & span {
        display: block;
        line-height: 22px;
        font-size: 14px;
        font-weight: 500;
      }
      & span:first-child {
        color: #7d7d7d;
        margin-right: 6px;
      }
      & span:last-child {
        color: #697cff;
      }
    }
  }
}
.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}
.init-state {
  display: inline-block;
  width: 76px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #d7d7d7;
}
.state-gray {
  border-color: #d7d7d7;
  color: #7d7d7d;
}
.state-blue {
  border-color: #697cff;
  color: #697cff;
}

.state-green {
  border-color: #3dc861;
  color: #3dc861;
}
.state-red {
  border-color: #fb3030;
  color: #fb3030;
}

// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
