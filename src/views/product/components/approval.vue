<template>
  <div class="approval-box">
    <!-- 融资申请流程 -->
    <basic-container>
      <el-collapse v-model="activeNames1" @change="handleChange1">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change1Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>融资申请</span>
                  <span class="long-string" />
                  <span class="interest-rate">任务编号：</span>
                  <span class="interest-rate" style="color: #697cff">
                    2974300920123
                  </span>
                </h1>
              </div>
            </div>
          </template>
          <!--  -->
          <wf-flow
            :flow="financeFlowList"
            style="margin-top: 25px; margin-left: 7px"
          ></wf-flow>
        </el-collapse-item>
      </el-collapse>
    </basic-container>

    <!-- 放款申请流程 -->
    <basic-container>
      <el-collapse v-model="activeNames2" @change="handleChange2">
        <el-collapse-item name="furtherInformation">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': change2Type,
                  }"
                ></i>
                <h1 class="fromLeft-title">
                  <span>放款申请</span>
                  <span class="long-string" />
                  <span class="interest-rate">任务编号：</span>
                  <span class="interest-rate" style="color: #697cff">
                    2974300920123
                  </span>
                </h1>
              </div>
            </div>
          </template>
          <!--  -->
          <wf-flow
            :flow="loanFlowList"
            style="margin-top: 25px; margin-left: 7px"
          ></wf-flow>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
// import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'

export default {
  // mixins: [customExForm],
  components: { WfFlow },
  watch: {},
  props: {
    approveData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      activeNames1: [],
      activeNames2: [],
      change1Type: false,
      change2Type: false,
      financeFlowList: [
        {
          appFormUrl: null,
          assignee: null,
          assigneeName: '深圳市企链通科技有限公司',
          attachment: null,
          attachments: null,
          businessId: null,
          businessTable: null,
          category: null,
          categoryName: null,
          claimTime: null,
          comment: null,
          comments: null,
          copyUser: null,
          copyUserName: null,
          createTime: '2022-05-23 18:07:38',
          endTime: '2022-05-23 18:07:38',
          endTimeRange: null,
          executionId: null,
          formKey: null,
          formUrl: null,
          hideAttachment: null,
          hideCopy: null,
          hideExamine: null,
          historyActivityDurationTime: '',
          historyActivityId: 'startEvent_1',
          historyActivityName: '开始',
          historyActivityType: 'startEvent',
          historyProcessInstanceId: null,
          historyTaskEndTime: null,
          isMultiInstance: null,
          isOwner: null,
          isReturnable: null,
          nodeId: null,
          pass: false,
          processDefinitionDesc: null,
          processDefinitionDiagramResName: null,
          processDefinitionId: null,
          processDefinitionKey: null,
          processDefinitionName: null,
          processDefinitionResName: null,
          processDefinitionVersion: 0,
          processInstanceId: null,
          processIsFinished: null,
          processNo: null,
          processStatus: null,
          serialNumber: null,
          startTimeRange: null,
          startUsername: null,
          status: null,
          taskDefinitionKey: null,
          taskId: null,
          taskName: null,
          variables: null,
          withdrawType: null,
          xml: null,
        },
      ],
      loanFlowList: [],
    }
  },
  methods: {
    handleChange1() {
      // 融资申请流程折叠面板收缩控制
      this.change1Type = !this.change1Type
    },
    handleChange2() {
      // 放款申请流程折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
  },
}
</script>

<style lang="scss" scoped>
.approval-box {
  margin-top: 10px;

  .fromHeader {
    width: 100%;
    font-size: 16px;
    background: #fff;
    color: rgba(16, 16, 16, 100);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .fromLeft {
      display: flex;
      align-items: center;
      width: 60%;

      .fromLeft-title {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        display: flex;
        align-items: center;

        .long-string {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        .interest-rate {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .i-active {
      transform: rotate(0deg) !important;
    }
  }

  // 更改折叠面板默认样式
  ::v-deep {
    i.el-icon-caret-bottom {
      font-size: 150%;
      transform: rotate(-90deg);
      transition: transform 0.4s;
    }
    i.el-collapse-item__arrow {
      display: none;
    }
    div.el-collapse-item__header {
      height: 15px;
      border-bottom: none;
    }
    div.el-collapse-item__content {
      padding-bottom: 0 !important;
    }
    .el-collapse {
      border-top: none;
      border-bottom: none;
    }
    .el-card {
      border-radius: 8px;
      .el-collapse-item__wrap {
        border-bottom: none;
      }
    }
  }
}
</style>
