<template>
  <div class="ProductsMining">
    <h1 class="title-ProductsMining">支持代采商品</h1>
    <h3 class="introduce-ProductsMining">
      <span>优质货源，多家高端品牌供应商入驻，更多好货等待您的选购</span>
      <span class="viewMore-box" @click="handleViewMore()">
        查看更多
        <MySvgIcon
          icon-class="icon-youjiantou"
          style="fill: #8a94a6; font-size: 16px"
        ></MySvgIcon>
      </span>
    </h3>
    <div class="ProductsMining-for-box">
      <div
        class="ProductsMining-for-item"
        @click="goFinancing(item.id)"
        v-for="item in ProductsMiningArr"
        :key="item.id"
      >
        <div class="item-img-box">
          <img
            :src="item.attachList?.[0].link"
            alt=""
            ondragstart="return false"
          />
        </div>
        <div class="item-padding-box">
          <div class="item-title-box">{{ item.name }}</div>
          <div class="item-sum-box">
            <span class="rmb-symbol">￥</span>
            <span class="rmb-num">{{ item.minPriceStr1 }}.</span>
            <span class="rmb-decimals">{{ item.minPriceStr2 }}</span>
            <span class="rmb-text">起</span>
          </div>
          <div class="transverse-line" />
          <div class="company-box">
            <span class="img-company-box">
              <img :src="item.supplierLogo" alt="" />
            </span>
            <span class="name-company-box">{{ item.supplierName }}</span>
          </div>
        </div>
      </div>
    </div>
    <a-row :gutter="24" v-if="isLoading">
      <a-col :span="6" v-for="item in 8" :key="item">
        <a-card class="ProductsMining-for-item skeleton-card">
          <a-skeleton :paragraph="{ rows: 4 }" loading active />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ProductsMining',
}
</script>
<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
import { ref } from 'vue'
import { PRODUCT_API } from '@/api/index'

const isLoading = ref(true)
const ProductsMiningArr = ref([])

// 获取商品list
const purchaseCommodityPageFun = () => {
  isLoading.value = true
  const params = {
    goodsId: route.query.goodId,
    size: 8,
    current: 1,
  }
  PRODUCT_API.purchaseCommodityPage(params)
    .then(({ data }) => {
      if (data.success) {
        const { data: resData } = data
        for (const item of resData.records) {
          const [minPriceStrRoot1, minPriceStrRoot2] = String(
            item.minPrice
          ).split('.')
          ProductsMiningArr.value.push({
            ...item,
            minPriceStr1: minPriceStrRoot1,
            minPriceStr2: minPriceStrRoot2,
          })
        }
        isLoading.value = false
      }
    })
    .catch(() => {
      isLoading.value = false
    })
}
purchaseCommodityPageFun()

/**
 * 查看商品详情
 * @description:
 * @param {*} id 商品id
 * @return {*}
 * @author: 95hwp27
 */
const goFinancing = id => {
  router.push({
    name: 'productMiningApply',
    query: { id },
  })
}

// 查看更多
const handleViewMore = () => {
  router.push({
    name: 'generationOfMiningMallDetail',
    query: { goodId: route.query.goodId },
  })
}
</script>

<style lang="scss" scoped>
.ProductsMining {
  margin: auto;
  max-width: 1400px;
  box-sizing: border-box;
  margin-top: 100px;

  .title-ProductsMining {
    height: 56px;
    font-size: 40px;
    @include family-PingFangSC-Semibold;
    font-weight: 600;
    color: #0a1f44;
    line-height: 56px;
    margin-bottom: 12px;
    text-align: left;
  }
  .introduce-ProductsMining {
    height: 24px;
    font-size: 16px;
    font-weight: 400;
    color: #53627c;
    line-height: 24px;
    margin-bottom: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .viewMore-box {
      font-size: 14px;
      color: #8a94a6;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }

  .ProductsMining-for-box {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
  }

  $spance: 24px;
  .skeleton-card {
    margin-bottom: $spance;
  }
  .ProductsMining-for-item {
    width: 332px;
    height: 488px;
    background: #ffffff;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    border-radius: 16px;
    border: 0;
    display: flex;
    flex-direction: column;
    justify-content: end;
    padding-bottom: 22px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    border: 1px solid #ffffff00;
    margin: 0 $spance $spance 0;
    cursor: pointer;

    &:nth-child(5n-1) {
      margin-right: 0;
    }
    // &:nth-child(4) {
    //   margin: $spance 0;
    // }
    // &:nth-child(5) {
    //   margin: $spance $spance;
    // }
    // &:nth-child(6) {
    //   margin: $spance 0;
    // }
    &:hover {
      border: 1px solid #0c66ff;
    }
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      width: 166px;
      height: 225px;
      bottom: -40px;
      right: -30px;
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.13;
      border-radius: 150px;
      filter: blur(30px);
    }
    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      width: 137px;
      height: 181px;
      top: -20px;
      left: -55px;
      background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
      opacity: 0.24;
      border-radius: 150px;
      filter: blur(47px);
    }

    .item-img-box {
      width: 332px;
      height: 332px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;

      img {
        width: 100%;
        object-fit: cover;
      }
    }
    .item-padding-box {
      padding: 0 16px;
      box-sizing: border-box;

      .item-title-box {
        width: 300px;
        height: 28px;
        font-size: 20px;
        font-weight: 400;
        color: #0a1f44;
        line-height: 28px;
        margin-top: 17px;
      }
      .item-sum-box {
        display: flex;
        justify-content: flex-start;
        align-items: baseline;
        margin-top: 12px;
        color: #dd2727;
        line-height: 24px;
        font-size: 14px;

        .rmb-num {
          font-size: 20px;
        }
        .rmb-text {
          font-size: 16px;
          margin-left: 1px;
        }
      }
      .transverse-line {
        width: 100%;
        height: 1px;
        background: #f1f2f4;
        margin-top: 16px;
      }
      .company-box {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: 16px;

        .img-company-box {
          width: 16px;
          height: 16px;
          border: 1px solid #efefef;
          border-radius: 100px;
          margin-right: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;

          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name-company-box {
          height: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
