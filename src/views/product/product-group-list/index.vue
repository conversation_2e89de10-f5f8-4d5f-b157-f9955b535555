<template>
  <div class="accounts-receivable-list">
    <div class="accounts-box">
      <header>
        <h1 class="he-title">资金产品</h1>
        <h4 class="he-info">汇聚多个资方，多种融资选择，融资方案多样化！</h4>
      </header>
      <article>
        <div class="screen-box" v-if="false">
          <!-- filterTabBar -->
          <div class="screen-left-box">
            <!-- <LabelBar :labelList="labelList" @switch="handleSwitch" /> -->
            <!-- <div class="screen-bottom-left-box">
              共查询到 {{ filterBox.total }} 个产品
            </div> -->
          </div>
          <div class="screen-right-box">
            <!-- 选择还款方式 -->
            <!-- <div class="modeOfRepayment-select">
              <a-select
                v-model:value="filterBox.modeOfRepayment"
                show-search
                placeholder="选择还款方式"
                :options="filterBox.modeselectOptions"
                size="large"
                :allowClear="true"
                :filter-option="filterOption"
                @change="handleVisibleChange"
              >
                <template #suffixIcon>
                  <MySvgIcon
                    icon-class="icon-xiajiantou"
                    style="font-size: 20px; fill: #344564"
                  />
                </template>
              </a-select>
            </div> -->
            <!-- 选择资金方 -->
            <!-- <div class="theFundingParty-select">
              <a-select
                v-model:value="filterBox.theFundingParty"
                show-search
                placeholder="选择资金方"
                :options="filterBox.theFunselectOptions"
                size="large"
                :allowClear="true"
                :filter-option="filterOption"
                @change="handleVisibleChange"
              >
                <template #suffixIcon>
                  <MySvgIcon
                    icon-class="icon-xiajiantou"
                    style="font-size: 20px; fill: #344564"
                  />
                </template>
              </a-select>
            </div> -->
            <!-- 请输入关键字查询 -->
            <!-- <div class="keywordQuery-input">
              <a-input
                class="large"
                style="border-radius: 4px"
                placeholder="请输入关键字查询"
                :allowClear="true"
                v-model:value="filterBox.keywordQuery"
                @change="handlePressEnter"
              >
                <template #suffix>
                  <MySvgIcon
                    icon-class="icon-sousuo1"
                    style="font-size: 20px; fill: #344564; cursor: pointer"
                  />
                </template>
              </a-input>
            </div> -->
          </div>
        </div>
      </article>
      <article>
        <div v-if="loadingType && goodListArr.length">
          <GoodsBox
            :loadingType="loadingType"
            :goodData="item"
            v-for="item in goodListArr"
            :key="item.id"
          />
        </div>
        <div v-else-if="!loadingType">
          <GoodsBox
            :loadingType="loadingType"
            :goodData="{}"
            v-for="item in 5"
            :key="item"
          />
        </div>
        <div v-else class="content-box-empty" style="height: 610px">
          <div class="receiving-empty">
            <div class="receiving-box">
              <img src="@/assets/images/empty_3.svg" />
              <span class="receiving-title">暂无产品</span>
              <!-- <span class="receiving-address" @click="handleOpen()">添加账户</span> -->
            </div>
          </div>
        </div>
        <div class="loading-text-box" v-show="loadingTypeText">
          正在努力加载中…
        </div>
      </article>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'productGroupList',
}
</script>
<script setup lang="ts">
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import GoodsBox from './components/goodsBox/index.vue'

import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useStore } from 'vuex'
import { formatMoney } from '@/utils/utils'
import { requestDictMap } from '@/api/common/index'
import { HOMEAPI } from '@/api/index'

const store = useStore()
const tenantId = computed(() => store.getters['Auth/tenantId'])
// taberBar筛选数据
const labelList = ['全部']
// 筛选box参数
const filterBox = reactive({
  searchType: void 0,
  // modeOfRepayment: null,
  // modeselectOptions: [],
  // theFundingParty: null,
  // theFunselectOptions: [],
  keywordQuery: null,
  total: 0,
})
const paging = reactive({
  size: 10,
  current: 1,
})
const goodListArr: any = ref([])
const loadingType = ref(false)
const loadingTypeText = ref(false)

// 无限滚动
const intersectionObserver = new IntersectionObserver(entries => {
  // 如果不可见，就返回
  if (entries[0].intersectionRatio <= 0) return
  setTimeout(() => {
    paging.current++
    goodsList(true)
  }, 500)
})

// 开始观察
onMounted(() => {
  intersectionObserver.observe(document.querySelector('.loading-text-box'))
})

onUnmounted(() => {
  // 关闭观察
  intersectionObserver.disconnect()
})

// taberBar选择事件
const handleSwitch = targetIndex => {
  filterBox.searchType = targetIndex
  goodsList()
}

// 下拉框选择事件
// const handleVisibleChange = tag => {
//   goodsList()
// }

// 按回车键搜索
const handlePressEnter = () => {
  goodsList()
}

// 选择框筛选事件
// const filterOption = (input: string, option: any) =>
//   option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0

// list请求
const goodsList = type => {
  if (!tenantId.value) {
    setTimeout(() => {
      goodsList()
    }, 200)
    return
  }
  requestDictMap('goods_load_term_unit').then(res => {
    const resData = res.data
    if (resData.code == 200) {
      // 处理字典数据
      const resList = []
      for (const item of resData.data) {
        resList.push({
          key: item.dictKey,
          value: item.dictValue,
          id: item.id,
        })
      }
      if (!type) {
        goodListArr.value = []
        loadingTypeText.value = false
        loadingType.value = false
        paging.current = 1
      }
      const params = {
        goodsType: 99, // 产品类型 1 应收 2 代采 3 云信 5 订单融资 99 资金产品
        searchType: filterBox.searchType || void 0, // 查询类型 1 可开通产品 2 优质产品 3 低利率 4 高额度
        lendingMethod: filterBox.modeOfRepayment, // 放款方式
        capitalId: filterBox.theFundingParty, // 资金方id
        enterpriseType: void 0, // 企业类型 1 融资企业 2 核心企业
        goodsName: filterBox.keywordQuery, // 产品名称
        size: paging.size, // 多少条
        current: paging.current, // 第几页
        tenantId: tenantId.value,
      }
      HOMEAPI.listGroupProduct(params).then(({ data }) => {
        // 获取应收账款产品list数据
        const { data: resData } = data
        if (data.success && resData) {
          filterBox.total = resData.total
          paging.current = resData.current
          if (resData.current < resData.pages) {
            loadingTypeText.value = true
          } else {
            loadingTypeText.value = false
          }

          for (const item of resData.records) {
            // 过滤出当前的最长期限单位
            const loadTer = resList.filter(
              itemS => itemS.key == item.loadTermUnit
            )
            const labelListData = []
            if (item.labelList) {
              for (const items of item.labelList) {
                labelListData.push({
                  name: items.name,
                  labelColor: items.labelColor,
                  id: items.id,
                })
              }
            }
            const itemObj = {
              ...item,
              loanAmountEnd: formatMoney(item.loanAmountEnd * 10000),
              loadTermUnit: loadTer.length ? loadTer[0]?.value : '',
              labelList: labelListData,
              isHighQuality: item.isHighQuality, // 是否优质产品 1 是 2 否
            }
            goodListArr.value.push(itemObj)
          }
          setTimeout(() => {
            loadingType.value = true
          }, 200)
          return
        }
        loadingType.value = true
      })
    }
  })
}

goodsList()

// 资金方list请求
// HOMEAPI.capitalList().then(({ data }) => {
//   const { data: resData } = data
//   if (data.success) {
//     const arrData = []
//     for (const item of resData) {
//       arrData.push({
//         label: item.name,
//         value: item.companyId,
//       })
//     }
//     filterBox.theFunselectOptions = arrData
//   }
// })

// 公用字典请求
// const goodsLoadTermUnit = ref([])

// const getDictionary = (params, dataS, isVal) => {
//   requestDictMap(params).then(res => {
//     const resData = res.data
//     if (resData.code == 200) {
//       // 处理字典数据
//       const resList = dataS
//       for (const item of resData.data) {
//         resList.push({
//           label: item.dictValue,
//           value: item.dictKey,
//           id: item.id,
//         })
//       }
//     }
//   })
// }
// 字典请求
// getDictionary('goods_load_term_unit', goodsLoadTermUnit.value) // 获取最长融资单位字典
// 放款方式list请求
// getDictionary('goods_lending_method', filterBox.modeselectOptions, true)
</script>

<style lang="scss" scoped>
@import '@/views/product/antdStyle.scss';

.accounts-receivable-list {
  position: relative;
  box-sizing: border-box;
  margin-bottom: -72px;
  top: -72px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 800px;
    // background: linear-gradient(180deg, #cce6ff 0%, #f6f6f6 100%);
    background-image: url('@/assets/images/home/<USER>');
  }

  .accounts-box {
    // max-width: 1400px;
    width: 99vw;
    // margin-left: 1vw;
    // margin-right: 1vw;
    // margin: 212px auto 0;
    // margin-top: 212px;
    margin: 112px auto 0;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    header {
      .he-title {
        height: 56px;
        font-size: 40px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 56px;
        text-align: center;
      }
      .he-info {
        height: 24px;
        font-size: 16px;
        font-weight: 400;
        color: #53627c;
        line-height: 24px;
        margin-top: 8px;
        text-align: center;
        // margin-bottom: 100px;
      }
    }

    .screen-box {
      // height: 132px;
      background: #ffffff;
      box-shadow: 0px 20px 60px 0px rgba(10, 31, 68, 0.04),
        0px 0px 1px 0px rgba(10, 31, 68, 0.1);
      border-radius: 16px;
      display: flex;
      justify-content: space-between;
      margin-top: 40px;
      padding: 24px;
      box-sizing: border-box;

      .screen-left-box {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .screen-bottom-left-box {
          height: 24px;
          font-size: 16px;
          font-weight: 400;
          color: #8a94a6;
          line-height: 24px;
          margin-top: 12px;
        }
      }
      :deep(.screen-right-box) {
        display: flex;
        justify-content: flex-end;

        .modeOfRepayment-select {
          margin-right: 20px;
          width: 141px;

          .ant-select {
            width: 100%;

            .ant-select-selection-item {
              font-size: 14px;
              line-height: 48px;
            }
            .ant-select-clear {
              transform: scale(1.4);
            }
          }
        }

        .theFundingParty-select {
          margin-right: 20px;
          width: 267px;

          .ant-select {
            width: 100%;
            .ant-select-selection-item {
              font-size: 14px;
              line-height: 48px;
            }
            .ant-select-clear {
              transform: scale(1.4);
            }
          }
        }

        .keywordQuery-input {
          width: 363px;
          .anticon.ant-input-clear-icon {
            transform: scale(1.4);
          }
        }

        // 覆盖antd选择器组件样式
        .ant-select-arrow {
          width: unset;
          height: unset;
          top: 40%;
          right: 8px;
        }
        .ant-select-selector {
          height: 48px;
          border-radius: 4px;
        }
        .ant-select-selection-search-input {
          height: 48px;
        }
        .ant-select-selection-placeholder {
          height: 48px;
          line-height: 48px;
          font-size: 14px;
        }
      }
    }

    .loading-text-box {
      height: 24px;
      font-size: 16px;
      font-weight: 400;
      color: #8a94a6;
      line-height: 24px;
      text-align: center;
      margin-bottom: 24px;
    }
  }

  .content-box-empty {
    display: flex;
    justify-content: center;
    align-items: center;

    .receiving-empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .receiving-box {
        display: flex;
        align-items: center;
        flex-direction: column;
        & > img {
          width: 200px;
          height: 200px;
        }
        .receiving-title {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #8a94a6;
          height: 20px;
          line-height: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
        }
        .receiving-address {
          margin-top: 12px;
          cursor: pointer;
          display: block;
          width: 96px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border: 1px solid #e1e4e8;
          color: #0a1f44;
          font-size: 14px;
          font-weight: 500;
          border-radius: 100px;
          background-color: #ffffff;
        }
      }
    }
  }
}
</style>
