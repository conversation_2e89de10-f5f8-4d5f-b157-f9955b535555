<template>
  <div class="contractSigningTwo-box">
    <!-- 合同  5 一起清分 6 独立收取-->
    <Contract
      style="margin-top: -33px"
      :flowingWave="true"
      :receiveData="finacCinId"
      :processIndex="signNodeProps === '9-1' ? 5 : 6"
      :externalParameterj="repaymentContractData"
    />
    <!-- 操作menu -->
    <div class="authorization-menu-box">
      <span v-if="!signLastStep">
        <NButton
          class="blue button-item"
          style="width: 188px; height: 48px"
          :bordered="true"
          round
          @click="lastStep()"
        >
          <span class="desc">上一步</span>
        </NButton>
      </span>
      <span>
        <NButton
          class="blue button-item primary"
          style="width: 188px; height: 48px"
          :bordered="true"
          type="info"
          round
          @click="nextToPape()"
        >
          <span class="desc">立即提交</span>
        </NButton>
      </span>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'AuthorizationS',
}
</script>
<script setup lang="ts">
import { ref, computed, inject } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
const route = useRoute()
const store = useStore()
import { message } from 'ant-design-vue'
import { NButton } from 'naive-ui'
import { PRODUCT_APPLI_CATIONS_API, PRODUCT_VERIFY_API } from '@/api/index.js'
import Contract from '@/views/product/component/contract'
import { contractListCheckId } from '@/views/product/component/contract/components/sign'

const emit = defineEmits(['setCurrentUpFun', 'previousToPape', 'nextToPape'])

// 生成合同模板使用数据
const repaymentContractData = inject('repaymentContractData')

const props = defineProps({
  signNodeProps: {
    type: String,
    required: true,
    defalut: '',
  },
})

// 判断是否有合同未签署（没配置合同就不会判断）
const signStatus = computed<boolean>(() => store.getters['Product/signStatus'])
// 判断是否有合同已经签署，有就禁止上一步按钮
const signLastStep = computed<boolean>(
  () => store.getters['Product/signLastStep']
)
const finacCinId = ref(
  JSON.parse(sessionStorage.getItem('financingDemandId')) ||
    route.query.id ||
    route.query.goodId
)
const lockSubmit = ref(true)
const demonstrator = computed<boolean>(
  () => store.getters['Product/demonstrator']
)
// 是否核心企业（Boolean）
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)

// 上一步事件
const lastStep = () => {
  if (props.signNodeProps === '9-1') {
    routerQueryDelte(0)
  } else {
    emit('previousToPape')
  }
}

// 下一步事件
const nextToPape = () => {
  if (signStatus.value && !demonstrator.value) {
    message.error('还有授权书未签署,请先签署完成')
    return
  }
  if (!lockSubmit.value) return
  lockSubmit.value = false
  if (props.signNodeProps === '9-1') {
    financeConfirmFun()
  } else {
    emit('nextToPape')
    lockSubmit.value = true
  }
}

// 自动放款-不收平台费-提交流程
const financeConfirmFun = () => {
  // const params = {
  //   businessId: finacCinId.value,
  //   type: 9,
  // }
  // // 获取流程进度
  // PRODUCT_VERIFY_API.getByBusinessIdAndType(params).then(({ data }) => {
  //   if (data.success) {
  //     const { data: resData } = data
  //     // 除了反驳其他状态禁止提交
  //     let turnOn = false
  //     const statusTurnOn = [1, 2, 3].indexOf(resData.status) === -1
  //     if (statusTurnOn) {
  //       turnOn = true
  //     }
  //     if (turnOn) {
  //       const props = {
  //         processIndex: 5,
  //         receiveData: finacCinId.value,
  //       }
  //       const data = { props, route, isC: isCoreEnterpriseAccount.value }
  //       // 获取合同签署的合同list的id
  //       contractListCheckId(data).then(({ idArr }) => {
  //         if (idArr) {
  //           const data = {
  //             financeApplyId: finacCinId.value,
  //             contractIdList: idArr,
  //           }
  //           PRODUCT_APPLI_CATIONS_API.financeConfirm(data)
  //             .then(({ data }) => {
  //               if (data.success) {
  //                 routerQueryDelte(2)
  //               }
  //             })
  //             .catch(() => {
  //               lockSubmit.value = true
  //             })
  //         }
  //       })
  //     } else {
  //       message.error('相同流程无法重复提交')
  //       lockSubmit.value = true
  //     }
  //   }
  // })
  {
    const props = {
      processIndex: 5,
      receiveData: finacCinId.value,
    }
    const data = { props, route, isC: isCoreEnterpriseAccount.value }
    // 获取合同签署的合同list的id
    contractListCheckId(data).then(({ idArr }) => {
      if (idArr) {
        const data = {
          financeApplyId: finacCinId.value,
          contractIdList: idArr,
        }
        PRODUCT_APPLI_CATIONS_API.financeApplySubmit(data)
          .then(({ data }) => {
            if (data.success) {
              routerQueryDelte(2)
            }
          })
          .catch(() => {
            lockSubmit.value = true
          })
      }
    })
  }
}

// 避免签署合同的多余参数污染下一个节点
const routerQueryDelte = page => {
  emit('setCurrentUpFun', page)
  if (page == 0) {
    // 回退页面的事件执行
    store.commit('Product/setGOBackPapeLock')
  }
}
</script>

<style lang="scss">
.contractSigningTwo-box {
  position: relative;
  max-width: 1400px;
  margin: auto;
  box-sizing: border-box;

  .authorization-menu-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    margin: 40px 0;

    & > span {
      width: 188px;
      height: 48px;
      margin-right: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      .ant-btn-round.ant-btn-lg {
        height: 100%;
      }
    }

    & > span:last-child {
      margin-right: 0;
    }
  }
}
</style>
