<template>
  <div v-if="qrUrl" class="payment-QR-code">
    <BaseCreateQRcode
      @refesh="handleRefesh"
      :size="180"
      :link="qrUrl"
      logoSrc="https://test.jingruiit.com:19000/supplychain/upload/20230130/a7df5aa1698b5f7a3133faebbb55e486.jpg"
    />
  </div>
</template>

<script>
export default {
  name: 'paymentQRCode',
}
</script>
<script setup>
import BaseCreateQRcode from '@/components/BaseCreateQRcode'

import { ref, watch } from 'vue'
const props = defineProps({
  linkSrc: {
    type: String,
    required: true,
  },
})
const qrUrl = ref('')

watch(
  () => props.linkSrc,
  val => {
    if (!val) return
    qrUrl.value = val
    // 0、应收账款类型融资申请节点 1、代采融资申请节点 2、云信类型 3、逾期协商类型
    // const protocol = window.location.protocol
    // const host = window.location.host
    // qrUrl.value = `${protocol}//${host}/api/blade-pay/web-back/pay_goods_order/buy?financeNo=${val}&type=0&tenantId=${tenantId.value}`
    // console.log('二维码', qrUrl.value)
  },
  { immediate: true }
)

// 点击二维码刷新按钮
const handleRefesh = () => {
  // setTimeout(() => {
  //   qrUrl.value = `${window.location.protocol}//${
  //     window.location.host
  //   }/pay/pay_goods_order/buy?amount=${333}`
  // }, 1500)
}
</script>

<style lang="scss" scoped>
.payment-QR-code {
  position: relative;
  display: flex;
  justify-content: center;

  :deep() {
    .ant-spin-container img {
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.1);
      border-radius: 8px;
      border: 14px solid #efefef00;
      pointer-events: none;
    }

    .qrcode-refesh {
      display: none;
      text-align: center;
      font-size: 15px;
    }
  }
}
</style>
