<template>
  <div class="label-list-container">
    <div class="label-list" :class="{ mini: size == 'mini' }">
      <div
        v-for="(item, index) in labelList"
        :key="index"
        :class="{ labelActive: index === state }"
        @click="disableM ? '' : toggle(index)"
      >
        <span>{{ item }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, watch } from 'vue'

export default defineComponent({
  name: 'LabelList',
})
</script>
<script lang="ts" setup>
import { ref } from 'vue'

const props = defineProps({
  labelList: {
    type: Array,
    required: true,
  },
  size: {
    type: String,
    default: 'default',
  },
  state: {
    type: Number,
    default: 0,
  },
  disableM: {
    type: Boolean,
    default: false,
  },
})

watch(
  () => props.state,
  val => {
    state.value = val
  }
)

const cursorM = computed(() => {
  if (props.disableM) {
    return 'not-allowed'
  } else {
    return 'pointer'
  }
})

const state = ref(props.state)
const emit = defineEmits(['switch'])
const toggle = (index: number) => {
  state.value = index
  emit('switch', index)
}
</script>

<style lang="scss" scoped>
.label-list-container {
  display: inline-block;
  box-sizing: border-box;

  .label-list {
    padding: 4px;
    height: 48px;
    box-sizing: border-box;
    border-radius: 24px;
    background-color: #f1f2f4;
    box-shadow: inset 0px 1px 1px 0px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    color: #53627c;

    div {
      display: inline-flex;
      align-items: center;
      height: 40px;
      line-height: 40px;
      margin-right: 4px;
      padding: 8px 20px;
      font-size: 14px;
      border-radius: 20px;
      cursor: v-bind(cursorM);
      transition: background ease 0.1s;

      &:last-child {
        margin-right: 0;
      }

      &.labelActive {
        padding: 8px 20px;
        border-radius: 20px;
        color: #0a1f44;
        background-color: #ffffff;
        box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.1);
        cursor: context-menu;
      }
    }

    &.mini {
      height: 32px;
      padding: 2px;

      div {
        padding: 6px 16px;
        font-size: 12px;
        height: auto;
        line-height: initial;
      }
    }
  }
}
</style>
