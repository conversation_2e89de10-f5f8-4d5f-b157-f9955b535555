<template>
  <div class="zi-dong-new-other-expenses">
    <span class="top-line" />
    <Feiyongwaihezi ref="feiyongwaiheziRef" />
  </div>
</template>

<script>
export default {
  name: 'ziDongNewotherExpenses',
}
</script>
<script setup>
import Fe<PERSON><PERSON>g<PERSON><PERSON><PERSON> from '@/components/feiyongxiaoheizi'
import { useRoute } from 'vue-router'
import store from '@/store'
import { ref, computed, inject } from 'vue'
import dayjs from 'dayjs'

import { PRODUCT_APPLI_CATIONS_API } from '@/api/index'
import { requestDictMap } from '@/api/common/index'
import { contractListCheckId } from '@/views/product/component/contract/components/sign'

const route = useRoute()

const setRepaymentContractDataFun = inject('setRepaymentContractDataFun')

const emit = defineEmits(['setCurrentUpFun'])

const feiyongwaiheziRef = ref(null)
const lockSubmit = ref(true)
const finacCinId = ref(
  JSON.parse(sessionStorage.getItem('financingDemandId')) ||
    route.query.id ||
    route.query.goodId
)
const goBackPapeLock = computed(() => store.getters['Product/goBackPapeLock'])
// 是否核心企业（Boolean）
const isCoreEnterpriseAccount = computed(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)

// 回显支付凭证
// const selectBillOrderAttachFun = () => {}

// 被驳回操作进入
// if (goBackPapeLock.value) {
//   selectBillOrderAttachFun()
// }

// 上一步
const previousToPape = () => {
  emit('setCurrentUpFun', 1)
  // 进度条回退
  store.commit('Product/setGOBackPapeLock')
  // 回退页面的事件执行
}

// 立即提交事件
const nextToPape = () => {
  if (!feiyongwaiheziRef.value.exposeFeiyongListFun()) {
    return
  }
  if (!lockSubmit.value) return
  lockSubmit.value = false
  {
    const props = {
      processIndex: 6,
      receiveData: finacCinId.value,
    }
    const data = { props, route, isC: isCoreEnterpriseAccount.value }
    // 获取合同签署的合同list的id
    contractListCheckId(data).then(({ idArr }) => {
      if (idArr) {
        const data = {
          goodsId: route.query.goodId,
          financeApplyId: finacCinId.value,
          contractIds: idArr,
          expenseInfoExpenseList:
            feiyongwaiheziRef.value.exposeFeiyongListFun(),
          // processInstanceId: resData.data?.processInstanceId,
        }
        const requestJiekou =
          route.query.goodType === '5'
            ? PRODUCT_APPLI_CATIONS_API.submitAutoLoanProcess1
            : PRODUCT_APPLI_CATIONS_API.submitAutoLoanProcess
        requestJiekou(data)
          .then(({ data }) => {
            if (data.success) {
              emit('setCurrentUpFun', 3)
            }
          })
          .catch(() => {
            lockSubmit.value = true
          })
      }
    })
  }
}

const onload = async () => {
  const resList = []
  const { data } = await requestDictMap('goods_billing_method')
  if (data.success) {
    const resData = data
    if (resData.code == 200) {
      // 处理字典数据
      for (const item of resData.data) {
        resList.push({
          key: item.dictKey,
          value: item.dictValue,
          id: item.id,
        })
      }
    }
  }
  await PRODUCT_APPLI_CATIONS_API.applyGetById(finacCinId.value).then(res => {
    const { data: resData, code } = res.data
    if (code === 200) {
      const filArr = resList.filter(
        itemed => itemed.key == resData.repaymentMode
      )
      // 合同模板生成数据处理
      const dialogdatas = {
        financeAmount: resData.amount, // 融资金额
        annualInterestRate: resData.annualInterestRate, // 年利率
        totalTerm: resData.loadTerm, // 总期数
        startTime: dayjs().format('YYYY-MM-DD'), // 开始时间
        refundType: filArr[0]?.key || -1, // 还款类型 '1','2','3'
        loadTermUnit: resData.loadTermUnit, // 1-天,2-期
      }
      setRepaymentContractDataFun(dialogdatas)
    }
  })
}
onload()

defineExpose({
  previousToPape,
  nextToPape,
})
</script>

<style lang="scss" scoped>
.zi-dong-new-other-expenses {
  max-width: 1400px;
  position: relative;
  margin: auto;
  box-sizing: border-box;

  .top-line {
    width: 100%;
    // height: 1px;
    display: block;
    background: #f1f2f4;
    margin-top: 40px;
    // margin-bottom: 40px;
  }
}
</style>
