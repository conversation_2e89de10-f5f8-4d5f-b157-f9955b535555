<template>
  <GlobalDialog
    title="借多久？"
    width="432px"
    ref="dialogRef"
    enableFooterSlot
    :enableFullscreen="false"
  >
    <div class="dialog-time-box">
      <div class="end-days-time">选择融资结束日期</div>
      <div class="antd-picker-box">
        <a-date-picker
          :disabled-date="disabledDate"
          :showToday="false"
          v-model:value="dayTime"
          valueFormat="YYYY-MM-DD HH:mm:ss"
          @change="pickerChange"
        />
      </div>
      <div class="end-days-time">
        融资天数（{{ props.loadTermObj.loadTermStart }}～{{
          props.loadTermObj.loadTermEnd
        }}天）
      </div>
      <div class="input-box">
        <n-input-number
          class="radius"
          style="width: 178px"
          :show-button="false"
          :min="props.loadTermObj.loadTermStart"
          :max="props.loadTermObj.loadTermEnd"
          placeholder=""
          v-model:value="day"
          @keyup="changeNumber"
          @update:value="inputChange"
        >
          <template #suffix>天</template>
        </n-input-number>
      </div>
    </div>
    <template #button>
      <div style="width: 100%; text-align: right">
        <n-button
          class="blue border"
          style="height: 40px; margin-right: 12px"
          round
          :bordered="false"
          @click="handleClose"
        >
          取消
        </n-button>
        <n-button
          class="border blue button-item primary"
          type="info"
          style="height: 40px"
          round
          :bordered="false"
          @click="handleConfirm"
        >
          确认
        </n-button>
      </div>
    </template>
  </GlobalDialog>
</template>

<script>
export default {
  name: 'dialogTimeDays',
}
</script>
<script setup>
import { onMounted, ref, watch } from 'vue'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import { NButton, NInputNumber } from 'naive-ui'
import dayjs from 'dayjs'

const props = defineProps({
  loadTermObj: {
    type: Object,
    required: true,
    default: () => {},
  },
  loanPeriod: {
    type: Number || String,
    required: true,
  },
})

const dialogRef = ref(null)
const dayTime = ref(null)
const day = ref(1)

const disabledDate = current =>
  current < dayjs().add(props.loadTermObj.loadTermStart - 1, 'days') ||
  current > dayjs().add(props.loadTermObj.loadTermEnd, 'days') // 只能选前7后7
// const disabledDate = current => current && current < dayjs().endOf('day')

const emit = defineEmits(['setLoanPeriod'])

const handleOpen = () => {
  dialogRef.value.handleOpen()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}

const handleConfirm = () => {
  emit('setLoanPeriod', { dayTime: dayTime.value, day: day.value })
  handleClose()
}

const pickerChange = (date, dateString) => {
  const date1 = dayjs(dateString)
  const date2 = dayjs(dayjs().format('YYYY-MM-DD'))
  day.value = date1.diff(date2, 'day') // 233
}

const changeNumber = () => {
  let str = '' + day.value
  if (str.indexOf('.') != -1) {
    let arr = str.split('.')
    day.value = +arr[0]
  }
}

const inputChange = () => {
  if (day.value && /^\d+$/.test(day.value)) {
    dayTime.value = dayjs().add(Number(day.value), 'day')
  }
}

// 默认获取最小值
watch(
  () => props.loadTermObj,
  val => {
    day.value = val.loadTermStart
    inputChange()
  },
  { deep: true }
)

// 数据回显时的值
watch(
  () => props.loanPeriod,
  val => {
    day.value = val
    inputChange()
  },
  { immediate: true }
)

onMounted(() => {
  inputChange()
})

defineExpose({
  handleOpen,
})
</script>

<style lang="scss" scoped>
@import '@/views/product/naivUiStyle.scss';

.dialog-time-box {
  .end-days-time {
    height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #8a94a6;
    line-height: 20px;
    margin-bottom: 12px;
  }

  .input-box {
    padding-bottom: 3px;
  }

  :deep(.antd-picker-box) {
    margin-bottom: 24px;

    .ant-picker {
      padding: 0 11px;
      width: 100%;
      border-radius: 100px;
    }
    .ant-picker-input > input {
      height: 40px;
    }
  }
}
</style>
