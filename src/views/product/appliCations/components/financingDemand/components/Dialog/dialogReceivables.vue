<template>
  <div>
    <GlobalDialog
      title="选择应收账款"
      width="1200px"
      ref="dialogRef"
      enableFooterSlot
      :enableFullscreen="false"
    >
      <div>
        <!-- 表格 -->
        <CustonTable ref="custonTable" />
      </div>
      <template #button>
        <div class="button-box">
          <div class="button-left-box">
            <n-button
              class="blue border"
              style="height: 40px; margin-right: 12px"
              round
              :bordered="false"
              @click="handleOpenTradeSpace"
            >
              添加应收账款
            </n-button>
            <n-button
              class="blue border"
              style="height: 40px"
              round
              :bordered="false"
              @click="handreFresh"
            >
              刷新
            </n-button>
          </div>
          <div style="width: 100%; text-align: right">
            <n-button
              class="blue border"
              style="height: 40px; margin-right: 12px"
              round
              :bordered="false"
              @click="handleClose"
            >
              取消
            </n-button>
            <n-button
              class="border blue button-item primary"
              type="info"
              style="height: 40px"
              round
              :bordered="false"
              @click="handleConfirm"
            >
              确认
            </n-button>
          </div>
        </div>
      </template>
    </GlobalDialog>
    <TradeSpace ref="tradeSpace" :goodsDetailList="goodsDetailList" />
  </div>
</template>

<script>
export default {
  name: 'dialogReceivables',
}
</script>
<script setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import GlobalDialog from '@/components/GlobalDialog/index.vue'
import CustonTable from './CustomTable/index.vue'
import TradeSpace from './dialogTradeSpace.vue'
import { NButton } from 'naive-ui'
import { PRODUCT_APPLI_CATIONS_API } from '@/api/index.js'

const route = useRoute()
const dialogRef = ref(null)
const custonTable = ref(null)
const tradeSpace = ref(null)
const goodsDetailList = ref([])

const emit = defineEmits(['allRecount'])

PRODUCT_APPLI_CATIONS_API.selectByCustomerGoodsId(
  route.query.customerGoodsId
).then(res => {
  const resData = res.data
  if (resData.code == 200) {
    const dat = resData.data
    goodsDetailList.value = []
    for (const item of dat) {
      goodsDetailList.value.push({
        label: item.enterpriseName,
        value: item.tradeBackgroundId,
      })
    }
  }
})

const handleOpen = () => {
  dialogRef.value.handleOpen()
}

const handleClose = () => {
  dialogRef.value.handleClose()
}

const handleConfirm = () => {
  custonTable.value.setSelectedDataInStore()
  emit('allRecount') // 触发增删金额事件
  handleClose()
}

const handreFresh = () => {
  custonTable.value.onLoad()
}

const handleOpenTradeSpace = () => {
  tradeSpace.value.handleOpen()
}

defineExpose({
  handleOpen,
  handreFresh,
})
</script>

<style lang="scss" scoped>
.button-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .button-left-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>
