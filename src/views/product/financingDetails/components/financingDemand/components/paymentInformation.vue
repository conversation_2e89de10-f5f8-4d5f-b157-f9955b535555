<template>
  <div class="application-content">
    <div class="content-money-text">需缴费用(元)</div>
    <div class="content-botton-money-box">
      <span class="content-money">
        <a-statistic
          :precision="2"
          :value="expenseInfoArr[expenseInfoArr.length - 1]?.amountS"
        >
          <template #prefix>
            <span>￥</span>
          </template>
        </a-statistic>
      </span>
    </div>
    <div class="pledged-goods">
      <a-collapse v-model:activeKey="activeKey" ghost>
        <!-- 费用明细 -->
        <a-collapse-panel key="1" :forceRender="true">
          <template #header>
            <div class="pledged-text-box">
              <span class="pledged-goods-text">费用明细</span>
            </div>
          </template>
          <div class="table-box">
            <BaseTableDetailTotal
              :tableData="expenseInfoArr"
              :columns="columns"
              :loading="tableDataLoad"
            />
          </div>
        </a-collapse-panel>
        <!-- 更多详情 -->
        <a-collapse-panel key="2" :forceRender="true">
          <template #header>
            <div class="pledged-text-box">
              <span class="pledged-goods-text">更多详情</span>
            </div>
          </template>
          <div class="forMore-etails">
            <div class="tradefor-be-surrounded-box">
              <div class="tradefor-box">
                <span>收款账户</span>
                <span class="tradefor-text">{{ expenseInfoData.account }}</span>
              </div>
              <div class="tradefor-box">
                <span>收款公司名</span>
                <span class="tradefor-text">{{
                  expenseInfoData.enterpriseName
                }}</span>
              </div>
              <div class="tradefor-box">
                <span>开户银行</span>
                <span class="tradefor-text">{{ expenseInfoData.bank }}</span>
              </div>
              <div class="tradefor-box">
                <span>支付方式</span>
                <span class="tradefor-text">
                  {{ expenseInfoData.payTypeFilter }}支付
                </span>
              </div>
              <div class="tradefor-box">
                <span>支付凭证</span>
                <span class="tradefor-text">
                  <span
                    class="mode-payment"
                    v-for="item in expenseInfoPayArr"
                    :key="item.id"
                    @click="
                      !item.isPdf
                        ? handleView(item.urlArr)
                        : handleViewPdf(item.url)
                    "
                  >
                    {{ item.laber }}
                  </span></span
                >
              </div>
              <div class="tradefor-box">
                <span>申请时间</span>
                <span class="tradefor-text">{{
                  expenseInfoData.applyTime
                }}</span>
              </div>
              <div class="tradefor-box">
                <span>操作人</span>
                <span class="tradefor-text">{{
                  expenseInfoData.operator
                }}</span>
              </div>
            </div>
          </div>
        </a-collapse-panel>

        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
      </a-collapse>
    </div>
    <PdfView ref="pdfView" />
  </div>
</template>

<script>
export default {
  name: 'paymentInformation',
}
</script>
<script setup>
import BaseTableDetailTotal from '@/components/BaseTableDetailTotal/index.vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'
import PdfView from '@/components/FilePreview/index.vue'
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import { ref } from 'vue'

defineProps({
  expenseInfoData: {
    type: Object,
    required: true,
    default: () => {},
  },
  expenseInfoArr: {
    type: Array,
    required: true,
    default: () => [],
  },
  expenseInfoPayArr: {
    type: Array,
    required: true,
    default: () => [],
  },
})

const pdfView = ref(null)
const tableDataLoad = ref(false)
const activeKey = ref([])
// const tableData = ref([
//   {
//     name: '服务费',
//     refundTime: '融资申请时',
//     monthlySupply: '融资金额*1.00%',
//     monthlyInterest: '￥53,419.00',
//   },
// ])
const columns = [
  {
    title: '费用名称',
    dataIndex: 'name',
  },
  {
    title: '费用类型',
    dataIndex: 'expenseTypeStr',
  },
  {
    title: '支付节点',
    dataIndex: 'chargePoint',
  },
  {
    title: '计费方式',
    dataIndex: 'chargeMethodStr',
  },
  {
    title: '应付金额',
    dataIndex: 'amount',
  },
]

// const preArr = ref([
//   {
//     id: 1,
//     laber: '查看图片',
//     urlArr: [
//       'http://139.159.136.77:9000/yqfdemo/upload/20220426/838f170112a65746834f8d0db5977b45.jpg',
//       'http://139.159.136.77:9000/yqfdemo/upload/20220426/838f170112a65746834f8d0db5977b45.jpg',
//       'http://139.159.136.77:9000/yqfdemo/upload/20220426/838f170112a65746834f8d0db5977b45.jpg',
//       'http://139.159.136.77:9000/yqfdemo/upload/20220426/838f170112a65746834f8d0db5977b45.jpg',
//     ],
//     isPdf: false,
//   },
//   {
//     id: 2,
//     laber: '查看文件',
//     url: 'http://************:9000/supplychain/upload/20220426/407c1eac5daac5d2eabc6bdcdca23c75.pdf',
//     isPdf: true,
//   },
// ])

const handleView = urlArr => {
  const imgList = []
  for (const item of urlArr) {
    imgList.push(item)
  }
  viewerApi({
    options: {
      toolbar: false,
      // navbar: false,
      // title: false,
    },
    images: imgList,
  })
}

const handleViewPdf = targetUrl => {
  pdfView.value.handleOpen(targetUrl)
}
</script>

<style lang="scss" scoped>
.application-content {
  .content-money-text {
    height: 32px;
    font-size: 24px;
    font-weight: 600;
    color: #0a1f44;
    line-height: 32px;
    margin-bottom: 12px;
    cursor: context-menu;
  }
  .content-botton-money-box {
    display: flex;
    justify-items: flex-start;
    align-items: baseline;
    margin-bottom: 40px;

    .content-money {
      cursor: context-menu;
      :deep(.ant-statistic-content) {
        .ant-statistic-content-value {
          font-size: 64px;
          font-weight: 600;
          color: #031222;
          line-height: 64px;
        }
        .ant-statistic-content-prefix {
          font-size: 25px;
          font-weight: 600;
          margin-right: -1px;
        }
      }
    }
    .content-trial-box {
      height: 24px;
      font-size: 16px;
      font-weight: 500;
      color: #0d55cf;
      line-height: 24px;
      margin-left: 24px;
      cursor: pointer;
    }
  }
  :deep(.pledged-goods) {
    .table-box {
      margin-bottom: 25px;
    }

    .pledged-text-box {
      display: flex;
      align-items: center;

      .pledged-goods-text {
        height: 24px;
        font-size: 16px;
        font-weight: 600;
        color: #0a1f44;
        line-height: 24px;
      }
      .long-string {
        display: inline-block;
        width: 1px;
        height: 16px;
        background: #b5bbc6;
        margin: 0 12px;
      }
      .pledged-goods-num {
        height: 24px;
        font-size: 16px;
        font-weight: 400;
        color: #53627c;
        line-height: 24px;
      }
    }
    // 更多信息class
    .forMore-etails {
      .tradefor-be-surrounded-box {
        .tradefor-box {
          cursor: context-menu;
          margin-bottom: 20px;
          display: flex;

          & > span:first-child {
            width: 116px;
            height: 20px;
            display: inline-block;
            font-weight: 400;
            color: #8a94a6;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }

          .tradefor-text {
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #0a1f44;
            line-height: 20px;

            .mode-payment {
              color: #0d55cf;
              margin-right: 12px;
              cursor: pointer;

              &:last-child {
                margin-right: 0;
              }
            }
          }
        }
      }
    }
    // 折叠面板样式修改
    .ant-collapse-header {
      display: flex;
      padding: 12px 3px 8px;
      box-sizing: border-box;
    }
    .ant-collapse-arrow {
      font-size: 19px;
      vertical-align: -4px;
      margin-right: 6px;
      color: #758196;
    }
    .ant-collapse-content-box {
      padding: 12px 0 0;
    }
  }
}
</style>
