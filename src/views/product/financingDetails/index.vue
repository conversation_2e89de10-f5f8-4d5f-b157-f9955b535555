<template>
  <div class="appli-detail">
    <FinancingDemand />
  </div>
</template>

<script>
export default {
  name: 'appliDetail',
}
</script>
<script setup>
import FinancingDemand from '@/views/product/financingDetails/components/financingDemand/index.vue'
</script>

<style lang="scss" scoped>
.appli-detail {
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  padding-top: 40px;
  flex: 1;
}
</style>
