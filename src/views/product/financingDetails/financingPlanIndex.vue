<template>
  <a-spin tip="加载中..." :spinning="loading">
    <div class="appli-detail">
      <div class="top__container">
        <div class="left-mask"></div>
        <div class="right-mask"></div>

        <LabelBar
          style="position: relative; z-index: 100"
          :labelList="labelData.labelList"
          :state="labelData.currentIndex"
          @switch="handleSwitch"
        />
      </div>
      <FinancingDemand v-if="isRender" :key="childKey" />
    </div>
  </a-spin>
</template>

<script>
export default { name: 'appliDetail' }
</script>
<script setup>
import { nextTick, getCurrentInstance, watch } from 'vue'
import FinancingDemand from '@/views/product/financingDetails/components/financingDemand/index.vue'
import { useRoute, useRouter } from 'vue-router'
import LabelBar from '@/views/user/components/LabelBar/index.vue'
import { PRODUCT_VERIFY_API } from '@/api/index.js'
import { reactive, ref, onMounted } from 'vue'
import { requestTableData } from '@/api/user/FinancingList'

// 为了重新渲染
const isRender = ref(false)
const childKey = ref(0)

const instance = getCurrentInstance() // 获取当前组件实例

// 获取产品列表后在显示页面
const loading = ref(false)
// 标签栏数据
const labelData = reactive({
  labelList: [],
  currentIndex: 0,
})
// 产品泪飙
const productList = ref([])
const route = useRoute()
const router = useRouter()

watch(
  () => route.query,
  async (newParams, oldParams) => {
    console.log('路由参数变化', newParams, oldParams)
    if (newParams.id) {
      // 重新渲染更新组件逻辑
      isRender.value = true
      await nextTick()
      // childKey.value++
    }
    // 在这里执行你的逻辑
  },
  { deep: true }
) // 使用deep选项来确保深度监听对象的变化

onMounted(() => {
  loading.value = true
  initDataRouter()
})

// 初始化数据路由
const initDataRouter = async () => {
  const { planId, id } = route.query

  // 获取进度拿到融资ids
  const { data: resData } = await PRODUCT_VERIFY_API.getByBusinessIdAndType3(
    planId
  )

  const idIn = resData.data.financeApplyIds?.join()
  // 根据融资ids获取产品列表
  const { data: listData } = await requestTableData({ idIn })
  const data = listData.data.records ?? []

  productList.value = data

  // 根据产品列表初始化label数据
  labelData.labelList = data.map(item => item.goodsName)
  labelData.currentIndex = id ? data.findIndex(item => item.id === id) : 0

  // 路由id不存在的时候证明首次进入，这时候需要更新路由进入到产品逻辑
  if (!id) {
    handleSwitch(labelData.currentIndex)
    instance.proxy.$forceUpdate() // 强制更新
  } else isRender.value = true

  loading.value = false
}

const handleSwitch = async index => {
  labelData.currentIndex = index

  // 通过切换路由实现多产品详情逻辑
  const record = productList.value[index]

  // 如果路由和需要切换的id一致则不做处理
  if (record.id === route.query.id) return

  isRender.value = false
  // 替换当前路由
  router.replace({
    name: 'financingPlanDetail',
    query: {
      planId: route.query.planId,
      status: route.query.status,
      id: record.id,
      goodId: record.goodsId,
      goodType: record.goodsType,
      customerGoodsId: record.customerGoodsId,
      lendingMethod: record.lendingMethod,
      chargeMethod: record.chargeMethod,
      canOpenDelayApply: record.canOpenDelayApply ? 1 : 2,
    },
  })
}
</script>

<style lang="scss" scoped>
.appli-detail {
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  padding-top: 40px;
  flex: 1;

  & .top__container {
    max-width: 1400px;
    position: relative;
    margin: auto;
    box-sizing: border-box;
    position: relative;
    width: 100%;
    padding: 28px 40px;
    border: 1px solid #efefef;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
    margin-bottom: 40px;
    .left-mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 212px;
      height: 80px;
      background: #0c66ff 100%;
      opacity: 0.1;
      filter: blur(24px);
    }
    .right-mask {
      position: absolute;
      right: 0;
      top: 0;
      width: 502px;
      height: 80px;
      background: #0c66ff 100%;
      opacity: 0.1;
      filter: blur(49px);
    }
  }
}
</style>
