<template>
  <div class="repayment-record-container">
    <a-spin :spinning="loading">
      <template v-if="recordData.length === 0">
        <template v-if="loading">
          <!-- 加载 loading 预留块 -->
          <div style="height: 300px"></div>
        </template>
        <template v-else>
          <NoData
            title="暂无数据"
            noButton
            :imgSrc="require('@/assets/images/empty.svg')"
          />
        </template>
      </template>
      <template v-else>
        <!-- 内容主体 -->
        <RecordCard v-for="item of recordData" :key="item.id" :record="item" />
        <a-pagination
          v-model:current="paginationData.currentPage"
          :total="paginationData.total"
          :itemRender="customPaginationRender"
          @change="handlePaginationChange"
        />
      </template>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { h, watchEffect } from 'vue'
import { NButton } from 'naive-ui'
import MySvgIcon from '@/components/MySvgIcon/index.vue'

export default {
  name: 'ProductOpenDetailRepaymentRecordIndex',
}

const customPaginationRender = ({
  originalElement,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  page,
  type,
}: {
  originalElement: any
  page: any
  type: any
}) => {
  if (type === 'prev') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-left custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h(MySvgIcon, { 'icon-class': 'icon-jiantou-zuo', 'font-size': '16px' }),
        h('span', {}, '上一页'),
      ]
    )
  } else if (type === 'next') {
    return h(
      NButton,
      {
        class:
          'blue t-border no-focus no-pressed custom-pagination-right custom-pagination-item-link',
        bordered: false,
      },
      () => [
        h('span', {}, '下一页'),
        h(MySvgIcon, { 'icon-class': 'icon-youjiantou1', 'font-size': '16px' }),
      ]
    )
  }
  return originalElement
}

const createInitPagData = () => ({
  pageSize: 10,
  currentPage: 1,
  maxPage: 1,
  total: 0,
})
</script>
<script lang="ts" setup>
import { ref } from 'vue'
import RecordCard from './RecordCard.vue'
import { requestTableData } from '@/api/user/FinancingList'
import NoData from '@/views/user/components/NoData/index.vue'

const props = defineProps({
  goodsId: {
    type: String,
    required: true,
    default: undefined,
  },
})
const loading = ref<boolean>(false)
const recordData = ref<any>([])
const paginationData: any = ref(createInitPagData())

// 加载数据函数
const loadRecordData = (current: number, pageSize: number | undefined) => {
  loading.value = true
  // 构建搜索请求参数
  let requestObj: any = {
    customerGoodsIdEqual: props.goodsId,
    goodsTypeEqual: 1,
    current,
    size: pageSize || 10,
  }
  requestTableData(requestObj)
    .then(({ data }) => {
      loading.value = false
      if (data.success) {
        data = data.data
        paginationData.value.currentPage = data.current
        paginationData.value.maxPage = data.pages
        paginationData.value.total = data.total
        const recordDataRes = data.records
        // 数据格式化
        for (const item of recordDataRes) {
          // item.date = dayjs(item.date, 'YYYY-MM-DD').format('YYYY.MM.DD')
        }
        recordData.value = recordDataRes
      }
    })
    .catch(() => {
      loading.value = false
    })
}

// 分页页码改变事件
const handlePaginationChange = (page: any, pageSize: any) => {
  paginationData.value.current = page
  paginationData.value.pageSize = pageSize
  loadRecordData(page, pageSize)
}

// 初始化页面函数
const initRecordData = () => {
  paginationData.value = createInitPagData()
  loadRecordData(
    paginationData.value.currentPage,
    paginationData.value.pageSize
  )
}

watchEffect(() => {
  if (props.goodsId) initRecordData()
})
</script>

<style lang="scss" scoped>
.repayment-record-container {
  // 分页
  :deep(.ant-pagination) {
    display: flex;
    align-items: center;
    justify-content: center;
    // 去除分页下外边距
    margin-bottom: 0;

    .ant-pagination-disabled {
      .custom-pagination-item-link {
        color: #a6aebc !important;
        border: 1px solid transparent;
      }
    }

    .custom-pagination-item-link {
      background: #f8f9fb;
      color: #53627c;
    }

    .custom-pagination-left {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px 0 12px;
      border-radius: 100px 0px 0px 100px;
    }

    .custom-pagination-right {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px 0 16px;
      border-radius: 0px 100px 100px 0px;
    }
  }
}
</style>
