<template>
  <div class="open-the-details-container">
    <div class="open-the-details-box">
      <div class="open-the-details-message" v-if="false">
        <div class="open-the-details-message-left">
          <span>
            <MySvgIcon
              icon-class="icon-shengyin"
              style="color: #182c4f; font-size: 32px"
            />
          </span>
          <span>
            您当前有一笔借据需要还款，请立即还款，否则会影响您的征信信息～
          </span>
        </div>
        <div class="open-the-details-message-right">
          <MySvgIcon
            icon-class="icon-youjiantou"
            style="color: #182c4f; font-size: 24px"
          />
        </div>
      </div>
      <div class="open-the-details-product-introduce">
        <div class="open-the-details-product-introduce-left">
          <div
            class="open-the-details-product-carousel-title"
            v-if="goodIntroduceData.openGoodListData?.length > 1"
          >
            <n-carousel show-arrow @update:current-index="currentIndex">
              <div
                class="carousel-box"
                v-for="item in goodIntroduceData.openGoodListData"
                :key="item.id"
              >
                <span>{{ `${item.goodsName}-${item.goodsTypeText}` }}</span>
              </div>

              <template #arrow="{ prev, next }">
                <div class="custom-arrow">
                  <button
                    type="button"
                    class="curtom-arrow--left"
                    @click="prev"
                  >
                    <MySvgIcon
                      icon-class="icon-zuojiantou"
                      style="color: #586680; font-size: 32px"
                    />
                  </button>
                  <button
                    type="button"
                    class="curtom-arrow--right"
                    @click="next"
                  >
                    <MySvgIcon
                      icon-class="icon-youjiantou"
                      style="color: #586680; font-size: 32px"
                    />
                  </button>
                </div>
              </template>
              <template #dots="{ total, currentIndex }">
                <ul class="custom-dots">
                  <li
                    v-for="index of total"
                    :class="{ ['is-active']: currentIndex === index - 1 }"
                    :key="index"
                  ></li>
                </ul>
              </template>
            </n-carousel>
          </div>
          <div class="open-the-details-product-introduceleft-title" v-else>
            <div class="carousel-box">
              <span v-if="goodIntroduceData.openGoodListData?.[0]">{{
                `${goodIntroduceData.openGoodListData[0].goodsName}-${goodIntroduceData.openGoodListData[0].goodsTypeText}`
              }}</span>
              <span v-else>加载中。。。</span>
            </div>
          </div>
          <div class="open-the-details-product-introduceleft-details">
            <div class="open-the-details-product-introduceleftdetails-left">
              <div class="active-box">
                <span class="available-credit">当前可用额度(元)</span>
                <MySvgIcon
                  v-if="goodIntroduceData.type == '额度调整'"
                  icon-class="icon-tixing"
                  style="color: #f07300; font-size: 20px"
                />
                <span
                  v-if="goodIntroduceData.type == '额度调整'"
                  class="reappraise-box"
                >
                  额度已重新评估，需要激活额度才能生效
                </span>
              </div>
              <span class="money-num-box">
                <a-statistic
                  :class="{
                    'moneyNum-decoration': goodIntroduceData.type == '已到期',
                    'moneyNum-disables': ['已禁用', '已冻结'].includes(
                      goodIntroduceData.type
                    ),
                  }"
                  :precision="2"
                  :value="goodIntroduceData.availableCredit || 0"
                />
              </span>
              <div
                class="open-the-details-product-introduceleftdetails-leftcapital"
              >
                <span>资金方：{{ goodIntroduceData.capitalName }}</span>
              </div>
              <span
                class="dailyInterestRate-box"
                :class="{
                  'text-disables':
                    goodIntroduceData.type == '已到期' ||
                    ['已禁用', '已冻结'].includes(goodIntroduceData.type),
                }"
              >
                日利率{{ goodIntroduceData.dailyInterestRate }}%起（年化利率{{
                  goodIntroduceData.annualInterestRate
                }}%）
              </span>
              <span
                class="expireTime-box"
                :class="{ disableSpan: goodIntroduceData.type == '已到期' }"
              >
                到期日：{{ goodIntroduceData.expireTime }}
              </span>
              <!-- 核心企业云信产品详情才能云信开单 -->
              <div
                v-if="
                  (goodIntroduceData.type == '可融资' ||
                    goodIntroduceData.type == '已禁用') &&
                  isCoreEnterpriseAccount &&
                  goodIntroduceData.goodsType == 3
                "
                :class="
                  goodIntroduceData.type == '已禁用'
                    ? 'product-to-financing-diable'
                    : 'product-to-financing'
                "
                @click="handleApplyCloud(goodIntroduceData.type)"
              >
                <div class="product-to-financing-fiter">
                  {{
                    goodIntroduceData.type == '可融资'
                      ? '申请开单'
                      : goodIntroduceData.type == '额度调整'
                      ? '激活额度'
                      : goodIntroduceData.type == '已到期'
                      ? '申请额度'
                      : goodIntroduceData.type == '已禁用'
                      ? '查看禁用原因'
                      : '回首页'
                  }}
                  <MySvgIcon
                    v-if="goodIntroduceData.type != '已禁用'"
                    icon-class="icon-youjiantou-chang"
                    style="color: white; font-size: 20px"
                  />
                </div>
              </div>
              <div
                v-if="
                  ['可融资', '已冻结', '已禁用'].includes(
                    goodIntroduceData.type
                  ) && !isCoreEnterpriseAccount
                "
                :class="{
                  'product-to-financing': goodIntroduceData.type != '已禁用',
                  'product-to-financing-diable':
                    goodIntroduceData.type == '已禁用',
                }"
                @click="toImmediate(goodIntroduceData.type)"
              >
                <div class="product-to-financing-fiter">
                  {{
                    goodIntroduceData.type == '可融资' && goodTypeData === 1
                      ? '立即融资'
                      : goodIntroduceData.type == '可融资' && goodTypeData === 2
                      ? '立即代采'
                      : goodIntroduceData.type == '额度调整'
                      ? '激活额度'
                      : goodIntroduceData.type == '已到期'
                      ? '申请额度'
                      : goodIntroduceData.type == '已冻结' && goodTypeData === 1
                      ? '申请解冻'
                      : goodIntroduceData.type == '已禁用'
                      ? '查看禁用原因'
                      : '回首页'
                  }}
                  <MySvgIcon
                    v-if="goodIntroduceData.type != '已禁用'"
                    icon-class="icon-youjiantou-chang"
                    style="color: white; font-size: 20px"
                  />
                </div>
              </div>
            </div>
            <div class="open-the-details-product-introduceleftdetails-right">
              <div
                class="open-the-details-product-introduceleftdetails-right-line"
              />
              <div
                class="open-the-details-product-introduceleftdetails-right-notline"
              >
                <div
                  class="open-the-details-product-introduceleftdetails-right-sum"
                >
                  <span>当前待还本金(元)：</span>
                  <span>
                    <a-statistic :precision="2" :value="0" />
                  </span>
                </div>
                <div
                  class="open-the-details-product-introduceleftdetails-right-sum"
                >
                  <span>当前申请金额(元)：</span>
                  <span>
                    <a-statistic :precision="2" :value="0" />
                  </span>
                </div>
                <div
                  class="open-the-details-product-introduceleftdetails-right-sum"
                >
                  <span>待还息费账单(元)：</span>
                  <span>
                    <a-statistic :precision="2" :value="0" />
                  </span>
                </div>
                <div
                  class="open-the-details-product-introduceleftdetails-right-sum"
                  style="margin-bottom: 32px"
                >
                  <span>当前逾期金额(元)：</span>
                  <span>
                    <a-statistic :precision="2" :value="0" />
                  </span>
                </div>
                <span
                  class="open-the-details-product-introduceleftdetails-rightrepay"
                  >申请还款</span
                >
              </div>
            </div>
            <div
              class="open-the-details-product-introduceleftdetails-after c8a94a6"
              :class="{
                c00865a: goodIntroduceData.type == '可融资',
                c0d55cf:
                  goodIntroduceData.type == '待申请额度' ||
                  goodIntroduceData.type == '待激活' ||
                  goodIntroduceData.type == '额度调整',
              }"
            >
              <span>{{
                goodIntroduceData.type == '额度调整'
                  ? '待激活'
                  : goodIntroduceData.type
              }}</span>
            </div>
          </div>
        </div>
        <div class="open-the-details-product-introduce-right">
          <div class="open-the-details-product-introduce-right-box">
            <span>数据总览</span>
            <span class="limit-name" style="margin-top: 24px">
              总授信额度(元)
              <MySvgIcon
                :icon-class="lookType ? 'icon-yanjing' : 'icon-biyan'"
                style="color: #586680; font-size: 20px; cursor: pointer"
                @click="looked"
              />
            </span>
            <span class="limit-num">
              <a-statistic
                v-if="lookType"
                :precision="2"
                :value="goodIntroduceData.creditAmount || 0"
              />
              <span v-else class="ban-view">******</span>
            </span>
            <span class="limit-name" style="margin-top: 20px">
              总融资金额(元)
              <MySvgIcon
                :icon-class="lookType2 ? 'icon-yanjing' : 'icon-biyan'"
                style="color: #586680; font-size: 20px; cursor: pointer"
                @click="looked2"
              />
            </span>
            <span class="limit-num">
              <a-statistic v-if="lookType2" :precision="2" :value="0" />
              <span v-else class="ban-view">******</span>
            </span>
          </div>
          <div
            class="open-the-details-product-introduce-right-box bottom-go-open"
          >
            <div class="to-open-more-icon" @click="toOpenMore">
              <MySvgIcon
                icon-class="icon-jia"
                style="color: #e8ecf1; font-size: 24px"
              />
            </div>
            <div class="to-open-more-text">开通更多产品</div>
          </div>
        </div>
      </div>
    </div>
    <DialogAuthority ref="dialogAuthority" />
    <DialogDisable ref="dialogDisable" :reason="reason" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'IntroduceS',
}
</script>
<script setup lang="ts">
import { NCarousel } from 'naive-ui'
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import DialogAuthority from '@/views/user/components/Dialog/dialogAuthority.vue'
import DialogDisable from '@/views/user/components/Dialog/dialogDisable.vue'
import { PRODUCT_API } from '@/api/index'
import { message } from 'ant-design-vue'
const dialogAuthority = ref(null)
const dialogDisable = ref(null)
const router = useRouter()
const store = useStore()
const props = defineProps({
  goodIntroduceData: Object,
})
const $myemit = defineEmits(['switchover'])
const reason = ref('')
const lookType = ref(false)
const lookType2 = ref(false)
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
watch(
  () => props.goodIntroduceData.type,
  (newValue, oldValue) => {
    if (newValue == '已禁用') {
      getDisable()
    }
  }
)
const roleMap = computed<any>(() => store.getters['Role/roleMap'])
const isEnterpriseAccount = computed(
  () => store.getters['Auth/isEnterpriseAccount']
)
// 产品类型
const goodTypeData = computed<number>(() => props.goodIntroduceData.goodsType)

// 查看状态取反1
const looked = () => {
  lookType.value = !lookType.value
}

// 查看状态取反2
const looked2 = () => {
  lookType2.value = !lookType2.value
}

const currentIndex = currentIndex => {
  $myemit('switchover', currentIndex)
}

const getDisable = async () => {
  const enterpriseQuotaId = props.goodIntroduceData?.enterpriseQuotaId
  const {
    data: { data, code },
  } = await PRODUCT_API.getDisableReason(enterpriseQuotaId)
  if (code == 200) {
    reason.value = data
  }
}

// 云信开单
const handleApplyCloud = type => {
  if (type == '已禁用') {
    dialogDisable.value.handleOpen()
  } else {
    router.push({
      name: 'cloudBillProcess',
      query: {
        cloudProductId: props.goodIntroduceData.goodsId,
        cloudProductName: props.goodIntroduceData.goodsName,
        fundName: props.goodIntroduceData.capitalName,
        fundId: props.goodIntroduceData.capitalId,
        overdueInterestRate: props.goodIntroduceData.annualInterestRate,
        enterpriseId: props.goodIntroduceData.enterpriseQuotaId,
        availableAmountStr: props.goodIntroduceData.availableCredit,
        expireTime: props.goodIntroduceData.expireTime,
      },
    })
  }
}
const toImmediate = type => {
  //  goodIntroduceData.type == '可融资'
  //  ? '立即融资'
  //  : goodIntroduceData.type == '额度调整'
  //  ? '激活额度'
  //  : goodIntroduceData.type == '已到期'
  //  ? '申请额度'
  //  : goodIntroduceData.type == '已禁用'
  //  ? '查看禁用原因'
  //  : '回首页'
  switch (true) {
    case type === '可融资' && goodTypeData.value === 1:
      if (
        !isEnterpriseAccount.value ||
        roleMap.value.my_product_applied_financing_button
      ) {
        router.push({
          name: 'appliCations',
          query: {
            goodId: props.goodIntroduceData.goodsId,
            goodType: props.goodIntroduceData.goodsType,
            customerGoodsId: props.goodIntroduceData.id,
            enterpriseQuotaId: props.goodIntroduceData.enterpriseQuotaId,
            lendingMethod: props.goodIntroduceData.lendingMethod,
            chargeMethod: props.goodIntroduceData.chargeMethod,
          },
        })
      } else {
        dialogAuthority.value.handleOpen()
      }
      break
    case type === '可融资' && goodTypeData.value === 2:
      if (
        !isEnterpriseAccount.value ||
        roleMap.value.my_product_purchase_button
      ) {
        router.push({
          name: 'generationOfMiningMallDetail',
          query: {
            goodId: props.goodIntroduceData.goodsId,
          },
        })
      } else {
        dialogAuthority.value.handleOpen()
      }
      break
    case type === '已冻结':
      router.push({
        name: 'linesThaw',
        query: {
          goodId: props.goodIntroduceData.goodsId,
          goodType: props.goodIntroduceData.goodsType,
          customerGoodsId: props.goodIntroduceData.id,
        },
      })
      break
    case type === '已禁用':
      dialogDisable.value.handleOpen()
      //
      break
  }
}

const toOpenMore = () => {
  const detail = props.goodIntroduceData
  switch (detail?.goodsType) {
    case 1:
      router.push('/product/accountsreceivablelist')
      break
    case 2:
      router.push('/product/generationofminingfinancing')
      break
    case 3:
      router.push('/product/cloudletterlist')
      break
    case 4:
      message.success('动产质押：待配置')
      // router.push({ name: '' })
      break
    case 5:
      router.push('/product/orderFinancing')
      break
    case 6:
      message.success('商票：待配置')
      // router.push({ name: '' })
      break
    case 99:
      router.push('/product/productgrouplist')
      break
  }
}
</script>

<style lang="scss" scoped>
.open-the-details-container {
  width: 100%;
  position: relative;
  margin: 0 auto;
  max-width: 1400px;
  box-sizing: border-box;

  .open-the-details-box {
    position: relative;
    width: 100%;
    box-sizing: border-box;

    .open-the-details-message {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
      box-sizing: border-box;
      height: 64px;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 16px;
      backdrop-filter: blur(4px);
      cursor: pointer;

      .open-the-details-message-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        & > span:last-child {
          font-size: 20px;

          font-weight: 400;
          color: #0a1f44;
          line-height: 28px;
          margin-left: 12px;
        }
      }

      @keyframes move {
        0% {
          transform: translate(0);
        }
        25% {
          transform: translate(50%);
        }
        50% {
          transform: translate(70%);
        }
      }

      &:hover .open-the-details-message-right > #my-svg-icons {
        animation: move 0.6s linear 2;
      }
    }

    .open-the-details-product-introduce {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 40px;

      .open-the-details-product-introduce-left {
        .open-the-details-product-carousel-title {
          width: 928px;
          height: 120px;
          background: rgba(255, 255, 255, 0.7);
          box-shadow: 0px 3px 12px 0px rgba(10, 31, 68, 0.04);
          border-radius: 16px 16px 0px 0px;
          border: 1px solid transparent;
          backdrop-filter: blur(4px);

          .carousel-box {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            & > span {
              width: 744px;
              height: 56px;
              font-size: 40px;
              @include family-PingFangSC-Semibold;
              font-weight: 600;
              color: #0a1f44;
              line-height: 56px;
              text-align: center;
              white-space: nowrap; //让文本不换行，在同一行里面
              overflow: hidden; //让超出的范围进行隐藏
              text-overflow: ellipsis; //超出的部分用省略号表示
            }
          }

          .custom-arrow {
            display: flex;
            position: absolute;
            width: 100%;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            justify-content: space-between;
          }

          .custom-arrow button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            border-width: 0;
            border-radius: 8px;
            cursor: pointer;

            &:first-child {
              margin-left: 40px;
            }
            &:last-child {
              margin-right: 40px;
            }
          }

          .custom-arrow button:active {
            transform: scale(0.95);
            transform-origin: center;
          }

          .custom-dots {
            display: flex;
            margin: 0;
            padding: 0;
            position: absolute;
            left: 50%;
            bottom: 8px;
            transform: translateX(-50%);
          }

          .custom-dots li {
            width: 4px;
            height: 4px;
            background: #182c4f;
            border-radius: 2px;
            opacity: 0.5;
            display: inline-block;
            margin-right: 8px;
            transition: width 0.3s,
              background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &:last-child {
              margin-right: 0;
            }
          }

          .custom-dots li.is-active {
            width: 12px;
            background: #0d55cf;
            opacity: 1;
          }
        }
        .open-the-details-product-introduceleft-title {
          width: 928px;
          height: 120px;
          background: rgba(255, 255, 255, 0.7);
          box-shadow: 0px 3px 12px 0px rgba(10, 31, 68, 0.04);
          border-radius: 16px 16px 0px 0px;
          border: 1px solid transparent;
          backdrop-filter: blur(4px);

          .carousel-box {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            & > span {
              width: 744px;
              height: 56px;
              font-size: 40px;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #0a1f44;
              line-height: 56px;
              text-align: center;
            }
          }
        }
        .open-the-details-product-introduceleft-details {
          width: 928px;
          height: 378px;
          background: rgba(255, 255, 255, 0.4);
          box-shadow: 0px 12px 64px 0px rgba(10, 31, 68, 0.08);
          border-radius: 0 0 16px 16px;
          border: 1px solid transparent;
          backdrop-filter: blur(4px);
          display: flex;
          position: relative;

          .open-the-details-product-introduceleftdetails-left {
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            padding-left: 48px;
            box-sizing: border-box;
            width: 50%;
            padding-top: 42px;
            box-sizing: border-box;

            .active-box {
              display: flex;
              align-items: center;

              .available-credit {
                height: 24px;
                font-size: 16px;
                @include family-PingFangSC-Medium;
                font-weight: 500;
                color: #8a94a6;
                line-height: 24px;
              }

              .reappraise-box {
                height: 20px;
                font-size: 14px;
                font-weight: 400;
                color: #f07300;
                line-height: 20px;
                margin-left: 1px;
              }

              #my-svg-icons {
                margin-left: 10px;
              }
            }

            .money-num-box {
              margin-top: 6px;

              :deep(.ant-statistic-content-value > span) {
                height: 77px;
                font-size: 60px;
                @include family-CoreSansD65Heavy;
                // color: #031222;
                line-height: 79px;
                overflow: hidden;
              }

              .moneyNum-decoration {
                :deep(.ant-statistic-content-value) {
                  display: flex;
                  text-decoration: line-through;
                }
              }
              .moneyNum-disables {
                :deep(.ant-statistic-content-value) {
                  display: flex;
                  text-decoration: line-through;
                  color: #8a94a6;
                }
              }
            }

            .dailyInterestRate-box {
              height: 24px;
              font-size: 14px;
              @include family-PingFangSC-Medium;
              font-weight: 500;
              color: #8a94a6;
              line-height: 24px;
              margin-top: 12px;
              margin-left: 2px;
            }

            .text-disables {
              text-decoration: line-through;
            }

            .expireTime-box {
              height: 20px;
              font-size: 14px;
              font-weight: 400;
              color: #00865a;
              line-height: 20px;
              margin-top: 12px;
              margin-left: 2px;
            }

            .disableSpan {
              color: #dd2727;
            }

            .open-the-details-product-introduceleftdetails-leftcapital {
              margin-top: -2px;

              span {
                background: rgba(255, 255, 255, 0);
                border: 1px solid #0d55cf;
                border-radius: 50px;
                padding: 3px 12px;
                box-sizing: border-box;
                font-size: 13px;
                font-weight: 500;
                color: #0d55cf;
                display: block;
              }
            }

            .product-to-financing {
              margin-top: 16px;
              text-align: left;
              width: 228px;
              height: 64px;
              background: #182c4f;
              border-radius: 32px;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              position: relative;
              overflow: hidden;

              .product-to-financing-fiter {
                font-size: 20px;
                @include family-PingFangSC-Semibold;
                font-weight: 600;
                color: #ffffff;
                line-height: 28px;
                position: relative;
                z-index: 1;

                & > #my-svg-icons {
                  flex-shrink: 0;
                  margin-left: 4px;
                }
              }

              @keyframes move {
                0% {
                  transform: translate(0);
                }
                25% {
                  transform: translate(50%);
                }
                50% {
                  transform: translate(70%);
                }
              }

              &::after {
                display: inline-block;
                width: 228px;
                height: 64px;
                border-radius: 25px;
                top: 0;
                bottom: 0;
                left: 0;
                transform: translate(-100%);
                position: absolute;
                background: rgba(255, 255, 255, 0.1);
                content: '';
                transition: all 0.4s;
              }

              &:hover::after {
                transform: translate(0);
              }

              &:hover #my-svg-icons {
                animation: move 0.8s linear 2;
              }
            }

            .product-to-financing-diable {
              margin-top: 28px;
              text-align: left;
              width: 228px;
              height: 64px;
              background: transparent;
              border-radius: 32px;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              position: relative;
              overflow: hidden;
              border: 1px solid #758196;

              .product-to-financing-fiter {
                font-size: 20px;
                @include family-PingFangSC-Semibold;
                font-weight: 600;
                color: #53627c;
                line-height: 28px;
                position: relative;
                z-index: 1;
              }
            }
          }

          .open-the-details-product-introduceleftdetails-right {
            display: flex;
            width: 50%;
            padding-top: 42px;
            box-sizing: border-box;

            .open-the-details-product-introduceleftdetails-right-line {
              width: 1px;
              height: 281px;
              background: linear-gradient(
                0deg,
                rgba(24, 44, 79, 0) 0%,
                #182c4f 50%,
                rgba(24, 44, 79, 0) 100%
              );
              margin-left: 47px;
            }
            .open-the-details-product-introduceleftdetails-right-notline {
              margin-left: 48px;

              .open-the-details-product-introduceleftdetails-right-sum {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 20px;

                & > span:first-child {
                  height: 24px;
                  font-size: 16px;
                  @include family-PingFangSC-Medium;
                  font-weight: 500;
                  color: #8a94a6;
                  line-height: 24px;
                }
                & > span:last-child {
                  :deep(.ant-statistic-content) {
                    display: flex;
                  }
                  :deep(.ant-statistic-content-value > span) {
                    height: 29px;
                    font-size: 24px;
                    @include family-CoreSansD65Heavy;
                    color: #031222;
                    line-height: 30px;
                    overflow: hidden;
                  }
                }
              }
              .open-the-details-product-introduceleftdetails-rightrepay {
                height: 48px;
                display: inline-block;
                border-radius: 100px;
                border: 1px solid #0c66ff;
                padding: 12px 28px;
                box-sizing: border-box;
                line-height: 23px;
                text-align: center;
                cursor: pointer;

                font-size: 16px;
                @include family-PingFangSC-Medium;
                font-weight: 500;
                color: #0d55cf;
              }
            }
          }

          .open-the-details-product-introduceleftdetails-after {
            position: absolute;
            right: -1px;
            bottom: 24px;

            & > span {
              display: inline-block;
              height: 32px;
              background: rgba(3, 18, 34, 0.04);
              border-radius: 100px 0px 0px 100px;
              box-shadow: inset 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
              text-align: center;
              line-height: 32px;
              padding: 0 10px;
              box-sizing: border-box;
            }
          }

          .c8a94a6 {
            color: #8a94a6;
          }
          .c00865a {
            color: #00865a;
          }
          .c0d55cf {
            color: #0d55cf;
          }
        }
      }
      .open-the-details-product-introduce-right {
        .open-the-details-product-introduce-right-box {
          width: 432px;
          height: 229px;
          background: rgba(255, 255, 255, 0.5);
          box-shadow: 0px 12px 64px 0px rgba(10, 31, 68, 0.08);
          border-radius: 16px;
          border: 1px solid transparent;
          backdrop-filter: blur(4px);
          display: flex;
          flex-direction: column;
          padding-left: 24px;
          box-sizing: border-box;

          & > span:first-child {
            font-size: 16px;
            @include family-PingFangSC-Medium;
            font-weight: 500;
            color: #0a1f44;
            line-height: 24px;
            margin-top: 21px;
          }

          .limit-name {
            font-size: 16px;
            @include family-PingFangSC-Medium;
            font-weight: 500;
            color: #8a94a6;
            line-height: 24px;
          }

          .limit-num {
            min-height: 34px;

            :deep(.ant-statistic-content) {
              display: flex;
            }
            :deep(.ant-statistic-content-value > span) {
              font-size: 28px;
              @include family-CoreSansD65Heavy;
              color: #031222;
              line-height: 35px;
            }

            .ban-view {
              font-size: 28px;
              @include family-CoreSansD65Heavy;
              color: #031222;
              line-height: 35px;
            }
          }
        }

        .bottom-go-open {
          margin-top: 40px;
          padding-left: 0;
          justify-content: center;
          align-items: center;

          .to-open-more-icon {
            width: 48px;
            height: 48px;
            background: #4d5d7a;
            border-radius: 150%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            #my-svg-icons {
              transition: transform 0.1s;
            }

            &:hover #my-svg-icons {
              transform: scale(1.5);
            }
          }

          .to-open-more-text {
            font-size: 20px;
            @include family-PingFangSC-Semibold;
            font-weight: 600;
            color: #53627c;
            line-height: 28px;
            margin-top: 16px;
            cursor: context-menu;
          }
        }
      }
    }
  }
}
</style>
