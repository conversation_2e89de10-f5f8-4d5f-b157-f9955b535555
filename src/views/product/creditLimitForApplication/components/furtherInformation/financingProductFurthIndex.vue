<template>
  <div class="further-information-container">
    <div class="further-information-box">
      <div class="further-information-show-part">
        <div class="further-information-state">
          <MySvgIcon
            icon-class="icon-bianji"
            style="fill: #007fff; font-size: 20px"
            targerUri
          ></MySvgIcon>
          <span>请补充以下相关资料，有助于授信评估</span>
          <span>，并为您开启</span>
          <div class="to-privacy" @click="tiaozhuanYSFun">
            <span>隐私保护</span>
            <MySvgIcon
              icon-class="icon-youjiantou"
              style="fill: #53627c; font-size: 16px"
            ></MySvgIcon>
          </div>
        </div>
        <div class="further-information-collapse-box">
          <a-collapse
            v-model:activeKey="activeKey"
            :bordered="false"
            class="further-information-collapse"
          >
            <template #expandIcon="{ isActive }">
              <caret-right-outlined
                :rotate="isActive ? 90 : 0"
                style="font-size: 20px"
              />
            </template>
            <a-collapse-panel
              :header="item.templateName"
              class="further-information-collapse-item"
              v-for="item in list"
              :key="item.id"
            >
              <div>
                <a-form
                  :ref="`formRef${item.orderNum}`"
                  name="further-information-custom-validation"
                  v-bind="layout"
                  :model="list"
                >
                  <a-form-item
                    class="further-information-collapse-item-form-item"
                    :colon="false"
                    :label="itemed.fieldDesc"
                    :name="itemed.id"
                    v-for="itemed in item.creditFromFields"
                    :key="itemed.id"
                    :rules="[
                      {
                        required: itemed.isRequired == '0' ? false : true,
                        validator: checkBurl,
                        trigger: itemed.triggerType ? 'blur' : 'change',
                      },
                    ]"
                  >
                    <!-- 数字类型 -->
                    <a-tooltip
                      v-if="itemed.dataType == 1"
                      :trigger="['focus']"
                      placement="topLeft"
                      overlay-class-name="numeric-input"
                    >
                      <template #title>
                        <span class="numeric-input-title">请输入数字</span>
                      </template>

                      <a-input
                        v-model:value="itemed.value"
                        :placeholder="`请输入${itemed.fieldDesc}`"
                        autocomplete="off"
                        size="large"
                        type="number"
                      />
                    </a-tooltip>
                    <!-- 文本类型 -->
                    <a-input
                      v-else-if="itemed.dataType == 3"
                      v-model:value="itemed.value"
                      :placeholder="`请输入${itemed.fieldDesc}`"
                      autocomplete="off"
                      size="large"
                    />
                    <!-- 元或者万元类型 -->
                    <a-tooltip
                      v-else-if="itemed.dataType == 5 || itemed.dataType == 6"
                      :trigger="['focus']"
                      placement="topLeft"
                      overlay-class-name="numeric-input"
                    >
                      <template #title>
                        <span class="numeric-input-title">请输入数字</span>
                      </template>

                      <a-input
                        v-model:value="itemed.value"
                        :placeholder="`请输入${itemed.fieldDesc}`"
                        :addon-after="itemed.dataType == 5 ? '元' : '万元'"
                        autocomplete="off"
                        size="large"
                        type="number"
                      />
                    </a-tooltip>
                    <!-- 选择类型 -->
                    <a-select
                      v-else-if="itemed.dataType == 2"
                      v-model:value="itemed.value"
                      show-search
                      :placeholder="`请选择${itemed.fieldDesc}`"
                      :options="itemed.selectOptions"
                      size="large"
                      :filter-option="filterOption"
                    ></a-select>
                    <!-- 日期类型 -->
                    <a-date-picker
                      v-else-if="itemed.dataType == 4"
                      style="width: 100%"
                      :placeholder="`请选择${itemed.fieldDesc}`"
                      v-model:value="itemed.value"
                      size="large"
                    />
                  </a-form-item>
                  <!-- <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
                    <a-button type="primary" html-type="submit"
                      >Submit</a-button
                    >
                    <a-button style="margin-left: 10px" @click="resetForm"
                      >Reset</a-button
                    >
                  </a-form-item>-->
                </a-form>
              </div>
            </a-collapse-panel>
            <a-collapse-panel
              v-if="list.length"
              key="furtherInformation"
              header="补充资料（必填）"
              class="further-information-collapse-item"
            >
              <a-form
                class="form-box"
                ref="formRefHwx"
                :colon="false"
                :hideRequiredMark="true"
                autocomplete="off"
                layout="vertical"
                :model="formUploadListHWP"
              >
                <a-form-item
                  :label="item.materialName"
                  :name="item.id"
                  :rules="[
                    {
                      required: true,
                      validator: uploadCheck,
                    },
                  ]"
                  v-for="item in formUploadListHWP"
                  :key="item.id"
                >
                  <div class="desc">
                    请上传{{ item.materialName }}原件的
                    <span>彩色照片或扫描件</span>
                    ，支持jpg/jpeg/png/pdf，最大20M（最多上传{{
                      item.maxNum
                    }}份资料）
                  </div>
                  <div class="upload-container-box">
                    <div
                      class="upload-container-item"
                      v-for="(itemed, index) in item.uploadArr"
                      :key="itemed.id"
                    >
                      <MySvgIcon
                        class="delete"
                        icon-class="icon-delete-filling"
                        v-if="item.uploadArr.length > 1 && itemed.url"
                        @click="deleteUploadData(item, index)"
                      />
                      <img
                        v-if="itemed.isPdf"
                        class="img-preview"
                        src="@/views/trade/pdf_default.png"
                      />
                      <div
                        v-else
                        class="img-preview"
                        :style="{
                          backgroundImage: itemed.url
                            ? `url(${itemed.url})`
                            : `url(${itemed.defaultUrl})`,
                        }"
                      />
                      <div class="preview-wrapper">
                        <div
                          class="preview-btn"
                          v-if="
                            itemed.loading ||
                            itemed.url !== undefined ||
                            itemed.defaultUrl
                          "
                          @click="
                            handleViewImg(
                              itemed.url || itemed.defaultUrl,
                              itemed.isPdf
                            )
                          "
                        >
                          <loading-outlined v-if="itemed.loading" />
                          <MySvgIcon
                            v-else
                            icon-class="icon-search"
                            style="
                              font-size: 24px;
                              fill: #fff;
                              display: inline-flex;
                            "
                          />
                        </div>
                      </div>
                      <a-form-item-rest>
                        <a-upload
                          class="upload-wrapper"
                          accept=".jpg, .jpeg, .png, .pdf"
                          :fileList="[]"
                          :before-upload="
                            file => beforeUpload(file, itemed, item)
                          "
                        >
                          <div class="upload-btn">
                            <span>点击上传</span>
                          </div>
                        </a-upload>
                      </a-form-item-rest>
                    </div>
                  </div>
                </a-form-item>
              </a-form>
            </a-collapse-panel>
          </a-collapse>
        </div>
        <PdfView ref="pdfView" />
      </div>
      <div v-if="!noBtn" class="further-information-menu-box">
        <span>
          <a-button
            :block="true"
            :ghost="false"
            shape="round"
            size="large"
            type="primary"
            @click="saverBtnFunction(false)"
            >保 存</a-button
          >
        </span>
        <span>
          <a-button
            :block="true"
            :ghost="false"
            shape="round"
            size="large"
            type="primary"
            @click="nextToPape()"
            >下一步</a-button
          >
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'FurtherInformation',
}
</script>
<script lang="ts" setup>
import { ref, getCurrentInstance, computed, watchEffect } from 'vue'
import type { RuleObject } from 'ant-design-vue/es/form'
import { CaretRightOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { fileUploadAttach } from '@/api/common'
import { api as viewerApi } from 'v-viewer'
import PdfView from '@/components/FilePreview/index.vue'
import 'viewerjs/dist/viewer.css'
import { PRODUCT_VERIFY_API, PRODUCT_CREDIT_LIMIT_API } from '@/api/index.js'
import { requestDictMap } from '@/api/common/index'
import { processTypeArr } from '@/views/product/component/contract/config'
import dayjs from 'dayjs'

const emit = defineEmits(['setCurrentUpFun', 'setbusineIdFun'])

defineProps({
  noBtn: {
    type: Boolean,
    required: false,
    default: false,
  },
})

const store = useStore()
const router = useRouter()
const route = useRoute()
const activeKey = ref()
const { proxy } = getCurrentInstance()
const formRefHwx: any = ref(null)
const pdfView: any = ref(null)
const saveIds = ref(null)
const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
const isEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isEnterpriseAccount']
)
const goBackPapeLock = computed<boolean>(
  () => store.getters['Product/goBackPapeLock']
)
const enterpriseTypeRoot = computed(() => {
  let enterpriseTypeRoot = 0
  // 企业类型 1 融资企业 2 核心企业 3 个人
  if (isCoreEnterpriseAccount.value) {
    enterpriseTypeRoot = 2
  } else if (!isEnterpriseAccount.value) {
    enterpriseTypeRoot = 1
    // enterpriseTypeRoot = 3
  } else {
    enterpriseTypeRoot = 1
  }
  return enterpriseTypeRoot
})

const formUploadListHWP = ref([])
// 补充资料默认添加格式封装
const uploadArrPushData = (idS, example) => {
  const examplePath = example.split('.')
  return {
    id: idS,
    loading: false,
    url: undefined,
    attachId: undefined,
    defaultUrl: example,
    isPdf: examplePath[examplePath.length - 1] == 'pdf' ? true : false,
    init: true,
  }
}

const uploadCheck = async (_rule: any, _value: string) => {
  const ruleData = formUploadListHWP.value.filter(
    item => item.id == _rule.fullField
  )[0]

  if (ruleData.init) {
    return Promise.resolve()
  }
  if (ruleData.uploadArr.length && ruleData.uploadArr[0].url === undefined) {
    return Promise.reject(`请上传${ruleData.materialName}`)
  }
  return Promise.resolve()
}

const beforeUpload = (file: any, target: object, parentData: any) => {
  if (parentData.uploadArr.length < parentData.maxNum) {
    parentData.uploadArr.push(
      uploadArrPushData(parentData.uploadArr.length, parentData.example)
    )
  }
  let passFlag = true

  // 检查文件格式
  const isAcceptFileTyle = [
    'image/jpg',
    'image/jpeg',
    'image/png',
    'application/pdf',
  ].includes(file.type)
  if (!isAcceptFileTyle) {
    message.warning('不支持的文件格式! 请上传支持的图片格式或者pdf文件 ')
    passFlag = false
  }

  // 检查文件大小
  const isLimit20M = file.size / 1024 / 1024 < 20
  if (!isLimit20M) {
    message.warning('图片或文件过大! 请上传小于20M的图片或文件')
    passFlag = false
  }

  const isPdf = file.type === 'application/pdf'

  if (passFlag) {
    // 上传文件
    target.loading = true

    const formData = new FormData()
    formData.append('file', file)

    fileUploadAttach(formData)
      .then(({ data }) => {
        target.loading = false
        if (data.success) {
          data = data.data
          message.success('上传成功')
          target.url = data.link
          target.isPdf = isPdf
          target.attachId = data.attachId
          formRefHwx.value.validateFields([parentData.id])
        } else {
          message.success(data.msg || '上传失败')
        }
      })
      .catch(() => {
        target.loading = false
        message.warning('上传失败!')
      })
  }

  return false
}

const handleViewImg = (targetUrl: string, isPdf: boolean) => {
  if (isPdf) {
    pdfView.value.handleOpen(targetUrl)
  } else {
    viewerApi({
      options: {
        toolbar: false,
        navbar: false,
        title: false,
      },
      images: [targetUrl],
    })
  }
}

const deleteUploadData = (item: object, index: number) => {
  item.uploadArr.splice(index, 1)

  if (item.uploadArr.length == 1 && item.uploadArr[0].url !== undefined) {
    item.uploadArr.push(uploadArrPushData(item.uploadArr.length, item.example))
  }
}

// 以下为非图片上传的js代码
const list = ref([])

// 校验规则
const checkBurl = (_rule: RuleObject) => {
  if (_rule.required) {
    for (const item of list.value) {
      const compare = item.creditFromFields.filter(
        itemed => itemed.id == _rule.fullField
      )[0]
      if (compare && !compare.value) {
        return Promise.reject(`${compare.fieldDesc}不能为空`)
      } else if (compare && compare.value) {
        return Promise.resolve()
      }
    }
  } else {
    return Promise.resolve()
  }
}

// // input失焦校验是否为number类型
// const excludeText = value => {
//   if (value.value && isNaN(value.value) === true) {
//     value.value = undefined
//   }
// }

// 下拉菜单搜索事件
const filterOption = (input: string, option: any) =>
  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0

// 布局属性
const layout = {
  labelCol: { span: 9 },
  wrapperCol: { span: 8 },
}

// 保存
const saverBtnFunction = boolean => {
  const objData = {}
  for (const item of list.value) {
    for (const itemed of item.creditFromFields) {
      if (itemed.value && typeof itemed.value !== 'string') {
        itemed['timeValue'] = dayjs(itemed.value).format('YYYY-MM-DD')
      }
      // 处理后端所需数据
      objData[getKey(itemed)] = itemed.value
    }
  }

  // 保存资料
  const params = {
    data: JSON.stringify(objData),
    creditForm: JSON.stringify(list.value),
    supplementMaterial: JSON.stringify(formUploadListHWP.value),
    goodsId: route.query.goodId,
    id: saveIds.value,
  }

  PRODUCT_CREDIT_LIMIT_API.materialSave(params).then(res => {
    const resData = res.data
    if (resData.code == 200 && resData.data) {
      // sessionStorage.setItem('customerMaterialId', JSON.stringify(resData.data)) // 保存成功后返回ID
      if (boolean) {
        routerQueryDelte(1)
        // store.commit('Product/setCreditLimitForApplication', 1)
      } else {
        message.success('已保存')
        saveIds.value = resData.data
      }
    }
  })
}
// 下一步
const nextToPape = async () => {
  let formRefxType = false
  for (const key in formUploadListHWP.value) {
    formUploadListHWP.value[key].init = false
  }
  // 授信表单资料校验
  for (const item of list.value) {
    try {
      if (proxy.$refs[`formRef${item.orderNum}`]) {
        await proxy.$refs[`formRef${item.orderNum}`][0]
          .validateFields()
          .catch(err => {
            formRefxType = true
          })
      } else {
        message.warning('还有资料未填写')
        return
      }
    } catch (errorInfo) {
      // console.log('Failed:', errorInfo)
    }
  }
  // 补充资料校验
  if (formRefHwx.value) {
    formRefHwx.value
      .validateFields()
      .then(res => {
        if (formRefxType) {
          message.warning('还有资料未填写')
          return
        }
        // 流程类型 no 融资企业 个人 yes 核心企业
        const typeRoot = isCoreEnterpriseAccount.value
          ? processTypeArr[1]['yes']
          : processTypeArr[1]['no']
        const typeRoot2 = isCoreEnterpriseAccount.value
          ? processTypeArr[2]['yes']
          : processTypeArr[2]['no']
        const params = {
          businessId: route.query.goodId,
          type: typeRoot,
        }
        // 额度申请流程保存参数
        const paramsSave1 = {
          businessId: route.query.goodId,
          progress: 1,
          type: typeRoot,
        }
        // 额度激活流程保存参数
        const paramsSave2 = {
          businessId: route.query.goodId,
          progress: 0,
          type: typeRoot2,
        }
        // 保存开通记录（除融资端的应收账款）
        const paramsSave3 = {
          goodsId: route.query.goodId,
          goodsType: route.query.goodType,
        }
        // 获取流程进度
        PRODUCT_VERIFY_API.getByBusinessIdAndType2(params).then(async res => {
          const resData = res.data
          if (resData.code == 200 && !resData.data) {
            // 保存开通进度
            const { data } =
              await PRODUCT_VERIFY_API.businessProcessProgressSave2(paramsSave1)
            await PRODUCT_VERIFY_API.businessProcessProgressSave2(paramsSave2)
            // 后端说：不用调这个我在那个获取可开通列表接口里面写了保存客户产品的逻辑了
            // await PRODUCT_VERIFY_API.saveCustomerGoods(paramsSave3)
            if (data.success) {
              const { data: resData } = data
              emit('setbusineIdFun', resData.id)
              saverBtnFunction(true)
            }
          } else if (resData.code == 200 && resData.data.progress === 0) {
            // 修改申请进度
            PRODUCT_VERIFY_API.businessProcessProgressUpdate(paramsSave1).then(
              ({ data }) => {
                // const { data: resData } = data
                if (data.success) {
                  saverBtnFunction(true)
                }
              }
            )
          } else if (resData.code == 200) {
            saverBtnFunction(true)
          }
        })
      })
      .catch(res => {})
  } else {
    message.warning('还有资料未填写')
  }
}

// 外部单纯组件化调用改造
const externalCallGet = async () => {
  let formRefxType = false
  for (const key in formUploadListHWP.value) {
    formUploadListHWP.value[key].init = false
  }
  // 授信表单资料校验
  for (const item of list.value) {
    try {
      if (proxy.$refs[`formRef${item.orderNum}`]) {
        await proxy.$refs[`formRef${item.orderNum}`][0]
          .validateFields()
          .catch(err => {
            formRefxType = true
          })
      } else {
        message.warning('还有资料未填写')
        return false
      }
    } catch (errorInfo) {
      // console.log('Failed:', errorInfo)
    }
  }
  // 补充资料校验
  if (formRefHwx.value) {
    return formRefHwx.value
      .validateFields()
      .then(res => {
        if (formRefxType) {
          message.warning('还有补充资料未上传完成')
          return false
        }
        const objData = {}
        for (const item of list.value) {
          for (const itemed of item.creditFromFields) {
            if (itemed.value && typeof itemed.value !== 'string') {
              itemed['timeValue'] = dayjs(itemed.value).format('YYYY-MM-DD')
            }
            // 处理后端所需数据
            objData[getKey(itemed)] = itemed.value
          }
        }

        // 保存资料
        const params = {
          data: JSON.stringify(objData),
          creditForm: JSON.stringify(list.value),
          supplementMaterial: JSON.stringify(formUploadListHWP.value),
          goodsId: route.query.goodId,
          id: saveIds.value,
        }
        return params
      })
      .catch(res => {
        message.warning('还有补充资料未上传')
        return false
      })
  } else {
    message.warning('还有补充资料未上传')
    return false
  }
}

// 获取授信表单
const getCreditFormTemplateListFun = () => {
  const params = {
    goodsId: route.query.goodId,
    goodsType: route.query.goodType,
    enterpriseType: enterpriseTypeRoot.value,
  }
  PRODUCT_CREDIT_LIMIT_API.getCreditFormTemplateList2(params).then(
    async res => {
      const resData = res.data
      if (resData.code == 200) {
        for (const item of resData.data) {
          // creditFromFields数据处理
          const arrChildrenData = []
          for (const itemed of item.creditFromFields) {
            const requestDictMapList = []
            // 获取下拉菜单data
            if (itemed.dataType == 2) {
              await requestDictMap(getKey(itemed)).then(res => {
                const resDataed = res.data
                if (resDataed.code == 200) {
                  // 处理字典数据
                  for (const reitem of resDataed.data) {
                    requestDictMapList.push({
                      value: reitem.dictKey,
                      label: reitem.dictValue,
                      id: reitem.id,
                    })
                  }

                  arrChildrenData.push({
                    id: itemed.id,
                    fieldDesc: itemed.fieldDesc,
                    fieldName: itemed.fieldName,
                    tableName: itemed.tableName,
                    isRequired: itemed.isRequired, // 0是非必填 1是必填
                    orderNum: itemed.orderNum,
                    dataType: itemed.dataType, // 1:数字;2:选择;3:文本;4日期;5元;6万元
                    triggerType:
                      itemed.dataType == 2 || itemed.dataType == 4
                        ? false
                        : true,
                    selectOptions: requestDictMapList,
                    value: undefined,
                  })
                }
              })
            } else {
              arrChildrenData.push({
                id: itemed.id,
                fieldDesc: itemed.fieldDesc,
                fieldName: itemed.fieldName,
                tableName: itemed.tableName,
                isRequired: itemed.isRequired, // 0是非必填 1是必填
                orderNum: itemed.orderNum,
                dataType: itemed.dataType, // 1:数字;2:选择;3:文本;4日期;5元;6万元
                triggerType:
                  itemed.dataType == 2 || itemed.dataType == 4 ? false : true,
                selectOptions: requestDictMapList,
                value: undefined,
              })
            }
          }
          list.value.push({
            id: item.id,
            templateName: item.templateName,
            orderNum: item.orderNum,
            creditFromFields: arrChildrenData,
          })
        }

        // if (goBackPapeLock.value) {
        materialDetailFun() // 驳回后请求之前所填写信息
        // }

        setTimeout(() => {
          if (list.value[0]?.id) {
            activeKey.value = [list.value[0].id] // 获取第一个折叠面板的key进行展开处理
          }
        }, 100)
      }
    }
  )
}

//获取表单唯一key
const getKey = (itemed: any) => {
  let key = ''
  if (itemed.tableName != null && itemed.tableName != '') {
    key = `${itemed.tableName}_${itemed.fieldName}`
  } else {
    key = `${itemed.fieldName}`
  }
  return key
}

// 获取补充资料
const getGoodsMaterialListFun = () => {
  const params = {
    goodsId: route.query.goodId,
    uploadNode: '2-0', // 2代表申请额度 0表示第一步
    uploadUser: enterpriseTypeRoot.value,
  }
  //   const isCoreEnterpriseAccount = computed<boolean>(
  //   () => store.getters['Auth/isCoreEnterpriseAccount']
  // )
  PRODUCT_CREDIT_LIMIT_API.getGoodsMaterialList(params).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      const arrData = []
      for (const item of resData.data) {
        if (enterpriseTypeRoot.value === item.uploadUser) {
          const uploadArr = []
          uploadArr.push(uploadArrPushData(1, item.example))
          arrData.push({
            id: item.id,
            materialId: item.materialId,
            materialName: item.materialName,
            multipleType: item.multipleType,
            maxNum: item.num,
            uploadArr: uploadArr,
            example: item.example,
          })
        }
      }
      formUploadListHWP.value = arrData
    }
  })
}

// 页面加载请求
const onLoad = () => {
  getCreditFormTemplateListFun() // 获取授信表单
  getGoodsMaterialListFun() // 获取补充资料
}

// 刷新后请求兼容
// 下一个页面进行上一步操作不进入
// if (!goBackPapeLock.value) {
//   if (isLogined.value) {
//   customerGoodsDetail()
// } else if (!initLoading.value) {
//   router.push({ name: 'Home' })
// } else {
//   watchEffect(() => {
//     if (isLogined.value && route.query.customerGoodsId) {
//       customerGoodsDetail()
//     }
//   })
// }
// watchEffect(() => {
//   if (isLogined.value && route.query.goodId) {
//     const params = {
//       businessId: route.query.goodId,
//       type: isCoreEnterpriseAccount.value ? 7 : 2, // 流程类型 2 融资企业 7 核心企业
//     }
//     // 查询申请额度流程进度
//     PRODUCT_VERIFY_API.getByBusinessIdAndType2(params)
//       .then(res => {
//         const resData = res.data
//         if (resData.code == 200 && resData.data) {
//           store.commit(
//             'Product/setCreditLimitForApplication',
//             resData.data.progress
//           )
//         } else if (resData.code == 200 && !resData.data) {
//           store.commit('Product/setCreditLimitForApplication', 0)
//           onLoad()
//         }
//       })
//       .catch(({ msg, hideMsgFunc }) => {
//         // console.warning(data)
//         // func()
//       })
//   }
// })
// }
// else {
watchEffect(() => {
  if (isLogined.value && route.query.goodId && route.query.goodType) {
    onLoad()
  }
})
// }

// 驳回后请求之前所填写信息
const materialDetailFun = () => {
  const params = {
    goodsId: route.query.goodId,
  }
  PRODUCT_CREDIT_LIMIT_API.materialDetail(params).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      const dat = resData.data
      if (!dat) return
      saveIds.value = dat?.id
      const creditForm = JSON.parse(dat.creditForm)
      const supplementMaterial = JSON.parse(dat.supplementMaterial)
      // 转义时间格式value
      for (const item of creditForm) {
        for (const itemed of item.creditFromFields) {
          if (itemed.dataType == 4 && itemed.timeValue) {
            itemed.value = dayjs(JSON.stringify(itemed.timeValue), 'YYYY-MM-DD')
          }
        }
      }
      // 授信表单数据回显
      for (const item of list.value) {
        for (const itemed of creditForm) {
          if (item.templateName == itemed.templateName) {
            const listData = item.creditFromFields
            const listData1 = itemed.creditFromFields
            for (let i = 0; i < listData.length; i++) {
              if (
                `${listData[i]?.tableName}_${listData[i]?.fieldName}` ==
                `${listData1[i]?.tableName}_${listData1[i]?.fieldName}`
              ) {
                listData[i].value = listData1[i].value
              } else {
                listData.forEach(item => {
                  if (
                    `${item?.tableName}_${item?.fieldName}` ==
                    `${listData1[i]?.tableName}_${listData1[i]?.fieldName}`
                  ) {
                    item.value = listData1[i].value
                  }
                })
              }
            }
            break
          }
        }
      }
      // 补充资料数据回显
      for (const item of formUploadListHWP.value) {
        for (const itemed of supplementMaterial) {
          if (item.materialId == itemed.materialId) {
            const listData = item.uploadArr
            const listData1 = itemed.uploadArr
            for (let i = 0; i < listData1.length; i++) {
              if (listData1[i]?.url && listData[i]) {
                const examplePath = listData1[i].url.split('.')
                listData[i].isPdf =
                  examplePath[examplePath.length - 1] == 'pdf' ? true : false
                listData[i].url = listData1[i].url
              } else {
                listData[i] = listData1[i]
              }
            }
            break
          }
        }
      }
    }
  })
}

// 步骤条修改
const routerQueryDelte = page => {
  emit('setCurrentUpFun', page)
  // if (page == 0) {
  //   // 回退页面的事件执行
  //   store.commit('Product/setGOBackPapeLock')
  // }
}

const tiaozhuanYSFun = () => {
  const routerRES = router.resolve({ name: 'agreement', params: { type: 2 } })
  window.open(routerRES.path, '_blank')
}

defineExpose({ externalCallGet })
</script>

<style lang="scss" scoped>
.further-information-container {
  position: relative;
  max-width: 1400px;
  margin: auto;
  box-sizing: border-box;
  padding-bottom: 60px;

  :deep(.ant-form-item-label > label) {
    height: 40px;
  }

  :deep(.ant-collapse-header) {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .further-information-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .further-information-show-part {
      width: 100%;

      .further-information-state {
        height: 36px;
        display: flex;
        align-items: center;
        justify-items: flex-start;
        background: #f8f9fb;
        border-radius: 6px;
        margin-top: 40px;
        padding: 0 12px;
        box-sizing: border-box;

        .to-privacy {
          cursor: pointer;

          & > span {
            color: #031222;
          }

          &:hover > span {
            text-decoration: underline;
          }
        }

        & > span {
          font-size: 14px;
          color: #53627c;
        }
      }

      .further-information-collapse-box {
        .further-information-collapse {
          background-color: #fff;

          .further-information-collapse-item {
            background: #ebf5ff;
            border-radius: 8px;
            border-bottom: 0;
            margin-top: 24px;

            :deep(.further-information-collapse-item-form-item) {
              input::-webkit-outer-spin-button,
              input::-webkit-inner-spin-button {
                -webkit-appearance: none;
              }

              input[type='number'] {
                appearance: textfield;
              }
            }

            & div.further-information-collapse-item-form-item:first-child {
              margin-top: 32px;

              .numeric-input .ant-tooltip-inner {
                min-width: 32px;
                min-height: 37px;
              }

              .numeric-input .numeric-input-title {
                font-size: 14px;
              }
            }

            :deep(.ant-collapse-content) {
              background-color: #fff;
              box-sizing: border-box;
            }

            :deep(.ant-collapse-content-box) {
              padding-bottom: 1px;
              box-sizing: border-box;
            }

            :deep(.ant-collapse-arrow) {
              font-size: 14px;
            }
          }
        }
      }
    }

    .further-information-menu-box {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      margin-top: 47px;

      & > span {
        width: 188px;
        height: 48px;
        margin-right: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;

        .ant-btn-round.ant-btn-lg {
          height: 100%;
        }
      }

      & > span:nth-child(2) {
        .ant-btn-primary {
          color: #fff;
          background: #0bb07b;
          border-color: #0bb07b;
        }

        .ant-btn-primary:hover,
        .ant-btn-primary:focus {
          color: #fff;
          background: #0ca574;
          border-color: #0ca574;
        }
      }

      & > span:last-child {
        margin-right: 0;
      }
    }
  }

  .form-box {
    margin-top: 24px;

    :deep(.ant-form-item-label > label) {
      font-size: 16px;
      @include family-PingFangSC-Semibold;
      font-weight: 600;
      color: #0a1f44;
    }

    :deep(.ant-form-item) {
      border-bottom: 1px dashed #e1e4e8;
      padding-bottom: 32px;
      margin-bottom: 31px;
    }

    :deep(.ant-form-item-with-help) {
      padding-bottom: 0;
      margin-bottom: 31px;
    }

    :deep(.ant-form-item-explain) {
      min-height: 32px;
      line-height: 32px;
    }

    .upload-container-box {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;

      .upload-container-item {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 208px;
        height: 208px;
        border-radius: 8px;
        border: 1px solid #efefef;
        overflow: hidden;
        margin-right: 20px;
        margin-top: 20px;

        &:last-child {
          margin-right: 0;
        }

        .delete {
          position: absolute;
          top: 4px;
          right: 4px;
          font-size: 20px;
          fill: rgba(24, 44, 79, 0.5);
          cursor: pointer;
          outline: none;
          z-index: 2;
          backdrop-filter: saturate(90%) blur(6px);
          -webkit-backdrop-filter: saturate(90%) blur(6px);
          transition: all 0.3s;

          &:hover {
            fill: rgba(24, 44, 79, 0.6);
          }
        }

        .img-preview {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          z-index: 1;
        }

        .preview-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          height: 100%;
          background-color: #fff;
        }

        .upload-wrapper {
          height: 48px;
          flex-shrink: 0;
          cursor: pointer;
          z-index: 2;

          :deep(.ant-upload-select) {
            width: 100%;
            height: 100%;
          }

          .upload-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            text-align: center;
            background: rgba(24, 44, 79, 0.5);
            border-radius: 0px 0px 8px 8px;
            backdrop-filter: saturate(90%) blur(6px);
            -webkit-backdrop-filter: saturate(90%) blur(6px);
            transition: all 0.3s;

            &:hover {
              background: rgba(24, 44, 79, 0.6);
            }

            & > span {
              font-size: 16px;
              @include family-PingFangSC-Semibold;
              font-weight: 600;
              color: #ffffff;
            }
          }
        }

        .preview-btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          background: rgba(24, 44, 79, 0.5);
          backdrop-filter: saturate(90%) blur(6px);
          -webkit-backdrop-filter: saturate(90%) blur(6px);
          border-radius: 50%;
          overflow: hidden;
          cursor: pointer;
          z-index: 2;
          transition: all 0.3s;

          &:hover {
            background: rgba(24, 44, 79, 0.6);
          }

          span {
            font-size: 16px;
            @include family-PingFangSC-Semibold;
            font-weight: 600;
            color: #ffffff;
            line-height: 24px;
          }
        }
      }
    }

    .desc {
      font-size: 14px;

      font-weight: 400;
      color: #53627c;
      margin-bottom: 4px;

      & > span {
        color: #007fff;
      }
    }
  }
}
</style>
