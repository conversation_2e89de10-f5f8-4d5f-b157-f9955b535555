<template>
  <div class="authorization-box">
    <div class="authorization-children">
      <div
        class="authorization-item"
        v-for="item in getGoodsContractTemplateList"
        :key="item.id"
      >
        <a-spin :spinning="item.loadingType">
          <div class="authorization-item-box">
            <div
              :class="{
                'authorization-item-left': item.completeSignStatus,
                'authorization-item-left-success': !item.completeSignStatus,
              }"
            >
              <span>{{ item.statusType }}</span>
              <span>{{ item.contractTemplateName }}</span>
            </div>
            <div
              class="authorization-item-right"
              @click="immediatelySignedClick(item)"
              v-if="item.completeSignStatus"
            >
              <span>立即签署</span>
              <span
                ><MySvgIcon
                  icon-class="icon-arrow-right1"
                  style="color: #0d55cf; font-size: 20px"
              /></span>
            </div>
            <div
              class="authorization-item-right"
              @click="getContractDownLoadUrl(item)"
              v-else
            >
              <span>查看合同</span>
              <span
                ><MySvgIcon
                  icon-class="icon-arrow-right1"
                  style="color: #0d55cf; font-size: 20px"
              /></span>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
    <div class="authorization-menu-box">
      <span>
        <a-button
          :block="true"
          :ghost="true"
          shape="round"
          size="large"
          type="primary"
          @click="goBackPape()"
          >上一步</a-button
        >
      </span>
      <span>
        <a-button
          :block="true"
          :ghost="false"
          shape="round"
          size="large"
          type="primary"
          @click="nextToPape()"
          >立即提交</a-button
        >
      </span>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'AuthorizationS',
}
</script>
<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const store = useStore()
import { message } from 'ant-design-vue'
import { PRODUCT_CREDIT_LIMIT_API, PRODUCT_VERIFY_API } from '@/api/index.js'

const getGoodsContractTemplateList = ref([])
const signStatus = ref(false)
const lock = ref(true)
const contractIdList = ref([])
const locked = ref(true)
const lockSubmit = ref(true)
const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
const contractLoadType = computed<boolean>(
  () => store.getters['Product/contractLoadType']
)
const demonstrator = computed<boolean>(
  () => store.getters['Product/demonstrator']
)
const demonstratorPath = computed<string>(
  () => store.getters['Product/demonstratorPath']
)
const demonstratorTest = computed<boolean>(
  () => store.getters['Product/demonstratorTest']
)

// 判断是否演示服
const determine = () => {
  const host = window.location?.host.split(':')[0]
  if (host === demonstratorPath.value || demonstratorTest.value) {
    store.commit('Product/setDemonstrator', true)
  } else {
    store.commit('Product/setDemonstrator', false)
  }
}

// 获取浏览器url参数
const getParams = key => {
  let search = window.location.search.replace(/^\?/, '')
  let pairs = search.split('&')
  let paramsMap = pairs
    .map(pair => {
      let [key, value] = pair.split('=')
      return [decodeURIComponent(key), decodeURIComponent(value)]
    })
    .reduce((res, [key, value]) => Object.assign(res, { [key]: value }), {})
  return paramsMap[key] || ''
}

// 检查签署合同状态是否已更新
const getGoodsContractCheck = () => {
  const data = {
    contractId: getParams('amp;contractId'),
  }
  PRODUCT_CREDIT_LIMIT_API.activeUpdateContract(data).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      let setTimeoutData = null
      const params = {
        goodsId: route.query.goodId,
        signNode: '2-1',
        signUser: isCoreEnterpriseAccount.value ? 2 : 1, // 企业类型 1 融资企业 2 核心企业 3 所有人
        processType: isCoreEnterpriseAccount.value ? 7 : 2, // 流程类型 2 融资企业 7 核心企业
      }
      PRODUCT_CREDIT_LIMIT_API.getGoodsContractTemplate(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          for (const item of resData.data) {
            if (item.templateId == getParams('amp;templateId')) {
              if (item.contractId && (item.status == 5 || item.status == 3)) {
                try {
                  message.success(`${item.contractTemplateName}已签署`)
                } catch (errorInfo) {
                  message.success('授权书已签署')
                }
                clearTimeout(setTimeoutData)
                getGoodsContractTemplateFun() // 获取合同模板list数据方法
                break
              } else {
                if (locked.value) {
                  getGoodsContractCheck() // 自调
                  setTimeoutData = setTimeout(() => {
                    locked.value = false
                  }, 20000)
                }
              }
            }
          }
        }
      })
    }
  })
}

// 获取合同模板
const getGoodsContractTemplateFun = () => {
  const params = {
    goodsId: route.query.goodId,
    signNode: '2-1',
    signUser: isCoreEnterpriseAccount.value ? 2 : 1, // 企业类型 1 融资企业 2 核心企业 3 所有人
    processType: isCoreEnterpriseAccount.value ? 7 : 2, // 流程类型 2 融资企业 7 核心企业
  }
  PRODUCT_CREDIT_LIMIT_API.getGoodsContractTemplate(params).then(res => {
    const resData = res.data
    if (resData.code == 200) {
      for (const item of resData.data) {
        let statusType = ''

        switch (item.status) {
          case 1:
            statusType = '待签署'
            signStatusSwitch()
            break
          case 2:
            statusType = '已取消'
            signStatusSwitch()
            break
          case 3:
            statusType = '已签署'
            contractIdList.value.push(item.contractId)
            break
          case 4:
            statusType = '已失效'
            signStatusSwitch()
            break
          case 5:
            statusType = '已完成'
            contractIdList.value.push(item.contractId)
            break
          default:
            statusType = '异常状态'
            signStatusSwitch()
            break
        }
        getGoodsContractTemplateList.value.push({
          id: item.id,
          status: item.status,
          templateId: item.templateId,
          contractTemplateName: item.contractTemplateName,
          statusType: statusType,
          loadingType: false,
          contractId: item.contractId,
          completeSignStatus:
            item.status == 5 || item.status == 3 ? false : true,
        })
        if (contractLoadType.value) {
          setTimeout(() => {
            store.commit('Product/setContractLoadType', false) // 合同签署完成状态检测loading
          }, 200)
        }
      }
    }
  })
}
// 刷新后请求兼容
watchEffect(() => {
  // 签署完成重新发送合同
  if (isLogined.value && route.query.goodId) {
    if (getParams('amp;come') == 'true') {
      store.commit('Product/setContractLoadType', true) // 合同签署完成状态检测loading
      getGoodsContractCheck() // 获取合同模板list数据方法
    } else {
      getGoodsContractTemplateFun() // 获取合同模板list数据方法
    }
  }
})
determine() //检查是否是测试服

// 发送合同
const immediatelySignedClick = item => {
  if (lock.value) {
    lock.value = false
    item.loadingType = true // 启动loading

    const data = {
      templateId: item.templateId, // 模板id
      goodId: route.query.goodId,
      signNode: '2-1', // 签署节点
      // businessId: '81', //先不传
      processType: 2, // 流程类型
    }
    // 生成合同
    PRODUCT_CREDIT_LIMIT_API.generateContractWithBusiness(data)
      .then(res => {
        const resData = res.data
        if (resData.code == 200 && resData.data) {
          const paramsDataed = {
            contractId: resData.data,
            returnUrl: `${window.location.href}&come=true&templateId=${item.templateId}&contractId=${resData.data}`,
          }
          // 签署合同
          PRODUCT_CREDIT_LIMIT_API.skipToSign(paramsDataed)
            .then(resed => {
              const resDataed = resed.data
              if (resDataed.code == 200 && resDataed.data) {
                window.open(resDataed.data)
                lock.value = true
                item.loadingType = false // 关闭loading
              }
            })
            .catch(({ msg, hideMsgFunc }) => {
              lock.value = true
              item.loadingType = false // 关闭loading
            })
        }
      })
      .catch(({ msg, hideMsgFunc }) => {
        lock.value = true
        item.loadingType = false // 关闭loading
        // console.error(data)
        // func()
      })
  }
}

// 查看合同
const getContractDownLoadUrl = item => {
  if (lock.value) {
    lock.value = false
    item.loadingType = true // 启动loading
    // 查看合同
    const params = {
      contractId: item.contractId,
    }
    PRODUCT_CREDIT_LIMIT_API.getContractDownLoadUrl(params)
      .then(resed => {
        const resDataed = resed.data
        if (resDataed.code == 200 && resDataed.data) {
          window.open(resDataed.data)
          lock.value = true
          item.loadingType = false // 关闭loading
        }
      })
      .catch(({ msg, hideMsgFunc }) => {
        lock.value = true
        item.loadingType = false // 关闭loading
      })
  }
}

// 上一步
const goBackPape = () => {
  store.commit('Product/setCreditLimitForApplication', 0)
  // 进度条回退
  store.commit('Product/setGOBackPapeLock')
  // 回退页面的事件执行
}

// 立即提交事件
const nextToPape = () => {
  if (!lockSubmit.value) return
  lockSubmit.value = false
  if (signStatus.value && !demonstrator.value) {
    message.error('还有授权书未签署,请先签署完成')
    prevenSubmit()
    return
  }
  const params = {
    businessId: route.query.goodId,
    type: isCoreEnterpriseAccount.value ? 7 : 2, // 流程类型 2 融资企业 7 核心企业
  }
  // 获取流程进度
  PRODUCT_VERIFY_API.getByBusinessIdAndType(params).then(res => {
    const resData = res.data
    const statusTurnOn = [1, 2, 3].indexOf(resData.data.status) == -1
    let turnOn = false
    if (!statusTurnOn && resData.data.progress === 1) {
      turnOn = true
    } else if (!turnOn) {
      turnOn = true
    }

    if (resData.code == 200 && turnOn) {
      const dataSave = {
        // customerMaterialId: JSON.parse(
        //   sessionStorage.getItem('customerMaterialId')
        // ), // 第一步返回的id
        contractIdList: contractIdList.value,
        goodsId: route.query.goodId,
        processInstanceId: resData.data.processInstanceId,
        processType: isCoreEnterpriseAccount.value ? 7 : 2, // 流程类型 2 融资企业 7 核心企业
        enterpriseType: isCoreEnterpriseAccount.value ? 2 : 1, // 企业类型 1 融资企业 2 核心企业
        goodsType: route.query.goodType || getParams('amp;goodType'), // 产品类型 1 应收 2 代采 3 云信
      }
      // 保存申请进度
      PRODUCT_CREDIT_LIMIT_API.submitQuotaApplyProcess(dataSave)
        .then(res => {
          const resData = res.data
          if (resData.code == 200) {
            store.commit('Product/setCreditLimitForApplication', 2)
            router.replace({
              query: {
                goodId: route.query.goodId,
                goodType: route.query.goodType || getParams('amp;goodType'),
                flowType: route.query.flowType || getParams('amp;flowType'),
              },
            })
          }
        })
        .catch(() => {
          prevenSubmit()
        })
    } else {
      message.error('相同流程无法重复提交')
      prevenSubmit()
    }
  })
}

// 切换允许提交状态
const signStatusSwitch = () => {
  signStatus.value = true
}

// 防止重复提交事件
const prevenSubmit = () => {
  setTimeout(() => {
    lockSubmit.value = true
  }, 2000)
}
</script>

<style lang="scss" scoped>
.authorization-box {
  position: relative;
  max-width: 1400px;
  margin: auto;
  box-sizing: border-box;

  .authorization-children {
    margin-top: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .authorization-item {
      width: 700px;
      height: 64px;
      border-radius: 8px;
      border: 1px solid #e1e4e8;
      transition: border 0.3s;
      box-sizing: border-box;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        border: 1px solid #0c66ff;
      }

      :deep(.ant-spin-nested-loading) {
        height: 100%;
      }

      .authorization-item-box {
        height: 64px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
      }

      .authorization-item-left {
        & > span:first-child {
          box-sizing: border-box;
          padding: 4px 10px;
          background: #ebf5ff;
          border-radius: 12px;
          margin-right: 8px;

          font-size: 11px;
          @include family-PingFangSC-Semibold-SFProText;
          font-weight: 600;
          color: #0d55cf;
        }

        & > span:last-child {
          font-size: 14px;
          @include family-PingFangSC-Semibold;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
        }
      }

      .authorization-item-left-success {
        & > span:first-child {
          box-sizing: border-box;
          padding: 4px 10px;
          background: #cff8eb;
          border-radius: 12px;
          margin-right: 8px;

          font-size: 11px;
          @include family-PingFangSC-Semibold-SFProText;
          font-weight: 600;
          color: #00865a;
        }

        & > span:last-child {
          font-size: 14px;
          @include family-PingFangSC-Semibold;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
        }
      }

      .authorization-item-right {
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover span:first-child {
          text-decoration: underline;
        }

        & > span:first-child {
          font-size: 14px;
          @include family-PingFangSC-Medium;
          font-weight: 500;
          color: #0d55cf;
        }
      }
    }
  }

  .authorization-menu-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    margin-top: 47px;

    & > span {
      width: 188px;
      height: 48px;
      margin-right: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      .ant-btn-round.ant-btn-lg {
        height: 100%;
      }
    }

    & > span:last-child {
      margin-right: 0;
    }
  }
}
</style>
