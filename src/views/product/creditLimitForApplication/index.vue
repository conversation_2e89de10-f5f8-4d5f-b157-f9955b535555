<template>
  <div class="credit-limit-for-application">
    <!-- <span class="nav-header-bg"></span> -->
    <ArticleSetps
      :succerssType="succerssPageType"
      :info="'申请额度'"
      :arrData="setpHeaderData"
      :current="current"
      :widths="'1400px'"
    />
    <FurtherInformation
      v-if="furtherInformationType"
      @setCurrentUpFun="currentUpFun"
      @setbusineIdFun="busineIdFun"
    />
    <Authorization
      v-if="authorizationType"
      :busineId="busineId"
      @setCurrentUpFun="currentUpFun"
    />
    <BusinessApproval
      :type="processTypeRoot"
      v-if="businessApprovalType"
      @goBack="currentUpFun"
    />
    <SuccerssPage v-if="succerssPageType" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'creditLimitForApplication',
}
</script>
<script setup lang="ts">
import { useStore } from 'vuex'
import ArticleSetps from '@/components/articleSetps/index.vue'
import FurtherInformation from '@/views/product/creditLimitForApplication/components/furtherInformation/index.vue'
import Authorization from '@/views/product/creditLimitForApplication/components/authorization/index.vue'
import BusinessApproval from '@/views/product/component/businessApproval.vue'
import SuccerssPage from '@/views/product/creditLimitForApplication/components/succerssPage/index.vue'
import { ref, onMounted, watch, computed, watchEffect } from 'vue'
import { PRODUCT_VERIFY_API } from '@/api/index.js'
import { useRoute } from 'vue-router'
const route = useRoute()
const store = useStore()
const furtherInformationType = ref(true)
const authorizationType = ref(false)
const businessApprovalType = ref(false)
const succerssPageType = ref(false)
const current = ref(-1)
const processTypeRoot = ref(0)
const busineId = ref('')
const isCoreEnterpriseAccount = computed<boolean>(
  () => store.getters['Auth/isCoreEnterpriseAccount']
)
const isLogined = computed<boolean>(() => store.getters['Auth/isLogined'])
import { processTypeArr } from '@/views/product/component/contract/config'
const setpHeaderData = [
  { id: 1, name: '补充资料' },
  { id: 2, name: '签署授权书' },
  { id: 3, name: '业务审批' },
  { id: 4, name: '获得额度' },
]

// 用于第一次保存流程进度的id获取
const busineIdFun = val => {
  busineId.value = val
}

// 变更流程进度
const currentUpFun = val => {
  current.value = val
}

// 查询流程进度
const getByBusinessIdAndTypeApi = () => {
  processTypeRoot.value = isCoreEnterpriseAccount.value
    ? processTypeArr[1]['yes']
    : processTypeArr[1]['no'] // 流程类型 no 融资企业 yes 核心企业 个人
  const params = {
    businessId: route.query.goodId,
    type: processTypeRoot.value,
  }
  PRODUCT_VERIFY_API.getByBusinessIdAndType(params)
    .then(res => {
      const resData = res.data
      busineId.value = resData.data.id
      if (resData.code == 200 && !resData.data) {
        currentUpFun(0)
      } else if (resData.code == 200 && resData.data) {
        currentUpFun(resData.data.progress)
      }
    })
    .catch(({ msg, hideMsgFunc }) => {})
}

watchEffect(() => {
  if (isLogined.value && route.query.goodId) {
    getByBusinessIdAndTypeApi()
  }
})

onMounted(() => {
  watch(
    () => current.value,
    val => {
      if (val == 1) {
        furtherInformationType.value = false
        authorizationType.value = true
        businessApprovalType.value = false
        succerssPageType.value = false
      } else if (val == 0) {
        furtherInformationType.value = true
        authorizationType.value = false
        businessApprovalType.value = false
        succerssPageType.value = false
      } else if (val == 2) {
        furtherInformationType.value = false
        authorizationType.value = false
        businessApprovalType.value = true
        succerssPageType.value = false
      } else if (val == 3) {
        furtherInformationType.value = false
        authorizationType.value = false
        businessApprovalType.value = false
        succerssPageType.value = true
      }
    }
  )
})
</script>

<style lang="scss" scoped>
.credit-limit-for-application {
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  padding-top: 40px;
  flex: 1;

  .nav-header-bg {
    height: 72px;
    background-color: RGB(255, 255, 255);
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    transform: translateY(-100%);
  }
}
</style>
