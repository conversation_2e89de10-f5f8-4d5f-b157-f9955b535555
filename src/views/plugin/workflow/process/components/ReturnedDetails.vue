<template>
  <div class="invoice-details-container-my">
    <CollapseCard title="还款明细" :active="false">
      <el-table
        :data="repaymentTable"
        show-summary
        border
        :summary-method="getSummaries"
      >
        <el-table-column
          fixed="left"
          prop="period"
          label="期数"
          :formatter="
            (row, column, cellValue) => {
              return `${cellValue}期`
            }
          "
          width="80"
          align="align"
        >
        </el-table-column>
        <el-table-column
          prop="repaymentTime"
          label="应还日期"
          min-width="120"
          align="align"
        >
        </el-table-column>
        <el-table-column
          prop="totalAmount"
          label="应还总额(元)"
          :formatter="
            (row, column, cellvalue) => formatMoney(cellvalue || '0.00')
          "
          min-width="120"
          align="align"
        >
        </el-table-column>
        <el-table-column
          prop="actualAmount"
          label="实还总额（元）"
          :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
          min-width="120"
          align="align"
        >
          <template slot-scope="{ row }">
            <div>
              <el-popover
                class="item"
                effect="light"
                placement="bottom-start"
                v-if="
                  row.loanManageRepaymentVOList &&
                  row.loanManageRepaymentVOList.length != 0
                "
              >
                <div class="props-container">
                  <el-table
                    :data="row.loanManageRepaymentVOList"
                    show-summary
                    border
                    :summary-method="getSummaries"
                  >
                    <el-table-column
                      v-for="(item, index) in repaymentTableItemColumn"
                      :key="index"
                      :fixed="item.fixed"
                      :prop="item.prop"
                      :label="item.label"
                      :width="item.width"
                      :min-width="item.minWidth"
                      :formatter="item.formatter"
                      :align="item.align"
                    >
                    </el-table-column>
                  </el-table>
                </div>
                <span
                  slot="reference"
                  style="color: #007fff; cursor: pointer"
                  >{{ formatMoney(row.actualAmount) }}</span
                >
              </el-popover>
              <div v-else>{{ formatMoney(row.actualAmount) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="unPayAmount"
          label="待还总额(元)"
          :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
          min-width="120"
          align="align"
        >
        </el-table-column>
        <el-table-column
          prop="principal"
          label="本金(元)"
          :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
          min-width="120"
          align="align"
        >
        </el-table-column>
        <el-table-column
          prop="interest"
          label="当前利息(元)"
          :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
          min-width="120"
          align="align"
        >
        </el-table-column>
        <el-table-column
          prop="penaltyInterest"
          label="逾期罚息(元)"
          :formatter="
            (row, column, cellvalue) => formatMoney(cellvalue || '0.00')
          "
          min-width="120"
          align="align"
        >
        </el-table-column>
        <!-- <el-table-column
          prop="serviceFee"
          label="服务费(元)"
          :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
          min-width="120"
        >
        </el-table-column> -->
        <!-- <el-table-column
          v-for="item in repaymentTable[0].repaymentPlanFeeVOList"
          :key="item.id"
          :prop="`amount${item.expenseTypeId}`"
          :label="item.feeName + '(元)'"
          :formatter="(row, column, value) => cellvalue || '0'"
          min-width="120"
        >
        </el-table-column> -->
        <el-table-column
          prop="overdueDay"
          label="逾期天数"
          :formatter="(row, column, cellvalue) => cellvalue || '0'"
          min-width="120"
          align="align"
        >
        </el-table-column>
        <el-table-column label="还款状态" min-width="120">
          <template slot-scope="{ row }">
            <div>
              <span style="color: #1fc374" v-if="row.repaymentStatus == 2"
                >已结清</span
              >
              <span v-else>使用中</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="overdue"
          label="状态"
          :formatter="
            (row, column, cellvalue) => {
              switch (cellvalue) {
                case 1:
                  return '正常'
                case 2:
                  return '逾期'
                default:
                  return ''
              }
            }
          "
          min-width="120"
        >
        </el-table-column>
      </el-table>
    </CollapseCard>
  </div>
</template>

<script>
import { repaymentDetails } from '@/api/loan/loanmanageiouDetail.js'
import CollapseCard from '@/views/loan/collapseCard'
import { formatMoney } from '@/util/filter'

export default {
  components: { CollapseCard },
  props: {
    fNo: { required: true, type: 'string' },
  },
  data() {
    return {
      repaymentTable: [], // 已还明细
      repaymentTableItemColumn: [
        {
          label: '还款日期',
          prop: 'repaymentTime',
          width: 110,
        },
        {
          label: '还款单号',
          prop: 'repaymentNo',
          width: 150,
        },
        {
          label: '实还总额（元）',
          prop: 'actualTotalAmount',
          align: 'right',
          width: 130,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '实还本金（元）',
          prop: 'actualPrincipal',
          width: 130,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '实还利息（元）',
          prop: 'actualInterest',
          align: 'right',
          width: 130,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '实还逾期罚息（元）',
          align: 'right',
          prop: 'actualPenaltyInterest',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue || '0.00')
          },
          width: 150,
        },
        {
          label: '实还手续费（元）',
          align: 'right',
          prop: 'actualServiceCharge',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue || '0.00')
          },
          width: 150,
        },
      ],
      // dongtaifeiyongDataListT: [],
      // dongtaihebingjisuanArr: [],
      linshicunchudongtaifeiy: {},
    }
  },
  watch: {
    fNo: {
      handler(newV) {
        if (!newV) return
        this.repaymentDetailsFun(newV)
      },
      immediate: true,
    },
  },
  methods: {
    formatMoney,
    repaymentDetailsFun(fNo) {
      repaymentDetails(fNo).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          // const dongtaifeiyongDataObj = {}
          const linshicunchudongtaifeiy = {}
          for (const item of resData) {
            // const dongtaifeiyongList = item.repaymentPlanFeeVOList
            // if (dongtaifeiyongList && dongtaifeiyongList.length) {
            //   for (const cItem of dongtaifeiyongList) {
            //     // 处理动态列
            //     const eId = cItem.expenseTypeId
            //     if (!dongtaifeiyongDataObj[eId]) {
            //         dongtaifeiyongDataObj[eId] = {
            //         id: cItem.id,
            //         label: cItem.feeName + '(元)',
            //         prop: eId,
            //       }
            //       // 记录动态列用于合并计算
            //       this.dongtaihebingjisuanArr.push(eId)
            //     }
            //     // 处理动态数据
            //     item[eId] = cItem.amount
            //   }
            // }
            item.loanManageRepaymentVOList.forEach(citem => {
              citem.repaymentFeeList.forEach(ditem => {
                const expenseTypeIdKey = ditem.expenseTypeId
                // 处理动态列
                if (!linshicunchudongtaifeiy[expenseTypeIdKey]) {
                  linshicunchudongtaifeiy[expenseTypeIdKey] = {
                    label: ditem.feeTypeName + ' (元)',
                    prop: `amount${expenseTypeIdKey}`,
                    formatter: (row, column, cellValue) => {
                      return formatMoney(cellValue || '0.00')
                    },
                    width: 150,
                  }
                }
                // 处理动态数据
                citem[`amount${expenseTypeIdKey}`] = ditem.actualAmount
              })
            })
          }
          // psuh动态费用
          for (const key in linshicunchudongtaifeiy) {
            this.repaymentTableItemColumn.push(linshicunchudongtaifeiy[key])
          }
          this.linshicunchudongtaifeiy = linshicunchudongtaifeiy
          this.repaymentTableItemColumn.push({
            label: '还款类型',
            prop: 'repaymentType',
            formatter: (row, column, cellValue) => {
              return (
                (this.loanManageRepaymentType &&
                  this.loanManageRepaymentType[cellValue]) ||
                ''
              )
            },
            width: 90,
          })
          // this.dongtaifeiyongDataListT = dongtaifeiyongDataObj
          this.repaymentTable = resData
        }
      })
    },
    getSummaries(param) {
      const totalFields = [
        'shouldPrincipal',
        'shouldInterest',
        'shouldPunishInterest',
        'surplusPrincipal',
        'surplusInterest',
        'surplusPunishInterest',
        'amount',
        // 还款计划-repaymentplanColumn
        'totalAmount',
        'principal',
        'interest',
        'penaltyInterest',
        // 已还明细
        'totalAmount',
        'principal',
        'interest',
        'serviceCharge',
        'penaltyInterest',
        // 还款明细
        'actualServiceCharge',
        'actualPenaltyInterest',
        'actualInterest',
        'actualPrincipal',
        'actualTotalAmount',
        // 'serviceFee',
        'actualAmount',
        'unPayAmount',
      ]

      // if (this.dongtaihebingjisuanArr.length) {
      //   for (const item of this.dongtaihebingjisuanArr) {
      //     totalFields.push(item)
      //   }
      // }
      for (const key in this.linshicunchudongtaifeiy) {
        totalFields.push(`amount${key}`)
      }

      // 非金额的字段(不需要格式化)
      const notFormat = ['overdueDay']

      // if (totalFields.indexOf(param.property)) return ''
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <strong>总计：</strong>
          return
        }
        const { property } = column
        if (totalFields.indexOf(property) === -1) return ''
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          if (notFormat.indexOf(property) === -1) {
            const val = sums[index].toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })
            sums[index] = <strong>{val}</strong>
          }
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
  },
}
</script>

<style lang="scss" scoped>
.invoice-details-container-my {
  margin-top: 10px;

  ::v-deep {
    .fromHeader {
      margin-left: 0;
    }
  }
}

::v-deep {
  .el-table__fixed-header-wrapper,
  .el-table__header-wrapper {
    .el-table__header th.el-table__cell {
      background-color: #fff1f1;
      color: #333333;
    }
  }

  .el-table__body tr.hover-row {
    &.table-row-overdue > td.el-table__cell {
      background-color: #ffc000;
      border-color: rgba(255, 255, 255, 0.2);
    }
  }

  .table-row-overdue,
  .table-row-overdue:hover {
    background-color: #ffc000;
    border-color: rgba(255, 255, 255, 0.2);
  }
}
</style>
