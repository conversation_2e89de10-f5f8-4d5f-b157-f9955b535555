<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '产品组-路由申请'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品组 -->
      <!--  <basic-container v-if="modeReadable.applyProduct">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">申请产品组</h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <!~~ <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div> ~~>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods">{{
                      processGoodsObj.goodsName
                    }}</span>
                    <span></span>
                    <!~~ <span>{{ processGoodsObj.capitalName }}</span> ~~>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype"
                >产品组</span
              >
            </div>
            <div class="descriptions-for-box">
              <el-descriptions
                title=""
                :column="tableData.length < 3 ? 2 : 3"
                border
              >
                <el-descriptions-item
                  v-for="item in tableData"
                  :key="item.id"
                  :label="item.label"
                  >{{ item.value }}</el-descriptions-item
                >
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>-->

      <!-- 补充资料文本 -->
      <div style="margin-top: 10px" v-if="modeReadable.furtherInformationText">
        <basic-container
          v-for="(item, index) in customerMaterialList"
          :key="index"
        >
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item :name="item.orderNum">
              <!-- title的solt -->
              <template slot="title">
                <div class="fromHeader">
                  <div class="fromLeft">
                    <i
                      class="el-icon-caret-bottom"
                      :class="{
                        'i-active':
                          item.orderNum ==
                          activeNames.filter(
                            filterItem => filterItem == item.orderNum
                          )[0],
                      }"
                    ></i>
                    <h1 class="fromLeft-title">{{ item.templateName }}</h1>
                  </div>
                </div>
              </template>
              <!-- 展示表单 -->
              <div class="descriptions-for-box">
                <el-descriptions title="" :column="3" border>
                  <el-descriptions-item
                    v-for="(itemed, indexed) in item.creditFromFields"
                    :key="indexed"
                    :label="itemed.fieldDesc"
                  >
                    <span v-if="![4, 5, 6].includes(itemed.dataType)">
                      {{ itemed.value }}
                    </span>
                    <span v-else-if="[5, 6].includes(itemed.dataType)">
                      {{ itemed.value }}
                      <template v-if="itemed.value">
                        {{ itemed.dataType === 5 ? '元' : '万元' }}
                      </template>
                    </span>
                    <span v-else>
                      {{ itemed.timeValue }}
                    </span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-collapse-item>
          </el-collapse>
        </basic-container>
      </div>

      <!-- 补充资料附件 -->
      <basic-container v-if="modeReadable.furtherInformationAccessory">
        <el-collapse v-model="activeNames3" @change="handleChange3">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change3Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">补充资料</h1>
                </div>
              </div>
            </template>
            <!-- 展示表单 -->
            <FilePreviewHWP :formUpload="customerMaterialFormUpload" />
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 产品选择 -->
      <basic-container v-if="modeReadable.chanpinxuanzhelanmu">
        <el-collapse v-model="activeNames4" @change="handleChange4">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change4Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">产品选择</h1>
                </div>
              </div>
            </template>
            <!-- 产品选择列表组件 -->
            <ProductsSelectionC
              :islook="true"
              :pArrInTo="pArrInTo"
              :huixianIdInTo="huixianIdInTo"
              @rrSelectCallFun="rrSelectCallFun"
            />
          </el-collapse-item>
        </el-collapse>
      </basic-container>
    </template>
  </div>
</template>

<script>
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import FilePreviewHWP from './component/preview-documents/index.vue'
import ProductsSelectionC from './component/products-selection-c.vue'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'

export default {
  mixins: [customExForm],
  components: { WfButton, WfFlow, FilePreviewHWP, ProductsSelectionC },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      processGoodsObj: {},
      tableData: [],
      customerMaterialList: [],
      customerMaterialFormUpload: [],
      activeNames: [1],
      changeType: true,
      activeNames3: ['furtherInformation'],
      change3Type: true,
      activeNames4: ['furtherInformation'],
      change4Type: true,
      pArrInTo: [],
      nowGoodKey: '',
      huixianIdInTo: '',
      // 一级大模块显影控制
      modeReadable: {
        applyProduct: true,
        furtherInformationText: true,
        furtherInformationAccessory: true,
        chanpinxuanzhelanmu: true,
      },
    }
  },
  methods: {
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        const { variables } = data
        // const { taskForm } = res.form
        this.resData = data
        this.variables = variables || {}
        this.variables.processInstanceId = res.process.processInstanceId
        const { processGoodsInfo } = variables
        this.processGoodsObj = processGoodsInfo

        // 大模块显影控制
        // const taskArrkey1 = [
        //   'applyProduct',
        //   'furtherInformationText',
        //   'furtherInformationAccessory',
        // ]
        // const taskFormFilter1 = taskForm.filter(
        //   item => taskArrkey1.includes(item.id) && item.readable
        // )
        // for (const item of taskFormFilter1) {
        //   this.modeReadable[item.id] = true
        // }

        // 流程产品信息
        // let loadTer = null
        // getDictionary('goods_load_term_unit').then(res => {
        //   const resData = res.data
        //   if (resData.code == 200) {
        //     // 处理字典数据
        //     const resList = []
        //     for (const item of resData.data) {
        //       resList.push({
        //         key: item.dictKey,
        //         value: item.dictValue,
        //         id: item.id,
        //       })
        //     }
        //     loadTer = resList.filter(
        //       // 过滤出当前的最长期限单位
        //       item => item.key == processGoodsInfo.loadTermUnit
        //     )
        //     // 处理字典数据
        //     const resList2 = []
        //     for (const item of resData.data) {
        //       resList2.push({
        //         key: item.dictKey,
        //         value: item.dictValue,
        //         id: item.id,
        //       })
        //     }

        //     const data = [
        //       {
        //         id: 1,
        //         label: '最高可借',
        //         value: `${processGoodsInfo.loanAmountEnd}万元`,
        //         key: 'applyProduct_loanAmountEnd',
        //       },
        //       {
        //         id: 2,
        //         label: '年利率低至',
        //         value: `${processGoodsInfo.annualInterestRateStart}%`,
        //         key: 'applyProduct_annualInterestRate',
        //       },
        //       {
        //         id: 3,
        //         label: '最长期限',
        //         value: `${processGoodsInfo.loadTermEnd}${loadTer[0].value}`,
        //         key: 'applyProduct_loadTermEnd',
        //       },
        //     ]
        //     this.tableData = data

        // 是否可读
        // const dataKey = data.map(item => item.key)
        // const taskFormFilter = taskForm.filter(item =>
        //   dataKey.includes(item.id)
        // )
        // const taskFormId = taskFormFilter.map(item => {
        //   if (item.readable) {
        //     return item.id
        //   }
        // })
        // const dataFilter = data.filter(item =>
        //   taskFormId.includes(item.key)
        // )
        // this.tableData = dataFilter
        //   }
        // })

        // 补充资料
        const { customerMaterial } = variables || {}
        this.customerMaterialList = JSON.parse(customerMaterial.creditForm)
        this.activeNames = this.customerMaterialList.map(item => item.orderNum)
        this.customerMaterialFormUpload = JSON.parse(
          customerMaterial.supplementMaterial
        ).map(item => {
          item.uploadArr = item.uploadArr
            .map(itemed => {
              if (itemed.url) {
                const file = itemed.url.split('/')
                itemed.fileType = file[file.length - 1].split('.')[1]
                return itemed
              }
            })
            .filter(Boolean)
          return item
        })

        // 产品选择列表数据
        if (variables.SELECT_GOODS_ID) {
          this.huixianIdInTo = variables.SELECT_GOODS_ID
          this.nowGoodKey = variables.SELECT_GOODS_ID
        }
        getDictionary('goods_load_term_unit').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            getDictionary('goods_billing_method').then(res => {
              const resData = res.data
              if (resData.code == 200) {
                // 处理字典数据
                const resList2 = []
                for (const item of resData.data) {
                  resList2.push({
                    key: item.dictKey,
                    value: item.dictValue,
                    id: item.id,
                  })
                }

                for (const item of processGoodsInfo) {
                  let loadTer,
                    filterTabBar = []
                  loadTer = resList.filter(
                    // 过滤出当前的最长期限单位
                    itemF => itemF.key == item.loadTermUnit
                  )

                  if (item.repaymentType === 1) {
                    if (item.billingMethod) {
                      item.billingMethod.split(',').forEach((itemFo, index) => {
                        // 过滤出当前的计费方式
                        filterTabBar[index] = resList2.filter(
                          itemed => itemed.key == itemFo
                        )[0].value
                      })
                    }
                  } else {
                    filterTabBar = ['随借随还']
                  }
                  const linshiArr = [
                    {
                      label: '最高可借',
                      value: `${item.loanAmountEnd}万元`,
                    },
                    {
                      label: '年利率低至',
                      value: `${item.annualInterestRateStart}%`,
                    },
                    {
                      label: '最长期限',
                      value: `${item.loadTermEnd}${loadTer[0].value}`,
                    },
                    {
                      label: '计费方式',
                      value: filterTabBar.toString(),
                    },
                  ]
                  this.pArrInTo.push({
                    id: item.id,
                    pName: item.goodsName,
                    cName: item.capitalName,
                    imgurl: item.capitalLogo,
                    dTableData: linshiArr,
                  })
                }
              }
            })
          }
        })
      })
    },
    // 通过前校验
    forValidata() {
      if (!this.nowGoodKey) {
        this.$message.warning('请选择产品,在进行提交')
        return
      }
      this.validataFunction(true, { SELECT_GOODS_ID: this.nowGoodKey })
    },
    // 通过
    handleExamine(pass) {
      if (pass) {
        this.forValidata()
      } else {
        this.validataFunction(pass)
      }
    },
    // 通过后调取接口函数
    validataFunction(pass, objInTo) {
      this.submitLoading = true
      this.handleCompleteTask(pass, objInTo)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 企业信息-财务信息折叠面板收缩控制
    handleChange() {
      this.changeType = !this.changeType
    },
    // 补充资料折叠面板收缩控制
    handleChange3() {
      this.change3Type = !this.change3Type
    },
    // 产品选择折叠面板收缩控制
    handleChange4() {
      this.change4Type = !this.change4Type
    },
    viewGoods() {
      this.$router.push({
        path: '/pcontrol/productgroupdetails',
        query: { id: this.processGoodsObj.id },
      })
      sessionStorage.setItem('look', 'true')
    },
    rrSelectCallFun(val) {
      this.nowGoodKey = val
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.creditLimitFinancing {
  margin-bottom: 40px;
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    font-family: SourceHanSansSC-regular;
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        // width: 107px;
        // height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
        padding: 2px 10px;
        box-sizing: border-box;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改外部组件默认样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
.bord-radius-left {
  border-radius: 20px 0 0 20px !important;
  border-left-color: #b3d8ff !important;
}
.bord-radius-right {
  border-radius: 0 20px 20px 0 !important;
  border-right-color: #b3d8ff !important;
}
// 覆盖组件库样式
::v-deep {
  .el-tabs--card {
    .el-tabs__header {
      border-bottom: none;
    }
    .el-tabs__item {
      border: none;
      padding: 0;
    }
    .el-tabs__item:nth-child(2) {
      padding-left: 0 !important;
    }
    .is-plain {
      background: #fff;
      border-color: #b3d8ff;
    }
    .is-plain:hover {
      color: #409eff;
      background: #ecf5ff;
      border-color: #b3d8ff;
    }
  }
  .avue-form__menu {
    display: none;
  }
  .el-input.is-disabled .el-input__inner {
    color: #000;
  }
  .el-textarea.is-disabled .el-textarea__inner {
    color: #000;
  }
  span {
    display: inline-block;
  }
}
</style>
