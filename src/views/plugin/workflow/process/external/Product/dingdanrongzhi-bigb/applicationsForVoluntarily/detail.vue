<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '订单融资-融资申请自动放款'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{ variables.applyUserName }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <basic-container>
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">
              <span>融资需求</span>
              <span class="long-string" />
              <div class="serial-number">
                <span>融资编号：</span>
                <span
                  @click="
                    handleLinkOrderOrProductPage(
                      { id: financeApply.id },
                      '/finance/orderFinancingReceivable/orderFinancingRecDetail'
                    )
                  "
                  >{{ financeNo }}</span
                >
              </div>
            </h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{ processGoodsObj.goodsName }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">订单融资</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item v-for="item in tableData1" :key="item.id" :label="item.label">{{
                  item.value
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 质押品 -->
      <basic-container v-if="resData.taskName != '融资放款'">
        <el-collapse v-model="activeNames1" @change="handleChange1">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change1Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">质押品</h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <el-table
                ref="table1"
                :data="tableData2"
                :summary-method="getSummaries"
                show-summary
                style="width: 100%; margin-top: 20px"
                class="table-border-style"
              >
                <el-table-column
                  prop="index"
                  label="#"
                  width="70"
                  align="center"
                  v-if="pledgedGoodsList.pledgedGoods_serialNumber"
                >
                </el-table-column>
                <el-table-column
                  prop="contractNo"
                  label="唯一编号"
                  v-if="pledgedGoodsList.pledgedGoods_uniqueIdentification"
                >
                  <template slot-scope="scope">
                    <span class="contract-no">{{ scope.row.contractNo }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="contractNo"
                  label="订单编号"
                  v-if="pledgedGoodsList.pledgedGoods_uniqueIdentification"
                >
                  <template slot-scope="scope">
                    <span class="contract-no">{{ scope.row.proofNo }}</span>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  prop="enterpriseName"
                  label="贸易背景"
                  v-if="pledgedGoodsList.pledgedGoods_tradeBackground"
                >
                </el-table-column> -->
                <el-table-column
                  v-if="resData.status != 'finished' && pledgedGoodsList.pledgedGoods_theCurrentValue"
                  prop="currentAmount"
                  label="当前价值（元）"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.currentAmount | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="resData.status != 'finished' && pledgedGoodsList.pledgedGoods_valueOfUse"
                  prop="amount"
                  label="本次使用价值（元）"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.amount | formatMoney }} </span>
                  </template>
                </el-table-column>
                <template slot="header" slot-scope="scope">
                  {{ scope }}
                </template>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 还款试算 -->
      <!--<basic-container
        v-if="
          processGoodsObj &&
          processGoodsObj.chargeMethod === 1 &&
          resData.taskName != '融资放款'
        "
      >
        <el-collapse v-model="activeNames2" @change="handleChange2">
          <el-collapse-item name="furtherInformation">
            <!~~ title的solt ~~>
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change2Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>还款试算</span>
                    <!~~ <span class="long-string" />
                    <span class="interest-rate"
                      >日利率{{ allDailyInterestRate }}%（年化利率{{
                        allAnnualInterestRate
                      }}%）</span
                    > ~~>
                  </h1>
                </div>
              </div>
            </template>
            <div
              class="table-top refund"
              v-if="bankInterestList.reimbursementTrial_bankInterest"
            >
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>资方费用</span>
                  <span />
                  <span
                    >日利率{{ dailyInterestRate }}%（年化利率{{
                      annualInterestRate
                    }}%）</span
                  >
                </div>
                <div class="title-right-box">计费方式：{{ chargeMode }}</div>
              </div>
              <el-table
                ref="table2"
                :data="tableData3"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  prop="term"
                  label="期数"
                  width="110"
                  align="center"
                  v-if="
                    bankInterestList.bankInterest_periods &&
                    processGoodsObj &&
                    processGoodsObj.repaymentType === 1
                  "
                >
                </el-table-column>
                <el-table-column
                  prop="refundTime"
                  label="还款日期"
                  v-if="bankInterestList.bankInterest_repaymentDate"
                >
                </el-table-column>
                <el-table-column
                  prop="monthlySupply"
                  label="应还总额"
                  v-if="bankInterestList.bankInterest_totalShouldAlso"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="monthlyPrincipal"
                  label="还款本金"
                  v-if="bankInterestList.bankInterest_repaymentPrincipal"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.monthlyPrincipal | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="planInterest"
                  label="应还利息"
                  v-if="bankInterestList.bankInterest_shouldAlsoInterest"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.planInterest | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-for="(item, index) in feeData.showRepaymentPlan
                    .stagRecords[0].planFeeList"
                  :key="item.id"
                  :prop="`amount${index}`"
                  :label="item.feeName"
                >
                </el-table-column>
              </el-table>
            </div>

            <div class="table-top refund" v-if="platformFeeList.length">
              <div class="table-title-box" style="margin-top: -10px">
                <div class="title-left-box">
                  <span>平台费用</span>
                </div>
              </div>
              <el-table
                ref="table3"
                :data="tableData4"
                :summary-method="getSummaries"
                show-summary
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
              >
                <el-table-column
                  prop="name"
                  label="费用名称"
                  v-if="costPlatformList.payAFee_costOfName"
                >
                </el-table-column>
                <el-table-column
                  prop="expenseTypeStr"
                  label="费用类型"
                  v-if="costPlatformList.payAFee_typeOfExpense"
                >
                  <template slot-scope="scope">
                    <span class="border-box">
                      {{ scope.row.expenseTypeStr }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="chargePoint"
                  label="支付节点"
                  v-if="costPlatformList.payAFee_payTheNode"
                >
                  <template slot-scope="scope">
                    <span class="border-box">
                      {{ scope.row.chargePoint }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="calculationStr"
                  label="计费方式"
                  v-if="costPlatformList.payAFee_chargeMode"
                >
                </el-table-column>
                <el-table-column
                  prop="amount"
                  label="应付金额"
                  v-if="costPlatformList.payAFee_amountPayable"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.amount | formatMoney }} </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="chain-line" />

            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥{{ sum | formatMoney }} </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>-->

      <!-- 还款计划 -->
      <basic-container v-if="processGoodsObj && resData.taskName != '融资放款'">
        <el-collapse v-model="activeNames4" @change="handleChange4">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change4Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>还款试算</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="table-top refund">
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>资方费用</span>
                  <span />
                  <span>日利率{{ dailyInterestRate }}%（年化利率{{ annualInterestRate }}%）</span>
                </div>
                <div class="title-right-box">计费方式：{{ chargeMode }}</div>
              </div>
              <el-table
                ref="table5"
                :data="tableData3"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  prop="term"
                  label="期数"
                  width="110"
                  align="center"
                  v-if="bankInterestList.bankInterest_periods && processGoodsObj && processGoodsObj.repaymentType === 1"
                >
                </el-table-column>
                <el-table-column prop="refundTime" label="还款日期" v-if="bankInterestList.bankInterest_repaymentDate">
                </el-table-column>
                <el-table-column
                  prop="monthlySupply"
                  label="应还总额"
                  v-if="bankInterestList.bankInterest_totalShouldAlso"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="monthlyPrincipal"
                  label="还款本金"
                  v-if="bankInterestList.bankInterest_repaymentPrincipal"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlyPrincipal | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="planInterest"
                  label="应还利息"
                  v-if="bankInterestList.bankInterest_shouldAlsoInterest"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.planInterest | formatMoney }} </span>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  v-for="(item, index) in feeData.showRepaymentPlan
                    .stagRecords[0].planFeeList"
                  :key="item.id"
                  :prop="`amount${index}`"
                  :label="item.feeName"
                >
                </el-table-column> -->
              </el-table>
            </div>

            <div v-if="platformFeeList.length">
              <div class="table-top refund" v-for="item in platformFeeList" :key="item.id">
                <div class="chain-line" />
                <div class="table-title-box" style="margin-top: -10px">
                  <div class="title-left-box">
                    <span>{{ item.parenExpenseName }}</span>
                  </div>
                </div>
                <el-table
                  ref="table3"
                  :data="item.expenseOrderDetailList"
                  :summary-method="getSummaries"
                  show-summary
                  style="width: 100%; margin-top: 13px"
                  class="table-border-style"
                >
                  <el-table-column prop="expenseTypeStr" label="费用名称" v-if="costPlatformList.payAFee_costOfName">
                  </el-table-column>
                  <!-- <el-table-column
                    prop="feeNameType"
                    label="费用类型"
                    v-if="costPlatformList.payAFee_typeOfExpense"
                  >
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNameType }}
                      </span>
                    </template>
                  </el-table-column> -->
                  <el-table-column prop="repaymentTerm" label="期数"> </el-table-column>
                  <el-table-column prop="feeNodeStr" label="计算节点" v-if="costPlatformList.payAFee_payTheNode">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="collectFeesNodeStr"
                    label="收费节点"
                    v-if="costPlatformList.payAFee_payTheNode"
                  >
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.collectFeesNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeFormulaName" label="计费方式" v-if="costPlatformList.payAFee_chargeMode">
                  </el-table-column>
                  <el-table-column prop="amount" label="应付金额" v-if="costPlatformList.payAFee_amountPayable">
                    <template slot-scope="scope">
                      <span>￥{{ scope.row.amount | formatMoney }} </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥{{ sum | formatMoney }} </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 合同签署 -->
      <basic-container v-if="resData.taskName != '融资放款'">
        <el-collapse v-model="activeNames3" @change="handleChange3">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change3Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>合同签署</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="boxs">
              <div class="boxs-to-apply-for-product">
                <!-- <h1 class="boxs-to-apply-for-product-h1">合同签署</h1> -->
                <div class="boxs-to-apply-for-product-body">
                  <el-table
                    ref="table4"
                    :data="tableData5"
                    :max-height="240"
                    style="width: 100%; border: 1px solid #ebeef5"
                  >
                    <el-table-column
                      v-if="tableData5Readable.contractSigning_serialNumber"
                      prop="serial"
                      label="序号"
                      width="80"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractSigning_contractNumber"
                      prop="contractId"
                      label="合同编号"
                      width="300"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractSigning_contractTitle"
                      prop="contractTitle"
                      label="合同标题"
                      width="300"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractSigning_creationTime"
                      prop="createTime"
                      label="创建时间"
                      width="250"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractSigning_signTheState"
                      prop="statusText"
                      label="签署状态"
                      min-width="150"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-tag type="success">{{ scope.row.statusText }}</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractSigning_operation"
                      prop="firstTradeTime"
                      label="操作"
                      min-width="100"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <div style="font-weight: unset">
                          <span class="view" @click="viewContract(scope)">详情</span>
                          <span class="down" @click="downContract(scope)">下载</span>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 缴纳费用 -->
      <basic-container v-if="processGoodsObj && processGoodsObj.chargeMethod === 2 && resData.taskName != '融资放款'">
        <el-collapse v-model="activeNames5" @change="handleChange5">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change5Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>缴纳费用</span>
                  </h1>
                </div>
              </div>
            </template>
            <Feiyongwaihezi ref="feiyongwaiheziRef" :feiyongList="expenseInfoExpenseList" :isLook="true" />
            <!-- <div class="table-top refund">
              <el-table
                ref="table6"
                :data="tableData4"
                :summary-method="getSummaries"
                show-summary
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
              >
                <el-table-column
                  prop="name"
                  label="费用名称"
                  v-if="costPlatformList.payAFee_costOfName"
                >
                </el-table-column>
                <el-table-column
                  prop="feeNameType"
                  label="费用类型"
                  v-if="costPlatformList.payAFee_typeOfExpense"
                >
                  <template slot-scope="scope">
                    <span class="border-box">
                      {{ scope.row.feeNameType }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="chargePoint"
                  label="支付节点"
                  v-if="costPlatformList.payAFee_payTheNode"
                >
                  <template slot-scope="scope">
                    <span class="border-box">
                      {{ scope.row.chargePoint }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="calculationStr"
                  label="计费方式"
                  v-if="costPlatformList.payAFee_chargeMode"
                >
                </el-table-column>
                <el-table-column
                  prop="amount"
                  label="应付金额"
                  v-if="costPlatformList.payAFee_amountPayable"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.amount | formatMoney }} </span>
                    <!~~ <div v-else style="width: 80%">
                      <el-input
                        placeholder="请输入金额"
                        v-model="scope.row.amount"
                        type="number"
                        :disabled="
                          !costPlatformList.costPlatform_amountPayable_w
                        "
                      >
                        <template slot="append">元</template>
                      </el-input>
                    </div> ~~>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="pay-box" v-if="isOnlyLife === 1">
              <div style="width: 100%">
                <pay-form
                  ref="form"
                  see
                  :platformImg="imageArr"
                  :platformPdf="pdfArr"
                  :purchaseBondArr="tableData4"
                  :platformFeeObj="platformFeeObj"
                ></pay-form>
              </div>

              <!~~ 订单状态 ~~>
              <!~~ <div
                v-if="costPlatformList.payAFee_orderStatus"
                class="order-box"
              >
                <span class="order-status">订单状态</span>
                <span
                  class="order-text"
                  :style="{
                    color: orderTextRoot.color,
                    'background-color': orderTextRoot.bgColor,
                  }"
                >
                  {{ orderTextRoot.text }}
                </span>
                <span
                  v-if="
                    !this.statusForm.paymentStatus &&
                    costPlatformList.payAFee_orderStatusRedact
                  "
                  class="order-modification-btn"
                  @click="openDialog"
                >
                  修改订单状态
                </span>

                <template v-else>
                  <span
                    v-if="costPlatformList.payAFee_orderStatusLook"
                    class="order-btn"
                    @click="openDialog('look')"
                  >
                    查看
                  </span>
                  <span
                    v-if="costPlatformList.payAFee_orderStatusRedact"
                    class="order-btn"
                    @click="openDialog"
                    >编辑</span
                  >
                </template>
              </div>

              <div
                class="payOrder-box"
                v-if="costPlatformList.payAFee_checkPaymentVoucher"
              >
                <div
                  :class="{ 'payOrder-left-box': pdfArr.length }"
                  v-if="imageArr.length"
                >
                  <span @click="viewVoucher('img')">查看支付凭证图片</span>
                </div>
                <div v-if="pdfArr.length">
                  <span v-if="pdfArr.length < 2" @click="viewVoucher(pdfArr[0])"
                    >查看支付凭证文件</span
                  >
                  <span
                    v-else
                    v-for="(item, index) in pdfArr"
                    :key="index"
                    @click="viewVoucher(item)"
                  >
                    查看支付凭证文件{{ index + 1 }}
                  </span>
                </div>
              </div> ~~>
            </div>
            <div class="pay-box" v-else>
              <!~~ 订单状态 ~~>
              <div
                v-if="costPlatformList.payAFee_orderStatus"
                class="order-box"
              >
                <span class="order-status">订单状态</span>
                <span
                  class="order-text"
                  style="color: #09c067; background-color: #ddf0e7"
                >
                  已付款
                </span>

                <div v-if="variables.billExpenseOrder" class="informition-box">
                  <span>
                    支付方式：
                    <span class="informition-box_bold">
                      {{ variables.billExpenseOrder.paymentMethod }}
                    </span>
                  </span>
                  <span>
                    付款金额：
                    <span class="informition-box_bold">
                      {{ variables.billExpenseOrder.amount }}
                    </span>
                  </span>
                  <span>
                    支付流水号：
                    <span class="informition-box_bold">
                      {{ variables.billExpenseOrder.billPaySerialNo }}
                    </span>
                  </span>
                  <span>
                    支付时间：
                    <span class="informition-box_bold">
                      {{ variables.billExpenseOrder.payTime }}
                    </span>
                  </span>
                </div>
              </div>
            </div>-->
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <basic-container v-if="resData.taskName == '融资放款'">
        <avue-form ref="bankInformation" :option="option" v-model="information"> </avue-form>
      </basic-container>

      <!-- 委托支付收款账户信息 -->
      <basic-container v-if="resData.taskName == '融资放款'">
        <el-collapse v-model="activeNames6" @change="handleChange6">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change6Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>委托支付收款账户信息</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="boxs">
              <div class="boxs-to-apply-for-product">
                <!-- <h1 class="boxs-to-apply-for-product-h1">合同签署</h1> -->
                <div class="boxs-to-apply-for-product-body">
                  <div class="descriptions-for-box">
                    <el-descriptions title="" :column="3" border>
                      <el-descriptions-item v-for="item in tableData7" :key="item.id" :label="item.label">{{
                        item.value
                      }}</el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>
    </template>

    <!-- 修改状态弹窗 -->
    <el-dialog title="修改状态" append-to-body width="35%" :visible.sync="statusBox1" class="avue-monry-form-box">
      <avue-form ref="AvueMoneyForm" v-model="statusForm" :option="statusOption">
        <template slot-scope="{ value }" slot="moneyBox">
          <div class="money-box" v-if="value">
            <div class="chlidern-money">
              <span class="label-box">仓储费(元)：</span>
              <span class="value-box"> ￥{{ value.storageCharge | formatMoney }} </span>
            </div>
            <div class="chlidern-money">
              <span class="label-box">物流费(元)：</span>
              <span class="value-box"> ￥{{ value.logisticsCost | formatMoney }} </span>
            </div>
            <div class="chlidern-money">
              <span class="label-box">服务费(元)：</span>
              <span class="value-box"> ￥{{ value.serviceCharge | formatMoney }} </span>
            </div>
            <div class="chlidern-money">
              <span class="label-box">应还总额(元)：</span>
              <span class="value-box" style="color: red"> ￥{{ value.totalPrices | formatMoney }} </span>
            </div>
          </div>
        </template>
        <!-- 查看支付凭证 -->
        <template slot="voucherUrl">
          <div class="look-btn-menu">
            <span v-if="imageArr.length" @click="viewVoucher('img')"> 查看图片 </span>
            <template v-if="pdfArr.length === 1">
              <span @click="viewVoucher(pdfArr[0])"> 查看附件 </span>
            </template>
            <template v-else>
              <span v-for="(item, index) in pdfArr" :key="index" @click="viewVoucher(item)">
                查看附件{{ index + 1 }}
              </span>
            </template>
          </div>
        </template>
      </avue-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="statusBox1 = false">取 消</el-button>
        <el-button v-show="!dialogLook" type="primary" @click="submit()"> 确 定 </el-button>
      </span>
    </el-dialog>

    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import goLinkPage from '@/views/plugin/workflow/mixins/goLinkPage'
import FilePreview from '@/components/file-preview'
import Feiyongwaihezi from '@/components/feiyongwaihezi'
import { formatMoney } from '@/util/filter.js'
import {
  getDictionary,
  OrderFinancingSaleContractList,
  orderFinancingRepaymentCalculationDetailById,
  // platformExpensesList,
  getByContractId,
  skipToPreview,
  contractDownload,
  getByGoodsIdAndUserId,
  entrustingPaymentAndCollectionAccountInformation,
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import { ids } from '@/api/resource/attach.js'
import { getDeByBankCard, completeTask, queryProductTransferFee } from '@/api/openAccount/index'
import md5 from 'js-md5'
import payForm from './payForm'
import { goodsTypeToPath } from '../../globalFun.js'
export default {
  mixins: [customExForm, goLinkPage],
  components: {
    WfButton,
    WfFlow,
    FilePreview,
    payForm,
    Feiyongwaihezi,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          this.processInsIdRoot = processInsId
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNames1() {
      setTimeout(() => {
        this.$refs.table1.$ready = false
      }, 50)
    },
    activeNames2() {
      setTimeout(() => {
        this.$refs.table2.$ready = false
        this.$refs.table3.$ready = false
      }, 50)
    },
    activeNames3() {
      setTimeout(() => {
        this.$refs.table4.$ready = false
      }, 50)
    },
    activeNames4() {
      setTimeout(() => {
        this.$refs.table5.$ready = false // 解决element组件折叠面板与表单冲突bug
      }, 50)
    },
    // activeNames5() {
    //   setTimeout(() => {
    //     this.$refs.table6.$ready = false // 解决element组件折叠面板与表单冲突bug
    //   }, 50)
    // },
    platformFeeList: {
      handler(val) {
        let num = this.sum1
        if (val && val.length) {
          for (const item of val) {
            const cunArr = item.expenseOrderDetailList
            if (cunArr && cunArr.length) {
              for (const cItem of cunArr) {
                if (cItem.amount) {
                  num = this.$numJiaFun(num, cItem.amount)
                }
              }
            }
          }
        }
        this.sum = num
      },
      immediate: false,
      deep: true,
    },
  },
  data() {
    return {
      financeApplyIdRoot: void 0,
      processInsIdRoot: void 0,
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      financeApply: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      financeNo: '',
      tableData1: [],
      tableData7: [],
      activeNames1: [],
      activeNames2: [],
      activeNames3: [],
      activeNames4: [],
      activeNames5: [],

      activeNames6: [],
      activeNames7: [],

      change1Type: false,
      change2Type: false,
      change3Type: false,
      change4Type: false,
      change5Type: false,

      change6Type: false,
      change7Type: false,

      change1ClassType: true,
      tableData2: [],
      tableData3: [],
      // tableData4: [],
      tableData5: [],
      tableData6: [],
      tableData4copy: [],
      sum: 0,
      sum1: 0,
      chargeMode: '先息后本',
      allDailyInterestRate: 0,
      allAnnualInterestRate: 0,
      dailyInterestRate: 0,
      annualInterestRate: 0,
      calculation: false,
      // 支付凭证
      isOnlyLife: 1,
      imageArr: [],
      pdfArr: [],
      pdfSrc: '',
      orderTextRoot: {},
      // 弹窗
      dialogLook: false,
      statusBox1: false,
      statusForm: {},
      statusOption: {
        detail: false,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: '110',
        column: [],
      },
      // 显影控制
      // 质押品
      pledgedGoodsList: {
        pledgedGoods_serialNumber: false,
        pledgedGoods_uniqueIdentification: false,
        pledgedGoods_tradeBackground: false,
        pledgedGoods_voucherType: false,
        pledgedGoods_dateDue: false,
        pledgedGoods_theCurrentValue: false,
        pledgedGoods_valueOfUse: false,
      },
      // 还款试算&&还款计划
      bankInterestList: {
        reimbursementTrial_bankInterest: false,
        bankInterest_periods: false,
        bankInterest_repaymentDate: false,
        bankInterest_totalShouldAlso: false,
        bankInterest_repaymentPrincipal: false,
        bankInterest_shouldAlsoInterest: false,
      },
      // 平台费用&&缴纳费用
      costPlatformList: {
        reimbursementTrial_costPlatform: false,
        payAFee_costOfName: false,
        payAFee_typeOfExpense: false,
        payAFee_payTheNode: false,
        payAFee_chargeMode: false,
        payAFee_amountPayable: false,
        payAFee_orderStatus: false,
        payAFee_orderStatusRedact: false,
        payAFee_orderStatusLook: false,
        payAFee_checkPaymentVoucher: false,
      },
      // 合同签署
      tableData5Readable: {
        contractSigning_serialNumber: false,
        contractSigning_contractNumber: false,
        contractSigning_contractTitle: false,
        contractSigning_creationTime: false,
        contractSigning_signTheState: false,
        contractSigning_operation: false,
      },
      information: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [
          {
            label: '付款金额',
            prop: 'payAmount',
            labelWidth: '120',
            span: 8,
            placeholder: '请输入银行名称',
            disabled: true,
            rules: [
              {
                required: true,
                message: '请输入银行名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '放款金额(元)',
            prop: 'transfer_amount',
            labelWidth: '120',
            span: 8,
            placeholder: '请输入放款金额',
            disabled: true,
            rules: [
              {
                required: true,
                message: '请输入放款金额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '手续费(元)',
            prop: 'transferFee',
            labelWidth: '120',
            span: 8,
            placeholder: '请输入手续费',
            disabled: true,
            rules: [
              {
                required: true,
                message: '请输入手续费',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '银行名称',
            prop: 'bankDeposit',
            labelWidth: '120',
            span: 8,
            placeholder: '请输入银行名称',
            disabled: true,
            rules: [
              {
                required: true,
                message: '请输入银行名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '银行卡号',
            prop: 'bankCardNo',
            labelWidth: '120',
            span: 8,
            placeholder: '请输入银行名称',
            disabled: true,
            rules: [
              {
                required: true,
                message: '请输入银行名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '银行密码',
            prop: 'transfer_password',
            type: 'password',
            labelWidth: '120',
            span: 8,
            placeholder: '请输入银行密码',
            disabled: true,
            rules: [
              {
                required: true,
                message: '请输入银行密码',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '打款备注',
            prop: 'transfer_remark',
            type: 'textarea',
            labelWidth: '120',
            showWordLimit: true,
            maxlength: 200,
            minRows: 3,
            maxRows: 5,
            span: 24,
            placeholder: '请输入打款备注',
            rules: [
              {
                required: true,
                message: '请输入打款备注',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      platformFeeObj: {},
      // 其他费用的
      platformFeeList: [],
      // 所有的费用
      feeData: { showRepaymentPlan: { stagRecords: [{ planFeeList: [] }] } },
      expenseInfoExpenseList: [],
      expenseInfoExpenseListCopy: [],
    }
  },
  created() {
    this.statusOption.column = [
      {
        label: '状态类型',
        placeholder: '请选择状态类型',
        prop: 'paymentStatus',
        span: 21,
        type: 'select',
        dicData: [
          {
            label: '已支付',
            value: 2,
          },
          {
            label: '支付失败',
            value: 3,
          },
        ],
        dataType: 'number',
        row: true,
        rules: [
          {
            required: true,
            message: '请选择状态',
            trigger: 'blur',
          },
        ],
        control: val => {
          if (val === 2) {
            return {
              moneyBox: {
                display: true,
              },
              amount: {
                display: true,
              },
              bank: {
                display: true,
              },
              account: {
                display: true,
              },
              payTime: {
                display: true,
              },
              failReason: {
                display: false,
              },
            }
          } else if (val === 3) {
            return {
              moneyBox: {
                display: false,
              },
              amount: {
                display: false,
              },
              bank: {
                display: false,
              },
              account: {
                display: false,
              },
              payTime: {
                display: false,
              },
              failReason: {
                display: true,
              },
            }
          } else {
            return {
              moneyBox: {
                display: false,
              },
              amount: {
                display: false,
              },
              bank: {
                display: false,
              },
              account: {
                display: false,
              },
              payTime: {
                display: false,
              },
              failReason: {
                display: false,
              },
            }
          }
        },
      },
      {
        label: '',
        prop: 'moneyBox',
        display: false,
        row: true,
      },
      {
        label: '实还金额',
        prop: 'amount',
        span: 21,
        append: '元',
        placeholder: '请输入实还金额',
        display: false,
        row: true,
        rules: [{ required: true, validator: this.validatePass, trigger: 'blur' }],
      },
      {
        label: '付款开户行',
        prop: 'bank',
        span: 21,
        placeholder: '请输入付款开户行',
        display: false,
        row: true,
        rules: [
          {
            required: true,
            message: '请输入付款开户行',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '付款开户账号',
        prop: 'account',
        span: 21,
        placeholder: '请输入付款开户账号',
        display: false,
        row: true,
        rules: [
          {
            required: true,
            message: '请输入付款开户账号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '支付时间',
        prop: 'payTime',
        type: 'datetime',
        span: 21,
        placeholder: '请选择支付时间',
        format: 'yyyy-MM-dd HH:mm:ss',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        disabled: false,
        display: false,
        rules: [
          {
            required: true,
            message: '请选择支付时间',
            trigger: 'change',
          },
        ],
      },
      {
        label: '失败原因',
        prop: 'failReason',
        type: 'textarea',
        display: false,
        span: 21,
        rules: [
          {
            required: true,
            message: '请输入失败原因',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '支付凭证',
        prop: 'voucherUrl',
        type: 'input',
        row: true,
        span: 21,
      },
    ]
  },
  methods: {
    // 合计
    allMonrySum(item) {
      // this.sum1 = 0
      this.sum1 = this.$numJiaFun(this.sum1, item.monthlySupply)
      // if (this.platformFeeList.length) {
      //   this.platformFeeList.forEach(item => {
      //     if (item.expenseOrderDetailList.length) {
      //       this.sum1 += Number(
      //         item.expenseOrderDetailList[
      //           item.expenseOrderDetailList.length - 1
      //         ].amount
      //       )
      //     }
      //   })
      // }
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(async res => {
        this.waiting = false

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm
        // 流程产品信息
        const {
          processGoodsInfo,
          financeNo,
          financeApply,
          financeApplyId,
          billExpenseOrder,
          payOnline,
          expenseInfoExpenseList,
        } = variables
        this.isOnlyLife = payOnline
        this.financeApplyIdRoot = financeApplyId
        this.processGoodsObj = processGoodsInfo
        this.financeNo = financeNo
        this.financeApply = financeApply || {}
        if (expenseInfoExpenseList && expenseInfoExpenseList.length) {
          this.expenseInfoExpenseList = expenseInfoExpenseList
        }

        // 查询平台账户
        getByGoodsIdAndUserId({
          goodsId: processGoodsInfo.id,
          userId: variables.userId,
        }).then(({ data }) => {
          const { data: resData } = data
          if (data.success && resData) {
            this.statusForm.bank = resData.bankDeposit
            this.statusForm.account = resData.bankCardNo

            if (variables.billExpenseOrder && variables.billExpenseOrder.amount) {
              this.platformFeeObj = {
                amount: variables.billExpenseOrder.amount,
                bank: variables.billExpenseOrder.bank,
                payAccount: variables.billExpenseOrder.account,
                status: variables.billExpenseOrder.paymentStatus,
                endDate: variables.billExpenseOrder.payTime,
                remark: variables.billExpenseOrder.failReason,
              }
            } else {
              this.platformFeeObj = {
                bank: resData.bankDeposit,
                payAccount: resData.bankCardNo,
              }
            }
          }
        })
        if (data.taskName == '融资放款') {
          const {
            data: { code, data },
          } = await getDeByBankCard({
            goodsId: variables.goodsId,
            userId: variables.userId,
            userType: 1,
          })
          const {
            data: { data: fee },
          } = await queryProductTransferFee({ amount: variables.financeApply.amount, productId: variables.goodsId })
          this.information.transferFee = fee.transferFee
          this.information.payAmount = fee.payAmount
          this.information.bankCardNo = data.bankCardNo
          this.information.bankDeposit = data.bankDeposit
          this.information.transfer_amount = variables.financeApply.amount
        }

        // 查支付方式
        if (variables.billExpenseOrder) {
          getDictionary('bill_pay').then(res => {
            const resData = res.data
            if (resData.code == 200) {
              // 处理字典数据
              const resList = []
              for (const item of resData.data) {
                resList.push({
                  key: item.dictKey,
                  value: item.dictValue,
                  id: item.id,
                })
              }
              const fData = resList.filter(item => item.key == variables.billExpenseOrder.paymentMethod)
              if (fData.length) {
                variables.billExpenseOrder.paymentMethod = fData[0].value
              }
            }
          })
        }

        // 流程产品信息
        let usage = []
        getDictionary('finance_apply_loan_usage').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            // 过滤出当前借款用途
            usage = resList.filter(item => item.key == financeApply.loanUsage)
            const data = [
              {
                id: 1,
                label: '融资金额',
                value: `${this.$numChuFun(financeApply.amount, 10000)}万元`,
                key: 'financingDemand_money',
              },
              {
                id: 2,
                label: '借款期限',
                value: `${financeApply.loadTerm}${financeApply.loadTermUnit == 2 ? '个月' : '天'}`,
                key: 'financingDemand_deadline',
              },
              {
                id: 3,
                label: '借款用途',
                value: `${usage[0].value}`,
                key: 'financingDemand_use',
              },
            ]

            // 是否可读
            const dataKey = data.map(item => item.key)
            const taskFormFilter = taskForm.filter(item => dataKey.includes(item.id))
            const taskFormId = taskFormFilter.map(item => {
              if (item.readable) {
                return item.id
              }
            })
            const dataFilter = data.filter(item => taskFormId.includes(item.key))
            this.tableData1 = dataFilter
          }
        })

        // 质押品数据处理
        getDictionary('jrzh_customer_front_sales_contract_proof_type').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            const taskArrKey = [
              'pledgedGoods_serialNumber',
              'pledgedGoods_uniqueIdentification',
              'pledgedGoods_tradeBackground',
              'pledgedGoods_voucherType',
              'pledgedGoods_dateDue',
              'pledgedGoods_theCurrentValue',
              'pledgedGoods_valueOfUse',
            ]
            const taskFormFilter = taskForm.filter(item => taskArrKey.includes(item.id) && item.readable)
            for (const item of taskFormFilter) {
              this.pledgedGoodsList[item.id] = true
            }
            OrderFinancingSaleContractList(financeApplyId).then(res => {
              const { data: resData } = res.data
              const arr = []
              let index = 1
              for (const item of resData) {
                const dat = item.financingLimit
                // usage = resList.filter(item => item.key == dat.proofType)
                arr.push({
                  index: String(index),
                  id: item.id,
                  contractNo: dat.id,
                  contractNo_notShow: true,
                  enterpriseName: item.enterpriseName,
                  // proofType: usage[0].value,
                  expireTime: dat.expireTime,
                  currentAmount: item.amount,
                  amount: item.amount,
                  proofNo: dat.orderNo,
                  proofNo_notShow: true,
                })
                index++
              }
              this.tableData2 = arr
            })
          }
        })

        // 还款试算
        // this.allAnnualInterestRate =
        //   Number(financeApply.annualInterestRate) +
        //   Number(financeApply.serviceRate) // 总年利率
        // this.allDailyInterestRate = (
        //   Number(this.allAnnualInterestRate) / 360
        // ).toFixed(3) // 总日利率
        // this.dailyInterestRate = financeApply.dailyInterestRate // 银行日利率
        // this.annualInterestRate = financeApply.annualInterestRate // 银行年利率
        if (processGoodsInfo.repaymentType === 1) {
          getDictionary('goods_billing_method').then(res => {
            const resData = res.data
            if (resData.code == 200) {
              // 处理字典数据
              const resList = []
              for (const item of resData.data) {
                resList.push({
                  key: item.dictKey,
                  value: item.dictValue,
                  id: item.id,
                })
              }
              // 过滤出当前的计费方式
              this.chargeMode = resList.filter(itemed => itemed.key == financeApply.repaymentMode)[0].value
            }
          })
        } else {
          this.chargeMode = '随借随还'
        }

        const taskArrKey = [
          'reimbursementTrial_bankInterest',
          'bankInterest_periods',
          'bankInterest_repaymentDate',
          'bankInterest_totalShouldAlso',
          'bankInterest_repaymentPrincipal',
          'bankInterest_shouldAlsoInterest',
        ]
        const taskFormFilter = taskForm.filter(item => taskArrKey.includes(item.id) && item.readable)
        for (const item of taskFormFilter) {
          this.bankInterestList[item.id] = true
        }

        orderFinancingRepaymentCalculationDetailById(financeApplyId).then(res => {
          const { data: resData, code } = res.data
          if (code == 200) {
            const arr = []
            this.dailyInterestRate = resData.showRepaymentPlan.dayRate // 银行日利率
            this.annualInterestRate = resData.showRepaymentPlan.yearRate // 银行年利率
            this.feeData = resData
            //  资方统一清分需要处理的逻辑
            let index = 0
            let Map = {}
            const { chargeMethod } = this.variables
            if (chargeMethod == 1) {
              resData.showRepaymentPlan.stagRecords[0].planFeeList.forEach((item, index) => {
                Map[item.feeName] = {
                  index,
                  collectFeeMethod: item.collectFeeMethod,
                  amount: item.amount,
                }
              })
            } else {
              resData.expenseOrderDetailFinanceVos.forEach(item => {
                for (const cItem of item.expenseOrderDetailList) {
                  cItem.repaymentTerm = cItem.repaymentTerm ? cItem.repaymentTerm + '期' : '--'
                }
                // let amount = item.expenseOrderDetailList.reduce((prve, next) => {
                //   if (next.feeFormulaName == '手填') {
                //     next.amount = '人工计算'
                //     return prve
                //   }
                //   return prve + Number(next.amount)
                // }, 0)
                // item.platformExpensesVOS.push({
                //   name: '总计:',
                //   expenseTypeStr: '',
                //   feeNodeStr: '',
                //   feeFormulaName: '',
                //   amount: `￥${formatMoney(amount)}`,
                // })
              })
              this.platformFeeList = resData.expenseOrderDetailFinanceVos
            }
            for (const item of resData.showRepaymentPlan.stagRecords) {
              if (processGoodsInfo.repaymentType === 1) {
                if (item.term) {
                  arr.push({
                    term: `${item.term}期`,
                    refundTime: item.refundTime,
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    planInterest: item.planInterest,
                  })
                  if (index != resData.showRepaymentPlan.stagRecords.length - 1 && chargeMethod == 1) {
                    if (index == 0 || item.planFeeList.length == resData.showRepaymentPlan.stagRecords[0].length) {
                      item.planFeeList.forEach((citem, cindex) => {
                        arr[index][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
                      })
                    } else {
                      for (const key in Map) {
                        arr[index][`amount${Map[key].index}`] = `￥${formatMoney(0)}`
                      }
                      item.planFeeList.forEach(citem => {
                        if (Map[citem.feeName]) {
                          let data = Map[citem.feeName]
                          arr[index][`amount${data.index}`] = `￥${formatMoney(citem.amount)}`
                        }
                      })
                    }
                  }
                } else {
                  this.allMonrySum(item)
                  arr.push({
                    term: '总计',
                    refundTime: '',
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    planInterest: item.planInterest,
                  })

                  if (chargeMethod == 1) {
                    for (const key in Map) {
                      // 一次性付清
                      if (Map[key].collectFeeMethod == 1) {
                        arr[index][`amount${Map[key].index}`] = `￥${formatMoney(Map[key].amount)}`
                      } else {
                        // 分期
                        arr[index][`amount${Map[key].index}`] = `￥${(formatMoney(Map[key].amount) * index).toFixed(2)}`
                      }
                    }
                  }
                }
                index++
              } else {
                // 这是随借随还的
                arr.push({
                  // term: '1期',
                  refundTime: item.refundTime,
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })
                arr.push({
                  // term: '总计:',
                  refundTime: '总计:',
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })
                if (chargeMethod == 1) {
                  item.planFeeList.forEach((citem, cindex) => {
                    arr[0][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
                    arr[1][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
                  })
                }

                this.allMonrySum(item)
              }
            }

            this.tableData3 = arr
          }
        })

        // 合同签署list
        const cotractNameList = [
          'contractSigning_serialNumber',
          'contractSigning_contractNumber',
          'contractSigning_contractTitle',
          'contractSigning_creationTime',
          'contractSigning_signTheState',
          'contractSigning_operation',
        ]
        const cotractNameListFilter = taskForm.filter(item => cotractNameList.includes(item.id))
        const readableList = cotractNameListFilter.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        for (const key in this.tableData5Readable) {
          readableList.forEach(item => {
            if (item == key) {
              this.tableData5Readable[key] = true
            }
          })
        }
        const paramsed = {
          ids: variables.contractId,
        }
        if (variables.contractId) {
          getByContractId(paramsed).then(res => {
            const resData = res.data
            if (resData.code == 200) {
              let num = 1
              for (const item of resData.data) {
                let statusText = ''
                if (item.status) {
                  switch (item.status) {
                    case 1:
                      statusText = '待签署'
                      break
                    case 2:
                      statusText = '已取消'
                      break
                    case 3:
                      statusText = '已签署'
                      break
                    case 4:
                      statusText = '已失效'
                      break
                    case 5:
                      statusText = '已完成'
                      break
                    case 6:
                      statusText = '签署中'
                      break
                  }
                }
                this.tableData5.push({
                  id: item.id,
                  serial: String(num),
                  contractTitle: item.contractTitle,
                  contractId: item.contractId,
                  createTime: item.createTime,
                  statusText: statusText,
                })
                num++
              }
            }
          })
        }

        // 平台费用&缴纳费用
        // 是否可读
        const taskArrKey3 = [
          'reimbursementTrial_costPlatform',
          'payAFee_costOfName',
          'payAFee_typeOfExpense',
          'payAFee_payTheNode',
          'payAFee_chargeMode',
          'payAFee_amountPayable',
          'payAFee_orderStatus',
          'payAFee_orderStatusRedact',
          'payAFee_orderStatusLook',
          'payAFee_checkPaymentVoucher',
        ]
        const taskFormFilter4 = taskForm.filter(item => taskArrKey3.includes(item.id) && item.readable)
        for (const item of taskFormFilter4) {
          this.costPlatformList[item.id] = true
        }
        // getDictionary('goods_expense_rule_fee_node').then(res => {
        //   const resData = res.data
        //   if (resData.code == 200) {
        //     // 处理字典数据
        //     const resList = []
        //     for (const item of resData.data) {
        //       resList.push({
        //         key: item.dictKey,
        //         value: item.dictValue,
        //         id: item.id,
        //       })
        //     }
        //     getDictionary('goods_expense_rule_type').then(res => {
        //       const resData = res.data
        //       if (resData.code == 200) {
        //         // 处理字典数据
        //         const resList1 = []
        //         for (const item of resData.data) {
        //           resList1.push({
        //             key: item.dictKey,
        //             value: item.dictValue,
        //             id: item.id,
        //           })
        //         }
        //         //
        //         platformExpensesList(financeNo).then(res => {
        //           const { data: resData, code } = res.data
        //           if (code == 200) {
        //             const arr = []
        //             for (const item of resData) {
        //               if (item.calculation == 1) {
        //                 this.calculation = true
        //               }
        //               // 过滤出当前的支付节点
        //               const chargePointFilter = resList.filter(
        //                 itemed => itemed.key == item.feeNode
        //               )
        //               // 过滤出当前的费用类型
        //               const expenseTypeFilter = resList1.filter(
        //                 itemed => itemed.key == item.expenseType
        //               )
        //               arr.push({
        //                 name: item.name,
        //                 feeNameType: expenseTypeFilter[0].value,
        //                 chargePoint: chargePointFilter[0].value,
        //                 calculationStr:
        //                   item.calculation != 1
        //                     ? item.feeFormulaName
        //                     : '手动录入',
        //                 amount: item.amount,
        //                 calculation: item.calculation,
        //               })
        //             }
        //             if (variables.platformExpenses) {
        //               for (const item of variables.platformExpenses) {
        //                 for (const itemed of arr) {
        //                   if (itemed.name == item.name) {
        //                     itemed.amount = item.amount
        //                     break
        //                   }
        //                 }
        //               }
        //             }

        //             this.tableData4 = arr
        //             this.tableData4copy = resData // 专门给后端的数据，不处理
        //           }
        //         })
        //       }
        //     })
        //   }
        // })

        // 获取支付凭证
        if (variables.attach_id) {
          ids(variables.attach_id).then(res => {
            const { data: resData, code } = res.data
            if (code == 200) {
              for (const item of resData) {
                if (item.extension !== 'pdf') {
                  //
                  this.imageArr.push({
                    url: item.link,
                  })
                } else {
                  this.pdfArr.push({
                    url: item.link,
                  })
                }
              }
            }
          })
        }
        // 获取订单状态并回显（dialog）
        const statusSession = sessionStorage.getItem(`statusForm${this.processInsIdRoot}`)
        if (statusSession) {
          this.statusForm = JSON.parse(statusSession)
          this.orderTextFun(this.statusForm.paymentStatus)
          return
        } else if (billExpenseOrder) {
          this.statusForm = billExpenseOrder
          this.orderTextFun(billExpenseOrder.paymentStatus)
          return
        }
        this.orderTextFun(1)
      })
    },
    // 通过
    async handleExamine(pass) {
      // if (this.isOnlyLife === 2) {
      //   this.validataFunction(pass)
      //   return
      // }
      let form = {}
      // if (this.resData.taskName != '融资放款' && this.isOnlyLife == 1) {
      //   form = this.$refs.form.form
      // } else
      if (this.resData.taskName == '融资放款' && this.isOnlyLife == 1) {
        form = this.platformFeeObj
      }
      if (this.resData.taskName == '融资放款') {
        // if (
        //   pass &&
        //   ((form.status && form.status === 2) ||
        //     !this.costPlatformList.payAFee_orderStatus ||
        //     this.financeApply.chargeMethod === 1)
        // ) {
        if (pass) {
          // if (this.resData.taskName == '融资放款') {
          this.$refs.bankInformation.validate((valid, done) => {
            if (valid) {
              done()
              this.validataFunction(pass)
            } else {
              return false
            }
          })
          // }
          //  else {
          //   if (this.isOnlyLife == 1) {
          //     this.$refs.form.check().validate((valid, done) => {
          //       if (valid) {
          //         done()
          //         this.validataFunction(pass)
          //       } else {
          //         return false
          //       }
          //     })
          //   } else {
          //     this.validataFunction(pass)
          //   }
          // }
        } else if (!pass) {
          // if (this.resData.taskName == '融资放款') {
          this.validataFunction(pass)
          // } else {
          // if (this.isOnlyLife == 1) {
          //   this.$refs.form.check().validate((valid, done) => {
          //     if (valid) {
          //       done()
          //       this.validataFunction(pass)
          //     } else {
          //       return false
          //     }
          //   })
          // } else {
          //   this.validataFunction(pass)
          // }
          // }
        } else {
          this.$message.error('请修改订单状态')
        }
      } else {
        if (pass) {
          this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.tongguojiaoyanFun()
        } else {
          this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.bohuijiaoyanFun()
        }
        if (!this.expenseInfoExpenseListCopy) return
        this.validataFunction(pass)
      }
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      let params = {}

      if (this.resData.taskName == '融资放款') {
        completeTask({
          pass,
          taskId: this.resData.taskId,
          processInstanceId: this.resData.processInstanceId,
          variables: {
            transfer_amount: this.information.transfer_amount,
            transfer_password: md5(this.information.transfer_password),
            transfer_remark: this.information.transfer_remark,
          },
        })
          .then(() => {
            this.$message.success('处理成功')
            this.$router.$avueRouter.closeTag()
            this.handleCloseTag('/business/businessprocess')
          })
          .catch(() => {
            this.submitLoading = false
          })
        return
      }
      // if (this.isOnlyLife === 1) {
      //   if (
      //     pass &&
      //     this.costPlatformList.payAFee_orderStatus &&
      //     this.financeApply.chargeMethod === 2
      //   ) {
      //     let formObj = {}
      //     const form = this.$refs.form.form
      //     formObj.financeApplyId = this.financeApplyIdRoot // 流程融资ID
      //     formObj.account = form.payAccount
      //     formObj.paymentStatus = form.status
      //     formObj.failReason = form.failReason
      //     formObj.bank = form.bank
      //     formObj.payTime = form.endDate
      //     formObj.amount = Number(form.amount).toFixed(2)
      //     params.billExpenseOrder = formObj
      //   }
      // }
      params.expenseInfoExpenseList = this.expenseInfoExpenseListCopy
      this.handleCompleteTask(pass, params)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 终止
    async handleTermination() {
      // if (this.isOnlyLife === 2 || this.variables.chargeMethod == 1) {
      //   this.validataterminationFunction()
      //   return
      // }
      if (this.resData.taskName == '融资放款') {
        this.validataterminationFunction()
        return
      }
      // const form = this.$refs.form.form
      // if (
      //   (form.status && form.status !== 1) ||
      //   !this.costPlatformList.payAFee_orderStatus ||
      //   this.financeApply.chargeMethod === 1
      // ) {
      //   this.validataterminationFunction()
      //   return
      // }
      // this.$message.error('请修改订单状态')
      this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.zhongzhijiaoyanFun()
      if (!this.expenseInfoExpenseListCopy) return
      this.validataterminationFunction()
    },
    // 终止后调取接口函数
    validataterminationFunction() {
      this.submitLoading = true
      let params = {}
      // if (this.isOnlyLife === 1) {
      //   if (
      //     this.costPlatformList.payAFee_orderStatus &&
      //     this.financeApply.chargeMethod === 2
      //   ) {
      //     if (this.resData.taskName != '融资放款') {
      //       let formObj = {}
      //       const form = this.$refs.form.form
      //       formObj.financeApplyId = this.financeApplyIdRoot // 流程融资ID
      //       formObj.account = form.payAccount
      //       formObj.paymentStatus = form.status
      //       formObj.failReason = form.failReason
      //       formObj.bank = form.bank
      //       formObj.payTime = form.endDate
      //       formObj.amount = form.amount
      //       params = {
      //         billExpenseOrder: formObj,
      //       }
      //     } else {
      //       params = {
      //         billExpenseOrder: this.variables.billExpenseOrder,
      //       }
      //     }
      //   }
      // }
      params.expenseInfoExpenseList = this.expenseInfoExpenseListCopy
      this.handleTerminateProcess(params)
    },
    handleChange1() {
      // 质押品折叠面板收缩控制
      this.change1Type = !this.change1Type
    },
    handleChange2() {
      // 还款试算折叠面板收缩控制（不收取平台费）
      this.change2Type = !this.change2Type
    },
    handleChange3() {
      // 合同模板折叠面板收缩控制
      this.change3Type = !this.change3Type
    },
    handleChange4() {
      // 还款计划折叠面板收缩控制（收取平台费）
      this.change4Type = !this.change4Type
    },
    handleChange5() {
      // 缴纳费用折叠面板收缩控制
      this.change5Type = !this.change5Type
    },
    // 预览合同
    viewContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      skipToPreview(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    // 下载合同
    downContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      contractDownload(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    // 查看支付凭证
    viewVoucher(item) {
      if (item === 'img') {
        this.$ImagePreview(this.imageArr, 0, {
          closeOnClickModal: true,
          showViewer: true,
          beforeClose: () => {
            // this.$message.success('关闭回调')
          },
        })
      } else {
        this.pdfSrc = item.url + '?time=' + new Date().getMilliseconds()
      }
    },
    // el-table表格合计功能
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }
        const values = data.map(item =>
          item[column.property] && !item[`${column.property}_notShow`] ? Number(item[column.property]) : NaN
        )
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    tableRowClassName({ row }) {
      if (!row.refundTime || row.refundTime === '总计:') {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      goodsTypeToPath(this.processGoodsObj)
    },
    // 弹窗
    // 重置数据
    // celarFun() {
    //   this.statusForm = Object.assign({}, this.$options.data().statusForm)
    //   this.statusForm.paymentStatus = void 0
    //   this.statusForm.amount = void 0
    //   this.statusForm.payTime = void 0
    //   this.statusForm.failReason = void 0
    // },

    // 修改状态
    openDialog(type) {
      if (type === 'look') {
        this.dialogLook = true
        for (const item of this.statusOption.column) {
          item.disabled = true
        }
      } else {
        this.dialogLook = false
        for (const item of this.statusOption.column) {
          item.disabled = false
        }
      }
      const obj = {
        serviceCharge: 0,
        storageCharge: 0,
        logisticsCost: 0,
      }
      let allMoney = 0
      for (const item of this.tableData4) {
        allMoney = this.$numJiaFun(allMoney, item.amount)
        if (item.feeNameType === '服务费') {
          obj.serviceCharge = this.$numJiaFun(obj.serviceCharge, item.amount)
        } else if (item.feeNameType === '仓储费') {
          obj.storageCharge = this.$numJiaFun(obj.storageCharge, item.amount)
        } else if (item.feeNameType === '物流费') {
          obj.logisticsCost = this.$numJiaFun(obj.logisticsCost, item.amount)
        }
      }
      obj.totalPrices = allMoney
      this.statusForm.moneyBox = obj
      this.statusForm.amount = allMoney
      this.statusBox1 = true
    },
    // 弹窗修改提交
    submit() {
      this.$refs.AvueMoneyForm.validate((valid, done) => {
        if (valid) {
          if (this.statusForm.paymentStatus === 2) {
            this.statusForm.failReason = ''
          }
          this.orderTextFun(this.statusForm.paymentStatus)
          sessionStorage.setItem(`statusForm${this.processInsIdRoot}`, JSON.stringify(this.statusForm))
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.statusBox1 = false
          done()
        }
      })
    },
    // 付款状态修改函数
    orderTextFun(type) {
      const obj = {}
      if (type === 1) {
        obj.text = '待付款'
        obj.color = '#697CFF'
        obj.bgColor = '#EBF1FF'
      } else if (type === 2) {
        obj.text = '已付款'
        obj.color = '#09C067'
        obj.bgColor = '#DDF0E7'
      } else if (type === 3) {
        obj.text = '已关闭'
        obj.color = '#9A9A9A'
        obj.bgColor = '#F5F5F5'
      }
      this.orderTextRoot = obj
    },
    // dialog实还金额自定义校验
    validatePass(rule, value, callback) {
      const totalPricesValid = this.statusForm.moneyBox.totalPrices
      // const amountValid = this.statusForm.amount
      if (value === '') {
        callback(new Error('请输入还款金额'))
      } else if (Number(totalPricesValid) !== Number(value)) {
        callback(new Error('实还金额必须等于应还金额'))
      } else {
        callback()
      }
    },

    // 获取委托支付收款账户信息
    getAccountInformation() {
      entrustingPaymentAndCollectionAccountInformation().then(res => {
        let enterpriseNameObj = {
          id: 1,
          key: 'enterpriseName',
          label: '账户名称',
          value: res.data.data.enterpriseName,
        }
        let bankDepositObj = {
          id: 2,
          key: 'bankDeposit',
          label: '开户银行',
          value: res.data.data.bankDeposit,
        }
        let bankCardNoObj = {
          id: 3,
          key: 'bankCardNo',
          label: '银行账户',
          value: res.data.data.bankCardNo,
        }
        this.tableData7 = [enterpriseNameObj, bankDepositObj, bankCardNoObj]
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.creditLimitFinancing {
  margin-bottom: 40px;
}

// 覆盖弹窗默认样式
::v-deep {
  .avue-dialog .el-dialog__body {
    margin-bottom: 0;
  }

  .el-dialog__body {
    padding: 0px 20px;
  }
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.pay-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  .order-box {
    display: flex;
    align-items: center;
    cursor: context-menu;

    .order-status {
      width: 56px;
      height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      margin-right: 8px;
    }

    .order-text {
      height: 30px;
      border-radius: 28px;
      font-size: 14px;
      font-family: Microsoft Yahei;
      padding: 4px 8px;
      box-sizing: border-box;
      margin-right: 12px;
    }

    .order-modification-btn {
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
    }

    .order-btn {
      width: 28px;
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }

    .informition-box {
      margin-left: 10px;
      color: #000000b5;
      font-size: 16px;

      & > * {
        margin-left: 70px;

        &:first-child {
          margin-left: 0;
        }
      }

      &_bold {
        font-weight: bold;
      }
    }
  }

  .payOrder-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    span {
      width: 103px;
      height: 30px;
      border-radius: 4px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      text-align: center;
      border: 1px solid rgba(105, 124, 255, 100);
      margin-right: 10px;
      padding: 2px 4px;
      box-sizing: border-box;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }
    }

    .payOrder-left-box {
      margin-right: 10px;
    }
  }
}

.avue-monry-form-box {
  .money-box {
    width: 432px;
    margin: -10px 0;
    border-radius: 6px;
    background-color: rgba(247, 247, 247, 100);
    padding: 0 12px 20px;
    box-sizing: border-box;
    overflow: hidden;

    .chlidern-money {
      display: flex;
      align-items: center;
      margin-top: 20px;

      .label-box {
        width: 95px;
        height: 20px;
        line-height: 20px;
        color: rgba(153, 153, 153, 100);
        font-size: 14px;
        text-align: right;
        font-family: SourceHanSansSC-regular;
      }

      .value-box {
        width: 311px;
        height: 20px;
        line-height: 20px;
        color: rgba(0, 7, 42, 100);
        font-weight: bold;
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .look-btn-menu {
    & span {
      height: 21px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      cursor: pointer;
      margin-right: 8px;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  // 覆盖弹窗默认样式
  ::v-deep {
    .avue-form__menu {
      display: none;
    }

    .el-input.is-disabled .el-input__inner {
      color: #000;
    }

    .el-textarea.is-disabled .el-textarea__inner {
      color: #000;
    }
  }
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;
        cursor: pointer;

        & > span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        & > span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }

          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }

            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }

      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}

::v-deep i.el-collapse-item__arrow {
  display: none;
}

::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}

::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}

::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}

::v-deep .el-card {
  border-radius: 8px;

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
