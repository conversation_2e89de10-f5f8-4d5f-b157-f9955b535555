<template>
  <div class="form-content">
    <div class="form-container">
      <avue-form ref="form1" :option="payOption" v-model="form"></avue-form>
    </div>
    <div class="payInfo">
      <div class="pay-box">
        <!-- <template>
          <div class="form-item" v-for="item of purchaseBondArr" :key="item.id">
            <span class="label">{{ item.expenseTypeStr }}(元):</span>
            <span>￥{{ item.amount | formatMoney }}</span>
          </div>
        </template>
        <div class="form-item">
          <span class="label">应还总额(元):</span>
          <span style="color: red">￥{{ totalMoney(purchaseBondArr) }}</span>
        </div> -->
        <div class="payImg">
          <div class="table-bottom">
            <el-button
              v-if="platformImg.length"
              @click="handlePreviewImage(platformImg)"
              >查看支付凭证</el-button
            >
            <span
              class="table-bottom-pdf"
              v-for="(item, index) in platformPdf"
              :key="item.link"
              @click="handlePreviewImage(item.link, 'pdf')"
            >
              附件{{ index + 1 }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <FilePreview :url="pdfSrc" />
  </div>
</template>
<script>
import FilePreview from '@/components/file-preview'
export default {
  components: {
    FilePreview,
  },
  created() {
    this.payOption.column = [
      {
        label: '付款状态',
        prop: 'status',
        type: 'select',
        clearable: false,
        span: 24,
        disabled: this.see,
        placeholder: '请选择付款状态',
        dicData: [
          {
            label: '待付款',
            value: '1',
          },
          {
            label: '已付款',
            value: '2',
          },
          {
            label: '支付失败',
            value: '3',
          },
        ],
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择付款状态',
            trigger: ['change'],
          },
        ],
        control: value => {
          if (value == 2) {
            return {
              amount: {
                display: true,
              },
              bank: {
                display: true,
              },
              payAccount: {
                display: true,
              },
              remark: {
                display: false,
              },
              endDate: {
                display: true,
              },
            }
          } else if (value == 3) {
            return {
              amount: {
                display: false,
              },
              bank: {
                display: false,
              },
              payAccount: {
                display: false,
              },
              remark: {
                display: true,
              },
              endDate: {
                display: false,
              },
            }
          } else if (value == 1) {
            return {
              amount: {
                display: false,
              },
              bank: {
                display: false,
              },
              payAccount: {
                display: false,
              },
              remark: {
                display: false,
              },
              endDate: {
                display: false,
              },
            }
          }
        },
      },
      {
        disabled: this.see,
        label: '付款金额',
        prop: 'amount',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            validator: this.validategoodsNum,
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        disabled: this.see,
        label: '付款开户行',
        prop: 'bank',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入付款开户行',
            trigger: ['blur'],
          },
        ],
      },
      {
        disabled: this.see,
        label: '付款账号',
        prop: 'payAccount',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入付款账号',
            trigger: ['blur'],
          },
        ],
      },
      {
        disabled: this.see,
        label: '还款时间',
        prop: 'endDate',
        type: 'datetime',
        display: false,
        span: 24,
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now()
          },
        },
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        rules: [
          {
            required: true,
            message: '请选择还款时间',
            trigger: ['change'],
          },
        ],
      },
      {
        label: '失败原因',
        prop: 'remark',
        span: 24,
        display: false,
        type: 'textarea',
        disabled: this.see,
        rules: [
          {
            required: true,
            message: '请输入失败原因',
            trigger: ['blur'],
          },
        ],
      },
    ]
  },
  data() {
    return {
      payOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [],
      },
      // 平台费用
      form: {},
      pdfSrc: '',
    }
  },
  props: {
    purchaseBondArr: {
      type: Array,
      default: () => {
        return []
      },
    },
    see: {
      type: Boolean,
    },
    bankInfo: {
      type: Object,
    },
    platformFee: {
      type: Object,
    },
    platformImg: {
      type: Array,
    },
    platformPdf: {
      type: Array,
    },
    type: {
      type: String,
    },
    pageData: {
      type: Object,
    },
    platformFeeObj: {
      type: Object,
    },
  },
  watch: {
    purchaseBondArr: {
      handler(val) {
        this.form.amount = this.totalMoney(val)
      },
      immediate: true,
      deep: true,
    },
    platformFeeObj: {
      handler(val) {
        this.form = {...val}
        this.form.status = val.status || 1
        this.form.endDate = val.endDate || ''
        console.log(this.form)
      },
      deep: true,
    },
  },
  methods: {
    // 预览图片
    handlePreviewImage(imgSrcArr = [], type = 'img') {
      if (type == 'img') {
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      } else {
        this.pdfSrc = imgSrcArr[0].url + '?time=' + new Date().getMilliseconds()
      }
    },
    validategoodsNum(rule, value, callback) {
      if (value === '') {
        callback(new Error('请输入实还金额'))
      } else if (value < 0) {
        callback(new Error('实还金额要大于0'))
      } else if (value != this.totalMoney(this.purchaseBondArr)) {
        callback(
          new Error(`实还金额应等${this.totalMoney(this.purchaseBondArr)}元`)
        )
      } else {
        callback()
      }
    },
    totalMoney(Arr) {
      let repayTotal = 0
      if (Arr.length == 0) {
        return 0
      }
      for (const item of Arr) {
        repayTotal = this.$numJiaFun(repayTotal, item.amount)
      }
      return repayTotal
    },
    check() {
      if (this.form.status === 1) {
        return false
      } else if (this.form.status === 3) {
        return false
      }
      return this.$refs.form1
    },
  },
}
</script>
<style lang="scss" scoped>
.form-content {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  .form-container {
    width: 50%;
  }
}
.pay-box {
  width: 100%;
  border-radius: 6px;
  box-sizing: border-box;
  line-height: 20px;
  & span:first-child {
    display: block;
    color: rgba(153, 153, 153, 100);
    font-size: 14px;
    font-family: SourceHanSansSC-regular;
    font-weight: 500;
    margin-right: 8px;
  }
  & span:last-child {
    color: #00072a;
    font-weight: 600;
    font-size: 14px;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 150px;
      // text-align: right;
    }
  }
}
</style>