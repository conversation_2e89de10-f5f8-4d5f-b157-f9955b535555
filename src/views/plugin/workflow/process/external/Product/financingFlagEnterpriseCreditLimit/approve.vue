<template>
  <div class="coreEnterpriseCreditLimit">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '申请额度-核心客户'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 额度信息1 -->
      <basic-container v-if="amountOfInformationReadable">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">额度信息</h1>
            <div class="amount-of-information">
              <avue-form ref="form" :option="amountOption" v-model="amountForm">
              </avue-form>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 额度信息2 -->
      <basic-container v-if="amountOfInformationTwoReadable">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">额度信息</h1>
            <div class="amount-of-information">
              <avue-form
                ref="form"
                :option="amountBankOption"
                v-model="amountForm"
              >
              </avue-form>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 风控信息 -->
      <basic-container v-if="riskControlInformationReadable">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1
              class="boxs-to-apply-for-product-h1"
              style="margin-bottom: 15px"
            >
              风控信息
            </h1>
            <RiskControlInformation
              ref="riskControlForm"
              @childByValue="childByValue"
              @validataFunction="validataFunction"
              :id="variables.ratingRecordId"
              :taskForm="taskForm"
              :variables="variables"
              :isCoreEnterprise="true"
            />
          </div>
        </div>
      </basic-container>

      <!-- 批复意见 -->
      <basic-container>
        <div class="approval-container">
          <span class="title">批复意见：</span>
          <el-input
            class="value"
            type="textarea"
            :rows="5"
            resize="none"
            placeholder="请输入批复意见"
            v-model="comment"
          >
          </el-input>
        </div>
      </basic-container>
    </template>
    <!-- 底部按钮 -->
    <wf-button
      class="custom-button"
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></wf-button>
  </div>
</template>

<script>
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import RiskControlInformation from '@/views/plugin/workflow/process/components/riskControlInformation.vue'

export default {
  mixins: [customExForm],
  components: { WfButton, WfFlow, RiskControlInformation },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) {
            this.getDetail(taskId, processInsId)
          }
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      variables: {},
      taskForm: {},
      resData: {},
      objData: {},

      amountForm: {},
      amountOfInformationReadable: true,
      amountOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [],
      },
      amountOfInformationTwoReadable: true,
      amountBankOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [],
      },
      riskControlInformationReadable: true,
    }
  },
  /*
选择企业
chooseEnterprise
选择资方
selectTheManagement
申请额度
creditLimitForApplication
银行年利率
bankAnnualInterestRate
生效日
effectiveDate
到期日
dueDate
额度类型
lineType


开户行
openingBank
银行卡号
creditCardNumbers
  */
  created() {
    // 额度信息1
    const columnData = [
      {
        label: '选择企业',
        prop: 'chooseEnterprise',
        id: 'amountOfInformation_enterpriseName',
        type: 'input',
        placeholder: false,
        span: 12,
        display: true,
        disabled: true,
      },
      {
        label: '选择产品',
        prop: 'selectTheManagement',
        id: 'amountOfInformation_goodsName',
        type: 'input',
        placeholder: false,
        span: 12,
        display: true,
        disabled: true,
      },
      {
        label: '申请额度',
        prop: 'creditLimitForApplication',
        id: 'amountOfInformation_creditAmount',
        type: 'input',
        placeholder: false,
        span: 12,
        append: '万元',
        display: true,
        disabled: true,
      },
      {
        label: '银行年利率',
        prop: 'bankAnnualInterestRate',
        id: 'amountOfInformation_annualInterestRate',
        type: 'input',
        placeholder: false,
        span: 12,
        append: '%',
        display: true,
        disabled: true,
      },
      {
        label: '生效日',
        prop: 'effectiveDate',
        id: 'amountOfInformation_effectiveTime',
        type: 'date',
        placeholder: false,
        span: 12,
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd',
        display: true,
        disabled: true,
      },
      {
        label: '到期日',
        prop: 'dueDate',
        id: 'amountOfInformation_expireTime',
        type: 'date',
        placeholder: false,
        span: 12,
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd',
        display: true,
        disabled: true,
      },
      {
        label: '额度类型',
        prop: 'lineType',
        id: 'amountOfInformation_quotaType',
        type: 'select',
        placeholder: false,
        span: 12,
        display: true,
        disabled: true,
        dicUrl: '/api/blade-system/dict-biz/dictionary?code=quota_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
      },
    ]
    this.amountOption.column = columnData
    // 额度信息2
    const amountBankOptionData = [
      {
        label: '开户行',
        prop: 'openingBank',
        id: 'amountOfInformationTwo_bank',
        type: 'input',
        placeholder: false,
        span: 12,
        display: true,
        disabled: true,
      },
      {
        label: '银行卡号',
        prop: 'creditCardNumbers',
        id: 'amountOfInformationTwo_bankCardNo',
        type: 'input',
        placeholder: false,
        span: 12,
        display: true,
        disabled: true,
      },
    ]
    this.amountBankOption.column = amountBankOptionData
  },
  methods: {
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        if (res) {
          this.waiting = false
          this.resData = res.process
          const { variables } = res.process
          const { taskForm } = res.form
          this.taskForm = taskForm || {}
          this.amountOfInformationReadable = this.filterBox(
            taskForm,
            'amountOfInformation'
          ).readable // 额度信息1 显隐控制
          this.amountOfInformationTwoReadable = this.filterBox(
            taskForm,
            'amountOfInformationTwo'
          ).readable // 额度信息2 显隐控制
          this.riskControlInformationReadable = this.filterBox(
            taskForm,
            'riskControlInformation'
          ).readable // 风控信息 显隐控制

          const columnDataArr = this.amountOption.column
            .concat(this.amountBankOption.column)
            .map(item => item.id)

          const columnDataFilterArr = this.taskForm.filter(item =>
            columnDataArr.includes(item.id)
          )

          const columnDataFilterArrId = columnDataFilterArr.map(item => {
            if (item.readable) {
              return item.id
            }
          })

          this.amountOption.column = this.amountOption.column.filter(item =>
            columnDataFilterArrId.includes(item.id)
          )
          this.amountBankOption.column = this.amountBankOption.column.filter(
            item => columnDataFilterArrId.includes(item.id)
          )

          this.variables = variables || {}
          this.variables.processInstanceId = res.process.processInstanceId
          const vars = variables.coreEnterpriseQuota
          this.amountForm.chooseEnterprise = vars.enterpriseName // 选择企业
          this.amountForm.selectTheManagement = vars.goodsName // 选择产品
          this.amountForm.creditLimitForApplication = vars.creditAmount // 申请额度
          this.amountForm.bankAnnualInterestRate = vars.annualInterestRate // 银行年利率
          this.amountForm.effectiveDate = vars.effectiveTime // 生效日
          this.amountForm.dueDate = vars.expireTime // 到期日
          this.amountForm.lineType = vars.quotaType // 额度类型
          this.amountForm.openingBank = vars.bank // 开户行
          this.amountForm.creditCardNumbers = vars.bankCardNo // 银行卡号
        }
      })
    },
    // 通过
    handleExamine(pass) {
      if (this.riskControlInformationReadable && pass) {
        this.$refs.riskControlForm.forValidata(pass)
      } else {
        this.validataFunction(pass)
      }
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      this.handleCompleteTask(pass, this.objData)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 子传父函数
    childByValue(value) {
      const obj = {
        riskControlAdvice: {
          lineOfCredit: value.lineOfCredit,
          riskThat: value.riskThat,
        },
        managementCredit: {
          chooseEnterprise: value.chooseEnterprise,
          selectTheManagement: value.selectTheManagement,
          bondProportion: value.bondProportion1,
          quotaType: value.lineType1,
          recycleType: value.recycleType1,
          // effectiveTime: value.effectiveDate1,
          expireTime: value.dueDate1,
          loanable: value.loanableInto1,
        },
        finalApproveAmount: {
          finalAmount: value.finalBatchOfTheForehead,
          serviceRate: value.serviceTariffing,
          quotaType: value.lineType2,
          recycleType: value.recycleType2,
          // effectiveTime: value.effectiveDate2,
          expireTime: value.dueDate2,
          annualInterestRate: value.financingCostRate,
          loanable: value.loanableInto2,
          bondProportion: value.bondProportion2,
        },
        score: value.score,
        // ratingRecordId: value.ratingRecordId,
      }
      this.objData = obj
    },
    filterBox(arr, condition) {
      return arr.filter(item => item.id == condition)[0]
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.coreEnterpriseCreditLimit {
  ::v-deep {
    .basic-container__card {
      border-radius: 8px;
    }

    .avue-form__menu {
      display: none;
    }

    .el-input.is-disabled .el-input__inner {
      color: #000;
    }

    .el-input-group__append {
      color: #000;
    }
  }

  .boxs {
    .boxs-to-apply-for-product {
      box-sizing: border-box;

      h1 {
        margin: 0;
      }

      .boxs-to-apply-for-product-h1 {
        height: 22px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }

      .amount-of-information {
        margin-top: 30px;
      }
    }
  }
}
</style>
