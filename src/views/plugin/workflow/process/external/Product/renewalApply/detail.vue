<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '展期申请查看'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <div class="boxs">
        <div class="boxs-to-apply-for-product">
          <div class="boxs-to-apply-title">
            <h2>产品信息</h2>
          </div>
          <div class="boxs-to-apply-item">
            <div class="boxs-to-apply-item-left">
              <div class="boxs-to-apply-item-left-logo">
                <img :src="loanDetail.productImg" />
              </div>
              <div class="boxs-to-apply-item-left-goodname">
                <span @click="viewGoods()">{{ loanDetail.productName }}</span>
                <span>{{ loanDetail.capitalName }}</span>
              </div>
            </div>
            <div class="boxs-to-apply-item-right">
              <span>应收账款质押</span>
            </div>
          </div>
          <div class="table-top">
            <el-descriptions
              title=""
              :column="3"
              border
              class="table-descriptions"
            >
              <el-descriptions-item label="总授信额度"
                >{{
                  $numChuFun(loanDetail.creditAmount || 0, 10000) | formatMoney
                }}万元</el-descriptions-item
              >
              <el-descriptions-item label="可用额度"
                >{{
                  $numChuFun(loanDetail.availableAmount || 0, 10000) | formatMoney
                }}万元</el-descriptions-item
              >
              <el-descriptions-item label="融资总额"
                >{{
                  $numChuFun(loanDetail.usedAmount, 10000) | formatMoney
                }}万元</el-descriptions-item
              >
              <el-descriptions-item label="待还总额"
                >{{
                  $numChuFun(loanDetail.repaymentAmount, 10000) | formatMoney
                }}万元</el-descriptions-item
              >
              <el-descriptions-item label="年利率"
                >{{
                  loanDetail.annualInterestRate || 0.0
                }}%</el-descriptions-item
              >
              <el-descriptions-item label="额度类型">{{
                loanDetail.recycleType == 1
                  ? '不可循环额度'
                  : loanDetail.recycleType == 2
                  ? '循环额度(还款后执行)'
                  : ''
              }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>

      <!-- 还款试算 -->
      <basic-container v-if="currentRepayPlan.currentRepayPlan">
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': changeType,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>当前还款计划</span>
                    <!-- <span class="long-string" />
                    <span class="interest-rate"
                      >日利率{{
                        Number(loanDetail.dailyInterestRate || 0).toFixed(3)
                      }}%&nbsp;(年化利率{{
                        loanDetail.annualInterestRate
                      }}%)</span
                    > -->
                  </h1>
                </div>
              </div>
            </template>

            <div
              class="table-top refund"
              v-if="currentRepayPlan.currentRepayPlan_waitPay"
            >
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>资方费用</span>
                  <span />
                  <span
                    >日利率{{ dailyInterestRate }}%（年化利率{{
                      annualInterestRate
                    }}%）</span
                  >
                </div>
                <div class="title-right-box">
                  计费方式:&nbsp;{{ loanDetail.billingMethod }}
                </div>
              </div>
              <el-table
                ref="table"
                :data="tableData"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  prop="term"
                  label="期数"
                  width="110"
                  align="center"
                  v-if="
                    currentRepayPlan.currentRepayPlan_waitPay_term &&
                    loanDetail &&
                    loanDetail.billingMethod !== '随借随还'
                  "
                >
                </el-table-column>
                <el-table-column
                  v-if="currentRepayPlan.currentRepayPlan_waitPay_endDate"
                  prop="refundTime"
                  label="还款日期"
                >
                </el-table-column>
                <el-table-column
                  v-if="currentRepayPlan.currentRepayPlan_waitPay_totalAmount"
                  prop="monthlySupply"
                  label="应还总额"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="currentRepayPlan.currentRepayPlan_waitPay_principal"
                  prop="monthlyPrincipal"
                  label="还款本金"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.monthlyPrincipal | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="
                    currentRepayPlan.currentRepayPlan_waitPay_shouldInterest
                  "
                  prop="planInterest"
                  label="应还利息"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.planInterest | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-if="platformFeeList.length">
              <div
                class="table-top refund"
                v-for="item in platformFeeList"
                :key="item.id"
              >
                <div class="chain-line" />
                <div class="table-title-box" style="margin-top: -10px">
                  <div class="title-left-box">
                    <span>{{ item.parenExpenseName }}</span>
                  </div>
                </div>
                <el-table
                  ref="table3"
                  :data="item.expenseOrderDetailList"
                  :summary-method="getSummaries"
                  show-summary
                  style="width: 100%; margin-top: 13px"
                  class="table-border-style"
                >
                  <el-table-column prop="expenseTypeStr" label="费用名称">
                  </el-table-column>
                  <!-- <el-table-column
                    prop="expenseTypeStr"
                    label="费用类型"
                  >
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.expenseTypeStr }}
                      </span>
                    </template>
                  </el-table-column> -->
                  <el-table-column prop="repaymentTerm" label="期数">
                  </el-table-column>
                  <el-table-column prop="feeNodeStr" label="计算节点">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="collectFeesNodeStr" label="收费节点">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.collectFeesNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeFormulaName" label="计费方式">
                  </el-table-column>
                  <el-table-column prop="amount" label="应付金额">
                    <template slot-scope="scope">
                      <!-- <span>￥{{ scope.row.amount | formatMoney }} </span> -->
                      <span v-if="scope.row.calculation !== 1"
                        >￥{{ scope.row.amount | formatMoney }}
                      </span>
                      <div v-else style="width: 80%">
                        <el-input
                          placeholder="请输入金额"
                          v-model="scope.row.amount"
                          type="number"
                          :disabled="
                            !costPlatformList.costPlatform_amountPayable_w
                          "
                        >
                          <template slot="append">元</template>
                        </el-input>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- <div
              class="chain-line"
              v-if="currentRepayPlan.currentRepayPlan_waitPay"
            /> -->

            <div class="chain-line" />
            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥{{ sum | formatMoney }} </span>
              </div>
            </div>

            <!--<div
              class="table-top refund"
              v-if="currentRepayPlan.currentRepayPlan_alreadyPay"
            >
              <div class="table-title-box" style="margin-top: -10px">
                <div class="title-left-box">
                  <span>已还明细</span>
                </div>
              </div>
              <el-table
                ref="table2"
                :data="tableData2"
                :summary-method="getSummaries"
                show-summary
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
              >
                <el-table-column
                  prop="period"
                  label="所属期数"
                  v-if="currentRepayPlan.currentRepayPlan_alreadyPay_term"
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.period }}期 </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="repaymentTime"
                  label="还款日期"
                  v-if="currentRepayPlan.currentRepayPlan_alreadyPay_endDate"
                >
                </el-table-column>
                <el-table-column
                  prop="totalAmount"
                  label="还款总额"
                  v-if="
                    currentRepayPlan.currentRepayPlan_alreadyPay_totalAmount
                  "
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.totalAmount | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="principal"
                  label="还款本金"
                  v-if="
                    currentRepayPlan.currentRepayPlan_alreadyPay_repaymentPrincipal
                  "
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.principal | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="interest"
                  label="还款利息"
                  v-if="
                    currentRepayPlan.currentRepayPlan_alreadyPay_monthlyInterest
                  "
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.interest | formatMoney }} </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>-->

            <!-- <div
              class="chain-line"
              v-if="currentRepayPlan.currentRepayPlan_alreadyPay"
            /> -->
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 展期申请信息 -->
      <basic-container v-if="extensionAppliyInfo.extensionAppliyInfo">
        <el-collapse v-model="activeNames2" @change="handleChange2">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change2Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>展期申请信息</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <el-descriptions
                title=""
                :column="3"
                border
                class="table-descriptions"
              >
                <el-descriptions-item
                  label="展期类型"
                  v-if="
                    extensionAppliyInfo.extensionAppliyInfo_deferType &&
                    loanDelay.repaymentType == 1
                  "
                  >{{
                    loanDelay.type == 2
                      ? '剩余待还全部展期'
                      : loanDelay.type == 1
                      ? '当前展期'
                      : ''
                  }}</el-descriptions-item
                >
                <el-descriptions-item
                  label="展期金额"
                  v-if="extensionAppliyInfo.extensionAppliyInfo_deferAmount"
                  >{{ loanDelay.amount | formatMoney }}元</el-descriptions-item
                >
                <el-descriptions-item
                  label="展期方式"
                  v-if="extensionAppliyInfo.extensionAppliyInfo_deferMethod"
                  >{{
                    loanDelay.delayMode == 1
                      ? '分期还款'
                      : loanDelay.delayMode == 3
                      ? '延迟还款'
                      : ''
                  }}</el-descriptions-item
                >
                <el-descriptions-item
                  label="延迟还款天数"
                  v-if="
                    loanDelay.delayMode == 3 &&
                    extensionAppliyInfo.extensionAppliyInfo_delayDays
                  "
                  >{{ loanDelay.delayDays || 0 }}天</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="
                    loanDelay.delayMode == 1 &&
                    extensionAppliyInfo.extensionAppliyInfo_delayTerm
                  "
                  label="展期期限"
                  >{{ loanDelay.delayTerm || 0 }}期</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="
                    loanDelay.delayMode == 1 &&
                    extensionAppliyInfo.extensionAppliyInfo_repaymentMode
                  "
                  label="计费方式"
                >
                  {{ loanDelay.repaymentModeText }}
                </el-descriptions-item>
                <el-descriptions-item
                  label="展期开始日"
                  :span="loanDelay.delayMode == 3 ? 2 : 1"
                  v-if="
                    extensionAppliyInfo.extensionAppliyInfo_loanDelayStartDate
                  "
                >
                  {{ loanDelay.loanDelayStartDate }}
                </el-descriptions-item>
                <el-descriptions-item
                  label="展期理由"
                  :span="3"
                  v-if="extensionAppliyInfo.extensionAppliyInfo_deferRemark"
                  >{{ loanDelay.reason }}</el-descriptions-item
                >
              </el-descriptions>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 展期处理 -->
      <basic-container v-if="extensionDeal.extensionDeal">
        <el-collapse v-model="activeNames3" @change="handleChange3">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change3Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>展期处理</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <el-form
                ref="form"
                class="loan-form"
                :model="form"
                label-position="right"
                label-width="120px"
                label-suffix=":"
                :rules="formRules"
              >
                <el-form-item
                  label="展期方式"
                  prop="delayMode"
                  v-if="extensionDeal.extensionDeal_deferMthod"
                >
                  <el-select
                    disabled
                    v-model="form.delayMode"
                    style="width: 100%"
                    placeholder="请选择展期方式"
                  >
                    <el-option
                      v-for="item in modeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item
                  label="展期期限"
                  v-if="
                    form.delayMode == 1 && extensionDeal.extensionDeal_delayTerm
                  "
                  prop="delayTerm"
                >
                  <el-input
                    disabled
                    v-model="form.delayTerm"
                    placeholder="请输入展期期限"
                  >
                    <template slot="append">期</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="最低还款金额"
                  prop="repaymentMoneyMin"
                  v-if="
                    form.delayMode == 2 &&
                    extensionDeal.extensionDeal_deferRefundAmount
                  "
                >
                  <el-input
                    disabled
                    v-model="form.repaymentMoneyMin"
                    placeholder="请输入最低还款金额"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="延迟还款天数"
                  v-if="
                    form.delayMode == 3 && extensionDeal.extensionDeal_delayDays
                  "
                  prop="delayDays"
                >
                  <el-input
                    disabled
                    v-model="form.delayDays"
                    placeholder="请输入延迟还款天数"
                  >
                    <template slot="append">天</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="展期年利率"
                  prop="interesRate"
                  v-if="extensionDeal.extensionDeal_deferRate"
                >
                  <el-input
                    disabled
                    v-model="form.interesRate"
                    placeholder="请输入展期年利率"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-form>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>
    </template>
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import { formatMoney } from '@/util/filter'
import { getDictionary } from '@/api/goods/pcontrol/pinformation'
import { getLoanDelayDetail } from '@/api/goods/pcontrol/workflow/productConfirmation'

export default {
  mixins: [customExForm],
  components: { WfFlow },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNames() {
      this.$refs.table.$ready = false
      this.$refs.table2.$ready = false
    },
    platformFeeList: {
      handler(val) {
        let num = this.sum
        if (val && val.length) {
          for (const item of val) {
            const cunArr = item.expenseOrderDetailList
            if (cunArr && cunArr.length) {
              for (const cItem of cunArr) {
                if (cItem.amount) {
                  num = this.$numJiaFun(num, cItem.amount)
                }
              }
            }
          }
        }
        this.sum = num
      },
      immediate: false,
      deep: true,
    },
  },

  data() {
    return {
      activeName: 'first',
      form: {
        delayMode: null,
        delayTerm: null,
        interesRate: null,
        delayDays: null,
        repaymentMoneyMin: null,
      },
      formRules: {
        delayMode: [
          { required: true, message: '请选择展期方式', trigger: 'change' },
        ],
        delayTerm: [
          { required: true, message: '请输入展期期限', trigger: 'blur' },
        ],
        interesRate: [
          { required: true, message: '请输入展期年利率', trigger: 'blur' },
        ],
      },
      modeOptions: [],
      resData: {},
      variables: {},
      taskForm: {},
      activeNames: ['furtherInformation'],
      activeNames2: ['furtherInformation'],
      activeNames3: ['furtherInformation'],
      changeType: true,
      change2Type: true,
      change3Type: true,

      tableData: [],
      tableData2: [],
      currentRepayPlan: {}, // 当前还款计划显隐
      extensionAppliyInfo: {}, // 展期申请信息
      extensionDeal: {}, // 展期处理

      loanDelay: {}, // 展期申请信息
      loanDetail: {}, // 展期申请审核详情
      repayMentOptions: [], // 计费方式

      dailyInterestRate: 0,
      annualInterestRate: 0,
      // 其他费用的
      platformFeeList: [],
      sum: 0,
      sum1: 0
    }
  },
  created() {
    this.getDictionary({ code: 'goods_billing_method' })
  },
  methods: {
    // 合计
    allMonrySum(item) {
      this.sum = this.$numJiaFun(this.sum, item.monthlySupply || 0)
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm

        // 流程产品信息
        const { loanDelay } = variables
        this.loanDelay = { ...loanDelay }

        let obj = this.repayMentOptions.find(
          item => item.dictKey == loanDelay.repaymentMode
        )
        this.loanDelay.repaymentModeText = obj ? obj.dictValue : null
        if (loanDelay.financeNo) {
          this.getLoanDelayDetail({ financeNo: loanDelay.financeNo })
        }

        this.form = {
          interesRate: loanDelay.interesRate,
          delayMode: loanDelay.delayMode,
          delayTerm: loanDelay.delayTerm,
          repaymentMoneyMin: loanDelay.repaymentMoneyMin,
          delayDays: loanDelay.delayDays,
        }
        if (loanDelay.repaymentType == 1) {
          // 分期
          if (loanDelay.type == 1) {
            // 当期展期
            this.modeOptions = [
              {
                label: '延迟还款',
                value: 3,
              },
            ]
          } else if (loanDelay.type == 2) {
            // 剩余待还全部展期
            this.modeOptions = [
              {
                label: '分期还款',
                value: 1,
              },
            ]
          }
        } else if (loanDelay.repaymentType == 2) {
          // 随借随还
          this.modeOptions = [
            {
              label: '分期还款',
              value: 1,
            },
            {
              label: '延迟还款',
              value: 3,
            },
          ]
        }

        
        this.costCalculusVOCunChu = variables.costCalculusVO
        const costCalculusVO = this.costCalculusVOCunChu
        if (costCalculusVO) {
          // 资方数据
          const showRepaymentPlan = costCalculusVO.showRepaymentPlan
          if (showRepaymentPlan) {
            this.dailyInterestRate = showRepaymentPlan.dayRate // 银行日利率
            this.annualInterestRate = showRepaymentPlan.yearRate // 银行年利率

            const arr = []

            for (const item of showRepaymentPlan.stagRecords) {
              if (loanDelay.repaymentType === 1) {
                if (item.term) {
                  arr.push({
                    term: `${item.term}期`,
                    refundTime: item.refundTime,
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    planInterest: item.planInterest,
                  })
                } else {
                  this.allMonrySum(item)
                  arr.push({
                    term: '总计',
                    refundTime: '',
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    planInterest: item.planInterest,
                  })
                }
              } else {
                // 这是随借随还的
                arr.push({
                  // term: '1期',
                  refundTime: item.refundTime,
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })
                arr.push({
                  // term: '总计:',
                  refundTime: '总计:',
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })

                this.allMonrySum(item)
              }
            }

            this.tableData = arr
          }
          // 动态费用
          const expenseOrderDetailFinanceVos = costCalculusVO.expenseOrderDetailFinanceVos
          if (expenseOrderDetailFinanceVos && expenseOrderDetailFinanceVos.length) {
            for (const item of expenseOrderDetailFinanceVos) {
              item.totalMoney = 0
              for (const citem of item.expenseOrderDetailList) {
                citem.repaymentTerm = citem.repaymentTerm
                  ? citem.repaymentTerm + '期'
                  : '--'
                item.totalMoney = this.$numJiaFun(item.totalMoney, citem.amount)
                if (citem.amount && Number(citem.amount) === 0) {
                  citem.enableInput = true
                }
              }
            }
            this.platformFeeList = expenseOrderDetailFinanceVos
          }
        }

        //控制显隐问题

        const taskArrkey1 = [
          'currentRepayPlan',
          'currentRepayPlan_waitPay',
          'currentRepayPlan_waitPay_term',
          'currentRepayPlan_waitPay_totalAmount',
          'currentRepayPlan_waitPay_endDate',
          'currentRepayPlan_waitPay_principal',
          'currentRepayPlan_waitPay_shouldInterest',
          'currentRepayPlan_alreadyPay',
          'currentRepayPlan_alreadyPay_term',
          'currentRepayPlan_alreadyPay_endDate',
          'currentRepayPlan_alreadyPay_totalAmount',
          'currentRepayPlan_alreadyPay_repaymentPrincipal',
          'currentRepayPlan_alreadyPay_monthlyInterest',
        ]
        const taskFormFilter1 = taskForm.filter(
          item => taskArrkey1.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter1) {
          this.currentRepayPlan[item.id] = true
        }

        // 展期申请信息
        const taskArrkey2 = [
          'extensionAppliyInfo',
          'extensionAppliyInfo_deferType',
          'extensionAppliyInfo_deferAmount',
          'extensionAppliyInfo_deferMethod',
          'extensionAppliyInfo_deferRemark',
          'extensionAppliyInfo_delayTerm',
          'extensionAppliyInfo_delayDays',
          'extensionAppliyInfo_repaymentMode',
          'extensionAppliyInfo_loanDelayStartDate',
        ]
        const taskFormFilter2 = taskForm.filter(
          item => taskArrkey2.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter2) {
          this.extensionAppliyInfo[item.id] = true
        }
        const taskArrkey3 = [
          'extensionDeal',
          'extensionDeal_deferMthod',
          'extensionDeal_deferRefundAmount',
          'extensionDeal_deferRate',
          'extensionDeal_delayTerm',
          'extensionDeal_delayDays',
        ]
        const taskFormFilter3 = taskForm.filter(
          item => taskArrkey3.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter3) {
          this.extensionDeal[item.id] = true
        }
      })
    },

    handleChange() {
      // 还款计划
      this.changeType = !this.changeType
    },
    handleChange2() {
      // 展期申请信息
      this.change2Type = !this.change2Type
    },
    handleChange3() {
      // 展期处理
      this.change3Type = !this.change3Type
    },

    viewGoods() {
      if (!this.loanDetail.productId) return
      this.$router.push({
        path: '/pcontrol/purchasing',
        query: { id: this.loanDetail.productId },
      })
      sessionStorage.setItem('look', 'true')
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    // 审核信息
    async getLoanDelayDetail(params) {
      const { data } = await getLoanDelayDetail(params)
      if (data.code === 200) {
        this.loanDetail = { ...data.data }
        // this.tableData = (data.data && data.data.noRepaymentList) || []
        // this.tableData2 = (data.data && data.data.repaymentList) || []
      }
    },
    // 计费方式
    async getDictionary(params) {
      const { data } = await getDictionary(params)
      let resData = data.data || []
      let list = []
      if (data.code === 200) {
        for (const item of resData) {
          list.push({ ...item })
        }
      }
      this.repayMentOptions = list
    },
    tableRowClassName({ row }) {
      if (!row.refundTime || row.refundTime === '总计:') {
        return 'aggregate-row'
      }
      return ''
    },
  },
}
</script>

<style lang="scss" scoped>
.creditLimitFinancing {
  margin-bottom: 40px !important;
}

::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
  .table-goodsInfo {
    display: flex;
    align-items: center;
    & img {
      width: 72px;
      height: 72px;
      object-fit: cover;
      margin-right: 8px;
    }
    .table-goodsInfo-right {
      .goodsInfo-right-top {
        cursor: pointer;
        width: 196px;
        color: #697cff;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
      .goodsInfo-right-bottom {
        width: 196px;
        line-height: 20px;
        color: rgba(141, 141, 141, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
  .table-text-red {
    color: #ff2929;
    font-weight: 500;
    font-size: 14px;
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  font-size: 16px;
  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;
    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}
.formRight {
  color: rgba(125, 125, 125, 100);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  font-family: SourceHanSansSC-bold;
}
.dis-flex {
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  justify-content: space-between;
}

.fees-at-box {
  margin-top: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}
.i-active {
  transform: rotate(0deg) !important;
}
.boxs {
  display: flex;
  justify-content: space-around;
  padding: 10px 6px;
  .boxs-to-apply-for-product {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    .boxs-to-apply-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      h2 {
        margin-right: 8px;
        height: 22px;
        line-height: 22px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
      }
      .boxs-to-apply-title-right {
        display: flex;
        align-items: center;
        border-left: 1px solid #d7d7d7;
        line-height: 20px;
        padding: 0 8px;
        & span:nth-of-type(1) {
          display: block;
          color: #7d7d7d;
          font-size: 14px;
          font-weight: 500;
          margin-right: 6px;
        }
        & span:nth-of-type(2) {
          display: block;
          font-weight: 500;
          font-size: 14px;
          color: #697cff;
        }
      }
    }

    .boxs-to-apply-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .boxs-to-apply-item-left {
        display: flex;
        align-items: center;
        .boxs-to-apply-item-left-logo {
          width: 48px;
          height: 48px;
          border-radius: 40px;
          border: 1px solid rgba(234, 234, 234, 100);
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          margin-right: 7px;
          & > img {
            width: 100%;
            object-fit: cover;
          }
        }
        .boxs-to-apply-item-left-goodname {
          display: flex;
          flex-direction: column;
          & > span:first-child {
            display: inline-block;
            // width: 168px;
            height: 24px;
            color: rgba(18, 119, 255, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 3px;
          }
          & > span:last-child {
            display: inline-block;
            // width: 168px;
            height: 18px;
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
      .boxs-to-apply-item-right {
        & > span {
          cursor: pointer;
          display: block;
          padding: 2px 10px;

          line-height: 24px;
          border-radius: 24px;
          background-color: #1277ff;
          color: #ffffff;
          font-size: 14px;
          font-weight: 500;
          text-align: center;
        }
      }
      .boxs-to-apply-item-label {
        text-align: left;
        font-size: 14px;
        font-weight: 500;
        color: #7d7d7d;
        height: 18px;
        line-height: 18px;
        .init-color {
          color: #000;
        }
      }
    }
    ::v-deep .el-descriptions-item__label {
      width: 135px;
      max-width: 135px;
    }
  }
}
.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}
.border-box {
  display: inline-block;
  color: #00072a;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

// 云信读取
::v-deep {
  .cloud-collapse {
    margin-top: 20px;
    .el-descriptions-item__label {
      width: 200px;
      max-width: 200px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
::v-deep .el-form {
  margin-top: 20px;
  .el-form-item__content {
    flex-grow: 1;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .table-bottom {
      margin-top: 16px;
      display: flex;
      justify-content: right;
      .el-button {
        padding: 0;
        width: 103px;
        height: 30px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px;
        color: #697cff;
        border-color: #697cff;
        box-sizing: border-box;
      }
      .table-bottom-pdf {
        cursor: pointer;
        display: inline-block;
        padding: 2px 6px;
        color: #697cff;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px;
        margin-left: 4px;
        border: 1px solid #697cff;
      }
    }

    .table-descriptions {
      margin-top: 30px;
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
  .loan-form {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    margin-right: -60px;
    .el-form-item {
      width: calc(50% - 60px);
      margin-bottom: 24px;
      margin-right: 60px;
    }
  }
}
</style>
