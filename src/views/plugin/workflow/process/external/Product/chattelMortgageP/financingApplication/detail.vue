<template>
  <div class="creditLimitFinancing" style="margin-bottom: 40px">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '动产质押-融资申请'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{ variables.applyUserName }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <basic-container>
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">
              <span>融资需求</span>
              <span class="long-string" />
              <div class="serial-number">
                <span>融资编号：</span>
                <span>{{ financeNo }}</span>
              </div>
            </h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{ processGoodsObj.goodsName }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">动产质押</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item v-for="item in tableData1" :key="item.id" :label="item.label">{{
                  item.value
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 实际入库信息 -->
      <basic-container v-if="cargoInformationLis.cargonformation_module">
        <el-collapse v-model="activeNames2" @change="handleChange2">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change2Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">实际入库信息</h1>
                </div>
              </div>
            </template>
            <div class="table-top"></div>
            <MyElTable ref="tableB2" :tableData="tableData2" :columns="columnList2">
              <template #goodsInfos="{ iScope: { row } }">
                <div style="display: flex; align-items: center; justify-content: flex-start">
                  <el-image
                    style="width: 50px; height: 50px"
                    :src="row.logo"
                    :preview-src-list="[row.logo]"
                    fit="contain"
                  ></el-image>
                  <span>{{ row.goodsName }}</span>
                </div>
              </template>
            </MyElTable>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 评估信息 -->
      <basic-container v-if="evaluationInformation.evaluation_module">
        <el-collapse v-model="activeNames6" @change="handleChange6">
          <el-collapse-item name="furtherInformation">
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change6Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">评估信息</h1>
                </div>
              </div>
            </template>
            <div class="table-top"></div>
            <MyElTable ref="tableB6" :tableData="tableData6" :columns="columnList6" :isGetSummaries="getSummaries">
              <template #goodsInfo="{ iScope: { row } }">
                <div class="table-goodsInfo">
                  <img :src="row.commodityUrl" alt />
                  <div class="table-goodsInfo-right">
                    <div class="goodsInfo-right-top" @click="handleLinkClick(row.commodityId)">
                      {{ row.name }}
                    </div>
                    <div class="goodsInfo-right-bottom">规格型号：{{ row.spec }}</div>
                  </div>
                </div>
              </template>
              <template #prices="{ iScope: { row } }">
                <span>{{ row.unitPrice }}</span>
              </template>
              <template #purchasePrice="{ iScope: { row } }">
                <span>{{ row.purchasePrice }}</span>
                <!-- <el-input-number
                  :disabled="!evaluationInformationW.er_m_purchasePrice"
                  v-model="row.purchasePrice"
                  @blur="accountingPrice(row)"
                  :controls="false"
                  placeholder="请输入单价"
                  :max="row.uMax[1]"
                  style="width: 100%"
                /> -->
                <!-- :min="row.uMax[0]" -->
              </template>
            </MyElTable>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 质押信息 -->
      <basic-container v-if="financingInforList.financing_module">
        <el-collapse v-model="activeNames1" @change="handleChange1">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change1Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">质押信息</h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <el-table ref="table1" :data="tableData" style="width: 100%" class="table-border-style">
                <el-table-column type="expand">
                  <template slot-scope="props">
                    <div>
                      <el-table :data="props.row.innerData">
                        <el-table-column label="商品图片" prop="commodityUrl">
                          <template slot-scope="scope">
                            <el-image
                              style="width: 50px; height: 50px"
                              :src="scope.row.commodityUrl"
                              :preview-src-list="[scope.row.commodityUrl]"
                            >
                            </el-image>
                          </template>
                        </el-table-column>
                        <el-table-column label="商品名称" prop="commodityName"> </el-table-column>
                        <el-table-column label="商品规格" prop="spec"> </el-table-column>
                        <el-table-column label="商品数量" prop="quantityStr"> </el-table-column>
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="index"
                  label="#"
                  width="70"
                  align="center"
                  v-if="financingInforList.fc_m_index"
                ></el-table-column>
                <el-table-column label="额度编号" prop="quotaNo" v-if="financingInforList.fc_m_quotaNo">
                </el-table-column>
                <el-table-column label="产品名称" prop="goodsName" v-if="financingInforList.fc_m_goodsName">
                </el-table-column>
                <el-table-column label="资方名称" prop="capitalName" v-if="financingInforList.fc_m_capitalName">
                </el-table-column>
                <el-table-column label="可用额度" prop="availableAmount" v-if="financingInforList.fc_m_availableAmount">
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.availableAmount | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column label="额度状态" prop="quotaType" v-if="financingInforList.fc_m_quotaType">
                </el-table-column>
                <el-table-column label="到期日" prop="expireTime" v-if="financingInforList.fc_m_expireTime">
                </el-table-column>
              </el-table>
            </div>
            <!-- <p
              v-if="financingInforList.fc_m_loanAmount"
              class="loan-amount-box"
            >
              <span class="loan-amount-box_asterisk">*</span>
              <span class="loan-amount-box_text">输入放款金额(元)：</span>
              <el-input-number
                v-model="loanAmount"
                :min="1"
                :controls="false"
                :disabled="!financingInforWList.fc_m_loanAmount"
                @blur="loanAmountBlurFun"
              ></el-input-number>
            </p> -->
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 费用试算 -->
      <basic-container v-if="trialCostInformationLis.trial_cost_module">
        <el-collapse v-model="activeNames4" @change="handleChange4">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change4Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>费用试算</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>银行利息</span>
                  <span />
                  <span v-if="rateObj">
                    日利率{{ rateObj.dailyInterestRate }}%(年化利率{{ rateObj.annualInterestRate }}%)
                  </span>
                </div>
                <div class="title-right-box">计费方式：随借随还</div>
              </div>
            </div>
            <!-- 银行利息 -->
            <MyElTable
              ref="tableB4"
              :tableData="tableData4"
              :columns="columnList4"
              emptyText="请输入放款金额进行试算"
              :rowClassNameCondition="{
                rKey: 'refundTime',
                rValue: '总计:',
              }"
            />
            <!-- 其他费用 -->
            <div v-if="otherFeeData.length" class="other-f-box">
              <div v-for="item in otherFeeData" :key="item.id" class="other-f-box_father-b">
                <div class="other-f-box_childer-name">
                  {{ item.parenExpenseName }}
                </div>
                <MyElTable
                  :tableData="item.expenseOrderDetailList"
                  :columns="columnList5"
                  :rowClassNameCondition="{
                    rKey: 'expenseTypeStr',
                    rValue: '总计:',
                  }"
                >
                  <template #feeNodeStr="{ iItem: { iProp }, iScope: { row } }">
                    <div v-if="row.expenseTypeStr !== '总计:'">
                      <span class="tag-styles-box">
                        {{ row[iProp] }}
                      </span>
                    </div>
                  </template>
                  <template #collectFeesNodeStr="{ iItem: { iProp }, iScope: { row } }">
                    <div v-if="row.expenseTypeStr !== '总计:'">
                      <span class="tag-styles-box">
                        {{ row[iProp] }}
                      </span>
                    </div>
                  </template>
                </MyElTable>
              </div>
            </div>

            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥{{ sum1 | formatMoney }} </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 合同签署 -->
      <basic-container v-if="contractSignList.contract_list">
        <el-collapse v-model="activeNames3" @change="handleChange3">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change3Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">合同签署</h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <el-table
                ref="table3"
                :data="tableData3"
                :max-height="240"
                style="width: 100%"
                class="table-border-style"
              >
                <el-table-column
                  v-if="contractSignList.cs_m_serial"
                  prop="serial"
                  label="序号"
                  width="80"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="contractSignList.cs_m_contractId"
                  prop="contractId"
                  label="合同编号"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="contractSignList.cs_m_contractTitle"
                  prop="contractTitle"
                  label="合同标题"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="contractSignList.cs_m_createTime"
                  prop="createTime"
                  label="创建时间"
                  width="250"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="contractSignList.cs_m_statusText"
                  prop="statusText"
                  label="签署状态"
                  min-width="150"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag :type="['已完成', '已签署'].includes(scope.row.statusText) ? 'success' : ''">{{
                      scope.row.statusText
                    }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="contractSignList.cs_m_operations"
                  prop="operations"
                  label="操作"
                  min-width="100"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div class="action-bar">
                      <!-- <span
                        v-if="
                          !['已完成', '已签署'].includes(scope.row.statusText)
                        "
                        :iDisabled="isUntreated"
                        class="action-bar_view"
                        @click="signContract(scope)"
                        >签署</span
                      > -->
                      <span class="action-bar_view" @click="viewContract(scope)">详情</span>
                      <span class="action-bar_down" @click="downContract(scope)">下载</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 质押品 -->
      <!--<basic-container>
        <el-collapse v-model="activeNames1" @change="handleChange1">
          <el-collapse-item name="furtherInformation">
            <!~~ title的solt ~~>
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change1Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">质押品</h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <el-table
                ref="table1"
                :data="tableData2"
                :summary-method="getSummaries"
                show-summary
                style="width: 100%; margin-top: 20px"
                class="table-border-style"
              >
                <el-table-column
                  prop="index"
                  label="#"
                  width="70"
                  align="center"
                  v-if="pledgedGoodsList.pledgedGoods_serialNumber"
                >
                </el-table-column>
                <el-table-column
                  prop="contractNo"
                  label="资产编号"
                  v-if="pledgedGoodsList.pledgedGoods_uniqueIdentification"
                >
                  <template slot-scope="scope">
                    <span class="contract-no">{{ scope.row.contractNo }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="enterpriseName"
                  label="贸易背景"
                  v-if="pledgedGoodsList.pledgedGoods_tradeBackground"
                >
                </el-table-column>
                <el-table-column
                  prop="proofType"
                  label="凭证类型"
                  v-if="pledgedGoodsList.pledgedGoods_voucherType"
                >
                  <template slot-scope="scope">
                    <span class="tag-box">{{ scope.row.proofType }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="expireTime"
                  label="到期日期"
                  v-if="pledgedGoodsList.pledgedGoods_dateDue"
                >
                </el-table-column>
                <!~~ <el-table-column
                  v-if="
                    resData.status != 'finished' &&
                    pledgedGoodsList.pledgedGoods_theCurrentValue
                  "
                  prop="currentAmount"
                  label="当前价值（元）"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.currentAmount | formatMoney }} </span>
                  </template>
                </el-table-column> ~~>
                <el-table-column
                  v-if="
                    resData.status != 'finished' &&
                    pledgedGoodsList.pledgedGoods_valueOfUse
                  "
                  prop="amount"
                  label="本次使用价值（元）"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.amount | formatMoney }} </span>
                  </template>
                </el-table-column>
                <template slot="header" slot-scope="scope">
                  {{ scope }}
                </template>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>-->

      <!-- 还款试算 -->
      <!--<basic-container>
        <el-collapse v-model="activeNames2" @change="handleChange2">
          <el-collapse-item name="furtherInformation">
            <!~~ title的solt ~~>
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change2Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>还款试算</span>
                    <!~~ <span class="long-string" />
                    <span class="interest-rate"
                      >日利率{{ allDailyInterestRate }}%（年化利率{{
                        allAnnualInterestRate
                      }}%）</span
                    > ~~>
                  </h1>
                </div>
              </div>
            </template>
            <div
              class="table-top refund"
              v-if="bankInterestList.reimbursementTrial_bankInterest"
            >
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>资方费用</span>
                  <span />
                  <span
                    >日利率{{ dailyInterestRate }}%（年化利率{{
                      annualInterestRate
                    }}%）</span
                  >
                </div>
                <div class="title-right-box">计费方式：{{ chargeMode }}</div>
              </div>
              <el-table
                ref="table2"
                :data="tableData3"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  prop="term"
                  label="期数"
                  width="110"
                  align="center"
                  v-if="
                    bankInterestList.bankInterest_periods &&
                    processGoodsObj &&
                    processGoodsObj.repaymentType === 1
                  "
                >
                </el-table-column>
                <el-table-column
                  prop="refundTime"
                  label="还款日期"
                  v-if="bankInterestList.bankInterest_repaymentDate"
                >
                </el-table-column>
                <el-table-column
                  prop="monthlySupply"
                  label="应还总额"
                  v-if="bankInterestList.bankInterest_totalShouldAlso"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="monthlyPrincipal"
                  label="还款本金"
                  v-if="bankInterestList.bankInterest_repaymentPrincipal"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.monthlyPrincipal | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="planInterest"
                  label="应还利息"
                  v-if="bankInterestList.bankInterest_shouldAlsoInterest"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.planInterest | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-for="(item, index) in feeData.showRepaymentPlan
                    .stagRecords[0].planFeeList"
                  :key="item.id"
                  :prop="`amount${index}`"
                  :label="item.feeName"
                >
                </el-table-column>
              </el-table>
            </div>

            <div v-if="platformFeeList.length">
              <div
                class="table-top refund"
                v-for="item in platformFeeList"
                :key="item.id"
              >
                <div class="chain-line" />
                <div class="table-title-box" style="margin-top: -10px">
                  <div class="title-left-box">
                    <span>{{ item.expenseName }}</span>
                  </div>
                </div>
                <el-table
                  ref="table3"
                  :data="item.platformExpensesVOS"
                  :summary-method="getSummaries"
                  show-summary
                  style="width: 100%; margin-top: 13px"
                  class="table-border-style"
                >
                  <el-table-column prop="expenseName" label="费用名称">
                  </el-table-column>
                  <el-table-column prop="expenseTypeStr" label="费用类型">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.expenseTypeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeNodeStr" label="支付节点">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeFormulaName" label="计费方式">
                  </el-table-column>
                  <el-table-column prop="amount" label="应付金额">
                    <template slot-scope="scope">
                      <span>￥{{ scope.row.amount | formatMoney }} </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div class="chain-line" />
            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥{{ sum | formatMoney }} </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>-->
    </template>
    <!-- <ContractSign
      :signObj="signObj"
      :isPledgeMovables="true"
      @resetList="getByContractIdFun(deptId)"
    /> -->
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import { formatMoney } from '@/util/filter.js'
import workflowTool from '@/util/workflowTool.js'
// import ContractSign from '@/components/ContractSign/index.vue'
import MyElTable from '@/components/myElTable/index.vue'
import {
  // getDictionary,
  // saleContractList,
  // repaymentCalculationDetailById,
  // platformExpensesList2,
  detailsByFinanceNo,
  getByBackContractId,
  skipToPreview,
  contractDownload,
  pledgeMovablesRepaymentCalculation,
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import dayjs from 'dayjs'

import { goodsTypeToPath } from '../../globalFun.js'

export default {
  mixins: [customExForm],
  components: { WfButton, WfFlow, MyElTable },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNames1() {
      setTimeout(() => {
        this.$refs.table1.$ready = false
      }, 50)
    },
    activeNames2() {
      setTimeout(() => {
        this.$refs.tableB2.$children[0].$ready = false
        // this.$refs.table3.$ready = false
      }, 50)
    },
    activeNames4() {
      setTimeout(() => {
        this.$refs.tableB4.$children[0].$ready = false
      }, 50)
    },
    activeNames6() {
      setTimeout(() => {
        this.$refs.tableB6.$children[0].$ready = false
      }, 50)
    },
    // tableData4: {
    //   handler(val) {
    //     if (val) {
    //       // let num = 0
    //       // for (const item of val) {
    //       //   num += Number(item.amount)
    //       // }
    //       //  Number(num) +
    //       this.sum = Number(this.sum1)
    //     }
    //   },
    //   immediate: false,
    //   deep: true,
    // },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      financeNo: '',
      activeNames1: ['furtherInformation'],
      activeNames2: ['furtherInformation'],
      activeNames3: ['furtherInformation'],
      activeNames4: ['furtherInformation'],
      activeNames6: ['furtherInformation'],
      change1Type: true,
      change2Type: true,
      change3Type: true,
      change4Type: true,
      change6Type: true,
      tableData: [],
      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData4copy: [],
      tableData6: [],
      sum: 0,
      sum1: 0,
      chargeMode: '先息后本',
      allDailyInterestRate: 0,
      allAnnualInterestRate: 0,
      dailyInterestRate: 0,
      annualInterestRate: 0,
      calculation: false,
      financeApplyAfterEnd: {}, // 给后端的数据

      // 显影控制
      // 质押信息显影控制
      financingInforList: {
        financing_module: false,
        fc_m_index: false,
        fc_m_quotaNo: false,
        fc_m_goodsName: false,
        fc_m_capitalName: false,
        fc_m_availableAmount: false,
        fc_m_quotaType: false,
        fc_m_expireTime: false,
        // fc_m_loanAmount: false,
      },
      // 质押信息填写控制
      // financingInforWList: {
      //   fc_m_loanAmount: false,
      // },
      // 货物信息显影控制
      cargoInformationLis: {
        cargonformation_module: false,
        cr_m_index: false,
        cr_m_financeNo: false,
        cr_m_goodsInfos: false,
        cr_m_supplierName: false,
        cr_m_unitName: false,
        cr_m_quantity: false,
        cr_m_warehouseInNum: false,
      },
      // 合同显影控制
      contractSignList: {
        contract_list: false,
        cs_m_serial: false,
        cs_m_contractId: false,
        cs_m_contractTitle: false,
        cs_m_createTime: false,
        cs_m_statusText: false,
        cs_m_operations: false,
      },
      // 费用试算显影控制
      trialCostInformationLis: {
        trial_cost_module: false,
      },
      // 评估信息显影控制
      evaluationInformation: {
        evaluation_module: false,
        er_m_index: false,
        er_m_goodsInfo: false,
        er_m_prices: false,
        er_m_purchasePrice: false,
        er_m_financingRatio: false,
        er_m_financingPrice: false,
        er_m_quantity: false,
        er_m_financingTotal: false,
      },
      // 评估信息填写控制
      evaluationInformationW: {
        er_m_purchasePrice: false,
      },
      // pledgedGoodsList: {
      //   pledgedGoods_serialNumber: false,
      //   pledgedGoods_uniqueIdentification: false,
      //   pledgedGoods_tradeBackground: false,
      //   pledgedGoods_voucherType: false,
      //   pledgedGoods_dateDue: false,
      //   pledgedGoods_theCurrentValue: false,
      //   pledgedGoods_valueOfUse: false,
      // },
      // bankInterestList: {
      //   reimbursementTrial_bankInterest: false,
      //   bankInterest_periods: false,
      //   bankInterest_repaymentDate: false,
      //   bankInterest_totalShouldAlso: false,
      //   bankInterest_repaymentPrincipal: false,
      //   bankInterest_shouldAlsoInterest: false,
      // },
      // costPlatformList: {
      //   reimbursementTrial_costPlatform: false,
      //   costPlatform_typeOfExpense: false,
      //   costPlatform_costOfName: false,
      //   costPlatform_payTheNode: false,
      //   costPlatform_chargeMode: false,
      //   costPlatform_amountPayable_r: false,
      //   costPlatform_amountPayable_w: false,
      // },
      // 其他费用的
      // platformFeeList: [],
      // 所有的费用
      // feeData: { showRepaymentPlan: { stagRecords: [{ planFeeList: [] }] } },
      isUntreated: false, // 货物节点并且入库完成可以签合同
      deptId: '', // 客户id 合同模块使用
      signObj: {},
      // loanAmount: undefined, // 放款金额
      otherFeeData: [], // 这个是其他费用的 资方分别收取用的
      hasBeenSigned: true, // 是否已完成合同签署
      rateObj: false, // 费用年利率对象
      columnList2: [
        {
          id: 1,
          iProp: 'index',
          iLabel: '#',
          iWidth: 70,
          iAlign: 'center',
          key: 'cr_m_index',
        },
        {
          id: 2,
          iProp: 'financeNo',
          iLabel: '融资编号',
          key: 'cr_m_financeNo',
        },
        {
          id: 3,
          iProp: 'goodsInfos',
          iLabel: '货物信息',
          isSlot: true,
          key: 'cr_m_goodsInfos',
        },
        {
          id: 4,
          iProp: 'supplierName',
          iLabel: '供应商',
          iWidth: 200,
          key: 'cr_m_supplierName',
        },
        {
          id: 5,
          iProp: 'readyToStorage',
          iLabel: '待入库数量',
          key: 'cr_m_quantity',
        },
        {
          id: 6,
          iProp: 'warehouseInNum',
          iLabel: '已入库数量',
          key: 'cr_m_warehouseInNum',
        },
        {
          id: 7,
          iProp: 'goodsUnitValue',
          iLabel: '单位值',
          key: 'cr_m_unitName',
        },
      ], // 货物信息列数据
      columnList4: [
        // {
        //   id: 1111,
        //   isType: true,
        //   iProp: 'isType',
        //   iLabel: '',
        // },
        {
          id: 1,
          iProp: 'refundTime',
          iLabel: '还款日期',
        },
        {
          id: 2,
          iProp: 'monthlySupply',
          iLabel: '应还总额',
        },
        {
          id: 3,
          iProp: 'monthlyPrincipal',
          iLabel: '应还本金',
        },
        {
          id: 4,
          iProp: 'planInterest',
          iLabel: '应还利息',
        },
      ], // 费用试算列数据
      columnList5: [
        {
          id: 1,
          iProp: 'expenseTypeStr',
          iLabel: '费用名称',
        },
        {
          id: 2,
          iProp: 'repaymentTerm',
          iLabel: '期数',
        },
        {
          id: 3,
          iProp: 'feeNodeStr',
          iLabel: '计算节点',
          isSlot: true,
        },
        {
          id: 4,
          iProp: 'collectFeesNodeStr',
          iLabel: '收费节点',
          isSlot: true,
        },
        {
          id: 5,
          iProp: 'feeFormulaName',
          iLabel: '计费方式',
        },
        {
          id: 6,
          iProp: 'amountStr',
          iLabel: '应付金额',
        },
      ], // 平台费用列数据
      columnList6: [
        {
          id: 1,
          iProp: 'index',
          iLabel: '#',
          iWidth: 70,
          iAlign: 'center',
          key: 'er_m_index',
        },
        {
          id: 2,
          iProp: 'goodsInfo',
          iLabel: '货品信息',
          iWidth: 240,
          isSlot: true,
          key: 'er_m_goodsInfo',
        },
        {
          id: 3,
          iProp: 'prices',
          iLabel: '发布单价(元)',
          iWidth: 120,
          isSlot: true,
          key: 'er_m_prices',
        },
        {
          id: 4,
          iProp: 'purchasePrice',
          iLabel: '评估单价(元)',
          iWidth: 140,
          isSlot: true,
          key: 'er_m_purchasePrice',
        },
        {
          id: 5,
          iProp: 'financingRatio',
          iLabel: '融资比例(%)',
          key: 'er_m_financingRatio',
        },
        {
          id: 6,
          iProp: 'financingPrice',
          iLabel: '融资单价(元)',
          isMoneyFilter: true,
          key: 'er_m_financingPrice',
        },
        {
          id: 7,
          iProp: 'quantity',
          iLabel: '数量',
          key: 'er_m_quantity',
        },
        {
          id: 8,
          iProp: 'financingTotal',
          iLabel: '总额(元)',
          isMoneyFilter: true,
          key: 'er_m_financingTotal',
        },
      ], // 评估信息列数据
    }
  },
  methods: {
    // 评估信息当前行合计
    accountingPrice(row) {
      row.financingPrice = this.$numChuFun(this.$numChengFun(row.purchasePrice, row.financingRatio.split('%')[0]), 100)
      row.financingTotal = this.$numChengFun(row.financingPrice, row.quantity)
      let moneyN = 0
      for (const item of this.tableData6) {
        moneyN = this.$numJiaFun(moneyN, item.financingTotal)
      }
      // 调用费用试算方法
      this.loanAmountBlurFun(moneyN)
    },
    // 合计
    allMonrySum(item) {
      this.sum1 = 0
      this.sum1 = this.$numJiaFun(this.sum1, item.monthlySupply)
      if (this.otherFeeData.length) {
        this.otherFeeData.forEach(item => {
          if (item.expenseOrderDetailList.length) {
            this.sum1 = this.$numJiaFun(
              this.sum1,
              item.expenseOrderDetailList[item.expenseOrderDetailList.length - 1].amountNum
            )
          }
        })
      }
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm

        // 流程产品信息
        const {
          pledge_quota,
          pledge_commodity_info,
          financeNo,
          financeApply,
          // financeApplyId,
        } = variables
        this.processGoodsObj = pledge_quota
        // this.processGoodsObj = processGoodsInfo
        this.financeNo = financeNo
        this.financeApplyAfterEnd = financeApply // 给后端的
        // 质押信息模块数据处理
        this.getTableDataFun(pledge_quota, pledge_commodity_info)
        // 货物信息模块数据处理
        this.getCargoInformationDataFun()
        // this.getCargoInformationDataFun(pledge_quota)
        // 合同模块数据处理
        this.getByContractIdFun(pledge_quota)
        // 费用试算模块显影
        this.getTrialCostInformationLisFun()
        // 评估记录模块
        this.evaluationRecordFun(pledge_commodity_info, pledge_quota.financingProportion + '%' || '0%')

        // 流程产品信息
        // let usage = []
        // getDictionary('finance_apply_loan_usage').then(res => {
        //   const resData = res.data
        //   if (resData.code == 200) {
        //     // 处理字典数据
        //     const resList = []
        //     for (const item of resData.data) {
        //       resList.push({
        //         key: item.dictKey,
        //         value: item.dictValue,
        //         id: item.id,
        //       })
        //     }
        //     // 过滤出当前借款用途
        //     usage = resList.filter(item => item.key == financeApply.loanUsage)
        {
          const data = [
            {
              id: 1,
              label: '融资金额',
              value: `${financeApply.amountW}万元`,
              // key: 'financingDemand_money',
            },
            {
              id: 2,
              label: '借款期限',
              value: `${financeApply.loadTerm}${financeApply.loadTermUnit == 2 ? '个月' : '天'}`,
              // key: 'financingDemand_deadline',
            },
            // {
            //   id: 3,
            //   label: '借款用途',
            //   value: `${usage[0].value}`,
            //   key: 'financingDemand_use',
            // },
          ]

          // 是否可读
          // const dataKey = data.map(item => item.key)
          // const taskFormFilter = taskForm.filter(item =>
          //   dataKey.includes(item.id)
          // )
          // const taskFormId = taskFormFilter.map(item => {
          //   if (item.readable) {
          //     return item.id
          //   }
          // })
          // const dataFilter = data.filter(item => !taskFormId.includes(item.key))
          // this.tableData1 = dataFilter
          this.tableData1 = data
        }
        //   }
        // })

        // // 质押品数据处理
        // getDictionary('jrzh_customer_front_sales_contract_proof_type').then(
        //   res => {
        //     const resData = res.data
        //     if (resData.code == 200) {
        //       // 处理字典数据
        //       const resList = []
        //       for (const item of resData.data) {
        //         resList.push({
        //           key: item.dictKey,
        //           value: item.dictValue,
        //           id: item.id,
        //         })
        //       }
        //       const taskArrKey = [
        //         'pledgedGoods_serialNumber',
        //         'pledgedGoods_uniqueIdentification',
        //         'pledgedGoods_tradeBackground',
        //         'pledgedGoods_voucherType',
        //         'pledgedGoods_dateDue',
        //         'pledgedGoods_theCurrentValue',
        //         'pledgedGoods_valueOfUse',
        //       ]
        //       const taskFormFilter = taskForm.filter(
        //         item => taskArrKey.includes(item.id) && item.readable
        //       )
        //       for (const item of taskFormFilter) {
        //         this.pledgedGoodsList[item.id] = true
        //       }
        //       saleContractList(financeApplyId).then(res => {
        //         const { data: resData } = res.data
        //         const arr = []
        //         let index = 1
        //         for (const item of resData) {
        //           const dat = item.salesContract
        //           usage = resList.filter(item => item.key == dat.proofType)
        //           arr.push({
        //             index: String(index),
        //             id: item.id,
        //             contractNo: dat.contractNo,
        //             enterpriseName: item.enterpriseName,
        //             proofType: usage[0].value,
        //             expireTime: dat.expireTime,
        //             // currentAmount: item.currentAmount,
        //             amount: item.amount,
        //           })
        //           index++
        //         }
        //         this.tableData2 = arr
        //       })
        //     }
        //   }
        // )

        // // 还款试算
        // // this.allAnnualInterestRate =
        // //   Number(financeApply.annualInterestRate) +
        // //   Number(financeApply.serviceRate) // 总年利率
        // // this.allDailyInterestRate = (
        // //   Number(this.allAnnualInterestRate) / 360
        // // ).toFixed(3) // 总日利率
        // // this.dailyInterestRate = financeApply.dailyInterestRate // 银行日利率
        // // this.annualInterestRate = financeApply.annualInterestRate // 银行年利率
        // if (processGoodsInfo.repaymentType === 1) {
        //   getDictionary('goods_billing_method').then(res => {
        //     const resData = res.data
        //     if (resData.code == 200) {
        //       // 处理字典数据
        //       const resList = []
        //       for (const item of resData.data) {
        //         resList.push({
        //           key: item.dictKey,
        //           value: item.dictValue,
        //           id: item.id,
        //         })
        //       }
        //       // 过滤出当前的计费方式
        //       this.chargeMode = resList.filter(
        //         itemed => itemed.key == financeApply.repaymentMode
        //       )[0].value
        //     }
        //   })
        // } else {
        //   this.chargeMode = '随借随还'
        // }

        // const taskArrKey = [
        //   'reimbursementTrial_bankInterest',
        //   'bankInterest_periods',
        //   'bankInterest_repaymentDate',
        //   'bankInterest_totalShouldAlso',
        //   'bankInterest_repaymentPrincipal',
        //   'bankInterest_shouldAlsoInterest',
        // ]
        // const taskFormFilter = taskForm.filter(
        //   item => taskArrKey.includes(item.id) && item.readable
        // )
        // for (const item of taskFormFilter) {
        //   this.bankInterestList[item.id] = true
        // }

        // repaymentCalculationDetailById(financeApplyId).then(res => {
        //   const { data: resData, code } = res.data
        //   if (code == 200) {
        //     const arr = []
        //     this.dailyInterestRate = resData.showRepaymentPlan.dayRate // 银行日利率
        //     this.annualInterestRate = resData.showRepaymentPlan.yearRate // 银行年利率
        //     this.feeData = resData
        //     //  资方统一清分需要处理的逻辑
        //     let index = 0
        //     let Map = {}
        //     const { chargeMethod } = this.variables
        //     if (chargeMethod == 1) {
        //       resData.showRepaymentPlan.stagRecords[0].planFeeList.forEach(
        //         (item, index) => {
        //           Map[item.feeName] = {
        //             index,
        //             collectFeeMethod: item.collectFeeMethod,
        //             amount: item.amount,
        //           }
        //         }
        //       )
        //     } else {
        //       resData.parentExpenseTypeVOList.forEach(item => {
        //         let amount = item.platformExpensesVOS.reduce((prve, next) => {
        //           if (next.feeFormulaName == '手填') {
        //             next.amount = '人工计算'
        //             return prve
        //           }
        //           return prve + Number(next.amount)
        //         }, 0)
        //       })
        //       this.platformFeeList = resData.parentExpenseTypeVOList
        //     }
        //     for (const item of resData.showRepaymentPlan.stagRecords) {
        //       if (processGoodsInfo.repaymentType === 1) {
        //         if (item.term) {
        //           arr.push({
        //             term: `${item.term}期`,
        //             refundTime: item.refundTime,
        //             monthlySupply: item.monthlySupply,
        //             monthlyPrincipal: item.monthlyPrincipal,
        //             planInterest: item.planInterest,
        //           })
        //           if (
        //             index != resData.showRepaymentPlan.stagRecords.length - 1 &&
        //             chargeMethod == 1
        //           ) {
        //             if (
        //               index == 0 ||
        //               item.planFeeList.length ==
        //                 resData.showRepaymentPlan.stagRecords[0].length
        //             ) {
        //               item.planFeeList.forEach((citem, cindex) => {
        //                 arr[index][`amount${cindex}`] = `￥${formatMoney(
        //                   citem.amount
        //                 )}`
        //               })
        //             } else {
        //               for (const key in Map) {
        //                 arr[index][
        //                   `amount${Map[key].index}`
        //                 ] = `￥${formatMoney(0)}`
        //               }
        //               item.planFeeList.forEach(citem => {
        //                 if (Map[citem.feeName]) {
        //                   let data = Map[citem.feeName]
        //                   arr[index][`amount${data.index}`] = `￥${formatMoney(
        //                     citem.amount
        //                   )}`
        //                 }
        //               })
        //             }
        //           }
        //         } else {
        //           this.allMonrySum(item)
        //           arr.push({
        //             term: '总计',
        //             refundTime: '',
        //             monthlySupply: item.monthlySupply,
        //             monthlyPrincipal: item.monthlyPrincipal,
        //             planInterest: item.planInterest,
        //           })

        //           if (chargeMethod == 1) {
        //             for (const key in Map) {
        //               // 一次性付清
        //               if (Map[key].collectFeeMethod == 1) {
        //                 arr[index][
        //                   `amount${Map[key].index}`
        //                 ] = `￥${formatMoney(Map[key].amount)}`
        //               } else {
        //                 // 分期
        //                 arr[index][`amount${Map[key].index}`] = `￥${(
        //                   formatMoney(Map[key].amount) * index
        //                 ).toFixed(2)}`
        //               }
        //             }
        //           }
        //         }
        //         index++
        //       } else {
        //         // 这是随借随还的
        //         arr.push({
        //           // term: '1期',
        //           refundTime: item.refundTime,
        //           monthlySupply: item.monthlySupply,
        //           monthlyPrincipal: item.monthlyPrincipal,
        //           planInterest: item.planInterest,
        //         })
        //         arr.push({
        //           // term: '总计:',
        //           refundTime: '总计:',
        //           monthlySupply: item.monthlySupply,
        //           monthlyPrincipal: item.monthlyPrincipal,
        //           planInterest: item.planInterest,
        //         })
        //         if (chargeMethod == 1) {
        //           item.planFeeList.forEach((citem, cindex) => {
        //             arr[0][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
        //             arr[1][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
        //           })
        //         }

        //         this.allMonrySum(item)
        //       }
        //     }

        //     this.tableData3 = arr
        //   }
        // })

        // // 平台费用
        // // 是否可读
        // const taskArrKey2 = [
        //   'reimbursementTrial_costPlatform',
        //   'costPlatform_typeOfExpense',
        //   'costPlatform_costOfName',
        //   'costPlatform_payTheNode',
        //   'costPlatform_chargeMode',
        //   'costPlatform_amountPayable',
        // ]
        // const taskFormFilter2 = taskForm.filter(
        //   item => taskArrKey2.includes(item.id) && item.readable
        // )
        // for (const item of taskFormFilter2) {
        //   if (item.id == 'costPlatform_amountPayable') {
        //     this.costPlatformList['costPlatform_amountPayable_r'] = true
        //   } else {
        //     this.costPlatformList[item.id] = true
        //   }
        // }
        // // 是否可写
        // const taskFormFilter3 = taskForm.filter(
        //   item => item.id == 'costPlatform_amountPayable' && item.writeable
        // )
        // if (taskFormFilter3.length) {
        //   this.costPlatformList['costPlatform_amountPayable_w'] = true
        // }
        // getDictionary('goods_expense_rule_fee_node').then(res => {
        //   const resData = res.data
        //   if (resData.code == 200) {
        //     // 处理字典数据
        //     const resList = []
        //     for (const item of resData.data) {
        //       resList.push({
        //         key: item.dictKey,
        //         value: item.dictValue,
        //         id: item.id,
        //       })
        //     }
        //     getDictionary('goods_expense_rule_type').then(res => {
        //       const resData = res.data
        //       if (resData.code == 200) {
        //         // 处理字典数据
        //         const resList1 = []
        //         for (const item of resData.data) {
        //           resList1.push({
        //             key: item.dictKey,
        //             value: item.dictValue,
        //             id: item.id,
        //           })
        //         }
        //         //
        //         platformExpensesList2({ financeNo, type: 0 }).then(res => {
        //           const { data: resData, code } = res.data
        //           if (code == 200) {
        //             const arr = []
        //             for (const item of resData) {
        //               if (item.calculation == 1) {
        //                 this.calculation = true
        //               }
        //               // 过滤出当前的支付节点
        //               const chargePointFilter = resList.filter(
        //                 itemed => itemed.key == item.feeNode
        //               )
        //               // 过滤出当前的费用类型
        //               const expenseTypeFilter = resList1.filter(
        //                 itemed => itemed.key == item.expenseType
        //               )
        //               arr.push({
        //                 name: item.name,
        //                 expenseTypeStr: expenseTypeFilter[0]
        //                   ? expenseTypeFilter[0].value
        //                   : '',
        //                 chargePoint: chargePointFilter[0]
        //                   ? chargePointFilter[0].value
        //                   : '',
        //                 calculationStr:
        //                   item.calculation != 1
        //                     ? item.feeFormulaName
        //                     : '手动录入',
        //                 amount: item.calculation != 1 ? item.amount : '',
        //                 calculation: item.calculation,
        //               })
        //             }
        //             if (variables.platformExpenses) {
        //               for (const item of variables.platformExpenses) {
        //                 for (const itemed of arr) {
        //                   if (itemed.name == item.name) {
        //                     itemed.amount = item.amount
        //                     break
        //                   }
        //                 }
        //               }
        //             }
        //             this.tableData4 = arr
        //             this.tableData4copy = resData // 专门给后端的数据，不处理
        //           }
        //         })
        //       }
        //     })
        //   }
        // })
      })
    },
    // 质押信息模块数据处理
    async getTableDataFun(pledgeQuota, pledgeCommodityInfo) {
      // 先权限判断完全没权限就不加载后续逻辑
      const { taskFormFilterRArr } = workflowTool.workModulePermission(
        this.taskForm,
        this.financingInforList
        // { iswhitelist: true }
      )
      const cTaskArr = taskFormFilterRArr
      if (!cTaskArr || !cTaskArr.length) return false
      for (const item of cTaskArr) {
        this.financingInforList[item] = true
      }
      // for (const item of taskFormFilterWArr) {
      //   this.financingInforWList[item] = true
      // }
      const codeD = await workflowTool.expGetDictionaryFun('pledge_quota_status', pledgeQuota.quotaType)
      const innerData = []
      if (pledgeCommodityInfo && pledgeCommodityInfo.length) {
        for (const itemC of pledgeCommodityInfo) {
          innerData.push({
            quantityStr: `${itemC.quantity} ${itemC.unitName}`,
            commodityUrl: itemC.commodityUrl,
            commodityName: itemC.name,
            spec: itemC.spec, // 规格
          })
        }
      }

      const arrData = [
        {
          index: 1,
          id: pledgeQuota.id,
          quotaNo: pledgeQuota.quotaNo,
          goodsName: pledgeQuota.goodsName,
          capitalName: pledgeQuota.capitalName,
          availableAmount: pledgeQuota.availableAmount,
          quotaType: (codeD && codeD.label) || '无',
          expireTime: pledgeQuota.expireTime,
          innerData,
        },
      ]
      this.tableData = arrData
    },
    // 货物信息模块数据处理
    getCargoInformationDataFun() {
      // 先权限判断完全没权限就不加载后续逻辑
      const { taskFormFilterRArr } = workflowTool.workModulePermission(
        this.taskForm,
        this.cargoInformationLis
        // { iswhitelist: true }
      )
      const cTaskArr = taskFormFilterRArr
      if (!cTaskArr || !cTaskArr.length) return false
      for (const item of cTaskArr) {
        this.cargoInformationLis[item] = true
      }
      // 表格列是否显示过滤数组赋值
      this.columnList2 = workflowTool.multiFilter(this.columnList2, {
        key: cTaskArr,
      })

      const arrData2 = []
      let numI = 1
      // 根据融资单号查询待入库记录
      detailsByFinanceNo({ financeNo: this.financeNo }).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          for (const item of resData) {
            if (!this.isUntreated && Number(item.readyToStorage) !== 0) {
              this.isUntreated = true
            }
            arrData2.push(Object.assign(item, { index: numI }))
            numI++
          }
        }
      })
      this.tableData2 = arrData2

      // const arrData2 = []
      // let numI = 1
      // for (const item of pledgeCommodityInfo) {
      //   arrData2.push(Object.assign(item, { index: numI }))
      //   numI++
      // }
      // this.tableData2 = arrData2
    },
    // 合同列表获取（可签署）
    getByContractIdFun(pledgeQuota) {
      // 先权限判断完全没权限就不加载后续逻辑
      const { taskFormFilterRArr } = workflowTool.workModulePermission(
        this.taskForm,
        this.contractSignList
        // { iswhitelist: true }
      )
      const cTaskArr = taskFormFilterRArr
      if (!cTaskArr || !cTaskArr.length) return false
      for (const item of cTaskArr) {
        this.contractSignList[item] = true
      }

      const cIds = this.variables.contractId
      // const cIds = this.variables.contractId.length && this.variables.contractId
      const sId = (pledgeQuota && pledgeQuota.capitalId) || pledgeQuota
      if (cIds && sId) {
        this.deptId = sId
        const arrData3 = []
        const paramsed = {
          ids: cIds,
          // ids: cIds.join(),
          signerId: sId,
        }
        {
          getByBackContractId(paramsed).then(({ data }) => {
            if (data.success) {
              const { data: resData } = data
              let num = 1
              this.hasBeenSigned = true // 是否已签署
              for (const item of resData) {
                let statusText = ''
                if (item.status) {
                  switch (item.status) {
                    case 1:
                      statusText = '待签署'
                      break
                    case 2:
                      statusText = '已取消'
                      break
                    case 3:
                      statusText = '已签署'
                      break
                    case 4:
                      statusText = '已失效'
                      break
                    case 5:
                      statusText = '已完成'
                      break
                    case 6:
                      statusText = '签署中'
                      break
                  }
                  if (this.hasBeenSigned && ![3, 5].includes(item.status)) {
                    this.hasBeenSigned = false
                  }
                }
                arrData3.push({
                  id: item.id,
                  serial: String(num),
                  contractTitle: item.contractTitle,
                  contractId: item.contractId,
                  createTime: item.createTime,
                  fileUrl: item.fileUrl,
                  statusText: statusText,
                })
                num++
              }
              this.tableData3 = arrData3
            }
          })
        }
      }
    },
    // 费用试算模块显影
    getTrialCostInformationLisFun() {
      // 先权限判断完全没权限就不加载后续逻辑
      const { taskFormFilterRArr } = workflowTool.workModulePermission(
        this.taskForm,
        this.trialCostInformationLis
        // { iswhitelist: true }
      )
      const cTaskArr = taskFormFilterRArr
      if (!cTaskArr || !cTaskArr.length) return false
      for (const item of cTaskArr) {
        this.trialCostInformationLis[item] = true
      }
    },
    // 评估记录模块
    evaluationRecordFun(pCommoDInfo, isFinancingProportion) {
      // 先权限判断完全没权限就不加载后续逻辑
      const { taskFormFilterRArr, taskFormFilterWArr } = workflowTool.workModulePermission(
        this.taskForm,
        this.evaluationInformation,
        {
          rORW: 'RW',
          // iswhitelist: true,
        }
      )
      const cTaskArr = taskFormFilterRArr
      if (!cTaskArr || !cTaskArr.length) return false
      for (const item of cTaskArr) {
        this.evaluationInformation[item] = true
      }
      for (const item of taskFormFilterWArr) {
        this.evaluationInformationW[item] = true
      }

      if (pCommoDInfo && pCommoDInfo.length) {
        let moneyN = 0
        for (const [index, item] of pCommoDInfo.entries()) {
          moneyN = this.$numJiaFun(moneyN, item.financingTotal)
          this.tableData6.push(
            Object.assign(item, {
              index: index + 1,
              uMax: item.unitPrice.split(' ~ '),
              financingRatio: isFinancingProportion,
            })
          )
        }
        workflowTool.firstObjectFilter(this.columnList6, 'quantity').iLabel += `(${pCommoDInfo[0].unitName})`
        // 调用费用试算方法
        this.loanAmountBlurFun(moneyN)
      }
    },
    // 通过
    handleExamine(pass) {
      // 页面存在评估信息,判断内部对象的purchasePriceSum属性是否存在
      if (this.evaluationInformation.evaluation_modul && pass) {
        const isPurchasePriceSumExist = this.tableData6.every(item => Number(item.purchasePrice) > 0)
        if (pass && !isPurchasePriceSumExist) {
          this.$message.warning('请输入评估单价')
          return false
        }
        // 计算内部对象的financingTotal合计
        const financingTotalSum = this.tableData6.reduce((acc, cur) => {
          return acc + Number(cur.financingTotal)
        }, 0)
        this.financeApplyAfterEnd.amount = financingTotalSum
        this.validataFunction(pass, { financeApply: this.financeApplyAfterEnd })
        return
      }
      // if (this.calculation && pass) {
      //   for (const item of this.tableData4) {
      //     if (!item.amount) {
      //       this.$message.error('请手动录入应付金额')
      //       return
      //     }
      //   }
      // }
      // this.tableData4copy.forEach((item, index) => {
      //   if (item.calculation === 1) {
      //     item.amount = this.tableData4[index].amount
      //   }
      // })
      // if (!this.hasBeenSigned) {
      //   this.$message.warning('请完成合同签署')
      //   return false
      // }
      this.validataFunction(pass)
    },
    // 通过后调取接口函数
    validataFunction(pass, paramsP) {
      this.submitLoading = true
      this.handleCompleteTask(pass, paramsP)
        // this.handleCompleteTask(pass)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    handleChange1() {
      // 质押信息折叠面板收缩控制
      this.change1Type = !this.change1Type
    },
    handleChange2() {
      // 货物信息折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
    handleChange3() {
      // 合同折叠面板收缩控制
      this.change3Type = !this.change3Type
    },
    handleChange4() {
      // 费用试算折叠面板收缩控制
      this.change4Type = !this.change4Type
    },
    handleChange6() {
      // 评估记录折叠面板收缩控制
      this.change6Type = !this.change6Type
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })
      //只保留第一个和最后一个
      sums.forEach((sum, index) => {
        if (index === 0 || index === sums.length - 1) {
          return
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    tableRowClassName({ row }) {
      if (!row.refundTime || row.refundTime === '总计:') {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      const product = { type: this.processGoodsObj.productType, id: this.processGoodsObj.goodsId }
      goodsTypeToPath(product)
    },
    // 签署合同
    signContract(item) {
      // if (this.isUntreated) {
      //   this.$message.warning('等待货物完全入库,在进行合同签署')
      //   return
      // }
      this.signObj = {
        pdfUrl: `${item.row.fileUrl}?time=${new Date().getMilliseconds()}`,
        contractId: item.row.contractId,
        deptId: this.deptId,
      }
    },
    // 预览合同
    viewContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      skipToPreview(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    // 下载合同
    downContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      contractDownload(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    // 放款金额输入框失焦事件
    loanAmountBlurFun(nums) {
      // if (!this.loanAmount) return false
      if (!nums) return false
      const cFinanceApply = this.variables.financeApply
      // let tarGet = false
      const dataP = {
        financeAmount: nums, // 融资金额
        annualInterestRate: cFinanceApply.annualInterestRate, // 年利率
        totalTerm: cFinanceApply.loadTerm, // 总期数
        startTime: dayjs().format('YYYY-MM-DD'), // 开始时间
        refundType: -1, // 还款类型 '1','2','3'
        loadTermUnit: cFinanceApply.loadTermUnit, // 1-天,2-期
        goodsId: cFinanceApply.goodsId,
        goodType: 4,
        chargePoint: 8, // 8 融资申请
        pledgeQuotaId: this.variables.pledge_quota_id,
      }
      pledgeMovablesRepaymentCalculation(dataP).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          // 独立收取
          // const resData = {
          //   parentExpenseTypeVOList: [
          //     {
          //       id: '1622793912335917057',
          //       createUser: '1568205681205551106',
          //       createDept: '1123598813738675202',
          //       createTime: '2023-02-07 11:06:33',
          //       updateUser: '1568205681205551106',
          //       updateTime: '2023-03-03 14:18:07',
          //       status: 1,
          //       isDeleted: 0,
          //       tenantId: '000000',
          //       expenseName: '资方费用',
          //       parentId: '0',
          //       goodsTypeId:
          //         '1468844021325687937,1468844021325687999,1469496942342737474',
          //       goodsType: '1,3,2',
          //       enterpriseType: 2,
          //       accountType: 2,
          //       repaymentType: '1,2',
          //       expenseKey: 1,
          //       sort: 0,
          //       chargeMethod: 1,
          //       enterpriseTypeStr: null,
          //       type: null,
          //       platformExpensesVOS: [
          //         {
          //           id: null,
          //           createUser: null,
          //           createDept: null,
          //           createTime: null,
          //           updateUser: null,
          //           updateTime: null,
          //           status: null,
          //           isDeleted: null,
          //           tenantId: null,
          //           name: '资方费用',
          //           calculation: 2,
          //           feeFormulaName: '借款本金x0.1',
          //           feeNode: 8,
          //           amount: '1222.20',
          //           financeNo: null,
          //           expenseType: 1,
          //           billExpenseNo: null,
          //           type: null,
          //           feeNameType: null,
          //           chargeMethod: null,
          //           expenseTypeId: '1628281332951953409',
          //           expenseTypeNo: null,
          //           collectFeesNode: null,
          //           accountId: null,
          //           costPayMode: null,
          //           chargePointName: null,
          //           expenseName: '资金方服务费',
          //           expenseParentId: null,
          //           expenseParentKey: null,
          //           feeNodeStr: '融资申请',
          //           expenseTypeStr: '服务费',
          //         },
          //       ],
          //     },
          //     {
          //       id: '1622505743195529217',
          //       createUser: '1568205681205551106',
          //       createDept: '1123598813738675202',
          //       createTime: '2023-02-06 16:01:29',
          //       updateUser: '1568205681205551106',
          //       updateTime: '2023-02-15 10:44:44',
          //       status: 1,
          //       isDeleted: 0,
          //       tenantId: '000000',
          //       expenseName: '平台费用',
          //       parentId: '0',
          //       goodsTypeId:
          //         '1468844021325687937,1468844021325687999,1469496942342737474',
          //       goodsType: '1,3,2',
          //       enterpriseType: 1,
          //       accountType: 1,
          //       repaymentType: '1,2',
          //       expenseKey: 2,
          //       sort: 1,
          //       chargeMethod: 1,
          //       enterpriseTypeStr: null,
          //       type: null,
          //       platformExpensesVOS: [
          //         {
          //           id: null,
          //           createUser: null,
          //           createDept: null,
          //           createTime: null,
          //           updateUser: null,
          //           updateTime: null,
          //           status: null,
          //           isDeleted: null,
          //           tenantId: null,
          //           name: '平台服务费',
          //           calculation: 2,
          //           feeFormulaName: '借款本金x0.2',
          //           feeNode: 8,
          //           amount: '2444.40',
          //           financeNo: null,
          //           expenseType: 1,
          //           billExpenseNo: null,
          //           type: null,
          //           feeNameType: null,
          //           chargeMethod: null,
          //           expenseTypeId: '1625073785142202370',
          //           expenseTypeNo: null,
          //           collectFeesNode: null,
          //           accountId: null,
          //           costPayMode: null,
          //           chargePointName: null,
          //           expenseName: '应收账款-服务费',
          //           expenseParentId: null,
          //           expenseParentKey: null,
          //           feeNodeStr: '融资申请',
          //           expenseTypeStr: '服务费',
          //         },
          //       ],
          //     },
          //   ],
          //   showRepaymentPlan: {
          //     stagRecords: [
          //       {
          //         term: '1',
          //         monthlySupply: '12230.07',
          //         monthlyPrincipal: '12222',
          //         planInterest: '8.07',
          //         discountAmount: null,
          //         undertakeAccountId: null,
          //         realMonthlyInterest: '8.07',
          //         penaltyInterest: null,
          //         overdueInterest: null,
          //         processInterest: null,
          //         principalSurplus: null,
          //         refundTime: '2023-04-26',
          //         planFeeList: [],
          //       },
          //     ],
          //     dayRate: '0.033',
          //     yearRate: '12.00',
          //   },
          // }
          // 统一清分
          // const resData = {
          //   parentExpenseTypeVOList: [],
          //   showRepaymentPlan: {
          //     stagRecords: [
          //       {
          //         term: '1',
          //         monthlySupply: '43688.23',
          //         monthlyPrincipal: '33333',
          //         planInterest: '22.00',
          //         discountAmount: null,
          //         undertakeAccountId: null,
          //         realMonthlyInterest: '22.00',
          //         penaltyInterest: null,
          //         overdueInterest: null,
          //         processInterest: null,
          //         principalSurplus: null,
          //         refundTime: '2023-04-26',
          //         planFeeList: [
          //           {
          //             id: null,
          //             createUser: null,
          //             createDept: null,
          //             createTime: null,
          //             updateUser: null,
          //             updateTime: null,
          //             status: null,
          //             isDeleted: null,
          //             tenantId: null,
          //             planId: null,
          //             iouNo: null,
          //             period: null,
          //             repaymentTime: null,
          //             userId: null,
          //             financeApplyId: null,
          //             customerGoodsId: null,
          //             goodsId: null,
          //             repaymentStatus: null,
          //             iouId: null,
          //             goodsType: null,
          //             amount: '3333.30',
          //             feeName: '资金方服务费',
          //             feeTypeName: null,
          //             expenseTypeId: null,
          //             collectFeeMethod: null,
          //             expenseKey: null,
          //             relationExpensesId: null,
          //           },
          //           {
          //             id: null,
          //             createUser: null,
          //             createDept: null,
          //             createTime: null,
          //             updateUser: null,
          //             updateTime: null,
          //             status: null,
          //             isDeleted: null,
          //             tenantId: null,
          //             planId: null,
          //             iouNo: null,
          //             period: null,
          //             repaymentTime: null,
          //             userId: null,
          //             financeApplyId: null,
          //             customerGoodsId: null,
          //             goodsId: null,
          //             repaymentStatus: null,
          //             iouId: null,
          //             goodsType: null,
          //             amount: '6666.60',
          //             feeName: '应收账款-服务费',
          //             feeTypeName: null,
          //             expenseTypeId: null,
          //             collectFeeMethod: null,
          //             expenseKey: null,
          //             relationExpensesId: null,
          //           },
          //           {
          //             id: null,
          //             createUser: null,
          //             createDept: null,
          //             createTime: null,
          //             updateUser: null,
          //             updateTime: null,
          //             status: null,
          //             isDeleted: null,
          //             tenantId: null,
          //             planId: null,
          //             iouNo: null,
          //             period: null,
          //             repaymentTime: null,
          //             userId: null,
          //             financeApplyId: null,
          //             customerGoodsId: null,
          //             goodsId: null,
          //             repaymentStatus: null,
          //             iouId: null,
          //             goodsType: null,
          //             amount: '333.33',
          //             feeName: '技术服务费',
          //             feeTypeName: null,
          //             expenseTypeId: null,
          //             collectFeeMethod: null,
          //             expenseKey: null,
          //             relationExpensesId: null,
          //           },
          //         ],
          //       },
          //     ],
          //     dayRate: '0.033',
          //     yearRate: '12.00',
          //   },
          // }
          {
            const dat = resData
            this.rateObj = {
              dailyInterestRate: dat.showRepaymentPlan.dayRate || 0,
              annualInterestRate: dat.showRepaymentPlan.yearRate || 0,
            }
            // 记录那些是一次性付清的
            // let Map = {}
            // const chargeMethod = this.variables.chargeMethod
            // if (chargeMethod == 1) {
            //   dat.showRepaymentPlan.stagRecords[0].planFeeList.forEach(
            //     (item, index) => {
            //       Map[item.feeName] = {
            //         index,
            //         collectFeeMethod: item.collectFeeMethod,
            //         amount: item.amount,
            //       }
            //       // 第一次加载添加，第二次不添加
            //       if (!tarGet) {
            //         this.columnList4.push({
            //           iLabel: item.feeName,
            //           iProp: 'amount' + index,
            //         })
            //       }
            //     }
            //   )
            //   tarGet = true
            // } else {
            dat.expenseOrderDetailFinanceVos.forEach(item => {
              let amount = item.expenseOrderDetailList.reduce((prve, next) => {
                next.repaymentTerm = next.repaymentTerm ? next.repaymentTerm + '期' : '--'
                if (next.feeFormulaName == '手填') {
                  next.amount = '人工计算'
                  return prve
                }
                const prveA = this.$numJiaFun(prve, next.amount)
                next.amountStr = `￥${formatMoney(next.amount)}`
                return prveA
              }, 0)
              item.expenseOrderDetailList.push({
                expenseTypeStr: '总计:',
                feeNameType: '',
                feeNodeStr: '',
                feeFormulaName: '',
                amountNum: amount,
                amountStr: `￥${formatMoney(amount)}`,
              })
            })
            this.otherFeeData = dat.expenseOrderDetailFinanceVos
            // }
            this.tableData4 = []
            // let index = 0
            for (const item of dat.showRepaymentPlan.stagRecords) {
              if (cFinanceApply.loadTermUnit) {
                this.allMonrySum(item)
                this.tableData4.push({
                  // name: '1期',
                  refundTime: item.refundTime,
                  monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
                  monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
                  planInterest: `￥${formatMoney(item.planInterest)}`,
                })
                this.tableData4.push({
                  // name: '总计:',
                  refundTime: '总计:',
                  monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
                  monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
                  planInterest: `￥${formatMoney(item.planInterest)}`,
                })
                //  资方统一清分动态费用 随借随还的逻辑
                // if (chargeMethod == 1) {
                //   item.planFeeList.forEach((citem, cindex) => {
                //     this.tableData4[0][`amount${cindex}`] = `￥${formatMoney(
                //       citem.amount
                //     )}`
                //     this.tableData4[1][`amount${cindex}`] = `￥${formatMoney(
                //       citem.amount
                //     )}`
                //   })
                // }
              } else {
                if (!item.term) {
                  this.allMonrySum(item)
                }
                this.tableData4.push({
                  name: item.term ? `${item.term}期` : '总计:',
                  refundTime: item.refundTime,
                  monthlySupply: `￥${formatMoney(item.monthlySupply)}`,
                  monthlyPrincipal: `￥${formatMoney(item.monthlyPrincipal)}`,
                  planInterest: `￥${formatMoney(item.planInterest)}`,
                })
                // if (
                //   index != dat.showRepaymentPlan.stagRecords.length - 1 &&
                //   chargeMethod == 1
                // ) {
                //   if (
                //     index == 0 ||
                //     item.planFeeList.length ==
                //       dat.showRepaymentPlan.stagRecords[0].length
                //   ) {
                //     item.planFeeList.forEach((citem, cindex) => {
                //       this.tableData4[index][
                //         `amount${cindex}`
                //       ] = `￥${formatMoney(citem.amount)}`
                //     })
                //   } else {
                //     for (const key in Map) {
                //       this.tableData4[index][
                //         `amount${Map[key].index}`
                //       ] = `￥${formatMoney(0)}`
                //     }
                //     item.planFeeList.forEach(citem => {
                //       if (Map[citem.feeName]) {
                //         let data = Map[citem.feeName]
                //         this.tableData4[index][
                //           `amount${data.index}`
                //         ] = `￥${formatMoney(citem.amount)}`
                //       }
                //     })
                //   }
                // } else if (
                //   index == dat.showRepaymentPlan.stagRecords.length - 1 &&
                //   chargeMethod == 1
                // ) {
                //   for (const key in Map) {
                //     // 一次性付清
                //     if (Map[key].collectFeeMethod == 1) {
                //       this.tableData4[index][
                //         `amount${Map[key].index}`
                //       ] = `￥${formatMoney(Map[key].amount)}`
                //     } else {
                //       // 分期
                //       this.tableData4[index][`amount${Map[key].index}`] = `￥${(
                //         formatMoney(Map[key].amount) * index
                //       ).toFixed(2)}`
                //     }
                //   }
                // }
                // index++
              }
            }
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.table-goodsInfo {
  display: flex;
  align-items: center;
  & img {
    width: 60px;
    height: 43px;
    object-fit: cover;
    margin-right: 8px;
  }
  .table-goodsInfo-right {
    .goodsInfo-right-top {
      cursor: pointer;
      width: 196px;
      color: #697cff;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 1px;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .goodsInfo-right-bottom {
      width: 196px;
      line-height: 20px;
      color: rgba(141, 141, 141, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        & > span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
        & > span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

// .loan-amount-box {
//   display: flex;
//   align-items: center;
//   margin-top: 19px;

//   &_asterisk {
//     color: red;
//     font-size: 20px;
//     transform: translateY(4px);
//     margin-right: 2px;
//   }

//   &_text {
//     font-size: 15px;
//     font-weight: bold;
//   }

//   ::v-deep {
//     .el-input__inner {
//       text-align: left;
//     }
//     .is-disabled .el-input__inner {
//       color: #000;
//     }
//   }
// }

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

.action-bar {
  &_view {
    color: #3894ff;
    margin-right: 10px;
    cursor: pointer;
  }

  &_down {
    color: #3894ff;
    cursor: pointer;
  }

  span[iDisabled='true'] {
    color: #dddee0;
    cursor: help;
    // cursor: no-drop;
  }
}

.other-f-box {
  &_father-b {
    margin-top: 20px;
  }

  &_childer-name {
    color: #7d7d7d;
    font-size: 14px;
    font-weight: 600;
  }

  .tag-styles-box {
    padding: 2px 13px;
    box-sizing: border-box;
    border-radius: 20px;
    border: 1px solid #7d7d7d;
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    margin-top: 13px;

    .table-title-box {
      height: 22px;
      // margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 15px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          // height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
