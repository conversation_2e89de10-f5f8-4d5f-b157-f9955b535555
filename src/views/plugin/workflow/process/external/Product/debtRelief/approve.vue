<template>
  <div class="creditLimitFinancing">
    <basic-container v-if="!isApply">
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '减免申请'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 产品信息 -->
      <basic-container>
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">
              <span>产品信息</span>
              <span class="long-string" />
              <div class="serial-number">
                <span>融资编号：</span>
                <span>{{ fNo }}</span>
              </div>
            </h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{
                      processGoodsObj.goodsName
                    }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">{{
                processGoodsObj.type == 1 ? '应收账款质押' : '代采融资'
              }}</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item
                  v-for="item in tableData1"
                  :key="item.id"
                  :label="item.label"
                  >{{ item.value }}</el-descriptions-item
                >
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 订单信息 -->
      <basic-container>
        <el-collapse v-model="activeNames1_1" @change="handleChange1_1">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change1_1Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>订单信息</span>
                    <!-- <span class="long-string" /> -->
                  </h1>
                </div>
              </div>
            </template>
            <!-- 展示表单 -->
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item
                  v-for="item in tableData2_1"
                  :key="item.id"
                  :label="item.label"
                >
                  <span>
                    {{ item.value }}
                  </span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 已还明细 -->
      <basic-container v-if="false">
        <el-collapse v-model="activeNames2" @change="handleChange2">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change2Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>已还明细</span>
                    <!-- <span class="long-string" />
                    <span class="interest-rate"
                      >日利率{{ allDailyInterestRate }}%（年化利率{{
                        allAnnualInterestRate
                      }}%）</span
                    > -->
                  </h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <!-- <div class="table-title-box">
                <div class="title-left-box">
                  <span>资方费用</span>
                  <span />
                  <span
                    >日利率{{ dailyInterestRate }}%（年化利率{{
                      annualInterestRate
                    }}%）</span
                  >
                </div>
                <div class="title-right-box">计费方式：{{ chargeMode }}</div>
              </div> -->
              <el-table
                ref="table2"
                :data="tableData3"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <!-- <el-table-column
                  prop="period"
                  label="#"
                  width="110"
                  align="center"
                  v-if="bankInterestList.reimburS_No"
                >
                </el-table-column> -->
                <el-table-column
                  prop="repaymentTime"
                  label="还款日期"
                  v-if="bankInterestList.reimburS_repaymentTime"
                >
                </el-table-column>
                <el-table-column
                  prop="repaymentNo"
                  label="还款单号"
                  v-if="bankInterestList.reimburS_repaymentNo"
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.repaymentNo }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="periodDes"
                  label="所属期数"
                  v-if="bankInterestList.reimburS_periodDes"
                >
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.periodDes }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="totalAmount"
                  label="还款总额"
                  v-if="bankInterestList.reimburS_totalAmount"
                >
                  <template slot-scope="scope">
                    <span> ￥{{ scope.row.totalAmount | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="principal"
                  label="本金(元)"
                  v-if="bankInterestList.reimburS_principal"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.principal | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="interest"
                  label="利息(元)"
                  v-if="bankInterestList.reimburS_interest"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.interest | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="serviceCharge"
                  label="手续费(元)"
                  v-if="bankInterestList.reimburS_serviceCharge"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.serviceCharge | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="penaltyInterest"
                  label="逾期罚息(元)"
                  v-if="bankInterestList.reimburS_penaltyInterest"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.penaltyInterest | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥{{ sum1 | formatMoney }} </span>
              </div>
            </div> -->
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 还款明细 -->
      <ReturnedDetails :fNo="fNo" />

      <!-- 减免信息：申请用 -->
      <basic-container v-if="isApply">
        <el-collapse v-model="activeNames4" @change="handleChange4">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change4Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>减免信息</span>
                  </h1>
                </div>
              </div>
            </template>

            <avue-form
              ref="myForm"
              class="management-accounts"
              v-model="manForm"
              :key="myFormReload"
              :option="derateFOption"
              @submit="mySubmit"
            >
              <template slot="attachList" slot-scope="Mrow">
                <BaseImageUpload
                  :imgData.sync="manForm.attachList"
                  :disabled="Mrow.disabled"
                />
                <p style="color: #87898c">
                  只能上传jpg/png/jpeg/pdf文件，且不超过2M
                </p>
              </template>
            </avue-form>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <template v-else>
        <!-- 减免申请信息：审核用 -->
        <basic-container>
          <el-collapse v-model="activeNames4_1" @change="handleChange4_1">
            <el-collapse-item name="furtherInformation">
              <!-- title的solt -->
              <template slot="title">
                <div class="fromHeader">
                  <div class="fromLeft">
                    <i
                      class="el-icon-caret-bottom"
                      :class="{
                        'i-active': change4_1Type,
                      }"
                    ></i>
                    <h1 class="fromLeft-title">
                      <span>减免申请信息</span>
                      <!-- <span class="long-string" /> -->
                    </h1>
                  </div>
                </div>
              </template>
              <!-- 展示表单 -->
              <div class="descriptions-for-box">
                <el-descriptions title="" :column="3" border>
                  <el-descriptions-item
                    v-for="item in tableData4_1"
                    :key="item.id"
                    :label="item.label"
                  >
                    <span v-if="item.key !== 'loanAlteration_attachList'">
                      {{ item.value }}
                    </span>
                    <div v-else class="my-basebox">
                      <BaseImageUpload
                        :imgData.sync="item.value"
                        :disabled="true"
                      />
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-collapse-item>
          </el-collapse>
        </basic-container>

        <!-- 减免调整 -->
        <basic-container>
          <el-collapse v-model="activeNames4" @change="handleChange4">
            <el-collapse-item name="furtherInformation">
              <!-- title的solt -->
              <template slot="title">
                <div class="fromHeader">
                  <div class="fromLeft">
                    <i
                      class="el-icon-caret-bottom"
                      :class="{
                        'i-active': change4Type,
                      }"
                    ></i>
                    <h1 class="fromLeft-title">
                      <span>减免调整</span>
                    </h1>
                  </div>
                </div>
              </template>

              <avue-form
                ref="myForm"
                class="management-accounts"
                :key="myFormReload2"
                v-model="manForm"
                :option="derateFOption"
                @submit="mySubmit"
              >
              </avue-form>
            </el-collapse-item>
          </el-collapse>
        </basic-container>
      </template>

      <!-- 减免测算 -->
      <basic-container>
        <el-collapse v-model="activeNames5" @change="handleChange5">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change5Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>
                      {{ isApply ? '历史变更' : '减免测算' }}
                    </span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="order-header-container">
              <el-radio-group v-model="processStatus" @change="handleTabButton">
                <el-radio-button
                  v-for="item in listRecords"
                  :key="item.id"
                  :label="item.label"
                >
                  {{ item.value }}
                </el-radio-button>
              </el-radio-group>
            </div>
            <div class="boxs">
              <div class="boxs-to-apply-for-product">
                <!-- <h1 class="boxs-to-apply-for-product-h1">合同签署</h1> -->
                <div class="boxs-to-apply-for-product-body">
                  <!-- :max-height="240" -->
                  <el-table
                    ref="table4"
                    :data="tableData5"
                    v-loading="table5Loading"
                    class="table-border-style"
                    :row-class-name="tableRowClassName2"
                  >
                    <el-table-column
                      v-if="tableData5Readable.contractS_repaymentTime"
                      prop="repaymentTime"
                      label="还款日期"
                      min-width="250"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractS_period"
                      prop="period"
                      label="还款期数"
                      min-width="250"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <div v-if="scope.row.period">
                          {{ scope.row.period }}期
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractS_totalAmount"
                      prop="totalAmount"
                      label="还款总额"
                      min-width="250"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <div>￥{{ scope.row.totalAmount }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractS_principal"
                      prop="principal"
                      label="本金"
                      min-width="250"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <div>￥{{ scope.row.principal }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="tableData5Readable.contractS_interest"
                      prop="planInterest"
                      label="利息"
                      min-width="250"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <div>￥{{ scope.row.planInterest }}</div>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column
                      v-if="tableData5Readable.contractS_serviceFee"
                      prop="serviceFee"
                      label="服务费"
                      min-width="250"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <div>￥{{ scope.row.serviceFee }}</div>
                      </template>
                    </el-table-column> -->
                    <el-table-column
                      v-if="
                        tableData5Readable.contractS_repaymentStatus &&
                        processStatus !== 't1'
                      "
                      prop="repaymentStatus"
                      label="还款状态"
                      min-width="250"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <div v-if="scope.row.repaymentStatus">
                          {{
                            scope.row.repaymentStatus === 1 ? '待结清' : '结清'
                          }}
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div v-if="alterationReason">
                    变更原因：
                    {{ alterationReason }}
                  </div>
                </div>
              </div>

              <div v-if="platformFeeList.length">
                <div
                  class="table-top refund"
                  v-for="item in platformFeeList"
                  :key="item.id"
                >
                  <div class="chain-line" />
                  <div class="table-title-box" style="margin-top: -10px">
                    <div class="title-left-box">
                      <span>{{ item.parenExpenseName }}</span>
                    </div>
                  </div>
                  <el-table
                    ref="table3"
                    :data="item.expenseOrderDetailList"
                    :summary-method="getSummaries"
                    show-summary
                    style="width: 100%; margin-top: 13px"
                    class="table-border-style"
                  >
                    <el-table-column prop="expenseTypeStr" label="费用名称">
                    </el-table-column>
                    <!-- <el-table-column
                      prop="feeNameType"
                      label="费用类型"
                    >
                      <template slot-scope="scope">
                        <span class="border-box">
                          {{ scope.row.feeNameType }}
                        </span>
                      </template>
                    </el-table-column> -->
                    <el-table-column prop="repaymentTerm" label="期数">
                    </el-table-column>
                    <el-table-column prop="feeNodeStr" label="计算节点">
                      <template slot-scope="scope">
                        <span class="border-box">
                          {{ scope.row.feeNodeStr }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="collectFeesNodeStr" label="收费节点">
                      <template slot-scope="scope">
                        <span class="border-box">
                          {{ scope.row.collectFeesNodeStr }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="feeFormulaName" label="计费方式">
                    </el-table-column>
                    <el-table-column prop="amount" label="应付金额">
                      <template slot-scope="scope">
                        <span>￥{{ scope.row.amount | formatMoney }} </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>

              <div class="fees-at-box">
                <div class="fees-left-at">
                  以上仅为试算结果，以实际放款为准～
                </div>
                <div class="fees-right-at">
                  应还总额：
                  <span> ￥{{ sum | formatMoney }} </span>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 批复意见 -->
      <basic-container v-if="!isApply">
        <div class="approval-container">
          <span class="title">批复意见：</span>
          <el-input
            class="value"
            type="textarea"
            :rows="5"
            resize="none"
            placeholder="请输入批复意见"
            v-model="comment"
          >
          </el-input>
        </div>
      </basic-container>
    </template>

    <!-- 底部按钮 -->
    <wf-button
      v-if="!isApply"
      class="custom-button"
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></wf-button>
    <!-- 脚脚 -->
    <template v-else>
      <div class="occupied-box" />
      <div class="footer-container">
        <el-button
          type="success"
          class="nextBtn btn-bg-color-blue"
          :loading="subbmitType"
          @click="nextBtnFun"
        >
          提 交
        </el-button>
      </div>
    </template>
    <!-- <FilePreview :url="pdfSrc" /> -->
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
// import FilePreview from '@/components/file-preview'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import BaseImageUpload from '@/components/BaseImageUpload'
import ReturnedDetails from '@/views/plugin/workflow/process/components/ReturnedDetails'
import { formatMoney } from '@/util/filter.js'
import { dateFormat } from '@/util/date'
import {
  getDictionary,
  repaymentCalculation,
  platformExpensesList,
  getByContractId,
  loanDerateAlterationDetail,
  getRepaymentFinanceIdChuangcai,
  loanDerateAlterationApply,
  loanDerateAlterationCalculate,
  getRepaymentPlanFee,
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import { ids } from '@/api/resource/attach.js'

const DIC = {
  // 减免类型
  type: [
    {
      label: '当前减免',
      value: 1,
    },
    {
      label: '剩余待还全部减免',
      value: 2,
    },
  ],
  // 减免方式
  mode: [
    {
      label: '减免比例',
      value: 1,
    },
    {
      label: '固定金额',
      value: 2,
    },
  ],
  // 减免金额类型
  typeOfamount: [
    // {
    //   label: '减免本金',
    //   value: 1,
    // },
    {
      label: '减免利息',
      value: 2,
    },
    {
      label: '减免服务费',
      value: 3,
    },
  ],
}

export default {
  mixins: [customExForm],
  components: {
    WfButton,
    WfFlow,
    // FilePreview,
    BaseImageUpload,
    ReturnedDetails,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          this.processInsIdRoot = processInsId
          if (taskId && processInsId) {
            this.getDetail2(taskId, processInsId)
            // this.getDetail(taskId, processInsId)
          }
        }
      },
      immediate: true,
    },
    activeNames2() {
      setTimeout(() => {
        this.$refs.table2.$ready = false // 解决element组件折叠面板与表单冲突bug
      }, 50)
    },
    // 附件上传完成校验
    'manForm.attachList': {
      handler(val) {
        if (val && this.$refs.myForm) {
          setTimeout(() => {
            this.$refs.myForm.validateField('attachList')
          }, 50)
        }
      },
    },
    platformFeeList: {
      handler(val) {
        let num = this.sum1
        if (val && val.length) {
          for (const item of val) {
            const cunArr = item.expenseOrderDetailList
            if (cunArr && cunArr.length) {
              for (const cItem of cunArr) {
                if (cItem.amount) {
                  num = this.$numJiaFun(num, cItem.amount)
                }
              }
            }
          }
        }
        this.sum = num
      },
      immediate: false,
      deep: true,
    },
    allAppend: {
      handler(val) {
        const isYuan = val === '元' ? true : false
        const dongtaiFTagerArr = this.dongtaishuruFeiArr.map(
          item => item.expenseTypeId
        )
        const arr = [
          // 'deratePrincipal',
          'derateInterest',
          // 'derateServiceCharge',
          ...dongtaiFTagerArr,
        ]
        for (const item of this.derateFOption.column) {
          if (arr.includes(item.prop)) {
            item.append = val
            item.tip = isYuan ? item.tipNotShow : false
          }
        }
      },
    },
  },
  data() {
    return {
      fNo: this.$route.query.fNo,
      fId: this.$route.query.fId,
      baseRate: '',
      reFinObj: {},
      subbmitType: false,
      manForm: {},
      derateFOption: {
        emptyBtn: false,
        submitBtn: true,
        submitText: '减免测算',
        labelPosition: 'right',
        labelWidth: 190,
        gutter: 100,
        column: [
          {
            label: '减免类型',
            key: 'regulating_interest_rate',
            placeholder: '请选择减免类型',
            prop: 'type',
            type: 'select',
            disabled: false,
            display: true,
            span: 12,
            row: true,
            rules: [
              {
                required: true,
                message: '请选择减免类型',
                trigger: 'change',
              },
            ],
            dicData: DIC.type,
            change: ({ value }) => {
              if (this.isApply && value) {
                this.fengzhuangdongtaibiaodanShowFun(value)
              }
            },
          },
        ],
      },
      processStatus: 't1',
      listRecords: [
        {
          id: 1,
          label: 't1',
          value: '当前减免试算',
        },
      ],
      alterationReason: '',
      activeName: 'first',
      defaults: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      processGoodsObj: {},
      activeNames1_1: [],
      activeNames2: [],
      activeNames3: [],
      activeNames4: [],
      activeNames4_1: [],
      activeNames5: [],
      change1_1Type: false,
      change2Type: false,
      change3Type: false,
      change4Type: false,
      change4_1Type: false,
      change5Type: false,
      table5Loading: false,
      tableData1: [],
      tableData2_1: [],
      tableData3: [],
      tableData4: [],
      tableData4_1: [],
      tableData5: [],
      chargeMode: '先息后本',
      allDailyInterestRate: 0,
      allAnnualInterestRate: 0,
      dailyInterestRate: 0,
      annualInterestRate: 0,
      // 其他费用的
      platformFeeList: [],
      sum1: 0,
      sum: 0,
      allAppend: '',
      dongtaishuruFeiArr: [],
      formLock: false,
      shenpiGiveHouduan: {},
      myFormReload: Math.random(),
      myFormReload2: Math.random(),
      // 显影控制
      bankInterestList: {
        reimburS_No: true,
        reimburS_repaymentTime: true,
        reimburS_repaymentNo: true,
        reimburS_periodDes: true,
        reimburS_totalAmount: true,
        reimburS_principal: true,
        reimburS_interest: true,
        reimburS_serviceCharge: true,
        reimburS_penaltyInterest: true,
      },
      tableData5Readable: {
        contractS_repaymentTime: true,
        contractS_period: true,
        contractS_totalAmount: true,
        contractS_principal: true,
        contractS_interest: true,
        contractS_serviceFee: true,
        contractS_repaymentStatus: true,
      },
    }
  },
  computed: {
    // 是申请还是审批
    isApply() {
      return this.$route.query.fNo ? true : false
    },
  },
  created() {
    if (this.isApply) {
      this.mrJiangGetDetail()
      this.activeNames4 = ['furtherInformation']
      this.change4Type = true
    }
  },
  methods: {
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm
        // 流程产品信息
        const {
          processGoodsInfo,
          financeNo,
          financeApply,
          financeApplyId,
          billExpenseOrder,
        } = variables
        this.processGoodsObj = processGoodsInfo
        this.financeNo = financeNo

        // 流程产品信息
        let usage = []
        getDictionary('finance_apply_loan_usage').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            // 过滤出当前借款用途
            usage = resList.filter(item => item.key == financeApply.loanUsage)
            const data = [
              {
                id: 1,
                label: '融资金额',
                value: `${this.$numChuFun(financeApply.amount, 10000)}万元`,
                key: 'financingDemand_money',
              },
              {
                id: 2,
                label: '借款期限',
                value: `${financeApply.loadTerm}${
                  financeApply.loadTermUnit == 2 ? '个月' : '天'
                }`,
                key: 'financingDemand_deadline',
              },
              {
                id: 3,
                label: '借款用途',
                value: `${usage[0].value}`,
                key: 'financingDemand_use',
              },
            ]

            // 是否可读
            // const dataKey = data.map(item => item.key)
            // const taskFormFilter = taskForm.filter(item =>
            //   dataKey.includes(item.id)
            // )
            // const taskFormId = taskFormFilter.map(item => {
            //   if (item.readable) {
            //     return item.id
            //   }
            // })
            // const dataFilter = data.filter(item =>
            //   taskFormId.includes(item.key)
            // )
            this.tableData2_1 = data
          }
        })

        // 还款计划
        // this.allAnnualInterestRate =
        //   Number(financeApply.annualInterestRate) +
        //   Number(financeApply.serviceRate) // 总年利率
        // this.allDailyInterestRate = (
        //   Number(this.allAnnualInterestRate) / 360
        // ).toFixed(3) // 总日利率
        this.dailyInterestRate = financeApply.dailyInterestRate // 银行日利率
        this.annualInterestRate = financeApply.annualInterestRate // 银行年利率
        if (processGoodsInfo.repaymentType === 1) {
          getDictionary('goods_billing_method').then(res => {
            const resData = res.data
            if (resData.code == 200) {
              // 处理字典数据
              const resList = []
              for (const item of resData.data) {
                resList.push({
                  key: item.dictKey,
                  value: item.dictValue,
                  id: item.id,
                })
              }
              // 过滤出当前的计费方式
              this.chargeMode = resList.filter(
                itemed => itemed.key == financeApply.repaymentMode
              )[0].value
            }
          })
        } else {
          this.chargeMode = '随借随还'
        }

        const taskArrKey = [
          'reimbursementTrial_periods',
          'reimbursementTrial_repaymentDate',
          'reimbursementTrial_totalShouldAlso',
          'reimbursementTrial_repaymentPrincipal',
          'reimbursementTrial_shouldAlsoInterest',
        ]
        const taskFormFilter = taskForm.filter(
          item => taskArrKey.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter) {
          this.bankInterestList[item.id] = true
        }
        // 还款试算list数据
        repaymentCalculation(financeApplyId).then(res => {
          const { data: resData, code } = res.data
          if (code == 200) {
            const arr = []
            for (const item of resData.stagRecords) {
              if (resData.dayRate) {
                if (item.term) {
                  arr.push({
                    term: `${item.term}期`,
                    refundTime: item.refundTime,
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    monthlyInterest: item.monthlyInterest,
                  })
                } else {
                  arr.push({
                    term: '总计',
                    refundTime: '',
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    monthlyInterest: item.monthlyInterest,
                  })
                  this.allMonrySum(item)
                }
              } else {
                arr.push({
                  term: '1期',
                  refundTime: item.refundTime,
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  monthlyInterest: item.monthlyInterest,
                })
                arr.push({
                  term: '总计',
                  refundTime: '',
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  monthlyInterest: item.monthlyInterest,
                })
                this.allMonrySum(item)
              }
            }
            this.tableData3 = arr
          }
        })

        // 合同签署list
        const cotractNameList = [
          'contractSigning_serialNumber',
          'contractSigning_contractNumber',
          'contractSigning_contractTitle',
          'contractSigning_creationTime',
          'contractSigning_signTheState',
          'contractSigning_operation',
        ]
        const cotractNameListFilter = taskForm.filter(item =>
          cotractNameList.includes(item.id)
        )
        const readableList = cotractNameListFilter.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        for (const key in this.tableData5Readable) {
          readableList.forEach(item => {
            if (item == key) {
              this.tableData5Readable[key] = true
            }
          })
        }
        const paramsed = {
          ids: variables.contractId,
        }
        if (variables.contractId) {
          getByContractId(paramsed).then(res => {
            const resData = res.data
            if (resData.code == 200) {
              let num = 1
              for (const item of resData.data) {
                let statusText = ''
                if (item.status) {
                  switch (item.status) {
                    case 1:
                      statusText = '待签署'
                      break
                    case 2:
                      statusText = '已取消'
                      break
                    case 3:
                      statusText = '已签署'
                      break
                    case 4:
                      statusText = '已失效'
                      break
                    case 5:
                      statusText = '已完成'
                      break
                    case 6:
                      statusText = '签署中'
                      break
                  }
                }
                this.tableData5.push({
                  id: item.id,
                  serial: String(num),
                  contractTitle: item.contractTitle,
                  contractId: item.contractId,
                  createTime: item.createTime,
                  statusText: statusText,
                })
                num++
              }
            }
          })
        }

        // 缴纳费用
        // 是否可读
        const taskArrKey2 = [
          'payAFee_costOfName',
          'payAFee_typeOfExpense',
          'payAFee_payTheNode',
          'payAFee_chargeMode',
          'payAFee_amountPayable',
          'payAFee_orderStatus',
          'payAFee_orderStatusRedact',
          'payAFee_orderStatusLook',
          'payAFee_checkPaymentVoucher',
        ]
        const taskFormFilter2 = taskForm.filter(
          item => taskArrKey2.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter2) {
          this.costPlatformList[item.id] = true
        }
        getDictionary('goods_expense_rule_fee_node').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            getDictionary('goods_expense_rule_type').then(res => {
              const resData = res.data
              if (resData.code == 200) {
                // 处理字典数据
                const resList1 = []
                for (const item of resData.data) {
                  resList1.push({
                    key: item.dictKey,
                    value: item.dictValue,
                    id: item.id,
                  })
                }
                //
                platformExpensesList(financeNo).then(res => {
                  const { data: resData, code } = res.data
                  if (code == 200) {
                    const arr = []
                    for (const item of resData) {
                      if (item.calculation == 1) {
                        this.calculation = true
                      }
                      // 过滤出当前的支付节点
                      const chargePointFilter = resList.filter(
                        itemed => itemed.key == item.feeNode
                      )
                      // 过滤出当前的费用类型
                      const expenseTypeFilter = resList1.filter(
                        itemed => itemed.key == item.expenseType
                      )
                      arr.push({
                        name: item.name,
                        expenseTypeStr: expenseTypeFilter[0].value,
                        chargePoint: chargePointFilter[0].value,
                        calculationStr:
                          item.calculation != 1
                            ? item.feeFormulaName
                            : '手动录入',
                        amount: item.amount,
                        calculation: item.calculation,
                      })
                    }
                    if (variables.platformExpenses) {
                      for (const item of variables.platformExpenses) {
                        for (const itemed of arr) {
                          if (itemed.name == item.name) {
                            itemed.amount = item.amount
                            break
                          }
                        }
                      }
                    }
                    this.tableData4 = arr
                  }
                })
              }
            })
          }
        })

        // 获取支付凭证
        if (variables.attach_id) {
          ids(variables.attach_id).then(res => {
            const { data: resData, code } = res.data
            if (code == 200) {
              for (const item of resData) {
                if (item.extension !== 'pdf') {
                  //
                  this.imageArr.push({
                    url: item.link,
                  })
                } else {
                  this.pdfArr.push({
                    url: item.link,
                  })
                }
              }
            }
          })
        }
        // 获取订单状态并回显（dialog）
        const statusSession = sessionStorage.getItem(
          `statusForm${this.processInsIdRoot}`
        )
        if (statusSession) {
          this.statusForm = JSON.parse(statusSession)
          this.orderTextFun(this.statusForm.paymentStatus)
          return
        } else if (billExpenseOrder) {
          this.statusForm = billExpenseOrder
          this.orderTextFun(billExpenseOrder.paymentStatus)
          return
        }
        this.orderTextFun(1)
      })
    },
    // 获取任务详情
    getDetail2(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        const { form, process } = res
        const { financeNo, financeId } =
          process.variables.derate_alteration_delay
        const { taskForm } = form
        this.taskForm = taskForm

        // 控制减免调整信息是否可编辑
        const tArr = [
          'derate_amount_type',
          'derate_principal',
          'derate_interest',
          'derate_service_charge',
        ]
        for (const item of tArr) {
          for (const items of taskForm) {
            if (item === items.id) {
              for (const mitem of this.derateFOption.column) {
                if (mitem.key === item) {
                  // mitem.display = items.readable ? true : false
                  mitem.disabled = items.writeable ? false : true
                  break
                }
              }
              break
            }
          }
        }

        this.resData = res.process
        this.variables = res.process.variables
        if (financeNo && financeId) {
          this.fNo = financeNo
          this.fId = financeId
          this.mrJiangGetDetail()
        }
      })
    },
    // 获取任务详情
    mrJiangGetDetail() {
      this.waiting = false

      const paramsD = {
        // financeNo: 'J42316552384812',
        financeNo: this.fNo,
      }
      loanDerateAlterationDetail(paramsD).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          // console.log('resData', resData)

          // 流程产品信息
          let usage = []
          getDictionary('finance_apply_loan_usage').then(res => {
            const resDatac = res.data
            if (resDatac.code == 200) {
              // 处理字典数据
              const resList = []
              for (const item of resDatac.data) {
                resList.push({
                  key: item.dictKey,
                  value: item.dictValue,
                  id: item.id,
                })
              }
              const infoData = {
                capitalLogo: resData.productImg,
                goodsName: resData.productName,
                capitalName: resData.capitalName,
                type: 1,
              }
              this.processGoodsObj = infoData

              // 过滤出当前借款用途
              usage = resList.filter(item => item.key == resData.loanUsage)
              const data = [
                {
                  id: 1,
                  label: '融资金额',
                  value: `${this.$numChuFun(resData.usedAmount, 10000)}万元`,
                  key: 'financingDemand_money',
                },
                {
                  id: 2,
                  label: '额度类型',
                  value: `${resData.recycleType === 1 ? '不可循环' : '可循环'}`,
                  key: 'financingDemand_recycleType',
                },
                {
                  id: 3,
                  label: '借款用途',
                  value: `${usage[0].value}`,
                  key: 'financingDemand_use',
                },
              ]

              // 是否可读
              // const dataKey = data.map(item => item.key)
              // const taskFormFilter = taskForm.filter(item =>
              //   dataKey.includes(item.id)
              // )
              // const taskFormId = taskFormFilter.map(item => {
              //   if (item.readable) {
              //     return item.id
              //   }
              // })
              // const dataFilter = data.filter(item =>
              //   taskFormId.includes(item.key)
              // )
              this.tableData1 = data
            }
          })

          // 已还信息
          {
            const loanMArr = resData.loanManageIouVO.loanManageRepayments || []
            for (const item of loanMArr) {
              this.tableData3.push({
                repaymentTime: item.repaymentTime,
                repaymentNo: item.repaymentNo,
                periodDes: item.periodDes,
                totalAmount: item.totalAmount,
                principal: item.principal,
                interest: item.interest,
                serviceCharge: item.serviceCharge,
                penaltyInterest: item.penaltyInterest,
              })
            }
            if (loanMArr.length) {
              this.tableData3.push({
                repaymentTime: '总计',
                repaymentNo: '',
                periodDes: '',
                totalAmount: loanMArr[0].monthlySupply,
                principal: loanMArr[0].monthlyPrincipal,
                interest: loanMArr[0].monthlyInterest,
                serviceCharge: loanMArr[0].monthlyServiceFee,
                penaltyInterest: loanMArr[0].monthlyPenaltyInterest,
              })
            }
          }

          // 订单信息
          getDictionary('goods_billing_method').then(res => {
            const resDatac2 = res.data
            if (resDatac2.code == 200 && Array.isArray(resDatac2.data)) {
              const goodsBillingMethodMap = {}
              resDatac2.data.map(d => {
                const { dictKey, dictValue } = d
                goodsBillingMethodMap[dictKey] = dictValue
              })

              const rlo = resData.loanManageIouVO
              const data = [
                {
                  id: 1,
                  label: '借款人名称',
                  value: rlo.userName,
                  key: 'reBreathing_userName',
                },
                {
                  id: 2,
                  label: '借款金额',
                  value: rlo.iouAmount,
                  key: 'reBreathing_iouAmount',
                },
                {
                  id: 3,
                  label: '放款日',
                  value: rlo.loanTime,
                  key: 'reBreathing_loanTime',
                },
                {
                  id: 4,
                  label: '到期日',
                  value: rlo.expireTime,
                  key: 'reBreathing_loanTime',
                },
                {
                  id: 5,
                  label: '年利率(单利)',
                  value: `${rlo.financeApply.annualInterestRate}%(日利率*360)`,
                  key: 'financingDemand_use',
                },
                {
                  id: 6,
                  label: '日利率',
                  value: `${rlo.financeApply.dailyInterestRate}%`,
                  key: 'financingDemand_use',
                },
                {
                  id: 7,
                  label: '还款方式',
                  value:
                    goodsBillingMethodMap[rlo.financeApply.repaymentMode] ||
                    '随借随还',
                  key: 'financingDemand_repaymentMode',
                },
                {
                  id: 8,
                  label: '首次还款日',
                  value: this.getRepaymentFirstDay(
                    rlo.firstRepaymentDate,
                    'yyyy-MM-dd'
                  ),
                  key: 'financingDemand_firstRepaymentDate',
                },
                {
                  id: 8,
                  label: '还款日',
                  value: `每月${this.getRepaymentFirstDay(
                    rlo.firstRepaymentDate,
                    'dd'
                  )}号`,
                  key: 'financingDemand_firstRepaymentdd',
                },
                {
                  id: 9,
                  label: '借款期限',
                  value: rlo.periodDes,
                  key: 'financingDemand_periodDes',
                },
              ]
              this.tableData2_1 = data
            }
          })

          // 减免申请信息
          {
            if (!this.isApply) {
              const loa = resData.loanDerateAlteration
              const tStr = []

              const Munit = loa.derateAmountType === 1 ? '%' : '元'
              const data = [
                {
                  id: 1,
                  label: '减免类型',
                  value: DIC.type[loa.type - 1]
                    ? DIC.type[loa.type - 1].label
                    : '无',
                  key: 'loanAlteration_type',
                },
                {
                  id: 2,
                  label: '减免方式',
                  value: DIC.mode[loa.derateAmountType - 1]
                    ? DIC.mode[loa.derateAmountType - 1].label
                    : '无',
                  key: 'loanAlteration_derateAmountType',
                },
                {
                  id: 3,
                  label: '减免金额类型',
                  value: '',
                  key: 'loanAlteration_derateType',
                },
              ]

              const mobj = {
                // 1: {
                //   id: 4,
                //   label: '减免本金',
                //   value: `${loa.deratePrincipal}${Munit}`,
                //   key: 'loanAlteration_deratePrincipal',
                // },
                2: {
                  id: 5,
                  label: '减免利息',
                  value: `${loa.derateInterest}${Munit}`,
                  key: 'loanAlteration_derateInterest',
                },
                // 3: {
                //   id: 6,
                //   label: '减免服务费',
                //   value: `${loa.derateServiceCharge}${Munit}`,
                //   key: 'loanAlteration_derateServiceCharge',
                // },
              }
              for (const item of DIC.typeOfamount) {
                if (
                  item.label !== '减免服务费' &&
                  loa.derateType.includes(String(item.value))
                ) {
                  tStr.push(item.label)
                  data.push(mobj[item.value])
                  // data.splice(item.value + 2, 0, mobj[item.value])
                } else if (loa.derateType.includes(String(item.value))) {
                  // 减免服务费特殊处理
                  tStr.push(item.label)
                  const lRepay = loa.repaymentPlanFeeTotalList
                  if (lRepay && lRepay.length) {
                    for (const cItem of lRepay) {
                      data.push({
                        id: cItem.expenseTypeId,
                        label: cItem.feeTypeName,
                        value: `${cItem.feeVal}${Munit}`,
                      })
                    }
                  }
                }
              }
              data[2].value = tStr.join()

              data.push({
                id: 7,
                label: '减免原因',
                value: loa.reason,
                key: 'loanAlteration_reason',
              })
              data.push({
                id: 8,
                label: '附件信息',
                value: resData.attachList,
                key: 'loanAlteration_attachList',
              })

              this.tableData4_1 = data
            }
          }

          // 减免调整信息回显
          {
            if (!this.isApply) {
              const loa = resData.loanDerateAlteration
              this.shenpiGiveHouduan = loa
              this.activeNames4 = ['furtherInformation']
              this.change4Type = true
              this.fengzhuangdongtaibiaodanShowFun(
                loa.repaymentPlanFeeTotalList || [],
                true
              )
              const mObj = {
                // 1: 'deratePrincipal',
                2: 'derateInterest',
                3: 'derateServiceCharge',
              }
              const arrD = ['type', 'derateAmountType', 'derateType']
              // 动态显示用户申请的项目
              for (const item of DIC.typeOfamount) {
                if (
                  item.label !== '减免服务费' &&
                  loa.derateType.includes(String(item.value))
                ) {
                  arrD.push(mObj[item.value])
                  // this.findObject(
                  //   this.derateFOption.column,
                  //   mObj[item.value]
                  // ).display = true
                } else if (loa.derateType.includes(String(item.value))) {
                  // 减免服务费特殊处理
                  const lRepay = loa.repaymentPlanFeeTotalList
                  if (lRepay && lRepay.length) {
                    for (const cItem of lRepay) {
                      this.manForm[cItem.expenseTypeId] = cItem.feeVal
                    }
                  }
                }
              }
              // 回显
              for (const item of arrD) {
                if (loa[item]) {
                  this.manForm[item] = loa[item]
                }
              }
            }
          }

          // 减免测算
          if (!this.isApply) {
            this.loanAdjustApplyCalculateFun()
          }
          // 变更历史
          this.getRepaymentFinanceIdFun()
        }
      })
    },
    // 获取可减免的动态费用
    getRepaymentPlanFeeFun(typeValue2) {
      const dataD = {
        financeNo: this.fNo,
        type: typeValue2,
      }
      return getRepaymentPlanFee(dataD).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          return resData
        }
      })
    },
    // 判断是否展示可输入的动态费用填写框
    showInputmadeFun(thisStartType) {
      if (thisStartType) {
        for (const item of this.dongtaishuruFeiArr) {
          this.findObject(
            this.derateFOption.column,
            item.expenseTypeId
          ).display = true
        }
        return
      }
      for (const item of this.dongtaishuruFeiArr) {
        this.findObject(
          this.derateFOption.column,
          item.expenseTypeId
        ).display = false
      }
    },
    async fengzhuangdongtaibiaodanShowFun(typeValue, shifou) {
      this.formLock = true
      const one = [
        {
          label: '减免类型',
          key: 'regulating_interest_rate',
          placeholder: '请选择减免类型',
          prop: 'type',
          type: 'select',
          disabled: false,
          display: true,
          span: 12,
          row: true,
          rules: [
            {
              required: true,
              message: '请选择减免类型',
              trigger: 'change',
            },
          ],
          dicData: DIC.type,
          change: ({ value }) => {
            if (!this.formLock && value) {
              this.fengzhuangdongtaibiaodanShowFun(value)
            }
          },
        },
        {
          label: '减免方式',
          key: 'regulating_interest_rate',
          placeholder: '请选择减免方式',
          prop: 'derateAmountType',
          type: 'select',
          disabled: false,
          display: true,
          span: 12,
          row: true,
          rules: [
            {
              required: true,
              message: '请选择减免方式',
              trigger: 'change',
            },
          ],
          dicData: DIC.mode,
          control: val => {
            if (val === 1) {
              this.allAppend = '%'
            } else if (val === 2) {
              this.allAppend = '元'
            }
            // if (val === 1) {
            //   return {
            //     deratePrincipal: {
            //       append: '%',
            //     },
            //     derateInterest: {
            //       append: '%',
            //     },
            //     derateServiceCharge: {
            //       append: '%',
            //     },
            //   }
            // } else if (val === 2) {
            //   return {
            //     deratePrincipal: {
            //       append: '元',
            //     },
            //     derateInterest: {
            //       append: '元',
            //     },
            //     derateServiceCharge: {
            //       append: '元',
            //     },
            //   }
            // }
          },
        },
        {
          label: '减免金额类型',
          key: 'regulating_interest_rate',
          placeholder: '请选择减免金额类型',
          prop: 'derateType',
          type: 'select',
          disabled: false,
          display: true,
          multiple: true,
          span: 12,
          row: true,
          rules: [
            {
              required: true,
              message: '请选择减免金额类型',
              trigger: 'change',
            },
          ],
          dataType: 'string',
          dicData: DIC.typeOfamount,
          change: ({ value }) => {
            // const deratePrincipal = this.findObject(
            //   this.derateFOption.column,
            //   'deratePrincipal'
            // )
            const derateInterest = this.findObject(
              this.derateFOption.column,
              'derateInterest'
            )
            // const derateServiceCharge = this.findObject(
            //   this.derateFOption.column,
            //   'derateServiceCharge'
            // )
            // 本金
            // if (value.includes('1')) {
            //   deratePrincipal.display = true
            // } else {
            //   deratePrincipal.display = false
            // }
            // 利息
            if (value.includes('2')) {
              derateInterest.display = true
            } else {
              derateInterest.display = false
            }
            // 服务费
            if (value.includes('3')) {
              // derateServiceCharge.display = true
              this.showInputmadeFun(true)
            } else {
              // derateServiceCharge.display = false
              this.showInputmadeFun(false)
            }
          },
        },
        // {
        //   label: '减免本金',
        //   key: 'regulating_interest_rate',
        //   placeholder: '请填写减免本金',
        //   prop: 'deratePrincipal',
        //   type: 'input',
        //   disabled: false,
        //   display: false,
        //   span: 12,
        //   row: true,
        //   controls: false,
        //   append: '',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请填写减免本金',
        //       trigger: 'blur',
        //     },
        //   ],
        //   blur: ({ value }) => {
        //     if (value) {
        //       this.manForm.deratePrincipal =
        //         this.manForm.deratePrincipal.replace(/[^\d.]/g, '')
        //     }
        //   },
        // },
        {
          label: '减免利息',
          key: 'regulating_interest_rate',
          placeholder: '请填写减免利息',
          prop: 'derateInterest',
          type: 'input',
          disabled: false,
          display: false,
          span: 12,
          row: true,
          controls: false,
          append: '',
          rules: [
            {
              required: true,
              message: '请填写减免利息',
              trigger: 'blur',
            },
          ],
          blur: ({ value }) => {
            if (value) {
              this.manForm.derateInterest = this.manForm.derateInterest.replace(
                /[^\d.]/g,
                ''
              )
            }
          },
        },
        // {
        //   label: '减免服务费',
        //   key: 'regulating_interest_rate',
        //   placeholder: '请填写减免服务费',
        //   prop: 'derateServiceCharge',
        //   type: 'input',
        //   disabled: false,
        //   display: false,
        //   span: 12,
        //   row: true,
        //   controls: false,
        //   append: '',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请填写减免服务费',
        //       trigger: 'blur',
        //     },
        //   ],
        //   blur: ({ value }) => {
        //     if (value) {
        //       this.manForm.derateServiceCharge =
        //         this.manForm.derateServiceCharge.replace(/[^\d.]/g, '')
        //     }
        //   },
        // },
      ]
      const two = [
        {
          label: '减免方式',
          key: 'derate_amount_type',
          placeholder: '请选择减免方式',
          prop: 'derateAmountType',
          type: 'select',
          disabled: false,
          display: true,
          span: 12,
          row: true,
          rules: [
            {
              required: true,
              message: '请选择减免方式',
              trigger: 'change',
            },
          ],
          dicData: DIC.mode,
          control: val => {
            if (val === 1) {
              this.allAppend = '%'
            } else if (val === 2) {
              this.allAppend = '元'
            }
            //   if (val === 1) {
            //     return {
            //       deratePrincipal: {
            //         append: '%',
            //       },
            //       derateInterest: {
            //         append: '%',
            //       },
            //       derateServiceCharge: {
            //         append: '%',
            //       },
            //     }
            //   } else if (val === 2) {
            //     return {
            //       deratePrincipal: {
            //         append: '元',
            //       },
            //       derateInterest: {
            //         append: '元',
            //       },
            //       derateServiceCharge: {
            //         append: '元',
            //       },
            //     }
            //   }
          },
        },
        {
          label: '减免金额类型',
          key: 'regulating_interest_rate',
          placeholder: '请选择减免金额类型',
          prop: 'derateType',
          type: 'select',
          disabled: true,
          display: true,
          multiple: true,
          span: 12,
          row: true,
          dataType: 'string',
          dicData: DIC.typeOfamount,
          change: ({ value }) => {
            // const deratePrincipal = this.findObject(
            //   this.derateFOption.column,
            //   'deratePrincipal'
            // )
            const derateInterest = this.findObject(
              this.derateFOption.column,
              'derateInterest'
            )
            // 本金
            // if (value.includes('1')) {
            //   deratePrincipal.display = true
            // } else {
            //   deratePrincipal.display = false
            // }
            // 利息
            if (value.includes('2')) {
              derateInterest.display = true
            } else {
              derateInterest.display = false
            }
            // 服务费
            if (value.includes('3')) {
              this.showInputmadeFun(true)
            } else {
              this.showInputmadeFun(false)
            }
          },
        },
        // {
        //   label: '减免本金',
        //   key: 'derate_principal',
        //   placeholder: '请填写减免本金',
        //   prop: 'deratePrincipal',
        //   type: 'input',
        //   disabled: false,
        //   display: false,
        //   span: 12,
        //   row: true,
        //   controls: false,
        //   append: '',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请填写减免本金',
        //       trigger: 'blur',
        //     },
        //   ],
        //   blur: ({ value }) => {
        //     if (value) {
        //       this.manForm.deratePrincipal =
        //         this.manForm.deratePrincipal.replace(/[^\d.]/g, '')
        //     }
        //   },
        // },
        {
          label: '减免利息',
          key: 'derate_interest',
          placeholder: '请填写减免利息',
          prop: 'derateInterest',
          type: 'input',
          disabled: false,
          display: false,
          span: 12,
          row: true,
          controls: false,
          append: '',
          rules: [
            {
              required: true,
              message: '请填写减免利息',
              trigger: 'blur',
            },
          ],
          blur: ({ value }) => {
            if (value) {
              this.manForm.derateInterest = this.manForm.derateInterest.replace(
                /[^\d.]/g,
                ''
              )
            }
          },
        },
      ]

      if (shifou) {
        this.dongtaishuruFeiArr = typeValue
      } else {
        this.dongtaishuruFeiArr = await this.getRepaymentPlanFeeFun(typeValue)
      }
      const dongtaiArrDeTarget = this.isApply ? one : two
      for (const item of this.dongtaishuruFeiArr) {
        dongtaiArrDeTarget.push({
          label: item.feeTypeName,
          placeholder: `请填写${item.feeTypeName}`,
          prop: item.expenseTypeId,
          type: 'input',
          disabled: false,
          display: false,
          span: 12,
          tipNotShow: `最高支持输入${item.amount}`,
          row: true,
          controls: false,
          append: '',
          rules: [
            {
              required: true,
              message: `请填写${item.feeTypeName}`,
              trigger: 'blur',
            },
          ],
          blur: ({ value }) => {
            if (value) {
              this.manForm[item.expenseTypeId] = this.manForm[
                item.expenseTypeId
              ].replace(/[^\d.]/g, '')
            }
          },
        })
      }
      one.push({
        label: '减免理由',
        key: 'regulating_reason',
        placeholder: '请填写减免理由',
        prop: 'reason',
        type: 'textarea',
        minRows: 2,
        disabled: false,
        display: true,
        span: 24,
        rules: [
          {
            required: true,
            message: '请填写减免理由',
            trigger: 'blur',
          },
        ],
      })
      one.push({
        label: '上传附件',
        placeholder: '请上传附件',
        prop: 'attachList',
        type: 'input',
        disabled: false,
        display: true,
        span: 24,
        rules: [
          {
            required: true,
            validator: this.validatePassImg,
            trigger: 'change',
          },
        ],
      })
      if (this.isApply) {
        this.derateFOption.column = one
        this.myFormReload = Math.random()
        this.$refs.myForm.resetFields()
        this.manForm.type = typeValue
        setTimeout(() => {
          this.formLock = false
        }, 500)
      } else {
        this.derateFOption.column = two
        this.myFormReload2 = Math.random()
      }
    },
    // 通过
    handleExamine(pass) {
      if (!this.$refs.myForm) return
      this.$refs.myForm.validate((valid, done) => {
        done()
        if (!valid) {
          this.$message.warning('请完善减免信息，再进行通过')
          return
        }
        for (const item of this.dongtaishuruFeiArr) {
          const huoquZhi = this.manForm[item.expenseTypeId]
          if (huoquZhi) {
            item.feeVal = huoquZhi
          }
        }
        this.shenpiGiveHouduan['derateInterest'] =
          this.manForm['derateInterest']
        // this.shenpiGiveHouduan['deratePrincipal'] =
        //   this.manForm['deratePrincipal']

        this.validataFunction(pass)
      })
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      const params = {
        loanDerateAlteration: this.shenpiGiveHouduan,
      }
      this.handleCompleteTask(pass, params)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 终止
    // handleTermination() {
    //   if (
    //     (this.statusForm.paymentStatus &&
    //       this.statusForm.paymentStatus !== 1) ||
    //     !this.costPlatformList.payAFee_orderStatus
    //   ) {
    //     this.validataterminationFunction()
    //     return
    //   }
    //   this.$message.error('请修改订单状态')
    // },
    // 终止后调取接口函数
    // validataterminationFunction() {
    //   this.submitLoading = true
    //   let params = {}
    //   if (this.costPlatformList.payAFee_orderStatus) {
    //     this.statusForm.financeApplyId = this.financeApplyIdRoot // 流程融资ID
    //     params = {
    //       billExpenseOrder: this.statusForm,
    //     }
    //   }
    //   this.handleTerminateProcess(params)
    // },
    handleChange1_1() {
      // 订单详情折叠面板收缩控制
      this.change1_1Type = !this.change1_1Type
    },
    handleChange2() {
      // 已还明细折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
    // handleChange3() {
    //   // 缴纳费用折叠面板收缩控制
    //   this.change3Type = !this.change3Type
    // },
    handleChange4() {
      // 减免信息折叠面板收缩控制
      this.change4Type = !this.change4Type
    },
    handleChange4_1() {
      // 减免申请信息折叠面板收缩控制
      this.change4_1Type = !this.change4_1Type
    },
    handleChange5() {
      // 减免测算折叠面板收缩控制
      this.change5Type = !this.change5Type
    },
    getRepaymentFirstDay(loanTime, format) {
      const nextLoanDate = new Date(loanTime)
      nextLoanDate.setMonth(nextLoanDate.getMonth())
      // nextLoanDate.setMonth(nextLoanDate.getMonth() + 1)
      return nextLoanDate == 'Invalid Date'
        ? '..'
        : dateFormat(nextLoanDate, format)
    },
    // 减免测算按钮
    mySubmit(form, done) {
      this.activeNames5 = ['furtherInformation']
      this.change5Type = true
      this.handleTabButton('t1')
      this.processStatus = 't1'
      done()
    },
    // 减免测算
    loanAdjustApplyCalculateFun() {
      if (!this.$refs.myForm) return
      this.$refs.myForm.validate((valid, done) => {
        done()
        if (!valid) {
          this.$message.warning('请完善减免信息，再进行测算')
          return
        }

        this.table5Loading = true
        for (const item of this.dongtaishuruFeiArr) {
          const huoquZhi = this.manForm[item.expenseTypeId]
          if (huoquZhi) {
            item.feeVal = huoquZhi
          }
        }
        const paramsD = {
          financeNo: this.fNo,
          type: this.manForm.type,
          derateType: this.manForm.derateType,
          derateAmountType: this.manForm.derateAmountType,
          // deratePrincipal: this.manForm.deratePrincipal || null,
          derateInterest: this.manForm.derateInterest || null,
          // derateServiceCharge: this.manForm.derateServiceCharge || null,
          repaymentPlanFeeTotalList: this.dongtaishuruFeiArr,
        }
        loanDerateAlterationCalculate(paramsD)
          .then(({ data }) => {
            if (data.success) {
              const { data: resData } = data
              // console.log('resData', resData)
              const a1 = resData.loanHistoryRepaymentPlanVO || []
              const b1 = resData.expenseOrderDetailFinanceVos || []
              for (const item of a1) {
                this.tableData5.push({
                  repaymentTime: item.repaymentTime,
                  period: item.period,
                  totalAmount: item.totalAmount,
                  principal: item.principal,
                  planInterest: item.planInterest,
                  // serviceFee: item.serviceFee,
                })
              }
              if (this.tableData5.length) {
                this.tableData5.push({
                  repaymentTime: '总计',
                  period: '',
                  totalAmount: a1[0].monthlySupply,
                  principal: a1[0].monthlyPrincipal,
                  planInterest: a1[0].monthlyInterest,
                  // serviceFee: a1[0].monthlyServiceFee,
                })
              }
              this.sum1 = Number(a1[0].monthlySupply)

              if (b1.length) {
                for (const cItem of b1) {
                  for (const dItem of cItem.expenseOrderDetailList) {
                    dItem.repaymentTerm = dItem.repaymentTerm
                      ? dItem.repaymentTerm + '期'
                      : '--'
                  }
                }
              }
              this.platformFeeList = b1
              this.table5Loading = false
            }
          })
          .catch(() => {
            this.table5Loading = false
          })
      })
    },
    // 变更历史记录
    getRepaymentFinanceIdFun() {
      const paramsD = {
        // financeId: '1602939298715426818',
        financeId: this.fId,
      }
      getRepaymentFinanceIdChuangcai(paramsD).then(({ data }) => {
        if (data.success) {
          const { data: resData } = data
          // console.log('resData', resData)
          const arr = []
          for (const key in resData) {
            arr.push({
              a: resData[key].loanHistoryRepaymentPlanVO,
              b: resData[key].expenseOrderDetailFinanceVos || [],
            })
          }
          for (const itemD of arr) {
            if (itemD.a.length) {
              let textD = ''
              switch (itemD.a[0].alterationType) {
                case 1:
                  textD = '调息'
                  break
                case 2:
                  textD = '减免'
                  break
                case 3:
                  textD = '逾期协商'
                  break
                case 4:
                  textD = '展期'
                  break
              }
              this.listRecords.push({
                id: itemD.a[0].id,
                label: itemD.a[0].id,
                value: `${textD} ${itemD.a[0].createTime}`,
              })
              itemD.a.push({
                repaymentTime: '总计',
                period: '',
                repaymentStatus: '',
                totalAmount: itemD.a[0].monthlySupply,
                principal: itemD.a[0].monthlyPrincipal,
                planInterest: itemD.a[0].monthlyInterest,
                serviceFee: itemD.a[0].monthlyServiceCharge,
              })
              this.reFinObj[itemD.a[0].id] = itemD.a
            }
            if (itemD.b.length) {
              for (const cItem of itemD.b) {
                for (const dItem of cItem.expenseOrderDetailList) {
                  dItem.repaymentTerm = dItem.repaymentTerm
                    ? dItem.repaymentTerm + '期'
                    : '--'
                }
              }
              this.reFinObj[`${itemD.a[0].id}_pingtaiFei`] = itemD.b
            } else {
              this.reFinObj[`${itemD.a[0].id}_pingtaiFei`] = []
            }
          }
        }
      })
    },
    // 变更记录切换
    handleTabButton(acceptVal) {
      if (acceptVal !== 't1') {
        this.tableData5 = this.reFinObj[acceptVal]
        this.alterationReason =
          this.tableData5[this.tableData5.length - 2].alterationReason
        this.sum1 = Number(
          this.tableData5[this.tableData5.length - 1].totalAmount
        )
        this.platformFeeList = this.reFinObj[`${acceptVal}_pingtaiFei`]
        return
      }
      this.alterationReason = ''
      this.sum1 = 0
      this.tableData5 = []
      this.platformFeeList = []
      this.loanAdjustApplyCalculateFun()
    },
    // 提交申请
    nextBtnFun() {
      this.$refs.myForm.validate((valid, done) => {
        done()
        if (!valid) {
          this.$message.warning('请完善减免信息，再进行提交')
          return
        }

        this.subbmitType = true
        for (const item of this.dongtaishuruFeiArr) {
          const huoquZhi = this.manForm[item.expenseTypeId]
          if (huoquZhi) {
            item.feeVal = huoquZhi
          }
        }
        const aId = this.manForm.attachList.map(item => item.id).join()
        const dataP = {
          financeNo: this.fNo,
          type: this.manForm.type,
          derateAmountType: this.manForm.derateAmountType,
          derateType: this.manForm.derateType,
          // deratePrincipal: this.manForm.deratePrincipal,
          derateInterest: this.manForm.derateInterest,
          // derateServiceCharge: this.manForm.derateServiceCharge,
          reason: this.manForm.reason,
          adjunctProof: aId,
          repaymentPlanFeeTotalList: this.dongtaishuruFeiArr,
        }
        loanDerateAlterationApply(dataP)
          .then(({ data }) => {
            if (data.success) {
              // const { data: resData } = data
              this.$message.success('申请成功')
              this.$router.$avueRouter.closeTag()
              this.handleCloseTag('/loan/financingorder')
            }
            this.subbmitType = false
          })
          .catch(() => {
            this.subbmitType = false
          })
      })
    },
    // 上传附件校验
    validatePassImg(rule, value, callback) {
      if (value === '' && rule.required) {
        callback(new Error('请上传附件'))
      } else {
        callback()
      }
    },
    // 调息利率校验
    validatePassAnnR(rule, value, callback) {
      if (value === '') {
        callback(new Error('请填写调息'))
      }
      // else if (this.baseRate && Number(value) > Number(this.baseRate)) {
      //   callback(new Error('调息利率不能大于原利率'))
      // }
      else {
        callback()
      }
    },
    // 查看支付凭证
    // viewVoucher(item) {
    //   if (item === 'img') {
    //     this.$ImagePreview(this.imageArr, 0, {
    //       closeOnClickModal: true,
    //       showViewer: true,
    //       beforeClose: () => {
    //         // this.$message.success('关闭回调')
    //       },
    //     })
    //   } else {
    //     this.pdfSrc = item.url + '?time=' + new Date().getMilliseconds()
    //   }
    // },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    tableRowClassName({ row }) {
      if (!row.periodDes) {
        return 'aggregate-row'
      }
      return ''
    },
    tableRowClassName2({ row }) {
      if (!row.period) {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      if (this.processGoodsObj.type == 2) {
        this.$router.push({
          path: '/pcontrol/pinformation',
          query: { id: this.processGoodsObj.id },
        })
        sessionStorage.setItem('look', 'true')
      } else {
        this.$router.push({
          path: '/pcontrol/purchasing',
          query: { id: this.processGoodsObj.id },
        })
        sessionStorage.setItem('look', 'true')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

::v-deep {
  .el-input.is-disabled .el-input__inner,
  .el-textarea.is-disabled .el-textarea__inner {
    color: #000;
  }
}

.my-basebox {
  ::v-deep {
    .base-image-item {
      margin-bottom: 0;

      .base-image-item-disabled {
        display: none;
      }
    }
  }
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.pay-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  .order-box {
    display: flex;
    align-items: center;
    cursor: context-menu;

    .order-status {
      width: 56px;
      height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      margin-right: 8px;
    }

    .order-text {
      height: 30px;
      border-radius: 28px;
      font-size: 14px;
      font-family: Microsoft Yahei;
      padding: 4px 8px;
      box-sizing: border-box;
      margin-right: 12px;
    }

    .order-modification-btn {
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
    }

    .order-btn {
      width: 28px;
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .payOrder-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    span {
      width: 103px;
      height: 30px;
      border-radius: 4px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      text-align: center;
      border: 1px solid rgba(105, 124, 255, 100);
      margin-right: 10px;
      padding: 2px 4px;
      box-sizing: border-box;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }
    }

    .payOrder-left-box {
      margin-right: 10px;
    }
  }
}

.avue-monry-form-box {
  .money-box {
    width: 432px;
    margin: -10px 0;
    border-radius: 6px;
    background-color: rgba(247, 247, 247, 100);
    padding: 0 12px 20px;
    box-sizing: border-box;
    overflow: hidden;

    .chlidern-money {
      display: flex;
      align-items: center;
      margin-top: 20px;

      .label-box {
        width: 95px;
        height: 20px;
        line-height: 20px;
        color: rgba(153, 153, 153, 100);
        font-size: 14px;
        text-align: right;
        font-family: SourceHanSansSC-regular;
      }

      .value-box {
        width: 311px;
        height: 20px;
        line-height: 20px;
        color: rgba(0, 7, 42, 100);
        font-weight: bold;
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .look-btn-menu {
    & span {
      height: 21px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      cursor: pointer;
      margin-right: 8px;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  // 覆盖弹窗默认样式
  ::v-deep {
    .avue-form__menu {
      display: none;
    }

    .el-input.is-disabled .el-input__inner {
      color: #000;
    }

    .el-textarea.is-disabled .el-textarea__inner {
      color: #000;
    }
  }
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.occupied-box {
  height: 104px;
  width: 1px;
}

.footer-container {
  position: fixed;
  z-index: 1;
  height: 68px;
  width: calc(100% - 267px);
  display: flex;
  justify-content: center;
  align-items: center;
  bottom: 0;
  color: #000;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 #0000001a;

  & > * {
    margin-right: 18px;

    &:last-child {
      margin-right: 0;
    }
  }

  .nextBtn {
    width: 80px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    color: rgba(255, 255, 255, 100);
    font-size: 14px;
    text-align: center;
    padding: 0;
    font-family: Microsoft Yahei;
    cursor: pointer;
  }

  .btn-bg-color-blue {
    background-color: #579eff;
  }
}

.management-accounts {
  margin-top: 35px;
}

.order-header-container {
  width: 100%;
  // height: 41px;
  // height: 37px;
  // border: 1px solid #f3f3f3;
  background-color: #ffff;
  margin-top: 15px;

  .el-radio-group {
    height: 100%;
    // height: 49px;
    display: flex;
    overflow-x: auto;

    .el-radio-button {
      height: 100%;

      &.is-active {
        border: 1px sloid gray;
      }
    }

    .el-radio-button__inner {
      border: none !important;
      height: 100%;
    }
  }
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        & > span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
        & > span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
