<template>
  <div class="the-file-preview">
    <div class="the-for-box" v-for="(item, index) in formUpload" :key="index">
      <span
        class="the-file-lable"
        style="font-size: 14px; color: rgba(125, 125, 125, 100)"
        >{{ item.materialName }}：</span
      >
      <div class="the-for-children-box">
        <div
          class="the-for-children-box-width"
          v-for="(itemed, indexed) in item.uploadArr"
          :Key="indexed"
        >
          <div
            class="the-file-name-box"
            style="width: 354px; height: 30px"
            @click="openPreview(itemed)"
          >
            <span
              class="the-file-name"
              style="font-size: 14px; color: rgba(72, 72, 72, 100)"
              >{{ item.materialName
              }}{{ item.uploadArr.length > 1 ? indexed + 1 : '' }}.{{
                itemed.fileType
              }}
              </span>
            <span class="the-file-icon">
              <svg-icon
                icon-class="icon-line-chenggong"
                style="color: #3dc861; font-size: 16px"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import FilePreview from '@/components/file-preview'

export default {
  name: 'FilePreviewHWP',
  components: { FilePreview },
  data() {
    return {
      pdfSrc: '',
    }
  },
  props: {
    formUpload: {
      type: Array,
      required: true,
    },
  },
  // computed: {
  //   className() {
  //     return `#${this.iconClass}`
  //   },
  // },
  methods: {
    openPreview(item) {
      if (item.fileType != 'pdf') {
        const imagerDatas = [
          {
            url: `${item.url}`,
          },
        ]
        this.$ImagePreview(imagerDatas, 0, {
          closeOnClickModal: true,
          beforeClose: () => {
            // this.$message.success('关闭回调')
          },
        })
      } else {
        this.pdfSrc = item.url + '?time=' + new Date().getMilliseconds()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
/* .svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
} */
.the-file-preview {
  display: flex;
  flex-direction: column;

  .the-for-box {
    margin-top: 25px;
  }

  .the-file-lable {
    min-width: 130px;
    height: 21px;
    color: rgba(125, 125, 125, 100);
    text-align: left;
    cursor: context-menu;
  }

  .the-for-children-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .the-for-children-box-width {
      width: calc(100% - (100% - (100% / 3)));
      display: flex;
      align-items: center;

      .the-file-name-box {
        .the-file-name {
          position: relative;

          &::after {
            content: '';
            display: inline-block;
            position: absolute;
            left: 0;
            bottom: 1px;
            width: 0;
            border-bottom: 1px solid #000;
            transition: width 0.3s;
          }
        }

        &:hover .the-file-name:after {
          width: 100%;
        }

        border-radius: 7px;
        background-color: rgba(246, 246, 246, 100);
        display: flex;
        justify-content: space-between;
        align-items: center;
        overflow: hidden;
        padding: 0 12px;
        margin-top: 10px;
        cursor: pointer;

        .the-file-icon {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
</style>
