<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '申请额度-融资产品'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{ variables.applyUserName }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <basic-container v-if="modeReadable.applyProduct">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">申请产品</h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{ processGoodsObj.goodsName }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">{{
                processGoodsObj.type == 1
                  ? '应收账款质押'
                  : processGoodsObj.type == 2
                  ? '代采融资'
                  : processGoodsObj.type == 5
                  ? '订单融资'
                  : '云信'
              }}</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="tableData.length < 3 ? 2 : 3" border>
                <el-descriptions-item v-for="item in tableData" :key="item.id" :label="item.label">{{
                  item.value
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 补充资料文本 -->
      <div style="margin-top: 10px" v-if="modeReadable.furtherInformationText">
        <basic-container v-for="(item, index) in customerMaterialList" :key="index">
          <el-collapse v-model="activeNames" @change="handleChange()">
            <el-collapse-item :name="item.orderNum">
              <!-- title的solt -->
              <template slot="title">
                <div class="fromHeader">
                  <div class="fromLeft">
                    <i
                      class="el-icon-caret-bottom"
                      :class="{
                        'i-active': item.orderNum == activeNames.filter(filterItem => filterItem == item.orderNum)[0],
                      }"
                    ></i>
                    <h1 class="fromLeft-title">{{ item.templateName }}</h1>
                  </div>
                </div>
              </template>
              <!-- 展示表单 -->
              <div class="descriptions-for-box">
                <el-descriptions title="" :column="3" border>
                  <el-descriptions-item
                    v-for="(itemed, indexed) in item.creditFromFields"
                    :key="indexed"
                    :label="itemed.fieldDesc"
                  >
                    <span v-if="![4, 5, 6].includes(itemed.dataType)">
                      {{ itemed.value }}
                    </span>
                    <span v-else-if="[5, 6].includes(itemed.dataType)">
                      {{ itemed.value }}
                      <template v-if="itemed.value">
                        {{ itemed.dataType === 5 ? '元' : '万元' }}
                      </template>
                    </span>
                    <span v-else>
                      {{ itemed.timeValue }}
                    </span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-collapse-item>
          </el-collapse>
        </basic-container>
      </div>

      <!-- 补充资料附件 -->
      <basic-container v-if="modeReadable.furtherInformationAccessory">
        <el-collapse v-model="activeNames3" @change="handleChange3">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change3Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">补充资料</h1>
                </div>
              </div>
            </template>
            <!-- 展示表单 -->
            <FilePreviewHWP :formUpload="customerMaterialFormUpload" />
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 补充信息 -->
      <basic-container v-if="modeReadable.supplementaryInformation">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">补充信息</h1>
            <div style="margin-top: 10px">
              <el-tabs v-model="activeNameed" type="card">
                <el-tab-pane :key="item.name" :name="item.name" v-for="(item, index) in supplementaryInformation">
                  <!-- tab插槽 start -->
                  <template slot="label">
                    <el-button-group>
                      <el-button
                        type="primary"
                        size="medium"
                        :plain="item.name == activeNameed ? false : true"
                        :class="{
                          'bord-radius-left': index == 0,
                          'bord-radius-right': index == supplementaryInformation.length - 1,
                        }"
                        :style="{
                          color: item.errorM && item.name != activeNameed ? 'red' : '',
                        }"
                        >{{ item.title }}</el-button
                      >
                    </el-button-group>
                  </template>
                  <!-- tab插槽 end -->
                  <!-- 价值分析 start -->
                  <div v-if="item.name == 'supplementaryInformation_valueAnalysis'">
                    <avue-form
                      label-position="top"
                      ref="valueAnalysisRef"
                      :option="valueAnalysisOption"
                      v-model="formControl"
                    >
                    </avue-form>
                  </div>
                  <!-- 价值分析 end -->
                  <!-- 征信信息 start -->
                  <div v-else-if="item.name == 'supplementaryInformation_creditInformation'">
                    <avue-form
                      label-position="top"
                      ref="creditInformationRef"
                      :option="creditInformationOption"
                      v-model="formControl"
                    >
                    </avue-form>
                  </div>
                  <!-- 征信信息 end -->
                  <!-- 财务指标 start -->
                  <div v-else-if="item.name == 'supplementaryInformation_financialData'">
                    <avue-form
                      label-position="top"
                      ref="financialDataRef"
                      :option="financialDataOption"
                      v-model="formControl"
                    >
                    </avue-form>
                  </div>
                  <!-- 财务指标 end -->
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 合同签署 -->
      <basic-container v-if="modeReadable.contractSigning">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">合同签署</h1>
            <div class="boxs-to-apply-for-product-body">
              <el-table :data="tableData2" :max-height="240" style="width: 100%; border: 1px solid #ebeef5">
                <el-table-column
                  v-if="tableData2Readable.contractSigning_serialNumber"
                  prop="serial"
                  label="序号"
                  width="80"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_contractNumber"
                  prop="contractId"
                  label="合同编号"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_contractTitle"
                  prop="contractTitle"
                  label="合同标题"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_creationTime"
                  prop="createTime"
                  label="创建时间"
                  width="250"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_signTheState"
                  prop="statusText"
                  label="签署状态"
                  min-width="150"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag type="success">{{ scope.row.statusText }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_operation"
                  prop="firstTradeTime"
                  label="操作"
                  min-width="100"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div style="font-weight: unset">
                      <span class="view" @click="viewContract(scope)">预览</span>
                      <span class="down" @click="downContract(scope)">下载</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 风控信息 -->
      <basic-container v-if="modeReadable.riskControlInformation">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1" style="margin-bottom: 15px">风控信息</h1>
            <RiskControlInformation
              ref="riskControlForm"
              @childByValue="childByValue"
              @validataFunction="validataFunction"
              :id="variables.ratingRecordId"
              :taskForm="taskForm"
              :variables="variables"
              :writeableType="false"
              :isCoreEnterprise="isCoreEnterprise"
            />
          </div>
        </div>
      </basic-container>
    </template>
  </div>
</template>

<script>
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import RiskControlInformation from './component/riskControlInformation.vue'
import FilePreviewHWP from './component/preview-documents/index.vue'
import {
  getByContractId,
  contractDownload,
  skipToPreview,
  getDictionary,
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import { goodsTypeToPath } from '../globalFun.js'

export default {
  mixins: [customExForm],
  components: { WfButton, WfFlow, FilePreviewHWP, RiskControlInformation },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNameed(newData, oldData) {
      if (newData == oldData) return
      if (newData != '') {
        this.supplementaryInformation = this.supplementaryInformation.map(item => {
          if (item.name == newData) {
            item.errorM = false
          }
          return item
        })
      }
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      tableData: [],
      customerMaterialList: [],
      customerMaterialFormUpload: [],
      activeNames: [1],
      changeType: true,
      activeNames3: ['furtherInformation'],
      change3Type: true,
      activeNameed: 'supplementaryInformation_valueAnalysis',
      supplementaryInformation: [],
      supplementaryInformationWriter: [],
      supplementaryInformationData: [
        {
          title: '价值分析',
          name: 'supplementaryInformation_valueAnalysis',
          refed: 'valueAnalysisRef',
        },
        {
          title: '征信信息11',
          name: 'supplementaryInformation_creditInformation',
          refed: 'creditInformationRef',
        },
        {
          title: '财务指标',
          name: 'supplementaryInformation_financialData',
          refed: 'financialDataRef',
        },
      ],
      formControl: {},
      valueAnalysisOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 140,
        gutter: 50,
        column: [],
      },
      creditInformationOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 185,
        gutter: 50,
        column: [],
      },
      financialDataOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 'auto',
        gutter: 50,
        column: [],
      },
      tableData2: [],
      tableData2Readable: {
        contractSigning_serialNumber: false,
        contractSigning_contractNumber: false,
        contractSigning_contractTitle: false,
        contractSigning_creationTime: false,
        contractSigning_signTheState: false,
        contractSigning_operation: false,
      },
      // 一级大模块显影控制
      modeReadable: {},
      lock: false,
      isCoreEnterprise: false, // 是否核心企业
    }
  },
  methods: {
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        if (data.processDefinitionKey === 'core_auto_apply_quota') {
          this.isCoreEnterprise = true
        }
        const { variables } = data
        const { taskForm } = res.form
        this.taskForm = taskForm
        this.resData = data
        this.variables = variables || {}
        this.variables.processInstanceId = res.process.processInstanceId
        const { processGoodsInfo } = variables
        this.processGoodsObj = processGoodsInfo

        // 放款信息显影
        const taskArrkey1 = [
          'applyProduct',
          'furtherInformationText',
          'furtherInformationAccessory',
          'supplementaryInformation',
          'contractSigning',
          'riskControlInformation',
        ]
        const taskFormFilter1 = taskForm.filter(item => taskArrkey1.includes(item.id) && item.readable)
        console.log('taskFormFilter1', taskFormFilter1)
        for (const item of taskFormFilter1) {
          this.modeReadable[item.id] = true
        }

        // 流程产品信息
        let loadTer,
          filterTabBar = []
        getDictionary('goods_load_term_unit').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            loadTer = resList.filter(
              // 过滤出当前的最长期限单位
              item => item.key == processGoodsInfo.loadTermUnit
            )
            getDictionary('goods_billing_method').then(res => {
              const resData = res.data
              if (resData.code == 200) {
                // 处理字典数据
                const resList2 = []
                for (const item of resData.data) {
                  resList2.push({
                    key: item.dictKey,
                    value: item.dictValue,
                    id: item.id,
                  })
                }
                if (processGoodsInfo.repaymentType === 1) {
                  if (processGoodsInfo.billingMethod) {
                    processGoodsInfo.billingMethod.split(',').forEach((item, index) => {
                      // 过滤出当前的计费方式
                      filterTabBar[index] = resList2.filter(itemed => itemed.key == item)[0].value
                    })
                  }
                } else {
                  filterTabBar = ['随借随还']
                }

                const filterTabBarStr = filterTabBar.toString()
                const data = [
                  {
                    id: 1,
                    label: '最高可借',
                    value: `${processGoodsInfo.loanAmountEnd}万元`,
                    key: 'applyProduct_loanAmountEnd',
                  },
                  // {
                  //   id: 2,
                  //   label: '年利率低至',
                  //   value: `${processGoodsInfo.annualInterestRateStart}%`,
                  //   key: 'applyProduct_annualInterestRate',
                  // },
                  {
                    id: 3,
                    label: '最长期限',
                    value: `${processGoodsInfo.loadTermEnd}${loadTer[0].value}`,
                    key: 'applyProduct_loadTermEnd',
                  },
                  // {
                  //   id: 4,
                  //   label: '计费方式',
                  //   value: filterTabBarStr,
                  //   key: 'applyProduct_billingMethod',
                  // },
                ]
                if (processGoodsInfo.annualInterestRateStart) {
                  const dataArr2 = {
                    id: 2,
                    label: '年利率低至',
                    value: `${processGoodsInfo.annualInterestRateStart}%`,
                    key: 'applyProduct_annualInterestRate',
                  }
                  data.splice(1, 0, dataArr2)
                }
                if (filterTabBarStr) {
                  const dataArr4 = {
                    id: 4,
                    label: '计费方式',
                    value: filterTabBarStr,
                    key: 'applyProduct_billingMethod',
                  }
                  data.splice(3, 0, dataArr4)
                }

                // 是否可读
                const dataKey = data.map(item => item.key)
                const taskFormFilter = taskForm.filter(item => dataKey.includes(item.id))
                const taskFormId = taskFormFilter.map(item => {
                  if (item.readable) {
                    return item.id
                  }
                })
                const dataFilter = data.filter(item => taskFormId.includes(item.key))
                this.tableData = dataFilter
              }
            })
          }
        })

        // 补充资料
        const { customerMaterial } = variables || {}
        this.customerMaterialList = JSON.parse(customerMaterial.creditForm)
        this.customerMaterialFormUpload = JSON.parse(customerMaterial.supplementMaterial).map(item => {
          item.uploadArr = item.uploadArr
            .map(itemed => {
              if (itemed.url) {
                const file = itemed.url.split('/')
                itemed.fileType = file[file.length - 1].split('.')[1]
                return itemed
              }
            })
            .filter(Boolean)
          return item
        })

        // 补充信息
        // 回显补充信息
        const variablesObj = {
          ...variables.frontValue,
          ...variables.frontCreditInformation,
          ...variables.frontFinancialData,
        }
        this.unsupplementaryInformationValue(variablesObj)
        // 控制补充信息tag栏
        const supplementaryInformationArr = this.supplementaryInformationData.map(item => item.name)

        const supplementaryInformationArrFilter = taskForm.filter(item => supplementaryInformationArr.includes(item.id))
        const arrs = supplementaryInformationArrFilter.map(item => {
          if (item.readable) {
            return {
              title: item.name,
              name: item.id,
            }
          }
        })
        this.supplementaryInformation = arrs.filter(Boolean)
        this.supplementaryInformationWriter = supplementaryInformationArrFilter
          .map(item => {
            if (item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        // 价值分析Option
        const valueAnalysisOptionArr = [
          {
            label: '去年纳税销售总额',
            id: 'valueAnalysis_talTaxSalesLastYear',
            append: '万元',
          },
          {
            label: '前年纳税销售总额',
            id: 'valueAnalysis_talTaxSalesOfThePreviousYear',
            append: '万元',
          },
          {
            label: '净利润',
            id: 'valueAnalysis_retainedProfits',
            append: '万元',
          },
          {
            label: '去年应纳税总额',
            id: 'valueAnalysis_talAmountOfTaxPayableLastYear',
            append: '万元',
          },
          {
            label: '前年应纳税总额',
            id: 'valueAnalysis_talTaxableAmountOfPreviousYear',
            append: '万元',
          },
          {
            label: '同比销售增长率',
            id: 'valueAnalysis_yearSalesGrowthRate',
            append: '%',
          },
          {
            label: '环比销售增长率',
            id: 'valueAnalysis_sequentialSalesGrowthRate',
            append: '%',
          },
          {
            label: '应收账款总额',
            id: 'valueAnalysis_talAccountsReceivable',
            append: '万元',
          },
          {
            label: '应还账款总额',
            id: 'valueAnalysis_talAmountOfAccountsReceivable',
            append: '万元',
          },
          {
            label: '近1年回款率',
            id: 'valueAnalysis_recentYearReturnRate',
            append: '%',
          },
        ]
        const dataFilterArr = valueAnalysisOptionArr.map(item => item.id)
        // 是否可读
        const arr = taskForm.filter(item => dataFilterArr.includes(item.id))
        const arred = arr.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        const valueAnalysisOptionFilterArr = valueAnalysisOptionArr.filter(item => arred.includes(item.id))
        this.valueAnalysisOption.column = valueAnalysisOptionFilterArr.map(item => {
          return {
            label: item.label,
            prop: item.id,
            type: 'input',
            placeholder: `请输入${item.label}`,
            append: item.append,
            span: 12,
            display: true,
            disabled: false,
            rules: [
              {
                required: true,
                message: `请输入${item.label}`,
                trigger: 'blur',
              },
            ],
          }
        })
        // 是否可写
        const arrDisabled = arr
          .map(item => {
            if (!item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        if (arrDisabled) {
          const arrDisabledId = this.valueAnalysisOption.column.filter(item => arrDisabled.includes(item.prop))
          for (const item of arrDisabledId) {
            item.disabled = true
          }
        }
        // 征信信息Option
        const creditInformationOption = [
          {
            label: '当前关注',
            id: 'creditInformation_currentFocusOn',
            append: '笔',
          },
          {
            label: '当前次级',
            id: 'creditInformation_currentSubprime',
            append: '笔',
          },
          {
            label: '当前可疑',
            id: 'creditInformation_currentSuspicious',
            append: '笔',
          },
          {
            label: '当前损失',
            id: 'creditInformation_currentLoss',
            append: '笔',
          },
          {
            label: '当前逾期',
            id: 'creditInformation_currentOverdue',
            append: '笔',
          },
          {
            label: '近24个月逾期为M1',
            id: 'creditInformation_M1',
            append: '笔',
          },
          {
            label: '近24个月逾期为M2',
            id: 'creditInformation_M2',
            append: '笔',
          },
          {
            label: '近24个月逾期为M3',
            id: 'creditInformation_M3',
            append: '笔',
          },
          {
            label: '近2个月审批、征信查询',
            id: 'creditInformation_nearly2MonthsForApproval',
            append: '笔',
          },
          {
            label: '近12个月审批、征信查询',
            id: 'creditInformation_nearly12MonthsForApproval',
            append: '笔',
          },
          {
            label: '企业涉诉案件',
            id: 'creditInformation_casesInvolvingEnterprisesInLitigation',
            append: '笔',
          },
          {
            label: '企业涉诉未结案件',
            id: 'creditInformation_unresolvedCasesInvolvingLitigationOfEnterprises',
            append: '次',
          },
          {
            label: '逾期率',
            id: 'creditInformation_yuqilv',
            append: '%',
          },
        ]
        const dataFilterArr1 = creditInformationOption.map(item => item.id)
        // 是否可读
        const arr1 = taskForm.filter(item => dataFilterArr1.includes(item.id))
        const arred1 = arr1.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        const creditInformationOptionFilterArr = creditInformationOption.filter(item => arred1.includes(item.id))
        this.creditInformationOption.column = creditInformationOptionFilterArr.map(item => {
          return {
            label: item.label,
            prop: item.id,
            type: 'input',
            placeholder: `请输入${item.label}`,
            append: item.append,
            span: 12,
            display: true,
            disabled: false,
            rules: [
              {
                required: true,
                message: `请输入${item.label}`,
                trigger: 'blur',
              },
            ],
          }
        })
        // 是否可写
        const arrDisabled1 = arr1
          .map(item => {
            if (!item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        if (arrDisabled1) {
          const arrDisabledId = this.creditInformationOption.column.filter(item => arrDisabled1.includes(item.prop))
          for (const item of arrDisabledId) {
            item.disabled = true
          }
        }

        // 财务指标Option
        const financialDataOptionArr = [
          {
            label: '流动资产',
            id: 'financialData_currentAssets',
            append: '万元',
          },
          {
            label: '流动负债',
            id: 'financialData_currentLiabilities',
            append: '万元',
          },
          {
            label: '存货',
            id: 'financialData_inventory',
            append: '万元',
          },
          {
            label: '现金及现金等价物净增加额',
            id: 'financialData_netIncreaseInCashAndCashEquivalents',
            append: '万元',
          },
          {
            label: '负债合计',
            id: 'financialData_totalLiabilities',
            append: '万元',
          },
          {
            label: '资产总计',
            id: 'financialData_totalAssets',
            append: '万元',
          },
          {
            label: '净利润',
            id: 'financialData_netProfit',
            append: '万元',
          },
          {
            label: '利息费用',
            id: 'financialData_interestExpense',
            append: '万元',
          },
          {
            label: '所得税费用',
            id: 'financialData_incomeTaxExpense',
            append: '万元',
          },
          {
            label: '营业收入',
            id: 'financialData_operatingRevenue',
            append: '万元',
          },
          {
            label: '营业成本',
            id: 'financialData_costOfRevenue',
            append: '万元',
          },
          {
            label: '利润总额',
            id: 'financialData_totalProfit',
            append: '万元',
          },
          {
            label: '应收账款',
            id: 'financialData_accountsReceivable',
            append: '万元',
          },
          {
            label: '存货期末余额',
            id: 'financialData_endingInventoryBalance',
            append: '万元',
          },
          {
            label: '存货期初余额',
            id: 'financialData_beginningInventoryBalance',
            append: '万元',
          },
          {
            label: '购建固定资产、无形资产和其他长期资产所支付的现金',
            id: 'financialData_cashOutForAcqConstFixedAssets',
            append: '万元',
          },
          {
            label: '处置固定资产、无形资产和其他长期资产所收回的现金净额',
            id: 'financialData_netCashInFromDispFixedAssets',
            append: '万元',
          },
          {
            label: '当期营业收入',
            id: 'financialData_currentPeriodOperatingRevenue',
            append: '万元',
          },
          {
            label: '当期净利润',
            id: 'financialData_currentPeriodNetProfit',
            append: '万元',
          },
        ]
        const dataFilterArr2 = financialDataOptionArr.map(item => item.id)
        // 是否可读
        const arr2 = taskForm.filter(item => dataFilterArr2.includes(item.id))
        const arred2 = arr2.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        const financialDataOptionFilterArr = financialDataOptionArr.filter(item => arred2.includes(item.id))
        this.financialDataOption.column = financialDataOptionFilterArr.map(item => {
          return {
            label: item.label,
            prop: item.id,
            type: 'input',
            placeholder: `请输入${item.label}`,
            append: item.append,
            span: 12,
            display: true,
            disabled: false,
            rules: [
              {
                required: true,
                message: `请输入${item.label}`,
                trigger: 'blur',
              },
            ],
          }
        })
        // 是否可写
        const arrDisabled2 = arr2
          .map(item => {
            if (!item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        if (arrDisabled2) {
          const arrDisabledId = this.financialDataOption.column.filter(item => arrDisabled2.includes(item.prop))
          for (const item of arrDisabledId) {
            item.disabled = true
          }
        }

        // 合同签署list
        const cotractNameList = [
          'contractSigning_serialNumber',
          'contractSigning_contractNumber',
          'contractSigning_contractTitle',
          'contractSigning_creationTime',
          'contractSigning_signTheState',
          'contractSigning_operation',
        ]
        const cotractNameListFilter = taskForm.filter(item => cotractNameList.includes(item.id))
        const readableList = cotractNameListFilter.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        for (const key in this.tableData2Readable) {
          readableList.forEach(item => {
            if (item == key) {
              this.tableData2Readable[key] = true
            }
          })
        }
        const paramsed = {
          ids: variables.contractId,
        }
        if (!variables.contractId) return
        getByContractId(paramsed).then(res => {
          const resData = res.data
          if (resData.code == 200) {
            let num = 1
            for (const item of resData.data) {
              let statusText = ''
              if (item.status) {
                switch (item.status) {
                  case 1:
                    statusText = '待签署'
                    break
                  case 2:
                    statusText = '已取消'
                    break
                  case 3:
                    statusText = '已签署'
                    break
                  case 4:
                    statusText = '已失效'
                    break
                  case 5:
                    statusText = '已完成'
                    break
                  case 6:
                    statusText = '签署中'
                    break
                }
              }
              this.tableData2.push({
                id: item.id,
                serial: String(num),
                contractTitle: item.contractTitle,
                contractId: item.contractId,
                createTime: item.createTime,
                statusText: statusText,
              })
              num++
            }
          }
        })
      })
    },
    forValidata(pass) {
      const arrData = this.supplementaryInformationData.filter(item =>
        this.supplementaryInformationWriter.includes(item.name)
      )
      if (arrData.length) {
        arrData.forEach((item, index) => {
          if (item.refed && index + 1 == arrData.length) {
            this.validata(pass, item, 'last')
          } else if (item.refed) {
            this.validata(pass, item)
          }
        })
      } else {
        this.supplementaryInformationValue()
        if (this.modeReadable.riskControlInformation && pass) {
          this.$refs.riskControlForm.forValidata(pass)
        } else {
          this.validataFunction(pass)
        }
      }
    },
    // 封装校验方法
    validata(pass, refed, last) {
      // 通过前校验
      const thi = `this.$refs.${refed.refed}[0]`
      eval(thi).validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0][0].message
          if (!errMsg) {
            errMsg = '必填项未填'
          }
          if (!this.lock) {
            this.lock = true
            this.$message.error(errMsg)
            setTimeout(() => {
              this.lock = false
            }, 500)
          }
          this.supplementaryInformation = this.supplementaryInformation.map((item, index) => {
            if (item.name == refed.name && index != 0) {
              item.errorM = true
            }
            return item
          })
        } else if (last) {
          this.supplementaryInformationValue()
          if (this.modeReadable.riskControlInformation && pass) {
            this.$refs.riskControlForm.forValidata(pass)
          } else {
            this.validataFunction(pass)
          }
        }
        done()
      })
    },
    // 通过
    handleExamine(pass) {
      if (pass) {
        this.forValidata(pass)
      } else {
        this.validataFunction(pass)
      }
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      this.handleCompleteTask(pass, this.objData)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 补充信息处理
    supplementaryInformationValue() {
      const value = this.formControl
      const supplementaryInformationObj = {
        frontValue: {
          totalTaxLastYear: value.valueAnalysis_talTaxSalesLastYear,
          totalTaxPreviousYear: value.valueAnalysis_talTaxSalesOfThePreviousYear,
          netProfit: value.valueAnalysis_retainedProfits,
          totalTaxPayableLastYear: value.valueAnalysis_talAmountOfTaxPayableLastYear,
          totalTaxPayablePreviousYear: value.valueAnalysis_talTaxableAmountOfPreviousYear,
          yearGrowth: value.valueAnalysis_yearSalesGrowthRate,
          annulusGrowth: value.valueAnalysis_sequentialSalesGrowthRate,
          totalAccountsReceivable: value.valueAnalysis_talAccountsReceivable,
          totalStill: value.valueAnalysis_talAmountOfAccountsReceivable,
          collectionRate: value.valueAnalysis_recentYearReturnRate,
        },
        frontCreditInformation: {
          currentConcerns: value.creditInformation_currentFocusOn,
          currentTimes: value.creditInformation_currentSubprime,
          currentSuspicious: value.creditInformation_currentSuspicious,
          currentLoss: value.creditInformation_currentLoss,
          currentOverdue: value.creditInformation_currentOverdue,
          overdueMone: value.creditInformation_M1,
          overdueMtwo: value.creditInformation_M2,
          overdueMthree: value.creditInformation_M3,
          approveLetterQueryTwo: value.creditInformation_nearly2MonthsForApproval,
          approveLetterQueryTetracosa: value.creditInformation_nearly12MonthsForApproval,
          closedCases: value.creditInformation_casesInvolvingEnterprisesInLitigation,
          openCases: value.creditInformation_unresolvedCasesInvolvingLitigationOfEnterprises,
          overdueRate: value.creditInformation_yuqilv,
        },
        frontFinancialData: {
          financialData_currentAssets: value.financialData_currentAssets,
          financialData_currentLiabilities: value.financialData_currentLiabilities,
          financialData_inventory: value.financialData_inventory,
          financialData_netIncreaseInCashAndCashEquivalents: value.financialData_netIncreaseInCashAndCashEquivalents,
          financialData_totalLiabilities: value.financialData_totalLiabilities,
          financialData_totalAssets: value.financialData_totalAssets,
          financialData_netProfit: value.financialData_netProfit,
          financialData_interestExpense: value.financialData_interestExpense,
          financialData_incomeTaxExpense: value.financialData_incomeTaxExpense,
          financialData_operatingRevenue: value.financialData_operatingRevenue,
          financialData_costOfRevenue: value.financialData_costOfRevenue,
          financialData_totalProfit: value.financialData_totalProfit,
          financialData_accountsReceivable: value.financialData_accountsReceivable,
          financialData_endingInventoryBalance: value.financialData_endingInventoryBalance,
          financialData_beginningInventoryBalance: value.financialData_beginningInventoryBalance,
          financialData_cashOutForAcqConstFixedAssets: value.financialData_cashOutForAcqConstFixedAssets,
          financialData_netCashInFromDispFixedAssets: value.financialData_netCashInFromDispFixedAssets,
          financialData_currentPeriodOperatingRevenue: value.financialData_currentPeriodOperatingRevenue,
          financialData_currentPeriodNetProfit: value.financialData_currentPeriodNetProfit,
        },
      }
      this.objData = supplementaryInformationObj
    },
    // 补充信息反处理
    unsupplementaryInformationValue(value) {
      const unsupplementaryInformationObj = {
        valueAnalysis_talTaxSalesLastYear: value.totalTaxLastYear,
        valueAnalysis_talTaxSalesOfThePreviousYear: value.totalTaxPreviousYear,
        valueAnalysis_retainedProfits: value.netProfit,
        valueAnalysis_talAmountOfTaxPayableLastYear: value.totalTaxPayableLastYear,
        valueAnalysis_talTaxableAmountOfPreviousYear: value.totalTaxPayablePreviousYear,
        valueAnalysis_yearSalesGrowthRate: value.yearGrowth,
        valueAnalysis_sequentialSalesGrowthRate: value.annulusGrowth,
        valueAnalysis_talAccountsReceivable: value.totalAccountsReceivable,
        valueAnalysis_talAmountOfAccountsReceivable: value.totalStill,
        valueAnalysis_recentYearReturnRate: value.collectionRate,
        //
        creditInformation_currentFocusOn: value.currentConcerns,
        creditInformation_currentSubprime: value.currentTimes,
        creditInformation_currentSuspicious: value.currentSuspicious,
        creditInformation_currentLoss: value.currentLoss,
        creditInformation_currentOverdue: value.currentOverdue,
        creditInformation_M1: value.overdueMone,
        creditInformation_M2: value.overdueMtwo,
        creditInformation_M3: value.overdueMthree,
        creditInformation_nearly2MonthsForApproval: value.approveLetterQueryTwo,
        creditInformation_nearly12MonthsForApproval: value.approveLetterQueryTetracosa,
        creditInformation_casesInvolvingEnterprisesInLitigation: value.closedCases,
        creditInformation_unresolvedCasesInvolvingLitigationOfEnterprises: value.openCases,
        creditInformation_yuqilv: value.overdueRate,
        // 财务指标
        financialData_currentAssets: value.financialData_currentAssets,
        financialData_currentLiabilities: value.financialData_currentLiabilities,
        financialData_inventory: value.financialData_inventory,
        financialData_netIncreaseInCashAndCashEquivalents: value.financialData_netIncreaseInCashAndCashEquivalents,
        financialData_totalLiabilities: value.financialData_totalLiabilities,
        financialData_totalAssets: value.financialData_totalAssets,
        financialData_netProfit: value.financialData_netProfit,
        financialData_interestExpense: value.financialData_interestExpense,
        financialData_incomeTaxExpense: value.financialData_incomeTaxExpense,
        financialData_operatingRevenue: value.financialData_operatingRevenue,
        financialData_costOfRevenue: value.financialData_costOfRevenue,
        financialData_totalProfit: value.financialData_totalProfit,
        financialData_accountsReceivable: value.financialData_accountsReceivable,
        financialData_endingInventoryBalance: value.financialData_endingInventoryBalance,
        financialData_beginningInventoryBalance: value.financialData_beginningInventoryBalance,
        financialData_cashOutForAcqConstFixedAssets: value.financialData_cashOutForAcqConstFixedAssets,
        financialData_netCashInFromDispFixedAssets: value.financialData_netCashInFromDispFixedAssets,
        financialData_currentPeriodOperatingRevenue: value.financialData_currentPeriodOperatingRevenue,
        financialData_currentPeriodNetProfit: value.financialData_currentPeriodNetProfit,
      }
      this.formControl = unsupplementaryInformationObj
    },
    // 子传父函数
    childByValue(value) {
      const obj = {
        riskControlAdvice: {
          lineOfCredit: value.lineOfCredit,
          riskThat: value.riskThat,
        },
        managementCredit: {
          chooseEnterprise: value.chooseEnterprise,
          selectTheManagement: value.selectTheManagement,
          bondProportion: value.bondProportion1,
          quotaType: value.lineType1,
          recycleType: value.recycleType1,
          // effectiveTime: value.effectiveDate1,
          expireTime: value.dueDate1,
          loanable: value.loanableInto1,
        },
        finalApproveAmount: {
          finalAmount: value.finalBatchOfTheForehead,
          serviceRate: value.serviceTariffing,
          quotaType: value.lineType2,
          recycleType: value.recycleType2,
          // effectiveTime: value.effectiveDate2,
          expireTime: value.dueDate2,
          annualInterestRate: value.financingCostRate,
          loanable: value.loanableInto2,
          bondProportion: value.bondProportion2,
          financingProportion: value.financingProportion,
        },
        score: value.score,
      }
      this.objData = { ...this.objData, ...obj }
    },
    filterBox(arr, condition) {
      return arr.filter(item => item.id == condition)[0]
    },
    handleChange() {
      // 企业信息-财务信息折叠面板收缩控制
      this.changeType = !this.changeType
    },
    handleChange3() {
      // 补充资料折叠面板收缩控制
      this.change3Type = !this.change3Type
    },
    // 预览合同
    viewContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      skipToPreview(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    // 下载合同
    downContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      contractDownload(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    viewGoods() {
      goodsTypeToPath(this.processGoodsObj)
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.creditLimitFinancing {
  margin-bottom: 40px;
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    font-family: SourceHanSansSC-regular;
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        // width: 107px;
        // height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
        padding: 2px 10px;
        box-sizing: border-box;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改外部组件默认样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
.bord-radius-left {
  border-radius: 20px 0 0 20px !important;
  border-left-color: #b3d8ff !important;
}
.bord-radius-right {
  border-radius: 0 20px 20px 0 !important;
  border-right-color: #b3d8ff !important;
}
// 覆盖组件库样式
::v-deep {
  .el-tabs--card {
    .el-tabs__header {
      border-bottom: none;
    }
    .el-tabs__item {
      border: none;
      padding: 0;
    }
    .el-tabs__item:nth-child(2) {
      padding-left: 0 !important;
    }
    .is-plain {
      background: #fff;
      border-color: #b3d8ff;
    }
    .is-plain:hover {
      color: #409eff;
      background: #ecf5ff;
      border-color: #b3d8ff;
    }
  }
  .avue-form__menu {
    display: none;
  }
  .el-input.is-disabled .el-input__inner {
    color: #000;
  }
  .el-textarea.is-disabled .el-textarea__inner {
    color: #000;
  }
  span {
    display: inline-block;
  }
}
</style>
