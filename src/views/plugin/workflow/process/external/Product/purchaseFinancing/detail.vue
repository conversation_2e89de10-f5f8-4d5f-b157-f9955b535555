<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '代采融资申请'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <div class="boxs">
        <div class="boxs-to-apply-for-product">
          <div class="boxs-to-apply-title">
            <h2>融资需求</h2>
            <div class="boxs-to-apply-title-right">
              <span>融资编号:</span>
              <span>{{ financeNo }}</span>
            </div>
          </div>
          <div class="boxs-to-apply-item">
            <div class="boxs-to-apply-item-left">
              <div class="boxs-to-apply-item-left-logo">
                <img :src="processGoodsObj.capitalLogo" />
              </div>
              <div class="boxs-to-apply-item-left-goodname">
                <span @click="viewGoods()">{{
                  processGoodsObj.goodsName
                }}</span>
                <span>{{ processGoodsObj.capitalName }}</span>
              </div>
            </div>
            <div class="boxs-to-apply-item-right">
              <span>代采融资</span>
            </div>
          </div>
          <div class="table-top">
            <el-descriptions
              title=""
              :column="3"
              border
              class="table-descriptions"
            >
              <el-descriptions-item label="融资金额"
                >{{
                  $numChuFun(financeApplyChidren.amount || 0, 10000) | formatMoney
                }}万元</el-descriptions-item
              >
              <el-descriptions-item label="借款期限"
                >{{ financeApplyChidren.loadTerm
                }}{{
                  financeApplyChidren.loadTermUnit == 2 ? '个月' : '天'
                }}</el-descriptions-item
              >
              <el-descriptions-item label=""></el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>

      <!-- 代采订单 -->
      <basic-container v-if="substituteTable.substituteTable">
        <el-collapse v-model="activeNames1" @change="handleChange1">
          <el-collapse-item name="furtherInformation">
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change1Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">代采订单</h1>
                </div>
              </div>
            </template>

            <div class="table-top">
              <el-table
                ref="table1"
                :data="tableData"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
              >
                <el-table-column
                  prop="index"
                  label="#"
                  width="80"
                  v-if="substituteTable.substituteTable_index"
                ></el-table-column>
                <el-table-column
                  v-if="substituteTable.substituteTable_productDetail"
                  prop="goodsInfo"
                  label="货品信息"
                  width="300px"
                >
                  <template slot-scope="scope">
                    <div class="table-goodsInfo">
                      <img :src="scope.row.commodityUrl" alt />
                      <div class="table-goodsInfo-right">
                        <div
                          class="goodsInfo-right-top"
                          @click="handleLinkClick(scope.row.commodityId)"
                        >
                          {{ scope.row.name }}
                        </div>
                        <div class="goodsInfo-right-bottom">
                          规格型号：{{ scope.row.spec }}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="substituteTable.substituteTable_unitPrice"
                  prop="prices"
                  label="发布单价(元)"
                >
                  <template slot-scope="scope">
                    <div class="table-prices">
                      <span>{{ scope.row.unitPrice }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="substituteTable.substituteTable_quantity"
                  prop="quantity"
                  :label="`数量(${unitName})`"
                ></el-table-column>
                <el-table-column
                  v-if="substituteTable.substituteTable_purchasePrice"
                  prop="purchasePrice"
                  label="采购单价(元)"
                  ><template slot-scope="scope">
                    <span>￥{{ scope.row.purchasePrice | formatMoney }}</span>
                  </template></el-table-column
                >
                <el-table-column
                  v-if="substituteTable.substituteTable_financingPrice"
                  prop="financingPrice"
                  label="融资单价(元)"
                >
                  <template slot-scope="scope">
                    <span class="table-text-red"
                      >￥{{ scope.row.financingPrice | formatMoney }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="substituteTable.substituteTable_purchaseTotal"
                  prop="purchaseTotal"
                  label="采购总额(元)"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.purchaseTotal | formatMoney }}</span>
                  </template></el-table-column
                >
                <el-table-column
                  v-if="substituteTable.substituteTable_financingTotal"
                  prop="financingTotal"
                  label="融资总额(元)"
                >
                  <template slot-scope="scope">
                    <span class="table-text-red"
                      >￥{{ scope.row.financingTotal | formatMoney }}</span
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="table-top cloud-collapse">
              <el-descriptions
                title=""
                :column="3"
                border
                class="table-descriptions"
              >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_pickUpManner"
                  label="提货方式"
                  >{{
                    financeApply.pickUpManner == 1 ? '第三方物流配送' : '自提'
                  }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_receiveCompanyName"
                  label="收货对象"
                  >{{ financeApply.receiveCompanyName }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_receiveAddress"
                  label="收货地址"
                  >{{ financeApply.receiveAddress }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_receiveName"
                  label="联系人"
                  >{{ financeApply.receiveName }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_receiveNumber"
                  label="联系方式"
                  >{{ financeApply.receiveNumber }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_deliverTime"
                  label="交付时间"
                  >{{ financeApply.deliverTime }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_receiptStandard"
                  label="收货标准"
                  >{{ financeApply.receiptStandard }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_paymentType"
                  label="代收代付类型"
                  >{{ financeApply.paymentTypes }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="substituteTable.substituteTable_remark"
                  label="备注"
                  >{{ financeApply.remark }}</el-descriptions-item
                >
              </el-descriptions>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 还款试算 -->
      <basic-container v-if="reimbursementTrial.reimbursementTrial">
        <el-collapse v-model="activeNames2" @change="handleChange2">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change2Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>还款试算</span>
                    <!-- <span class="long-string" />
                    <span class="interest-rate"
                      >日利率{{ allDailyInterestRate }}%&nbsp;(年化利率{{
                        allAnnualInterestRate
                      }}%)</span
                    > -->
                  </h1>
                </div>
              </div>
            </template>
            <div
              class="table-top refund"
              v-if="reimbursementTrial.reimbursementTrial_bankInterest"
            >
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>资方费用</span>
                  <span />
                  <span
                    >日利率{{ dailyInterestRate }}%&nbsp;(年化利率{{
                      annualInterestRate
                    }}%)</span
                  >
                </div>
                <div class="title-right-box">
                  计费方式:&nbsp;{{ chargeMode }}
                </div>
              </div>
              <el-table
                ref="table2"
                :data="tableData2"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  v-if="reimbursementTrial.bankInterest_repaymentDate"
                  prop="refundTime"
                  label="还款日期"
                >
                </el-table-column>
                <el-table-column
                  v-if="reimbursementTrial.bankInterest_totalShouldAlso"
                  prop="monthlySupply"
                  label="应还总额"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="reimbursementTrial.bankInterest_repaymentPrincipal"
                  prop="monthlyPrincipal"
                  label="还款本金"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.monthlyPrincipal | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="reimbursementTrial.bankInterest_shouldAlsoInterest"
                  prop="planInterest"
                  label="应还利息"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.planInterest | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  v-for="(item, index) in feeData.showRepaymentPlan
                    .stagRecords[0].planFeeList"
                  :key="item.id"
                  :prop="`amount${index}`"
                  :label="item.feeName"
                >
                </el-table-column> -->
              </el-table>
            </div>
            <div v-if="platformFeeList.length">
              <div
                class="table-top refund"
                v-for="item in platformFeeList"
                :key="item.id"
              >
                <div class="chain-line" />
                <div class="table-title-box" style="margin-top: -10px">
                  <div class="title-left-box">
                    <span>{{ item.parenExpenseName }}</span>
                  </div>
                </div>
                <el-table
                  ref="table3"
                  :data="item.expenseOrderDetailList"
                  :summary-method="getSummaries"
                  show-summary
                  style="width: 100%; margin-top: 13px"
                  class="table-border-style"
                >
                  <el-table-column prop="expenseTypeStr" label="费用名称">
                  </el-table-column>
                  <!-- <el-table-column prop="feeNameType" label="费用类型">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNameType }}
                      </span>
                    </template>
                  </el-table-column> -->
                  <el-table-column
                    prop="repaymentTerm"
                    label="期数"
                  >
                  </el-table-column>
                  <el-table-column prop="feeNodeStr" label="计算节点">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="collectFeesNodeStr" label="收费节点">
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.collectFeesNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeFormulaName" label="计费方式">
                  </el-table-column>
                  <el-table-column prop="amount" label="应付金额">
                    <template slot-scope="scope">
                      <span>￥{{ scope.row.amount | formatMoney }} </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <div
              class="chain-line"
              v-if="
                reimbursementTrial.reimbursementTrial_costPlatform &&
                financeApplyChidren.chargeMethod === 2
              "
            />

            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥ {{ sum | formatMoney }} </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 补充资料 -->
      <basic-container v-if="furtherInformationAble">
        <el-collapse v-model="activeNames3" @change="handleChange3">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change3Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>补充资料</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="table-top refund">
              <FilePreviewHWP :formUpload="fileData" />
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 保证金 -->
      <!--<basic-container
        v-if="financeApply.lendingMethod == 1 && bondTable.bondTable"
      >
        <el-collapse v-model="activeNames4" @change="handleChange4">
          <el-collapse-item name="furtherInformation">
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change4Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>保证金</span>
                  </h1>
                </div>
              </div>
            </template>
            <div class="table-top refund">
              <el-table
                ref="table5"
                :data="tableData5"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :summary-method="getSummaries1"
                show-summary
              >
                <el-table-column
                  v-if="bondTable.bondTable_type"
                  prop="cashDepositType"
                  label="保证金类型"
                  ><template slot-scope="scope">
                    <span
                      class="border-box"
                      v-if="scope.row.cashDepositType == 1"
                      >初始保证金</span
                    >
                    <span
                      class="border-box"
                      v-else-if="scope.row.cashDepositType == 2"
                      >追加保证金</span
                    >
                  </template></el-table-column
                >
                <el-table-column
                  v-if="bondTable.bondTable_payNode"
                  prop="payNodes"
                  label="支付节点"
                  ><template slot-scope="scope">
                    <span class="border-box">{{
                      scope.row.payNode === 1 ? '放款申请' : ''
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="bondTable.bondTable_chargeMode"
                  prop="cashDepositRates"
                  label="计算方式"
                  ><template slot-scope="scope">
                    <span
                      >融资金额*保证金比例({{
                        scope.row.cashDepositRate
                      }}%)</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="bondTable.bondTable_returnMethods"
                  prop="refundTypes"
                  label="退还方式"
                >
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.refundType === 1
                        ? '信贷结清后退还'
                        : '同还款比例等比退还'
                    }}</span>
                  </template></el-table-column
                >
                <el-table-column
                  v-if="bondTable.bondTable_amount"
                  prop="payableAmount"
                  label="应缴金额(元)"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.payableAmount | formatMoney }} </span>
                  </template>
                </el-table-column>
              </el-table>
              <div v-if="financeApply.lendingMethod == 1">
                <payForm
                  ref="form"
                  type="1"
                  :pageData="pageData"
                  :platformFeeObj="platformFeeObj"
                  :purchaseBondArr="purchaseBondArr"
                  :bankInfo="platformAccount"
                  :platformFee="bondTable"
                  :platformImg="bailImg"
                  :platformPdf="bailPdf"
                ></payForm>
              </div>
              <!~~ <div class="form-content" v-if="financeApply.lendingMethod == 1">
                <div class="form-container">
                  <avue-form
                    ref="form1"
                    :option="payOption"
                    v-model="form1"
                  ></avue-form>
                </div>
                <div class="payInfo">
                  <div class="pay-box">
                    <template>
                      <div
                        class="form-item"
                        v-for="item of purchaseBondArr"
                        :key="item.id"
                      >
                        <span class="label">{{ item.expenseType }}(元):</span>
                        <span>￥{{ item.money | formatMoney }}</span>
                      </div>
                    </template>
                    <div class="form-item">
                      <span class="label">应还总额(元):</span>
                      <span style="color: red"
                        >￥{{ totalMoney(purchaseBondArr) }}</span
                      >
                    </div>
                    <div class="payImg">
                      <div
                        class="table-bottom"
                        v-if="bondTable.bondTable_checkPaymentVoucher"
                      >
                        <el-button
                          v-if="bailImg.length"
                          @click="handlePreviewImage('bailImg')"
                          >查看支付凭证</el-button
                        >
                        <span
                          class="table-bottom-pdf"
                          v-for="(item, index) in bailPdf"
                          :key="item.link"
                          @click="handlePreviewPdf(item.link)"
                        >
                          附件{{ index + 1 }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div> ~~>
              <!~~ v-if="
                        customForm.repaymentNote_bank_status_button_apply &&
                        customForm.repaymentNote_bank_status_button_apply
                          .readable
                      " ~~>
              <!~~ <template v-if="financeApply.lendingMethod == 1">
                <div class="repayment-status-container">
                  <div class="left-box">
                    <span class="title">订单状态</span>
                    <Tag
                      class="tag"
                      :name="orderStatusMap[pageData.cashDepositPaymentStatus]"
                      color="#697CFF"
                      backgroundColor="#EBF1FF"
                      borderColor="transparent"
                      :radius="true"
                    />
                    <template v-if="bondTable.bondTable_bank">
                      <template v-if="pageData.cashDepositPaymentStatus === 1">
                        <el-button
                          class="button"
                          type="text"
                          @click="handleApplyRepayment({ isEdit: true })"
                          >修改订单状态</el-button
                        >
                      </template>
                      <template v-else>
                        <el-button
                          class="button"
                          type="text"
                          @click="handleApplyRepayment({ isView: true })"
                          >查看</el-button
                        >
                        <el-button
                          class="button"
                          type="text"
                          style="margin-left: 12px"
                          @click="handleApplyRepayment({ isEdit: true })"
                          >编辑</el-button
                        >
                      </template>
                    </template>
                  </div>
                  <div
                    class="table-bottom"
                    v-if="bondTable.bondTable_checkPaymentVoucher"
                  >
                    <el-button
                      v-if="bailImg.length"
                      @click="handlePreviewImage('bailImg')"
                      >查看支付凭证</el-button
                    >
                    <span
                      class="table-bottom-pdf"
                      v-for="(item, index) in bailPdf"
                      :key="item.link"
                      @click="handlePreviewPdf(item.link)"
                    >
                      附件{{ index + 1 }}
                    </span>
                  </div>
                </div>
              </template> ~~>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>-->
      <!-- 缴纳费用 -->
      <basic-container
        v-if="
          financeApply.lendingMethod == 1 &&
          financeApplyChidren.chargeMethod === 2 &&
          platformFee.platformFee
        "
      >
        <el-collapse v-model="activeNames5" @change="handleChange5">
          <el-collapse-item name="furtherInformation">
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change5Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>缴纳费用</span>
                  </h1>
                </div>
              </div>
            </template>
            <Feiyongwaihezi
              ref="feiyongwaiheziRef"
              :feiyongList="expenseInfoExpenseList"
              :isLook="true"
            />
            <!--<div class="table-top refund">
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>平台费用</span>
                </div>
              </div>
              <el-table
                ref="table6"
                :data="tableData3"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :summary-method="getSummaries"
                show-summary
              >
                <el-table-column
                  v-if="platformFee.platformFee_costOfName"
                  prop="name"
                  label="费用名称"
                >
                </el-table-column>
                <el-table-column
                  v-if="platformFee.platformFee_typeOfExpense"
                  prop="feeNameType"
                  label="费用类型"
                >
                  <template slot-scope="scope">
                    <span class="border-box">{{
                      scope.row.feeNameType
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="platformFee.platformFee_payTheNode"
                  prop="chargePoint"
                  label="支付节点"
                >
                  <template slot-scope="scope">
                    <span class="border-box">
                      {{ scope.row.chargePoint }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="platformFee.platformFee_chargeMode"
                  prop="calculationStr"
                  label="计费方式"
                >
                </el-table-column>
                <el-table-column
                  v-if="platformFee.platformFee_amountPayable"
                  prop="amount"
                  label="应付金额"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.amount !== '待录入'"
                      >￥{{ scope.row.amount | formatMoney }}
                    </span>
                    <span v-else>
                      {{ scope.row.amount }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
              <div v-if="financeApply.lendingMethod == 1">
                <payForm
                  ref="form1"
                  type="2"
                  :pageData="pageData"
                  :platformFeeObj="purchaseBondObj"
                  :purchaseBondArr="platformFeeArr"
                  :bankInfo="platformAccount"
                  :platformFee="platformFee"
                  :platformImg="platformImg"
                  :platformPdf="platformPdf"
                ></payForm>
              </div>

              <!~~ <div class="form-content" v-if="financeApply.lendingMethod == 1">
                <div class="form-container">
                  <avue-form
                    ref="form"
                    :option="payOption"
                    v-model="form"
                  ></avue-form>
                </div>
                <div class="payInfo">
                  <div class="pay-box">
                    <template>
                      <div
                        class="form-item"
                        v-for="item of platformFeeArr"
                        :key="item.id"
                      >
                        <span class="label">{{ item.expenseType }}(元):</span>
                        <span>￥{{ item.money | formatMoney }}</span>
                      </div>
                    </template>
                    <div class="form-item">
                      <span class="label">应还总额(元):</span>
                      <span style="color: red"
                        >￥{{ totalMoney(platformFeeArr) }}</span
                      >
                    </div>
                    <div class="payImg">
                      <div
                        class="table-bottom"
                        v-if="platformFee.platformFee_checkPaymentVoucher"
                      >
                        <el-button
                          v-if="platformImg.length"
                          @click="handlePreviewImage('platformImg')"
                          >查看支付凭证</el-button
                        >
                        <span
                          class="table-bottom-pdf"
                          v-for="(item, index) in platformPdf"
                          :key="item.link"
                          @click="handlePreviewPdf(item.link)"
                        >
                          附件{{ index + 1 }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div> ~~>
              <!~~ v-if="
                        customForm.repaymentNote_bank_status_button_apply &&
                        customForm.repaymentNote_bank_status_button_apply
                          .readable
                      " 放在 Tag下~~>
              <!~~ <template v-if="financeApply.lendingMethod == 1">
                <div class="repayment-status-container">
                  <div class="left-box">
                    <span class="title">订单状态</span>
                    <Tag
                      class="tag"
                      :name="orderStatusMap[pageData.paymentStatus]"
                      color="#697CFF"
                      backgroundColor="#EBF1FF"
                      borderColor="transparent"
                      :radius="true"
                    />
                    <template v-if="platformFee.platformFee_bank">
                      <template v-if="pageData.paymentStatus === 1">
                        <el-button
                          class="button"
                          type="text"
                          @click="handleEditOrderStatus({ isEdit: true })"
                          >修改订单状态</el-button
                        >
                      </template>
                      <template v-else>
                        <el-button
                          class="button"
                          type="text"
                          @click="handleEditOrderStatus({ isView: true })"
                          >查看</el-button
                        >
                        <el-button
                          class="button"
                          type="text"
                          style="margin-left: 12px"
                          @click="handleEditOrderStatus({ isEdit: true })"
                          >编辑</el-button
                        >
                      </template>
                    </template>
                  </div>
                  <div
                    class="table-bottom"
                    v-if="platformFee.platformFee_checkPaymentVoucher"
                  >
                    <el-button
                      v-if="platformImg.length"
                      @click="handlePreviewImage('platformImg')"
                      >查看支付凭证</el-button
                    >
                    <span
                      class="table-bottom-pdf"
                      v-for="(item, index) in platformPdf"
                      :key="item.link"
                      @click="handlePreviewPdf(item.link)"
                    >
                      附件{{ index + 1 }}
                    </span>
                  </div>
                </div>
              </template> ~~>
            </div>-->
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 合同签署 -->
      <basic-container
        v-if="
          financeApply.lendingMethod == 1 && contractSigning.contractSigning
        "
      >
        <el-collapse v-model="activeNames6" @change="handleChange6">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change6Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">合同签署</h1>
                </div>
              </div>
            </template>
            <div class="table-top refund">
              <el-table
                ref="table6"
                :data="tableData4"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
              >
                <el-table-column
                  v-if="contractSigning.contractSigning_index"
                  prop="index"
                  label="#"
                  width="60"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="contractSigning.contractSigning_contractNumber"
                  prop="contractCode"
                  label="合同编号"
                >
                </el-table-column>
                <el-table-column
                  v-if="contractSigning.contractSigning_contractTitle"
                  prop="contractName"
                  label="合同标题"
                ></el-table-column>
                <el-table-column
                  v-if="contractSigning.contractSigning_creationTime"
                  prop="createTime"
                  label="创建时间"
                ></el-table-column>
                <el-table-column
                  v-if="contractSigning.contractSigning_signTheState"
                  prop="statusText"
                  label="签署状态"
                >
                  <template slot-scope="scope">
                    <el-tag effect="plain" type="success">{{
                      scope.row.statusText
                    }}</el-tag>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="contractSigning.contractSigning_operation"
                  prop="action"
                  label="操作"
                >
                  <div class="table-action">
                    <el-button type="text">查看</el-button>
                    <el-button type="text">下载</el-button>
                  </div>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 融资端申请的内置表单渲染 -->
      <VFormRenderP :vFormDataObj="vFormDataObj" :isDefaultExpansion="true" />

      <!-- 内置表单渲染 -->
      <template v-if="inlayFormShow">
        <basic-container>
          <div class="wf-theme-default">
            <avue-form
              v-if="
                inlayOption &&
                ((inlayOption.column && inlayOption.column.length > 0) ||
                  (inlayOption.group && inlayOption.group.length > 0))
              "
              v-model="inlayForm"
              ref="inlayFormRef"
              :defaults.sync="inlayDefaults"
              :option="inlayOption"
              :upload-preview="handleUploadPreview"
            >
            </avue-form>
          </div>
        </basic-container>
      </template>
    </template>

    <EditStatusDialog
      ref="editStatusDialogRef"
      @handleViewImg="handlePreviewImage"
      @handleViewPdf="handlePreviewPdf"
      @handleConfirm="handleEditStatusDialogConfirm"
    />

    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import FilePreview from '@/components/file-preview'
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import { formatMoney, formatMoneyEventRound } from '@/util/filter.js'
import FilePreviewHWP from '@/views/plugin/workflow/process/external/Product/creditLimitFinancing/component/preview-documents/index.vue'
import Tag from '@/views/customer/archives/components/Tag/index.vue'
import EditStatusDialog from './components/EditStatusDialog/index.vue'
import payForm from './payForm'
import Feiyongwaihezi from '@/components/feiyongwaihezi'
import {
  getDictionary,
  repaymentPurchaseCalculation,
  platformExpensesList2,
  getByGoodsIdAndUserId,
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import { getPurchaseBondData } from '@/api/purchase/purchaseplan'
import insideOutsideForm from '@/views/plugin/workflow/mixins/inside-outside-form'
import VFormRenderP from '@/components/VFormRenderP/index.vue'

export default {
  mixins: [customExForm, insideOutsideForm],
  components: {
    WfButton,
    WfFlow,
    FilePreviewHWP,
    FilePreview,
    Tag,
    EditStatusDialog,
    payForm,
    VFormRenderP,
    Feiyongwaihezi,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNames2() {
      this.$refs.table2.$ready = false
    },
    activeNames3() {
      this.$refs.table3.$ready = false
    },
    activeNames6() {
      this.$refs.table6.$ready = false
    },
    platformFeeList: {
      handler(val) {
        let num = this.sum1
        if (val && val.length) {
          for (const item of val) {
            const cunArr = item.expenseOrderDetailList
            if (cunArr && cunArr.length) {
              for (const cItem of cunArr) {
                if (cItem.amount) {
                  num = this.$numJiaFun(num, cItem.amount)
                }
              }
            }
          }
        }
        this.sum = num
      },
      immediate: false,
      deep: true,
    },
    // tableData3: {
    //   handler(val) {
    //     if (val) {
    //       let num = 0
    //       for (const item of val) {
    //         num += Number(item.amount) || 0
    //       }
    //       // Number(num)
    //       this.sum =  + Number(this.sum1)
    //     }
    //   },
    //   immediate: false,
    //   deep: true,
    // },
  },
  data() {
    return {
      activeName: 'first',
      form: {},
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      activeNames1: ['furtherInformation'],
      activeNames2: ['furtherInformation'],
      activeNames3: ['furtherInformation'],
      activeNames4: ['furtherInformation'],
      activeNames5: ['furtherInformation'],
      activeNames6: ['furtherInformation'],
      change1Type: true,
      change2Type: true,
      change3Type: true,
      change4Type: true,
      change5Type: true,
      change6Type: true,
      processGoodsObj: {},
      financeNo: '',
      financeApplyChidren: {},
      financeApply: {},
      allAnnualInterestRate: 0,
      allDailyInterestRate: 0,
      dailyInterestRate: 0,
      annualInterestRate: 0,
      calculation: false,
      chargeMode: '随借随还',
      fileData: [], // 补充资料数据
      sum: 0,
      sum1: 0,
      tableData: [],
      unitName: '',
      tableData2: [],
      tableData3: [],
      tableData3copy: [],
      tableData4: [
        {
          contractCode: '*************',
          index: 1,
          contractName: '借款借据合同',
          createTime: '2016-05-04 12:34:20',
          statusText: '已签署',
        },
      ],
      ruleFrom: {
        bailBankName: '',
        bailBankCard: '',
        bailPayTime: '',
        platformBankName: '',
        platformBankCard: '',
        platformPayTime: '',
      },
      tableData5: [],
      tableData6: [],
      substituteTable: {},
      reimbursementTrial: {},
      bondTableDisabled: {},
      furtherInformationAble: false, // 补充资料
      bondTable: {}, // 保证金
      contractSigning: {}, // 合同
      platformFee: {}, //缴纳费用
      bailImg: [], // 保证金图片类型
      bailPdf: [], //  保证金pdf
      platformPdf: [], // 缴纳费用PDF
      platformImg: [], // 缴纳费用片类型
      pdfSrc: null,
      // 修改订单状态
      pageData: { cashDepositPaymentStatus: 1, paymentStatus: 1 },
      purchaseBondArr: [],
      purchaseBondObj: {},
      platformFeeArr: [],
      platformFeeObj: {},
      orderStatusArr: [],
      orderStatusMap: {},
      platformAccount: {
        bank: '',
        account: '',
      },
      // 其他费用的
      platformFeeList: [],
      // 所有的费用
      feeData: { showRepaymentPlan: { stagRecords: [{ planFeeList: [] }] } },
      // 融资端内置表单数据存储
      vFormDataObj: {},
      expenseInfoExpenseList: [],
      expenseInfoExpenseListCopy: [],
    }
  },
  created() {
    this.orderStatusArr = [
      {
        id: '1506170254753021954',
        key: 1,
        value: '待付款',
      },
      {
        id: '1506170310570819585',
        key: 2,
        value: '已付款',
      },
      {
        id: '1506170361485475841',
        key: 3,
        value: '已关闭',
      },
    ]
    this.orderStatusMap = {
      1: '待付款',
      2: '已付款',
      3: '已关闭',
    }
  },
  methods: {
    totalMoney(Arr) {
      let repayTotal = 0
      for (const item of Arr) {
        repayTotal = this.$numJiaFun(repayTotal, item.money)
      }
      return repayTotal
    },

    // 合计
    // allMonrySum(item) {
    //   // this.sum1 = 0
    //   this.sum1 += Number(item.monthlySupply)
    //   if (this.platformFeeList.length) {
    //     this.platformFeeList.forEach(item => {
    //       if (item.expenseOrderDetailList.length) {
    //         this.sum1 += Number(
    //           item.expenseOrderDetailList[
    //             item.expenseOrderDetailList.length - 1
    //           ].amount
    //         )
    //       }
    //     })
    //   }
    // },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        // 融资端申请的内置表单数据
        if (res.actExTaskForm && res.actExFormTaskData) {
          this.vFormDataObj = {
            jsonData: res.actExTaskForm,
            valueData: res.actExFormTaskData,
          }
        }

        // 内置表单start
        const { process, form } = res
        this.inlayProcess = process
        const { variables } = process
        const { allForm, indepFormKey } = form
        if (allForm) {
          this.indepFormKey = indepFormKey
          const { option, vars } = this.handleResolveOption(
            eval('(' + allForm + ')'),
            form.taskForm,
            // status
            'finished'
          )
          option.menuBtn = false
          const myVariables = {}
          for (let key in variables) {
            // if (!variables[key]) delete variables[key]
            if (key.indexOf('@_@') !== -1) {
              const myKey = key.split('@_@')
              if (myKey.length > 1) myVariables[myKey[1]] = variables[key]
            }
          }
          const co = option.column
          const gr = option.group
          if ((co && co.length) || (gr && gr.length)) {
            this.inlayFormShow = true
          }
          this.inlayOption = option
          this.inlayVars = vars
          this.inlayForm = myVariables
        }
        // 内置表单end

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        // const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm
        // 流程产品信息
        const { processGoodsInfo, financeNo, financeApply, bail, platform, expenseInfoExpenseList } =
          variables
        if (expenseInfoExpenseList && expenseInfoExpenseList.length) {
          this.expenseInfoExpenseList = expenseInfoExpenseList
        }

        // const params = {
        //   goodId: financeApply.financeApply.goodsId,
        //   userId: variables.userId,
        // }
        // 平台账号请求
        // this.getByGoodsIdAndUserIdFun(params)

        this.processGoodsObj = processGoodsInfo
        this.financeApplyChidren = financeApply.financeApply
        this.financeApply = financeApply
        this.financeNo = financeNo
        this.tableData =
          financeApply.purchaseCommodityList &&
          financeApply.purchaseCommodityList.length
            ? financeApply.purchaseCommodityList.map((item, index) => {
                return {
                  ...item,
                  index: index + 1,
                }
              })
            : []
        if (this.tableData.length) {
          this.unitName = this.tableData[0].unitName
        }
        // 还款试算
        if (financeNo) {
          repaymentPurchaseCalculation({financeNo:financeNo,type:1}).then(res => {
            const { data: resData, code } = res.data
            if (code == 200) {
              const arr = []
              this.dailyInterestRate = resData.showRepaymentPlan.dayRate // 银行日利率
              this.annualInterestRate = resData.showRepaymentPlan.yearRate // 银行年利率
              this.feeData = resData
              //  资方统一清分需要处理的逻辑
              let Map = {}
              const { charge_method } = this.variables
              if (charge_method == 1) {
                resData.showRepaymentPlan.stagRecords[0].planFeeList.forEach(
                  (item, index) => {
                    Map[item.feeName] = {
                      index,
                      collectFeeMethod: item.collectFeeMethod,
                      amount: item.amount,
                    }
                  }
                )
              } else {
                resData.expenseOrderDetailFinanceVos.forEach(item => {
                  const cunArr = item.expenseOrderDetailList
                  if (cunArr && cunArr.length) {
                    for (const item of cunArr ) {
                      item.repaymentTerm = item.repaymentTerm
                      ? item.repaymentTerm + '期'
                      : '--'
                      if (item.calculation === 1) {
                        if (variables.platformExpenses) {
                          cunArrChengXunHuan: for (const cItem of variables.platformExpenses) {
                            const cunArr = cItem.expenseOrderDetailList
                              if (cunArr && cunArr.length) {
                                for (const ditem of cunArr) {
                                  if (item.name == ditem.name) {
                                    item.amount = ditem.amount
                                    break cunArrChengXunHuan
                                  }
                                }
                              }
                          }
                        } else {
                          item.amount = ''
                        }
                      }
                      if (!this.calculation && item.calculation === 1) {
                        this.calculation = true
                      }
                    }
                  }
                  // let amount = item.expenseOrderDetailList.reduce((prve, next) => {
                  //   if (next.feeFormulaName == '手填') {
                  //     next.amount = '人工计算'
                  //     return prve
                  //   }
                  //   return prve + Number(next.amount)
                  // }, 0)
                })
                this.platformFeeList = resData.expenseOrderDetailFinanceVos
              }
              for (const item of resData.showRepaymentPlan.stagRecords) {
                // 这是随借随还的
                arr.push({
                  // term: '1期',
                  refundTime: item.refundTime,
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })
                arr.push({
                  // term: '总计:',
                  refundTime: '总计:',
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })
                if (charge_method == 1) {
                  item.planFeeList.forEach((citem, cindex) => {
                    arr[0][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
                    arr[1][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
                  })
                }
                // this.allMonrySum(item)
                this.sum1 = this.$numJiaFun(this.sum1, item.monthlySupply)
              }

              this.tableData2 = arr
            }
          })

          // 获取保证金数据
          getPurchaseBondData({ financeNo }).then(({ data }) => {
            let list2 = []
            if (data.code === 200 && data.data) {
              list2.push(data.data)
              this.purchaseBondArr = [
                {
                  expenseType:
                    data.data.cashDepositType === 1
                      ? '初始保证金'
                      : '追加保证金',
                  money: data.data.payableAmount,
                },
              ]
              this.purchaseBondObj.amount = this.totalMoney(
                this.purchaseBondArr
              )
            }
            this.tableData5 = list2
          })
        }

        // this.allAnnualInterestRate =
        //   Number(financeApply.financeApply.annualInterestRate) +
        //   Number(financeApply.financeApply.serviceRate) // 总年利率
        // this.allDailyInterestRate = (
        //   Number(this.allAnnualInterestRate) / 360
        // ).toFixed(3) // 总日利率
        // this.dailyInterestRate = financeApply.financeApply.dailyInterestRate // 银行日利率
        // this.annualInterestRate = financeApply.financeApply.annualInterestRate // 银行年利率
        if (financeApply.financeApply.repaymentType === 1) {
          // getDictionary('goods_billing_method').then(({ data }) => {
          //   const list = []
          //   if (data.code === 200 && data.data) {
          //     for (const item of data.data) {
          //       list.push({
          //         key: item.dictKey,
          //         value: item.dictValue,
          //         id: item.id,
          //       })
          //     }
          //     this.chargeMode = resList.filter(
          //       itemed => itemed.key == financeApply.financeApply.repaymentMode
          //     )[0].value
          //   }
          // })
        } else {
          this.chargeMode = '随借随还'
        }

        // 获取补充资料
        if (
          financeApply.customerMaterial &&
          financeApply.customerMaterial.supplementMaterial
        ) {
          let list = []
          let uploadArr = []
          let supplementMaterial = JSON.parse(
            financeApply.customerMaterial.supplementMaterial
          )
          for (const item of supplementMaterial) {
            uploadArr = item.uploadArr.map(child => {
              return {
                ...child,
                url: child.url || child.defaultUrl,
                fileType: child.isPdf ? 'pdf' : '',
              }
            })
            list.push({
              ...item,
              uploadArr,
            })
          }
          this.fileData = list
        }

        // 代付类型
        getDictionary('goods_bank_card_collection_type').then(({ data }) => {
          let paymentTypes = null
          if (data.code === 200 && data.data) {
            let list = data.data.find(
              item => Number(item.dictKey) === financeApply.paymentType
            )
            paymentTypes = list ? list.dictValue : null
          }
          this.financeApply = { ...financeApply, paymentTypes }
        })
        // 查看保证金的图片
        if (bail && bail.length) {
          for (const item of bail) {
            if (item.link.includes('pdf')) {
              this.bailPdf.push(item)
            } else {
              this.bailImg.push({ ...item, url: item.link })
            }
          }
        }

        if (platform && platform.length) {
          for (const item of platform) {
            if (item.link.includes('pdf')) {
              this.platformPdf.push(item)
            } else {
              this.platformImg.push({ ...item, url: item.link })
            }
          }
        }
        //控制显隐问题
        const taskArrkey1 = [
          'substituteTable',
          'substituteTable_index',
          'substituteTable_productDetail',
          'substituteTable_unitPrice',
          'substituteTable_quantity',
          'substituteTable_purchasePrice',
          'substituteTable_financingPrice',
          'substituteTable_purchaseTotal',
          'substituteTable_financingTotal',
          'substituteTable_pickUpManner',
          'substituteTable_receiveCompanyName',
          'substituteTable_receiveAddress',
          'substituteTable_receiveName',
          'substituteTable_receiveNumber',
          'substituteTable_deliverTime',
          'substituteTable_receiptStandard',
          'substituteTable_paymentType',
          'substituteTable_remark',
        ]
        const taskFormFilter1 = taskForm.filter(
          item => taskArrkey1.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter1) {
          this.substituteTable[item.id] = true
        }
        // this.ruleFrom = {
        //   bailBankName: variables.bailBankName || '',
        //   bailBankCard: variables.bailBankCard || '',
        //   bailPayTime: variables.bailPayTime || '',
        //   platformBankName: variables.platformBankName || '',
        //   platformBankCard: variables.platformBankCard || '',
        //   platformPayTime: variables.platformPayTime || '',
        // }
        // 保证金

        this.purchaseBondObj.bank = variables.bailBankName || ''
        this.purchaseBondObj.payAccount = variables.bailBankCard || ''
        this.purchaseBondObj.endDate = variables.bailPayTime || ''
        this.purchaseBondObj.status = variables.cashDepositPaymentStatus || 1
        this.purchaseBondObj.remark = ''
        this.pageData.cashDepositPaymentStatus =
          variables.cashDepositPaymentStatus || 1

        // 平台费
        this.platformFeeObj.bank = variables.platformBankName || ''
        this.platformFeeObj.payAccount = variables.platformBankCard || ''
        this.platformFeeObj.endDate = variables.platformPayTime || ''
        this.platformFeeObj.remark = ''
        this.platformFeeObj.status = variables.cashDepositPaymentStatus || 1
        this.pageData.paymentStatus = variables.cashDepositPaymentStatus || 1
        // 还款试算的显隐
        const taskArrkey2 = [
          'reimbursementTrial',
          'reimbursementTrial_bankInterest',
          'bankInterest_periods',
          'bankInterest_repaymentDate',
          'bankInterest_totalShouldAlso',
          'bankInterest_repaymentPrincipal',
          'bankInterest_shouldAlsoInterest',
          'reimbursementTrial_costPlatform',
          'payAFee_costOfName',
          'payAFee_typeOfExpense',
          'payAFee_payTheNode',
          'payAFee_chargeMode',
          'payAFee_amountPayable',
        ]
        const taskFormFilter2 = taskForm.filter(
          item => taskArrkey2.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter2) {
          this.reimbursementTrial[item.id] = true
        }
        // 补充资料
        let index = taskForm.findIndex(item => item.id === 'furtherInformation')
        this.furtherInformationAble =
          index > -1 ? taskForm[index].readable : false

        // 保证金
        const taskArrkey3 = [
          'bondTable',
          'bondTable_type',
          'bondTable_payNode',
          'bondTable_chargeMode',
          'bondTable_returnMethods',
          'bondTable_amount',
          'bondTable_checkPaymentVoucher',
          'bondTable_bank',
          'bondTable_bankCard',
          'bondTable_payTime',
        ]
        const taskFormFilter3 = taskForm.filter(
          item => taskArrkey3.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter3) {
          this.bondTable[item.id] = true
        }
        // const taskFormWriter = [
        //   'bondTable_bank',
        //   'bondTable_bankCard',
        //   'bondTable_payTime',
        //   'platformFee_bank',
        //   'platformFee_bankCard',
        //   'platformFee_payTime',
        // ]
        // // 表单的提交
        // const taskFormWriteFilter = taskForm.filter(
        //   item => taskFormWriter.includes(item.id) && item.writeable
        // )
        // for (const item of taskFormWriteFilter) {
        //   this.bondTableDisabled[item.id] = true
        // }
        // 合同显隐
        const taskArrkey4 = [
          'contractSigning',
          'contractSigning_index',
          'contractSigning_contractNumber',
          'contractSigning_contractTitle',
          'contractSigning_creationTime',
          'contractSigning_signTheState',
          'contractSigning_operation',
        ]
        const taskFormFilter4 = taskForm.filter(
          item => taskArrkey4.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter4) {
          this.contractSigning[item.id] = true
        }

        // 缴纳费用
        const taskArrkey5 = [
          'platformFee',
          'platformFee_costOfName',
          'platformFee_typeOfExpense',
          'platformFee_payTheNode',
          'platformFee_chargeMode',
          'platformFee_amountPayable',
          'platformFee_bank',
          'platformFee_bankCard',
          'platformFee_payTime',
          'platformFee_checkPaymentVoucher',
        ]
        const taskFormFilter5 = taskForm.filter(
          item => taskArrkey5.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter5) {
          this.platformFee[item.id] = true
        }

        // getDictionary('goods_expense_rule_fee_node').then(res => {
        //   const resData = res.data
        //   if (resData.code == 200) {
        //     // 处理字典数据
        //     const resList = []
        //     for (const item of resData.data) {
        //       resList.push({
        //         key: item.dictKey,
        //         value: item.dictValue,
        //         id: item.id,
        //       })
        //     }
        //     getDictionary('goods_expense_rule_type').then(res => {
        //       const resData = res.data
        //       if (resData.code == 200) {
        //         // 处理字典数据
        //         const resList1 = []
        //         for (const item of resData.data) {
        //           resList1.push({
        //             key: item.dictKey,
        //             value: item.dictValue,
        //             id: item.id,
        //           })
        //         }
        //         //
        //         platformExpensesList2({ financeNo, type: 1 }).then(res => {
        //           const { data: resData, code } = res.data
        //           const arr = []
        //           if (code == 200) {
        //             for (const item of resData) {
        //               if (item.calculation == 1) {
        //                 this.calculation = true
        //               }
        //               // 过滤出当前的支付节点
        //               const chargePointFilter = resList.filter(
        //                 itemed => itemed.key == item.feeNode
        //               )
        //               // 过滤出当前的费用类型
        //               const expenseTypeFilter = resList1.filter(
        //                 itemed => itemed.key == item.expenseType
        //               )
        //               arr.push({
        //                 name: item.name,
        //                 feeNameType: expenseTypeFilter[0]
        //                   ? expenseTypeFilter[0].value
        //                   : '',
        //                 chargePoint: chargePointFilter[0]
        //                   ? chargePointFilter[0].value
        //                   : '',
        //                 calculationStr:
        //                   item.calculation != 1
        //                     ? item.feeFormulaName
        //                     : '手动录入',
        //                 amount: item.calculation != 1 ? item.amount : 0,
        //                 calculation: item.calculation,
        //               })
        //             }
        //             if (variables.platformExpenses) {
        //               for (const item of variables.platformExpenses) {
        //                 for (const itemed of arr) {
        //                   if (itemed.name == item.name) {
        //                     itemed.amount = item.amount
        //                     break
        //                   }
        //                 }
        //               }
        //             }
        //           }
        //           // 平台费用订单状态修改数据处理
        //           const ExpensesArr = {}
        //           for (const item of arr) {
        //             let changeT = false
        //             if (JSON.stringify(ExpensesArr) !== '{}') {
        //               for (const key in ExpensesArr) {
        //                 if (key === item.feeNameType) {
        //                   changeT = true
        //                   ExpensesArr[key] += Number(item.amount)
        //                   break
        //                 }
        //               }
        //               if (!changeT) {
        //                 ExpensesArr[item.feeNameType] = Number(item.amount)
        //               }
        //             } else {
        //               ExpensesArr[item.feeNameType] = Number(item.amount)
        //             }
        //           }
        //           for (const key in ExpensesArr) {
        //             this.platformFeeArr.push({
        //               id: key,
        //               expenseType: key,
        //               money: Number(ExpensesArr[key]),
        //             })
        //           }
        //           this.tableData3 = arr
        //           this.tableData3copy = resData // 专门给后端的数据，不处理
        //         })
        //       }
        //     })
        //   }
        // })
      })
    },

    handleChange1() {
      // 质押品折叠面板收缩控制
      this.change1Type = !this.change1Type
    },
    handleChange2() {
      // 还款试算折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
    handleChange3() {
      // 还款试算折叠面板收缩控制
      this.change3Type = !this.change3Type
    },
    handleChange4() {
      // 合同签署折叠面板收缩控制
      this.change4Type = !this.change4Type
    },
    handleChange5() {
      // 缴纳费用
      this.change5Type = !this.change5Type
    },
    handleChange6() {
      // 合同签署
      this.change6Type = !this.change6Type
    },
    // 跳转商品详情
    handleLinkClick(id) {
      if (id) {
        this.$router.push(
          `/commodity/lookProducts/` +
            Buffer.from(JSON.stringify(id)).toString('base64')
        )
      }
    },

    filterBox(arr, condition) {
      return arr.filter(item => item.id == condition)[0]
    },

    async handleExamine(pass) {
      // if (this.calculation && pass) {
      //   for (const item of this.tableData3) {
      //     if (
      //       item.amount === '' ||
      //       item.amount === undefined ||
      //       item.amount === null
      //     ) {
      //       this.$message.error('请手动录入应付金额')
      //       return
      //     }
      //   }
      // }
      // this.tableData3copy.forEach((item, index) => {
      //   if (item.calculation === 1) {
      //     item.amount = this.tableData3[index].amount
      //   }
      // })
      // let formType
      // if (this.financeApply.lendingMethod == 1 && pass) {
      //   const resData = this.$refs.form.check()
      //   let resData1 = false
      //   if (this.variables.charge_method == 1) {
      //     resData1 = true
      //   } else {
      //     resData1 = this.$refs.form1.check()
      //   }

      //   if (!resData || !resData1) {
      //     return this.$message.warning('订单状态完成才能通过')
      //   }
      //   resData.validate((valid, done, msg) => {
      //     if (!valid) {
      //       let errMsg = Object.values(msg)[0].message
      //       if (!errMsg) {
      //         errMsg = Object.values(msg)[0][0].message
      //         if (!errMsg) {
      //           errMsg = '必填项未填'
      //         }
      //       }
      //       this.$message.error(errMsg)
      //       return
      //     }
      //     this.pageData.cashDepositPaymentStatus = 2
      //     done()
      //   })
      //   if (this.variables.charge_method == 2) {
      //     resData1.validate((valid, done, msg) => {
      //       if (!valid) {
      //         let errMsg = Object.values(msg)[0].message
      //         if (!errMsg) {
      //           errMsg = Object.values(msg)[0][0].message
      //           if (!errMsg) {
      //             errMsg = '必填项未填'
      //           }
      //         }
      //         this.$message.error(errMsg)
      //         return
      //       }
      //       this.pageData.paymentStatus = 2
      //       done()
      //     })
      //   }

      //   setTimeout(() => {
      //     if (this.financeApplyChidren.chargeMethod === 2) {
      //       formType =
      //         this.pageData.paymentStatus === 2 &&
      //         this.pageData.cashDepositPaymentStatus === 2
      //     } else {
      //       formType = this.pageData.cashDepositPaymentStatus === 2
      //     }
      //     if (formType) {
      //       let params = {
      //         platformExpenses: this.tableData3copy,
      //         bailBankName: resData.form.bank,
      //         bailBankCard: resData.form.account,
      //         bailPayTime: resData.form.endDate,
      //         cashDepositPaymentStatus:
      //           resData.form.status || this.pageData.cashDepositPaymentStatus,
      //       }
      //       if (this.financeApplyChidren.chargeMethod === 2) {
      //         Object.assign(params, {
      //           platformBankName: resData1.form.bank,
      //           platformBankCard: resData1.form.account,
      //           platformPayTime: resData1.form.endDate,
      //           paymentStatus:
      //             resData1.form.status || this.pageData.paymentStatus,
      //         })
      //       }
      //       this.validataFunction(pass, params)
      //     } else {
      //       this.$message.warning('必填项未填')
      //       return
      //     }
      //   }, 100)
      // } else {
      //   this.validataFunction(pass, { platformExpenses: this.tableData3copy })
      // }
      if (this.financeApply.lendingMethod == 1) {
        if (pass) {
            this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.tongguojiaoyanFun()
        } else {
            this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.bohuijiaoyanFun()
        }
        if (!this.expenseInfoExpenseListCopy) return
        let params = {}
        params.expenseInfoExpenseList = this.expenseInfoExpenseListCopy
        this.validataFunction(pass, params)
      } else {
        for (const item of this.platformFeeList) {
          const cunArr = item.expenseOrderDetailList
          if (cunArr && cunArr.length) {
            for (const item of cunArr ) {
              if (!item.amount) {
                this.$message.warning('请手动录入应付金额')
                return
              }
            }
          }
        }
        this.validataFunction(pass, { platformExpenses: this.platformFeeList })
      }
    },
    // 通过后调取接口函数
    validataFunction(pass, params) {
      this.submitLoading = true
      // if (pass && JSON.stringify(this.inlayVariables) !== '{}') {
      //   Object.assign(params, this.inlayVariables)
      // }
      this.handleCompleteTask(pass, params)
        .then(() => {
          this.$message.success('处理成功')
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 终止后调取接口函数
    async validataterminationFunction() {
      this.submitLoading = true
      let params = {}
      // if (this.financeApply.lendingMethod == 1) {
      //   // const cStatus = this.form.status
      //   // if (cStatus == 1) {
      //   //   this.$message.warning('请修改订单状态')
      //   //   return
      //   // }
      //   const form = this.$refs.form.form
      //   const cStatus = form.status // 保证金
      //   if (cStatus === 1) {
      //     this.$message.warning('请修改订单状态')
      //     return
      //   }

      //   if (cStatus === 2) {
      //     params = {
      //       bailBankName: form.bank,
      //       bailBankCard: form.account,
      //       bailPayTime: form.endDate,
      //       cashDepositPaymentStatus:
      //         form.status || this.pageData.cashDepositPaymentStatus,
      //     }
      //   } else if (cStatus === 3) {
      //     params = {
      //       cashDepositPaymentStatus: form.status,
      //     }
      //   }
      //   if (this.financeApplyChidren.chargeMethod == 1) {
      //     this.handleTerminateProcess(params).then(() => {
      //       this.$message.success('处理成功')
      //       this.handleCloseTag('/business/businessprocess')
      //     })
      //   }
      //   const form1 = this.$refs.form1.form

      //   const pStatus = form1.status // 平台费用

      //   if (this.financeApplyChidren.chargeMethod === 2) {
      //     if (pStatus === 1) {
      //       this.$message.warning('请修改订单状态')
      //       return
      //     }

      //     if (pStatus === 2) {
      //       Object.assign(params, {
      //         platformBankName: form1.bank,
      //         platformBankCard: form1.account,
      //         platformPayTime: form1.endDate,
      //         paymentStatus: form1.status || this.pageData.paymentStatus,
      //       })
      //     } else if (pStatus === 3) {
      //       Object.assign(params, {
      //         paymentStatus: form1.status,
      //         failReason: form1.remark,
      //       })
      //     }
      //   }
      // }
      if (this.financeApply.lendingMethod == 1) { 
        this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.zhongzhijiaoyanFun()
        if (!this.expenseInfoExpenseListCopy) {
          this.submitLoading = false
          return
        }
        params.expenseInfoExpenseList = this.expenseInfoExpenseListCopy
      }

      this.handleTerminateProcess(params).then(() => {
        this.$message.success('处理成功')
        this.handleCloseTag('/business/businessprocess')
      })
      .catch(() => {
        this.submitLoading = false
      })
    },
    viewGoods() {
      this.$router.push({
        path: '/pcontrol/pinformation',
        query: { id: this.financeApplyChidren.goodsId },
      })
      sessionStorage.setItem('look', 'true')
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    getSummaries1(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(formatMoneyEventRound(money))}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },

    handlePreviewImage(props) {
      this.$ImagePreview(this[props], 0, {
        closeOnClickModal: true,
      })
    },
    handlePreviewPdf(targetUrl) {
      this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
    },
    downLoad(url) {
      if (url) {
        window.open(url)
      }
    },
    // 查询核心账户
    getByGoodsIdAndUserIdFun(obj) {
      const params = {
        goodsId: obj.goodId,
        userId: obj.userId,
      }
      getByGoodsIdAndUserId(params).then(({ data }) => {
        const { data: resData } = data
        if (data.success && resData) {
          this.platformAccount.bank = resData.bankDeposit
          this.platformAccount.account = resData.bankCardNo
          // this.form.bank = resData.bankDeposit
          // this.form.account = resData.bankCardNo
          // this.form1.bank = resData.bankDeposit
          // this.form1.account = resData.bankCardNo
        }
      })
    },
    // 保证金修改订单状态
    handleApplyRepayment(modeObj = {}) {
      this.$refs.editStatusDialogRef.handleOpen({
        isPlatformFee: false,
        statusMap: this.orderStatusMap,
        statusArr: this.orderStatusArr,
        status: this.pageData.cashDepositPaymentStatus,
        repaymentProofArr: {
          img: this.bailImg,
          pdf: this.bailPdf,
        },
        // 展示费用信息
        plaExpenseCulationVoList: this.purchaseBondArr,
        // 核心账户回显数据
        platformAccount: this.platformAccount,
        isViewMode: modeObj.isView === true,
        formList: this.purchaseBondObj || {},
      })
    },
    // 平台费用修改订单状态
    handleEditOrderStatus(modeObj = {}) {
      this.$refs.editStatusDialogRef.handleOpen({
        isPlatformFee: true,
        statusMap: this.orderStatusMap,
        statusArr: this.orderStatusArr,
        status: this.pageData.paymentStatus,
        repaymentProofArr: {
          img: this.platformImg,
          pdf: this.platformPdf,
        },
        // 展示费用信息
        plaExpenseCulationVoList: this.platformFeeArr,
        // 核心账户回显数据
        platformAccount: this.platformAccount,
        isViewMode: modeObj.isView === true,
        formList: this.platformFeeObj || {},
      })
    },
    // 修改状态弹窗确认事件
    handleEditStatusDialogConfirm(
      submitData = { isPlatformFee: false, formList: {} }
    ) {
      if (submitData.isPlatformFee) {
        this.platformFeeObj = submitData.formList
        this.pageData.paymentStatus = submitData.formList.status
      } else {
        this.purchaseBondObj = submitData.formList
        this.pageData.cashDepositPaymentStatus = submitData.formList.status
      }
    },
    tableRowClassName({ row }) {
      if (!row.refundTime || row.refundTime === '总计:') {
        return 'aggregate-row'
      }
      return ''
    },
  },
}
</script>

<style lang="scss" scoped>
.creditLimitFinancing {
  margin-bottom: 40px !important;
}

::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      // text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
  .table-goodsInfo {
    display: flex;
    align-items: center;
    & img {
      width: 72px;
      height: 72px;
      object-fit: cover;
      margin-right: 8px;
    }
    .table-goodsInfo-right {
      .goodsInfo-right-top {
        cursor: pointer;
        width: 196px;
        color: #697cff;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
      .goodsInfo-right-bottom {
        width: 196px;
        line-height: 20px;
        color: rgba(141, 141, 141, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
  .table-text-red {
    color: #ff2929;
    font-weight: 500;
    font-size: 14px;
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;
    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}
.formRight {
  color: rgba(125, 125, 125, 100);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  font-family: SourceHanSansSC-bold;
}
.dis-flex {
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  justify-content: space-between;
}

.fees-at-box {
  margin-top: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}
.i-active {
  transform: rotate(0deg) !important;
}
.boxs {
  display: flex;
  justify-content: space-around;
  padding: 10px 6px;
  .boxs-to-apply-for-product {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    .boxs-to-apply-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      h2 {
        margin-right: 8px;
        height: 22px;
        line-height: 22px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
      }
      .boxs-to-apply-title-right {
        display: flex;
        align-items: center;
        border-left: 1px solid #d7d7d7;
        line-height: 20px;
        padding: 0 8px;
        & span:nth-of-type(1) {
          display: block;
          color: #7d7d7d;
          font-size: 14px;
          font-weight: 500;
          margin-right: 6px;
        }
        & span:nth-of-type(2) {
          display: block;
          font-weight: 500;
          font-size: 14px;
          color: #697cff;
        }
      }
    }
    .boxs-to-apply-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .boxs-to-apply-item-left {
        display: flex;
        .boxs-to-apply-item-left-logo {
          width: 48px;
          height: 48px;
          border-radius: 40px;
          border: 1px solid rgba(234, 234, 234, 100);
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          margin-right: 7px;
          & > img {
            width: 100%;
            object-fit: cover;
          }
        }
        .boxs-to-apply-item-left-goodname {
          display: flex;
          flex-direction: column;
          & > span:first-child {
            display: inline-block;
            // width: 168px;
            height: 24px;
            color: rgba(18, 119, 255, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 3px;
          }
          & > span:last-child {
            display: inline-block;
            // width: 168px;
            height: 18px;
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
      .boxs-to-apply-item-right {
        & > span {
          display: block;
          padding: 2px 6px;
          line-height: 24px;
          border-radius: 24px;
          background-color: #1277ff;
          color: #ffffff;
          font-size: 14px;
          font-weight: 500;
          text-align: center;
        }
      }
      .boxs-to-apply-item-label {
        text-align: left;
        font-size: 14px;
        font-weight: 500;
        color: #7d7d7d;
        height: 18px;
        line-height: 18px;
        .init-color {
          color: #000;
        }
        .init-deepen-color {
          color: #697cff;
        }
      }
    }
    ::v-deep .el-descriptions-item__label {
      width: 135px;
      max-width: 135px;
    }
  }
}
.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.border-box {
  display: inline-block;
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

// 云信读取
::v-deep {
  .cloud-collapse {
    .el-descriptions-item__label {
      width: 200px;
      max-width: 200px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .table-bottom {
      margin-top: 16px;
      text-align: right;
      .el-button {
        padding: 0;
        width: 103px;
        height: 30px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px;
        color: #697cff;
        border-color: #697cff;
        box-sizing: border-box;
      }
      .table-bottom-pdf {
        cursor: pointer;
        display: inline-block;
        padding: 2px 6px;
        color: #697cff;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px;
        margin-left: 4px;
        border: 1px solid #697cff;
      }
    }
    .el-form {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .table-descriptions {
      margin-top: 30px;
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }

  .repayment-status-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;

    .left-box {
      .title {
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }

      .tag {
        margin-left: 8px;
      }

      .button {
        margin-left: 8px;
      }
    }
  }
}
.form-content {
  display: flex;
  margin-top: 20px;
  .form-container {
    width: 50%;
  }
}
.pay-box {
  padding: 20px 12px;
  width: 100%;
  background-color: #f7f7f7;
  border-radius: 6px;
  box-sizing: border-box;
  margin-top: 9px;
  line-height: 20px;
  & span:first-child {
    display: block;
    color: rgba(153, 153, 153, 100);
    font-size: 14px;
    font-family: SourceHanSansSC-regular;
    font-weight: 500;
    margin-right: 8px;
  }
  & span:last-child {
    color: #00072a;
    font-weight: 600;
    font-size: 14px;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 150px;
      // text-align: right;
    }
  }
}
</style>
