<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '提前结清'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{ variables.applyUserName }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template>
      <basic-container>
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">
              <span>产品信息</span>
              <span class="long-string" />
              <div class="serial-number">
                <span>融资编号：</span>
                <span>{{ processGoodsObj.financeNo }}</span>
              </div>
            </h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.backGround" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{
                      processGoodsObj.financeApply && processGoodsObj.financeApply.goodsName
                    }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">{{
                processGoodsObj.financeApply && processGoodsObj.financeApply.goodsType == 1
                  ? '应收账款质押'
                  : '代采融资'
              }}</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item v-for="item in tableData1" :key="item.id" :label="item.label">{{
                  item.value
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>
      <basic-container>
        <el-collapse v-model="activeNames1_1" @change="handleChange1_1">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change1_1Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>订单信息</span>
                    <!-- <span class="long-string" /> -->
                  </h1>
                </div>
              </div>
            </template>
            <!-- 展示表单 -->
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item v-for="item in tableData2_1" :key="item.id" :label="item.label">
                  <span>
                    {{ item.value }}
                  </span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>
      <CollapseCard title="还款明细" active>
        <el-table :data="repaymentTable" show-summary border :summary-method="getSummaries">
          <el-table-column
            fixed="left"
            prop="period"
            label="期数"
            :formatter="
              (row, column, cellValue) => {
                return `${cellValue}期`
              }
            "
            width="80"
            align="align"
          >
          </el-table-column>
          <el-table-column prop="repaymentTime" label="应还日期" min-width="120" align="align"> </el-table-column>
          <el-table-column
            prop="totalAmount"
            label="应还总额(元)"
            :formatter="(row, column, cellvalue) => formatMoney(cellvalue || '0.00')"
            min-width="120"
            align="align"
          >
          </el-table-column>
          <el-table-column
            prop="actualAmount"
            label="实还总额（元）"
            :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
            min-width="120"
            align="align"
          >
            <template slot-scope="{ row }">
              <div>
                <el-popover
                  class="item"
                  effect="light"
                  placement="bottom-start"
                  v-if="row.loanManageRepaymentVOList && row.loanManageRepaymentVOList.length != 0"
                >
                  <div class="props-container">
                    <el-table :data="row.loanManageRepaymentVOList" show-summary border :summary-method="getSummaries">
                      <el-table-column
                        v-for="(item, index) in repaymentTableItemColumn"
                        :key="index"
                        :fixed="item.fixed"
                        :prop="item.prop"
                        :label="item.label"
                        :width="item.width"
                        :min-width="item.minWidth"
                        :formatter="item.formatter"
                        :align="item.align"
                      >
                      </el-table-column>
                    </el-table>
                  </div>
                  <span slot="reference" style="color: #007fff; cursor: pointer">{{
                    formatMoney(row.actualAmount)
                  }}</span>
                </el-popover>
                <div v-else>{{ formatMoney(row.actualAmount) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="unPayAmount"
            label="待还总额(元)"
            :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
            min-width="120"
            align="align"
          >
          </el-table-column>
          <el-table-column
            prop="principal"
            label="本金(元)"
            :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
            min-width="120"
            align="align"
          >
          </el-table-column>
          <el-table-column
            prop="interest"
            label="利息(元)"
            :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
            min-width="120"
            align="align"
          >
          </el-table-column>
          <el-table-column
            prop="penaltyInterest"
            label="逾期罚息(元)"
            :formatter="(row, column, cellvalue) => formatMoney(cellvalue || '0.00')"
            min-width="120"
            align="align"
          >
          </el-table-column>
          <!-- <el-table-column
            prop="serviceFee"
            label="服务费(元)"
            :formatter="(row, column, cellvalue) => formatMoney(cellvalue)"
            min-width="120"
            align="align"
          >
          </el-table-column> -->
          <el-table-column
            v-for="(item, index) in repaymentTable[0].repaymentPlanFeeVOList"
            :key="item.id"
            :prop="`amount${index}`"
            :label="item.feeName + '(元)'"
            :formatter="(row, column, cellvalue) => cellvalue || '0'"
            min-width="120"
          >
          </el-table-column>
          <el-table-column
            prop="overdueDay"
            label="逾期天数"
            :formatter="(row, column, cellvalue) => cellValue || '0'"
            min-width="120"
            align="align"
          >
          </el-table-column>
          <el-table-column label="还款状态" min-width="120">
            <template slot-scope="{ row }">
              <div>
                <span style="color: #1fc374" v-if="row.repaymentStatus == 2">已结清</span>
                <span v-else>使用中</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="overdue"
            label="状态"
            :formatter="
              (row, column, cellvalue) => {
                switch (cellvalue) {
                  case 1:
                    return '正常'
                  case 2:
                    return '逾期'
                  default:
                    return ''
                }
              }
            "
            min-width="120"
          >
          </el-table-column>
        </el-table>
      </CollapseCard>
      <CollapseCard title="提前结清信息" :active="true">
        <div class="from-container">
          <div class="from">未还总额(元)：</div>
          <div>{{ formatMoney(totalMoney) }}</div>
        </div>
        <div class="from-container">
          <div class="from">待还本金(元)：</div>
          <div>{{ advance_settled.surplusPrincipal }}</div>
        </div>
        <div class="from-container">
          <div class="from">当前待还利息(元)：</div>
          <div>{{ advance_settled.interest }}</div>
          <div style="margin-left: 20px">
            <el-checkbox v-model="checked" disabled>是否要还当前利息</el-checkbox>
          </div>
        </div>
        <avue-form v-model="form" ref="form" :option="fromOption"></avue-form>
      </CollapseCard>
      <!-- 批复意见 -->
    </template>
    <!-- 底部按钮 -->
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import goLinkPage from '@/views/plugin/workflow/mixins/goLinkPage'
import FilePreview from '@/components/file-preview'
import CollapseCard from '@/views/loan/collapseCard.vue'
import { formatMoney } from '@/util/filter.js'
import { dateFormat } from '@/util/date'
import { ids } from '@/api/resource/attach.js'
import { getRepaymentDetail, repaymentDetails } from '@/api/loan/loanmanageiouDetail.js'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import { goodsTypeToPath } from '../globalFun.js'
export default {
  mixins: [customExForm, goLinkPage],
  components: {
    WfButton,
    WfFlow,
    FilePreview,
    CollapseCard,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          this.processInsIdRoot = processInsId
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      checked: true,
      financeApplyIdRoot: void 0,
      processInsIdRoot: void 0,
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      financeApply: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      advance_settled: {},
      financeNo: '',
      loanManageRepaymentPlanList: [],
      repaymentplanColumn: [
        {
          label: '期数',
          prop: 'period',
          width: 80,
          fixed: 'left',
          formatter: (row, column, cellValue) => {
            return `${cellValue}期`
          },
        },
        {
          label: '应还日期',
          prop: 'repaymentTime',
          minWidth: 90,
        },
        {
          label: '应还总额（元）',
          prop: 'totalAmount',
          align: 'right',
          minWidth: 120,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '本金（元）',
          prop: 'principal',
          minWidth: 120,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '利息（元）',
          prop: 'interest',
          align: 'right',
          minWidth: 120,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '逾期罚息（元）',
          align: 'right',
          prop: 'penaltyInterest',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue || '0.00')
          },
          minWidth: 120,
        },
        {
          label: '逾期天数',
          align: 'center',
          prop: 'overdueDay',
          formatter: (row, column, cellValue) => {
            return cellValue || '0'
          },
          minWidth: 60,
        },
        {
          label: '还款状态',
          prop: 'repaymentStatus',
          formatter: (row, column, cellValue) => {
            // return <span>ddd</span>
            if (cellValue == 1) {
              return <span>使用中</span>
            } else if (cellValue == 2) {
              return <span style="color:#1FC374">已结清</span>
            }
          },
          minWidth: 120,
        },
        {
          label: '状态',
          prop: 'overdue',
          minWidth: 120,
          formatter: (row, column, cellValue) => {
            // return this.loanManageRepaymentPlanStatusMap && this.loanManageRepaymentPlanStatusMap[cellValue] || ''
            // 1 正常 2 逾期
            switch (cellValue) {
              case 1:
                return '正常'
              case 2:
                return '逾期'
              default:
                return ''
            }
          },
        },
      ], // 还款计划
      loanManageRepayments: [],
      repaymentRecordColumn: [
        {
          label: '#',
          prop: 'repaymentTime',
          width: 80,
          fixed: 'left',
          formatter: (row, column, cellValue, index) => {
            return index + 1
          },
        },
        {
          label: '还款日期',
          prop: 'repaymentTime',
          minWidth: 90,
        },
        {
          label: '还款单号',
          prop: 'repaymentNo',
          minWidth: 120,
        },
        {
          label: '所属期数',
          prop: 'period',
          minWidth: 90,
          formatter: (row, column, cellValue) => {
            if (cellValue === -1) {
              return '提前还清'
            }
            return `第${cellValue}期`
          },
        },
        {
          label: '还款总额',
          prop: 'totalAmount',
          align: 'right',
          minWidth: 90,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '本金（元）',
          prop: 'principal',
          align: 'right',
          minWidth: 120,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '利息（元）',
          prop: 'interest',
          align: 'right',
          minWidth: 120,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '手续费（元）',
          prop: 'serviceCharge',
          align: 'right',
          minWidth: 120,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '逾期罚息（元）',
          prop: 'penaltyInterest',
          align: 'right',
          minWidth: 120,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
      ], // 已还明细
      // 提前还款原因
      fromOption: {
        emptyBtn: false, //隐藏清空按钮
        submitBtn: false, //隐藏提交按钮
        height: 'auto',
        column: [
          {
            labelWidth: '170',
            label: '提前结清手续费（元）',
            prop: 'prepaymentServiceFee',
            placeholder: '请输入提前结清手续费',
            disabled: true,
            span: 24,
            rules: [
              {
                required: true,
                validator: this.validateServiceFee,
                trigger: 'blur',
              },
            ],
          },
          {
            labelWidth: '170',
            label: '有效支付时间',
            prop: 'timeNum',
            placeholder: '请输入有效支付时间',
            span: 9,
            disabled: true,
            rules: [
              {
                required: false,
                validator: this.timeNum,
                trigger: 'blur',
              },
            ],
          },
          {
            labelWidth: '',
            label: '',
            prop: 'unity',
            type: 'select',
            span: 4,
            disabled: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_time_unit',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            placeholder: '请选择支付单位',
            dataType: 'string',
            rules: [
              {
                required: this.checked,
                validator: this.unity,
                trigger: 'change',
              },
            ],
          },
          {
            labelWidth: '170',
            label: '提前结清原因',
            type: 'textarea',
            prop: 'reason',
            span: 12,
            disabled: true,
            rules: [
              {
                required: false,
                message: '请输入提前结清原因',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      // 产品信息
      processGoodsObj: {},
      tableData1: [],
      activeNames1_1: ['furtherInformation'],
      change1_1Type: true,
      tableData2_1: [],
      repaymentTableItemColumn: [
        {
          label: '还款日期',
          prop: 'repaymentTime',
          width: 110,
        },
        {
          label: '还款单号',
          prop: 'repaymentNo',
          width: 150,
        },
        {
          label: '实还总额（元）',
          prop: 'actualAmount',
          align: 'right',
          width: 130,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '实还本金（元）',
          prop: 'actualPrincipal',
          width: 130,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '实还利息（元）',
          prop: 'actualInterest',
          align: 'right',
          width: 130,
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue)
          },
        },
        {
          label: '实还逾期罚息（元）',
          align: 'right',
          prop: 'actualPenaltyInterest',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue || '0.00')
          },
          width: 150,
        },
        {
          label: '实还手续费（元）',
          align: 'right',
          prop: 'actualServiceCharge',
          formatter: (row, column, cellValue) => {
            return formatMoney(cellValue || '0.00')
          },
          width: 150,
        },
      ],
      repaymentTable: [],
    }
  },
  computed: {
    totalMoney() {
      if (this.checked) {
        if (!isNaN(+this.form.prepaymentServiceFee)) {
          return (
            +this.advance_settled.totalAmount +
            +this.form.prepaymentServiceFee -
            this.advance_settled.prepaymentServiceFee
          )
        } else {
          return +this.advance_settled.totalAmount
        }
      } else {
        if (!isNaN(+this.form.prepaymentServiceFee)) {
          return (
            +this.advance_settled.totalAmount +
            +this.form.prepaymentServiceFee -
            this.advance_settled.prepaymentServiceFee -
            +this.advance_settled.interest
          )
        }
        return (
          this.advance_settled.totalAmount - this.advance_settled.prepaymentServiceFee - +this.advance_settled.interest
        )
      }
    },
  },
  methods: {
    formatMoney,
    getRepaymentFirstDay(loanTime, format) {
      const nextLoanDate = new Date(loanTime)
      nextLoanDate.setMonth(nextLoanDate.getMonth())
      // nextLoanDate.setMonth(nextLoanDate.getMonth() + 1)
      return nextLoanDate == 'Invalid Date' ? '..' : dateFormat(nextLoanDate, format)
    },
    getSummaries(param) {
      const totalFields = [
        'shouldPrincipal',
        'shouldInterest',
        'shouldPunishInterest',
        'surplusPrincipal',
        'surplusInterest',
        'surplusPunishInterest',
        'amount',
        // 还款计划-repaymentplanColumn
        'totalAmount',
        'principal',
        'interest',
        'penaltyInterest',
        'overdueDay',
        // 已还明细
        'totalAmount',
        'principal',
        'interest',
        'serviceCharge',
        'penaltyInterest',
        // 还款明细
        'actualServiceCharge',
        'actualPenaltyInterest',
        'actualInterest',
        'actualPrincipal',
        'actualTotalAmount',
        'serviceFee',
        'actualAmount',
        'unPayAmount',
      ]
      if (this.repaymentTable.length > 0 && this.repaymentTable[0].repaymentPlanFeeVOList.length > 0) {
        let length = this.repaymentTable[0].repaymentPlanFeeVOList.length
        for (let index = 0; index < length; index++) {
          totalFields.push(`amount${index}`)
        }
      }
      // 非金额的字段(不需要格式化)
      const notFormat = ['overdueDay']

      // if (totalFields.indexOf(param.property)) return ''
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <strong>总计：</strong>
          return
        }
        const { property } = column
        if (totalFields.indexOf(property) === -1) return ''
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          if (notFormat.indexOf(property) === -1) {
            const val = sums[index].toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })
            sums[index] = <strong>{val}</strong>
          }
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    validateServiceFee(rule, value, callback) {
      // if(value)
      if (isNaN(value)) {
        callback('格式不对，请输入为数字格式的')
      } else if (value <= 0) {
        callback('手续费最低不能小于等于0')
      }
    },

    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(async res => {
        this.waiting = false
        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm
        this.advance_settled = variables.advance_settled
        this.loanManageRepaymentPlanList = variables.advance_settled.loanManageIouVO.loanManageRepaymentPlanList || []
        this.loanManageRepayments = variables.advance_settled.loanManageIouVO.loanManageRepayments || []
        this.form.unity = variables.unity || '1'
        this.form.timeNum = variables.timeNum
        this.form.loanAmount = variables.advance_settled.repaymentExpenseResp.loanAmount // 借款总额
        this.form.surplusPrincipal = variables.advance_settled.loanManageIouVO.totalByRepaymentFee //应还总额
        this.form.reason = variables.advance_settled.reason // 提前结清原因
        this.form.prepaymentServiceFee = variables.prepaymentServiceFee
        const {
          data: { code, data: resData1 },
        } = await getRepaymentDetail(variables.advance_settled.iouId)
        repaymentDetails(resData1.financeNo).then(resData => {
          let target = false
          let Map = {}
          resData.data.data.forEach((item, dindex) => {
            item.repaymentPlanFeeVOList.forEach((citem, index) => {
              for (let cindex = 0; cindex < resData.data.data[0].repaymentPlanFeeVOList.length.length; cindex++) {
                item[`amount${cindex}`] = '0.00'
              }
              if (!target) {
                Map[citem.feeName] = `amount${index}`
                let obj = {
                  label: citem.feeName,
                  prop: `amount${index}`,
                  formatter: (row, column, cellValue) => {
                    return formatMoney(cellValue || '0.00')
                  },
                  width: 150,
                }

                this.repaymentTableItemColumn.push(obj)
              }
              if (dindex === 0) {
                item[`amount${index}`] = citem.amount
              } else {
                item[Map[citem.feeName]] = citem.amount
              }
            })
            target = true
            item.loanManageRepaymentVOList.forEach(citem => {
              citem.repaymentFeeList.forEach((ditem, index) => {
                citem[`amount${index}`] = ditem.actualAmount
              })
            })
          })
          this.repaymentTableItemColumn.push({
            label: '还款类型',
            prop: 'repaymentType',
            formatter: (row, column, cellValue) => {
              return (this.loanManageRepaymentType && this.loanManageRepaymentType[cellValue]) || ''
            },
            width: 90,
          })
          this.repaymentTable = resData.data.data
        })
        this.processGoodsObj = resData1
        const {
          data: { data: loan_usage },
        } = await getDictionary('finance_apply_loan_usage')
        const resList = []
        for (const item of loan_usage) {
          resList.push({
            key: item.dictKey,
            value: item.dictValue,
            id: item.id,
          })
        }
        // 过滤出当前借款用途
        let usage = resList

        let value = usage.filter(item => item.key == resData1.financeApply.loanUsage)

        const resdata = [
          {
            id: 1,
            label: '融资金额',
            value: `${this.$numChuFun(resData1.financeApply.amount, 10000)}万元`,
            key: 'financingDemand_money',
          },
          {
            id: 2,
            label: '额度类型',
            value: `${resData1.financeApply.recycleType === 1 ? '不可循环' : '可循环'}`,
            key: 'financingDemand_recycleType',
          },
          {
            id: 3,
            label: '借款用途',
            value: `${value[0].value}`,
            key: 'financingDemand_use',
          },
        ]
        this.tableData1 = resdata
        const rlo = resData1
        const {
          data: { data: resData3 },
        } = await getDictionary('goods_billing_method')
        const goodsBillingMethodMap = {}
        resData3.map(d => {
          const { dictKey, dictValue } = d
          goodsBillingMethodMap[dictKey] = dictValue
        })
        let repaymentMode = goodsBillingMethodMap[resData1.financeApply.repaymentMode]
        const tableData2_1 = [
          {
            id: 1,
            label: '借款人名称',
            value: resData1.userName,
            key: 'reBreathing_userName',
          },
          {
            id: 2,
            label: '借款金额',
            value: resData1.iouAmount,
            key: 'reBreathing_iouAmount',
          },
          {
            id: 3,
            label: '放款日',
            value: rlo.loanTime,
            key: 'reBreathing_loanTime',
          },
          {
            id: 4,
            label: '到期日',
            value: resData1.expireTime,
            key: 'reBreathing_loanTime',
          },
          {
            id: 5,
            label: '年利率(单利)',
            value: `${resData1.financeApply.annualInterestRate}%(日利率*360)`,
            key: 'financingDemand_use',
          },
          {
            id: 6,
            label: '日利率',
            value: `${resData1.financeApply.dailyInterestRate}%`,
            key: 'financingDemand_use',
          },
          {
            id: 7,
            label: '还款方式',
            value: repaymentMode || '随借随还',
            key: 'financingDemand_repaymentMode',
          },
          {
            id: 8,
            label: '首次还款日',
            value: this.getRepaymentFirstDay(resData1.firstRepaymentDate, 'yyyy-MM-dd'),
            key: 'financingDemand_firstRepaymentDate',
          },
          {
            id: 8,
            label: '还款日',
            value: `每月${this.getRepaymentFirstDay(resData1.firstRepaymentDate, 'dd')}号`,
            key: 'financingDemand_firstRepaymentdd',
          },
          {
            id: 9,
            label: '借款期限',
            value: resData1.periodDes,
            key: 'financingDemand_periodDes',
          },
        ]
        // console.log(this.goodsBillingMethodMap[data.financeApply.repaymentMode])
        this.tableData2_1 = tableData2_1
      })
    },
    handleChange1_1() {
      // 订单详情折叠面板收缩控制
      this.change1_1Type = !this.change1_1Type
    },
    // 通过
    async handleExamine(pass) {
      if (pass) {
        this.$refs.form.validate((valid, done, msg) => {
          if (valid) {
            done()
            this.validataFunction(pass)
          } else {
            done()
            if (msg) {
              const key = Object.keys(msg)[0]
              const rules = msg[key]
              this.$message.error(rules.map(r => r.message).join(' | '))
              return
            }
          }
        })
        this.validataFunction(pass)
      }
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      this.advance_settled.repaymentExpenseResp.advanceSettleServiceFee = this.form.advanceSettleServiceFee
      this.handleCompleteTask(pass, this.advance_settled)
        .then(() => {
          this.$message.success('处理成功')
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 终止
    handleTermination() {
      if (
        (this.statusForm.paymentStatus && this.statusForm.paymentStatus !== 1) ||
        !this.costPlatformList.payAFee_orderStatus ||
        this.financeApply.chargeMethod === 1
      ) {
        this.validataterminationFunction()
        return
      }
      this.$message.error('请修改订单状态')
    },
    // 终止后调取接口函数
    validataterminationFunction() {
      this.submitLoading = true
      let params = {}
      if (this.costPlatformList.payAFee_orderStatus && this.financeApply.chargeMethod === 2) {
        this.statusForm.financeApplyId = this.financeApplyIdRoot // 流程融资ID
        params = {
          billExpenseOrder: this.statusForm,
        }
      }
      this.handleTerminateProcess(params)
    },
    tableRowClassName({ row }) {
      if (!row.refundTime) {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      goodsTypeToPath(this.processGoodsObj)
    },
  },
}
</script>

<style lang="scss" scoped>
.from-container {
  display: flex;
  margin-bottom: 5px;
}
.from {
  width: 170px;
  text-align: end;
  padding-right: 12px;
  font-size: 14px;
  color: #606266;
  // box-sizing: border-box;
}
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

// 覆盖弹窗默认样式
::v-deep {
  .avue-dialog .el-dialog__body {
    margin-bottom: 0;
  }

  .el-dialog__body {
    padding: 0px 20px;
  }
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.pay-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  .order-box {
    display: flex;
    align-items: center;
    cursor: context-menu;

    .order-status {
      width: 56px;
      height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      margin-right: 8px;
    }

    .order-text {
      height: 30px;
      border-radius: 28px;
      font-size: 14px;
      font-family: Microsoft Yahei;
      padding: 4px 8px;
      box-sizing: border-box;
      margin-right: 12px;
    }

    .order-modification-btn {
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
    }

    .order-btn {
      width: 28px;
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .payOrder-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    span {
      width: 103px;
      height: 30px;
      border-radius: 4px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      text-align: center;
      border: 1px solid rgba(105, 124, 255, 100);
      margin-right: 10px;
      padding: 2px 4px;
      box-sizing: border-box;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }
    }

    .payOrder-left-box {
      margin-right: 10px;
    }
  }
}

.avue-monry-form-box {
  .money-box {
    width: 432px;
    margin: -10px 0;
    border-radius: 6px;
    background-color: rgba(247, 247, 247, 100);
    padding: 0 12px 20px;
    box-sizing: border-box;
    overflow: hidden;

    .chlidern-money {
      display: flex;
      align-items: center;
      margin-top: 20px;

      .label-box {
        width: 95px;
        height: 20px;
        line-height: 20px;
        color: rgba(153, 153, 153, 100);
        font-size: 14px;
        text-align: right;
        font-family: SourceHanSansSC-regular;
      }

      .value-box {
        width: 311px;
        height: 20px;
        line-height: 20px;
        color: rgba(0, 7, 42, 100);
        font-weight: bold;
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
      }
    }
  }

  .look-btn-menu {
    & span {
      height: 21px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      cursor: pointer;
      margin-right: 8px;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  // 覆盖弹窗默认样式
  ::v-deep {
    .avue-form__menu {
      display: none;
    }

    .el-input.is-disabled .el-input__inner {
      color: #000;
    }

    .el-textarea.is-disabled .el-textarea__inner {
      color: #000;
    }
  }
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;
        cursor: pointer;

        & > span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        & > span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }

          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }

            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }

      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}

::v-deep i.el-collapse-item__arrow {
  display: none;
}

::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}

::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}

::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}

::v-deep .el-card {
  border-radius: 8px;

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
