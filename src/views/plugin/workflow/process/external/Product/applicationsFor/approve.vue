<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '应收账款质押-融资申请'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <basic-container>
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">
              <span>融资需求</span>
              <span class="long-string" />
              <div class="serial-number">
                <span>融资编号：</span>
                <span>{{ financeNo }}</span>
              </div>
            </h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{
                      processGoodsObj.goodsName
                    }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">{{
                processGoodsObj.type == 1 ? '应收账款质押' : '代采融资'
              }}</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item
                  v-for="item in tableData1"
                  :key="item.id"
                  :label="item.label"
                  >{{ item.value }}</el-descriptions-item
                >
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 质押品 -->
      <basic-container>
        <el-collapse v-model="activeNames1" @change="handleChange1">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change1Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">质押品</h1>
                </div>
              </div>
            </template>
            <div class="table-top">
              <el-table
                ref="table1"
                :data="tableData2"
                :summary-method="getSummaries"
                show-summary
                style="width: 100%; margin-top: 20px"
                class="table-border-style"
              >
                <el-table-column
                  prop="index"
                  label="#"
                  width="70"
                  align="center"
                  v-if="pledgedGoodsList.pledgedGoods_serialNumber"
                >
                </el-table-column>
                <el-table-column
                  prop="contractNo"
                  label="资产编号"
                  v-if="pledgedGoodsList.pledgedGoods_uniqueIdentification"
                >
                  <template slot-scope="scope">
                    <span class="contract-no">{{ scope.row.contractNo }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="enterpriseName"
                  label="贸易背景"
                  v-if="pledgedGoodsList.pledgedGoods_tradeBackground"
                >
                </el-table-column>
                <el-table-column
                  prop="proofType"
                  label="凭证类型"
                  v-if="pledgedGoodsList.pledgedGoods_voucherType"
                >
                  <template slot-scope="scope">
                    <span class="tag-box">{{ scope.row.proofType }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="expireTime"
                  label="到期日期"
                  v-if="pledgedGoodsList.pledgedGoods_dateDue"
                >
                </el-table-column>
                <!-- <el-table-column
                  v-if="
                    resData.status != 'finished' &&
                    pledgedGoodsList.pledgedGoods_theCurrentValue
                  "
                  prop="currentAmount"
                  label="当前价值（元）"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.currentAmount | formatMoney }} </span>
                  </template>
                </el-table-column> -->
                <el-table-column
                  v-if="
                    resData.status != 'finished' &&
                    pledgedGoodsList.pledgedGoods_valueOfUse
                  "
                  prop="amount"
                  label="本次使用价值（元）"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.amount | formatMoney }} </span>
                  </template>
                </el-table-column>
                <template slot="header" slot-scope="scope">
                  {{ scope }}
                </template>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 还款试算 -->
      <basic-container>
        <el-collapse v-model="activeNames2" @change="handleChange2">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change2Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">
                    <span>还款试算</span>
                    <!-- <span class="long-string" />
                    <span class="interest-rate"
                      >日利率{{ allDailyInterestRate }}%（年化利率{{
                        allAnnualInterestRate
                      }}%）</span
                    > -->
                  </h1>
                </div>
              </div>
            </template>
            <div
              class="table-top refund"
              v-if="bankInterestList.reimbursementTrial_bankInterest"
            >
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>资方费用</span>
                  <span />
                  <span
                    >日利率{{ dailyInterestRate }}%（年化利率{{
                      annualInterestRate
                    }}%）</span
                  >
                </div>
                <div class="title-right-box">计费方式：{{ chargeMode }}</div>
              </div>
              <el-table
                ref="table2"
                :data="tableData3"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  prop="term"
                  label="期数"
                  width="110"
                  align="center"
                  v-if="
                    bankInterestList.bankInterest_periods &&
                    processGoodsObj &&
                    processGoodsObj.repaymentType === 1
                  "
                >
                </el-table-column>
                <el-table-column
                  prop="refundTime"
                  label="还款日期"
                  v-if="bankInterestList.bankInterest_repaymentDate"
                >
                </el-table-column>
                <el-table-column
                  prop="monthlySupply"
                  label="应还总额"
                  v-if="bankInterestList.bankInterest_totalShouldAlso"
                >
                  <template slot-scope="scope">
                    <span>￥{{ scope.row.monthlySupply | formatMoney }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="monthlyPrincipal"
                  label="还款本金"
                  v-if="bankInterestList.bankInterest_repaymentPrincipal"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.monthlyPrincipal | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="planInterest"
                  label="应还利息"
                  v-if="bankInterestList.bankInterest_shouldAlsoInterest"
                >
                  <template slot-scope="scope">
                    <span
                      >￥{{ scope.row.planInterest | formatMoney }}
                    </span>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  v-for="(item, index) in feeData.showRepaymentPlan
                    .stagRecords[0].planFeeList"
                  :key="item.id"
                  :prop="`amount${index}`"
                  :label="item.feeName"
                >
                </el-table-column> -->
              </el-table>
            </div>

            <div v-if="platformFeeList.length">
              <div
                class="table-top refund"
                v-for="item in platformFeeList"
                :key="item.id"
              >
                <div class="chain-line" />
                <div class="table-title-box" style="margin-top: -10px">
                  <div class="title-left-box">
                    <span>{{ item.parenExpenseName }}</span>
                  </div>
                </div>
                <el-table
                  ref="table3"
                  :data="item.expenseOrderDetailList"
                  :summary-method="getSummaries"
                  show-summary
                  style="width: 100%; margin-top: 13px"
                  class="table-border-style"
                >
                  <el-table-column
                    prop="expenseTypeStr"
                    label="费用名称"
                  >
                  </el-table-column>
                  <!-- <el-table-column
                    prop="expenseTypeStr"
                    label="费用类型"
                  >
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.expenseTypeStr }}
                      </span>
                    </template>
                  </el-table-column> -->
                  <el-table-column
                    prop="repaymentTerm"
                    label="期数"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="feeNodeStr"
                    label="计算节点"
                  >
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.feeNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="collectFeesNodeStr"
                    label="收费节点"
                  >
                    <template slot-scope="scope">
                      <span class="border-box">
                        {{ scope.row.collectFeesNodeStr }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="feeFormulaName"
                    label="计费方式"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="amount"
                    label="应付金额"
                  >
                    <template slot-scope="scope">
                      <!-- <span>￥{{ scope.row.amount | formatMoney }} </span> -->
                      <span v-if="scope.row.calculation !== 1"
                        >￥{{ scope.row.amount | formatMoney }}
                      </span>
                      <div v-else style="width: 80%">
                        <el-input
                          placeholder="请输入金额"
                          v-model="scope.row.amount"
                          type="number"
                          :disabled="
                            !costPlatformList.costPlatform_amountPayable_w
                          "
                        >
                          <template slot="append">元</template>
                        </el-input>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div class="chain-line" />
            <div class="fees-at-box">
              <div class="fees-left-at">以上仅为试算结果，以实际放款为准～</div>
              <div class="fees-right-at">
                应还总额：
                <span> ￥{{ sum | formatMoney }} </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 批复意见 -->
      <basic-container>
        <div class="approval-container">
          <span class="title">批复意见：</span>
          <el-input
            class="value"
            type="textarea"
            :rows="5"
            resize="none"
            placeholder="请输入批复意见"
            v-model="comment"
          >
          </el-input>
        </div>
      </basic-container>
    </template>
    <!-- 底部按钮 -->
    <wf-button
      class="custom-button"
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></wf-button>
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import { formatMoney } from '@/util/filter.js'
import {
  getDictionary,
  saleContractList,
  repaymentCalculationDetailById,
  // platformExpensesList2,
} from '@/api/goods/pcontrol/workflow/productConfirmation'

export default {
  mixins: [customExForm],
  components: { WfButton, WfFlow },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNames1() {
      setTimeout(() => {
        this.$refs.table1.$ready = false
      }, 50)
    },
    activeNames2() {
      setTimeout(() => {
        this.$refs.table2.$ready = false
        this.$refs.table3.$ready = false
      }, 50)
    },
    platformFeeList: {
      handler(val) {
        let num = this.sum1
        if (val && val.length) {
          for (const item of val) {
            const cunArr = item.expenseOrderDetailList
            if (cunArr && cunArr.length) {
              for (const cItem of cunArr) {
                if (cItem.amount) {
                  num = this.$numJiaFun(num, cItem.amount)
                }
              }
            }
          }
        }
        this.sum = num
      },
      immediate: false,
      deep: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      financeNo: '',
      tableData1: [],
      activeNames1: [],
      activeNames2: [],
      change1Type: false,
      change2Type: false,
      change1ClassType: true,
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData4copy: [],
      sum: 0,
      sum1: 0,
      chargeMode: '先息后本',
      allDailyInterestRate: 0,
      allAnnualInterestRate: 0,
      dailyInterestRate: 0,
      annualInterestRate: 0,
      calculation: false,

      // 显影控制
      pledgedGoodsList: {
        pledgedGoods_serialNumber: false,
        pledgedGoods_uniqueIdentification: false,
        pledgedGoods_tradeBackground: false,
        pledgedGoods_voucherType: false,
        pledgedGoods_dateDue: false,
        pledgedGoods_theCurrentValue: false,
        pledgedGoods_valueOfUse: false,
      },
      bankInterestList: {
        reimbursementTrial_bankInterest: false,
        bankInterest_periods: false,
        bankInterest_repaymentDate: false,
        bankInterest_totalShouldAlso: false,
        bankInterest_repaymentPrincipal: false,
        bankInterest_shouldAlsoInterest: false,
      },
      costPlatformList: {
        reimbursementTrial_costPlatform: false,
        costPlatform_typeOfExpense: false,
        costPlatform_costOfName: false,
        costPlatform_payTheNode: false,
        costPlatform_chargeMode: false,
        costPlatform_amountPayable_r: false,
        costPlatform_amountPayable_w: false,
      },
      // 其他费用的
      platformFeeList: [],
      // 所有的费用
      feeData: { showRepaymentPlan: { stagRecords: [{ planFeeList: [] }] } },
    }
  },
  methods: {
    // 合计
    allMonrySum(item) {
      // this.sum1 = 0
      this.sum1 = this.$numJiaFun(this.sum1, item.monthlySupply)
      // if (this.platformFeeList.length) {
      //   this.platformFeeList.forEach(item => {
      //     if (item.expenseOrderDetailList.length) {
      //       this.sum1 += Number(
      //         item.expenseOrderDetailList[
      //           item.expenseOrderDetailList.length - 1
      //         ].amount
      //       )
      //     }
      //   })
      // }
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm
        // 流程产品信息
        const { processGoodsInfo, financeNo, financeApply, financeApplyId } =
          variables
        this.processGoodsObj = processGoodsInfo
        this.financeNo = financeNo

        // 流程产品信息
        let usage = []
        getDictionary('finance_apply_loan_usage').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            // 过滤出当前借款用途
            usage = resList.filter(item => item.key == financeApply.loanUsage)
            const data = [
              {
                id: 1,
                label: '融资金额',
                value: `${this.$numChuFun(financeApply.amount, 10000)}万元`,
                key: 'financingDemand_money',
              },
              {
                id: 2,
                label: '借款期限',
                value: `${financeApply.loadTerm}${
                  financeApply.loadTermUnit == 2 ? '个月' : '天'
                }`,
                key: 'financingDemand_deadline',
              },
              {
                id: 3,
                label: '借款用途',
                value: `${usage[0].value}`,
                key: 'financingDemand_use',
              },
            ]

            // 是否可读
            const dataKey = data.map(item => item.key)
            const taskFormFilter = taskForm.filter(item =>
              dataKey.includes(item.id)
            )
            const taskFormId = taskFormFilter.map(item => {
              if (item.readable) {
                return item.id
              }
            })
            const dataFilter = data.filter(item =>
              taskFormId.includes(item.key)
            )
            this.tableData1 = dataFilter
          }
        })

        // 质押品数据处理
        getDictionary('jrzh_customer_front_sales_contract_proof_type').then(
          res => {
            const resData = res.data
            if (resData.code == 200) {
              // 处理字典数据
              const resList = []
              for (const item of resData.data) {
                resList.push({
                  key: item.dictKey,
                  value: item.dictValue,
                  id: item.id,
                })
              }
              const taskArrKey = [
                'pledgedGoods_serialNumber',
                'pledgedGoods_uniqueIdentification',
                'pledgedGoods_tradeBackground',
                'pledgedGoods_voucherType',
                'pledgedGoods_dateDue',
                'pledgedGoods_theCurrentValue',
                'pledgedGoods_valueOfUse',
              ]
              const taskFormFilter = taskForm.filter(
                item => taskArrKey.includes(item.id) && item.readable
              )
              for (const item of taskFormFilter) {
                this.pledgedGoodsList[item.id] = true
              }
              saleContractList(financeApplyId).then(res => {
                const { data: resData } = res.data
                const arr = []
                let index = 1
                for (const item of resData) {
                  const dat = item.salesContract
                  usage = resList.filter(item => item.key == dat.proofType)
                  arr.push({
                    index: String(index),
                    id: item.id,
                    contractNo: dat.contractNo,
                    enterpriseName: item.enterpriseName,
                    proofType: usage[0].value,
                    expireTime: dat.expireTime,
                    // currentAmount: item.currentAmount,
                    amount: item.amount,
                  })
                  index++
                }
                this.tableData2 = arr
              })
            }
          }
        )

        // 还款试算
        // this.allAnnualInterestRate =
        //   Number(financeApply.annualInterestRate) +
        //   Number(financeApply.serviceRate) // 总年利率
        // this.allDailyInterestRate = (
        //   Number(this.allAnnualInterestRate) / 360
        // ).toFixed(3) // 总日利率
        // this.dailyInterestRate = financeApply.dailyInterestRate // 银行日利率
        // this.annualInterestRate = financeApply.annualInterestRate // 银行年利率
        if (processGoodsInfo.repaymentType === 1) {
          getDictionary('goods_billing_method').then(res => {
            const resData = res.data
            if (resData.code == 200) {
              // 处理字典数据
              const resList = []
              for (const item of resData.data) {
                resList.push({
                  key: item.dictKey,
                  value: item.dictValue,
                  id: item.id,
                })
              }
              // 过滤出当前的计费方式
              this.chargeMode = resList.filter(
                itemed => itemed.key == financeApply.repaymentMode
              )[0].value
            }
          })
        } else {
          this.chargeMode = '随借随还'
        }

        const taskArrKey = [
          'reimbursementTrial_bankInterest',
          'bankInterest_periods',
          'bankInterest_repaymentDate',
          'bankInterest_totalShouldAlso',
          'bankInterest_repaymentPrincipal',
          'bankInterest_shouldAlsoInterest',
        ]
        const taskFormFilter = taskForm.filter(
          item => taskArrKey.includes(item.id) && item.readable
        )
        for (const item of taskFormFilter) {
          this.bankInterestList[item.id] = true
        }
        // 是否可写
        const taskFormFilter3 = taskForm.filter(
          item => item.id == 'costPlatform_amountPayable' && item.writeable
        )
        if (taskFormFilter3.length) {
          this.costPlatformList['costPlatform_amountPayable_w'] = true
        }

        repaymentCalculationDetailById(financeApplyId).then(res => {
          const { data: resData, code } = res.data
          if (code == 200) {
            const arr = []
            this.dailyInterestRate = resData.showRepaymentPlan.dayRate // 银行日利率
            this.annualInterestRate = resData.showRepaymentPlan.yearRate // 银行年利率
            this.feeData = resData
            //  资方统一清分需要处理的逻辑
            let index = 0
            let Map = {}
            const { chargeMethod } = this.variables
            if (chargeMethod == 1) {
              resData.showRepaymentPlan.stagRecords[0].planFeeList.forEach(
                (item, index) => {
                  Map[item.feeName] = {
                    index,
                    collectFeeMethod: item.collectFeeMethod,
                    amount: item.amount,
                  }
                }
              )
            } else {
              resData.expenseOrderDetailFinanceVos.forEach(item => {
                const cunArr = item.expenseOrderDetailList
                if (cunArr && cunArr.length) {
                  for (const item of cunArr ) {
                    item.repaymentTerm = item.repaymentTerm
                      ? item.repaymentTerm + '期'
                      : '--'
                    if (item.calculation === 1) {
                      if (variables.platformExpenses) {
                        cunArrChengXunHuan: for (const cItem of variables.platformExpenses) {
                          const cunArr = cItem.expenseOrderDetailList
                            if (cunArr && cunArr.length) {
                              for (const ditem of cunArr) {
                                if (item.name == ditem.name) {
                                  item.amount = ditem.amount
                                  break cunArrChengXunHuan
                                }
                              }
                            }
                        }
                      } else {
                        item.amount = ''
                      }
                    }
                    if (!this.calculation && item.calculation === 1) {
                      this.calculation = true
                    }
                  }
                }
                // let amount = item.expenseOrderDetailList.reduce((prve, next) => {
                //   if (next.feeFormulaName == '手填') {
                //     next.amount = '人工计算'
                //     return prve
                //   }
                //   return prve + Number(next.amount)
                // }, 0)
              })
              this.platformFeeList = resData.expenseOrderDetailFinanceVos
            }
            for (const item of resData.showRepaymentPlan.stagRecords) {
              if (processGoodsInfo.repaymentType === 1) {
                if (item.term) {
                  arr.push({
                    term: `${item.term}期`,
                    refundTime: item.refundTime,
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    planInterest: item.planInterest,
                  })
                  if (
                    index != resData.showRepaymentPlan.stagRecords.length - 1 &&
                    chargeMethod == 1
                  ) {
                    if (
                      index == 0 ||
                      item.planFeeList.length ==
                        resData.showRepaymentPlan.stagRecords[0].length
                    ) {
                      item.planFeeList.forEach((citem, cindex) => {
                        arr[index][`amount${cindex}`] = `￥${formatMoney(
                          citem.amount
                        )}`
                      })
                    } else {
                      for (const key in Map) {
                        arr[index][
                          `amount${Map[key].index}`
                        ] = `￥${formatMoney(0)}`
                      }
                      item.planFeeList.forEach(citem => {
                        if (Map[citem.feeName]) {
                          let data = Map[citem.feeName]
                          arr[index][`amount${data.index}`] = `￥${formatMoney(
                            citem.amount
                          )}`
                        }
                      })
                    }
                  }
                } else {
                  this.allMonrySum(item)
                  arr.push({
                    term: '总计',
                    refundTime: '',
                    monthlySupply: item.monthlySupply,
                    monthlyPrincipal: item.monthlyPrincipal,
                    planInterest: item.planInterest,
                  })

                  if (chargeMethod == 1) {
                    for (const key in Map) {
                      // 一次性付清
                      if (Map[key].collectFeeMethod == 1) {
                        arr[index][
                          `amount${Map[key].index}`
                        ] = `￥${formatMoney(Map[key].amount)}`
                      } else {
                        // 分期
                        arr[index][`amount${Map[key].index}`] = `￥${(
                          formatMoney(Map[key].amount) * index
                        ).toFixed(2)}`
                      }
                    }
                  }
                }
                index++
              } else {
                // 这是随借随还的
                arr.push({
                  // term: '1期',
                  refundTime: item.refundTime,
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })
                arr.push({
                  // term: '总计:',
                  refundTime: '总计:',
                  monthlySupply: item.monthlySupply,
                  monthlyPrincipal: item.monthlyPrincipal,
                  planInterest: item.planInterest,
                })
                if (chargeMethod == 1) {
                  item.planFeeList.forEach((citem, cindex) => {
                    arr[0][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
                    arr[1][`amount${cindex}`] = `￥${formatMoney(citem.amount)}`
                  })
                }

                this.allMonrySum(item)
              }
            }

            this.tableData3 = arr
          }
        })

        // 平台费用
        // 是否可读
        // const taskArrKey2 = [
        //   'reimbursementTrial_costPlatform',
        //   'costPlatform_typeOfExpense',
        //   'costPlatform_costOfName',
        //   'costPlatform_payTheNode',
        //   'costPlatform_chargeMode',
        //   'costPlatform_amountPayable',
        // ]
        // const taskFormFilter2 = taskForm.filter(
        //   item => taskArrKey2.includes(item.id) && item.readable
        // )
        // for (const item of taskFormFilter2) {
        //   if (item.id == 'costPlatform_amountPayable') {
        //     this.costPlatformList['costPlatform_amountPayable_r'] = true
        //   } else {
        //     this.costPlatformList[item.id] = true
        //   }
        // }
        // // 是否可写
        // const taskFormFilter3 = taskForm.filter(
        //   item => item.id == 'costPlatform_amountPayable' && item.writeable
        // )
        // if (taskFormFilter3.length) {
        //   this.costPlatformList['costPlatform_amountPayable_w'] = true
        // }
        // getDictionary('goods_expense_rule_fee_node').then(res => {
        //   const resData = res.data
        //   if (resData.code == 200) {
        //     // 处理字典数据
        //     const resList = []
        //     for (const item of resData.data) {
        //       resList.push({
        //         key: item.dictKey,
        //         value: item.dictValue,
        //         id: item.id,
        //       })
        //     }
        //     getDictionary('goods_expense_rule_type').then(res => {
        //       const resData = res.data
        //       if (resData.code == 200) {
        //         // 处理字典数据
        //         const resList1 = []
        //         for (const item of resData.data) {
        //           resList1.push({
        //             key: item.dictKey,
        //             value: item.dictValue,
        //             id: item.id,
        //           })
        //         }
        //         //
        //         platformExpensesList2({ financeNo, type: 0 }).then(res => {
        //           const { data: resData, code } = res.data
        //           if (code == 200) {
        //             const arr = []
        //             console.log('resData',resData)
        //             for (const item of resData) {
        //               if (item.calculation == 1) {
        //                 this.calculation = true
        //               }
        //               // 过滤出当前的支付节点
        //               const chargePointFilter = resList.filter(
        //                 itemed => itemed.key == item.feeNode
        //               )
        //               // 过滤出当前的费用类型
        //               const expenseTypeFilter = resList1.filter(
        //                 itemed => itemed.key == item.expenseType
        //               )
        //               arr.push({
        //                 name: item.name,
        //                 expenseTypeStr: expenseTypeFilter[0]
        //                   ? expenseTypeFilter[0].value
        //                   : '',
        //                 chargePoint: chargePointFilter[0]
        //                   ? chargePointFilter[0].value
        //                   : '',
        //                 calculationStr:
        //                   item.calculation != 1
        //                     ? item.feeFormulaName
        //                     : '手动录入',
        //                 amount: item.calculation != 1 ? item.amount : '',
        //                 calculation: item.calculation,
        //               })
        //             }
        //             if (variables.platformExpenses) {
        //               for (const item of variables.platformExpenses) {
        //                 for (const itemed of arr) {
        //                   if (itemed.name == item.name) {
        //                     itemed.amount = item.amount
        //                     break
        //                   }
        //                 }
        //               }
        //             }
        //             this.tableData4 = arr
        //             this.tableData4copy = resData // 专门给后端的数据，不处理
        //           }
        //         })
        //       }
        //     })
        //   }
        // })
      })
    },
    // 通过
    handleExamine(pass) {
      if (this.calculation && pass) {
        for (const item of this.platformFeeList) {
          const cunArr = item.expenseOrderDetailList
          if (cunArr && cunArr.length) {
            for (const item of cunArr ) {
              if (!item.amount) {
                this.$message.warning('请手动录入应付金额')
                return
              }
            }
          }
        }
        // for (const item of this.tableData4) {
        //   if (!item.amount) {
        //     this.$message.error('请手动录入应付金额')
        //     return
        //   }
        // }
      }
      // this.tableData4copy.forEach((item, index) => {
      //   if (item.calculation === 1) {
      //     item.amount = this.tableData4[index].amount
      //   }
      // })

      // if (this.platformFeeList.length) {
      //   this.platformFeeList.forEach(item => {
      //     if (item.expenseOrderDetailList.length) {
      //       this.sum1 += Number(
      //         item.expenseOrderDetailList[
      //           item.expenseOrderDetailList.length - 1
      //         ].amount
      //       )
      //     }
      //   })
      // }
      this.validataFunction(pass)
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      // this.handleCompleteTask(pass, { platformExpenses: this.tableData4copy })
      this.handleCompleteTask(pass, { platformExpenses: this.platformFeeList })
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    handleChange1() {
      // 质押品折叠面板收缩控制
      this.change1Type = !this.change1Type
    },
    handleChange2() {
      // 还款试算折叠面板收缩控制
      this.change2Type = !this.change2Type
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    tableRowClassName({ row }) {
      if (!row.refundTime || row.refundTime === '总计:') {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      if (this.processGoodsObj.type == 2) {
        this.$router.push({
          path: '/pcontrol/pinformation',
          query: { id: this.processGoodsObj.id },
        })
        sessionStorage.setItem('look', 'true')
      } else {
        this.$router.push({
          path: '/pcontrol/purchasing',
          query: { id: this.processGoodsObj.id },
        })
        sessionStorage.setItem('look', 'true')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        & > span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
        & > span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
</style>
