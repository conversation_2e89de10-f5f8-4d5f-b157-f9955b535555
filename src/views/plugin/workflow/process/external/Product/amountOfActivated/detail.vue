<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '额度激活'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{ variables.applyUserName }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <basic-container v-if="applyProductReadable">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">申请产品</h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{ processGoodsObj.goodsName }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">{{
                processGoodsObj.type == 1
                  ? '应收账款质押'
                  : processGoodsObj.type == 2
                  ? '代采融资'
                  : processGoodsObj.type == 5
                  ? '订单融资'
                  : '云信'
              }}</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="tableData.length < 3 ? 2 : 3" border>
                <el-descriptions-item v-for="item in tableData" :key="item.id" :label="item.label">{{
                  item.value
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 合同签署 -->
      <basic-container v-if="contractSigningReadable">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">合同签署</h1>
            <div class="boxs-to-apply-for-product-body">
              <el-table :data="tableData2" :max-height="240" style="width: 100%; border: 1px solid #ebeef5">
                <el-table-column
                  v-if="tableData2Readable.contractSigning_serialNumber"
                  prop="serial"
                  label="序号"
                  width="80"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_contractNumber"
                  prop="contractId"
                  label="合同编号"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_contractTitle"
                  prop="contractTitle"
                  label="合同标题"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_creationTime"
                  prop="createTime"
                  label="创建时间"
                  width="250"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_signTheState"
                  prop="statusText"
                  label="签署状态"
                  min-width="150"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.statusText === '待签署' ? '' : 'success'">{{
                      scope.row.statusText
                    }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_operation"
                  prop="firstTradeTime"
                  label="操作"
                  min-width="100"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div style="font-weight: unset">
                      <span class="view" @click="viewContract(scope)">预览</span>
                      <span class="down" @click="downContract(scope)">下载</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 绑定对公账户 -->
      <basic-container v-if="bindTheCorporateAccountReadable" style="margin-bottom: 40px">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">绑定对公账户</h1>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="3" border>
                <el-descriptions-item v-for="item in tableData3" :key="item.id" :label="item.label">{{
                  item.value
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="account-for-details" v-for="item in tableData3Children" :key="item.id">
              <div class="account-details">
                <div
                  class="enterprise-name flex-box"
                  v-if="bindReadable.bindTheCorporateAccount_downstreamCoreEnterprise"
                >
                  <span>下游核心企业</span>
                  <span>{{ item.enterpriseName }}</span>
                </div>
                <!-- 贸易合同编号 -->
                <div class="enterprise-name flex-box" v-if="bindReadable.bindTheCorporateAccount_contractNumber">
                  <span>贸易合同编号</span>
                  <span>{{ item.tradeContractNo }}</span>
                </div>
                <!-- 核心企业回款账户变更凭证 -->
                <div class="change-certificate flex-box" v-if="bindReadable.bindTheCorporateAccount_changeCertificate">
                  <span>核心企业回款账户变更凭证</span>
                  <div class="change-certificate-box" @click="viewVoucher(item.collectionAccountUrl)">
                    <span>变更凭证</span>
                    <span>
                      <svg-icon icon-class="icon-line-chenggong" style="color: #3dc861; font-size: 16px" />
                    </span>
                  </div>
                </div>
              </div>
              <!-- 签署合同 -->
              <div
                class="sign-aContract"
                v-if="bindReadable.bindTheCorporateAccount_signAContract && item.contractList.length"
              >
                <span>签署合同</span>
                <div class="contract-for-box">
                  <div
                    v-for="item in item.contractList"
                    :key="item.contractId"
                    class="contract-box"
                    @click="viewContract(item.contractId)"
                  >
                    <span>{{ item.contractTitle }}</span>
                    <span>{{ item.contractId ? '已签署' : '待签署' }}</span>
                  </div>
                </div>
              </div>
              <!-- 应收账款质押登记记录截图 -->
              <div class="file-upload-container" v-if="bindReadable.bindTheCorporateAccount_recordTheScreenshot">
                <span>应收账款质押登记记录截图</span>
                <div
                  :class="{
                    'file-upload-box-container': bindWriteable.bindTheCorporateAccount_recordTheScreenshot,
                    'file-upload-box-Unactiv-container': !bindWriteable.bindTheCorporateAccount_recordTheScreenshot,
                  }"
                >
                  <el-upload
                    class="file-upload"
                    drag
                    action="/api/blade-resource/oss/endpoint/put-file-kv"
                    multiple
                    :on-success="
                      (response, file, fileList) => {
                        return handleUpSuccess(response, file, fileList, item)
                      }
                    "
                    :on-remove="
                      (file, fileList) => {
                        return handleFileRemove(file, fileList, item)
                      }
                    "
                    :on-preview="handleFilePreview"
                    :file-list="item.fileListData"
                    :disabled="!bindWriteable.bindTheCorporateAccount_recordTheScreenshot"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">只能上传jpg/jpeg/png/pdf文件，且不超过20M</div>
                  </el-upload>
                  <div class="right-side">
                    <span @click="lockExample(item)" v-if="bindReadable.bindTheCorporateAccount_seeTheSample"
                      >查看示例</span
                    >
                    <span
                      :class="{
                        toRegisterUnactive: !bindWriteable.bindTheCorporateAccount_toRegister,
                        toRegisterActive: bindWriteable.bindTheCorporateAccount_toRegister,
                      }"
                      @click="register()"
                      v-if="bindReadable.bindTheCorporateAccount_toRegister"
                      >去登记</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </basic-container>
    </template>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import FilePreview from '@/components/file-preview'
import {
  getByContractId,
  contractDownload,
  skipToPreview,
  getDictionary,
  getGoodsMaterialList,
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import { goodsTypeToPath } from '../globalFun.js'
export default {
  mixins: [customExForm],
  components: { WfFlow, FilePreview },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      processGoodsObj: {},
      tableData: [],
      tableData2: [],
      tableData2Readable: {
        contractSigning_serialNumber: false,
        contractSigning_contractNumber: false,
        contractSigning_contractTitle: false,
        contractSigning_creationTime: false,
        contractSigning_signTheState: false,
        contractSigning_operation: false,
      },

      tableData3: [],
      tableData3Children: [],
      pdfSrc: '',
      applyProductReadable: true,
      contractSigningReadable: true,
      bindTheCorporateAccountReadable: true,
      bindReadable: {
        bindTheCorporateAccount_downstreamCoreEnterprise: false,
        bindTheCorporateAccount_changeCertificate: false,
        bindTheCorporateAccount_contractNumber: false,
        bindTheCorporateAccount_signAContract: false,
        bindTheCorporateAccount_recordTheScreenshot: false,
        bindTheCorporateAccount_seeTheSample: false,
        bindTheCorporateAccount_toRegister: false,
      },
      bindWriteable: {
        bindTheCorporateAccount_recordTheScreenshot: false,
        bindTheCorporateAccount_toRegister: false,
      },
    }
  },
  methods: {
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        const { variables } = data
        const { taskForm } = res.form
        this.taskForm = taskForm
        this.resData = data
        this.variables = variables || {}
        this.variables.processInstanceId = res.process.processInstanceId
        const { processGoodsInfo } = variables
        this.processGoodsObj = processGoodsInfo

        this.applyProductReadable = this.filterBox(taskForm, 'applyProduct').readable // 申请产品 显隐控制
        this.contractSigningReadable = this.filterBox(taskForm, 'contractSigning').readable // 合同签署 显隐控制
        this.bindTheCorporateAccountReadable = this.filterBox(taskForm, 'bindTheCorporateAccount').readable // 绑定对公账户 显隐控制

        // 流程产品信息
        let loadTer,
          filterTabBar = []
        getDictionary('goods_load_term_unit').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            loadTer = resList.filter(
              // 过滤出当前的最长期限单位
              item => item.key == processGoodsInfo.loadTermUnit
            )
          }
          getDictionary('goods_billing_method').then(res => {
            const resData = res.data
            if (resData.code == 200) {
              // 处理字典数据
              const resList2 = []
              for (const item of resData.data) {
                resList2.push({
                  key: item.dictKey,
                  value: item.dictValue,
                  id: item.id,
                })
              }
              if (processGoodsInfo.repaymentType === 1) {
                if (processGoodsInfo.billingMethod) {
                  processGoodsInfo.billingMethod.split(',').forEach((item, index) => {
                    // 过滤出当前的计费方式
                    filterTabBar[index] = resList2.filter(itemed => itemed.key == item)[0].value
                  })
                }
              } else {
                filterTabBar = ['随借随还']
              }
              const filterTabBarStr = filterTabBar.toString()

              const data = [
                {
                  id: 1,
                  label: '最高可借',
                  value: `${processGoodsInfo.loanAmountEnd}万元`,
                  key: 'applyProduct_loanAmountEnd',
                },
                // {
                //   id: 2,
                //   label: '年利率低至',
                //   value: `${processGoodsInfo.annualInterestRateStart}%`,
                //   key: 'applyProduct_annualInterestRate',
                // },
                {
                  id: 3,
                  label: '最长期限',
                  value: `${processGoodsInfo.loadTermEnd}${loadTer[0].value}`,
                  key: 'applyProduct_loadTermEnd',
                },
                // {
                //   id: 4,
                //   label: '计费方式',
                //   value: filterTabBarStr,
                //   key: 'applyProduct_billingMethod',
                // },
              ]
              if (processGoodsInfo.annualInterestRateStart) {
                const dataArr2 = {
                  id: 2,
                  label: '年利率低至',
                  value: `${processGoodsInfo.annualInterestRateStart}%`,
                  key: 'applyProduct_annualInterestRate',
                }
                data.splice(1, 0, dataArr2)
              }
              if (filterTabBarStr) {
                const dataArr4 = {
                  id: 4,
                  label: '计费方式',
                  value: filterTabBarStr,
                  key: 'applyProduct_billingMethod',
                }
                data.splice(3, 0, dataArr4)
              }

              // 是否可读
              const dataKey = data.map(item => item.key)
              const taskFormFilter = taskForm.filter(item => dataKey.includes(item.id))
              const taskFormId = taskFormFilter.map(item => {
                if (item.readable) {
                  return item.id
                }
              })
              const dataFilter = data.filter(item => taskFormId.includes(item.key))
              this.tableData = dataFilter
            }
          })
        })

        // 合同签署list
        const cotractNameList = [
          'contractSigning_serialNumber',
          'contractSigning_contractNumber',
          'contractSigning_contractTitle',
          'contractSigning_creationTime',
          'contractSigning_signTheState',
          'contractSigning_operation',
        ]
        const cotractNameListFilter = taskForm.filter(item => cotractNameList.includes(item.id))
        const readableList = cotractNameListFilter.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        for (const key in this.tableData2Readable) {
          readableList.forEach(item => {
            if (item == key) {
              this.tableData2Readable[key] = true
            }
          })
        }
        if (variables.contractIdList && variables.contractIdList.length) {
          const idsArr = []
          for (const item of variables.contractIdList) {
            idsArr.push(item)
          }
          const paramsed = {
            ids: idsArr.join(),
          }
          getByContractId(paramsed).then(res => {
            const resData = res.data
            if (resData.code == 200) {
              let num = 1
              for (const item of resData.data) {
                let statusText = ''
                if (item.status) {
                  switch (item.status) {
                    case 1:
                      statusText = '待签署'
                      break
                    case 2:
                      statusText = '已取消'
                      break
                    case 3:
                      statusText = '已签署'
                      break
                    case 4:
                      statusText = '已失效'
                      break
                    case 5:
                      statusText = '已完成'
                      break
                    case 6:
                      statusText = '签署中'
                      break
                  }
                }
                this.tableData2.push({
                  id: item.id,
                  serial: String(num),
                  contractTitle: item.contractTitle,
                  contractId: item.contractId,
                  createTime: item.createTime,
                  statusText: statusText,
                })
                num++
              }
            }
          })
        }

        // 处理对公账户数据
        const corporateAccountData = [
          {
            id: 1,
            label: '指定银行账户',
            value: variables.capitalName,
            key: 'bindTheCorporateAccount_designatedBankAccount',
          },
          {
            id: 2,
            label: '开户行',
            value: variables.bank,
            key: 'bindTheCorporateAccount_bank',
          },
          {
            id: 3,
            label: '银行账户',
            value: variables.bankCardNo,
            key: 'bindTheCorporateAccount_bankAccount',
          },
        ]
        const corporateArr = corporateAccountData.map(item => item.key)
        const corporateFilterArr = taskForm.filter(item => corporateArr.includes(item.id) && item.readable)
        const arrNew = corporateFilterArr.map(item => item.id)
        this.tableData3 = corporateAccountData.filter(item => arrNew.includes(item.key))

        //核心企业数据控制显隐
        // 是否可读
        const bindarrDatas = [
          'bindTheCorporateAccount_downstreamCoreEnterprise',
          'bindTheCorporateAccount_changeCertificate',
          'bindTheCorporateAccount_contractNumber',
          'bindTheCorporateAccount_signAContract',
          'bindTheCorporateAccount_recordTheScreenshot',
          'bindTheCorporateAccount_seeTheSample',
          'bindTheCorporateAccount_toRegister',
        ]
        const bindarrFilterDatas = taskForm.filter(item => bindarrDatas.includes(item.id) && item.readable)
        for (const key in this.bindReadable) {
          for (const item of bindarrFilterDatas) {
            if (key == item.id) {
              this.bindReadable[key] = true
            }
          }
        }
        // 是否可写
        const bindToarrDatas = ['bindTheCorporateAccount_recordTheScreenshot', 'bindTheCorporateAccount_toRegister']
        const bindToarrFilterDatas = taskForm.filter(item => bindToarrDatas.includes(item.id) && item.writeable)
        for (const key in this.bindReadable) {
          for (const item of bindToarrFilterDatas) {
            if (key == item.id) {
              this.bindWriteable[key] = true
            }
          }
        }

        if (
          this.bindReadable.bindTheCorporateAccount_recordTheScreenshot &&
          variables.collectionAccountChangeVouchers
        ) {
          let urlArr,
            UrlArrData = []
          this.tableData3Children = variables.collectionAccounts.map((item, index) => {
            const arr = []
            if (item.contractId) {
              getByContractId({
                ids: item.contractId,
              }).then(res => {
                const resData = res.data
                if (resData.code == 200) {
                  for (const item of resData.data) {
                    arr.push({
                      contractTitle: item.contractTitle,
                      contractId: item.contractId,
                    })
                  }
                }
              })
            }
            if (variables.collectionAccountChangeVouchers) {
              urlArr = variables.collectionAccountChangeVouchers[index].accountsReceivableUrl
              UrlArrData = JSON.parse(JSON.stringify(urlArr))
            }
            return {
              id: item.id,
              enterpriseName: item.enterpriseName,
              collectionAccountUrl: item.collectionAccountUrl,
              tradeContractNo: item.tradeContractNo,
              contractList: arr,
              fileList: JSON.parse(urlArr),
              fileListData: JSON.parse(UrlArrData),
            }
          })
        } else {
          this.tableData3Children = variables.collectionAccounts.map(item => {
            const arr = []
            if (item.contractId) {
              getByContractId({
                ids: item.contractId,
              }).then(res => {
                const resData = res.data
                if (resData.code == 200) {
                  for (const item of resData.data) {
                    arr.push({
                      contractTitle: item.contractTitle,
                      contractId: item.contractId,
                    })
                  }
                }
              })
            }
            return {
              id: item.id,
              enterpriseName: item.enterpriseName,
              collectionAccountUrl: item.collectionAccountUrl,
              tradeContractNo: item.tradeContractNo,
              contractList: arr,
              fileList: [],
              fileListData: [],
            }
          })
        }
        // 每个核心企业添加查看示例
        // 获取流程key(核心企业)
        const processDefinitionKey = res.process.processDefinitionKey
        const GoodsMateriaParams = {
          goodsId: variables.businessId,
          uploadNode: '3-0',
          // 核心企业传2，融资企业传1
          uploadUser: processDefinitionKey === "core_auto_quota_active" ? 2 : 1
        }
        getGoodsMaterialList(GoodsMateriaParams).then(res => {
          const resData = res.data
          if (resData.code == 200) {
            for (const item of resData.data) {
              if (item.example && item.materialName == '应收账款质押登记记录截图') {
                for (const itemed of this.tableData3Children) {
                  itemed.example = item.example
                }
              }
            }
          }
        })
      })
    },
    // 通过
    handleExamine(pass) {
      if (pass) {
        if (this.bindWriteable.bindTheCorporateAccount_recordTheScreenshot) {
          const arrData = JSON.parse(JSON.stringify(this.tableData3Children))
          // 数组无长度，可能是核心企业，直接通过
          if (arrData.length) {
            for (const [index, item] of arrData.entries()) {
              if (item.fileList.length) {
                const accountsReceivableUrl = []
                for (const itemed of item.fileList) {
                  accountsReceivableUrl.push({
                    name: itemed.name,
                    url: itemed.url,
                  })
                }
                item.accountsReceivableUrl = JSON.stringify(accountsReceivableUrl)
                if (index == arrData.length - 1) {
                  this.validataFunction(pass, arrData)
                }
              } else {
                this.$message.error('请确认所有核心企业应收账款质押登记记录截图已上传')
                break
              }
            }
          } else {
            this.validataFunction(pass)
          }
        } else {
          this.validataFunction(pass)
        }
      } else {
        this.validataFunction(pass)
      }
    },
    // 通过后调取接口函数
    validataFunction(pass, arrData = null) {
      this.submitLoading = true
      this.handleCompleteTask(pass, {
        collectionAccountChangeVouchers: arrData,
      })
        .then(() => {
          this.$message.success('处理成功')
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    filterBox(arr, condition) {
      return arr.filter(item => item.id == condition)[0]
    },
    // 查看附件
    viewVoucher(item) {
      if (!item) return
      const file = item.split('/')
      if (file[file.length - 1].split('.')[1] != 'pdf') {
        const imagerDatas = [
          {
            url: `${item}`,
          },
        ]
        this.$ImagePreview(imagerDatas, 0, {
          closeOnClickModal: true,
          beforeClose: () => {
            // this.$message.success('关闭回调')
          },
        })
      } else {
        this.pdfSrc = item + '?time=' + new Date().getMilliseconds()
      }
    },
    // 预览合同
    viewContract(item) {
      if (!item) return
      const params = {
        contractId: item,
      }
      if (item.row) {
        params.contractId = item.row.contractId
      }
      skipToPreview(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    // 下载合同
    downContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      contractDownload(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    viewGoods() {
      goodsTypeToPath(this.processGoodsObj)
    },
    handleUpSuccess(response, file, fileList, item) {
      const arr = []
      for (const itemed of fileList) {
        if (itemed.response) {
          const { data = {} } = itemed.response
          arr.push({
            name: data.name,
            url: data.url,
            // attachId: data.attachId,
          })
        } else {
          arr.push({
            name: itemed.name,
            url: itemed.url,
          })
        }
      }
      item.fileList = arr
    },
    handleFileRemove(file, fileList, item) {
      item.fileList = fileList
    },
    handleFilePreview(file) {
      if (!file.response.data) return
      const targetUrl = file.response.data.url
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    lockExample(item) {
      this.viewVoucher(item.example)
    },
    register() {
      if (this.bindWriteable.bindTheCorporateAccount_toRegister) {
        window.open('https://www.zhongdengwang.org.cn/')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        // width: 107px;
        // height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
        padding: 2px 10px;
        box-sizing: border-box;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}
.account-for-details {
  display: flex;
  flex-direction: column;
  border-top: #000 dashed 1px;
  margin-top: 20px;
  padding-top: 20px;

  .account-details {
    display: flex;
    justify-content: space-between;

    .flex-box {
      display: flex;
      flex-direction: column;
    }

    .enterprise-name {
      & > span:first-child {
        width: 349px;
        height: 21px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 11px;
      }
      & > span:last-child {
        width: 349px;
        height: 21px;
        color: rgba(0, 0, 0, 1);
        font-size: 14px;
      }
    }

    .change-certificate {
      width: 354px;
      margin-right: 24px;
      cursor: context-menu;

      & > span:first-child {
        width: 349px;
        height: 21px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 6px;
      }

      .change-certificate-box {
        background-color: rgba(246, 246, 246, 100);
        height: 30px;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 0 9px;
        cursor: pointer;

        & > span:first-child {
          color: rgba(72, 72, 72, 100);
          font-size: 14px;
          position: relative;

          &::before {
            content: '';
            display: inline-block;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 1px;
            background: #000;
            transition: width 0.3s;
          }
        }

        &:hover span:first-child::before {
          width: 100%;
        }
      }
    }
  }

  .sign-aContract {
    cursor: context-menu;
    margin-top: 20px;

    & > span:first-child {
      width: 349px;
      height: 21px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 6px;
    }

    .contract-for-box {
      display: flex;
      flex-wrap: wrap;
      margin-top: -6px;

      .contract-box {
        background-color: rgba(246, 246, 246, 100);
        width: calc((100% - 48px) / 3);
        height: 30px;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 24px;
        margin-top: 12px;
        padding: 0 9px;
        box-sizing: border-box;
        cursor: pointer;

        &:nth-child(3n) {
          margin-right: 0;
        }

        & > span:first-child {
          color: rgba(105, 124, 255, 100);
          font-size: 14px;
          position: relative;

          &::before {
            content: '';
            display: inline-block;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 1px;
            background: rgba(105, 124, 255, 100);
            transition: width 0.3s;
          }
        }

        &:hover span:first-child::before {
          width: 100%;
        }

        & > span:last-child {
          color: rgba(61, 200, 97, 100);
          font-size: 14px;
        }
      }
    }
  }

  .file-upload-container {
    display: flex;
    flex-direction: column;
    align-items: start;
    margin-top: 20px;

    & > span:first-child {
      width: 349px;
      height: 21px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .view-demo {
      margin-left: 22px;
    }
    .file-upload-box-container {
      display: flex;

      ::v-deep {
        .el-upload-list__item {
          background-color: #f5f7fa;
        }
        .el-upload-list__item .el-progress {
          display: none;
        }
      }

      .right-side {
        display: flex;
        flex-direction: column;
        margin-left: 22px;

        & > span:first-child {
          width: 84px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          color: rgba(16, 16, 16, 100);
          font-size: 14px;
          text-align: center;
          border: 1px solid rgba(187, 187, 187, 100);
          cursor: pointer;
        }
        .toRegisterActive {
          width: 84px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          background-color: rgba(105, 124, 255, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          text-align: center;
          border: 1px solid rgba(105, 124, 255, 100);
          margin-top: 10px;
          cursor: pointer;
        }
        .toRegisterUnactive {
          width: 84px;
          height: 30px;
          line-height: 30px;
          opacity: 0.4;
          border-radius: 4px;
          background-color: rgba(105, 124, 255, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          text-align: center;
          border: 1px solid rgba(105, 124, 255, 100);
          margin-top: 10px;
          cursor: not-allowed;
        }
      }
    }
    .file-upload-box-Unactiv-container {
      display: flex;

      ::v-deep {
        .el-upload-list__item {
          background-color: #f5f7fa;
        }
        .el-upload-list__item .el-progress {
          display: none;
        }
        .el-upload-dragger {
          background-color: #ebebeb;
          cursor: not-allowed;
        }
        .el-upload__text {
          color: #a7a7a7;
        }
      }

      .right-side {
        display: flex;
        flex-direction: column;
        margin-left: 22px;

        & > span:first-child {
          width: 84px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          color: rgba(16, 16, 16, 100);
          font-size: 14px;
          text-align: center;
          border: 1px solid rgba(187, 187, 187, 100);
          cursor: pointer;
        }
        .toRegisterActive {
          width: 84px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          background-color: rgba(105, 124, 255, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          text-align: center;
          border: 1px solid rgba(105, 124, 255, 100);
          margin-top: 10px;
          cursor: pointer;
        }
        .toRegisterUnactive {
          width: 84px;
          height: 30px;
          line-height: 30px;
          opacity: 0.4;
          border-radius: 4px;
          background-color: rgba(105, 124, 255, 100);
          color: rgba(255, 255, 255, 100);
          font-size: 14px;
          text-align: center;
          border: 1px solid rgba(105, 124, 255, 100);
          margin-top: 10px;
          cursor: not-allowed;
        }
      }
    }
  }
}
// // 更改外部组件默认样式
// ::v-deep i.el-icon-caret-bottom {
//   font-size: 150%;
//   transform: rotate(-90deg);
//   transition: transform 0.4s;
// }
// ::v-deep i.el-collapse-item__arrow {
//   display: none;
// }
// ::v-deep div.el-collapse-item__header {
//   height: 15px;
//   border-bottom: none;
// }
// ::v-deep div.el-collapse-item__content {
//   padding-bottom: 0 !important;
// }
// ::v-deep .el-collapse {
//   border-top: none;
//   border-bottom: none;
// }
// ::v-deep .el-card {
//   border-radius: 8px;
//   .el-collapse-item__wrap {
//     border-bottom: none;
//   }
// }
// .bord-radius-left {
//   border-radius: 20px 0 0 20px !important;
//   border-left-color: #b3d8ff !important;
// }
// .bord-radius-right {
//   border-radius: 0 20px 20px 0 !important;
//   border-right-color: #b3d8ff !important;
// }
// // 覆盖组件库样式
// ::v-deep {
//   .el-tabs--card {
//     .el-tabs__header {
//       border-bottom: none;
//     }
//     .el-tabs__item {
//       border: none;
//       padding: 0;
//     }
//     .el-tabs__item:nth-child(2) {
//       padding-left: 0 !important;
//     }
//     .is-plain {
//       background: #fff;
//       border-color: #b3d8ff;
//     }
//     .is-plain:hover {
//       color: #409eff;
//       background: #ecf5ff;
//       border-color: #b3d8ff;
//     }
//   }
//   .avue-form__menu {
//     display: none;
//   }
//   .el-input.is-disabled .el-input__inner {
//     color: #000;
//   }
//   .el-textarea.is-disabled .el-textarea__inner {
//     color: #000;
//   }
//   span {
//     display: inline-block;
//   }
// }
</style>
