<template>
  <div class="container">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '赎货申请审批'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 自定义主体 - 开始 -->
      <!-- 赎货单 -->
      <basic-container
        v-if="customForm.redemptionNote && customForm.redemptionNote.readable"
      >
        <div class="foreclosure-note-container" v-loading="pageDataLoading">
          <!-- 加载动画 -->
          <template v-if="pageDataLoading">
            <div style="height: 200px" />
          </template>
          <!-- 内容主体 -->
          <template v-else>
            <div class="title-container">
              <span class="title">赎货单</span>
              <template
                v-if="
                  customForm.redemptionNote_redeemNo &&
                  customForm.redemptionNote_redeemNo.readable
                "
              >
                <span class="divider" />
                <div class="number-container">
                  <span class="number-type">赎货单号：</span>
                  <span class="number-value">{{ variables.redeemNo }}</span>
                </div>
              </template>
            </div>
            <div class="goods-container">
              <template
                v-if="
                  customForm.redemptionNote_redeemGoods &&
                  customForm.redemptionNote_redeemGoods.readable
                "
              >
                <div class="goods-wrapper">
                  <el-image
                    style="
                      width: 72px;
                      height: 72px;
                      flex-shrink: 0;
                      border-radius: 4px;
                    "
                    :src="redeemData.goodLogo"
                    fit="contain"
                  />
                  <div class="goods-content">
                    <span class="goods-name">{{ redeemData.goodsName }}</span>
                    <div class="goods-type-container">
                      <span class="goods-type">规格型号：</span>
                      <span class="goods-type-value">{{
                        redeemData.goodsSpec
                      }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_purchasePrice &&
                  customForm.redemptionNote_purchasePrice.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">采购单价(元)</span>
                  <span class="goods-desc-value">{{
                    redeemData.purchasePrice | formatMoney
                  }}</span>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_financingPrice &&
                  customForm.redemptionNote_financingPrice.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">融资单价(元)</span>
                  <span class="goods-desc-value">{{
                    redeemData.financingPrice | formatMoney
                  }}</span>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_redeemNum &&
                  customForm.redemptionNote_redeemNum.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">赎货数量</span>
                  <span class="goods-desc-value">{{ redeemData.num }}</span>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_goodsUnitValue &&
                  customForm.redemptionNote_goodsUnitValue.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">单位</span>
                  <span class="goods-desc-value">{{
                    redeemData.goodsUnitValue
                  }}</span>
                </div>
              </template>
            </div>
            <div class="descriptions-container">
              <el-descriptions labelStyle="width: 136px" :column="3" border>
                <template
                  v-if="
                    customForm.redemptionNote_stockNumber &&
                    customForm.redemptionNote_stockNumber.readable
                  "
                >
                  <el-descriptions-item label="库存编号">{{
                    redeemData.warehouseNo || '--'
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_financingNumber &&
                    customForm.redemptionNote_financingNumber.readable
                  "
                >
                  <el-descriptions-item label="融资编号">{{
                    redeemData.financeNo || '--'
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_supplierName &&
                    customForm.redemptionNote_supplierName.readable
                  "
                >
                  <el-descriptions-item label="供应商">{{
                    redeemData.supplierName || '--'
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_warehousingCompany &&
                    customForm.redemptionNote_warehousingCompany.readable
                  "
                >
                  <el-descriptions-item label="仓储公司">{{
                    pageData.storageCompany || '--'
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_storehouse &&
                    customForm.redemptionNote_storehouse.readable
                  "
                >
                  <el-descriptions-item label="仓库">{{
                    pageData.warehouseName || '--'
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_warehouseInDate &&
                    customForm.redemptionNote_warehouseInDate.readable
                  "
                >
                  <el-descriptions-item label="入库日期">{{
                    redeemData.warehouseInDate || '--'
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_warehouseAge &&
                    customForm.redemptionNote_warehouseAge.readable
                  "
                >
                  <el-descriptions-item label="库龄(天)">{{
                    redeemData.warehouseAge === null
                      ? '--'
                      : redeemData.warehouseAge
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_redemptionDate &&
                    customForm.redemptionNote_redemptionDate.readable
                  "
                >
                  <el-descriptions-item label="约定赎货日">{{
                    redeemData.redemptionDate || '--'
                  }}</el-descriptions-item>
                </template>
                <template
                  v-if="
                    customForm.redemptionNote_extractType &&
                    customForm.redemptionNote_extractType.readable
                  "
                >
                  <el-descriptions-item label="提货方式">{{
                    redeemData.extractType || '--'
                  }}</el-descriptions-item>
                </template>
              </el-descriptions>
            </div>
            <template
              v-if="
                customForm.redemptionNote_delivery &&
                customForm.redemptionNote_delivery.readable
              "
            >
              <!-- 收货地址 -->
              <div
                v-if="redeemData.extractType === '第三方物流'"
                class="delivery-address-container"
              >
                <span class="title">收货地址</span>
                <div class="form-item">
                  <span class="label">收货对象</span>
                  <span class="value">{{
                    redeemData.financingAddress.enterpriseName
                  }}</span>
                </div>
                <div class="form-item">
                  <span class="label">收货地址</span>
                  <span class="value">{{
                    redeemData.financingAddress.addressTarget ||
                    redeemData.financingAddress.urbanAreas
                  }}</span>
                </div>
                <div class="form-item">
                  <span class="label">联系人</span>
                  <span class="value">{{
                    redeemData.financingAddress.contacts
                  }}</span>
                </div>
                <div class="form-item">
                  <span class="label">联系方式</span>
                  <span class="value">{{
                    redeemData.financingAddress.addressPhone
                  }}</span>
                </div>
              </div>
              <!-- 自提信息 -->
              <div v-else class="delivery-address-container">
                <span class="title">自提信息</span>
                <div class="form-item">
                  <span class="label">赎货人</span>
                  <span class="value">{{
                    `${redeemData.redeemUser.username}｜${redeemData.redeemUser.idCard}｜${redeemData.redeemUser.phone}`
                  }}</span>
                </div>
                <div class="form-item">
                  <span class="label">车牌号</span>
                  <span class="value">{{
                    redeemData.redeemUser.licensePlate
                  }}</span>
                </div>
                <div class="form-item">
                  <span class="label">提货日期</span>
                  <span class="value">{{
                    redeemData.redeemUser.arriveTime
                  }}</span>
                </div>
              </div>
            </template>
          </template>
        </div>
      </basic-container>
      <!-- 还款单 -->
      <basic-container
        v-if="
          customForm.repaymentNote_bank &&
          customForm.repaymentNote_bank.readable
        "
      >
        <div class="repayment-note-container" v-loading="pageDataLoading">
          <!-- 加载动画 -->
          <template v-if="pageDataLoading">
            <div style="height: 200px" />
          </template>
          <!-- 内容主体 -->
          <template v-else>
            <div class="title-container">
              <span class="title">还款单</span>
            </div>
            <div class="form-container">
              <!-- 银行还款单 -->
              <template
                v-if="
                  customForm.repaymentNote_bank &&
                  customForm.repaymentNote_bank.readable
                "
              >
                <div class="table-top refund">
                  <div class="table-title-box">
                    <div class="title-left-box">
                      <span>银行还款单</span>
                      <!-- <template
                        v-if="
                          customForm.repaymentNote_bank_yearRate &&
                          customForm.repaymentNote_bank_yearRate.readable
                        "
                      >
                        <span />
                        <span>年化利率{{ pageData.yearRate || '--' }}%</span>
                      </template> -->
                    </div>
                    <!-- <template
                      v-if="
                        customForm.repaymentNote_bank_repayFunds &&
                        customForm.repaymentNote_bank_repayFunds.readable
                      "
                    >
                      <div class="title-right-box">
                        应还本金(元)
                        <span style="color: black">{{
                          (Number(redeemData.financingPrice) *
                            Number(redeemData.num))
                            | formatMoney
                        }}</span>
                      </div>
                    </template> -->
                  </div>
                  <div class="zifangfeiyongzhanshi-d">
                    <p>年利率: <span>{{ yinhangDataObj.yearRate }}</span> %</p>
                    <p>日利率: <span>{{ yinhangDataObj.dayRate }}</span> %</p>
                    <p>本金: <span>{{ yinhangDataObj.principal | formatMoney }}</span> (元)</p>
                    <p>利息: <span>{{ yinhangDataObj.interest | formatMoney }}</span> (元)</p>
                    <p>总计: <span>{{ yinhangDataObj.total | formatMoney }}</span> (元)</p>
                  </div>
                  <!-- <el-table
                    ref="table2"
                    :data="tableData2"
                    style="width: 100%; margin-top: 13px"
                    class="table-border-style"
                    :summary-method="getSummaries"
                    :show-summary="
                      customForm.repaymentNote_bank_table_money &&
                      customForm.repaymentNote_bank_table_money.readable
                    "
                  >
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_name &&
                        customForm.repaymentNote_bank_table_name.readable
                      "
                      prop="name"
                      label="费用名称"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_expenseType &&
                        customForm.repaymentNote_bank_table_expenseType.readable
                      "
                      prop="expenseType"
                      label="费用类型"
                    >
                      <template slot-scope="{ row }">
                        <Tag
                          :name="row.expenseType"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_node &&
                        customForm.repaymentNote_bank_table_node.readable
                      "
                      prop="node"
                      label="支付节点"
                    >
                      <template>
                        <Tag
                          name="确认赎货"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_feeFormula &&
                        customForm.repaymentNote_bank_table_feeFormula.readable
                      "
                      prop="feeFormula"
                      label="计费方式"
                    />
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_money &&
                        customForm.repaymentNote_bank_table_money.readable
                      "
                      prop="money"
                      label="应付金额(元)"
                    >
                      <template slot-scope="{ row }">
                        <span> ￥{{ row.money | formatMoney }} </span>
                      </template>
                    </el-table-column>
                  </el-table> -->
                </div>
              </template>
              <!-- <div class="chain-line" /> -->

              <!-- 平台费用单 -->
              <!-- <template
                v-if="
                  customForm.repaymentNote_platform &&
                  customForm.repaymentNote_platform.readable
                "
              >
                <div class="table-top refund">
                  <div class="table-title-box">
                    <div class="title-left-box">
                      <span>平台费用单</span>
                    </div>
                  </div>
                  <el-table
                    ref="table3"
                    :data="tableData3"
                    style="width: 100%; margin-top: 13px"
                    class="table-border-style"
                    :summary-method="getSummaries"
                    :show-summary="
                      customForm.repaymentNote_platform_table_money &&
                      customForm.repaymentNote_platform_table_money.readable
                    "
                  >
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_name &&
                        customForm.repaymentNote_bank_table_name.readable
                      "
                      prop="name"
                      label="费用名称"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_expenseType &&
                        customForm.repaymentNote_bank_table_expenseType.readable
                      "
                      prop="expenseType"
                      label="费用类型"
                    >
                      <template slot-scope="{ row }">
                        <Tag
                          :name="row.expenseType"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_node &&
                        customForm.repaymentNote_bank_table_node.readable
                      "
                      prop="node"
                      label="支付节点"
                    >
                      <template>
                        <Tag
                          name="确认赎货"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_feeFormula &&
                        customForm.repaymentNote_bank_table_feeFormula.readable
                      "
                      prop="feeFormula"
                      label="计费方式"
                    />
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_money &&
                        customForm.repaymentNote_bank_table_money.readable
                      "
                      prop="money"
                      label="应付金额(元)"
                    >
                      <template slot-scope="{ row }">
                        <template v-if="row.enableInput">
                          <div style="width: 80%">
                            <el-input
                              placeholder="请输入金额"
                              v-model="row.money"
                              type="number"
                            >
                              <template slot="append">元</template>
                            </el-input>
                          </div>
                        </template>
                        <template v-else>
                          <span>￥{{ row.money | formatMoney }}</span>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template> -->

              <!-- 其他费用单 -->
              <template
                v-if="
                  customForm.repaymentNote_totalRepayment &&
                  customForm.repaymentNote_totalRepayment.readable
                "
              >
                <div
                  v-for="(item, index) in tableData4"
                  :key="index"
                  class="table-top refund"
                >
                  <div class="table-title-box">
                    <div class="title-left-box">
                      <span>{{ item.parenExpenseName }}</span>
                    </div>
                    <div
                      class="account"
                      v-if="item.totalMoney && item.payMode === 1"
                    >
                      <div class="accountText">账户：</div>
                      <div class="accountNum">
                        <el-input
                          :value="item.openHouseName"
                          size="medium"
                          @click.native="selectAccount(item)"
                          suffixIcon="el-icon-arrow-right"
                        ></el-input>
                      </div>
                    </div>
                  </div>
                  <el-table
                    ref="table3"
                    :data="item.expenseOrderDetailList"
                    style="width: 100%; margin-top: 13px"
                    class="table-border-style"
                    :summary-method="getSummaries"
                    :show-summary="
                      customForm.repaymentNote_platform_table_money &&
                      customForm.repaymentNote_platform_table_money.readable
                    "
                  >
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_name &&
                        customForm.repaymentNote_bank_table_name.readable
                      "
                      prop="expenseTypeStr"
                      label="费用名称"
                    >
                    </el-table-column>
                    <!-- <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_expenseType &&
                        customForm.repaymentNote_bank_table_expenseType.readable
                      "
                      prop="expenseTypeStr"
                      label="费用类型"
                    >
                      <template slot-scope="{ row }">
                        <Tag
                          :name="row.expenseTypeStr"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column> -->
                    <el-table-column
                      prop="repaymentTerm"
                      label="期数"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_node &&
                        customForm.repaymentNote_bank_table_node.readable
                      "
                      prop="feeNodeStr"
                      label="计算节点"
                    >
                      <template slot-scope="{ row }">
                        <Tag
                          :name="row.feeNodeStr"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_node &&
                        customForm.repaymentNote_bank_table_node.readable
                      "
                      prop="collectFeesNodeStr"
                      label="收费节点"
                    >
                      <template slot-scope="{ row }">
                        <Tag
                          :name="row.collectFeesNodeStr"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_feeFormula &&
                        customForm.repaymentNote_bank_table_feeFormula.readable
                      "
                      prop="feeFormulaName"
                      label="计费方式"
                    />
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_money &&
                        customForm.repaymentNote_bank_table_money.readable
                      "
                      prop="amount"
                      label="应付金额(元)"
                    >
                      <template slot-scope="{ row }">
                        <template v-if="row.enableInput">
                          <div style="width: 80%">
                            <el-input
                              placeholder="请输入金额"
                              v-model="row.amount"
                              type="number"
                            >
                              <template slot="append">元</template>
                            </el-input>
                          </div>
                        </template>
                        <template v-else>
                          <span>￥{{ row.amount | formatMoney }}</span>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>

              <template
                v-if="
                  customForm.repaymentNote_totalRepayment &&
                  customForm.repaymentNote_totalRepayment.readable
                "
              >
                <!-- <div class="chain-line" /> -->

                <div class="fees-at-box">
                  <div class="fees-right-at">
                    应还总额：
                    <span>￥{{ totalRepayment | formatMoney }} </span>
                  </div>
                </div>
              </template>
            </div>
          </template>
        </div>
      </basic-container>
      <!-- <Dialog
        ref="editAddressDialog"
        title="更改地址"
        center
        noButton
        @cancel="handleCancel"
        @confirm="handleConfirm"
      >
        <div class="form-wrapper">
          <avue-form :option="option" v-model="form" @submit="handleSubmit" />
        </div>
      </Dialog> -->
      <!-- 自定义主体 - 结束 -->

      <!-- 内置表单渲染 -->
      <template v-if="inlayFormShow">
        <basic-container>
          <div
            :class="inlayProcess.status !== 'todo' ? 'wf-theme-default' : ''"
          >
            <avue-form
              v-if="
                inlayOption &&
                ((inlayOption.column && inlayOption.column.length > 0) ||
                  (inlayOption.group && inlayOption.group.length > 0))
              "
              v-model="inlayForm"
              ref="inlayFormRef"
              :defaults.sync="inlayDefaults"
              :option="inlayOption"
              :upload-preview="handleUploadPreview"
            >
            </avue-form>
          </div>
        </basic-container>
      </template>

      <basic-container>
        <div class="approval-container">
          <span class="title">批复意见：</span>
          <el-input
            class="value"
            type="textarea"
            :rows="5"
            resize="none"
            placeholder="请输入批复意见"
            v-model="comment"
          >
          </el-input>
        </div>
      </basic-container>
    </template>
    <!-- 底部按钮 -->
    <wf-button
      class="custom-button"
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleInlayVerify"
      @user-select="handleUserSelect"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcessMyFUn"
      @withdraw="handleWithdrawTask"
    ></wf-button>
    <!-- 选择账户弹窗 -->
    <el-dialog
      title="选择账户"
      :visible.sync="type2"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="avue-dialog"
      width="70%"
    >
      <avue-crud
        ref="crud"
        :option="payMode == 1 ? arrOption2 : arrOption3"
        :data="arr_2"
        @row-click="rowClick2"
        @search-reset="searchReset2"
        @search-change="searchChange2"
        @size-change="sizeChangeScope2"
        :table-loading="tableLoading2"
        :page.sync="accountPagingObj"
        @current-change="currentChangeScope2"
      >
        <template slot="radio" slot-scope="{ row }">
          <el-radio v-model="selectRow2" :label="row.$index">&nbsp;</el-radio>
        </template>
      </avue-crud>
      <div class="avue-dialog__footer">
        <el-button @click="type2 = false">取 消</el-button>
        <el-button @click="cardEngth2" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WfButton from '../../components/button.vue'
import WfFlow from '../../components/flow.vue'
import customExForm from '../../../mixins/custom-ex-form'
import Tag from '@/views/customer/archives/components/Tag/index.vue'
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { formatMoney } from '@/util/filter'
import {
  customerTagdicDataMap,
  enterpriseTypeDicDataMap,
  coreEnterpriseTypeDicDataMap,
} from '@/views/customer/archives/config'
import { getRedeemDetail } from '@/api/plugin/workflow/custom/redeem'
import insideOutsideForm from '@/views/plugin/workflow/mixins/inside-outside-form'
import { selectListAccount } from '@/api/goods/pcontrol/pinformation'

export default {
  mixins: [customExForm, insideOutsideForm],
  components: {
    WfButton,
    WfFlow,
    Tag,
    Dialog,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) {
            this.getDetail(taskId, processInsId)
          }
        }
      },
      immediate: true,
    },
  },
  computed: {
    totalRepayment() {
      let tableData4ToTal = 0
      this.tableData4.forEach(item => {
        tableData4ToTal = this.$numJiaFun(tableData4ToTal, item.totalMoney)
      })
      // return (
      //   Number(this.redeemData.financingPrice) * Number(this.redeemData.num) +
      //   this.tableData2.reduce((sum, val) => sum + Number(val.money), 0) +
      //   this.tableData3.reduce((sum, val) => sum + Number(val.money), 0) +
      //   tableData4ToTal
      // )
      return this.$numJiaFun(tableData4ToTal, this.yinhangDataObj.total)
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      variables: {},
      resData: {},
      // 自定义表单显影数据
      customForm: {},
      // 页面字典
      customerTagdicDataMap,
      enterpriseTypeDicDataMap,
      coreEnterpriseTypeDicDataMap,
      // ------------ 页面数据 -----------
      pageDataLoading: true,
      pageData: {},
      redeemData: { financingAddress: {}, redeemUser: {} },
      redeemSend: {},
      // tableData2: [],
      // tableData3: [{ money: '' }, { money: '' }],
      tableData4: [],
      // 普通账户
      arrOption2: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },
      // 虚拟账户
      arrOption3: {
        selection: false,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: false,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchBtn: true,
        searchMenuPosition: 'right',
        searchMenuSpan: 12, // 搜索菜单栏宽带
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        maxHeight: 300,
        index: false,
        column: [],
      },
      type2: false,
      payMode: null,
      arr_2: [],
      accountType: 0,
      selectRow2: '',
      searchChangeData2: {},
      accountPagingObj: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 30, 40, 50, 100],
        layout: 'total,sizes,pager',
      },
      tableLoading2: false,
      // 临时保存数据的
      TempObj: {},
      tableDataResult: {},
      // 存储银行费用信息
      yinhangDataObj: {},
      costCalculusVOCunChu: {},
    }
  },
  methods: {
    selectAccount(accountInfo) {
      this.payMode = accountInfo.payMode
      this.initialize2()
      this.accountType = accountInfo.accountType
      this.selectedWarehouse(
        accountInfo.payMode == 1 ? 2 : 1,
        accountInfo.openHouseName
      )
      // this.type2 = 2
      this.TempObj = accountInfo
    },
    // 账户的
    rowClick2(row) {
      if (this.selectRow2 !== row.$index) {
        this.selectRow2 = row.$index
        this.rowsData = row // 点击当前行数据
      }
    },
    initialize2() {
      this.accountPagingObj.total = 0
      this.accountPagingObj.pageSize = 10
      this.accountPagingObj.currentPage = 1
      this.searchChangeData2 = {}
      this.selectRow2 = false
      this.arr_2 = []
    },
    // 账户弹窗--弹窗搜索事件
    searchChange2(params, done) {
      this.searchChangeData2 = params
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(params, type)
      done()
    },
    // 账户弹窗--弹窗清空按钮事件
    searchReset2() {
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun({}, type)
    },
    // 账户弹窗--分页页码切换事件
    currentChangeScope2(currentPage) {
      this.accountPagingObj.currentPage = currentPage
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(this.searchChangeData2, type)
    },
    // 账户弹窗--分页页数切换事件
    sizeChangeScope2(pageSize) {
      // 分页页数切换事件
      this.accountPagingObj.currentPage = 1
      this.accountPagingObj.pageSize = pageSize
      let type = this.payMode == 1 ? 2 : 1
      this.selectListAccountFun(this.searchChangeData2, type)
    },
    selectedWarehouse(type, accountName) {
      this.selectListAccountFun({}, type, accountName)
      this.type2 = true
    },
    cardEngth2() {
      if (this.rowsData) {
        this.TempObj.openHouseName = this.rowsData.openHouseName
        this.TempObj.accountId = this.rowsData.id
        this.TempObj.bankCardNo = this.rowsData.bankCardNo
        this.TempObj.bankDeposit = this.rowsData.bankDeposit
        this.TempObj.enterpriseName = this.rowsData.enterpriseName
        if (this.rowsData.merchantNo) {
          this.TempObj.merchantNo = this.rowsData.merchantNo
        }
      }

      this.type2 = false
    },
    // 账户数据
    selectListAccountFun(filter = {}, type, accountName) {
      this.tableLoading2 = true
      const params = {
        enterpriseName: filter.enterpriseName,
        bankCardNo: filter.bankCardNo,
        accountType: this.accountType,
        current: this.accountPagingObj.currentPage,
        size: this.accountPagingObj.pageSize,
      }
      params.type = type
      selectListAccount(params).then(({ data }) => {
        if (data.success) {
          this.tableLoading2 = false
          const { data: resData } = data
          this.accountPagingObj.total = resData.total || 0
          if (resData.records.length) {
            this.arr_2 = resData.records
            // 反选之前已选列
            let onlyId = accountName

            for (const [index, item] of resData.records.entries()) {
              if (onlyId && item.openHouseName === onlyId) {
                this.selectRow2 = index
                break
              } else if (
                resData.records.length - 1 == index &&
                (this.selectRow2 === 0 || this.selectRow2)
              ) {
                this.selectRow2 = false
              }
            }
          } else {
            this.arr_2 = []
          }
        }
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false
        if (res) {
          // 内置表单start
          const { process, form } = res
          this.inlayProcess = process
          const { variables, status } = process
          const { allForm, indepFormKey } = form
          if (allForm) {
            this.indepFormKey = indepFormKey
            const { option, vars } = this.handleResolveOption(
              eval('(' + allForm + ')'),
              form.taskForm,
              status
              // 'finished'
            )
            option.menuBtn = false
            const myVariables = {}
            for (let key in variables) {
              // if (!variables[key]) delete variables[key]
              if (key.indexOf('@_@') !== -1) {
                const myKey = key.split('@_@')
                if (myKey.length > 1) myVariables[myKey[1]] = variables[key]
              }
            }
            const co = option.column
            const gr = option.group
            if ((co && co.length) || (gr && gr.length)) {
              this.inlayFormShow = true
            }
            this.inlayOption = option
            this.inlayVars = vars
            this.inlayForm = myVariables
          }
          // 内置表单end

          this.costCalculusVOCunChu = variables.costCalculusVO
          const costCalculusVO = this.costCalculusVOCunChu
          if (costCalculusVO) {
            // 资方数据
            this.yinhangDataObj = costCalculusVO.showRepaymentPlan || {}
            // 动态费用
            const expenseOrderDetailFinanceVos = costCalculusVO.expenseOrderDetailFinanceVos
            if (expenseOrderDetailFinanceVos && expenseOrderDetailFinanceVos.length) {
              for (const item of expenseOrderDetailFinanceVos) {
                item.totalMoney = 0
                for (const citem of item.expenseOrderDetailList) {
                  citem.repaymentTerm = citem.repaymentTerm
                      ? citem.repaymentTerm + '期'
                      : '--'
                  item.totalMoney = this.$numJiaFun(item.totalMoney, citem.amount)
                  if (citem.amount && Number(citem.amount) === 0) {
                    citem.enableInput = true
                  }
                }
              }
              this.tableData4 = expenseOrderDetailFinanceVos
            }
          }

          // const { variables = {} } = res.process
          const { taskForm = [] } = res.form || {}
          const customForm = {}
          for (const item of taskForm) {
            customForm[item.id] = item
          }
          // 自定义页面数据
          const requestObj = {
            redeemNo: variables.redeemNo,
          }
          getRedeemDetail(requestObj)
            .then(({ data }) => {
              if (data.success) {
                data = data.data
                const redeemData = data.redeemDetailCargoCurrencyVO
                const redeemSend = data.redeemSend
                redeemData.financingAddress = redeemData.financingAddress || {}
                redeemData.redeemUser = redeemData.redeemUser || {}
                // 银行还款单
                // let tableData2 = []
                // if (
                //   redeemData.manExpenseCulationVo &&
                //   redeemData.manExpenseCulationVo.manExpenseCulationVoList
                // ) {
                //   tableData2 =
                //     redeemData.manExpenseCulationVo.manExpenseCulationVoList
                // }
                // const tableDataResult = redeemData.manExpenseCulationVo || {}
                // 平台费用单
                // const tableData3 = redeemData.plaExpenseCulationVoList || []
                // for (const item of tableData3) {
                //   if (item.money === null) {
                //     item.enableInput = true
                //   }
                // }
                // 其他的费用单

                // const tableData4 = redeemData.plaExpenseVOList || []
                // for (const item of tableData4) {
                //   item.totalMoney = 0
                //   for (const citem of item.plaExpenseCulationVoList) {
                //     if (citem.money === null) {
                //       citem.enableInput = true
                //     }
                //   }
                // }
                this.pageDataLoading = false
                this.pageData = data
                this.redeemSend = redeemSend
                this.redeemData = redeemData
                // this.tableData2 = tableData2
                // this.tableDataResult = tableDataResult
                // this.tableData3 = tableData3
                // this.tableData4 = tableData4
              }
            })
            .catch(() => {})

          this.resData = res.process
          this.variables = variables
          this.customForm = customForm
        }
      })
    },
    // 通过
    handleExamine(pass) {
      // this.submitLoading = true
      // 处理变量
      // const variables = {
      //   expenseInfo: [],
      // }
      // for (const item of this.tableData3) {
      //   if (item.enableInput) {
      //     variables.expenseInfo.push({ id: item.id, money: item.money })
      //   }
      // }
      // 后端当前需要以字符串接收
      // variables.expenseInfo = JSON.stringify(variables.expenseInfo)
      // let expenseInfoStr = ''
      // for (const item of variables.expenseInfo) {
      //   if (expenseInfoStr !== '') {
      //     expenseInfoStr += ','
      //   }
      //   expenseInfoStr += item.money
      // }
      // variables.expenseInfo = expenseInfoStr
      // this.redeemData.plaExpenseVOList.forEach(item => {
      //   if (!item.totalMoney) {
      //     item.accountName = ''
      //     item.accountId = ''
      //   } else {
      //     item.plaExpenseCulationVoList.forEach(citem => {
      //       if (citem.accountId != item.accountId) {
      //         citem.accountId = item.accountId
      //       }
      //     })
      //   }
      // })
      // variables.manExpenseVOList = this.tableDataResult
      // variables.plaExpenseVOList = this.redeemData.plaExpenseVOList
      // if (pass && JSON.stringify(this.inlayVariables) !== '{}') {
      //   Object.assign(variables, this.inlayVariables)
      // }

      // TODO：等真实的手填费用出现在校验是否正确
      // for (const item of this.tableData4) {
      //   if (item.enableInput && !item.amount) {
      //     this.$message.warning('请完成手填费用的输入')
      //     return
      //   }
      // }
      // 优化：使用 some 方法提前终止循环，并暴露未填值的数据信息
      let unFilledInfo = null;
      const hasUnFilled = this.tableData4.some(item => {
        return item.expenseOrderDetailList.some(citem => {
          if (citem.enableInput && (isNaN(Number(citem.amount)) || Number(citem.amount) <= 0)) {
            // 记录未正确输入的信息
            unFilledInfo = {
              parentExpenseName: item.parenExpenseName,
              expenseTypeName: citem.expenseTypeStr || '未知费用类型'
            };
            return true;
          }
          return false;
        });
      });

      if (hasUnFilled && unFilledInfo) {
        this.$message.warning(`还款单【${unFilledInfo.parentExpenseName}】下的【${unFilledInfo.expenseTypeName}】未正确输入，请检查并完成输入`);
        return;
      }

      const variables = {
        costCalculusVO: this.costCalculusVOCunChu
      }
      this.handleCompleteTask(pass, variables)
        .then(() => {
          this.$message.success('处理成功')
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 终止
    handleTerminateProcessMyFUn() {
      const variables = {
        costCalculusVO: this.costCalculusVOCunChu
      }
      this.handleTerminateProcess(variables)
    }
  },

  created() {
    this.arrOption3.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '账户类型',
        prop: 'merchantType',
        width: 90,
      },
      {
        label: '商户号',
        prop: 'merchantNo',
        width: 100,
      },
      {
        label: '开户名',
        prop: 'accountName',
      },
      {
        label: '银行账户',
        prop: 'accountNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'signName',
      },
    ]
    this.arrOption2.column = [
      {
        label: '',
        prop: 'radio',
        width: 60,
        hide: false,
      },
      {
        label: '账户类型',
        prop: 'type',
        width: 90,
        dicUrl:
          '/api/blade-system/dict-biz/dictionary?code=account_type_status',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
      },
      {
        label: '开户银行',
        prop: 'bankDeposit',
      },
      {
        label: '开户名',
        prop: 'openHouseName',
      },
      {
        label: '银行账户',
        prop: 'bankCardNo',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入银行账户',
      },
      {
        label: '账户名称',
        prop: 'enterpriseName',
        search: true,
        searchSpan: 12,
        searchLabelWidth: 100,
        searchClearable: false,
        placeholder: '请输入账户名称',
      },
    ]
  },
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: right;
      font-family: SourceHanSansSC-regular;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

// 自定义主体样式 - 开始
.foreclosure-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .divider {
      display: inline-block;
      margin: 0 12px;
      width: 1px;
      height: 16px;
      background: rgba(215, 215, 215, 100);
    }

    .number-container {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;

      .number-type-value {
        color: rgba(105, 124, 255, 100);
      }
    }
  }

  .goods-container {
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin-top: 16px;
    border-top: 1px solid rgba(233, 235, 239, 100);

    .goods-wrapper {
      display: flex;
      align-items: center;
      max-width: 40%;

      .goods-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 6px 0 6px 12px;
        width: 100%;
        height: 72px;

        .goods-name {
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .goods-type-container {
          margin-top: 12px;

          .goods-type {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
          }

          .goods-type-value {
            font-size: 14px;
            font-family: SFProText-Medium, SFProText;
            font-weight: 500;
            color: #53627c;
            line-height: 20px;
          }
        }
      }
    }

    .goods-desc-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 20px;

      .goods-desc-title {
        color: rgba(112, 112, 112, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
        line-height: 20px;
      }

      .goods-desc-value {
        margin-top: 8px;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-bold;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }

  .delivery-address-container {
    margin-top: 12px;

    .title {
      color: rgba(112, 112, 112, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-bold;
      font-weight: 600;
    }

    .form-item {
      margin-top: 14px;

      .label {
        display: inline-block;
        width: 96px;
        color: rgba(112, 112, 112, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }

      .value {
        color: rgba(36, 36, 36, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }
    }
  }
}

.repayment-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}
// 自定义主体样式 - 结束
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}
.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  // justify-content: space-between;
  justify-content: right;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: #242424;
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      color: #ff2929;
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zifangfeiyongzhanshi-d {
  display: flex;
  font-size: 16px;
  margin-top: 10px;

  & > * {
    margin-right: 30px;
    color: #808080;

    span {
      color: #000;
    }
  }
}

::v-deep .account {
  display: flex;
  width: 400px;
  align-items: center;
  .accountText {
    margin-right: 20px;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: #fff;
    cursor: pointer;
  }
}
</style>
