<template>
  <div class="container">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '贸易背景-应收账款确权'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <div
      v-if="!waiting && activeName == 'first'"
      class="related-data-container"
    >
      <basic-container v-if="customForm.goods.readable">
        <avue-title value="关联产品" />
        <div class="product-container">
          <div class="product-name-container">
            <div class="product-name">
              <span
                class="img-wrapper"
                :style="{
                  backgroundImage: 'url(' + (productData.capitalLogo + ')'),
                }"
              >
              </span>
              <div class="name-wrapper">
                <router-link :to="{ path: '/' }" class="name">{{
                  productData.goodsName || '--'
                }}</router-link>
                <span class="provider">{{
                  productData.capitalName || '--'
                }}</span>
              </div>
            </div>
            <Tag
              :name="productTypeMap[productData.type].name || '--'"
              backgroundColor="#697CFF"
              color="#fff"
              radius
            />
          </div>
        </div>
      </basic-container>
      <basic-container
        v-if="showConfirmQuota && customForm.enterpriseQuota.readable"
      >
        <div class="quota-container">
          <span class="title">确权金额</span>
          <span class="value">
            {{ formatMoney(variables.confirmAmount) + '元' }}
          </span>
        </div>
      </basic-container>
      <basic-container v-else-if="customForm.confirmQuota.readable">
        <avue-title value="核心企业额度信息" />
        <el-descriptions class="margin-top" :column="2" :size="size">
          <el-descriptions-item label="授信总额">{{
            formatMoney(enterpriseQuota.creditAmount) + '元'
          }}</el-descriptions-item>
          <el-descriptions-item label="当前可用额度">{{
            formatMoney(enterpriseQuota.availableAmount) + '元'
          }}</el-descriptions-item>
          <el-descriptions-item label="有效期限">{{
            enterpriseQuota.expireTime !== undefined &&
            enterpriseQuota.expireTime !== null
              ? enterpriseQuota.expireTime.split(' ')[0]
              : '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="额度编号">
            <span>{{
              enterpriseQuota.quotaNo !== undefined &&
              enterpriseQuota.quotaNo !== null
                ? enterpriseQuota.quotaNo
                : '--'
            }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </basic-container>
    </div>

    <template v-if="!waiting && activeName == 'first'">
      <basic-container>
        <avue-title value="应收账款信息"></avue-title>
        <ReceivableItem
          v-for="item of receivableDataArr"
          :key="item.id"
          :data="item"
          :proofTypeMap="proofTypeMap"
          @handlePreview="handleView"
          @handleBtnClick="handleBtnClick"
          :customForm="customForm"
          :disableBtn="true"
        />
      </basic-container>
    </template>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import WfButton from '../../components/button.vue'
import WfFlow from '../../components/flow.vue'
import customExForm from '../../../mixins/custom-ex-form'
import Tag from '@/views/customer/archives/components/Tag/index.vue'
import ReceivableItem from './components/Item/index.vue'
import { getDictionary } from '@/api/system/dictbiz'
import FilePreview from '@/components/file-preview/index.vue'
import { productTypeMap } from './config'
import { getEnterpriseQuota } from '@/api/plugin/workflow/custom/receivableConfirm'
import { formatMoney } from '@/util/filter'

export default {
  mixins: [customExForm],
  components: { WfButton, WfFlow, Tag, ReceivableItem, FilePreview },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) {
            this.getDetail(taskId, processInsId)
          }
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      variables: {},
      resData: {},
      // 字典
      productTypeMap,
      // 页面数据
      productData: {},
      receivableDataArr: [],
      proofTypeMap: {},
      pdfSrc: '',
      showConfirmQuota: false,
      enterpriseQuota: {},
      customForm: {},
      // 函数
      formatMoney,
    }
  },
  created() {
    getDictionary({ code: 'jrzh_customer_front_sales_contract_proof_type' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          const proofTypeMap = {}
          for (const item of data) {
            proofTypeMap[item.dictKey] = item.dictValue
          }
          this.proofTypeMap = proofTypeMap
        }
      })
      .catch(() => {})
  },
  methods: {
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false
        if (res) {
          const { variables = {}, status } = res.process
          const { taskForm = [] } = res.form || {}
          const customForm = {}
          for (const item of taskForm) {
            customForm[item.id] = item
          }
          const receivableDataArr =
            variables.salesFormData && variables.salesFormData.salesContractList
              ? variables.salesFormData.salesContractList
              : []
          if (['finished', 'terminated'].includes(status)) {
            this.showConfirmQuota = true
          } else {
            getEnterpriseQuota({
              id: variables.goodsId,
              backId: variables.backId,
            })
              .then(({ data }) => {
                if (data.success) {
                  data = data.data
                  this.enterpriseQuota = data
                }
              })
              .catch(() => {})
          }
          this.customForm = customForm
          this.resData = res.process
          this.variables = variables
          this.productData = variables.goods
          this.receivableDataArr = receivableDataArr
        }
      })
    },
    // 通过
    handleExamine(pass) {
      this.submitLoading = true
      let confirmAmount = 0
      for (const item of this.receivableDataArr) {
        if (item.confirmStatus === 2)
          confirmAmount = this.$numJiaFun(confirmAmount, item.effectiveAmount)
      }
      this.variables.salesFormData.salesContractList = this.receivableDataArr
      this.variables.confirmAmount = String(confirmAmount)
      this.handleCompleteTask(pass, this.variables)
        .then(() => {
          this.$message.success('处理成功')
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 附件预览
    handleView(url) {
      const targetUrl = url
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    handleBtnClick(id, value) {
      for (const item of this.receivableDataArr) {
        if (item.id === id) {
          item.confirmStatus = value
          return
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  margin-bottom: 35px;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: right;
      font-family: SourceHanSansSC-regular;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.related-data-container {
  display: flex;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: space-between;

  .basic-container {
    padding-top: 10px;
    width: calc(100% / 2 - 6px);

    ::v-deep {
      .el-descriptions {
        margin-top: 8px;
      }

      .basic-container__card {
        height: 100%;
      }

      .el-descriptions__table {
        tbody:last-child {
          td {
            padding-bottom: 0;
          }
        }
      }

      .el-card {
        display: flex;

        .el-card__body {
          display: flex;
          flex-direction: column;
          flex: 1;

          .product-container {
            display: flex;
            align-content: center;
            flex: 1;

            .product-name-container {
              margin: 0;
              width: 100%;
            }
          }
        }
      }

      .quota-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        flex: 1;

        .title {
          color: rgba(100, 100, 100, 100);
          font-size: 16px;
          text-align: left;
          font-family: SourceHanSansSC-regular;
          font-weight: 500;
        }

        .value {
          color: rgba(0, 0, 0, 1);
          font-size: 28px;
          text-align: center;
          font-family: SourceHanSansSC-bold;
          font-weight: 500;
        }
      }
    }
  }

  .product-container {
    .product-name-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .product-name {
        display: flex;
        align-items: center;

        .img-wrapper {
          display: inline-block;
          width: 48px;
          height: 48px;
          box-shadow: 0px 0px 0px 1px #efefef;
          margin-right: 8px;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          border-radius: 50%;
        }

        .name-wrapper {
          display: flex;
          flex-direction: column;

          .name {
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            color: rgba(105, 124, 255, 100);

            &:hover {
              color: #1277ff;
              text-decoration: underline;
            }
          }

          .provider {
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
            cursor: default;
          }
        }
      }
    }
  }
}

// .info-container {
// }

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}
</style>
