<template>
  <div class="receivable-item-container">
    <el-descriptions :column="3" :size="size" border>
      <el-descriptions-item
        v-if="customForm.proofType.readable"
        label="凭证类型"
      >
        {{
          data.proofType !== null && data.proofType !== underline
            ? proofTypeMap[data.proofType]
            : '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item v-if="customForm.proofNo.readable" label="凭证编码">
        {{ data.proofNo || '--' }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.orderAmount.readable"
        label="订单金额"
      >
        {{ data.orderAmount | formatMoney }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.accountAmount.readable"
        label="账面金额"
      >
        {{ data.accountAmount | formatMoney }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.effectiveAmount.readable"
        label="有效金额"
      >
        {{ data.effectiveAmount | formatMoney }}
      </el-descriptions-item>
      <el-descriptions-item v-if="customForm.paymentDays.readable" label="账期">
        {{
          data.paymentDays !== null && data.paymentDays !== undefined
            ? data.paymentDays + '天'
            : '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.startTime.readable"
        label="开始日期"
      >
        {{ data.startTime || '--' }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.expireTime.readable"
        label="到期日期"
      >
        {{ data.expireTime || '--' }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.gracePeriod.readable"
        label="宽限期"
      >
        {{ data.gracePeriod || '--' }}
      </el-descriptions-item>
    </el-descriptions>
    <div
      class="file-container"
      v-if="data.attach && customForm.attach.readable"
    >
      <span class="title">凭证：</span>
      <div class="file-list">
        <div class="file-item" @click="handlePreview(data.attach.link)">
          <span class="file-name">{{ data.attach.originalName }}</span>
          <icon
            class="el-icon-circle-check"
            style="color: #3dc861; font-size: 16px"
          />
        </div>
      </div>
    </div>
    <el-descriptions
      v-if="
        data.customerBusinessInvoice !== null &&
        data.customerBusinessInvoice !== undefined
      "
      style="margin-top: 20px"
      :column="3"
      :size="size"
      border
    >
      <el-descriptions-item
        v-if="customForm.invoice_code.readable"
        label="发票代码"
      >
        {{ data.customerBusinessInvoice.code || '--' }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.invoice_number.readable"
        label="发票号码"
      >
        {{ data.customerBusinessInvoice.number || '--' }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.invoice_total.readable"
        label="发票金额（含税）"
      >
        {{
          data.customerBusinessInvoice.total !== null &&
          data.customerBusinessInvoice.total !== undefined
            ? formatMoney(
                data.customerBusinessInvoice.total.replace('￥', '')
              ) + '元'
            : '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.invoice_subtotalAmount.readable"
        label="发票金额（不含税）"
      >
        {{
          data.customerBusinessInvoice.subtotalAmount !== null &&
          data.customerBusinessInvoice.subtotalAmount !== undefined
            ? formatMoney(
                data.customerBusinessInvoice.subtotalAmount.replace('￥', '')
              ) + '元'
            : '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.invoice_issueDate.readable"
        label="开票日期"
      >
        {{
          data.customerBusinessInvoice.issueDate !== null &&
          data.customerBusinessInvoice.issueDate !== undefined
            ? data.customerBusinessInvoice.issueDate
            : '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="customForm.invoice_checkCode.readable"
        label="校验码"
      >
        {{
          data.customerBusinessInvoice.checkCode !== null &&
          data.customerBusinessInvoice.checkCode !== undefined
            ? data.customerBusinessInvoice.checkCode
            : '--'
        }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="button-container" :class="{ disable: disableBtn }">
      <div
        class="confirm-btn-container"
        :class="{ checked: data.confirmStatus === 2 }"
        @click="handleBtnClick(2)"
      >
        <span class="option">
          <span class="check-option" />
        </span>
        <span class="name">我确认有该应还账款</span>
      </div>
      <div
        class="confirm-btn-container"
        :class="{ checked: data.confirmStatus === 0 }"
        @click="handleBtnClick(0)"
      >
        <span class="option">
          <span class="check-option" />
        </span>
        <span class="name">我不承认有该应还账款</span>
      </div>
    </div>
    <div
      v-if="
        confirmStatusCheck &&
        data.confirmStatus !== 0 &&
        data.confirmStatus !== 2
      "
      style="margin-top: 4px; color: #f56c6c; font-size: 14px; line-height: 1"
    >
      请选择是否确认该确权表单信息
    </div>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter'

export default {
  name: 'WorkFlowReceivableConfirmItem',
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    proofTypeMap: {
      type: Object,
      default: () => {},
    },
    customForm: {
      type: Object,
      default: () => {},
    },
    disableBtn: {
      type: Boolean,
      default: false,
    },
    confirmStatusCheck: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return { formatMoney }
  },
  methods: {
    handlePreview(targetUrl) {
      this.$emit('handlePreview', targetUrl)
    },
    handleBtnClick(value) {
      if (this.disableBtn) return
      this.$emit('handleBtnClick', this.data.id, value)
    },
  },
}
</script>

<style lang="scss" scoped>
.receivable-item-container {
  margin-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed rgba(72, 72, 72, 100);

  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }
}

.file-container {
  margin-top: 20px;

  .title {
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
  }

  .file-list {
    margin-top: 8px;
  }

  .file-item {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    min-width: 354px;
    padding: 8px;
    background-color: rgba(246, 246, 246, 100);
    border-radius: 4px;
    cursor: pointer;

    .file-name {
      margin-right: 10px;
      color: rgba(72, 72, 72, 100);
      font-size: 14px;
      text-align: left;
      font-family: Roboto;
    }
  }
}

.button-container {
  display: flex;
  align-items: center;
  text-align: left;
  margin-top: 20px;

  &.disable {
    .confirm-btn-container {
      opacity: 0.5;
      cursor: default;
      background: #f7f7f7;
      color: #dce0e7;
    }
  }

  .confirm-btn-container {
    display: inline-flex;
    align-items: center;
    padding: 10px 16px;
    margin-right: 12px;
    border-radius: 3px;
    border: 1px solid rgba(220, 224, 231, 100);
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }

    &.checked {
      border: 1px solid #697cff !important;

      .option {
        border-color: #697cff !important;

        .check-option {
          background: #697cff !important;
        }
      }

      .name {
        color: #697cff !important;
      }
    }

    .option {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 12px;
      height: 12px;
      background-color: #fff;
      border: 2px solid rgba(220, 224, 231, 100);
      border-radius: 50%;

      .check-option {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
    }

    .name {
      margin-left: 10px;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}
</style>
