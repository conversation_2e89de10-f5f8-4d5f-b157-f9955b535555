<template>
  <div class="form-content">
    <div class="form-container">
      <avue-form ref="form1" :option="payOption" v-model="form"></avue-form>
    </div>
    <div class="payInfo">
      <div class="pay-box">
        <!-- <template>
          <div class="form-item" v-if="type == 1">
            <span class="label">应还本金(元):</span>
            <span style="color: red">￥{{ repayFunds }}</span>
          </div>
          <div class="form-item" v-for="item of purchaseBondArr" :key="item.id">
            <span class="label">{{ item.expenseType }}(元):</span>
            <span>￥{{ item.money | formatMoney }}</span>
          </div>
        </template>
        <div class="form-item">
          <span class="label">应还总额(元):</span>
          <span style="color: red">￥{{ totalMoney(purchaseBondArr) }}</span>
        </div> -->
        <div class="payImg">
          <div class="table-bottom">
            <el-button @click="handleViewBalanceProof()"
              >查看支付凭证</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <FilePreview :url="pdfSrc" />
  </div>
</template>
<script>
import FilePreview from '@/components/file-preview'
export default {
  components: {
    FilePreview,
  },
  created() {
    if (this.type == 1 && !this.see) {
      this.form.status = 2
    } else if (this.type == 2 && !this.see) {
      this.form.status = 1
    }
    this.payOption.column = [
      {
        label: '付款状态',
        prop: 'status',
        type: 'select',
        clearable: false,
        disabled: this.see,
        span: 24,
        placeholder: '请选择付款状态',
        dicUrl: `/api/blade-system/dict-biz/dictionary?code=${
          this.type == 1 ? 'loan_repayment_status' : 'bill_pay_status'
        }`,
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择付款状态',
            trigger: ['change'],
          },
        ],
        control: value => {
          if (
            (value == 3 && this.type == 1) ||
            (value == 2 && this.type == 2)
          ) {
            return {
              amount: {
                display: true,
              },
              bank: {
                display: true,
              },
              payAccount: {
                display: true,
              },
              remark: {
                display: false,
              },
              endDate: {
                display: true,
              },
            }
          } else if (
            (value == 4 && this.type == 1) ||
            (value == 3 && this.type == 2 && !this.see)
          ) {
            return {
              amount: {
                display: false,
              },
              bank: {
                display: false,
              },
              payAccount: {
                display: false,
              },
              remark: {
                display: true,
              },
              endDate: {
                display: false,
              },
            }
          } else if (
            (!(value == 4 || value == 3) && this.type == 1) ||
            (!(value == 3 || value == 2) && this.type == 2) ||
            (this.see && this.type == 2)
          ) {
            return {
              amount: {
                display: false,
              },
              bank: {
                display: false,
              },
              payAccount: {
                display: false,
              },
              remark: {
                display: false,
              },
              endDate: {
                display: false,
              },
            }
          }
        },
      },
      {
        disabled: this.see,
        label: '付款金额',
        prop: 'amount',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            validator: this.validategoodsNum,
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        disabled: this.see,
        label: '付款开户行',
        prop: 'bank',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入付款开户行',
            trigger: ['blur'],
          },
        ],
      },
      {
        disabled: this.see,
        label: '付款账号',
        prop: 'payAccount',
        span: 24,
        display: false,
        rules: [
          {
            required: true,
            message: '请输入付款账号',
            trigger: ['blur'],
          },
        ],
      },
      {
        disabled: this.see,
        label: '还款时间',
        prop: 'endDate',
        type: 'datetime',
        display: false,
        span: 24,
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now()
          },
        },
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        rules: [
          {
            required: true,
            message: '请选择还款时间',
            trigger: ['change'],
          },
        ],
      },
      {
        disabled: this.see,
        label: '失败原因',
        prop: 'remark',
        span: 24,
        display: false,
        type: 'textarea',
        rules: [
          {
            required: true,
            message: '请输入失败原因',
            trigger: ['blur'],
          },
        ],
      },
    ]
  },
  data() {
    return {
      payOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [],
      },
      // 平台费用
      form: {},
      pdfSrc: '',
    }
  },
  props: {
    purchaseBondArr: {
      type: Array,
      default: () => {
        return []
      },
    },
    repayFunds: {
      type: String,
      default: '0',
    },
    bankInfo: {
      type: Object,
    },
    platformFee: {
      type: Object,
    },
    platformImg: {
      type: Array,
      default: () => {
        return []
      },
    },
    platformPdf: {
      type: Array,
      default: () => {
        return []
      },
    },
    type: {
      type: String,
    },
    pageData: {
      type: Object,
    },
    platformFeeObj: {
      type: Object,
    },
    see: {
      type: Boolean,
    },
    amount: {
      type: String,
      default: '0.00',
    },
  },
  watch: {
    bankInfo: {
      handler(val) {
        if (this.form.payAccount) {
          return
        }
        this.form.payAccount = val.account
        this.form.bank = val.bank
      },
      deep: true,
    },
    purchaseBondArr: {
      handler(val) {
        this.form.amount = this.totalMoney(val)
      },
      immediate: true,
      deep: true,
    },
    // pageData: {
    //   handler(val) {
    //     if (this.type == 1) {
    //       this.form.status = val.cashDepositPaymentStatus
    //     } else {
    //       this.form.status = val.paymentStatus
    //     }
    //   },
    //   immediate: true,
    //   deep: true,
    // },
    platformFeeObj: {
      handler(val) {
        if (val) {
          this.form.status = val.status
          this.form.bank = val.bank
          this.form.payAccount = val.payAccount || val.bankCardNo
          this.form.endDate = val.endDate || val.repaymentTime
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 预览图片
    handleViewBalanceProof() {
      const imgSrcArr = []
      const pdfSrcArr = []
      for (const item of this.platformImg) {
        if (item.extension != 'pdf') {
          imgSrcArr.push({ name: item.name, url: item.link })
        } else {
          pdfSrcArr.push({ name: item.name, url: item.link })
        }
      }
      if (pdfSrcArr.length == 0) {
        this.handlePreviewImage(imgSrcArr)
      } else {
        this.handlePreviewImage(pdfSrcArr, 'pdf')
      }
    },
    handlePreviewImage(imgSrcArr = [], type = 'img') {
      if (type == 'img') {
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      } else {
        this.pdfSrc = imgSrcArr[0].url + '?time=' + new Date().getMilliseconds()
      }
    },
    validategoodsNum(rule, value, callback) {
      if (value === '') {
        callback(new Error('请输入实还金额'))
      } else if (value < 0) {
        callback(new Error('实还金额要大于0'))
      } else if (value != this.totalMoney(this.purchaseBondArr)) {
        callback(
          new Error(`实还金额应等${this.totalMoney(this.purchaseBondArr)}元`)
        )
      } else {
        callback()
      }
    },
    totalMoney(Arr) {
      let repayTotal = 0
      if (this.type == 1) {
        return this.amount
      }
      if (!Arr || Arr.length == 0) {
        return 0
      }
      for (const item of Arr) {
        repayTotal = this.$numJiaFun(repayTotal, item.money)
      }
      return this.$numJiaFun(repayTotal, this.repayFunds)
    },
    check() {
      return this.$refs.form1
    },
  },
}
</script>
<style lang="scss" scoped>
.form-content {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  .form-container {
    width: 50%;
  }
}
.pay-box {
  width: 100%;
  border-radius: 6px;
  box-sizing: border-box;
  line-height: 20px;
  & span:first-child {
    display: block;
    color: rgba(153, 153, 153, 100);
    font-size: 14px;
    font-family: SourceHanSansSC-regular;
    font-weight: 500;
    margin-right: 8px;
  }
  & span:last-child {
    color: #00072a;
    font-weight: 600;
    font-size: 14px;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 150px;
      // text-align: right;
    }
  }
}
</style>