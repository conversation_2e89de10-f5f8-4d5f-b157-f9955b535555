<template>
  <div class="container" style="margin-bottom: 48px">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title
              :value="process.processDefinitionName || '赎货申请审批'"
            ></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i
                    class="el-icon-time"
                    style="color: #4e9bfc; font-size: 40px"
                  />
                  <span class="status">待审批</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'finished'"
                >
                  <i
                    class="el-icon-circle-check"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已完成</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'done'"
                >
                  <i
                    class="el-icon-time"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">审批中</span>
                </div>
                <div
                  class="right-icon-box"
                  v-else-if="resData.status == 'terminate'"
                >
                  <i
                    class="icon-line-tixing"
                    style="color: #3dc861; font-size: 40px"
                  />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span
                      class="target"
                      style="color: rgba(105, 124, 255, 100)"
                    >
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design
                ref="bpmn"
                style="height: 500px; margin-top: 5px"
                :options="bpmnOption"
              ></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 自定义主体 - 开始 -->
      <!-- 赎货单 -->
      <basic-container
        v-if="customForm.redemptionNote && customForm.redemptionNote.readable"
      >
        <div class="foreclosure-note-container" v-loading="pageDataLoading">
          <!-- 加载动画 -->
          <template v-if="pageDataLoading">
            <div style="height: 200px" />
          </template>
          <!-- 内容主体 -->
          <template v-else>
            <div class="title-container">
              <span class="title">赎货单</span>
              <template
                v-if="
                  customForm.redemptionNote_redeemNo &&
                  customForm.redemptionNote_redeemNo.readable
                "
              >
                <span class="divider" />
                <div class="number-container">
                  <span class="number-type">赎货单号：</span>
                  <span class="number-value">{{ variables.redeemNo }}</span>
                </div>
              </template>
            </div>
            <div class="goods-container">
              <template
                v-if="
                  customForm.redemptionNote_redeemGoods &&
                  customForm.redemptionNote_redeemGoods.readable
                "
              >
                <div class="goods-wrapper">
                  <el-image
                    style="
                      width: 72px;
                      height: 72px;
                      flex-shrink: 0;
                      border-radius: 4px;
                    "
                    :src="redeemData.goodLogo"
                    fit="contain"
                  />
                  <div class="goods-content">
                    <span class="goods-name">{{ redeemData.goodsName }}</span>
                    <div class="goods-type-container">
                      <span class="goods-type">规格型号：</span>
                      <span class="goods-type-value">{{
                        redeemData.goodsSpec
                      }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_purchasePrice &&
                  customForm.redemptionNote_purchasePrice.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">采购单价(元)</span>
                  <span class="goods-desc-value">{{
                    redeemData.purchasePrice | formatMoney
                  }}</span>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_financingPrice &&
                  customForm.redemptionNote_financingPrice.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">融资单价(元)</span>
                  <span class="goods-desc-value">{{
                    redeemData.financingPrice | formatMoney
                  }}</span>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_redeemNum &&
                  customForm.redemptionNote_redeemNum.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">赎货数量</span>
                  <span class="goods-desc-value">{{ redeemData.num }}</span>
                </div>
              </template>
              <template
                v-if="
                  customForm.redemptionNote_goodsUnitValue &&
                  customForm.redemptionNote_goodsUnitValue.readable
                "
              >
                <div class="goods-desc-container">
                  <span class="goods-desc-title">单位</span>
                  <span class="goods-desc-value">{{
                    redeemData.goodsUnitValue
                  }}</span>
                </div>
              </template>
            </div>
            <div class="descriptions-container">
              <el-descriptions labelStyle="width: 136px" :column="3" border>
                <template v-if="true">
                  <el-descriptions-item label="库存编号">{{
                    redeemData.warehouseNo || '--'
                  }}</el-descriptions-item>
                </template>
                <el-descriptions-item label="融资编号">{{
                  redeemData.financeNo || '--'
                }}</el-descriptions-item>
                <el-descriptions-item label="供应商">{{
                  redeemData.supplierName || '--'
                }}</el-descriptions-item>
                <el-descriptions-item label="仓储公司">{{
                  pageData.storageCompany || '--'
                }}</el-descriptions-item>
                <el-descriptions-item label="仓库">{{
                  pageData.warehouseName || '--'
                }}</el-descriptions-item>
                <el-descriptions-item label="入库日期">{{
                  redeemData.warehouseInDate || '--'
                }}</el-descriptions-item>
                <el-descriptions-item label="库龄(天)">{{
                  redeemData.warehouseAge === null
                    ? '--'
                    : redeemData.warehouseAge
                }}</el-descriptions-item>
                <el-descriptions-item label="约定赎货日">{{
                  redeemData.redemptionDate || '--'
                }}</el-descriptions-item>
                <el-descriptions-item label="提货方式">{{
                  redeemData.extractType || '--'
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
            <!-- 收货地址 -->
            <div
              v-if="redeemData.extractType === '第三方物流'"
              class="delivery-address-container"
            >
              <span class="title">收货地址</span>
              <div class="form-item">
                <span class="label">收货对象</span>
                <span class="value">{{
                  redeemData.financingAddress.enterpriseName
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">收货地址</span>
                <span class="value">{{
                  redeemData.financingAddress.addressTarget ||
                  redeemData.financingAddress.urbanAreas
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">联系人</span>
                <span class="value">{{
                  redeemData.financingAddress.contacts
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">联系方式</span>
                <span class="value">{{
                  redeemData.financingAddress.addressPhone
                }}</span>
              </div>
            </div>
            <!-- 自提信息 -->
            <div v-else class="delivery-address-container">
              <span class="title">自提信息</span>
              <div class="form-item">
                <span class="label">赎货人</span>
                <span class="value">{{
                  `${redeemData.redeemUser.username}｜${redeemData.redeemUser.idCard}｜${redeemData.redeemUser.phone}`
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">车牌号</span>
                <span class="value">{{
                  redeemData.redeemUser.licensePlate
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">提货日期</span>
                <span class="value">{{
                  redeemData.redeemUser.arriveTime
                }}</span>
              </div>
            </div>
          </template>
        </div>
      </basic-container>
      <!-- 还款单 -->
      <basic-container
        v-if="
          customForm.repaymentNote_bank &&
          customForm.repaymentNote_bank.readable
        "
      >
        <div class="repayment-note-container" v-loading="pageDataLoading">
          <!-- 加载动画 -->
          <template v-if="pageDataLoading">
            <div style="height: 200px" />
          </template>
          <!-- 内容主体 -->
          <template v-else>
            <Feiyongwaihezi
              ref="feiyongwaiheziRef"
              :feiyongList="expenseInfoExpenseList"
              :isLook="true"
            />
            <!--<div class="title-container">
              <span class="title">还款单</span>
            </div>
            <div class="form-container">
              <!~~ 银行还款单 ~~>
              <template
                v-if="
                  customForm.repaymentNote_bank &&
                  customForm.repaymentNote_bank.readable
                "
              >
                <div class="table-top refund">
                  <div class="table-title-box">
                    <div class="title-left-box">
                      <span>银行还款单</span>
                      <template
                        v-if="
                          customForm.repaymentNote_bank_yearRate &&
                          customForm.repaymentNote_bank_yearRate.readable
                        "
                      >
                        <span />
                        <span>年化利率{{ pageData.yearRate || '--' }}%</span>
                      </template>
                    </div>
                    <template
                      v-if="
                        customForm.repaymentNote_bank_repayFunds &&
                        customForm.repaymentNote_bank_repayFunds.readable
                      "
                    >
                      <div class="title-right-box">
                        应还本金(元)
                        <span style="color: black">{{
                          repayFunds | formatMoney
                        }}</span>
                      </div>
                    </template>
                  </div>
                  <el-table
                    ref="table2"
                    :data="tableData2"
                    style="width: 100%; margin-top: 13px"
                    class="table-border-style"
                    :summary-method="getSummaries"
                    :show-summary="
                      customForm.repaymentNote_bank_table_money &&
                      customForm.repaymentNote_bank_table_money.readable
                    "
                  >
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_name &&
                        customForm.repaymentNote_bank_table_name.readable
                      "
                      prop="name"
                      label="费用名称"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_expenseType &&
                        customForm.repaymentNote_bank_table_expenseType.readable
                      "
                      prop="expenseType"
                      label="费用类型"
                    >
                      <template slot-scope="{ row }">
                        <Tag
                          :name="row.expenseType"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_node &&
                        customForm.repaymentNote_bank_table_node.readable
                      "
                      prop="node"
                      label="支付节点"
                    >
                      <template>
                        <Tag
                          name="确认赎货"
                          color="#00072A"
                          backgroundColor="#EAECF1"
                          borderColor="transparent"
                          :radius="true"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_feeFormula &&
                        customForm.repaymentNote_bank_table_feeFormula.readable
                      "
                      prop="feeFormula"
                      label="计费方式"
                    />
                    <el-table-column
                      v-if="
                        customForm.repaymentNote_bank_table_money &&
                        customForm.repaymentNote_bank_table_money.readable
                      "
                      prop="money"
                      label="应付金额(元)"
                    >
                      <template slot-scope="{ row }">
                        <span> ￥{{ row.money | formatMoney }} </span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <template
                    v-if="
                      customForm.repaymentNote_bank_status &&
                      customForm.repaymentNote_bank_status.readable
                    "
                  >
                    <div v-if="tableData2Result.payMode == 1">
                      <pay-form
                        ref="form"
                        type="1"
                        :platformFeeObj="bankRepaymentData"
                        :platformImg="redeemData.manAttachList"
                        :purchaseBondArr="redeemData.manExpenseCulationVoList"
                        :repayFunds="repayFunds"
                        :bankInfo="platformAccount"
                        :amount="bankRepaymentData.totalAmount"
                      ></pay-form>
                    </div>
                    <div class="pay-box" v-else>
                      <!~~ 订单状态 ~~>
                      <div class="order-box">
                        <span class="order-status">订单状态</span>
                        <span
                          class="order-text"
                          style="color: #09c067; background-color: #ddf0e7"
                        >
                          已付款
                        </span>

                        <div
                          v-if="
                            tableData2Result.loanManageRepayment
                              .onlinePayRepaymentCode
                          "
                          class="informition-box"
                        >
                          <span>
                            支付方式：
                            <span class="informition-box_bold">
                              {{
                                tableData2Result.loanManageRepayment.payMode ==
                                2
                                  ? '线上支付'
                                  : '线下支付'
                              }}
                            </span>
                          </span>
                          <span>
                            付款金额：
                            <span class="informition-box_bold">
                              {{
                                tableData2Result.loanManageRepayment
                                  .actualAmount
                              }}
                            </span>
                          </span>
                          <span>
                            线上还款编号：
                            <span class="informition-box_bold">
                              {{
                                tableData2Result.loanManageRepayment
                                  .onlinePayRepaymentCode
                              }}
                            </span>
                          </span>
                          <span>
                            支付时间：
                            <span class="informition-box_bold">
                              <!~~ 为了格式统一用了createTime ~~>
                              {{
                                tableData2Result.loanManageRepayment.createTime
                              }}
                              <!~~ {{
                                tableData2Result.loanManageRepayment
                                  .repaymentTime
                              }} ~~>
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <!~~ <div class="repayment-status-container">
                      <div class="left-box">
                        <span class="title">还款状态</span>
                        <Tag
                          class="tag"
                          :name="payStatusMap[pageData.repaymentStatus]"
                          color="#697CFF"
                          backgroundColor="#EBF1FF"
                          borderColor="transparent"
                          :radius="true"
                        />
                        <template
                          v-if="
                            customForm.repaymentNote_bank_status_button_apply &&
                            customForm.repaymentNote_bank_status_button_apply
                              .readable
                          "
                        >
                          <template v-if="pageData.repaymentStatus === 1">
                            <el-button
                              class="button"
                              type="text"
                              @click="handleApplyRepayment({ isEdit: true })"
                              >申请还款</el-button
                            >
                          </template>
                          <template v-else>
                            <el-button
                              class="button"
                              type="text"
                              @click="handleApplyRepayment({ isView: true })"
                              >查看</el-button
                            >
                            <el-button
                              v-if="isAllowEditBankRepaymentStatus"
                              class="button"
                              type="text"
                              style="margin-left: 12px"
                              @click="handleApplyRepayment({ isEdit: true })"
                              >编辑</el-button
                            >
                          </template>
                        </template>
                      </div>
                      <template
                        v-if="
                          customForm.repaymentNote_bank_status_button_view &&
                          customForm.repaymentNote_bank_status_button_view
                            .readable
                        "
                      >
                        <div class="right">
                          <el-button
                            size="small"
                            @click="handleViewBalanceProof"
                            >查看余额凭证</el-button
                          >
                        </div>
                      </template>
                    </div> ~~>
                  </template>
                </div>
              </template>

              <template
                v-if="
                  customForm.repaymentNote_platform &&
                  customForm.repaymentNote_platform.readable
                "
              >
                <!~~ 平台费用单 ~~>
                <div class="table-container">
                  <div
                    class="table-top refund"
                    v-for="(item, index) in tableData3"
                    :key="index"
                  >
                    <div class="table-title-box">
                      <div class="title-left-box">
                        <span>{{ item.expenseTypeStr }}</span>
                      </div>
                    </div>
                    <el-table
                      ref="table3"
                      :data="item.plaExpenseCulationVoList"
                      style="width: 100%; margin-top: 13px"
                      class="table-border-style"
                      :summary-method="getSummaries"
                      :show-summary="
                        customForm.repaymentNote_platform_table_money &&
                        customForm.repaymentNote_platform_table_money.readable
                      "
                    >
                      <el-table-column
                        v-if="
                          customForm.repaymentNote_bank_table_name &&
                          customForm.repaymentNote_bank_table_name.readable
                        "
                        prop="name"
                        label="费用名称"
                      >
                      </el-table-column>
                      <el-table-column
                        v-if="
                          customForm.repaymentNote_bank_table_expenseType &&
                          customForm.repaymentNote_bank_table_expenseType
                            .readable
                        "
                        prop="expenseType"
                        label="费用类型"
                      >
                        <template slot-scope="{ row }">
                          <Tag
                            :name="row.expenseType"
                            color="#00072A"
                            backgroundColor="#EAECF1"
                            borderColor="transparent"
                            :radius="true"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-if="
                          customForm.repaymentNote_bank_table_node &&
                          customForm.repaymentNote_bank_table_node.readable
                        "
                        prop="node"
                        label="支付节点"
                      >
                        <template>
                          <Tag
                            name="确认赎货"
                            color="#00072A"
                            backgroundColor="#EAECF1"
                            borderColor="transparent"
                            :radius="true"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-if="
                          customForm.repaymentNote_bank_table_feeFormula &&
                          customForm.repaymentNote_bank_table_feeFormula
                            .readable
                        "
                        prop="feeFormula"
                        label="计费方式"
                      />
                      <el-table-column
                        v-if="
                          customForm.repaymentNote_bank_table_money &&
                          customForm.repaymentNote_bank_table_money.readable
                        "
                        prop="money"
                        label="应付金额(元)"
                      >
                        <template slot-scope="{ row }">
                          <template v-if="row.enableInput">
                            <div style="width: 80%">
                              <el-input
                                placeholder="请输入金额"
                                v-model="row.money"
                                type="number"
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </div>
                          </template>
                          <template v-else>
                            <span>￥{{ row.money | formatMoney }}</span>
                          </template>
                        </template>
                      </el-table-column>
                    </el-table>
                    <template
                      v-if="
                        customForm.repaymentNote_platform_status &&
                        customForm.repaymentNote_platform_status.readable
                      "
                    >
                      <div v-if="item.payMode == 1">
                        <pay-form
                          type="2"
                          :ref="`form${index}`"
                          :platformImg="item.attachList"
                          :purchaseBondArr="item.plaExpenseCulationVoList"
                          :bankInfo="platformAccount"
                          :platformFeeObj="item.platformFeeObj"
                        ></pay-form>
                      </div>
                      <div class="pay-box" v-else>
                        <!~~ 订单状态 ~~>
                        <div class="order-box">
                          <span class="order-status">订单状态</span>
                          <span
                            class="order-text"
                            style="color: #09c067; background-color: #ddf0e7"
                          >
                            已付款
                          </span>

                          <div
                            v-if="item.billExpenseOrder"
                            class="informition-box"
                          >
                            <span>
                              支付方式：
                              <span class="informition-box_bold">
                                {{
                                  item.payMode == 2 ? '线上支付' : '线下支付'
                                }}
                              </span>
                            </span>
                            <span>
                              付款金额：
                              <span class="informition-box_bold">
                                {{ item.billExpenseOrder.amount }}
                              </span>
                            </span>
                            <span>
                              支付流水号：
                              <span class="informition-box_bold">
                                {{ item.billExpenseOrder.billPaySerialNo }}
                              </span>
                            </span>
                            <span>
                              支付时间：
                              <span class="informition-box_bold">
                                {{ item.billExpenseOrder.payTime }}
                              </span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </template>
            </div>-->
          </template>
        </div>
      </basic-container>
      <!-- <EditStatusDialog
        ref="editStatusDialogRef"
        @handleViewProof="handlePreviewImage"
        @handleConfirm="handleEditStatusDialogConfirm"
      /> -->
      <!-- 自定义主体 - 结束 -->

      <!-- 内置表单渲染 -->
      <template v-if="inlayFormShow">
        <basic-container>
          <div class="wf-theme-default">
            <avue-form
              v-if="
                inlayOption &&
                ((inlayOption.column && inlayOption.column.length > 0) ||
                  (inlayOption.group && inlayOption.group.length > 0))
              "
              v-model="inlayForm"
              ref="inlayFormRef"
              :defaults.sync="inlayDefaults"
              :option="inlayOption"
              :upload-preview="handleUploadPreview"
            >
            </avue-form>
          </div>
        </basic-container>
      </template>

      <FilePreview :url="pdfSrc" />
    </template>
  </div>
</template>

<script>
import WfButton from '../../components/button.vue'
import WfFlow from '../../components/flow.vue'
import customExForm from '../../../mixins/custom-ex-form'
import Tag from '@/views/customer/archives/components/Tag/index.vue'
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { formatMoney } from '@/util/filter'
import FilePreview from '@/components/file-preview'
import {
  customerTagdicDataMap,
  enterpriseTypeDicDataMap,
  coreEnterpriseTypeDicDataMap,
} from '@/views/customer/archives/config'
// import { getByGoodsIdAndUserId } from '@/api/goods/pcontrol/workflow/productConfirmation'
import { getRedeemDetail } from '@/api/plugin/workflow/custom/redeem'
import { getDictionary } from '@/api/system/dictbiz'
import EditStatusDialog from './components/EditStatusDialog/index.vue'
import insideOutsideForm from '@/views/plugin/workflow/mixins/inside-outside-form'
import Feiyongwaihezi from '@/components/feiyongwaihezi'
// import payForm from './payForm.vue'

export default {
  mixins: [customExForm, insideOutsideForm],
  components: {
    WfButton,
    WfFlow,
    Tag,
    Dialog,
    EditStatusDialog,
    FilePreview,
    Feiyongwaihezi,
    // payForm,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) {
            this.getDetail(taskId, processInsId)
          }
        }
      },
      immediate: true,
    },
  },
  computed: {
    // 应还本金
    repayFunds() {
      return (
        this.$numChengFun(this.redeemData.financingPrice, this.redeemData.num)
      )
    },
  },
  data() {
    return {
      pdfSrc: '',
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      variables: {},
      resData: {},
      // 自定义表单显影数据
      customForm: {},
      // 页面字典
      customerTagdicDataMap,
      enterpriseTypeDicDataMap,
      coreEnterpriseTypeDicDataMap,
      // ------------ 页面数据 -----------
      pageDataLoading: true,
      pageData: {},
      redeemSend: {},
      redeemData: { financingAddress: {}, redeemUser: {} },
      tableData2: [{ money: '123.213' }],
      tableData3: [{ money: '' }, { money: '' }],
      // 字典
      payStatusArr: [],
      payStatusMap: {},
      orderStatusArr: [],
      orderStatusMap: {},
      // 是否允许编辑银行还款单还款状态
      isAllowEditBankRepaymentStatus: true,
      // 保存修改后的银行还款单支付状态
      bankRepaymentData: {},
      // 保存修改后的平台费用单支付状态
      platformFeeData: {},
      // 核心账户保存
      platformAccount: {
        bank: '',
        account: '',
      },
      tableData2Result: {},
      expenseInfoExpenseList: [],
      expenseInfoExpenseListCopy: [],
    }
  },
  created() {
    getDictionary({ code: 'loan_repayment_status' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          // 处理字典数据
          const statusArr = []
          const statusMap = {}
          for (const item of data) {
            item.dictKey = Number(item.dictKey)
            statusArr.push({
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            })
            statusMap[item.dictKey] = item.dictValue
          }
          this.payStatusArr = statusArr
          this.payStatusMap = statusMap
        }
      })
      .catch(() => {})
    getDictionary({ code: 'bill_pay_status' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          // 处理字典数据
          const orderArr = []
          const orderMap = {}
          for (const item of data) {
            item.dictKey = Number(item.dictKey)
            orderArr.push({
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            })
            orderMap[item.dictKey] = item.dictValue
          }
          this.orderStatusArr = orderArr
          this.orderStatusMap = orderMap
        }
      })
      .catch(() => {})
  },
  methods: {
    getSummaries(param) {
      const { columns, data } = param
      const sums = []

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false
        if (res) {
          // 内置表单start
          const { process, form } = res
          this.inlayProcess = process
          const { variables } = process
          const { allForm, indepFormKey } = form
          if (allForm) {
            this.indepFormKey = indepFormKey
            const { option, vars } = this.handleResolveOption(
              eval('(' + allForm + ')'),
              form.taskForm,
              // status
              'finished'
            )
            option.menuBtn = false
            const myVariables = {}
            for (let key in variables) {
              // if (!variables[key]) delete variables[key]
              if (key.indexOf('@_@') !== -1) {
                const myKey = key.split('@_@')
                if (myKey.length > 1) myVariables[myKey[1]] = variables[key]
              }
            }
            const co = option.column
            const gr = option.group
            if ((co && co.length) || (gr && gr.length)) {
              this.inlayFormShow = true
            }
            this.inlayOption = option
            this.inlayVars = vars
            this.inlayForm = myVariables
          }
          // 内置表单end

          const expenseInfoExpenseList = variables.expenseInfoExpenseList
          if (expenseInfoExpenseList && expenseInfoExpenseList.length) {
            this.expenseInfoExpenseList = expenseInfoExpenseList
          }

          // const { variables = {} } = res.process
          const { taskForm = [] } = res.form || {}
          const customForm = {}
          for (const item of taskForm) {
            customForm[item.id] = item
          }
          // 自定义页面数据
          const requestObj = {
            redeemNo: variables.redeemNo,
          }
          getRedeemDetail(requestObj)
            .then(({ data }) => {
              if (data.success) {
                data = data.data

                // const params = {
                //   goodsId: data.loanManageRepayment.goodsId,
                //   userId: data.loanManageRepayment.userId,
                // }
                // 平台账号请求
                // getByGoodsIdAndUserId(params).then(({ data }) => {
                //   const { data: resData } = data
                //   if (data.success && resData) {
                //     this.platformAccount.bank = resData.bankDeposit
                //     this.platformAccount.account = resData.bankCardNo
                //   }
                // })

                const redeemData = data.redeemDetailCargoCurrencyVO
                const redeemSend = data.redeemSend
                redeemData.financingAddress = redeemData.financingAddress || {}
                redeemData.redeemUser = redeemData.redeemUser || {}
                redeemData.manAttachList = redeemData.manAttachList || []
                redeemData.plaAttachList = redeemData.plaAttachList || []

                // 银行还款单
                // const tableData2 =
                //   redeemData.manExpenseCulationVo.manExpenseCulationVoList || []
                // const tableData2Result = redeemData.manExpenseCulationVo
                // 平台费用单
                // const tableData3 = redeemData.plaExpenseVOList || []
                // const onLine = []
                // const offLine = []
                // for (const item of tableData3) {
                //   if (item.money === null) {
                //     item.enableInput = true
                //   }
                //   let {
                //     paymentStatus,
                //     bank,
                //     account,
                //     failReason,
                //     payTime,
                //     amount,
                //     billExpenseNo,
                //   } = item.billExpenseOrder
                //   item.platformFeeObj = {
                //     amount: amount || '0.00',
                //     status: paymentStatus || 1,
                //     bank: bank || '中国建设银行股份有限公司',
                //     payAccount: account || '***********',
                //     failReason: failReason || '',
                //     payTime: payTime || '',
                //     billExpenseNo: billExpenseNo,
                //   }
                //   if (item.payMode === 2) {
                //     onLine.push(item)
                //   } else {
                //     offLine.push(item)
                //   }
                // }
                // const tableData3ConcatArr = onLine.concat(offLine)
                this.bankRepaymentData = data.loanManageRepayment
                // 处理银行还款单已预先支付下的查看弹窗数据回显
                if (data.repaymentStatus === 3) {
                  this.isAllowEditBankRepaymentStatus = false
                  const loanManageRepayment = data.loanManageRepayment
                  loanManageRepayment.attachList =
                    loanManageRepayment.attachList || [{}]
                  this.bankRepaymentData = {
                    status: loanManageRepayment.status,
                    amount: loanManageRepayment.actualAmount,
                    bank: loanManageRepayment.bank,
                    payAccount: loanManageRepayment.bankCardNo,
                    endDate: loanManageRepayment.repaymentTime,
                    remark: '',
                    voucher: [
                      {
                        imgUrl: loanManageRepayment.attachList[0].link,
                        link: loanManageRepayment.attachList[0].link,
                        id: loanManageRepayment.attachList[0].id,
                      },
                    ],
                  }
                }

                this.pageDataLoading = false
                this.pageData = data
                this.redeemSend = redeemSend
                this.redeemData = redeemData
                // this.tableData2Result = tableData2Result
                // this.tableData2 = tableData2
                // this.tableData3 = tableData3ConcatArr
              }
            })
            .catch(() => {})

          this.resData = res.process
          this.variables = variables
          this.customForm = customForm
        }
      })
    },
    // 申请还款
    handleApplyRepayment(modeObj = {}) {
      this.$refs.editStatusDialogRef.handleOpen({
        isPlatformFee: false,
        statusMap: this.payStatusMap,
        statusArr: this.payStatusArr,
        status: this.pageData.repaymentStatus,
        repaymentProofArr: [],
        // 应还本金
        repayFunds: this.repayFunds,
        // 资方费用信息
        manExpenseCulationVoList: this.redeemData.manExpenseCulationVoList,
        // 平台费用信息
        plaExpenseCulationVoList: this.redeemData.plaExpenseCulationVoList,
        // 核心账户回显数据
        platformAccount: this.platformAccount,
        isViewMode: modeObj.isView === true,
        formList: this.bankRepaymentData || {},
      })
    },
    // 修改订单状态
    handleEditOrderStatus(modeObj = {}) {
      this.$refs.editStatusDialogRef.handleOpen({
        isPlatformFee: true,
        statusMap: this.orderStatusMap,
        statusArr: this.orderStatusArr,
        status: this.pageData.billPayStatus,
        repaymentProofArr: this.redeemData.plaAttachList,
        // 应还本金
        repayFunds: this.repayFunds,
        // 资方费用信息
        manExpenseCulationVoList: this.redeemData.manExpenseCulationVoList,
        // 平台费用信息
        plaExpenseCulationVoList: this.redeemData.plaExpenseCulationVoList,
        // 核心账户回显数据
        platformAccount: this.platformAccount,
        isViewMode: modeObj.isView === true,
        formList: this.platformFeeData || {},
      })
    },
    // 查看余额凭证
    handleViewBalanceProof() {
      const imgSrcArr = []
      const pdfSrcArr = []
      for (const item of this.redeemData.manAttachList) {
        if (item.extension != 'pdf') {
          imgSrcArr.push({ name: item.name, url: item.link })
        } else {
          pdfSrcArr.push({ name: item.name, url: item.link })
        }
      }
      if (pdfSrcArr.length == 0) {
        this.handlePreviewImage(imgSrcArr)
      } else {
        this.handlePreviewImage(pdfSrcArr, 'pdf')
      }
    },
    // 查看支付凭证
    handleViewPayProof() {
      const imgSrcArr = []
      const pdfSrcArr = []
      for (const item of this.redeemData.plaAttachList) {
        if (item.extension != 'pdf') {
          imgSrcArr.push({ name: item.name, url: item.link })
        } else {
          pdfSrcArr.push({ name: item.name, url: item.link })
        }
      }
      if (pdfSrcArr.length == 0) {
        this.handlePreviewImage(imgSrcArr)
      } else {
        this.handlePreviewImage(pdfSrcArr, 'pdf')
      }
    },
    // 通过
    async handleExamine(pass) {
      if (pass) {
        this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.tongguojiaoyanFun()
      } else {
        this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.bohuijiaoyanFun()
      }
      if (!this.expenseInfoExpenseListCopy) return
      const params = {
        expenseInfoExpenseList: this.expenseInfoExpenseListCopy
      }
      this.handleCompleteTask(pass, params)
        .then(() => {
          this.$message.success('处理成功')
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
      // let target = false
      // let feeTarget = true
      // let form = {}
      // if (this.tableData2Result.payMode == 1) {
      //   form = this.$refs.form.form
      // }
      // this.tableData3.forEach((item, index) => {
      //   if (item.payMode == 1) {
      //     if (this.$refs[`form${index}`][0].form.status != 2) {
      //       target = true
      //     }
      //   }
      // })
      // if (pass) {
      //   if (
      //     !(
      //       (form.status === 3 || this.tableData2Result.payMode == 2) &&
      //       !target
      //     )
      //   ) {
      //     this.$message.error('请检查费用支付状态')
      //     return
      //   }
      // }
      // this.tableData3.forEach((item, index) => {
      //   if (item.payMode == 1) {
      //     this.$refs[`form${index}`][0].check().validate((valid, done, msg) => {
      //       if (!valid) {
      //         let errMsg = Object.values(msg)[0].message
      //         feeTarget = false
      //         if (!errMsg) {
      //           errMsg = Object.values(msg)[0][0].message
      //           if (!errMsg) {
      //             errMsg = '必填项未填'
      //           }
      //         }
      //         this.$message.error(errMsg)
      //         return
      //       }
      //       done()
      //     })
      //   }
      // })
      // if (this.tableData2Result.payMode == 1) {
      //   const resData = this.$refs.form.check()
      //   resData.validate((valid, done, msg) => {
      //     if (!valid) {
      //       let errMsg = Object.values(msg)[0].message
      //       feeTarget = false
      //       if (!errMsg) {
      //         errMsg = Object.values(msg)[0][0].message
      //         if (!errMsg) {
      //           errMsg = '必填项未填'
      //         }
      //       }
      //       this.$message.error(errMsg)
      //       return
      //     }
      //     this.pageData.cashDepositPaymentStatus = 2
      //     done()
      //   })
      // }

      // setTimeout(() => {
      //   this.submitLoading = true
      //   // 处理变量
      //   const variables = {}
      //   // 银行还款单
      //   if (this.tableData2Result.payMode == 1 && form.status !== undefined) {
      //     const loanManageRepayment = {
      //       status: form.status,
      //     }
      //     if (feeTarget == false) {
      //       return
      //     }
      //     if (form.status === 3) {
      //       loanManageRepayment.actualAmount = String(form.amount)
      //       loanManageRepayment.bank = form.bank
      //       loanManageRepayment.bankCardNo = form.payAccount
      //       loanManageRepayment.repaymentTime = form.endDate
      //     } else if (form.status === 4) {
      //       loanManageRepayment.failReason = form.remark
      //     }
      //     if (
      //       this.redeemData.manAttachList &&
      //       this.redeemData.manAttachList.length > 0
      //     ) {
      //       const resArr = []
      //       for (const item of this.redeemData.manAttachList) {
      //         if (item.id) resArr.push(item.id)
      //       }
      //       loanManageRepayment.voucher = resArr.toString()
      //     }
      //     variables.loanManageRepayment = loanManageRepayment
      //   }
      //   // 平台费用单
      //   let Arr = []
      //   this.tableData3.forEach((item, index) => {
      //     let billExpenseOrder = {
      //       status: 1,
      //       voucher: '',
      //     }
      //     if (item.payMode == 1) {
      //       let form = this.$refs[`form${index}`][0].form
      //       billExpenseOrder.status = form.status
      //       billExpenseOrder.voucher = form.voucher
      //       if (form.status === 2) {
      //         billExpenseOrder.actualAmount = String(form.amount)
      //         billExpenseOrder.bank = form.bank
      //         billExpenseOrder.bankCardNo = form.payAccount
      //         billExpenseOrder.repaymentTime = form.endDate
      //         billExpenseOrder.billExpenseNo = item.platformFeeObj.billExpenseNo
      //       } else if (form.status === 3) {
      //         billExpenseOrder.failReason = form.remark
      //       }
      //     } else {
      //       billExpenseOrder.status = item.billExpenseOrder.paymentStatus
      //       billExpenseOrder.voucher = ''
      //       billExpenseOrder.actualAmount = item.billExpenseOrder.amount
      //       billExpenseOrder.bank = item.accountName
      //       billExpenseOrder.bankCardNo = item.accountId
      //       billExpenseOrder.billExpenseNo = item.billExpenseOrder.billExpenseNo
      //     }

      //     Arr.push(billExpenseOrder)
      //   })
      //   variables.billExpenseOrderList = Arr
      //   variables.redeemNo = this.variables.redeemNo
      //   if (pass && JSON.stringify(this.inlayVariables) !== '{}') {
      //     Object.assign(variables, this.inlayVariables)
      //   }
      //   this.submitLoading = false

      //   this.handleCompleteTask(pass, variables)
      //     .then(() => {
      //       this.$message.success('处理成功')
      //       this.handleCloseTag('/business/businessprocess')
      //     })
      //     .catch(() => {
      //       this.submitLoading = false
      //     })
      // })
    },

    // 终止
    handleTermination() {
      this.validataterminationFunction()
    },
    // 终止后调取接口函数
    async validataterminationFunction() {
      // const form = this.$refs.form.form
      // let Arr = []
      // this.tableData3.forEach((item, index) => {
      //   let billExpenseOrder = {
      //     status: 1,
      //     voucher: '',
      //   }
      //   if (item.payMode == 1) {
      //     let form = this.$refs[`form${index}`][0].form
      //     billExpenseOrder.status = form.status
      //   } else {
      //     billExpenseOrder.status = item.billExpenseOrder.paymentStatus
      //   }

      //   Arr.push(billExpenseOrder)
      // })
      // const params = {
      //   loanManageRepayment: {
      //     status: form.status == 1 || form.status == 2 ? 6 : form.status,
      //   },
      //   billExpenseOrderList: Arr,
      // }
      this.expenseInfoExpenseListCopy = await this.$refs.feiyongwaiheziRef.zhongzhijiaoyanFun()
      if (!this.expenseInfoExpenseListCopy) return
      const params = {
        expenseInfoExpenseList: this.expenseInfoExpenseListCopy
      }
      this.handleTerminateProcess(params)
        .then(() => {
          this.$message.success('处理成功')
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 预览图片
    handlePreviewImage(imgSrcArr = [], type = 'img') {
      if (type == 'img') {
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      } else {
        this.pdfSrc = imgSrcArr[0].url + '?time=' + new Date().getMilliseconds()
      }
    },
    // 修改状态弹窗确认事件
    handleEditStatusDialogConfirm(
      submitData = { isPlatformFee: false, formList: {} }
    ) {
      if (submitData.isPlatformFee) {
        this.platformFeeData = submitData.formList
        this.pageData.billPayStatus = submitData.formList.status
      } else {
        this.bankRepaymentData = submitData.formList
        this.pageData.repaymentStatus = submitData.formList.status
      }
    },
    // 查询核心账户
    // getByGoodsIdAndUserIdFun(obj) {
    //   const params = {
    //     goodsId: obj.goodId,
    //     userId: obj.userId,
    //   }
    // },
  },
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: right;
      font-family: SourceHanSansSC-regular;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

// 自定义主体样式 - 开始
.foreclosure-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .divider {
      display: inline-block;
      margin: 0 12px;
      width: 1px;
      height: 16px;
      background: rgba(215, 215, 215, 100);
    }

    .number-container {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;

      .number-type-value {
        color: rgba(105, 124, 255, 100);
      }
    }
  }

  .goods-container {
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin-top: 16px;
    border-top: 1px solid rgba(233, 235, 239, 100);

    .goods-wrapper {
      display: flex;
      align-items: center;
      max-width: 40%;

      .goods-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 6px 0 6px 12px;
        width: 100%;
        height: 72px;

        .goods-name {
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .goods-type-container {
          margin-top: 12px;

          .goods-type {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
          }

          .goods-type-value {
            font-size: 14px;
            font-family: SFProText-Medium, SFProText;
            font-weight: 500;
            color: #53627c;
            line-height: 20px;
          }
        }
      }
    }

    .goods-desc-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 20px;

      .goods-desc-title {
        color: rgba(112, 112, 112, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
        line-height: 20px;
      }

      .goods-desc-value {
        margin-top: 8px;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-bold;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }

  .delivery-address-container {
    margin-top: 12px;

    .title {
      color: rgba(112, 112, 112, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-bold;
      font-weight: 600;
    }

    .form-item {
      margin-top: 14px;

      .label {
        display: inline-block;
        width: 96px;
        color: rgba(112, 112, 112, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }

      .value {
        color: rgba(36, 36, 36, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }
    }
  }
}

.repayment-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.repayment-status-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;

  .left-box {
    .title {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .tag {
      margin-left: 8px;
    }

    .button {
      margin-left: 8px;
    }
  }
}
// 自定义主体样式 - 结束
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}
.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  // justify-content: space-between;
  justify-content: right;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: #242424;
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      color: #ff2929;
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pay-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  .order-box {
    display: flex;
    align-items: center;
    cursor: context-menu;

    .order-status {
      width: 56px;
      height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      margin-right: 8px;
    }

    .order-text {
      height: 30px;
      border-radius: 28px;
      font-size: 14px;
      font-family: Microsoft Yahei;
      padding: 4px 8px;
      box-sizing: border-box;
      margin-right: 12px;
    }

    .order-modification-btn {
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
    }

    .order-btn {
      width: 28px;
      height: 22px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-bold;
      cursor: pointer;
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }

    .informition-box {
      margin-left: 10px;
      color: #000000b5;
      font-size: 16px;

      & > * {
        margin-left: 70px;

        &:first-child {
          margin-left: 0;
        }
      }

      &_bold {
        font-weight: bold;
      }
    }
  }

  .payOrder-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    span {
      width: 103px;
      height: 30px;
      border-radius: 4px;
      color: rgba(105, 124, 255, 100);
      font-size: 14px;
      text-align: center;
      border: 1px solid rgba(105, 124, 255, 100);
      margin-right: 10px;
      padding: 2px 4px;
      box-sizing: border-box;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }
    }

    .payOrder-left-box {
      margin-right: 10px;
    }
  }
}
</style>
