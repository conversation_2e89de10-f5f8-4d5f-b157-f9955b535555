<template>
  <el-dialog
    title="修改状态"
    append-to-body
    width="35%"
    :visible.sync="visible"
  >
    <el-form
      :model="formList"
      :rules="isViewMode ? {} : rules"
      class="rule-form"
      ref="form"
    >
      <el-form-item label="状态类型:" prop="status">
        <el-select
          style="width: 100%"
          v-model="formList.status"
          placeholder="请选择状态"
          :disabled="isViewMode"
          @change="handleChange"
        >
          <el-option
            v-for="item in statusOption"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
        <div
          class="pay-box"
          v-if="
            (isPlatformFee && formList.status === 2) ||
            (!isPlatformFee && formList.status === 3)
          "
        >
          <template v-if="isPlatformFee">
            <div
              class="form-item"
              v-for="item of repayData.plaExpenseCulationVoList"
              :key="item.id"
            >
              <span class="label">{{ item.expenseType }}(元):</span>
              <span>￥{{ item.money | formatMoney }}</span>
            </div>
          </template>
          <template v-else>
            <div class="form-item">
              <span class="label">应还本金(元):</span>
              <span>￥{{ repayData.repayFunds | formatMoney }}</span>
            </div>
            <div
              class="form-item"
              v-for="item of repayData.manExpenseCulationVoList"
              :key="item.id"
            >
              <span class="label">{{ item.expenseType }}(元):</span>
              <span>￥{{ item.money | formatMoney }}</span>
            </div>
          </template>
          <div class="form-item">
            <span class="label">应还总额(元):</span>
            <span style="color: red">￥{{ repayData.total }}</span>
          </div>
        </div>
      </el-form-item>

      <template
        v-if="
          (isPlatformFee && formList.status === 2) ||
          (!isPlatformFee && formList.status === 3)
        "
      >
        <el-form-item label="实还金额:" prop="amount" key="amount">
          <el-input
            :placeholder="`不超过${repayData.total}`"
            v-model="formList.amount"
            :disabled="isViewMode"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="付款开户行:" prop="bank" key="bank">
          <el-input
            placeholder="请输入付款开户行"
            v-model="formList.bank"
            :disabled="isViewMode"
          />
        </el-form-item>
        <el-form-item label="付款账号:" prop="payAccount" key="payAccount">
          <el-input
            placeholder="请输入付款账号"
            v-model="formList.payAccount"
            :disabled="isViewMode"
          />
        </el-form-item>
        <el-form-item label="还款时间:" prop="endDate" key="endDate">
          <el-date-picker
            :picker-options="pickerOptions"
            style="width: 100%"
            v-model="formList.endDate"
            type="datetime"
            placeholder="选择还款时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            :disabled="isViewMode"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          v-if="!isPlatformFee"
          label="还款凭证"
          prop="voucher"
          key="voucher"
        >
          <template v-if="isViewMode">
            <el-image
              style="width: 120px; height: 120px"
              :src="formList.voucher[0].imgUrl"
              :preview-src-list="[formList.voucher[0].imgUrl]"
            />
          </template>
          <template v-else>
            <BaseImageUpload
              :imgData.sync="formList.voucher"
              :disabled="false"
              :length="1"
            ></BaseImageUpload>
          </template>
        </el-form-item>
      </template>
      <template
        v-if="
          (isPlatformFee && formList.status === 3) ||
          (!isPlatformFee && formList.status === 4)
        "
      >
        <el-form-item label="失败原因:" prop="remark" key="remark">
          <el-input
            type="textarea"
            v-model="formList.remark"
            placeholder="请输入失败原因"
            :disabled="isViewMode"
          ></el-input>
        </el-form-item>
      </template>
      <template v-if="isPlatformFee">
        <el-form-item label="支付凭证" prop="previewProof" key="previewProof">
          <el-button type="text" @click="handleViewProof">查看</el-button>
        </el-form-item>
      </template>

      <el-form-item>
        <div style="text-align: right">
          <template v-if="isViewMode">
            <el-button @click="visible = false">关 闭</el-button>
          </template>
          <template v-else>
            <el-button @click="visible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </template>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import BaseImageUpload from '@/components/BaseImageUpload/index.vue'
import { formatMoney } from '@/util/filter'

const getInitFormData = () => ({
  status: null,
  amount: '',
  bank: '',
  payAccount: '',
  endDate: '',
  remark: '',
  voucher: [],
})

export default {
  name: 'RedeemConfirmEditStatusDialogIndex',
  components: {
    BaseImageUpload,
  },
  data() {
    const rules = {
      status: [{ required: true, message: '请选择状态', trigger: 'change' }],
      amount: [{ required: true, message: '请输入实还金额', trigger: 'blur' }],
      bank: [{ required: true, message: '请输入付款开户行', trigger: 'blur' }],
      payAccount: [
        { required: true, message: '请输入付款账号', trigger: 'blur' },
      ],
      endDate: [
        { required: true, message: '请选择还款时间', trigger: 'change' },
      ],
      remark: [
        { required: true, message: '请输入失败原因', trigger: 'change' },
      ],
      voucher: [
        { required: true, message: '请上传还款凭证', trigger: 'change' },
      ],
    }

    return {
      visible: false,
      formList: getInitFormData(),
      rules,
      statusOption: [],
      repayData: {
        repayFunds: 0,
        manExpenseCulationVoList: [],
        plaExpenseCulationVoList: [],
        total: 0,
        originTotal: 0,
      },
      // 是否为平台费用单确认还款
      isPlatformFee: false,
      repaymentProofArr: [],
      isViewMode: false,
    }
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      }
    },
  },
  watch: {
    'formList.amount': function (newVal) {
      if (Number(newVal) > this.repayData.originTotal) {
        this.formList.amount = this.repayData.originTotal
        this.$message.error('实还金额不应大于应还总额!')
      }
    },
  },
  methods: {
    handleChange() {
      this.formList = {
        amount: '',
        bank: '',
        payAccount: '',
        endDate: '',
        ...this.formList,
        remark: '',
      }
    },
    handleOpen(
      optionData = {
        isPlatformFee: false,
        statusMap: {},
        statusArr: [],
        status: undefined,
        repaymentProofArr: [],
        // 应还本金
        repayFunds: '',
        // 资方费用信息
        manExpenseCulationVoList: [],
        // 平台费用信息
        plaExpenseCulationVoList: [],
        // 核心账户回显数据
        platformAccount: {},
        isViewMode: false,
        formList: {},
      }
    ) {
      this.formList = optionData.formList.status
        ? optionData.formList
        : getInitFormData()
      this.formList.status = optionData.status
      this.statusOption = optionData.statusArr
      this.isViewMode = optionData.isViewMode
      this.isPlatformFee = optionData.isPlatformFee
      this.repaymentProofArr = optionData.repaymentProofArr
      this.repayData.repayFunds = optionData.repayFunds
      this.repayData.manExpenseCulationVoList =
        optionData.manExpenseCulationVoList
      this.repayData.plaExpenseCulationVoList =
        optionData.plaExpenseCulationVoList

      let repayTotal = 0
      if (this.isPlatformFee) {
        for (const item of optionData.plaExpenseCulationVoList) {
          repayTotal = this.$numJiaFun(repayTotal, item.money)
        }
      } else {
        repayTotal = Number(optionData.repayFunds)
        for (const item of optionData.manExpenseCulationVoList) {
          repayTotal = this.$numJiaFun(repayTotal, item.money)
        }
      }
      this.repayData.originTotal = repayTotal
      this.repayData.total = formatMoney(repayTotal)
      this.formList.amount = this.repayData.originTotal
      this.formList.bank = optionData.platformAccount.bank
      this.formList.payAccount = optionData.platformAccount.account

      this.visible = true
    },
    handleViewProof() {
      const imgArr = []
      for (const item of this.repaymentProofArr) {
        imgArr.push({
          name: item.name,
          url: item.link,
        })
      }
      this.$emit('handleViewProof', imgArr)
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (
            (!this.isPlatformFee && this.formList.status !== 3) ||
            (this.isPlatformFee && this.formList.status !== 2)
          ) {
            this.formList.amount = ''
            this.formList.bank = ''
            this.formList.endDate = ''
            this.formList.payAccount = ''
            this.formList.voucher = []
          }
          this.$emit('handleConfirm', {
            isPlatformFee: this.isPlatformFee,
            formList: this.formList,
          })
          this.visible = false
        } else {
          this.$message.error('请完善信息!')
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scope>
.rule-form {
  padding: 0 15px;
  ::v-deep {
    .el-form-item {
      display: flex;
      .el-form-item__label {
        width: 110px;
        max-width: 110px;
        text-align: left;
      }
      .el-form-item__content {
        flex-grow: 1;
      }
    }
  }
  .pay-box {
    padding: 20px 12px;
    width: 100%;
    background-color: #f7f7f7;
    border-radius: 6px;
    box-sizing: border-box;
    margin-top: 9px;
    line-height: 20px;
    & span:first-child {
      display: block;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      font-weight: 500;
      margin-right: 8px;
    }
    & span:last-child {
      color: #00072a;
      font-weight: 600;
      font-size: 14px;
    }

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 150px;
        text-align: right;
      }
    }
  }
}
</style>
