<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '应收账款质押-融资申请'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 产品信息 -->
      <ProductInfo :financeNo="financeNo" :dataInfo="goodInfo" />

      <!-- 订单信息 -->
      <OrderInfo :dataInfo="goodInfo" />

      <!-- 费用详情 -->
      <CostInfo :redeemCargoCalculationVO="redeemCargoCalculationVO" :dataInfo="goodInfo" />

      <!-- 处置货物信息 -->
      <DisposeGoodInfo :dataInfo="goodInfo" />

      <!-- 处置比例 -->
      <!-- 处置比例 -->
      <basic-container>
        <div class="cargo">
          <div class="cargo-to-apply-for-product">
            <h1 class="cargo-to-apply-for-product-h1">
              <span>处置比例</span>
            </h1>
          </div>
        </div>

        <div class="table-top refund">

          <!-- <div class="table-item-wrap" v-for="(item, index) in cargoSolveRatios" :key="index">
            <div class="title">{{ index }}</div> -->
          <el-table ref="table4" :data="cargoSolveRatios" style="width: 100%; margin-top: 13px" class="table-border-style"
            :summary-method="getSummaries" show-summary>
            <el-table-column v-if="taskCargoSolveExpenseList.cargoSolveExpenseList_name" prop="name"
              label="费用名称"></el-table-column>
            <el-table-column v-if="taskCargoSolveExpenseList.cargoSolveExpenseList_payable" prop="payable"
              label="应付(元)"></el-table-column>
            <el-table-column v-if="taskCargoSolveExpenseList.cargoSolveExpenseList_paymentRatio" prop="paymentRatio"
              label="分账比例(%)" :formatter="formatterPaymentRatio">
              <template slot-scope="scope">
                <div class="tabl-input-wrap" :class="scope.row.input_status" v-if="isFZ">
                  <el-input type="number" :min="0" :max="100" v-model="scope.row.paymentRatio" class="inp"
                    placeholder="请输入比例" @focus="onFocusTableInput(scope.row, scope.$index)" @blur="onBlurTableInput"
                    @input="(value) => { adapterRatio(value, scope.row, scope.$index) }"></el-input>
                  <span class="err">{{ tis_text || '请输入比例' }}</span>
                </div>
                <span v-else>{{ scope.row.paymentRatio }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="actualPayment" label="分账金额(元)"
              v-if="taskCargoSolveExpenseList.cargoSolveExpenseList_actualPayment">
              <template slot-scope="scope">
                <div class="tabl-input-wrap" :class="scope.row.input_status" v-if="isFZ">
                  <el-input type="number" min="0" v-model="scope.row.actualPayment" class="inp" placeholder="请输入金额"
                    @focus="onFocusTableInput(scope.row, scope.$index)" @blur="onBlurTableInput"
                    @input="(value) => { adapterPayment(value, scope.row, scope.$index) }"></el-input>
                  <span class="err">{{ tis_text || '请输入金额' }}</span>
                </div>
                <span v-else>{{ scope.row.actualPayment }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- </div> -->

          <div class="footer">
            <div class="l annex"></div>
            <div class="right">
              <div class="sp-text">处置总额 = 处置金额 + 保证金</div>
              处置总额: <span class="am">{{ goodInfo.cargoSolveTotal }}</span>元
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 处置信息 （分账不需要显示） -->
      <basic-container v-if="!isFZ">
        <div class="cargo">
          <div class="cargo-to-apply-for-product">
            <h1 class="cargo-to-apply-for-product-h1">
              <span>处置信息</span>
            </h1>
          </div>
        </div>

        <div class="descriptions-for-box">
          <el-descriptions title="" :column="3" border>

            <el-descriptions-item label="处置公司">
              <div class="tabl-input-wrap" v-if="taskScargoSolve.cargo_solve_cargoSolveCompany"
                :class="errInfo.cargoSolveCompany_input_status">
                <el-input v-model="goodInfo.cargoSolveCompany" class="inp" placeholder="请输入处置公司"
                  @focus="onBlurTableRoot('cargoSolveCompany')"></el-input>
                <span class="err">请输入处置公司</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="处置公司负责人">
              <div class="tabl-input-wrap" v-if="taskScargoSolve.cargo_solve_cargoSolveCompany"
                :class="errInfo.cargoSolvePerson_input_status">
                <el-input v-model="goodInfo.cargoSolvePerson" class="inp" placeholder="请输入负责人"
                  @focus="onBlurTableRoot('cargoSolvePerson')"></el-input>
                <span class="err">请输入负责人</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="联系方式">
              <div class="tabl-input-wrap" v-if="taskScargoSolve.cargo_solve_cargoSolveCompany"
                :class="errInfo.cargoSolvePhone_input_status">
                <el-input v-model="goodInfo.cargoSolvePhone" class="inp" placeholder="请输入联系方式"
                  @focus="onBlurTableRoot('cargoSolvePhone')"></el-input>
                <span class="err">请输入联系方式</span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="处置总额">{{
              goodInfo.cargoSolveTotal || '0.00'
            }}元</el-descriptions-item>
            <el-descriptions-item label="应付金额">{{
              goodInfo.payableAmount
            }}元</el-descriptions-item>
            <el-descriptions-item label="剩余退回">{{
              goodInfo.returnAmount
            }}元</el-descriptions-item>
            <el-descriptions-item label="是否足价清算">{{
              goodInfo.isFullPrice == '1' ? '是' : '否'
            }}</el-descriptions-item>
          </el-descriptions>

        </div>


        <div v-if="!isFZ && goodInfo.isFullPrice == '1' && bankCards.length">
          <div class="cargo pt20">
            <div class="cargo-to-apply-for-product">
              <h1 class="cargo-to-apply-for-product-h1">
                <span>退回信息</span>
              </h1>
            </div>
          </div>

          <div class="refund table-top">
            <el-table ref="table4" :data="bankCards" style="width: 100%; margin-top: 13px" class="table-border-style">
              <el-table-column v-if="taskBankCard.bankCard_bankName" prop="bankName" label="开户银行"></el-table-column>
              <el-table-column v-if="taskBankCard.bankCard_enterpriseName" prop="enterpriseName"
                label="开户名"></el-table-column>
              <el-table-column v-if="taskBankCard.bankCard_bankCardNo" prop="bankCardNo" label="银行账号"></el-table-column>
              <el-table-column v-if="taskBankCard.bankCard_returnAmount" prop="returnAmount"
                label="退回金额"></el-table-column>
            </el-table>
          </div>
        </div>

      </basic-container>

    </template>


    <!-- 批复意见 -->
    <basic-container>
      <div class="approval-container">
        <span class="title">批复意见：</span>
        <el-input class="value" type="textarea" :rows="5" resize="none" placeholder="请输入批复意见" v-model="comment">
        </el-input>
      </div>
    </basic-container>

    <!-- 底部按钮 -->
    <wf-button class="custom-button" :loading="submitLoading" :button-list="buttonList" :process="process"
      :comment="comment" @examine="handleExamine" @user-select="handleUserSelect" @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess" @withdraw="handleWithdrawTask"></wf-button>
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import { formatMoney } from '@/util/filter.js'
import {
  getDictionary,
  saleContractList,
  repaymentCalculationDetailById,
  platformExpensesList2,
} from '@/api/goods/pcontrol/workflow/productConfirmation'

import ProductInfo from '@/views/loan/cargosolveComponent/ProductInfo'
import OrderInfo from '@/views/loan/cargosolveComponent/OrderInfo'
import CostInfo from '@/views/loan/cargosolveComponent/CostInfo'
import DisposeGoodInfo from './cargosolveComponent/DisposeGoodInfo'
import DisposeRatio from '@/views/loan/cargosolveComponent/DisposeRatio'
import DisposeInfo from '@/views/loan/cargosolveComponent/DisposeInfo'

import { findIndex } from 'lodash'

export default {
  mixins: [customExForm],
  components: {
    WfButton,
    WfFlow,
    ProductInfo,
    OrderInfo,
    CostInfo,
    DisposeGoodInfo,
    DisposeRatio,
    DisposeInfo,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNames1() {
      setTimeout(() => {
        this.$refs.table1.$ready = false
      }, 50)
    },
    activeNames2() {
      setTimeout(() => {
        this.$refs.table2.$ready = false
        this.$refs.table3.$ready = false
      }, 50)
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      financeNo: '',
      tableData1: [],
      activeNames1: [],
      activeNames2: [],
      change1Type: false,
      change2Type: false,
      change1ClassType: true,
      tableData2: [],
      tableData3: [],
      tableData4copy: [],
      sum1: 0,
      chargeMode: '先息后本',
      allDailyInterestRate: 0,
      allAnnualInterestRate: 0,
      dailyInterestRate: 0,
      annualInterestRate: 0,
      calculation: false,

      // 显影控制
      // 处置信息
      taskScargoSolve: {
        cargo_solve: null,
        cargo_solve_cargoSolveCompany: null,
        cargo_solve_cargoSolvePerson: null,
        cargo_solve_cargoSolvePhone: null,
      },
      // 退回信息
      taskBankCard: {
        bankCard_bankName: null,
        bankCard_enterpriseName: null,
        bankCard_bankCardNo: null,
        bankCard_returnAmount: null,
      },
      // 处置比例
      taskCargoSolveExpenseList: {
        cargoSolveExpenseList_name: null,
        cargoSolveExpenseList_payable: null,
        cargoSolveExpenseList_paymentRatio: null,
        cargoSolveExpenseList_actualPayment: null,
      },
      // 货物详情
      goodInfo: {},
      cargoSolveRatios: [],
      // 费用详情
      redeemCargoCalculationVO: {
        manExpenseCulationVoList: [],
        plaExpenseCulationVoList: [],
      },
      isFZ: false,
      errInfo: {
        cargoSolveCompany_input_status: '',
        cargoSolvePerson_input_status: '',
        cargoSolvePhone_input_status: '',
        // 分账金额
        tis_text: ''
      },
      bankCards: [],
    }
  },
  methods: {
    // 合计
    allMonrySum(item) {
      this.sum1 = 0
      this.sum1 = this.$numJiaFun(this.sum1, item.monthlySupply)
      if (this.platformFeeList.length) {
        this.platformFeeList.forEach(item => {
          if (item.platformExpensesVOS.length) {
            this.sum1 = this.$numJiaFun(
              this.sum1,
              item.platformExpensesVOS[item.platformExpensesVOS.length - 1]
                .amount
            )
          }
        })
      }
    },
    onBlurTableInput() {
      this.adapterBeforeAmo()
    },
    onFocusTableInput(row, $index) {
      this.cargoSolveRatios[$index].input_status = ''
    },
    onBlurTableRoot(field) {
      this.errInfo[`${field}_input_status`] = ''
    },
    // 比例计算
    adapterRatio(value, row, index) {
      const cargoSolveRatios = this.cargoSolveRatios
      if (this.tis_text) {
        this.tis_text = ''
        cargoSolveRatios.map(d => {
          d.input_status = ''
        })
      }
      let newvalue = value > 100 ? 100 : value < 0 ? 0 : value
      const { payable } = row
      cargoSolveRatios[index].paymentRatio = newvalue
      // 分账金额 = 应付 * 分账比例 给到 actualPayment
      // cargoSolveRatios[index].actualPayment = (payable * (newvalue / 100)).toFixed(2)
      cargoSolveRatios[index].actualPayment = this.$numChengFun(this.$numChuFun(newvalue, 100), payable)
      this.cargoSolveRatios = [...cargoSolveRatios]
      this.totalActualPaymentTest()
    },
    // 金额计算
    adapterPayment(value, row, index) {
      const cargoSolveRatios = this.cargoSolveRatios
      if (this.tis_text) {
        this.tis_text = ''
        cargoSolveRatios.map(d => {
          d.input_status = ''
        })
      }


      const { payable } = row
      // let nval = value > Number(payable) ? Number(payable) : value < 0 ? 0 : value
      let nval = this.$numBidaFun(value, payable)
        ? Number(payable)
        : this.$numBixiaoFun(value, 0)
        ? 0
        : value
      if (this.$numBidaFun(nval, payable, true)) {
        // 当前输入超总额
        let curInpTotal = 0
        let nCurInpTotal = 0
        const cargoSolveTotal = Number(this.goodInfo.cargoSolveTotal)
        cargoSolveRatios.map((d, di) => {
          if (index === di) {
            curInpTotal += nval
          } else if (d.actualPayment) {
            curInpTotal = this.$numJiaFun(curInpTotal, d.actualPayment)
            nCurInpTotal = this.$numJiaFun(nCurInpTotal, d.actualPayment)
          }
        })
        // 超出总额
        // if (curInpTotal > cargoSolveTotal && cargoSolveTotal - nCurInpTotal > 0) {
        //   nval = cargoSolveTotal - nCurInpTotal
        // }
        if (
          this.$numBidaFun(curInpTotal, cargoSolveTotal) &&
          this.$numBidaFun(
            this.$numJianFun(cargoSolveTotal, nCurInpTotal),
            0
          )
        ) {
          nval = this.$numJianFun(cargoSolveTotal, nCurInpTotal)
        }
      }

      cargoSolveRatios[index].actualPayment = nval
      // 分账比例 = 应付 / 分账金额 值给到 paymentRatio 举例 50%  赋值50
      // cargoSolveRatios[index].paymentRatio = Number(nval / payable).toFixed(4) * 100
      cargoSolveRatios[index].paymentRatio = this.$numChengFun(this.$numChuFun(nval, payable), 100)
      this.cargoSolveRatios = [...cargoSolveRatios]
      this.totalActualPaymentTest()
    },
    // 最后金额自动计算
    adapterBeforeAmo() {
      const cargoSolveRatios = this.cargoSolveRatios
      const nr = cargoSolveRatios.filter(d => (!d.actualPayment))
      if (nr.length !== 1) return
      const target = nr[0]
      // const targetIndex = cargoSolveRatios.indexOf(nr)

      // 当前输入总额
      let curInpTotal = 0
      cargoSolveRatios.map(d => {
        if (d.actualPayment) {
          curInpTotal = this.$numJiaFun(curInpTotal, d.actualPayment)
        }
      })

      const cargoSolveTotal = Number(this.goodInfo.cargoSolveTotal)

      if (curInpTotal < cargoSolveTotal) {
        let newVal = this.$numJianFun(cargoSolveTotal, curInpTotal)
        // if (newVal > Number(target.payable)) newVal = Number(target.payable)
        if (this.$numBidaFun(newVal, target.payable)) {
          newVal = Number(target.payable)
        }
        target.actualPayment = newVal
        // target.paymentRatio = Number(newVal / Number(target.payable)).toFixed(4) * 100
        target.paymentRatio = this.$numChengFun(this.$numChuFun(newVal, target.payable), 100)
        this.cargoSolveRatios = [...cargoSolveRatios]
      }
    },
    // 总金额计算校验
    totalActualPaymentTest() {
      const cargoSolveRatios = this.cargoSolveRatios
      let fzTootal = 0
      cargoSolveRatios.map(d => {
        fzTootal = this.$numJiaFun(fzTootal, d.actualPayment)
      })
      // if (fzTootal > Number(this.goodInfo.cargoSolveTotal)) {
      if (this.$numBidaFun(fzTootal, this.goodInfo.cargoSolveTotal)) {
        cargoSolveRatios.map(d => d.input_status = 'err')
        this.tis_text = '分账总额不得超过处置总额'
        this.cargoSolveRatios = [...cargoSolveRatios]
        return false
      }
      return true
    },

    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        // 流程产品信息
        const { cargo_solve, processNo, } = variables

        console.log('-cargo_solve', JSON.parse(JSON.stringify(cargo_solve)))
        this.goodInfo = cargo_solve
        const { redeemCargoCalculationVO, cargoSolveExpenseList, financeNo, warehouseDetailsVOList, bankCard, returnAmount
        } = cargo_solve
        if (warehouseDetailsVOList && warehouseDetailsVOList[0]) {
          this.goodInfo.commodityUrl = warehouseDetailsVOList[0].logo
          this.goodInfo.commodityName = warehouseDetailsVOList[0].goodsName
        }
        this.redeemCargoCalculationVO = redeemCargoCalculationVO || {}
        this.financeNo = financeNo

        // 处置比例
        this.cargoSolveRatios = [...cargoSolveExpenseList]
        console.log('-cargoSolveExpenseList', this.taskCargoSolveExpenseList, cargoSolveExpenseList)
        this.adapterIsShow(taskForm)
        // 是否为分账
        this.isFZ = data.taskName === '分账审批' // findIndex(taskForm, { id: 'cargoSolveExpenseList' }) !== -1


        if (bankCard) {
          this.bankCards = [{ ...bankCard, returnAmount: returnAmount }]
        }

      })
    },
    // 显隐控制
    adapterIsShow(taskForm) {
      this.taskForm = taskForm

      taskForm.map(d => {
        const key = d.id
        const readable = d.readable
        if (Object.hasOwnProperty.call(this.taskScargoSolve, key) && readable) this.taskScargoSolve[key] = d
        if (Object.hasOwnProperty.call(this.taskBankCard, key) && readable) this.taskBankCard[key] = d
        if (Object.hasOwnProperty.call(this.taskCargoSolveExpenseList, key) && readable) this.taskCargoSolveExpenseList[key] = d
      })


    },
    // 通过
    handleExamine(pass) {
      if (!this.goodInfo.redeemSpecList || !this.goodInfo.redeemSpecList.length) {
        return this.$message.warning('没有可处置的货物')
      }
      if (pass) {
        let result = true
        const cargoSolveRatios = [...this.cargoSolveRatios]
        if (this.isFZ && Array.isArray(cargoSolveRatios)) {
          // 分账
          cargoSolveRatios.map(d => {
            if ((!d.actualPayment && d.actualPayment !== 0) || (!d.paymentRatio && d.paymentRatio !== 0)) {
              d.input_status = 'err'
              result = false
            }
          })
          if (!result) {
            this.cargoSolveRatios = cargoSolveRatios
            return result
          }
          if (!this.totalActualPaymentTest()) return

        } else {
          // 非分账
          if (!this.goodInfo.cargoSolveCompany) {
            this.errInfo[`cargoSolveCompany_input_status`] = 'err'
            result = false
          }
          if (!this.goodInfo.cargoSolvePerson) {
            this.errInfo[`cargoSolvePerson_input_status`] = 'err'
            result = false
          }
          if (!this.goodInfo.cargoSolvePhone) {
            this.errInfo[`cargoSolvePhone_input_status`] = 'err'
            result = false
          }
          if (!result) {
            this.goodInfo = { ...this.goodInfo }
            return result
          }
        }
      }
      this.validataFunction(pass)
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      const cargo_solve = this.goodInfo
      if (this.isFZ) cargo_solve.cargoSolveRatios = this.cargoSolveRatios
      console.log('tj-cargo_solve', cargo_solve)
      this.handleCompleteTask(pass, { cargo_solve })
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 处置比例总
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计:'
          return
        }

        if (column.property !== 'actualPayment') return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    tableRowClassName({ row }) {
      if (!row.refundTime || row.refundTime === '总计:') {
        return 'aggregate-row'
      }
      return ''
    },
    viewGoods() {
      if (this.processGoodsObj.type == 2) {
        this.$router.push({
          path: '/pcontrol/pinformation',
          query: { id: this.processGoodsObj.id },
        })
        sessionStorage.setItem('look', 'true')
      } else {
        this.$router.push({
          path: '/pcontrol/purchasing',
          query: { id: this.processGoodsObj.id },
        })
        sessionStorage.setItem('look', 'true')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.table-item-wrap {
  .title {
    font-size: 14px;
    color: #7d7d7d;
    padding-top: 20px;
  }
}

::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    >* {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    >div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        &>span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        &>span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            &>img {
              width: 100%;
              object-fit: cover;
            }
          }

          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            &>span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }

            &>span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }

      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover>td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}

::v-deep i.el-collapse-item__arrow {
  display: none;
}

::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}

::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}

::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}

::v-deep .el-card {
  border-radius: 8px;

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell>.cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}


.cargo {
  .cargo-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .cargo-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .space {
        display: inline-block;
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: #d7d7d7;
        text-align: center;
        margin: 0 8px;
      }

      .sub-title {
        line-height: 22px;
        color: #7d7d7d;
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;

        .code {
          color: #697cff;
        }
      }

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        &>span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        &>span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .cargo-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .cargo-to-apply-for-product-left-logo-box {
        margin-top: 17px;
        display: flex;
        align-items: center;

        .cargo-to-apply-for-product-left-logo-box-img {
          width: 48px;
          height: 48px;
          border-radius: 40px;
          border: 1px solid rgba(234, 234, 234, 100);
          overflow: hidden;
          margin-right: 7px;

          &>img {
            width: 100%;
            object-fit: cover;
          }
        }

        .cargo-to-apply-for-product-left-logo-box-goodname {
          display: flex;
          flex-direction: column;

          &>span:first-child {
            display: block;
            height: 24px;
            color: rgba(18, 119, 255, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
          }

          &>span:last-child {
            display: block;
            height: 18px;
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }

      .cargo-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .cargo-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover>td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }

    .fromLeft-title-user {
      line-height: 22px;
      color: #7d7d7d;
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .fromLeft-title-name {
      color: #697cff !important;
      line-height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}


.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      min-width: 135px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }

}

.descriptions-for-box2 {
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 200px;
      min-width: 200px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      // width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}


.footer {
  padding-top: 15px;
  display: flex;
  justify-content: space-between;

  .r {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;

    .am {
      padding-left: 5px;
      font-size: 22px;
    }
  }

  .right {

    .sp-text {
      color: #0087ff;
      font-weight: normal;
      padding-bottom: 5px;
    }

    font-size: 16px;
    font-weight: 600;

    .am {
      padding-left: 5px;
      font-size: 22px;
    }
  }
}

.repayment-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 5px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.confirm-form-wrap {
  .formula-wrap {
    padding: 20px 40px;
    color: #aaa;
  }
}

.pt20 {
  padding-top: 20px;
}



.header {
  .pic-wrap {
    .pic-img {
      width: 48px;
      height: 48px;
      border-radius: 40px;
      border: 1px solid #eaeaea;
      overflow: hidden;
      margin-right: 7px;
    }
  }

  .info {
    .name {
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.align-center {
  display: flex;
  align-items: center;
}

.annex {
  .fj-label {
    color: #7d7d7d;
    font-size: 14px;
  }

  .fj-item {
    a {
      color: #3894ff;
      /* line-height: 36px; */
      /* padding-top: 10px; */
      display: inline-block;
      background: #f8f8f8;
      width: 200px;
      border: 1px solid #ededed;
      font-size: 14px;
      /* line-break: anywhere; */
      line-height: 26px;
      padding-left: 10px;
      ;
    }
  }
}

.tabl-input-wrap {

  &.err {
    .inp {
      ::v-deep {
        .el-input__inner {
          border-color: red;
        }
      }
    }

    .err {
      display: block;
    }
  }

  .err {
    display: none;
    color: red;
  }
}
</style>
