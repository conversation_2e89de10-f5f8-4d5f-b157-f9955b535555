<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '动产质押货物处置'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{
                      variables.applyUserName
                    }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">

      <div class="recDetail-box">
        <!-- 产品信息 -->
        <ProductInfo :financeNo="financeNo" :dataInfo="goodInfo" />

        <!-- 订单信息 -->
        <OrderInfo :dataInfo="goodInfo" />

        <CostInfo :redeemCargoCalculationVO="redeemCargoCalculationVO" :dataInfo="goodInfo" />

        <!-- 处置货物信息 -->
        <DisposeGoodInfo :dataInfo="goodInfo" />

        <!-- 处置比例 -->
        <DisposeRatio :dataInfo="goodInfo" />

        <!-- 处置信息（分账不需要显示） -->
        <DisposeInfo :dataInfo="goodInfo" :bankCards="bankCards"/>
      </div>

    </template>
  </div>
</template>

<script>
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import { formatMoney } from '@/util/filter.js'
import {
  getDictionary,
  saleContractList,
  repaymentCalculationDetailById,
  platformExpensesList2,
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import ProductInfo from '@/views/loan/cargosolveComponent/ProductInfo'
import OrderInfo from '@/views/loan/cargosolveComponent/OrderInfo'
import CostInfo from '@/views/loan/cargosolveComponent/CostInfo'
import DisposeGoodInfo from '@/views/loan/cargosolveComponent/DisposeGoodInfo'
import DisposeRatio from '@/views/loan/cargosolveComponent/DisposeRatio'
import DisposeInfo from '@/views/loan/cargosolveComponent/DisposeInfo'

export default {
  mixins: [customExForm],
  components: {
    WfButton,
    WfFlow,
    ProductInfo,
    OrderInfo,
    CostInfo,
    DisposeGoodInfo,
    DisposeRatio,
    DisposeInfo,
  },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNames1() {
      setTimeout(() => {
        this.$refs.table1.$ready = false
      }, 50)
    },
    activeNames2() {
      setTimeout(() => {
        this.$refs.table2.$ready = false
        this.$refs.table3.$ready = false
      }, 50)
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      financeNo: '',
      tableData1: [],
      activeNames1: [],
      activeNames2: [],
      change1Type: false,
      change2Type: false,
      change1ClassType: true,
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData4copy: [],
      sum1: 0,
      chargeMode: '先息后本',
      allDailyInterestRate: 0,
      allAnnualInterestRate: 0,
      dailyInterestRate: 0,
      annualInterestRate: 0,
      calculation: false,

      // 显影控制
      // pledgedGoodsList: {
      //   pledgedGoods_serialNumber: false,
      //   pledgedGoods_uniqueIdentification: false,
      //   pledgedGoods_tradeBackground: false,
      //   pledgedGoods_voucherType: false,
      //   pledgedGoods_dateDue: false,
      //   pledgedGoods_theCurrentValue: false,
      //   pledgedGoods_valueOfUse: false,
      // },
      // bankInterestList: {
      //   reimbursementTrial_bankInterest: false,
      //   bankInterest_periods: false,
      //   bankInterest_repaymentDate: false,
      //   bankInterest_totalShouldAlso: false,
      //   bankInterest_repaymentPrincipal: false,
      //   bankInterest_shouldAlsoInterest: false,
      // },
      // costPlatformList: {
      //   reimbursementTrial_costPlatform: false,
      //   costPlatform_typeOfExpense: false,
      //   costPlatform_costOfName: false,
      //   costPlatform_payTheNode: false,
      //   costPlatform_chargeMode: false,
      //   costPlatform_amountPayable_r: false,
      //   costPlatform_amountPayable_w: false,
      // },
      // // 其他费用的
      // platformFeeList: [],
      // // 所有的费用
      // feeData: { showRepaymentPlan: { stagRecords: [{ planFeeList: [] }] } },


      // ------------------------------
      // 货物详情
      goodInfo: {},
      // 货物详情
      goodsDetail: {},
      // 费用详情
      redeemCargoCalculationVO: {
        manExpenseCulationVoList: [],
        plaExpenseCulationVoList: [],
      },
      bankCards: [],
    }
  },
  methods: {
    // 合计
    allMonrySum(item) {
      this.sum1 = 0
      this.sum1 = this.$numJiaFun(this.sum1, item.monthlySupply)
      if (this.platformFeeList.length) {
        this.platformFeeList.forEach(item => {
          if (item.platformExpensesVOS.length) {
            this.sum1 = this.$numJiaFun(
              this.sum1,
              item.platformExpensesVOS[item.platformExpensesVOS.length - 1]
                .amount
            )
          }
        })
      }
      console.log(this.sum1)
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        this.waiting = false

        const data = res.process
        this.resData = data
        const { taskForm } = res.form
        const { variables } = data
        this.variables = variables || {}
        this.taskForm = taskForm
        // 流程产品信息
        const { cargo_solve, processNo, } =
          variables

        this.goodInfo = cargo_solve
        const { redeemCargoCalculationVO, cargoSolveCalculationMap, financeNo, warehouseDetailsVOList, bankCard, returnAmount } = cargo_solve
        this.redeemCargoCalculationVO = redeemCargoCalculationVO || {}

        if (warehouseDetailsVOList && warehouseDetailsVOList[0]) {
          this.goodInfo.commodityUrl = warehouseDetailsVOList[0].logo
          this.goodInfo.commodityName = warehouseDetailsVOList[0].goodsName
        }
        this.redeemCargoCalculationVO = redeemCargoCalculationVO || {}
        console.log('-this.goodInfo', this.goodInfo)
        this.financeNo = financeNo
        // let tableData1 = [];
        // let tableData2 = [];
        // if (Array.isArray(cargoSolveExpenses)) {
        //   cargoSolveExpenses.map(d => {
        //     const { type } = d
        //     if (type === 3) {
        //       // 仓储费
        //       tableData1.push(d)
        //     } else {
        //       // 银行还款单
        //       tableData2.push(d)
        //     }
        //   })
        // }
        // this.tableData1 = tableData1
        // this.tableData2 = tableData2
        if (bankCard) {
          this.bankCards = [{ ...bankCard, returnAmount: returnAmount }]
        }

      })
    },
    // getCargoSolveDetail(params) {
    //   console.log('---params', params)
    //   getDetail(
    //     params
    //   ).then(res => {
    //     if (res.status === 200 && res.data) {
    //       const resData = res.data
    //       console.log('resData1', resData);
    //       this.goodInfo = resData.data
    //       this.redeemCargoCalculationVO = resData.data.redeemCargoCalculationVO


    //     }
    //   })

    //   getInfo(
    //     params
    //   ).then(res => {
    //     if (res.status === 200 && res.data) {
    //       const resData = res.data
    //       console.log('resData2', resData);
    //       this.goodsDetail = resData.data

    //       const { cargoSolveExpenses } = resData.data
    //       let tableData1 = [];
    //       let tableData2 = [];
    //       if (Array.isArray(cargoSolveExpenses)) {
    //         cargoSolveExpenses.map(d => {
    //           const { type } = d
    //           if (type === 3) {
    //             // 仓储费
    //             tableData1.push(d)
    //           } else {
    //             // 银行还款单
    //             tableData2.push(d)
    //           }
    //         })
    //       }
    //       this.tableData1 = tableData1
    //       this.tableData2 = tableData2
    //     }
    //   })
    // },
    // 通过
    handleExamine(pass) {
      if (this.calculation) {
        for (const item of this.tableData4) {
          if (!item.amount) {
            this.$message.error('请手动录入应付金额')
            return
          }
        }
      }
      this.validataFunction(pass)
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      this.handleCompleteTask(pass)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;
  flex: 1;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    >* {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    >div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: rgba(16, 16, 16, 100);
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        &>span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        &>span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            &>img {
              width: 100%;
              object-fit: cover;
            }
          }

          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            &>span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }

            &>span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }

      .boxs-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover>td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}

::v-deep i.el-collapse-item__arrow {
  display: none;
}

::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}

::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}

::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}

::v-deep .el-card {
  border-radius: 8px;

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell>.cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    display: flex;
    align-items: center;

    .long-string {
      display: inline-block;
      width: 1px;
      height: 16px;
      line-height: 20px;
      background-color: rgba(215, 215, 215, 100);
      text-align: center;
      margin: 0 8px;
    }

    .interest-rate {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
    }

    .fromLeft-title-user {
      line-height: 22px;
      color: #7d7d7d;
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .fromLeft-title-name {
      color: #697cff !important;
      line-height: 22px;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.i-active {
  transform: rotate(0deg) !important;
}


.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      min-width: 135px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }

}

.descriptions-for-box2 {
  position: relative;
  z-index: 1;

  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 200px;
      min-width: 200px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }

    .el-descriptions-item__content {
      // width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改折叠组件样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}

::v-deep i.el-collapse-item__arrow {
  display: none;
}

::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}

::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}

::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}

::v-deep .el-card {
  border-radius: 8px;

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

// 更改表格组件样式
::v-deep {
  .table-top {
    margin-top: 23px;

    .table-title-box {
      height: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          height: 22px;
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 3px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .table-title {
      line-height: 21px;
      color: #7d7d7d;
      font-size: 14px;
      font-weight: 400;
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell>.cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}

.cargo {
  .cargo-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .cargo-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;

      .space {
        display: inline-block;
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: #d7d7d7;
        text-align: center;
        margin: 0 8px;
      }

      .sub-title {
        line-height: 22px;
        color: #7d7d7d;
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;

        .code {
          color: #697cff;
        }
      }

      .long-string {
        width: 1px;
        height: 16px;
        line-height: 20px;
        background-color: rgba(215, 215, 215, 100);
        text-align: center;
        display: inline-block;
        margin: 0 8px;
      }

      .serial-number {
        display: flex;
        align-items: baseline;

        &>span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }

        &>span:last-child {
          color: #697cff;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    .cargo-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .cargo-to-apply-for-product-left-logo-box {
        margin-top: 17px;
        display: flex;
        align-items: center;

        .cargo-to-apply-for-product-left-logo-box-img {
          width: 48px;
          height: 48px;
          border-radius: 40px;
          border: 1px solid rgba(234, 234, 234, 100);
          overflow: hidden;
          margin-right: 7px;

          &>img {
            width: 100%;
            object-fit: cover;
          }
        }

        .cargo-to-apply-for-product-left-logo-box-goodname {
          display: flex;
          flex-direction: column;

          &>span:first-child {
            display: block;
            height: 24px;
            color: rgba(18, 119, 255, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
            text-decoration: underline;
            font-weight: 600;
            cursor: pointer;
          }

          &>span:last-child {
            display: block;
            height: 18px;
            color: rgba(105, 124, 255, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }

      .cargo-to-apply-for-product-right-goodtype {
        width: 107px;
        height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
      }
    }

    .cargo-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }

        .el-table__body tr:hover>td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }

      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}


// 自定义主体样式 - 结束
// 更改表格组件样式
::v-deep {
  .table-top {
    margin-top: 23px;

    .table-title-box {
      height: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }

        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }

        & span:last-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }

    .contract-no {
      color: #697cff;
    }

    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }

    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }

    .el-table th.el-table__cell>.cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }

    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}

::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}

.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  // justify-content: space-between;
  justify-content: right;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: #242424;
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      color: #ff2929;
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

::v-deep .account {
  display: flex;
  width: 400px;
  align-items: center;

  .accountText {
    margin-right: 20px;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: #fff;
    cursor: pointer;
  }
}



.footer {
  padding-top: 15px;
  display: flex;
  justify-content: space-between;

  .r {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;

    .am {
      padding-left: 5px;
      font-size: 22px;
    }
  }
}

.repayment-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 5px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.confirm-form-wrap {
  .formula-wrap {
    padding: 20px 40px;
    color: #aaa;
  }
}

.pt20 {
  padding-top: 20px;
}



.title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;

  .titleText {
    display: flex;
    align-items: center;

    .title-icon {
      width: 4px;
      height: 15px;
      background: #697cff;
      border-radius: 2px;
      margin-right: 8px;
    }

    h1 {
      margin: 0;
      height: 22px;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      display: flex;
      align-items: center;
    }
  }
}

.el-tab-pane {
  display: flex;
}

::v-deep.cardContainer {
  display: flex;
  width: 100%;
  // justify-content: space-between;
  box-sizing: border-box;

  .card-img {
    width: calc(50% - 12.5px);
    height: 156px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f6f6f6;

    .el-card__body {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

    }
  }

  .card-box {
    margin-right: 25px;
    width: calc(50% - 12.5px);
    height: 156px;
    display: flex;
    background: #f6f6f6;

    .card-item {
      margin-bottom: 20px;
      height: 20px;
      line-height: 20px;
      margin-right: 4px;
      text-align: left;
      font-size: 14px;
      font-weight: 500;

      span {
        color: #697cff;
      }

      .card-item-name {
        color: #7d7d7d;
        display: inline-block;
        width: 130px;
        text-align: right;
      }
    }
  }
}

::v-deep .el-row {
  margin-top: 20px;
  height: 48px;
  display: flex;
}

.container-item,
.container-text {
  height: 48px;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 20px;
  border: 1px solid #f3f3f3;
  box-sizing: border-box;
}

.container-item {
  background: #f7f7f7;
}

.tag {
  padding: 0 20px;
  background: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
  margin-left: 20px;
  font-size: 14px;
  border-radius: 14px;
}

.tag-container {
  display: flex;
  align-items: center;
  justify-content: center;

  .tag-item {
    margin-right: 18px;
    text-align: center;
    align-items: center;
    justify-content: center;
    width: 80px;
  }

  .tag-frist {
    color: #000000;
  }
}

::v-deep .seluserContainer {
  .el-dialog__body {
    padding-top: 20px;
  }
}

::v-deep .followUpContainer {
  .el-dialog__body {
    padding: 0 20px;

    .followUp {
      height: 296px;
    }
  }

  .el-dialog__footer {
    border-top: 2px solid #f3f3f3;
  }
}

::v-deep .el-dialog__header {
  border-bottom: 2px solid #f3f3f3;
}

.invoice-apply-right-icon {
  font-size: 40px !important;
}

.invoice-apply-status-name {
  display: block;
  font-weight: bold;
  font-size: 16px;
  margin-top: 5px;

}

.nodata {
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-img {
  .desc {
    color: #7d7d7d;
    font-size: 14px;
  }
}

.annex {
  color: #7d7d7d;
  font-size: 14px;
  padding-top: 15px;
}

.recDetail-box {
  padding-bottom: 168px !important;
}
</style>
