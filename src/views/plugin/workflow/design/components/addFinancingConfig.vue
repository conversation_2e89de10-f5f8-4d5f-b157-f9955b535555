<template>
  <div class="add-financing-config">
    <el-dialog
      :visible.sync="configVisible"
      width="70%"
      @close="close"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="融资端节点配置"
    >
      <div class="content-box">
        <avue-form
          ref="configForm"
          v-model="configData"
          v-loading="configLoading"
          :option="configOption"
        >
          <template slot-scope="{ row, size }" slot="myMenu">
            <el-button
              type="text"
              icon="el-icon-delete"
              :size="size"
              @click="handleDelete(row)"
            >
              删 除
            </el-button>
          </template>
          <template slot-scope="{ row, size }" slot="name">
            <el-input
              v-model="row.name"
              :size="size"
              clearable
              :disabled="row.isAbsolutely"
              placeholder="请输入节点名称"
            ></el-input>
          </template>
          <template slot-scope="{ row, size, dic }" slot="nodeType">
            <el-select
              v-model="row.nodeType"
              :size="size"
              clearable
              :disabled="row.isAbsolutely"
              placeholder="请选择节点类型"
            >
              <el-option
                v-for="item in dic"
                :disabled="item.disabled"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
          <template slot-scope="{ row, size, dic }" slot="tableKey">
            <el-select
              v-model="row.tableKey"
              :size="size"
              clearable
              :disabled="row.isAbsolutely"
              placeholder="请选择表单key"
            >
              <el-option
                v-for="item in dic"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
          <template slot-scope="{ row, size, dic }" slot="taskNodeAssign">
            <el-select
              v-model="row.taskNodeAssign"
              :size="size"
              clearable
              multiple
              :disabled="row.isAbsolutely"
              placeholder="请选择节点类型"
            >
              <el-option
                v-for="item in dic"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
          <template slot-scope="{ row, size }" slot="keyName">
            <el-input
              v-model="row.keyName"
              :size="size"
              clearable
              :disabled="row.isAbsolutely"
              placeholder="请输入key名称"
            ></el-input>
          </template>
          <template slot-scope="{ row, size }" slot="izCommit">
            <el-switch
              v-model="row.izCommit"
              :active-value="1"
              :inactive-value="0"
              :disabled="[3, 4, 5].includes(row.nodeType)"
            ></el-switch>
          </template>
          <template slot-scope="{ row, size, index }" slot="taskListener">
            <div>
              <el-tooltip
                effect="dark"
                :content="row.taskListener"
                :disabled="!row.taskListener"
                placement="top"
              >
                <el-input
                  :value="row.taskListener"
                  :size="size"
                  placeholder="请输入监听表达式"
                  @focus="focusFun(row, index)"
                ></el-input>
              </el-tooltip>
              <el-dialog
                width="30%"
                title="监听表达式填写"
                :visible.sync="innerVisible"
                :modal="false"
              >
                <div>若设置多个请换行进行分割</div>
                <el-input
                  :autosize="{ minRows: 2 }"
                  autofocus
                  type="textarea"
                  placeholder="请输入监听表达式"
                  :validate-event="false"
                  v-model="innerTextarea"
                >
                </el-input>
                <span slot="footer" class="dialog-footer">
                  <el-button @click="innerVisible = false">取 消</el-button>
                  <el-button type="primary" @click="fuzhi">确 定</el-button>
                </span>
              </el-dialog>
            </div>
          </template>
        </avue-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="configVisible = false">取 消</el-button>
        <el-button type="primary" :loading="sBtnLoading" @click="handleC">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  sourceList,
  actExFormBaseSource,
  sourceUserTask,
  actExFormSubmit,
  actExFormDetail,
} from '@/api/plugin/workflow/model'
export default {
  name: 'addFinancingConfig',
  props: {
    configVisible: {
      type: Boolean,
      required: true,
      default: false,
    },
    listData: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      configData: {},
      configOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 0,
        column: [
          {
            prop: 'configsDy',
            type: 'dynamic',
            span: 24,
            children: {
              // align: 'center',
              // headerAlign: 'center',
              delBtn: false,
              column: [],
              // rowDel: (row, done) => {
              //   // if (row.isAbsolutely) {
              //   //   this.$message.warning('基础页面不允许删除')
              //   //   return
              //   // }
              //   done()
              // },
            },
          },
        ],
      },
      configLoading: false,
      sBtnLoading: false,
      innerVisible: false,
      innerTextarea: '',
      rowIndex: null,
    }
  },
  watch: {
    listData: {
      handler(newV) {
        if (newV.categoryId) {
          // 查流程类型下拉
          this.sourceListFun(newV.categoryId)
        }
        if (newV.modelKey) {
          // 根据模型key查询任务节点信息
          this.sourceUserTaskFun(newV.modelKey)
        }
        if (newV.id) {
          // 详情
          this.gong(newV)
          this.configLoading = true
        }
      },
    },
  },
  created() {
    this.configOption.column[0].children.column = [
      {
        label: '节点名称',
        prop: 'name',
        rules: [
          {
            required: true,
            message: '请输入节点名称',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '节点类型',
        prop: 'nodeType',
        type: 'select',
        dicData: [
          {
            label: '基础页面',
            value: 1,
            disabled: true,
          },
          {
            label: '审批页',
            value: 4,
            disabled: true,
          },
          {
            label: '完成页',
            value: 5,
            disabled: true,
          },
          {
            label: '附加页面',
            value: 2,
          },
          {
            label: '插槽',
            value: 3,
          },
        ],
        rules: [
          {
            required: true,
            message: '请选择节点类型',
            trigger: 'change',
          },
        ],
      },
      {
        label: '节点进度',
        prop: 'progress',
        rules: [
          {
            required: true,
            message: '请输入节点进度',
            trigger: 'blur',
          },
        ],
      },
      {
        label: 'key名称',
        prop: 'keyName',
        rules: [
          {
            required: true,
            message: '请输入key名称',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '表单key',
        prop: 'tableKey',
        type: 'select',
        dicData: [],
        rules: [
          {
            required: true,
            message: '请选择表单key',
            trigger: 'change',
          },
        ],
      },
      {
        label: '监听表达式',
        prop: 'taskListener',
        rules: [
          {
            required: false,
            message: '请输入监听表达式',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '指定审批节点',
        prop: 'taskNodeAssign',
        type: 'select',
        multiple: true,
        dataType: 'string',
        dicData: [],
        rules: [
          {
            required: false,
            message: '请选择指定审批节点',
            trigger: 'change',
          },
        ],
      },
      {
        label: '是否为提交节点',
        prop: 'izCommit',
        type: 'switch',
        value: 0,
        dicData: [
          {
            label: '',
            value: 0,
          },
          {
            label: '',
            value: 1,
          },
        ],
        rules: [
          {
            required: false,
            message: '请选择是否为提交节点',
            trigger: 'change',
          },
        ],
      },
      {
        label: '操作',
        prop: 'myMenu',
        width: 70,
      },
    ]
  },
  methods: {
    // 删除行数据
    handleDelete(row) {
      if (row.necessary) {
        this.$message.warning('该节点不允许删除')
        return
      }
      this.configData.configsDy.splice(row.$index, 1)
    },
    // 临时存值
    focusFun(row, index) {
      this.innerTextarea = row.taskListener
      this.rowIndex = index
      this.innerVisible = true
    },
    // 赋值
    fuzhi() {
      this.configData.configsDy[this.rowIndex].taskListener = this.innerTextarea
      this.innerVisible = false
      this.innerTextarea = ''
    },
    // 根据模型key查询任务节点信息
    sourceUserTaskFun(mId) {
      sourceUserTask({
        modelKey: mId,
      }).then(({ data }) => {
        const { data: resData } = data
        if (data.success) {
          const arrL = []
          for (const item of resData) {
            arrL.push({
              label: item.name,
              value: item.id,
            })
          }
          const taskNodeAssign = this.findObject(
            this.configOption.column[0].children.column,
            'taskNodeAssign'
          )
          taskNodeAssign.dicData = arrL
        }
      })
    },
    // 请求流程类型下拉数据
    sourceListFun(cId) {
      const paramsP = {
        categoryId: cId,
        fromType: '2', // 表单类型 1原始表单 2pro表单
      }
      sourceList(paramsP).then(({ data }) => {
        const { data: resData } = data
        if (data.success) {
          const arrL = []
          for (const item of resData) {
            arrL.push({
              label: item.name,
              value: `${item.name}_@_${item.formKey}`,
            })
          }
          const tableKey = this.findObject(
            this.configOption.column[0].children.column,
            'tableKey'
          )
          tableKey.dicData = arrL
        }
      })
    },
    // 共有请求
    async gong(obj) {
      // 重置数据
      this.configData = {
        configsDy: [],
      }
      const arrD = []
      // 基础页面数据源
      await actExFormBaseSource({ processTypeId: obj.categoryId }).then(
        ({ data }) => {
          const { data: resData } = data
          if (data.success) {
            for (const item of resData) {
              arrD.push({
                name: item.name,
                keyName: item.baseKey,
                nodeType: Number(item.baseType),
                taskListener: item.baseTaskListener,
                baseTaskListener: item.baseTaskListener,
                necessary: item.necessary, // 是否允许删除
                isAbsolutely: true, // 标记改行不能编辑性质
                tableKey: '无需编辑',
              })
            }
          }
        }
      )
      // 详情
      await actExFormDetail({ modelKeyId: obj.id }).then(({ data }) => {
        const { data: resData } = data
        this.configLoading = false
        if (data.success && resData.length) {
          for (let item of resData) {
            const fArr = arrD.findIndex(fitem => fitem.keyName === item.keyName)
            if (fArr !== -1) {
              delete arrD[fArr].taskListener
              item = Object.assign({}, item, arrD[fArr])
              arrD.splice(fArr, 1)
            }
            let taskNodeAssignArr = []
            if (item.taskNodeAssign) {
              taskNodeAssignArr = item.taskNodeAssign.split(',')
            }
            arrD.push({
              name: item.name,
              progress: item.progress,
              tableKey: item.isAbsolutely
                ? item.tableKey
                : `${item.tableName}_@_${item.tableKey}`,
              keyName: item.keyName,
              nodeType: item.nodeType,
              izCommit: item.izCommit,
              taskListener: item.taskListener || item.baseTaskListener,
              necessary: item.necessary, // 是否允许删除
              isAbsolutely: item.isAbsolutely, // 标记改行不能编辑性质
              taskNodeAssign: taskNodeAssignArr,
            })
          }
        }
        this.configData = {
          configsDy: arrD,
        }
      })
    },
    // 确认并校验事件
    handleC() {
      if (this.configLoading) {
        this.$message.warning('数据加载中')
        return
      }
      this.$refs.configForm.validate((valid, done, errObj) => {
        done()
        if (valid) {
          this.saveFun()
          return
        }
        // 提示用户缺失的未填写信息
        for (const key in errObj) {
          for (const keys in errObj[key]) {
            if (errObj[key][keys]) {
              this.$message.warning(errObj[key][keys][0].message)
              break
            }
          }
        }
      })
    },
    // 保存
    saveFun() {
      const arrD = []
      for (const key in this.configData) {
        for (const item of this.configData[key]) {
          let taskNodeAssignStr = ''
          if (item.taskNodeAssign.length) {
            taskNodeAssignStr = item.taskNodeAssign.join()
          }
          let tableArr = []
          if (item.isAbsolutely) {
            tableArr = ['无需编辑', 'kong']
          } else {
            tableArr = item.tableKey.split('_@_')
          }
          const [tableName, tableKey] = tableArr
          arrD.push({
            name: item.name,
            progress: item.progress,
            tableName,
            tableKey,
            keyName: item.keyName,
            nodeType: item.nodeType,
            izCommit: item.izCommit,
            taskListener: item.taskListener,
            modelKeyId: this.listData.id,
            taskNodeAssign: taskNodeAssignStr,
            processTypeId: this.listData.categoryId,
          })
        }
      }
      if (!arrD.length) {
        this.$message.warning('请添加数据')
        return
      }
      this.sBtnLoading = true
      const dataP = {
        actExForm: arrD,
        modelKeyId: this.listData.id,
      }
      actExFormSubmit(dataP)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('保存成功')
            this.configVisible = false
            this.sBtnLoading = false
            this.reset()
          }
        })
        .catch(() => {
          this.sBtnLoading = false
        })
    },
    // 弹窗关闭回调
    close() {
      this.$emit('configClose')
    },
    // 重置父组件数据回调
    reset() {
      this.$emit('configReset')
    },
  },
}
</script>

<style lang="scss" scoped>
.add-financing-config {
  .content-box {
    ::v-deep {
      .avue-form__menu {
        display: none;
      }
      .el-input.is-disabled .el-input__inner {
        color: #000;
      }
    }
  }
}
</style>
