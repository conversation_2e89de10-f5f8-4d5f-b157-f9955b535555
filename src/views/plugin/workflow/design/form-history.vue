<template>
  <basic-container>
    <el-container>
      <el-aside width="200px">
        <avue-tree :option="treeOption"
                   :data="treeData"
                   @node-click="nodeClick"></avue-tree>
      </el-aside>
      <el-main style="margin-left: 10px;">
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page.sync="page"
                   :search.sync="query"
                   v-model="form"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @refresh-change="onLoad(page, query)">
          <template slot="menuLeft">
            <el-button type="danger"
                       size="mini"
                       icon="el-icon-delete"
                       plain
                       v-if="permission.wf_design_form_history_delete"
                       @click="handleDelete">删 除
            </el-button>
          </template>
          <template slot="menu"
                    slot-scope="{row}">
            <el-button v-if="permission.wf_design_form_history_view"
                       type="text"
                       size="mini"
                       icon="el-icon-view"
                       @click="handlePreview(row)">预览</el-button>
            <el-button v-if="permission.wf_design_form_history_main"
                       type="text"
                       size="mini"
                       icon="el-icon-edit"
                       @click="handleMain(row)">设为主版本</el-button>
          </template>
        </avue-crud>
      </el-main>
    </el-container>

    <el-drawer :visible.sync="viewVisible"
               title="表单预览"
               size="50%"
               append-to-body>
      <template v-if="formType === 'avueF'">
        <avue-form v-if="viewVisible"
                  style="height: 100%; overflow: auto;"
                  ref="viewForm"
                  v-model="viewForm"
                  :option="viewOption">
        </avue-form>
      </template>
      <template v-else-if="formType === 'VForm'">
        <div class="VForm-show-box">
          <v-form-render :form-json="formJson" ref="VFormRender">
          </v-form-render>
        </div>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script>
import { getList, remove, setMainVersion } from "@/api/plugin/workflow/form-history";
import { tree } from '@/api/plugin/workflow/category';

import { mapGetters } from "vuex";

export default {
  data() {
    return {
      formVisible: false,
      options: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        size: 'mini',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        selection: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        dialogType: 'drawer',
        align: 'center',
        searchMenuSpan: 6,
        column: [
          {
            label: "表单key",
            prop: "formKey",
            search: true
          },
          {
            label: "表单名称",
            prop: "name",
            search: true
          },
          {
            label: '分类',
            prop: 'categoryId',
            type: 'tree',
            props: {
              label: 'name',
              value: 'id'
            },
            dicData: [],
            rules: [{
              required: true,
              message: "请选择分类",
              trigger: "change"
            }],
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            span: 24
          },
          {
            label: '版本',
            prop: 'version',
            display: false
          },
        ]
      },
      data: [],
      viewVisible: false,
      viewForm: {},
      viewOption: {},
      treeData: [],
      treeOption: {
        size: 'mini',
        addBtn: false,
        props: {
          label: 'name',
          value: 'id'
        }
      },
      formType: '', // 预览弹窗表单判断条件
      formJson: {}, // VForm预览
    };
  },
  watch: {
    '$route.params.id': {
      handler(val) {
        this.formId = val
        this.onLoad(this.page, this.query)
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters(["permission", "tag"]),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  mounted() {
    this.getCategoryList()
  },
  methods: {
    getCategoryList() {
      tree().then(res => {
        const data = res.data.data
        this.findObject(this.option.column, 'categoryId').dicData = this.deepClone(data)

        this.treeData = data
        this.treeData.unshift({ id: '', name: '全部' })
      })
    },
    nodeClick({ id }) {
      this.categoryId = id
      this.searchChange(this.query)
    },
    handleMain(row) {
      this.$confirm("当前主版本会自动保存到历史，确定要将此版本设为主版本吗？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        setMainVersion({ id: row.id }).then(() => {
          this.$message.success("操作成功")
          this.$store.commit('DEL_TAG', this.tag)
          this.$router.push("/plugin/workflow/design/form")
        })
      })
    },
    handlePreview(row) {
      if (row.fromType === 2) {
        this.formJson = JSON.parse(row.content)
        this.formType = 'VForm'
        this.viewVisible = true
        return
      }
      this.viewOption = { ...eval('(' + row.content + ')'), menuBtn: false }
      this.formType = 'avueF'
      this.viewVisible = true
      setTimeout(() => {
        this.$refs.viewForm.clearValidate()
      })
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      if (done) done()
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page, this.query);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.formId = this.formId

      if (this.categoryId) params['categoryId'] = this.categoryId
      else delete params['categoryId']

      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.VForm-show-box {
  margin: 0 25px;
}
</style>
