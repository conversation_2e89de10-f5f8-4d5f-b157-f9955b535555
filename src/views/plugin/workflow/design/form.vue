<template>
  <basic-container>
    <el-container>
      <el-aside width="200px">
        <avue-tree :option="treeOption"
                   :data="treeData"
                   @node-click="nodeClick"></avue-tree>
      </el-aside>
      <el-main style="margin-left: 10px;">
        <avue-crud ref="crud"
                   :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page.sync="page"
                   :search.sync="query"
                   :permission="permissionList"
                   v-model="form"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @refresh-change="onLoad(page, query)"
                   @on-load="onLoad">
          <template #menuLeft>
            <el-button type="success"
                       size="mini"
                       icon="el-icon-connection"
                       v-if="permission.wf_design_model_change_category"
                       @click="handleChangeCategory"> 更改分类
            </el-button>
          </template>
          <template slot="menu"
                    slot-scope="{row}">
            <el-button v-if="permission.wf_design_form_design"
                       type="text"
                       size="mini"
                       icon="el-icon-edit"
                       @click="handleDesign(row)">设计</el-button>
            <el-button v-if="permission.wf_design_form_copy"
                       type="text"
                       size="mini"
                       icon="el-icon-document-copy"
                       @click="handleCopy(row)">拷贝</el-button>
            <el-button v-if="permission.wf_design_form_history"
                       type="text"
                       size="mini"
                       icon="el-icon-time"
                       @click="handleHistory(row)">历史</el-button>
          </template>
        </avue-crud>
      </el-main>
    </el-container>
    <el-dialog :visible.sync="formVisible"
               append-to-body
               @opened="openFun"
               :title="title"
               fullscreen>
      <template v-if="formType === 'avueF'">
        <avue-form-design v-if="formVisible"
                          style="height: 88vh"
                          ref="formDesign"
                          :toolbar="['clear', 'preview', 'import', 'generate']"
                          :includeFields="['group', 'dynamic', 'input', 'textarea', 'number', 'map', 'radio','checkbox','select','tree','cascader', 'date','time','datetime','daterange','datetimerange','timerange','switch','rate','color','icon','slider']"
                          :customFields="customFields"
                          :default-values="defaultValues"
                          :options="options">
          <template #toolbar>
            <el-button type="text"
                      size="medium"
                      icon="el-icon-download"
                      @click="handleSubmit">保存</el-button>
          </template>
        </avue-form-design>
      </template>
      <template v-else-if="formType === 'VForm'">
        <v-form-designer ref="VFormDesigner" :designer-config="designerConfig">
          <!-- 自定义按钮插槽演示 -->
          <template #customToolButtons>
            <el-button type="text" @click="handleSubmitNew"><i class="el-icon-download" />保存</el-button>
          </template>
        </v-form-designer>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="copyVisible"
               append-to-body
               title="拷贝表单">
      <avue-form :option="copyOption"
                 v-model="form"
                 ref="copyForm"
                 @submit="handleCopySubmit">
      </avue-form>
    </el-dialog>

    <el-dialog :visible.sync="categoryVisible"
               append-to-body
               title="选择分类">
      <avue-form v-model="form"
                 :option="{column:[{type:'tree',label:'流程分类',span:24,props:{label:'name',value:'id'},prop:'category',dicUrl:'/api/blade-workflow/design/category/tree',required:true,rules:[{required:true,message:'请选择分类'}]}]}"
                 @submit="handleChangeCategorySubmit"></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getList, add, update, remove, listType, changeCategory } from "@/api/plugin/workflow/form";
import { tree } from '@/api/plugin/workflow/category';

import { mapGetters } from "vuex";

import customFields from '../mixins/custom-fields'

export default {
  mixins: [customFields],
  data() {
    return {
      formVisible: false,
      options: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        menuWidth: 350,
        size: 'mini',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        selection: true,
        dialogType: 'drawer',
        align: 'center',
        searchMenuSpan: 6,
        column: [
          {
            label: "表单key",
            prop: "formKey",
            rules: [{
              required: true,
              message: "请输入表单key",
              trigger: "blur"
            }],
            search: true
          },
          {
            label: "表单名称",
            prop: "name",
            rules: [{
              required: true,
              message: "请输入表单名称",
              trigger: "blur"
            }],
            search: true
          },
          {
            label: '表单分类',
            prop: 'fromType',
            type: 'select',
            dicData: [
              {
                label: 'avue',
                value: 1,
              },
              {
                label: 'vform',
                value: 2,
              },
            ],
            rules: [{
              required: true,
              message: "请选择表单分类",
              trigger: "change"
            }],
          },
          {
            label: '流程分类',
            prop: 'categoryId',
            type: 'tree',
            props: {
              label: 'name',
              value: 'id'
            },
            dicData: [],
            rules: [{
              required: true,
              message: "请选择流程分类",
              trigger: "change"
            }],
          },
          {
            label: '版本',
            prop: 'version',
            display: false
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            value: 1,
            dicData: [{
              label: '可用',
              value: 1
            }, {
              label: '禁用',
              value: 2
            }],
            rules: [{
              required: true,
              message: "请选择状态",
              trigger: "change"
            }],
            search: true
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            span: 24
          },
        ]
      },
      data: [],
      defaultValues: {},
      copyOption: {
        column: [{
          label: "表单key",
          prop: "formKey",
          rules: [{
            required: true,
            message: "请输入表单key",
            trigger: "blur"
          }],
        },
        {
          label: "表单名称",
          prop: "name",
          rules: [{
            required: true,
            message: "请输入表单名称",
            trigger: "blur"
          }],
        },
        {
          label: '流程分类',
          prop: 'categoryId',
          type: 'tree',
          props: {
            label: 'name',
            value: 'id'
          },
          dicData: [],
          rules: [{
            required: true,
            message: "请选择流程分类",
            trigger: "change"
          }],
        },
        {
          label: '状态',
          prop: 'status',
          type: 'select',
          value: 1,
          dicData: [{
            label: '可用',
            value: 1
          }, {
            label: '禁用',
            value: 2
          }],
          rules: [{
            required: true,
            message: "请选择状态",
            trigger: "change"
          }],
          search: true
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24
        },]
      },
      isNewVersion: false,
      isCopy: false,
      copyVisible: false,
      categoryVisible: false,
      treeData: [],
      treeOption: {
        size: 'mini',
        addBtn: false,
        props: {
          label: 'name',
          value: 'id'
        }
      },
      title: '表单设计',
      formType: '', // 设计弹窗表单设计器判断条件
      designerConfig: { // VForm 头部按钮显影控制
        languageMenu: false,
        externalLink: false,
        //是否显示导入JSON按钮
        importJsonButton: true,
        //是否显示导出JSON器按钮
        exportJsonButton: true,
        //是否显示导出代码按钮
        exportCodeButton: false,
        //是否显示生成SFC按钮
        generateSFCButton: false,
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.wf_design_form_add, false),
        viewBtn: this.vaildData(this.permission.wf_design_form_view, false),
        delBtn: this.vaildData(this.permission.wf_design_form_delete, false),
        editBtn: this.vaildData(this.permission.wf_design_form_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  mounted() {
    this.getDefaultValues()
    this.getCategoryList()
  },
  methods: {
    handleChangeCategorySubmit(form, done) {
      const { category } = form
      changeCategory({ ids: this.ids, category }).then(() => {
        this.$message.success('修改成功')
        done()
        this.categoryVisible = false
        this.onLoad(this.page, this.query)
      })
    },
    handleChangeCategory() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.categoryVisible = true
    },
    getCategoryList() {
      tree().then(res => {
        const data = res.data.data
        this.findObject(this.option.column, 'categoryId').dicData = this.deepClone(data)
        this.findObject(this.copyOption.column, 'categoryId').dicData = this.deepClone(data)

        this.treeData = data
        this.treeData.unshift({ id: '', name: '全部' })
      })
    },
    nodeClick({ id }) {
      this.categoryId = id
      this.searchChange(this.query)
    },
    handleSubmit() {
      this.$refs.formDesign.getData('string').then(data => {
        this.$refs.formDesign.getData('app').then(appData => {
          if (this.isCopy) {
          this.copyVisible = true
          this.form.content = data
          this.form.appContent = JSON.stringify(appData)
        } else {
          this.row.content = data
          this.row.appContent = JSON.stringify(appData)

          if (this.isNewVersion) {
            this.row.newVersion = false

            update(this.row).then(() => {
              this.$message.success("保存成功")
              this.onLoad(this.page, this.query)
              this.formVisible = false
            })
          } else {
            this.$confirm('是否将此表单保存为新版本？这意味着可以返回到以前的版本。', '提示', {
              distinguishCancelAndClose: true,
              confirmButtonText: '否',
              cancelButtonText: '是',
              type: 'warning'
            }).then(() => {
              this.row.newVersion = false

              update(this.row).then(() => {
                this.$message.success("保存成功")
                this.onLoad(this.page, this.query)
                this.formVisible = false
              })
            }).catch(action => {
              if (action == 'cancel') {
                this.row.newVersion = true

                update(this.row).then(() => {
                  this.$message.success("保存成功")
                  this.onLoad(this.page, this.query)
                  this.formVisible = false
                })
              }
            })
          }
        }
        })
      })
    },
    handleDesign(row) {
      // VForm or avueF
      if (row.fromType === 2) {
        this.options = this.deepClone(row.content || '')
        if (row.content) this.isNewVersion = false
        else this.isNewVersion = true
        this.row = row
        this.isCopy = false
        this.title = `表单设计 - ${row.name}`
        this.formType = 'VForm'
        this.formVisible = true
        return
      }
      this.options = this.deepClone(row.content || '{column: []}')
      if (row.content) this.isNewVersion = false
      else this.isNewVersion = true
      this.row = row
      this.isCopy = false
      this.title = `表单设计 - ${row.name}`
      this.formType = 'avueF'
      this.formVisible = true
    },
    handleCopy(row) {
      // VForm or avueF
      if (row.fromType === 2) {
        this.options = this.deepClone(row.content || '')
        this.isCopy = true
        this.row = row
        this.formType = 'VForm'
        this.formVisible = true
        return
      }
        this.options = this.deepClone(row.content || '{column: []}')
        this.isCopy = true
        this.row = row
        this.formType = 'avueF'
        this.formVisible = true
    },
    handleCopySubmit(form, done) {
      const param = {
        ...form,
        content: this.form.content,
        fromType: this.row.fromType,
      }
      add(param).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        this.$refs.copyForm.resetFields()
        done()
        this.copyVisible = false
        this.formVisible = false
      }).catch(() => {
        done()
      })
    },
    // VForm画布初始化
    openFun() {
      if (!this.formType || this.formType === 'avueF') return
      // 新数据清空设计面板
      if (!this.options) {
        this.$refs.VFormDesigner.clearDesigner()
        return
      }
      this.$refs.VFormDesigner.setFormJson(this.options)
    },
    // VForm保存数据
    handleSubmitNew() {
      const getJson = this.$refs.VFormDesigner.getFormJson()
      if (!getJson.widgetList.length) {
        this.$message.warning('请设计表单后再进行保存')
        return
      }
      const stringTo = JSON.stringify(getJson)
      if (this.isCopy) {
      this.copyVisible = true
      this.form.content = stringTo
    } else {
      this.row.content = stringTo

      if (this.isNewVersion) {
        this.row.newVersion = false

        update(this.row).then(() => {
          this.$message.success("保存成功")
          this.onLoad(this.page, this.query)
          this.formVisible = false
        })
      } else {
        this.$confirm('是否将此表单保存为新版本？这意味着可以返回到以前的版本。', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '否',
          cancelButtonText: '是',
          type: 'warning'
        }).then(() => {
          this.row.newVersion = false

          update(this.row).then(() => {
            this.$message.success("保存成功")
            this.onLoad(this.page, this.query)
            this.formVisible = false
          })
        }).catch(action => {
          if (action == 'cancel') {
            this.row.newVersion = true

            update(this.row).then(() => {
              this.$message.success("保存成功")
              this.onLoad(this.page, this.query)
              this.formVisible = false
            })
          }
        })
      }
    }
    },
    handleHistory(row) {
      this.$router.push('/workflow/design/form/history/' + row.id)
    },
    getDefaultValues() {
      listType().then(res => {
        this.defaultValues = res.data.data
      })
    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("删除全部版本或者回退到最后版本?", {
        distinguishCancelAndClose: true,
        confirmButtonText: "回退",
        cancelButtonText: "全部删除",
        type: "warning"
      }).then(() => {
        const param = {
          id: row.id,
          rollback: true
        }
        remove(param).then(() => {
          this.onLoad(this.page);
          this.$message.success("操作成功")
        })
      }).catch(action => {
        if (action == 'cancel') {
          const param = {
            id: row.id,
            rollback: false
          }
          remove(param).then(() => {
            this.onLoad(this.page);
            this.$message.success("操作成功")
          })
        }
      })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      if (done && typeof done == 'function') done()
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;

      if (this.categoryId) params['categoryId_equal'] = this.categoryId
      else delete params['categoryId_equal']

      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    }
  }
};
</script>

<style>
</style>
