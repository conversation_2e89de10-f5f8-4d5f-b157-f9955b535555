// import defaultValues from './default-values'
// import { mapGetters } from 'vuex'
import('/src/views/plugin/workflow/styles/theme/default.scss')

export default {
  // mixins: [defaultValues],
  // computed: {
  //   ...mapGetters(['tag', 'userInfo', 'permission']),
  // },
  data() {
    return {
      inlayFormShow: false,
      indepFormKey: '', // 内置表单key
      inlayProcess: {},
      inlayOption: {},
      inlayForm: {},
      inlayDefaults: {},
      inlayVars: [], // 内置表单需要提交的字段
      inlayVariables: {}, // 内置表单用户填写数据存储
      // process: {}, // 流程定义/流程实例信息
      // buttonList: [], // 配置按钮信息
      // flow: [], // 流转信息
      // userSelectType: '', // 人员选择类型 transfer转办 delegate委托 copy抄送 assignee审核人
      // checkType: 'radio', // 人员选择check类型 radio单选 checkbox多选
      // comment: '', // 评论
      // bpmnOption: {}, // 流程图配置信息
      // defaultChecked: '', // 人员选择默认选中
      // waiting: true, // 骨架屏加载中
    }
  },
  methods: {
    handleResolveOption(option, taskForm, status) {
      const { column, group } = option
      let vars = []
      if (status != 'todo') {
        // 已办，删除字段默认值
        let event = ['change', 'blur', 'click', 'focus']
        option.detail = true
        if (column && column.length > 0) {
          // 处理column
          column.forEach(col => {
            if (col.type == 'dynamic')
              col.children.column.forEach(cc => {
                delete cc.value
                delete cc.event
                event.forEach(e => delete cc[e])
              })
            else {
              delete col.value
              delete col.event
              event.forEach(e => delete col[e])
            }
          })
        }

        if (group && group.length > 0) {
          // 处理group
          group.forEach(gro => {
            if (gro.column && gro.column.length > 0) {
              gro.column.forEach(col => {
                if (col.type == 'dynamic')
                  col.children.column.forEach(cc => {
                    delete cc.value
                    delete cc.event
                    event.forEach(e => delete cc[e])
                  })
                else {
                  delete col.value
                  delete col.event
                  event.forEach(e => delete col[e])
                }
              })
            }
          })
        }
      } else {
        const columnFilter = this.filterAvueColumn(column, taskForm)
        const columnArr = columnFilter.column
        vars = columnFilter.vars || []

        const groupArr = []
        if (group && group.length > 0) {
          // 处理group
          group.forEach(gro => {
            const groupFilter = this.filterAvueColumn(gro.column, taskForm)
            gro.column = groupFilter.column
            vars = vars.concat(groupFilter.vars)
            if (gro.column.length > 0) groupArr.push(gro)
          })
        }

        if (process.variables && process.variables.serialNumber) {
          columnArr.unshift({
            label: '流水号',
            prop: 'serialNumber',
            span: 24,
            detail: true,
          })
        }
        option.column = columnArr
        option.group = groupArr
      }
      return { option, vars }
    },
    // 根据可读可写，过滤avue column
    filterAvueColumn(
      column,
      taskForm,
      isExForm = false,
      props = { label: 'label', prop: 'prop' }
    ) {
      const _this = this

      if (!column || column.length == 0) return { column, vars: [] }

      const values = []
      const vars = []
      column.forEach(col => {
        let c = taskForm.find(s => s.id == col[props.prop])
        if (c && c.readable) {
          // /**
          //  * @deprecated 与节点配置可读可写冲突
          //  */
          // if (!c) { // 未重新点击节点设计表单字段可读可写。
          //   if ((this.process.isOwner && this.process.status == 'todo') || !this.process.hasOwnProperty('isOwner')) c = { readable: true, writable: true }
          //   else c = { readable: true, writable: false }
          // }
          let event = ['change', 'blur', 'click', 'focus']
          if (c.writable || c.writeable) {
            // 可写，记录需要提交的字段、处理字段默认值
            vars.push(col[props.prop])
            if (col.value) col.value = _this.getDefaultValues(col.value)

            if (!isExForm) {
              // 非外置表单 处理事件
              event.forEach(e => {
                if (col[e])
                  col[e] = eval((col[e] + '').replace(/this/g, '_this'))
              })
              if (col.event)
                Object.keys(col.event).forEach(
                  key =>
                  (col.event[key] = eval(
                    (col.event[key] + '').replace(/this/g, '_this')
                  ))
                )
            }
          } else {
            // 不可写，清除校验、默认值、事件
            if (col.type == 'dynamic') {
              col.children.addBtn = false
              col.children.delBtn = false
            } else {
              col.readonly = true
              col.disabled = true
            }
            delete col.rules
            delete col.value
            delete col.event
            event.forEach(e => delete col[e])
          }
          if (col.type == 'dynamic') {
            // 处理子表单
            col.children.column = _this.filterAvueColumn(
              col.children.column,
              taskForm
            ).column
          }
          if (col.rules && col.pattern) {
            // 处理正则
            col.rules.forEach(c => {
              if (c.pattern) c.pattern = new RegExp(col.pattern)
            })
          }

          values.push(col)
        }
      })
      return { column: values, vars }
    },
    // 关闭当前tag，并跳转
    handleCloseTag(path) {
      this.$store.commit('DEL_TAG', this.tag)
      if (path) this.$router.push(path)
    },
    // 上传组件预览
    handleUploadPreview(file, column, done) {
      const { url } = file
      const { video, img } = this.$typeList
      if (video.test(url) || img.test(url)) done()
      else window.open(url)
    },
    // 内置表单必填校验
    handleInlayVerify(pass) {
      // 内置表单不存在直接调用通过方法
      if (!this.$refs.inlayFormRef) {
        if (this.handleExamine) {
          this.handleExamine(pass)
          return
        }
        this.$message.warning('通过事件的调用函数不存在')
        return
      } else if (!pass) {
        this.handleExamine(pass)
      }
      this.$refs.inlayFormRef.validate((valid, done, msg) => {
        if (valid) {
          const variables = {}
          this.inlayVars.forEach(v => {
            if (v != 'comment' && this.inlayForm[v]) variables[`${this.indepFormKey}@_@${v}`] = this.inlayForm[v]
          })
          this.inlayVariables = variables
          this.handleExamine(pass)
          done()
        } else {
          done()
          if (msg) {
            const key = Object.keys(msg)[0]
            const rules = msg[key]
            this.$message.error(rules.map(r => r.message).join(' | '))
          }
        }
      })
    },
  },
}
