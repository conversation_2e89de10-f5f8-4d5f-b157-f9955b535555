<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft"></template>
      <template
        slot-scope="{ row, type }"
        slot="menuForm"
      >
        <el-button
          type="primary"
          size="small"
          plain
          v-if="type == 'edit' || type == 'add'"
          @click="handleFormEnabled(row)"
        >启用
        </el-button>
      </template>
      <template
        slot-scope="{ row }"
        slot="menu"
      >
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.status == 0 && permission.norm_view"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.status == 0 && permission.norm_delete"
        >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          @click="enable(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
      <template
        slot-scope="{ row }"
        slot="status"
      >
        <el-tag
          type="success"
          v-if="row.status == 1"
        >已启用</el-tag>
        <el-tag
          type="info"
          v-if="row.status == 0"
        >已禁用</el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { add, batchDisabled, batchEnabled, getColumnAll, getDetail, getList, remove, update, } from '@/api/riskmana/norm'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      typeId: '',
      columnData: [],
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        optionData: [],
        delBtn: false,
        editBtn: false,
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '指标编号',
            search: true,
            prop: 'normNo',
            display: false,
          },
          {
            label: '指标名称',
            search: true,
            prop: 'name',
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入指标名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '参数格式',
            prop: 'paramType',
            display: false,
            type: 'select',
            search: true,
            dicData: [
              {
                label: '数字范围',
                value: 1
              },
              {
                label: '日期范围',
                value: 4
              },
              {
                label: '选项命中',
                value: 2
              }
            ],
          },
          {
            label: '业务字段',
            hide: true,
            prop: 'columnId',
            type: 'tree',
            dicUrl: `/api/blade-resource/web-back/resource/businessfield/listDetailsAllOrName?param=`,
            props: {
              label: 'indexName',
              value: 'id',
            },
            rules: [
              {
                required: true,
                message: '请选择业务字段',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '最高分数',
            prop: 'maxScore',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入最高分数',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上次修改时间',
            prop: 'updateTime',
            display: false,
          },
          {
            label: '操作人',
            prop: 'userName',
            display: false,
          },
          {
            label: '状态',
            prop: 'status',
            formslot: true,
            display: false,
          },
          {
            prop: 'moduleType',
            display: false,
            hide: true,
          },
          //得分规则子表单
          {
            label: '得分规则',
            prop: 'numChildren',
            hide: true,
            display: false,
            type: 'dynamic',
            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                done({
                  input: '默认值',
                })
              },
              rowDel: (row, done) => {
                done()
              },
              column: [
                {
                  width: 200,
                  label: '比较',
                  prop: 'compareSymbol',
                  dataType: 'string',
                  type: 'select',
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=compare_value',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  rules: [
                    {
                      required: true,
                      message: '请输入比较',
                      trigger: 'blur',
                    },
                  ],
                },
                // {
                //   label: '参数类型',
                //   prop: 'compareValueType',
                //   type: 'select',
                //   dicUrl:
                //     '/api/blade-system/dict-biz/dictionary?code=parameterType',
                //   props: {
                //     label: 'dictValue',
                //     value: 'dictKey',
                //   },
                //   rules: [
                //     {
                //       message: '请选择参数类型',
                //       required: true,
                //       trigger: 'blur',
                //     },
                //   ],
                // },
                {
                  label: '参数值',
                  prop: 'compareValue',
                  type: 'number',
                  rules: [
                    {
                      message: '请输入参数值',
                      required: true,
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '得分',
                  prop: 'score',
                  type: 'number',
                  minRows: 0,
                  maxRows: 100,
                  rules: [
                    {
                      message: '请输入得分',
                      required: true,
                      trigger: 'blur',
                    },
                  ],
                },
              ],
            },
          },

          //得分规则子表单
          {
            label: '得分规则',
            prop: 'dateTimeChildren',
            hide: true,
            display: false,
            type: 'dynamic',
            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                done({
                  input: '默认值',
                })
              },
              rowDel: (row, done) => {
                done()
              },
              column: [
                {
                  width: 200,
                  label: '比较',
                  prop: 'compareSymbol',
                  dataType: 'string',
                  type: 'select',
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=compare_value',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  rules: [
                    {
                      required: true,
                      message: '请选择比较',
                      trigger: 'blur',
                    },
                  ],
                },
                // {
                //   label: '参数类型',
                //   prop: 'compareValueType',
                //   type: 'select',
                //   dicUrl:
                //     '/api/blade-system/dict-biz/dictionary?code=parameterType',
                //   props: {
                //     label: 'dictValue',
                //     value: 'dictKey',
                //   },
                //   rules: [
                //     {
                //       message: '请选择参数类型',
                //       required: true,
                //       trigger: 'blur',
                //     },
                //   ],
                // },
                {
                  label: '参数值',
                  prop: 'compareValue',
                  dataType: 'string',
                  type: 'date',
                  format: 'yyyy-MM-dd hh:mm:ss',
                  valueFormat: 'yyyy-MM-dd hh:mm:ss',
                  rules: [
                    {
                      message: '请输入参数值',
                      required: true,
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '得分',
                  prop: 'score',
                  type: 'number',
                  minRows: 0,
                  maxRows: 100,
                  rules: [
                    {
                      message: '请输入得分',
                      required: true,
                      trigger: 'blur',
                    },
                  ],
                },
              ],
            },
          },
          //得分规则子表单
          {
            label: '得分规则',
            prop: 'optionChildren',
            display: false,
            hide: true,
            type: 'dynamic',
            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                done({
                  input: '默认值',
                })
              },
              rowDel: (row, done) => {
                done()
              },
              column: [
                {
                  width: 200,
                  label: '比较',
                  dataType: 'string',
                  prop: 'compareSymbol',
                  type: 'select',
                  dicData: [
                    {
                      label: '=',
                      value: 'eq',
                    },
                  ],
                  rules: [
                    {
                      required: true,
                      message: '请选择比较',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '参数值',
                  prop: 'compareValue',
                  type: 'select',
                  dicData: [],
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  dataType: 'string',
                  rules: [
                    {
                      message: '请选择选项',
                      required: true,
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '得分',
                  prop: 'score',
                  type: 'number',
                  minRows: 0,
                  maxRows: 100,
                  rules: [
                    {
                      message: '请输入得分',
                      required: true,
                      trigger: 'blur',
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.norm_add, false),
        viewBtn: this.vaildData(this.permission.norm_view, false),
        delBtn: this.vaildData(this.permission.norm_delete, false),
        editBtn: this.vaildData(this.permission.norm_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {
    'form.columnId': {
      handler(newValue) {
        if (newValue && newValue != '') {
          let columnData = this.columnData
          let find = columnData.find(e => {
            return e.id == newValue
          })
          if (find) {
            this.showChildrenForm(find)
          }
        }
      },
      deep: true,
      immediate: true,
    },
    'form.optionChildren': {
      handler(newValue) {
      },
      deep: true,
    },
    'form.dateTimeChildren': {
      handler(newValue) {
        console.log(this.form);
      },
      deep: true,
    },
    'form.numChildren': {
      handler(newValue) {
        console.log(this.form.numChildren)
      },
      deep: true,
    }
  },
  methods: {
    showChildrenForm(find) {
      const { tableName, fieldName } = find
      let paramType = find.dataType
      let url = tableName ? tableName + '_' + fieldName : fieldName
      this.form.moduleType = find.moduleType;
      if (paramType == 1 || paramType == 5 || paramType == 6) {
        this.form.paramType = 1
        this.showNumForm()
      } else if (paramType == 4) {
        this.form.paramType = 4
        this.showDateTimeForm()
      } else if (paramType == 2) {
        this.form.paramType = 2
        this.showOptionForm()
        this.$axios
          .get('/api/blade-system/dict-biz/dictionary?code=' + url)
          .then(res => {
            //找到选项子表单 的字典
            const optionChildren = this.findObject(
              this.option.column,
              'optionChildren'
            )
            optionChildren.children.column[1].dicData = res.data.data
          })
      }
    },
    getScoreRuleList(row) {
      //获取参数格式
      let paramType = this.form.paramType
      //根据参数格式决定对哪个子表单赋值
      if (paramType == 1) {
        row.scoreRuleList = this.form.numChildren
      } else if (paramType == 4) {
        row.scoreRuleList = this.form.dateTimeChildren
      } else if (paramType == 2) {
        row.scoreRuleList = this.form.optionChildren
      }
      return []
    },
    showNumForm() {
      let c1 = this.findObject(this.option.column, 'numChildren')
      let c2 = this.findObject(this.option.column, 'dateTimeChildren')
      let c3 = this.findObject(this.option.column, 'optionChildren')
      c1.display = true
      c2.display = false
      c3.display = false
    },
    showDateTimeForm() {
      let c1 = this.findObject(this.option.column, 'numChildren')
      let c2 = this.findObject(this.option.column, 'dateTimeChildren')
      let c3 = this.findObject(this.option.column, 'optionChildren')
      c1.display = false
      c2.display = true
      c3.display = false
    },
    showOptionForm() {
      let c1 = this.findObject(this.option.column, 'numChildren')
      let c2 = this.findObject(this.option.column, 'dateTimeChildren')
      let c3 = this.findObject(this.option.column, 'optionChildren')
      c1.display = false
      c2.display = false
      c3.display = true
    },
    enable(row) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
        this.enabledAndDisabled(msg, row)
      } else {
        this.$axios.get('/api/blade-riskmana/web-back/riskmana/norm/cascade/cascadeInfo?id=' + row.id).then(res => {
          msg = '确定禁用操作 ' + (res.data.data ? res.data.data : '')
        }
        ).then(() => {
          this.enabledAndDisabled(msg, row);
        })
      }

    }
    ,
    enabledAndDisabled(msg, row, done) {
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          batchDisabled(row.id).then(() => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          })
        } else {
          batchEnabled(row.id).then(() => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          })
        }
      })
    }
    ,
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    }
    ,
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    }
    ,
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    }
    ,
    handleFormEnabled() {
      this.form.status = 1
      this.$refs.crud.rowUpdate()
    }
    ,
    rowSave(row, done, loading) {
      this.getScoreRuleList(row)
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    }
    ,
    rowUpdate(row, index, done, loading) {
      this.getScoreRuleList(row)
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    }
    ,
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    }
    ,
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?' + res.data.data, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })

    }
    ,
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
          let paramType = this.form.paramType
          console.log(paramType)
          this.setScoreRuleList(paramType)
        })
      }
      done()
    }
    ,
    setScoreRuleList(paramType) {
      if (paramType == 1) {
        this.form.numChildren = this.form.scoreRuleList
      } else if (paramType == 4) {
        this.form.dateTimeChildren = this.form.scoreRuleList
      } else if (paramType == 2) {
        this.form.optionChildren = this.form.scoreRuleList
      }
    }
    ,
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    }
    ,
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    }
    ,
    selectionChange(list) {
      this.selectionList = list
    }
    ,
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    }
    ,
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    }
    ,
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    }
    ,
    refreshChange() {
      this.onLoad(this.page, this.query)
    }
    ,
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
      getColumnAll().then(res => {
        this.columnData = res.data
      })
    }
    ,
  },
}
</script>

<style></style>
