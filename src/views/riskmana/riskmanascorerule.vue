<template>
  <avue-crud :data="data" :option="option" v-model="form" @row-save="rowSave">
    <!-- <div v-if="slot"> -->
    <template slot="ruleForm">
      <div class="rule-box">
        <table class="rule-table">
          <tr>
            <th class="btn">
              <div class="xz" @click="newtab">
                <i class="el-icon-plus"></i>
              </div>
            </th>
            <th class="level">
              <div>信用级别</div>
            </th>
            <th class="compare">
              <div>比较</div>
            </th>
            <th class="type">
              <div>分数类型</div>
            </th>
            <th class="value">
              <div>分值</div>
            </th>
            <th class="quota">
              <div>风险额度</div>
            </th>
          </tr>
          <tr v-for="(item, index) in slot" :key="index">
            <td class="delel">
              <div class="del" @click="deltr(index)">
                <i class="el-icon-delete"></i>
              </div>
            </td>
            <td>
              <el-input v-model="datalist[index].level"></el-input>
            </td>
            <td>
              <el-select
                v-if="slot"
                v-model="datalist[index].compare"
                placeholder="请选择类型"
              >
                <el-option
                  v-for="item in compare"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </td>
            <td>
              <el-select v-model="datalist[index].type1" placeholder="选择类型">
                <el-option
                  v-for="item in typa"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </td>
            <td>
              <div v-if="datalist[index].type1" class="min">
                <el-input
                  v-model="datalist[index].min"
                  placeholder="起"
                ></el-input>
                <span>~</span>
                <el-input
                  v-model="datalist[index].max"
                  placeholder="至"
                ></el-input>
              </div>
              <el-input
                v-if="!datalist[index].type1"
                v-model="datalist[index].fraction"
                placeholder=""
              ></el-input>
            </td>
            <td>
              <el-input
                v-model="datalist[index].quota"
                placeholder="请输入额度"
              >
                <template slot="append">万元</template>
              </el-input>
            </td>
          </tr>
        </table>
        <div @click="acs">asdw</div>
      </div>
    </template>
    <!-- </div> -->
  </avue-crud>
</template>
<script>
export default {
  data() {
    return {
      slot: 0,
      datalist: [],
      form: {},
      compare: [
        {
          label: '<=',
          value: 0,
        },
        {
          label: '<',
          value: 1,
        },
        {
          label: '=',
          value: 2,
        },
        {
          label: '>',
          value: '3',
        },
        {
          label: '>=',
          value: 4,
        },
      ],
      typa: [
        {
          label: '范围值',
          value: 1,
        },
        {
          label: '固定值',
          value: 0,
        },
      ],
      data: [
        {
          name: '张三',
          sex: '男',
        },
        {
          name: '李四',
          sex: '女',
        },
        {
          name: '王五',
          sex: '女',
        },
        {
          name: '赵六',
          sex: '男',
        },
      ],
      option: {
        align: 'center',
        menuAlign: 'center',
        column: [
          {
            label: '模板名称',
            span: 18,
            prop: 'name',
            rules: {
              required: true,
              message: '请输入模板名称',
            },
          },
          {
            label: '风控模板',
            span: 13,
            prop: 'sex',
            type: 'select',
            rules: {
              required: true,
              message: '请选择风控模板',
            },
            dicData: [
              {
                label: 'sb',
                value: 'lj',
              },
              {
                label: 'wdnmd',
                value: 'cao',
              },
            ],
          },
          {
            label: '业务类型',
            prop: 'business',
            span: 13,
            type: 'select',
            rules: {
              required: true,
              message: '请选择业务类型',
            },
            dicData: [
              { label: 'duoshaodaidaianbing', value: 'shuodjiusni' },
              {
                label: 'shigeirenxiedm',
                value: 'haonana',
              },
            ],
          },
          {
            label: '级别规则',
            prop: 'rule',
            hide: true,
            span: 24,
            type: 'dynamic',
            rules: {
              required: true,
              message: '',
            },
          },
        ],
      },
    }
  },
  methods: {
    newtab() {
      this.slot += 1
      this.form.rule = 1
      this.datalist.push({
        level: '',
        compare: '',
        type1: '',
        min: '',
        max: '',
        fraction: '',
        quota: '',
      })
    },
    acs() {
      console.log(this.datalist)
    },
    deltr(index) {
      this.slot -= 1
      console.log(index)
      this.datalist.splice(index, 1)
      //   this.datalist[index] = {
      //     level: '',
      //     compare: '',
      //     type: '',
      //     min: '',
      //     max: '',
      //     fraction: '',
      //     quota: '',
      //   }
    },
    rowSave() {
      console.log(this.datalist)
    },
  },
  //   computed: {},
  //   watch: {
  //     'form.rule': {
  //       handler() {
  //         // console.log(this.data2.length)
  //         console.log(this.form.rule)
  //         if (this.form.rule[0].type == 1) {
  //           this.slot = '固定值'
  //         }
  //       },
  //       deep: true,
  //       immediate: true,
  //     },
  //   },
}
</script>
<style scoped>
.min {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.min span {
  flex: 1;
  text-align: center;
}
.rule-box {
  width: 100%;
}
.rule-table {
  width: 100%;
  border: 1px solid #bbbbbb;
}
.rule-table th {
  padding: 0 0 0 8px;
  box-sizing: border-box;
  text-align: left;
  background-color: #fff1f1;
  font-size: 14px;
  font-weight: 400;
  border: 1px solid #bbbbbb;
}

.rule-table td {
  height: 55px;
  padding: 0 3px 0 3px;
  font-size: 14px;
  font-weight: 400;
  background-color: #ffffff;
  border: 1px solid #bbbbbb;
}
.rule-table .btn,
.rule-table .delel {
  box-sizing: border-box;
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}
.rule-table .btn .xz {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3894ff;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  color: #ffffff;
}
.rule-table .level {
  width: 12%;
}
.rule-table .compare {
  width: 17%;
}
.rule-table .type {
  width: 15%;
}
.rule-table .value {
  width: 33%;
}
.rule-table .quota {
  width: 23%;
}
.rule-table .delel .del {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 77, 54, 0.1);
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  color: #ff4d36;
}
</style>
