<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="fileDownlod(row)"
          v-if="row.status==3||row.status==2"
          size="small"
        >下载报告</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getDetail, getList, check } from '@/api/riskmana/riskmanaapply'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '风控系统id',
            prop: 'processId',
            rules: [
              {
                required: true,
                message: '请输入风控系统id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '订单编号',
            search: true,
            prop: 'orderNo',
            rules: [
              {
                required: true,
                message: '请输入订单编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '用户名称',
            prop: 'userName',
            search: true,
          },
          {
            label: '服务费率(%)',
            hide: true,
            prop: 'serviceRateStr',
            rules: [
              {
                required: true,
                message: '请输入服务费率',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '生效日',
            hide: true,
            prop: 'effectiveTime',
            rules: [
              {
                required: true,
                message: '请输入生效日',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '到期日',
            hide: true,
            prop: 'expireTime',
            rules: [
              {
                required: true,
                message: '请输入到期日',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '额度类型',
            hide: true,
            prop: 'quotaType',
            type: 'select',
            dicData: [
              {
                label: '一次性额度',
                prop: '1',
              },
              {
                label: '循环额度',
                prop: '2',
              },
            ],
            rules: [
              {
                required: true,
                message: '请输入额度类型',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资费率(%)',
            hide: true,
            prop: 'annualInterestRateStr',
            rules: [
              {
                required: true,
                message: '请输入融资费率',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            type: 'select',
            search: true,
            prop: 'status',
            dicData: [
              {
                label: '待批额',
                value: 1,
              },
              {
                label: '已终止',
                value: 2,
              },
              {
                label: '已批额',
                value: 3,
              },
              {
                label: '驳回',
                value: 4,
              },
            ],
          },
          {
            label: '风控批准额度（万）',
            prop: 'finalAmountStr',
            rules: [
              {
                required: true,
                message: '请输入风控批准额度（万）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '模板供应商',
            prop: 'supplierNo',
            type: 'select',
            dicData: [
              {
                label: '精锐风控系统',
                value: 'jrRiskOrderApi',
              },
              {
                label: '精锐2.0',
                value: 'jrSupplyRisk',
              },
            ],
            display: false,
          },
          {
            label: '终审人',
            hide: true,
            prop: 'finalAssignee',
            rules: [
              {
                required: true,
                message: '请输入终审人',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.riskmanaapply_add, false),
        viewBtn: this.vaildData(this.permission.riskmanaapply_view, false),
        delBtn: this.vaildData(this.permission.riskmanaapply_delete, false),
        editBtn: this.vaildData(this.permission.riskmanaapply_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    //文件下载
    async fileDownlod(row) {
      // ?
      try {
        const url = 'api/riskmana/riskmanaApply/downLoadReport?riskId=' + row.id
        const { data } = await this.axios.get(url)
        if (data.code === 200) {
          this.contracturl = data.data.link
          var tempLink = document.createElement('a')
          tempLink.style.display = 'none'
          tempLink.href = this.contracturl
          tempLink.setAttribute('download', `报告`)
          if (typeof tempLink.download === 'undefined') {
            tempLink.setAttribute('target', '_blank')
          }
          document.body.appendChild(tempLink)
          tempLink.click()
          document.body.removeChild(tempLink)
          window.URL.revokeObjectURL(this.contracturl)
        }
      } catch (e) {
        // console.log('---------', e)
      }
      // ? 2024-10-11 张灿壕 # 未提 a is not defined
      // a.style.display = 'none'
      // document.body.append(a)
      // a.click()
      // document.body.removeChild(a)
    },
    handleCheck(row) {
      check(row.processId).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '更新完成!',
          })
        },
        error => {
          window.console.log(error)
        }
      )
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style>
</style>
