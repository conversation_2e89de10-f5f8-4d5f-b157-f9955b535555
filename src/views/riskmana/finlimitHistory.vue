<template>
  <div class="applicationForAdjustmentOfQuota">
    <!-- 基本信息 -->
    <template>
      <basic-container>
        <div class="credit-limit-for-application">额度历史</div>
        <div class="apply-container">
          <div class="left">
            <div class="form-item">
              <span class="title">任务编号：</span>
              <span class="value">{{ detail.taskNo }}</span>
            </div>
            <div class="form-item">
              <span class="title">申请人：</span>
              <span class="value">
                <span style="color: rgba(105, 124, 255, 100)">{{
                  detail.enterpriseName
                }}</span>
              </span>
            </div>
            <div class="form-item">
              <span class="title">申请时间：</span>
              <span class="value">{{ detail.createTime }}</span>
            </div>
          </div>
          <div class="right">
            <!-- <svg-icon class="" /> -->
            <div class="right-icon-box" v-if="detail.status == 2">
              <i
                class="el-icon-circle-check"
                style="color: #3dc861; font-size: 40px"
              />
              <span class="status">{{ statusstr(detail.status) }}</span>
            </div>
            <div
              class="right-icon-box"
              v-else-if="
                detail.status == 3 || detail.status == 4 || detail.status == 5
              "
            >
              <i
                class="icon-line-tixing"
                style="color: #4e9bfc; font-size: 40px"
              />
              <span class="status">{{ statusstr(detail.status) }}</span>
            </div>
            <div class="el-icon-time" v-else>
              <SvgIcon
                icon-class="icon-delete-filling"
                class="invoice-apply-right-icon"
                style="fill: #c1c1c1"
              ></SvgIcon>
              <span class="status">{{ statusstr(detail.status) }}</span>
            </div>
          </div>
        </div>
      </basic-container>
    </template>
    <!-- 中间三个卡片 -->
    <template>
      <div class="card-container">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-card>
              <div class="card-item">
                {{
                  detail.creditAmount &&
                  $numChuFun(detail.creditAmount, 10000)
                }}
              </div>
              <div>当前授信额度（万元）</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card>
              <div class="card-item">
                {{
                  detail.availableAmount &&
                  $numChuFun(detail.availableAmount, 10000)
                }}
              </div>
              <div>当前可用额度（万元）</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card>
              <div class="card-item">
                {{
                  detail.usedAmount && $numChuFun(detail.usedAmount, 10000)
                }}
              </div>
              <div>当前已用额度（万元）</div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </template>
    <template>
      <basic-container>
        <el-radio-group v-model="tabPosition" style="margin-bottom: 17px">
          <el-radio-button label="top"> 额度变化</el-radio-button>
          <el-radio-button label="right">使用明细</el-radio-button>
        </el-radio-group>
        <div
          v-loading="loadingType"
          class="quotaContainer"
          v-show="tabPosition == 'top'"
        >
          <template>
            <div v-for="(item, index) in allArr" :key="index">
              <div class="year" style="margin-bottom: 23px">
                {{ item.year }}年
              </div>
              <div
                class="nameContainer"
                v-for="(citem, index) in item.mouthArr"
                :key="index"
              >
                <div class="monthContainer">
                  <div class="month">{{ citem.mouth }}月</div>
                  <div class="liner"></div>
                </div>
                <div>
                  <div
                    v-for="(data, dindex) in citem.day"
                    :key="data.id"
                    :style="{
                      marginBottom:
                        dindex == citem.day.length - 1 ? '0px' : '23px',
                    }"
                  >
                    <el-card
                      :body-style="{
                        width: '694px',
                        padding: '10px 24px',
                        boxSizing: 'border-box',
                      }"
                    >
                      <div class="cardContainer">
                        <div>
                          <div class="title">
                            {{ quotaHistorystatus(data.changeType) }}
                          </div>
                          <div class="data">
                            {{
                              data.changeTime && data.changeTime.split(' ')[0]
                            }}
                          </div>
                        </div>
                        <div class="des">
                          {{ text(data.changeType, data.creditAmount) }}
                        </div>
                      </div>
                    </el-card>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>

        <div
          v-loading="loadingType"
          class="usedes"
          v-if="tabPosition == 'right'"
        >
          <div
            v-for="item in quotaUseDetailList"
            :key="item.id"
            style="margin-bottom: 23px"
          >
            <el-card
              :body-style="{
                width: '694px',
                padding: '10px 24px',
                boxSizing: 'border-box',
              }"
              style="width: 694px"
            >
              <div class="cardContainer">
                <div>
                  <div class="title">
                    融资编号：
                    <span>{{ item.financeNo }}</span>
                    &nbsp;
                    产品名称：
                    <span>{{ item.goodsName }}</span>
                  </div>
                  <div class="data">
                    记录时间：
                    {{ item.updateTime && item.updateTime.split(' ')[0] }}
                  </div>
                </div>
                <div class="des" style="color: rgba(16, 16, 16, 1)">
                  金额：
                  {{ item.amountSignStr }}{{ item.amount }}
                </div>
              </div>
            </el-card>
          </div>
        </div>
        <div class="my-pagination">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            layout="total, prev, pager, next"
            :current-page.sync="paginationObj.current"
            :page-size="paginationObj.pageSize"
            :total="paginationObj.total"
          >
          </el-pagination>
        </div>
      </basic-container>
    </template>
    <!-- 操作按钮 -->
    <div class="applicationForAdjustmentOfQuota-menu">
      <span @click="cancel()">返回</span>
    </div>
  </div>
</template>
<script>
import {
  financingQuotaHistory,
  quotaHistoryList,
  quotaUseDetailsList,
} from '@/api/riskmana/enterprisequota'
import { getDictionary } from '@/api/plugin/workflow/externalForm'
export default {
  data() {
    return {
      tabPosition: 'top',
      detail: {},
      allArr: [],
      quotaUseDetailList: [],
      quotaHistorystatusList: [],
      paginationObj: {
        total: 0,
        pageSize: 10,
        current: 1,
      },
      loadingType: false,
    }
  },
  watch: {
    tabPosition: {
      handler(val) {
        this.paginationObj.current = 1
        if (val === 'top') {
          this.quotaHistoryListFun()
          return
        }
        this.quotaUseDetailsListFun()
      },
      // immediate: true,
    },
  },
  created() {
    this.getDictionary()
    this.getDetail()
    this.quotaHistoryListFun()
  },
  methods: {
    async getDictionary() {
      const {
        data: { data },
      } = await getDictionary('quota_update_type')
      this.quotaHistorystatusList = data || []
    },
    async getDetail() {
      const id = this.$route.query.id
      const {
        data: { data },
      } = await financingQuotaHistory(id)
      this.detail = data || {}
      // this.quotaUseDetailList = data.quotaUseDetailList || []
      // let quotaHistoryList = data.quotaHistoryList || []
      // for (let i = 1; i < quotaHistoryList.length; i++) {
      //   const { year: oldYear, mouth: oldMouth } = this.dataObj(
      //     quotaHistoryList[i - 1].changeTime
      //   )
      //   const { year: newYear, mouth: newMouth } = this.dataObj(
      //     quotaHistoryList[i].changeTime
      //   )
      //   if (this.allArr.length == 0) {
      //     let yearObj = this.yearObj()
      //     let mouthObj = this.mouthObj()
      //     mouthObj.day.push(quotaHistoryList[0])
      //     mouthObj.mouth = oldMouth
      //     yearObj.year = oldYear
      //     yearObj.mouthArr.push(mouthObj)
      //     this.allArr[0] = yearObj
      //   }
      //   if (quotaHistoryList[i]) {
      //     if (oldYear == newYear) {
      //       if (oldMouth == newMouth) {
      //         this.allArr[this.allArr.length - 1].mouthArr[
      //           this.allArr[this.allArr.length - 1].mouthArr.length - 1
      //         ].day.push(quotaHistoryList[i])
      //       } else {
      //         let mouthObj = this.mouthObj()
      //         mouthObj.day.push(quotaHistoryList[i])
      //         mouthObj.mouth = newMouth
      //         this.allArr[this.allArr.length - 1].mouthArr.push(mouthObj)
      //       }
      //     } else {
      //       let yearObj = this.yearObj()
      //       let mouthObj = this.mouthObj()
      //       mouthObj.day.push(quotaHistoryList[i])
      //       mouthObj.mouth = newMouth
      //       yearObj.year = newYear
      //       yearObj.mouthArr.push(mouthObj)
      //       this.allArr.push(yearObj)
      //     }
      //   }
      // }
    },
    quotaHistorystatus(status) {
      let str = '额度调整'
      this.quotaHistorystatusList.forEach(item => {
        if (item.dictKey == status) {
          str = item.dictValue
        }
      })
      return str
    },
    // 额度变化
    quotaHistoryListFun() {
      this.loadingType = true
      this.allArr = []
      const id = this.$route.query.id
      const paramsD = {
        current: this.paginationObj.current,
        size: this.paginationObj.pageSize,
        enterpriseQuotaId: id,
      }
      quotaHistoryList(paramsD)
        .then(({ data }) => {
          if (data.success) {
            const { data: resData } = data
            this.paginationObj.total = resData.total
            const quotaHistoryList = resData.records
            for (let i = 1; i <= quotaHistoryList.length; i++) {
              const { year: oldYear, mouth: oldMouth } = this.dataObj(
                quotaHistoryList[i - 1].changeTime
              )
              let newObj = {}
              if (quotaHistoryList[i]) {
                newObj = this.dataObj(quotaHistoryList[i].changeTime)
              }
              if (this.allArr.length == 0) {
                let yearObj = this.yearObj()
                let mouthObj = this.mouthObj()
                mouthObj.day.push(quotaHistoryList[0])
                mouthObj.mouth = oldMouth
                yearObj.year = oldYear
                yearObj.mouthArr.push(mouthObj)
                this.allArr[0] = yearObj
              }
              if (quotaHistoryList[i]) {
                if (oldYear == newObj.year) {
                  if (oldMouth == newObj.mouth) {
                    this.allArr[this.allArr.length - 1].mouthArr[
                      this.allArr[this.allArr.length - 1].mouthArr.length - 1
                    ].day.push(quotaHistoryList[i])
                  } else {
                    let mouthObj = this.mouthObj()
                    mouthObj.day.push(quotaHistoryList[i])
                    mouthObj.mouth = newObj.mouth
                    this.allArr[this.allArr.length - 1].mouthArr.push(mouthObj)
                  }
                } else {
                  let yearObj = this.yearObj()
                  let mouthObj = this.mouthObj()
                  mouthObj.day.push(quotaHistoryList[i])
                  mouthObj.mouth = newObj.mouth
                  yearObj.year = newObj.year
                  yearObj.mouthArr.push(mouthObj)
                  this.allArr.push(yearObj)
                }
              }
            }
            this.allArr = Object.assign({}, this.allArr)
            setTimeout(() => {
              this.loadingType = false
            }, 300)
          }
        })
        .catch(() => {
          this.loadingType = false
        })
    },
    // 使用明细
    quotaUseDetailsListFun() {
      this.loadingType = true
      this.quotaUseDetailList = []
      const id = this.$route.query.id
      const paramsD = {
        current: this.paginationObj.current,
        size: this.paginationObj.pageSize,
        enterpriseQuotaId: id,
      }
      quotaUseDetailsList(paramsD)
        .then(({ data }) => {
          if (data.success) {
            const { data: resData } = data
            this.paginationObj.total = resData.total
            const arrD = resData.records
            for (const item of arrD) {
              switch (item.amountSign) {
                case -1:
                  item.amountSignStr = '-'
                  break
                case 1:
                  item.amountSignStr = '+'
                  break
                default:
                  item.amountSignStr = ''
                  break
              }
            }
            this.quotaUseDetailList = arrD
            setTimeout(() => {
              this.loadingType = false
            }, 300)
          }
        })
        .catch(() => {
          this.loadingType = false
        })
    },
    yearObj() {
      return {
        year: '',
        mouthArr: [],
      }
    },
    mouthObj() {
      return {
        mouth: '',
        day: [],
      }
    },
    dataObj(time) {
      const year = time.split(' ')[0].split('-')[0]
      const mouth = time.split(' ')[0].split('-')[1]
      const day = time.split(' ')[0].split('-')[2]
      return { year, mouth, day }
    },
    cancel() {
      this.$router.$avueRouter.closeTag()
      // this.$router.push({ path: '/riskmana/coreenterprisequota' })
      this.$router.back()
    },
    statusstr(status) {
      if (status == 1) {
        return '待激活'
      } else if (status == 2) {
        return '生效中'
      } else if (status == 3) {
        return '已冻结'
      } else if (status == 4) {
        return '已禁用'
      } else if (status == 5) {
        return '已过期'
      } else if (status == 6) {
        return '调整中'
      } else if (status == 7) {
        return '续期中'
      }
    },
    text(status, creditAmount) {
      if (status == 1) {
        return `调整后通用授信额度${this.$numChuFun(creditAmount, 10000)}万元`
      } else if (status == 2) {
        return `续期后通用授信额度${this.$numChuFun(creditAmount, 10000)}万元`
      } else if (status == 3) {
        return `解冻后通用授信额度${this.$numChuFun(creditAmount, 10000)}万元`
      } else if (status == 4) {
        return `恢复后通用授信额度${this.$numChuFun(creditAmount, 10000)}万元`
      } else if (status == 5) {
        return `${this.$numChuFun(creditAmount, 10000)}万元`
      }
    },
    handleCurrentChange(val) {
      this.paginationObj.current = val
      if (this.tabPosition === 'top') {
        this.quotaHistoryListFun()
        return
      }
      this.quotaUseDetailsListFun()
    },
  },
}
</script>

<style lang="scss" scoped>
.year {
  height: 26px;
  color: rgba(16, 16, 16, 0.37);
  font-size: 20px;
  text-align: left;
  font-family: SourceHanSansSC-medium;
}
.cardContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    height: 24px;
    color: rgba(16, 16, 16, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
  }
  .data {
    height: 20px;
    color: rgba(16, 16, 16, 0.52);
    font-size: 14px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
  }
  .des {
    height: 24px;
    color: rgba(16, 16, 16, 0.52);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
  }
}
.nameContainer {
  display: flex;
  margin-bottom: 23px;
  .monthContainer {
    margin: 0 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .month {
      height: 24px;
      color: rgba(16, 16, 16, 0.52);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-medium;
    }
    // .liner {
    //   width: 2px;
    //   flex: 2;
    //   background-image:  #BBBBBB; /* 35%设置虚线点y轴上的长度 */
    //   background-position: left; /* right配置右边框位置的虚线 */
    //   background-size: 1px 3px; /* 第一个参数设置虚线点x轴上的长度；第二个参数设置虚线点的间距 */
    //   background-repeat: repeat-y;
    // }
  }
}
.card-container {
  padding: 5px 6px;
  ::v-deep {
    .el-card {
      display: flex;
      align-items: center;
      justify-content: center;
      .card-item {
        font-size: 20px;
        color: #1277ff;
      }
      div {
        text-align: center;
      }
    }
  }
}
.applicationForAdjustmentOfQuota {
  ::v-deep {
    .basic-container__card {
      border-radius: 8px;
    }

    .avue-form__menu {
      display: none;
    }

    .el-input.is-disabled .el-input__inner {
      color: #000;
    }

    .el-input-group__append {
      color: #000;
    }
  }

  .credit-limit-for-application {
    height: 68px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    border-bottom: 1px solid rgba(233, 235, 239, 100);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: -20px;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .applicationForAdjustmentOfQuota-menu {
    height: 36px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 14px 0 50px;
    padding: 0 4px;

    span {
      width: 93px;
      height: 36px;
      line-height: 36px;
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
      margin-right: 16px;
      cursor: pointer;
    }
  }
}
.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }
  }
}
</style>
