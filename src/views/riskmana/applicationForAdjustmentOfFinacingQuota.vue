<template>
  <div class="applicationForAdjustmentOfQuota">
    <!-- 基本信息 -->
    <basic-container>
      <div class="credit-limit-for-application">
        {{ !this.rowId ? '申请额度' : '调整额度' }}
      </div>
      <div class="boxs">
        <div class="boxs-to-apply-for-product">
          <h1 class="boxs-to-apply-for-product-h1">基本信息</h1>
          <div class="amount-of-information">
            <avue-form
              ref="amountForm"
              :option="amountOption"
              v-model="amountForm"
            >
            </avue-form>
          </div>
        </div>
      </div>
    </basic-container>

    <!-- 对公账户 -->
    <basic-container>
      <div class="boxs">
        <div class="boxs-to-apply-for-product">
          <h1 class="boxs-to-apply-for-product-h1">对公账户</h1>
          <div class="amount-of-information">
            <avue-form
              ref="amountBankForm"
              :option="amountBankOption"
              v-model="amountForm"
            >
            </avue-form>
          </div>
        </div>
      </div>
    </basic-container>

    <!-- 操作按钮 -->
    <div class="applicationForAdjustmentOfQuota-menu">
      <span @click="cancel()">取消</span>
      <span @click="validate()">提交</span>
    </div>
  </div>
</template>

<script>
import {
  allEnableList,
  allOnShelfGoodsList,
  allCloudOnShelfGoodsList,
  enterpriseQuotaApply,
  enterpriseQuotaDetail,
  financingEnterpriseQuotaUpdate,
} from '@/api/riskmana/enterprisequota'

export default {
  data() {
    return {
      rowId: this.$route.query.id,
      viewData: {},
      amountForm: {},
      amountOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [],
      },
      amountBankOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [],
      },
    }
  },
  /*
选择企业
chooseEnterprise
选择资方
selectTheManagement
申请额度
creditLimitForApplication
银行年利率
bankAnnualInterestRate
生效日
effectiveDate
到期日
dueDate
额度类型
lineType

开户行
openingBank
银行卡号
creditCardNumbers
  */
  created() {
    // 基本信息
    const columnData = [
      {
        label: '选择企业',
        prop: 'chooseEnterprise',
        type: 'select',
        placeholder: '请选择企业',
        span: 12,
        display: true,
        disabled: false,
        dicData: [],
        rules: [
          {
            required: true,
            message: '请选择企业',
            trigger: 'change',
          },
        ],
      },
      {
        label: '产品类型',
        prop: 'productType',
        type: 'select',
        placeholder: '请选择产品类型',
        span: 12,
        display: true,
        disabled: false,
        dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择产品类型',
            trigger: 'change',
          },
        ],
        control: val => {
          if (val) {
            const params = {
              type: val,
            }
            if (val == 3) {
              allCloudOnShelfGoodsList().then(res => {
                const resData = res.data
                if (resData.code == 200) {
                  const selectTheManagementArr = []
                  for (const item of resData.data) {
                    selectTheManagementArr.push({
                      value: item.id,
                      label: item.goodsName,
                      goodsTypeId: item.goodsTypeId,
                      capitalId: item.capitalId,
                    })
                  }
                  this.amountOption.column[2].dicData = selectTheManagementArr
                }
              })
            } else {
              allOnShelfGoodsList(params).then(res => {
                const resData = res.data
                if (resData.code == 200) {
                  const selectTheManagementArr = []
                  for (const item of resData.data) {
                    selectTheManagementArr.push({
                      value: item.id,
                      label: `${item.goodsName} (${
                        item.type == 1 ? '应收账款质押' : '代采融资'
                      })`,
                      goodsTypeId: item.goodsTypeId,
                      capitalId: item.capitalId,
                    })
                  }
                  this.amountOption.column[2].dicData = selectTheManagementArr
                }
              })
            }
          }
        },
      },
      {
        label: '选择产品',
        prop: 'selectTheManagement',
        type: 'select',
        placeholder: '请选择产品',
        span: 12,
        display: true,
        disabled: false,
        dicData: [],
        rules: [
          {
            required: true,
            message: '请选择产品',
            trigger: 'change',
          },
        ],
      },
      {
        label: '申请额度',
        prop: 'creditLimitForApplication',
        type: 'input',
        placeholder: '请填写申请额度',
        span: 12,
        append: '元',
        display: true,
        disabled: false,
        rules: [
          {
            required: true,
            message: '请填写申请额度',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '银行年利率',
        prop: 'bankAnnualInterestRate',
        type: 'input',
        placeholder: '请填写银行年利率',
        span: 12,
        append: '%',
        display: true,
        disabled: false,
        rules: [
          {
            required: true,
            message: '请填写银行年利率',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '生效日',
        prop: 'effectiveDate',
        type: 'date',
        placeholder: '选择日期',
        span: 12,
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd hh:mm:ss',
        display: true,
        disabled: false,
        control: val => {
          if (val && !this.amountOption.column[4].disabled && !this.rowId) {
            if (
              new Date(val).getTime() <=
              new Date(new Date() - 24 * 60 * 60 * 1000).getTime()
            ) {
              this.$message.error('请确保生效日不小于今日')
              const eff = setInterval(() => {
                this.amountForm.effectiveDate = ''
                if (!this.amountForm.effectiveDate) {
                  clearInterval(eff)
                }
              }, 50)
            }
          }
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < new Date().getTime() - 3600 * 1000 * 24
          },
        },
        rules: [
          {
            required: true,
            message: '选择日期',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '到期日',
        prop: 'dueDate',
        type: 'date',
        placeholder: '选择日期',
        span: 12,
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd hh:mm:ss',
        display: true,
        disabled: false,
        control: val => {
          if (val && !this.amountOption.column[5].disabled && !this.rowId) {
            if (
              new Date(val).getTime() <=
              new Date(this.amountForm.effectiveDate).getTime()
            ) {
              this.$message.error('请确保到期日大于生效日')
              const due = setInterval(() => {
                this.amountForm.dueDate = ''
                if (!this.amountForm.dueDate) {
                  clearInterval(due)
                }
              }, 50)
            } else if (!this.amountForm.effectiveDate) {
              this.$message.error('请先选择生效日')
              const due = setInterval(() => {
                this.amountForm.dueDate = ''
                if (!this.amountForm.dueDate) {
                  clearInterval(due)
                }
              }, 50)
            }
          }
        },
        pickerOptions: {
          disabledDate: time => {
            return (
              time.getTime() < new Date(this.amountForm.effectiveDate).getTime()
            )
          },
        },
        rules: [
          {
            required: true,
            message: '选择日期',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '额度类型',
        prop: 'lineType',
        type: 'select',
        placeholder: '请选择额度类型',
        span: 12,
        display: true,
        disabled: false,
        // dicData: [],
        dicUrl: '/api/blade-system/dict-biz/dictionary?code=quota_type',
        props: {
          label: 'dictValue',
          value: 'dictKey',
        },
        dataType: 'number',
        rules: [
          {
            required: true,
            message: '请选择额度类型',
            trigger: 'change',
          },
        ],
      },
    ]
    this.amountOption.column = columnData
    // 对公账户
    const amountBankOptionData = [
      {
        label: '开户行',
        prop: 'openingBank',
        type: 'input',
        placeholder: '请填写开户行',
        span: 12,
        display: true,
        disabled: false,
        rules: [
          {
            required: true,
            message: '请填写开户行',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '银行卡号',
        prop: 'creditCardNumbers',
        type: 'input',
        placeholder: '请填写银行卡号',
        span: 12,
        display: true,
        disabled: false,
        rules: [
          {
            required: true,
            message: '请填写银行卡号',
            trigger: 'blur',
          },
        ],
      },
    ]
    this.amountBankOption.column = amountBankOptionData
    if (!this.rowId) {
      this.onload() // 挂载请求
    } else {
      // 循环处理disabled
      for (const item of this.amountOption.column) {
        if (!item.append) {
          item.disabled = true
        }
      }
      // 循环处理disabled
      for (const item of this.amountBankOption.column) {
        item.disabled = true
      }
      this.enterpriseQuotaDetail() // 查看请求
    }
  },
  methods: {
    validate() {
      this.$refs.amountForm.validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0].message
          if (!errMsg) {
            errMsg = Object.values(msg)[0][0].message
            if (!errMsg) {
              errMsg = '必填项未填'
            }
          }
          this.$message.error(errMsg)
          return
        }
        done()
        this.$refs.amountBankForm.validate((valid, done, msg) => {
          if (!valid) {
            let errMsg = Object.values(msg)[0].message
            if (!errMsg) {
              errMsg = Object.values(msg)[0][0].message
              if (!errMsg) {
                errMsg = '必填项未填'
              }
            }
            this.$message.error(errMsg)
            return
          }
          done()
          if (!this.rowId) {
            this.submit() // 提交
          } else {
            this.updDta() // 更新提交
          }
        })
      })
    },
    cancel() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({ path: '/riskmana/financingenterprisequota' })
    },
    submit() {
      const FData = this.amountForm
      // 根据已选核心企业筛选出对应数据
      const chooseEnterpriseArrSelsctData = this.filterColumn(
        0,
        'value',
        FData.chooseEnterprise
      )
      // 根据已选产品筛选出对应数据
      const selectTheManagementArrSelsctData = this.filterColumn(
        2,
        'value',
        FData.selectTheManagement
      )
      const data = {
        annualInterestRate: FData.bankAnnualInterestRate,
        creditAmount: FData.creditLimitForApplication,
        bank: FData.openingBank,
        bankCardNo: FData.creditCardNumbers,
        capitalId: selectTheManagementArrSelsctData.capitalId,
        enterpriseId: chooseEnterpriseArrSelsctData.value,
        enterpriseName: chooseEnterpriseArrSelsctData.label,
        enterpriseType: '2', // 企业类型 1 融资 2 核心
        goodsId: selectTheManagementArrSelsctData.value,
        goodsName: selectTheManagementArrSelsctData.label,
        quotaType: String(FData.lineType),
        effectiveTime: FData.effectiveDate,
        expireTime: FData.dueDate,
        productType: FData.productType,
        // serviceRate: 0, // 服务费率
      }
      enterpriseQuotaApply(data).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          this.$message.success('提交成功,请静待审核完成')
          this.$router.$avueRouter.closeTag()
          this.$router.push({ path: '/riskmana/financingenterprisequota' })
        }
      })
    },
    updDta() {
      const FData = this.amountForm
      const data = this.viewData
      data.annualInterestRate = FData.bankAnnualInterestRate
      data.creditAmount = FData.creditLimitForApplication

      financingEnterpriseQuotaUpdate(data).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          this.$message.success('提交成功,请静待审核完成')
          this.$router.$avueRouter.closeTag()
          this.$router.push({ path: '/riskmana/financingenterprisequota' })
        }
      })
    },
    // 查询已经启用的核心企业
    allEnableList() {
      allEnableList({ value: 1 }).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          const chooseEnterpriseArr = []
          for (const item of resData.data) {
            chooseEnterpriseArr.push({
              value: item.id,
              label: item.name,
              customerCode: item.customerCode,
              companyId: item.companyId,
            })
          }
          this.amountOption.column[0].dicData = chooseEnterpriseArr
        }
      })
    },
    // 查询所有上架产品
    allOnShelfGoodsList() {
      const params = {
        type: 1,
      }
      allOnShelfGoodsList(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          const selectTheManagementArr = []
          for (const item of resData.data) {
            selectTheManagementArr.push({
              value: item.id,
              label: `${item.goodsName} (${
                item.type == 1 ? '应收账款质押' : '代采融资'
              })`,
              goodsTypeId: item.goodsTypeId,
              capitalId: item.capitalId,
            })
          }
          this.amountOption.column[2].dicData = selectTheManagementArr
        }
      })
    },
    // 挂载请求
    onload() {
      this.allEnableList() // 查询已经启用的核心企业
      //  this.allOnShelfGoodsList() // 查询所有上架产品
    },
    // 查看请求
    enterpriseQuotaDetail() {
      const params = {
        id: this.rowId,
      }
      enterpriseQuotaDetail(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          const ite = resData.data
          this.amountForm.chooseEnterprise = ite.enterpriseName // 选择企业
          this.amountForm.productType = ite.productType // 产品类型
          this.amountForm.selectTheManagement = ite.goodsName // 选择产品
          this.amountForm.creditLimitForApplication = ite.creditAmount // 申请额度
          this.amountForm.bankAnnualInterestRate = ite.annualInterestRate // 银行年利率
          this.amountForm.effectiveDate = ite.effectiveTime // 生效日
          this.amountForm.dueDate = ite.expireTime // 到期日
          this.amountForm.lineType = ite.quotaType // 额度类型
          this.amountForm.openingBank = ite.bank // 开户行
          this.amountForm.creditCardNumbers = ite.bankCardNo // 银行卡号
          this.viewData = resData.data // 对整个data进行存储
        }
      })
    },
    // 过滤函数
    filterColumn(index, matching, compare) {
      if (index != 1) {
        return this.amountOption.column[index].dicData.filter(
          item => item[matching] == compare
        )[0]
      }
    },
  },
  openlimitHistory(rowId){
      this.$router.push({
        path: '/riskmana/finlimitHistory',
        query: { id: rowId },
      })
    },
}
</script>

<style lang="scss" scoped>
.applicationForAdjustmentOfQuota {
  ::v-deep {
    .basic-container__card {
      border-radius: 8px;
    }

    .avue-form__menu {
      display: none;
    }

    .el-input.is-disabled .el-input__inner {
      color: #000;
    }

    .el-input-group__append {
      color: #000;
    }
  }

  .credit-limit-for-application {
    height: 68px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    border-bottom: 1px solid rgba(233, 235, 239, 100);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: -20px;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .boxs {
    .boxs-to-apply-for-product {
      box-sizing: border-box;

      h1 {
        margin: 0;
      }

      .boxs-to-apply-for-product-h1 {
        height: 22px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        position: relative;

        &::before {
          content: '';
          display: inline-block;
          width: 8px;
          height: 16px;
          line-height: 20px;
          border-radius: 15px;
          background-color: rgba(18, 119, 255, 100);
          text-align: center;
          transform: translateY(2px);
          box-sizing: border-box;
          margin-right: 4px;
        }
      }

      .amount-of-information {
        margin-top: 30px;
      }
    }
  }

  .applicationForAdjustmentOfQuota-menu {
    height: 36px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 14px 0 50px;
    padding: 0 4px;

    & > span:first-child {
      width: 93px;
      height: 36px;
      line-height: 36px;
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
      margin-right: 16px;
      cursor: pointer;
    }

    & > span:last-child {
      width: 93px;
      height: 36px;
      line-height: 36px;
      border-radius: 4px;
      background-color: rgba(18, 119, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
