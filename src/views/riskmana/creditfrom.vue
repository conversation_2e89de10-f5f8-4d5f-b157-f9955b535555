<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row }" slot="goodsType">
        <div style="text-align: center;">
          <el-tag type="params" v-if="row.goodsType == 1">{{
            row.$goodsType
          }}</el-tag>
          <el-tag type="success" v-else>{{ row.$goodsType }}</el-tag>
        </div>
      </template>
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="addData"
          >新增
        </el-button>
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.creditfrom_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>

      <template slot-scope="{ row }" slot="status">
        <el-tag v-if="!row.status" type="info">已禁用</el-tag>
        <el-tag v-else type="success">已启用</el-tag>
      </template>

      <template slot-scope="{ row }" slot="menu">
        <el-button type="text" size="small" @click="lookData(row)"
          >查看
        </el-button>
        <el-button
          type="text"
          size="small"
          @click="upData(row)"
          v-if="!row.status"
          >编辑
        </el-button>
        <el-button
          type="text"
          size="small"
          @click="rowDel(row)"
          v-if="!row.status"
          >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          @click="startUp(row)"
          v-if="!row.status"
          >启用
        </el-button>
        <el-button
          type="text"
          size="small"
          @click="stopDown(row)"
          v-if="row.status"
          >禁用
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  fromStopType,
  enable,
  disable
} from '@/api/riskmana/creditfrom'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        // viewBtn: true,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '表单名称',
            prop: 'fromName',
            rules: [
              {
                required: true,
                message: '请输入表单名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '业务类型',
            prop: 'goodsType',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
          },
          {
            label: '上次修改时间',
            prop: 'updateTime',
          },
          {
            label: '操作人',
            prop: 'realName',
          },
          {
            label: '状态',
            prop: 'status',
            width: 80,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.creditfrom_add, false),
        // viewBtn: this.vaildData(this.permission.creditfrom_view, false),
        // delBtn: this.vaildData(this.permission.creditfrom_delete, false),
        // editBtn: this.vaildData(this.permission.creditfrom_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  mounted() {
    const ins = setInterval(() => {
      this.onLoad(this.page)
    }, 200)
    setTimeout(() => {
      clearInterval(ins)
    }, 500)
  },
  methods: {
    startUp(row) {
      this.$confirm('确定将选择数据启用?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return enable(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    stopDown(row) {
      this.$confirm('确定将选择数据禁用，该操作会使所有使用该表单的产品全部下架?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return disable(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    addData() {
      this.$router.push({ path: '/riskmana/add' })
      sessionStorage.setItem('lookCollect', '')
    },
    lookData(row) {
      this.$router.push({ path: '/riskmana/add' })
      sessionStorage.setItem('lookCollect', 'true')
      sessionStorage.setItem('lookCollectID', `${row.id}`)
    },
    upData(row) {
      this.$router.push({ path: '/riskmana/add' })
      sessionStorage.setItem('lookCollect', 'false')
      sessionStorage.setItem('lookCollectID', `${row.id}`)
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(String(row.id))
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
