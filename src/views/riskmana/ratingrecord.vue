<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
      </template>
      <template slot-scope="{ row }" slot="status">
        <el-tag v-if="row.status == 1">有效</el-tag>
        <el-tag type="info" v-if="row.status == 0">失效</el-tag>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-view"
          @click="cedit(row)"
          size="small"
          v-if="permission.ratingrecord_view"
        >查看
        </el-button>
<!--        <el-button-->
<!--          type="text"-->
<!--          icon="el-icon-edit"-->
<!--          @click="fileDownlod(row)"-->
<!--          size="small"-->
<!--          v-if="permission.ratingrecord_view"-->
<!--        >下载报告-->
<!--        </el-button>-->
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/riskmana/ratingrecord";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          editBtn:false,
          delBtn:false,
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: false,
          addBtn:false,
          selection: false,
          dialogClickModal: false,
          column: [
            {
              label: "评分编号",
              search:true,
              prop: "recordNo",
              rules: [{
                required: true,
                message: "请输入评分编号",
                trigger: "blur"
              }]
            },
            {
              label: "评分模板",
              search:true,
              prop: "ratingName",
              rules: [{
                required: true,
                message: "请输入评分模板",
                trigger: "blur"
              }]
            },
            {
              label: "客户名称",
              search:true,
              prop: "userName",
            },
            {
              label: "最终得分",
              prop: "strFinalScore",
              rules: [{
                required: true,
                message: "请输入最终得分",
                trigger: "blur"
              }]
            },
            {
              label: "信用级别",
              prop: "grade",
              rules: [{
                required: true,
                message: "请输入信用评级",
                trigger: "blur"
              }]
            },
            {
              label: "风险限额",
              prop: "strLimitAmount",
              rules: [{
                required: true,
                message: "请输入风险限额",
                trigger: "blur"
              }]
            },
            {
              label: '评级时间',
              prop: 'createTime',
              format: 'yyyy-MM-dd',
              type: 'datetime',
            },
            {
              label: "到期时间",
              format: 'yyyy-MM-dd',
              prop: "expireTime",
              type:'datetime',
            },
            {
              label:'评分状态',
              type:'select',
              search:true,
              dicData:[
                {
                  label:'有效',
                  value:1
                },
                {
                  label:'失效',
                  value:0
                }
              ],
              prop: 'status',
              formslot: true,
            },
            {
              label: '评级时间',
              prop: 'createTimeRange',
              type: 'datetime',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd 00:00:00',
              searchRange: true,
              hide: true,
              display: false,
              search: true,
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.ratingrecord_add, false),
          viewBtn: this.vaildData(this.permission.ratingrecord_view, false),
          delBtn: this.vaildData(this.permission.ratingrecord_delete, false),
          editBtn: this.vaildData(this.permission.ratingrecord_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      //文件下载
      fileDownlod(row){
        console.log(row)
      },
      cedit(row){
        this.$router.push('/riskmana/ratingrecord/detail/'+Buffer.from(JSON.stringify(row.id)).toString('base64'))
        // this.$router.push('/blade-riskmana/web-back/riskmana/ratingrecord/test')
        // this.axios.get("/api/blade-riskmana/web-back/riskmana/ratingrecord/test")

      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        const {createTimeRange} = this.query
        let values = {
          ...params,
        }
        if (createTimeRange) {
          values = {
            ...params,
            create_time_datege: createTimeRange[0],
            create_time_datele: createTimeRange[1],
            ...this.query,
          }
          values.createTimeRange = null
        }
        this.loading = true;
        getList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query, values)
        ).then(res => {
          const data = res.data.data
          this.page.total = data.total
          this.data = data.records
          this.loading = false
          this.selectionClear()
        })
      }
    }
  };
</script>

<style>
</style>
