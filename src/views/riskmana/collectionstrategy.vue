<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{ row, index, type }" slot="menuForm">
        <el-button
          type="primary"
          size="small"
          plain
          v-if="
            (type == 'edit' || type == 'add') && permission.material_enabled
          "
          @click="handleFormEnabled()"
        >启用
        </el-button>
      </template>


      <template slot-scope="{ scope, row }" slot="status">
        <el-tag type="success" v-if="row.status == 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.status == 0 && permission.material_view"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.status == 0 && permission.material_delete"
        >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          v-if="permission.material_enabled"
          @click="enable(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/riskmana/collectionstrategy";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        editBtn:false,
        delBtn:false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "催收方式",
            prop: "type",
            type: 'select',
            dicData: [
              {
                label: "站内信",
                value: 1
              },
              {
                label: "短信",
                value: 2
              },
              {
                label: "邮件",
                value: 3
              }, {
                label: "智能语音",
                value: 4
              },
               {
                label: "人工",
                value: 5
              },
            ],
            rules: [{
              required: true,
              message: "请选择催收方式",
              trigger: "blur"
            }]
          },
          {
            prop:"jobId",
            hide:true,
            display: false,
          },
          {
            label: "触发因子",
            prop: "triggerFactor",
            rules: [{
              required: true,
              message: "请输入触发因子",
              trigger: "blur"
            }]
          },
          {
            label: "催收开始时间(时)",
            prop: "startTime",
            type:'time',
            format:'H',
            valueFormat: 'H',
            rules: [{
              required: true,
              message: "请输入催收开始时间",
              trigger: "blur"
            }]
          },
          {
            label: "corn表达式",
            prop: "cornExpression",
            hide: true,
            display: false,
            rules: [{
              required: true,
              message: "请输入corn表达式",
              trigger: "blur"
            }]
          },
          {
            label: "循环时间(时)",
            prop: "cycleTime",
            type: "number",
            minRows: 1,
            maxRows: 360,
            rules: [{
              required: true,
              message: "请输入循环时间",
              trigger: "blur"
            }]
          },
          {
            label: "生效时间(时)",
            prop: "effectiveTime",
            format:'H',
            valueFormat: 'H',
            type:'timerange',
            rules: [{
              required: true,
              message: "请输入生效时间开始",
              trigger: "blur"
            }]
          },
          {
            label: '状态',
            prop: 'status',
            display: false,
            formslot: true,
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.collectionstrategy_add, false),
        viewBtn: this.vaildData(this.permission.collectionstrategy_view, false),
        delBtn: this.vaildData(this.permission.collectionstrategy_delete, false),
        editBtn: this.vaildData(this.permission.collectionstrategy_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    enable(row) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
      } else {
        msg = '确定禁用操作'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          row.status = 0
        } else {
          row.status = 1
        }
        this.rowUpdate(row)
      })
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    },
    handleFormEnabled() {
      this.form.status = 1
      this.$refs.crud.rowSave()
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
