<template>
  <div class="applicationForAdjustmentOfQuota">
    <!-- 基本信息 -->
    <basic-container>
      <div class="credit-limit-for-application">额度调整</div>
      <div class="boxs">
        <div class="boxs-to-apply-for-product">
          <h1 class="boxs-to-apply-for-product-h1">调整信息 ---已经使用{{ viewData.usedAmountStr }}</h1>
          <div class="amount-of-information">
            <!-- 调整额度的 -->
            <avue-form ref="adjustment" :option="adjustmentOption" v-model="adjustmentForm"></avue-form>
          </div>
        </div>
      </div>
    </basic-container>

    <!-- 操作按钮 -->
    <div class="applicationForAdjustmentOfQuota-menu">
      <span @click="cancel()">取消</span>
      <span @click="validate()">提交</span>
    </div>
  </div>
</template>

<script>
import { enterpriseQuotaDetail, enterpriseQuotaUpdate } from '@/api/riskmana/enterprisequota'

export default {
  data() {
    return {
      rowId: this.$route.query.id,
      look: this.$route.query.look,
      submitTag: false,
      viewData: {},
      adjustmentForm: {},
      adjustmentOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [
          {
            label: '申请额度',
            prop: 'newCreditAmount',
            type: 'input',
            placeholder: '请填写申请额度',
            span: 12,
            append: '万元',
            display: true,
            disabled: false,
            rules: [{ required: true, validator: this.No, trigger: 'blur' }],
          },
          {
            label: '额度类型',
            prop: 'lineType',
            type: 'select',
            placeholder: '请选择额度类型',
            span: 12,
            display: true,
            disabled: false,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=quota_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择额度类型',
                trigger: 'change',
              },
            ],
          },
          {
            label: '银行年利率',
            prop: 'bankAnnualInterestRate',
            type: 'input',
            placeholder: '请填写银行年利率',
            span: 12,
            append: '%',
            display: true,
            disabled: false,
            rules: [{ required: true, validator: this.validateNum, trigger: 'blur' }],
          },
          {
            label: '到期日',
            prop: 'dueDate',
            type: 'date',
            placeholder: '选择日期',
            span: 12,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd hh:mm:ss',
            display: true,
            disabled: false,
            rules: [{ required: true, validator: this.DataNo, trigger: 'blur' }],
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() < new Date().getTime() - 3600 * 1000 * 24
              },
            },
          },
          {
            label: '调整原因',
            prop: 'reason',
            type: 'textarea',
            maxlength: 20,
            minRows: 3,
            maxRows: 5,
            span: 24,
            showWordLimit: true,
            rules: [{ required: true, message: '请输入调整原因', trigger: 'blur' }],
          },
        ],
      },
    }
  },
  created() {
    this.enterpriseQuotaDetail()
  },

  methods: {
    validateNum(rule, value, callback) {
      let regName = /[^\d.]/g
      if (regName.test(value)) {
        callback(new Error('请确认只输入了数字'))
      } else if (!value) {
        callback(new Error('不能为空'))
      } else {
        callback()
      }
    },
    // 检验日期的
    DataNo(rule, value, callback) {
      const newDataArr = value.split(' ')[0].split('-')
      const oldDataArr = this.viewData.expireTime.split(' ')[0].split('-')
      const oldMoney = this.viewData.creditAmount
      const newMoney = this.adjustmentForm.newCreditAmount * 10000
      if (!value) {
        callback(new Error('日期不能为空'))
      } else if (newMoney < oldMoney) {
        if (oldDataArr[0] < newDataArr[0]) {
          callback(new Error('融资金额调小不能延长过期日'))
          this.adjustmentForm.dueDate = this.viewData.expireTime
        } else if (oldDataArr[0] == newDataArr[0] && oldDataArr[1] < newDataArr[1]) {
          callback(new Error('融资金额调小不能延长过期日'))
          this.adjustmentForm.dueDate = this.viewData.expireTime
        } else if (oldDataArr[0] == newDataArr[0] && oldDataArr[1] == newDataArr[1] && oldDataArr[2] < newDataArr[2]) {
          callback(new Error('融资金额调小不能延长过期日'))
          this.adjustmentForm.dueDate = this.viewData.expireTime
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validate() {
      this.$refs.adjustment.validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0].message
          if (!errMsg) {
            errMsg = Object.values(msg)[0][0].message
            if (!errMsg) {
              errMsg = '必填项未填'
            }
          }
          this.$message.error(errMsg)
          return
        }
        done()
        this.updata()
      })
    },
    No(rule, value, callback) {
      let regName = /[^\d.]/g
      if (regName.test(value)) {
        callback(new Error('请确认只输入了数字'))
      } else if (!value) {
        callback(new Error('不能为空'))
      } else if (value * 10000 < +this.viewData.usedAmount) {
        callback(new Error('申请金额最低不能低于已用金额'))
        this.adjustmentForm.creditLimitForApplication = this.viewData.usedAmount
      } else {
        callback()
      }
    },
    cancel() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({ path: '/riskmana/coreenterprisequota' })
    },
    async updata() {
      if (this.submitTag) return
      this.submitTag = true
      const {
        newCreditAmount,
        lineType: newQuotaType,
        bankAnnualInterestRate: newAnnualInterestRate,
        dueDate: newExpireTime,
        reason,
      } = this.adjustmentForm
      try {
        const {
          data: { code },
        } = await enterpriseQuotaUpdate({
          id: this.rowId,
          newCreditAmount: newCreditAmount * 10000 + '',
          newQuotaType,
          reason,
          newAnnualInterestRate,
          newExpireTime,
        })
        if (code != 200) return (this.submitTag = false)
        this.cancel()
        // 防重复提交
        setTimeout(() => {
          this.submitTag = false
        }, 3000)
      } catch (error) {
        this.submitTag = false
      }
    },
    // 查看请求
    enterpriseQuotaDetail() {
      const params = {
        id: this.rowId,
      }
      enterpriseQuotaDetail(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          const ite = resData.data
          this.adjustmentForm.productType = ite.productType // 产品类型
          this.adjustmentForm.selectTheManagement = ite.goodsName // 选择产品
          this.adjustmentForm.newCreditAmount = (+ite.creditAmount / 10000).toFixed(4) // 申请额度
          this.adjustmentForm.bankAnnualInterestRate = ite.annualInterestRate // 银行年利率
          this.adjustmentForm.effectiveDate = ite.effectiveTime // 生效日
          this.adjustmentForm.dueDate = ite.expireTime // 到期日
          this.adjustmentForm.lineType = ite.quotaType // 额度类型
          this.adjustmentForm.openingBank = ite.bank // 开户行
          this.adjustmentForm.creditCardNumbers = ite.bankCardNo // 银行卡号
          this.viewData = resData.data // 对整个data进行存储
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.applicationForAdjustmentOfQuota {
  ::v-deep {
    .basic-container__card {
      border-radius: 8px;
    }

    .avue-form__menu {
      display: none;
    }

    .el-input.is-disabled .el-input__inner {
      color: #000;
    }

    .el-input-group__append {
      color: #000;
    }
  }

  .credit-limit-for-application {
    height: 68px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    border-bottom: 1px solid rgba(233, 235, 239, 100);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: -20px;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .boxs {
    .boxs-to-apply-for-product {
      box-sizing: border-box;

      h1 {
        margin: 0;
      }

      .boxs-to-apply-for-product-h1 {
        height: 22px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        position: relative;

        &::before {
          content: '';
          display: inline-block;
          width: 8px;
          height: 16px;
          line-height: 20px;
          border-radius: 15px;
          background-color: rgba(18, 119, 255, 100);
          text-align: center;
          transform: translateY(2px);
          box-sizing: border-box;
          margin-right: 4px;
        }
      }

      .amount-of-information {
        margin-top: 30px;
      }
    }
  }

  .applicationForAdjustmentOfQuota-menu {
    height: 36px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 14px 0 50px;
    padding: 0 4px;

    & > span:first-child {
      width: 93px;
      height: 36px;
      line-height: 36px;
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
      margin-right: 16px;
      cursor: pointer;
    }

    & > span:last-child {
      width: 93px;
      height: 36px;
      line-height: 36px;
      border-radius: 4px;
      background-color: rgba(18, 119, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
