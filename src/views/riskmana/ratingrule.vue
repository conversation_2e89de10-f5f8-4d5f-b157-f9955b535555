<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-dropdown @command="syncTemplate">
          <el-button type="primary" size="small">
            风控模板同步<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, key) in supplierNos" :key="key" :label="key" :command="key">{{
                item
              }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot-scope="{ row, index, type }" slot="menuForm">
        <el-button
          type="primary"
          size="small"
          plain
          v-if="type == 'edit' || type == 'add'"
          @click="handleFormEnabled(row)"
        >启用
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.status == 0 && permission.ratingrule_view&&isSysTempalte(row)"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.status == 0 && permission.ratingrule_delete&&isSysTempalte(row)"
        >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          @click="enable(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
      <template slot-scope="{ scope, row }" slot="status">
        <el-tag type="success" v-if="row.status == 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>
      <template slot-scope="{ scope, row }" slot="typeNo">
        <el-tag v-if="row.typeNo == '1'">贷采融资</el-tag>
        <el-tag v-if="row.typeNo == '0'">应收账款质押</el-tag>
      </template>

      <template slot="levelRuleList1Form">
        <div class="rule-box">
          <table class="rule-table">
            <tr>
              <th class="btn" v-if="!state">
                <div class="xz" @click="newtab">
                  <i class="el-icon-plus"></i>
                </div>
              </th>
              <th class="level">
                <div>信用级别</div>
              </th>
              <th class="compare">
                <div>比较</div>
              </th>
              <th class="type">
                <div>分数类型</div>
              </th>
              <th class="value">
                <div>分值</div>
              </th>
              <th class="quota">
                <div>风险额度</div>
              </th>
            </tr>
            <tr v-for="(item, index) in slot" :key="index">
              <td class="delel" v-if="!state">
                <div class="del" @click="deltr(index)">
                  <i class="el-icon-delete"></i>
                </div>
              </td>
              <td class="level">
                <el-input
                  v-model="levelRuleList[index].grade"
                  placeholder=""
                  :disabled="state ? true : false"
                ></el-input>
              </td>
              <td class="compare">
                <el-select
                  v-if="slot"
                  v-model="levelRuleList[index].compareValue"
                  placeholder="请选择类型"
                  :disabled="state ? true : false"
                >
                  <el-option
                    v-for="item in compare"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </td>
              <td class="type">
                <el-select
                  v-model="levelRuleList[index].scoreType"
                  placeholder="选择类型"
                  :disabled="state ? true : false"
                >
                  <el-option
                    v-for="item in type1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </td>
              <td class="value">
                <div class="min">
                  <p v-if="!levelRuleList[index].scoreType">小:</p>
                  <el-input
                    v-model="levelRuleList[index].scoreLeft"
                    :placeholder="!levelRuleList[index].scoreType ? '起' : ''"
                    :disabled="state ? true : false"
                  ></el-input>
                  <span v-if="!levelRuleList[index].scoreType">~</span>
                  <p v-if="!levelRuleList[index].scoreType">大:</p>
                  <el-input
                    v-if="!levelRuleList[index].scoreType"
                    v-model="levelRuleList[index].scoreRight"
                    placeholder="至"
                    :disabled="state ? true : false"
                  ></el-input>
                </div>
                <!-- <el-input
                  v-if="levelRuleList[index].scoreType"
                  v-model="levelRuleList[index].fraction"
                  placeholder=""
                  @change="abc"
                  :disabled="state ? true : false"
                ></el-input> -->
              </td>
              <td class="quota">
                <el-input
                  v-model="levelRuleList[index].limitAmount"
                  placeholder="请输入额度"
                  :disabled="state ? true : false"
                >
                  <template slot="append">万元</template>
                </el-input>
              </td>
            </tr>
          </table>
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  add,
  batchDisabled,
  batchEnabled,
  getDetail,
  getList,
  mapSupplierByType,
  remove,
  syncTemplate,
  update,
} from '@/api/riskmana/ratingrule'
import {mapGetters} from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      supplierNos: {},
      loading: true,
      addState: 0,
      canOperator: false,
      state: 0,
      levelRuleList: [],
      slot: 0,
      compare: [
        {
          label: '<=',
          value: 'le',
        },
        {
          label: '<',
          value: 'lt',
        },
        {
          label: '=',
          value: 'eq',
        },
        {
          label: '>',
          value: 'gt',
        },
        {
          label: '>=',
          value: 'ge',
        },
      ],
      type1: [
        {
          label: '范围值',
          value: 0,
        },
        {
          label: '固定值',
          value: 1,
        },
      ],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        delBtn: false,
        editBtn: false,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '模板编号',
            search: true,
            prop: 'ratingNo',
            display: false,
          },
          {
            label: '模板名称',
            search: true,
            span: 24,
            prop: 'name',
            rules: [
              {
                required: true,
                message: '请输入模板名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '风控模板',
            search: true,
            type: 'tree',
            dicUrl: '/api/blade-riskmana/web-back/riskmana/riskmanatemplate/listDetails',
            props: {
              label: 'name',
              value: 'id',
            },
            prop: 'templateId',
            rules: [
              {
                required: true,
                message: '请选择风控模板',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '风险限额',
            display: false,
            prop: 'strLimit',
          },
          {
            label: '有效周期（天）',
            prop: 'period',
            type: 'number',
            minRows: 1,
            maxRows: 10000,
            rules: [
              {
                required: true,
                message: '请输入有效周期（天）',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '操作人',
            prop: 'userName',
            display: false,
          },
          {
            label: '状态',
            prop: 'status',
            display: false,
          },
          {
            label: '上次修改时间',
            prop: 'updateTime',
            display: false,
          },
          //改样式
          {
            label: '级别规则',
            prop: 'levelRuleList1',
            slot: true,
            row: true,
            hide: true,
            span: 24,
          },
          {
            label: '所属供应商',
            prop: "supplierNo",
            type: "select",
            display: false,
            dicUrl: '/api/blade-system/dict/dictionary?code=otherApi',
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.ratingrule_add, false),
        viewBtn: this.vaildData(this.permission.ratingrule_view, false),
        delBtn: this.vaildData(this.permission.ratingrule_delete, false),
        editBtn: this.vaildData(this.permission.ratingrule_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {
    levelRuleList: {
      handler(newVal) {
        console.log(newVal)
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    isSysTempalte(row) {
      return 'jrSupplyRisk' == row.supplierNo
    },
    syncTemplate(key) {
      this.isLoad = true
      syncTemplate(key).then(res => {
        if (res.data.code == 200) {
          this.isLoad = false
          this.$message({
            type: 'success',
            message: '数据同步成功'
          })
        }
      })
    },
    abc() {
      console.log(1)
    },
    newtab() {
      this.slot += 1
      this.levelRuleList.push({
        grade: '',
        compareValue: '',
        scoreType: '',
        scoreLeft: '',
        scoreRight: '',
        fraction: '',
        limitAmount: '',
      })
    },
    deltr(index) {
      if (this.slot > 0) {
        this.slot -= 1
        this.levelRuleList.splice(index, 1)
      }
    },
    enable(row, done) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
      } else {
        msg = '确定禁用操作'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          batchDisabled(row.id).then(() => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          })
        } else {
          batchEnabled(row.id).then(() => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          })
        }
      })
    },
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    handleFormEnabled() {
      this.form.status = 1
      this.$refs.crud.rowSave()
    },
    rowSave(row, done, loading) {
      //   for (let item of this.levelRuleList) {
      //     if (item.scoreType) {
      //       item.scoreLeft = JSON.stringify(JSON.parse(item.fraction))
      //       console.log(item.scoreType)
      //     }
      //   }
      row.levelRuleList = this.levelRuleList
      console.log(row.levelRuleList)
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.levelRuleList = []
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeClose(done, type) {
      if (type == 'add') {
        if (this.addState) {
          this.addState = 0
        }
      }
      this.slot = 0
      this.levelRuleList = []
      done()
    },
    beforeOpen(done, type) {
      if (type == 'view') {
        this.state = 1
      }
      if (type == 'add') {
        console.log(type)
        this.state = 0
        this.levelRuleList = []
      }
      if (type == 'edit') {
        this.state = 0
      }
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          //   console.log(res.data.data)
          this.form = res.data.data
          this.levelRuleList = res.data.data.levelRuleList
          this.slot = res.data.data.levelRuleList.length
          //   for (let item of res.data.data.levelRuleList) {
          //     console.log(item)
          //     if (item.scoreType) {
          //       item.fraction = item.scoreLeft
          //     }
          //   }
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
      mapSupplierByType("riskOrderSys").then(res => {
        console.log(res.data.data)
        this.supplierNos = res.data.data;
      })
    },
  },
}
</script>

<style scoped>
.min {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.min span {
  flex: 1;
  text-align: center;
}

.min p {
  width: 40px;
  margin: 0;
  text-align: center;
  height: 56px;
  line-height: 56px;
}

.rule-box {
  width: 100%;
}

.rule-table {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-collapse: inherit;
  border-bottom: 0;
}

.rule-table th {
  /* padding: 0 0 0 8px; */
  box-sizing: border-box;
  height: 49px;
  /* text-align: left; */
  background-color: #fafafa;
  font-size: 14px;
  font-weight: 400;
  border: 1px solid #dcdfe6;
}

.rule-table th:nth-child(1) {
  border-top: 0;
  border-left: 0;
}

.rule-table th:nth-child(2) {
  border-top: 0;
  border-left: 0;
}

.rule-table th:nth-child(3) {
  border-top: 0;
  border-left: 0;
}

.rule-table th:nth-child(4) {
  border-top: 0;
  border-left: 0;
}

.rule-table th:nth-child(5) {
  border-top: 0;
  border-left: 0;
}

.rule-table th:nth-child(6) {
  border-top: 0;
  border-left: 0;
  border-right: 0;
}

.rule-table td {
  height: 49px;
  padding: 0 3px 0 3px;
  box-sizing: border-box;
  font-size: 14px;
  font-weight: 400;
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #dcdfe6;
}

.rule-table td:nth-child(1) {
  border-top: 0;
  border-left: 0;
}

.rule-table tr:hover {
  background-color: #fafafa;
}

.rule-table td:nth-child(2) {
  border-top: 0;
  border-left: 0;
}

.rule-table td:nth-child(3) {
  border-top: 0;
  border-left: 0;
}

.rule-table td:nth-child(4) {
  border-top: 0;
  border-left: 0;
}

.rule-table td:nth-child(5) {
  border-top: 0;
  border-left: 0;
}

.rule-table td:nth-child(6) {
  border-top: 0;
  border-left: 0;
  border-right: 0;
}

.rule-table .btn {
  box-sizing: border-box;
  width: 50px;
  height: 49px;

  /* border: 0; */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.rule-table .delel {
  box-sizing: border-box;
  width: 50px;
  height: 58px;
  display: flex;
  justify-content: center;
  align-items: center;

  /* border: 0; */
  padding: 0;
}

.rule-table .btn .xz {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #3894ff;
  line-height: 28px;
  text-align: center;
  font-size: 14px;
  color: #ffffff;
}

.rule-table .level {
  width: 13%;
  height: 49px;
}

.rule-table .compare {
  width: 17%;
  height: 49px;
}

.rule-table .type {
  width: 15%;
  height: 49px;
}

.rule-table .value {
  width: 28%;
  height: 49px;
}

.rule-table .quota {
  width: 23%;
  height: 49px;
}

.rule-table .delel .del {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 77, 54, 0.1);
  line-height: 28px;
  text-align: center;
  font-size: 14px;
  color: #ff4d36;
}

.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
