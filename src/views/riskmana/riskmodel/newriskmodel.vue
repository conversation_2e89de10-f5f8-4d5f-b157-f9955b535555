<template>
  <div class="newriskmodel">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">新增风控模板</div>
      </div>
      <div class="new-box">
        <avue-form :data="form" :option="option"></avue-form>
      </div>
      <div class="qiye-box">
        <avue-form v-model="form" :option="option2"></avue-form>
        <div class="transfer-box">
          <el-transfer
            filterable
            :filter-method="filterMethod1"
            filter-placeholder="请输入行业名称"
            :titles="['未选指标', '已选指标']"
            v-model="value1"
            :data="data1"
          >
          </el-transfer>
        </div>
      </div>
        <div class="btn-box">
          <div class="no">取消</div>
          <p class="yes">保存</p>
          <span class="up">启用</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {},
      option: {
        emptyBtn: false,
        submitBtn: false,
        gutter: 60,
        column:[
            {
                label:'模板名称',
                prop:'name',
                span:24,
                type:'input',
                rules:[
                    {
                        required:true,
                        message:'请输入模板名称',
                        trigger:'blur'
                    }
                ]
            },{
                label:'业务类型',
                prop:'type',
                type:'select',
                span:10,
                rules:[{
                    required:true,
                    message:'请选择模板类型',
                    trigger:'blur'
                }],
                dicData:[
                    {
                        label:'sbb',
                        value:'wdnmd'
                    },{
                        label:'asd',
                        value:'wasas'
                    }
                ]
            }
        ]
      },
      option2: {
        emptyBtn: false,
        submitBtn: false,
        gutter: 60,
        column: [
          {
            label: '选择企业',
            prop: 'text',
            span: 1,
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请输入姓名',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
    }
  },
}
</script>
<style scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.new-box{
    margin-top: 20px;
}
.qiye-box {
  position: relative;
  margin-top: -30px;
  z-index: 8;
  width: 100%;
  height: 360px;
  display: flex;
  align-items: flex-start;
}
.transfer-box {
  position: absolute;
  top: 0;
  left: 120px;
  width: 100%;
  z-index: 9;
  flex: 1;
  height: 100%;
}
.btn-box{
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.btn-box *{
    width: 60px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
}
.btn-box .no{
    border: 1px solid #bbbbbb;
    margin-right: 12px;
}
.btn-box .yes{
    background-color: #2dad28;
    color: #ffffff;
    margin-right: 12px
}
.btn-box .up{
    background-color: #1277ff;
    color: #ffffff;
}
</style>
