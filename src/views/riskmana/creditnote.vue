<template>
  <div class="risk">
    <div class="risk-box">
      <avue-crud
        :data="data"
        :option="option"
        :before-open="beforeOpen"
        :page.sync="page"
        @on-load="onLoad"
        @row-del="rowDel"
        @row-update="rowUpdate"
      >
        <template slot="menuLeft" slot-scope="{ size }">
          <el-button
            type="danger"
            icon="el-icon-delete"
            :size="size"
            @click="toggleSelection()"
            >删除</el-button
          >
        </template>
        <template slot-scope="{ type, size }" slot="menu">
          <el-button icon="el-icon-check" :size="size" :type="type"
            >启用</el-button
          >
        </template>
      </avue-crud>
      <New v-if="xian"></New>
      <div v-if="xian" class="zzc"></div>
    </div>
  </div>
</template>
<script>
import New from './components/new.vue'
import { getshujulist, delshuju } from '../../api/risk/risk-tab'
export default {
  components: { New },
  data() {
    return {
      xian: 0,
      data: [
        {
          id: 1,
          name: '张三',
          type: '男',
        },
      ],
      page: {
        currentPage: 1,
        total: 0,
        layout: 'total,pager,prev, next',
        pageSize: 10,
      },
      option: {
        selection: true,
        menuWidth: 260,
        viewBtn: true,
        border: true,
        index: true,
        tip: false,
        column: [
          {
            label: '表单名称',
            prop: 'creditNoteName',
          },
          {
            label: '类型',
            prop: 'type',
          },
        ],
      },
    }
  },
  methods: {
    selectionChange(list) {
      this.$message.success('选中的数据' + JSON.stringify(list))
    },
    // toggleSelection(val) {
    //   // this.$refs.crud.toggleSelection(val)
    //   // console.log(val);
    // },
    beforeOpen(done, type) {
      if (type == 'add') {
        this.xian = 1
      } else {
        done()
      }
    },
    // rowUpdate(row){
    huoq(){
      getshujulist().then(res => {
            console.log(res.data)
            this.page.total = res.data.data.records.length
            this.data = res.data.data.records
          })
    },
    // },
    rowDel(row) {
      // console.log(row.id);
      delshuju(row.id).then(res => {
        // console.log(res.data.msg);
        if (res.data.msg == '操作成功') {
          this.huoq()
        }
      })
    },
  }, 
  mounted() {
    this.huoq()
  },
}
</script>
<style scoped>
.risk {
  background-color: #ffffff;
  border-radius: 16px;
  margin-left: 10px;
}
.risk-box {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}
/deep/ .el-table--enable-row-transition .el-table__body td {
  width: 280px;
}
.zzc {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 8;
}
</style>
