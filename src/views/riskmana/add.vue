<template>
  <basic-container>
    <div class="collect-newly">
      <!-- up -->
      <header>
        <h1 class="title-top">基本信息</h1>
        <div class="guaranteeSetTop">
          <avue-form ref="form" :option="option" v-model="information">
          </avue-form>
        </div>
      </header>
      <!-- down -->
      <el-collapse v-model="activeNames">
        <transition-group tag="ul" class="drag-list" name="drag">
          <el-collapse-item
            :class="{ fromCollapse: true }"
            :title="item.templateName"
            :name="item.orderNum"
            v-for="(item, index) in list"
            :key="item.orderNum"
            :draggable="draggStart"
            @dragstart.native="dragStartCard($event, item, index)"
            @dragenter.native="dragEnterCard($event, item, index)"
            @dragover.native="dragOverCard($event, item, index)"
            @drop.native="dropCard($event, item, index)"
          >
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader" @keyup.stop="prevent()">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': activeNames.includes(item.orderNum),
                    }"
                  ></i>
                  <div @click.stop="prevent()" class="edit-input">
                    <el-input
                      v-if="detail && index == inpClickNum"
                      v-model="list[index].templateName"
                      placeholder="请输入模版名称"
                      clearable
                      size="medium"
                      :id="`inp${index}`"
                      @blur="draggStopFlip('detail')"
                      @focus="draggStop"
                    ></el-input>
                    <span
                      v-else
                      @click.stop="detailFlip(index)"
                      :class="{ inpactive: !item.templateName }"
                    >
                      {{
                        item.templateName
                          ? item.templateName
                          : '点击编辑模版名称'
                      }}
                    </span>
                  </div>
                  <i
                    class="el-icon-edit"
                    @click.stop="detailFlip(index)"
                    v-if="!lookCollect"
                  ></i>
                </div>
                <div class="fromHeadermenu" v-if="!lookCollect">
                  <i
                    :class="{
                      'el-icon-top': index != 0,
                      'el-icon-pear': index == 0,
                    }"
                    @click.stop="operateMenuUp(item, index)"
                  ></i>
                  <i
                    :class="{
                      'el-icon-bottom': index != list.length - 1,
                      'el-icon-pear': index == list.length - 1,
                    }"
                    @click.stop="operateMenuDown(item, index)"
                  ></i>
                  <i
                    class="el-icon-delete-solid"
                    @click.stop="operateMenuDelete(item, index)"
                  ></i>
                </div>
              </div>
            </template>
            <!-- 子表单 -->
            <div class="guaranteeSetBottom" @click="nameless(item)">
              <avue-form
                :option="subformOption"
                v-model="obj[item.data].dynamic"
              >
                <template slot-scope="{ row }" slot="tableDesc">
                  <div>{{ row.tableDesc }}</div>
                </template>
                <template slot-scope="{ row }" slot="fieldDesc">
                  <div>{{ row.fieldDesc }}</div>
                </template>
                <template slot-scope="{ row }" slot="dataType">
                  <el-tag type="" effect="plain">{{ row.dataType }}</el-tag>
                </template>
              </avue-form>
            </div>
          </el-collapse-item>
        </transition-group>
      </el-collapse>
      <!-- 增加子表单 -->
      <div class="subitems" @click="addbtn" v-if="!lookCollect">
        <div class="subadd">
          <i class="el-icon-circle-plus"></i>
          <span>新增模块</span>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="updata" v-if="!lookCollect">
        <el-button @click="goBack()">取消</el-button>
        <el-button type="success" v-if="upDateType" @click="upData('go')"
          >上架</el-button
        >
        <el-button type="primary" v-if="upDateType" @click="upData()"
          >保存</el-button
        >
        <el-button type="success" v-if="!upDateType" @click="setData('up')"
          >上架</el-button
        >
        <el-button type="primary" v-if="!upDateType" @click="setData()"
          >保存</el-button
        >
      </div>
      <div class="updata" v-else>
        <el-button @click="goBack()">取消</el-button>
      </div>

      <!-- 弹窗 -->
      <el-dialog
        title="选择业务字段"
        :visible.sync="popupShow"
        :append-to-body="true"
        width="50%"
      >
        <!--
        arrOption: 配置 arr
        data: 数据源 arr
        search.sync: 获取实时的搜索值 obj
        search-change: 搜索点击事件 fan
        current-change: 分页功能页码点击事件 fan
        on-load: 首次加载事件 fan
        size-change: 分页总页数事件 fan
        selection-change: checkbox事件 fan
        table-loading: 表单加载动画 boolean
        page: 配置分页信息 obj
        -->
        <avue-crud
          ref="crud"
          :option="arrOption"
          :data="arrS"
          @search-change="searchChange"
          @search-reset="searchReset"
          @current-change="currentChangeScope"
          @size-change="sizeChangeScope"
          @selection-change="selectionChange"
          @on-load="onLoad"
          @row-click="handleRowClick"
          :table-loading="loading"
          :page="page"
        >
          <template slot-scope="{ row }" slot="node">
            <el-tag type="info">{{ row.node }}</el-tag>
          </template>
        </avue-crud>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cardfals">取 消</el-button>
          <el-button @click="cardEngth" type="primary">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </basic-container>
</template>

<script>
// import { set } from 'vue/types/umd'
import {
  getBusinessList,
  creditFrom,
  getDictionaryTwo,
  getFromDetail,
  upFromData,
} from '@/api/riskmana/creditfrom'
// import {mapGetters} from "vuex";
// import func from "@/util/func";

export default {
  data() {
    return {
      upDateType: false,
      lookCollect: false,
      draggStart: true,
      detail: false,
      absolutelyNum: 1,
      inpClickNum: -1,
      instantlyData: '',
      activeNames: [],
      dragStartIndex: -1,
      dragStartData: '',

      lookId: '',

      popupShow: false,
      cardList: [],
      loading: true,
      information: {
        fromName: '',
        goodsType: '',
      },
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [
          {
            label: '表单名称',
            prop: 'fromName',
            span: 12,
            placeholder: '请输入表单名称',
            disabled: false,
            rules: [
              {
                required: true,
                message: '请输入表单名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '业务类型',
            prop: 'goodsType',
            span: 12,
            placeholder: '请选择业务类型',
            disabled: false,
            rules: [
              {
                required: true,
                message: '请选择业务类型',
                trigger: 'change',
              },
            ],
            type: 'select',
            dicData: [
              // {
              //   label: '应收账款',
              //   value: 0,
              // },
              // {
              //   label: '代采融资',
              //   value: 1,
              // },
            ],
          },
        ],
      },
      // 手风琴element组件
      obj: {},
      list: [],
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 110,
        typeslot: true,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            // disabled: true,
            children: {
              delBtn: true,
              addBtn: true,
              align: 'center',
              headerAlign: 'center',
              rowAdd: () => {
                this.popupShow = true
                // this.$message.success('新增回调')
                // done({
                //   input: '默认值',
                // })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                // this.checkUpType(row)
                done()
              },
              column: [
                // {
                //   width: 90,
                //   label: '序号',
                //   prop: 'le',
                //   disabled: true,
                //   // type: 'text',
                //   formslot: true,
                // },
                {
                  width: 300,
                  label: '表名称',
                  prop: 'tableDesc',
                  disabled: true,
                  align: 'left',
                  // formslot: true,
                  // type: 'select',
                },
                {
                  width: 300,
                  label: '字段名称',
                  prop: 'fieldDesc',
                  disabled: true,
                  align: 'left',
                  // formslot: true,
                  // type: 'select',
                },
                {
                  width: 250,
                  label: '表单类型',
                  prop: 'dataType',
                  disabled: true,
                  // formslot: true,
                  // type: 'select',
                },
                {
                  label: '排序',
                  prop: 'orderNum',
                  disabled: false,
                  clearable: false,
                  align: 'center',
                  placeholder: false,
                  // type: 'select',
                  focus: () => {
                    this.draggStop()
                  },
                  blur: () => {
                    this.draggStopFlip()
                  },
                },
                {
                  label: '必填',
                  prop: 'must',
                  type: 'checkbox',
                  disabled: false,
                  dicData: [
                    {
                      label: '',
                      value: '1',
                    },
                  ],

                  // disabled: false,
                  // formslot: true,
                  // type: 'select',
                },
              ],
            },
          },
        ],
      },
      arrS: [],
      arr: [
        // {
        //   indexName: '合同列表1',
        //   tableName: 'hours',
        //   fieldName: 'status',
        //   dataType: '数字',
        //   id: '1',
        // },
      ],
      page: {
        // total: 27,
        // pageSize: 10,
      },
      arrOption: {
        selection: true,
        tip: false,
        align: 'center',
        menuAlign: 'center',
        menuWidth: 400,
        viewBtn: true,
        menu: false,
        addBtn: false,
        printBtn: false,
        excelBtn: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        searchMenuSpan: 7, // 搜索菜单栏宽带
        maxHeight: 300,
        index: true,
        menuTitle: '其它',
        addTitle: '保存标题',
        editTitle: '编辑标题',
        viewTitle: '查看标题',
        searchBtnText: '搜索',
        emptyBtnText: '清空',
        addBtnText: '新增文案',
        delBtnText: '删除文案',
        delBtnIcon: 'null',
        editBtnText: '编辑文案',
        viewBtnText: '查看文案',
        printBtnText: '打印文案',
        excelBtnText: '导出文案',
        updateBtnText: '修改文案',
        saveBtnText: '保存文案',
        cancelBtnText: '取消文案',
        column: [
          {
            label: 'id',
            prop: 'id',
            hide: true,
            display: false,
          },
          {
            label: '字段描述',
            prop: 'indexName',
            search: true,
            searchSpan: 10,
            searchClearable: false,
            placeholder: '按字段搜索',
            span: 10,
          },
          {
            // width: 200,
            label: '表名',
            prop: 'tableName',
            search: true,
            searchSpan: 10,
            searchClearable: false,
            placeholder: '按表名搜索',
          },
          {
            // width: 200,
            label: '字段名',
            prop: 'fieldName',
            search: true,
            searchSpan: 10,
            searchClearable: false,
            placeholder: '按字段名搜索',
          },
          {
            // width: 200,
            label: '数据类型',
            prop: 'dataType',
            search: true,
            searchSpan: 10,
            searchClearable: false,
            placeholder: '按类型搜索',
          },
        ],
      },
      checkif: false,
      lock: false,
    }
  },
  watch: {
    popupShow(val) {
      if (!val) {
        // 弹窗关闭清空搜索值
        this.$refs.crud.searchReset()
      }
    },
  },
  created() {
    // http请求
    const stringCode = 'jrzh_resource_business_field_data_type'
    const stringType = 'goods_type'
    // 业务字典表随时可能变化
    getDictionaryTwo(stringType).then(res => {
      // 业务类型字典
      const resed = res.data
      if (resed.code == 200) {
        const dicArr = []
        for (const item of resed.data) {
          dicArr.push({
            label: item.dictValue,
            value: Number(item.dictKey),
          })
        }
        this.option.column[1].dicData = dicArr
      }
    })
    getDictionaryTwo(stringCode).then(res => {
      // 数据类型字典
      const resed = res.data
      if (resed.code == 200) {
        const dictionaryTypeArr = []
        for (const item of resed.data) {
          dictionaryTypeArr.push({
            key: item.dictKey,
            value: item.dictValue,
          })
        }
        getBusinessList().then(res => {
          // 获取业务字段list
          const resed = res.data
          let num = 0
          if (resed.code == 200) {
            const beforeArr = []
            for (const item of resed.data) {
              const keyS = dictionaryTypeArr.filter(itemS =>
                itemS.key.includes(item.dataType)
              )
              beforeArr.push({
                indexName: item.indexName,
                tableName: item.tableName,
                fieldName: item.fieldName,
                dataKey: item.dataType,
                dataType: keyS[0].value,
                tableDesc: item.tableDesc,
                fieldDesc: item.fieldDesc,
                id: item.id,
                num: num,
              })
              num++
            }
            this.arr = beforeArr
          }
          if (sessionStorage.getItem('lookCollect') == 'true') {
            // 查看状态将对应组件进行禁用
            this.view(sessionStorage.getItem('lookCollectID'))
            this.lookCollect = true
            for (const item of this.option.column) {
              item.disabled = true
            }
            for (const item of this.subformOption.column[0].children.column) {
              item.disabled = true
            }
            this.subformOption.column[0].children.addBtn = false
            this.subformOption.column[0].children.delBtn = false
          } else if (sessionStorage.getItem('lookCollect') == 'false') {
            // 编辑
            this.upDateType = true
            this.view(sessionStorage.getItem('lookCollectID'))
          }
        })
      }
    })
  },
  mounted() {
    // 清除组件默认class样式
    document.querySelector('.el-card__body').classList.remove('el-card__body')
  },
  methods: {
    draggStop() {
      this.draggStart = false
    },
    draggStopFlip(detail) {
      if (detail) {
        this.detail = !this.detail
      }
      this.draggStart = true
    },
    dropCard(e, item, index) {
      if (index != this.dragStartIndex) {
        // this.list = this.swapArr(this.list, index, this.dragStartIndex)
        // this.swapObj(this.obj, this.dragStartData, this.obj, item.data)
      }
    },
    dragOverCard(e) {
      e.preventDefault()
    },
    dragEnterCard(e, item, index) {
      e.preventDefault()
      // 避免源对象触发自身的dragenter事件
      if (this.dragStartIndex !== index && !this.lock) {
        this.lock = true
        const source = this.list[this.dragStartIndex]
        this.list.splice(this.dragStartIndex, 1)
        this.list.splice(index, 0, source)
        // 排序变化后目标对象的索引变成源对象的索引
        this.dragStartIndex = index
        setTimeout(() => {
          this.lock = false
        }, 100)
      }
    },
    dragStartCard(e, item, index) {
      // ev.preventDefault()
      this.dragStartIndex = index
      this.dragStartData = item.data
    },
    swapArr(Darr, index1, index2) {
      /*数组两个元素位置互换*/
      Darr[index1] = Darr.splice(index2, 1, Darr[index1])[0]
      return Darr
    },
    swapObj(sourceObj, sourceKey, targetObj, targetKey) {
      /*对象两个属性位置互换*/
      let temp = sourceObj[sourceKey]
      sourceObj[sourceKey] = targetObj[targetKey]
      targetObj[sourceKey] = temp
      return sourceObj
    },
    nameless(item) {
      this.instantlyData = item.data
      const exception = setInterval(() => {
        if (this && this.popupShow) {
          this.filterDiaolArr()
          setTimeout(() => {
            this.checkUpType()
          }, 100)
          clearInterval(exception)
        }
      }, 100)
    },
    operateMenuUp(item, index) {
      this.list = this.swapArr(this.list, index, index - 1)
    },
    operateMenuDown(item, index) {
      this.list = this.swapArr(this.list, index, index + 1)
    },
    operateMenuDelete(item, index) {
      this.list.splice(index, 1)
      delete this.obj[item.data]
      if (!this.list.length) {
        this.absolutelyNum = 1
      }
    },
    setData(type) {
      // 保存前校验
      this.$refs.form.validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0][0].message
          if (!errMsg) {
            errMsg = '必填项未填'
          }
          this.$message.error(errMsg)
        } else {
          const listD = JSON.parse(JSON.stringify(this.list))
          const objD = JSON.parse(JSON.stringify(this.obj))
          for (let i = 0; i < listD.length; i++) {
            listD[i].orderNum = i + 1
            if (objD[listD[i].data].dynamic) {
              listD[i].creditFromFields = []
              for (const item of objD[listD[i].data].dynamic.dynamic) {
                listD[i].creditFromFields.push({
                  dataType: item.dataKey,
                  tableDesc: item.tableDesc,
                  tableName: item.tableName,
                  fieldName: item.fieldName,
                  fieldDesc: item.fieldDesc,
                  isRequired: item.must && item.must.length ? 1 : 0,
                  orderNum: item.orderNum,
                  businessFieldId: item.id,
                })
              }
              delete listD[i].data
              delete listD[i].fromId
            }
          }
          // 新增数据
          const data = {
            fromName: this.information.fromName,
            goodsType: this.information.goodsType,
            creditFromTemplates: listD,
            status: type ? 1 : 0,
          }
          // 新增请求
          creditFrom(data).then(res => {
            if (res.data.code == 200) {
              this.$message.success('新增成功')
              if (type) {
                setTimeout(() => {
                  this.$message.success('已启用')
                  this.goBack()
                }, 100)
              }
            }
          })
          done()
        }
      })
    },
    upData(type) {
      // 保存前校验
      this.$refs.form.validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0][0].message
          if (!errMsg) {
            errMsg = '必填项未填'
          }
          this.$message.error(errMsg)
        } else {
          const listUP = JSON.parse(JSON.stringify(this.list))
          const objUP = JSON.parse(JSON.stringify(this.obj))
          const upDataArr = []
          for (let i = 0; i < listUP.length; i++) {
            upDataArr.push({
              orderNum: i + 1,
              id: listUP[i].fromId ? listUP[i].fromId : 'null',
              templateName: listUP[i].templateName,
            })
            if (objUP[listUP[i].data].dynamic) {
              upDataArr[i].creditFromFields = []
              for (const item of objUP[listUP[i].data].dynamic.dynamic) {
                upDataArr[i].creditFromFields.push({
                  dataType: item.dataKey,
                  tableDesc: item.tableDesc,
                  tableName: item.tableName,
                  fieldName: item.fieldName,
                  fieldDesc: item.fieldDesc,
                  isRequired: item.must && item.must.length ? 1 : 0,
                  orderNum: item.orderNum,
                  businessFieldId: item.id || item.businessFieldId,
                  id: item.dictionaryID,
                })
              }
              delete listUP[i].data
            }
          }
          // 编辑数据
          const data = {
            fromName: this.information.fromName,
            goodsType: this.information.goodsType,
            id: this.lookId,
            creditFromTemplates: upDataArr,
            status: type ? 1 : 0,
          }
          // 编辑请求
          upFromData(data).then(res => {
            if (res.data.code == 200) {
              this.$message.success('保存成功')
              if (type) {
                setTimeout(() => {
                  this.$message.success('已启用')
                  this.goBack()
                }, 100)
              }
            }
          })
          done()
        }
      })
    },
    goBack() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({ path: '/riskmana/creditfrom' })
    },
    view(id) {
      getFromDetail(id).then(res => {
        const resed = res.data
        this.lookId = resed.data.id
        if (resed.code == 200) {
          const fromData = resed.data
          this.information.fromName = fromData.fromName
          this.information.goodsType = fromData.goodsType
          // 基本信息赋值
          const fromSArr = []
          let dynamic = []
          this.list = []
          for (const item of fromData.creditFromTemplates) {
            this.list.push({
              // 子表单list数据拆分
              orderNum: item.orderNum,
              templateName: item.templateName,
              fromId: item.id,
              data: `dyn${item.orderNum}`,
            })
            for (const itemS of item.creditFromFields) {
              let dataT = ''
              let numed = 0
              let must = ''
              for (const itemed of this.arr) {
                const indexN = `${itemS.tableNmae}_${itemS.fieldName}`
                const indexOrigin = `${itemed.tableNmae}_${itemed.fieldName}`
                if (indexOrigin == indexN) {
                  // 对dataType根据字典数据进行处理
                  dataT = itemed.dataType
                  numed = itemed.num
                  // checkbox反选唯一标识添加
                  if (itemS.isRequired) {
                    must = ['1']
                  }
                }
              }
              dynamic.push({
                tableDesc: itemS.tableDesc,
                fieldDesc: itemS.fieldDesc,
                orderNum: itemS.orderNum,
                must: must,
                dataType: dataT,
                dataKey: itemS.dataType,
                num: numed,
                dictionaryID: itemS.id,
                businessFieldId: itemS.businessFieldId,
              })
            }
            const fromArr = { dynamic }
            fromSArr.push({
              orderNum: item.orderNum,
              // templateName: item.templateName,
              dynamic: fromArr,
            })
            dynamic = []
          }
          fromSArr.forEach(item => {
            this.obj[`dyn${item.orderNum}`] = item
          })
        }
      })
    },
    prevent() {
      // 用于阻止elm组件事件冒泡
    },
    detailFlip(index) {
      if (this.lookCollect) return
      this.inpClickNum = index
      this.detail = !this.detail
      setTimeout(() => {
        document.getElementById(`inp${index}`).focus()
        // 让input得焦
      }, 300)
    },
    addbtn() {
      if (this.list.length) {
        this.absolutelyNum += this.list.length
      }
      this.obj[`dyn${this.absolutelyNum}`] = {}
      this.list.push({
        // id: this.absolutelyNum,
        orderNum: this.absolutelyNum,
        templateName: '',
        data: `dyn${this.absolutelyNum}`,
      })
      this.absolutelyNum++
    },
    searchChange(params, done) {
      // card搜索事件
      let ar = []
      const arrD = ['indexName', 'tableName', 'fieldName', 'dataType']
      this.filterDiaolArr()
      for (const item of this.arrS) {
        for (const items of arrD) {
          if (item[items] && item[items].indexOf(params.indexName) != -1) {
            ar.push(item)
            break
          }
        }
      }
      this.arrS = ar
      setTimeout(() => {
        this.checkUpType()
      }, 100)
      done()
    },
    searchReset() {
      this.filterDiaolArr()
      setTimeout(() => {
        this.checkUpType()
      }, 100)
    },
    // 实现过滤已选择数据
    filterDiaolArr() {
      this.arrS = this.arr
      const filterArr = []
      for (const key in this.obj) {
        if (key != this.instantlyData) {
          const items = this.obj[key].dynamic.dynamic
          if (items.length) {
            for (const itemed of items) {
              // 将其他子表单已选择项添加至同一个过滤数组
              filterArr.push(`${itemed.tableDesc}_${itemed.fieldDesc}`)
            }
          }
        }
      }
      this.arrS = this.arrS.filter(
        // 将其他已选择的项 过滤掉，重新赋值
        item => filterArr.indexOf(`${item.tableDesc}_${item.fieldDesc}`) === -1
      )
    },
    // 弹窗表格点击勾选checkout
    handleRowClick(row) {
      this.toggleSelection([
        this.arrS[
          this.getArrayIndex2(this.arrS, `${row.tableDesc}_${row.fieldDesc}`)
        ],
      ])
    },
    onLoad() {
      // card首次加载事件
      this.loading = false
      this.arrS = this.arr
    },
    currentChangeScope(currentPage) {
      // 分页页码切换事件
      return console.log(currentPage)
    },
    sizeChangeScope(pageSize) {
      // 分页页数切换事件
      return console.log(pageSize)
    },
    selectionChange(cardList) {
      // checkbox事件
      this.cardList = cardList
      if (!this.checkif) {
        this.checkif = true
      }
    },
    cardEngth() {
      // 弹窗表格确认按钮
      this.popupShow = false
      this.upBeforeCheck()
    },
    checkUpType(rows) {
      // 根据rows是否存在判断是选择操作还是删除操作 不需要了的说
      const iData = this.instantlyData
      if (iData) {
        const forArr = this.obj[iData].dynamic.dynamic
        if (!forArr.length) {
          // 表单无数据，用户并没有进行保存操作，对checkbox状态全部清除
          this.toggleSelection()
          return
        }
        let exclude = []
        if (!rows) {
          exclude = this.fun(forArr, this.cardList, 'num')
        } else {
          let Earr = []
          let arrs = [rows]
          this.cardList.map(item => {
            Earr.push(item.num)
          })
          exclude = arrs.filter(item => Earr.indexOf(item.num))
        }
        if (exclude.length) {
          exclude.map(item => {
            this.toggleSelection([
              this.arr[this.getArrayIndex(this.arr, item.num)],
            ])
          })
        }
      }
    },
    cardfals() {
      // 弹窗表格取消按钮
      this.popupShow = false
      // if (this.checkif) {
      //   // 当checkbox事件被触发，并且没有确定更改，对checkbox进行状态复原
      //   this.checkUpType()
      // }
      // this.checkif = false
    },
    toggleSelection(val) {
      // 选择checkbox状态
      this.$refs.crud.toggleSelection(val)
    },
    upBeforeCheck() {
      const iData = this.instantlyData
      let forArr = this.obj[iData].dynamic.dynamic
      let compareArr = []
      let storeArr = []
      forArr.map(item => {
        compareArr.push(item.num)
      })
      if (compareArr.length < this.cardList.length) {
        storeArr = this.cardList.filter(item => !compareArr.includes(item.num))
      } else {
        // 如果数据list的length比checkbox的length小，进行一个重新赋值
        storeArr = this.cardList.filter(item => compareArr.includes(item.num))
        storeArr = storeArr.map((item, index) => {
          item.$index = index
          return item
        })
        forArr.splice(0, forArr.length)
      }
      const deepCopyStoreArr = JSON.parse(JSON.stringify(storeArr))
      for (let item of deepCopyStoreArr) {
        // 筛选出checkbox为true并且dynamic 数据List没有的进行push操作
        forArr.push(item)
      }
    },
    getArrayIndex(Garr, obj) {
      /*
       * 获取某个元素下标
       * Garr: 传入的数组
       * obj: 需要获取下标的元素
       * */
      var i = Garr.length
      while (i--) {
        if (Garr[i].num === obj) {
          return i
        }
      }
      return -1
    },
    getArrayIndex2(Garr, obj) {
      /*
       * 获取某个元素下标
       * Garr: 传入的数组
       * obj: 需要获取下标的元素
       * */
      var i = Garr.length
      while (i--) {
        if (`${Garr[i].tableDesc}_${Garr[i].fieldDesc}` === obj) {
          return i
        }
      }
      return -1
    },
    fun(fArr, cArr, field) {
      let diffRes = []
      let fDatas = []
      let cDatas = []
      for (let i in fArr) {
        let flg = false
        for (let j in cArr) {
          if (cArr[j][field] === fArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          fDatas.push(fArr[i])
        }
      }
      for (let i in cArr) {
        let flg = false
        for (let j in fArr) {
          if (fArr[j][field] === cArr[i][field]) {
            flg = true
            break
          }
        }
        if (!flg) {
          cDatas.push(cArr[i])
        }
      }
      diffRes.push(...cDatas.concat(fDatas))
      return diffRes
    },
  },
}
</script>

<style lang="scss">
.collect-newly {
  // 更改外部组件默认样式
  i.el-icon-edit {
    font-size: 150%;
    color: rgba(105, 124, 255, 100);
  }
  i.el-icon-caret-bottom {
    font-size: 150%;
    transform: rotate(-90deg);
    transition: transform 0.4s;
  }
  .i-active {
    transform: rotate(0deg) !important;
  }
  i.el-icon-pear {
    opacity: 0;
    font-size: 150%;
  }
  i.el-icon-top {
    color: rgba(68, 68, 68, 100);
  }
  i.el-icon-bottom {
    color: rgba(68, 68, 68, 100);
  }
  i.el-icon-delete-solid {
    font-size: 120%;
    color: rgba(255, 77, 54, 100);
  }
  i.el-icon-circle-plus {
    color: rgba(105, 124, 255, 100);
  }
  i.el-collapse-item__arrow {
    display: none;
  }
  div.el-collapse-item__header {
    height: 55px;
  }
  div.el-collapse-item__content {
    padding-bottom: 0 !important;
  }
  .el-input.is-disabled .el-input__inner {
    color: #000;
  }

  $bg-color: #f3f3f3;
  $bg-tcolor: #fff;
  // 非组件类样式修改
  background: $bg-color;
  padding: 20px;
  header {
    padding: 20px;
    background: $bg-tcolor;
    border-radius: 10px;
    overflow: hidden;

    .title-top {
      width: 100%;
      height: 24px;
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-bold;
      background: $bg-tcolor;

      &::before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 16px;
        line-height: 20px;
        border-radius: 15px;
        background-color: rgba(18, 119, 255, 100);
        text-align: center;
        transform: translateY(2px);
        box-sizing: border-box;
        margin-right: 4px;
      }
    }
    .guaranteeSetTop {
      border: 2px solid RGB(245, 245, 245);
      padding-top: 40px;
      box-sizing: border-box;
      margin-bottom: 20px;
      background: $bg-tcolor;
      border-radius: 10px;
      overflow: hidden;
    }
  }
  .guaranteeSetBottom {
    // border: 2px solid RGB(245, 245, 245);
    border-radius: 10px;
    padding: 35px 30px 0 30px;
    box-sizing: border-box;
    background: $bg-tcolor;
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
    min-height: 60px;
    line-height: 60px;
  }
  .drag-list {
    border-radius: 7px;
    box-sizing: border-box;
    overflow: hidden;
    .drag-move {
      transition: transform 0.3s;
    }
  }
  .fromCollapse {
    margin-top: 2%;
    box-sizing: border-box;
    overflow: hidden;
    border-radius: 10px;
  }
  .fromHeader {
    width: 100%;
    font-size: 16px;
    font-family: SourceHanSansSC-bold;
    background: $bg-tcolor;
    color: rgba(16, 16, 16, 100);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .fromLeft {
    display: flex;
    align-items: center;
    width: 60%;

    .edit-input {
      margin-right: 3%;
    }

    .inpactive {
      color: rgba(125, 125, 125, 100);
      font-size: 16px;
      font-family: SourceHanSansSC-bold;
    }
  }
  .fromHeadermenu {
    width: 5%;
    position: relative;
    right: 35px;
    top: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;

    i.el-icon-top {
      font-size: 150%;
    }
    i.el-icon-bottom {
      font-size: 150%;
    }
  }
  .subitems {
    width: 100%;
    height: 48px;
    margin-top: 1.5%;
    border-radius: 31px;
    background-color: rgba(255, 255, 255, 100);
    text-align: center;
    background: $bg-tcolor;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    & .subadd {
      width: 85px;
      height: 24px;
      color: rgba(105, 124, 255, 100);
      font-size: 16px;
      font-family: SourceHanSansSC-bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .dialo-hide {
    opacity: 0;
    z-index: -10;
  }
}
</style>
