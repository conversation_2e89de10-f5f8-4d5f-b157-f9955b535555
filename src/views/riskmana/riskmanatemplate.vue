<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{ row, index, type }" slot="menuForm">
        <el-button
          type="primary"
          size="small"
          plain
          v-if="
            type == 'edit' || type == 'add'
          "
          @click="handleFormEnabled(row)"
        >启用
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.status == 0 && permission.riskmanatemplate_view"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.status == 0 && permission.riskmanatemplate_delete"
        >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          @click="enable(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
      <template slot-scope="{ scope, row }" slot="status">
        <el-tag type="success" v-if="row.status == 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>
      <template slot="menuLeft">

      </template>
      <template slot-scope="{ scope, row }" slot='normTemplateListForm'>
        <div class="transfer-box">
          <el-transfer
            filterable
            :titles="['未选指标', '已选指标']"
            filter-placeholder="请输入指标名称"
            v-model="listTable"
            :data="formData">
          </el-transfer>
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  add,
  batchDisabled,
  batchEnabled,
  getDetail,
  getList,
  listDetails,
  remove,
  update
} from "@/api/riskmana/riskmanatemplate";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      listTable: [],
      formData: [],
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        delBtn: false,
        editBtn: false,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: "模板编号",
            display: false,
            prop: "templateNo",
          },
          {
            label: "模板名称",
            search: true,
            width: '250',
            prop: "name",
            span: 24,
            rules: [{
              required: true,
              message: "请输入模板名称",
              trigger: "blur"
            }]
          },
          {
            label: "业务类型",
            search:true,
            prop: "goodsTypeNo",
            type: "select",
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请输入业务类型",
              trigger: "blur"
            }]
          },
          {
            label: "最高总分",
            display: false,
            prop: "maxScore",
          },
          {
            width: '150',
            label: "上次修改时间",
            display: false,
            prop: "updateTime",
          },
          {
            label: "操作人",
            display: false,
            prop: "userName"
          },
          {
            label: '选择指标',
            hide: true,
            span: 24,
            prop: 'normTemplateList',
            slot: true,
          },
          {
            label: '状态',
            span: 24,
            display: false,
            prop: 'status',
            formslot: true,
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.riskmanatemplate_add, false),
        viewBtn: this.vaildData(this.permission.riskmanatemplate_view, false),
        delBtn: this.vaildData(this.permission.riskmanatemplate_delete, false),
        editBtn: this.vaildData(this.permission.riskmanatemplate_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    enable(row) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
        this.enabledAndDisabled(msg, row)
      } else {
        this.$axios.get('/api/blade-riskmana/web-back/riskmana/riskmanatemplate/cascadeInfo?id=' + row.id).then(res => {
            msg = '确定禁用操作 ' + (res.data.data ? res.data.data : '')
          }
        ).then(() => {
          this.enabledAndDisabled(msg, row);
        })
      }
    },
    enabledAndDisabled(msg, row, done) {
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          batchDisabled(row.id).then(() => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          })
        } else {
          batchEnabled(row.id).then(() => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            done()
          })
        }
      })
    },
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    handleFormEnabled() {
      this.form.status = 1
      this.$refs.crud.rowUpdate()
    },
    rowSave(row, done, loading) {
      row.normIds = this.listTable;
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      row.normIds = this.listTable;
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
          this.listTable = res.data.data.normIds;
        })
      } else {
        this.listTable = [];
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
      listDetails().then(res => {
        let list = res.data.data;
        //先进行清除
        this.formData = []
        list.forEach((e) => {
          this.formData.push({
            key: e.id,
            label: e.name
          })
        })
      });
    }
  }
};
</script>

<style>

</style>
