<template>
  <div class="new-box">
    <avue-form ref="form" v-model="form" :option="option1"></avue-form>
    <div class="table">
      <span>表单管理:</span>
      <div class="table-btn" @click="uptab">
        <i class="el-icon-plus"></i>
      </div>
    </div>
    <div class="table-box" v-for="(item, index) in tablist" :key="index">
      <div class="tab-name">
        <span>表名{{ index + 1 }}:</span>
        <span>{{ index + 1 }}</span>
      </div>
      <div class="name-se">
        <avue-form
          :option="optionlist1[index].option"
          v-model="E[index].a"
          @click="biaod = item - 1"
        ></avue-form>
      </div>
      <div class="ejtab">
        <div class="ai-tab" @click="tmnp = index">
          <avue-form
            :option="optionlist2[index].option"
            v-model="objlist[index].obj"
            @rowAdd="rowAdd(this)"
          >
          </avue-form>
        </div>
      </div>
    </div>
    <div class="tab-btn">
      <span class="add" @click="baoc">保存</span>
      <span @click="yinc">取消</span>
    </div>
  </div>
</template>
<script>
import {
  getzhiduanlist,
  getfumianyishu,
  setshuju,
} from '../../../api/risk/risk-tab'
import _ from 'lodash'
export default {
  props: ['xian'],
  data() {
    return {
      tmnp: 0,
      biaod: 0,
      selist1: [
        {
          label: '代采融资',
          value: 0,
        },
        {
          label: '应收账款',
          value: 1,
        },
      ],
      selist2: [],
      selist3: [],
      tablist: 0,
      form: {},
      objlist: [],
      F: { a: {} },
      E: [],
      D: { obj: {} },
      B: {
        option: {
          emptyBtn: false,
          submitBtn: false,
          column: [
            {
              label: '表名',
              span: 12,
              prop: 'tbName',
              type: 'select',
              index: 0,
              rules: [{ required: true, message: '请输入表单' }],
              dicData: [],
              cascaderItem: ['dynamic'],
              change: e => {
                if (e.value != '') {
                  setTimeout(() => {
                    console.log(e.column)
                    console.log(this.optionlist1, 1111111111)
                    this.objlist[e.column.index].obj.dynamic = []
                    this.optionlist2[
                      e.column.index
                    ].option.column[0].children.column[0].dicData = []
                    this.optionlist2[
                      e.column.index
                    ].option.column[0].children.column[1].dicData = []
                  })
                }
              },
            },
          ],
        },
      },
      C: {
        option: {
          emptyBtn: false,
          submitBtn: false,
          column: [
            {
              label: '二级表单',
              prop: 'dynamic',
              type: 'dynamic',
              span: 24,
              children: {
                align: 'center',
                headerAlign: 'center',
                rowAdd: done => {
                  setTimeout(() => {
                    getzhiduanlist(this.E[this.tmnp].a.tbName).then(res => {
                      console.log(res.data.data)
                      for (let i = 0; i < res.data.data.length; i++) {
                        this.optionlist2[
                          this.tmnp
                        ].option.column[0].children.column[0].dicData.push({
                          label: res.data.data[i].fieldName,
                          value: res.data.data[i].fieldName,
                        })
                        this.optionlist2[
                          this.tmnp
                        ].option.column[0].children.column[1].dicData.push({
                          label: res.data.data[i].fieldType,
                          value: res.data.data[i].fieldType,
                        })
                      }
                    })
                  })
                  done({})
                },
                column: [
                  {
                    label: '字段名',
                    prop: 'name',
                    type: 'select',
                    formslot: true,
                    dicData: [],
                  },
                  {
                    label: '字段类型',
                    prop: 'type',
                    type: 'select',
                    dicData: [],
                    rules: [
                      {
                        required: true,
                        message: '请选择选择框',
                      },
                    ],
                  },
                  {
                    label: '排序',
                    prop: 'checkbox',
                    type: 'input',
                  },
                ],
              },
            },
          ],
        },
      },
      option1: {
        labelWidth: 120,
        emptyBtn: false,
        submitBtn: false,
        column: [
          {
            label: '表单名称',
            prop: 'name',
            span: 17,
            rules: [
              {
                required: true,
                message: '请输入表单名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '类型',
            prop: 'type',
            span: 12,
            type: 'select',
            rules: [
              {
                required: true,
                message: '请输入表单类型',
              },
            ],
            dicData: [],
          },
        ],
      },
      optionlist1: [],
      optionlist2: [],
    }
  },

  methods: {
    addAll() {},
    yinc() {
      this.$parent.xian = 0
    },
    uptab() {
      this.tablist += 1
      if (this.tablist == 1) {
        this.optionlist1.push(_.cloneDeep(this.B))
        this.optionlist2.push(_.cloneDeep(this.C))
        this.E.push(_.cloneDeep(this.F))
        this.objlist.push(_.cloneDeep(this.D))
      } else {
        this.B.option.column[0].index += 1
        this.optionlist1.push(_.cloneDeep(this.B))
        this.optionlist2.push(_.cloneDeep(this.C))
        this.E.push(_.cloneDeep(this.F))
        this.objlist.push(_.cloneDeep(this.D))
      }
      this.option1.column[1].dicData = this.selist1
      this.optionlist1[this.tablist - 1].option.column[0].dicData = this.selist2
      this.optionlist2[
        this.tablist - 1
      ].option.column[0].children.column[1].dicData = this.selist3
      this.optionlist2[
        this.tablist - 1
      ].option.column[0].children.column[0].dicData = this.selist3
    },
    baoc() {
      let list = [{}]
      let creditNote = {}
      creditNote.creditNoteName = this.form.name
      creditNote.type = this.form.type
      creditNote.creditNodeRequiredDTOS = []
      let a = {}
      let b = {}
      for (let i = 0; i < this.E.length; i++) {
        b.requiredTableName = this.E[i].a.tbName
        b.creditNodeFieldDTOS = []
        for (let j = 0; j < this.objlist[i].obj.dynamic.length; j++) {
          console.log(this.objlist[j].obj.dynamic)
          for (let k = 0; k < this.objlist[j].obj.dynamic.length; k++) {
            a.sort = this.objlist[j].obj.dynamic[k].checkbox
            a.fieldName = this.objlist[j].obj.dynamic[k].name
            a.fielsType = this.objlist[j].obj.dynamic[k].type
          }
          b.creditNodeFieldDTOS.push(a)
          a = {}
        }
        creditNote.creditNodeRequiredDTOS.push(b)
        b = {}
      }
      list[0] = creditNote
      // list[0] = JSON.stringify(list[0])
      console.log(list[0])
      setshuju(list[0]).then(res => {
        console.log(res.data)
        if (res.data.msg == '操作成功') {
          this.$parent.xian = 0
          this.$parent.huoq()
        }
      })
    },
  },

  mounted() {
    // console.log(this.$parent.xian);
    getfumianyishu().then(res => {
      console.log(res.data)
      for (let i = 0; i < res.data.data.length; i++) {
        this.selist2.push({
          label: res.data.data[i].tableName,
          value: res.data.data[i].tableName,
        })
      }
    })
  },
}
</script>
<style scoped>
.new-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 800px;
  overflow-x: hidden;
  z-index: 10;
  border-radius: 16px;
  background-color: #ffffff;
  padding: 20px;
  box-sizing: border-box;
}
.table {
  width: 100%;
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: -20px 0 0 40px;
  font-size: 20px;
  z-index: 9;
}
.table-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  background-color: #0184ff;
  font-size: 20px;
  border-radius: 50%;
  color: #ffffff;
  font-weight: 700;
}
.table-box {
  margin: 20px 0 0 80px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.tab-name span:nth-child(1) {
  color: #cecece;
}
.name-se {
  margin-left: -40px;
  width: 100%;
}
.ejtab {
  width: 100%;
  display: flex;
  align-items: flex-start;
}

/deep/ .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-top: -40px;
  position: relative;
  z-index: 19;
}
.tab-btn {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.tab-btn span {
  width: 60px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 4px;
  background-color: rgba(255, 110, 110, 0.3);
  color: #ff5757;
}
.tab-btn .add {
  margin-right: 20px;
  background-color: rgba(96, 250, 91, 0.3);
  color: #3ff785;
}
</style>