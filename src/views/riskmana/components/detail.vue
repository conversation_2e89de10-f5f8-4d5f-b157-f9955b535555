<template>
  <div class="gride-detail">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">评分结果</div>
      </div>
      <div class="result-box">
        <div class="result-list">
          <div class="zhyg">
            <div class="result-top">
              <span class="result-company">{{ resultlist.ratingScore }}</span>
              <span>分</span>
            </div>
            <div>评级得分</div>
          </div>
          <div class="zhyg">
            <div class="result-top">
              <span class="result-company">{{ resultlist.creditRating }}</span>
              <span>{{ resultlist.company }}</span>
            </div>
            <div>信用级别</div>
          </div>
          <div class="zhyg">
            <div class="result-top">
              <span class="result-company">{{ resultlist.totalScore }}</span>
              <span>分</span>
            </div>
            <div>指标项总分</div>
          </div>
          <div class="zhyg">
            <div class="result-top">
              <span class="result-company">{{ resultlist.riskLimits }}</span>
              <span>万元</span>
            </div>
            <div>风险额度</div>
          </div>
          <div class="zhyg">
            <div class="result-top">
              <span class="result-company">{{ resultlist.validTime }}</span>
              <span>天</span>
            </div>
            <div>有效期</div>
          </div>
        </div>
        <div class="xian"></div>
        <div class="result-state">
          <div class="result-state-left">
            <div class="num">
              <span class="tit">评分编码:</span>
              <span class="record-number">{{ statelist.recordNo }}</span>
            </div>
            <div class="num">
              <span class="tit">评分模板:</span>
              <span>{{ statelist.ratingName }}</span>
            </div>
            <div class="num-state">
              <span class="tit">评级状态:</span>
              <span class="effective" v-if="statelist.status">有效</span>
              <span class="invalid" v-if="!statelist.status">失效</span>
            </div>
            <div class="num">
              <span class="tit">评级时间:</span>
              <span>{{ statelist.createTime }}</span>
            </div>
            <div class="num">
              <span class="tit">到期时间:</span>
              <span>{{ statelist.expireTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="details-content2">
      <div class="details-content2-tit">
        <div class="title-dot"></div>
        <div class="title">指标项分数明细</div>
      </div>
      <avue-crud
        :data="data"
        :option="option"
        :page.sync="page"
        @current-change="currentChange"
      ></avue-crud>
    </div>
  </div>
</template>
<script>
import { getdata } from '../../../api/riskmana/ratingrecord.js'
export default {
  data() {
    return {
      id: '',
      resultlist: {
        ratingScore: '',
        creditRating: '',
        totalScore: '',
        riskLimits: '',
        validTime: '',
        company: '',
      },

      statelist: {},

      data: [],
      page: {
        currentPage: 1,
        total: 5,
        layout: ' prev,pager, next',
        pageSize: 4,
      },
      option: {
        align: 'center',
        editBtn: false,
        addBtn: false,
        columnBtn: false,
        refreshBtn: false,
        delBtn: false,
        index: true,
        indexLabel: '序号',
        background: '#dddddd',
        border: true,
        menu: false,
        menuAlign: 'center',
        column: [
          {
            label: '指标编码',
            prop: 'normNo',
          },
          {
            label: '指标名称',
            prop: 'name',
          },
          {
            label: '最大分数',
            prop: 'maxScore',
          },
          {
            label: '企业传入数据',
            prop: 'enterpriseData',
          },
          {
            label: '最终分数',
            prop: 'finalScore',
          },
        ],
      },
    }
  },
  created() {
    this.id = JSON.parse(
      Buffer.from(this.$route.params.params, 'base64')
    ).toString('base64')
    getdata(this.id, this.page.pageSize, this.page.currentPage).then(res => {
      this.page.total = res.data.data.ratingNormRecordList.total
      this.resultlist.ratingScore = res.data.data.finalScore
      this.resultlist.totalScore = res.data.data.totalScore
      this.data = res.data.data.ratingNormRecordList.records
      this.statelist.recordNo = res.data.data.recordNo
      this.statelist.ratingName = res.data.data.ratingName
      this.statelist.status = res.data.data.status
      let timeList = res.data.data.createTime.split(' ')
      this.statelist.createTime = timeList[0]
      let dateList = res.data.data.expireTime.split(' ')
      this.statelist.expireTime = dateList[0]
      this.resultlist.riskLimits = res.data.data.limitAmount
      this.resultlist.validTime = parseInt(res.data.data.validTime)
      let letter = res.data.data.grade.split('')
      this.resultlist.creditRating = letter[0]
      this.resultlist.company = letter[1]
    })
  },
  methods: {
    currentChange(val) {
      this.page.currentPage = val
      this.datalist()
    },
    datalist() {
      this.data = []
      getdata(this.id, this.page.pageSize, this.page.currentPage).then(res => {
        for (let item of res.data.data.ratingNormRecordList.records) {
          this.data.push(item)
        }
      })
    },
  },
}
</script>
<style scoped>
.details-content1,
.details-content2 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content2 {
  margin-bottom: 50px;
}
.details-content1-tit,
.details-content2-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title,
.details-content2-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.result-box {
  margin-top: 12px;
}
.result-list {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.result {
  width: calc((100% - 80px) / 5);
  height: 140px;
  background-color: #f7f7f7;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40px;
  justify-content: center;
}
.zhyg {
  width: calc((100% - 80px) / 5);
  height: 140px;
  background-color: #f7f7f7;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  justify-content: center;
}
.zhyg:nth-child(5n) {
  margin: 0;
}
.result-top {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  font-size: 16px;
  color: #1277ff;
  font-weight: 600;
}
.result-company {
  font-size: 26px;
  line-height: 22px;
  margin-right: 4px;
}
.xian {
  width: 100%;
  height: 2px;
  background-color: #f3f3f3;
  margin-top: 20px;
}
.result-state {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.result-state-left {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  font-size: 16px;
}
.result-state-left div {
  width: calc((100% - 40px) / 3);
  height: 24px;
  margin: 0 20px 10px 0;
}
.result-state-left div:nth-child(3n) {
  margin-right: 0;
}
.result-state-left span {
  color: #101010;
}
.result-state-left .tit,
.result-state-left .num .tit {
  color: #84868d;
  margin-right: 4px;
}
.result-state-left .num span {
  color: #101010;
}
.result-state-left .num .record-number {
  color: #1277ff;
}
.result-state-left .num-state {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.result-state-left .num-state .effective {
  width: 42px;
  height: 22px;
  line-height: 22px;
  border-radius: 2px;
  font-size: 12px;
  border: 1px solid #1ac475;
  color: #1ac475;
  background-color: rgba(133, 255, 139, 0.2);
  text-align: center;
  display: block;
}
.result-state-left .num-state .invalid {
  width: 46px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  border-radius: 2px;
  border: 1px solid #7c7c7c;
  color: #555555;
  background-color: rgba(122, 122, 122, 0.2);
  text-align: center;
  display: block;
}
.result-state-right {
  padding: 4px 14px;
  border: 1px solid #1277ff;
  color: #1277ff;
  border-radius: 4px;
}
::v-deep .avue-crud__menu {
  min-height: 12px;
  height: 12px;
}
</style>
