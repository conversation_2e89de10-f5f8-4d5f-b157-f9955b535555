<template>
  <div class="frozenAmountDialog-box">
    <el-dialog
      title="冻结记录"
      :visible.sync="dialogT"
      :modal-append-to-body="false"
      class="avue-dialog avue-dialog--top"
      width="30%"
    >
      <div class="all-box" v-if="!empty">
        <div v-if="dataArr.length" class="all-box_for">
          <div
            class="all-box_for_children"
            v-for="item in dataArr"
            :key="item.financeNo"
          >
            <el-card shadow="always">
              <div class="show-box">
                <div class="show-box_left">
                  <span>
                    融资编号：
                    {{ item.financeNo }}
                  </span>
                  <span>
                    创建时间：
                    {{ item.createTime }}
                  </span>
                </div>
                <div class="show-box_right">
                  冻结金额：
                  {{ item.amount }}元
                </div>
              </div>
            </el-card>
          </div>
        </div>
        <div v-else class="all-box_for">
          <div class="all-box_for_children" v-for="item in 2" :key="item">
            <el-card shadow="always">
              <el-skeleton animated>
                <template slot="template">
                  <div style="padding: 6px 14px">
                    <el-skeleton-item variant="p" style="width: 100%" />
                  </div>
                </template>
              </el-skeleton>
            </el-card>
          </div>
        </div>
        <!-- 切换页码组件 -->
        <div class="my-pagination">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            layout="total, prev, pager, next"
            :current-page.sync="paginationObj.current"
            :page-size="paginationObj.pageSize"
            :total="paginationObj.total"
          >
          </el-pagination>
        </div>
      </div>
      <el-empty v-else description="暂无数据" />
      <div class="avue-dialog__footer">
        <el-button @click="cardfals">关 闭</el-button>
        <!-- <el-button @click="cardEngth" type="primary">确 定</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { frozenQuotaList } from '@/api/riskmana/enterprisequota'
export default {
  name: 'frozenAmountDialog',
  data() {
    return {
      dialogT: false,
      Did: '',
      dataArr: [],
      paginationObj: {
        total: 0,
        pageSize: 2,
        current: 1,
      },
      empty: false,
    }
  },
  methods: {
    // 取消
    cardfals() {
      this.dialogT = false
    },
    // 确认
    cardEngth() {
      this.dialogT = false
    },
    // 打开弹窗
    handleOpen(val) {
      this.Did = val
      this.empty = false
      this.paginationObj.current = 1
      this.frozenQuotaListFun()
      this.dialogT = true
    },
    // 列表请求
    frozenQuotaListFun() {
      const paramsD = {
        current: this.paginationObj.current,
        size: this.paginationObj.pageSize,
        enterpriseQuotaId: this.Did,
      }
      this.dataArr = []
      frozenQuotaList(paramsD)
        .then(({ data }) => {
          if (data.success) {
            const { data: resData } = data
            if (resData.records.length) {
              const arr = []
              for (const item of resData.records) {
                arr.push({
                  financeNo: item.financeNo,
                  createTime: item.createTime,
                  amount: item.amount,
                })
              }
              setTimeout(() => {
                this.paginationObj.total = resData.total
                this.dataArr = arr
              }, 220)
              return
            }
            this.empty = true
          }
        })
        .catch(() => {
          this.empty = true
        })
    },
    // 页码切换事件
    handleCurrentChange(val) {
      this.paginationObj.current = val
      this.frozenQuotaListFun()
    },
  },
}
</script>
<style lang="scss" scoped>
.frozenAmountDialog-box {
  .all-box {
    &_for {
      &_children {
        margin-bottom: 19px;

        .show-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 16px;

          &_left {
            display: flex;
            flex-direction: column;

            & > span:first-child {
              margin-bottom: 3px;
            }
          }
        }
      }
    }
  }
}
</style>
