<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.material_delete"
          @click="handleDelete"
        >删 除
        </el-button>
        <el-button
          type="success"
          size="small"
          plain
          v-if="permission.material_enabled"
          @click="handleEnable"
        >启 用
        </el-button>
        <el-button
          type="danger"
          size="small"
          plain
          v-if="permission.material_disabled"
          @click="handleDisabled"
        >禁 用
        </el-button>
      </template>
      <template slot-scope="{  type }" slot="menuForm">
        <el-button
          type="primary"
          size="small"
          plain
          v-if="
            (type == 'edit' || type == 'add') && permission.material_enabled
          "
          @click="handleFormEnabled()"
        >启用
        </el-button>
      </template>


      <template slot-scope="{ row }" slot="status">
        <el-tag type="success" v-if="row.status == 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>


      <template slot-scope="{ row }" slot="supportedFormat">
        <el-tag
          type="success"
          style="margin-right: 10px;"
          v-for="item in getFormatItem(row.supportedFormat)"
        >{{ item }}
        </el-tag
        >
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.status == 0 && permission.material_view"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.status == 0 && permission.material_delete"
        >删除
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          v-if="permission.material_enabled"
          @click="enable(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, disabled, enabled, getDetail, getList, remove, update,} from '@/api/riskmana/material'
import {mapGetters} from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        editBtn: false,
        delBtn: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '资料名称',
            prop: 'name',
            rules: [
              {
                required: true,
                message: '请输入资料名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '示例',
            prop: 'example',
            type: 'upload',
            dataType: 'string',
            accept: '.jpg,.png,.pdf,.jpeg',
            fileSize: 20000,
            listType: 'picture-img',
            tip: '只能上传jpg,png,pdf,jpeg格式，且不超过20m',
            action: '/api/blade-resource/oss/endpoint/put-file',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            hide: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请上传示例',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '多文档',
            type: 'radio',
            prop: 'multipleType',
            dicData: [
              {
                label: '不支持',
                value: 0,
              },
              {
                label: '支持',
                value: 1,
              },
            ],
            rules: [
              {
                required: true,
                message: '请选择是否多文档',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '收集节点',
            prop: 'collectionNode',
            type: 'tree',
            multiple: true,
            placeholder: '收集节点',
            clearable: false,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_upload_node',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'string',
            rules: [
              {
                required: true,
                message: '请选择收集节点',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '上传数量',
            prop: 'num',
            display: false,
            hide: true,
            minRows: 2,
            maxRows: 9,
            type: 'number',
            rules: [
              {
                required: true,
                message: '请输入上传数量;最多9张',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '排序',
            prop: 'sort',
            minRows: 0,
            type: 'number',
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '操作人',
            prop: 'updateUserName',
            display: false,
          },
          {
            label: '操作时间',
            prop: 'updateTime',
            display: false,
          },
          {
            label: '状态',
            prop: 'status',
            display: false,
            formslot: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.material_add, false),
        viewBtn: this.vaildData(this.permission.material_view, false),
        delBtn: this.vaildData(this.permission.material_delete, false),
        editBtn: this.vaildData(this.permission.material_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {
    'form.multipleType': {
      handler: function (newVal) {
        const cloumn = this.findObject(this.option.column, 'num')
        if (newVal == 1) {
          cloumn.display = true
        } else {
          cloumn.display = false
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleFormEnabled() {
      this.form.status = 1
      this.$refs.crud.rowSave()
    },
    enable(row) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
      } else {
        msg = '确定禁用操作'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          disabled(row.id).then(
            () => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
              done()
            },
            error => {
              loading()
              window.console.log(error)
            }
          )
        } else {
          enabled(row.id).then(
            () => {
              this.onLoad(this.page)
              this.$message({
                type: 'success',
                message: '操作成功!',
              })
              done()
            },
            error => {
              loading()
              window.console.log(error)
            }
          )
        }
      })
    },
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    handleEnable() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定启用操作?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return enabled(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleDisabled() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定禁用操作?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return disabled(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    getFormatItem(supportedFormat) {
      return supportedFormat.split(',')
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
