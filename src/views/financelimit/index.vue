<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row, type, size }" slot="menu">
        <el-button :size="size" :type="type" @click="calculateDetail(row)"
          >查看
        </el-button>
      </template>
    </avue-crud>

    <!-- 查看 -->
    <el-dialog
      title="查看"
      :visible.sync="dialogDetail"
      width="50%"
      append-to-body
    >
      <el-descriptions :column="3" border>
        <el-descriptions-item label="水位唯一编码">
          {{ dialogDetailData.limitNo }}
        </el-descriptions-item>
        <el-descriptions-item label="原单据金额">
          {{ dialogDetailData.orderAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="水位金额"
          >{{ dialogDetailData.limitAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="计算公式">{{
          dialogDetailData.calculateFormula
        }}</el-descriptions-item>
        <el-descriptions-item label="计算参数">
          {{ dialogDetailData.calculateParam }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </basic-container>
</template>

<script>
import { limitList, calculateDetail } from '@/api/financelimit/financelimit'
import { Loading } from 'element-ui'

export default {
  data() {
    return {
      dialogDetail: false,
      dialogDetailData: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        headerAlign: 'center',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        dialogClickModal: false,
        menu: true,
        menuWidth: 80,
        columnBtn: false,
        column: [
          {
            label: '订单编号',
            prop: 'orderNo',
            search: true,
          },
          {
            label: '水位编号',
            prop: 'limitNo',
            search: true,
          },
          {
            label: '资方编号',
            prop: 'capitalNo',
          },
          {
            label: '核心企业',
            prop: 'buyerCreditCode',
            search: true,
          },
          {
            label: '供应商名称',
            prop: 'sellerCreditCode',
            search: true,
            searchLabelWidth: 100,
            labelWidth: 100,
          },
          {
            label: '水位金额',
            prop: 'limitAmount',
          },
          {
            label: '在贷金额',
            prop: 'loanBalance',
          },
          {
            label: '订单金额',
            prop: 'orderAmount',
          },
          {
            label: '在贷余额',
            prop: 'loanBalance',
          },
          {
            label: '业务类型',
            prop: 'businessType',
            type: 'select',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            dataType: 'number',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            search: true,
          },
          {
            label: '水位创建时间',
            prop: 'limitDate',
          },
        ],
      },
      data: [],
      hasJsonFlag: true,
    }
  },
  methods: {
    //
    // 水位计算公式弹窗
    calculateDetail(row) {
      const loading = Loading.service({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      calculateDetail({ limitId: row.id })
        .then(({ data }) => {
          this.dialogDetailData = data.data

          setTimeout(() => {
            loading.close()
            this.dialogDetail = true
          }, 500)
        })
        .catch(() => {
          loading.close()
        })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      limitList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>
