<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template
        slot-scope="{ row }"
        slot="status"
      >
        <el-tag :style="{
              color: row.status == 1 ? '#67c23a' : '#A6AEBC',
              border:
                row.status == 1 ? '1px solid #67c23a' : '1px solid #C9CED6',
              background: row.status == 1 ? '#EAFCF7' : '#fff',
            }">{{
            row.status == 1 ? '已启用' : '已禁用'
          }}
        </el-tag>
      </template>
      <template
        slot-scope="scope"
        slot="menu"
      >
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row, index)"
          v-if="scope.row.status != 1"
          size="small"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row, index)"
          v-if="scope.row.status != 1"
          size="small"
        >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-success"
          size="small"
          v-if="scope.row.status==0"
          @click.stop="handleDisable(scope.row,scope.index)"
        >
          启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-warning"
          size="small"
          v-if="scope.row.status==1"
          @click.stop="handleDisable(scope.row,scope.index)"
        >
          禁用
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, changeStatus, getDetail, getList, remove, update} from "@/api/othersapi/othersapi";
import {mapGetters} from "vuex";
import Test from "../test";

export default {
  components: {Test},
  data() {
    return {
      infoOption: {
        column: [{
          label: '年龄',
          prop: 'sex',
        }]
      },
      form: {},
      query: {},
      bizValue: {},
      bizName: "",
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        indexLabel: '序号',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        dialogClickModal: false,
        column: [
          {
            label: "api名称",
            prop: "apiName",
            search: true,
            editDisabled: true,
            rules: [{
              required: true,
              message: "请输入api名称",
              trigger: "blur"
            }]
          },
          {
            label: "api编号",
            prop: "apiNo",
            dicUrl: '/api/blade-system/dict/dictionary?code=otherApi',
            type: 'select',
            search: true,
            editDisabled: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [{
              required: true,
              message: "请选择api编号",
              trigger: "blur"
            }]
          },
          {
            label: "api类型",
            prop: "apiType",
            search: true,
            span: 25,
            type: 'tree',
            dicUrl:
              '/api/othersapitype/tree',
            props: {
              label: 'title',
              value: 'id',
            },
            rules: [{
              required: true,
              message: "请输入api类型",
              trigger: "blur"
            }]
          },
          {
            label: "api图片",
            prop: "apiImg",
            type: "upload",
            display: false,
            span: 15,
          },
          {
            label: "状态",
            prop: "status",
            search: true,
            hide: false,
            type: 'select',
            dataType: 'number',
            span: 25,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=api_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '租户号',
            prop: "tenantId",
            search: true,
            display: false,
          },
        ],
        group: [{
          label: 'api请求参数',
          column: [
            {
              prop: 'othersApiParamsList',
              type: 'dynamic',
              span: 24,
              rules: [
                {
                  type: 'array',
                  required: false,
                  message: '请添加api参数',
                  trigger: 'blur',
                },
              ],
              children: {
                align: 'center',
                headerAlign: 'center',
                rowAdd: (done) => {
                  done({
                    apiParamsType: '',
                  })
                },
                rowDel: (row, done) => {
                  done()
                },
                column: [
                  {
                    label: 'api请求key',
                    prop: 'apiKeyName',
                    formslot: true,
                    rules: [
                      {
                        type: 'string',
                        required: true,
                        message: '请输入api参数名称',
                        trigger: 'blur',
                      },
                    ],
                  },
                  {
                    label: 'api请求value',
                    prop: 'apiKeyValue',
                    formslot: true,
                    rules: [
                      {
                        type: 'string',
                        required: true,
                        message: '请输入api参数名称',
                        trigger: 'blur',
                      },
                    ],
                  },
                  {
                    label: '备注',
                    prop: 'remark',
                  },
                ],
              },
            },
          ]
        },
          {
            label: 'api接口方法信息',
            column: [
              {
                prop: 'othersApiOpenList',
                type: 'dynamic',
                span: 24,
                rules: [
                  {
                    type: 'array',
                    required: false,
                    message: '请添加api接口方法信息',
                    trigger: 'blur',
                  },
                ],
                children: {
                  align: 'center',
                  headerAlign: 'center',
                  rowAdd: (done) => {
                    done({
                      apiParamsType: '',
                    })
                  },
                  rowDel: (row, done) => {
                    done()
                  },
                  column: [
                    {
                      label: '接口名称',
                      prop: 'openApiName',
                      rules: [
                        {
                          type: 'string',
                          required: true,
                          message: '请输入api参数名称',
                          trigger: 'blur',
                        },
                      ],
                    },
                    {
                      label: '接口请求地址',
                      prop: 'openApiUrl',
                      rules: [
                        {
                          type: 'string',
                          required: true,
                          message: '请输入接口请求地址',
                          trigger: 'blur',
                        },
                      ],
                    },
                    {
                      label: '有效次数',
                      prop: 'validCount',
                      type:'number',
                      dataType:'number',
                      rules: [
                        {
                          required: true,
                          message: '请输入有效次数,-1则无限使用',
                          trigger: 'blur',
                        },
                      ],
                    },
                    {
                      label: '状态',
                      prop: 'status',
                      dataType:'number',
                      type:'select',
                      dicData:[
                        {
                          label:'关闭',
                          value:2
                        },
                        {
                          label:'开启',
                          value:1
                        }
                      ]
                    },
                  ],
                },
              },
            ]
          },
          {
            label: 'api-logo',
            column: [
              {
                label: "apiLogo",
                prop: "apiImg",
                type: "upload",
                labelWidth: 110,
                tip: "只能上传jpg/png格式，且不超过500kb",
                span: 24,
                listType: 'picture-img',
                accept: ".jpg,.png",
                fileSize: 500,
                propsHttp: {
                  res: 'data'
                },
                action: "/api/blade-resource/oss/endpoint/put-file-kv",
                limit: 1,
                rules: [{
                  required: false,
                  message: "请上传api图标",
                  trigger: "blur"
                }],
                uploadAfter: (res, done) => {
                  this.form.attachLogoId = res.attachId;
                  done();
                },
                uploadBefore(file, done, loading) {
                  var first = file.name.lastIndexOf('.')
                  const type = file.name.substring(first + 1, file.length)

                  if (['jpg', 'png'].includes(type)) {
                    const isLt20M = file.size / 1024 > 500
                    if (isLt20M) {
                      loading()
                      this.$message.error('文件大小不能超过500KB')
                      return
                    }
                    done()
                  } else {
                    loading()
                    this.$message.error('文件格式错误')
                    return
                  }

                }
              },
            ]
          }
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.othersapi_add, false),
        viewBtn: this.vaildData(this.permission.othersapi_view, false),
        delBtn: this.vaildData(this.permission.othersapi_delete, false),
        editBtn: this.vaildData(this.permission.othersapi_edit, false)
      };
    },
    infoData() {

    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    rowSave(row, done, loading) {
      if (row.apiImg instanceof Array) {
        let apiLogo = row.apiImg[0]
        row.apiImg = apiLogo
      }
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleEnableAndDisAbleList(status) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let msg = status === 1 ? "确定将选择数据禁用" : "确定将选择数据启用"
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return changeStatus(this.ids, status);
      })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });

    },

    handleDisable(row) {

      if (row.status === 1) {
        var msg = "确定将选择数据禁用?"
        var realStatus = 0
      } else {
        msg = "确定将选择数据启用?"
        realStatus = 1
      }

      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return changeStatus(row.id, realStatus);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    beforeOpen(done, type) {

      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    getBizeValue(bizName) {

      getDictionary(bizName).then(res => {
        this.bizValue = res.data.data;
      });
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>

.is-shang-jia > button:nth-child(2) {
  display: none;
}

.is-shang-jia > button:nth-child(3) {
  display: none;
}
</style>
