<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.resourcemail_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template slot="mailTypeForm">
        <el-select v-model="form.mailType">
          <el-option
            v-for="item in mailTypeDic"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </template>
      <template slot-scope="{ scope, row }" slot="mailType">
        <el-tag>{{ matchMail(row.mailType) }}</el-tag>
      </template>
      <template slot-scope="{ scope, row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="permission.resourcemail_view"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="permission.resourcemail_delete"
          >删除
        </el-button>

        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          @click="enabled(row)"
        >
          <!--          {{ getEnableName(row.status) }}-->
          启用
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          @click="handleDebug(row)"
        >
          <!--          {{ getEnableName(row.status) }}-->
          调试
        </el-button>
      </template>
      <template slot-scope="{ scope, row }" slot="status">
        <el-tag type="success" v-if="row.status == 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>
    </avue-crud>
    <el-dialog
      title="邮箱发送调试"
      append-to-body
      :visible.sync="box"
      width="550px"
    >
      <avue-form
        ref="form"
        :option="debugOption"
        v-model="debugForm"
        @submit="handleSubmit"
      />
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  enable,
} from '@/api/resource/resourcemail'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      box: false,
      debugForm: {
        to: '',
        subject: '测试',
        content: '测试成功',
      },
      debugOption: {
        submitText: '提交',
        column: [
          {
            label: '邮箱格式',
            prop: 'mailType',
            type: 'select',
            dicData: [
              {
                label: '腾讯邮箱',
                value: 'qqMail',
              },
              {
                label: '腾讯企业邮箱',
                value: 'qqExMail',
              },
            ],
            span: 24,
          },
          {
            label: '接收人邮箱',
            prop: 'to',
            span: 24,
          },
        ],
      },
      mailTypeDic: [
        {
          label: '腾讯邮箱',
          value: 'qqMail',
        },
        {
          label: '腾讯企业邮箱',
          value: 'qqExMail',
        },
      ],
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '邮箱类型',
            formslot: true,
            prop: 'mailType',
            span: 24,
            rules: [
              {
                required: true,
                message: '请选择邮箱类型',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'host',
            prop: 'host',
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入服务器主机',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'port',
            span: 24,
            prop: 'port',
            rules: [
              {
                required: true,
                message: '请输入服务器端口',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'protocol',
            prop: 'protocol',
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入服务器使用的协议',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'username',
            span: 24,
            prop: 'username',
            rules: [
              {
                required: true,
                message: '请输入账号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'password',
            span: 24,
            prop: 'password',
            rules: [
              {
                required: true,
                message: '请输入密钥',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'defaultEncoding',
            span: 24,
            prop: 'defaultEncoding',
            rules: [
              {
                required: true,
                message: '请输入默认编码',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'status',
            display: false,
            slot: true,
            dicData: [
              {
                label: '禁用',
                value: 0,
              },
              {
                label: '启用',
                value: 1,
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  watch: {},
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.resourcemail_add, false),
        viewBtn: this.vaildData(this.permission.resourcemail_view, false),
        delBtn: this.vaildData(this.permission.resourcemail_delete, false),
        editBtn: this.vaildData(this.permission.resourcemail_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    matchMail(type){
      if(type=='qqMail'){
        return '腾讯邮箱'
      }
      if(type=='qqExMail'){
        return '腾讯企业邮箱'
      }
    },
    handleSubmit(form, done) {
      console.log(this.debugForm)
      this.$axios.post('/api/blade-resource/endpoint/mail/send', form).then(
        () => {
          this.box = false
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!,请前往邮箱查看',
          })
          done()
        },
        error => {
          window.console.log(error)
          done()
        }
      )
    },
    handleDebug(row) {
      this.box = true
      this.debugForm.mailType = row.mailType
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    // getEnableName(status) {
    //   if (status == 0) {
    //     return '启用'
    //   } else {
    //     return '禁用'
    //   }
    // },
    enabled(row, done) {
      let msg = '确定启用操作'
      // if (row.status == 0) {
      //   msg = '确定启用操作'
      // } else {
      //   msg = '确定禁用操作'
      // }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        enable(row.id).then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        })
      })
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
