<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               :span-method="spanMethod"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
<!--        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.enterprisedivision_delete"
                   @click="handleDelete">删 除
        </el-button>-->
      </template>
      <template slot-scope="{ row }" slot="large">
        <p>>={{row.large}}</p>
      </template>
      <template slot-scope="{ row }" slot="medium">
        <p>{{row.medium}}~{{row.large-1}}</p>
      </template>
      <template slot-scope="{ row }" slot="small">
        <p>{{row.small}}~{{row.medium-1}}</p>
      </template>
      <template slot-scope="{ row }" slot="miniature">
        <p><{{row.small}}</p>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/resource/enterprisedivision";
  import {mapGetters} from "vuex";

  export default {
    data() {
      var checkMediumStart= (rule, value, callback) => {
        if(value>=this.form.large){
          callback(new Error('不能大于或等于大型标准'))
        }else if(value <= 0 ){
          callback(new Error('不能小于或等于零'))
        }
        callback()
      }
      var checkminia= (rule, value, callback) => {
        if(value>=this.form.small){
          callback(new Error('不能大于或等于小型标准'))
        }else if(value <= 0 ){
          callback(new Error('不能小于或等于零'))
        }
        callback()
      }
      var checkSmallStart= (rule, value, callback) => {
        if(value>=this.form.medium){
          callback(new Error('不能大于或等于中型标准'))
        }else if(value <= 0 ){
          callback(new Error('不能小于或等于零'))
        }
        callback()
      }
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        spanArr:[],
        key:"industryName",
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: false,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          delBtn:false,
          addBtn:false,
          column: [
            {
              label: "行业名称",
              prop: "industryName",
              display: false,
              rules: [{
                required: true,
                message: "请输入行业名称",
                trigger: "blur"
              }]
            },
            {
              label: "指标名称",
              prop: "indicatorType",
              type:"select",
              display: false,
              dicData: [{
                label: '营业收入(万)',
                value: 1
              }, {
                label: '从业人员(人)',
                value: 2
              }, {
                label: '资产总额(万)',
                value: 3
              }
              ],
            },
            {
              label: "大型",
              prop: "large",
              display: false,
            },
            {
              label: "中型",
              prop: "medium",
              display: false,
              rules: [{
                required: true,
                message: "请输入中型起",
                trigger: "blur"
              }]
            },
            {
              label: "小型",
              prop: "small",
              display: false,
              rules: [{
                required: true,
                message: "请输入小型",
                trigger: "blur"
              }]
            },
            {
              label: "微型企业",
              prop: "miniature",
              display: false,
              rules: [{
                required: true,
                message: "请输入微型企业",
                trigger: "blur"
              }]
            },
          ],
          group: [
            {
              column: [
                {
                  label: "行业名称",
                  prop: "industryName",
                  rules: [{
                    required: true,
                    message: "请输入行业名称",
                    trigger: "blur"
                  }]
                },
                {
                  label: "指标名称",
                  prop: "indicatorType",
                  type:"select",
                  dicData: [{
                    label: '营业收入(万)',
                    value: 1
                  }, {
                    label: '从业人员(人)',
                    value: 2
                  }, {
                    label: '资产总额(万)',
                    value: 3
                  }
                  ],
                  rules: [{
                    required: true,
                    message: "请选择指标名称",
                  }]
                },
                {
                  label: "大型-中型止",
                  prop: "large",
                  type:"number",
                  precision:0,
                  rules: [{
                    required: true,
                    message: "请输入大型-中型止",
                  }]
                },
                {
                  label: "中型起-小型止",
                  prop: "medium",
                  type:"number",
                  precision:0,
                  rules: [{
                    required: true,
                    validator: checkMediumStart,
                    trigger: "blur"
                  }]
                },
                {
                  label: "小型起-微型止",
                  prop: "small",
                  type:"number",
                  precision:0,
                  rules: [{
                    required: true,
                    validator: checkSmallStart,
                    trigger: "blur"
                  }]
                },
                {
                  label: "微型企业",
                  prop: "miniature",
                  display: false,
                  type:"number",
                  precision:0,
                  rules: [{
                    required: true,
                    validator: checkminia,
                    trigger: "blur"
                  }]
                },
              ]
            }
          ]
        },
        data: [],
      };
    },
    created(){

    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.enterprisedivision_add, false),
          viewBtn: this.vaildData(this.permission.enterprisedivision_view, false),
          delBtn: this.vaildData(this.permission.enterprisedivision_delete, false),
          editBtn: this.vaildData(this.permission.enterprisedivision_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowspan() {
        this.spanArr=[];
        this.position=0;
        this.data.forEach((item,index)=>{
          if(index===0){
            this.spanArr.push(1)
            this.position=0;
          }else{
            if(this.data[index][this.key]===this.data[index-1][this.key]){
              this.spanArr[this.position] +=1;
              this.spanArr.push(0)
            }else{
              this.spanArr.push(1)
              this.position=index
            }
          }
        })
      },
      spanMethod({ row, column, rowIndex, columnIndex }) {
        if(column.property==[this.key]){
          const _row=this.spanArr[rowIndex];
          const _col=_row>0?1:0;
          return {
            rowspan:_row,
            colspan:_col
          }
        }
        },

      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.rowspan()
          this.selectionClear();
        });

      }
    },

  };
</script>

<style>
</style>
