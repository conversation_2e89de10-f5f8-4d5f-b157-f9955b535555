<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <!--      <template slot="menuLeft">-->
      <!--        <el-button-->
      <!--          type="primary"-->
      <!--          size="small"-->
      <!--          icon="el-icon-plus"-->
      <!--          v-if="permission.businessfield_add"-->
      <!--          @click="cadd"-->
      <!--        >新 增-->
      <!--        </el-button>-->
      <!--      </template>-->
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(row, index)"
          size="small"
          v-if="row.resourceType == 0 && permission.businessfield_view"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(row, index)"
          size="small"
          v-if="row.resourceType == 0 && permission.businessfield_delete"
        >删除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/resource/businessfield";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: "数据来源",
            prop: "dataForm",
            search: true,
            type: "select",
            dicData: [
              {
                label: "数据库",
                value: 0,
              },
              {
                label: "自定义",
                value: 1,
              },
            ],
            rules: [{
              required: true,
              message: "请输入表名",
              trigger: "blur"
            }]
          }, {
            label: "授信表单",
            prop: "creditType",
            search: true,
            type: "select",
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            rules: [{
              required: true,
              message: "请输入表名",
              trigger: "blur"
            }]
          },
          {
            label: "表名",
            search: true,
            prop: "tableName",
            type: "tree",
            display: false,
            dicUrl: `/api/blade-resource/web-back/resource/businessfield/getTableNames`,
            props: {
              label: "tableName",
              value: "tableName",
            },
            rules: [{
              required: true,
              message: "请输入表名",
              trigger: "blur"
            }]
          },
          {
            label: "字段名",
            display: false,
            type: "select",
            prop: "fieldName",
            rules: [{
              required: true,
              message: "请输入字段名",
              trigger: "blur"
            }]
          },
          {
            label: "数据类型",
            prop: "dataType",
            type: "tree",
            dataType: 'string',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=jrzh_resource_business_field_data_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [{
              required: true,
              message: "请输入数据类型",
              trigger: "blur"
            }]
          },
          {
            label: "指标名",
            prop: "indexName",
            search: true,
            rules: [{
              required: true,
              message: "请输入指标名",
              trigger: "blur"
            }]
          },
          {
            label: '字段模块',
            prop: "moduleType",
            dicUrl: '/api/blade-resource/web-back/resource/resourceBusinessType/listDetailsAllOrName',
            type: "tree",
            props: {
              label: 'name',
              value: 'id'
            }
          },
          {
            label: "排序",
            prop: "orderNum",
            rules: [{
              required: true,
              message: "请输入排序",
              trigger: "blur"
            }]
          },
        ],
        group: [
          {
            display: false,
            hide: true,
            column: [
              {
                label: "表名",
                prop: "tableName",
                type: "tree",
                cascaderItem: ['fieldName'],
                cascaderChange: true,
                dicUrl: '/api/blade-resource/web-back/resource/businessfield/getTableNames',
                props: {
                  label: "tableName",
                  value: "tableName",
                },
                rules: [{
                  required: true,
                  message: "请输入表名",
                  trigger: "blur"
                }]
              },
              {
                label: "字段名",
                prop: "fieldName",
                type: "tree",
                dicUrl: `/api/blade-resource/web-back/resource/businessfield/getFieldByTableName?tableName={{key}}`,
                props: {
                  label: "columnName",
                  value: "columnName",
                },
                rules: [{
                  required: true,
                  message: "请输入字段名",
                  trigger: "blur"
                }]
              },
            ],
          },
          {
            hide: true,
            display: false,
            column: [
              {
                label: "算法类型",
                prop: "customizeType",
                type: 'select',
                dataType: 'string',
                dicUrl: '/api/blade-system/dict-biz/dictionary?code=customizeType',
                props: {
                  label: 'dictValue',
                  value: 'dictKey',
                },
              },
              {
                label: "自定义名称",
                prop: "customizeName",
                rules: [{
                  required: true,
                  message: "请输入自定义名称",
                  trigger: "blur"
                }]
              },
            ]
          }
        ],
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.businessfield_add, false),
        viewBtn: this.vaildData(this.permission.businessfield_view, false),
        delBtn: this.vaildData(this.permission.businessfield_delete, false),
        editBtn: this.vaildData(this.permission.businessfield_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  watch: {
    'form.dataForm': {
      handler(newVal) {
        let tableGroup = this.option.group[0];
        let customizeGroup = this.option.group[1];
        if (newVal == 0) {
          this.dataSourceFieldShow(tableGroup, customizeGroup)
        } else {
          this.customizeFieldShow(tableGroup, customizeGroup)
        }
        let form = this.option;
        console.log(form)
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    cdelete(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    customizeFieldShow(tableGroup, customizeGroup) {
      tableGroup.display = false;
      customizeGroup.display = true;
    },
    dataSourceFieldShow(tableGroup, customizeGroup) {
      tableGroup.display = true;
      customizeGroup.display = false;
    },
    rowSave(row, done, loading) {
      this.beforeCommit(row)
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      this.beforeCommit(row)
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeCommit(row) {
      if (row.dataForm == 0) {
        this.resetCustomize(row)
      } else {
        this.resetDataSource(row)
      }
    },
    resetCustomize(form) {
      form.customizeName = null;
      form.customizeType = null;
    },
    resetDataSource(form) {
      form.tableName = null;
      form.tableDesc = null;
      form.fieldName = null;
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      } else {
        this.initForm();
      }
      done();
    },
    initForm() {
      this.form.dataForm = 0
      this.form.creditType = 0
      this.form.orderNum = 1
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
