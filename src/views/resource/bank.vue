<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft"> </template>
      <template slot-scope="{ scope, row }" slot="status">
        <el-tag v-if="row.status == 1">已启动</el-tag>
        <el-tag type="info" v-if="row.status == 0">已禁用</el-tag>
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          @click="enabled(row)"
        >
          {{ getEnableName(row.status) }}
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/resource/bank'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        addBtn: true,
        editBtn: true,
        delBtn: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '银行行号',
            search: true,
            prop: 'bankNo',
            rules: [
              {
                required: true,
                message: '请输入银行行号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '银行名称',
            search: true,
            prop: 'name',
            rules: [
              {
                required: true,
                message: '请输入银行名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '清算行行号',
            search: true,
            prop: 'clearBankNo',
            rules: [
              {
                required: true,
                message: '请输入清算行行号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '代理行行号',
            search: true,
            prop: 'agentBankNo',
            rules: [
              {
                required: true,
                message: '请输入代理行行号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'status',
            display: false,
            formslot: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.bank_add, false),
        viewBtn: this.vaildData(this.permission.bank_view, false),
        delBtn: this.vaildData(this.permission.bank_delete, false),
        editBtn: this.vaildData(this.permission.bank_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    enabled(row, done) {
      let msg = ''
      if (row.status == 0) {
        msg = '确定启用操作'
      } else {
        msg = '确定禁用操作'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row.status == 1) {
          row.status = 0
        } else {
          row.status = 1
        }
        this.rowSave(row)
      })
    },
    getEnableName(status) {
      if (status == 0) {
        return '启用'
      } else {
        return '禁用'
      }
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
