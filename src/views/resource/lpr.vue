<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="success"
          size="small"
          icon="el-icon-help"
          target="_blank"
          @click="toweb"
        >LPR信息来源
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from '@/api/resource/lpr'
import {mapGetters} from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '发布时间',
            prop: 'publishTimeRange',
            type: 'datetime',
            format: 'yyyy-MM-dd hh:mm:ss',
            valueFormat: 'yyyy-MM-dd hh:mm:ss',
            searchRange: true,
            hide: true,
            display: false,
            search: true,
          },
          {
            label: '发布日期',
            prop: 'publishTime',
            type: 'date',
            format: 'yyyy-MM-dd hh:mm:ss',
            valueFormat: 'yyyy-MM-dd hh:mm:ss',
            rules: [
              {
                required: true,
                message: '请输入通知时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '年LPR(%)',
            prop: 'yearLprRate',
            type:'number',
            minRows:0,
            rules: [
              {
                required: true,
                message: '请输入年LPR',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '5年LPR(%)',
            type:'number',
            minRows:0,
            prop: 'fiveYearLprRate',
            rules: [
              {
                required: true,
                message: '请输入5年LPR',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.lpr_add, false),
        viewBtn: this.vaildData(this.permission.lpr_view, false),
        delBtn: this.vaildData(this.permission.lpr_delete, false),
        editBtn: this.vaildData(this.permission.lpr_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    toweb() {
      window.open('http://www.pbc.gov.cn/zhengcehuobisi/125207/125213/125440/3876551/index.html','_blank');
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      const {publishTimeRange} = this.query
      let values = {
        ...params,
      }
      if (publishTimeRange) {
        values = {
          ...params,
          publish_time_datege: publishTimeRange[0],
          publish_time_datele: publishTimeRange[1],
          ...this.query,
        }
        values.publishTimeRange = null
      }
      console.log(values)
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query, values)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
