<template>
  <div class="customer-container">
    <div class="customer-card-panel">
      <div class="header-container">
        <div class="user-container">
          <el-image
            class="avatar"
            style="width: 56px; height: 56px"
            :src="detail.logoSrc"
            :fit="contain"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <div class="info-container">
            <div class="title-container">
              <div class="title-content">
                <span class="title">{{
                  detail.name || detail.personalName || '--'
                }}</span>
                <el-rate
                  :v-model="3"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                >
                </el-rate>
              </div>
              <div class="user-info-container">
                <template v-if="this.archiveType === 'person'">
                  <div class="user-item text">
                    <span class="label">用户名：</span>
                    <span class="value">{{ detail.accountUser || '--' }}</span>
                  </div>
                  <div class="user-item text">
                    <span class="label">手机号：</span>
                    <span class="value">{{
                      detail.personalPhone || '--'
                    }}</span>
                  </div>
                </template>
                <template v-else>
                  <div class="user-item text">
                    <span class="label">所属者：</span>
                    <span class="value">{{ detail.belonging || '--' }}</span>
                  </div>
                </template>
                <div class="user-item text">
                  <span class="label">客户经理：</span>
                  <span class="value">{{ detail.invitationName || '--' }}</span>
                </div>
              </div>
            </div>
            <div class="desc-container">
              <div class="tag-list">
                <Tag
                  v-if="isRealName"
                  name="已实名"
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  v-if="customerTagdicDataMap[detail.customerLabel]"
                  :name="customerTagdicDataMap[detail.customerLabel] || '--'"
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  v-if="clientType"
                  :name="clientType"
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  :name="
                    detail.customerScore
                      ? `${parseInt(detail.customerScore, 10)}分`
                      : '未评分'
                  "
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  v-if="archiveType !== 'person' && detail.operationStatus"
                  :name="detail.operationStatus"
                  :color="'#4E9BFC'"
                  :backgroundColor="'#f2f2f2'"
                  :radius="true"
                />
                <Tag
                  v-if="archiveType !== 'person' && detail.id"
                  :name="detail.companyType || '规模未知'"
                  color="#4E9BFC"
                  backgroundColor="#f2f2f2"
                  :radius="true"
                />
              </div>
              <div class="date-container">
                <template v-if="this.archiveType === 'person'">
                  <div class="access-date-wrapper">
                    <span class="text">{{ detail.lastVisitTime }}访问过</span>
                  </div>
                </template>
                <div class="open-date-wrapper">
                  <span class="text">{{ detail.createTime }}创建</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <LabelBar :labelList="labelList" @handleSwitch="handleSwitch" />
      </div>
    </div>
    <ModuleBaseData
      v-if="activeModule === '基础资料'"
      :unifiedSocialCode="detail.unifiedSocialCode"
      :companyName="detail.name"
      :companyId="companyId"
      :isRealName="isRealName"
      :businessLicenceAttachId="detail.businessLicenceAttachId"
      :corporationBackAttachId="detail.corporationBackAttachId"
      :corporationFaceAttachId="detail.corporationFaceAttachId"
    />
    <ModuleValueAnalysis
      :companyId="companyId"
      v-else-if="activeModule === '价值分析'"
    />
    <ModuleCreditInformationIndex
      :companyId="companyId"
      v-else-if="activeModule === '征信信息'"
    />
    <ModuleNegativeFactor
      v-else-if="activeModule === '负面因素'"
      :unifiedSocialCode="detail.unifiedSocialCode"
      :companyName="detail.name"
      :companyId="companyId"
    />
    <ModuleOpenProduct
      v-else-if="activeModule === '开通产品'"
      :unifiedSocialCode="detail.unifiedSocialCode"
      :companyId="companyId"
    />
    <ModuleTrade v-else-if="activeModule === '贸易伙伴'" />
    <ModuleHistoricalChange
      v-else-if="activeModule === '历史变更'"
      :companyId="companyId"
    />
    <ModuleDocumentInfo
      v-else-if="activeModule === '证件资料'"
      :unifiedSocialCode="detail.unifiedSocialCode"
      :companyName="detail.name"
      :personalName="detail.personalName"
      :companyId="companyId"
    />
    <ModuleEnterprisePerson
      v-else-if="activeModule === '企业成员'"
      :companyId="companyId"
    />
    <ModuleAssetInformation
      v-else-if="activeModule === '资产信息'"
      :companyId="companyId"
    />
    <ModuleDebtInformationCoreEnt
      v-else-if="
        activeModule === '负债信息' && archiveType === 'coreEnterprise'
      "
      :companyId="companyId"
    />
    <ModulePersonContact
      v-else-if="activeModule === '联系人'"
      :customerId="detail.customerId"
    />
    <ModuleFinancingRecords
      v-else-if="activeModule === '融资记录'"
      :companyId="companyId"
    />
    <BasicFooter
      v-if="archiveType != 'coreEnterprise'"
      :btn-options="btnOptions"
      @click="handleClickEvent"
    ></BasicFooter>
  </div>
</template>

<script>
import BasicFooter from '@/components/basic-footer/index'
import LabelBar from './components/LabelBar/index.vue'
import ModuleBaseData from './components/ModuleBaseData/index.vue'
import ModuleValueAnalysis from './components/ModuleValueAnalysis/index.vue'
import ModuleCreditInformationIndex from './components/ModuleCreditInformation/index.vue'
import ModuleNegativeFactor from './components/ModuleNegativeFactor/index.vue'
import ModuleOpenProduct from './components/ModuleOpenProduct/index.vue'
import ModuleTrade from './components/ModuleTrade/index.vue'
import ModuleHistoricalChange from './components/ModuleHistoricalChange/index.vue'
import ModuleDocumentInfo from './components/ModuleDocumentInfo/index.vue'
import ModuleEnterprisePerson from './components/ModuleEnterprisePerson/index.vue'
import ModuleAssetInformation from './components/ModuleAssetInformation/index.vue'
import ModuleDebtInformationCoreEnt from './components/ModuleDebtInformationCoreEnt/index.vue'
import ModulePersonContact from './components/ModulePersonContact/index.vue'
import ModuleFinancingRecords from './components/ModuleFinancingRecords/index.vue'
import Tag from './components/Tag/index.vue'
import { getDetail as requestFinancingArchivesDetail } from '@/api/customer/frontfinancinglist'
// import { requestCoreArchiveDetail } from '@/api/customer/archives/core'
import { requestPersonArchiveDetail } from '@/api/customer/archives/person'
import {
  customerTagdicDataMap,
  enterpriseTypeDicDataMap,
  coreEnterpriseTypeDicDataMap,
} from './config'
const btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
  {
    btnName: '客户画像',
    funName: 'Archives',
    type: 'primary',
  },
]
export default {
  name: 'CustomerArchivesIndex',
  components: {
    LabelBar,
    ModuleBaseData,
    ModuleValueAnalysis,
    ModuleCreditInformationIndex,
    ModuleNegativeFactor,
    ModuleOpenProduct,
    ModuleTrade,
    ModuleHistoricalChange,
    ModuleDocumentInfo,
    ModuleEnterprisePerson,
    ModuleAssetInformation,
    ModuleDebtInformationCoreEnt,
    ModulePersonContact,
    ModuleFinancingRecords,
    Tag,
    BasicFooter,
  },
  data() {
    return {
      customerTagdicDataMap,
      enterpriseTypeDicDataMap,
      coreEnterpriseTypeDicDataMap,
      id: undefined,
      companyId: undefined,
      archiveType: undefined,
      labelList: [],
      activeModule: undefined,
      detail: {},
      btnOptions,
    }
  },
  computed: {
    isRealName() {
      return this.detail.customerStatus === 1
    },
    clientType() {
      return this.archiveType === 'coreEnterprise'
        ? coreEnterpriseTypeDicDataMap[this.detail.enterpriseType]
        : this.archiveType === 'person'
        ? '个人'
        : this.detail.customerStatus !== 1
        ? '--'
        : enterpriseTypeDicDataMap[this.detail.status]
    },
  },
  watch: {
    // eslint-disable-next-line no-unused-vars
    // $route(to, from) {
    //   this.$router.go(0)
    // },
  },
  provide() {
    return {
      id: this.$route.params.id,
      /**
       * 该值现为异步获取，函数形式的无法保持响应式，仅用于子组件提交时获取 companyId
       * 如果要获取数据用 props 传递给子组件，或者将列表部分的 companyID 以参数形式传递过来
       */
      companyIdFunc: () => this.companyId,
      companyNameFunc: () => this.detail.name,
      unifiedSocialCodeFunc: () => this.detail.unifiedSocialCode,
      // type: 什么对象的档案；企业(enterprise) 个人(person) 核心企业(coreEnterprise)
      type: this.$route.params.type,
    }
  },
  created() {
    this.archiveType = this.$route.params.type
    this.id = this.$route.params.id
    if (this.archiveType === 'coreEnterprise') {
      this.labelList = [
        '基础资料',
        '负面因素',
        '企业成员',
        '征信信息',
        '价值分析',
        '资产信息',
        '负债信息',
        '开通产品',
        '贸易伙伴',
        '历史变更',
        '证件资料',
        '账户信息',
      ]
    } else if (this.archiveType === 'enterprise') {
      this.labelList = [
        '基础资料',
        '负面因素',
        '企业成员',
        '征信信息',
        '价值分析',
        '资产信息',
        '负债信息',
        '融资记录',
        '开通产品',
        '贸易伙伴',
        '历史变更',
        '证件资料',
        '账户信息',
      ]
    } else {
      this.labelList = [
        '基础资料',
        '联系人',
        '征信信息',
        '价值分析',
        '资产信息',
        '负债信息',
        '融资记录',
        '开通产品',
        '贸易伙伴',
        '历史变更',
        '证件资料',
        '账户信息',
      ]
    }
    if (this.activeModule === undefined) this.activeModule = '基础资料'
    // if (this.archiveType === 'coreEnterprise') {
    //   // 核心企业
    //   requestCoreArchiveDetail(this.id)
    //     .then(({ data }) => {
    //       if (data.success) {
    //         data = data.data
    //         this.detail = data || {}
    //         this.companyId = data.companyId
    //       }
    //     })
    //     .catch(() => {})
    // } else if (this.archiveType === 'enterprise') {
    // 该核心企业接口后台已弃用，现使用融资企业的接口
    if (
      this.archiveType === 'enterprise' ||
      this.archiveType === 'coreEnterprise'
    ) {
      // 融资客户
      requestFinancingArchivesDetail(this.id)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.detail = data || {}
            this.companyId = data.companyId
          }
        })
        .catch(() => {})
    } else {
      // 融资个体户
      requestPersonArchiveDetail(this.id)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            data.companyId = data.userId
            data.customerId = data.customerFrontUserId
            data.userId = data.roleUserId
            this.detail = data || {}
            this.companyId = data.userId
          }
        })
        .catch(() => {})
    }
  },
  methods: {
    handleSwitch(value) {
      this.activeModule = value
    },
    handleClickEvent(name) {
      switch (name) {
        case 'Revert':
          this.$router.$avueRouter.closeTag()
          this.$router.push('/customer/financingEnterprise')
          break
        case 'Archives':
          this.$router.$avueRouter.closeTag()
          this.$router.push({
            path: `/customer/customerInfo/${this.archiveType}`,
            query: {
              userId: this.companyId,
              id: this.id,
            },
          })
          break
      }
    },
  },
}
</script>

<style scoped lang="scss">
.customer-container {
  margin-bottom: 64px;

  .customer-card-panel {
    margin-bottom: 20px;

    .header-container {
      .user-container {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        margin-bottom: 20px;

        .avatar {
          flex-shrink: 0;

          ::v-deep {
            .image-slot {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
              height: 100%;
              background: #f5f7fa;
              color: #909399;
              font-size: 24px;
            }
          }
        }

        .info-container {
          width: 100%;
          padding: 0 12px;

          .text {
            color: rgba(100, 100, 100, 100);
            font-size: 14px;
          }

          .title-container {
            display: flex;
            align-content: center;
            justify-content: space-between;

            .title-content {
              display: inline-flex;
              align-content: center;

              > * {
                margin-right: 10px;

                &:last-child {
                  margin-right: 0;
                }
              }

              .title {
                display: inline-block;
                color: rgba(0, 0, 0, 0.85);
                font-size: 16px;
                font-weight: 600;
                text-align: left;
              }
            }

            .user-info-container {
              display: inline-flex;
              align-content: center;

              > * {
                margin-right: 24px;

                &:last-child {
                  margin-right: 0;
                }
              }
            }
          }

          .desc-container {
            display: flex;
            align-content: center;
            justify-content: space-between;
            margin-top: 4px;

            .tag-list {
              > * {
                margin-right: 10px;

                &:last-child {
                  margin-right: 0;
                }
              }
            }

            .date-container {
              display: flex;
              align-items: center;
            }

            .open-date-wrapper,
            .access-date-wrapper {
              display: inline-flex;
              align-items: center;

              span {
                font-size: 14px;
                line-height: 14px;
              }
            }

            .access-date-wrapper {
              margin-right: 24px;
            }
          }
        }
      }

      .user-desc-container {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
        padding: 20px 20px 0;
        background-color: rgba(247, 247, 247, 100);
        border-radius: 8px;

        .desc-item {
          display: inline-flex;
          align-items: center;
          flex-wrap: nowrap;
          width: calc(100% / 3);
          margin-bottom: 20px;
          line-height: 24px;

          .label {
            flex-shrink: 0;
            display: inline-block;
            width: 120px;
            text-align: right;
          }

          .value {
            display: inline-block;
            width: 100%;
          }
        }
      }
    }
  }
}
</style>

<style>
.customer-card-panel {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
}
</style>
