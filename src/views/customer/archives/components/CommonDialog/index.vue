<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :custom-class="center ? 'common-dialog center' : 'common-dialog'"
    :fullscreen="isFullscreen"
    :show-close="false"
    append-to-body
    destroy-on-close
    v-bind="$attrs"
  >
    <div slot="title">
      <div class="common-title-wrapper" :class="{ 'no-border': noBorder }">
        <span
          class="common-dialog-title"
          :style="`${centerTitle ? 'width: 100%; text-align: center;' : ''}`"
          >{{ title }}</span
        >
        <div class="common-dialog-menu">
          <i
            v-if="enableFullScreenBtn"
            @click="handleFullScreen"
            class="el-icon-full-screen"
          />
          <i @click="handleClose" class="el-icon-close" />
        </div>
      </div>
    </div>
    <div
      class="scroll-wrapper"
      :style="{
        height: bodyHeight,
        maxHeight: `calc(100vh - 58px ${
          this.noButton ? '' : '- 58px'
        })`,
        overflowY: 'scroll',
      }"
    >
      <el-scrollbar style="height: 100%">
        <slot></slot>
      </el-scrollbar>
    </div>
    <template v-if="!noButton" slot="footer">
      <div class="common-footer-wrapper" :class="{ 'no-border': noBorder }">
        <el-button :disabled="cancelDisable" @click="cancelBtn" size="small">{{
          cancelBtnText
        }}</el-button>
        <el-button
          v-if="!noConfirmBtn"
          type="primary"
          :loading="confirmLoading"
          @click="confirmBtn"
          size="small"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'CommonDialog',
  props: {
    title: {
      type: String,
      default: '',
    },
    centerTitle: {
      type: Boolean,
      default: false,
    },
    center: {
      type: Boolean,
      default: false,
    },
    height: {
      type: String,
      default: '',
    },
    cancelDisable: {
      type: Boolean,
      default: false,
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    enableFullScreenBtn: {
      type: Boolean,
      default: true,
    },
    noButton: {
      type: Boolean,
      default: false,
    },
    noConfirmBtn: {
      type: Boolean,
      default: false,
    },
    cancelBtnText: {
      type: String,
      default: '取消',
    },
    noBorder: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isFullscreen: false,
      bodyHeight: '',
    }
  },
  created() {
    this.bodyHeight = this.height
      ? `calc(${this.height} - 58px ${this.noButton ? '' : '- 58px'})`
      : ''
    this.isFullscreen = false
  },
  methods: {
    handleOpen() {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    },
    cancelBtn() {
      this.handleClose()
      this.$emit('cancel')
    },
    confirmBtn() {
      this.$emit('confirm')
    },
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen
      if (this.isFullscreen) {
        this.bodyHeight = `calc(100vh - 58px ${this.noButton ? '' : '- 58px'})`
      } else {
        this.bodyHeight = this.height
          ? `calc(${this.height} - 58px ${this.noButton ? '' : '- 58px'})`
          : ''
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .common-dialog {
    display: flex;
    flex-direction: column;
    max-height: 100vh;

    &.center {
      margin: 0 auto !important;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    padding: 0 !important;
  }

  .el-dialog__body {
    max-height: calc(100vh - 58px);
  }

  .a123 {
    height: 50vh;
  }
}

.common-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 58px;
  border-bottom: 1px solid #dbdbdb;
  box-sizing: border-box;

  &.no-border {
    border-bottom: 1px solid transparent;
  }

  .common-dialog-title {
    font-size: 16px;
    font-family: SourceHanSansSC-regular;
  }

  .common-dialog-menu {
    > * {
      margin-right: 10px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.65);

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}

.common-footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: end;
  height: 58px;
  padding: 0 18px;
  border-top: 1px solid #dbdbdb;
  box-sizing: border-box;

  &.no-border {
    border-top: 1px solid transparent;
  }
}
</style>
