<template>
  <div class="label-bar-container">
    <el-radio-group v-model="radio" @change="handleChange">
      <el-radio-button
        v-for="item of labelList"
        :key="item"
        :label="item"
      ></el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
export default {
  name: 'CustomerLabelBarIndex',
  props: {
    labelList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      radio: '',
    }
  },
  created() {
    const labelList = this.labelList
    if (Object.prototype.toString.call(labelList) === '[object Array]') {
      this.radio = this.labelList[0]
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('handleSwitch', value)
    },
  },
}
</script>

<style lang="scss" scoped>
.label-bar-container {
  .el-radio-group {
    width: 100%;
    display: flex;

    .el-radio-button {
      flex: 1;

      ::v-deep {
        .el-radio-button__inner {
          width: 100%;
        }
      }
    }
  }
}
</style>
