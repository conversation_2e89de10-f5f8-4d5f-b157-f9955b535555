<template>
  <Dialog
    title="还款信息"
    ref="DialogRef"
    width="60vw"
    center
    noConfirmBtn
    cancelBtnText="关闭"
  >
    <div class="content">
      <template v-if="loading">
        <div v-loading="loading" style="height: 100px"></div>
      </template>
      <template v-else-if="!loading && recordsData.length === 0">
        <el-empty :image-size="200"></el-empty>
      </template>
      <template v-else>
        <div v-for="item of recordsData" :key="item.id" class="card-container">
          <div class="progress-bar-wrapper">
            <span class="point" />
            <span class="line" />
          </div>
          <div class="card-content">
            <div class="date">
              <span>{{ item.createTime }}</span>
            </div>
            <div class="card-msg-container">
              <div class="form-item">
                <span class="form-title">还款金额：</span>
                <span class="form-value"
                  >{{ item.refundAmount | formatMoney }}元</span
                >
              </div>
              <div class="form-item">
                <span class="form-title">还款凭证：</span>
                <el-image
                  style="width: 100px; height: 100px"
                  :src="item.proof"
                  :preview-src-list="[item.proof]"
                >
                </el-image>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { requestAssetRefundDetail } from '@/api/customer/archives/archive'

export default {
  name: 'CustomerArchivesModuleAssetInformationDialogUseDetail',
  components: { Dialog },
  data() {
    return {
      loading: true,
      recordsData: [],
    }
  },
  methods: {
    handleOpen(id) {
      if (!id) return
      this.loading = true
      this.recordsData = []
      this.$refs.DialogRef.handleOpen()
      requestAssetRefundDetail({ saleContractId: id })
        .then(({ data }) => {
          if (data.success) {
            data = data.data || []

            this.recordsData = data
          }
          this.loading = false
        })
        .catch(() => {})
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
  },
}
</script>

<style lang="scss" scoped>
$pointWidthHeight: 12px;
$cardMarginBottom: 32px;

.content {
  padding: 24px 24px 24px 48px;

  .card-container {
    position: relative;
    margin-bottom: $cardMarginBottom;

    &:last-child {
      margin-bottom: 0;

      .progress-bar-wrapper {
        .line {
          display: none;
        }
      }
    }

    .progress-bar-wrapper {
      position: absolute;
      top: 10px - $pointWidthHeight / 2;
      left: -24px;
      bottom: -($cardMarginBottom + 10px - $pointWidthHeight / 2);

      .point {
        position: absolute;
        top: 0;
        display: inline-block;
        width: $pointWidthHeight;
        height: $pointWidthHeight;
        background-color: rgba(215, 215, 215, 100);
        border-radius: 50%;
      }

      .line {
        position: absolute;
        left: $pointWidthHeight / 2 - 1px;
        display: inline-block;
        width: 2px;
        height: 100%;
        background-color: rgba(215, 215, 215, 100);
      }
    }

    .card-content {
      .date {
        margin-bottom: 12px;
        height: 20px;
        color: rgba(106, 106, 106, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }

      .card-msg-container {
        padding: 24px 32px;
        border-radius: 6px;
        text-align: center;
        border: 1px solid rgba(215, 215, 215, 100);

        .form-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .form-title {
            width: 70px;
            height: 20px;
            color: rgba(106, 106, 106, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }

          .form-value {
            color: rgba(77, 0, 0, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
    }
  }
}
</style>
