<template>
  <Dialog
    title="应收账款凭证"
    ref="DialogRef"
    width="600px"
    center
    noConfirmBtn
    cancelBtnText="返回"
  >
    <div class="preview-proof-container">
      <el-descriptions
        class="margin-top"
        :column="1"
        :labelStyle="{
          width: '150px',
          textAlign: 'right',
          color: 'rgba(153, 153, 153, 100)',
          fontSize: '14px',
          marginBottom: '24px',
        }"
        :contentStyle="{
          color: 'rgba(38, 38, 38, 100)',
          fontSize: '14px',
        }"
      >
        <el-descriptions-item label="凭证类型">{{
          previewProofData.invoiceId === null
            ? previewProofData.targetType
            : '发票'
        }}</el-descriptions-item>
        <template v-if="previewProofData.invoiceId !== null">
          <el-descriptions-item label="发票类型">{{
            invoiceTypeMap[previewProofData.type] || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="发票代码">{{
            previewProofData.code
          }}</el-descriptions-item>
          <el-descriptions-item label="发票号码">{{
            previewProofData.number
          }}</el-descriptions-item>
          <el-descriptions-item label="发票金额（含税）">
            {{
              previewProofData.total && previewProofData.total !== '--'
                ? previewProofData.total.replace(/[^\d.]/g, '')
                : '--' | formatMoney
            }}{{
              previewProofData.total && previewProofData.total !== '--'
                ? '元'
                : ''
            }}
          </el-descriptions-item>
          <el-descriptions-item label="发票金额（不含税）">
            {{
              previewProofData.subtotalAmount && previewProofData.subtotalAmount !== '--'
                ? previewProofData.subtotalAmount.replace(/[^\d.]/g, '')
                : '--' | formatMoney
            }}{{
              previewProofData.subtotalAmount &&
              previewProofData.subtotalAmount !== '--'
                ? '元'
                : ''
            }}
          </el-descriptions-item>
          <el-descriptions-item label="开票日期">{{
            previewProofData.issueDate
          }}</el-descriptions-item>
          <el-descriptions-item label="校验码">{{
            previewProofData.checkCode || '--'
          }}</el-descriptions-item>
        </template>
        <el-descriptions-item label="查看凭证">
          <el-image
            style="width: 100px; height: 100px"
            :src="previewProofData.preUrl"
            fit="contain"
            @click="handlePreviewImage"
          >
            <div slot="error" class="image-error-container">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <FilePreview :url="pdfSrc" />
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { getDetail } from '@/api/resource/attach'
import { requestInvoiceDetail } from '@/api/customer/archives/archive'

export default {
  name: 'CustomerArchivesModuleAssetInformationDialogPreviewProofIndex',
  components: { Dialog },
  props: {
    invoiceTypeMap: {
      type: Object,
      default: () => ({}),
    },
    proofTypeMap: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      // 查看凭证
      previewProofData: {},
      pdfSrc: '',
    }
  },
  methods: {
    // 传入表格当前行数据，获取对应的凭证数据
    handleOpen(row) {
      let previewProofData = { invoiceId: row.invoiceId }
      getDetail(row.proof)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            previewProofData.preUrl = data.link
          }
        })
        .catch(() => {})
        .finally(() => {
          if (row.invoiceId !== undefined && row.invoiceId !== null) {
            previewProofData = {
              ...previewProofData,
              code: '--',
              number: '--',
              total: '--',
              subtotalAmount: '--',
              issueDate: '--',
              checkCode: '--',
            }
            requestInvoiceDetail({ id: row.invoiceId })
              .then(({ data }) => {
                if (data.success) {
                  data = data.data
                  this.previewProofData = {
                    ...previewProofData,
                    ...data,
                  }
                }
              })
              .catch(() => {})
          } else {
            previewProofData.targetType =
              this.proofTypeMap[row.proofType] || '--'
          }
          this.previewProofData = previewProofData
          this.$refs.DialogRef.handleOpen()
        })
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
    handlePreviewImage() {
      const targetUrl = this.previewProofData.preUrl
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.preview-proof-container {
  padding: 24px 24px 0;

  .el-image {
    cursor: pointer;

    ::v-deep {
      .image-error-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #e8e8e8;
        border-radius: 6px;
      }
    }
  }
}
</style>
