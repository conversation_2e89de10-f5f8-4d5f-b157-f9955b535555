<template>
  <div class="asset-info-container">
    <LayoutCard title="资产信息">
      <div v-loading="loading" class="content">
        <div class="label-bar-container">
          <el-tabs v-model="activeTab">
            <el-tab-pane
              v-for="item of tabList"
              :label="item"
              :name="item"
              :key="item"
            />
          </el-tabs>
        </div>
        <div class="table-container">
          <el-table
            :data="tableData"
            style="width: 100%"
            :header-cell-style="
              () => {
                return { backgroundColor: '#f7f7f7', color: '#000' }
              }
            "
          >
            <el-table-column type="index" label="#" width="48" align="center" />
            <el-table-column prop="contractNo" label="唯一标识" min-width="100">
              <template slot-scope="{ row }">
                <span
                  >{{ row.contractNo !== null ? row.contractNo : '--' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="proofType" label="类型" min-width="100">
              <template slot-scope="{ row }">
                <Tag
                  :name="
                    row.proofType !== null && proofTypeMap.ready
                      ? proofTypeMap[row.proofType]
                      : '--'
                  "
                  color="#00072A"
                  backgroundColor="#EAECF1"
                  borderColor="transparent"
                  radius
                />
              </template>
            </el-table-column>
            <el-table-column prop="contractNo" label="应付方" min-width="100">
              <template slot-scope="{ row }">
                <span class="link"
                  >{{ row.enterpriseName !== null ? row.enterpriseName : '--' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="proofNo" label="凭证编号" min-width="200" />
            <el-table-column prop="orderAmount" label="订单金额">
              <template slot-scope="{ row }">
                <span>¥{{ row.orderAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="accountAmount" label="账面金额">
              <template slot-scope="{ row }">
                <span>¥{{ row.accountAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="effectiveAmount" label="有效金额">
              <template slot-scope="{ row }">
                <span>¥{{ row.effectiveAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="paymentDays" label="账期">
              <template slot-scope="{ row }">
                <span>{{ row.paymentDays }}天</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="startTime"
              label="开始日期"
              min-width="100"
            />
            <el-table-column
              prop="expireTime"
              label="到期日期"
              min-width="100"
            />
            <el-table-column prop="gracePeriod" label="宽限期" min-width="100">
              <template slot-scope="{ row }">
                <span>{{ row.gracePeriod }}天</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="proofStatus"
              label="凭证状态"
              min-width="100"
            >
              <template slot-scope="{ row }">
                <Tag
                  :name="proofStatusMap[row.proofStatus].name"
                  :color="proofStatusMap[row.proofStatus].color"
                  :backgroundColor="
                    proofStatusMap[row.proofStatus].backgroundColor
                  "
                  :borderColor="proofStatusMap[row.proofStatus].borderColor"
                  minWidth="64px"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="options"
              label="操作"
              fixed="right"
              min-width="260"
            >
              <template slot-scope="{ row }">
                <el-button
                  @click="handleBtnViewProof(row)"
                  type="text"
                  size="small"
                  >查看凭证</el-button
                >
                <!-- <el-button
                  @click="handleBtnApproveDetail(row)"
                  type="text"
                  size="small"
                  >审批详情</el-button
                > -->
                <el-button
                  v-if="row.confirmStatus === 2"
                  @click="handleBtnUseDetail(row)"
                  type="text"
                  size="small"
                  >使用明细</el-button
                >
                <el-button
                  v-if="row.confirmStatus === 2"
                  @click="handleBtnRepaymentDetail(row)"
                  type="text"
                  size="small"
                  >回款信息</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            hide-on-single-page
            @current-change="handleCurrentChange"
            :current-page.sync="paginationData.currentPage"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
          >
          </el-pagination>
        </div>
      </div>
    </LayoutCard>
    <DialogPreviewProof
      ref="DialogPreviewProofRef"
      :invoiceTypeMap="invoiceTypeMap"
      :proofTypeMap="proofTypeMap"
    />
    <DialogUseDetail ref="DialogUseDetailRef" />
    <DialogReplaymentDetail ref="DialogReplaymentDetailRef" />
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import Tag from '../Tag/index.vue'
import DialogPreviewProof from './components/DialogPreviewProof/index.vue'
import DialogUseDetail from './components/DialogUseDetail/index.vue'
import DialogReplaymentDetail from './components/DialogRepaymentDetail/index.vue'
import { requestClientAssetInformationList } from '@/api/customer/archives/archive'
import { proofStatusMap } from '@/views/customer/tradeDetail/config'
import { getDictionary } from '@/api/system/dictbiz'
import { requestProcessInfo } from '@/api/customer/archives/archive'
import { routerMapKeyToPath } from '@/views/business/config'

const initPaginationData = () => ({
  pageSize: 10,
  currentPage: 1,
  maxPage: undefined,
  total: 0,
})

const frontUserMap = {
  enterprise: 1,
  coreEnterprise: 2,
  person: 3,
}

export default {
  name: 'CustomerModuleDocumentInfoIndex',
  components: {
    LayoutCard,
    Tag,
    DialogPreviewProof,
    DialogUseDetail,
    DialogReplaymentDetail,
  },
  props: {
    companyId: {
      type: [String, undefined],
      default: undefined,
    },
  },
  inject: ['type'],
  data() {
    return {
      loading: true,
      alreadyLoad: false,
      tableLoadingBtnMap: {
        downId: undefined,
        preViewId: undefined,
        invalidId: undefined,
        delId: undefined,
      },
      paginationData: initPaginationData(),
      tabList: ['应收账款', '应收票据', '仓单', '存款', '房产'],
      activeTab: '应收账款',
      tableData: [],
      // 字典
      proofStatusMap,
      invoiceTypeMap: {},
      proofTypeMap: {},
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
    activeTab() {
      this.handleTabChange()
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
    getDictionary({ code: 'customer_invoice_type' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          const invoiceTypeMap = {}
          for (const item of data) {
            invoiceTypeMap[item.dictKey] = item.dictValue
          }
          invoiceTypeMap.ready = true
          this.invoiceTypeMap = invoiceTypeMap
        }
      })
      .catch(() => {})
    getDictionary({ code: 'jrzh_customer_front_sales_contract_proof_type' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          const proofTypeMap = {}
          for (const item of data) {
            proofTypeMap[item.dictKey] = item.dictValue
          }
          proofTypeMap.ready = true
          this.proofTypeMap = proofTypeMap
        }
      })
      .catch(() => {})
  },
  methods: {
    handleTabChange() {
      this.paginationData = initPaginationData()
      this.initData()
    },
    initData() {
      this.loading = true
      this.tableData = []
      this.requestData()
    },
    requestData() {
      const { currentPage, pageSize } = this.paginationData
      const request = {
        companyId: this.companyId,
        frontUser: frontUserMap[this.type],
        typeStatus: this.tabList.indexOf(this.activeTab) + 1,
        current: currentPage,
        size: pageSize,
      }
      requestClientAssetInformationList(request)
        .then(({ data }) => {
          this.loading = false
          if (data.success) {
            data = data.data
            const records = data.records || []
            this.tableData = records
            this.paginationData = {
              ...this.paginationData,
              currentPage: currentPage,
              maxPage: data.pages,
              total: data.total,
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleCurrentChange(currentPage) {
      this.loading = true
      this.paginationData.currentPage = currentPage
      this.requestData()
    },
    // 查看凭证
    handleBtnViewProof(row) {
      this.$refs.DialogPreviewProofRef.handleOpen(row)
    },
    // 审批详情
    handleBtnApproveDetail(row) {
      requestProcessInfo({ processInsId: row.processInstanceId })
        .then(({ data }) => {
          const { taskId, processInsId, processDefinitionKey } = data.data
          const target = routerMapKeyToPath[processDefinitionKey]
          if (!target) return
          this.$router.push(
            `${target}/detail/${Buffer.from(
              JSON.stringify({
                taskId,
                processInsId,
              })
            ).toString('base64')}`
          )
        })
        .catch(() => {})
    },
    // 使用明细
    handleBtnUseDetail(row) {
      this.$refs.DialogUseDetailRef.handleOpen(row.id)
    },
    // 回款信息
    handleBtnRepaymentDetail(row) {
      this.$refs.DialogReplaymentDetailRef.handleOpen(row.id)
    },
  },
}
</script>

<style lang="scss" scoped>
.table-container {
  .table-container {
    .link {
      line-height: 20px;
      font-size: 14px;
      text-align: left;
      font-family: Roboto;
      color: #697cff;
    }
  }

  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 18px;
  }
}
</style>
