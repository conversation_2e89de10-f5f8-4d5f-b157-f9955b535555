<template>
  <div
    class="customer-tag"
    :class="{ radius }"
    :style="{
      border: borderColor
        ? `1px solid ${borderColor}`
        : `1px solid transparent`,
      background: backgroundColor ? `${backgroundColor}` : `transparent`,
      minWidth: minWidth ? minWidth : '',
    }"
  >
    <span class="tag-name" :style="{ color }">{{ name }}</span>
  </div>
</template>

<script>
export default {
  name: 'CustomerTagIndex',
  props: {
    borderColor: {
      type: String,
    },
    backgroundColor: {
      type: String,
    },
    color: {
      type: String,
    },
    radius: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
      default: '',
    },
    minWidth: {
      type: String,
      default: undefined,
    },
  },
}
</script>

<style lang="scss" scoped>
.customer-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;

  &.radius {
    border-radius: 100px;
  }

  .tag-name {
    line-height: 1;
    white-space:nowrap;
  }
}
</style>
