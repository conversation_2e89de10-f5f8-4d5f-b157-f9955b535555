<template>
  <div v-loading="loading" class="change-list-item">
    <div v-if="changeData.bussinessShow" class="item business-container">
      <span class="title">工商信息</span>
      <div>
        <Business :data="changeData.business" :isRealName="false" />
      </div>
    </div>
    <div v-if="changeData.personnelShow" class="item personnel-container">
      <span class="title">人员信息</span>
      <div>
        <Personnel
          :data="changeData.personnel"
          :isRealName="false"
          :customShowPersonFirst="changeData.customShowPersonFirst"
          :customShowPersonSecond="changeData.customShowPersonSecond"
          :customShowEntFirst="changeData.customShowEntFirst"
          :customShowEntSecond="changeData.customShowEntSecond"
        />
      </div>
    </div>
    <div
      v-if="changeData.equity && changeData.equity.length > 0"
      class="item equity-container"
    >
      <span class="title">股权结构</span>
      <div>
        <Equity :data="changeData.equity" />
      </div>
    </div>
    <div v-if="changeData.valueAnalysisShow" class="item equity-container">
      <span class="title">价值分析</span>
      <div class="card-list">
        <SimpleInfoCard
          v-for="item of changeData.valueAnalysis"
          :key="item.key"
          :value="item.value"
          :label="item.label"
          :desc="item.desc"
          :color="item.color"
        />
        <!-- 以下占位，解决flex布局最后一行拉伸问题 -->
        <SimpleInfoCard
          v-for="item of [1, 2, 3, 4, 5, 6, 7, 8, 9]"
          :key="item"
          empty
        />
      </div>
    </div>
    <div v-if="changeData.creditInfoShow" class="item equity-container">
      <span class="title">征信信息</span>
      <div class="card-list">
        <SimpleInfoCard
          v-for="item of changeData.creditInfo"
          :key="item.key"
          :value="item.value"
          :label="item.label"
          :desc="item.desc"
          :color="item.color"
        />
        <!-- 以下占位，解决flex布局最后一行拉伸问题 -->
        <SimpleInfoCard
          v-for="item of [1, 2, 3, 4, 5, 6, 7, 8, 9]"
          :key="item"
          empty
        />
      </div>
    </div>
  </div>
</template>

<script>
import { requestClientHistoricalChangeDetail } from '@/api/customer/archives/client'
import Business from '../../../ModuleBaseData/components/Business/index.vue'
import Personnel from '../../../ModuleBaseData/components/Personnel/index.vue'
import Equity from '../../../ModuleBaseData/components/Equity/index.vue'
import SimpleInfoCard from '../../../SimpleInfoCard/index.vue'
import cloneDeep from 'lodash/cloneDeep'

const entFirstMap = {
  corporationName: undefined,
  corporationIdType: undefined,
  corporationIdCardNumber: undefined,
  corporationSex: undefined,
  corporationValidTime: undefined,
  corporationNation: undefined,
  corporationCountry: undefined,
  corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx: undefined,
  corporationAddress: undefined,
}

const entSecondMap = {
  operatorXXXXXXXXXXXXXX: undefined,
  operatorIdcard: undefined,
  operatorPhone: undefined,
  operatorSex: undefined,
  operatorValidTime: undefined,
  operatorNation: undefined,
  operatorCountry: undefined,
  operatorAddress: undefined,
}

const valueAnalysisMap = [
  {
    key: 'totalTaxLastYear',
    desc: '去年纳税销售总额',
    label: '万元',
    color: '#697CFF',
    value: undefined,
    needFormat: true,
  },
  {
    key: 'totalTaxPreviousYear',
    desc: '前年纳税销售总额',
    label: '万元',
    color: '#697CFF',
    value: undefined,
    needFormat: true,
  },
  {
    key: 'netProfit',
    desc: '净利润',
    label: '万元',
    color: '#697CFF',
    value: undefined,
    needFormat: true,
  },
  {
    key: 'totalTaxPayableLastYear',
    desc: '去年应纳税总额',
    label: '万元',
    color: '#697CFF',
    value: undefined,
    needFormat: true,
  },
  {
    key: 'totalTaxPayablePreviousYear',
    desc: '前年应纳税总额',
    label: '万元',
    color: '#697CFF',
    value: undefined,
    needFormat: true,
  },
  {
    key: 'yearGrowth',
    desc: '同比销售增长率',
    label: '%',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'annulusGrowth',
    desc: '环比销售增长率',
    label: '%',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'totalAccountsReceivable',
    desc: '应收账款总额',
    label: '万元',
    color: '#697CFF',
    value: undefined,
    needFormat: true,
  },
  {
    key: 'totalStill',
    desc: '应还账款总额',
    label: '万元',
    color: '#697CFF',
    value: undefined,
    needFormat: true,
  },
  {
    key: 'collectionRate',
    desc: '回款率',
    label: '%',
    color: '#697CFF',
    value: undefined,
  },
]

const creditInfoMap = [
  {
    key: 'currentConcerns',
    desc: '当前关注',
    label: '笔',
    color: '#826F00',
    value: undefined,
  },
  {
    key: 'currentTimes',
    desc: '当前次级',
    label: '笔',
    color: '#826F00',
    value: undefined,
  },
  {
    key: 'currentSuspicious',
    desc: '当前可疑',
    label: '笔',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'currentLoss',
    desc: '当前损失',
    label: '笔',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'currentOverdue',
    desc: '当前逾期',
    label: '笔',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'overdueMone',
    desc: '近24个月逾期为M1',
    label: '笔',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'overdueMtwo',
    desc: '近24个月逾期为M2',
    label: '笔',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'overdueMthree',
    desc: '近24个月逾期为M3',
    label: '笔',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'approveLetterQueryTwo',
    desc: '近2个月审批、征信查询',
    label: '笔',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'approveLetterQueryTetracosa',
    desc: '近12个月审批、征信查询',
    label: '笔',
    color: '#826F00',
    value: undefined,
  },
  {
    key: 'closedCases',
    desc: '企业涉诉结案件',
    label: '次',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'openCases',
    desc: '企业涉诉未结案件',
    label: '次',
    color: '#697CFF',
    value: undefined,
  },
  {
    key: 'overdueRate',
    desc: '逾期率',
    label: '%',
    color: '#826F00',
    value: undefined,
  },
]

export default {
  name: 'CustomerModuleHistoricalChangeItemIndex',
  components: {
    Business,
    Personnel,
    Equity,
    SimpleInfoCard,
  },
  props: {
    isShow: {
      type: Boolean,
      required: true,
    },
    // cacheData: {
    //   type: Array,
    // },
    id: {
      type: String,
    },
  },
  data() {
    return {
      loading: true,
      isLoaded: false,
      changeData: {},
      valueAnalysisMap: valueAnalysisMap,
    }
  },
  inject: ['type'],
  watch: {
    isShow(value) {
      if (value && !this.isLoaded) {
        this.initData()
      }
    },
  },
  // created() {
  //   if (this.cacheData) {
  //     this.changeData = this.cacheData
  //     this.loading = false
  //     console.log(this.cacheData)
  //   } else {
  //     this.initData()
  //   }
  // },
  methods: {
    initData() {
      this.loading = true
      requestClientHistoricalChangeDetail({
        id: this.id,
      })
        .then(({ data }) => {
          this.loading = false
          if (data.success) {
            this.isLoaded = true
            data = data.data
            const formatData = {}
            // 处理工商信息
            if (data.customerBusinessInfoSub) {
              const business = {
                legalPersonName: undefined,
                phoneNumber: undefined,
                estiblishTime: undefined,
                creditCode: undefined,
                orgNumber: undefined,
                approvedTime: undefined,
                regNumber: undefined,
                totalAssets: undefined,
                regDate: undefined,
                regCapital: undefined,
                paidAmount: undefined,
                regInstitute: undefined,
                companyOrgType: undefined,
                industry: undefined,
                employeeNum: undefined,
                medicalInsurance: undefined,
                regLocation: undefined,
                businessScope: undefined,
                ...data.customerBusinessInfoSub,
              }
              let showFlag = false
              for (const key in business) {
                if (business[key] === null) business[key] = undefined
                if (business[key] !== undefined) {
                  showFlag = true
                }
              }
              formatData.business = business
              if (showFlag) formatData.bussinessShow = true
            }
            // 处理人员信息
            if (data.customerInfoSub) {
              const personnel = {
                ...entFirstMap,
                ...entSecondMap,
                ...data.customerInfoSub,
              }
              let showFlag = false
              let customShowPersonFirst = false
              let customShowPersonSecond = false
              let customShowEntFirst = false
              let customShowEntSecond = false
              for (const key in personnel) {
                if (personnel[key] === null) personnel[key] = undefined
                if (personnel[key] !== undefined) {
                  showFlag = true
                  if (this.type === 'person') {
                    // 个人
                  } else {
                    // 融资企业、核心企业
                    if (entFirstMap.hasOwnProperty(key)) {
                      customShowEntFirst = true
                    } else if (entSecondMap.hasOwnProperty(key)) {
                      customShowEntSecond = true
                    }
                  }
                }
              }
              formatData.personnel = personnel
              if (showFlag) formatData.personnelShow = true
              formatData.customShowPersonFirst = customShowPersonFirst
              formatData.customShowPersonSecond = customShowPersonSecond
              formatData.customShowEntFirst = customShowEntFirst
              formatData.customShowEntSecond = customShowEntSecond
            }
            // 处理股权结构
            if (data.supplierOwnerShipStructList) {
              const Equity = data.supplierOwnerShipStructList
              formatData.equity = Equity
            } else {
              formatData.equity = []
            }
            // 处理价值分析
            if (data.customerFrontValueSub) {
              const valueAnalysisData = data.customerFrontValueSub
              const formatValueAnalysisData = []
              let showFlag = false
              for (const item of valueAnalysisMap) {
                const currentValue = valueAnalysisData[item.key]
                if (currentValue !== undefined || null) {
                  showFlag = true
                  item.value = currentValue
                  if (item.needFormat) {
                    item.value = Number(item.value).toLocaleString()
                  }
                  formatValueAnalysisData.push(item)
                }
              }
              formatData.valueAnalysis = formatValueAnalysisData
              if (showFlag) formatData.valueAnalysisShow = true
            }
            // 处理征信信息
            if (data.customerFrontCreditInformationSub) {
              const creditInfoData = data.customerFrontCreditInformationSub
              const formatCreditInfoData = []
              let showFlag = false
              for (const item of creditInfoMap) {
                const currentValue = creditInfoData[item.key]
                if (currentValue !== undefined || null) {
                  showFlag = true
                  item.value = currentValue
                  formatCreditInfoData.push(item)
                }
              }
              formatData.creditInfo = formatCreditInfoData
              if (showFlag) formatData.creditInfoShow = true
            }
            // 保存到当前组件使用
            this.changeData = cloneDeep(formatData)
            // 使用父级缓存
            // this.$parent.$parent.$parent.cacheData(this.id, formatData)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.change-list-item {
  .item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      display: inline-block;
      margin-bottom: 6px;
      font-size: 16px;
      text-align: left;
      font-weight: bold;
      color: rgba(16, 16, 16, 100);
    }

    .card-list {
      display: flex;
      flex-wrap: wrap;
      margin: -10px -12px;

      > * {
        flex: 1;
        margin: 10px 12px;
      }
    }
  }
}
</style>
