<template>
  <div class="history-container" v-loading="initLoading">
    <template v-if="changeList.length === 0">
      <div style="background-color: #fff; border-radius: 8px">
        <el-empty description="暂无变更记录" />
      </div>
    </template>
    <template v-else>
      <el-collapse v-model="activeIds" @change="handleChange">
        <el-collapse-item
          v-for="item of changeList"
          :key="item.id"
          :name="item.id"
        >
          <template slot="title">
            <div class="title-container">
              <span class="icon-arrow" /><span class="title">{{
                item.changeTime + '之前数据'
              }}</span>
            </div>
          </template>
          <div class="content-wrapper">
            <ChangeItem
              :isShow="activeIdMap[item.id]"
              :id="item.id"
            />
            <!-- <ChangeItem
            v-if="activeIdMap[item.id]"
            :cacheData="cacheDataMap[item.id]"
            :id="item.id"
          /> -->
          </div>
        </el-collapse-item>
      </el-collapse>
      <button
        class="loading-more"
        :class="{
          'no-more': paginationData.maxPage < paginationData.nextPage,
        }"
        @click="loadMore"
        v-loading="loadingMore"
        v-if="paginationData.maxPage !== undefined"
      >
        <template v-if="paginationData.maxPage < paginationData.nextPage">
          <span>没有更多了</span>
        </template>
        <template v-else>
          <span>查看更多历史数据</span>
        </template>
      </button>
    </template>
  </div>
</template>

<script>
import ChangeItem from './components/Item/index.vue'
import { requestClientHistoricalChange } from '@/api/customer/archives/client'

export default {
  name: 'CustomerModuleHistoricalChangeIndex',
  components: { ChangeItem },
  props: {
    companyId: {
      type: [String, undefined],
      default: undefined,
    },
  },
  data() {
    return {
      initLoading: true,
      loadingMore: false,
      activeIds: [],
      activeIdMap: {},
      changeList: [],
      cacheDataMap: {},
      paginationData: {
        pageSize: 5,
        currentPage: 1,
        nextPage: 2,
        maxPage: undefined,
        total: 0,
      },
    }
  },
  inject: ['type', 'id'],
  created() {
    this.requestData()
  },
  methods: {
    handleChange(value) {
      const activeIdMap = {}
      for (const item of value) {
        activeIdMap[item] = true
      }
      this.activeIdMap = activeIdMap
    },
    requestData() {
      const { currentPage, pageSize } = this.paginationData
      requestClientHistoricalChange({
        companyId: this.companyId,
        current: currentPage,
        size: pageSize,
      })
        .then(({ data }) => {
          this.initLoading = false
          this.loadingMore = false
          if (data.success) {
            data = data.data
            const records = data.records || []
            this.changeList = this.changeList.concat(records)
            this.paginationData = {
              ...this.paginationData,
              currentPage: currentPage,
              nextPage: currentPage + 1,
              maxPage: data.pages,
              total: data.total,
            }
          }
        })
        .catch(() => {
          this.initLoading = false
          this.loadingMore = false
        })
    },
    loadMore() {
      if (this.paginationData.maxPage < this.paginationData.nextPage) return
      this.loadingMore = true
      this.paginationData.currentPage = this.paginationData.nextPage
      this.requestData()
    },
    // cacheData(targetId, data) {
    //   this.cacheDataMap[targetId] = data
    // },
  },
}
</script>

<style lang="scss" scoped>
.history-container {
  ::v-deep {
    .el-collapse {
      .el-collapse-item {
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
      }

      .el-collapse-item__header {
        height: 62px;
        line-height: 1;
        padding: 0 18px;
        border: none;

        &.is-active {
          .title-container {
            .icon-arrow {
              transform: rotate(90deg);
            }
          }
        }

        .title-container {
          display: flex;
          align-items: center;

          .icon-arrow {
            width: 0;
            height: 0;
            margin-right: 12px;
            border-style: solid;
            border-width: 5px 0 5px 8px;
            border-color: transparent transparent transparent #007bff;
            transition: transform 0.3s;
          }

          .title {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
      .el-collapse-item__arrow {
        display: none;
      }
      .el-collapse-item__wrap {
        border-top: 1px solid #ebeef5;
        border-bottom: none;

        // .el-collapse-item__content {
        // }
      }
    }
  }

  .content-wrapper {
    padding: 24px 24px 0;
  }

  .loading-more {
    width: 100%;
    height: 62px;
    background-color: #fff;
    border: none;
    border-radius: 8px;
    overflow: hidden;
    line-height: 62px;
    text-align: center;
    cursor: pointer;

    &.no-more {
      background-color: #f9f9f9;
      cursor: not-allowed;

      span {
        color: #666;
      }
    }

    span {
      height: 24px;
      color: rgba(105, 124, 255, 100);
      font-size: 16px;
      text-align: center;
      font-weight: 500;
    }
  }
}
</style>
