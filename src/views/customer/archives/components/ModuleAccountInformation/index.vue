<template>
    <div class="enterprise-person-container">
      <LayoutCard title="账户信息">
        <div v-loading="loading" class="table-container">
          <el-table
            class="negative-factor-table"
            :data="tableData"
            style="width: 100%"
            :header-cell-style="
              () => {
                return { backgroundColor: '#f7f7f7', color: '#000' }
              }
            "
          >
            <el-table-column type="index" label="#" width="48" align="center" />
            <el-table-column prop="bankDeposit" label="开户银行" />
            <el-table-column prop="bankName" label="账户名称" />
            <el-table-column prop="bankCardNo" label="银行卡号" />
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            hide-on-single-page
            @current-change="handleCurrentChange"
            :current-page.sync="paginationData.currentPage"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
          >
          </el-pagination>
        </div>
      </LayoutCard>
    </div>
  </template>
  
  <script>
  import LayoutCard from '../LayoutCard/index.vue'
  import Tag from '../Tag/index.vue'
  import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
  import Card from '../ModuleTrade/components/Card/index.vue'
  import { getAccountInformation } from '@/api/customer/archives/account'

  const initPaginationData = () => ({
    pageSize: 10,
    currentPage: 1,
    nextPage: 2,
    maxPage: undefined,
    total: 0,
  })
  
  export default {
    name: 'CustomerModuleEnterprisePersonIndex',
    components: { LayoutCard, Tag, Dialog, Card },
    props: {
      companyId: {
        type: [String, undefined],
        default: undefined,
      },
    },
    inject: ['type'],
    data() {
      return {
        loading: true,
        tradeDialogLoading: true,
        alreadyLoad: false,
        paginationData: initPaginationData(),
        tableData: [],
        currentTradeList: [],
        currentTradeData: {},
      }
    },
    watch: {
      companyId(newVal) {
        if (
          !this.alreadyLoad &&
          Object.prototype.toString.call(newVal) === '[object String]'
        ) {
          this.initData()
        }
      },
    },
    created() {
      this.loading = true
      if (Object.prototype.toString.call(this.companyId) === '[object String]') {
        this.alreadyLoad = true
        this.initData()
      }
    },
    methods: {
      initData() {
        this.loading = true
        this.tableData = []
        this.requestData()
      },
      requestData() {
        const { currentPage, pageSize } = this.paginationData
        const request = {
           userId: this.companyId,
          current: currentPage,
          size: pageSize,
        }
        getAccountInformation(request)
          .then(({ data }) => {
            this.loading = false
            if (data.success) {
              data = data.data || {}
              this.tableData = data || []
              this.paginationData = {
                ...this.paginationData,
                currentPage: currentPage,
                nextPage: currentPage + 1,
                maxPage: data.pages,
                total: data.total,
              }
            }
          })
          .catch(() => {
            this.loading = false
          })
      },
      handleCurrentChange(currentPage) {
        this.loading = true
        this.paginationData.currentPage = currentPage
        this.requestData()
      }
    },
  }
  </script>
  
  <style lang="scss" scoped>
  .head-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  
    .label-bar-container {
      .el-radio-group {
        .el-radio-button:first-child {
          border-radius: 100px 0 0 100px !important;
  
          ::v-deep {
            .el-radio-button__inner {
              border-radius: 100px 0 0 100px !important;
            }
          }
        }
  
        .el-radio-button:last-child {
          border-radius: 0 100px 100px 0 !important;
  
          ::v-deep {
            .el-radio-button__inner {
              border-radius: 0 100px 100px 0 !important;
            }
          }
        }
  
        .el-radio-button {
          height: 38px;
  
          ::v-deep {
            .el-radio-button__orig-radio:checked + .el-radio-button__inner {
              background-color: #0f100f;
              border-color: #0f100f;
            }
          }
        }
      }
    }
  
    .el-button {
      height: 38px;
      padding: 0 20px;
      border-radius: 32px;
    }
  }
  
  .table-container {
    .el-pagination {
      display: flex;
      justify-content: center;
      margin-top: 18px;
    }
  }
  
  .trade-list-container {
    padding: 24px 24px 0;
  }
  </style>
  