<template>
  <div class="doc-info-container">
    <LayoutCard title="开通产品">
      <div v-loading="loading" class="table-container">
        <el-table
          class="negative-factor-table"
          :data="tableData"
          border
          style="width: 100%"
          :header-cell-style="
            () => {
              return { backgroundColor: '#f7f7f7', color: '#000' }
            }
          "
        >
          <el-table-column prop="openNo" label="开通编号" min-width="142"></el-table-column>
          <el-table-column prop="goodsName" label="产品名称" />
          <el-table-column prop="goodsType" label="业务类型" min-width="130">
            <template slot-scope="{ row }">
              <Tag
                v-if="goodsTypeStatusMap[row.goodsType]"
                :name="goodsTypeStatusMap[row.goodsType].name"
                :color="goodsTypeStatusMap[row.goodsType].color"
                :backgroundColor="
                  goodsTypeStatusMap[row.goodsType].backgroundColor
                "
                :borderColor="goodsTypeStatusMap[row.goodsType].borderColor"
                minWidth="64px"
              />
              <span v-else>{{ row.goodsType }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="capitalName" label="资金方">
            <template slot-scope="{ row }">
              <el-button @click="handleCapital(row)" type="text" size="small">
                {{
                row.capitalName
                }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="availableCredit" label="当前可用额度" min-width="120">
            <template slot-scope="{ row }">
              <span>
                {{
                row.availableCredit !== undefined &&
                row.availableCredit !== null
                ? row.availableCredit.toLocaleString('zh', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 2,
                }) + '元'
                : '--'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="annualInterestRate" label="年利率">
            <template slot-scope="{ row }">
              <span>
                {{
                row.annualInterestRate !== undefined &&
                row.annualInterestRate !== null
                ? row.annualInterestRate + '%'
                : '--'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="expireTime" label="到期日">
            <template slot-scope="{ row }">
              <span>
                {{
                row.expireTime !== null && row.expireTime !== undefined
                ? row.expireTime
                : '--'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="开通状态" min-width="130">
            <template slot-scope="{ row }">
              <Tag
                :name="openStatusMap[row.status].name"
                :color="openStatusMap[row.status].color"
                :backgroundColor="openStatusMap[row.status].backgroundColor"
                :borderColor="openStatusMap[row.status].borderColor"
                minWidth="64px"
              />
            </template>
          </el-table-column>
          <el-table-column prop="options" label="操作" fixed="right" width="200">
            <template slot-scope="{ row }">
              <el-button
                v-if="[2, 3].includes(row.status) && row.ratingRecordId !== null"
                @click="handleScoreRecord(row)"
                type="text"
                size="small"
              >评分记录</el-button>
              <el-button
                v-if="[0].includes(row.status)"
                @click="handleBtnAmountDetail(row)"
                type="text"
                size="small"
              >额度详情</el-button>
              <el-button
                v-if="[1, 2, 3, 4, 7].includes(row.status)"
                @click="handleBtnTrade(row)"
                type="text"
                size="small"
              >贸易空间</el-button>
              <el-button
                v-if="[0].includes(row.status)"
                @click="handleBtnApproveDetail(row)"
                type="text"
                size="small"
              >审批详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          hide-on-single-page
          @current-change="handleCurrentChange"
          :current-page.sync="paginationData.currentPage"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
        ></el-pagination>
      </div>
    </LayoutCard>
    <Dialog title="选择贸易空间" ref="tradeDialog" width="800px" center noButton>
      <div v-loading="tradeDialogLoading" class="trade-list-container">
        <template v-if="currentTradeList && currentTradeList.length > 0">
          <Card
            v-for="item of currentTradeList"
            :key="item.id"
            :id="item.tradeBackgroundId"
            :title="item.enterpriseName"
            :initDate="item.firstTradeTime"
            :bindDate="item.relationTime"
            :status="item.status"
            :processInsId="item.processInstanceId"
            enableTradeSpace
            :tradeSpaceParams="currentTradeData"
            two
            hideStatus
            @anyBtnClick="handleCardBtnClick"
          />
        </template>
        <template v-else>
          <el-empty description="暂无数据" />
        </template>
      </div>
    </Dialog>
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import Tag from '../Tag/index.vue'
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import Card from '../ModuleTrade/components/Card/index.vue'
import {
  requestOpenProductList,
  requestOpenProductTradeList,
  requestCapitalByCompanyId,
} from '@/api/customer/archives/archive'
import { goodsTypeStatusMap, openStatusMap } from './config/index'
import { dateFormat } from '@/util/date'
// import { getDictionary } from '@/api/system/dictbiz'

const initPaginationData = () => ({
  pageSize: 10,
  currentPage: 1,
  nextPage: 2,
  maxPage: undefined,
  total: 0,
})

export default {
  name: 'CustomerModuleDocumentInfoIndex',
  components: { LayoutCard, Tag, Dialog, Card },
  props: {
    companyId: {
      type: String,
      default: undefined,
    },
    unifiedSocialCode: {
      type: String,
      default: undefined,
    },
  },
  inject: ['type', 'companyIdFunc', 'id'],
  data() {
    return {
      loading: true,
      tradeDialogLoading: true,
      alreadyLoad: false,
      paginationData: initPaginationData(),
      tableData: [],
      goodsTypeStatusMap,
      openStatusMap,
      currentTradeList: [],
      currentTradeData: {},
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    initData() {
      this.loading = true
      this.tableData = []
      this.requestData()
      // this.requestDic()
    },
    requestData() {
      const { currentPage, pageSize } = this.paginationData
      const request = {
        enterpriseId: this.companyIdFunc(),
        enterpriseType: this.type === 'coreEnterprise' ? 2 : 1,
        current: currentPage,
        size: pageSize,
      }
      requestOpenProductList(request)
        .then(({ data }) => {
          this.loading = false
          if (data.success) {
            data = data.data || []
            console.log(data)
            for (const item of data) {
              if (item.expireTime !== null && item.expireTime !== undefined) {
                item.expireTime = dateFormat(
                  new Date(item.expireTime.replace(/-/g, '/')),
                  'yyyy.MM.dd'
                )
              }
            }
            this.tableData = data
            this.paginationData = {
              ...this.paginationData,
              currentPage: currentPage,
              nextPage: currentPage + 1,
              maxPage: data.pages,
              total: data.total,
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleCurrentChange(currentPage) {
      this.loading = true
      this.paginationData.currentPage = currentPage
      this.requestData()
    },
    // requestDic() {
    //   // getDictionary({ code: 'goods_type' })
    //   getDictionary({ code: 'customer_goods_status' })
    //     .then(({ data }) => {
    //       debugger
    //     })
    //     .catch(() => {})
    // },
    handleCapital(row) {
      requestCapitalByCompanyId({ companyId: row.capitalId })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.$router.push({
              path: `/customer/capitalDetail`,
              query: { id: data.id, companyId: data.companyId },
            })
          }
        })
        .catch(() => {
          row.capitalLoading = false
        })
    },
    handleScoreRecord(row) {
      this.$router.push(
        '/riskmana/ratingrecord/detail/' +
          Buffer.from(JSON.stringify(row.ratingRecordId)).toString('base64')
      )
    },
    handleBtnAmountDetail(row) {
      this.$router.push(
        '/riskmana/ratingrecord/detail/' +
          Buffer.from(JSON.stringify(row.id)).toString('base64')
      )
    },
    handleBtnTrade(row) {
      this.tradeDialogLoading = true
      this.currentTradeList = []
      requestOpenProductTradeList({ customerGoodsId: row.id })
        .then(({ data }) => {
          this.tradeDialogLoading = false
          if (data.success) {
            data = data.data
            this.currentTradeList = data
          }
        })
        .catch(() => {})
      this.currentTradeData = {
        location: row.goodsType == 1 ? 'lower' : 'height',
        entType: row.enterpriseType == 1 ? 'enterprise' : 'coreEnterprise',
      }
      this.$refs.tradeDialog.handleOpen()
    },
    handleBtnApproveDetail(row) {
      this.$router.push(
        '/riskmana/ratingrecord/detail/' +
          Buffer.from(JSON.stringify(row.id)).toString('base64')
      )
    },
    handleCardBtnClick() {
      this.$refs.tradeDialog.handleClose()
    },
  },
}
</script>

<style lang="scss" scoped>
.head-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .label-bar-container {
    .el-radio-group {
      .el-radio-button:first-child {
        border-radius: 100px 0 0 100px !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 100px 0 0 100px !important;
          }
        }
      }

      .el-radio-button:last-child {
        border-radius: 0 100px 100px 0 !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 0 100px 100px 0 !important;
          }
        }
      }

      .el-radio-button {
        height: 38px;

        ::v-deep {
          .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            background-color: #0f100f;
            border-color: #0f100f;
          }
        }
      }
    }
  }

  .el-button {
    height: 38px;
    padding: 0 20px;
    border-radius: 32px;
  }
}

.table-container {
  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 18px;
  }
}

.trade-list-container {
  padding: 24px 24px 0;
}
</style>
