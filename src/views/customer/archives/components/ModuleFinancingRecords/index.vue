<template>
  <div class="doc-info-container">
    <LayoutCard title="融资记录">
      <div v-loading="loading" class="table-container">
        <div class="label-bar-box">
          <LabelBar :labelList="labelList" @handleSwitch="handleSwitch" />
        </div>
        <el-table class="negative-factor-table" :data="tableData" border style="width: 100%" :header-cell-style="() => {
          return { backgroundColor: '#f7f7f7', color: '#000' }
        }
          ">
          <el-table-column label="#" align="center" width="44" type="index" />
          <el-table-column prop="financeNo" label="融资编号" min-width="142" />
          <el-table-column prop="goodsType" label="业务类型" min-width="130">
            <template slot-scope="{ row }">
              <Tag :name="goodsTypeStatusMap[row.goodsType].name" :color="goodsTypeStatusMap[row.goodsType].color"
                :backgroundColor="goodsTypeStatusMap[row.goodsType].backgroundColor
                  " :borderColor="goodsTypeStatusMap[row.goodsType].borderColor" minWidth="64px" />
            </template>
          </el-table-column>
          <el-table-column prop="goodsName" label="申请产品" />
          <el-table-column prop="amount" label="融资金额(元)" min-width="80">
            <template slot-scope="{ row }">
              <span>{{ row.amount | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建日期" min-width="100">
            <template slot-scope="{ row }">
              <span>{{
                row.createTime !== null && row.createTime !== undefined
                  ? row.createTime
                  : '--'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" align="center" label="融资状态" min-width="70">
            <template slot-scope="{ row }">
              <span v-if="row.goodsType == 1" :style="{ color: financingStatusMap[row.status].color }">{{
                financingStatusMap[row.status].name }}
              </span>
              <span v-else-if="row.goodsType == 2" :style="{ color: purchasingStatusMap[row.status].color }">{{
                purchasingStatusMap[row.status].name }}
              </span>
              <span v-else :style="{ color: cloudStatusMap[row.status].color }">{{ cloudStatusMap[row.status].name }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" hide-on-single-page @current-change="handleCurrentChange"
          :current-page.sync="paginationData.currentPage" :total="paginationData.total"
          :page-size="paginationData.pageSize">
        </el-pagination>
      </div>
    </LayoutCard>
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import Tag from '../Tag/index.vue'
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import Card from '../ModuleTrade/components/Card/index.vue'
import LabelBar from '../LabelBar/index.vue'
import {
  requestFinanceApplyList,
  cloudRequestFinanceApplyList,
} from '@/api/customer/archives/archive'
import {
  goodsTypeStatusMap,
  financingStatusMap,
  purchasingStatusMap,
  cloudStatusMap,
} from './config/index'
import { dateFormat } from '@/util/date'
// import { getDictionary } from '@/api/system/dictbiz'

const initPaginationData = () => ({
  pageSize: 7,
  currentPage: 1,
  nextPage: 2,
  maxPage: undefined,
  total: 0,
})

export default {
  name: 'CustomerModuleDocumentInfoIndex',
  components: { LayoutCard, Tag, Dialog, Card, LabelBar },
  props: {
    companyId: {
      type: String,
      default: undefined,
    },
  },
  // inject: ['type', 'companyIdFunc', 'id'],
  data() {
    return {
      loading: true,
      tradeDialogLoading: true,
      alreadyLoad: false,
      paginationData: initPaginationData(),
      tableData: [],
      labelList: ['应收账款', '代采融资', '云信', '动产质押', '订单融资'],
      activeType: 0,
      goodsTypeStatusMap,
      financingStatusMap,
      purchasingStatusMap,
      cloudStatusMap,
      currentTradeList: [],
      currentTradeData: {},
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    initData() {
      this.loading = true
      this.tableData = []
      this.requestData()
      // this.requestDic()
    },
    requestData(typeD) {
      const { currentPage, pageSize } = this.paginationData
      const request = {
        userId: this.companyId,
        current: currentPage,
        size: pageSize,
        type: typeD || 1,
      }
      let funC
      if (typeD === 3) {
        funC = cloudRequestFinanceApplyList(request)
      } else {
        funC = requestFinanceApplyList(request)
      }
      funC
        .then(({ data }) => {
          this.loading = false
          if (data.success) {
            data = data.data || {}
            const tableList = data.records || []
            for (const item of tableList) {
              if (typeD === 3) {
                item.financeNo = item.cloudCode
                item.goodsName = item.cloudProductName
                item.amount = item.financingMoney
              }
              if (item.expireTime !== null && item.expireTime !== undefined) {
                item.expireTime = dateFormat(
                  new Date(item.expireTime.replace(/-/g, '/')),
                  'yyyy.MM.dd'
                )
              }
            }
            this.tableData = tableList
            this.paginationData = {
              ...this.paginationData,
              currentPage: currentPage,
              nextPage: currentPage + 1,
              maxPage: data.pages,
              total: data.total,
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleCurrentChange(currentPage) {
      this.loading = true
      this.paginationData.currentPage = currentPage
      this.requestData(this.activeType)
    },
    handleSwitch(value) {
      this.loading = true
      this.paginationData.currentPage = 1

      // ? 2024-10-14 张灿壕 Bug # 4480
      const valueD = this.labelList.findIndex((item) => item === value) + 1
      // -?

      this.activeType = valueD
      this.requestData(valueD)
    },
  },
}
</script>

<style lang="scss" scoped>
.head-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .label-bar-container {
    .el-radio-group {
      .el-radio-button:first-child {
        border-radius: 100px 0 0 100px !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 100px 0 0 100px !important;
          }
        }
      }

      .el-radio-button:last-child {
        border-radius: 0 100px 100px 0 !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 0 100px 100px 0 !important;
          }
        }
      }

      .el-radio-button {
        height: 38px;

        ::v-deep {
          .el-radio-button__orig-radio:checked+.el-radio-button__inner {
            background-color: #0f100f;
            border-color: #0f100f;
          }
        }
      }
    }
  }

  .el-button {
    height: 38px;
    padding: 0 20px;
    border-radius: 32px;
  }
}

.table-container {
  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 18px;
  }
}

.trade-list-container {
  padding: 24px 24px 0;
}

.label-bar-box {
  margin-bottom: 12px;
  width: 35%;
}
</style>
