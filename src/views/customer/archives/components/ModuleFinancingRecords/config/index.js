export const goodsTypeStatusMap = {
  1: {
    name: '应收账款质押',
    color: '#1277FF',
    backgroundColor: '#fff',
    borderColor: '#1277FF',
  },
  2: {
    name: '代采融资',
    color: '#3DC861',
    backgroundColor: '#fff',
    borderColor: '#3DC861',
  },
  3: {
    name: '云信',
    color: '#697CFF',
    backgroundColor: '#fff',
    borderColor: '#697CFF',
  },
  // TODO 以下颜色是复制3的，待优化
  4: {
    name: '动产质押',
    color: '#3DC861',
    backgroundColor: '#fff',
    borderColor: '#3DC861',
  },
  5: {
    name: '订单融资',
    color: '#3DC861',
    backgroundColor: '#fff',
    borderColor: '#3DC861',
  },
}

// 应收状态
export const financingStatusMap = {
  null: { name: '', color: '' },
  undefined: { name: '', color: '' },
  0: { name: '待提交', color: '#0D55CF' },
  1: { name: '融资申请审核中', color: '#0D55CF' },
  2: { name: '融资申请已驳回', color: '#FF8F00' },
  3: { name: '融资申请拒绝', color: '#B5BBC6' },
  4: { name: '融资申请待放款', color: '#0D55CF' },
  5: { name: '放款申请审核中', color: '#0D55CF' },
  6: { name: '放款申请已驳回', color: '#FF8F00' },
  7: { name: '放款申请拒绝', color: '#FF8F00' },
  8: { name: '待结清', color: '#0D55CF' },
  9: { name: '逾期未结清', color: '#DD2727' },
  10: { name: '提前结清', color: '#0BB07B' },
  11: { name: '到期已结清', color: '#0BB07B' },
  12: { name: '逾期已结清', color: '#FF8F00' },
  13: { name: '已作废', color: '#B5BBC6' },
  14: { name: '坏账已核销', color: '#DD2727' },
  15: { name: '逾期协商审核中', color: '#0D55CF' },
  16: { name: '逾期协商已驳回', color: '#FF8F00' },
  17: { name: '展期申请审核中', color: '#0D55CF' },
  18: { name: '展期申请已驳回', color: '#FF8F00' },
  19: { name: '展期申请待确认', color: '#0D55CF' },
  20: { name: '展期确认审核中', color: '#0D55CF' },
  21: { name: '展期确认已驳回', color: '#FF8F00' },
}

// 代采状态
export const purchasingStatusMap = {
  null: { name: '', color: '' },
  undefined: { name: '', color: '' },
  1: { name: '代采申请审核中', color: '#0D55CF' },
  2: { name: '已作废', color: '#B5BBC6' },
  3: { name: '超时已作废', color: '#B5BBC6' },
  4: { name: '代采申请待确认', color: '#0D55CF' },
  5: { name: '代采确认审核中', color: '#0D55CF' },
  6: { name: '待入库', color: '#0D55CF' },
  7: { name: '待赎货', color: '#0D55CF' },
  8: { name: '提前已赎货', color: '#0BB07B' },
  9: { name: '到期已赎货', color: '#0BB07B' },
  10: { name: '逾期未赎货', color: '#DD2727' },
  11: { name: '逾期已赎货', color: '#FF8F00' },
  12: { name: '待提交', color: '#0D55CF' },
}

// 云信状态
export const cloudStatusMap = {
  null: { name: '', color: '' },
  undefined: { name: '', color: '' },
  1: { name: '融资申请审核中', color: '#0D55CF' },
  2: { name: '融资申请已驳回', color: '#FF8F00' },
  3: { name: '融资申请待放款', color: '#0D55CF' },
  4: { name: '已放款', color: '#0BB07B' },
  5: { name: '融资申请已作废', color: '#B5BBC6' },
  6: { name: '放款申请待审核', color: '#0D55CF' },
  7: { name: '放款申请已作废', color: '#B5BBC6' },
  8: { name: '放款申请已驳回', color: '#FF8F00' },
  9: { name: '待结清', color: '#0D55CF' },
  10: { name: '逾期未结清', color: '#DD2727' },
  11: { name: '到期已结清', color: '#0BB07B' },
  12: { name: '逾期已结清', color: '#FF8F00' },
}
