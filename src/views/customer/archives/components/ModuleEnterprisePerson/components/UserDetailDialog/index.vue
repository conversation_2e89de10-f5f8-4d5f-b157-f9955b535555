<template>
  <Dialog
    title="成员详情"
    ref="DialogRef"
    center
    noConfirmBtn
    cancelBtnText="关闭"
    noBorder
  >
    <div v-loading="loading" class="content">
      <avue-form :option="option" v-model="formData">
        <template slot-scope="scope" slot="avatar">
          <el-avatar :size="150" fit="contain" :src="scope.value" />
        </template>
        <template slot="roleName">
          <el-tag
            v-for="item of formData.roleName"
            :key="item"
            size="mini"
            type="info"
            >{{ item }}</el-tag
          >
        </template>
        <template slot="deptName">
          <el-tag
            v-for="item of formData.deptName"
            :key="item"
            size="mini"
            type="info"
            >{{ item }}</el-tag
          >
        </template>
        <template slot="postName">
          <el-tag
            v-for="item of formData.postName"
            :key="item"
            size="mini"
            type="info"
            >{{ item }}</el-tag
          >
        </template>
      </avue-form>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { requestCoreEnterprisepPersonDetail } from '@/api/customer/archives/core'

export default {
  name: 'CustomerArchivesModuleEnterprisePersonUserDetailDialogIndex',
  components: { Dialog },
  data() {
    return {
      loading: true,
      formData: {},
      option: {
        detail: true,
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: '基础信息',
            prop: 'baseInfo',
            icon: 'el-icon-user-solid',
            column: [
              {
                label: '头像',
                prop: 'avatar',
                formslot: true,
              },
              {
                label: '所属租户',
                prop: 'tenantId',
                type: 'tree',
                dicUrl: '/api/blade-system/tenant/select',
                props: {
                  label: 'tenantName',
                  value: 'tenantId',
                },
                span: 24,
              },
              {
                label: '登录账号',
                prop: 'account',
              },
              {
                label: '用户平台',
                type: 'select',
                dicUrl: '/api/blade-system/dict/dictionary?code=user_type',
                props: {
                  label: 'dictValue',
                  value: 'dictKey',
                },
                dataType: 'number',
                slot: true,
                prop: 'userType',
              },
            ],
          },
          {
            label: '详细信息',
            prop: 'detailInfo',
            icon: 'el-icon-s-order',
            column: [
              {
                label: '用户昵称',
                prop: 'name',
                hide: true,
              },
              {
                label: '用户姓名',
                prop: 'realName',
              },
              {
                label: '手机号码',
                prop: 'phone',
                overHidden: true,
              },
              {
                label: '电子邮箱',
                prop: 'email',
                hide: true,
                overHidden: true,
              },
              {
                label: '用户性别',
                prop: 'sex',
                type: 'select',
                dicData: [
                  {
                    label: '男',
                    value: 1,
                  },
                  {
                    label: '女',
                    value: 2,
                  },
                  {
                    label: '未知',
                    value: 3,
                  },
                ],
                hide: true,
              },
              {
                label: '用户生日',
                type: 'date',
                prop: 'birthday',
                format: 'yyyy-MM-dd hh:mm:ss',
                valueFormat: 'yyyy-MM-dd hh:mm:ss',
                hide: true,
              },
              {
                label: '账号状态',
                prop: 'statusName',
                hide: true,
                display: false,
              },
            ],
          },
          {
            label: '职责信息',
            prop: 'dutyInfo',
            icon: 'el-icon-s-custom',
            column: [
              {
                label: '用户编号',
                prop: 'code',
              },
              {
                label: '所属角色',
                prop: 'roleName',
                formslot: true,
              },
              {
                label: '所属部门',
                prop: 'deptName',
                formslot: true,
              },
              {
                label: '所属岗位',
                prop: 'postName',
                formslot: true,
              },
            ],
          },
        ],
      },
    }
  },
  methods: {
    handleOpen(id) {
      this.loading = true
      requestCoreEnterprisepPersonDetail({ id })
        .then(({ data }) => {
          data = data.data
          data.roleName = data.roleName ? data.roleName.split(',') : []
          data.deptName = data.deptName ? data.deptName.split(',') : []
          data.postName = data.postName ? data.postName.split(',') : []
          this.formData = data

          this.loading = false
        })
        .catch(() => {})
      this.$refs.DialogRef.handleOpen()
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
}
</style>
