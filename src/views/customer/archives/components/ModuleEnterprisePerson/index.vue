<template>
  <div class="enterprise-person-container">
    <LayoutCard title="成员列表">
      <div v-loading="loading" class="table-container">
        <el-table
          class="negative-factor-table"
          :data="tableData"
          style="width: 100%"
          :header-cell-style="
            () => {
              return { backgroundColor: '#f7f7f7', color: '#000' }
            }
          "
        >
          <el-table-column type="index" label="#" width="48" align="center" />
          <el-table-column prop="avatar" label="头像" width="86" align="center">
            <template slot-scope="{ row }">
              <div style="display: flex; align-items: center; height: 100%">
                <el-avatar :src="row.logoSrc" @error="errorHandler" />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="用户名">
            <template slot-scope="{ row }">
              <!-- {{ type === 'coreEnterprise' ? row.account : row.accountUser }} -->
              {{ row.accountUser }}
            </template>
          </el-table-column>
          <el-table-column prop="personalName" label="姓名" />
          <el-table-column prop="roleName" label="角色">
            <template slot-scope="{ row }">
              <Tag
                :name="row.roleName"
                color="#00072A"
                backgroundColor="#EAECF1"
                borderColor="transparent"
              />
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="绑定时间">
          </el-table-column>
          <el-table-column
            prop="options"
            label="操作"
            fixed="right"
            width="100"
          >
            <template slot-scope="{ row }">
              <el-button @click="handleBtnDetail(row)" type="text" size="small"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          hide-on-single-page
          @current-change="handleCurrentChange"
          :current-page.sync="paginationData.currentPage"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
        >
        </el-pagination>
      </div>
    </LayoutCard>
    <UserDetailDialog ref="userDetailDialogRef" />
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import Tag from '../Tag/index.vue'
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import Card from '../ModuleTrade/components/Card/index.vue'
import UserDetailDialog from './components/UserDetailDialog/index.vue'
import { requestClientEnterprisepPersonList } from '@/api/customer/archives/client'
// import { requestCoreEnterprisepPersonList } from '@/api/customer/archives/core'
import { goodsTypeStatusMap, openStatusMap } from './config/index'
// import { dateFormat } from '@/util/date'
// import { getDictionary } from '@/api/system/dictbiz'

const initPaginationData = () => ({
  pageSize: 10,
  currentPage: 1,
  nextPage: 2,
  maxPage: undefined,
  total: 0,
})

export default {
  name: 'CustomerModuleEnterprisePersonIndex',
  components: { LayoutCard, Tag, Dialog, Card, UserDetailDialog },
  props: {
    companyId: {
      type: [String, undefined],
      default: undefined,
    },
  },
  inject: ['type'],
  data() {
    return {
      loading: true,
      tradeDialogLoading: true,
      alreadyLoad: false,
      paginationData: initPaginationData(),
      tableData: [],
      goodsTypeStatusMap,
      openStatusMap,
      currentTradeList: [],
      currentTradeData: {},
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    initData() {
      this.loading = true
      this.tableData = []
      this.requestData()
      // this.requestDic()
    },
    requestData() {
      const { currentPage, pageSize } = this.paginationData
      const request = {
        companyId: this.companyId,
        current: currentPage,
        size: pageSize,
      }
      // if (this.type === 'enterprise') {
      requestClientEnterprisepPersonList(request)
        .then(({ data }) => {
          this.loading = false
          if (data.success) {
            data = data.data || {}
            // for (const item of data) {
            //   if (item.expireTime !== null && item.expireTime !== undefined) {
            //     item.expireTime = dateFormat(
            //       new Date(item.expireTime.replace(/-/g, '/')),
            //       'yyyy.MM.dd'
            //     )
            //   }
            // }
            this.tableData = data.records || []
            this.paginationData = {
              ...this.paginationData,
              currentPage: currentPage,
              nextPage: currentPage + 1,
              maxPage: data.pages,
              total: data.total,
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
      // 该核心企业接口后台已弃用，现使用融资企业的接口
      // } else {
      //   requestCoreEnterprisepPersonList(request)
      //     .then(({ data }) => {
      //       this.loading = false
      //       if (data.success) {
      //         data = data.data || {}
      //         // for (const item of data) {
      //         //   if (item.expireTime !== null && item.expireTime !== undefined) {
      //         //     item.expireTime = dateFormat(
      //         //       new Date(item.expireTime.replace(/-/g, '/')),
      //         //       'yyyy.MM.dd'
      //         //     )
      //         //   }
      //         // }
      //         this.tableData = data.records || []
      //         this.paginationData = {
      //           ...this.paginationData,
      //           currentPage: currentPage,
      //           nextPage: currentPage + 1,
      //           maxPage: data.pages,
      //           total: data.total,
      //         }
      //       }
      //     })
      //     .catch(() => {
      //       this.loading = false
      //     })
      // }
    },
    handleCurrentChange(currentPage) {
      this.loading = true
      this.paginationData.currentPage = currentPage
      this.requestData()
    },
    handleBtnDetail(row) {
      // 该核心企业接口后台已弃用，现使用融资企业的方法
      // if (this.type === 'enterprise') {
      this.$router.push('/customer/archives/person/' + row.frontId)
      // } else {
      //   this.$refs.userDetailDialogRef.handleOpen(row.id)
      // }
    },
  },
}
</script>

<style lang="scss" scoped>
.head-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .label-bar-container {
    .el-radio-group {
      .el-radio-button:first-child {
        border-radius: 100px 0 0 100px !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 100px 0 0 100px !important;
          }
        }
      }

      .el-radio-button:last-child {
        border-radius: 0 100px 100px 0 !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 0 100px 100px 0 !important;
          }
        }
      }

      .el-radio-button {
        height: 38px;

        ::v-deep {
          .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            background-color: #0f100f;
            border-color: #0f100f;
          }
        }
      }
    }
  }

  .el-button {
    height: 38px;
    padding: 0 20px;
    border-radius: 32px;
  }
}

.table-container {
  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 18px;
  }
}

.trade-list-container {
  padding: 24px 24px 0;
}
</style>
