<template>
  <div class="negative-factor-container">
    <LayoutCard title="常规负面因素分析">
      <el-table
        v-loading="loading"
        class="negative-factor-table"
        :data="tableData"
        border
        empty-text="该企业暂未查询到负面因素"
        :header-cell-style="
          () => {
            return { backgroundColor: '#f7f7f7', color: '#000' }
          }
        "
      >
        <el-table-column prop="type" label="负面因素分析类型" width="166" />
        <el-table-column prop="value" label="内容" />
      </el-table>
    </LayoutCard>
    <div class="button-container">
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        plain
        @click="handleRefreshBtn"
        >刷新数据</el-button
      >
    </div>
    <CommonDialog
      ref="refreshDialog"
      title="是否确定刷新数据？"
      class="archives-refresh-dialog"
      center
      noBorder
      :enableFullScreenBtn="false"
      width="40%"
      :cancelDisable="dialogLoading"
      :confirmLoading="dialogLoading"
      @cancel="handleRefreshCancel"
      @confirm="handleRefreshConfirm"
    >
      <div class="archives-refresh-wrapper">
        <span>每次【刷新数据】都会使用天眼查-工商信息接口，1元/次</span>
      </div>
    </CommonDialog>
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import CommonDialog from '../CommonDialog/index.vue'
import {
  requestBaseDataCompanyNegative,
  requestRefreshBaseDataCompanyNegative,
} from '@/api/customer/archives/archive'

export default {
  name: 'CustomerModuleTradeIndex',
  components: { LayoutCard, CommonDialog },
  props: {
    unifiedSocialCode: {
      type: String,
      default: undefined,
    },
    companyName: {
      type: String,
      default: undefined,
    },
    companyId: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      loading: false,
      alreadyLoad: false,
      tableData: [],
      // 确认刷新弹窗加载
      dialogLoading: false,
    }
  },
  inject: ['type', 'id'],
  watch: {
    companyName(newVal) {
      if (
        !this.alreadyLoad &&
        this.companyId &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (
      Object.prototype.toString.call(this.companyName) === '[object String]' &&
      this.companyId
    ) {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    formatTableData(data = {}) {
      const tableData = []
      // 处理-处罚信息
      const punishList = data.punishList || []
      for (const item of punishList) {
        tableData.push({
          type: '处罚信息',
          value: item.punishContent,
        })
      }
      // 处理-登记信息
      const mortList = data.mortList || []
      for (const item of mortList) {
        tableData.push({
          type: '登记信息',
          value: item.mortContent,
        })
      }
      // 处理-违法案件
      const illegalList = data.illegalList || []
      for (const item of illegalList) {
        tableData.push({
          type: '违法案件',
          value: item.illegalContent,
        })
      }
      // 处理-司法案件
      const judicialList = data.judicialList || []
      for (const item of judicialList) {
        tableData.push({
          type: '司法案件',
          value: item.judicialContent,
        })
      }
      // 处理-质押案件
      const iprPledgeList = data.iprPledgeList || []
      for (const item of iprPledgeList) {
        tableData.push({
          type: '质押案件',
          value: item.iprPledgeContent,
        })
      }
      this.tableData = tableData
    },
    initData() {
      this.loading = true
      requestBaseDataCompanyNegative({
        keyword: this.unifiedSocialCode || this.companyName,
        companyId: this.companyId,
      })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.formatTableData(data)
            this.loading = false
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleRefreshBtn() {
      this.$refs.refreshDialog.handleOpen()
    },
    handleRefreshCancel() {
      this.$refs.refreshDialog.handleClose()
    },
    handleRefreshConfirm() {
      this.dialogLoading = true
      requestRefreshBaseDataCompanyNegative({
        keyword: this.unifiedSocialCode || this.companyName,
      })
        .then(({ data }) => {
          this.dialogLoading = false
          if (data.success) {
            this.$message.success('操作成功')
            this.$refs.refreshDialog.handleClose()
            this.initData()
          }
        })
        .catch(() => {
          this.dialogLoading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
// .negative-factor-container {
//   .negative-factor-table {
//   }
// }
</style>
