<template>
  <div class="trade-container">
    <LayoutCard :title="`上游供应商(${upList.length})`">
      <div v-loading="loading" class="card-list">
        <Card
          v-for="item of upList"
          :key="item.id"
          :title="item.companyHeightName"
          :initDate="item.firstTradeTime"
          :bindDate="item.relationTime"
          :status="item.status"
          :processInsId="item.processId"
        />
        <NewCard @newClick="handleNewClick('up')" />
      </div>
    </LayoutCard>
    <LayoutCard
      :title="`下游${this.type === 'coreEnterprise' ? '经销商' : '核心企业'}(${
        downList.length
      })`"
    >
      <div v-loading="loading" class="card-list">
        <Card
          v-for="item of downList"
          :key="item.id"
          :title="item.companyLowerName"
          :initDate="item.firstTradeTime"
          :bindDate="item.relationTime"
          :status="item.status"
          :processInsId="item.processId"
        />
        <NewCard
          v-if="this.type === 'coreEnterprise'"
          @newClick="handleNewClick('down')"
        />
      </div>
    </LayoutCard>
    <LinkUpSupplier ref="linkUpSupplier" @complete="LinkUpSupplierComplete" />
    <InviteSupplier ref="inviteSupplier" :isUpTarget="isUpTarget" />
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import Card from './components/Card/index.vue'
import NewCard from './components/NewCard/index.vue'
import { requestCoreTradeList } from '@/api/customer/archives/core'
import { requestClientTradeList } from '@/api/customer/archives/client'
import LinkUpSupplier from './components/LinkUpSupplier/index.vue'
import InviteSupplier from './components/InviteSupplier/index.vue'

export default {
  name: 'CustomerModuleTradeIndex',
  components: { LayoutCard, Card, NewCard, LinkUpSupplier, InviteSupplier },
  data() {
    return {
      loading: true,
      upList: [],
      downList: [],
      isUpTarget: true,
    }
  },
  inject: ['type', 'id', 'companyIdFunc'],
  created() {
    this.initData()
  },
  methods: {
    initData() {
      if (this.type === 'coreEnterprise') {
        requestCoreTradeList(this.companyIdFunc()).then(({ data }) => {
          if (data.success) {
            this.requestResultFormat(data.data)
          }
        })
      } else {
        requestClientTradeList(this.companyIdFunc()).then(({ data }) => {
          if (data.success) {
            this.requestResultFormat(data.data)
          }
        })
      }
    },
    requestResultFormat(data) {
      this.upList = data.companyHeight
      this.downList = data.companyLower
      this.loading = false
    },
    handleNewClick(target) {
      if (target === 'up') {
        if (this.type === 'coreEnterprise') {
          this.isUpTarget = true
          this.$refs.inviteSupplier.handleOpen()
        } else {
          this.$refs.linkUpSupplier.handleOpen()
        }
      } else {
        if (this.type === 'coreEnterprise') {
          this.isUpTarget = false
          this.$refs.inviteSupplier.handleOpen()
        } else {
          //
        }
      }
    },
    LinkUpSupplierComplete() {
      this.initData()
    },
  },
}
</script>

<style lang="scss" scoped>
.trade-container {
  .header {
    display: flex;
    margin-bottom: 20px;
    align-items: center;

    .sign {
      display: inline-block;
      width: 8px;
      height: 16px;
      margin-right: 4px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
    }
  }

  .card-list {
    display: flex;
    flex-wrap: wrap;
    min-height: 100px;
    margin-bottom: -24px;
  }
}
</style>
