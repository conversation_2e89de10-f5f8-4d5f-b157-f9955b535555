<template>
  <div class="card-item-container">
    <el-popconfirm title="确定删除该条记录吗？" @confirm="handleDelete">
      <SvgIcon
        slot="reference"
        class="card-close-btn"
        icon-class="icon-line-guanbi"
        style="font-size: 16px"
      />
    </el-popconfirm>
    <el-form
      :model="formData"
      :rules="rules"
      ref="form"
      label-width="120px"
      class="form-container"
    >
      <el-form-item label="选择关联企业：" prop="targetId">
        <el-select
          v-model="formData.targetId"
          filterable
          placeholder="选择关联企业"
        >
          <el-option
            v-for="item in supplierList"
            :key="item.id"
            :label="item.supperName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="初次合作时间：" prop="date">
        <el-date-picker
          type="date"
          placeholder="选择初次合作时间"
          v-model="formData.date"
        ></el-date-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
const rules = {
  targetId: [
    { required: true, message: '请选择关联企业', trigger: ['blur', 'change'] },
  ],
  date: [
    {
      type: 'date',
      required: true,
      message: '请选择日期',
      trigger: ['blur', 'change'],
    },
  ],
}

export default {
  name: 'CustomerModuleTradeNewItem',
  props: {
    data: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    checkAccumulator: {
      type: Number,
      required: true,
    },
    supplierList: {
      type: Array,
      required: true,
      default: () => [],
    }
  },
  data() {
    return {
      rules,
      formData: {
        targetId: '',
        date: '',
      },
    }
  },
  watch: {
    checkAccumulator: {
      handler() {
        this.$refs.form
          .validate()
          .then(() => {
            this.$emit('validatePass')
          })
          .catch(() => {})
      },
    },
    formData: {
      handler(newValue) {
        this.$emit('updateFormData', newValue, this.index)
      },
      deep: true, //为true，表示深度监听，这时候就能监测到a值变化
    },
  },
  methods: {
    handleDelete() {
      this.$emit('deleteForm')
    },
  },
}
</script>

<style lang="scss" scoped>
.card-item-container {
  position: relative;
  padding: 24px 32px;
  margin-bottom: 20px;
  border-radius: 8px;
  background-color: #f7f7f7;

  .card-close-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 16px;
    fill: #9f9f9f;

    &:hover {
      cursor: pointer;
      fill: #3f3f3f;
    }
  }

  .form-container {
    .el-form-item {
      &:last-child {
        margin-bottom: 0;
      }

      .el-select {
        display: block;
      }
    }
  }
}
</style>
