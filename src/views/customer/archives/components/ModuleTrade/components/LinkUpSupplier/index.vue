<template>
  <Dialog
    ref="linkUp"
    title="关联上游供应商"
    center
    height="60vh"
    :cancelDisable="cancelDisable"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
  >
    <div class="add-trade-container">
      <div class="card-list">
        <Item
          v-for="(item, index) of cardList"
          :data="item"
          :index="index"
          :checkAccumulator="formCheckAccumulator"
          :key="item.key"
          :supplierList="supplierList"
          @updateFormData="handleUpdateFormData"
          @deleteForm="handleDeleteForm(index)"
          @validatePass="validatePass"
        ></Item>
      </div>
      <div class="add-btn" @click="handleAdd">
        <SvgIcon icon-class="icon-line-jia" style="font-size: 16px" />
        <span class="btn-name">新增关联</span>
      </div>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import Item from './Item.vue'
import { requestBindUpSupplier } from '@/api/customer/archives/client'
import { requestSupplierList } from '@/api/customer/archives/archive'
import { dateFormat } from '@/util/date'

const createInitCard = () => ({
  targetId: '',
  date: '',
  key: `${new Date().getTime()}${Math.ceil(Math.random() * 1000)}`,
})

const initCardList = () => [createInitCard()]

export default {
  name: 'CustomerModuleTradeLinkUpSupplierIndex',
  components: { Dialog, Item },
  inject: ['companyIdFunc'],
  data: function () {
    return {
      cardList: initCardList(),
      formCheckAccumulator: 0,
      passCount: 0,
      cancelDisable: false,
      confirmLoading: false,
      supplierList: [],
    }
  },
  methods: {
    initData() {
      requestSupplierList()
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.supplierList = data || []
          }
        })
        .catch(() => {})
    },
    handleOpen() {
      this.cancelDisable = false
      this.confirmLoading = false
      this.cardList = initCardList()
      this.$refs.linkUp.handleOpen()
      this.initData()
    },
    handleClose() {
      this.$refs.linkUp.handleClose()
    },
    handleDeleteForm(index) {
      this.cardList.splice(index, 1)
    },
    handleAdd() {
      this.cardList.push(createInitCard())
    },
    handleCancel() {},
    handleConfirm() {
      if (this.cardList.length === 0) return
      this.passCount = 0
      this.formCheckAccumulator += 1
    },
    handleUpdateFormData(value, index) {
      this.cardList[index] = { ...this.cardList[index], ...value }
    },
    submitForm() {
      this.cancelDisable = true
      this.confirmLoading = true
      const idDuplicateCheck = {}
      const request = {
        companyId: this.companyIdFunc(),
        backgroundList: [],
      }
      for (const item of this.cardList) {
        if (idDuplicateCheck[item.targetId]) {
          this.$message.error('含有重复关联的企业，请检查')
          this.cancelDisable = false
          this.confirmLoading = false
          return
        } else {
          idDuplicateCheck[item.targetId] = true
          request.backgroundList.push({
            companyHeightId: item.targetId,
            firstTradeTime: dateFormat(item.date, 'yyyy-MM-dd'),
          })
        }
      }
      requestBindUpSupplier(request)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('提交成功')
            this.$emit('complete')
            this.handleClose()
          }
        })
        .catch(() => {
          this.cancelDisable = false
          this.confirmLoading = false
        })
    },
    validatePass() {
      this.passCount++
      if (this.passCount === this.cardList.length) {
        this.submitForm()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.add-trade-container {
  padding: 24px;

  .add-btn {
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    border: 1px dashed rgba(155, 205, 255, 100);
    color: #697cff;
    cursor: pointer;

    .btn-name {
      margin-left: 4px;
      font-size: 16px;
    }
  }
}
</style>
