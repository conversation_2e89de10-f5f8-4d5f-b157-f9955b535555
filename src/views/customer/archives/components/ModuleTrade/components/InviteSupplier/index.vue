<template>
  <div>
    <Dialog
      ref="inviteUp"
      :title="isUpTarget ? '邀请上游供应商' : '邀请下游经销商'"
      center
      :cancelDisable="cancelDisable"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    >
      <div class="trade-invite-container">
        <el-form
          :model="formData"
          :rules="rules"
          ref="form"
          label-width="120px"
          class="form-container"
        >
          <el-form-item label="企业名称：" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入企业名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系人：" prop="contactName">
            <el-input
              v-model="formData.contactName"
              placeholder="请输入联系人"
              style="width: 240px"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号：" prop="contactPhone">
            <el-input
              v-model="formData.contactPhone"
              placeholder="请输入手机号"
              style="width: 240px"
            ></el-input>
          </el-form-item>
          <el-form-item label="初次合作时间：" prop="date">
            <el-date-picker
              type="date"
              v-model="formData.date"
              placeholder="选择初次合作时间"
              style="width: 240px"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
    </Dialog>
    <Dialog
      ref="inviteInfo"
      :title="isUpTarget ? '邀请供应商信息' : '邀请经销商信息'"
      centerTitle
      width="340px"
      center
      :enableFullScreenBtn="false"
      :noButton="true"
      :noBorder="true"
      @close="handleInviteInfoClose"
    >
      <div class="invite-form-container">
        <div class="form-item">
          <!-- <span class="label"></span> -->
          <span class="value">{{
            inviteResult.coreEnterpriseName +
            `邀请您作为${
              isUpTarget ? '供应商' : '经销商'
            }注册使用精锐供应链金融平台`
          }}</span>
        </div>
        <div class="form-item">
          <!-- <span class="label"></span> -->
          <span class="value"
            ><a style="color: #697cff" href="https://sc.jingruiit.com"
              >https://sc.jingruiit.com</a
            ></span
          >
        </div>
        <!-- <div class="form-item">
          <span class="label">联系人：</span>
          <span class="value">{{ inviteResult.linkMan }}</span>
        </div> -->
        <!-- <div class="form-item">
          <span class="label">手机号：</span>
          <span class="value">{{ inviteResult.phone }}</span>
        </div> -->
        <div class="form-item">
          <span class="label">邀请码：</span>
          <span class="value">{{ inviteResult.code }}</span>
        </div>
        <div class="form-item">
          <span class="label">邀请码有效期：</span>
          <span class="value">{{ inviteResult.expireDate }}</span>
        </div>
        <div class="form-item">
          <span class="label">绑定步骤：</span>
          <span class="value">注册成功-用户中心-实名认证-绑定贸易伙伴。</span>
        </div>
        <div class="btn-container">
          <el-button class="sms" type="info" size="medium">短信邀请</el-button>
          <el-button
            class="copy"
            type="info"
            size="medium"
            @click="handleCopy($event)"
            >复制邀请</el-button
          >
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { requestCoreTradeInvite } from '@/api/customer/archives/core'
import { dateFormat } from '@/util/date'
import Clipboard from 'clipboard'

const rules = {
  name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  contactName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactPhone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/,
      message: '请输入合法手机号/电话号',
      trigger: 'blur',
    },
  ],
  date: [
    {
      type: 'date',
      required: true,
      message: '选择初次合作时间',
      trigger: 'change',
    },
  ],
}
const initFormData = () => ({
  name: '',
  contactName: '',
  contactPhone: '',
  date: '',
})

export default {
  name: 'CustomerModuleTradeLinkUpzSupplierIndex',
  components: { Dialog },
  inject: ['companyIdFunc'],
  props: {
    isUpTarget: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      rules,
      formData: initFormData(),
      confirmLoading: false,
      cancelDisable: false,
      inviteResult: {},
    }
  },
  methods: {
    handleOpen() {
      this.formData = initFormData()
      this.cancelDisable = false
      this.confirmLoading = false
      this.$refs.inviteUp.handleOpen()
    },
    handleCancel() {},
    handleConfirm() {
      this.$refs.form
        .validate()
        .then(() => {
          this.submitForm()
        })
        .catch(() => {})
    },
    submitForm() {
      this.cancelDisable = true
      this.confirmLoading = true
      const request = {
        coreEnterpriseId: this.companyIdFunc(),
        frontEnterpriseName: this.formData.name,
        linkMan: this.formData.contactName,
        phone: this.formData.contactPhone,
        start: dateFormat(this.formData.date, 'yyyy-MM-dd'),
      }
      requestCoreTradeInvite(request)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('提交成功')
            data = data.data
            this.inviteResult = data || []
            // setTimeou 延迟执行，避免提前关闭蒙层导致背景闪动
            setTimeout(() => {
              this.$refs.inviteUp.handleClose()
            })
            this.$refs.inviteInfo.handleOpen()
            this.cancelDisable = false
            this.confirmLoading = false
          }
        })
        .catch(() => {
          this.cancelDisable = false
          this.confirmLoading = false
        })
    },
    handleInviteInfoClose() {
      // this.$refs.inviteUp.handleClose()
    },
    handleCopy(event) {
      const copyValue = `${this.inviteResult.coreEnterpriseName}邀请您作为${
        this.isUpTarget ? '供应商' : '经销商'
      }注册使用精锐供应链金融平台
https://sc.jingruiit.com
邀请码：${this.inviteResult.code || '--'}
邀请码有效期：${this.inviteResult.expireDate || '--'}
绑定步骤：注册成功-用户中心-实名认证-绑定贸易伙伴。`
      const clipboard = new Clipboard(event.target, { text: () => copyValue })
      clipboard.on('success', () => {
        this.$message({ type: 'success', message: '复制成功' })
        // 释放内存
        clipboard.off('error')
        clipboard.off('success')
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        console.error(e)
        // 不支持复制
        this.$message({ type: 'waning', message: '该浏览器不支持自动复制' })
        // 释放内存
        clipboard.off('error')
        clipboard.off('success')
        clipboard.destroy()
      })
      clipboard.onClick(event)
    },
  },
}
</script>

<style lang="scss" scoped>
.trade-invite-container {
  padding: 24px 80px;

  .form-container {
    .el-form-item {
      &:last-child {
        margin-bottom: 0;
      }

      .el-select {
        display: block;
      }
    }
  }
}

.invite-form-container {
  padding: 0 20px 20px;

  .form-item {
    margin-bottom: 12px;

    .label {
      font-size: 14px;
    }

    .value {
      font-size: 14px;
    }
  }

  .btn-container {
    > .el-button {
      display: block;
      width: 100%;
      margin: 0;
      margin-bottom: 12px;
      color: #fff;

      &.sms {
        background-color: #697cff;
        border-color: transparent;
      }

      &.copy {
        background-color: #f0b314;
        border-color: transparent;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
