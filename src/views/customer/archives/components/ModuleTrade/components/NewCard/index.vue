<template>
  <div class="trade-new-container" @click="handleClick">
    <i class="el-icon-plus" style="font-size: 32px" />
  </div>
</template>

<script>
export default {
  name: 'CustomerTradeNewCardIndex',
  methods: {
    handleClick() {
      this.$emit('newClick')
    },
  },
}
</script>

<style lang="scss" scoped>
.trade-new-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: calc((100% - 48px) / 3);
  min-height: 178px;
  margin-bottom: 24px;
  padding: 16px;
  line-height: 20px;
  border: 1px solid rgba(215, 215, 215, 100);
  border-radius: 8px;
  box-sizing: border-box;

  &:hover {
    cursor: pointer;
    background-color: rgba(247, 247, 247, 100);
  }
}
</style>
