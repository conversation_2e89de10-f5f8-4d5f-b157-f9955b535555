<template>
  <div class="trade-card-container" :class="{ two }">
    <div class="title-wrapper">
      <span class="title">{{ title }}</span>
      <Tag
        v-if="!hideStatus"
        :name="statusMap[status].name"
        :color="statusMap[status].color"
        :borderColor="statusMap[status].borderColor"
        radius
      />
    </div>
    <span class="init-date">初次合作日期：{{ initDate }}</span>
    <span class="bind-date">绑定时间：{{ bindDate }}</span>
    <div class="botton-container">
      <el-button
        v-if="processInsId !== null"
        :loading="btnLoading"
        @click="handleViewDetail"
        size="small"
        >审批详情</el-button
      >
      <el-button
        v-if="enableTradeSpace"
        @click="handleRedirectTradeSpace"
        size="small"
        >贸易空间</el-button
      >
    </div>
  </div>
</template>

<script>
import Tag from '../../../Tag/index.vue'
import { tradeStatusMap } from '../../../../config'
import { requestProcessInfo } from '@/api/customer/archives/archive'
import { routerMapKeyToPath } from '@/views/business/config'

export default {
  name: 'CustomerTradeCardIndex',
  components: { Tag },
  props: {
    id: {
      type: String,
    },
    title: {
      type: String,
      default: '',
    },
    initDate: {
      type: String,
      default: '',
    },
    bindDate: {
      type: String,
      default: '',
    },
    status: {
      type: String,
    },
    processInsId: {
      type: String,
      required: true,
    },
    two: {
      type: Boolean,
      default: false,
    },
    enableTradeSpace: {
      type: Boolean,
      default: false,
    },
    tradeSpaceParams: {
      type: Object,
      default: () => {},
    },
    hideStatus: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      btnLoading: false,
      statusMap: tradeStatusMap,
    }
  },
  methods: {
    handleViewDetail() {
      this.btnLoading = true
      requestProcessInfo({ processInsId: this.processInsId })
        .then(({ data }) => {
          this.btnLoading = false
          const { taskId, processInsId, processDefinitionKey } = data.data
          const target = routerMapKeyToPath[processDefinitionKey]
          if (!target) return
          this.$router.push(
            `${target}/detail/${Buffer.from(
              JSON.stringify({
                taskId,
                processInsId,
              })
            ).toString('base64')}`
          )
          this.$emit('anyBtnClick')
        })
        .catch(() => {
          this.btnLoading = false
        })
    },
    handleRedirectTradeSpace() {
      this.$router.push(
        `/customer/archives/${this.tradeSpaceParams.entType}/${this.id}/trade/detail/${this.tradeSpaceParams.location}`
      )
      this.$emit('anyBtnClick')
    },
  },
}
</script>

<style lang="scss" scoped>
.trade-card-container {
  display: inline-block;
  width: calc((100% - 48px) / 3);
  min-height: 178px;
  margin-right: 24px;
  margin-bottom: 24px;
  padding: 16px;
  line-height: 20px;
  border-radius: 8px;
  background-color: rgba(247, 247, 247, 100);
  box-sizing: border-box;

  &.two {
    width: calc((100% - 24px) / 2);
    margin-right: 24px !important;

    &:nth-child(2n) {
      margin-right: 0 !important;
    }
  }

  &:nth-child(3n) {
    margin-right: 0;
  }

  .title-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .title {
      color: rgba(105, 124, 255, 100);
      font-size: 16px;
      text-align: left;
    }
  }

  .init-date,
  .bind-date {
    display: inline-block;
    width: 100%;
    line-height: 20px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
    margin-bottom: 16px;
  }
}
</style>
