<template>
  <Dialog
    title="使用明细"
    ref="DialogRef"
    width="80vw"
    center
    noConfirmBtn
    cancelBtnText="关闭"
    noBorder
  >
    <div class="content">
      <template v-if="loading">
        <div v-loading style="height: 100px"></div>
      </template>
      <template v-else>
        <div class="desc-container">
          <div class="desc-item">
            <span
              class="desc-item-value"
              style="color: rgba(105, 124, 255, 100)"
              >￥{{ amountData.unUseAmount | formatMoney }}</span
            >
            <span class="desc-item-label">未使用额度(元)</span>
          </div>
          <div class="desc-item">
            <span class="desc-item-value" style="color: rgba(31, 195, 116, 100)"
              >￥{{ amountData.usedAmount | formatMoney }}</span
            >
            <span class="desc-item-label">已用额度(元)</span>
          </div>
          <div class="desc-item">
            <span class="desc-item-value" style="color: rgba(91, 91, 91, 100)"
              >￥{{ amountData.confirmAmount | formatMoney }}</span
            >
            <span class="desc-item-label">确权总额(元)</span>
          </div>
        </div>
        <div class="table-container">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            :header-cell-style="
              () => {
                return { backgroundColor: '#FFF1F1', color: '#000' }
              }
            "
          >
            <el-table-column type="index" label="#" width="48" align="center" />
            <el-table-column prop="useType" label="使用方式" min-width="100">
              <template slot-scope="{ row }">
                <Tag
                  :name="useType == 1 ? '质押' : '转让'"
                  color="#00072A"
                  backgroundColor="#EAECF1"
                  borderColor="transparent"
                  radius
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="enterpriseName"
              label="质押/转让对象"
              min-width="200"
            >
              <template slot-scope="{ row }">
                <span>{{ row.enterpriseName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="使用金额">
              <template slot-scope="{ row }">
                <span>¥{{ row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="使用日期"
              min-width="100"
            />
            <el-table-column prop="options" label="操作" min-width="120">
              <template slot-scope="{ row }">
                <template v-if="false">
                  <el-button
                    @click="handleBtnViewDetail(row)"
                    type="text"
                    size="small"
                    >订单详情</el-button
                  >
                  <el-button
                    @click="handleBtnApproveDetail(row)"
                    type="text"
                    size="small"
                    >审批详情</el-button
                  >
                </template>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            hide-on-single-page
            @current-change="handleTableCurrentChange"
            :current-page.sync="paginationData.currentPage"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
          >
          </el-pagination>
        </div>
      </template>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import Tag from '../../../Tag/index.vue'
import { requestAssetUseDetail } from '@/api/customer/archives/archive'

const initPaginationData = () => ({
  pageSize: 10,
  currentPage: 1,
  maxPage: undefined,
  total: 0,
})

export default {
  name: 'CustomerArchivesModuleAssetInformationDialogUseDetail',
  components: { Dialog, Tag },
  data: function () {
    return {
      loading: true,
      amountData: {},
      tableData: [],
      paginationData: initPaginationData(),
    }
  },
  methods: {
    handleOpen(id) {
      this.loading = true
      this.recordsData = []
      this.$refs.DialogRef.handleOpen()
      requestAssetUseDetail({ saleContractId: id })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.tableData = data.salesContractDetail || []
            this.amountData = {
              confirmAmount: data.confirmAmount,
              unUseAmount: data.unUseAmount,
              usedAmount: data.usedAmount,
            }
          }
          this.loading = false
        })
        .catch(() => {})
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 24px 24px 0px;
  border-top: 1px solid #dbdbdb;

  .desc-container {
    display: flex;
    height: 123px;
    margin-bottom: 12px;

    .desc-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      line-height: 20px;
      border-radius: 4px;
      background-color: rgba(247, 247, 247, 100);
      text-align: center;
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }

      .desc-item-value {
        font-size: 28px;
        text-align: center;
        font-family: SourceHanSansSC-black;
        margin-bottom: 12px;
      }

      .desc-item-label {
        color: rgba(0, 7, 42, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }
    }
  }
}
</style>
