<template>
  <div class="info-card" :class="{ empty }">
    <template v-if="!empty">
      <div class="value-container" :style="{ color }">
        <span class="value">{{ value }}</span>
        <span class="label">{{ label }}</span>
      </div>
      <span class="desc">{{ desc }}</span>
    </template>
  </div>
</template>

<script>
export default {
  name: 'CustomerArchivesSimpleInfoCardIndex',
  props: {
    value: {
      tyle: String,
    },
    label: {
      type: String,
    },
    desc: {
      type: String,
    },
    color: {
      type: String,
      default: '#697CFF',
    },
    empty: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.info-card {
  display: inline-block;
  min-width: 200px;
  padding: 24px 24px 26px;
  border-radius: 8px;
  background-color: rgba(247, 247, 247, 100);
  text-align: center;

  &.empty {
    height: 0;
    padding: 0 24px;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .value-container {
    display: block;
    line-height: 28px;

    .value {
      font-size: 20px;
      font-weight: 600;
    }

    .label {
      font-size: 14px;
      font-weight: 500;
    }
  }

  .desc {
    margin-top: 4px;
    line-height: 20px;
    font-size: 12px;
  }
}
</style>
