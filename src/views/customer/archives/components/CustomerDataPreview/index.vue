<template>
  <div class="container">
    <div class="container-head box1">
      <h3>数据总览</h3>
      <div class="card-ul dis-al">
        <div class="card-li card-blue">
          <span>总授信额度(元)</span>
          <span>{{ detail.totalCreditAmount | formatMoney }}</span>
        </div>
        <div class="card-li card-green">
          <span>可用额度(元)</span>
          <span>{{ detail.totalAvailableAmount | formatMoney }}</span>
        </div>
        <div class="card-li card-gold">
          <span>借款总额(元)</span>
          <span>{{ detail.totalLoanAmount | formatMoney }}</span>
        </div>
        <div class="card-li card-pink">
          <span>逾期金额(元)</span>
          <span>{{ detail.overDueLoanAmount | formatMoney }}</span>
        </div>
      </div>
    </div>
    <div class="container-main dis-al">
      <div class="main-left box1">
        <h3>客户评分</h3>
        <div
          id="score-norm"
          :style="{ display: isShowScore ? '' : 'none' }"
        ></div>
        <div
          class="score-norm dis_flex_between"
          :style="{ display: !isShowScore ? '' : 'none' }"
        >
          暂无数据
        </div>
      </div>
      <div class="main-right">
        <div class="box1">
          <h3>风险指标</h3>
          <div
            id="risk-norm"
            :style="{ display: isShowRisk ? '' : 'none' }"
          ></div>
          <div
            class="norm dis_flex_between"
            :style="{ display: !isShowRisk ? '' : 'none' }"
          >
            暂无数据
          </div>
        </div>
        <div class="box1">
          <h3>贷款产品偏好</h3>
          <div
            id="goods-norm"
            :style="{ display: isShowProduct ? '' : 'none' }"
          ></div>
          <div
            class="norm dis_flex_between"
            :style="{ display: !isShowProduct ? '' : 'none' }"
          >
            暂无数据
          </div>
        </div>
      </div>
    </div>
    <div class="container-bottom box1">
      <div class="bt-head">
        <h3>信贷记录</h3>
        <el-select
          v-model="recentDay"
          style="width: 100px"
          @change="handleSelectChange"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div
        id="credit-records"
        :style="{ display: isShowRecord ? '' : 'none' }"
      ></div>
      <div
        class="credit-records dis_flex_between"
        :style="{ display: !isShowRecord ? '' : 'none' }"
      >
        暂无数据
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {
  getDataStatistics,
  getCustomerScore,
  getDataRiskNorm,
  getDataLoanNorm,
  getDataLoanRecord,
} from '@/api/customer/archives/person'
export default {
  name: 'CustomerDataPreview',
  data() {
    return {
      options: [
        {
          label: '近7日',
          value: '7',
        },
        {
          label: '近30日',
          value: '30',
        },
        {
          label: '近3月',
          value: '90',
        },
        {
          label: '近1年',
          value: '360',
        },
      ],
      isShowRisk: true,
      isShowProduct: true,
      isShowScore: true,
      isShowRecord: true,
      recentDay: '7',
      detail: {},
    }
  },
  inject: ['userId'],

  mounted() {
    let params = {
      userId: this.userId,
      userType: 1,
    }
    // 数据总览
    this.getDataStatistics(params)
    // 客户评分
    this.getCustomerScore(params)

    // 风险指标
    this.getDataRiskNorm(params)
    // 贷款产品偏好
    this.getDataLoanNorm(params)
    // 信贷记录
    this.getDataLoanRecord({ ...params, recentDay: '7' })
  },
  methods: {
    // 数据总览
    async getDataStatistics(params) {
      const { data } = await getDataStatistics(params)
      if (data.code === 200) {
        this.detail = { ...data.data }
      }
    },
    // 客户评分
    async getCustomerScore(params) {
      const { data } = await getCustomerScore(params)
      this.isShowScore =
        data.data && data.data.indicator.length && data.data.series.length
          ? true
          : false
      if (data.code === 200) {
        if (data.data) {
          for (const item of data.data.series) {
            item.type = 'radar'
            item.lineStyle = {
              color: '#63b9fe',
            }
            item.areaStyle = {
              color: '#a7d8ff',
            }
            item.symbol = 'none'
            item.markPoint = {
              clickable: false,
              symbol: 'diamond', //使用中间是空的图形类型，设置边框为0后，就不显示了
              data: [
                {
                  name: '',
                  value: data.data && data.data.totalScore,
                  x: '50%',
                  y: '50%',
                  symbolSize: 0,
                  label: {
                    fontSize: 72,
                    color: '#0f40f5',
                  },
                },
                {
                  name: '',
                  value: data.data && data.data.grade,
                  x: '50%',
                  y: '58%',
                  symbolSize: 0,
                  label: {
                    fontSize: 18,
                    color: '#0f40f5',
                  },
                },
              ],
            }
          }
        }
        this.initScopeNorm(data.data)
      }
    },
    // 风险指标
    async getDataRiskNorm(params) {
      const { data } = await getDataRiskNorm(params)
      if (data.code === 200) {
        if (data.data) {
          data.data.series =
            (data.data.series &&
              data.data.series.map(item => {
                return {
                  ...item,
                  realtimeSort: true,
                  labelShow: false,
                  type: 'bar',
                  itemStyle: {
                    borderColor: '#d97559',
                    color: '#d97559',
                  },
                }
              })) ||
            []

          this.initRiskNorm(data.data)
        }
      }
      this.isShowRisk =
        data.data && data.data.series && data.data.yaxis ? true : false
    },
    // 贷款产品偏好
    async getDataLoanNorm(params) {
      const { data } = await getDataLoanNorm(params)
      this.isShowProduct = data.data && data.data.data ? true : false
      console.log(this.isShowProduct, '111')
      if (data.code === 200) {
        this.initGoodsNorm(data.data)
      }
    },
    handleSelectChange() {
      const params = {
        userType: 1,
        userId: this.userId,
        recentDay: this.recentDay,
      }
      this.getDataLoanRecord(params)
    },
    // 信贷记录
    async getDataLoanRecord(params) {
      const { data } = await getDataLoanRecord(params)
      this.isShowRecord =
        data.data.series.length && data.data.yaxis.length ? true : false
      if (data.code === 200) {
        if (data.data) {
          data.data.yaxis =
            (data.data.yaxis &&
              data.data.yaxis.map(item => {
                return {
                  ...item,
                  type: 'value',
                  axisLabel: {
                    formatter: '{value} 元',
                  },
                }
              })) ||
            []
        }
        this.initCredit(data.data)
      }
    },

    // 客户评分
    initScopeNorm(data = {}) {
      let options = {
        title: {
          text: '',
        },
        radar: {
          // shape: 'circle',
          indicator: (data && data.indicator) || [],
          splitArea: {
            areaStyle: {
              color: ['#fff'],
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10,
            },
          },
          axisName: {
            color: '#666',
          },
          axisLine: {
            lineStyle: {
              color: '#ecedf1',
              width: 2,
            },
          },
          splitLine: {
            lineStyle: {
              color: '#ecedf1',
              width: 2,
            },
          },
        },
        series: data && data.series,
      }
      let initEchart = echarts.init(document.getElementById('score-norm'))
      initEchart && initEchart.setOption(options)
    },
    // 风险指标
    initRiskNorm(data = {}) {
      let options = {
        title: {
          text: '',
          textStyle: {
            fontSize: 16,
          },
        },
        legend: {
          bottom: '0',
        },

        grid: {
          top: '15%',
          left: '2%',
          right: '4%',
          bottom: '12%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} 次',
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#666',
            },
          },
        },
        yAxis: {
          type: (data && data.yaxis && data.yaxis.type) || '',
          inverse: true,
          data: (data && data.yaxis && data.yaxis.data) || [],
          axisLine: {
            show: true,
            lineStyle: {
              color: '#666',
            },
          },
        },
        series: data && data.series,
      }

      let initEchart = echarts.init(document.getElementById('risk-norm'))
      initEchart && initEchart.setOption(options)
    },
    // 贷款产品偏好
    initGoodsNorm(data = {}) {
      let options = {
        title: {
          text: '',
          textStyle: {
            fontSize: 16,
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            itemStyle: {
              borderRadius: 4,
              borderWidth: 1,
            },
            label: {
              formatter: '{b} {d}%',
              width: 160,
              color: '#666',
            },
            data: (data && data.data) || [],
          },
        ],
        media: [
          {
            option: {
              series: [{ center: ['50%', '50%'] }],
            },
          },
        ],
      }
      let initEchart = echarts.init(document.getElementById('goods-norm'))

      initEchart && initEchart.setOption(options)
    },
    // 信贷
    initCredit(data = {}) {
      let options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
        },
        legend: {
          data: (data && data.legend) || [],
          bottom: '0',
        },
        xAxis: [
          {
            type: 'category',
            data: (data && data.xaxis) || [],
            axisPointer: {
              type: 'shadow',
            },
          },
        ],
        yAxis: (data && data.yaxis) || [],
        series: (data && data.series) || [],
      }
      let initEchart = echarts.init(document.getElementById('credit-records'))
      initEchart && initEchart.setOption(options)
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;

  .container-head {
    box-sizing: border-box1;

    .card-ul {
      margin-top: 10px;
      .card-li {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        list-style: none;
        border-radius: 8px;
        height: 156px;
        width: 100%;
        & span:nth-of-type(1) {
          line-height: 30px;
          color: #101010;
          font-size: 14px;
          font-weight: 400;
        }
        & span:nth-of-type(2) {
          line-height: 30px;
          color: #101010;
          font-size: 28px;
          font-weight: 500;
        }
        &:not(:last-child) {
          margin-right: 16px;
        }
      }
    }
  }
  .container-main {
    .main-left {
      width: 60%;
      margin-right: 14px;
      #score-norm,
      .score-norm {
        height: 480px;
      }
    }
    .main-right {
      flex-grow: 1;

      .norm,
      #risk-norm,
      #goods-norm {
        height: 260px;
      }
    }
  }
  .container-bottom {
    .bt-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      h3 {
        color: #101010;
        font-size: 16px;
        font-weight: bold;
      }
    }
    #credit-records,
    .credit-records {
      height: 366px;
    }
  }
}
.dis-al {
  display: flex;
  align-content: center;
}
.dis_flex_between {
  display: flex;
  align-items: center;
  justify-content: center;
}
.box1 {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 12px;
}
h3 {
  color: #101010;
  line-height: 28px;
  font-size: 16px;
  font-weight: bold;
  font-family: SourceHanSansSC-bold;
}
.card-blue {
  background-color: rgba(147, 210, 243, 0.15);
}
.card-green {
  background-color: rgba(204, 247, 131, 0.15);
}
.card-gold {
  background-color: rgba(252, 202, 0, 0.15);
}
.card-pink {
  background-color: rgba(222, 134, 143, 0.15);
}
</style>
