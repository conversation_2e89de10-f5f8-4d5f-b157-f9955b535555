<template>
  <div class="archives-layout-card">
    <div class="header">
      <span class="sign" />
      <span class="title">{{ title }}</span>
    </div>
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ArchivesLayoutCardIndex',
  props: {
    title: {
      type: String,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.archives-layout-card {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  margin-bottom: 20px;
  background: #fff;

  .header {
    display: flex;
    margin-bottom: 20px;
    align-items: center;

    .sign {
      display: inline-block;
      width: 8px;
      height: 16px;
      margin-right: 4px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
    }
  }

  .content {
    position: relative;
    flex: 1;
  }
}
</style>
