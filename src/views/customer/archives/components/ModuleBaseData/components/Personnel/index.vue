<template>
  <div class="personnel-container">
    <el-tabs v-model="activeName">
      <template v-if="this.type === 'person'">
        <!-- <el-tab-pane v-if="customShowPersonFirst" label="借款人" name="first"> -->
        <el-descriptions
          v-loading="false"
          labelStyle="width: 136px"
          :column="3"
          border
        >
          <el-descriptions-item v-if="data.name !== undefined" label="姓名">{{
            data.name
          }}</el-descriptions-item>
          <el-descriptions-item label="证件类型">身份证</el-descriptions-item>
          <el-descriptions-item
            v-if="data.identity !== undefined"
            label="证件号码"
            >{{ data.identity }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.account !== undefined"
            label="手机号"
            >{{ data.account }}</el-descriptions-item
          >
          <el-descriptions-item v-if="data.sex !== undefined" label="性别">{{
            data.sex === 0 ? '男' : data.sex === 1 ? '女' : '未知'
          }}</el-descriptions-item>
          <el-descriptions-item
            v-if="data.identityEffective !== undefined"
            label="证件有效期"
            >{{ data.identityEffective || '--' }}</el-descriptions-item
          >
          <el-descriptions-item v-if="data.nation !== undefined" label="民族">{{
            data.nation || '--'
          }}</el-descriptions-item>
          <el-descriptions-item
            v-if="data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="国籍"
            >{{
              data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="婚姻状况"
            >{{
              data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="职业类别"
            >{{
              data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="学历"
            >{{
              data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="税收居民类型"
            >{{
              data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.address !== undefined"
            label="证件地址"
            :span="3"
            >{{ data.address }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="现居住址"
            :span="3"
            >{{
              data.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
        </el-descriptions>
        <!-- </el-tab-pane>
        <el-tab-pane
          v-if="customShowPersonSecond"
          label="借款人配偶"
          name="second"
        >
          <el-descriptions
            v-loading="false"
            labelStyle="width: 136px"
            :column="3"
            border
          >
            <el-descriptions-item label="姓名"
              >18100000000</el-descriptions-item
            >
            <el-descriptions-item label="证件类型">苏州市</el-descriptions-item>
            <el-descriptions-item label="证件号码"
              >kooriookami</el-descriptions-item
            >
            <el-descriptions-item label="手机号"
              >18100000000</el-descriptions-item
            >
            <el-descriptions-item label="性别">苏州市</el-descriptions-item>
            <el-descriptions-item label="证件有效期"
              >kooriookami</el-descriptions-item
            >
            <el-descriptions-item label="民族"
              >18100000000</el-descriptions-item
            >
            <el-descriptions-item label="国籍">苏州市</el-descriptions-item>
            <el-descriptions-item label="婚姻状况"
              >kooriookami</el-descriptions-item
            >
            <el-descriptions-item label="职业类别"
              >18100000000</el-descriptions-item
            >
            <el-descriptions-item label="学历">苏州市</el-descriptions-item>
            <el-descriptions-item label="税收居民类型"
              >kooriookami</el-descriptions-item
            >
            <el-descriptions-item label="注册地址" :span="3">{{
              '江苏省张家港市张家港经济技术开发区国泰北路1号（悦丰大厦）'
            }}</el-descriptions-item>
            <el-descriptions-item label="经营范围" :span="3">{{
              '从事内燃机汽车的研发、生产、全出口销售；汽车零部件及配件的研发、生产、销售；新能源汽车（包括纯电动乘用车）的研发、生产和销售；出口整车及其零部件、组件和配饰（国家禁止或涉及行政审批的货物和技术进出口除外）；提供售后服务；汽车配饰购销；仓储服务；道路货物运输服务；提供相关的培训（不含国家统一认可的职业证书类培训）、咨询、试验和技术服务。（依法须经批准的项目，经相关部门批准后方可开展经营活动）'
            }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane> -->
      </template>
      <template v-else>
        <el-descriptions
          v-loading="false"
          labelStyle="width: 136px"
          :column="3"
          border
        >
          <el-descriptions-item
            v-if="data.corporationName !== undefined"
            label="姓名"
            >{{ data.corporationName }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationIdType !== undefined"
            label="证件类型"
            >{{
              data.corporationIdType == 1 ? '护照号码' : '身份证号码'
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationIdCardNumber !== undefined"
            label="证件号码"
            >{{ data.corporationIdCardNumber }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.mobile !== undefined"
            label="手机号"
            >{{ data.mobile }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationSex !== undefined"
            label="性别"
            >{{
              data.corporationSex === '--'
                ? '--'
                : data.corporationSex == 0
                ? '男'
                : '女'
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationValidTime !== undefined"
            label="证件有效期"
            >{{ data.corporationValidTime }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationNation !== undefined"
            label="民族"
            >{{ data.corporationNation }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationCountry !== undefined"
            label="国籍"
            >{{ data.corporationCountry }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="婚姻状况"
            >{{
              data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="职业类别"
            >{{
              data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="学历"
            >{{
              data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="税收居民类型"
            >{{
              data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationAddress !== undefined"
            label="证件地址"
            :span="3"
            >{{ data.corporationAddress }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx !== undefined"
            label="现居住址"
            :span="3"
            >{{
              data.corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx
            }}</el-descriptions-item
          >
        </el-descriptions>
      </template>
    </el-tabs>
    <div v-if="isRealName && hasViewCert" class="btn-container">
      <el-button
        :loading="viewCertBtnLoading"
        size="small"
        @click="handleViewCert"
        >查看证书</el-button
      >
    </div>
  </div>
</template>

<script>
import { requestBaseDataPicByAttachId } from '@/api/customer/archives/archive'

export default {
  name: 'ArchivesBaseDataPersonnelIndex',
  props: {
    data: {
      type: Object,
      required: true,
    },
    isRealName: {
      type: Boolean,
      default: false,
    },
    customShowPersonFirst: {
      type: Boolean,
      default: true,
    },
    customShowPersonSecond: {
      type: Boolean,
      default: true,
    },
    customShowEntFirst: {
      type: Boolean,
      default: true,
    },
    customShowEntSecond: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      viewCertBtnLoading: false,
    }
  },
  computed: {
    hasViewCert() {
      return (
        this.data.corporationFaceAttachId || this.data.corporationBackAttachId
      )
    },
  },
  inject: ['type'],
  methods: {
    async handleViewCert() {
      this.viewCertBtnLoading = true
      let faceAttachId = undefined
      let backAttachId = undefined
      const imgSrcArr = []
      if (this.type === 'person') {
        // 个人
        // 个人 - 借款人数据处理
        // if (this.activeName === 'first') {
        //   // 借款人
        // } else {
        //   // 借款人配偶
        // }
      } else {
        // 企业
        faceAttachId = this.data.corporationFaceAttachId
        backAttachId = this.data.corporationBackAttachId
      }
      try {
        let [faceData, backData] = await Promise.all([
          requestBaseDataPicByAttachId({ attachId: faceAttachId }),
          requestBaseDataPicByAttachId({ attachId: backAttachId }),
        ])
        faceData = faceData.data || {}
        faceData = faceData.data || []
        for (const item of faceData) {
          imgSrcArr.push({ url: item })
        }
        backData = backData.data || {}
        backData = backData.data || []
        for (const item of backData) {
          imgSrcArr.push({ url: item })
        }
        this.viewCertBtnLoading = false
        this.$ImagePreview(imgSrcArr, 0, { closeOnClickModal: false })
      } catch (err) {
        this.viewCertBtnLoading = false
        return
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.personnel-container {
  margin-top: -14px;

  .btn-container {
    margin-top: 14px;
    text-align: right;
  }
}
</style>
