<template>
  <div class="equity-container">
    <el-table :data="tableData" style="width: 100%">
      <!-- <el-table-column type="index" label="序号" width="112"></el-table-column> -->
      <el-table-column prop="name" label="股东名称"></el-table-column>
      <el-table-column prop="amomon" label="出资金额"></el-table-column>
      <el-table-column prop="percent" label="持股比例"> </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'ArchivesBaseDataEquityIndex',
  props: {
    data: {
      type: Array,
      required: true,
    },
    isRealName: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.prototype.toString.call(val) === '[object Array]') {
          this.tableData = val
        } else {
          val = []
        }
        this.tableData = val
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
// .equity-container {
// }
</style>
