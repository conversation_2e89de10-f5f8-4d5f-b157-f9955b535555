<template>
  <div class="business-container">
    <el-descriptions labelStyle="width: 136px" :column="3" border>
      <el-descriptions-item
        v-if="data.legalPersonName !== undefined"
        label="法定代表人"
        >{{ data.legalPersonName }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.phoneNumber !== undefined"
        label="法人手机号"
        >{{ data.phoneNumber }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.estiblishTime !== undefined"
        label="成立日期"
        >{{ data.estiblishTime }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.creditCode !== undefined"
        label="统一社会代码"
        >{{ data.creditCode }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.orgNumber !== undefined"
        label="组织机构代码"
        >{{ data.orgNumber }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.approvedTime !== undefined"
        label="营业期限"
        >{{ data.approvedTime }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.regNumber !== undefined"
        label="工商注册号"
        >{{ data.regNumber }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.totalAssets !== undefined"
        label="纳税人资质"
        >{{ data.totalAssets }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.regDate !== undefined"
        label="核准日期"
        >{{ data.regDate }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.regCapital !== undefined"
        label="注册资本"
        >{{ data.regCapital }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.paidAmount !== undefined"
        label="实缴资本"
        >{{ data.paidAmount }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.regInstitute !== undefined"
        label="登记机关"
        >{{ data.regInstitute }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.companyOrgType !== undefined"
        label="企业类型"
        >{{ data.companyOrgType }}</el-descriptions-item
      >
      <el-descriptions-item v-if="data.industry !== undefined" label="行业">{{
        data.industry
      }}</el-descriptions-item>
      <el-descriptions-item
        v-if="data.employeeNum !== undefined"
        label="人员规模"
        >{{ data.employeeNum }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.medicalInsurance !== undefined"
        label="参保人员"
      >
        {{ data.medicalInsurance }}
      </el-descriptions-item>
      <el-descriptions-item
        v-if="data.regLocation !== undefined"
        label="注册地址"
        :span="2"
        >{{ data.regLocation }}</el-descriptions-item
      >
      <el-descriptions-item
        v-if="data.businessScope !== undefined"
        label="经营范围"
        :span="3"
        >{{ data.businessScope }}</el-descriptions-item
      >
    </el-descriptions>
    <div
      v-if="isRealName && data.businessLicenceAttachId"
      class="btn-container"
    >
      <el-button
        :loading="viewCertBtnLoading"
        size="small"
        @click="handleViewLicense"
        >营业执照</el-button
      >
    </div>
  </div>
</template>

<script>
import { requestBaseDataPicByAttachId } from '@/api/customer/archives/archive'

export default {
  name: 'ArchivesBaseDataBusinessIndex',
  props: {
    data: {
      type: Object,
      required: true,
    },
    isRealName: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      viewCertBtnLoading: false,
    }
  },
  methods: {
    handleViewLicense() {
      this.viewCertBtnLoading = true
      requestBaseDataPicByAttachId({
        attachId: this.data.businessLicenceAttachId,
      })
        .then(({ data }) => {
          data = data.data || []
          const imgSrcArr = []
          for (const item of data) {
            imgSrcArr.push({ url: item })
          }
          this.viewCertBtnLoading = false
          this.$ImagePreview(imgSrcArr, 0, { closeOnClickModal: false })
        })
        .catch(() => {
          this.viewCertBtnLoading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.business-container {
  .btn-container {
    margin-top: 14px;
    text-align: right;
  }
}
</style>
