<template>
  <div class="base-data-container">
    <LayoutCard v-if="this.type !== 'person'" title="工商信息">
      <Business
        v-loading="loading"
        :data="companyBasicInfo"
        :isRealName="isRealName"
      />
    </LayoutCard>
    <LayoutCard :title="this.type === 'person' ? '借款人信息' : '法人信息'">
      <Personnel
        v-loading="loading"
        :data="customerInfo"
        :isRealName="isRealName"
      />
    </LayoutCard>
    <LayoutCard v-if="this.type !== 'person'" title="股权结构">
      <Equity v-loading="loading" :data="supplierOwnerShipStructList" />
    </LayoutCard>
    <div v-if="this.type !== 'person'" class="button-container">
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        plain
        @click="handleRefreshBtn"
        >刷新数据</el-button
      >
    </div>
    <CommonDialog
      ref="refreshDialog"
      title="是否确定刷新数据？"
      class="archives-refresh-dialog"
      noBorder
      :enableFullScreenBtn="false"
      center
      width="40%"
      :cancelDisable="dialogLoading"
      :confirmLoading="dialogLoading"
      @cancel="handleRefreshCancel"
      @confirm="handleRefreshConfirm"
    >
      <div class="archives-refresh-wrapper">
        <span>每次【刷新数据】都会使用天眼查-工商信息接口，1元/次</span>
      </div>
    </CommonDialog>
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import Business from './components/Business/index.vue'
import Personnel from './components/Personnel/index.vue'
import Equity from './components/Equity/index.vue'
import CommonDialog from '../CommonDialog/index.vue'
import {
  requestBaseDataPersonInfo,
  requestBaseDataBusinessInfo,
  requestRefreshBaseDataBusinessInfo,
} from '@/api/customer/archives/archive'

export default {
  name: 'CustomerModuleBaseDataIndex',
  components: {
    LayoutCard,
    Business,
    Personnel,
    Equity,
    CommonDialog,
  },
  props: {
    unifiedSocialCode: {
      type: String,
      default: undefined,
    },
    companyName: {
      type: String,
      default: undefined,
    },
    companyId: {
      type: String,
      default: undefined,
    },
    businessLicenceAttachId: {
      type: String,
      default: undefined,
    },
    corporationFaceAttachId: {
      type: String,
      default: undefined,
    },
    corporationBackAttachId: {
      type: String,
      default: undefined,
    },
    isRealName: {
      type: Boolean,
      default: false,
    },
  },
  inject: ['type'],
  data() {
    return {
      loading: true,
      alreadyLoad: false,
      // 工商信息
      companyBasicInfo: {},
      // 股权结构
      supplierOwnerShipStructList: {},
      // 人员信息
      customerInfo: {},
      // 确认刷新弹窗加载
      dialogLoading: false,
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    initData() {
      this.loading = true
      if (this.type === 'person') {
        requestBaseDataPersonInfo({
          companyId: this.companyId,
        })
          .then(({ data }) => {
            if (data.success) {
              data = data.data || {}

              // 处理、过滤数据
              const customerInfo = {
                name: '--',
                identity: '--',
                account: '--',
                sex: '--',
                identityEffective: '--',
                nation: '--',
                address: '--',
                xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx: '--',
                ...data,
              }
              for (const key in customerInfo) {
                if (
                  customerInfo[key] === null ||
                  customerInfo[key] === undefined
                ) {
                  customerInfo[key] = '--'
                }
              }

              this.customerInfo = customerInfo
              this.loading = false
            }
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        requestBaseDataBusinessInfo({
          keyword: this.unifiedSocialCode || this.companyName,
          companyId: this.companyId,
        })
          .then(({ data }) => {
            if (data.success) {
              data = data.data

              // 处理工商信息
              data.companyBasicInfo = data.companyBasicInfo || {}
              const companyBasicInfo = {
                legalPersonName: '--',
                phoneNumber: '--',
                estiblishTime: '--',
                creditCode: '--',
                orgNumber: '--',
                approvedTime: '--',
                regNumber: '--',
                totalAssets: '--',
                regDate: '--',
                regCapital: '--',
                paidAmount: '--',
                regInstitute: '--',
                companyOrgType: '--',
                industry: '--',
                employeeNum: '--',
                medicalInsurance: '--',
                regLocation: '--',
                businessScope: '--',
                // businessLicenceAttachId: data.customerInfo
                //   ? data.customerInfo.businessLicenceAttachId
                //   : undefined,s
                ...data.companyBasicInfo,
                businessLicenceAttachId: this.businessLicenceAttachId,
              }
              for (const key in companyBasicInfo) {
                if (
                  companyBasicInfo[key] === null ||
                  companyBasicInfo[key] === undefined
                ) {
                  companyBasicInfo[key] = '--'
                }
              }
              // 处理人员信息
              data.customerInfo = data.customerInfo || {}
              const customerInfo = {
                corporationName: '--',
                corporationIdType: '--',
                corporationIdCardNumber: '--',
                mobile: '--',
                corporationSex: '--',
                corporationValidTime: '--',
                corporationNation: '--',
                corporationCountry: '--',
                corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx: '--',
                corporationAddress: '--',
                operatorName: '--',
                operatorXXXXXXXXXXXXXX: '--',
                operatorIdcard: '--',
                operatorPhone: '--',
                operatorSex: '--',
                operatorValidTime: '--',
                operatorNation: '--',
                operatorCountry: '--',
                operatorAddress: '--',
                ...data.customerInfo,
                corporationFaceAttachId: this.corporationFaceAttachId,
                corporationBackAttachId: this.corporationBackAttachId,
              }
              for (const key in customerInfo) {
                if (
                  customerInfo[key] === null ||
                  customerInfo[key] === undefined
                ) {
                  customerInfo[key] = '--'
                }
              }

              this.supplierOwnerShipStructList =
                data.supplierOwnerShipStructList
              this.companyBasicInfo = companyBasicInfo
              this.customerInfo = customerInfo
              this.loading = false
            }
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    handleRefreshBtn() {
      this.$refs.refreshDialog.handleOpen()
    },
    handleRefreshCancel() {
      this.$refs.refreshDialog.handleClose()
    },
    handleRefreshConfirm() {
      this.dialogLoading = true
      requestRefreshBaseDataBusinessInfo({
        keyword: this.unifiedSocialCode || this.companyName,
      })
        .then(({ data }) => {
          this.dialogLoading = false
          if (data.success) {
            this.$message.success('操作成功')
            this.$refs.refreshDialog.handleClose()
            this.initData()
          }
        })
        .catch(() => {
          this.dialogLoading = false
        })
    },
  },
}
</script>

<style lang="scss">
.button-container {
  text-align: right;
}

.archives-refresh-dialog {
  .archives-refresh-wrapper {
    padding: 24px;
  }
}
</style>
