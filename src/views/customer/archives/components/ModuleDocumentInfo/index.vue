<template>
  <div class="doc-info-container">
    <LayoutCard title="证件资料">
      <div class="head-container">
        <div class="label-bar-container">
          <el-radio-group v-model="radio" @change="handleChange">
            <el-radio-button
              v-for="item of labelList"
              :key="item"
              :label="item"
            ></el-radio-button>
          </el-radio-group>
        </div>
        <el-button @click="handleUpload">上传证件</el-button>
      </div>
      <div v-loading="loading" class="table-container">
        <el-table
          class="negative-factor-table"
          :data="tableData"
          border
          :header-cell-style="
            () => {
              return { backgroundColor: '#f7f7f7', color: '#000' }
            }
          "
        >
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column prop="type" label="证件类型">
            <template slot-scope="{ row }">
              {{ typeMap[row.type] || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="fileType" label="证件格式" width="108" />
          <el-table-column prop="updateTime" label="上传时间" />
          <el-table-column prop="status" label="状态">
            <template slot-scope="{ row }">
              <Tag
                :name="statusMap[row.status].name"
                :color="statusMap[row.status].color"
                :backgroundColor="statusMap[row.status].backgroundColor"
                :borderColor="statusMap[row.status].borderColor"
                minWidth="64px"
              />
            </template>
          </el-table-column>
          <el-table-column prop="options" label="操作">
            <template slot-scope="{ row }">
              <el-button
                :loading="tableLoadingBtnMap.downId === row.id"
                @click="handleBtnDown(row)"
                type="text"
                size="small"
                >下载</el-button
              >
              <el-button
                :loading="tableLoadingBtnMap.preViewId === row.id"
                @click="handleBtnPreView(row)"
                type="text"
                size="small"
                >预览</el-button
              >
              <el-button
                :loading="tableLoadingBtnMap.invalidId === row.id"
                @click="handleBtnInvalid(row)"
                type="text"
                size="small"
                >作废</el-button
              >
              <el-button
                :loading="tableLoadingBtnMap.delId === row.id"
                @click="handleBtnDel(row)"
                type="text"
                size="small"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          hide-on-single-page
          @current-change="handleCurrentChange"
          :current-page.sync="paginationData.currentPage"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
        >
        </el-pagination>
      </div>
    </LayoutCard>
    <UploadFile ref="uploadFile" :personalName="personalName" />
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import UploadFile from './components/UploadFile/index.vue'
import FilePreview from '@/components/file-preview/index.vue'
import Tag from '../Tag/index.vue'
import {
  requestDocInfo,
  requestDocTypeMap,
  requestCertificateUrlById,
  requestCertificateUrlByIds,
  requestInvalidCertificate,
  requestDelCertificate,
} from '@/api/customer/archives/archive'
import { statusMap } from './config/index'

const initPaginationData = () => ({
  pageSize: 10,
  currentPage: 1,
  nextPage: 2,
  maxPage: undefined,
  total: 0,
})

export default {
  name: 'CustomerModuleDocumentInfoIndex',
  components: { LayoutCard, UploadFile, FilePreview, Tag },
  props: {
    companyId: {
      type: String,
      default: undefined,
    },
    companyName: {
      type: String,
      default: undefined,
    },
    // 临时修改，融资个体户没有企业名称，以个人名称使用
    personalName: {
      type: String,
      default: undefined,
    },
    unifiedSocialCode: {
      type: String,
      default: undefined,
    },
  },
  inject: ['type'],
  data() {
    return {
      loading: true,
      alreadyLoad: false,
      tableLoadingBtnMap: {
        downId: undefined,
        preViewId: undefined,
        invalidId: undefined,
        delId: undefined,
      },
      radio: '1',
      labelList: ['1', '2', '3'],
      paginationData: initPaginationData(),
      tableData: [],
      typeMapArr: [],
      typeMap: {},
      pdfSrc: '',
      statusMap: statusMap,
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    const currentYear = new Date().getFullYear()
    this.radio = currentYear
    this.labelList = [currentYear, currentYear - 1, currentYear - 2]
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    handleChange() {
      this.paginationData = initPaginationData()
      this.initData()
    },
    initData() {
      this.loading = true
      this.tableData = []
      this.requestData()
      this.requestTypeMap()
    },
    requestData() {
      const { currentPage, pageSize } = this.paginationData
      const request = {
        companyName: this.companyName || this.personalName,
        companyCreditCode: this.unifiedSocialCode,
        year: this.radio,
        current: currentPage,
        size: pageSize,
      }
      requestDocInfo(request)
        .then(({ data }) => {
          this.loading = false
          if (data.success) {
            data = data.data
            const records = data.records || []
            this.tableData = records
            this.paginationData = {
              ...this.paginationData,
              currentPage: currentPage,
              nextPage: currentPage + 1,
              maxPage: data.pages,
              total: data.total,
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    requestTypeMap() {
      requestDocTypeMap()
        .then(({ data = [] }) => {
          const typeMapObj = {}
          for (const item of data) {
            typeMapObj[item.id] = item.name
          }
          this.typeMapArr = data
          this.typeMap = {
            ...typeMapObj,
            null: '未知',
            undefined: '未知',
          }
        })
        .catch(() => {})
    },
    handleUpload() {
      this.$refs.uploadFile.handleOpen()
    },
    handleCurrentChange(currentPage) {
      this.loading = true
      this.paginationData.currentPage = currentPage
      this.requestData()
    },
    handleBtnDown(row) {
      if (!this.checkAllowTableBtn()) return
      this.tableLoadingBtnMap.downId = row.id
      if (row.attachId.indexOf(',') === -1) {
        requestCertificateUrlById({ id: row.id })
          .then(({ data }) => {
            // 以下后台多重嵌套，可能优化接口后导致报错
            const { name, url } = data.data.data
            this.tableLoadingBtnMap.downId = undefined
            const a = document.createElement('a')
            a.href = url
            a.download = name
            a.style.display = 'none'
            document.body.append(a)
            a.click()
            document.body.removeChild(a)
          })
          .catch(() => {
            this.tableLoadingBtnMap.downId = undefined
          })
      } else {
        requestCertificateUrlByIds({ ids: row.attachId })
          .then(({ data }) => {
            this.tableLoadingBtnMap.downId = undefined
            // 多个
            this.$message('暂不支持多个附件')
          })
          .catch(() => {
            this.tableLoadingBtnMap.downId = undefined
          })
      }
    },
    handleBtnPreView(row) {
      if (!this.checkAllowTableBtn()) return
      this.tableLoadingBtnMap.preViewId = row.id
      if (row.attachId.indexOf(',') === -1) {
        requestCertificateUrlById({ id: row.id })
          .then(({ data }) => {
            this.tableLoadingBtnMap.preViewId = undefined
            // 以下后台多重嵌套，可能优化接口后导致报错
            const { url } = data.data.data
            const targetUrl = url
            if (targetUrl.endsWith('.pdf')) {
              this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
            } else {
              const imgSrcArr = []
              imgSrcArr.push({ url: targetUrl })
              this.$ImagePreview(imgSrcArr, 0, {
                closeOnClickModal: true,
              })
            }
          })
          .catch(() => {
            this.tableLoadingBtnMap.preViewId = undefined
          })
      } else {
        requestCertificateUrlByIds({ ids: row.attachId })
          .then(({ data }) => {
            this.tableLoadingBtnMap.preViewId = undefined
            // 多个
            this.$message('暂不支持多个附件')
          })
          .catch(() => {
            this.tableLoadingBtnMap.preViewId = undefined
          })
      }
    },
    handleBtnInvalid(row) {
      if (!this.checkAllowTableBtn()) return
      this.tableLoadingBtnMap.invalidId = row.id
      requestInvalidCertificate({ id: row.id })
        .then(({ data }) => {
          this.tableLoadingBtnMap.invalidId = undefined
          if (data.success) {
            this.$message.success('操作成功')
            this.loading = true
            this.requestData()
          }
        })
        .catch(() => {
          this.tableLoadingBtnMap.invalidId = undefined
        })
    },
    handleBtnDel(row) {
      if (!this.checkAllowTableBtn()) return
      this.tableLoadingBtnMap.delId = row.id
      requestDelCertificate({ ids: row.id })
        .then(({ data }) => {
          this.tableLoadingBtnMap.delId = undefined
          if (data.success) {
            this.$message.success('删除成功')
            this.loading = true
            this.requestData()
          }
        })
        .catch(() => {
          this.tableLoadingBtnMap.delId = undefined
        })
    },
    checkAllowTableBtn() {
      for (const key in this.tableLoadingBtnMap) {
        if (this.tableLoadingBtnMap[key] !== undefined) return false
      }
      return true
    },
  },
}
</script>

<style lang="scss" scoped>
.head-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .label-bar-container {
    .el-radio-group {
      .el-radio-button:first-child {
        border-radius: 100px 0 0 100px !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 100px 0 0 100px !important;
          }
        }
      }

      .el-radio-button:last-child {
        border-radius: 0 100px 100px 0 !important;

        ::v-deep {
          .el-radio-button__inner {
            border-radius: 0 100px 100px 0 !important;
          }
        }
      }

      .el-radio-button {
        height: 38px;

        ::v-deep {
          .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            background-color: #0f100f;
            border-color: #0f100f;
          }
        }
      }
    }
  }

  .el-button {
    height: 38px;
    padding: 0 20px;
    border-radius: 32px;
  }
}

.table-container {
  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 18px;
  }
}
</style>
