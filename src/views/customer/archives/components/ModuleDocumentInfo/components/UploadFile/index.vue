<template>
  <Dialog
    ref="uploadFile"
    title="上传证件"
    center
    height="80vh"
    :cancelDisable="cancelDisable"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
  >
    <div class="add-file-container">
      <div class="card-list">
        <Item
          v-for="(item, index) of cardList"
          :data="item"
          :index="index"
          :checkAccumulator="formCheckAccumulator"
          :key="item.key"
          :typeMap="typeMap"
          @updateFormData="handleUpdateFormData"
          @deleteForm="handleDeleteForm(index)"
          @validatePass="validatePass"
        ></Item>
      </div>
      <div class="add-btn" @click="handleAdd">
        <SvgIcon icon-class="icon-line-jia" style="font-size: 16px" />
        <span class="btn-name">新增资料</span>
      </div>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import Item from './Item.vue'
import {
  requestDocTypeMap,
  requestUploadCertificate,
} from '@/api/customer/archives/archive'

const createInitCard = () => ({
  targetType: '',
  fileList: [],
  key: `${new Date().getTime()}${Math.ceil(Math.random() * 1000)}`,
})

const initCardList = () => [createInitCard()]

export default {
  name: 'CustomerModuleDocInfoUploadFileIndex',
  components: { Dialog, Item },
  inject: ['companyIdFunc', 'companyNameFunc', 'unifiedSocialCodeFunc'],
  props: {
    // 临时修改，融资个体户没有企业名称，以个人名称使用
    personalName: {
      type: String,
      default: undefined,
    },
  },
  data: function () {
    return {
      cardList: initCardList(),
      formCheckAccumulator: 0,
      passCount: 0,
      cancelDisable: false,
      confirmLoading: false,
      typeMap: [],
    }
  },
  created() {},
  methods: {
    initData() {
      requestDocTypeMap()
        .then(({ data = [] }) => {
          this.typeMap = data
        })
        .catch(() => {})
    },
    handleOpen() {
      this.cancelDisable = false
      this.confirmLoading = false
      this.cardList = initCardList()
      this.$refs.uploadFile.handleOpen()
      this.initData()
    },
    handleClose() {
      this.$refs.uploadFile.handleClose()
    },
    handleDeleteForm(index) {
      this.cardList.splice(index, 1)
    },
    handleAdd() {
      this.cardList.push(createInitCard())
    },
    handleCancel() {},
    handleConfirm() {
      if (this.cardList.length === 0) return
      this.passCount = 0
      this.formCheckAccumulator += 1
    },
    handleUpdateFormData(value, index) {
      this.cardList[index] = { ...this.cardList[index], ...value }
    },
    submitForm() {
      this.cancelDisable = true
      this.confirmLoading = true
      const idDuplicateCheck = {}
      const requestObj = []
      for (const item of this.cardList) {
        if (idDuplicateCheck[item.targetType]) {
          this.$message.error('含有重复的资料类型，请检查')
          this.cancelDisable = false
          this.confirmLoading = false
          return
        } else {
          idDuplicateCheck[item.targetType] = true
          let attachId = ''
          for (const item of item.fileList) {
            if (attachId === '') {
              attachId = item.attachId
            } else {
              attachId = attachId + ',' + item.attachId
            }
          }
          let targetFileType = ''
          for (const typeMapItem of this.typeMap) {
            if (item.targetType === typeMapItem.id) {
              targetFileType = typeMapItem.name
              break
            }
          }
          requestObj.push({
            fileType: targetFileType,
            type: item.targetType,
            attachId,
            companyName: this.companyNameFunc() || this.personalName,
            customerId: this.companyIdFunc(),
            companyCreditCode: this.unifiedSocialCodeFunc(),
          })
        }
      }
      requestUploadCertificate(requestObj)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('提交成功')
            this.handleClose()
            this.$parent.requestData()
          }
        })
        .catch(() => {
          this.cancelDisable = false
          this.confirmLoading = false
        })
    },
    validatePass() {
      this.passCount++
      if (this.passCount === this.cardList.length) {
        this.submitForm()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.add-file-container {
  padding: 24px;

  .add-btn {
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    border: 1px dashed rgba(155, 205, 255, 100);
    color: #697cff;
    cursor: pointer;

    .btn-name {
      margin-left: 4px;
      font-size: 16px;
    }
  }
}
</style>
