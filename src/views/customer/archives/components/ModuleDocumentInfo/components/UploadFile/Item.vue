<template>
  <div class="card-item-container">
    <el-popconfirm title="确定删除该条记录吗？" @confirm="handleDelete">
      <SvgIcon
        slot="reference"
        class="card-close-btn"
        icon-class="icon-line-guanbi"
        style="font-size: 16px"
      />
    </el-popconfirm>
    <el-form
      :model="formData"
      :rules="rules"
      ref="form"
      label-width="120px"
      class="form-container"
    >
      <el-form-item label="资料类型：" prop="targetType">
        <el-select
          v-model="formData.targetType"
          filterable
          placeholder="选择资料类型"
        >
          <el-option
            v-for="item in typeMap"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传文件：" prop="fileList">
        <div class="file-upload-container">
          <el-upload
            class="file-upload"
            drag
            action="/api/blade-resource/oss/endpoint/put-file-kv"
            multiple
            :on-success="handleUpSuccess"
            :on-remove="handleFileRemove"
            :on-preview="handleFilePreview"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              只能上传jpg/jpeg/png/pdf文件，且不超过20M
            </div>
          </el-upload>
          <el-button class="view-demo" size="mini">查看示例</el-button>
        </div>
      </el-form-item>
    </el-form>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import FilePreview from '@/components/file-preview/index.vue'

const rules = {
  targetType: [
    { required: true, message: '请选择资料类型', trigger: ['blur', 'change'] },
  ],
  fileList: [
    {
      type: 'array',
      required: true,
      message: '请上传文件',
      trigger: ['blur', 'change'],
    },
  ],
}

export default {
  name: 'CustomerModuleDocInfoNewItem',
  components: { FilePreview },
  props: {
    data: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    checkAccumulator: {
      type: Number,
      required: true,
    },
    typeMap: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      rules,
      formData: {
        targetType: '',
        fileList: [],
      },
      pdfSrc: '',
    }
  },
  watch: {
    checkAccumulator: {
      handler() {
        this.$refs.form
          .validate()
          .then(() => {
            this.$emit('validatePass')
          })
          .catch(() => {})
      },
    },
    formData: {
      handler(newValue) {
        this.$emit('updateFormData', newValue, this.index)
      },
      deep: true, //为true，表示深度监听，这时候就能监测到a值变化
    },
  },
  methods: {
    handleDelete() {
      this.$emit('deleteForm')
    },
    handleUpSuccess(response, file, fileList) {
      for (const item of fileList) {
        const { data = {} } = item.response
        item.name = data.name
        item.url = data.url
        item.attachId = data.attachId
      }
      this.formData.fileList = fileList
    },
    handleFileRemove(file, fileList) {
      this.formData.fileList = fileList
    },
    handleFilePreview(file) {
      const targetUrl = file.url
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.card-item-container {
  position: relative;
  padding: 24px 32px;
  margin-bottom: 20px;
  border-radius: 8px;
  background-color: #f7f7f7;

  .card-close-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 16px;
    fill: #9f9f9f;

    &:hover {
      cursor: pointer;
      fill: #3f3f3f;
    }
  }

  .form-container {
    .el-form-item {
      &:last-child {
        margin-bottom: 0;
      }

      .el-select {
        display: block;
      }
    }
  }

  .file-upload {
    display: inline-block;

    .el-upload__tip {
      line-height: 1;
    }

    ::v-deep {
      .el-upload-list {
        background-color: #fff;
        border-radius: 8px;
      }
    }
  }

  .file-upload-container {
    display: flex;
    align-items: start;

    .view-demo {
      margin-left: 22px;
    }
  }
}
</style>
