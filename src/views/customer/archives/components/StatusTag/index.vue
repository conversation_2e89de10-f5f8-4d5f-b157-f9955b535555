<template>
  <div
    v-if="status == 0"
    class="status-tag"
    :style="{ backgroundColor: '#409EFF', color: '#fff' }"
  >
    <SvgIcon
      class="status-icon-class"
      icon-class="icon-line-dengdai"
      style="fill: #409eff"
    />
    <span class="name">待实名</span>
  </div>
  <div
    v-else-if="status == 1"
    class="status-tag"
    :style="{ backgroundColor: '#3DC861', color: '#fff' }"
  >
    <SvgIcon
      class="status-icon-class"
      icon-class="icon-line-gouxuan"
      style="fill: #3dc861"
    />
    <span class="name">已实名</span>
  </div>
</template>

<script>
export default {
  name: 'CustomerStatusTagIndex',
  props: {
    status: {
      type: Number,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 8px 14px;
  border-radius: 100px;

  .status-icon-class {
    font-size: 20px;
    margin-right: 4px;
    background-color: #fff;
    border-radius: 50%;
  }

  .name {
    font-size: 16px;
    line-height: 20px;
  }
}
</style>
