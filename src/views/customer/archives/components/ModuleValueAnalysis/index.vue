<template>
  <div class="trade-container">
    <LayoutCard title="经营数据">
      <div v-loading="loading">
        <div class="card-list">
          <SimpleInfoCard
            :value="
              detail.totalTaxLastYear !== undefined
                ? Number(detail.totalTaxLastYear).toLocaleString()
                : '--'
            "
            label="万元"
            desc="去年纳税销售总额"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.totalTaxPreviousYear !== undefined
                ? Number(detail.totalTaxPreviousYear).toLocaleString()
                : '--'
            "
            label="万元"
            desc="前年纳税销售总额"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.netProfit !== undefined
                ? Number(detail.netProfit).toLocaleString()
                : '--'
            "
            label="万元"
            desc="净利润"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.totalTaxPayableLastYear !== undefined
                ? Number(detail.totalTaxPayableLastYear).toLocaleString()
                : '--'
            "
            label="万元"
            desc="去年应纳税总额"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.totalTaxPayablePreviousYear !== undefined
                ? Number(detail.totalTaxPayablePreviousYear).toLocaleString()
                : '--'
            "
            label="万元"
            desc="前年应纳税总额"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="detail.yearGrowth || '--'"
            label="%"
            desc="同比销售增长率"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="detail.annulusGrowth || '--'"
            label="%"
            desc="环比销售增长率"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.totalAccountsReceivable !== undefined
                ? Number(detail.totalAccountsReceivable).toLocaleString()
                : '--'
            "
            label="万元"
            desc="应收账款总额"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.totalStill !== undefined
                ? Number(detail.totalStill).toLocaleString()
                : '--'
            "
            label="万元"
            desc="应还账款总额"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.collectionRate !== undefined
                ? Number(detail.collectionRate).toLocaleString() || '--'
                : '--'
            "
            label="%"
            desc="回款率"
            color="#697CFF"
          />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
        </div>
        <div
          v-if="detail.financeForm"
          class="button-container"
          style="margin-top: 24px"
        >
          <el-button size="small" @click="handleView">查看财务报表</el-button>
        </div>
      </div>
    </LayoutCard>
    <div class="button-container">
      <el-button type="primary" size="small" plain @click="handleEdit"
        >编辑</el-button
      >
    </div>
    <Dialog
      ref="editDialog"
      title="编辑经营数据"
      center
      noButton
      width="1000px"
      height="80vh"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    >
      <div class="form-wrapper">
        <avue-form :option="option" v-model="form" @submit="handleSubmit" />
      </div>
    </Dialog>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import SimpleInfoCard from '../SimpleInfoCard/index.vue'
import Dialog from '../CommonDialog/index.vue'
import {
  requestArchiveValueAnalysis,
  requestUpdateArchiveValueAnalysis,
} from '@/api/customer/archives/archive'
import FilePreview from '@/components/file-preview/index.vue'

export default {
  name: 'CustomerModuleValueAnalysisIndex',
  components: { LayoutCard, SimpleInfoCard, Dialog, FilePreview },
  props: {
    companyId: {
      type: String,
      default: undefined,
    },
  },
  inject: ['type'],
  data() {
    const validateNumber = (rule, value, callback) => {
      const reg = /(^[1-9]{1}[0-9]*$)|(^[0-9]*\.[0-9]{4}$)/
      if (!reg.test(value)) {
        callback(new Error('请输入正确的数字，允许四位小数'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      alreadyLoad: false,
      detail: {},
      form: {},
      option: {
        emptyBtn: false,
        labelWidth: '180px',
        column: [
          {
            label: '去年纳税销售总额',
            prop: 'totalTaxLastYear',
            append: '万元',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入去年纳税销售总额',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '前年纳税销售总额',
            prop: 'totalTaxPreviousYear',
            append: '万元',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入前年纳税销售总额',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '净利润',
            prop: 'netProfit',
            append: '万元',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入净利润',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '去年应纳税总额',
            prop: 'totalTaxPayableLastYear',
            append: '万元',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入去年应纳税总额',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '前年应纳税总额',
            prop: 'totalTaxPayablePreviousYear',
            append: '万元',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入前年应纳税总额',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '同比销售增长率',
            prop: 'yearGrowth',
            append: '%',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入同比销售增长率',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '环比销售增长率',
            prop: 'annulusGrowth',
            append: '%',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入环比销售增长率',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '应收账款总额',
            prop: 'totalAccountsReceivable',
            append: '万元',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入应收账款总额',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '应还账款总额',
            prop: 'totalStill',
            append: '万元',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入应还账款总额',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '回款率',
            prop: 'collectionRate',
            append: '%',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入回款率',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '财务报表',
            prop: 'financeForm',
            type: 'upload',
            loadText: '附件上传中，请稍等',
            span: 24,
            limit: 1,
            headers: {
              timeout: 60000,
            },
            propsHttp: {
              res: 'data',
            },
            rules: [
              {
                required: true,
                message: '请上传报表',
                trigger: 'blur',
              },
            ],
            uploadBefore: (file, done, loading) => {
              this.form.financeForm = []
              var first = file.name.lastIndexOf('.')
              const type = file.name.substring(first + 1, file.length)

              if (['jpg', 'jpeg', 'png', 'pdf'].includes(type)) {
                const isLt20M = file.size / 1024 / 1024 > 20
                if (isLt20M) {
                  loading()
                  this.$message.error('文件大小不能超过20M')
                  return
                }
                done()
              } else {
                loading()
                this.$message.error('文件格式错误')
                return
              }
            },
            uploadPreview: (file, column, done) => {
              var fileName = file.url
              if (
                fileName.endsWith('.jpg') ||
                fileName.endsWith('.jpeg') ||
                fileName.endsWith('.png') ||
                fileName.endsWith('.gif')
              ) {
                done()
              }
              if (fileName.endsWith('.pdf')) {
                this.pdfSrc = file.url + '?time=' + new Date().getMilliseconds()
              }
            },
            tip: '只能上传1张jpg/png/gif/pdf文件，且不超过20M',
            action: '/api/blade-resource/oss/endpoint/put-file-kv',
          },
        ],
      },
      pdfSrc: '',
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    initData() {
      this.loading = true
      requestArchiveValueAnalysis({ companyId: this.companyId })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.detail = data || {}
            this.loading = false
          }
        })
        .catch(() => {})
    },
    handleView() {
      const targetUrl = this.detail.financeForm
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    handleEdit() {
      this.form = {}
      this.$refs.editDialog.handleOpen()
    },
    handleSubmit(form, done) {
      if (form.financeForm && form.financeForm[0]) {
        form.financeForm = form.financeForm[0].value
      } else {
        form.financeForm = ''
      }
      const request = {
        ...form,
        // 接口变更，该 ID 现在不用传，然后现在需要传
        id: this.detail.id,
        companyId: this.companyId,
      }
      requestUpdateArchiveValueAnalysis(request)
        .then(() => {
          this.$refs.editDialog.handleClose()
          this.initData()
          done()
        })
        .catch(() => {
          done()
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.trade-container {
  .header {
    display: flex;
    margin-bottom: 20px;
    align-items: center;

    .sign {
      display: inline-block;
      width: 8px;
      height: 16px;
      margin-right: 4px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
    }
  }

  .card-list {
    display: flex;
    flex-wrap: wrap;
    margin: -10px -12px;

    > * {
      flex: 1;
      margin: 10px 12px;
    }
  }

  .button-container {
    text-align: right;
  }
}

.form-wrapper {
  padding: 24px 24px 12px;
}
</style>
