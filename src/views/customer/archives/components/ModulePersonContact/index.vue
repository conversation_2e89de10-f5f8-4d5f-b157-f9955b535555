<template>
  <div class="person-contact-container">
    <LayoutCard title="联系人列表">
      <template v-if="loading">
        <div v-loading="loading" style="min-height: 100px" />
      </template>
      <template v-else>
        <template v-if="!listData || listData.length === 0">
          <el-empty description="暂无数据" />
        </template>
        <template v-else>
          <div class="card-list">
            <ContactCard
              v-for="item of listData"
              :key="item.id"
              :name="item.contactsName || '--'"
              :tagName="
                tagNameMap.ready && item.contactsType !== null
                  ? tagNameMap[item.contactsType]
                  : '--'
              "
              :phone="item.contactsPhone || '--'"
              :date="item.createTime || '--'"
            />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
            <ContactCard empty />
          </div>
        </template>
      </template>
    </LayoutCard>
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import ContactCard from './components/ContactCard/index.vue'
import { requestPersonContactList } from '@/api/customer/archives/person'
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'CustomerModuleCreditInformationIndex',
  components: { LayoutCard, ContactCard },
  props: {
    customerId: {
      type: [String, undefined],
      default: undefined,
    },
  },
  inject: ['type'],
  data() {
    return {
      loading: false,
      alreadyLoad: false,
      listData: [],
      // 字典
      tagNameMap: {},
    }
  },
  watch: {
    customerId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.customerId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    initData() {
      this.loading = true
      const requestObj = {
        customerId: this.customerId,
      }
      requestPersonContactList(requestObj)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.listData = data || []
            this.loading = false
          }
        })
        .catch(() => {})
      // 字典
      getDictionary({ code: 'jrzh_customer_front_financing_contacts' })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            const tagNameMap = {}
            for (const item of data) {
              tagNameMap[item.dictKey] = item.dictValue
            }
            tagNameMap.ready = true
            this.tagNameMap = tagNameMap
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.person-contact-container {
  .card-list {
    display: flex;
    flex-wrap: wrap;
    margin: -10px -12px;

    > * {
      flex: 1;
      margin: 10px 12px;
    }
  }
}
</style>
