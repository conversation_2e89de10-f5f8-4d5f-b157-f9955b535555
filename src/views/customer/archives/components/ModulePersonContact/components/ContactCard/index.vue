<template>
  <div class="contact-card" :class="{ empty }">
    <template v-if="!empty">
      <div class="name-container">
        <span class="name">{{ name }}</span>
        <Tag :name="tagName" color="#00072A" borderColor="#646464" radius style="padding: 4px 12px" />
      </div>
      <span class="phone-container item-value">{{ phone }}</span>
      <span class="date-container item-value">{{ date }}</span>
    </template>
  </div>
</template>

<script>
import Tag from '../../../Tag/index.vue'

export default {
  name: 'CustomerArchivesSimpleInfoCardIndex',
  components: {
    Tag,
  },
  props: {
    name: {
      tyle: String,
      request: true,
    },
    tagName: {
      type: String,
      request: true,
    },
    phone: {
      tyle: String,
      request: true,
    },
    date: {
      type: String,
      default: '',
    },
    empty: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.contact-card {
  display: inline-block;
  min-width: 240px;
  padding: 16px 16px 22px;
  border-radius: 8px;
  background-color: rgba(247, 247, 247, 100);

  &.empty {
    height: 0;
    padding: 0 16px;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .name-container {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .name {
      margin-right: 8px;
      color: rgba(0, 7, 42, 100);
      font-size: 16px;
      font-weight: 500;
      font-family: SourceHanSansSC-bold;
      text-align: left;
      line-height: 16px;
    }
  }

  .item-value {
    display: inline-block;
    width: 100%;
    margin-bottom: 16px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
    font-family: SourceHanSansSC-regular;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
