<template>
  <div class="trade-container">
    <LayoutCard title="征信信息">
      <div v-loading="loading">
        <div class="card-list">
          <SimpleInfoCard
            :value="
              detail.currentConcerns !== undefined
                ? detail.currentConcerns
                : '--'
            "
            label="笔"
            desc="当前关注"
            color="#826F00"
          />
          <SimpleInfoCard
            :value="
              detail.currentTimes !== undefined ? detail.currentTimes : '--'
            "
            label="笔"
            desc="当前次级"
            color="#826F00"
          />
          <SimpleInfoCard
            :value="
              detail.currentSuspicious !== undefined
                ? detail.currentSuspicious
                : '--'
            "
            label="笔"
            desc="当前可疑"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.currentLoss !== undefined ? detail.currentLoss : '--'
            "
            label="笔"
            desc="当前损失"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.currentOverdue !== undefined ? detail.currentOverdue : '--'
            "
            label="笔"
            desc="当前逾期"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.overdueMone !== undefined ? detail.overdueMone : '--'
            "
            label="笔"
            desc="近24个月逾期为M1"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.overdueMtwo !== undefined ? detail.overdueMtwo : '--'
            "
            label="笔"
            desc="近24个月逾期为M2"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.overdueMthree !== undefined ? detail.overdueMthree : '--'
            "
            label="笔"
            desc="近24个月逾期为M3"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.approveLetterQueryTwo !== undefined
                ? detail.approveLetterQueryTwo
                : '--'
            "
            label="笔"
            desc="近2个月审批、征信查询"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.approveLetterQueryTetracosa !== undefined
                ? detail.approveLetterQueryTetracosa
                : '--'
            "
            label="笔"
            desc="近12个月审批、征信查询"
            color="#826F00"
          />
          <SimpleInfoCard
            :value="
              detail.closedCases !== undefined ? detail.closedCases : '--'
            "
            label="次"
            desc="企业涉诉结案件"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="detail.openCases !== undefined ? detail.openCases : '--'"
            label="次"
            desc="企业涉诉未结案件"
            color="#697CFF"
          />
          <SimpleInfoCard
            :value="
              detail.overdueRate !== undefined ? detail.overdueRate : '--'
            "
            label="%"
            desc="逾期率"
            color="#826F00"
          />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
          <SimpleInfoCard empty />
        </div>
        <div
          v-if="detail.creditReport"
          class="button-container"
          style="margin-top: 24px"
        >
          <el-button size="small" @click="handleView">查看征信报告</el-button>
        </div>
      </div>
    </LayoutCard>
    <div class="button-container">
      <el-button type="primary" size="small" plain @click="handleEdit"
        >编辑</el-button
      >
    </div>
    <Dialog
      ref="editDialog"
      title="编辑征信信息"
      center
      noButton
      width="1000px"
      height="80vh"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    >
      <div class="form-wrapper">
        <avue-form :option="option" v-model="form" @submit="handleSubmit" />
      </div>
    </Dialog>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import LayoutCard from '../LayoutCard/index.vue'
import SimpleInfoCard from '../SimpleInfoCard/index.vue'
import Dialog from '../CommonDialog/index.vue'
import {
  requestArchiveCreditInformation,
  requestUpdateArchiveCreditInformation,
} from '@/api/customer/archives/archive'
import FilePreview from '@/components/file-preview/index.vue'

export default {
  name: 'CustomerModuleCreditInformationIndex',
  components: { LayoutCard, SimpleInfoCard, Dialog, FilePreview },
  props: {
    companyId: {
      type: String,
      default: undefined,
    },
  },
  inject: ['type'],
  data() {
    const validateNumber = (rule, value, callback) => {
      const reg = /(^[0-9]*$)/
      if (!reg.test(value)) {
        callback(new Error('请输入正确的数字'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      alreadyLoad: false,
      detail: {},
      form: {},
      option: {
        emptyBtn: false,
        labelWidth: '180px',
        column: [
          {
            label: '当前关注',
            prop: 'currentConcerns',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入当前关注',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '当前次级',
            prop: 'currentTimes',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入当前次级',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '当前可疑',
            prop: 'currentSuspicious',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入当前可疑',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '当前损失',
            prop: 'currentLoss',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入当前损失',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '当前逾期',
            prop: 'currentOverdue',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入当前逾期',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '近24个月逾期为M1',
            prop: 'overdueMone',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入近24个月逾期为M1',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '近24个月逾期为M2',
            prop: 'overdueMtwo',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入近24个月逾期为M2',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '近24个月逾期为M3',
            prop: 'overdueMthree',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入近24个月逾期为M3',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '近2个月审批、征信查询',
            prop: 'approveLetterQueryTwo',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入近2个月审批、征信查询',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '近12个月审批、征信查询',
            prop: 'approveLetterQueryTetracosa',
            append: '笔',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入近12个月审批、征信查询',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '企业涉诉结案件',
            prop: 'closedCases',
            append: '次',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入企业涉诉结案件',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '企业涉诉未结案件',
            prop: 'openCases',
            append: '次',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入企业涉诉未结案件',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '逾期率',
            prop: 'overdueRate',
            append: '次',
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请输入逾期率',
                trigger: 'blur',
              },
              {
                validator: validateNumber,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '查看征信报告',
            prop: 'creditReport',
            type: 'upload',
            loadText: '附件上传中，请稍等',
            span: 24,
            limit: 1,
            headers: {
              timeout: 60000,
            },
            propsHttp: {
              res: 'data',
            },
            rules: [
              {
                required: true,
                message: '请上传报表',
                trigger: 'blur',
              },
            ],
            uploadBefore: (file, done, loading) => {
              this.form.creditReport = []
              var first = file.name.lastIndexOf('.')
              const type = file.name.substring(first + 1, file.length)

              if (['jpg', 'jpeg', 'png', 'pdf'].includes(type)) {
                const isLt20M = file.size / 1024 / 1024 > 20
                if (isLt20M) {
                  loading()
                  this.$message.error('文件大小不能超过20M')
                  return
                }
                done()
              } else {
                loading()
                this.$message.error('文件格式错误')
                return
              }
            },
            uploadPreview: (file, column, done) => {
              var fileName = file.url
              if (
                fileName.endsWith('.jpg') ||
                fileName.endsWith('.jpeg') ||
                fileName.endsWith('.png') ||
                fileName.endsWith('.gif')
              ) {
                done()
              }
              if (fileName.endsWith('.pdf')) {
                this.pdfSrc = file.url + '?time=' + new Date().getMilliseconds()
              }
            },
            tip: '只能上传1张jpg/png/gif/pdf文件，且不超过20M',
            action: '/api/blade-resource/oss/endpoint/put-file-kv',
          },
        ],
      },
      pdfSrc: '',
    }
  },
  watch: {
    companyId(newVal) {
      if (
        !this.alreadyLoad &&
        Object.prototype.toString.call(newVal) === '[object String]'
      ) {
        this.initData()
      }
    },
  },
  created() {
    this.loading = true
    if (Object.prototype.toString.call(this.companyId) === '[object String]') {
      this.alreadyLoad = true
      this.initData()
    }
  },
  methods: {
    initData() {
      this.loading = true
      requestArchiveCreditInformation({ companyId: this.companyId })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.detail = data || {}
            this.loading = false
          }
        })
        .catch(() => {})
    },
    handleView() {
      const targetUrl = this.detail.creditReport
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    handleEdit() {
      this.form = {}
      this.$refs.editDialog.handleOpen()
    },
    handleSubmit(form, done) {
      if (form.creditReport && form.creditReport[0]) {
        form.creditReport = form.creditReport[0].value
      } else {
        form.creditReport = ''
      }
      const request = {
        ...form,
        // 接口变更，该 ID 现在不用传，然后现在需要传
        id: this.detail.id,
        companyId: this.companyId,
      }
      requestUpdateArchiveCreditInformation(request)
        .then(() => {
          this.$refs.editDialog.handleClose()
          this.initData()
          done()
        })
        .catch(() => {
          done()
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.trade-container {
  .header {
    display: flex;
    margin-bottom: 20px;
    align-items: center;

    .sign {
      display: inline-block;
      width: 8px;
      height: 16px;
      margin-right: 4px;
      border-radius: 15px;
      background-color: rgba(18, 119, 255, 100);
    }
  }

  .card-list {
    display: flex;
    flex-wrap: wrap;
    margin: -10px -12px;

    > * {
      flex: 1;
      margin: 10px 12px;
    }
  }

  .button-container {
    text-align: right;
  }
}

.form-wrapper {
  padding: 24px 24px 12px;
}
</style>
