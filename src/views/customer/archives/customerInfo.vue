<template>
  <div class="customer-container">
    <div class="customer-card-panel">
      <div class="header-container">
        <div class="user-container">
          <el-image
            class="avatar"
            style="width: 56px; height: 56px"
            :src="detail.logoSrc"
            :fit="contain"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <div class="info-container">
            <div class="title-container">
              <div class="title-content">
                <span class="title">{{
                  detail.name || detail.personalName || '--'
                }}</span>
                <el-rate
                  v-model="detail.comprehensiveScore"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                >
                </el-rate>
              </div>
              <div class="user-info-container">
                <template v-if="this.archiveType === 'person'">
                  <div class="user-item text">
                    <span class="label">用户名：</span>
                    <span class="value">{{ detail.accountUser || '--' }}</span>
                  </div>
                  <div class="user-item text">
                    <span class="label">手机号：</span>
                    <span class="value">{{
                      detail.personalPhone || '--'
                    }}</span>
                  </div>
                </template>
                <template v-else>
                  <div class="user-item text">
                    <span class="label">所属者：</span>
                    <span class="value">{{ detail.belonging || '--' }}</span>
                  </div>
                </template>
                <div class="user-item text">
                  <span class="label">客户经理：</span>
                  <span class="value">{{ detail.invitationName || '--' }}</span>
                </div>
              </div>
            </div>
            <div class="desc-container">
              <div class="tag-list">
                <Tag
                  v-if="isRealName"
                  name="已实名"
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  v-if="customerTagdicDataMap[detail.customerLabel]"
                  :name="customerTagdicDataMap[detail.customerLabel] || '--'"
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  v-if="clientType"
                  :name="clientType"
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  :name="
                    detail.customerScore
                      ? `${parseInt(detail.customerScore, 10)}分`
                      : '未评分'
                  "
                  :color="'#fff'"
                  :backgroundColor="'#444444'"
                  :radius="true"
                />
                <Tag
                  v-if="archiveType !== 'person' && detail.operationStatus"
                  :name="detail.operationStatus"
                  :color="'#4E9BFC'"
                  :backgroundColor="'#f2f2f2'"
                  :radius="true"
                />
                <Tag
                  v-if="archiveType !== 'person' && detail.id"
                  :name="detail.companyType || '规模未知'"
                  color="#4E9BFC"
                  backgroundColor="#f2f2f2"
                  :radius="true"
                />
              </div>
              <div class="date-container">
                <template>
                  <div class="access-date-wrapper">
                    <span class="text">{{ detail.lastVisitTime }}访问过</span>
                  </div>
                </template>
                <div class="open-date-wrapper">
                  <span class="text">{{ detail.createTime }}创建</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <CustomerDataPreview />

    <BasicFooter
      :btn-options="btnOptions"
      @click="handleClickEvent"
    ></BasicFooter>
  </div>
</template>

<script>
import BasicFooter from '@/components/basic-footer/index'
import Tag from './components/Tag/index.vue'
import CustomerDataPreview from './components/CustomerDataPreview/index.vue'
import { getDetail } from '@/api/customer/frontfinancinglist'
import {
  customerTagdicDataMap,
  enterpriseTypeDicDataMap,
  coreEnterpriseTypeDicDataMap,
} from './config'

const btnOptions = [
  {
    btnName: '返回',
    funName: 'Revert',
  },
  {
    btnName: '客户档案',
    funName: 'Archives',
    type: 'primary',
  },
]
export default {
  name: 'CustomerIndex',
  components: {
    Tag,
    CustomerDataPreview,
    BasicFooter,
  },
  data() {
    return {
      customerTagdicDataMap,
      coreEnterpriseTypeDicDataMap,
      enterpriseTypeDicDataMap,
      id: undefined,
      btnOptions,
      activeModule: undefined,
      detail: {},
      archiveType: undefined,
    }
  },
  computed: {
    isRealName() {
      return this.detail.customerStatus === 1
    },
    clientType() {
      return this.archiveType === 'coreEnterprise'
        ? coreEnterpriseTypeDicDataMap[this.detail.enterpriseType]
        : this.archiveType === 'person'
        ? '个人'
        : this.detail.customerStatus !== 1
        ? '--'
        : enterpriseTypeDicDataMap[this.detail.status]
    },
  },
  watch: {},
  provide() {
    return {
      id: this.$route.query.id,
      userId: this.$route.query.userId,
    }
  },
  created() {
    this.id = this.$route.query.id
    this.archiveType = this.$route.params.type
    if (this.id) {
      this.getDetail(this.id)
    }
  },
  methods: {
    // 查询融资个体户信息
    async getDetail(id) {
      const { data } = await getDetail(id)
      if (data.code === 200) {
        this.detail = { ...data.data }
      }
    },
    handleClickEvent(name) {
      switch (name) {
        case 'Revert':
          this.$router.$avueRouter.closeTag()
          this.$router.push('/customer/financingEnterprise')
          break
        case 'Archives':
          this.$router.$avueRouter.closeTag()
          this.$router.push({
            path: `/customer/archives/${this.archiveType}/${this.id}`,
          })
          break
      }
    },
  },
}
</script>

<style scoped lang="scss">
.customer-container {
  margin-bottom: 64px;

  .customer-card-panel {
    margin-bottom: 20px;

    .header-container {
      .user-container {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        margin-bottom: 20px;

        .avatar {
          flex-shrink: 0;

          ::v-deep {
            .image-slot {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
              height: 100%;
              background: #f5f7fa;
              color: #909399;
              font-size: 24px;
            }
          }
        }

        .info-container {
          width: 100%;
          padding: 0 12px;

          .text {
            color: rgba(100, 100, 100, 100);
            font-size: 14px;
          }

          .title-container {
            display: flex;
            align-content: center;
            justify-content: space-between;

            .title-content {
              display: inline-flex;
              align-content: center;

              > * {
                margin-right: 10px;

                &:last-child {
                  margin-right: 0;
                }
              }

              .title {
                display: inline-block;
                color: rgba(0, 0, 0, 0.85);
                font-size: 16px;
                font-weight: 600;
                text-align: left;
              }
            }

            .user-info-container {
              display: inline-flex;
              align-content: center;

              > * {
                margin-right: 24px;

                &:last-child {
                  margin-right: 0;
                }
              }
            }
          }

          .desc-container {
            display: flex;
            align-content: center;
            justify-content: space-between;
            margin-top: 4px;

            .tag-list {
              > * {
                margin-right: 10px;

                &:last-child {
                  margin-right: 0;
                }
              }
            }

            .date-container {
              display: flex;
              align-items: center;
            }

            .open-date-wrapper,
            .access-date-wrapper {
              display: inline-flex;
              align-items: center;

              span {
                font-size: 14px;
                line-height: 14px;
              }
            }

            .access-date-wrapper {
              margin-right: 24px;
            }
          }
        }
      }

      .user-desc-container {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
        padding: 20px 20px 0;
        background-color: rgba(247, 247, 247, 100);
        border-radius: 8px;

        .desc-item {
          display: inline-flex;
          align-items: center;
          flex-wrap: nowrap;
          width: calc(100% / 3);
          margin-bottom: 20px;
          line-height: 24px;

          .label {
            flex-shrink: 0;
            display: inline-block;
            width: 120px;
            text-align: right;
          }

          .value {
            display: inline-block;
            width: 100%;
          }
        }
      }
    }
  }
}
</style>

<style>
.customer-card-panel {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
}
</style>
