<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.whitelisttemplate_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>

      <template
        slot="menu"
        slot-scope="{ row, type, size, index, loading, done }"
      >
        <el-button
          icon="el-icon-check"
          v-if="row.status == 0"
          :size="size"
          :type="type"
          @click="editrtaion(row, index)"
          >编 辑</el-button
        >
        <el-button
          icon="el-icon-check"
          v-if="row.status == 0"
          :size="size"
          :type="type"
          @click="deltaion(row, index)"
          >删 除</el-button
        >
        <el-button
          icon="el-icon-check"
          v-if="row.status == 0"
          :size="size"
          :type="type"
          @click="enableupdate(row, index)"
          >启 用</el-button
        >

        <el-button
          icon="el-icon-check"
          v-if="row.status == 1"
          :size="size"
          :type="type"
          @click="disableupdate(row, index)"
          >禁 用</el-button
        >
      </template>

      <template slot="status" slot-scope="{row}" >
        <el-tag type="info" v-if="row.status === 0">已禁用</el-tag>
        <el-tag type="success" v-if="row.status === 1">已启用</el-tag>
      </template>

      <template
        slot-scope="{ row, type, disabled, size }"
        slot="customerTagesForm"
      >
        <el-select
          v-model="form.whitelistcustomerTagIdList"
          :disabled="disabled"
          multiple
          multiple-limit="3"
          filterable
          clearable
          default-first-option
          placeholder="请选择"
        >
          <el-option
            v-for="item in customerTagIdList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  whuteListtemooff,
  getAllCustomerTagIdList,
  whuteListtemoOn,
} from '@/api/customer/whitelisttemplate'
import { mapGetters } from 'vuex'
import {BLADE_RESOURCE} from '@/config/apiPrefix'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
     // blade:BLADE_RESOURCE,
      selectionList: [],
      customerTagIdList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        delBtn: false,
        column: [
          {
            label: '模板名称',
            prop: 'templateName',
            rules: [
              {
                required: true,
                message: '请输入模板名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '企业类型',
            prop: 'whitelistType',
            type: 'select',
            dicData: [
              {
                label: '融资企业',
                value: 0,
              },
              {
                label: '核心企业',
                value: 1,
              },
            ],
            rules: [
              {
                required: true,
                message: '0-，1-核心企业',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '业务类型',
            prop: 'businessType',
            span: 18,
            type: 'select',
            rules: [
              {
                required: true,
                message: '请选择业务类型',
                trigger: 'blur',
              },
            ],
            dicData: [
              {
                label: '应收账款',
                value: 1,
              },
              {
                label: '云信',
                value: 2,
              },
            ],
          },
          {
            label: '黑白名单',
            prop: 'blackWhiteType',
            type: 'select',
            rules: [
              {
                required: true,
                message: '0黑名单，1白名单',
                trigger: 'blur',
              },
            ],
            dicData: [
              {
                label: '黑名单',
                value: 0,
              },
              {
                label: '白名单',
                value: 1,
              },
            ],
          },
          {
            label: '选择客户',
            prop: 'customerTages',
            hide: true,
            span: 24,
            type: 'tree',
            formslot: true,
          },
          {
            label: '禁入行业',
            prop: 'middleProhibit',
            hide: true,
            span: 24,
            dicUrl: BLADE_RESOURCE+'/resource/proibit/chile-proibit',
            props: {
              label: 'codeName',
              value: 'id',
            },
            multiple: true,
            type: 'tree',
          },
          {
            label: '排序',
            type: 'number',
            prop: 'sort',
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '模板编号(或ID)',
            prop: 'whitelistTemplateCode',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入模板编号(或ID)',
                trigger: 'blur',
              },
            ],
          },
          {
            label: "状态",
            prop: "status",
            type:"select",
            addDisplay: false,
            editDisplay: false,
            dicData:[
              {
                label: '已禁用',
                value: 0,
              },
              {
                label: '已启用',
                value: 1,
              }],
            rules: [{
              message: "状态",
              trigger: "blur"
            }]
          },
          {
            label: "上次操作人",
            prop: "realName",
            addDisplay: false,
            editDisplay: false,
            rules: [{
              required: true,
              trigger: "blur"
            }]
          },
          {
            label: "上次操作时间",
            prop: "updateTime",
            addDisplay: false,
            editDisplay: false,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.whitelisttemplate_add, false),
        viewBtn: this.vaildData(this.permission.whitelisttemplate_view, false),
        delBtn: this.vaildData(this.permission.whitelisttemplate_delete, false),
        editBtn: this.vaildData(this.permission.whitelisttemplate_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {
    'form.whitelistType': {
      handler(value) {
        getAllCustomerTagIdList(value).then(res => {
          this.customerTagIdList = res.data.data
        })
      },
    },
  },

  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      // console.log(type);
      if (type == 'add') {
        // console.log(this.$router);
        this.$router.push('/customer/newWhitelist')
      }
      if (type == 'view') {
        this.$router.push(`/customer/lookWhitelist?id=${this.form.id}`)
      }
      if (type == 'edit') {
        this.$router.push(`/customer/editWhitelist?id=${this.form.id}`)
      }
      //   if (['edit'].includes(type)) {
      //     getDetail(this.form.id).then(res => {
      //       this.form = res.data.data
      //       this.customerTagIdList = this.form.customerTagIdList
      //     })
      // if ('add' == type) {
      //   console.log(type);
      //   // getAllCustomerTagIdList(this.form.whitelistType).then(res => {
      //   //   this.customerTagIdList = res.data.data
      //   // })
      //   /* getAllSpecification().then((res) => {
      //       this.form.productSpecifications = res.data.data
      //     })*/
      // }

      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },

    conlistrel() {
      this.form.status = '1'
      if (this.form.id) {
        this.$refs.crud.rowUpdate
      } else {
        this.$refs.crud.rowSave(this.form)
      }
    },
    editrtaion(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    deltaion(row, index) {
      this.$refs.crud.rowDel(row, index)
    },

    disableupdate(row) {
      this.$confirm('确定禁用？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return whuteListtemooff(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },

    enableupdate(row) {
      this.$confirm('确定进行发布操作吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return whuteListtemoOn(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },

    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
