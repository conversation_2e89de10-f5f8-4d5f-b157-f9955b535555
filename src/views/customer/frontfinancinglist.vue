<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row }" slot="invitationName">
        <p>{{ row.invitationName == '' ? '------' : row.invitationName }}</p>
      </template>

      <template slot-scope="{ row }" slot="customerScore">
        <p>{{ row.customerScore == '' ? '------' : row.customerScore }}</p>
      </template>
      <template slot-scope="{ row }" slot="customerStatus">
        <el-tag v-if="row.customerStatus === 0">未实名</el-tag>
        <el-tag type="success" v-if="row.customerStatus === 1">已实名</el-tag>
      </template>

      <template slot-scope="{ row }" slot="customerScore">
        <p>{{ row.score === -1 ? '------' : row.customerScore }}</p>
      </template>

      <template slot-scope="{ row }" slot="status">
        <el-tag v-if="row.status === 0">未知</el-tag>
        <el-tag type="success" v-if="row.status === 1">个人</el-tag>
        <el-tag type="success" v-if="row.status === 2">企业</el-tag>
      </template>

      <template slot-scope="{ row }" slot="customerLabel">
        <el-tag v-if="row.customerLabel === 5">融资大神</el-tag>
        <el-tag v-if="row.customerLabel === 1">游 客 </el-tag>
        <el-tag v-if="row.customerLabel === 2">未入门 </el-tag>
        <el-tag v-if="row.customerLabel === 3">融资萌新</el-tag>
        <el-tag v-if="row.customerLabel === 4">融资高手</el-tag>
      </template>
      <template slot-scope="{ row }" slot="customerScore1">
        <avue-rate disabled show-score v-model="row.customerScore"></avue-rate>
      </template>

      <template slot="menu" slot-scope="{ row, size, type, index }">
        <el-button
          icon="el-icon-document"
          :size="size"
          :type="type"
          v-if="row.customerStatus != 0"
          @click="handleRedirectArchives(row)"
          >档 案</el-button
        >

        <el-button
          icon="el-icon-check"
          :size="size"
          :type="type"
          v-if="row.customerStatus != 0"
          @click="editrtaion(row, index)"
          >客户经理
        </el-button>

        <el-button
          icon="el-icon-document"
          :size="size"
          :type="type"
          v-if="row.customerStatus != 0"
          @click="handleCustomer(row)"
          >客户画像</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from '@/api/customer/frontfinancinglist'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        column: [
          {
            label: '头像',
            prop: 'logoSrc',
            width: 170,
            listType: 'picture-img',
            dataType: 'string',
            type: 'upload',
            display: false,
            action: '/api/blade-resource/oss/endpoint/put-file',
            tip: '只能上传jpg/png/格式的图片',
            accept: '.jpg,.png',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
          },
          {
            label: '用户名',
            prop: 'accountUser',
            display: false,
            rules: [
              {
                required: true,
                message: '头像 ',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '客户名称',
            prop: 'name',
            display: false,
            rules: [
              {
                required: true,
                message: '客户名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '手机号',
            prop: 'phone',
            display: false,
            rules: [
              {
                required: true,
                message: '客户经理 ',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'customerStatus',
            type: 'select',
            display: false,
            addDisplay: false,
            editDisplay: false,
            dicData: [
              {
                label: '未实名',
                value: 0,
              },
              {
                label: '已实名',
                value: 1,
              },
            ],
            rules: [
              {
                required: true,
                message: '状态',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '客户星级',
            prop: 'customerScore1',
            display: false,
            rules: [
              {
                required: true,
                message: '头像 ',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '客户经理名称',
            prop: 'invitationName',
            display: false,
            rules: [
              {
                required: true,
                message: '头像 ',
                trigger: 'blur',
              },
            ],
          },
          /* {
            label: '客户标签',
            prop: 'customerLabel',
            viewDisplay: false,
            span: 24,
            addDisplay: false,
            editDisplay: false,
            display: false,
            dicData: [
              {
                label: '游客',
                value: 1,
              },
              {
                label: '未入门',
                value: 2,
              },
              {
                label: '融资萌新',
                value: 3,
              },
              {
                label: '融资高手',
                value: 4,
              },
              {
                label: '融资大神',
                value: 5,
              },
            ],
          },
          {
            label: '客户类型',
            prop: 'status',
            type: 'select',
            display: false,
            addDisplay: false,
            editDisplay: false,
            dicData: [
              {
                label: '未知',
                value: 0,
              },
              {
                label: '个人',
                value: 1,
              },
              {
                label: '企业',
                value: 2,
              },
            ],
            rules: [
              {
                required: true,
                message: '客户类型',
                trigger: 'blur',
              },
            ],
          },*/
          {
            label: '上次访问时间',
            prop: 'lastVisitTime',
            display: false,
            rules: [
              {
                required: true,
                message: '上次访问时间',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '融资企业评分',
            prop: 'customerScore',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入融资企业评分',
                trigger: 'blur',
              },
            ],
          },
        ],
        group: [
          {
            display: true,
            column: [
              {
                label: '头像',
                prop: 'logoSrc',
                width: 170,
                listType: 'picture-img',
                dataType: 'string',
                type: 'upload',
                action: '/api/blade-resource/oss/endpoint/put-file',
                tip: '只能上传jpg/png/格式的图片',
                accept: '.jpg,.png',
                propsHttp: {
                  res: 'data',
                  url: 'link',
                },
              },
              {
                label: '用户名',
                prop: 'accountUser',
                rules: [
                  {
                    required: true,
                    message: '头像 ',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '客户经理名称',
                prop: 'invitationName',
                rules: [
                  {
                    required: true,
                    message: '客户经理名称',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '手机号',
                prop: 'phone',
                rules: [
                  {
                    required: true,
                    message: '手机号 ',
                    trigger: 'blur',
                  },
                ],
              },
              /*{
                label: '客户标签',
                prop: 'customerLabel',
                type: 'select',
                dicData: [
                  {
                    label: '游客',
                    value: 1,
                  },
                  {
                    label: '未入门',
                    value: 2,
                  },
                  {
                    label: '融资萌新',
                    value: 3,
                  },
                  {
                    label: '融资高手',
                    value: 4,
                  },
                  {
                    label: '融资大神',
                    value: 5,
                  },
                ],
              },
              {
                label: '客户类型',
                prop: 'status',
                type: 'select',
                addDisplay: false,
                editDisplay: false,
                dicData: [
                  {
                    label: '未知',
                    value: 0,
                  },
                  {
                    label: '个人',
                    value: 1,
                  },
                  {
                    label: '企业',
                    value: 2,
                  },
                ],
                rules: [
                  {
                    required: true,
                    message: '状态',
                    trigger: 'blur',
                  },
                ],
              },*/
              {
                label: '状态',
                prop: 'customerStatus',
                type: 'select',
                addDisplay: false,
                editDisplay: false,
                dicData: [
                  {
                    label: '待实名',
                    value: 0,
                  },
                  {
                    label: '已实名',
                    value: 1,
                  },
                ],
                rules: [
                  {
                    required: true,
                    message: '客户类型',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '客户星级',
                prop: 'customerScore1',
                rules: [
                  {
                    required: true,
                    message: '级别 ',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '融资企业评分',
                prop: 'customerScore',
                rules: [
                  {
                    required: true,
                    message: '请输入融资企业评分',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '上次访问时间',
                prop: 'lastVisitTime',
                rules: [
                  {
                    required: true,
                    message: '上次访问时间',
                    trigger: 'blur',
                  },
                ],
              },
            ],
          },
          {
            display: true,
            column: [
              {
                label: '客户经理名称',
                prop: 'invitationCode',
                type: 'select',
                dicUrl:
                  '/api/blade-user/userList?userType=1',
                props: {
                  label: 'realName',
                  value: 'id',
                },
                rules: [
                  {
                    required: true,
                    message: '头像 ',
                    trigger: 'blur',
                  },
                ],
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.frontfinancinglist_add, false),
        viewBtn: this.vaildData(this.permission.frontfinancinglist_view, false),
        delBtn: this.vaildData(
          this.permission.frontfinancinglist_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.frontfinancinglist_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (type == 'view') {
        this.option.group[1].display = false
        this.option.group[0].display = true
      }
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    handleRedirectArchives(row) {
      this.$router.push({
        path: `/customer/archives/person/${row.id}`,
      })
    },
    handleCustomer(row) {
      this.$router.push({
        path: `/customer/customerInfo/person`,
        query: {
          userId: row.companyId,
          id: row.id,
        },
      })
    },
    editrtaion(row, index) {
      this.option.group[0].display = false
      this.option.group[1].display = true
      this.$refs.crud.rowEdit(row, index)
    },

    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records || []
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
