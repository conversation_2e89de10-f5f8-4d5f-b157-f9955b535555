<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      v-model="form"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      @row-del="rowDel"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @tree-load="treeLoad"
    >
      <template slot-scope="{ row, index, type }" slot="menuForm"></template>
      <!--      <template slot="status" slot-scope="{ row }">
        <el-tag type="info" v-if="row.status === 1">已禁用</el-tag>
        <el-tag type="success" v-if="row.status === 2">已启用</el-tag>
      </template>-->
      <template slot="menu" slot-scope="{ row, type, size, index, loading, done }">
        <el-button icon="el-icon-view" :size="size" :type="type" @click="lookDetail(row)">档 案</el-button>
        <el-button
          icon="el-icon-view"
          :size="size"
          :type="type"
          @click="eduUpdate(row)"
          v-if="row.totalCredit == null"
        >添加额度</el-button>

        <el-button
          icon="el-icon-view"
          :size="size"
          :type="type"
          v-if="row.totalCredit != null"
          @click="eduUpdate(row)"
        >编辑额度</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  enable,
  disable,
  getLazyList,
} from '@/api/customer/capital'
import { getFormatObj } from '@/util/util'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      parentId: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        calcHeight: 30,
        tip: false,
        lazy: true,
        tree: true,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: false,
        dialogClickModal: false,
        dialogWidth: '30%',
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        addBtn: false,
        column: [
          /* {
            label: '用户名',
            prop: 'account',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入用户名',
                trigger: 'blur',
              },
            ],
          },*/
          {
            label: '资金方名称',
            prop: 'name',
            display: false,
            rules: [
              {
                required: true,
                message: '资金方名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '统一社会代码',
            addDisplay: false,
            editDisplay: false,
            prop: 'unifiedSocialCode',
            rules: [
              {
                required: true,
                message: '统一社会代码',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'logo',
            prop: 'avatar',
            listType: 'picture-img',
            dataType: 'string',
            display: false,
            type: 'upload',
            rules: [
              {
                message: '请上传图片',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '联系人',
            prop: 'realName',
            display: false,
          },
          {
            label: '手机号',
            prop: 'phone',
            display: false,
          },
          {
            label: '手机号',
            prop: 'phoneEqual',
            search: true,
            hide: true,
            display: false,
          },
          {
            label: '授信总额',
            prop: 'totalCredit',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入授信总额',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '可用额度',
            prop: 'creditableAmount',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入授信总额',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '上级机构',
            prop: 'parentId',
            dicData: [],
            type: 'tree',
            hide: true,
            addDisabled: false,
            display: false,
            props: {
              label: 'title',
            },
            rules: [
              {
                required: false,
                message: '请选择上级机构',
                trigger: 'click',
              },
            ],
          },
          {
            label: '最后操作人',
            prop: 'operator',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入最后操作人',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上次操作时间',
            prop: 'updateTime',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入最后操作人',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '注册时间',
            prop: 'createTimeDateEq',
            search: true,
            type: 'date',
            display: false,
            valueFormat: 'yyyy-MM-dd',
            hide: true,
          },
          {
            label: '企业ID',
            prop: 'companyId',
            display: false,
            hide: true,
          },
        ],
        group: [
          {
            display: true,
            column: [
              {
                label: '授信额度',
                prop: 'creditTotal',
                controlsPosition: '',
                type: 'number',
                precision: 2,
                minRows: 0,
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '请输入授信额度',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '银行年利率(%)',
                prop: 'annualInterestRate',
                controlsPosition: '',
                type: 'number',
                precision: 3,
                minRows: 0,
                maxRows: 100,
                span: 24,
              },
              {
                label: '生效日',
                prop: 'fromValidity',
                type: 'date',
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd hh:mm:ss',
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '请输入生效日',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '到期日',
                prop: 'fxpirationDate',
                type: 'date',
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd hh:mm:ss',
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '请输入到期日',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '额度类型',
                prop: 'type',
                type: 'select',
                span: 24,
                dicData: [
                  {
                    label: '循环额度',
                    value: 1,
                  },
                  {
                    label: '一次性额度',
                    value: 2,
                  },
                ],
                rules: [
                  {
                    required: true,
                    message: '请选择额度类型;',
                    trigger: 'blur',
                  },
                ],
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.capital_add, false),
        viewBtn: this.vaildData(this.permission.capital_view, false),
        delBtn: this.vaildData(this.permission.capital_delete, false),
        editBtn: this.vaildData(this.permission.capital_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      // ? zch2024-10-11 未提bug 时间编辑格式为date报错
      const newRow = getFormatObj(row)
      // -?

      update(newRow).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          // 刷新表格数据并重载
          this.data = []
          this.parentId = 0
          this.$refs.crud.refreshTable()
          this.$refs.crud.toggleSelection()
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.companyId).then(res => {
          this.form = res.data.data
        })
        /*this.initData();*/
      }
      done()
    },
    lookDetail(row) {
      this.$router.push({
        path: 'capitalDetail',
        query: { id: row.id, companyId: row.companyId },
      })
    },
    searchReset() {
      this.query = {}
      this.parentId = 0
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.parentId = ''
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },

    amountSet(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    enable(row) {
      this.$confirm('确定启用吗吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return enable(row.id)
        })
        .then(resp => {
          if (resp.data.data == 0) {
            this.$message({
              type: 'error',
              message: '操作失败',
            })
          } else {
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
          }
          this.onLoad(this.page)
          this.$refs.crud.toggleSelection()
        })
    },
    disable(row) {
      this.$confirm('确定禁用吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return disable(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    onLoad(page, params = {}) {
      this.loading = true
      Object.assign(params, this.query, {
        current: page.currentPage,
        size: page.pageSize,
      })
      getList(params).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    eduUpdate(row, index) {
      row.id = ''
      this.$refs.crud.rowEdit(row, index)
    },
    treeLoad(tree, treeNode, resolve) {
      const parentId = tree.companyId
      getLazyList(parentId).then(res => {
        resolve(res.data.data)
      })
    },
    beforeClose(done) {
      this.parentId = ''
      const column = this.findObject(this.option.column, 'parentId')
      column.value = ''
      column.addDisabled = false
      this.onLoad(this.page)
      done()
    },
    conlistrel() {
      this.form.status = '1'
      if (this.form.id) {
        this.$refs.crud.rowUpdate()
      } else {
        this.$refs.crud.rowSave()
      }
    },
  },
}
</script>

<style></style>
