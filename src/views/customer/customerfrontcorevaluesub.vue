<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customerfrontcorevaluesub_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/customer/customerfrontcorevaluesub";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "去年纳税销售总额",
              prop: "totalTaxLastYear",
              rules: [{
                required: true,
                message: "请输入去年纳税销售总额",
                trigger: "blur"
              }]
            },
            {
              label: "前年纳税销售总额",
              prop: "totalTaxPreviousYear",
              rules: [{
                required: true,
                message: "请输入前年纳税销售总额",
                trigger: "blur"
              }]
            },
            {
              label: "净利润",
              prop: "netProfit",
              rules: [{
                required: true,
                message: "请输入净利润",
                trigger: "blur"
              }]
            },
            {
              label: "去年应纳税总额",
              prop: "totalTaxPayableLastYear",
              rules: [{
                required: true,
                message: "请输入去年应纳税总额",
                trigger: "blur"
              }]
            },
            {
              label: "前年应纳税总额",
              prop: "totalTaxPayablePreviousYear",
              rules: [{
                required: true,
                message: "请输入前年应纳税总额",
                trigger: "blur"
              }]
            },
            {
              label: "同比销售增长率",
              prop: "yearGrowth",
              rules: [{
                required: true,
                message: "请输入同比销售增长率",
                trigger: "blur"
              }]
            },
            {
              label: "环比销售增长率",
              prop: "annulusGrowth",
              rules: [{
                required: true,
                message: "请输入环比销售增长率",
                trigger: "blur"
              }]
            },
            {
              label: "应收账款总额",
              prop: "totalAccountsReceivable",
              rules: [{
                required: true,
                message: "请输入应收账款总额",
                trigger: "blur"
              }]
            },
            {
              label: "应还账款总额",
              prop: "totalStill",
              rules: [{
                required: true,
                message: "请输入应还账款总额",
                trigger: "blur"
              }]
            },
            {
              label: "回款率",
              prop: "collectionRate",
              rules: [{
                required: true,
                message: "请输入回款率",
                trigger: "blur"
              }]
            },
            {
              label: "变更表ID",
              prop: "recordId",
              rules: [{
                required: true,
                message: "请输入变更表ID",
                trigger: "blur"
              }]
            },
            {
              label: "财务报表",
              prop: "financeForm",
              rules: [{
                required: true,
                message: "请输入财务报表",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.customerfrontcorevaluesub_add, false),
          viewBtn: this.vaildData(this.permission.customerfrontcorevaluesub_view, false),
          delBtn: this.vaildData(this.permission.customerfrontcorevaluesub_delete, false),
          editBtn: this.vaildData(this.permission.customerfrontcorevaluesub_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
