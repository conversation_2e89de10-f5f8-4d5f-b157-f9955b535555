<template>
  <LayoutCard title="授信记录">
    <div v-loading="loading">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="
          () => {
            return { backgroundColor: '#f7f7f7', color: '#000' }
          }
        "
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column
          prop="quotaNo"
          label="额度编号"
          min-width="140"
        ></el-table-column>
        <el-table-column prop="logo" label="头像">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center; height: 100%">
              <el-avatar :src="scope.row.avatar" @error="errorHandler" />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="enterpriseNames"
          label="用户名称"
          min-width="160"
        >
          <template slot-scope="scope">
            <span class="text-blue">{{ scope.row.enterpriseName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="goodsName" label="产品名称" min-width="150">
        </el-table-column>
        <el-table-column prop="productTypeStrs" label="业务类型" width="120">
          <template slot-scope="scope">
            <span class="product-box" v-if="scope.row.productTypeStr">{{
              scope.row.productTypeStr
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creditAmount" label="授信额度(元)">
          <template slot-scope="{ row }">{{ row.creditAmounts }}</template>
        </el-table-column>
        <el-table-column prop="effectiveTime" label="生效日" min-width="120">
        </el-table-column>
        <el-table-column prop="expireTime" label="到期日" min-width="120">
        </el-table-column>
        <el-table-column prop="statusText" label="额度状态" min-width="120">
          <template slot-scope="scope">
            <span
              class="text-status"
              :class="getStatusTextColor(scope.row.status)"
              >{{ getStatusText(scope.row.status) }}</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="repayment-pagination" v-if="tableData.length">
        <el-pagination
          background
          hide-on-single-page
          layout="prev, pager, next"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :current-page="pagination.currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </LayoutCard>
</template>

<script>
import { getCapitalCreditRecordData } from '@/api/customer/capital'
import LayoutCard from '../archives/components/LayoutCard/index.vue'
import { formatMoney } from '@/util/filter'
export default {
  name: 'WhiteListCompany',
  components: { LayoutCard },
  props: {
    companyId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableData: [],
      loading: true,
      pagination: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
    }
  },
  mounted() {
    if (this.companyId) {
      this.getList({
        current: this.pagination.currentPage,
        size: this.pagination.pageSize,
        companyId: this.companyId,
      })
    }
  },
  methods: {
    getList(params) {
      let list = []
      getCapitalCreditRecordData(params)
        .then(({ data }) => {
          this.loading = false
          if (data.code === 200 && data.data) {
            this.pagination.total = data.data.enterpriseQuotas.total || 0
            if (data.data.enterpriseQuotas) {
              for (const item of data.data.enterpriseQuotas.records) {
                let productTypeStr = ''
                switch (item.productType) {
                  case 1:
                    productTypeStr = '应收账款质押'
                    break
                  case 2:
                    productTypeStr = '代采融资'
                    break
                  case 3:
                    productTypeStr = '云信'
                    break
                  case 4:
                    productTypeStr = '动产质押'
                    break
                  case 5:
                    productTypeStr = '订单融资'
                    break
                }
                list.push({
                  ...item,
                  productTypeStr,
                  creditAmounts: item.creditAmount
                    ? formatMoney(item.creditAmount)
                    : '',
                })
              }
            }
          }
          this.tableData = list
        })
        .catch(() => {
          this.loading = false
        })
    },

    getStatusTextColor(state) {
      let textColor = ''
      if ([1, 3, 7].includes(state)) {
        textColor = 'text-status-blue'
      } else if ([2, 6].includes(state)) {
        textColor = 'text-status-green'
      } else if ([4, 5].includes(state)) {
        textColor = 'text-status-gray'
      }
      return textColor
    },
    getStatusText(state) {
      let text = ''
      switch (state) {
        case 1:
          text = '待激活'
          break
        case 2:
          text = '生效中'
          break
        case 3:
          text = '已冻结'
          break
        case 4:
          text = '已禁止'
          break
        case 5:
          text = '已过期'
          break
        case 6:
          text = '生效中'
          break
        case 7:
          text = '续期中'
          break
      }
      return text
    },
    // 分页触发事件
    handleCurrentChange(current) {
      this.pagination.currentPage = current
      this.getList({
        current,
        size: this.pagination.pageSize,
        companyId: this.companyId,
      })
    },
    // 表格统计
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计:'
          return
        }
        console.log(column.property, '1')
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
  },
}
</script>

<style lang="scss" scoped>
.credit-capital-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.product-box {
  display: inline-block;
  padding: 4px 10px;
  box-sizing: border-box;
  border-radius: 56px;
  background-color: #eaecf1;
  font-size: 12px;
  color: #00072a;
  font-weight: 500;
  font-family: Microsoft Yahei;
}
.text-blue {
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.text-status {
  border-radius: 2px;
  display: inline-block;
  padding: 2px 6px;
  box-sizing: border-box;
}
.text-status-blue {
  background-color: #f7f8ff;
  color: #697cff;
  border: 1px solid #697cff;
}
.text-status-green {
  background-color: #f5f5f5;
  color: #1ac475;
  border: 1px solid #1ac475;
}
.text-status-gray {
  background-color: #f5f5f5;
  color: #84868d;
  border: 1px solid #d9d9d9;
}
.repayment-pagination {
  text-align: right;
  margin-top: 26px;
}
</style>
