<template>
  <div class="base-data-container">
    <LayoutCard title="工商信息">
      <div v-loading="loading">
        <el-descriptions labelStyle="width: 136px" :column="3" border>
          <el-descriptions-item
            v-if="companyBasicInfo.legalPersonName"
            label="法定代表人"
            >{{ companyBasicInfo.legalPersonName }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.phoneNumber"
            label="法人手机号"
            >{{ companyBasicInfo.phoneNumber }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.estiblishTime"
            label="成立日期"
            >{{ companyBasicInfo.estiblishTime }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.creditCode"
            label="统一社会代码"
            >{{ companyBasicInfo.creditCode }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.orgNumber"
            label="组织机构代码"
            >{{ companyBasicInfo.orgNumber }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.approvedTime"
            label="营业期限"
            >{{ companyBasicInfo.approvedTime }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.regNumber"
            label="工商注册号"
            >{{ companyBasicInfo.regNumber }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.totalAssets"
            label="纳税人资质"
            >{{ companyBasicInfo.totalAssets }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.regDate"
            label="核准日期"
            >{{ companyBasicInfo.regDate }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.regCapital"
            label="注册资本"
            >{{ companyBasicInfo.regCapital }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.paidAmount"
            label="实缴资本"
            >{{ companyBasicInfo.paidAmount }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.regInstitute"
            label="登记机关"
            >{{ companyBasicInfo.regInstitute }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.companyOrgType"
            label="企业类型"
            >{{ companyBasicInfo.companyOrgType }}</el-descriptions-item
          >
          <el-descriptions-item v-if="companyBasicInfo.industry" label="行业">{{
            companyBasicInfo.industry
          }}</el-descriptions-item>
          <el-descriptions-item
            v-if="companyBasicInfo.employeeNum"
            label="人员规模"
            >{{ companyBasicInfo.employeeNum }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.medicalInsurance"
            label="参保人员"
          >
            {{ companyBasicInfo.medicalInsurance }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="companyBasicInfo.regLocation"
            label="注册地址"
            :span="2"
            >{{ companyBasicInfo.regLocation }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="companyBasicInfo.businessScope"
            label="经营范围"
            :span="3"
            >{{ companyBasicInfo.businessScope }}</el-descriptions-item
          >
        </el-descriptions>
        <div
          class="btn-container"
          v-if="companyBasicInfo.businessLicenceAttachId"
        >
          <!-- <el-button
            :loading="viewCertBtnLoading"
            size="small"
            @click="handleViewLicense"
            >营业执照</el-button
          > -->
        </div>
      </div>
    </LayoutCard>

    <LayoutCard title="法人信息">
      <div v-loading="loading">
        <el-descriptions
          v-loading="false"
          labelStyle="width: 136px"
          :column="3"
          border
        >
          <el-descriptions-item
            v-if="customerInfo.corporationName"
            label="姓名"
            >{{ customerInfo.corporationName }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="customerInfo.corporationIdType"
            label="证件类型"
            >{{
              customerInfo.corporationIdType == 1 ? '护照号码' : '身份证'
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="customerInfo.corporationIdCardNumber"
            label="证件号码"
            >{{ customerInfo.corporationIdCardNumber }}</el-descriptions-item
          >
          <el-descriptions-item v-if="customerInfo.mobile" label="手机号">{{
            customerInfo.mobile
          }}</el-descriptions-item>
          <el-descriptions-item
            v-if="customerInfo.corporationSex"
            label="性别"
            >{{
              customerInfo.corporationSex === '--'
                ? '--'
                : customerInfo.corporationSex == 0
                ? '男'
                : '女'
            }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="customerInfo.corporationValidTime"
            label="证件有效期"
            >{{ customerInfo.corporationValidTime }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="customerInfo.corporationNation"
            label="民族"
            >{{ customerInfo.corporationNation }}</el-descriptions-item
          >
          <el-descriptions-item
            v-if="customerInfo.corporationCountry"
            label="国籍"
            >{{ customerInfo.corporationCountry }}</el-descriptions-item
          >
          <el-descriptions-item label="婚姻状况"></el-descriptions-item>
          <el-descriptions-item label="职业类别"></el-descriptions-item>
          <el-descriptions-item label="学历"></el-descriptions-item>
          <el-descriptions-item label="税收居民类型"></el-descriptions-item>
          <el-descriptions-item
            v-if="customerInfo.corporationAddress"
            label="证件地址"
            :span="3"
            >{{ customerInfo.corporationAddress }}</el-descriptions-item
          >
          <el-descriptions-item
            label="现居住址"
            :span="3"
          ></el-descriptions-item>
        </el-descriptions>
        <div class="btn-container">
          <!-- <el-button
            :loading="viewCertBtnLoading"
            size="small"
            @click="handleViewCert"
            >查看证件</el-button
          > -->
        </div>
      </div>
    </LayoutCard>

    <LayoutCard title="股权信息">
      <div v-loading="loading">
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="
            () => {
              return { backgroundColor: '#f7f7f7', color: '#000' }
            }
          "
        >
          <el-table-column
            type="index"
            label="序号"
            width="112"
          ></el-table-column>
          <el-table-column prop="name" label="股东名称"></el-table-column>
          <el-table-column prop="amomon" label="出资金额"></el-table-column>
          <el-table-column prop="percent" label="持股比例"> </el-table-column>
        </el-table>
      </div>
    </LayoutCard>

    <div class="button-container">
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        plain
        @click="handleRefreshBtn"
        >刷新数据</el-button
      >
    </div>
    <CommonDialog
      ref="refreshDialog"
      title="是否确定刷新数据？"
      class="archives-refresh-dialog"
      noBorder
      :enableFullScreenBtn="false"
      center
      width="40%"
      :cancelDisable="dialogLoading"
      :confirmLoading="dialogLoading"
      @cancel="handleRefreshCancel"
      @confirm="handleRefreshConfirm"
    >
      <div class="archives-refresh-wrapper">
        <span>每次【刷新数据】都会使用天眼查-工商信息接口，1元/次</span>
      </div>
    </CommonDialog>
  </div>
</template>

<script>
import LayoutCard from '../archives/components/LayoutCard/index.vue'
import CommonDialog from '../archives/components/CommonDialog/index.vue'
import {
  requestBaseDataBusinessInfo,
  requestRefreshBaseDataBusinessInfo,
  requestBaseDataPicByAttachId,
} from '@/api/customer/archives/archive'

export default {
  name: 'CustomerModuleBaseDataIndex',
  components: {
    LayoutCard,
    CommonDialog,
    LayoutCard,
    LayoutCard,
  },
  props: {
    unifiedSocialCode: {
      type: String,
      default: '',
    },
    companyName: {
      type: String,
      default: undefined,
    },
    companyId: {
      type: String,
      default: '',
    },
  },
  inject: ['type'],
  data() {
    return {
      loading: true,
      alreadyLoad: false,
      // 工商信息
      companyBasicInfo: {},
      // 人员信息
      customerInfo: {},
      // 确认刷新弹窗加载
      dialogLoading: false,
      // 股权结构
      tableData: [],
    }
  },
  created() {
    if (this.companyId && this.unifiedSocialCode) {
      this.initData()
    }
  },
  methods: {
    // 查看营业执照
    handleViewLicense() {
      this.viewCertBtnLoading = true
      requestBaseDataPicByAttachId({
        attachId: this.companyBasicInfo.businessLicenceAttachId,
      })
        .then(({ data }) => {
          data = data.data || []
          const imgSrcArr = []
          for (const item of data) {
            imgSrcArr.push({ url: item })
          }
          this.viewCertBtnLoading = false
          this.$ImagePreview(imgSrcArr, 0, { closeOnClickModal: false })
        })
        .catch(() => {
          this.viewCertBtnLoading = false
        })
    },

    // 查看证件
    async handleViewCert() {
      this.viewCertBtnLoading = true
      let faceAttachId = undefined
      let backAttachId = undefined
      const imgSrcArr = []
      faceAttachId = this.customerInfo.corporationFaceAttachId
      backAttachId = this.customerInfo.corporationBackAttachId

      try {
        let [faceData, backData] = await Promise.all([
          requestBaseDataPicByAttachId({ attachId: faceAttachId }),
          requestBaseDataPicByAttachId({ attachId: backAttachId }),
        ])
        faceData = faceData.data || {}
        faceData = faceData.data || []
        for (const item of faceData) {
          imgSrcArr.push({ url: item })
        }
        backData = backData.data || {}
        backData = backData.data || []
        for (const item of backData) {
          imgSrcArr.push({ url: item })
        }
        this.$ImagePreview(imgSrcArr, 0, { closeOnClickModal: false })
      } finally {
        this.viewCertBtnLoading = false
      }
    },

    initData() {
      this.loading = true
      requestBaseDataBusinessInfo({
        keyword: this.unifiedSocialCode || this.companyName,
        companyId: this.companyId,
      })
        .then(({ data }) => {
          if (data.success) {
            data = data.data

            // 处理工商信息
            data.companyBasicInfo = data.companyBasicInfo || {}
            const companyBasicInfo = {
              legalPersonName: '--',
              phoneNumber: '--',
              estiblishTime: '--',
              creditCode: '--',
              orgNumber: '--',
              approvedTime: '--',
              regNumber: '--',
              totalAssets: '--',
              regDate: '--',
              regCapital: '--',
              paidAmount: '--',
              regInstitute: '--',
              companyOrgType: '--',
              industry: '--',
              employeeNum: '--',
              medicalInsurance: '--',
              regLocation: '--',
              businessScope: '--',
              businessLicenceAttachId: data.customerInfo
                ? data.customerInfo.businessLicenceAttachId
                : undefined,
              ...data.companyBasicInfo,
            }
            for (const key in companyBasicInfo) {
              if (
                companyBasicInfo[key] === null ||
                companyBasicInfo[key] === undefined
              ) {
                companyBasicInfo[key] = '--'
              }
            }
            // 处理人员信息
            data.customerInfo = data.customerInfo || {}
            const customerInfo = {
              corporationName: '--',
              corporationIdType: '--',
              corporationIdCardNumber: '--',
              mobile: '--',
              corporationSex: '--',
              corporationValidTime: '--',
              corporationNation: '--',
              corporationCountry: '--',
              corporationxxxxxxxxxxxxxxxxxxxxxxxxxxx: '--',
              corporationAddress: '--',
              operatorName: '--',
              operatorXXXXXXXXXXXXXX: '--',
              operatorIdcard: '--',
              operatorPhone: '--',
              operatorSex: '--',
              operatorValidTime: '--',
              operatorNation: '--',
              operatorCountry: '--',
              operatorAddress: '--',
              ...data.customerInfo,
            }
            for (const key in customerInfo) {
              if (
                customerInfo[key] === null ||
                customerInfo[key] === undefined
              ) {
                customerInfo[key] = '--'
              }
            }

            this.tableData = data.supplierOwnerShipStructList
            this.companyBasicInfo = companyBasicInfo // 工商信息
            this.customerInfo = customerInfo
            this.loading = false
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleRefreshBtn() {
      this.$refs.refreshDialog.handleOpen()
    },
    handleRefreshCancel() {
      this.$refs.refreshDialog.handleClose()
    },
    handleRefreshConfirm() {
      this.dialogLoading = true
      requestRefreshBaseDataBusinessInfo({
        keyword: this.unifiedSocialCode || this.companyName,
      })
        .then(({ data }) => {
          this.dialogLoading = false
          if (data.success) {
            this.$message.success('操作成功')
            this.$refs.refreshDialog.handleClose()
            this.initData()
          }
        })
        .catch(() => {
          this.dialogLoading = false
        })
    },
  },
}
</script>

<style lang="scss">
.base-data-container {
  margin-top: 20px;
  .btn-container {
    margin-top: 15px;
    text-align: right;
  }
}
.button-container {
  text-align: right;
}

.archives-refresh-dialog {
  .archives-refresh-wrapper {
    padding: 24px;
  }
}
</style>
