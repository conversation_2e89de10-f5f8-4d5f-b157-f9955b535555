<template>
  <LayoutCard title="合同列表">
    <div v-loading="loading">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="
          () => {
            return { backgroundColor: '#fff1f1', color: '#000' }
          }
        "
      >
        <el-table-column type="index" label="序号" width="60"></el-table-column>
        <el-table-column prop="contractId" label="合同编号"></el-table-column>
        <el-table-column
          prop="contractTitle"
          label="合同标题"
        ></el-table-column>
        <el-table-column prop="goodName" label="产品名称">
          <template slot-scope="scope">
            <span class="text-blue">{{ scope.row.goodName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="processTypeText" label="流程类型">
        </el-table-column>
        <el-table-column prop="signNodeText" label="流程节点">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间"> </el-table-column>
        <el-table-column prop="state" label="签署状态">
          <template slot-scope="scope">
            <span
              class="text-status"
              :class="getStatusTextColor(scope.row.status)"
              >{{ getStatusText(scope.row.status) }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleDetail(scope.row)"
              >详情</el-button
            >
            <el-button type="text" @click="handleDown(scope.row)"
              >下载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="repayment-pagination" v-if="tableData.length">
        <el-pagination
          background
          hide-on-single-page
          layout="prev, pager, next"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :current-page="pagination.currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </LayoutCard>
</template>

<script>
import { getContractsListData } from '@/api/customer/capital'
import LayoutCard from '../archives/components/LayoutCard/index.vue'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import { contractDownload } from '@/api/goods/pcontrol/workflow/productConfirmation'

export default {
  name: 'WhiteListCompany',
  components: { LayoutCard },
  props: {
    companyId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableData: [],
      pagination: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      loading: true,
      processType: {},
      processNode: {},
    }
  },
  created() {
    this.getDictionaryType()
    this.getDictionaryNode()
  },
  mounted() {
    if (this.companyId) {
      this.getList({
        current: this.pagination.currentPage,
        size: this.pagination.pageSize,
        companyId: this.companyId,
      })
    }
  },
  methods: {
    // 查看合同
    handleDetail({ contractId }) {
      if (contractId) {
        this.$router.push(
          '/contract/contractDetail/' +
            Buffer.from(JSON.stringify(contractId)).toString('base64')
        )
      }
    },
    // 下载合同
    handleDown({ contractId }) {
      let bool = false
      if (contractId) {
        if (!bool) {
          bool = true
          contractDownload({ contractId })
            .then(({ data }) => {
              if (data.code === 200 && data.data) {
                window.open(data.data)
              }
              bool = false
            })
            .catch(() => {
              bool = false
            })
        }
      } else {
        this.$message.error('当前不存在该合同!')
        return
      }
    },

    // 签署状态样式
    getStatusTextColor(state) {
      let textColor = ''
      if ([1].includes(state)) {
        textColor = 'text-status-blue'
      } else if ([3, 5].includes(state)) {
        textColor = 'text-status-green'
      } else if ([2, 4].includes(state)) {
        textColor = 'text-status-gray'
      }
      return textColor
    },
    getStatusText(state) {
      let text = ''
      switch (state) {
        case 1:
          text = '待签署'
          break
        case 2:
          text = '已取消'
          break
        case 3:
          text = '已签署'
          break
        case 4:
          text = '已失效'
          break
        case 5:
          text = '已完成'
          break
      }
      return text
    },

    // 合同列表数据源
    getList(params) {
      let list = []

      getContractsListData(params)
        .then(({ data }) => {
          this.loading = false
          if (data.code === 200) {
            this.pagination.total = data.data.total || 0
            if (data.data.records) {
              data.data.records.forEach(item => {
                list.push({
                  ...item,
                  signNodeText: this.processNode[item.signNode],
                  processTypeText: this.processType[item.processType],
                })
              })
            }
          }
          this.tableData = list
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 流程类型
    getDictionaryType() {
      let list = {}
      getDictionary('business_process_type').then(({ data }) => {
        if (data.code === 200 && data.data) {
          for (const item of data.data) {
            list[item.dictKey] = item.dictValue
          }
        }
        this.processType = list
      })
    },
    // 流程节点
    getDictionaryNode() {
      let list = {}
      getDictionary('goods_sign_node').then(({ data }) => {
        if (data.code === 200 && data.data) {
          for (const item of data.data) {
            list[item.dictKey] = item.dictValue
          }
        }
        this.processNode = list
      })
    },

    // 分页触发事件
    handleCurrentChange(current) {
      this.pagination.currentPage = current
      this.getList({
        current,
        size: this.pagination.pageSize,
        companyId: this.companyId,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.repayment-pagination {
  text-align: right;
  margin-top: 26px;
}
.text-blue {
  line-height: 20px;
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  font-family: Roboto;
}
.text-status {
  border-radius: 2px;
  display: inline-block;
  padding: 2px 6px;
  box-sizing: border-box;
}
.text-status-blue {
  background-color: #f7f8ff;
  color: #697cff;
  border: 1px solid #697cff;
}
.text-status-green {
  background-color: #f5f5f5;
  color: #1ac475;
  border: 1px solid #1ac475;
}
.text-status-gray {
  background-color: #f5f5f5;
  color: #84868d;
  border: 1px solid #d9d9d9;
}
</style>
