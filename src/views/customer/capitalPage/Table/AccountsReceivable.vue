<template>
  <div v-loading="loading">
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="
        () => {
          return { backgroundColor: '#f7f7f7', color: '#000' }
        }
      "
    >
      <el-table-column type="index" label="#" width="48" align="center" />
      <el-table-column prop="contractNo" label="资产编号" width="200">
        <template slot-scope="{ row }">
          <span>{{ row.contractNo !== null ? row.contractNo : '--' }} </span>
        </template>
      </el-table-column>
      <el-table-column prop="paymentEmp" label="应付方" min-width="220">
        <template slot-scope="{ row }">
          <span class="text-blue">{{ row.paymentEmp }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="proofTypeText" label="凭证类型" min-width="100">
        <template slot-scope="{ row }">
          <span class="border-box">{{ row.proofTypeText }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="proofNo" label="凭证编号" min-width="200" />
      <el-table-column prop="orderAmount" label="订单金额" min-width="120">
        <template slot-scope="{ row }">
          <span>¥{{ row.orderAmount | formatMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="accountAmount" label="账面金额" min-width="120">
        <template slot-scope="{ row }">
          <span>¥{{ row.accountAmount | formatMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="effectiveAmount" label="有效金额" min-width="120">
        <template slot-scope="{ row }">
          <span>¥{{ row.effectiveAmount | formatMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="paymentDays" label="账期" min-width="120">
        <template slot-scope="{ row }">
          <span>{{ row.paymentDays }}天</span>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开始日期" min-width="100" />
      <el-table-column prop="expireTime" label="到期日期" min-width="100" />
      <el-table-column prop="gracePeriod" label="宽限期" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.gracePeriod }}天</span>
        </template>
      </el-table-column>
      <el-table-column prop="proofStatusText" label="凭证状态" min-width="140">
        <template slot-scope="{ row }">
          <span
            v-if="row.status !== null"
            class="text-status"
            :class="getStatusTextColor(row.proofStatus)"
            >{{ getStatusText(row.proofStatus) }}</span
          >
          <span v-else>--</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="proofStatusText" label="应收状态" min-width="100">
        <template slot-scope="{ row }">
          <span
            v-if="row.status !== null"
            class="text-status"
            :class="getStatusTextColor(row.status)"
            >{{ getStatusText(row.status) }}</span
          >
          <span v-else>--</span>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="options"
        label="操作"
        fixed="right"
        min-width="260"
      >
        <template slot-scope="{ row }">
          <el-button @click="handleOpen(row, 'Get')" type="text" size="small"
            >查看凭证</el-button
          >
          <!-- <el-button
            @click="handleOpen(row, 'Approval')"
            type="text"
            size="small"
            >审批详情</el-button
          > -->
          <el-button @click="handleOpen(row, 'Detail')" type="text" size="small"
            >使用明细</el-button
          >
          <el-button
            @click="handleOpen(row, 'PayDetail')"
            type="text"
            size="small"
            >回款信息</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next"
      hide-on-single-page
      @current-change="handleCurrentChange"
      :current-page.sync="paginationData.currentPage"
      :total="paginationData.total"
      :page-size="paginationData.pageSize"
    >
    </el-pagination>

    <DialogPreviewProof
      ref="DialogPreviewProofRef"
      :invoiceTypeMap="invoiceTypeMap"
      :proofTypeMap="proofTypeMap"
    />

    <DialogUseDetail ref="DialogUseDetailRef" />
    <DialogReplaymentDetail ref="DialogReplaymentDetailRef" />
  </div>
</template>

<script>
import { getAccountsReceivableData } from '@/api/customer/archives/archive'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import { formatMoney } from '@/util/filter'
import { requestProcessInfo } from '@/api/customer/archives/archive'
import { routerMapKeyToPath } from '@/views/business/config'
import DialogPreviewProof from '../../archives/components/ModuleAssetInformation/components/DialogPreviewProof/index'
import DialogUseDetail from '../../archives/components/ModuleAssetInformation/components/DialogUseDetail/index'
import DialogReplaymentDetail from '../../archives/components/ModuleAssetInformation/components/DialogRepaymentDetail/index'
export default {
  name: 'AccountsReceivable',
  props: {
    companyId: {
      type: String,
      default: '',
    },
  },
  components: { DialogPreviewProof, DialogUseDetail, DialogReplaymentDetail },
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      loading: true,
      proofTypeConfig: {},
      invoiceTypeMap: {},
      proofTypeMap: {},
    }
  },
  created() {
    this.getDictionaryType()
    if (this.companyId) {
      this.getList()
    }
    getDictionary({ code: 'customer_invoice_type' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          const invoiceTypeMap = {}
          for (const item of data) {
            invoiceTypeMap[item.dictKey] = item.dictValue
          }
          invoiceTypeMap.ready = true
          this.invoiceTypeMap = invoiceTypeMap
        }
      })
      .catch(() => {})
    getDictionary({ code: 'jrzh_customer_front_sales_contract_proof_type' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          const proofTypeMap = {}
          for (const item of data) {
            proofTypeMap[item.dictKey] = item.dictValue
          }
          proofTypeMap.ready = true
          this.proofTypeMap = proofTypeMap
        }
      })
      .catch(() => {})
  },
  methods: {
    // 凭证状态样式
    getStatusTextColor(state) {
      let textColor = ''
      if ([1, 3].includes(state)) {
        textColor = 'text-status-blue'
      } else if ([2, 4, 5].includes(state)) {
        textColor = 'text-status-gray'
      } else if ([6].includes(state)) {
        textColor = 'text-status-green'
      }
      return textColor
    },

    getStatusText(state) {
      let text = ''
      switch (state) {
        case 1:
          text = '待确权'
          break
        case 2:
          text = '已作废'
          break
        case 3:
          text = '可使用'
          break
        case 4:
          text = '已使用'
          break
        case 5:
          text = '到期未结清'
          break
        case 6:
          text = '已结清'
          break
      }
      return text
    },

    // 凭证类型
    getDictionaryType() {
      let list = {}
      getDictionary('jrzh_customer_front_sales_contract_proof_type').then(
        ({ data }) => {
          if (data.code === 200 && data.data) {
            for (const item of data.data) {
              list[item.dictKey] = item.dictValue
            }
          }
          this.proofTypeConfig = list
        }
      )
    },

    handleCurrentChange(current) {
      this.paginationData.currentPage = current
      this.updateList({
        size: this.paginationData.pageSize,
        current,
      })
    },

    handleOpen(row, name) {
      switch (name) {
        case 'Get':
          this.$refs.DialogPreviewProofRef.handleOpen(row)
          break
        case 'Approval':
          this.handleBtnApproveDetail(row)
          break
        case 'Detail':
          this.$refs.DialogUseDetailRef.handleOpen(row.id)
          break
        case 'PayDetail':
          this.$refs.DialogReplaymentDetailRef.handleOpen(row.id)
          break
      }
    },
    // 审批详情
    handleBtnApproveDetail(row) {
      requestProcessInfo({ processInsId: row.processInstanceId })
        .then(({ data }) => {
          const { taskId, processInsId, processDefinitionKey } = data.data
          const target = routerMapKeyToPath[processDefinitionKey]
          if (!target) return
          this.$router.push(
            `${target}/detail/${Buffer.from(
              JSON.stringify({
                taskId,
                processInsId,
              })
            ).toString('base64')}`
          )
        })
        .catch(() => {})
    },

    getList() {
      this.updateList({
        size: this.paginationData.pageSize,
        current: this.paginationData.currentPage,
      })
    },
    updateList({ size, current }) {
      this.loading = true
      const params = {
        size,
        current,
        companyId: this.companyId,
      }
      let list = []
      getAccountsReceivableData(params)
        .then(({ data }) => {
          this.paginationData.total = data.data.total || 0
          if (data.code === 200 && data.data) {
            data.data.records.forEach((item, index) => {
              list.push({
                ...item,
                index: index + 1 + (this.paginationData.currentPage - 1) * 10,
                proofTypeText: this.proofTypeConfig[item.proofType],
              })
            })
          }

          this.loading = false
          this.tableData = list
        })
        .catch(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.border-box {
  display: inline-block;
  color: #101010;
  background-color: #eaecf1;
  border-radius: 36px;
  font-size: 14px;
  font-weight: 500;
  padding: 3px 12px;
  box-sizing: border-box;
}
.text-blue {
  line-height: 20px;
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  font-family: Roboto;
}
.text-status {
  border-radius: 2px;
  display: inline-block;
  padding: 2px 6px;
  box-sizing: border-box;
}
.text-status-blue {
  background-color: #f7f8ff;
  color: #697cff;
  border: 1px solid #697cff;
}
.text-status-green {
  background-color: #f5f5f5;
  color: #1ac475;
  border: 1px solid #1ac475;
}
.text-status-gray {
  background-color: #f5f5f5;
  color: #84868d;
  border: 1px solid #d9d9d9;
}
</style>
