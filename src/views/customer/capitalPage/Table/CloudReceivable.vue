<template>
  <div v-loading="loading">
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="
        () => {
          return { backgroundColor: '#f7f7f7', color: '#000' }
        }
      "
    >
      <el-table-column type="index" label="#" width="48"></el-table-column>
      <el-table-column prop="cloudCode" label="云信编号" min-width="150">
      </el-table-column>
      <el-table-column prop="companyName" label="开单企业" min-width="270">
        <template slot-scope="{ row }">
          <span class="text-blue">{{ row.companyName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="financingModelText"
        label="融资付息模式"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span
            class="border-box"
            :class="row.financingModel === 0 ? 'text-blue' : ''"
            >{{
              row.financingModel !== null
                ? row.financingModel === 1
                  ? '融资用户付息'
                  : '开单企业付息'
                : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="cloudBillAmount"
        label="云信金额"
        min-width="200"
      ></el-table-column>
      <el-table-column prop="endDate" label="到期日期"> </el-table-column>

      <el-table-column prop="statusText" label="状态" min-width="100">
        <template slot-scope="{ row }">
          <span :class="getStatusTextColor(row.status)">{{
            getStatusText(row.status)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="options"
        label="操作"
        fixed="right"
        min-width="260"
      >
        <template slot-scope="{ row }">
          <el-button @click="handleOpen(row, 'Detail')" type="text" size="small"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      hide-on-single-page
      layout="prev, pager, next"
      @current-change="handleCurrentChange"
      :current-page.sync="paginationData.currentPage"
      :total="paginationData.total"
      :page-size="paginationData.pageSize"
    >
    </el-pagination>
  </div>
</template>

<script>
import { getCloudTableList } from '@/api/customer/archives/archive'
import { formatMoney } from '@/util/filter'
import dayjs from 'dayjs'
export default {
  name: 'AccountsReceivable',
  props: {
    companyId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      loading: true,
    }
  },
  created() {
    if (this.companyId) {
      this.getList()
    }
  },
  methods: {
    handleCurrentChange(current) {
      this.paginationData.currentPage = current
      this.updateList({
        size: this.paginationData.pageSize,
        current,
      })
    },

    handleOpen(row, name) {
      switch (name) {
        case 'Detail':
          this.$router.push(
            `/product/cloudAssetsDetail/${Buffer.from(
              JSON.stringify({ id: row.id })
            ).toString('base64')}`
          )

          break
      }
    },

    getList() {
      this.updateList({
        size: this.paginationData.pageSize,
        current: this.paginationData.currentPage,
      })
    },
    updateList({ size, current }) {
      this.loading = true
      const params = {
        size,
        current,
        companyId: this.companyId,
      }
      let list = []
      getCloudTableList(params)
        .then(({ data }) => {
          this.paginationData.total = data.data.total || 0
          if (data.code === 200 && data.data) {
            data.data.records.forEach(item => {
              list.push({
                ...item,
                endDate: dayjs(item.endDate).format('YYYY-MM-DD'),
                cloudBillAmount: item.cloudBillAmount
                  ? `￥${formatMoney(item.cloudBillAmount)}`
                  : '',
              })
            })
          }

          this.loading = false
          this.tableData = list
        })
        .catch(() => {
          this.loading = false
        })
    },

    // 状态样式
    getStatusTextColor(state) {
      let textColor = ''
      if ([0, 4, 6].includes(state)) {
        textColor = 'text-status-blue'
      } else if ([2].includes(state)) {
        textColor = 'text-status-green'
      } else if ([1, 3, 5, 7, 9].includes(state)) {
        textColor = 'text-status-gray'
      } else if ([8].includes(state)) {
        textColor = 'text-stauts-red'
      }
      return textColor
    },
    getStatusText(state) {
      let text = ''
      switch (state) {
        case 0:
          text = '待签收'
          break
        case 1:
          text = '签收已拒绝'
          break
        case 2:
          text = '持单中'
          break
        case 3:
          text = '超时作废'
          break
        case 4:
          text = '拆分中'
          break
        case 5:
          text = '已拆分'
          break
        case 6:
          text = '到期未收款'
          break
        case 7:
          text = '到期已收款'
          break
        case 8:
          text = '逾期未收款'
          break
        case 9:
          text = '逾期已收款'
          break
      }
      return text
    },
  },
}
</script>

<style lang="scss" scoped>
.border-box {
  display: inline-block;
  color: #101010;
  background-color: #eaecf1;
  border-radius: 36px;
  font-size: 14px;
  font-weight: 500;
  padding: 3px 12px;
  box-sizing: border-box;
}
.text-blue {
  line-height: 20px;
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  font-family: Roboto;
}
.text-status-gray {
  color: #646464;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.text-status-blue {
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.text-status-green {
  color: #1fc374;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.text-status-red {
  color: #ff4d4d;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
</style>
