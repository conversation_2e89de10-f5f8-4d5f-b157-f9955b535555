<template>
  <div v-loading="loading">
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="
        () => {
          return { backgroundColor: '#FFF1F1', color: '#000' }
        }
      "
    >
      <el-table-column type="index" width="50" label="#"> </el-table-column>
      <el-table-column
        v-for="item in columnOption"
        :key="item.prop"
        :label="item.label"
        :prop="item.prop"
        :min-width="item.minWidth"
      >
        <template slot-scope="{ row }">
          <div v-if="item.prop === 'type'">
            <el-tag type="" v-if="row.type === 1" effect="plain"
              >应收账款质押</el-tag
            >
            <el-tag type="" v-if="row.type === 2" effect="plain"
              >代采融资</el-tag
            >
            <el-tag type="" v-if="row.type === 3" effect="plain">云信</el-tag>
            <el-tag type="" v-if="row.type === 4" effect="plain">动产质押</el-tag>
            <el-tag type="" v-if="row.type === 5" effect="plain">订单融资</el-tag>
          </div>
          <div v-else-if="item.prop === 'status'">
            <div>
              <el-tag type="info" v-if="row.status === 1">未上架</el-tag>
              <el-tag type="success" v-if="row.status === 2">已上架</el-tag>
              <el-tag type="info" v-if="row.status === 3">已下架</el-tag>
            </div>
          </div>
          <div v-else-if="item.prop === 'action'">
            <el-button type="text" @click="handleGetDetail(row)"
              >查看</el-button
            >
          </div>
          <div v-else>{{ row[item.prop] }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="repayment-pagination">
      <el-pagination
        background
        layout="prev, pager, next"
        hide-on-single-page
        @current-change="handleCurrentChange"
        :current-page.sync="current"
        :total="total"
        :page-size="pageSize"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
// import { update } from '@/api/goods/goodscontracttemplate'
export default {
  name: 'CommonTable',
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    current: {
      type: Number,
      default: 1,
    },
    total: {
      type: Number,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    columnOption: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    page: {
      type: String,
      default: '',
    },
  },
  methods: {
    handleCurrentChange(current) {
      this.$emit('update', { current })
    },

    handleGetDetail(row) {
      switch (row.type) {
        case 1:
          this.$router.push({
            path: '/pcontrol/purchasing',
            query: { id: row.id },
          })
          sessionStorage.setItem('look', 'true')
          break
        case 2:
          this.$router.push({
            path: '/pcontrol/pinformation',
            query: { id: row.id },
          })
          sessionStorage.setItem('look', 'true')
          break
        case 3:
          this.$router.push({
            path: '/pcontrol_cloud/purchasing',
            query: { id: row.id },
          })
          sessionStorage.setItem('look', 'true')
          break
        case 4:
          this.$router.push({
            path: '/pcontrol/pledgeMovables',
            query: { id: row.id },
          })
          sessionStorage.setItem('look', 'true')
          break
        case 5:
          this.$router.push({
            path: '/pcontrol/orderFinancing',
            query: { id: row.id },
          })
          sessionStorage.setItem('look', 'true')
          break
      }
      // if (this.page === 'goods' || this.page === 'agentGoods') {
      //   if (row.type == 2) {
      //     this.$router.push({
      //       path: '/pcontrol/pinformation',
      //       query: { id: row.id },
      //     })
      //     sessionStorage.setItem('look', 'true')
      //   } else {
      //     this.$router.push({
      //       path: '/pcontrol/purchasing',
      //       query: { id: row.id },
      //     })
      //     sessionStorage.setItem('look', 'true')
      //   }
      // } else if (this.page === 'cloudGoods') {
      //   this.$router.push({
      //     path: '/pcontrol_cloud/purchasing',
      //     query: { id: row.id },
      //   })
      //   sessionStorage.setItem('look', 'true')
      // }
    },
  },
}
</script>

<style lang="scss" scoped>
.repayment-pagination {
  text-align: right;
  margin-top: 26px;
}
</style>
