<template>
  <div class="asset-info-container">
    <LayoutCard title="资产信息">
      <div class="content">
        <div class="label-bar-container">
          <el-tabs v-model="activeTab">
            <el-tab-pane
              v-for="item of tabList"
              :label="item"
              :name="item"
              :key="item"
            />
          </el-tabs>
        </div>

        <div class="table-container">
          <AccountsReceivable
            :companyId="companyId"
            v-if="activeTab === '应收账款'"
          />

          <CloudReceivable :companyId="companyId" v-if="activeTab === '云信'" />
        </div>
      </div>
    </LayoutCard>
  </div>
</template>

<script>
import LayoutCard from '../archives/components/LayoutCard/index.vue'
import AccountsReceivable from './Table/AccountsReceivable.vue'
import CloudReceivable from './Table/CloudReceivable.vue'

export default {
  name: 'CustomerModuleDocumentInfoIndex',
  components: {
    LayoutCard,
    AccountsReceivable,
    CloudReceivable,
  },
  props: {
    companyId: {
      type: [String, undefined],
      default: undefined,
    },
  },
  data() {
    return {
      tabList: ['应收账款', '云信', '应收票据', '仓单', '存款', '房产'],
      activeTab: '应收账款',
    }
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.table-container {
  .table-container {
    .link {
      line-height: 20px;
      font-size: 14px;
      text-align: left;
      font-family: Roboto;
      color: #697cff;
    }
  }

  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 18px;
  }
}
</style>
