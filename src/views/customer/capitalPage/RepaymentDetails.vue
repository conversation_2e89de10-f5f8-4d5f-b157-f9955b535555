<template>
  <LayoutCard title="还款明细">
    <div v-loading="loading">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="
          () => {
            return { backgroundColor: '#fff1f1', color: '#000' }
          }
        "
      >
        <el-table-column type="index" label="#" width="60"></el-table-column>
        <el-table-column
          prop="repaymentNo"
          label="还款单号"
          min-width="160"
        ></el-table-column>
        <el-table-column prop="iouNo" label="借据号" min-width="160">
          <template slot-scope="scope">
            <span class="text-blue">{{ scope.row.iouNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="还款用户" min-width="220">
        </el-table-column>
        <el-table-column prop="periodDes" label="所属期数" min-width="130">
        </el-table-column>
        <el-table-column
          prop="totalAmount"
          label="应还金额(元)"
          min-width="160"
        >
        </el-table-column>
        <el-table-column
          prop="actualAmount"
          label="实还金额(元)"
          min-width="160"
        >
        </el-table-column>
        <el-table-column prop="createTime" label="创建日期" min-width="180">
        </el-table-column>
        <el-table-column prop="repaymentTime" label="还款日期" min-width="180">
        </el-table-column>
        <el-table-column
          prop="repaymentTypeText"
          label="还款类型"
          min-width="140"
        >
          <template slot-scope="scope">
            <span class="border-box" v-if="scope.row.repaymentTypeText">
              {{ scope.row.repaymentTypeText }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="100">
          <template slot-scope="scope">
            <span :class="getStatusColor(scope.row.status)">{{
              getStatusText(scope.row.status)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="操作时间" min-width="200">
        </el-table-column>
        <el-table-column prop="userName" label="操作人" min-width="120">
        </el-table-column>
        <el-table-column prop="action" label="操作" min-width="180">
          <template slot-scope="scope">
            <el-button type="text" @click="handleDetail(scope.row)"
              >明细</el-button
            >
            <!-- <el-button
              type="text"
              v-if="scope.row.status === 2"
              @click="handleEditStatus(scope.row)"
              >修改状态</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
      <div class="repayment-pagination" v-if="tableData.length">
        <el-pagination
          background
          hide-on-single-page
          layout="prev, pager, next"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :current-page="pagination.currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      title="明细"
      append-to-body
      width="35%"
      :visible.sync="statusBox"
    >
      <div class="detail-box">
        <div class="children-box">
          <span class="laber-box">本金(元)</span>
          <span class="value-box">￥{{ rowItem.principal | formatMoney }}</span>
        </div>
        <div class="children-box">
          <span class="laber-box">利息(元)</span>
          <span class="value-box">￥{{ rowItem.interest | formatMoney }}</span>
        </div>
        <template v-if="rowItem.repaymentFeeList && rowItem.repaymentFeeList.length">
          <div class="children-box" v-for="item in rowItem.repaymentFeeList" :key="item.id">
            <span class="laber-box">{{ item.feeName }}</span>
            <span class="value-box">￥{{ item.shouldAmount | formatMoney }}</span>
          </div>
        </template>
        <!-- <div class="children-box">
          <span class="laber-box">手续费(元)</span>
          <span class="value-box">{{
            rowItem.serviceCharge | formatMoney
          }}</span>
        </div>
        <div class="children-box">
          <span class="laber-box">逾期利息(元)</span>
          <span class="value-box"></span>
        </div> -->
        <div class="children-box">
          <span class="laber-box">应还金额(元)</span>
          <span class="value-box"
          >￥{{ (rowItem.totalAmount || 0) | formatMoney }}</span
          >
        </div>
        <div class="children-box">
          <span class="laber-box">实还金额(元)</span>
          <span class="value-box"
            >￥{{ rowItem.actualAmount | formatMoney }}</span
          >
        </div>
        <div class="children-box">
          <span class="laber-box">修改时间</span>
          <span class="value-box">{{ rowItem.updateTime }}</span>
        </div>
        <div class="children-box" v-if="rowItem.status === 3">
          <span class="laber-box">还款凭证</span>
          <div class="value-link">
            <el-button
              v-if="imgSrcList.length"
              type="text"
              @click="handlePreviewImage"
              >查看图片</el-button
            >
            <template v-if="pdfSrcList.length">
              <el-button
                type="text"
                v-for="(item, index) in pdfSrcList"
                :key="item.link"
                @click="handlePreviewPdf(item.link)"
                >查看附件{{ index + 1 }}</el-button
              >
            </template>
          </div>
        </div>
        <div class="children-box" v-else>
          <span class="laber-box">说明</span>
          <span class="value-box">{{ rowItem.failReason }}</span>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="statusBox = false">取 消</el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="修改状态"
      append-to-body
      width="35%"
      :visible.sync="statusBox2"
    >
      <el-form
        :model="formList"
        :rules="rules"
        class="rule-form"
        ref="formName"
      >
        <el-form-item label="状态类型:" prop="status">
          <el-select
            style="width: 100%"
            v-model="formList.status"
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in statusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <div class="pay-box" v-if="formList.status == 3">
            <div class="children-box">
              <span class="label-box">应还本金(元)：</span>
              <span class="value-box">￥{{ rowItem.principal || '--' }}</span>
            </div>
          </div>
        </el-form-item>

        <template v-if="formList.status == 3">
          <el-form-item label="实还金额:" prop="amount" key="amount">
            <el-input placeholder="请输入实还金额" v-model="formList.amount">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="还款时间:" prop="endDate" key="endDate">
            <el-date-picker
              :picker-options="pickerOptions"
              style="width: 100%"
              v-model="formList.endDate"
              type="datetime"
              placeholder="选择还款时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="还款凭证" prop="voucher" key="voucher">
            <BaseImageUpload
              :imgData.sync="formList.voucher"
              :disabled="false"
              :length="1"
            ></BaseImageUpload>
          </el-form-item>
        </template>
        <template v-else-if="formList.status == 4">
          <el-form-item label="失败原因:" prop="remark" key="remark">
            <el-input
              type="textarea"
              v-model="formList.remark"
              placeholder="请输入失败原因"
            ></el-input>
          </el-form-item>
        </template>

        <el-form-item>
          <div style="text-align: right">
            <el-button @click="statusBox2 = false">取 消</el-button>
            <el-button
              type="primary"
              @click="handleSubmit('formName')"
              :loading="submitLoading"
              >确 定</el-button
            >
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
    <FilePreview :url="pdfSrc" />
  </LayoutCard>
</template>

<script>
import { getRepaymentListData } from '@/api/customer/capital'
import LayoutCard from '../archives/components/LayoutCard/index.vue'
import { formatMoney } from '@/util/filter'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import BaseImageUpload from '@/components/BaseImageUpload'
import FilePreview from '@/components/file-preview'
import { updateCloudPaymentList } from '@/api/cloud/cloudpaymentlist'
import dayjs from 'dayjs'
export default {
  name: 'WhiteListCompany',
  components: { LayoutCard, BaseImageUpload, FilePreview },
  props: {
    companyId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: true,
      tableData: [],
      pagination: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      loanRepayMentConfig: {},
      statusBox: false,
      statusBox2: false,
      rowItem: {},
      statusOption: [],
      formList: {
        status: null,
        amount: '',
        endDate: '',
        remark: '',
        voucher: [],
        total: 0,
      },
      rules: {
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        amount: [
          { required: true, message: '请输入实还金额', trigger: 'blur' },
        ],
        endDate: [
          { required: true, message: '请选择还款时间', trigger: 'change' },
        ],
        remark: [
          { required: true, message: '请输入失败原因', trigger: 'change' },
        ],
        voucher: [
          { required: true, message: '请上传还款凭证', trigger: 'change' },
        ],
      },
      submitLoading: false,
      imgSrcList: [],
      pdfSrcList: [],
      pdfSrc: null,
    }
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      }
    },
  },
  created() {
    this.getDictionaryType()
    this.getDictionaryStatus()
  },
  mounted() {
    if (this.companyId) {
      this.getList({
        current: this.pagination.currentPage,
        size: this.pagination.pageSize,
        capitalIdEqual: this.companyId,
      })
    }
  },
  methods: {
    handleDetail(row) {
      this.rowItem = { ...row }
      this.statusBox = true
      if (row.attachList) {
        for (const item of row.attachList) {
          if (item.link.includes('pdf')) {
            this.pdfSrcList.push(item)
          } else {
            this.imgSrcList.push({
              ...item,
              url: item.link,
            })
          }
        }
      }
    },
    handleEditStatus(row) {
      this.rowItem = { ...row, principal: formatMoney(row.principal) }
      this.statusBox2 = true
    },
    // 图片查看
    handlePreviewImage() {
      this.$ImagePreview(this.imgSrcList, 0, {
        closeOnClickModal: true,
      })
    },
    // pdf查看
    handlePreviewPdf(targetUrl) {
      this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
    },
    // 提交修改状态
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.submitLoading = true
          let list = []
          if (this.formList.voucher && this.formList.voucher.length) {
            this.formList.voucher.forEach(item => {
              list.push(item.id)
            })
            this.formList.cloudAttachId = list.join(',')
          }
          let params = {
            id: this.rowItem.id,
            status: Number(this.formList.status),
          }
          if (this.formList.status == 3) {
            Object.assign(params, {
              endDate: this.formList.endDate,
              cloudAttachId: this.formList.cloudAttachId,
            })
          } else if (this.formList.status == 4) {
            Object.assign(params, {
              failReason: this.formList.remark,
            })
          }
          updateCloudPaymentList(params)
            .then(({ data }) => {
              if (data.code === 200) {
                this.$message.success('修改成功!')
                this.statusBox2 = false
                this.onLoad(this.page, this.query)
              }
              this.submitLoading = false
            })
            .catch(() => {
              this.submitLoading = false
            })
        } else {
          this.$message.error('请完善信息!')
          return false
        }
      })
    },

    // 还款类型
    getDictionaryType() {
      let list = {}
      getDictionary('loan_repayment_type').then(({ data }) => {
        if (data.code === 200 && data.data) {
          for (const item of data.data) {
            list[item.dictKey] = item.dictValue
          }
        }
        this.loanRepayMentConfig = list
      })
    },

    getDictionaryStatus() {
      let list = []
      getDictionary('loan_repayment_status').then(({ data }) => {
        if (data.code === 200 && data.data) {
          for (const item of data.data) {
            list.push({
              ...item,
              label: item.dictValue,
              value: Number(item.dictKey),
            })
          }
        }
        this.statusOption = list
      })
    },
    // 还款明细数据
    getList(params) {
      this.loading = true
      let list = []
      getRepaymentListData(params)
        .then(({ data }) => {
          if (data.code === 200) {
            this.pagination.total = data.data.total || 0
            if (data.data.records) {
              data.data.records.forEach(item => {
                list.push({
                  ...item,
                  actualAmount: item.actualAmount
                    ? formatMoney(item.actualAmount)
                    : '--',
                  totalAmount: item.totalAmount
                    ? formatMoney(item.totalAmount)
                    : '--',
                  createTime: item.createTime
                    ? dayjs(item.createTime).format('YYYY-MM-DD')
                    : '',
                  repaymentTime: item.repaymentTime
                    ? dayjs(item.repaymentTime).format('YYYY-MM-DD')
                    : '',
                  repaymentTypeText:
                    this.loanRepayMentConfig[item.repaymentType],
                })
              })
            }
            this.tableData = list
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 分页触发事件
    handleCurrentChange(current) {
      this.pagination.currentPage = current
      this.getList({
        current,
        size: this.pagination.pageSize,
        capitalIdEqual: this.companyId,
      })
    },

    // 状态
    getStatusText(state) {
      let text = ''
      switch (state) {
        case 1:
          text = '未支付'
          break
        case 2:
          text = '支付中'
          break
        case 3:
          text = '已支付'
          break
        case 4:
          text = '支付失败'
          break
        case 5:
          text = '已重提'
          break
        case 6:
          text = '已撤销'
          break
        case 7:
          text = '已失效'
          break
      }
      return text
    },

    getStatusColor(state) {
      let textColor = ''
      if ([1, 5, 6, 7].includes(state)) {
        textColor = 'text-status-gray'
      } else if ([2].includes(state)) {
        textColor = 'text-status-blue'
      } else if ([3].includes(state)) {
        textColor = 'text-status-green'
      } else if ([4].includes(state)) {
        textColor = 'text-status-red'
      }
      return textColor
    },
  },
}
</script>

<style lang="scss" scoped>
.text-blue {
  line-height: 20px;
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  font-family: Roboto;
}
.border-box {
  display: inline-block;
  color: #101010;
  background-color: #eaecf1;
  border-radius: 36px;
  font-size: 14px;
  font-weight: 500;
  padding: 3px 12px;
  box-sizing: border-box;
}
.repayment-pagination {
  text-align: right;
  margin-top: 26px;
}
.detail-box {
  display: flex;
  flex-direction: column;
  margin-left: 17px;
  margin-top: -30px;

  .children-box {
    display: flex;
    // align-items: center;
    margin-top: 20px;

    .laber-box {
      width: 95px;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
    .value-box {
      width: 370px;
      height: 20px;
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
    ::v-deep {
      .el-button {
        padding-top: 0;
        line-height: 20px;
        padding-bottom: 12px !important;
        margin-left: 10px;
      }
    }
    .value-link {
      margin-left: -10px;
      display: flex;
      align-items: center;
      flex-flow: row wrap;
    }
  }
}
.text-status-gray {
  color: #646464;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.text-status-blue {
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.text-status-green {
  color: #1fc374;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.text-status-red {
  color: #ff4d4d;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.rule-form {
  padding: 0 15px;
  ::v-deep {
    .el-form-item {
      display: flex;
      .el-form-item__label {
        width: 110px;
        max-width: 110px;
        text-align: left;
      }
      .el-form-item__content {
        flex-grow: 1;
      }
    }
  }
  .pay-box {
    padding: 20px 12px;
    width: 100%;
    background-color: #f7f7f7;
    border-radius: 6px;

    box-sizing: border-box;
    margin-top: 9px;

    .children-box {
      display: flex;
      align-items: center;
      line-height: 20px;
      margin-top: 20px;
      .laber-box {
        width: 95px;
        color: rgba(153, 153, 153, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
      }
      .value-box {
        width: 370px;
        height: 20px;
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
      }
    }
  }
}
</style>
