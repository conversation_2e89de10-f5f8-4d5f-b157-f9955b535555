<template>
  <LayoutCard title="放款记录">
    <div v-loading="loading">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="
          () => {
            return { backgroundColor: '#fff1f1', color: '#000' }
          }
        "
      >
        <el-table-column type="index" label="#" width="60"></el-table-column>
        <el-table-column prop="financeNo" label="融资编号" min-width="120">
          <template slot-scope="{ row }">
            <span class="text-blue">{{ row.financeNo }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="iouNo"
          label="借据单号"
          min-width="120"
        ></el-table-column>
        <el-table-column prop="iouType" label="借据类型">
          <template slot-scope="{ row }">
            <span class="border-box" v-if="row.iouType">{{
              row.iouType === 1
                ? '正常借据'
                : row.iouType === 2
                ? '展期借据'
                : ''
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="融资用户" min-width="120">
        </el-table-column>
        <el-table-column prop="iouAmount" label="借款金额(元)">
        </el-table-column>
        <el-table-column prop="loanTime" label="放款日"> </el-table-column>
        <el-table-column prop="iouAmount" label="放款金额(元)">
        </el-table-column>
        <el-table-column prop="period" label="期限">
          <template slot-scope="{ row }">
            <span>{{ row.period ? row.periodDes : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="expireTime" label="到期日"> </el-table-column>
        <el-table-column prop="fiveLevelCategory" label="五级分类">
          <template slot-scope="{ row }">
            <span
              class="border-level"
              :class="row.fiveLevelCategory === 1 ? 'level-gray' : 'level-red'"
              v-if="row.fiveLevelCategory !== null"
              >{{ levelConfig[row.fiveLevelCategory] }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="statusText" label="状态">
          <template slot-scope="{ row }">
            <span>{{ statusTextConfig[row.status] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="repayment-pagination" v-if="tableData.length">
        <el-pagination
          background
          hide-on-single-page
          layout="prev, pager, next"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :current-page="pagination.currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </LayoutCard>
</template>

<script>
import { getCapitalLoanData } from '@/api/customer/capital'
import LayoutCard from '../archives/components/LayoutCard/index.vue'
import { formatMoney } from '@/util/filter'

export default {
  name: 'WhiteListCompany',

  components: { LayoutCard },
  props: {
    companyId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableData: [],
      loading: true,
      pagination: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      levelConfig: {
        1: '正常',
        2: '关注',
        3: '次级',
        4: '可疑',
        5: '损失',
      },
      statusTextConfig: {
        1: '待结清',
        2: '逾期未结清',
        3: '提前结清',
        4: '正常结清',
        5: '逾期结清',
        6: '展期已核销',
        7: '坏账已核销',
      },
    }
  },
  mounted() {
    if (this.companyId) {
      this.getList({
        current: this.pagination.currentPage,
        size: this.pagination.pageSize,
        companyId: this.companyId,
      })
    }
  },
  methods: {
    getList(params) {
      let list = []
      getCapitalLoanData(params)
        .then(({ data }) => {
          this.loading = false
          this.pagination.total = data.data.total || 0
          if (data.code === 200 && data.data) {
            if (data.data.records) {
              data.data.records.forEach((item, index) => {
                list.push({
                  ...item,
                  index: index + 1 + (this.pagination.currentPage - 1) * 10,
                  iouAmount: formatMoney(item.iouAmount || 0),
                })
              })
            }
          }
          this.tableData = list
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 分页触发事件
    handleCurrentChange(current) {
      this.pagination.currentPage = current
      this.getList({
        current,
        size: this.pagination.pageSize,
        companyId: this.companyId,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.text-blue {
  line-height: 20px;
  color: #697cff;
  font-size: 14px;
  font-weight: 500;
  font-family: Roboto;
}
.border-box {
  display: inline-block;
  color: #101010;
  background-color: #eaecf1;
  border-radius: 36px;
  font-size: 14px;
  font-weight: 500;
  padding: 3px 12px;
  box-sizing: border-box;
}
.repayment-pagination {
  text-align: right;
  margin-top: 26px;
}
.border-level {
  display: inline-block;
  border-radius: 42px;
  padding: 3px 12px;
  font-weight: 500;
  box-sizing: border-box;
  font-size: 14px;
  font-family: Microsoft Yahei;
}
.level-red {
  background-color: #ff4d4d;
  color: #fb3030;
}
.level-gray {
  background-color: #eaecf1;
  color: #101010;
}
</style>
