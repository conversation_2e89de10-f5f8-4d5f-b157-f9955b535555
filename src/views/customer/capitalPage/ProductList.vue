<template>
  <div class="asset-info-container">
    <LayoutCard title="产品信息">
      <div class="content">
        <div class="label-bar-container">
          <el-tabs v-model="activeTab">
            <el-tab-pane
              v-for="item of tabList"
              :label="item"
              :name="item"
              :key="item"
            />
          </el-tabs>
        </div>

        <div class="table-container">
          <div v-if="activeTab === '应收账款质押'">
            <CommonTable
              page="goods"
              :table-data="tableData1"
              :columnOption="columnOption1"
              :total="pagination1.total"
              :current.sync="pagination1.current"
              :pageSize="pagination1.pageSize"
              @update="updateList"
            />
          </div>
          <div v-else-if="activeTab === '代采融资'">
            <CommonTable
              page="agentGoods"
              :table-data="tableData2"
              :columnOption="columnOption2"
              :total="pagination2.total"
              :current.sync="pagination2.current"
              :pageSize="pagination2.pageSize"
              @update="updateList2"
            />
          </div>
          <div v-else-if="activeTab === '云信'">
            <CommonTable
              page="cloudGoods"
              :table-data="tableData3"
              :columnOption="columnOption3"
              :total="pagination3.total"
              :current.sync="pagination3.current"
              :pageSize="pagination3.pageSize"
              @update="updateList3"
            />
          </div>
          <div v-else-if="activeTab === '动产质押'">
            <CommonTable
              :table-data="tableData4"
              :columnOption="columnOption1"
              :total="pagination4.total"
              :current.sync="pagination4.current"
              :pageSize="pagination4.pageSize"
              @update="updateList4"
            />
          </div>
          <div v-else-if="activeTab === '订单融资'">
            <CommonTable
              :table-data="tableData5"
              :columnOption="columnOption1"
              :total="pagination5.total"
              :current.sync="pagination5.current"
              :pageSize="pagination5.pageSize"
              @update="updateList5"
            />
          </div>
        </div>
      </div>
    </LayoutCard>
  </div>
</template>

<script>
import LayoutCard from '../archives/components/LayoutCard/index.vue'
import CommonTable from './Table/CommonTable'

import {
  getLoanProductTableData,
  getAgentProductTableData,
  getCloudProductTableData,
} from '@/api/customer/capital'

const columnOption1 = [
  {
    prop: 'goodsCode',
    label: '产品编号',
    minWidth: '110',
  },
  {
    prop: 'type',
    label: '产品类型',
    minWidth: '125',
  },
  {
    prop: 'goodsName',
    label: '产品名称',
    minWidth: '130',
  },
  {
    prop: 'loanAmountStr',
    label: '借款金额(万)',
  },
  {
    prop: 'loadTermStr',
    label: '借款期限',
  },
  {
    prop: 'updateTime',
    label: '上次修改时间',
    minWidth: '120',
  },
  {
    prop: 'operator',
    label: '操作人',
  },
  {
    prop: 'status',
    label: '状态 ',
  },
  {
    prop: 'action',
    label: '操作',
  },
]
const columnOption2 = [
  {
    prop: 'goodsCode',
    label: '产品编号',
  },
  {
    prop: 'type',
    label: '产品类型',
  },
  {
    prop: 'goodsName',
    label: '产品名称',
  },
  {
    prop: 'loanAmountStr',
    label: '借款金额(万)',
  },
  {
    prop: 'loadTermStr',
    label: '借款期限',
  },
  {
    prop: 'updateTime',
    label: '上次修改时间',
  },
  {
    prop: 'operator',
    label: '操作人',
  },
  {
    prop: 'status',
    label: '状态',
  },
  {
    prop: 'action',
    label: '操作',
  },
]
const columnOption3 = [
  {
    prop: 'goodsCode',
    label: '产品编号',
  },
  {
    prop: 'type',
    label: '产品类型',
  },
  {
    prop: 'goodsName',
    label: '产品名称',
  },
  {
    prop: 'loanAmountStr',
    label: '借款金额(万)',
  },
  {
    prop: 'loadTermStr',
    label: '借款期限',
  },
  {
    prop: '',
    label: '最低年利率(%)',
  },
  {
    prop: 'updateTime',
    label: '上次修改时间',
  },
  {
    prop: 'operator',
    label: '操作人',
  },
  {
    prop: 'status',
    label: '状态',
  },
  {
    prop: 'action',
    label: '操作',
  },
]
export default {
  name: 'ProductList',
  components: {
    LayoutCard,
    CommonTable,
  },
  props: {
    companyId: {
      type: [String, undefined],
      default: undefined,
    },
  },
  watch: {
    'pagination1.pageSize': {
      handler(size) {
        this.getLoanProductTableData({
          current: this.pagination1.current,
          size,
          typeEqual: 1,
          companyId: this.companyId,
        })
      },
      immediate: true,
    },
    'pagination2.pageSize': {
      handler(size) {
        this.getAgentProductTableData({
          current: this.pagination2.current,
          size,
          typeEqual: 2,
          companyId: this.companyId,
        })
      },
      immediate: true,
    },
    'pagination3.pageSize': {
      handler(size) {
        this.getCloudProductTableData({
          current: this.pagination3.current,
          size,
          typeEqual: 3,
          companyId: this.companyId,
        })
      },
      immediate: true,
    },
    'pagination4.pageSize': {
      handler(size) {
        this.getLoanProductTableData4({
          current: this.pagination4.current,
          size,
          typeEqual: 4,
          companyId: this.companyId,
        })
      },
      immediate: true,
    },
    'pagination5.pageSize': {
      handler(size) {
        this.getLoanProductTableData5({
          current: this.pagination5.current,
          size,
          typeEqual: 5,
          companyId: this.companyId,
        })
      },
      immediate: true,
    },
  },
  data() {
    return {
      tabList: ['应收账款质押', '代采融资', '云信', '动产质押', '订单融资'],
      activeTab: '应收账款质押',
      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData5: [],
      pagination1: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination2: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination3: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination4: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination5: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      columnOption1,
      columnOption2,
      columnOption3,
    }
  },
  methods: {
    updateList({ current, pageSize = 10 }) {
      this.pagination1.current = current
      this.getLoanProductTableData({ current, size: pageSize,companyId: this.companyId, typeEqual: 1 })
    },
    updateList2({ current, pageSize = 10 }) {
      this.pagination2.current = current
      this.getAgentProductTableData({ current, size: pageSize,companyId: this.companyId, typeEqual: 2 })
    },
    updateList3({ current, pageSize = 10 }) {
      this.pagination3.current = current
      this.getCloudProductTableData({ current, size: pageSize,companyId: this.companyId, typeEqual: 3 })
    },
    updateList4({ current, pageSize = 10 }) {
      this.pagination4.current = current
      this.getLoanProductTableData4({ current, size: pageSize,companyId: this.companyId, typeEqual: 4 })
    },
    updateList5({ current, pageSize = 10 }) {
      this.pagination5.current = current
      this.getLoanProductTableData5({ current, size: pageSize,companyId: this.companyId, typeEqual: 5 })
    },

    // 应收账款列表
    getLoanProductTableData(params) {
      let list = []
      getLoanProductTableData(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          this.pagination1.total = data.data.total || 0
          if (data.data.records) {
            for (const item of data.data.records) {
              list.push({
                ...item,
              })
            }
          }
        }
        this.tableData1 = list
      })
    },
    // 代采融资列表
    getAgentProductTableData(params) {
      let list = []
      getAgentProductTableData(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          this.pagination2.total = data.data.total || 0
          if (data.data.records) {
            for (const item of data.data.records) {
              list.push({
                ...item,
              })
            }
          }
        }
        this.tableData2 = list
      })
    },
    // 云信融资列表
    getCloudProductTableData(params) {
      let list = []
      getCloudProductTableData(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          this.pagination3.total = data.data.total || 0
          if (data.data.records) {
            for (const item of data.data.records) {
              list.push({
                ...item,
              })
            }
          }
        }
        this.tableData3 = list
      })
    },
    // 动产质押列表
    getLoanProductTableData4(params) {
      let list = []
      getLoanProductTableData(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          this.pagination4.total = data.data.total || 0
          if (data.data.records) {
            for (const item of data.data.records) {
              list.push({
                ...item,
              })
            }
          }
        }
        this.tableData4 = list
      })
    },
    // 订单融资列表
    getLoanProductTableData5(params) {
      let list = []
      getLoanProductTableData(params).then(({ data }) => {
        if (data.code === 200 && data.data) {
          this.pagination5.total = data.data.total || 0
          if (data.data.records) {
            for (const item of data.data.records) {
              list.push({
                ...item,
              })
            }
          }
        }
        this.tableData5 = list
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.table-container {
  .table-container {
    margin-top: 18px;
    .link {
      line-height: 20px;
      font-size: 14px;
      text-align: left;
      font-family: Roboto;
      color: #697cff;
    }
  }

  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 18px;
  }
}
</style>
