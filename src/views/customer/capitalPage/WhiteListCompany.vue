<template>
  <LayoutCard title="白名单列表">
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="
        () => {
          return { backgroundColor: '#f7f7f7', color: '#000' }
        }
      "
    >
      <el-table-column type="index" label="#" width="60"></el-table-column>
      <el-table-column prop="logo" label="logo"></el-table-column>
      <el-table-column prop="companyName" label="企业名称"></el-table-column>
      <el-table-column prop="percent" label="统一社会代码"> </el-table-column>
      <el-table-column prop="percent" label="用户类型"> </el-table-column>
      <el-table-column prop="percent" label="所属行业"> </el-table-column>
      <el-table-column prop="percent" label="绑定事件"> </el-table-column>
      <el-table-column prop="action" label="操作"> </el-table-column>
    </el-table>
  </LayoutCard>
</template>

<script>
import LayoutCard from '../archives/components/LayoutCard/index.vue'
export default {
  name: 'WhiteListCompany',
  components: { LayoutCard },
  data() {
    return {
      tableData: [],
    }
  },
}
</script>

<style></style>
