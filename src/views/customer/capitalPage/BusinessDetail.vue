<template>
  <div class="capital-particulars-for">
    <div class="capital-particulars-box-for">
      <div class="capital-title-particulars-box-for">
        <span />
        <h2>业务总览</h2>
      </div>
      <div class="capital-box">
        <div
          class="capital-for-particulars"
          v-for="item in particularsArr"
          :key="item.id"
        >
          <div class="capital-for-box-particulars">
            <div class="capital-num-box-particulars">
              <div class="capital-money-box">
                <span>{{ item.moneyNum | formatMoney }}</span>
                <span>元</span>
              </div>
              <span class="capital-explain-box">{{ item.explain }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { formatMoney } from '@/util/filter'
import { cusCapitalQuotaDetail } from '@/api/customer/capital'

export default {
  name: 'BusinessDetail',
  props: {
    companyId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      particularsArr: [],
    }
  },
  mounted() {
    if (this.companyId) {
      this.getDetail()
    }
  },
  methods: {
    getDetail(){
        // 资方金额详情
      cusCapitalQuotaDetail(this.companyId).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          const dat = resData.data
          this.particularsArr = [
            {
              id: 1,
              moneyNum: dat.creditTotal,
              explain: '授信总额',
            },
            {
              id: 2,
              moneyNum: dat.acAmount,
              explain: '可授信额度',
            },
            {
              id: 3,
              moneyNum: dat.acGranted,
              explain: '已授信额度',
            },
            {
              id: 4,
              moneyNum: dat.amountReleased,
              explain: '总放款额度',
            },
            {
              id: 5,
              moneyNum: dat.penPrincipal,
              explain: '待回款本金',
            },
            {
              id: 6,
              moneyNum: dat.penCost,
              explain: '待回款利息',
            },
            {
              id: 7,
              moneyNum: dat.overdueAmount,
              explain: '已逾期金额',
            },
            {
              id: 8,
              moneyNum: dat.totalPrincipal,
              explain: '总回款本金',
            },
            {
              id: 9,
              moneyNum: dat.totalExpenses,
              explain: '总回款利息',
            },
          ]
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.capital-particulars-for {
  background-color: #fff;
  width: 100%;
  border-radius: 8px;
  margin-top: 20px;

  .capital-particulars-box-for {
    width: 100%;
    height: 100%;
    padding: 20px 0 0;
    box-sizing: border-box;

    .capital-title-particulars-box-for {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 12px;
      padding: 0 20px;
      box-sizing: border-box;

      & > span:first-child {
        width: 8px;
        height: 16px;
        line-height: 20px;
        border-radius: 15px;
        background-color: rgba(18, 119, 255, 100);
      }

      & > h2 {
        width: 319px;
        height: 24px;
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        text-align: left;
        margin-left: 4px;
      }
    }

    .capital-box {
      display: flex;
      flex-wrap: wrap;

      .capital-for-particulars {
        width: calc(100% / 4);
        padding: 0 0 20px 20px;
        box-sizing: border-box;

        .capital-for-box-particulars {
          height: 131px;
          border-radius: 8px;
          background-color: rgba(247, 247, 247, 100);
          padding: 20px;
          box-sizing: border-box;

          .capital-num-box-particulars {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;

            .capital-money-box {
              display: flex;
              justify-content: center;
              align-items: baseline;
              cursor: context-menu;

              & > span:first-child {
                color: rgba(18, 119, 255, 100);
                font-size: 22px;
              }

              & > span:last-child {
                color: rgba(18, 119, 255, 100);
                font-size: 13px;
              }
            }

            .capital-explain-box {
              height: 20px;
              line-height: 20px;
              color: rgba(16, 16, 16, 100);
              font-size: 14px;
              text-align: center;
              cursor: context-menu;
            }

            .capital-lock-box {
              text-align: center;
              cursor: pointer;

              & span:first-child {
                width: 80px;
                height: 30px;
                line-height: 30px;
                border-radius: 4px;
                color: rgba(105, 124, 255, 100);
                font-size: 14px;
                border: 1px solid rgba(105, 124, 255, 100);
              }
            }
          }
        }
      }
    }
  }
}
</style>
