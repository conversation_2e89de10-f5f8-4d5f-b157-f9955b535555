<template>
  <Dialog
    title="查看详情"
    ref="DialogRef"
    center
    noConfirmBtn
    cancelBtnText="关闭"
  >
    <div v-loading="loading" class="content">
      <div class="custom-desc-container">
        <el-descriptions
          class="margin-top"
          :column="1"
          :labelStyle="{
            width: '150px',
            textAlign: 'right',
            color: 'rgba(153, 153, 153, 100)',
            fontSize: '14px',
            marginBottom: '24px',
          }"
          :contentStyle="{
            color: 'rgba(38, 38, 38, 100)',
            fontSize: '14px',
          }"
        >
          <el-descriptions-item label="公司名称">{{
            detailData.companyName || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="开户行">{{
            detailData.companyName || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="银行账户">{{
            detailData.companyName || '--'
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'

export default {
  name: 'TradeDetailRepaymentAccountViewDetailDialogIndex',
  components: { Dialog },
  data: function () {
    return {
      detailData: {},
    }
  },
  methods: {
    handleOpen() {
      this.$refs.DialogRef.handleOpen()
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
  .custom-desc-container {
    padding: 0 24px;
    margin-top: 24px;

    ::v-deep {
      .el-descriptions-item__cell {
        padding: 0;
      }
    }
  }
}
</style>
