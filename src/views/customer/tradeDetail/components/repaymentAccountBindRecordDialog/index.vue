<template>
  <Dialog
    title="查看详情"
    ref="DialogRef"
    center
    noConfirmBtn
    cancelBtnText="关闭"
  >
    <div v-loading="loading" class="content">
      <div class="card-container">
        <div class="progress-bar-wrapper">
          <span class="point" />
          <span class="line" />
        </div>
        <div class="card-content">
          <div class="date">
            <span>2021-10-14 17:14:30</span>
          </div>
          <div class="card-msg-container">
            <div class="form-item">
              <span class="form-title">任务编号：</span>
              <span class="form-value">923891284781273</span>
            </div>
            <div class="form-item">
              <span class="form-title">开户行：</span>
              <span class="form-value">中国人民银行深圳分行宝安支行</span>
            </div>
            <div class="form-item">
              <span class="form-title">银行卡号：</span>
              <span class="form-value">59123081293812931221367</span>
            </div>
            <div class="form-item">
              <span class="form-title">变更凭证：</span>
              <el-image
                style="width: 100px; height: 100px"
                :src="'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'"
                :preview-src-list="[
                  'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                ]"
              >
              </el-image>
            </div>
          </div>
        </div>
      </div>
      <div class="card-container">
        <div class="progress-bar-wrapper">
          <span class="point" />
          <span class="line" />
        </div>
        <div class="card-content">
          <div class="date">
            <span>2021-10-14 17:14:30</span>
          </div>
          <div class="card-msg-container">
            <div class="form-item">
              <span class="form-title">任务编号：</span>
              <span class="form-value">923891284781273</span>
            </div>
            <div class="form-item">
              <span class="form-title">开户行：</span>
              <span class="form-value">中国人民银行深圳分行宝安支行</span>
            </div>
            <div class="form-item">
              <span class="form-title">银行卡号：</span>
              <span class="form-value">59123081293812931221367</span>
            </div>
            <div class="form-item">
              <span class="form-title">变更凭证：</span>
              <el-image
                style="width: 100px; height: 100px"
                :src="'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'"
                :preview-src-list="[
                  'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                ]"
              >
              </el-image>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'

export default {
  name: 'TradeDetailRepaymentAccountBindRecordDialog',
  components: { Dialog },
  data: function () {
    return {
      detailData: {},
    }
  },
  methods: {
    handleOpen() {
      this.$refs.DialogRef.handleOpen()
    },
    handleClose() {
      this.$refs.DialogRef.handleClose()
    },
  },
}
</script>

<style lang="scss" scoped>
$pointWidthHeight: 12px;
$cardMarginBottom: 32px;

.content {
  padding: 24px 24px 24px 48px;
  margin: 24px;

  .card-container {
    position: relative;
    margin-bottom: $cardMarginBottom;

    &:last-child {
      margin-bottom: 0;

      .progress-bar-wrapper {
        .line {
          display: none;
        }
      }
    }

    .progress-bar-wrapper {
      position: absolute;
      top: 10px - $pointWidthHeight / 2;
      left: -24px;
      bottom: -($cardMarginBottom + 10px - $pointWidthHeight / 2);

      .point {
        position: absolute;
        top: 0;
        display: inline-block;
        width: $pointWidthHeight;
        height: $pointWidthHeight;
        background-color: rgba(215, 215, 215, 100);
        border-radius: 50%;
      }

      .line {
        position: absolute;
        left: $pointWidthHeight / 2 - 1px;
        display: inline-block;
        width: 2px;
        height: 100%;
        background-color: rgba(215, 215, 215, 100);
      }
    }

    .card-content {
      .date {
        margin-bottom: 12px;
        height: 20px;
        color: rgba(106, 106, 106, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }

      .card-msg-container {
        padding: 24px 32px;
        border-radius: 6px;
        text-align: center;
        border: 1px solid rgba(215, 215, 215, 100);

        .form-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .form-title {
            flex-shrink: 0;
            width: 70px;
            height: 20px;
            color: rgba(106, 106, 106, 100);
            font-size: 14px;
            text-align: right;
            font-family: SourceHanSansSC-regular;
          }

          .form-value {
            color: rgba(77, 0, 0, 100);
            font-size: 14px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }
    }
  }
}
</style>
