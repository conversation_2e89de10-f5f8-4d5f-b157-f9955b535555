<template>
  <div class="trade-detail-container">
    <div class="top-bar">
      <div class="height">
        <span>上游：{{ pageData.companyHeightName || '--' }}</span>
      </div>
      <div class="lower">
        <span>下游：{{ pageData.companyLowerName || '--' }}</span>
      </div>
      <span class="icon">
        <svg-icon
          icon-class="icon-line-keji"
          style="fill: #3dc861; font-size: 40px"
        />
      </span>
    </div>
    <div class="middle-container">
      <LayoutCard
        title="贸易信息"
        style="
          display: inline-block;
          width: calc(100% - 378px);
          height: 368px;
          margin: 0 20px 0 0;
          box-sizing: border-box;
        "
      >
        <div class="top-wrapper">
          <template v-if="type === 'coreEnterprise'">
            <SimpleInfoCard
              :value="
                pageData.confirmAmount !== undefined
                  ? Number(pageData.confirmAmount).toLocaleString('zh', {
                      minimumFractionDigits: 4,
                    })
                  : '--'
              "
              label="元"
              desc="总确权额度"
              color="#697CFF"
              style="min-width: auto; margin-right: 16px"
            />
            <SimpleInfoCard
              :value="
                pageData.nonPaymentAmount !== undefined
                  ? Number(pageData.nonPaymentAmount).toLocaleString('zh', {
                      minimumFractionDigits: 4,
                    })
                  : '--'
              "
              label="元"
              desc="待付款金额"
              color="#697CFF"
              style="min-width: auto; margin-right: 16px"
            />
            <SimpleInfoCard
              :value="
                pageData.creditedAmount !== undefined
                  ? Number(pageData.creditedAmount).toLocaleString('zh', {
                      minimumFractionDigits: 4,
                    })
                  : '--'
              "
              label="元"
              desc="当前授予额度"
              color="#697CFF"
              style="min-width: auto"
            />
          </template>
          <template v-else>
            <SimpleInfoCard
              :value="
                pageData.financingAvailableAmount !== undefined
                  ? Number(pageData.financingAvailableAmount).toLocaleString(
                      'zh',
                      { minimumFractionDigits: 4 }
                    )
                  : '--'
              "
              label="元"
              desc="可融资额度"
              color="#697CFF"
              style="min-width: auto; margin-right: 16px"
            />
            <SimpleInfoCard
              :value="
                pageData.financedAmount !== undefined
                  ? Number(pageData.financedAmount).toLocaleString('zh', {
                      minimumFractionDigits: 4,
                    })
                  : '--'
              "
              label="元"
              desc="已融资额度"
              color="#697CFF"
              style="min-width: auto; margin-right: 16px"
            />
            <SimpleInfoCard
              :value="
                pageData.accountReceivable !== undefined
                  ? Number(pageData.accountReceivable).toLocaleString('zh', {
                      minimumFractionDigits: 4,
                    })
                  : '--'
              "
              label="元"
              desc="待回款额度"
              color="#697CFF"
              style="min-width: auto"
            />
          </template>
        </div>
        <div class="bottom-wrapper">
          <div class="left">
            <span class="desc">其他数据</span>
            <div class="form-container">
              <div class="form-item">
                <span class="label">待确权金额:</span>
                <span class="value">
                  <template v-if="pageData.unConfirmAmount">
                    <span>{{
                      `${Number(pageData.unConfirmAmount).toLocaleString('zh', {
                        minimumFractionDigits: 4,
                      })}元`
                    }}</span>
                  </template>
                  <template v-else>--</template>
                </span>
              </div>
              <template v-if="type === 'coreEnterprise'">
                <div class="form-item">
                  <span class="label">已结清金额:</span>
                  <span class="value">
                    <template v-if="pageData.settledAmount">
                      <span>{{
                        `${Number(pageData.settledAmount).toLocaleString('zh', {
                          minimumFractionDigits: 4,
                        })}元`
                      }}</span>
                    </template>
                    <template v-else>--</template>
                  </span>
                </div>
              </template>
              <template v-else>
                <div class="form-item">
                  <span class="label">未结清金额:</span>
                  <span class="value">
                    <template v-if="pageData.unSettledAmount">
                      <span>{{
                        `${Number(pageData.unSettledAmount).toLocaleString(
                          'zh',
                          {
                            minimumFractionDigits: 4,
                          }
                        )}元`
                      }}</span>
                    </template>
                    <template v-else>--</template>
                  </span>
                </div>
              </template>
              <div class="form-item">
                <span class="label">已回款金额:</span>
                <span class="value">
                  <template v-if="pageData.settledAmount">
                    <span>{{
                      `${Number(pageData.settledAmount).toLocaleString('zh', {
                        minimumFractionDigits: 4,
                      })}元`
                    }}</span>
                  </template>
                  <template v-else>--</template>
                </span>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="header-wrapper">
              <span class="desc">交易数据</span>
              <div class="label-bar-container">
                <el-radio-group
                  v-model="transactionRadio"
                  @change="handleCardSwitch"
                  size="mini"
                >
                  <el-radio-button
                    v-for="item of ['近3月', '近6月', '近1年']"
                    :key="item"
                    :label="item"
                  ></el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="form-container">
              <div class="form-item">
                <span class="label">交易金额:</span>
                <span class="value">
                  <template
                    v-if="transactionCurrentData.transaction !== undefined"
                  >
                    <span>{{
                      `${Number(
                        transactionCurrentData.transaction
                      ).toLocaleString()}元`
                    }}</span>
                  </template>
                  <template v-else>--</template>
                </span>
              </div>
              <div class="form-item">
                <span class="label">平均回款周期:</span>
                <span class="value">{{
                  transactionCurrentData.avgRefundTime !== undefined
                    ? transactionCurrentData.avgRefundTime
                    : '--'
                }}</span>
              </div>
              <div class="form-item">
                <span class="label">回款率:</span>
                <span class="value">{{
                  transactionCurrentData.refundRate !== undefined
                    ? transactionCurrentData.refundRate
                    : '--'
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </LayoutCard>
      <LayoutCard title="关联产品" class="product-layout-card">
        <div class="product-container">
          <div class="product-name-container">
            <div class="product-name">
              <span
                class="img-wrapper"
                :style="{
                  backgroundImage: 'url(' + (productData.capitalAvatar + ')'),
                }"
              >
              </span>
              <span class="name">{{ productData.goodsName || '--' }}</span>
            </div>
            <Tag
              v-if="productData.goodsType"
              :name="goodsTypeMap[productData.goodsType].name"
              backgroundColor="#697CFF"
              color="#fff"
              radius
            />
          </div>
          <div class="valid-quota">
            <span class="title" style="display: inline-block; width: 100%"
              >当前可用额度(元)</span
            >
            <span class="value">{{
              productData.availableCredit !== undefined
                ? Number(productData.availableCredit).toLocaleString('zh', {
                    minimumFractionDigits: 2,
                  })
                : '--'
            }}</span>
          </div>
          <div class="form-item">
            <span>{{
              `日利率${
                productData.dailyInterestRate
                  ? productData.dailyInterestRate + '%'
                  : '--'
              }起（年化利率${
                productData.annualInterestRate
                  ? productData.annualInterestRate + '%'
                  : '--'
              }）`
            }}</span>
          </div>
          <div class="form-item">
            <span
              >到期日：{{
                productData.expireTime ? productData.expireTime : '--'
              }}</span
            >
          </div>
          <div class="form-item">
            <span
              >关联时间：{{
                productData.firstTradeTime ? productData.firstTradeTime : '--'
              }}</span
            >
          </div>
          <span style="flex: 1" />
          <div class="account-container">
            <span class="title">对公账户</span>
            <div class="btn-container">
              <el-button
                @click="handleRepaymentAccountViewDetail"
                size="mini"
                round
                >查看详情</el-button
              >
              <el-button
                @click="handleRepaymentAccountBindRecord"
                size="mini"
                round
                >绑定记录</el-button
              >
            </div>
          </div>
        </div>
      </LayoutCard>
    </div>
    <LayoutCard title="销售合同" style="margin-top: 20px">
      <div class="lab-container">
        <el-radio-group
          v-model="TableLabRadio"
          size="mini"
          @change="handleTableLabChange"
        >
          <el-radio-button label="应收账款" />
          <el-radio-button label="已结清" />
        </el-radio-group>
      </div>
      <div v-loading="loading" class="table-container">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          :header-cell-style="
            () => {
              return { backgroundColor: '#f7f7f7', color: '#000' }
            }
          "
        >
          <el-table-column prop="contractNo" label="资产编号" min-width="100">
            <template slot-scope="{ row }">
              <span
                >{{ row.contractNo !== null ? row.contractNo : '--' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="proofNo" label="凭证编号" min-width="200" />
          <el-table-column prop="proofType" label="凭证类型" min-width="100">
            <template slot-scope="{ row }">
              <span
                >{{
                  row.proofType !== null && proofTypeMap.ready
                    ? proofTypeMap[row.proofType]
                    : '--'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="orderAmount" label="订单金额">
            <template slot-scope="{ row }">
              <span>¥{{ row.orderAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="accountAmount" label="账面金额">
            <template slot-scope="{ row }">
              <span>¥{{ row.accountAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="effectiveAmount" label="有效金额">
            <template slot-scope="{ row }">
              <span>¥{{ row.effectiveAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="paymentDays" label="账期">
            <template slot-scope="{ row }">
              <span>{{ row.paymentDays }}天</span>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开通日期" min-width="100" />
          <el-table-column prop="expireTime" label="到期日期" min-width="100" />
          <el-table-column prop="gracePeriod" label="宽限期" min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.gracePeriod }}天</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="financingAvailableAmount"
            label="可融资额度"
            min-width="100"
          >
            <template slot-scope="{ row }">
              <span>{{
                row.financingAvailableAmount !== null
                  ? '¥' + row.financingAvailableAmount
                  : '--'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="proofStatus" label="凭证状态" min-width="100">
            <template slot-scope="{ row }">
              <Tag
                :name="proofStatusMap[row.proofStatus].name"
                :color="proofStatusMap[row.proofStatus].color"
                :backgroundColor="
                  proofStatusMap[row.proofStatus].backgroundColor
                "
                :borderColor="proofStatusMap[row.proofStatus].borderColor"
                minWidth="64px"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="options"
            label="操作"
            fixed="right"
            align="center"
            min-width="260"
          >
            <template slot-scope="{ row }">
              <el-button
                @click="handleBtnViewProof(row)"
                type="text"
                size="small"
                >查看凭证</el-button
              >
              <el-button
                @click="handleBtnApproveDetail(row)"
                type="text"
                size="small"
                >审批详情</el-button
              >
              <el-button
                v-if="row.comfirmStatus === 2"
                @click="handleBtnUseDetail(row)"
                type="text"
                size="small"
                >使用明细</el-button
              >
              <el-button
                v-if="row.comfirmStatus === 2"
                @click="handleBtnRepaymentDetail(row)"
                type="text"
                size="small"
                >回款信息</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          hide-on-single-page
          @current-change="handleTableCurrentChange"
          :current-page.sync="paginationData.currentPage"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
        >
        </el-pagination>
      </div>
    </LayoutCard>
    <DialogPreviewProof
      ref="DialogPreviewProofRef"
      :invoiceTypeMap="invoiceTypeMap"
      :proofTypeMap="proofTypeMap"
    />
    <DialogUseDetail ref="DialogUseDetailRef" />
    <DialogReplaymentDetail ref="DialogReplaymentDetailRef" />
    <!-- 还款账户-查看详情 -->
    <RepaymentAccountViewDetailDialog
      ref="repaymentAccountViewDetailDialogRef"
    />
    <!-- 还款账户-绑定记录 -->
    <RepaymentAccountBindRecordDialog
      ref="repaymentAccountBindRecordDialogRef"
    />
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import LayoutCard from '@/views/customer/archives/components/LayoutCard/index.vue'
import SimpleInfoCard from '@/views/customer/archives/components/SimpleInfoCard/index.vue'
import Tag from '@/views/customer/archives/components/Tag/index.vue'
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import { proofStatusMap, goodsTypeMap } from './config'
import {
  requestClientLowerDetail,
  requestClientLowerDetailContractList,
} from '@/api/customer/archives/client'
import {
  requestCoreHeightDetail,
  requestCoreHeightDetailContractList,
} from '@/api/customer/archives/core'
import {
  requestDetailBindGood,
  requestProcessInfo,
} from '@/api/customer/archives/archive'
import { getDictionary } from '@/api/system/dictbiz'
import { routerMapKeyToPath } from '@/views/business/config'
import DialogPreviewProof from '@/views/customer/archives/components/ModuleAssetInformation/components/DialogPreviewProof/index.vue'
import DialogUseDetail from '@/views/customer/archives/components/ModuleAssetInformation/components/DialogUseDetail/index.vue'
import DialogReplaymentDetail from '@/views/customer/archives/components/ModuleAssetInformation/components/DialogRepaymentDetail/index.vue'
import RepaymentAccountViewDetailDialog from './components/repaymentAccountViewDetailDialog/index.vue'
import RepaymentAccountBindRecordDialog from './components/repaymentAccountBindRecordDialog/index.vue'

const initPaginationData = () => ({
  pageSize: 10,
  currentPage: 1,
  maxPage: undefined,
  total: 0,
})

export default {
  name: 'CustomerTradeDetailIndex',
  components: {
    LayoutCard,
    SimpleInfoCard,
    Tag,
    Dialog,
    DialogPreviewProof,
    DialogUseDetail,
    DialogReplaymentDetail,
    RepaymentAccountViewDetailDialog,
    RepaymentAccountBindRecordDialog,
  },
  data() {
    return {
      loading: false,
      type: undefined,
      id: undefined,
      position: undefined,
      // 上方信息
      transactionRadio: '近3月',
      pageData: {},
      transactionData: {},
      transactionCurrentData: {},
      productData: {},
      // 表格数据
      tableLoading: true,
      TableLabRadio: '应收账款',
      paginationData: initPaginationData(),
      tableData: [],
      // 字典
      proofStatusMap,
      goodsTypeMap,
      invoiceTypeMap: {},
      proofTypeMap: {},
      // 查看凭证
      previewProofData: {},
      pdfSrc: '',
    }
  },
  created() {
    this.initPage()
  },
  watch: {
    '$route.params.id'() {
      this.initPage()
    },
  },
  methods: {
    initPage() {
      const { type, id, position } = this.$route.params
      this.type = type
      this.id = id
      this.position = position
      this.requestPageData()
      this.requestProductData()
      this.requestTableData()
      getDictionary({ code: 'customer_invoice_type' })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            const invoiceTypeMap = {}
            for (const item of data) {
              invoiceTypeMap[item.dictKey] = item.dictValue
            }
            invoiceTypeMap.ready = true
            this.invoiceTypeMap = invoiceTypeMap
          }
        })
        .catch(() => {})
      getDictionary({ code: 'jrzh_customer_front_sales_contract_proof_type' })
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            const proofTypeMap = {}
            for (const item of data) {
              proofTypeMap[item.dictKey] = item.dictValue
            }
            proofTypeMap.ready = true
            this.proofTypeMap = proofTypeMap
          }
        })
        .catch(() => {})
    },
    handleCardSwitch(value) {
      this.transactionCurrentData = undefined
      switch (value) {
        case '近3月':
          this.transactionCurrentData = this.transactionData.transactionThree
          break
        case '近6月':
          this.transactionCurrentData = this.transactionData.transactionSix
          break
        case '近1年':
          this.transactionCurrentData = this.transactionData.transactionTwelve
          break
        default:
          break
      }
    },
    requestPageData() {
      if (this.type === 'coreEnterprise') {
        if (this.position === 'height') {
          requestCoreHeightDetail({ backId: this.id })
            .then(({ data }) => {
              if (data.success) {
                data = data.data || {}
                this.pageData = data
                // 格式化近3月交易数据
                if (data.transactionThree) {
                  const originData = data.transactionThree
                  if (originData.avgRefundTime) {
                    originData.avgRefundTime += '天'
                  }
                  if (originData.refundRate) {
                    originData.refundRate += '%'
                  }
                  this.transactionData.transactionThree = originData
                } else {
                  this.transactionData.transactionThree = {}
                }
                // 格式化近6月交易数据
                if (data.transactionSix) {
                  const originData = data.transactionSix
                  if (originData.avgRefundTime) {
                    originData.avgRefundTime += '天'
                  }
                  if (originData.refundRate) {
                    originData.refundRate += '%'
                  }
                  this.transactionData.transactionSix = originData
                } else {
                  this.transactionData.transactionSix = {}
                }
                // 格式化近12月交易数据
                if (data.transactionTwelve) {
                  const originData = data.transactionTwelve
                  if (originData.avgRefundTime) {
                    originData.avgRefundTime += '天'
                  }
                  if (originData.refundRate) {
                    originData.refundRate += '%'
                  }
                  this.transactionData.transactionTwelve = originData
                } else {
                  this.transactionData.transactionTwelve = {}
                }
                // 交易数据赋值
                this.transactionCurrentData =
                  this.transactionData.transactionThree || {}
              }
            })
            .catch(() => {})
        } else if (this.position === 'lower') {
          //
        }
      } else if (this.type === 'enterprise') {
        if (this.position === 'height') {
          //
        } else if (this.position === 'lower') {
          requestClientLowerDetail({ id: this.id })
            .then(({ data }) => {
              if (data.success) {
                data = data.data || {}
                this.pageData = data
                // 格式化近3月交易数据
                if (data.transactionThree) {
                  const originData = data.transactionThree
                  if (originData.avgRefundTime) {
                    originData.avgRefundTime += '天'
                  }
                  if (originData.refundRate) {
                    originData.refundRate += '%'
                  }
                  this.transactionData.transactionThree = originData
                } else {
                  this.transactionData.transactionThree = {}
                }
                // 格式化近6月交易数据
                if (data.transactionSix) {
                  const originData = data.transactionSix
                  if (originData.avgRefundTime) {
                    originData.avgRefundTime += '天'
                  }
                  if (originData.refundRate) {
                    originData.refundRate += '%'
                  }
                  this.transactionData.transactionSix = originData
                } else {
                  this.transactionData.transactionSix = {}
                }
                // 格式化近12月交易数据
                if (data.transactionTwelve) {
                  const originData = data.transactionTwelve
                  if (originData.avgRefundTime) {
                    originData.avgRefundTime += '天'
                  }
                  if (originData.refundRate) {
                    originData.refundRate += '%'
                  }
                  this.transactionData.transactionTwelve = originData
                } else {
                  this.transactionData.transactionTwelve = {}
                }
                // 交易数据赋值
                this.transactionCurrentData =
                  this.transactionData.transactionThree || {}
              }
            })
            .catch(() => {})
        }
      }
    },
    // 获取绑定的商品
    requestProductData() {
      requestDetailBindGood(this.id)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            // if (data.expireTime) {
            //   data.expireTime = '2019-12-12 12:12:12'
            //   // data.expireTime = dayjs(
            //   //   data.expireTime,
            //   //   'YYYY-MM-DD HH:mm:ss'
            //   // ).format('YYYY.MM.DD')
            // }
            // if (data.effectiveDate) {
            //   data.effectiveDate = '1111-12-12 12:12:12'
            //   // data.effectiveDate = dayjs(
            //   //   data.effectiveDate,
            //   //   'YYYY-MM-DD HH:mm:ss'
            //   // ).format('YYYY.MM.DD HH:mm:ss')
            // }
            this.productData = data || {}
          }
        })
        .catch(() => {})
    },
    requestTableData() {
      const { currentPage, pageSize } = this.paginationData
      const request = {
        bankId: this.id,
        current: currentPage,
        size: pageSize,
        settleStatus: this.TableLabRadio === '应收账款' ? '0' : '1',
      }
      if (this.type === 'coreEnterprise') {
        if (this.position === 'height') {
          requestCoreHeightDetailContractList(request)
            .then(({ data }) => {
              if (data.success) {
                data = data.data
                const records = data.records || []
                this.tableData = records
                this.paginationData = {
                  ...this.paginationData,
                  currentPage: currentPage,
                  maxPage: data.pages,
                  total: data.total,
                }
              }
            })
            .catch(() => {})
        } else if (this.position === 'lower') {
          //
        }
      } else if (this.type === 'enterprise') {
        if (this.position === 'height') {
          //
        } else if (this.position === 'lower') {
          requestClientLowerDetailContractList(request)
            .then(({ data }) => {
              if (data.success) {
                data = data.data
                const records = data.records || []
                this.tableData = records
                this.paginationData = {
                  ...this.paginationData,
                  currentPage: currentPage,
                  maxPage: data.pages,
                  total: data.total,
                }
              }
            })
            .catch(() => {})
        }
      }
    },
    handleTableCurrentChange(currentPage) {
      this.tableLoading = true
      this.paginationData.currentPage = currentPage
      this.requestTableData()
    },
    handleTableLabChange() {
      this.tableLoading = true
      this.paginationData = initPaginationData()
      this.requestTableData()
    },
    // 表格按钮 - 查看凭证
    handleBtnViewProof(row) {
      this.$refs.DialogPreviewProofRef.handleOpen(row)
    },
    // 表格按钮 - 审批详情
    handleBtnApproveDetail(row) {
      requestProcessInfo({ processInsId: row.processInsId })
        .then(({ data }) => {
          const { taskId, processInsId, processDefinitionKey } = data.data
          const target = routerMapKeyToPath[processDefinitionKey]
          if (!target) return
          this.$router.push(
            `${target}/detail/${Buffer.from(
              JSON.stringify({
                taskId,
                processInsId,
              })
            ).toString('base64')}`
          )
        })
        .catch(() => {})
    },
    // 使用明细
    handleBtnUseDetail(row) {
      this.$refs.DialogUseDetailRef.handleOpen(row.id)
    },
    // 回款信息
    handleBtnRepaymentDetail(row) {
      this.$refs.DialogReplaymentDetailRef.handleOpen(row.id)
    },
    // 对公账户（还款账户） - 查看详情
    handleRepaymentAccountViewDetail() {
      this.$refs.repaymentAccountViewDetailDialogRef.handleOpen(
        this.productData
      )
    },
    // 对公账户（还款账户） - 绑定记录
    handleRepaymentAccountBindRecord() {
      this.$refs.repaymentAccountBindRecordDialogRef.handleOpen(
        this.productData
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.trade-detail-container {
  margin: 0 auto 24px;

  .top-bar {
    display: flex;
    margin: 20px 0;
    position: relative;
    height: 64px;
    border-radius: 100px;
    overflow: hidden;

    .height,
    .lower {
      flex: 1;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: #0bb07b;
      font-size: 20px;
      font-weight: 600;
      color: #ffffff;
      line-height: 20px;
    }

    .lower {
      background: #0d55cf;
    }

    .icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      position: absolute;
      left: 50%;
      border-radius: 50%;
      transform: translateX(-50%);
      background: white;
    }
  }

  .middle-container {
    display: flex;
    align-items: stretch;
    flex-wrap: nowrap;

    .top-wrapper {
      display: flex;
      flex-wrap: nowrap;

      > * {
        flex: 1;
      }
    }

    .bottom-wrapper {
      display: flex;
      flex-grow: 1;
      margin-top: 20px;

      .left {
        margin-right: 10px;
      }

      .right {
        margin-left: 10px;

        .header-wrapper {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      .left,
      .right {
        width: 100%;
        padding: 20px;
        background: #f8f9fb;
        border-radius: 8px;

        .desc {
          font-size: 14px;
          font-weight: 600;
          color: #4d0000;
        }

        .form-container {
          .form-item {
            display: flex;
            flex-wrap: nowrap;
            margin-top: 16px;

            .label {
              flex-shrink: 0;
              width: 112px;
              margin-right: 12px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #53627c;
            }

            .value {
              width: 100%;
              font-size: 14px;
              font-family: SFProText-Medium, SFProText;
              font-weight: 600;
              color: #0d55cf;

              :deep(.ant-statistic-content) {
                font-size: 14px;
                font-family: SFProText-Medium, SFProText;
                font-weight: 600;
                color: #0d55cf;

                .ant-statistic-content-suffix {
                  margin: 0;
                }
              }
            }
          }
        }
      }
    }

    .product-layout-card {
      display: inline-flex;
      flex-direction: column;
      width: 358px;
      height: 368px;
      margin: 0;
      box-sizing: border-box;

      :deep(.content) {
        flex: 1;
        display: none;
      }
    }

    .product-container {
      display: flex;
      flex-direction: column;
      margin-top: -10px;
      padding-top: 20px;
      border-top: 1px solid #eaeaea;

      .product-name-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .product-name {
          display: flex;
          align-items: center;
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #0a1f44;
          line-height: 28px;

          .img-wrapper {
            display: inline-block;
            width: 28px;
            height: 28px;
            margin-right: 8px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            border-radius: 100px;
          }

          .name {
            color: rgba(16, 16, 16, 100);
            font-size: 16px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }
        }
      }

      .valid-quota {
        .title {
          color: rgba(125, 125, 125, 100);
          font-size: 12px;
        }

        .value {
          display: inline-block;
          margin-top: 4px;
          color: rgba(0, 0, 0, 1);
          font-weight: 600;
          font-size: 20px;
          line-height: 24px;
          text-align: left;
          font-family: SourceHanSansSC-bold;
        }
      }

      .form-item {
        margin-top: 12px;
        color: rgba(125, 125, 125, 100);
        font-size: 12px;
      }

      .account-container {
        position: absolute;
        bottom: 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 88px;
        margin-top: 10px;
        padding: 12px 12px 14px;
        border-radius: 6px;
        background-color: rgba(247, 247, 247, 100);
        box-sizing: border-box;

        .title {
          color: rgba(16, 16, 16, 100);
          font-size: 14px;
          font-weight: 600;
        }
      }
    }

    .label-bar-container {
      .el-radio-group {
        .el-radio-button:first-child {
          border-radius: 100px 0 0 100px !important;

          ::v-deep {
            .el-radio-button__inner {
              border-radius: 100px 0 0 100px !important;
            }
          }
        }

        .el-radio-button:last-child {
          border-radius: 0 100px 100px 0 !important;

          ::v-deep {
            .el-radio-button__inner {
              border-radius: 0 100px 100px 0 !important;
            }
          }
        }

        ::v-deep {
          .el-radio-button__inner {
            height: 24px;
            padding: 0 10px;
            line-height: 24px;
          }
        }
      }
    }
  }

  .lab-container {
    margin-bottom: 16px;
  }

  .table-container {
    .el-pagination {
      display: flex;
      justify-content: right;
      margin-top: 18px;
    }
  }
}
</style>
