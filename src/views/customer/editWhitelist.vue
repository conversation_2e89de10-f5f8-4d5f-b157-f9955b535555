<template>
  <div class="newWhitelist">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">编辑白名单</div>
      </div>
      <div class="updata">
        <avue-form v-model="form" :option="option1">
          <template slot="text1">
            <div class="transfer-box">
              <el-transfer
                filterable
                filter-placeholder="请输入企业名称"
                :titles="['未选企业', '已选企业']"
                v-model="form.text1"
                :data="data1"
              >
              </el-transfer>
            </div>
          </template>
          <template slot="text2">
            <div class="transfer-box">
              <el-transfer
                filterable
                filter-placeholder="请输入行业名称"
                :titles="['未选行业', '已选行业']"
                v-model="form.text2"
                :data="data2"
              >
              </el-transfer>
            </div>
          </template>
        </avue-form>
      </div>

      <div class="btn-box">
        <div class="no" @click="returnup">取消</div>
        <p class="yes" @click="updata">保存</p>
        <span class="up" @click="enable">启用</span>
      </div>
    </div>
  </div>
</template>
<script>
import {
  gettree,
  gethangye,
  editshuju,
  getshuju,
} from '../../api/customer/whitelistcustomer'

export default {
  data() {
    return {
      row: {},
      form: {
        text1: [],
        text2: [],
      },
      id: 0,
      option1: {
        emptyBtn: false,
        submitBtn: false,
        column: [
          {
            label: '排序',
            prop: 'sort',
            span: 18,
            // rules: [
            //   {
            //     required: true,
            //     message: '请输入序号',
            //     trigger: 'blur',
            //   },
            // ],
            rules: [
              {required: true, validator: this.validateNum, trigger: 'blur'},
            ],
          },
          {
            label: '模板名称',
            prop: 'name',
            type: 'input',
            span: 18,

            rules: [
              {
                required: true,
                message: '请输入模板名称',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '模板类型',
            prop: 'type',
            span: 18,
            type: 'select',
            rules: [
              {
                required: true,
                message: '请输入模板名称',
                trigger: 'blur',
              },
            ],
            dicData: [
              {
                label: '核心企业',
                value: 1,
              },
            ],
          },
          {
            label: '黑白名单',
            prop: 'blackWhiteType',
            span: 18,
            type: 'select',
            rules: [
              {
                required: true,
                message: '请选择黑/白名单类型',
                trigger: 'blur',
              },
            ],
            dicData: [
              {
                label: '黑名单',
                value: 0,
              },
              {
                label: '白名单',
                value: 1,
              },
            ],
          },
          {
            label: '业务类型',
            prop: 'businessType',
            span: 18,
            type: 'select',
            rules: [
              {
                required: true,
                message: '请选择业务类型',
                trigger: 'blur',
              },
            ],
            dicData: [
              {
                label: '应收账款',
                value: 1,
              },
              {
                label: '云信',
                value: 2,
              },
            ],
            blur: () => {
              this.lockRequest2 = false
            },
          },
          {
            label: '产品名称',
            prop: 'goodsCodes',
            span: 18,
            placeholder: '请选择产品',
            rules: [
              {
                required: false,
                message: '请选择产品',
                trigger: 'change',
              },
            ],
            type: 'select',
            dicData: [],
            props: {
              label: 'goodsName',
              value: 'goodsCode',
            },
            multiple: true,
            dataType: 'string',
            display: false,
          },
          {
            label: '选择企业',
            prop: 'text1',
            span: 24,
            // rules: [
            //   {
            //     required: true,
            //     message: '请选择企业',
            //     trigger: 'change',
            //   },
            // ],
          },
          {
            label: '选择行业',
            prop: 'text2',
            span: 24,
            // errorslot: true,
            // rules: [
            //   {
            //     required: true,
            //     message: '请选择行业',
            //     trigger: 'change',
            //   },
            // ],
          },
        ],
      },
      data1: [],
      data2: [],
      lockRequest: false,
      lockRequest2: true,
      // filterMethod1(query, item) {
      //   return item.pinyin.indexOf(query) > -1
      // },
    }
  },
  watch: {
    'form.type': {
      handler() {
        this.data1 = []
        this.data2 = []
        let i = {}
        i.value = this.form.type
        if (i.value || i.value === 0) {
          gettree(i).then(res => {
            if (res.data.data.length) {
              for (let j = 0; j < res.data.data.length; j++) {
                this.data1.push({
                  label: res.data.data[j].name,
                  key: res.data.data[j].id,
                  pinyin: j,
                })
              }
            }
          })
          gethangye().then(res => {
            if (res.data.data.length) {
              for (let j = 0; j < res.data.data.length; j++) {
                this.data2.push({
                  label: res.data.data[j].codeName,
                  key: res.data.data[j].id,
                  pinyin: j,
                })
              }
            }
          })
        }
      },
      deep: true,
      immediate: true,
    },
    'form.businessType': {
      handler(val) {
        if (!this.lockRequest2) {
          this.form.goodsCodes = ''
        }
        console.log(1)
        let goodsCodes = this.findObject(this.option1.column, 'goodsCodes')
        if (val === 1) {
          goodsCodes.display = true
          this.$axios.get('api/blade-goods/web-back/goods/onShelfGoodsList').then(res => {
            goodsCodes.dicData = res.data.data
          })
        } else if (val === 2) {
          goodsCodes.display = true
          this.$axios.get('api/blade-goods/web-back/goods/cloudProduct/onShelfGoodsList').then(res => {
            goodsCodes.dicData = res.data.data
          })
        } else {
          goodsCodes.display = false
        }
      }
    }
  },
  methods: {
    returnup() {
      this.$router.$avueRouter.closeTag()
      this.$router.replace('/customer/whitelisttemplate')
    },
    enable() {
      this.row.status = 1
      this.updata()
    },
    updata() {
      if (this.lockRequest) return
      this.lockRequest = true
      if (
        this.form.name &&
        this.form.sort &&
        this.form.text1.length &&
        this.form.text2.length
      ) {
        this.row.id = this.id
        this.row.templateName = this.form.name
        this.row.whitelistType = this.form.type
        this.row.sort = this.form.sort
        this.row.whitelistcustomerTagIdList = this.form.text1
        this.row.middleProhibit = this.form.text2
        this.row.blackWhiteType = this.form.blackWhiteType
        this.row.goodsCodes = this.form.goodsCodes
        this.row.businessType = this.form.businessType
        editshuju(this.row)
          .then(res => {
            if (res.data.code == 200) {
              this.$message({
                type: 'success',
                message: '编辑成功!',
              })
              this.returnup()
            }
          })
          .catch(() => {
            this.lockRequest = false
          })
      } else {
        this.$message({
          type: 'warning',
          message: '还有数据未录入',
        })
        this.lockRequest = false
      }
    },
    // 数字输入框校验
    validateNum(rule, value, callback) {
      let regName = /[^\d.]/g
      if (regName.test(value)) {
        callback(new Error('请确认只输入了数字'))
      } else {
        callback()
      }
    },
  },
  mounted() {
    this.id = this.$route.query.id
    getshuju(this.id).then(res => {
      this.form.name = res.data.data.templateName
      this.form.type = res.data.data.whitelistType
      this.form.sort = res.data.data.sort
      this.form.text1 = res.data.data.whitelistcustomerTagIdList
      this.form.text2 = res.data.data.middleProhibit
      this.form.blackWhiteType = res.data.data.blackWhiteType
      this.form.goodsCodes = res.data.data.goodsCodes
      this.form.businessType = res.data.data.businessType
    })
  },
}
</script>
<style lang="scss" scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  margin-bottom: 50px;
  padding: 20px;
  box-sizing: border-box;
}

.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}

.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}

.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}

.updata {
  width: 100%;
  margin-top: 20px;
}

.transfer-box {
  width: 100%;
  height: 360px;
}

/* .qiye-box {
  position: relative;
  margin-top: -30px;
  z-index: 8;
  width: 100%;
  height: 360px;
  display: flex;
  align-items: flex-start;
}
.transfer-box {
  position: absolute;
  top: 0;
  left: 95px;
  width: 100%;
  z-index: 9;
  flex: 1;
  height: 100%;
}*/
.btn-box {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.btn-box * {
  width: 60px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
}

.btn-box .no {
  border: 1px solid #bbbbbb;
  margin-right: 12px;
}

.btn-box .yes {
  background-color: #2dad28;
  color: #ffffff;
  margin-right: 12px;
}

.btn-box .up {
  background-color: #1277ff;
  color: #ffffff;
}

::v-deep .el-transfer {
  width: 100%;
  height: 100%;
}

::v-deep .el-transfer-panel {
  width: 40%;
}
</style>
