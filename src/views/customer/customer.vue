<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <!--        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customer_delete"
                   @click="handleDelete">删 除
                    v-if="userInfo.role_name.includes('admin')"
        </el-button>-->

        <el-button
          type="warning"
          size="small"
          plain
          icon="el-icon-download"
          @click="handleExport"
          >导出
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="enterpriseName">
        <p>{{ row.enterpriseName === '' ? '------' : row.enterpriseName }}</p>
      </template>
      <template slot-scope="{ row }" slot="customerManager">
        <p>{{ row.customerManager === '' ? '------' : row.customerManager }}</p>
      </template>
      <template slot-scope="{ row }" slot="isWhiteList">
        <el-tag>{{ row.isWhiteList === 0 ? '否' : '是' }}</el-tag>
      </template>

      <template slot-scope="{ row }" slot="score">
        <p>{{ row.score === -1 ? '------' : row.score }}</p>
      </template>

      <!--      <template slot="status" slot-scope="{row}" >
        <el-tag type="info" v-if="row.status === 0">未实名</el-tag>
        <el-tag type="success" v-if="row.status === 1">已实名</el-tag>
        <el-tag type="info" v-if="row.status === 2">已认证</el-tag>
      </template>-->

      <template
        slot="menu"
        slot-scope="{ row, type, size, index, loading, done }"
      >
        <el-button
          icon="el-icon-check"
          :size="size"
          :type="type"
          @click="editrtaion(row, index)"
          >编 辑</el-button
        >

        <el-button
          icon="el-icon-check"
          v-if="row.isWhiteList == 0"
          :size="size"
          :type="type"
          @click="iswhite(row)"
          >设为白名单</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  isWhite,
} from '@/api/customer/customer'
import { mapGetters } from 'vuex'

export default {
  data() {
    var validatePassword = (rule, value, callback) => {
      if (this.form.newPassword != value) {
        callback(new Error('密码与确认密码不符合'))
      }
      callback()
    }
    var validateLangth = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码长度小于6位'))
      } else if (value.length > 18) {
        callback(new Error('密码长度大于18位'))
      }
      callback()
    }
    var validateAccountLangth = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('长度小于6位'))
      } else if (value.length > 18) {
        callback(new Error('长度大于18位'))
      }
      callback()
    }

    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        column: [
          {
            label: 'logo',
            prop: 'logoSrc',
            width: 170,
            listType: 'picture-img',
            dataType: 'string',
            type: 'upload',
            display: false,
            action: '/api/blade-resource/oss/endpoint/put-file',
            tip: '只能上传jpg/png/格式的图片',
            accept: '.jpg,.png',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            rules: [
              {
                required: true,
                message: '请上传图片',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '用户名',
            prop: 'account',
            display: false,
            rules: [
              {
                required: true,
                message: '用户名',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '客户名称',
            prop: 'enterpriseName',
            display: false,
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入企业名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '客户经理',
            prop: 'customerManager',
            addDisplay: false,
            editDisplay: false,
            display: false,
            rules: [
              {
                required: true,
                message: '请输入客户经理',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '手机号',
            display: false,
            search: true,
            prop: 'phone',
            rules: [
              {
                required: true,
                message: '请输入手机号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '是否白名',
            prop: 'isWhiteList',
            display: false,
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入是否白名',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '综合评分',
            prop: 'score',
            display: false,
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入综合评分',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '客户类型',
            prop: 'status',
            search: true,
            display: false,
            addDisplay: false,
            editDisplay: false,
            type: 'select',
            dicData: [
              {
                label: '未实名',
                value: 0,
              },
              {
                label: '已实名',
                value: 2,
              },
              {
                label: '已认证',
                value: 3,
              },
            ],
            rules: [
              {
                required: true,
                message: '请输入状态',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上次访问时间',
            prop: 'lastVisitTime',
            addDisplay: false,
            display: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入上次访问时间',
                trigger: 'blur',
              },
            ],
          },
        ],
        group: [
          {
            label: '新增用户',
            column: [
              {
                label: '头像',
                prop: 'logoSrc',
                width: 170,
                listType: 'picture-img',
                dataType: 'string',
                type: 'upload',
                span: 24,
                action: '/api/blade-resource/oss/endpoint/put-file',
                tip: '只能上传jpg/png/格式的图片',
                accept: '.jpg,.png',
                propsHttp: {
                  res: 'data',
                  url: 'link',
                },
                rules: [
                  {
                    message: '请上传图片',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '用户名',
                prop: 'account',
                rules: [
                  {
                    required: true,
                    validator: validateAccountLangth,
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '手机号',
                prop: 'phone',
                rules: [
                  {
                    required: true,
                    message: '请输入手机号',
                    trigger: 'blur',
                  },
                ],
              },
              {
                label: '密码',
                prop: 'newPassword',
                type: 'password',
                hide: true,
                viewDisplay: false,
                rules: [
                  {
                    required: true,
                    validator: validateLangth,
                    trigger: 'blur',
                  },
                ],
              },
              {
                labelWidth: 120,
                label: '确认密码',
                type: 'password',
                hide: true,
                viewDisplay: false,
                prop: 'confirmPassword',
                rules: [
                  {
                    required: true,
                    validator: validatePassword,
                    trigger: 'blur',
                  },
                ],
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.customer_add, false),
        viewBtn: this.vaildData(this.permission.customer_view, false),
        delBtn: this.vaildData(this.permission.customer_delete, false),
        editBtn: this.vaildData(this.permission.customer_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      row.enterpriseType = '0'
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      row.enterpriseType = '0'
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (type == 'view') {
        this.$router.push(
          '/customer/enterprise/details/' +
            Buffer.from(JSON.stringify(this.form.id)).toString('base64')
        )
      }
      if (['edit'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    editrtaion(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    onLoad(page, params = {}) {
      this.loading = true
      ;(params.enterpriseType = 0),
        getList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then(res => {
          const data = res.data.data
          this.page.total = data.total
          this.data = data.records
          this.loading = false
          this.selectionClear()
        })
    },
    handleExport() {
      this.$confirm('是否导出所有数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        //${this.website.tokenHeader}=${getToken()}&
        window.open(
          `/api/customer/export-customer?account=${this.search.phone}&realName=${this.search.realName}`
        )
      })
    },
    iswhite(row) {
      this.$confirm('确定进行将这条数据设置为白名单吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return isWhite(row.id)
        })
        .then(resp => {
          if (resp.data.data == 0) {
            this.$message({
              type: 'error',
              message: '设置失败!',
            })
          } else {
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
          }
          this.onLoad(this.page)
          this.$refs.crud.toggleSelection()
        })
    },
  },
}
</script>

<style></style>
