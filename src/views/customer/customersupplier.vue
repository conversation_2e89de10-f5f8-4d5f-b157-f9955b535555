<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   @click="handleDelete">删 除
        </el-button>

        <el-button
          type="success"
          size="small"
          icon="el-icon-top"
          plain
          @click="handlerOnShelf"
        >启 用
        </el-button>

        <el-button
          type="warning"
          size="small"
          icon="el-icon-bottom"
          plain
          @click="conlistoff">
          禁 用
        </el-button>
      </template>

      <template
        slot="menu"
        slot-scope="{ row, type, size, index, loading, done }"
      >
        <el-button
          icon="el-icon-check"
          v-if="row.status == 0"
          :size="size"
          :type="type"
          @click="editrtaion(row, index)"
        >编 辑
        </el-button
        >
        <el-button
          icon="el-icon-check"
          v-if="row.status == 0"
          :size="size"
          :type="type"
          @click="deltaion(row, index)"
        >删 除
        </el-button
        >
        <el-button
          icon="el-icon-check"
          v-if="row.status == 0"
          :size="size"
          :type="type"
          @click="enableupdate(row, index)"
        >启 用
        </el-button>

        <el-button
          icon="el-icon-check"
          v-if="row.status == 1"
          :size="size"
          :type="type"
          @click="disableupdate(row, index)">禁 用
        </el-button>
      </template>

      <template slot-scope="{ row, index, type }" slot="menuForm">
        <el-button
          type="success"
          icon="el-icon-check"
          size="small"
          plain
          v-if="type=='edit'||type=='add'"
          @click="saveOrUpdateAndOnShelf()"
        >发 布
        </el-button>
      </template>
      <template slot="status" slot-scope="{row}">
        <el-tag type="info" v-if="row.status === 0">已禁用</el-tag>
        <el-tag type="success" v-if="row.status === 1">已启用</el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  add,
  conlistoff,
  getDetail,
  getList,
  handlerOnShelf,
  release,
  remove,
  update
} from "@/api/customer/customersupplier";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        delBtn: false,
        column: [
          {
            label: "供应商编号",
            prop: "supperCode",
            viewDisplay: false,
            addDisplay: false,
            editDisplay: false,
            hide: true,
            rules: [{
              required: true,
              message: "请输入供应商编号",
              trigger: "blur"
            }]
          },
          {
            label: '头像',
            prop: 'supperLogo',
            width: 170,
            span: 24,
            listType: 'picture-img',
            dataType: 'string',
            type: 'upload',
            action: '/api/blade-resource/oss/endpoint/put-file',
            tip: '只能上传jpg/png/格式的图片',
            accept: '.jpg,.png',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            rules: [
              {
                required: true,
                message: '请上传轮播图',
                trigger: 'blur',
              },
            ],
          },
          {
            label: "供应商名称",
            prop: "supperName",
            editDisabled: true,
            search: true,
            span: 24,
            rules: [{
              required: true,
              message: "请输入供应商名称",
              trigger: "blur"
            }]
          },
          {
            label: "统一社会代码",
            prop: "unifiedCode",
            editDisabled: true,
            /*hide:true,*/
            span: 24,
            rules: [{
              required: true,
              message: "请输入统一社会代码",
              trigger: "blur"
            }]
          },
          {
            label: "联系人",
            prop: "contacts",
            span: 24,
            rules: [{
              required: true,
              message: "请输入联系人",
              trigger: "blur"
            }]
          },
          {
            label: "联系方式",
            prop: "supperPhone",
            search: true,
            span: 24,
            rules: [{
              required: true,
              message: "请输入手机号",
              trigger: "blur"
            }]
          },
          {
            label: "开户银行",
            prop: "depositBankId",
            type: 'select',
            span: 24,
            props: {
              required: true,
              label: 'name',
              value: 'id',
            },
            remote: true, //开启远程访问
            dicFlag: false, //初次获得焦点不下拉
            hide: true,
            dicUrl: '/api/blade-resource/web-back/resource/bank/all?name={{key}}',
            rules: [{
              required: true,
              message: "请输入开户银行",
              trigger: "blur"
            }]
          },
          {
            label: "开户银行",
            prop: "depositBankName",
            viewDisplay: false,
            addDisplay: false,
            editDisplay: false,
            span: 24,
            rules: [{
              required: true,
              message: "请输入银行名称",
              trigger: "blur"
            }]
          },
          {
            label: "银行账户",
            prop: "bankAccount",
            span: 24,
            rules: [{
              required: true,
              message: "请输入银行账户",
              trigger: "blur"
            }]
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            dicData: [
              {
                label: '已禁用',
                value: 0,
              },
              {
                label: '已启用',
                value: 1,
              }],
            rules: [{

              message: "状态",
              trigger: "blur"
            }]
          },
          {
            label: "操作人",
            prop: "realName",
            viewDisplay: false,
            span: 24,
            addDisplay: false,
            editDisplay: false,
            rules: [{
              message: "操作人",
              trigger: "blur"
            }]
          },
          {
            label: "上次操作时间",
            prop: "updateTime",
            viewDisplay: false,
            span: 24,
            addDisplay: false,
            editDisplay: false,
            rules: [{
              message: "请输入银行账户",
              trigger: "blur"
            }]
          },

          {
            label: '注册时间',
            prop: 'createTime',
            type: 'date',
            display: false,
            valueFormat: 'yyyy-MM-dd',
            hide: true,
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.customersupplier_add, false),
        viewBtn: this.vaildData(this.permission.customersupplier_view, false),
        delBtn: this.vaildData(this.permission.customersupplier_delete, false),
        editBtn: this.vaildData(this.permission.customersupplier_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    editrtaion(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    deltaion(row, index) {
      this.$refs.crud.rowDel(row, index)
    },
    enableupdate(row) {
      this.$confirm('确定进行发布操作吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return release(row.id)
        })
        .then((resp) => {
          if (resp.data.data == 0) {
            this.$message({
              type: 'error',
              message: '操作失败,数据未能填写完成!',
            })
          } else {
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
          }
          this.onLoad(this.page)
          this.$refs.crud.toggleSelection()
        })
    },
    disableupdate(row) {
      this.$confirm('确定进行取消发布操作吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return conlistoff(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },

    conlistoff() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择取消发布吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return conlistoff(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    saveOrUpdateAndOnShelf() {
      this.form.status = '1'
      if (this.form.id) {
        this.$refs.crud.rowUpdate(this.form)
      } else {
        this.$refs.crud.rowSave(this.form)
      }
    },

    handlerOnShelf() {
      var length = this.selectionList.length
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择发布吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return handlerOnShelf(this.ids)
        })
        .then((resp) => {
          let num = length - resp.data.data
          let msg = ''

          if (num > 0) {
            msg = `成功上架${resp.data.data}条数据,有${num}条数据未填写完整`
          } else {
            msg = '操作成功'
          }

          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: msg,
          })
        })
    },

  }
};
</script>

<style>
</style>
