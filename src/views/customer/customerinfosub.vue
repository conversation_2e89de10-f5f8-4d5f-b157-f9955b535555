<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customerinfosub_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/customer/customerinfosub";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "企业名称",
              prop: "corpName",
              rules: [{
                required: true,
                message: "请输入企业名称",
                trigger: "blur"
              }]
            },
            {
              label: "工商注册号",
              prop: "businessLicenceNumber",
              rules: [{
                required: true,
                message: "请输入工商注册号",
                trigger: "blur"
              }]
            },
            {
              label: "工商号图片id",
              prop: "registPicAttchId",
              rules: [{
                required: true,
                message: "请输入工商号图片id",
                trigger: "blur"
              }]
            },
            {
              label: "法人姓名",
              prop: "corporationName",
              rules: [{
                required: true,
                message: "请输入法人姓名",
                trigger: "blur"
              }]
            },
            {
              label: "法人证件号",
              prop: "corporationIdCardNumber",
              rules: [{
                required: true,
                message: "请输入法人证件号",
                trigger: "blur"
              }]
            },
            {
              label: "法人性别;0 男 1 女  2 不展示",
              prop: "corporationSex",
              rules: [{
                required: true,
                message: "请输入法人性别;0 男 1 女  2 不展示",
                trigger: "blur"
              }]
            },
            {
              label: "法人国家",
              prop: "corporationCountry",
              rules: [{
                required: true,
                message: "请输入法人国家",
                trigger: "blur"
              }]
            },
            {
              label: "法人名族",
              prop: "corporationNation",
              rules: [{
                required: true,
                message: "请输入法人名族",
                trigger: "blur"
              }]
            },
            {
              label: "法人证件有效期",
              prop: "corporationValidTime",
              rules: [{
                required: true,
                message: "请输入法人证件有效期",
                trigger: "blur"
              }]
            },
            {
              label: "法人居住地",
              prop: "corporationAddress",
              rules: [{
                required: true,
                message: "请输入法人居住地",
                trigger: "blur"
              }]
            },
            {
              label: "法人手机号",
              prop: "mobile",
              rules: [{
                required: true,
                message: "请输入法人手机号",
                trigger: "blur"
              }]
            },
            {
              label: "法人证件号附件id",
              prop: "leagalNoAttachId",
              rules: [{
                required: true,
                message: "请输入法人证件号附件id",
                trigger: "blur"
              }]
            },
            {
              label: "经办人姓名",
              prop: "operatorName",
              rules: [{
                required: true,
                message: "请输入经办人姓名",
                trigger: "blur"
              }]
            },
            {
              label: "经办人证件号",
              prop: "operatorIdcard",
              rules: [{
                required: true,
                message: "请输入经办人证件号",
                trigger: "blur"
              }]
            },
            {
              label: "经办人性别;0 男 1 女  2 不展示",
              prop: "operatorSex",
              rules: [{
                required: true,
                message: "请输入经办人性别;0 男 1 女  2 不展示",
                trigger: "blur"
              }]
            },
            {
              label: "经办人国籍",
              prop: "operatorCountry",
              rules: [{
                required: true,
                message: "请输入经办人国籍",
                trigger: "blur"
              }]
            },
            {
              label: "经办人名族",
              prop: "operatorNation",
              rules: [{
                required: true,
                message: "请输入经办人名族",
                trigger: "blur"
              }]
            },
            {
              label: "经办人证件有效期",
              prop: "operatorValidTime",
              rules: [{
                required: true,
                message: "请输入经办人证件有效期",
                trigger: "blur"
              }]
            },
            {
              label: "经办人居住地",
              prop: "operatorAddress",
              rules: [{
                required: true,
                message: "请输入经办人居住地",
                trigger: "blur"
              }]
            },
            {
              label: "经办人手机号",
              prop: "operatorPhone",
              rules: [{
                required: true,
                message: "请输入经办人手机号",
                trigger: "blur"
              }]
            },
            {
              label: "经办人附件id",
              prop: "operatorAttachId",
              rules: [{
                required: true,
                message: "请输入经办人附件id",
                trigger: "blur"
              }]
            },
            {
              label: "法人证件类型-若证件类型为“0”、法人证件号码为身份证号码，证件类型为“1”、法人证件号码为护照号码",
              prop: "corporationIdType",
              rules: [{
                required: true,
                message: "请输入法人证件类型-若证件类型为“0”、法人证件号码为身份证号码，证件类型为“1”、法人证件号码为护照号码",
                trigger: "blur"
              }]
            },
            {
              label: "法人证件正面附件id",
              prop: "corporationFaceAttachId",
              rules: [{
                required: true,
                message: "请输入法人证件正面附件id",
                trigger: "blur"
              }]
            },
            {
              label: "法人证件反面附件",
              prop: "corporationBackAttachId",
              rules: [{
                required: true,
                message: "请输入法人证件反面附件",
                trigger: "blur"
              }]
            },
            {
              label: "经办人证件正面id",
              prop: "operatorFaceAttachId",
              rules: [{
                required: true,
                message: "请输入经办人证件正面id",
                trigger: "blur"
              }]
            },
            {
              label: "经办人证件反面附件id",
              prop: "operatorBackAttachId",
              rules: [{
                required: true,
                message: "请输入经办人证件反面附件id",
                trigger: "blur"
              }]
            },
            {
              label: "公司经营证件附件id",
              prop: "businessLicenceAttachId",
              rules: [{
                required: true,
                message: "请输入公司经营证件附件id",
                trigger: "blur"
              }]
            },
            {
              label: "经营状况",
              prop: "operationStatus",
              rules: [{
                required: true,
                message: "请输入经营状况",
                trigger: "blur"
              }]
            },
            {
              label: "经营期限自",
              prop: "operationFrom",
              rules: [{
                required: true,
                message: "请输入经营期限自",
                trigger: "blur"
              }]
            },
            {
              label: "经营期限至",
              prop: "operationTo",
              rules: [{
                required: true,
                message: "请输入经营期限至",
                trigger: "blur"
              }]
            },
            {
              label: "法人邮箱",
              prop: "corporationEmail",
              rules: [{
                required: true,
                message: "请输入法人邮箱",
                trigger: "blur"
              }]
            },
            {
              label: "变更表ID",
              prop: "recordId",
              rules: [{
                required: true,
                message: "请输入变更表ID",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.customerinfosub_add, false),
          viewBtn: this.vaildData(this.permission.customerinfosub_view, false),
          delBtn: this.vaildData(this.permission.customerinfosub_delete, false),
          editBtn: this.vaildData(this.permission.customerinfosub_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
