<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      ref="crud"
      v-model="form"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      @row-del="rowDel"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @tree-load="treeLoad"
    >
      <template slot="menuLeft"> </template>

      <template slot-scope="{ row }" slot="status">
        <el-tag v-if="row.status === 0">已禁用</el-tag>
        <el-tag v-if="row.status === 1">已启用</el-tag>
      </template>
      <template slot-scope="{ row }" slot="customerManager">
        <p>{{ row.customerScore === '' ? '------' : row.customerScore }}</p>
      </template>
      <template slot-scope="{ row }" slot="customerWhite">
        <p>{{ row.customerWhite === 0 ? '否' : '是' }}</p>
      </template>
      <template slot-scope="{ row }" slot="enterpriseType">
        <el-tag v-if="row.enterpriseType === 0">集团</el-tag>
        <el-tag v-if="row.enterpriseType === 1">公司</el-tag>
        <el-tag v-if="row.enterpriseType === 2">子公司</el-tag>
      </template>
      <template slot-scope="{ row }" slot="customerScore">
        <p>{{ row.customerScore === -1 ? '----' : row.customerScore }}</p>
      </template>

      <template slot="status" slot-scope="{ row }">
        <el-tag type="info" v-if="row.status === 0">已禁用</el-tag>
        <el-tag type="success" v-if="row.status === 1">已启用</el-tag>
      </template>

      <template
        slot="menu"
        slot-scope="{ row, type, size, index, loading, done }"
      >
        <el-button
          icon="el-icon-document"
          :size="size"
          :type="type"
          @click="handleRedirectArchives(row)"
          >档 案</el-button
        >
      </template>

      <!--      <template
        slot="menu"
        slot-scope="{ row, type, size, index, loading, done }"
      >
        <el-button
          icon="el-icon-check"
          v-if="row.status == 0"
          :size="size"
          :type="type"
          @click="enableupdate(row, index)"
          >启 用</el-button
        >

        <el-button
          icon="el-icon-check"
          v-if="row.status == 1"
          :size="size"
          :type="type"
          @click="disableupdate(row, index)"
          >禁 用</el-button
        >
      </template>-->
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  isWhite,
  conlistoff,
  conlistput,
} from '@/api/customer/frontcorelist'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      parentId: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        lazy: true,
        tree: true,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        column: [
          {
            label: '企业名称',
            addDisplay: false,
            editDisplay: false,
            prop: 'name',
            rules: [
              {
                required: true,
                message: '企业名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '统一社会代码',
            addDisplay: false,
            editDisplay: false,
            prop: 'unifiedSocialCode',
            rules: [
              {
                required: true,
                message: '统一社会代码',
                trigger: 'blur',
              },
            ],
          },
          {
            label: 'logo',
            prop: 'logoSrc',
            listType: 'picture-img',
            dataType: 'string',
            display: false,
            type: 'upload',
            rules: [
              {
                message: '请上传图片',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '企业类型',
            prop: 'enterpriseType',
            type: 'select',
            addDisplay: false,
            editDisplay: false,
            dicData: [
              {
                label: '集团',
                value: 0,
              },
              {
                label: '公司',
                value: 1,
              },
              {
                label: '子公司',
                value: 2,
              },
            ],
            rules: [
              {
                required: true,
                message: '请输入企业类型 0-集团 1-子公司;',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '授信额度(万)',
            prop: 'creditLine',
            display: false,
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入授信额度(万)',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '核心企业评分',
            prop: 'customerScore',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入核心企业评分',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上级机构',
            prop: 'parentId',
            dicData: [],
            type: 'tree',
            hide: true,
            addDisabled: false,
            display: false,
            props: {
              label: 'title',
            },
            rules: [
              {
                required: false,
                message: '请选择上级机构',
                trigger: 'click',
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.frontcorelist_add, false),
        viewBtn: this.vaildData(this.permission.frontcorelist_view, false),
        delBtn: this.vaildData(this.permission.frontcorelist_delete, false),
        editBtn: this.vaildData(this.permission.frontcorelist_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.data = []
          this.parentId = 0
          this.$refs.crud.refreshTable()
          this.$refs.crud.toggleSelection()
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      // console.log(type)
      /* if (type == 'view') {
        this.$router.push(
          '/customer/enterprise/coreDetails/' +
            Buffer.from(JSON.stringify(this.form.id)).toString('base64')
        )
      }*/
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.parentId = 0
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.parentId = ''
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    /*editrtaion(row, index) {


      this.option.group[0].display = false;
      this.option.group[1].display = true;
      this.$refs.crud.rowEdit(row, index)
    },
    editrtaion1(row, index) {
      this.option.group[1].display = false;
      this.option.group[0].display = true;
      this.$refs.crud.rowEdit(row, index)
    },*/
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(this.parentId, Object.assign(params, this.query)).then(res => {
        this.data = res.data.data
        this.loading = false
        this.selectionClear()
      })
    },
    enableupdate(row) {
      this.$confirm('确定进行发布操作吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return conlistput(row.id)
        })
        .then(resp => {
          if (resp.data.data == 0) {
            this.$message({
              type: 'error',
              message: '操作失败,数据未能填写完成!',
            })
          } else {
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
          }
          this.onLoad(this.page)
          this.$refs.crud.toggleSelection()
        })
    },
    disableupdate(row) {
      this.$confirm('确定进行取消发布操作吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return conlistoff(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },

    treeLoad(tree, treeNode, resolve) {
      const parentId = tree.id
      getList(parentId).then(res => {
        resolve(res.data.data)
      })
    },
    beforeClose(done) {
      this.parentId = ''
      const column = this.findObject(this.option.column, 'parentId')
      column.value = ''
      column.addDisabled = false
      done()
    },
    iswhite(row) {
      this.$confirm('确定进行将这条数据设置为白名单吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return isWhite(row.id)
        })
        .then(resp => {
          if (resp.data.data == 0) {
            this.$message({
              type: 'error',
              message: '设置失败!',
            })
          } else {
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
          }
          this.onLoad(this.page)
          this.$refs.crud.toggleSelection()
        })
    },
    handleRedirectArchives(row) {
      this.$router.push({
        path: `/customer/archives/coreEnterprise/${row.id}`,
      })
    },
  },
}
</script>

<style></style>
