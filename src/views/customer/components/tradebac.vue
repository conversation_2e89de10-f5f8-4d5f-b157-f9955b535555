<template>
  <div class="trade-back">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">上游供应商({{ upsupplierList.length }})</div>
      </div>
      <div class="up-supplier-list">
        <div
          v-for="(item, index) in upsupplierList"
          :key="index"
          class="supplier-box"
        >
          <div class="supplier">
            <div class="supplier-up">
              <div>{{ item.name }}</div>
              <span>法人:{{ item.person }}</span>
            </div>
            <div class="supplier-bottom">
              <div class="look" @click="todetail(item.name)">查看贸易明细</div>
              <div class="del">删除关系</div>
            </div>
          </div>
        </div>
        <div class="new-supplier-box">
          <i class="el-icon-plus"></i>
        </div>
      </div>
    </div>
    <div class="details-content2">
      <div class="details-content2-tit">
        <div class="title-dot"></div>
        <div class="title">下游供应商({{ botsupplierList.length }})</div>
      </div>
      <div class="up-supplier-list">
        <div
          v-for="(item, index) in botsupplierList"
          :key="index"
          class="supplier-box"
        >
          <div class="supplier">
            <div class="supplier-up">
              <div>{{ item.name }}</div>
              <span>法人:{{ item.person }}</span>
            </div>
            <div class="supplier-bottom">
              <div class="look" @click="todetail(item.name)">查看贸易明细</div>
              <div class="del">删除关系</div>
            </div>
          </div>
        </div>
        <div class="new-supplier-box">
          <i class="el-icon-plus"></i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      upsupplierList: [
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
      ],
      botsupplierList: [
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
        { name: '深圳市中集车辆有限公司', person: '张哲峰' },
      ],
    }
  },
  methods: {
    todetail(tradename) {
      this.$router.push({
        path: '/trade/detail',
        query: { tradename },
      })
    },
  },
}
</script>
<style scoped>
.details-content1,
.details-content2 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content2 {
  margin-bottom: 50px;
}
.details-content1-tit,
.details-content2-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title,
.details-content2-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.up-supplier-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
}
.supplier-box {
  width: 291px;
  height: 131px;
  border-radius: 8px;
  background-color: #f7f7f7;
  margin-top: 20px;
  margin-right: 20px;
}
.supplier {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 16px;
  box-sizing: border-box;
  align-items: flex-start;
  flex-direction: column;
  justify-content: space-between;
}
.supplier-up {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.supplier-up div {
  font-size: 16px;
  color: #697cff;
  font-weight: 600;
  margin-bottom: 8px;
}
.supplier-up span {
  font-size: 14px;
  color: #7d7d7d;
}
.supplier-bottom {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.supplier-bottom .look {
  width: 102px;
  height: 28px;
  text-align: center;
  font-size: 14px;
  line-height: 30px;
  border-radius: 4px;
  border: 1px solid #697cff;
  color: #697cff;
  margin-right: 10px;
}
.supplier-bottom .del {
  width: 79px;
  height: 28px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ff4d36;
  color: #ff4d36;
}
.new-supplier-box {
  width: 291px;
  height: 131px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #7d7d7d;
  margin-top: 20px;
  border: 1px dashed #d7d7d7;
}
.supplier-box:nth-child(4n) {
  margin-right: 0;
}
</style>
