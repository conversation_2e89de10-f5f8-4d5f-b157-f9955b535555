<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customerserviceconfig_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button
          type="success"
          size="small"
          icon="el-icon-success"
          plain
          @click="toAble()"
        >启用
        </el-button>
        <el-button
          type="info"
          size="small"
          icon="el-icon-info"
          plain
          @click="toDisable()"
        >禁用
        </el-button>
      </template>


      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          v-if="scope.row.status==1"
          @click="singleAble(scope.row, scope.index)"
        >
          启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row, index)"
          v-if="scope.row.status != 1"
          size="small"
        >编辑
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-circle-plus"
          v-if="scope.row.status==0"
          @click="singleDisable(scope.row, scope.index)"
        >
          禁用
        </el-button>
      </template>


      <template slot-scope="{ scope, row }" slot="status">
        <el-tag type="info" v-if="row.status == 1">已禁用</el-tag>
        <el-tag type="success" v-if="row.status == 0">已启用</el-tag>
      </template>


      <template slot-scope="{row,index,type}" slot="menuForm">
        <el-button type="primary"
                   size="small"
                   plain
                   v-if="(type=='edit'||type=='add')"
                   @click="toEnable(row,index)"
        >启用
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove, enable, disable} from "@/api/customer/customerserviceconfig";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        indexLabel: '序号',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        editBtn: false,
        delBtn: false,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "客服请求地址",
            prop: "url",
            search: true,
            rules: [{
              required: true,
              message: "请输入客服请求地址",
              trigger: "blur"
            }]
          },
          {
            label: "服务网址",
            prop: "webSite",
            search: true,
            rules: [{
              required: true,
              message: "请输入服务网址",
              trigger: "blur"
            }]
          },
          {
            label: '操作人',
            search: true,
            display: false,
            prop: 'operateName',
          },
          {
            label: '状态',
            prop: 'status',
            search: true,
            display: false,
            hide: false,
            type: 'select',
            dataType: 'number',
            dicData: [
              {
                label: '启用',
                value: 0,
              },
              {
                label: '禁用',
                value: 1,
              },
            ],
          },

        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.customerserviceconfig_add, false),
        viewBtn: this.vaildData(this.permission.customerserviceconfig_view, false),
        delBtn: this.vaildData(this.permission.customerserviceconfig_delete, false),
        editBtn: this.vaildData(this.permission.customerserviceconfig_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    toEnable(row,done,index){
      this.form.status=0
      this.$refs.crud.rowSave()
    },
    cedit(row, index) {
      this.$refs.crud.rowEdit(row, index)
    },
    singleAble(row) {
      let msg = '确认将选择的数据启用?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids=row.id;
          return enable(ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    singleDisable(row) {
      let msg = '确认将选择的数据禁用?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids=row.id;
          return disable(ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    toAble() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      let msg = '确认将选择的数据启用?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return enable(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    toDisable() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      let msg = '确认将选择的数据禁用用?'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return disable(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })

    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
