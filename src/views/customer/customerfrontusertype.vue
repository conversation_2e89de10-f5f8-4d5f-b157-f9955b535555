<template>
  <basic-container>
    <avue-crud  :option="option"
                :table-loading="loading"
                :data="data"
                ref="crud"
                v-model="form"
                :permission="permissionList"
                :before-open="beforeOpen"
                :before-close="beforeClose"
                @row-del="rowDel"
                @row-update="rowUpdate"
                @row-save="rowSave"
                @search-change="searchChange"
                @search-reset="searchReset"
                @selection-change="selectionChange"
                @current-change="currentChange"
                @size-change="sizeChange"
                @refresh-change="refreshChange"
                @on-load="onLoad"
                @tree-load="treeLoad"
    >
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customerfrontusertype_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/customer/customerfrontusertype";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        parentId: 0,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          addBtn:false,
          editBtn:false,
          delBtn:false,
          column: [
            {
              label: "用户ID",
              prop: "customerId",
              rules: [{
                required: true,
                message: "请输入用户ID",
                trigger: "blur"
              }]
            },
            {
              label: "1-个人  2-企业",
              prop: "type",
              rules: [{
                required: true,
                message: "请输入1-个人  2-企业",
                trigger: "blur"
              }]
            },
            {
              label: "个人id  或 融资企业id",
              prop: "companyId",
              rules: [{
                required: true,
                message: "请输入个人id  或 融资企业id",
                trigger: "blur"
              }]
            },
            {
              label: '上级机构',
              prop: 'parentId',
              dicData: [],
              type: 'tree',
              hide: true,
              addDisabled: false,
              display: false,
              props: {
                label: 'title',
              },
              rules: [
                {
                  required: false,
                  message: '请选择上级机构',
                  trigger: 'click',
                },
              ],
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.customerfrontusertype_add, false),
          viewBtn: this.vaildData(this.permission.customerfrontusertype_view, false),
          delBtn: this.vaildData(this.permission.customerfrontusertype_delete, false),
          editBtn: this.vaildData(this.permission.customerfrontusertype_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.data = []
            this.parentId = 0
            this.$refs.crud.refreshTable()
            this.$refs.crud.toggleSelection()
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.parentId = 0
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.parentId = ''
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      treeLoad(tree, treeNode, resolve) {
        const parentId = tree.id
        getList(parentId).then(res => {
          resolve(res.data.data)
        })
      },
      beforeClose(done) {
        this.parentId = ''
        const column = this.findObject(this.option.column, 'parentId')
        column.value = ''
        column.addDisabled = false
        this.onLoad(this.page)
        done()
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(this.parentId,  Object.assign(params, this.query)).then(res => {
          this.data = res.data.data
          this.loading = false
          this.selectionClear()
        });
      }
    }
  };
</script>

<style>
</style>
