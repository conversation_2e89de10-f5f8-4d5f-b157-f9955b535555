<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customerbusinessinfosub_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/customer/customerbusinessinfosub";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "法定代表人",
              prop: "legalPersonName",
              rules: [{
                required: true,
                message: "请输入法定代表人",
                trigger: "blur"
              }]
            },
            {
              label: "手机号码",
              prop: "phoneNumber",
              rules: [{
                required: true,
                message: "请输入手机号码",
                trigger: "blur"
              }]
            },
            {
              label: "成立日期",
              prop: "estiblishTime",
              rules: [{
                required: true,
                message: "请输入成立日期",
                trigger: "blur"
              }]
            },
            {
              label: "统一社会信用代码",
              prop: "creditCode",
              rules: [{
                required: true,
                message: "请输入统一社会信用代码",
                trigger: "blur"
              }]
            },
            {
              label: "组织机构代码",
              prop: "orgNumber",
              rules: [{
                required: true,
                message: "请输入组织机构代码",
                trigger: "blur"
              }]
            },
            {
              label: "经营开始时间",
              prop: "fromTime",
              rules: [{
                required: true,
                message: "请输入经营开始时间",
                trigger: "blur"
              }]
            },
            {
              label: "经营结束时间",
              prop: "toTime",
              rules: [{
                required: true,
                message: "请输入经营结束时间",
                trigger: "blur"
              }]
            },
            {
              label: "工商注册号",
              prop: "regNumber",
              rules: [{
                required: true,
                message: "请输入工商注册号",
                trigger: "blur"
              }]
            },
            {
              label: "核准日期",
              prop: "approvedTime",
              rules: [{
                required: true,
                message: "请输入核准日期",
                trigger: "blur"
              }]
            },
            {
              label: "注册资本",
              prop: "regCapital",
              rules: [{
                required: true,
                message: "请输入注册资本",
                trigger: "blur"
              }]
            },
            {
              label: "登记机关",
              prop: "regInstitute",
              rules: [{
                required: true,
                message: "请输入登记机关",
                trigger: "blur"
              }]
            },
            {
              label: "企业类型",
              prop: "companyOrgType",
              rules: [{
                required: true,
                message: "请输入企业类型",
                trigger: "blur"
              }]
            },
            {
              label: "行业",
              prop: "industry",
              rules: [{
                required: true,
                message: "请输入行业",
                trigger: "blur"
              }]
            },
            {
              label: "人员规模",
              prop: "employeeNum",
              rules: [{
                required: true,
                message: "请输入人员规模",
                trigger: "blur"
              }]
            },
            {
              label: "参保人员",
              prop: "medicalInsurance",
              rules: [{
                required: true,
                message: "请输入参保人员",
                trigger: "blur"
              }]
            },
            {
              label: "注册地址",
              prop: "regLocation",
              rules: [{
                required: true,
                message: "请输入注册地址",
                trigger: "blur"
              }]
            },
            {
              label: "总资产",
              prop: "totalAssets",
              rules: [{
                required: true,
                message: "请输入总资产",
                trigger: "blur"
              }]
            },
            {
              label: "经营范围",
              prop: "businessScope",
              rules: [{
                required: true,
                message: "请输入经营范围",
                trigger: "blur"
              }]
            },
            {
              label: "注册时间",
              prop: "regDate",
              rules: [{
                required: true,
                message: "请输入注册时间",
                trigger: "blur"
              }]
            },
            {
              label: "变更表ID",
              prop: "recordId",
              rules: [{
                required: true,
                message: "请输入变更表ID",
                trigger: "blur"
              }]
            },
            {
              label: "更新时间",
              prop: "changeDate",
              rules: [{
                required: true,
                message: "请输入更新时间",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.customerbusinessinfosub_add, false),
          viewBtn: this.vaildData(this.permission.customerbusinessinfosub_view, false),
          delBtn: this.vaildData(this.permission.customerbusinessinfosub_delete, false),
          editBtn: this.vaildData(this.permission.customerbusinessinfosub_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
