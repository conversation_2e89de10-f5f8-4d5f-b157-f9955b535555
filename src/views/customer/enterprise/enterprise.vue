<template>
  <div>
    <avue-form ref="form" v-model="form" :option="option">
      <template slot="menuForm">
        <el-button icon="el-icon-search" type="primary" @click="handleSubmit"
          >搜 索</el-button
        >
        <el-button icon="el-icon-delete" @click="handleEmpty">清 空</el-button>
      </template>
    </avue-form>
    <div class="customer-list">
      <CustomerList></CustomerList>
    </div>
  </div>
</template>
<script>
import CustomerList from './components/customerList.vue'
export default {
  components: {
    CustomerList,
  },
  data() {
    return {
      form: {},
      option: {
        emptyBtn: false,
        submitBtn: false,
        column: [
          {
            label: '手机号',
            prop: 'username',

            span: 10,
            maxlength: 11,
            rules: [
              {
                message: '请输入手机号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '注册日期',
            prop: 'date',
            type: 'date',
            span: 10,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
            },
          },
        ],
      },
    }
  },
  methods: {
    handleEmpty() {
      this.$refs.form.resetForm()
    },
    handleSubmit() {
      //this.$refs.form.submit();
      this.$refs.form.validate((vaild, done) => {
        if (vaild) {
          this.$message.success(JSON.stringify(this.form))
          setTimeout(() => {
            done()
          }, 2000)
        }
      })
    },
  },
}
</script>

<style scoped>
/deep/ .avue-crud .el-input--small .el-input__inner,
.avue-form .el-input--small .el-input__inner {
  height: 36px;

  line-height: 36px;
}

/deep/ .el-form-item {
  margin: 26px 0 7px 0;
}

/deep/ .avue-form__menu {
  padding: 0px 10px 0 0;
}

/deep/ .avue-form__group {
  flex-wrap: nowrap;
}

/deep/ .avue-form__menu {
  display: inherit;
}
</style>
