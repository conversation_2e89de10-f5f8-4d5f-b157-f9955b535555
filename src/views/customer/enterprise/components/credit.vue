<template>
  <div>
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">授信额度</div>
      </div>
      <div class="table">
        <avue-crud
          :data="data"
          :option="option"
          :page.sync="page1"
          @current-change="currentChange"
        >
          <template slot-scope="{ row }" slot="state">
            <el-tag
              :type="
                row.state == 0
                  ? 'info'
                  : row.state == 1
                  ? 'info'
                  : row.state == 2
                  ? 'info'
                  : row.state == 3
                  ? ''
                  : 'success'
              "
              >{{
                row.state == 0
                  ? '已作废'
                  : row.state == 1
                  ? '已结清'
                  : row.state == 2
                  ? '已取消'
                  : row.state == 3
                  ? '待还款'
                  : '待放款'
              }}</el-tag
            >
          </template>
        </avue-crud>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      page1: {
        currentPage: 1,
        total: 0,
        background: false,
        pageSize: 10,
      },
      data: [
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 2,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 4,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 3,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 0,
        },
        {
          num: '123124',
          orderNum: '********',
          bank: '中国建设银行',
          name: 'wdnmd',
          money: '234',
          term: '24',
          mode: '就不还',
          interest: '10000',
          time: '2021-12-17',
          state: 1,
        },
      ],
      option: {
        align: 'center',
        editBtn: false,
        addBtn: false,
        columnBtn: false,
        refreshBtn: false,
        delBtn: false,
        background: '#dddddd',
        border: true,
        menu: false,
        menuAlign: 'center',
        column: [
          {
            label: '信货编号',
            prop: 'num',
            html: true,
            formatter: val => {
              console.log()
              return (
                '<span style="color:rgb(18, 119, 255)">' + val.num + '</span>'
              )
            },
          },
          {
            label: '销售订单编号',
            prop: 'orderNum',
            html: true,
            formatter: val => {
              console.log()
              return (
                '<span style="color:rgb(18, 119, 255)">' +
                val.orderNum +
                '</span>'
              )
            },
          },
          {
            label: '资金方',
            prop: 'bank',
            html: true,
            formatter: val => {
              console.log()
              return (
                '<span style="color:rgb(18, 119, 255)">' + val.bank + '</span>'
              )
            },
          },
          {
            label: '产品名称',
            prop: 'name',
          },
          {
            label: '贷款金额(万元)',
            prop: 'money',
          },
          {
            label: '贷款期限(月)',
            prop: 'term',
          },
          {
            label: '还款方式',
            prop: 'mode',
          },
          {
            label: '年利息(%)',
            prop: 'interest',
          },
          {
            label: '创建时间',
            prop: 'time',
          },
          {
            label: '信货状态',
            prop: 'state',
          },
        ],
      },
    }
  },
  created() {
    this.getList(), (this.page1.total = this.data.length)
  },
  methods: {
    getList() {
      //   if (this.page1.currentPage === 1) {
      //     this.data = [
      //       {
      //         id: 1,
      //         name: '张三',
      //         sex: '男',
      //         state:0
      //       },
      //       {
      //         id: 2,
      //         name: '李四',
      //         sex: '女',
      //         state:3
      //       },
      //     ]
      //   } else if (this.page1.currentPage == 2) {
      //     this.data = [
      //       {
      //         id: 3,
      //         name: '王五',
      //         sex: '女',
      //       },
      //       {
      //         id: 4,
      //         name: '赵六',
      //         sex: '女',
      //       },
      //     ]
      //   }
      //   if (this.page1.currentPage === 1) {
      //     this.data = [
      //       {
      //         id: 1,
      //         name: '张三',
      //         sex: '男',
      //       },
      //       {
      //         id: 2,
      //         name: '李四',
      //         sex: '女',
      //       },
      //     ]
      //   } else if (this.page1.currentPage == 2) {
      //     this.data = [
      //       {
      //         id: 3,
      //         name: '王五',
      //         sex: '女',
      //       },
      //       {
      //         id: 4,
      //         name: '赵六',
      //         sex: '女',
      //       },
      //     ]
      //   }
    },
    sizeChange(val) {
      this.page1.currentPage = 1
      this.page1.pageSize = val
      this.getList()
      this.$message.success('行数' + val)
    },
    currentChange(val) {
      this.page1.currentPage = val
      this.getList()
      this.$message.success('页码' + val)
    },
  },
}
</script>
<style scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
/deep/.el-table th {
  background-color: #f7f7f7;
}
/deep/.el-table .cell {
  font-size: 14px;
  text-align: left;
}
</style>
