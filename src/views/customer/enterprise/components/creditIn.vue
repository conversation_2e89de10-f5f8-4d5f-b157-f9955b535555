<template>
  <div class="Credit-in">
    <div class="details-content1">
      <div class="credit-tit">
        <div class="details-content1-tit">
          <div class="title-dot"></div>
          <div class="title">征信信息</div>
        </div>
        <div class="edit-btn" @click="xians">编辑</div>
      </div>
      <div class="credit-list">
        <div
          v-for="(item, index) in creditList"
          :key="index"
          :class="(index + 1) % 5 == 0 ? 'rowzuihd' : 'credit'"
        >
          <div :class="item.val > 0 ? 'have' : 'frequency'">
            <span class="num">{{ item.val }}</span>
            <span>{{ item.company }}</span>
          </div>
          <div class="tit">{{ item.tit }}</div>
        </div>
      </div>
      <div v-if="xian" class="zzc" @click="yin"></div>
      <div v-if="xian" class="edit-box">
        <div class="edit-list">
          <div class="edit" v-for="(item, index) in creditList" :key="index">
            <span class="tit">{{ item.tit }}</span>
            <input type="text" v-model="item.val" />
            <span>{{ item.company }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      creditList: [
        { tit: '当前逾期', company: '笔', val: 0 },
        { tit: '当前次级', company: '笔', val: 0 },
        { tit: '当前损失', company: '笔', val: 0 },
        { tit: '当前逾期数', company: '笔', val: 0 },
        { tit: '当前逾期数', company: '笔', val: 0 },
        { tit: '近24个月逾期为M1', company: '笔', val: 0 },
        { tit: '近24个月逾期为M2', company: '笔', val: 0 },
        { tit: '近24个月逾期为M3', company: '笔', val: 0 },
        { tit: '近2个月审批、征信查询', company: '笔', val: 0 },
        { tit: '近12个月审批、征信查询', company: '笔', val: 0 },
        { tit: '企业涉诉结案件', company: '次', val: 0 },
        { tit: '企业涉诉未结案件', company: '次', val: 0 },
        { tit: '逾期率', company: '%', val: 0 },
      ],
      xian: 0,
    }
  },
  methods: {
    xians() {
      this.xian = 1
    },
    yin() {
      this.xian = 0
    },
  },
}
</script>
<style scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.credit-tit {
  width: 100%;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.edit-btn {
  width: 50px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  font-size: 16px;
  color: #1277ff;
  border: 1px solid #1277ff;
  cursor: pointer;
}
.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.credit-list {
  display: flex;
  background-color: #ffffff;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: 12px;
}
.credit {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 40px 40px 0;
  background-color: #f7f7f7;
  border-radius: 8px;
  width: calc((100% - 160px) / 5);
  height: 100px;
}
.rowzuihd {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border-radius: 8px;
  width: calc((100% - 160px) / 5);
  height: 100px;
  font-size: 14px;
  margin-bottom: 40px;
}
.frequency {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  font-weight: 600;
}
.have {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  font-weight: 600;
  color: #826f00;
}
.frequency .num {
  font-size: 20px;
}
.credit .tit {
  margin-top: 8px;
}
.zzc {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9998;
  background-color: rgba(0, 0, 0, 0.3);
}

.edit-list {
  position: fixed;
  height: 500px;
  width: 1200px;
  padding: 20px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  background-color: #ffffff;
  border-radius: 8px;
  z-index: 9999;
}
.edit {
  width: 45%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dddddd;
  border-radius: 8px;
  height: 40px;
  padding: 15px;
  margin-right: 5%;
  box-sizing: border-box;
}
.edit span {
  font-size: 16px;
  width: 10%;
}
.edit .tit {
  font-size: 16px;
  width: 40%;
}
.edit input {
  width: 50%;
  border: 0;
  outline: 0;
  height: 38px;
  font-size: 20px;
}
</style>
