<template>
  <div class="customerlist">
    <avue-crud
      :data="data"
      v-model="form"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      :option="option"
    ></avue-crud>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {},
      data: [
        {
          id: 1,
          logo: require('../../../../../public/img/logo_reverse_white.svg'),
          userID: 123123,
          name: '小米科技有限责任公司',
          customerManager: '穆明清',
          contacts: 'aaaa',
          contactInformation: 'bbbbbbbb',
          WhiteName: '是',
          ComprehensiveScore: '98',
          state: '已授信',
          LastVisited: '2021-12-9',
        },
      ],
      option: {
        align: 'center',
        menuAlign: 'center',
        viewBtn: true,
        column: [
          {
            label: 'logo',
            prop: 'logo',
          },
          {
            label: '用户ID',
            prop: 'userID',
          },
          {
            label: '企业名称',
            prop: 'name',
          },
          {
            label: '客户经理',
            prop: 'customerManager',
          },
          {
            label: '联系人',
            prop: 'contacts',
          },
          {
            label: '联系方式',
            prop: 'contactInformation',
          },
          {
            label: '是否白名',
            prop: 'WhiteName',
          },
          {
            sortable: true,
            label: '综合评分',
            prop: 'ComprehensiveScore',
          },
          {
            label: '状态',
            prop: 'state',
          },
          {
            label: '上次访问时间',
            prop: 'LastVisited',
          },
        ],
      },
    }
  },
  // created() {
  //   this.getList()
  // },
  methods: {
    //   getList() {
    //     if (this.page1.currentPage === 1) {
    //       this.data = [
    //         {
    //           id: 1,
    //           name: '张三',
    //           sex: '男',
    //         },
    //         {
    //           id: 2,
    //           name: '李四',
    //           sex: '女',
    //         },
    //       ]
    //     } else if (this.page1.currentPage == 2) {
    //       this.data = [
    //         {
    //           id: 3,
    //           name: '王五',
    //           sex: '女',
    //         },
    //         {
    //           id: 4,
    //           name: '赵六',
    //           sex: '女',
    //         },
    //       ]
    //     }
    //     if (this.page1.currentPage === 1) {
    //       this.data = [
    //         {
    //           id: 1,
    //           name: '张三',
    //           sex: '男',
    //         },
    //         {
    //           id: 2,
    //           name: '李四',
    //           sex: '女',
    //         },
    //       ]
    //     } else if (this.page1.currentPage == 2) {
    //       this.data = [
    //         {
    //           id: 3,
    //           name: '王五',
    //           sex: '女',
    //         },
    //         {
    //           id: 4,
    //           name: '赵六',
    //           sex: '女',
    //         },
    //       ]
    //     }
    //   },
    //   sizeChange(val) {
    //     this.page1.currentPage = 1
    //     this.page1.pageSize = val
    //     this.getList()
    //     this.$message.success('行数' + val)
    //   },
    //   currentChange(val) {
    //     this.page1.currentPage = val
    //     this.getList()
    //     this.$message.success('页码' + val)
    //   },
    beforeOpen(done, type) {
      if (type == 'view') {
        this.$router.push(
          `/customer/enterprise/details?keyword=${this.form.name}`
        )
      }
      done()
    },
  },
}
</script>
<style scoped>
.avue-crud {
  background-color: #ffffff;
  padding: 0 15px;
  box-sizing: border-box;
}
</style>
