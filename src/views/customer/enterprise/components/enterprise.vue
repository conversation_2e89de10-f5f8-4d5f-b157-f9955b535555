<template>
  <div class="Enterprise-certificate">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">法定代表人</div>
      </div>
      <div class="function">
        <div class="tap-switch">
          <div
            v-for="(item, index) in date"
            :key="index"
            :class="
              sfxz == index
                ? 'xz'
                : index == 0
                ? 'deyig'
                : index == date.length - 1
                ? 'zuihoyig'
                : 'wxz'
            "
            @click="qiehuan(index)"
          >
            <span
              :class="
                index == date.length - 1
                  ? 'zuihoyig'
                  : index == 0
                  ? 'deyig'
                  : 'qitade'
              "
              >{{ item }}</span
            >
          </div>
        </div>
        <div class="up-data">上传证件</div>
      </div>
      <avue-crud
        :data="data"
        :option="option"
        ref="crud"
        :page.sync="page1"
        @row-save="handleSave"
        @row-update="handleUpdate"
      >
        <template slot-scope="{ row, index, type, size }" slot="menu">
          <el-button
            :size="size"
            :type="type"
            @click.stop="$refs.crud.rowEdit(row, index)"
            >下载</el-button
          >
          <el-button
            :size="size"
            :type="type"
            @click.stop="$refs.crud.rowView(row, index)"
            @click="tiao(row)"
            >预览</el-button
          >
          <el-button :size="size" :type="type" @click="zuofei(row)"
            >作废</el-button
          >
          <el-button :size="size" :type="type" @click="shanchu(row)"
            >删除</el-button
          >
        </template>
        <template slot-scope="{ row }" slot="state">
          <el-tag :type="row.state ? 'success' : 'info'">{{
            row.state == 0 ? '已作废' : '有效'
          }}</el-tag>
        </template>
      </avue-crud>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      date: ['2021年', '2020年', '2019年'],
      time: '',
      sfxz: 0,
      form: {},
      page1: {
        currentPage: 1,
        total: 20,
        layout: 'prev,pager,next',
        pageSize: 10,
      },
      data: [
        {
          type: '营业执照',
          format: 'png',
          cTime: '2021-10-20 10:36:27',
          sTime: '2021-10-20 10:36:27',
          eTime: '2021-10-20 10:36:27',
          state: 0,
        },
      ],
      option: {
        columnBtn: false,
        refreshBtn: false,
        addBtn: false,
        delBtn: false,
        border: true,
        index: true,
        indexLabel: '序号',
        align: 'center',
        menuAlign: 'center',
        editBtn: false,
        column: [
          {
            label: '证件类型',
            prop: 'type',
          },
          {
            label: '证件格式',
            span: 2,
            prop: 'format',
          },
          {
            label: '创建时间',
            prop: 'cTime',
          },
          {
            label: '签署时间',
            prop: 'sTime',
          },
          {
            label: '有效时间',
            prop: 'eTime',
          },
          {
            label: '状态',
            prop: 'state',
          },
        ],
      },
    }
  },
  methods: {
    qiehuan(index) {
      this.sfxz = index
      this.time = this.date[index]
    },
    zuofei(row) {
      row.state = 0
    },
    shanchu(row) {
      this.data.splice(row.$index, 1)
    },
  },
}
</script>
<style scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.function {
  margin: 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tap-switch {
  height: 40px;
  width: 250px;
  display: flex;
  align-items: center;
  border: 1px solid #161616;
  border-radius: 20px;
  overflow: hidden;
}
.zuihoyig {
  display: block;
  height: 100%;
  line-height: 40px;
  border-right: none;
  text-align: center;
  border-top-right-radius: 19px;
  border-bottom-right-radius: 19px;
  background-color: #ffffff;
  color: #161616;
  flex: 1;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  cursor: pointer;
}
.deyig {
  display: block;
  height: 100%;
  line-height: 40px;
  text-align: center;
  border-right: 1px solid #161616;
  border-top-left-radius: 19px;
  border-bottom-left-radius: 19px;
  background-color: #ffffff;
  cursor: pointer;
  color: #161616;
  flex: 1;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
}
.deyig .deyig {
  border-right: none;
  cursor: pointer;
}
.xz .deyig {
  color: #ffffff;
  cursor: pointer;
}
.xz .zuihoyig {
  color: #ffffff;
  cursor: pointer;
}
.qitade {
  display: block;
  height: 100%;
  line-height: 40px;
  text-align: center;
  border-right: 1px solid #161616;
  cursor: pointer;
}
.wxz {
  background-color: #ffffff;
  color: #161616;
  flex: 1;
  height: 100%;
}
.xz {
  background-color: #0f0f0f;
  flex: 1;
  height: 101%;
  color: #ffffff;
}
.up-data {
  height: 40px;
  width: 90px;
  border: 1px solid #1684fc;
  border-radius: 20px;
  color: #1684fc;
  line-height: 40px;
  text-align: center;
  cursor: pointer;
}
.el-button.is-plain:focus,
.el-button.is-plain:hover {
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  outline: 0;
}
/deep/.avue-crud__menu {
  display: none;
}
/deep/.el-table .cell {
  font-size: 14px;
  text-align: left;
}
</style>
