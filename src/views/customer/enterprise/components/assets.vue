<template>
  <div class="assets">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">资产负债</div>
      </div>
      <div class="function">
        <div class="tap-switch">
          <div
            v-for="(item, index) in date"
            :key="index"
            :class="
              sfxz == index
                ? 'xz'
                : index == 0
                ? 'deyig'
                : index == date.length - 1
                ? 'zuihoyig'
                : 'wxz'
            "
            @click="qiehuan(index)"
          >
            <span
              :class="
                index == date.length - 1
                  ? 'zuihoyig'
                  : index == 0
                  ? 'deyig'
                  : 'qitade'
              "
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
      <table>
        <thead>
          房产/土地
        </thead>
        <tr>
          <th class="address">所在地址</th>
          <th class="worth">评估净值(万元)</th>
          <th class="mortgage">是否抵押</th>
          <th class="operation">操作</th>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </table>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      date: ['企业', '法人'],
      sfxz: 0,
    }
  },
  methods: {
    qiehuan(index) {
      this.sfxz = index
      this.time = this.date[index]
    },
  },
}
</script>
<style scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.function {
  margin: 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tap-switch {
  height: 40px;
  width: 160px;
  display: flex;
  align-items: center;
  border: 1px solid #161616;
  border-radius: 20px;
  overflow: hidden;
}
.zuihoyig {
  display: block;
  height: 100%;
  line-height: 40px;
  border-right: none;
  text-align: center;
  border-top-right-radius: 19px;
  border-bottom-right-radius: 19px;
  background-color: #ffffff;
  color: #161616;
  flex: 1;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  cursor: pointer;
}
.deyig {
  display: block;
  height: 100%;
  line-height: 40px;
  text-align: center;
  border-right: 1px solid #161616;
  border-top-left-radius: 19px;
  border-bottom-left-radius: 19px;
  background-color: #ffffff;
  cursor: pointer;
  color: #161616;
  flex: 1;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
}
.deyig .deyig {
  border-right: none;
  cursor: pointer;
}
.xz .deyig {
  color: #ffffff;
  cursor: pointer;
}
.xz .zuihoyig {
  color: #ffffff;
  cursor: pointer;
}
.qitade {
  display: block;
  height: 100%;
  line-height: 40px;
  text-align: center;
  border-right: 1px solid #161616;
  cursor: pointer;
}
.wxz {
  background-color: #ffffff;
  color: #161616;
  flex: 1;
  height: 100%;
}
.xz {
  background-color: #0f0f0f;
  flex: 1;
  height: 101%;
  color: #ffffff;
}
table {
  width: 100%;
  margin-top: 18px;
  border: 1px solid #fefefe;
}
table th {
  background-color: #f7f7f7;
  padding: 14px;
  height: 40px;
  box-sizing: border-box;
  font-size: 14px;
  text-align: left;
  color: #101010;
  border: 1px solid #bbbbbb;
}
table td {
  background-color: #ffffff;
}
table .address {
  width: 359px;
}
table .worth {
  width: 185px;
}
table .mortgage {
  width: 162px;
  text-align: center;
}
table .operation {
  width: 279px;
}
</style>
