<template>
  <div class="tap-switch">
    <div
      v-for="(item, index) in xuanzlist"
      :key="index"
      :class="
        a == index
          ? 'xz'
          : index == 0
          ? 'deyig'
          : index == xuanzlist.length - 1
          ? 'zuihoyig'
          : 'wxz'
      "
      @click="qiehuan(index)"
    >
      <span
        :class="
          index == xuanzlist.length - 1
            ? 'zuihoyig'
            : index == 0
            ? 'deyig'
            : 'qitade'
        "
        >{{ item }}</span
      >
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      xuanzlist: [
        '基础资料',
        '价值分析',
        '征信信息',
        '资产负债',
        '负面因素',
        '开通产品',
        '贸易背景',
        '历史变更',
        '证件资料',
        '账户信息',
      ],
      a: 0,
    }
  },
  methods: {
    qiehuan(index) {
      this.a = index
      this.$emit('switch', index)
    },
  },
}
</script>
<style scoped>
.tap-switch {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  border: 1px solid #1684fc;
  border-radius: 4px;
}
.zuihoyig {
  display: block;
  height: 100%;
  line-height: 40px;
  border-right: none;
  text-align: center;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  background-color: #ffffff;
  color: #1684fc;
  flex: 1;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
}
.deyig {
  display: block;
  height: 100%;
  line-height: 40px;
  text-align: center;
  border-right: 1px solid #1684fc;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  background-color: #ffffff;
  color: #1684fc;
  flex: 1;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
}
.deyig .deyig {
  border-right: 0;
}
.xz .deyig {
  color: #ffffff;
  border-right: 1px solid #1684fc;
}
.xz .zuihoyig {
  color: #ffffff;
}
.qitade {
  display: block;
  height: 100%;
  line-height: 40px;
  text-align: center;
  border-right: 1px solid #1684fc;
}
.wxz {
  background-color: #ffffff;
  color: #1684fc;
  flex: 1;
  height: 100%;
}
.xz {
  background-color: #1684fc;
  flex: 1;
  height: 100%;
  color: #ffffff;
}
</style>
