<template>
  <div class="value">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">经营数据</div>
      </div>
      <table class="management-table">
        <tr>
          <th>去年纳税销售总额(万元)</th>
          <td>{{ data | qian }}</td>
          <th>前年纳税销售总额(万元)</th>
          <td>{{ '1400' | qian }}</td>
          <th>净利润(万元)</th>
          <td>{{ '600' | qian }}</td>
        </tr>
        <tr>
          <th>去年应纳税总额(万元)</th>
          <td></td>
          <th>前年应纳税总额(万元)</th>
          <td></td>
          <th>同比销售增长率</th>
          <td>%</td>
        </tr>
        <tr>
          <th>同比销售增长率</th>
          <td>%</td>
          <th>环比销售增长率(季度)</th>
          <td>%</td>
          <th>回款率</th>
          <td>%</td>
        </tr>
      </table>
      <div class="echier">
        <div class="echier-tit">
          <div class="qiehuan-btn">
            <div
              :class="index == 1 ? 'collection' : ''"
              v-for="(item, index) in btnlist"
              :key="index"
              @click="getdate(index)"
            >
              <span :class="ad == index ? 'xz' : ''">{{ item }}</span>
            </div>
          </div>
          <p>最近12月趋势</p>
        </div>
        <div id="echier-box"></div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
console.log(echarts)

export default {
  data() {
    return {
      data: '20000000',
      ad: 0,
      btnlist: ['纳税收入', '销售回款'],
    }
  },
  filters: {
    qian(data) {
      let b = data.split('')
      let f = b.reverse()
      function chunk(array, size) {
        //获取数组的长度，如果你传入的不是数组，那么获取到的就是undefined
        const length = array.length
        //判断不是数组，或者size没有设置，size小于1，就返回空数组
        if (!length || !size || size < 1) {
          return []
        }
        let index = 0 //用来表示切割元素的范围start
        let resIndex = 0 //用来递增表示输出数组的下标
        let result = new Array(Math.ceil(length / size))
        while (index < length) {
          result[resIndex++] = array.slice(index, (index += size))
        }
        return result
      }
      let c = chunk(f, 3)
      let d = ''
      if (data.length % 3 != 0) {
        for (let i = 0; i < c.length; i++) {
          if (c[i].length == 3 && i != c.length - 1) {
            d += c[i].join('') + ','
          } else {
            d += c[i].join('')
          }
        }
      } else {
        return data
      }
      let e = d.split('').reverse()
      return e.join('')
    },
  },
  methods: {
    getdate(index) {
      this.ad = index
    },
    charts() {
      console.log(echarts)
      let myChart = echarts.init(document.getElementById('echier-box'))
      // 绘制图表
      myChart.setOption({
        color: ['#1277ff'],
        tooltip: {},
        xAxis: {
          data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子'],
          splitLine: {
            show: false,
          },
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              color: '#1277ff',
              width: '2',
            },
          },
        },
        yAxis: {
          show: false,
        },
        series: [
          {
            name: '销量',
            type: 'bar',
            data: [5, 20, 36, 10, 10, 20],
            barWidth: 4,
          },
        ],
      })
    },
  },
  mounted() {
    this.charts()
  },
}
</script>
<style scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.management-table {
  margin-top: 12px;
  width: 100%;
  border: 1px solid #bbbbbb;
}
.management-table th {
  width: 200px;
  padding: 8px;
  box-sizing: border-box;
  text-align: left;
  height: 40px;
  font-size: 16px;
  background-color: #f7f7f7;
  border: 1px solid #bbbbbb;
}
.management-table td {
  height: 40px;
  padding: 8px;
  box-sizing: border-box;
  text-align: left;
  font-size: 16px;
  border: 1px solid #bbbbbb;
}
.echier {
  margin-top: 12px;
  width: 100%;
  height: 350px;
  border: 1px solid #bbbbbb;
  border-radius: 8px;
  position: relative;
}
.echier-tit {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.qiehuan-btn {
  width: 180px;
  height: 38px;
  border: 1px solid #1277ff;
  color: #1277ff;
  display: flex;
  align-items: center;
  border-radius: 4px;
  overflow: hidden;
  justify-content: center;
}
.qiehuan-btn div {
  width: 90px;
  height: 38px;
  line-height: 38px;
  text-align: center;
}
.qiehuan-btn .collection {
  border-left: 1px solid #1277ff;
}
.qiehuan-btn span {
  width: 90px;
  display: block;
  height: 38px;
  background-color: rgba(0, 0, 0, 0);
}
.qiehuan-btn .xz {
  display: block;
  width: 90px;
  height: 38px;
  background-color: #1277ff;
  color: #ffffff;
}

.echier-tit p {
  color: #7d7d7d;
  font-size: 14px;
  margin-top: 20px;
}
#echier-box {
  width: 100%;
  height: 100%;
}
</style>
