<template>
  <div class="risk-box">
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">额度列表</div>
      </div>
      <avue-crud
        :data="data"
        :option="option"
        :page.sync="page1"
        @current-change="currentChange"
      />
      <div class="state-dsp" v-if="staDsp">
        <input type="text" v-model="state" />
        <span>2:为正常</span>
        <span>1:为已冻结</span>
        <span>0:为已失效</span>
      </div>
      <div class="zzc" v-if="staDsp" @click="yinstate"></div>
    </div>
    <div class="details-content1">
      <div class="details-content1-tit">
        <div class="title-dot"></div>
        <div class="title">评分结果</div>
      </div>
      <div class="result-box">
        <div class="result-list">
          <div
            :class="(index + 1) % 5 == 0 ? 'zhyg' : 'result'"
            v-for="(item, index) in resultlist"
            :key="index"
          >
            <div class="result-top">
              <span class="result-company">84.21</span>
              <span>分</span>
            </div>
            <div>评级得分</div>
          </div>
        </div>
        <div class="xian"></div>
        <div class="result-state">
          <div class="result-state-left">
            <div
              v-for="(item, index) in statelist"
              :key="index"
              :class="index == 0 ? 'num' : ''"
            >
              <span class="tit">风控编码:</span>
              <span>57678989584725</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      state: 0,
      staDsp: 0,
      resultlist: [
        { tit: '评级得分', val: '84.21', company: '分' },
        { tit: '信用级别', val: 'B', company: '级' },
        { tit: '指标项总分', val: '387', company: '分' },
        { tit: '风险限额', val: '1841', company: '万元' },
        { tit: '有效期', val: '360', company: '天' },
      ],
      statelist: [
        { tit: '风控编码:', val: '57678989584725' },
        { tit: '风控编码:', val: '57678989584725' },
        { tit: '风控编码:', val: '57678989584725' },
      ],
      page: {
        currentPage: 1,
        total: 20,
        layout: 'prev,pager,next',
        pageSize: 10,
      },
      data: [],
      option: {
        menu: false,
        columnBtn: false,
        refreshBtn: false,
        cancelBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: true,
        align: 'center',
        menuAlign: 'center',
        indexLabel: '序号',
        column: [
          {
            label: '指标编码',
            prop: 'num',
          },
          {
            label: '指标名字',
            prop: 'name',
          },
          {
            label: '最大分数',
            prop: 'max',
          },
          {
            label: '企业传入数据',
            prop: 'setdata',
          },
          {
            label: '最终得分',
            prop: 'fens',
          },
        ],
      },
    }
  },
  methods: {
    setstate() {
      this.staDsp = 1
    },
    yinstate() {
      this.staDsp = 0
    },
  },
}
</script>
<style scoped>
.details-content1 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content1-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.quota-table {
  width: 100%;
  margin-top: 12px;
  border: 1px solid #bbbbbb;
}
.quota-table th {
  background-color: #f7f7f7;
}
.quota-table th,
.quota-table td {
  border: 1px solid #bbbbbb;
  height: 40px;
  padding: 10px 8px;
  text-align: left;
  box-sizing: border-box;
  font-size: 14px;
}
.quota-table .num {
  width: 120px;
}
.quota-table .fund {
  width: 180px;
}
.quota-table .risk,
.quota-table .available,
.quota-table .used {
  width: 150px;
}
.quota-table .state {
  width: 110px;
}
.quota-table .handler,
.quota-table .updata {
  width: 90px;
}
.quota-table td {
  font-size: 18px;
}
.quota-table .num-val {
  color: #1277ff;
}
.quota-table .state-val {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0;
}
.quota-table .state-val div {
  width: 60px;
  height: 24px;
  border: 1px solid #75dcac;
  line-height: 24px;
  font-size: 12px;
  text-align: center;
  color: #75dcac;
  border-radius: 4px;
  margin-top: 8px;
}
.quota-table .state-val .invalid {
  width: 60px;
  height: 24px;
  border: 1px solid #eaeaea;
  line-height: 24px;
  text-align: center;
  color: #84868d;
  margin-top: 8px;
  background-color: #f5f5f5;
}
.quota-table .state-val .frozen {
  width: 60px;
  height: 24px;
  border: 1px solid #70adff;
  line-height: 24px;
  text-align: center;
  color: #70adff;
  margin-top: 8px;
}
.quota-table .oper-val {
  display: flex;
  height: 40px;
  border: 0;
  align-items: center;
  justify-content: flex-start;
}
.quota-table .oper-val div {
  color: #1277ff;
  margin-right: 8px;
  cursor: pointer;
}
.zzc {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9998;
  background-color: rgba(0, 0, 0, 0.3);
}
.state-dsp {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 100px;
  background-color: #ffffff;
  border-radius: 8px;
  display: flex;
  z-index: 9999;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.state-dsp input {
  width: 200px;
  height: 40px;
  border: 1px solid #bbbbbb;
  border-radius: 4px;
  outline: 0;
}
.result-box {
  margin-top: 12px;
}
.result-list {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.result {
  width: calc((100% - 80px) / 5);
  height: 140px;
  background-color: #f7f7f7;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40px;
  justify-content: center;
}
.zhyg {
  width: calc((100% - 80px) / 5);
  height: 140px;
  background-color: #f7f7f7;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 0px;
  justify-content: center;
}
.result-top {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  font-size: 20px;
  color: #1277ff;
  font-weight: 600;
}
.result-company {
  font-size: 26px;
}
.xian {
  width: 100%;
  height: 2px;
  background-color: #f3f3f3;
  margin-top: 20px;
}
.result-state {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.result-state-left {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
.result-state-left div {
  margin-right: 20px;
}
.result-state-left span {
  color: #101010;
}
.result-state-left .tit,
.result-state-left .num .tit {
  color: #84868d;
  margin-right: 4px;
}
.result-state-left .num span {
  color: #1277ff;
}
.result-state-right {
  padding: 4px 14px;
  border: 1px solid #1277ff;
  color: #1277ff;
  border-radius: 4px;
}
.zuid {
  margin-bottom: 100px;
}
</style>
