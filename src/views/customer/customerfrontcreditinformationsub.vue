<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customerfrontcreditinformationsub_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/customer/customerfrontcreditinformationsub";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "当前逾期",
              prop: "currentOverdue",
              rules: [{
                required: true,
                message: "请输入当前逾期",
                trigger: "blur"
              }]
            },
            {
              label: "当前次数",
              prop: "currentTimes",
              rules: [{
                required: true,
                message: "请输入当前次数",
                trigger: "blur"
              }]
            },
            {
              label: "当前损失",
              prop: "currentLoss",
              rules: [{
                required: true,
                message: "请输入当前损失",
                trigger: "blur"
              }]
            },
            {
              label: "当前可疑",
              prop: "currentSuspicious",
              rules: [{
                required: true,
                message: "请输入当前可疑",
                trigger: "blur"
              }]
            },
            {
              label: "当前关注",
              prop: "currentConcerns",
              rules: [{
                required: true,
                message: "请输入当前关注",
                trigger: "blur"
              }]
            },
            {
              label: "m1近24个月逾期为M1",
              prop: "overdueMone",
              rules: [{
                required: true,
                message: "请输入m1近24个月逾期为M1",
                trigger: "blur"
              }]
            },
            {
              label: "M2近24个月逾期为M2",
              prop: "overdueMtwo",
              rules: [{
                required: true,
                message: "请输入M2近24个月逾期为M2",
                trigger: "blur"
              }]
            },
            {
              label: "M3近24个月逾期为M3",
              prop: "overdueMthree",
              rules: [{
                required: true,
                message: "请输入M3近24个月逾期为M3",
                trigger: "blur"
              }]
            },
            {
              label: "近2个月审批、征信查询",
              prop: "approveLetterQueryTwo",
              rules: [{
                required: true,
                message: "请输入近2个月审批、征信查询",
                trigger: "blur"
              }]
            },
            {
              label: "近24个月审批、征信查询",
              prop: "approveLetterQueryTetracosa",
              rules: [{
                required: true,
                message: "请输入近24个月审批、征信查询",
                trigger: "blur"
              }]
            },
            {
              label: "企业涉诉结案件",
              prop: "closedCases",
              rules: [{
                required: true,
                message: "请输入企业涉诉结案件",
                trigger: "blur"
              }]
            },
            {
              label: "企业涉诉未结案件",
              prop: "openCases",
              rules: [{
                required: true,
                message: "请输入企业涉诉未结案件",
                trigger: "blur"
              }]
            },
            {
              label: "逾期率",
              prop: "overdueRate",
              rules: [{
                required: true,
                message: "请输入逾期率",
                trigger: "blur"
              }]
            },
            {
              label: "征信报表",
              prop: "creditReport",
              rules: [{
                required: true,
                message: "请输入征信报表",
                trigger: "blur"
              }]
            },
            {
              label: "变更表ID",
              prop: "recordId",
              rules: [{
                required: true,
                message: "请输入变更表ID",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.customerfrontcreditinformationsub_add, false),
          viewBtn: this.vaildData(this.permission.customerfrontcreditinformationsub_view, false),
          delBtn: this.vaildData(this.permission.customerfrontcreditinformationsub_delete, false),
          editBtn: this.vaildData(this.permission.customerfrontcreditinformationsub_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
