<template>
  <div class="capital-detail">
    <div class="capital-information">
      <!-- 资方信息 -->
      <div class="capital-box-information">
        <div class="capital-img">
          <img :src="informationObj.avatar" alt="" />
        </div>
        <div class="capital-text-information">
          <div class="capital-top-text-information">
            <span class="capital-bank-top-text">{{ informationObj.name }}</span>
            <span class="capital-capitalid-top-text">资金方</span>
            <span class="capital-extension-top-text">{{
              informationObj.totalCredit ? '已授信' : '未授信'
            }}</span>
          </div>
          <div class="capital-bottom-text-information">
            <div class="capital-bottom-left">
              <div class="capital-social-bottom-text">
                <span>统一社会代码：</span>
                <span>{{
                  informationObj.unifiedSocialCode
                    ? informationObj.unifiedSocialCode
                    : '-'
                }}</span>
              </div>
              <div class="capital-contacts-social-bottom-text">
                <span>联系人：</span>
                <span>{{
                  informationObj.realName ? informationObj.realName : '-'
                }}</span>
              </div>
              <div class="capital-contactsway-social-bottom-text">
                <span>联系方式：</span>
                <span>{{
                  informationObj.phone ? informationObj.phone : '-'
                }}</span>
              </div>
            </div>
            <div class="capital-bottom-right">
              <span>授信额度: {{ informationObj.totalCredit || '-' }}元</span>
            </div>
          </div>
        </div>
      </div>

      <div class="capital-label-group">
        <div class="tabBar-box">
          <div
            class="tabBar-for-box"
            :class="{ 'active-box': activeName == item.label }"
            v-for="item in labelList"
            :key="item.value"
            @click="activeName = item.label"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
    <!-- 业务总览 -->
    <BusinessDetail
      :companyId="ids.companyId"
      v-if="activeName === '业务总览'"
      ref="businessDetail"
    />

    <!-- 基础资料 -->
    <ModuleBaseData
      :companyName="informationObj.name"
      :unifiedSocialCode="informationObj.unifiedSocialCode"
      :companyId="ids.companyId"
      v-else-if="activeName === '基础资料'"
    />
    <!-- 白名单企业 -->
    <WhiteListCompany v-else-if="activeName === '白名单企业'" />

    <!-- 产品列表 -->
    <ProductList
      :companyId="ids.companyId"
      v-else-if="activeName === '产品列表'"
    />

    <!-- 授信记录 -->
    <CreditRecord
      :companyId="ids.companyId"
      v-else-if="activeName === '授信记录'"
    />

    <!-- 放款记录 -->
    <LoanRecord
      :companyId="ids.companyId"
      v-else-if="activeName === '放款记录'"
    />
    <!-- 还款明细 -->
    <RepaymentDetails
      :companyId="ids.companyId"
      v-else-if="activeName === '还款明细'"
    />
    <!-- 资产信息 -->
    <ModuleAssetInformation
      :companyId="ids.companyId"
      v-else-if="activeName === '资产信息'"
    />

    <!-- 历史变更 -->
    <ModuleHistoricalChange
      :companyId="ids.companyId"
      v-else-if="activeName === '历史变更'"
    />
    <!-- 合同列表 -->
    <ContractsList
      :companyId="ids.companyId"
      v-else-if="activeName === '合同列表'"
    />
    <!-- 操作menu -->
    <BasicFooter :btn-options="btnOptions" @click="handleClickEvent" />

    <!-- 额度编辑弹窗 -->
    <el-dialog
      title="编辑额度"
      :visible.sync="dialogFormVisible"
      append-to-body="true"
      :close-on-click-modal="false"
      width="30%"
    >
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item
          label="申请额度:"
          :label-width="formLabelWidth"
          prop="money"
        >
          <el-input
            v-model.number="form.money"
            autocomplete="off"
            placeholder="请输入申请额度"
            :size="size"
            type="number"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="银行年利率:"
          :label-width="formLabelWidth"
          prop="annualInterestRate"
        >
          <el-input
            v-model.number="form.annualInterestRate"
            autocomplete="off"
            placeholder="请输入银行年利率"
            :size="size"
            type="number"
          >
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="生效日:"
          :label-width="formLabelWidth"
          prop="effectiveDate"
        >
          <el-date-picker
            v-model="form.effectiveDate"
            type="date"
            placeholder="选择日期"
            :size="size"
            style="width: 100%"
            :picker-options="pickerOptions"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd HH:mm:ss"
            :clearable="false"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="到期日:"
          :label-width="formLabelWidth"
          prop="dueDate"
        >
          <el-date-picker
            v-model="form.dueDate"
            type="date"
            placeholder="选择日期"
            :size="size"
            style="width: 100%"
            :picker-options="pickerOptions1"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd HH:mm:ss"
            :clearable="false"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="额度类型:"
          :label-width="formLabelWidth"
          prop="limitType"
        >
          <el-select
            v-model="form.limitType"
            placeholder="请选择额度类型"
            :size="size"
            style="width: 100%"
          >
            <el-option
              v-for="item in getDictionaryData"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')"
          >启 用</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  capitalDetail,
  cusCapitalQuotaDetail,
  cusCapitalQuotaAdditionDetail,
  cusCapitalQuotaAdditionSubmit,
} from '@/api/customer/capital'
import { getDictionary } from '@/api/goods/pcontrol/workflow/productConfirmation'
import BusinessDetail from './capitalPage/BusinessDetail.vue'
import ModuleBaseData from './capitalPage/ModuleBaseData.vue'
import WhiteListCompany from './capitalPage/WhiteListCompany.vue'
import BasicFooter from '@/components/basic-footer'
import CreditRecord from './capitalPage/CreditRecord.vue'
import LoanRecord from './capitalPage/LoanRecord.vue'
import ProductList from './capitalPage/ProductList.vue'
import RepaymentDetails from './capitalPage/RepaymentDetails.vue'
import ModuleAssetInformation from './capitalPage/ModuleAssetInformation.vue'
import ContractsList from './capitalPage/ContractsList.vue'
import { formatMoney } from '@/util/filter'
import ModuleHistoricalChange from './archives/components/ModuleHistoricalChange/index'
export default {
  components: {
    BasicFooter,
    BusinessDetail,
    ModuleBaseData,
    WhiteListCompany,
    CreditRecord,
    LoanRecord,
    RepaymentDetails,
    ModuleAssetInformation,
    ContractsList,
    ProductList,
    ModuleHistoricalChange,
  },
  data() {
    return {
      ids: {
        id: this.$route.query.id,
        companyId: this.$route.query.companyId,
      },
      informationObj: {},
      particularsArr: [],

      dialogFormVisible: false,
      form: {
        money: '',
        annualInterestRate: '',
        effectiveDate: '',
        dueDate: '',
        limitType: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < new Date().getTime() - 3600 * 1000 * 24
        },
      },
      pickerOptions1: {
        disabledDate: time => {
          return time.getTime() <= new Date(this.form.effectiveDate).getTime()
        },
      },
      rules: {
        money: [{ required: true, validator: this.checkNum, trigger: 'blur' }],
        annualInterestRate: [
          { required: true, validator: this.checkNum, trigger: 'blur' },
        ],
        effectiveDate: [
          { required: true, validator: this.checkDate, trigger: 'blur' },
        ],
        dueDate: [
          { required: true, validator: this.checkDate, trigger: 'blur' },
        ],
        limitType: [
          { required: true, validator: this.checkSelect, trigger: 'change' },
        ],
      },
      formLabelWidth: '100px',
      size: 'medium',

      getDictionaryData: [],
      cusCapitalQuotaAdditionDetailIds: '',

      labelList: [
        {
          label: '业务总览',
          value: 0,
        },
        {
          label: '基础资料',
          value: 1,
        },
        {
          label: '白名单企业',
          value: 2,
        },
        {
          label: '产品列表',
          value: 3,
        },
        {
          label: '授信记录',
          value: 4,
        },
        {
          label: '放款记录',
          value: 5,
        },
        {
          label: '还款明细',
          value: 6,
        },
        {
          label: '资产信息',
          value: 7,
        },
        {
          label: '历史变更',
          value: 8,
        },
        {
          label: '合同列表',
          value: 9,
        },
        {
          label: '账户信息',
          value: 10,
        },
      ], //
      activeName: '业务总览',
      btnOptions: [
        {
          btnName: '返回',
          funName: 'Revert',
        },
        {
          btnName: '编辑额度',
          funName: 'Edit',
          type: 'primary',
        },
      ],
    }
  },

  created() {
    this.getDictionary() // 获取字典数据
    // 资方详情
    this.getdetail()
  },
  methods: {
    // 提交修改后的额度
    async getdetail() {
      const res = await capitalDetail(this.ids.id)
      const resData = res.data
      if (res.data.code == 200) {
        this.informationObj = {
          ...resData.data,
          totalCredit: formatMoney(resData.data.totalCredit),
        }
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.cusCapitalQuotaAdditionSubmit()
        } else {
          this.$message.warning('请完善信息!')
          return false
        }
      })
    },
    // 字典数据
    getDictionary() {
      getDictionary('quota_type').then(res => {
        const resData = res.data
        if (resData.code == 200) {
          // 处理字典数据
          const resList = []
          for (const item of resData.data) {
            resList.push({
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            })
          }
          this.getDictionaryData = resList
          this.cusCapitalQuotaAdditionDetail() // 获取额度信息
        }
      })
    },
    // 获取额度调整信息
    cusCapitalQuotaAdditionDetail() {
      cusCapitalQuotaAdditionDetail(this.ids.companyId).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          const dat = resData.data
          this.form = {
            money: dat.creditTotal,
            annualInterestRate: dat.annualInterestRate,
            effectiveDate: dat.fromValidity,
            dueDate: dat.fxpirationDate,
            limitType: String(dat.type),
          }
          this.cusCapitalQuotaAdditionDetailIds = dat.id
        }
      })
    },
    // 保存调整后的额度信息
    async cusCapitalQuotaAdditionSubmit() {
      const form = this.form
      const data = {
        id: this.cusCapitalQuotaAdditionDetailIds,
        companyId: this.ids.companyId,
        creditTotal: form.money,
        annualInterestRate: form.annualInterestRate,
        fromValidity: form.effectiveDate,
        fxpirationDate: form.dueDate,
        type: form.limitType,
      }
      // .then(res => {
      //     const resData = res.data
      //     if (resData.code == 200) {
      //       this.$message.success('调整成功')
      //       this.getdetail()
      //       // this.dialogFormVisible = false
      //     }
      //   })
      const res = await cusCapitalQuotaAdditionSubmit(data)
      const resData = res.data
      if (resData.code == 200) {
        this.$message.success('调整成功')
        this.$refs.businessDetail.getDetail()
        this.dialogFormVisible = false
      }
      // .catch(() => {})
    },
    goToBack() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({
        path: '/customer/capital',
      })
    },
    checkNum: (rule, value, callback) => {
      if (!value) {
        if (value === '') {
          return callback(new Error('不能为空'))
        }
      }
      if (value <= 0) {
        callback(new Error('必须大于零'))
      } else {
        callback()
      }
    },
    checkDate: (rule, value, callback) => {
      if (!value) {
        return callback(new Error('日期不能为空'))
      } else {
        callback()
      }
    },
    checkSelect: (rule, value, callback) => {
      if (!value) {
        return callback(new Error('类型不能为空'))
      } else {
        callback()
      }
    },

    // 底部按钮操作
    handleClickEvent(name) {
      switch (name) {
        case 'Revert':
          this.goToBack()
          break
        case 'Edit':
          this.dialogFormVisible = true
          break
      }
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep.avue-view {
  padding: 0 20px 120px !important;
}
// 去除input type为number类型时的上下选择箭头
::v-deep {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
}
.capital-detail {
  position: relative;

  span {
    display: inline-block;
  }

  .capital-information {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;

    .capital-box-information {
      height: 100%;

      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .capital-img {
        width: 56px;
        height: 56px;

        & > img {
          width: 100%;
          object-fit: cover;
        }
      }

      .capital-text-information {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 12px;

        .capital-top-text-information {
          .capital-bank-top-text {
            min-width: 96px;
            height: 24px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-size: 16px;
            text-align: left;
            margin-right: 20px;
          }
          .capital-capitalid-top-text {
            width: 65px;
            height: 24px;
            line-height: 24px;
            border-radius: 26px;
            background-color: rgba(85, 85, 85, 100);
            color: rgba(255, 255, 255, 100);
            font-size: 12px;
            text-align: center;
            margin-right: 12px;
          }
          .capital-extension-top-text {
            width: 62px;
            height: 24px;
            line-height: 24px;
            border-radius: 48px;
            background-color: #f0f2ff;
            color: rgba(78, 155, 252, 100);
            font-size: 12px;
            text-align: center;
          }
        }

        .capital-bottom-text-information {
          margin-top: 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .capital-bottom-left {
            display: flex;
            align-items: center;
            .capital-social-bottom-text {
              height: 22px;
              line-height: 22px;
              color: rgba(100, 100, 100, 100);
              font-size: 14px;
              text-align: left;
              margin-right: 60px;
            }
            .capital-contacts-social-bottom-text {
              height: 22px;
              line-height: 22px;
              color: rgba(100, 100, 100, 100);
              font-size: 14px;
              text-align: left;
              margin-right: 60px;
            }
            .capital-contactsway-social-bottom-text {
              height: 22px;
              line-height: 22px;
              color: rgba(100, 100, 100, 100);
              font-size: 14px;
              text-align: left;
            }
          }
          .capital-bottom-right {
            & span {
              color: #ff2929;
              font-size: 14px;
              font-weight: 500;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
    }
    .capital-label-group {
      margin-top: 20px;
      .tabBar-box {
        margin-top: 20px;
        display: flex;
        border: 1px solid rgba(105, 124, 255, 100);
        border-radius: 6px;
        overflow: hidden;

        .tabBar-for-box {
          height: 40px;
          width: 100%;
          background-color: rgba(255, 255, 255, 100);
          border-right: 1px solid rgba(105, 124, 255, 100);
          text-align: center;
          line-height: 40px;
          cursor: pointer;
          transition: background-color 0.3s;
          font-size: 14px;
          color: #449bfc;

          &:first-child {
            border-radius: 6px 0 0 6px;
          }

          &:last-child {
            border-radius: 0 6px 6px 0;
            border-right: none;
          }

          &:hover {
            background-color: #697cff2b;
          }
        }
        .active-box {
          background-color: #449bfc;
          color: #fff;
          &:hover {
            background-color: #449bfc;
          }
        }
      }
    }
  }

  .manipulation-menu {
    width: calc(100% - 270px);
    height: 68px;
    display: flex;
    justify-content: center;
    border-radius: 4px 4px 0px 0px;
    position: fixed;
    bottom: 35px;
    left: 0;
    padding: 0 20px;
    box-sizing: border-box;

    .manipulation-menu-box {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;

      & > span:first-child {
        width: 80px;
        height: 30px;
        line-height: 30px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 100);
        color: rgba(0, 7, 42, 100);
        font-size: 14px;
        text-align: center;
        border: 1px solid rgba(187, 187, 187, 100);
        margin-right: 18px;
        cursor: pointer;
      }

      & > span:last-child {
        width: 102px;
        height: 30px;
        line-height: 30px;
        border-radius: 4px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}
</style>
