<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.contracttemplateconfig_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          @click="syncSsq(scope.row)"
          size="small"
        >同步
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  listFieldConfig,
  listSsqFields,
  remove,
  syncSSqDetails,
  update
} from "@/api/contract/contracttemplateconfig";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      ssqFieldNameData: [],
      fieldNameData: [],
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "合同模板",
            prop: "templateId",
            type: "tree",
            search: true,
            dicUrl: 'api/blade-contract/web-back/contractTemplate/listAll',
            editDisabled: true,
            props: {
              label: 'templateName',
              value: 'templateId',
            },
            dataType: "string",
            rules: [{
              required: true,
              message: "请选择模板",
              trigger: "blur"
            }]
          },
          {
            label: "数据源Bean",
            search: true,
            type: 'select',
            multiple: true,
            dataType:'string',
            prop: "beanClazzPath",
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=contract_config_bean_clazz',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [{
              required: true,
              message: "请选择数据源Bean",
              trigger: "blur"
            }]
          },
          {
            label: '签署节点',
            prop: 'signNode',
            search:true,
            type: 'tree',
            multiple: true,
            placeholder: '签署节点',
            clearable: false,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_sign_node',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'string',
            rules: [
              {
                required: true,
                message: '请选择签署节点',
                trigger: 'blur',
              },
            ],
          },
          {
            label: "产品类型",
            prop: "goodsType",
            type: 'tree',
            dataType: "number",
            search: true,
            multiple: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [{
              required: true,
              message: "请输入bean的全限定类名",
              trigger: "blur"
            }]
          },
          {
            label: '字段绑定',
            prop: 'templateFieldsConfigs',
            hide: true,
            addDisplay: false,
            type: 'dynamic',
            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              column: [
                {
                  label: "是否为前端传输字段",
                  prop: "izFrontField",
                  type: "select",
                  dicData: [
                    {
                      label: "否",
                      value: 0
                    },
                    {
                      label: "是",
                      value: 1
                    }
                  ],
                  rules: [{
                    required: true,
                    message: "请选择是否为前端传输字段",
                    trigger: "blur"
                  }]
                },
                {
                  label: "上上签业务字段",
                  prop: "ssqFieldName",
                  type: 'tree',
                  dicData: [],
                  props: {
                    label: 'name',
                    value: 'name',
                  },
                  dataType: 'string',
                  rules: [{
                    required: true,
                    message: "请选择bean的全限定类名",
                    trigger: "blur"
                  }]
                },
                {
                  label: '数据源',
                  type: 'select',
                  prop: "clazzName",
                  dataType: 'string',
                  dicUrl:
                    '/api/blade-system/dict-biz/dictionary?code=contract_config_bean_clazz',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  cascaderItem: ['configId'],
                },
                {
                  label: "实体类字段",
                  prop: "configId",
                  dicUrl:
                    '/api/othersapi/other-api-entity-data-source/datasource?clazzName={{key}}',
                  type: 'tree',
                  dataType: 'string',
                  props: {
                    label: 'desc',
                    value: 'id',
                    desc: 'fieldName',
                  },
                  dicFormatter: ({ data: resData }) => {
                    const arrD = []
                    for (const key in resData) {
                      const listD = resData[key]
                      if (listD && listD.length) {
                        for (const item of listD) {
                          arrD.push(item)
                        }
                        break
                      }
                    }
                    return arrD
                  },
                },
                {
                  label: "属性字段",
                  prop: "fieldName",
                },
              ]
            }
          }
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.contracttemplateconfig_add, false),
        viewBtn: this.vaildData(this.permission.contracttemplateconfig_view, false),
        delBtn: this.vaildData(this.permission.contracttemplateconfig_delete, false),
        editBtn: this.vaildData(this.permission.contracttemplateconfig_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    syncSsq(row) {
      syncSSqDetails(row.id).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
      })
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
          let id = this.form.id;
          const templateFieldsConfigs = this.findObject(
            this.option.column,
            'templateFieldsConfigs'
          )
          listSsqFields(id).then(res => {
            templateFieldsConfigs.children.column[1].dicData = res.data.data
            if (!this.form.templateFieldsConfigs.length && res.data.data.length) {
              for (const item of res.data.data) {
                this.form.templateFieldsConfigs.push({
                  ssqFieldName: item.name,
                  izFrontField:0
                })
              }
            }
          })
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
