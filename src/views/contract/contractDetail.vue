<template>
  <div v-loading="loading">
    <div class="detail-box">
      <div class="platform-related">
        <div class="title">平台相关合同</div>
        <div class="dividing-line"></div>
        <div class="contract-information">
          <div class="left">
            <div
              class="infor"
              v-for="(item, index) in contractInforList"
              :key="index"
            >
              <p>{{ item.key }}</p>
              <span>{{ item.val }}</span>
            </div>
          </div>
          <div class="state">
            <div class="contract-state" v-if="contractState == 1">
              <SvgIcon
                icon-class="icon-line-dengdai"
                style="
                  fill: #ffffff;
                  width: 20px;
                  height: 20px;
                  margin-right: 2px;
                  background-color: #4e9bfc;
                  border-radius: 50%;
                "
              ></SvgIcon>
              <span>待签署</span>
            </div>
            <div class="contract-state" v-else-if="contractState == 5">
              <SvgIcon
                icon-class="icon-line-chenggong"
                style="
                  fill: #ffffff;
                  width: 20px;
                  height: 20px;
                  margin-right: 2px;
                  background-color: #04be02;
                  border-radius: 50%;
                "
              ></SvgIcon>
              <span>已完成</span>
            </div>
            <div class="contract-state" v-else-if="contractState == 3">
              <SvgIcon
                icon-class="icon-line-chenggong"
                style="
                  fill: #ffffff;
                  width: 20px;
                  height: 20px;
                  margin-right: 2px;
                  background-color: #04be02;
                  border-radius: 50%;
                "
              ></SvgIcon>
              <span>已签署</span>
            </div>
            <div class="contract-state" v-else-if="contractState == 2">
              <SvgIcon
                icon-class="icon-line-shibai"
                style="
                  fill: #ffffff;
                  width: 20px;
                  height: 20px;
                  margin-right: 2px;
                  background-color: #787878;
                  border-radius: 50%;
                "
              ></SvgIcon>
              <span>已撤销</span>
            </div>
            <div class="count-down">
              <span v-if="contractState == 6"
                >合同待签署，合同在{{ time }}后将失效</span
              >
              <span v-else-if="contractState == 4">合同已过期</span>
            </div>
            <div class="btn-box">
              <!-- TODO: 待后端了解对接逻辑在接上 -->
              <div
                style="display: none"
                class="revoke"
                v-if="contractState == 1 || contractState == 6"
                @click="revoke"
              >
                撤销
              </div>
              <div class="download" @click="download">下载合同</div>
              <div class="download" @click="lookContract">查看合同</div>
              <div
                class="send-out"
                @click="send_out"
                v-if="
                  contractState == 'REJECT' ||
                  contractState == 'REVOKE_CANCEL' ||
                  contractState == 'OVERDUE'
                "
              >
                重新发送
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="details-content1">
        <div class="details-content1-tit">
          <div class="title-dot"></div>
          <div class="title">签约方信息</div>
        </div>
        <div class="contract-party">
          <avue-crud :data="data" :option="option">
            <template slot-scope="{ type, size, row }" slot="menu">
              <el-button
                v-if="
                  (row.status === 'BEING_CARRIED' ||
                    row.status === 'COMPLETE_SET_APPROVAL_FLOW' ||
                    row.status === 'WAIT_FOR_SET_APPROVAL_FLOW' ||
                    row.status === 'WAIT_FOR_CLAIM' ||
                    row.status === 'SIGN_PROCESS' ||
                    row.status === 'APPROVAL_WAIT') &&
                  !(
                    contractState === 'REJECT' ||
                    contractState === 'COMPLETE' ||
                    contractState === 'REVOKE_CANCEL'
                  )
                "
                :size="size"
                :type="type"
                @click="remind(row)"
                >提醒</el-button
              >
            </template>
            <template slot-scope="{ row }" slot="userType">
              <el-tag v-if="row.userType === 2">企业</el-tag>
              <el-tag v-else>个人</el-tag>
            </template>

            <template slot-scope="{ row }" slot="status">
              <el-tag v-if="row.status == 5" type="success">已完成</el-tag>
              <el-tag v-if="row.status == 3" type="success">已签署</el-tag>
              <el-tag v-if="row.status == 1">未开始签署</el-tag>
              <el-tag v-if="row.status == 6">开始签署</el-tag>
              <el-tag v-if="row.status == 4" type="info">合同以拒签</el-tag>
              <el-tag v-if="row.status == 2" type="info">合同以撤销</el-tag>
            </template>
            <template slot="hasAuthenticated">
              需要实名认证
              <!-- {{ row.hasAuthenticated ? '需要实名认证' : '不需要实名认证' }} -->
            </template>
            <template slot-scope="{ row }" slot="verifyType">
              <div v-if="['1'].includes(row.verifyType)">短信校验</div>
            </template>
            <!-- <template slot-scope="{ row }" slot="signRequirement">
              <template v-if="row.signRequirement">
                <span v-if="row.signRequirement.faceFirst">优先刷脸 </span>
                <span v-if="row.signRequirement.forceHandWrite">必须手写 </span>
                <span v-if="row.signRequirement.handWritingRecognition"
                  >笔迹识别
                </span>
                <span v-if="row.signRequirement.handWriteNotAllowed"
                  >不允许手写
                </span>
                <span v-if="row.signRequirement.faceVerify">刷脸签署 </span>
                <span v-if="row.signRequirement.requireEnterIdentityAssurance"
                  >签约主体必须实名
                </span>
                <span v-if="row.signRequirement.requireIdentityAssurance"
                  >企业经办人必须实名
                </span>
              </template>
            </template> -->
          </avue-crud>
        </div>
      </div>
      <div class="details-content2" style="display: none">
        <div class="details-content2-tit">
          <div class="title-dot"></div>
          <div class="title">子合同信息</div>
        </div>
        <div class="sub-contract">
          <div
            class="contract-box"
            v-for="(item, index) in subConractList"
            :key="index"
          >
            <div class="contract-title">
              <div class="contract-name" @click="looksudview(index)">
                {{ item.docTitle }}
              </div>
              <div
                class="download-contract"
                @click="downloadSubContract(index)"
              >
                <SvgIcon
                  icon-class="icon-line-xiazai"
                  style="
                    fill: #7d7d7d;
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                  "
                ></SvgIcon>
              </div>
            </div>
            <div
              class="contract-infor"
              v-for="(item, index) in item.participants"
              :key="item.number"
            >
              <div class="left">
                <div class="recipient">{{ item.participantName }}</div>
                <div class="sender">
                  <span>{{ index ? '合同签署人:' : '合同发送人:' }}</span>
                  <span class="name">{{ item.name }}</span>
                  <span class="number">{{ item.number }}</span>
                </div>
              </div>
              <div class="right">
                {{ item.userType == 'ENTERPRISE' ? '企业' : '个人' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <FilePreview :url="contracturl" /> -->

    <!-- <Pdf :src="contracturl" /> -->
    <FilePreview :url="subContracturl" />
  </div>
</template>

<script>
import {
  getdata,
  getcontract,
  // downloadContract,
  downloadSubContract,
  revokecontract,
  resendContract,
} from '../../api/contracttemplate/contracttemplate'

import { getCountDownHtml } from '@/util/util'

import FilePreview from '@/components/file-preview/index.vue'
export default {
  components: { FilePreview },
  data() {
    return {
      loading: true,
      contractState: 0,
      contractId: '',
      time: '',
      inTervalTime: null,
      contractInforList: [
        { key: '合同编号:', val: '' },
        { key: '创建时间:', val: '' },
        { key: '截止签署时间:', val: '' },
        { key: '签约结束时间:', val: '' },
      ],
      subConractList: [],
      contracturl: '',
      subContracturl: '',
      data: [],
      mingtian: 0,
      contractPDFurl: '',
      contractTitle: '',
      option: {
        align: 'center',
        menuAlign: 'center',
        addBtn: false,
        border: true,
        delBtn: false,
        columnBtn: false,
        editBtn: false,
        refreshBtn: false,
        labelPosition: 'left',
        indexWidth: 88,
        index: true,
        menu: false,
        indexLabel: '签约顺序',
        column: [
          {
            label: '接受方',
            labelWidth: 173,
            prop: 'signerCompanyName',
          },
          {
            label: '签署人',
            prop: 'signerName',
          },
          {
            label: '接收方类型',
            prop: 'userType',
          },
          {
            label: '签署状态',
            prop: 'status',
          },
          {
            label: '签署时间',
            prop: 'updateTime',
          },
          {
            label: '签约要求',
            prop: 'verifyType',
          },
          {
            label: '详情',
            prop: 'hasAuthenticated',
          },
        ],
      },
    }
  },
  methods: {
    getcontractinfor(contractId) {
      this.loading = true
      getdata(contractId).then(({ data }) => {
        const { data: resData } = data
        // 基本状态信息
        this.contractInforList[0].val = resData.contract.contractId
        this.contractInforList[1].val = resData.contract.sendTime
        this.contractInforList[2].val = resData.contract.signDeadLine
        this.contractInforList[3].val = resData.contract.finishTime || '--'
        this.contractState = resData.contract.status
        this.contractPDFurl = resData.contract.fileUrl
        this.contractTitle = resData.contract.contractTitle
        // 签约方信息
        for (const item of resData.contractOperators) {
          const templateData = resData.contract.contractTemplate
          if (templateData && JSON.stringify(templateData) !== '{}') {
            item.verifyType = templateData.verifyType
          }
        }
        this.data = resData.contractOperators
        // 倒计时
        this.inTervalTime = setInterval(() => {
          const nowTime = new Date().valueOf() // 当前时间
          const dayTime = new Date(resData.contract.signDeadLine).valueOf() // 截止时间
          if (dayTime <= nowTime) {
            clearInterval(this.inTervalTime)
          }
          this.time = getCountDownHtml(dayTime - nowTime) // 倒计时
        }, 1000)
        // this.data = data.sigerInfo
        // for (let item of this.data) {
        //   if (
        //     this.contractState == 'COMPLETE' ||
        //     this.contractState == 'REVOKE_CANCEL' ||
        //     this.contractState == 'APPROVAL_DENY' ||
        //     this.contractState == 'OVERDUE' ||
        //     this.contractState == 'REJECT'
        //   ) {
        //     item.finishTime = data.platFormInfo.finishTime
        //   }
        // }
        // let datalist = data.sigerInfo
        // this.subConractList = data.subContract
        this.loading = false
      })
    },
    // 提醒
    remind() {
      getcontract(this.contractInforList[0].val).then(res => {
        if (res.data.code == 200) {
          this.$message({
            type: 'success',
            message: res.data.msg,
          })
        }
      })
    },
    // 撤销
    revoke() {
      revokecontract(this.contractInforList[0].val).then(res => {
        if (res.data.code == 200) {
          this.$message({
            type: 'success',
            message: res.data.msg,
          })
          this.getcontractinfor(this.contractInforList[0].val)
        }
      })
    },
    // 下载
    download() {
      const t_his = this
      // downloadContract(this.contractInforList[0].val).then(res => {
      //   this.contracturl = res.data.data
      let x = new XMLHttpRequest()
      x.open('GET', this.contractPDFurl, true)
      x.responseType = 'blob'
      x.onload = function () {
        var tempLink = document.createElement('a')
        tempLink.style.display = 'none'
        tempLink.href = window.URL.createObjectURL(x.response)
        tempLink.download = `${t_his.contractTitle}.pdf`
        document.body.appendChild(tempLink)
        tempLink.click()
        document.body.removeChild(tempLink)
        // window.URL.revokeObjectURL(this.contracturl)
        // tempLink.setAttribute('download', `合同`)
        // if (typeof tempLink.download === 'undefined') {
        //   tempLink.setAttribute('target', '_blank')
        // }
      }
      x.send()
      // })
    },
    // 查看
    lookContract() {
      this.subContracturl =
        this.contractPDFurl + '?time=' + new Date().getMilliseconds()
    },
    // 重新发送
    send_out() {
      resendContract(this.contractInforList[0].val).then(res => {
        if (res.data.code == 200) {
          this.$message({
            type: 'success',
            message: '重新发送成功',
          })
        }
      })
    },
    // 子合同预览
    looksudview(index) {
      downloadSubContract(this.subConractList[index].subContractId).then(
        res => {
          this.subContracturl =
            res.data.data + '?time=' + new Date().getMilliseconds()
        }
      )
    },
    // 子合同下载
    downloadSubContract(index) {
      downloadSubContract(this.subConractList[index].subContractId).then(
        res => {
          let downloadUrl = res.data.data
          var tempLink = document.createElement('a')
          tempLink.style.display = 'none'
          tempLink.href = downloadUrl
          tempLink.setAttribute(
            'download',
            `与${this.subConractList[index].docTitle}合同`
          )
          if (typeof tempLink.download === 'undefined') {
            tempLink.setAttribute('target', '_blank')
          }
          document.body.appendChild(tempLink)
          tempLink.click()
          document.body.removeChild(tempLink)
          window.URL.revokeObjectURL(downloadUrl)
        }
      )
    },
  },
  mounted() {
    // console.log(this.$route)
    this.contractId = JSON.parse(
      Buffer.from(this.$route.params.id, 'base64').toString()
    )
    this.getcontractinfor(this.contractId)
    // let contractId = '2962893570442264576'
    // getdata(contractId).then(res => {
    //   console.log(res.data.data)
    //   let data = res.data.data
    //   this.contractInforList[0].val = data.platFormInfo.contractId
    //   this.contractInforList[1].val = data.platFormInfo.sendTime
    //   this.contractInforList[2].val = data.platFormInfo.signDeadline
    //   this.contractInforList[3].val = data.platFormInfo.finishTime
    //   this.mingtian = data.platFormInfo.effectTime
    //   this.contractState = data.platFormInfo.status
    //   this.data = data.sigerInfo
    //   let datalist = data.sigerInfo
    //   this.subConractList = data.subContract
    //   console.log(datalist, res.data.data.sigerInfo)
    //   //   for (let i = 0; i < data.sigerInfor.length; i++) {
    //   //     this.data[i].$index = datalist[i].routeOrder
    //   //   }
    //   console.log(this.contractInforList, this.data)
    //   let timer = ''
    //   timer = setInterval(() => {
    //     if (this.mingtian) {
    //       this.mingtian -= 1000
    //     } else {
    //       //   this.endTime = new Date()
    //       let data = new Date()
    //       let nian = data.getFullYear()
    //       let yue = data.getMonth() + 1
    //       let tian = data.getDate()
    //       let shi = data.getHours()
    //       let fen = data.getMinutes()
    //       let miao = data.getSeconds()
    //       this.contractInforList[3].val = `${nian}-${yue}-${tian}  ${shi}:${fen}:${miao}`
    //       clearInterval(timer)
    //     }
    //     this.time = `${Math.floor(
    //       (this.mingtian / 24 / 60 / 60 / 1000) % 366
    //     )}天
    //     ${Math.floor((this.mingtian / 60 / 60 / 1000) % 24)}时${Math.floor(
    //       (this.mingtian / 60 / 1000) % 60
    //     )}分${Math.floor((this.mingtian / 1000) % 60)}秒`
    //   }, 1000)
    // })
  },
}
</script>
<style lang="scss" scoped>
.platform-related {
  height: 231px;
  width: 100%;
  border-radius: 8px;
  background-color: #ffffff;
}
.dividing-line {
  width: 100%;
  height: 1px;
  background-color: #e9ebef;
}
.platform-related .title {
  height: 62px;
  padding: 0 0 0 20px;
  box-sizing: border-box;
  line-height: 62px;
  font-size: 16px;
  font-weight: 600;
  color: #4d0000;
}
.contract-information {
  height: 168px;
  display: flex;
  align-items: center;
}
.contract-information .left {
  width: 30%;
  padding-left: 20px;
  height: 100%;
  border-right: 1px solid #e9ebef;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-evenly;
}
.contract-information .left .infor {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #444444;
  font-size: 14px;
  /* letter-spacing: 1px; */
}
.infor p {
  width: 98px;
  height: 20px;
  text-align: left;
  line-height: 20px;
  font-size: 14px;
  letter-spacing: 0px;
  color: #9b9b9b;
  margin-right: 4px;
}
.contract-information .state {
  width: 70%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.contract-state {
  display: flex;
  align-content: center;
  justify-content: center;
  line-height: 20px;
  font-weight: 600;
}
.count-down {
  margin-top: 12px;
  font-size: 12px;
  color: #84868d;
}
.btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
}
.btn-box div {
  border: 1px solid #697cff;
  border-radius: 4px;
  font-size: 14px;
  color: #697cff;
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
}
.btn-box .revoke {
  width: 60px;
  margin-right: 12px;
}
.btn-box .download {
  width: 81px;
  margin-right: 12px;
}
.btn-box .send-out {
  width: 81px;
}
.details-content1,
.details-content2 {
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 16px;
  padding: 20px;
  box-sizing: border-box;
}
.details-content2 {
  margin-bottom: 50px;
}
.details-content1-tit,
.details-content2-tit {
  display: flex;
  align-items: center;
  height: 24px;
}
.title-dot {
  width: 8px;
  height: 16px;
  background-color: #1277ff;
  border-radius: 4px;
  margin-right: 4px;
}
.details-content1-tit .title,
.details-content2-tit .title {
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  color: #101010;
  font-weight: 600;
  margin: 0;
}
.sub-contract {
  margin-top: 14px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
}
.contract-box {
  width: calc((100% - 80px) / 3);
  border: 1px solid#E9EBEF;
  border-radius: 8px;
  margin: 0 30px 20px 0;
}
.contract-box:nth-child(3n) {
  margin-right: 0;
}
.contract-title {
  height: 48px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  width: 100%;
  display: flex;
  border-bottom: 1px solid #f7f7f7;
  background-color: #e9ebef;
  padding: 14px 20px;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  margin-top: -1px;
}
.contract-name {
  color: #697cff;
  cursor: pointer;
  font-size: 14px;
}
.contract-infor {
  display: flex;
  align-items: center;
  height: 66px;
  justify-content: space-between;
  border-bottom: 1px solid #f7f7f7;
  padding: 0 20px;
  box-sizing: border-box;
}
.contract-infor .left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-around;
}
.contract-infor .left .recipient {
  font-size: 14px;
  color: #444444;
  margin-bottom: 5px;
}
.contract-infor .left .sender {
  display: flex;
  align-items: center;
  justify-items: flex-start;
  font-size: 12px;
  color: #9b9b9b;
}
.contract-infor .left .sender .name {
  margin: 0 4px;
}
.contract-infor .left .sender .number {
  height: 16px;
  line-height: 18px;
}
.contract-infor .right {
  width: 46px;
  height: 24px;
  font-size: 12px;
  color: #697cff;
  border-radius: 12px;
  line-height: 24px;
  text-align: center;
  background-color: rgba(105, 124, 255, 0.1);
}
.contract-infor:last-child {
  border: 0;
}
::v-deep .avue-crud__menu {
  min-height: 12px;
}
</style>
