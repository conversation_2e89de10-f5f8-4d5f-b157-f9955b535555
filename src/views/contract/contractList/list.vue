<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="success"
          size="small"
          icon="el-icon-plus"
          plain
          v-if="permission.contractlist_add"
          @click="handleAdd"
          >新 增
        </el-button>
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.contractlist_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="status">
        <el-tag>
          {{
            row.status == 1
              ? '待签署'
              : row.status == 2
              ? '已取消'
              : row.status == 3
              ? '已签署'
              : row.status == 4
              ? '已失效'
              : row.status == 5
              ? '已完成'
              : '未知'
          }}
        </el-tag>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="toDetail(scope.row, index)"
          size="small"
          >详情
        </el-button>
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="remind(scope.row, index)"
          v-if="scope.row.status == 1 || scope.row.status == 3"
          size="small"
          >提醒
        </el-button>
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="downLoadContract(scope.row)"
          size="small"
          >下载
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="revoke(scope.row, index)"
          v-if="scope.row.status == 1"
          size="small"
          >撤销
        </el-button>
        <el-button
          type="text"
          @click="send_out(scope.row)"
          v-if="[2, 4,3].includes(scope.row.status)"
          size="small"
          >延期7天
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  contractRemind,
  contractRevoke,
} from '@/api/contractlist/contractlist'
import { resendContract } from '@/api/contracttemplate/contracttemplate'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      documents: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        indexLabel: '序号',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '合同编号',
            prop: 'contractId',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入模板id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '合同标题',
            prop: 'contractTitle',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入合同标题',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '产品名称',
            prop: 'goodName',
            search: true,
            type: 'tree',
            dicUrl: '/api/blade_product/web-back/product/onShelfGoodsList',
            props: {
              label: 'goodsName',
              value: 'id',
            },
            errorslot: true,
            rules: [
              {
                required: true,
                message: '请选择产品',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '产品类型',
            prop: 'goodType',
            type: 'select',
            display: false,
            dataType:'string',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=goods_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '流程类型',
            prop: 'typeName',
            display: false,
            align: 'center',
          },
          {
            label: '流程节点',
            prop: 'signNode',
            display: false,
            align: 'center',
            dataType: 'String',
            search:true,
            require: true,
            formslot: true,
            type: 'select',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_sign_node',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '创建时间',
            prop: 'createTime',
            span: 24,
          },

          {
            label: '合同状态',
            prop: 'status',
            display: false,
            align: 'center',
            search: true,
            dataType: 'number',
            require: true,
            formslot: true,
            type: 'select',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=contract_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.contractlist_add, false),
        viewBtn: this.vaildData(this.permission.contractlist_view, false),
        delBtn: this.vaildData(this.permission.contractlist_delete, false),
        editBtn: this.vaildData(this.permission.contractlist_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {},
  methods: {
    toDetail(row) {
      this.$router.push(
        '/contract/contractDetail/' +
          Buffer.from(JSON.stringify(row.contractId)).toString('base64')
      )
    },
    // 下载
    downLoadContract(row) {
      let x = new XMLHttpRequest()
      x.open('GET', row.fileUrl, true)
      x.responseType = 'blob'
      x.onload = function () {
        var tempLink = document.createElement('a')
        tempLink.style.display = 'none'
        tempLink.href = window.URL.createObjectURL(x.response)
        tempLink.download = `${row.contractTitle}.pdf`
        document.body.appendChild(tempLink)
        tempLink.click()
        document.body.removeChild(tempLink)
      }
      x.send()
    },
    remind(row) {
      let obj = {
        contractId: row.contractId,
      }
      contractRemind(obj).then(
        res => {
          this.$message({
            type: 'success',
            message: '提醒成功!',
          })
          this.onLoad(this.page)
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    revoke(row) {
      let obj = {
        contractId: row.contractId,
      }
      contractRevoke(obj).then(
        res => {
          this.$message({
            type: 'success',
            message: '撤销成功!',
          })
          this.onLoad(this.page)
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    send_out(data) {
      if (data.contractId) {
        resendContract(data.contractId).then(res => {
          if (res.data.code == 200) {
            this.$message({
              type: 'success',
              message: '成功延期7天',
            })
            this.onLoad(this.page)
          }
        })
      }
    },
    handleAdd() {
      skipToTemplate().then(res => {
        window.open(res.data.data.data.value)
      })
    },
    updateTemplateStatus(templateId, status) {
      if (status == 0) {
        var msg = '确定将选择数据禁用?'
      } else {
        msg = '确定将选择数据启用?'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids = templateId
          return updateStatus(ids, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    skiptobestsign() {},
    cedit(row) {
      this.$router.push(`/contract/detail?templateId=${row.templateId}`)
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      skipToTemplate().then(res => {
        window.open(res.data.data.data.value)
      })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        this.data = res.data.records
        this.page.total = res.data.total
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
