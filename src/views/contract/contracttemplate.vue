<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="success"
          size="small"
          icon="el-icon-plus"
          plain
          v-if="permission.contracttemplate_add"
          @click="handleAdd(row,index)"
        >新 增
        </el-button>
        <el-button
          type="success"
          size="small"
          icon="el-icon-plus"
          plain
          v-if="permission.contracttemplate_add"
          @click="createTemplate"
        >远程添加模板
        </el-button>

        <el-button
          type="primary"
          size="small"
          icon="el-icon-primary"
          plain
          :loading="this.isLoad"
          @click="syncTemplate()"
        >模板同步
        </el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-view"
          @click="toDetail(scope.row)"
          size="small"
        >预览
        </el-button>
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row)"
          v-if="scope.row.status == 0"
          size="small"
        >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-success"
          size="small"
          v-if="scope.row.status == 0"
          @click.stop="updateTemplateStatus(scope.row.id, 1)"
        >
          启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-warning"
          size="small"
          v-if="scope.row.status == 1"
          @click.stop="updateTemplateStatus(scope.row.id, 0)"
        >
          禁用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row)"
          v-if="scope.row.status == 0"
          size="small"
        >删除
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="status">
        <el-tag
          :style="{
            color: row.status == 1 ? '#67c23a' : '#A6AEBC',
            border: row.status == 1 ? '1px solid #67c23a' : '1px solid #C9CED6',
            background: row.status == 1 ? '#EAFCF7' : '#fff',
          }"
        >{{ row.status == 1 ? '已启用' : '已禁用' }}
        </el-tag>
      </template>
    </avue-crud>

    <FilePreview :url="contractPDFurl"/>
  </basic-container>
</template>

<script>
import {
  add,
  getcontractTemplate,
  getData,
  getList,
  listAllNotContainSelf,
  remove,
  skipToTemplate,
  syncTemplate,
  update,
  updateStatus,
} from '@/api/contracttemplate/contracttemplate'
import {mapGetters} from 'vuex'
import FilePreview from '@/components/file-preview'

export default {
  components: {FilePreview},
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      res1: '',
      res: '',
      isLoad: false,
      documents: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      contractPDFurl: '',
      selectionList: [],
      option: {
        indexLabel: '序号',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        globalSize: '',
        globalPage: '',
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        viewBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '合同模板文件',
            prop: 'templateWordUrl',
            hide: true,
            type: 'upload',
            dataType: "string",
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            accept: '.docx',
            row: true,
            span: 24,
            display: false,
            fileSize: 15 * 1024,
            width: 200,
            tip: '只能上传docx文件，且不超过15M',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            rules: [
              {
                required: true,
                message: '请上传合同模板文件',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '模板id',
            prop: 'templateId',
            display: false,
            serach: true,
            type: 'select',
          },
          {
            label: '模板标题',
            prop: 'templateName',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入模板标题',
                trigger: 'blur',
              },
            ],
          },
          // {
          //   label: '过期时间(天)',
          //   prop: 'expireDay',
          //   hide: true,
          //   type: 'number',
          //   rules: [
          //     {
          //       required: true,
          //       message: '请输入过期时间(天)',
          //       trigger: 'blur',
          //     },
          //   ],
          // },
          {
            label: '模板供应商',
            prop: 'apiSupplier',
            type: 'select',
            dicData: [
              {
                label: '上上签',
                value: 'bestSign',
              },
              {
                label: '精锐纵横',
                value: '',
              }
            ],
            display: false,
          },
          {
            label: '合同生成方式 ',
            prop: 'contractGenType',
            display: false,
            type: 'select',
            dicData: [
              {
                label: '合同模板 ',
                value: 1,
              },
              {
                label: '自定义合同模板',
                value: 2,
              }
            ],
          },
          {
            label: '强制阅读时间（秒）',
            prop: 'forceReadingSecond',
            hide: true,
            type: 'number'
          },
          {
            label: '是否需要手写签名',
            hide: true,
            prop: 'needCustomizeWrite',
            type: 'select',
            dicData: [
              {
                label: '不需要',
                value: 0,
              },
              {
                label: '需要',
                value: 1,
              }
            ],
            dataType: 'number',
          },
          {
            label: '签署校验类型',
            hide: true,
            prop: 'verifyType',
            type: 'select',
            multiple: true,
            dataType: 'number',
            dicData: [
              {
                label: '短信',
                value: 1
              },
            ],
          },
          {
            label: '签署节点',
            prop: 'signNode',
            type: 'tree',
            multiple: true,
            placeholder: '签署节点',
            clearable: false,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_sign_node',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            dataType: 'string',
            rules: [
              {
                required: true,
                message: '请选择签署节点',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '关联子合同',
            prop: 'subContractNo',
            hide:true,
            type: 'tree',
            multiple: true,
            placeholder: '签署节点',
            clearable: false,
            dicData: [],
            props: {
              label: 'templateName',
              value: 'templateName',
            },
            dataType: 'string',
          },
          {
            label: '操作人',
            prop: 'operateName',
            display: false,
            search: true,
          },
          {
            label: '签署人数',
            type: 'number',
            prop: 'signerNum',
          },
          {
            label: '需要平台默认签署',
            hide: true,
            prop: 'needPlatSign',
            type: 'select',
            dicData: [
              {
                label: '不需要',
                value: 0,
              },
              {
                label: '需要',
                value: 1,
              }
            ],
            dataType: 'number',
          },
          {
            label: '操作时间',
            display: false,
            prop: 'operateTime',
          },
          {
            label: '状态',
            prop: 'status',
            display: false,
            formslot: true,
            editDisabled: true,
            hide: false,
            type: 'select',
            dataType: 'number',
            span: 25,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=api_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            prop: 'signConfigList',
            display: true,
            label: '签署关键字配置',
            hide: true,
            type: 'dynamic',
            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                done({
                  input: '默认值',
                })
              },
              rowDel: (row, done) => {
                done()
              },
              column: [
                {
                  label: '签署关键字',
                  prop: 'keywords',
                  rules: [
                    {
                      required: true,
                      message: '请输入签署关键字',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '关键字对齐方式',
                  prop: 'signAlign',
                  type: 'select',
                  dicUrl:
                    '/api/blade-system/dict/dictionary?code=signAlign',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  rules: [
                    {
                      required: true,
                      message: '选择关键字对齐方式',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '关键字类型',
                  prop: 'signType',
                  type: 'select',
                  dicData: [
                    {
                      label: '签名',
                      value: 1
                    },
                    {
                      label: '印章',
                      value: 2
                    },
                    {
                      label: '签署日期',
                      value: 3
                    },
                  ],
                  rules: [
                    {
                      required: true,
                      message: '请选择关键字类型',
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '签署人顺序',
                  prop: 'signNo',

                  type: 'number',
                  minRows:0,
                  rules: [
                    {
                      required: true,
                      message: '请输入签署人顺序',
                      trigger: 'blur',
                    },
                  ],
                },
              ]
            }
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.contracttemplate_add, false),
        viewBtn: this.vaildData(this.permission.contracttemplate_view, false),
        delBtn: this.vaildData(this.permission.contracttemplate_delete, false),
        editBtn: this.vaildData(this.permission.contracttemplate_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {},
  methods: {
    openPreview(row) {
      if (row.contractGenType === 2) {
        this.contractPDFurl = row.templateUrl + '?time=' + new Date().getMilliseconds()
        return
      }
      getcontractTemplate(row.templateId).then(res => {
        let data = res.data.data;
        let genType = data.contractGenType;
        let url = data.url;
        if (genType == 1) {
          window.open(url)
        } else {
          this.$router.push({
            path: url,
          })
        }
      })
    },
    // editBusinessWord(row) {
      // this.$refs.crud.rowEdit(row)
    // },
    cdelete(row) {
      this.$confirm('会下架模板产品,确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let ids = row.id
          return remove(ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleAdd(row, index) {
      this.$refs.crud.rowAdd(row, index)
    },
    createTemplate() {
      skipToTemplate().then(res => {
        window.open(res.data.data)
      })
    },
    syncTemplate() {
      this.isLoad = true
      syncTemplate(this.globalPage, this.globalSize).then(res => {
        if (res.data.code == 200) {
          this.isLoad = false
          this.refreshChange() // 刷新列表
          this.$message({
            type: 'success',
            message: '上上签模板数据同步成功',
          })
        }
      })
    },
    updateTemplateStatus(templateId, status) {
      if (status == 0) {
        var msg = '确定将选择数据禁用?'
      } else {
        msg = '确定将选择数据启用?'
      }
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          var ids = new Array()
          ids[0] = templateId
          let obj = {
            ids: ids,
            status: status,
          }
          return updateStatus(obj)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    skiptobestsign() {
    },
    cedit(row) {
      this.$refs.crud.rowEdit(row)
    },
    toDetail(row) {
      //todo  预览
      this.openPreview(row)
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      // if (this.selectionList.length === 0) {
      //   this.$message.warning("请选择至少一条数据");
      //   return;
      // }
      // this.$confirm("确定将选择数据删除?", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning"
      // })
      //   .then(() => {
      //     return remove(this.ids);
      //   })
      //   .then(() => {
      //     this.onLoad(this.page);
      //     this.$message({
      //       type: "success",
      //       message: "操作成功!"
      //     });
      //     this.$refs.crud.toggleSelection();
      //   });
    },
    beforeOpen(done, type) {

      // let signConfig = this.findObject(this.option.column, 'signConfigList');
      let templateWordUrl = this.findObject(this.option.column, 'templateWordUrl');
      const subContractNo = this.findObject(
        this.option.column,
        'subContractNo'
      )
      if (['add'].includes(type)) {
        this.form.expireDay = 3
        this.form.contractGenType = 2
        // signConfig.display = true
        templateWordUrl.display = true
        listAllNotContainSelf("").then(res => {
          subContractNo.dicData = res.data.data;
        })
      }
      if (['edit', 'view'].includes(type)) {
        getData(this.form.id).then(res => {
          this.form = res.data.data
          const data = res.data.data
          let configDisplay = data.contractGenType;
          if (configDisplay == null || configDisplay == 2) {
            // signConfig.display = true
            templateWordUrl.display = true
          } else {
            // signConfig.display = false
            templateWordUrl.display = false
          }
          listAllNotContainSelf(this.form.templateId).then(res => {
            subContractNo.dicData = res.data.data;
          })
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      // this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        this.globalPage = page.currentPage
        this.globalSize = page.pageSize
        let temp = res.data.data.records
        this.page.total = res.data.data.total
        this.data = temp
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
