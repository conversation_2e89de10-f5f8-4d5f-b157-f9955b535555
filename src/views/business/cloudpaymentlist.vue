<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!--支付状态-->
      <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="status" @change="handleTabButton">
            <el-radio-button
              v-for="(item, key) in statusMap"
              :key="key"
              :label="key"
              >{{ item }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </template>

      <!--状态-->
      <template slot-scope="{ row }" slot="status">
        <el-tag
          v-if="row.status === 2"
          :style="{
            color: '#646464',
            border: '0px solid #f6f6f6',
            background: '#f7f7f700',
            fontWeight: 'bolder',
          }"
          >未支付
        </el-tag>
        <el-tag
          v-else-if="row.status === 1"
          :style="{
            color: '#1277ff',
            border: '0px solid #f6f6f6',
            background: '#f7f7f700',
            fontWeight: 'bolder',
          }"
          >支付中
        </el-tag>
        <el-tag
          v-else-if="row.status === 3"
          :style="{
            color: '#1FC374',
            border: '0px solid #f6f6f6',
            background: '#f7f7f700',
            fontWeight: 'bolder',
          }"
          >已支付
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          :style="{
            color: '#FF4D4D',
            border: '0px solid #f6f6f6',
            background: '#f7f7f700',
            fontWeight: 'bolder',
          }"
          >支付失败
        </el-tag>
        <el-tag
          v-else-if="row.status === 5"
          :style="{
            color: '#646464',
            border: '0px solid #f6f6f6',
            background: '#f7f7f700',
            fontWeight: 'bolder',
          }"
          >已重提
        </el-tag>
        <el-tag
          v-else-if="row.status === 6"
          :style="{
            color: '#646464',
            border: '0px solid #f6f6f6',
            background: '#f7f7f700',
            fontWeight: 'bolder',
          }"
          >已撤销
        </el-tag>
        <el-tag
          v-else-if="row.status === 7"
          :style="{
            color: '#646464',
            border: '0px solid #f6f6f6',
            background: '#f7f7f700',
            fontWeight: 'bolder',
          }"
          >已失效
        </el-tag>
      </template>

      <!-- 还款类型颜色加深-->
      <template slot-scope="{ row }" slot="type">
        <el-tag
          :style="{
            color: 'rgb(16, 16, 16)',
            border: '1px solid rgba(234, 236, 241, 100)',
            backgroundColor: 'rgba(234, 236, 241, 100)',
            borderRadius: '17px',
            height: '28px',
            lineHeight: '28px',
          }"
        >
          {{ '当期还款' }}
        </el-tag>
      </template>

      <!--明细和修改状态-->
      <template slot-scope="{ row, type, size }" slot="menu">
        <el-button :size="size" :type="type" @click.stop="handleDetail(row)"
          >明细</el-button
        >

        <el-button
          v-if="row.status === 1"
          :size="size"
          :type="type"
          @click.stop="handleDetailTwo(row)"
          >修改状态</el-button
        >
      </template>
    </avue-crud>

    <el-dialog
      title="明细"
      append-to-body
      width="35%"
      :visible.sync="statusBox"
    >
      <div class="detail-box">
        <div class="children-box">
          <span class="laber-box">本金(元)</span>
          <span class="value-box"
            >￥{{ detailList.amountDue | formatMoney }}</span
          >
        </div>
        <!-- <div class="children-box">
          <span class="laber-box">利息(元)</span>
          <span class="value-box"></span>
        </div> -->
        <!-- <div class="children-box">
          <span class="laber-box">手续费(元)</span>
          <span class="value-box"></span>
        </div>
        <div class="children-box">
          <span class="laber-box">逾期利息(元)</span>
          <span class="value-box"></span>
        </div> -->
        <div class="children-box">
          <span class="laber-box">应还金额(元)</span>
          <span class="value-box"
            >￥{{ detailList.amountDue | formatMoney }}</span
          >
        </div>
        <div class="children-box">
          <span class="laber-box">实还金额(元)</span>
          <span class="value-box"
            >￥{{ (detailList.actualAmount || 0) | formatMoney }}</span
          >
        </div>
        <div class="children-box">
          <span class="laber-box">还款时间</span>
          <span class="value-box">{{ detailList.endDate }}</span>
        </div>
        <div class="children-box">
          <span class="laber-box">还款凭证</span>
          <el-upload
            ref="upload"
            class="file-upload"
            action="/api/blade-resource/oss/endpoint/put-file-kv"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :file-list="detailList.fileArr"
            :disabled="true"
          >
          </el-upload>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="statusBox = false">取 消</el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="修改状态"
      append-to-body
      width="35%"
      :visible.sync="statusBox2"
    >
      <el-form
        :model="formList"
        :rules="rules"
        class="rule-form"
        ref="formName"
      >
        <el-form-item label="状态类型:" prop="status">
          <el-select
            style="width: 100%"
            v-model="formList.status"
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in statusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <div class="pay-box" v-if="formList.status == 3">
            <span>应还本金(元):</span>
            <span>￥{{ formList.total }}</span>
          </div>
        </el-form-item>

        <template v-if="formList.status == 3">
          <el-form-item label="实还金额:" prop="amount" key="amount">
            <el-input placeholder="不超过" v-model="formList.amount">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="还款时间:" prop="endDate" key="endDate">
            <el-date-picker
              :picker-options="pickerOptions"
              style="width: 100%"
              v-model="formList.endDate"
              type="datetime"
              placeholder="选择还款时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="还款凭证" prop="voucher" key="voucher">
            <BaseImageUpload
              :imgData.sync="formList.voucher"
              :disabled="false"
              :length="1"
            ></BaseImageUpload>
          </el-form-item>
        </template>
        <template v-else-if="formList.status == 4">
          <el-form-item label="失败原因:" prop="remark" key="remark">
            <el-input
              type="textarea"
              v-model="formList.remark"
              placeholder="请输入失败原因"
            ></el-input>
          </el-form-item>
        </template>

        <el-form-item>
          <div style="text-align: right">
            <el-button @click="statusBox2 = false">取 消</el-button>
            <el-button
              type="primary"
              @click="handleSubmit('formName')"
              :loading="submitLoading"
              >确 定</el-button
            >
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  getCloudCashingPayDetail,
  updateCloudPaymentList,
} from '@/api/cloud/cloudpaymentlist'
import { mapGetters } from 'vuex'
import { formatMoney } from '@/util/filter'
import FilePreview from '@/components/file-preview'
import { getDictionary } from '@/api/system/dictbiz'
import BaseImageUpload from '@/components/BaseImageUpload'
import param from '@/mock/param'

var DIC = {
  TYPE: [
    {
      label: '当期还款',
      value: 1,
    },
    {
      label: '逾期付款',
      value: 2,
    },
  ],
  STATUS: [
    {
      label: '已付款',
      value: 1,
    },
  ],
}

export default {
  components: { FilePreview, BaseImageUpload },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      statusMap: {},
      status: '0',
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        align: 'center',
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        // menu: false,
        dialogClickModal: false,
        column: [
          {
            label: '还款单号',
            prop: 'repaymentNumber',
          },
          {
            label: '云信编号',
            prop: 'cloudCode',
          },
          {
            label: '还款用户',
            prop: 'repaymentName',
          },
          {
            label: '应还金额（元）',
            prop: 'amountDue',
          },
          {
            label: '实还金额（元）',
            prop: 'actualAmount',
          },
          {
            label: '创建日期',
            prop: 'createTime',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            type: 'date',
          },
          {
            label: '还款日期',
            prop: 'endDate',
          },
          {
            label: '还款类型',
            prop: 'type',
            dicData: DIC.TYPE,
            slot: true,
          },
          {
            label: '状态',
            prop: 'status',
            slot: true,
          },
          {
            label: '操作时间',
            prop: 'operateDate',
            format: 'yyyy-MM-dd HH:mm:ss',
          },
          {
            label: '操作人',
            prop: 'operateName',
          },
        ],
      },
      data: [],
      statusBox: false,
      statusBox2: false,
      rowItem: {},
      detailList: {},
      formList: {
        status: null,
        amount: '',
        endDate: '',
        remark: '',
        voucher: [],
        total: 0,
      },
      rules: {
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        amount: [
          { required: true, message: '请输入实还金额', trigger: 'blur' },
        ],
        endDate: [
          { required: true, message: '请选择还款时间', trigger: 'change' },
        ],
        remark: [
          { required: true, message: '请输入失败原因', trigger: 'change' },
        ],
        voucher: [
          { required: true, message: '请上传还款凭证', trigger: 'change' },
        ],
      },
      statusOption: [],
      submitLoading: false,
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.cloudpaymentlist_add, false),
        viewBtn: this.vaildData(this.permission.cloudpaymentlist_view, false),
        delBtn: this.vaildData(this.permission.cloudpaymentlist_delete, false),
        editBtn: this.vaildData(this.permission.cloudpaymentlist_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
    pickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      }
    },
  },
  methods: {
    //预览图片
    openPreview(row) {
      const resData = row.attach
      if (resData.extension == 'pdf') {
        this.pdfSrc = resData.link + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: resData.link })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },

    getDictionaryList() {
      getDictionary({ code: 'cloud_payment_list_status' }).then(({ data }) => {
        let list = []
        if (data.code === 200) {
          if (data.data && data.data.length) {
            for (const item of data.data) {
              list.push({
                ...item,
                label: item.dictValue,
                value: Number(item.dictKey),
              })
            }
          }
        }
        this.statusOption = list
      })
    },
    // 提交修改状态
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.submitLoading = true
          let list = []
          if (this.formList.voucher && this.formList.voucher.length) {
            this.formList.voucher.forEach(item => {
              list.push(item.id)
            })
            this.formList.cloudAttachId = list.join(',')
          }
          let params = {
            id: this.rowItem.id,
            status: Number(this.formList.status),
          }
          if (this.formList.status == 3) {
            Object.assign(params, {
              endDate: this.formList.endDate,
              cloudAttachId: this.formList.cloudAttachId,
            })
          } else if (this.formList.status == 4) {
            Object.assign(params, {
              remark: this.formList.remark,
            })
          }

          updateCloudPaymentList(params)
            .then(({ data }) => {
              if (data.code === 200) {
                this.$message.success('修改成功!')
                this.statusBox2 = false
                this.onLoad(this.page, this.query)
              }
              this.submitLoading = false
            })
            .catch(() => {
              this.submitLoading = false
            })
        } else {
          this.$message.error('请完善信息!')
          return false
        }
      })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },

    handleDetailTwo(row) {
      this.rowItem = { ...row }
      this.statusBox2 = true
      this.getDictionaryList()
      this.getCloudCashingPayDetail({ id: row.id })
    },
    handleDetail(row) {
      // this.rowItem = { ...row }
      this.statusBox = true
      this.getCloudCashingPayDetail({ id: row.id })
    },
    getCloudCashingPayDetail(params) {
      this.formList = {
        status: null,
        amount: '',
        endDate: '',
        voucher: [],
        total: 0,
      }
      getCloudCashingPayDetail(params).then(({ data }) => {
        let arr = []
        let imgArr = []
        let resData = data.data
        if (data.code === 200) {
          this.detailList = { ...resData }
          // 判断图片路径存在
          if (resData && resData.attach) {
            arr.push({ name: resData.attach.name, url: resData.attach.link })
            imgArr.push({
              ...resData.attach,
              imgUrl: resData.attach.link,
            })
          }

          this.formList = {
            status: data.data.status,
            amount: data.data.actualAmount,
            endDate: data.data.endDate,
            remark: data.data.remark,
            voucher: imgArr,
            total: formatMoney(data.data.amountDue || 0),
          }
        }
        this.detailList.fileArr = arr
      })
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      if (this.status !== '0') {
        this.query.status = this.status
      } else {
        this.query = {}
      }
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        data.records.forEach(amount => {
          amount.amountDue = formatMoney(amount.amountDue)
          amount.actualAmount = formatMoney(amount.actualAmount)
        })
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    //页面点击支付状态事件方法
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
  },
  created() {
    //页面初始化调用字典接口获取支付状态数据
    getDictionary({ code: 'cloud_payment_list_status' }).then(resp => {
      let data = resp.data.data
      this.statusMap[0] = '全部'
      data.forEach(status => {
        this.statusMap[status.dictKey] = status.dictValue
      })
    })
  },
}
</script>

<style lang="scss" scoped>
.detail-box {
  display: flex;
  flex-direction: column;
  margin-left: 17px;

  .children-box {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .laber-box {
      width: 95px;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
    .value-box {
      width: 370px;
      height: 20px;
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.rule-form {
  padding: 0 15px;
  ::v-deep {
    .el-form-item {
      display: flex;
      .el-form-item__label {
        width: 110px;
        max-width: 110px;
        text-align: left;
      }
      .el-form-item__content {
        flex-grow: 1;
      }
    }
  }
  .pay-box {
    padding: 20px 12px;
    width: 100%;
    background-color: #f7f7f7;
    border-radius: 6px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin-top: 9px;
    line-height: 20px;
    & span:first-child {
      display: block;
      color: rgba(153, 153, 153, 100);
      font-size: 14px;
      font-family: SourceHanSansSC-regular;
      font-weight: 500;
      margin-right: 8px;
    }
    & span:last-child {
      color: #00072a;
      font-weight: 600;
      font-size: 14px;
    }
  }
}
</style>
