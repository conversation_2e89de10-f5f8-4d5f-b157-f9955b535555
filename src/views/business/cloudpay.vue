<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.cloudpay_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template slot-scope="{ row, index, type, size }" slot="menu">
        <el-button type="text" size="small" @click="toDetail(row)"
          >详情
        </el-button>
        <el-button
          v-if="row.status === 0"
          :size="size"
          :type="type"
          @click.stop="handleClickDetail(row)"
          >去提交</el-button
        >
        <el-button
          v-if="row.status === 5"
          :size="size"
          :type="type"
          @click.stop="payment(row)"
          >付款</el-button
        >
        <el-button v-if="row.status == 3" @click.stop="updateStatus(row)"
          >去修改
        </el-button>
      </template>
    </avue-crud>

    <CloudBillPay
      :visible.sync="visible"
      :cloudCode="cloudCode"
      @onload="onLoad(page)"
    ></CloudBillPay>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/cloud/cloudpay'
import { mapGetters } from 'vuex'
import { formatMoney } from '@/util/filter'
import CloudBillPay from '@/views/system/components/cloudBillPay.vue'

export default {
  components: { CloudBillPay },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '云信编号',
            prop: 'cloudCode',
            search: true,
          },
          {
            label: '云信金额',
            prop: 'amount',
          },
          {
            label: '开单日',
            prop: 'startDate',
          },
          {
            label: '到期日',
            prop: 'endDate',
          },
          {
            label: '状态',
            prop: 'status',
            search: true,
            type: 'select',
            dataType: 'number',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=cloud_pay_status',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
        ],
      },
      data: [],
      visible: false, // 弹窗开关
      cloudCode: null,
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.cloudpay_add, false),
        viewBtn: this.vaildData(this.permission.cloudpay_view, false),
        delBtn: this.vaildData(this.permission.cloudpay_delete, false),
        editBtn: this.vaildData(this.permission.cloudpay_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    toDetail(row) {
      this.$router.push(
        `/system/cloudBillCashingDetail/${Buffer.from(
          JSON.stringify({
            id: row.id,
            cloudCode: row.cloudCode,
          })
        ).toString('base64')}`
      )
    },

    // 付款
    payment(row) {
      this.visible = true
      this.cloudCode = row.cloudCode
    },
    handleClickDetail(row) {
      const params = {
        id: row.id,
        btnState: 1, // 去提交
      }
      this.$router.push(
        `/system/cloudBillingLetter/${Buffer.from(
          JSON.stringify(params)
        ).toString('base64')}`
      )
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        data.records.forEach(amount => {
          amount.amount = formatMoney(amount.amount)
        })
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style></style>
