<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row }" slot="menu">
        <el-button
          v-if="row.processStatus !== 1 || row.processNo"
          :size="size"
          :type="type"
          @click="handleBtnViewBypass(row)"
          >查看</el-button
        >
        <el-button v-if="row.processStatus == 1" :size="size" :type="type" @click="handleBtnApprove(row)"
          >审批</el-button
        >
        <el-button :size="size" :type="type" @click="handleFlow(row)">流程图</el-button>
      </template>
      <template slot-scope="{ row }" slot="processStatus">
        <el-tag :type="row.processStatus === 1 ? 'primary' : row.processStatus === 3 ? 'success' : 'info'">{{
          statusMap[row.processStatus]
        }}</el-tag>
      </template>
      <template slot="header" class="tabsStyle">
        <div class="order-header-container">
          <el-radio-group v-model="processStatus" @change="handleTabButton">
            <el-radio-button v-for="(item, key) in statusMap" :key="key" :label="key">{{ item }}</el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </avue-crud>

    <el-dialog
      :visible.sync="bpmnVisible"
      append-to-body
      destroy-on-close
      title="流程图"
      width="70%"
      custom-class="wf-dialog"
    >
      <wf-design ref="bpmn" style="height: 60vh" :options="bpmnOption"></wf-design>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getList, getDetail } from '@/api/business/businessprocess'
import { getDictionary } from '@/api/system/dictbiz'
import exForm from '../plugin/workflow/mixins/ex-form'
import { mapGetters } from 'vuex'
import { detail } from '@/api/plugin/workflow/process'
import { routerMapKeyToPath } from './config'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      processStatus: 1,
      statusMap: {},
      bpmnVisible: false,
      bpmnOption: {},
      selectionList: [],
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        // selection: true,
        align: 'center',
        dialogClickModal: false,
        column: [
          {
            label: '流程编号',
            prop: 'processNo',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入流程编号',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '流程名称',
            prop: 'processDefinitionName',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入流程名称',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '流程分类',
            prop: 'category',
            search: true,
            type: 'tree',
            display: true,
            dicUrl: '/api/blade-workflow/design/category/tree',
            props: {
              label: 'name',
              value: 'id',
            },
            errorslot: true,
            rules: [
              {
                require: true,
                message: '请选择分类',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '申请人',
            prop: 'startUsername',
            hide: false,
            display: false,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入申请人用户名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '申请时间',
            prop: 'createTime',
          },
          {
            label: '当前节点',
            prop: 'taskName',
          },
          {
            label: '状态',
            prop: 'processStatus',
            hide: false,
            slot: true,
          },
        ],
      },
      data: [],
    }
  },
  watch: {
    processStatus(newVal) {
      let processStatus = this.findObject(this.option.column, 'processStatus')
      if (newVal == 5 || newVal == 6) {
        processStatus.hide = true
      } else {
        processStatus.hide = false
      }
    },
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.businessprocess_add, false),
        viewBtn: this.vaildData(this.permission.businessprocess_view, false),
        delBtn: this.vaildData(this.permission.businessprocess_delete, false),
        editBtn: this.vaildData(this.permission.businessprocess_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  mixins: [exForm],
  methods: {
    handleBtnViewBypass(row) {
      const { userAllIndepForms, indepFormKey } = row
      if (!userAllIndepForms && !indepFormKey) {
        this.handleBtnView2(row)
        return
      }
      this.handleBtnView1(row)
    },
    // 节点独立表单查看
    handleBtnView1(row) {
      const { taskId, processInstanceId, userAllIndepForms, indepFormKey } = row
      let targetF = ''
      const indepParam = {
        taskId: '',
        processInsId: processInstanceId,
      }
      if (indepFormKey) {
        targetF = indepFormKey.split('@')
        indepParam.taskId = taskId
      } else {
        sessionStorage.setItem(`userAllIndepFormsSessions_${processInstanceId}`, JSON.stringify(userAllIndepForms))
        targetF = userAllIndepForms[0].indepFormKey.split('@')
        indepParam.taskId = userAllIndepForms[0].taskId
      }

      const indepParams = Buffer.from(JSON.stringify(indepParam)).toString('base64')

      if (targetF.length > 1) {
        // 含有外置表单的节点独立表单
        const target = routerMapKeyToPath[targetF[0]]
        if (!target) {
          this.$message.warning('外置表单key配置错误')
          return
        }
        this.$router.push(`${target}/detail/${indepParams}`)
      } else {
        // 单纯的内置表单的节点独立表单
        this.$router.push(`/work/mywork/process/detail/${indepParams}`)
      }
    },
    // 外置表单，内置表单查看
    handleBtnView2(row) {
      const { taskId, processInstanceId, processDefinitionKey } = row
      const param = Buffer.from(
        JSON.stringify({
          taskId,
          processInsId: processInstanceId,
        })
      ).toString('base64')

      // 判断外置表单路径是否存在
      if (routerMapKeyToPath[processDefinitionKey]) {
        const targetExist = routerMapKeyToPath[processDefinitionKey]
        this.$router.push(`${targetExist}/detail/${param}`)
        return
      }

      // 判断是否有__号
      const index = processDefinitionKey.indexOf('__')
      let newKey = processDefinitionKey
      // 如果有的话，获取__号前的key
      if (!!~index) {
        newKey = processDefinitionKey.slice(0, index)
      }
      if (row.processNo) {
        // 外置表单
        const target = routerMapKeyToPath[newKey]
        if (!target) return
        this.$router.push(`${target}/detail/${param}`)
      } else {
        // 内置表单
        this.$router.push(`/workflow/process/detail/${param}`)
      }
    },
    // 审批跳转事件
    handleBtnApprove(row) {
      const { taskId, processInstanceId, processDefinitionKey, indepFormKey } = row
      const param = Buffer.from(
        JSON.stringify({
          taskId,
          processInsId: processInstanceId,
        })
      ).toString('base64')
      if (indepFormKey) {
        // 节点独立表单
        const targetF = indepFormKey.split('@')
        if (targetF.length > 1) {
          // 含有外置表单的节点独立表单
          const target = routerMapKeyToPath[targetF[0]]
          if (!target) {
            this.$message.warning('外置表单key配置错误')
            return
          }
          this.$router.push(`${target}/approve/${param}`)
        } else {
          // 单纯的内置表单的节点独立表单
          this.$router.push(`/workflow/process/detail/${param}`)
        }
      } else if (row.processNo) {
        // 判断外置表单路径是否存在
        if (routerMapKeyToPath[processDefinitionKey]) {
          const targetExist = routerMapKeyToPath[processDefinitionKey]
          this.$router.push(`${targetExist}/approve/${param}`)
          return
        }

        // 判断是否有__号
        const index = processDefinitionKey.indexOf('__')
        let newKey = processDefinitionKey
        // 如果有的话，获取__号前的key
        if (!!~index) {
          newKey = processDefinitionKey.slice(0, index)
        }
        // 外置表单
        const target = routerMapKeyToPath[newKey]
        if (!target) return
        this.$router.push(`${target}/approve/${param}`)
      } else {
        // 内置表单
        this.$router.push(`/workflow/process/detail/${param}`)
      }
    },
    handleFlow(row) {
      const { taskId, processInstanceId } = row
      detail({ taskId, processInsId: processInstanceId }).then(res => {
        const { process, flow } = res.data.data

        this.bpmnOption = {
          mode: 'view',
          xml: process.xml,
          flows: this.handleResolveFlows(flow),
        }

        this.bpmnVisible = true
      })
    },
    handleTabButton() {
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      this.query.status = null
      if (this.processStatus != 0) {
        this.query.status = this.processStatus
      }
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
  created() {
    getDictionary({ code: 'business_process_status' }).then(resp => {
      let data = resp.data.data
      data.forEach(status => {
        this.statusMap[status.dictKey] = status.dictValue
      })
      // console.log(this.statusMap)
    })
  },
}
</script>

<style lang="scss">
.order-header-container {
  width: 100%;
  height: 41px;
  border: 1px solid #f3f3f3;
  background-color: #ffff;

  .el-radio-group {
    height: 100%;

    .el-radio-button {
      height: 100%;

      &.is-active {
        border: 1px sloid gray;
      }
    }

    .el-radio-button__inner {
      border: none !important;
      height: 100%;
    }
  }
}
</style>
