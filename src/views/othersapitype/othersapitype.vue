<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               :before-close="beforeClose"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.othersapitype_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template
        slot-scope="scope"
        slot="menu"
      >
        <el-button
          type="text"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click.stop="handleAdd(scope.row,scope.index)"
          v-if="scope.row.parentId == 0"
        >新增子项
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, getOthersApiTypeTree, remove, update} from "@/api/othersapitype/othersapitype";
import {mapGetters} from "vuex";

export default {
  data() {
    var validateName = (rule, value, callback) => {
      if (value.trim().length > 10) {
        callback(new Error('分类名称不能超过10个字'))
      } else {
        callback()
      }
    }
    return {
      form: {},
      query: {},
      loading: true,
      parentId: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        // height: 'auto',
        indexLabel: '序号',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        tree: true,
        viewBtn: true,
        selection: true,
        menuWidth: 300,
        dialogClickModal: false,
        column: [
          {
            label: '是否支持多开启',
            prop: 'supportMultiOpen',
            type: 'select',
            row: true,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            rules: [
              {
                required: true,
                message: '请选择是否支持多开启',
                trigger: 'click',
              },
            ],
          },
          {
            label: '分类名称',
            prop: 'name',
            search: true,
            rules: [
              {
                required: true,
                validator: validateName,
                trigger: 'blur',
              },
            ],
          },
          {
            label: '上级菜单',
            prop: 'parentId',
            type: 'tree',
            dicUrl: '/api/othersapitype/all?parentId_equal=' + 0,
            dicFlag: true,
            hide: true,
            editDisabled: false,
            addDisabled: false,
            props: {
              label: 'name',
              value: 'id',
            },
            rules: [
              {
                required: false,
                message: '请选择上级菜单',
                trigger: 'click',
              },
            ],
          },
          {
            label: '编号',
            prop: 'code',
            search: true,
            rules: [
              {
                required: true,
                message: '请输入编号',
                trigger: 'click',
              },
            ],
          },
          {
            label: '排序',
            prop: 'sort',
            type: 'number',
            minRows: 0,
            precision: 0,
            rules: [
              {
                required: true,
                message: '请输入排序',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.othersapitype_add, false),
        viewBtn: this.vaildData(this.permission.othersapitype_view, false),
        delBtn: this.vaildData(this.permission.othersapitype_delete, false),
        editBtn: this.vaildData(this.permission.othersapitype_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    initData() {
      getOthersApiTypeTree.then(res => {
        const column = this.findObject(this.option.column, "parentId");
        column.dicData = res.data.data;
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          // 获取新增数据的相关字段
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    handleAdd(row, index) {
      const column = this.findObject(this.option.column, 'parentId')
      this.form.parentId = row.id
      column.addDisabled = true
      this.$refs.crud.rowAdd()
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        (error) => {
          loading()
          console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleEnable() {

    },
    handleDisable() {

    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data
        })
        if (this.form.parentId == 0) {
          const column = this.findObject(this.option.column, 'parentId')
          column.editDisabled = true
        }
      }
      done()
    },
    beforeClose(done, type) {
      if (['edit', 'view'].includes(type)) {
        if (this.form.parentId == 0) {
          const column = this.findObject(this.option.column, 'parentId')
          column.editDisabled = false
        }
      }
      if ('add' === type) {
        const column = this.findObject(this.option.column, 'parentId')
        column.addDisabled = false
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.parentId = 0
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.parentId = ''
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        this.data = res.data.data
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style>
</style>
