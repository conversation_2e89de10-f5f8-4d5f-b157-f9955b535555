<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @sort-change="sortChange"
      @on-load="onLoad"
    >
      <template slot="status" slot-scope="{ row }">
        <el-tag type="success" v-if="row.status === 1">已启用</el-tag>
        <el-tag type="info" v-if="row.status === 0">已禁用</el-tag>
      </template>
      <template slot-scope="{ row, type, size }" slot="menu">
        <el-button
          icon="el-icon-top"
          :size="size"
          :type="type"
          v-if="row.status == 0 && permissionList.onShelfBtn"
          @click="rowOnShelf(row)"
          >启用
        </el-button>
        <el-button
          icon="el-icon-bottom"
          :size="size"
          :type="type"
          v-if="row.status === 1 && permissionList.offShelfBtn"
          @click="rowOffShelf(row)"
          >禁用
        </el-button>
        <el-button
          icon="el-icon-delete"
          :size="size"
          :type="type"
          v-if="permissionList.delBtn"
          @click="rowRemove(row)"
          >删除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  saveOrUpdate,
  enable,
  un_enable,
  remove,
  capitalBankList,
} from '@/api/capitalOrderLimtCalculate/capitalOrderLimtCalculate'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {},
      operator: false,
      loading: true,
      query: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        // searchLabelWidth: 115,
        // menuWidth: 250,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: true,
        editBtn: true,
        delBtn: false,
        selection: true,
        align: 'center',
        dialogClickModal: false,
        column: [
          {
            label: '资金方',
            prop: 'deptId',
            type: 'select',
            search: true,
            // dicUrl:
            //   '/api/web-back/orderLevel/capitalOrderLimtCalculate/capitalBankList',
            // props: {
            //   label: 'deptName',
            //   value: 'id',
            // },
            dicData: [],
            rules: [
              {
                required: true,
                message: '请选择资金方',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '资金方编号',
            prop: 'capitalServiceNo',
            searchLabelWidth: 100,
            labelWidth: 100,
            // addDisplay: false,
            // editDisplay: false,
            display: false,
          },
          {
            label: '资金产品',
            prop: 'goodsId',
            type: 'select',
            // addDisplay: false,
            // editDisplay: false,
            dicUrl: '/api/web-back/orderLevel/capitalOrderLimtCalculate/allGoods',
            props: {
              label: 'goodsName',
              value: 'id',
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: '请选择资金产品',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '费用计算器',
            prop: 'goodsExpenseRuleId',
            type: 'select',
            searchLabelWidth: 100,
            labelWidth: 100,
            search: true,
            dicUrl:
              '/api/web-back/orderLevel/capitalOrderLimtCalculate/selectListByExpenseType?expenseType=999',
            props: {
              label: 'feeFormulaName',
              value: 'id',
            },
            rules: [
              {
                required: true,
                message: '请选择费用计算器',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '状态',
            prop: 'status',
            slot: true,
            // search: true,
            type: 'radio',
            addDisplay: false,
            editDisplay: false,
            dicData: [
              {
                label: '启用',
                value: 1,
              },
              {
                label: '禁用',
                value: 0,
              },
            ],
          },
        ],
      },
      data: [],
      capitalList: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.capitalOrderLimtCalculate_add, false),
        viewBtn: this.vaildData(this.permission.capitalOrderLimtCalculate_view, false),
        delBtn: this.vaildData(this.permission.capitalOrderLimtCalculate_delete, false),
        editBtn: this.vaildData(this.permission.capitalOrderLimtCalculate_edit, false),
        onShelfBtn: this.vaildData(this.permission.capitalOrderLimtCalculate_onShelfBtn, false),
        offShelfBtn: this.vaildData(this.permission.capitalOrderLimtCalculate_offShelfBtn, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    getCapitalBankList() {
      capitalBankList().then(({ data }) => {
        this.option.column.find(ele => {
          if (ele.prop === 'deptId') {
            ele.dicData = data.data.map((item) => {
              return {
                label: item.deptName,
                value: item.id,
              }
            })
          }
        })
        this.capitalList = data.data
      })
    },
    rowOnShelf(row) {
      let params = {
        id: row.id,
        deptId: row.deptId,
        goodsId: row.goodsId,
      }
      this.$confirm('确定启用吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return enable(params)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功',
          })
        })
    },
    rowOffShelf(row) {
      let params = {
        id: row.id,
        deptId: row.deptId,
      }
      this.$confirm('确定禁用吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return un_enable(params)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功',
          })
        })
    },
    rowRemove(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    rowSave(row, done, loading) {
      this.capitalList.find((item) => {
        if (item.id === row.deptId) {
          row.capitalServiceNo = item.capitalServiceNo || ''
        }
      })
      saveOrUpdate(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      this.capitalList.find((item) => {
        if (item.id === row.deptId) {
          row.capitalServiceNo = item.capitalServiceNo || ''
        }
      })
      saveOrUpdate(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        }
      )
    },
    beforeOpen(done) {
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
  created() {
    this.getCapitalBankList()
  },
}
</script>

<style lang="scss" scoped></style>
