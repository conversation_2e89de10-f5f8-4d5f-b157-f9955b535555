<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{ row }" slot="status">
        <el-tag
          v-if="row.status == 1"
          :style="{
            color: '#2a44e4',
            border: '1px solid #2a44e4',
            background: '#EAFCF7',
          }"
          >待入库
        </el-tag>
        <el-tag
          v-else-if="row.status == 2"
          :style="{
            color: '#2a44e4',
            border: '1px solid #2a44e4',
            background: '#EAFCF7',
          }"
          >待结算
        </el-tag>
        <el-tag
          v-else-if="row.status == 3"
          :style="{
            color: '#67c211',
            border:'1px solid #67c23a',
            background:'#fff',
          }"
          >已结算
        </el-tag>
        <el-tag
          v-else-if="row.status == 4"
          :style="{
            color: '#bbbbc0',
            border: '1px solid #bbbbc0',
            background: '#fff',
          }"
          >已作废
        </el-tag>
      </template>

      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="cedit(scope.row, index)"
          size="small"
          >详情
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="cdelete(scope.row, index)"
          v-if="scope.row.status == 1"
          size="small"
          >入库
        </el-button>
        <el-button
            type="text"
            icon="el-icon-delete"
            @click="cdelete(scope.row, index)"
            v-if="scope.row.status == 2"
            size="small"
            >结算
        </el-button>
        <el-button
          type="text"
          icon="el-icon-success"
          size="small"
          v-if="scope.row.status == 1 || scope.row.status == 2"
          @click.stop="openFollowDialog(scope.row)"
          >订单跟进
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="跟进"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="40%"
      :before-close="handleClose">
      <avue-form ref="from" v-model="followForm" :option="followOption">
        <template slot="menuForm">
          <el-button @click="saveFollow()">保存</el-button>
       </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/purchase/purchaseplanorderlist";
  import {saveFollow} from "@/api/purchase/purchaseplanorderfollow";
  import {mapGetters} from "vuex";
    var DIC = {
      EXPIRE_STATUS:[{
          label: '已延期',
          value: 2
      }, {
          label: '未延期',
          value: 1
    }]
  }
  export default {
    data() {
      return {
        followForm: {
          status: '',
          purchaseNo: '',
          instruction: '',
          followTime: ''
        },
        dialogVisible: false,
        purchaseNo: '',
        followTime: '',
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        followOption: {
          addBtn: false,
          submitText: '完成',
          submitBtn: false,
          resetBtn: false,
          span:24,
          column: [
            {
              label: "跟进状态",
              prop: "status",
              type: "select",
              dicUrl: '/api/blade-system/dict-biz/dictionary?code=follow_status',
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入跟进状态",
                trigger: "blur"
              }],
            },
            {
              label: "跟进时间",
              prop: "followTime",
              type: "datetime",
              format:'yyyy-MM-dd hh:mm:ss',
              valueFormat:'yyyy-MM-dd hh:mm:ss',
              rules: [{
                required: true,
                message: "请输入跟进时间",
                trigger: "blur"
              }],
            },
             {
              label: "跟进说明",
              prop: "instruction",
              rules: [{
                required: true,
                message: "请输入跟进说明",
                trigger: "blur"
              }],
            }
          ]
        },
        option: {
          labelWidth: 150,
          viewBtn: false,
          editBtn: false,
          delBtn: false,
          addBtn: false,
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          selection: true,
          dialogClickModal: false,
          headerAlign: 'center',
          align: 'center',
          column: [
            {
              label: "采购单号",
              prop: "purchaseNo",
              search: true,
              rules: [{
                required: true,
                message: "请输入采购单号",
                trigger: "blur"
              }]
            },
            {
              label: "采购计划单号Id",
              prop: "planId",
              hide: true,
              display: false,
              rules: [{
                required: true,
                message: "请输入采购计划单号",
                trigger: "blur"
              }]
            },
            {
              label: "计划单号",
              prop: "planNo",
              search: true,
              rules: [{
                required: true,
                message: "请输入采购计划单号",
                trigger: "blur"
              }]
            },
            {
              label: "供应商",
              type: "select",
              search: true,
              hide: true,
              prop: "supplierId",
              dicUrl: "/api/blade-customer/web-back/customer/customersupplier/supper-all",
              dicMethod: "get",
              props: {
                value: "id",
                label: "supperName"
              }
            },
            {
              labelWidth: 100,
              label: "融资用户",
              prop: "financingId",
              type: "select",
              hide: true,
              search: true,
              dicUrl: "/api/blade-customer/web-back/customer/frontfinancinglist/financing-all",
              dicMethod: "get",
              props: {
                value: "id",
                label: "name"
              }
            },
            {
              label: "采购总额",
              prop: "purchaseAmount",
              rules: [{
                required: true,
                message: "请输入采购总额",
                trigger: "blur"
              }]
            },
            {
              searchLabelWidth: 95,
              label: "采购负责人",
              prop: "peopleId",
              hide: true,
              search: true,
              type: "select",
              dicUrl: "/api/blade-user/user-all?userType=1",
              dicMethod: "get",
              props: {
                value: "id",
                label: "realName"
              }
            },{
              label: '供应商',
              display: false,
              prop: 'supplierName'
            },

            {
              label: '融资用户',
              display: false,
              prop: 'financingName'
            },
            {
              label: '采购负责人',
              display: false,
              prop: 'peopleName'
            },
            {
              label: '申请时间',
              display: false,
              type: 'datetime',
              prop: 'createTime'
            },
            {
              label: "订单状态",
              prop: "status"
            },
            {
              label: "延期状态",
              prop: "expireStatus",
              search: true,
              type: "select",
              hide: true,
              dicData: DIC.EXPIRE_STATUS
            },
            {
              label: "延期状态",
              prop: "expireStatusText",
              display: false
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.purchaseplanorderlist_add, false),
          viewBtn: this.vaildData(this.permission.purchaseplanorderlist_view, false),
          delBtn: this.vaildData(this.permission.purchaseplanorderlist_delete, false),
          editBtn: this.vaildData(this.permission.purchaseplanorderlist_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 打开跟进的弹窗
      openFollowDialog(row){
        this.dialogVisible = true;
        this.followForm.purchaseNo = row.purchaseNo;
      },
      saveFollow() {
        console.log(this.followForm);
        saveFollow(this.followForm).then(res => {
          this.dialogVisible = false;
          this.$message({
              type: "success",
              message: "订单跟进成功!"
            });
        })

      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
