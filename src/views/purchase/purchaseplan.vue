<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{ row }" slot="status">
        <el-tag
          v-if="row.status == 1"
          :style="{
            color: '#2a44e4',
            border: '1px solid #2a44e4',
            background: '#EAFCF7',
          }"
          >待采购
        </el-tag>
        <el-tag
          v-else-if="row.status == 4"
          :style="{
            color: '#2a44e4',
            border: '1px solid #2a44e4',
            background: '#EAFCF7',
          }"
          >待派单
        </el-tag>
        <el-tag
          v-else-if="row.status == 2"
          :style="{
            color: '#67c211',
            border:'1px solid #67c23a',
            background:'#fff',
          }"
          >已采购
        </el-tag>
        <el-tag
          v-else-if="row.status == 3"
          :style="{
            color: '#bbbbc0',
            border: '1px solid #bbbbc0',
            background: '#fff',
          }"
          >已作废
        </el-tag>

      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          @click="cedit(scope.row, index)"
          size="small"
          >详情
        </el-button>
        <el-button
          type="text"
          @click="cdelete(scope.row, index)"
          v-if="scope.row.status == 4"
          size="small"
          @click.stop="delivery(scope.row)"
          >派单
        </el-button>
        <el-button
          type="text"
          size="small"
          v-if="scope.row.status == 1"
          @click.stop="offShelf(scope.row, scope.index)"
          >采购
        </el-button>

        <el-button
          type="text"
          size="small"
          v-if="scope.row.status == 2"
          @click.stop="putShelf(scope.row, scope.index)"
        >采购订单
        </el-button>

        <el-button
          type="text"
          size="small"
          v-if="scope.row.status == 4 || scope.row.status == 1"
          @click.stop="abandonedOrder(scope.row, scope.index)"
        >作废
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="选择人员"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="60%">
      <avue-crud ref="crud"
        :data="userData"
        :option="userOption"
        :page.sync="dialogPage"
        :table-loading="loading"
        @search-change="searchChangeUser"
        @search-reset="searchResetUser"
        @selection-change="selectionChangeUser"
        @current-change="currentChangeUser"
        @size-change="sizeChangeUser">

      </avue-crud>

    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, abolitionOrder} from "@/api/purchase/purchaseplan";
  import {mapGetters} from "vuex";
  import {getList as userList} from "@/api/system/user";
  export default {
    data() {
      return {
        dialogVisible: false,
        userData:[],
        form: {},
        query: {},
        loading: true,
        dialogPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        userOption: {
          addBtn: false,
          index: true,
          menu:false,
          headerAlign: 'center',
          align: 'center',
          column:[
              {
                label:'头像',
                prop:'avatar',
                type: 'upload'
              },
              {
                label:'姓名',
                prop:'realName',
                search: true
              },
              {
                label:'部门',
                prop:'deptName'
              },
              {
                label:'职位',
                prop:'postName'
              },
              {
                label:'角色',
                prop:'roleName'
              }
            ]
        },
        option: {
          height:'auto',
          calcHeight: 30,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          selection: true,
          dialogClickModal: false,
          headerAlign: 'center',
          align: 'center',
          column: [
            {
              label: "计划单号",
              prop: "planNo",
              search: true,
              rules: [{
                required: true,
                message: "请输入计划单号",
                trigger: "blur"
              }]
            },
            {
              label: "融资编号",
              prop: "financingNo",
              search: true,
              rules: [{
                required: true,
                message: "请输入融资编号",
                trigger: "blur"
              }]
            },
            {
              label: "供应商",
              type: "select",
              search: true,
              prop: "supplierId",
              dicUrl: "/api/blade-customer/web-back/customer/customersupplier/supper-all",
              dicMethod: "get",
              props: {
                value: "id",
                label: "supperName"
              },
              rules: [{
                required: true,
                message: "请输入供应商id",
                trigger: "blur"
              }]
            },
            {
              label: "融资用户",
              prop: "financingId",
              type: "select",
              search: true,
              dicUrl: "/api/blade-customer/web-back/customer/frontfinancinglist/financing-all",
              dicMethod: "get",
              props: {
                value: "id",
                label: "name"
              },
              rules: [{
                required: true,
                message: "请输入融资用户id",
                trigger: "blur"
              }]
            },

            {
              label: "采购总额(元)",
              prop: "planTotalAmount",
              rules: [{
                required: true,
                message: "请输入采购总额",
                trigger: "blur"
              }]
            },
            {
              label: '申请时间',
              display: false,
              type: 'dateTime',
              prop: 'createTime'
            },
            {
              searchLabelWidth: 95,
              label: "采购负责人",
              prop: "peopleId",
              search: true,
              type: "select",
              dicUrl: "/api/blade-user/user-all?userType=1",
              dicMethod: "get",
              props: {
                value: "id",
                label: "realName"
              },
              rules: [{
                required: true,
                message: "请输入责任人id",
                trigger: "blur"
              }]
            },
            {
            label: '采购状态',
            prop: 'status',
            slot: true,
            display: false,
            type: "select",
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=plan_status',
            props: {
              label: "dictValue",
              value: "dictKey"
             }
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.purchaseplan_add, false),
          viewBtn: this.vaildData(this.permission.purchaseplan_view, false),
          delBtn: this.vaildData(this.permission.purchaseplan_delete, false),
          editBtn: this.vaildData(this.permission.purchaseplan_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      delivery(params) {
        this.dialogVisible = true;
        userList(this.dialogPage.currentPage,this.dialogPage.pageSize,Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.dialogPage.total = data.total;
          this.userData = data.records;
          this.loading = false;
        })
      },
      abandonedOrder(row) {
          if (row.status == 2) {
          this.$message({
            type: 'info',
            message: '不能作废已采购的订单',
          })
          return
        }
        let msg = '确认作废订单?'

        this.$confirm(msg, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            let id = row.id
            let status = 3
            return abolitionOrder(id, status)
          })
          .then(() => {
            this.onLoad(this.page)
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
          })
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchResetUser() {
        this.query = {};
        this.loading = true;
        userList(this.dialogPage.currentPage,this.dialogPage.pageSize).then(res => {
          const data = res.data.data;
          this.dialogPage.total = data.total;
          this.userData = data.records;
          this.loading = false;
        })
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      searchChangeUser(params, done) {
        this.query = params;
        this.dialogPage.currentPage = 1;
        this.delivery(this.dialogPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeUser(currentPage){
        this.dialogPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeUser(pageSize){
        this.dialogPage.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
