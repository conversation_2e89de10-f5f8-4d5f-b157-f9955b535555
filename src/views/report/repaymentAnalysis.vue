<template>
  <basic-container>
    <avue-crud
      class="cloud-report"
      :option="option"
      :table-loading="loading"
      :data="data"
      ref="crud"
      v-model="form"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
    </avue-crud>
    <iframe
      :src="reportSrc"
      style="width: 100%; height: calc(100vh - 300px)"
      :frameborder="0"
    ></iframe>
  </basic-container>
</template>

<script>
import dayjs from 'dayjs'
import { monthData, businessTypeData } from '@/util/config'
import { mapGetters } from 'vuex'
import { getDictionary } from '@/api/system/dict'
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      reportSrc: null,
      option: {
        height: '500',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        selection: false,
        viewBtn: true,
        addBtn: false,
        menu: false,
        dialogClickModal: false,
        column: [
          {
            label: '年',
            prop: 'year',
            type: 'year',
            format: 'yyyy',
            valueFormat: 'yyyy',
            search: true,
            hide: true,
          },
          {
            label: '月',
            prop: 'month',
            type: 'select',
            dicData: monthData,
            search: true,
            hide: true,
          },
          {
            label: '类型',
            prop: 'type',
            searchSpan: 3,
            type: 'select',
            dicData: businessTypeData,
            search: true,
            hide: true,
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  methods: {
    searchReset() {
      this.query = {}
      this.onLoad(this.query)
    },
    searchChange(params, done) {
      this.query = params
      this.onLoad(this.query)
      done()
    },
    refreshChange() {
      this.onLoad(this.query)
    },
    onLoad(params) {
      this.getDictionary({ code: 'system_url' }, params)
    },
    // 获取系统字典
    async getDictionary(params, query) {
      const { data } = await getDictionary(params)
      let baseUrl = null
      if (data.code === 200) {
        baseUrl = data.data && data.data[0].dictValue
      }
      this.handleData(query, baseUrl)
    },

    handleData(params, baseUrl) {
      let year = params.year || dayjs().year()
      let month = params.month || (params.year ? null : 'week')
      let type = params.type || null
      this.reportSrc = `${baseUrl}/ureport/preview/?_u=blade-回款分析表.ureport.xml&year=${year}&month=${month}&type=${type}&tenantId=${this.userInfo.tenant_id}`
      console.log(this.reportSrc)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-table,
  .avue-crud__pagination {
    display: none;
  }
}
</style>
