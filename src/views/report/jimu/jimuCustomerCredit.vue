<template>
    <basic-container>

      <el-scrollbar class="main">
        <iframe
          :src="src"
          class="iframe"/>
      </el-scrollbar>
    </basic-container>
</template>

<script>

import { getStore } from '@/util/store.js'
import {monthData} from "@/util/config";

export default {
  data() {
    return {
      src: "/jmreport/view/755220405300928512?token=" + getStore({name: 'token'}) || '',
    }
  },
  created() {
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.iframe {
  width: 100%;
  height: 70vh; /*设置高度百分比,一直调到只有一个滚动调为止*/
  border: 0;
  overflow: hidden;
  box-sizing: border-box;
}

</style>
