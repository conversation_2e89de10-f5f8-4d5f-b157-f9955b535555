<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      ref="crud"
      v-model="form"
      :page.sync="page"
      :permission="permissionList"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button type="warning"
                   size="small"
                   plain
                   v-if="userInfo.role_name.includes('admin')"
                   icon="el-icon-download"
                   @click="handleExport">导出
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, remove, businessReportPage } from '@/api/report/report'
import { mapGetters } from 'vuex'
import {getToken} from "@/util/auth";

export default {
  data() {
    return {
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        selection: false,
        viewBtn: true,
        menu:false,
        dialogClickModal: false,
        column: [
          {
            label: "融资客户",
            prop: 'userId',
            hide: true,
            search: true,
            type: 'select',
            dicUrl: '/api/blade-customer/web-back/customer/customer/all',
            props: {
              label: 'name',
              value: 'userId'
            },
          },
          {
            label: "日期范围",
            prop: "daterange",
            type: "daterange",
            format:'yyyy-MM-dd',
            valueFormat:'yyyy-MM-dd',
            startPlaceholder: '日期开始范围自定义',
            endPlaceholder: '日期结束范围自定义',
            searchRange: true,
            search: true,
            hide: true
          },
          {
            label: '融资编号',
            prop: 'financeNo',
          },
          {
            label: "进件时间",
            prop: "financeApplyDate",
            type: 'datetime',
            format: "yyyy-MM-dd",
          },
          {
            label: "审批通过时间",
            prop: "passTime",
          },
          {
            label: "业务类型",
            prop: "businessType",
          },
          {
            label: "客户名称",
            prop: "userName",
          },
          {
            label: '放款时间',
            prop: 'loanDate',
          },
          {
            label: '放款金额',
            prop: 'loanAmount',
          },
          {
            label: '利率',
            prop: 'annualInterestRate',
          },
          {
            label: "合同到期日",
            prop: "expireTime",
            type: 'datetime',
            format: "yyyy-MM-dd",
          },
          {
            label: "已收回本金",
            prop: "repaidPrincipal",
          },
          {
            label: "已收回利息",
            prop: "repaidInterest",
          },
          {
            label: "状态",
            prop: "status",
          },
          {
            label: "逾期金额",
            prop: "overdueAmount",
          },
          {
            label: "结清时间",
            prop: "settleDate",
          },

        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'permission']),
    permissionList() {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: true,
        editBtn: false,
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    handleExport() {
      if (!this.query.userId) {
        this.$message.warning('请选择融资客户')
        return;
      }
      this.$confirm("是否导出报表数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        window.open(`/api/blade-report/report/exportExcel?${this.website.tokenHeader}=${getToken()}&userId=${this.query.userId}&name=业务报表`);
      });
    },
    handlePreview(name) {
      this.$router.push({
        path: `/myiframe/urlPath?name=preview-${name}&src=${this.website.reportUrl}/preview?_u=blade-${name}`,
      })
    },
    handleDesign(name) {
      this.$router.push({
        path: `/myiframe/urlPath?name=designer-${name}&src=${this.website.reportUrl}/designer?_u=blade-${name}`,
      })
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      if (params.daterange) {
        this.query.startTime = params.daterange[0]
        this.query.endTime = params.daterange[1]
      }
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true;
      businessReportPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })


    },
  },
}
</script>

<style></style>
