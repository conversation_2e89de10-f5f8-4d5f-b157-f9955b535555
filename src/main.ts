import { createApp } from 'vue'
import App from './App.vue'
import store from './store'
import router from './router'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'
import ElementPlus from 'element-plus' //引入element-plus库
import 'element-plus/dist/index.css' //引入element-plus样式
import VFormRender from 'hwp-form3/render.umd.js' //引入VFormRender组件
// import 'vform3-builds/dist/render.style.css' //引入VFormRender样式
import '@/icons/iconfont_2502968/iconfont.js'
import '@/icons/iconfont_3584480/iconfont.js'
import MySvgIcon from '@/components/MySvgIcon/index.vue'
// import loadingDirective from '@/utils/Loading/directive.js' // 自定义loading指令
import 'normalize.css'
import '@/styles/global.scss'

// import { getTenantId } from './api/user/auth'

/** TODO：顶级层级获取租户ID，写法对代码规范性不友好，待优化 */
// const domainReg = /(\w+):\/\/([^/:]+)(:\d*)?/
// const domainRegArr = domainReg.exec(window.location.href)
// console.log(domainRegArr)
// const domain = domainRegArr ? domainRegArr[0] : window.location.href
// getTenantId(domain).then(({ data }) => {
//   const { data: resData } = data
//   if (data.success && JSON.stringify(resData) !== '{}') {
//     resData.tenantId = resData.tenantId || '000000'
//     store.dispatch('Auth/changeTenantId', resData.tenantId)
//   } else store.dispatch('Auth/changeTenantId', '000000')
// })

createApp(App)
  .use(store)
  .use(router)
  .use(ElementPlus)
  .use(VFormRender)
  // 当前全局引入 Antd ，生产发布前需要按需引入
  .use(Antd)
  // .directive('loading', loadingDirective)
  .component('MySvgIcon', MySvgIcon)
  .mount('#app')
