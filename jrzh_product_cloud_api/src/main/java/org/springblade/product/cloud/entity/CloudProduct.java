/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.cloud.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.product.common.entity.BaseProduct;

import java.math.BigDecimal;

/**
 * 云信产品表实体类
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Data
@TableName("jrzh_cloud_product")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CloudProduct对象", description = "云信产品表")
public class CloudProduct extends BaseProduct {

    private static final long serialVersionUID = 1L;

    /**
     * 计费方式
     */
    @ApiModelProperty(value = "计费方式")
    private String billingMethod;
    /**
     * 应收账款打折率
     */
    @ApiModelProperty(value = "应收账款打折率")
    private BigDecimal discount;

    /**
     * 保证金支付比例
     */
    @ApiModelProperty(value = "保证金支付比例")
    private BigDecimal bondPayProportion;
    /**
     * 银行卡代收类型
     */
    @ApiModelProperty(value = "银行卡代收类型")
    private Integer bankCardCollectionType;

    /**
     * 融资宽限期限
     */
    @ApiModelProperty(value = "融资宽限期限")
    private Integer financingGraceDay;

    @ApiModelProperty(value = "年利率")
    private BigDecimal annualInterestRate;
}
