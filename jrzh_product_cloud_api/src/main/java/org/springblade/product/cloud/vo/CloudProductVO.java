/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.cloud.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.tool.utils.Func;
import org.springblade.product.cloud.entity.CloudProduct;
import org.springblade.product.common.entity.GoodsOpeningProcess;
import org.springblade.product.common.entity.GoodsQuestion;
import org.springblade.product.common.vo.GoodsLabelRelationVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云信产品表视图实体类
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CloudProductVO对象", description = "云信产品表")
public class CloudProductVO extends CloudProduct {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资金方名称")
    private String capitalName;

    @ApiModelProperty(value = "资金方Logo")
    private String capitalLogo;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "标签id")
    private List<Long> labelIds;

    @ApiModelProperty(value = "产品开通流程")
    private List<GoodsOpeningProcess> goodsOpeningProcesses;

    @ApiModelProperty(value = "产品常见问题")
    private List<GoodsQuestion> goodsQuestions;

    @ApiModelProperty(value = "标签列表")
    private List<GoodsLabelRelationVO> labelList;

    @ApiModelProperty(value = "是否已开通")
    private Boolean openOrClose;


    @ApiModelProperty(value = "授信利率")
    private BigDecimal overdueInterestRate;

    @ApiModelProperty(value = "授信额度id")
    private Long enterpriseId;

    @ApiModelProperty(value = "授信金额")
    private BigDecimal availableAmountStr;

    @ApiModelProperty(value = "过期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "客户产品id")
    private Long customerGoodsId;

    public String getLoanAmountStr() {
        BigDecimal loanAmountStart = getLoanAmountStart();
        BigDecimal loanAmountEnd = getLoanAmountEnd();
        if (Func.hasEmpty(loanAmountStart, loanAmountEnd) ||
                loanAmountStart.compareTo(BigDecimal.ZERO) == 0 ||
                loanAmountEnd.compareTo(BigDecimal.ZERO) == 0) {
            return "";
        }
        return loanAmountStart.toString().concat("~").concat(loanAmountEnd.toString()).concat("万");
    }

    public String getLoadTermStr() {
        Integer loadTermEnd = getLoadTermEnd();
        Integer loadTermStart = getLoadTermStart();
        Integer loadTermUnit = getLoadTermUnit();
        if (Func.hasEmpty(loadTermStart, loadTermEnd, loadTermUnit) ||
                loadTermStart == 0 || loadTermEnd == 0) {
            return "";
        }
        String unit;
        if (loadTermUnit.equals(GoodsEnum.TERM.getCode())) {
            unit = GoodsEnum.TERM.getName();
        } else {
            unit = GoodsEnum.DAY.getName();
        }
        return String.valueOf(loadTermStart).concat("~").concat(String.valueOf(loadTermEnd)).concat(unit);
    }


}
