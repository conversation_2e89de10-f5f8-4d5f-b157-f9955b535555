package org.springblade.loan.strategy;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.expense.handler.ProductExpenseOrderDetailUtils;
import org.springblade.extension.dto.DelayCostCalculusDto;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.strategy.RepaymentPlanFeeCalStrategy;
import org.springblade.loan.utils.LoanUtils;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 还款试算费用策略类 需要填充还款试算时需要填充的字段
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 还款试算策略类（仅是还款计划需要提前生成，实际还款时还需要进行额外计算）
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ReceivableRepaymentPlanFeeCalStrategy implements RepaymentPlanFeeCalStrategy {
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final ProductDirector productDirector;

    @Override
    public ExpenseRuleDTO buildExtensionApply(String req) {
        DelayCostCalculusDto costCalculusDto = JSONUtil.toBean(req, DelayCostCalculusDto.class);
        Integer accumulateLoanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), costCalculusDto.getStartTime(), costCalculusDto.getTotalTerm());
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        BigDecimal interestDay = ProductExpenseOrderDetailUtils.
                getInterestDayByLoanDay(product, accumulateLoanDay);

        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(interestDay)
                .loanDays(BigDecimal.valueOf(costCalculusDto.getOldLoanDays()))
                .loanPeriods(BigDecimal.valueOf(costCalculusDto.getTotalTerm()))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())
                .rolloverDays(BigDecimal.valueOf(costCalculusDto.getTotalTerm()))
                .rolloverPeriods(BigDecimal.valueOf(costCalculusDto.getTotalTerm()))
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(costCalculusDto.getServiceRate()))
                .dayRate(LoanUtils.calculationDayRate(costCalculusDto.getAnnualInterestRate()))
                .surplusPrincipalRepayment(costCalculusDto.getFinanceAmount())
                .overdueDays(BigDecimal.ZERO)
                .prepaymentDays(BigDecimal.ZERO)
                .delayRate(ProductExpenseOrderDetailUtils.percentToBigNum(costCalculusDto.getAnnualInterestRate()))
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(costCalculusDto.getOldLoanRate())).build();
        //填充本期参数
        StagRecordVO stagRecordVO = costCalculusDto.getStagRecordVO();
        if (ObjectUtil.isNotEmpty(stagRecordVO)) {
            Integer loanDay1 = ProductExpenseOrderDetailUtils.getLoanDay(stagRecordVO.getStartTime(), stagRecordVO.getRefundTime());
            //填充本期还款参数
            expenseRuleDTO.setTermSurplusPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(product.getInterestDay() + loanDay1));
            expenseRuleDTO.setCurrentRepaymentAmount(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(loanDay1));
            expenseRuleDTO.setTermOverDueDay(BigDecimal.ZERO);
            expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
        }
        return expenseRuleDTO;
    }

    @Override
    public ExpenseRuleDTO buildAdvanceSettle(String req) {
        LoanInfoDTO loanInfoDTO = JSONUtil.toBean(req, LoanInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfoPlan(loanInfoDTO, expenseRuleDTO);
        if (ObjectUtil.isNotEmpty(loanInfoDTO.getCurrentRepaymentInfoDTO())) {
            //填充期数参数
            fullFieldLoanInfoTermPlan(loanInfoDTO.getCurrentRepaymentInfoDTO(), expenseRuleDTO);
            expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(loanInfoDTO.getCurrentRepaymentInfoDTO().getExpenseRate()));
        }

        return expenseRuleDTO;
    }

    @Override
    public ExpenseRuleDTO buildOverdueRepayment(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfoPlan(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTermPlan(repaymentInfoDTO, expenseRuleDTO);
        return expenseRuleDTO;
    }

    @Override
    public GoodsEnum support() {
        return GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE;
    }


    @Override
    public ExpenseRuleDTO buildFinanceApply(String req) {
        CostCalculusDto costCalculusDto = JSONUtil.toBean(req, CostCalculusDto.class);
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(costCalculusDto.getGoodsId(), costCalculusDto.getUserId(), costCalculusDto.getEnterpriseType());
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), costCalculusDto.getStartTime(), costCalculusDto.getTotalTerm());
        Integer interestDay = ObjUtil.isNotEmpty(product.getInterestDay()) ? product.getInterestDay() + loanDay : loanDay;
        Integer loanPeriods = GoodsEnum.TERM.getCode().equals(costCalculusDto.getLoadTermUnit()) ? costCalculusDto.getTotalTerm() : 1;
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(BigDecimal.valueOf(interestDay))
                .loanDays(BigDecimal.valueOf(loanDay))
                .loanPeriods(BigDecimal.valueOf(loanPeriods))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getServiceRate()))
                .dayRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getDailyInterestRate()))
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getAnnualInterestRate()))
                .overdueDays(BigDecimal.ZERO)
                .prepaymentDays(BigDecimal.ZERO)
                .currentRepaymentAmount(costCalculusDto.getFinanceAmount())
                .surplusPrincipalRepayment(costCalculusDto.getFinanceAmount())
                .build();
        //填充本期参数
        StagRecordVO stagRecordVO = costCalculusDto.getStagRecordVO();
        if (ObjectUtil.isNotEmpty(stagRecordVO)) {
            Integer loanDay1 = ProductExpenseOrderDetailUtils.getLoanDay(stagRecordVO.getStartTime(), stagRecordVO.getRefundTime());
            //填充本期还款参数
            expenseRuleDTO.setTermSurplusPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(product.getInterestDay() + loanDay1));
            expenseRuleDTO.setCurrentRepaymentAmount(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(loanDay1));
            expenseRuleDTO.setTermOverDueDay(BigDecimal.ZERO);
            expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
        }
        return expenseRuleDTO;
    }

    private Product getProductByGoodsId(Long goodsId) {
        return productDirector.detailBase(goodsId);
    }

    private EnterpriseQuota getEnterpriseQuota(Long goodsId, Long userId, Integer enterpriseType) {
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseIdBase(goodsId, enterpriseType, userId);
        return enterpriseQuota;

    }

    @Override
    public ExpenseRuleDTO buildAdvanceRepayment(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfoPlan(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTermPlan(repaymentInfoDTO, expenseRuleDTO);
        expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getExpenseRate()));
        return expenseRuleDTO;
    }

    @Override
    public ExpenseRuleDTO buildNormalRepayment(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfoPlan(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTermPlan(repaymentInfoDTO, expenseRuleDTO);
        expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getExpenseRate()));
        expenseRuleDTO.setRolloverDays(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setRolloverPeriods(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        return expenseRuleDTO;
    }

    /**
     * 填充期数字段 计划
     *
     * @param repaymentInfoDTO
     * @param expenseRuleDTO
     */
    public void fullFieldLoanInfoTermPlan(RepaymentInfoDTO repaymentInfoDTO, ExpenseRuleDTO expenseRuleDTO) {
        expenseRuleDTO.setTermPrincipalRepayment(repaymentInfoDTO.getPrincipal());
        expenseRuleDTO.setCurrentRepaymentAmount(repaymentInfoDTO.getSubPrincipal());
        expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(repaymentInfoDTO.getInterestAccrualDays()));
        expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(repaymentInfoDTO.getLoanDay()));
        expenseRuleDTO.setTermSurplusPrincipalRepayment(repaymentInfoDTO.getSubPrincipal());
        expenseRuleDTO.setTermOverDueDay(BigDecimal.valueOf(repaymentInfoDTO.getOverdueDays()));
        expenseRuleDTO.setTermPrepaymentDays(BigDecimal.valueOf(repaymentInfoDTO.getPrepaymentDays()));
    }

    /**
     * 填充总体借据字段 计划填充
     */
    public void fullFieldLoanInfoPlan(RepaymentInfoDTO repaymentInfoDTO, ExpenseRuleDTO expenseRuleDTO) {
        LoanManageIou loanManageIou = repaymentInfoDTO.getLoanManageIou();
        BigDecimal yearRate = ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getAnnualInterestRate());
        BigDecimal dayRate = LoanUtils.calculationDayRate(yearRate);
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(loanManageIou.getLoanTime(), loanManageIou.getExpireTime());
        expenseRuleDTO.setPrincipalRepayment(loanManageIou.getIouAmount());
        expenseRuleDTO.setLoanPrincipal(loanManageIou.getIouAmount());
//        expenseRuleDTO.setPurchasePrices(null);
        expenseRuleDTO.setSurplusPrincipalRepayment(repaymentInfoDTO.getSubTotalPrincipal());
        expenseRuleDTO.setInterestAccrualDays(BigDecimal.valueOf(loanDay + repaymentInfoDTO.getInterestDay()));
        expenseRuleDTO.setLoanDays(BigDecimal.valueOf(loanDay));
        expenseRuleDTO.setLoanPeriods(BigDecimal.valueOf(repaymentInfoDTO.getTerm()));
        expenseRuleDTO.setPrepaymentDays(BigDecimal.ZERO);
        expenseRuleDTO.setServiceRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getServiceFeeRate()));
        expenseRuleDTO.setDayRate(dayRate);
        expenseRuleDTO.setYearRate(yearRate);
//        expenseRuleDTO.setMarginRatio(null);
        expenseRuleDTO.setTaxRate(BigDecimal.ONE);
        expenseRuleDTO.setOverdueDays(BigDecimal.ZERO);
        //计划时 取剩余总本金
        expenseRuleDTO.setCurrentRepaymentAmount(repaymentInfoDTO.getSubTotalPrincipal());
    }

    @Override
    public ExpenseRuleDTO buildAdjustInterest(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfoPlan(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTermPlan(repaymentInfoDTO, expenseRuleDTO);
        expenseRuleDTO.setRolloverDays(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setRolloverPeriods(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getExpenseRate()));
        return expenseRuleDTO;
    }

    /**
     * 填充总体借据字段 动态填充
     *
     * @param loanInfoDTO    借款信息
     * @param expenseRuleDTO 填充字段
     */
    public void fullFieldLoanInfoPlan(LoanInfoDTO loanInfoDTO, ExpenseRuleDTO expenseRuleDTO) {
        BigDecimal yearRate = ProductExpenseOrderDetailUtils.percentToBigNum(loanInfoDTO.getAnnualInterestRate());
        BigDecimal dayRate = LoanUtils.calculationDayRate(yearRate);
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(loanInfoDTO.getLoanTime(), loanInfoDTO.getExpireTime());
        expenseRuleDTO.setPrincipalRepayment(loanInfoDTO.getIouAmount());
        expenseRuleDTO.setLoanPrincipal(loanInfoDTO.getIouAmount());
//        expenseRuleDTO.setPurchasePrices(null);
        expenseRuleDTO.setSurplusPrincipalRepayment(loanInfoDTO.getSubPrincipal());
        expenseRuleDTO.setInterestAccrualDays(BigDecimal.valueOf(loanDay + loanInfoDTO.getInterestDayStrategy()));
        expenseRuleDTO.setLoanDays(BigDecimal.valueOf(loanDay));
        expenseRuleDTO.setLoanPeriods(BigDecimal.valueOf(loanInfoDTO.getRepaymentInfoList().size()));
        expenseRuleDTO.setPrepaymentDays(BigDecimal.ZERO);
        expenseRuleDTO.setServiceRate(ProductExpenseOrderDetailUtils.percentToBigNum(loanInfoDTO.getServiceFeeRate()));
        expenseRuleDTO.setDayRate(dayRate);
        expenseRuleDTO.setYearRate(yearRate);
//        expenseRuleDTO.setMarginRatio(null);
        expenseRuleDTO.setTaxRate(BigDecimal.ONE);
        expenseRuleDTO.setOverdueDays(BigDecimal.ZERO);
        expenseRuleDTO.setCurrentRepaymentAmount(loanInfoDTO.getSubPrincipal());
    }

    @Override
    public ExpenseRuleDTO buildOverdueConsult(String req) {
        CostCalculusDto costCalculusDto = JSONUtil.toBean(req, CostCalculusDto.class);
        LocalDate startTime = costCalculusDto.getStartTime();
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(costCalculusDto.getGoodsId(), costCalculusDto.getUserId(), costCalculusDto.getEnterpriseType());
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), startTime, costCalculusDto.getTotalTerm());
//        Integer accumulateLoanDay = ProductExpenseOrderDetailUtils.getLoanDay(startTime, LocalDate.now());
        Integer accumulateLoanDay = loanDay;
        BigDecimal interestDay = ProductExpenseOrderDetailUtils.getInterestDayByLoanDay(product, accumulateLoanDay);
        Integer loanPeriods = GoodsEnum.TERM.getCode().equals(costCalculusDto.getLoadTermUnit()) ? costCalculusDto.getTotalTerm() : 1;
        StagRecordVO stagRecordVO = costCalculusDto.getStagRecordVO();
        //填充总体
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(interestDay)
                .loanDays(BigDecimal.valueOf(loanDay))
                .loanPeriods(BigDecimal.valueOf(loanPeriods))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getServiceRate()))
                .dayRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getDailyInterestRate()))
                .surplusPrincipalRepayment(costCalculusDto.getFinanceAmount())
                .overdueDays(BigDecimal.ZERO)
                .prepaymentDays(BigDecimal.ZERO)
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getAnnualInterestRate())).build();
        //填充本期参数
        if (ObjectUtil.isNotEmpty(stagRecordVO)) {
            Integer loanDay1 = ProductExpenseOrderDetailUtils.getLoanDay(stagRecordVO.getStartTime(), stagRecordVO.getRefundTime());
            //填充本期还款参数
            expenseRuleDTO.setTermSurplusPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(product.getInterestDay() + loanDay));
            expenseRuleDTO.setCurrentRepaymentAmount(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(loanDay1));
            expenseRuleDTO.setTermOverDueDay(BigDecimal.ZERO);
            expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
        }
        return expenseRuleDTO;
    }
}
