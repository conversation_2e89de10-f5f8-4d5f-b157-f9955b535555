module.exports = {
  // 行尾是否使用分号
  semi: false,
  // 字符串是否使用单引号
  singleQuote: true,
  // 是否使用尾随逗号，有三个可选值"<none|es5|all>"
  trailingComma: 'es5',
  // 一个tab代表几个空格数
  tabWidth: 2,
  // 是否启用tab缩进
  useTabs: false,
  // 箭头函数参数的括号"<always(始终存在)|avoid(尽可能省略)>"
  arrowParens: 'avoid',
  // 指定代码换行的行长度(默认80)。单行代码宽度超过指定的最大宽度，将会换行，如果都不想换，可以添加 "proseWrap": "never"
  printWidth: 80,
  // 是否在对象属性添加空格
  bracketSpacing: true,
}
