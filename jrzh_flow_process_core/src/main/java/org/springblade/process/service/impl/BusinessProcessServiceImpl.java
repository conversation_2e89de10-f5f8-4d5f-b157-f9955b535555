/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.process.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.flowable.variable.api.history.HistoricVariableInstanceQuery;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.ActExFormNodeTypeEnum;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.ProcessStatusEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.ThreadUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.*;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.process.constant.ProcessResultCode;
import org.springblade.process.dto.BusinessProcessProcessDTO;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.entity.BusinessProcessTaskData;
import org.springblade.process.listener.FrontTaskListener;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.process.service.IBusinessProcessTaskDataService;
import org.springblade.process.vo.BusinessProcessTaskDataVO;
import org.springblade.process.vo.BusinessProcessTaskVO;
import org.springblade.process.vo.GoodsProcessActExFormVO;
import org.springblade.product.common.entity.GoodsProcess;
import org.springblade.product.process_relation.service.IGoodsProcessService;
import org.springblade.product.process_relation.wrapper.GoodsProcessWrapper;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.workflow.core.cache.WfProcessCache;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.core.utils.WfTaskUtil;
import org.springblade.workflow.design.entity.WfForm;
import org.springblade.workflow.design.entity.WfModel;
import org.springblade.workflow.design.service.IActExFormService;
import org.springblade.workflow.design.service.IWfFormService;
import org.springblade.workflow.design.service.IWfModelService;
import org.springblade.workflow.design.vo.ActExFormVO;
import org.springblade.workflow.process.entity.WfCopy;
import org.springblade.workflow.process.model.WfProcess;
import org.springblade.workflow.process.service.IWfCopyService;
import org.springblade.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 业务流程 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Service
@RequiredArgsConstructor
public class BusinessProcessServiceImpl implements IBusinessProcessService {

    private final IWfProcessService wfProcessService;
    private final IWfCopyService copyService;
    private final HistoryService historyService;
    private final RuntimeService runtimeService;
    private final TaskService taskService;
    private final RepositoryService repositoryService;

    private final RemoteUserService remoteUserService;

    private final IBusinessProcessProgressService businessProcessProgressService;

    private final IBusinessProcessTaskDataService businessProcessTaskDataService;
    private final IGoodsProcessService goodsProcessService;
    private final IWfModelService modelService;
    private final IActExFormService actExFormService;
    private final IWfFormService wfFormService;
    @Override
    public IPage<WfProcess> selectBusinessProcessPage(WfProcess wf, Query query) {
        WfProcess wfProcess = new WfProcess();
        IPage<WfProcess> page;
        Integer status = Integer.valueOf(wf.getStatus());
        wfProcess.setProcessNo(wf.getProcessNo());
        wfProcess.setProcessDefinitionName(wf.getProcessDefinitionName());
        wfProcess.setCategory(wf.getCategory());
        wfProcess.setStartUsername(wf.getStartUsername());
        switch (status) {
            case 1:
                // 待办
                wfProcess.setStatus(WfProcessConstant.STATUS_TODO);
                page = wfProcessService.selectTaskPage(wfProcess, query);
                break;
            case 2:
                // 已办 done
                wfProcess.setStatus(WfProcessConstant.STATUS_DONE);
                wfProcess.setProcessIsFinished(WfProcessConstant.STATUS_UNFINISHED);
                page = wfProcessService.selectTaskPage(wfProcess, query);
                break;
            case 3:
                // 已完成 done
                wfProcess.setStatus(WfProcessConstant.STATUS_DONE);
                page = wfProcessService.selectProcessPage(wfProcess, query);
                break;
            case 4:
                // 已终止
                wfProcess.setProcessIsFinished(WfProcessConstant.STATUS_TERMINATE);
                page = wfProcessService.selectProcessPage(wfProcess, query);
                break;
            case 5:
                // 抄送
                page = copyList(query);
                break;
            case 6:
                // 我的请求
                wfProcess.setStatus(WfProcessConstant.STATUS_SEND);
                page = wfProcessService.selectProcessPage(wfProcess, query);
                break;
            default:
                page = Condition.getPage(query);
        }
        page.getRecords().forEach(wfProcess1 -> wfProcess1.setProcessStatus(status));
        return page;
    }

    @Override
    public WfProcess detail(String processInstanceId, Integer processStatus) {
        // 查询流程实例
        CompletableFuture<HistoricProcessInstance> processInstanceFuture = ThreadUtils.supplyAsync(() -> getHistoricProcessInstanceById(processInstanceId));
        // 查询流程变量
        CompletableFuture<Map<String, Object>> variableFuture = ThreadUtils.supplyAsync(() -> getVariables(processInstanceId));
        // 查询流程实例
        CompletableFuture<HistoricTaskInstance> taskInstanceFuture = ThreadUtils.supplyAsync(() -> getHistoricTaskInstanceByProcessInstanceId(processInstanceId));
        // 查询节点实例
        CompletableFuture<HistoricActivityInstance> activityInstanceFuture = ThreadUtils.supplyAsync(() -> wfProcessService.selectCurrentNode(processInstanceId));
        // 阻塞，等待执行完成
        List<?> list = ThreadUtils.allOf(processInstanceFuture, variableFuture, taskInstanceFuture, activityInstanceFuture).join();
        HistoricProcessInstance processInstance = (HistoricProcessInstance) list.get(0);
        Map<String, Object> variables = (Map<String, Object>) list.get(1);
        HistoricTaskInstance taskInstance = (HistoricTaskInstance) list.get(2);
        HistoricActivityInstance activityInstance = (HistoricActivityInstance) list.get(3);

        Assert.isTrue(Objects.nonNull(processInstance), "无法查询到该流程");


        WfProcess process = new WfProcess();

        process.setProcessNo((String) variables.getOrDefault(WfProcessConstant.PROCESS_NO, ""));
        process.setStartUsername((String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_APPLY_USER_NAME, ""));
        process.setCreateTime(processInstance.getStartTime());
        process.setTaskName(getTaskName(taskInstance, processInstance));
        process.setTaskId(taskInstance.getId());
        process.setProcessStatus(processStatus);
        process.setVariables(variables);
        if (Objects.nonNull(activityInstance)) {
            process.setHistoryActivityId(activityInstance.getActivityId());
            User user = remoteUserService.getUserById(WfTaskUtil.getUserId(activityInstance.getAssignee()), FeignConstants.FROM_IN).getData();
            if (user != null) {
                process.setAssignee(activityInstance.getAssignee());
                process.setAssigneeName(user.getName());
            }
        }

        return process;
    }

    @Override
    public String startProcess(String processDefinitionKey, Map<String, Object> variables) {
        ProcessDefinition processDefinition = wfProcessService.selectProcessDefinitionByKey(processDefinitionKey);
        Assert.notNull(processDefinition, "找不到审批流程");
        // 发起流程
        return wfProcessService.startProcessInstanceById(processDefinition.getId(), variables);
    }

    @Override
    public String submitProcess(Integer progress, Map<String, Object> variables) {
        String definitionKey = String.valueOf(variables.getOrDefault(ProcessConstant.DEFINITION_KEY, -1));
        //Integer processType = (Integer) variables.getOrDefault(ProcessConstant.PROCESS_TYPE, -1);
        // 开启流程
        String processInstanceId = startProcess(definitionKey, variables);

        // 更新流程进度
        //TODO 需要重写实现方式
        //businessProcessProgressService.updateBusinessProcessProgress(businessId, progress, processType, processInstanceId, AuthUtil.getUserId(), (Integer) variables.getOrDefault(ProcessConstant.PROCESS_STATUS, 1));
        return processInstanceId;
    }

    @Override
    public String startOrSubmit(String processKey, String processInstanceId, Map<String, Object> variables, Integer processProgress) {
        JSONObject jsonVar = JSONUtil.parseObj(variables);
        // 流程实例id是否存在，存在则直接通过
        if (StringUtil.isNotBlank(processInstanceId)) {
            // 检查审批人是否为当前用户
            Assert.isTrue(startByWith(processInstanceId), "审批人不是当前用户");
            completeTask(processInstanceId, jsonVar);
            businessProcessProgressService.updateStatusAndProgress(processInstanceId, ProcessStatusEnum.APPROVING.getCode(), processProgress);
        } else {
            processInstanceId = startProcess(processKey, jsonVar);
        }
        return processInstanceId;
    }

    @Override
    public void completeTaskAndUpdateProcessProgress(Integer progress, String processInstanceId, Map<String, Object> variables) {
        HistoricTaskInstance historicTaskInstance = wfProcessService.selectCurrentTask(processInstanceId);
        String taskId = historicTaskInstance.getId();
        WfProcess process = new WfProcess();
        process.setTaskId(taskId);
        process.setProcessInstanceId(processInstanceId);
        process.setPass(true);
        process.setVariables(variables);
        wfProcessService.completeTask(process);
        //TODO 重写实现方式
        //businessProcessProgressService.updateStatusAndProgress(processInstanceId, ProcessStatusEnum.APPROVING.getCode(), progress);
    }


    @Override
    public void setVariable(String processInstanceId, String variableKey, JSONObject variable) {
        List<Execution> list = runtimeService.createExecutionQuery().processInstanceId(processInstanceId).list();
        for (Execution execution : list) {
            runtimeService.setVariable(execution.getId(), variableKey, variable);
        }
    }

    @Override
    public void setVariable(String processInstanceId, JSONObject variable) {
        List<Execution> list = runtimeService.createExecutionQuery().processInstanceId(processInstanceId).list();
        for (Execution execution : list) {
            runtimeService.setVariables(execution.getId(), variable);
        }
    }

    @Override
    public GoodsProcessActExFormVO getProcessInfo(Long goodsId, Integer type) {
        GoodsProcess goodsProcess = goodsProcessService.getOne(Wrappers.<GoodsProcess>lambdaQuery()
                .eq(GoodsProcess::getGoodsId, goodsId)
                .eq(GoodsProcess::getProcessType, type));
        if (ObjectUtil.isEmpty(goodsProcess)) {
            throw new ServiceException("产品流程不存在");
        }
        WfModel model = modelService.getOne(Wrappers.<WfModel>lambdaQuery().eq(WfModel::getModelKey, goodsProcess.getProcessKey()).eq(WfModel::getTenantId, AuthUtil.getTenantId()));
        List<ActExFormVO> actExFormVOS = actExFormService.listByModel(model);
        if (CollUtil.isNotEmpty(actExFormVOS)) {
            List<String> tableKeys = actExFormVOS.stream().map(ActExFormVO::getTableKey).collect(Collectors.toList());
            Map<String, String> fromNameMap = wfFormService.list(Wrappers.<WfForm>lambdaQuery().in(WfForm::getFormKey, tableKeys)).stream().collect(Collectors.toMap(WfForm::getFormKey, WfForm::getName));
            for (ActExFormVO actExFormVO : actExFormVOS) {
                actExFormVO.setTableName(fromNameMap.getOrDefault(actExFormVO.getTableKey(), ""));
            }
        }
        GoodsProcessActExFormVO goodsProcessActExFormVO =BeanUtil.copyProperties(goodsProcess,GoodsProcessActExFormVO.class);
        goodsProcessActExFormVO.setActExForms(actExFormVOS);
        goodsProcessActExFormVO.setProcessName(model.getName());
        return goodsProcessActExFormVO;
    }


    private static String getInnerText(String text) {
        Pattern pattern = Pattern.compile("^([$]|[#])([{]([_a-zA-Z0-9]*[a-zA-Z]+[_a-zA-Z0-9]*)[}])$", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            return matcher.group(3);
        }
        return text;
    }

    /**
     * 获取当前前端节点配置 附加页/基础页数据
     *
     * @param actExFormVOList
     * @return
     */
    private ActExFormVO getCurrentActExFormVO(List<ActExFormVO> actExFormVOList) {
        for (ActExFormVO actExFormVO : actExFormVOList) {
            boolean needCheck = ActExFormNodeTypeEnum.ATTACH.getCode().equals(actExFormVO.getNodeType()) || ActExFormNodeTypeEnum.BASE.getCode().equals(actExFormVO.getNodeType());
            return actExFormVO;
        }
        return null;
    }

    /**
     * 追加流程Json
     *
     * @param variable
     * @param oldJson
     * @return
     */
    private Map<String, Object> appendProcessJson(Map<String, Object> variable, String oldJson) {
        Map<String, Object> nowVariable = new HashMap<>();
        if (StringUtil.isNotBlank(oldJson)) {
            nowVariable.putAll(JSONUtil.parseObj(oldJson));
        }
        if (CollUtil.isNotEmpty(variable)) {
            nowVariable.putAll(variable);
        }
        return nowVariable;
    }

    @Override
    public void completeTask(String processInstanceId, Map<String, Object> variables) {
        HistoricTaskInstance historicTaskInstance = wfProcessService.selectCurrentTask(processInstanceId);
        String taskId = historicTaskInstance.getId();
        WfProcess process = new WfProcess();
        process.setTaskId(taskId);
        process.setProcessInstanceId(processInstanceId);
        process.setPass(true);
        process.setVariables(variables);
        wfProcessService.completeTask(process);
    }

    private String getTaskName(HistoricTaskInstance taskInstance, HistoricProcessInstance processInstance) {
        if (Objects.nonNull(taskInstance)) {
            return taskInstance.getName();
        }

        if (processInstance.getEndActivityId() != null) {
            return "结束";
        }
        return "";
    }

    private String getStartUsername(String startUserId) {
        User user = remoteUserService.getUserById(Long.valueOf(startUserId), FeignConstants.FROM_IN).getData();
        return Objects.nonNull(user) ? user.getName() : "";
    }

    @Override
    public Map<String, Object> getVariables(String processInstanceId) {
        List<HistoricVariableInstance> list = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(processInstanceId)
                .list();
        Map<String, Object> variables = Maps.newHashMap();
        list.forEach(variableInstance -> variables.put(variableInstance.getVariableName(), variableInstance.getValue()));
        return variables;
    }

    @Override
    public Object getSingleVariable(String processInstanceId, String variableName) {
        HistoricVariableInstance historicVariableInstance = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(processInstanceId)
                .variableName(variableName)
                .singleResult();
        if (Objects.isNull(historicVariableInstance)) {
            return null;
        }
        return historicVariableInstance.getValue();
    }


    @Override
    public String getProcessNo(String processInstanceId) {
        if (StringUtil.isNotBlank(processInstanceId)) {
            return getSingleVariable(processInstanceId, WfProcessConstant.PROCESS_NO).toString();
        }
        return CodeUtil.generateCode(CodeEnum.PROCESS_NO);
    }

    @Override
    public String selectCommentList(String processInstanceId) {
        List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
        if (CollectionUtils.isEmpty(commentList)) {
            return "";
        }
        return commentList.get(0).getFullMessage();
    }

    @Override
    public String submitProcessNoContainUnValid(Integer progress, Map<String, Object> variables) {
        return null;
    }


//TODO 迁移
//	@Override
//	public void handlerGoodsProcessTerminate(String processInstanceId, Map<String, Object> variables) {
//		Integer type = (Integer) variables.getOrDefault(ProcessConstant.PROCESS_TYPE, -1);
//		if (type == ProcessTypeEnum.APPLY_QUOTA.getCode()
//			|| type == ProcessTypeEnum.QUOTA_ACTIVE.getCode()
//			|| type == ProcessTypeEnum.CORE_AUTONOMY_APPLY_QUOTA.getCode()
//			|| type == ProcessTypeEnum.CORE_AUTONOMY_QUOTA_ACTIVE.getCode()) {
//			Long customerGoodsId = (Long) variables.getOrDefault(ProcessConstant.CUSTOMER_GOODS_ID, -1);
//			Long businessId = (Long) variables.getOrDefault(ProcessConstant.BUSINESS_ID, -1);
//			Long userId = (Long) variables.getOrDefault(ProcessConstant.USER_ID, -1);
//			customerGoodsService.close(customerGoodsId, businessId, processInstanceId, userId, type);
//			//通知风控系统驳回
//			Long ratingRecordId = (Long) variables.get(ProcessConstant.RATING_RECORD_ID);
//			riskmanaApplyService.terminateApplyQuota(ratingRecordId, processInstanceId);
//		}
//	}

    @Override
    public boolean startByWith(String processId) {
        String taskUser = WfTaskUtil.getTaskUser();
        if (StringUtil.isNotBlank(taskUser)) {
            HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery()
                    .startedBy(taskUser)
                    .processInstanceId(processId);
            long count = query.count();
            return count > 0;
        }
        return false;
    }

    @Override
    public String startProcessByDefinitionKey(String definitionKey, Map<String, Object> variables) {
        // 查询部署流程
        List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(definitionKey)
                .orderByProcessDefinitionVersion()
                .desc()
                .processDefinitionTenantId(AuthUtil.getTenantId())
                .list();
        Assert.isTrue(!CollectionUtils.isEmpty(list), "找不到该审批流程");
        //获取流程id 并开启流程
        ProcessDefinition processDefinition = list.get(0);
        //开启流程后获得流程id
        return wfProcessService.startProcessInstanceById(processDefinition.getId(), variables);

    }

    @Override
    public BusinessProcessTaskVO getProcessByProcessInsId(String processInsId) {
        HistoricTaskInstance instance = wfProcessService.selectCurrentTask(processInsId);
        Assert.isTrue(ObjectUtil.isNotEmpty(instance), "流程审批已结束");
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(instance.getProcessDefinitionId())
                .singleResult();
        if (ObjectUtil.isNotEmpty(instance)) {
            BusinessProcessTaskVO vo = new BusinessProcessTaskVO();
            vo.setProcessInsId(instance.getProcessInstanceId());
            vo.setProcessDefinitionKey(getProcessDefinitionKey(instance.getProcessDefinitionId()));
            vo.setTaskId(instance.getId());
            vo.setProcessDefinitionKey(processDefinition.getKey());
            return vo;
        }
        return null;
    }

    private String getProcessDefinitionKey(String key) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .orderByProcessDefinitionVersion()
                .processDefinitionId(key)
                .desc()
                .singleResult();
        return processDefinition.getKey();
    }

    private HistoricProcessInstance getHistoricProcessInstanceById(String processInstanceId) {
        return historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
    }

    private HistoricTaskInstance getHistoricTaskInstanceByProcessInstanceId(String processInstanceId) {
        List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByTaskCreateTime().desc()
                .orderByHistoricTaskInstanceEndTime().desc()
                .list();
        return CollectionUtils.isEmpty(historyTasks) ? null : historyTasks.get(0);
    }

    private IPage<WfProcess> copyList(Query query) {
        IPage<WfCopy> page = copyService.page(Condition.getPage(query), Wrappers.<WfCopy>lambdaQuery().eq(WfCopy::getUserId, Long.valueOf(Objects.requireNonNull(WfTaskUtil.getTaskUser()))).orderByDesc(WfCopy::getCreateTime));
        List<WfProcess> processList = page.getRecords().parallelStream().map(copy -> {

            WfProcess process = new WfProcess();

            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(copy.getProcessId())
                    .singleResult();

            process.setStartUsername(getStartUsername(historicProcessInstance.getStartUserId()));
            process.setCreateTime(historicProcessInstance.getStartTime());
            process.setEndTime(historicProcessInstance.getEndTime());
            process.setProcessInstanceId(historicProcessInstance.getId());

            HistoricVariableInstanceQuery variableInstanceQuery = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(historicProcessInstance.getId());
            HistoricVariableInstance sn = variableInstanceQuery.variableName(WfProcessConstant.PROCESS_NO).singleResult();
            if (ObjectUtil.isNotEmpty(sn)) {
                process.setProcessNo(sn.getValue().toString());
            }
            ProcessDefinition processDefinition = WfProcessCache.getProcessDefinition(historicProcessInstance.getProcessDefinitionId());
            process.setProcessDefinitionId(processDefinition.getId());
            process.setProcessDefinitionName(processDefinition.getName());
            process.setProcessDefinitionKey(processDefinition.getKey());
            process.setCategory(processDefinition.getCategory());

            List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(historicProcessInstance.getId())
                    .orderByTaskCreateTime().desc()
                    .orderByHistoricTaskInstanceEndTime().desc()
                    .list();
            if (Func.isNotEmpty(historyTasks)) {
                HistoricTaskInstance historyTask = historyTasks.iterator().next();
                process.setTaskId(historyTask.getId());
                process.setTaskName(historyTask.getName());
            }

            if (historicProcessInstance.getEndActivityId() != null) {
                process.setTaskName("结束");
            }

            return process;

        }).collect(Collectors.toList());

        IPage<WfProcess> result = Condition.getPage(query);
        result.setTotal(page.getTotal());
        result.setRecords(processList);
        return result;
    }

    private boolean processCanEdit(BusinessProcessProgress old) {
        return ProcessStatusEnum.PROCESS_OPEN.getCode() == old.getStatus() || ProcessStatusEnum.REJECT.getCode() == old.getStatus();
    }


    /**
     * 更新节点信息 存在则赋予旧id 并更新信息
     *
     * @param taskDataVO
     * @param oldData
     * @return
     */
    private BusinessProcessTaskDataVO updateTaskData(BusinessProcessTaskDataVO taskDataVO, BusinessProcessTaskDataVO oldData) {
        if (ObjectUtil.isNotEmpty(taskDataVO)) {
            //获取原id 若存在
            taskDataVO.setId(ObjectUtil.isNotEmpty(oldData) ? oldData.getId() : null);
            Map<String, Object> taskVariable = taskDataVO.getVariable();
            taskDataVO.setNodeJson(CollUtil.isNotEmpty(taskVariable) ? JSONUtil.toJsonStr(taskVariable) : "");
            taskDataVO.setVariable(taskVariable);
        }
        return taskDataVO;
    }

    private void submitProcess(BusinessProcessProcessDTO businessProcessProcessDTO, Integer oldProgress, String processInstanceId, Map<String, Object> nowVariables) {
        if (businessProcessProcessDTO.getProgress() <= oldProgress) {
            throw new ServiceException("流程进度设置错误");
        }
        businessProcessProcessDTO.setStatus(ProcessStatusEnum.APPROVING.getCode());
        if (StringUtil.isNotBlank(processInstanceId)) {
            Map<String, Object> variables = getVariables(processInstanceId);
            variables.putAll(nowVariables);
            completeTask(processInstanceId, variables);
        } else {
            String processNo = businessProcessProcessDTO.getProcessNo();
            nowVariables.put(WfProcessConstant.PROCESS_NO, ObjectUtil.isNotEmpty(processNo) ? processNo : CodeUtil.generateCode(CodeEnum.PROCESS_NO));
            String processId = startProcessByDefinitionKey(businessProcessProcessDTO.getModelKey(), nowVariables);
            businessProcessProcessDTO.setProcessInstanceId(processId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessProcessProcessDTO saveOrUpdateByProcess(BusinessProcessProcessDTO businessProcessProcessDTO) {
        Integer oldProgress = null;
        String processInstanceId = businessProcessProcessDTO.getProcessInstanceId();
        Map<String, Object> variable = businessProcessProcessDTO.getVariable();
        /*K:节点名称 v:原id 若流程存在:1、校验状态是否可修改 2、追加原流程变量 3、赋予旧流程信息 不存在:设为流程开启*/
        Map<String, BusinessProcessTaskDataVO> oldDataMap = new HashMap<>();
        BusinessProcessProgress old = businessProcessProgressService
                .getValidByBusinessIdAndType(businessProcessProcessDTO.getBusinessId(), businessProcessProcessDTO.getType(), businessProcessProcessDTO.getUserId());
        if (ObjectUtil.isNotEmpty(old)) {
            //查看当前流程状态是否可修改
            if (!processCanEdit(old)) {
                throw new ServiceException(ProcessResultCode.NO_OPERATION);
            }
            //追加原流程变量
            variable = appendProcessJson(variable, old.getProcessJson());
            List<BusinessProcessTaskDataVO> businessProcessTaskData = businessProcessProgressService.listBusinessProcessTaskData(old.getId());
            if (CollUtil.isNotEmpty(businessProcessTaskData)) {
                oldDataMap = businessProcessTaskData.stream().collect(Collectors.toMap(BusinessProcessTaskDataVO::getNodeKey, e -> e));
            }
            oldProgress = old.getProgress();
            processInstanceId = old.getProcessInstanceId();
            //基础信息回填
            businessProcessProcessDTO.setBusinessId(old.getBusinessId());
            businessProcessProcessDTO.setType(old.getType());
            businessProcessProcessDTO.setId(old.getId());
        } else {
            variable = CollUtil.isNotEmpty(businessProcessProcessDTO.getVariable()) ? businessProcessProcessDTO.getVariable() : new HashMap<>();
            businessProcessProcessDTO.setStatus(ProcessStatusEnum.PROCESS_OPEN.getCode());
        }
        businessProcessProcessDTO.setVariable(variable);
        /*1、查询当前节点配置 循环执行当前所有节点的完成监听,若当前节点为提交节点 执行流程提交
          2、获取下一个节点配置 循环执行下一个节点所有节点的创建监听*/
        //查找当前流程配置 k:进度 v:进度上所有任务
        List<ActExFormVO> actExFormVOList = actExFormService.listByModelKey(businessProcessProcessDTO.getModelKey());
        Map<Integer, List<ActExFormVO>> actExFormVoMap = actExFormVOList.stream().collect(Collectors.groupingBy(ActExFormVO::getProgress));
        //若流程未开启 则使用第一个节点作为当前流程进度
        if (ObjectUtil.isEmpty(oldProgress)) {
            oldProgress = actExFormVOList.get(0).getProgress();
        }
        List<ActExFormVO> currentAllExForm = actExFormVoMap.get(oldProgress);
        //判断是否需要提交流程 开启则提交 否则重新提交
        ActExFormVO currentActExFormVO = getCurrentActExFormVO(currentAllExForm);
        if (ObjectUtil.isEmpty(currentActExFormVO)) {
            throw new ServiceException(ProcessResultCode.INVALID_APPLY);
        }
        List<BusinessProcessTaskDataVO> businessProcessTaskData = businessProcessProcessDTO.getBusinessProcessTaskData();
        List<BusinessProcessTaskDataVO> newProcessTaskData = new ArrayList<>(businessProcessTaskData.size());
        final Integer finalOldProgress = oldProgress;
        //k:节点key 节点信息
        Map<String, BusinessProcessTaskDataVO> businessProcessTaskDataMap = businessProcessTaskData.stream().collect(Collectors.toMap(BusinessProcessTaskData::getNodeKey, e -> e));
        //循环当前节点配置 并执行节点监听
        for (ActExFormVO actExFormVO : currentAllExForm) {
            String keyName = actExFormVO.getKeyName();
            BusinessProcessTaskDataVO taskDataVO = businessProcessTaskDataMap.get(keyName);
            businessProcessProcessDTO.setCommit(CommonConstant.YES.equals(actExFormVO.getIzCommit()));
            //任务监听执行
            String taskListener = actExFormVO.getTaskListener();
            if (StringUtil.isNotBlank(taskListener)) {
                String[] taskListeners = taskListener.split(StringPool.NEWLINE);
                for (String e : taskListeners) {
                    SpringUtil.getBean(getInnerText(e), FrontTaskListener.class).complete(businessProcessProcessDTO, taskDataVO);
                }
            }
            //保存节点信息
            if (ObjectUtil.isNotEmpty(taskDataVO)) {
                newProcessTaskData.add(updateTaskData(taskDataVO, oldDataMap.get(keyName)));
            }
        }
        //流程提交
        if (businessProcessProcessDTO.isPass() && CommonConstant.YES.equals(currentActExFormVO.getIzCommit())) {
            submitProcess(businessProcessProcessDTO, oldProgress, processInstanceId, businessProcessProcessDTO.getVariable());
        }
        //若下一个节点存在 则执行下一个节点监听
        List<ActExFormVO> nextActForm = actExFormVoMap.get(businessProcessProcessDTO.getProgress());
        if (CollUtil.isNotEmpty(nextActForm)) {
            for (ActExFormVO actExFormVO : nextActForm) {
                String keyName = actExFormVO.getKeyName();
                BusinessProcessTaskDataVO taskDataVO = oldDataMap.get(keyName);
                //任务监听执行
                String taskListener = actExFormVO.getTaskListener();
                if (StringUtil.isNotBlank(taskListener)) {
                    String[] taskListeners = taskListener.split(StringPool.NEWLINE);
                    for (String e : taskListeners) {
                        SpringUtil.getBean(getInnerText(e), FrontTaskListener.class).created(businessProcessProcessDTO, taskDataVO);
                    }
                }
                //保存节点信息
                if (ObjectUtil.isNotEmpty(taskDataVO)) {
                    newProcessTaskData.add(updateTaskData(taskDataVO, oldDataMap.get(keyName)));
                }
            }
        }

        /*执行任务完成 重新设置流程的最新变量 修改当前流程或开启流程*/
        //设置最新流程变量
        variable = businessProcessProcessDTO.getVariable();
        if (CollUtil.isNotEmpty(variable)) {
            businessProcessProcessDTO.setProcessJson(JSONUtil.toJsonStr(variable));
            businessProcessProcessDTO.setVariable(variable);
        }
        //保存业务节点数据 保存流程数据
        businessProcessProgressService.saveOrUpdate(businessProcessProcessDTO);
        if (CollUtil.isNotEmpty(newProcessTaskData)) {
            for (BusinessProcessTaskDataVO e : newProcessTaskData) {
                e.setProcessId(businessProcessProcessDTO.getId());
                businessProcessTaskDataService.saveOrUpdate(e);
            }
        }
        businessProcessProcessDTO.setBusinessProcessTaskData(newProcessTaskData);
        return businessProcessProcessDTO;
    }

}
