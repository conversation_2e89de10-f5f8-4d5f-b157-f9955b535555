/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.refund.service;

import org.springblade.otherapi.core.dto.PayGoodsOrderRefundNotify;
import org.springblade.refund.dto.RefundActualDTO;
import org.springblade.refund.dto.RefundDTO;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.entity.RefundActual;

import java.util.List;

/**
 * 退款订单 服务类
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
public interface IRefundBizService {
    /***
     * 退款信息
     * @param refundActualDTO
     */
    Boolean saveRefund(RefundActualDTO refundActualDTO);

    /**
     * 撤销退款订单
     *
     * @param id 退款id
     * @return
     */
    Refund cancelRefund(Long id);

    /**
     * 发起退款
     */
    RefundDTO applyRefund(Refund refund, RefundActual refundActual);

    /**
     * 接受退款异步回调
     *
     * @param refundNotify
     * @return
     */
    String refundNotify(PayGoodsOrderRefundNotify refundNotify, Long refundActualId);

    /**
     * 退款成功
     * @param refundActual
     * @param refund
     */
    void passRefund(RefundActual refundActual, Refund refund);

    /**
     * 退款失败
     * @param refundActual
     * @param refund
     */
    void failRefund(RefundActual refundActual, Refund refund);
    /**
     * 查询所有退款中的订单
     *
     * @return
     */
    List<RefundActual> listUnPayOrder();

    void save(Refund refund);
    void refundSave(Refund refund);
}
