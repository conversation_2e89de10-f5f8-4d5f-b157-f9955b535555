/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.refund.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.refund.entity.Refund;

/**
 * 退款订单 服务类
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
public interface IRefundBaseService extends BaseService<Refund> {

    /***
     * 新增Refund 新增退款费用
     * @param refund 新增退款费用
     */
    boolean refundSave(Refund refund);

    /**
     * 更新状态
     *
     * @param id
     * @param status
     * @return
     */
    boolean refundUpdateStatus(Long id, Integer status);

}
