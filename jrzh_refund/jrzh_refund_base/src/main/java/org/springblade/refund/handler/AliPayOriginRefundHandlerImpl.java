package org.springblade.refund.handler;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.bill.BillPayNameEnum;
import org.springblade.otherapi.core.dto.PayRefundDTO;
import org.springblade.othersapi.paymanager.dto.PayRefundOrderReturn;
import org.springblade.othersapi.paymanager.service.IPayManagerService;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.entity.RefundActual;
import org.springblade.resource.service.IParamService;
import org.springframework.stereotype.Service;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-01  14:49
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class AliPayOriginRefundHandlerImpl implements OriginRefundHandler {
    private final IPayManagerService payManagerService;
    private final IParamService paramService;

    @Override
    public BillPayNameEnum support() {
        return BillPayNameEnum.ALIPAY_WAP;
    }

    @Override
    public void refundCreate(Refund refund, RefundActual refundActual) {
        String SYS_HOST = paramService.getValue("SYS_NOTIFY_HOST");
        PayRefundDTO payRefundDTO = new PayRefundDTO();
        //退所有款
        payRefundDTO.setRefundAmount("");
        payRefundDTO.setDesc(refundActual.getRemark());
        payRefundDTO.setOrderNo(refund.getBillExpenseNo());
        payRefundDTO.setNotifyUrl(String.format(REFUND_NOTIFY_URL_FORMAT, SYS_HOST, refundActual.getId()));
        PayRefundOrderReturn refundOrderReturn = payManagerService.refund(payRefundDTO);
        refundActual.setPayRefundOrderNo(refundOrderReturn.getPayRefundOrderNo());
    }

    @Override
    public Integer refundQueryStatus(RefundActual refundActual) {
        PayRefundOrderReturn refund = payManagerService.getRefund(refundActual.getPayRefundOrderNo());
        return refund.getStatus();
    }
}
