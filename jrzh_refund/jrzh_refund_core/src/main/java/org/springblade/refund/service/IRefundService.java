/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.refund.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.refund.dto.RefundActualDTO;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.vo.RefundAndBilExpenseOrderVo;
import org.springblade.refund.vo.RefundVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 退款订单 服务类
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
public interface IRefundService extends BaseService<Refund> {

    /**
     * 自定义分页
     *
     * @param page
     * @param refund
     * @return
     */
    IPage<RefundVO> selectRefundPage(IPage<RefundVO> page, RefundVO refund);

    /***
     *  获取退款详情信息 费用
     * @param toLongList
     * @return
     */
    RefundAndBilExpenseOrderVo refundDetail(Long toLongList);

    /***
     * 新增Refund 新增退款费用
     * @param refund 新增退款费用
     */
    boolean refundSave(Refund refund);

    /***
     *
     * @param query
     * @param ratingRecord
     * @return
     */
    IPage<RefundVO> refundIPage(Query query, Map<String, Object> ratingRecord);

    /***
     * 退款信息
     * @param refundActualDTO
     */
    Boolean saveRefund(RefundActualDTO refundActualDTO);

    /**
     * @param billExpenseNoList 原订单号集合
     * @param userIds           融资企业用户ID 集合
     * @param refundAmount      退款金额 集合
     * @param bondRefundType    退款方式 集合 1-同还款比例等比释放 2- 结清释放
     * @param cashDepositRate   释放比例
     * @return
     */
    boolean refundSaveList(List<String> billExpenseNoList, List<Long> userIds, List<BigDecimal> refundAmount, List<Integer> bondRefundType, List<BigDecimal> cashDepositRate);

    /**
     * 查询退款详情
     *
     * @param id id
     * @return RefundAndBilExpenseOrderVo
     */
    RefundAndBilExpenseOrderVo refundParticulars(Long id);
}
