/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pledge.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.finance.vo.FinanceApplyVO;
import org.springblade.pledge.entity.PledgeFinance;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.warehouse.vo.WarehouseDetailsVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 质押融资表视图实体类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PledgeFinanceVO对象", description = "质押融资表")
public class PledgeFinanceVO extends PledgeFinance {
    private static final long serialVersionUID = 1L;

    /**
     * 融资信息
     */
    private FinanceApplyVO financeApply;

    /**
     * 资方名称
     */
    private String capitalName;

    /**
     * 资方logo
     */
    private String capitalAvatar;

    /**
     * 保证金释放方式
     */
    private Integer bondReleaseMode;


    /**
     * 实际入库信息
     */
    private List<WarehouseDetailsVO> warehouseDetailsVOList;

    /**
     * 实际质押货物信息
     */
    private List<PledgeCommodityVO> pledgeCommodityVOS;

    /**
     * 额度货物信息
     */
    private List<PurchaseCommodity> purchaseCommodityList;


    /**
     * 动产质押产品
     */
    private AgentGoods agentGoods;

    /**
     * 综合年利率
     */
    private BigDecimal totalAnnualInterestRate;

    /**
     * 综合日利率
     */
    private BigDecimal totalDailyInterestRate;

}
