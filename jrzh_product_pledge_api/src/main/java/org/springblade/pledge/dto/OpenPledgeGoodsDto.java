package org.springblade.pledge.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.procurement.finance.entity.PurchaseCommodity;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 动产质押产品开通
 * <AUTHOR>
 */
@Data
public class OpenPledgeGoodsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "产品id")
	private Long goodsId;

	@ApiModelProperty("授信表单")
	private String creditForm;

	@ApiModelProperty(value = "客户补充资料json值")
	private String data;

	@ApiModelProperty("补充资料")
	private String supplementMaterial;

	@ApiModelProperty("产品类型")
	private Integer goodsType;

	@ApiModelProperty("流程步骤")
	private Integer progress;

	@ApiModelProperty("流程类型")
	private Integer type;

	@ApiModelProperty("企业类型")
	private Integer enterpriseType;

	private String processInstanceId;

	@ApiModelProperty("合同id")
	private List<String> contractIdList;

	@ApiModelProperty("绑定账户id")
	private Long customerBankId;

	@ApiModelProperty("代采商品集合")
	private List<PurchaseCommodity> purchaseCommodityList;

}
