/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pledge.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springblade.customer.entity.BankCard;
import org.springblade.pledge.entity.PledgeCommodity;
import org.springblade.redeem.entity.CargoSolveExpense;
import org.springblade.redeem.vo.CargoSolveVO;
import org.springblade.resource.entity.Attach;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 货物处置记录表视图实体类
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CargoSolveVO对象", description = "货物处置记录表")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PledgeCargoSolveVO2 extends CargoSolveVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货物处置总额  货物处置费用 + 保证金")
    private BigDecimal cargoSolveTotal;

    @ApiModelProperty(value = "产品名称")
    private String goodName;

    @ApiModelProperty(value = "产品类型")
    private Integer goodType;

    @ApiModelProperty(value = "赎货日")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "放款日")
    private LocalDate loanTime;

    @ApiModelProperty(value = "融资金额")
    private BigDecimal financeAmount;

    @ApiModelProperty(value = "本金")
    private BigDecimal principal;

    @ApiModelProperty(value = "产品图片")
    private String productImg;

    @ApiModelProperty(value = "退款账户")
    private BankCard bankCard;

    @ApiModelProperty(value = "年利率")
    private BigDecimal annualInterestRate;

    /**
     * 处置商品信息
     */
    List<PledgeCommodity> pledgeCommodities;

    /**
     * 附件信息
     */
    List<Attach> attachList;

    /**
     * 处置费用信息
     */
    List<CargoSolveExpense> cargoSolveExpenses;

    @Override
    public BigDecimal getCargoSolveTotal() {
        if (Objects.isNull(this.getBond())) {
            this.setBond(BigDecimal.ZERO);
        }
        if (Objects.isNull(this.getCargoSolveAmount())) {
            this.setCargoSolveAmount(BigDecimal.ZERO);
        }
        return this.getBond().add(this.getCargoSolveAmount());
    }
}
