
package org.springblade.procurement.finance.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.procurement.finance.entity.PurchaseCommodity;

import java.time.LocalDate;

/**
 * 代采--采购商品视图实体类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PurchaseCommodityVO对象", description = "代采--采购商品")
public class PurchaseCommodityVO extends PurchaseCommodity {
    private static final long serialVersionUID = 1L;
    /**
     * 下标
     */
    private Integer index;
    private String companyName;
    private LocalDate deliveryTime;
}
