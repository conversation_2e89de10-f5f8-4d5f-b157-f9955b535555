/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.finance.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.entity.PurchaseInformation;

import java.util.List;


/**
 * 代采---基础信息视图实体类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PurchaseInformationVO对象", description = "代采---基础信息")
public class PurchaseInformationVO extends PurchaseInformation {
    private static final long serialVersionUID = 1L;

    /**
     * 代采--采购商品 集合
     */
    private List<PurchaseCommodity> purchaseCommodityList;

    /**
     * 融资申请数据（主数据）
     */
    private FinanceApply financeApply;

    /**
     * 补充资料
     */
    private CustomerMaterial customerMaterial;

    /**
     * 资方名称
     */
    private String capitalName;

    /**
     * 资方logo
     */
    private String capitalAvatar;

    /**
     * 保证金释放方式
     */
    @ApiModelProperty(value = "保证金释放方式")
    private Integer bondReleaseMode;
    /**
     * 费用详情
     */
    private List<ExpenseOrderDetail> expenseOrderDetails;
}
