/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.product.common.entity.GoodsOpeningProcess;
import org.springblade.product.common.entity.GoodsQuestion;
import org.springblade.product.common.entity.GoodsTiming;
import org.springblade.product.common.vo.LabelVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 代采产品表视图实体类
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AgentGoodsVO对象", description = "代采产品表")
public class AgentGoodsVO extends AgentGoods {
	private static final long serialVersionUID = 1L;


	private String capitalLogo;

	private String capitalName;

	private String operator;
	/**
	 * 客户产品状态，1-待申请额度，2-未激活，3-可融资，4-开通失败，5-已过期，6-已禁用，7-额度调整
	 */
	@ApiModelProperty(value = "客户产品状态内容")
	private String customerGoodsStatusStr;

	@ApiModelProperty(value = "当前可用额度，单位万元，需转化为元")
	private BigDecimal currentAvailableAmount;

	@ApiModelProperty(value = "产品年利率")
	private String goodsAnnualInterestRateStr;

	@ApiModelProperty(value = "产品到期日")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private LocalDateTime goodsExpireTime;


	/**
	 * 产品日利率
	 */
	@ApiModelProperty(value = "产品日利率")
	private String goodsDailyInterestRateStr;

	@ApiModelProperty(value = "产品开通流程")
	private List<GoodsOpeningProcess> goodsOpeningProcesses;

	@ApiModelProperty(value = "产品常见问题")
	private List<GoodsQuestion> goodsQuestions;

	@ApiModelProperty(value = "标签列表")
	private List<LabelVO> labelList;

	@ApiModelProperty(value = "定时器管理")
	private List<GoodsTiming> goodsTimingList;

	@ApiModelProperty(value = "标签id")
	private List<Long> labelIds;

	@ApiModelProperty(value = "客户产品id")
	private Long customerGoodsId;

	@ApiModelProperty("保证金比例")
	private BigDecimal bondProportion;

	@ApiModelProperty("最高融资比例")
	private BigDecimal financingProportion;

	public String getLoanAmountStr() {
		BigDecimal loanAmountStart = getLoanAmountStart();
		BigDecimal loanAmountEnd = getLoanAmountEnd();
		if (Func.hasEmpty(loanAmountStart,loanAmountEnd) ||
			loanAmountStart.compareTo(BigDecimal.ZERO) == 0 ||
			loanAmountEnd.compareTo(BigDecimal.ZERO) == 0) {
			return "";
		}
		return loanAmountStart.toString().concat("~").concat(loanAmountEnd.toString()).concat("万");
	}

	public String getLoadTermStr() {
		Integer loadTermEnd = getLoadTermEnd();
		Integer loadTermStart = getLoadTermStart();
		Integer loadTermUnit = getLoadTermUnit();
		if (Func.hasEmpty(loadTermStart,loadTermEnd,loadTermUnit) ||
			loadTermStart == 0 || loadTermEnd == 0) {
			return "";
		}
		String unit;
		if (loadTermUnit.equals(GoodsEnum.TERM.getCode())) {
			unit = GoodsEnum.TERM.getName();
		}else {
			unit = GoodsEnum.DAY.getName();
		}
		return String.valueOf(loadTermStart).concat("~").concat(String.valueOf(loadTermEnd)).concat(unit);
	}
	/**
	 * 可开通标识
	 */
	private Boolean canOpenStatus;
}
