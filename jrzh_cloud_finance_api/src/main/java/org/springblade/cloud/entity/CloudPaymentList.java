/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 云信付款列表实体类
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@Data
@TableName("jrzh_cloud_payment_list")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CloudPaymentList对象", description = "云信付款列表")
public class CloudPaymentList extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 还款单号
	*/
		@ApiModelProperty(value = "还款单号")
		private String repaymentNumber;
	/**
	* 云信编号
	*/
		@ApiModelProperty(value = "云信编号")
		private String cloudCode;
	/**
	* 还款方名称
	*/
		@ApiModelProperty(value = "还款方名称")
		private String repaymentName;
	/**
	* 还款方id
	*/
		@ApiModelProperty(value = "还款方id")
		private Long repaymentId;
	/**
	* 收款方名称
	*/
		@ApiModelProperty(value = "收款方名称")
		private String beneficiaryName;
	/**
	* 收款方id
	*/
		@ApiModelProperty(value = "收款方id")
		private Long beneficiaryId;
	/**
	* 应还金额
	*/
		@ApiModelProperty(value = "应还金额")
		private BigDecimal amountDue;
	/**
	* 实还金额
	*/
		@ApiModelProperty(value = "实还金额")
		private BigDecimal actualAmount;
	/**
	* 还款日期
	*/
		@ApiModelProperty(value = "还款日期")
		private LocalDateTime  endDate;
	/**
	* 还款类型;1、按时还款 2、逾期还款
	*/
		@ApiModelProperty(value = "还款类型;1、按时还款 2、逾期还款")
		private Integer type;
	/**
	* 付款凭证
	*/
		@ApiModelProperty(value = "付款凭证")
		private String cloudAttachId;
	/**
	 * 操作时间
	 */
		@ApiModelProperty(value = "操作时间")
		private LocalDateTime  operateDate;

	/**
	 * 操作人
	 */
		@ApiModelProperty(value = "操作人")
		private Long operateId;

	/**
	 * 失败原因
	 */
		@ApiModelProperty(value = "失败原因")
		private String failReason;

	/**
	 * 开单企业融资付息总计
	 */
	@ApiModelProperty(value = "开单企业融资付息总计")
	private BigDecimal costDifference;

}
