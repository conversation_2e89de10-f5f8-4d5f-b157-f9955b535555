/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 云信轨迹记录表实体类
 *
 * <AUTHOR>
 * @since 2022-05-21
 */
@Data
@TableName("jrzh_cloud_track_record")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CloudTrackRecord对象", description = "云信轨迹记录表")
public class CloudTrackRecord extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 父主键
	*/
		@ApiModelProperty(value = "父主键")
		private Long parentId;
	/**
	* 收单企业名称
	*/
		@ApiModelProperty(value = "收单企业名称")
		private String holdName;
	/**
	* 开单企业名称
	*/
		@ApiModelProperty(value = "开单企业名称")
		private String billName;
	/**
	* 云信金额
	*/
		@ApiModelProperty(value = "云信金额")
		private BigDecimal amount;
	/**
	* 核心企业云信编号
	*/
		@ApiModelProperty(value = "核心企业云信编号")
		private String cloudCoreCode;
	/**
	* 云信编号
	*/
		@ApiModelProperty(value = "云信编号")
		private String cloudCode;
	/**
	* 下级云信编号;根据云信编号查询出 下级云信轨迹记录
	*/
		@ApiModelProperty(value = "下级云信编号;根据云信编号查询出 下级云信轨迹记录")
		private String nextCloudCode;
	/**
	* 开单时间
	*/
		@ApiModelProperty(value = "开单时间")
		private LocalDate startDate;
	/**
	* 承若付款日
	*/
		@ApiModelProperty(value = "承若付款日")
		private LocalDate endDate;
	/**
	* 用户id
	*/
		@ApiModelProperty(value = "用户id")
		private Long userId;
	/**
	* 上级用户操作记录
	*/
		@ApiModelProperty(value = "上级用户操作记录")
		private String content;
	/**
	 * 云信类型：0、转让 1、云信融资
 	 */
		@ApiModelProperty(value = "云信类型")
		private Integer type;


}
