package org.springblade.cloud.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

@Data
@TableName("jrzh_cloud_financing_core_enterprise_payment")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CloudFinancingCoreEnterprisePayment对象", description = "云信融资开单企业付息表")
public class CloudFinancingCoreEnterprisePayment extends TenantEntity {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "融资利息费用总金额")
    BigDecimal cloudBillAmount;
    @ApiModelProperty(value = "融资金额")
    BigDecimal financingMoney;
    @ApiModelProperty(value = "云信编号")
    String cloudCode;
    @ApiModelProperty(value = "云信产品名称")
    String cloudProductName;
    @ApiModelProperty(value = "融资编号")
    String financingNo;
    @ApiModelProperty(value = "融资付息差值")
    BigDecimal costDifference;
    @ApiModelProperty(value = "核心企业云信编号")
    String cloudCoreCode;
}
