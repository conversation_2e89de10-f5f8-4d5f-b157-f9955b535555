package org.springblade.cloud.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.loan.dto.RepaymentCalculationDTO;

@Data
@EqualsAndHashCode
public class CloudRepaymentCalculationDTO extends RepaymentCalculationDTO {
    @ApiModelProperty("融资模式")
    private Integer financingModel;
    @ApiModelProperty("额度id")
    private Long enterpriseQuotaId;

}
