/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.vo;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import org.springblade.common.enums.CloudFinanceStatusEnum;
import org.springblade.customer.entity.CustomerBusinessInvoice;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.resource.entity.Attach;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 云信融资表视图实体类
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Data
@ApiModel(value = "CloudFinancingDetailVO", description = "云信融资表")
@Builder
public class CloudFinancingDetailVO {

    /**
     * 融资编号
     */
    private String financingNo;

    /**
     * 申请金额
     */
    private BigDecimal financingMoney;

    /**
     * 云信编号
     */
    private String cloudCode;

    /**
     * 开单企业
     */
    private String companyName;

    /**
     * 持单日期
     */
    private LocalDate startDate;

    /**
     * 承诺付款日
     */
    private LocalDate endDate;

    /**
     * 账期
     */
    private Integer paymentDays;

    /**
     * 融资付息模式
     */
    private Integer financingModel;

    /**
     * 融资年利率
     */
    private BigDecimal financingApr;


    /**
     * 需转让云信金额
     */
    private BigDecimal cloudBillAmount;

    /**
     * 融资期限
     */
    private Integer financingDays;

    /**
     * 还款方式
     */
    private String repaymentMode;


    /**
     * 合同
     */
    private List<Attach> contract;

    /**
     * 发票
     */
    private List<CustomerBusinessInvoice> invoice;

    /**
     * 申请时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private String applyUser;

    /**
     * 资方名称
     */
    private String capitalName;

    /**
     * 资方图片
     */
    private String capitalImg;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品Id
     */
    private Long productId;

    /**
     * 核心企业id
     */
    private Long coreCompanyId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 费用列表
     */
    private List<ExpenseOrderDetail> platformExpensesList;


    /**
     * 流程ID
     */
    private String processInstanceId;

    /**
     * 核心企业云信编号
     *
     * @return
     */
    private String cloudRoreCode;

    public String getStatusStr() {
        return CloudFinanceStatusEnum.getValueByKey(status);
    }
}
