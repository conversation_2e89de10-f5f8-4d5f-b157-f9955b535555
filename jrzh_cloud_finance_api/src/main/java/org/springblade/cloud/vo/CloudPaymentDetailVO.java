/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.cloud.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.cloud.entity.CloudPaymentDetail;

import java.math.BigDecimal;

/**
 * 云信付款明细表视图实体类
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CloudPaymentDetailVO对象", description = "云信付款明细表")
public class CloudPaymentDetailVO extends CloudPaymentDetail {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "应收方")
	private String companyName;
	@ApiModelProperty(value = "开户银行")
	private String bankDeposit;
	@ApiModelProperty(value = "银行卡号")
	private String bankCardNo;
	@ApiModelProperty(value = "开单付息金额")
	private BigDecimal costDifference;
}
