/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.timejob.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.timejob.entity.JobRun;
import org.springblade.timejob.vo.JobRunVO;

/**
 * 触发器 服务类
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
public interface IJobRunService extends BaseService<JobRun> {

    /**
     * 自定义分页
     *
     * @param page
     * @param jobRun
     * @return
     */
    IPage<JobRunVO> selectJobRunPage(IPage<JobRunVO> page, JobRunVO jobRun);

    R saveJob(JobRun jobRun);
}
