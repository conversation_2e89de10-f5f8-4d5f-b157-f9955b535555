package org.springblade.timejob.timing;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Data 2022/3/9 0009 - 10:57
 * @Depiction:
 */
public class SchedulingRunnable implements Runnable {

	private static final Logger logger = LoggerFactory.getLogger(SchedulingRunnable.class);

	private String beanName;

	private String methodName;

	private String params;

	private Long jobId;

	private String tenantId;

	public SchedulingRunnable(String beanName, String methodName, Long jobId, String tenantId) {
		this(beanName, methodName, null, jobId, tenantId);
	}

	public SchedulingRunnable(String beanName, String methodName, String params, Long jobId, String tenantId) {
		this.beanName = beanName;
		this.methodName = methodName;
		this.params = params;
		this.jobId = jobId;
		this.tenantId = tenantId;
	}

	@Override
	public void run() {
		logger.info("定时任务开始执行 - bean：{}，方法：{}，参数：{},租户id:{}", beanName, methodName, params, tenantId);
		long startTime = System.currentTimeMillis();
		try {
			executeTask();
		} catch (Exception ex) {
			logger.error(String.format("定时任务执行异常 - bean：%s，方法：%s，参数：%s 租户id:%s", beanName, methodName, params, tenantId), ex);
		}

		long times = System.currentTimeMillis() - startTime;
		logger.info("定时任务执行结束 - bean：{}，方法：{}，参数：{}，租户id:{}，耗时：{} 毫秒", beanName, methodName, params, tenantId, times);
	}

	public void executeTask() throws NoSuchMethodException {
		Object target = SpringUtil.getBean(beanName);
		Method method = null;
		if (StringUtil.isNotBlank(params)) {
			method = target.getClass().getDeclaredMethod(methodName, String.class);
		} else {
			Method[] declaredMethods = target.getClass().getDeclaredMethods();
			method = target.getClass().getDeclaredMethod(methodName);
		}

		ReflectionUtils.makeAccessible(method);
		Method finalMethod = method;
		if (StringUtil.isNotBlank(params)) {
			TenantBroker.runAs(this.tenantId, e -> finalMethod.invoke(target, params));
		} else {
			TenantBroker.runAs(this.tenantId, e -> finalMethod.invoke(target));
		}
	}


	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		SchedulingRunnable that = (SchedulingRunnable) o;
		if (params == null) {
			return beanName.equals(that.beanName) &&
				methodName.equals(that.methodName) &&
				jobId.equals(that.jobId) &&
				that.params == null
				&& tenantId.equals(that.tenantId);
		}

		return beanName.equals(that.beanName) &&
			methodName.equals(that.methodName) &&
			params.equals(that.params) &&
			jobId.equals(that.jobId)
			&& tenantId.equals(that.tenantId);
	}

	@Override
	public int hashCode() {
		if (params == null) {
			return Objects.hash(beanName, methodName, tenantId);
		}

		return Objects.hash(beanName, methodName, params, tenantId);
	}
}
