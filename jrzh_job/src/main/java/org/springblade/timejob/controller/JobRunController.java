/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.timejob.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.timejob.entity.JobRun;
import org.springblade.timejob.service.IJobRunService;
import org.springblade.timejob.vo.JobRunVO;
import org.springblade.timejob.wrapper.JobRunWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 触发器 控制器
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_JOB + CommonConstant.WEB_BACK + "/timerJob/jobrun")
@Api(value = "触发器", tags = "触发器接口")
public class JobRunController extends BladeController {

    private final IJobRunService jobRunService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入jobRun")
    @PreAuth("hasPermission('timerJob:jobRun:detail') or hasRole('administrator')")
    public R<JobRunVO> detail(JobRun jobRun) {
        JobRun detail = jobRunService.getOne(Condition.getQueryWrapper(jobRun));
        return R.data(JobRunWrapper.build().entityVO(detail));
    }

    /**
     * 分页 触发器
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入jobRun")
    @PreAuth("hasPermission('timerJob:jobRun:list') or hasRole('administrator')")
    public R<IPage<JobRunVO>> list(JobRun jobRun, Query query) {
        IPage<JobRun> pages = jobRunService.page(Condition.getPage(query), Condition.getQueryWrapper(jobRun));
        return R.data(JobRunWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 触发器
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入jobRun")
    @PreAuth("hasPermission('timerJob:jobRun:page') or hasRole('administrator')")
    public R<IPage<JobRunVO>> page(JobRunVO jobRun, Query query) {
        IPage<JobRunVO> pages = jobRunService.selectJobRunPage(Condition.getPage(query), jobRun);
        return R.data(pages);
    }

    /**
     * 新增或修改 触发器
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入jobRun")
    @PreAuth("hasPermission('timerJob:jobRun:submit') or hasRole('administrator')")
    public R submit(@Valid @RequestBody JobRun jobRun) {
        return jobRunService.saveJob(jobRun);
    }


    /**
     * 删除 触发器
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('timerJob:jobRun:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(jobRunService.deleteLogic(Func.toLongList(ids)));
    }


}
