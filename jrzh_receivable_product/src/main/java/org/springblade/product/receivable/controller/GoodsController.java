/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.receivable.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CustomerEnum;
import org.springblade.common.enums.CustomerGoodsEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.ProcessTypeEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.receivable.entity.Goods;
import org.springblade.product.receivable.service.IGoodsService;
import org.springblade.product.receivable.vo.GoodsVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 产品表 控制器
 *
 * <AUTHOR>
 * @since 2021-12-24
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS + CommonConstant.WEB_BACK + "/goods")
@Api(value = "产品表", tags = "产品表接口")
public class GoodsController extends BladeController {

    private final IGoodsService goodsService;
    private final ICustomerGoodsService customerGoodsService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入goods")
    @PreAuth("hasPermission('goods:detail') or hasRole('administrator')")
    public R<ProductVO> detail(@RequestParam Long id) {
        return R.data(goodsService.detail(id));
    }

    /**
     * 分页 产品表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入goods")
    @PreAuth("hasPermission('goods:list') or hasRole('administrator')")
    public R<IPage<ProductVO>> list(@RequestParam Map<String, Object> goods, Query query) {
        IPage<ProductVO> page = goodsService.selectGoodsPage(goods, query);
        return R.data(page);
    }

    /**
     * 新增 产品表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入goods")
    @PreAuth("hasPermission('goods:save') or hasRole('administrator')")
    public R<Long> save(@Valid @RequestBody ProductDTO goods) {
        return R.data(goodsService.saveGoods(goods).getId());
    }

    /**
     * 修改 产品表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入goods")
    @PreAuth("hasPermission('goods:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody ProductDTO goods) {
        goodsService.updateGoods(goods);
        return R.status(true);
    }

//	@PostMapping("/updateGoodsRiskControl")
//	@ApiOperation("更新产品风控规则")
//	public R<Boolean> updateGoodsRiskControl(@RequestBody Goods goods) {
//		return R.status(goodsService.updateGoodsRiskControl(goods));
//	}

    /**
     * 删除 产品表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('goods:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(goodsService.deleteLogic(Func.toLongList(ids)));
    }


    @GetMapping("/onShelf")
    @ApiOperation("上架")
    public R<Boolean> onShelf(@RequestParam Long id) {
        goodsService.onShelf(id);
        return R.status(true);
    }

    @GetMapping("/onShelfGoodsList")
    @ApiOperation("查询所有上架产品")
    public R<List<Goods>> onShelfGoodsList() {
        return R.data(goodsService.list(Wrappers.<Goods>lambdaQuery().eq(Goods::getStatus, GoodsEnum.ON_SHELF.getCode())));
    }

    @GetMapping("/batchOnShelf")
    @ApiOperation("批量上架")
    public R<Integer> batchOnShelf(@RequestParam String ids) {
        List<Long> goodsIds = Func.toLongList(ids);
        goodsService.batchOnShelf(goodsIds);
        return R.data(goodsIds.size());
    }

    @GetMapping("/offShelf")
    @ApiOperation("下架")
    public R<Boolean> offShelf(@RequestParam String ids) {
        goodsService.offShelf(Func.toLongList(ids));
        return R.status(true);
    }

    @GetMapping("/setIsHighQuality")
    @ApiOperation("设置产品为优质产品")
    public R<Boolean> setIsHighQuality(@RequestParam Long id, @RequestParam Integer isHighQuality) {
        goodsService.setIsHighQuality(id, isHighQuality);
        return R.status(true);
    }

//    @GetMapping("/processGoodsInfo")
//    @ApiOperation("流程产品信息")
//    public R<GoodsVO> processGoodsInfo(Long goodsId) {
//        return R.data(goodsService.processGoodsInfo(goodsId));
//    }

    @GetMapping("/allOnShelfGoodsList")
    @ApiOperation("查询所有上架产品")
    public R<List<Goods>> allOnShelfGoodsList(@RequestParam Integer type) {
        List<Goods> goodsList = goodsService.list(Wrappers.<Goods>lambdaQuery()
                .eq(Goods::getType, type)
                .eq(Goods::getStatus, GoodsEnum.ON_SHELF.getCode()));
        return R.data(goodsList);
    }

//    @GetMapping("/selectCapitalIdGoodsPage")
//    @ApiOperation("根据资方查询对应数据")
//    @PreAuth("hasPermission('goods:selectCapitalIdGoodsPage') or hasRole('administrator')")
//    public R<IPage<GoodsVO>> list(@RequestParam Map<String, Object> goods, Query query, Long companyId) {
//        if (ObjectUtil.isNotEmpty(goods)) {
//            goods.remove("companyId");
//        }
//        IPage<GoodsVO> page = goodsService.selectCapitalIdGoodsPage(goods, query, companyId);
//        return R.data(page);
//    }

    /**
     * 获取应收业务产品下拉列表
     *
     * @return
     */
    @GetMapping("getGoodsList")
    @ApiOperation("获取应收业务产品下拉列表")
    public R<List> getGoodsList() {
        List<GoodsVO> list = goodsService.getGoodsList();
        return R.data(list);
    }

//    /**
//     * 获取代采业务产品下拉列表
//     *
//     * @return
//     */
//    @GetMapping("getAgentGoodsList")
//    @ApiOperation("获取代采业务产品下拉列表")
//    public R<List> getAgentGoodsList() {
//        List<GoodsVO> list = iAgentGoodsService.getAgentGoodsList();
//        return R.data(list);
//    }
//
//    /**
//     * 获取应收和代采业务产品下拉列表
//     *
//     * @return
//     */
//    @GetMapping("getReceivableAndAgentGoodsList")
//    @ApiOperation("获取应收和代采业务产品下拉列表")
//    public R<List> getReceivableAndAgentGoodsList() {
//        List<GoodsVO> list = iAgentGoodsService.getReceivableAndAgentGoodsList();
//        return R.data(list);
//    }


    @GetMapping("/copyGoods")
    @ApiOperation("产品复制")
    public R<Boolean> copyGoods(Long id) {
        goodsService.copyById(id);
        return R.data(true);
    }

    @GetMapping("/canOperator")
    @ApiOperation("是否在非正常情况下可操作产品")
    public R getOperator() {
        return R.data(goodsService.canOperator());
    }

    /**
     *  应收账款申请额度重新开通
     * @param customerGoodsId 开通产品ID
     * @param businessId 产品ID
     * @return
     */
    @GetMapping("/reActivatedGoodsShenQing")
    @ApiOperation("应收账款申请额度重新开通")
    public R<Boolean> reActivatedGoodsShenQing(@RequestParam Long customerGoodsId,@RequestParam Long businessId) {
        if (ObjectUtil.isEmpty(customerGoodsId) || ObjectUtil.isEmpty(businessId)) {
            return R.data(false);
        }
        //逻辑删除我的产品
        customerGoodsService.removeOpenInfo(businessId,AuthUtil.getUserId(),customerGoodsId);
        return R.data(true);
    }

    /**
     *  应收账款激活额度重新开通
     * @param processInstanceId 流程实例ID
     * @param businessId 产品ID
     * @param customerGoodsId 我的开通产品ID
     * @return
     */
    @GetMapping("/reActivatedGoodsJiHuo")
    @ApiOperation("应收账款激活额度重新开通")
    public R<Boolean> reActivatedGoodsJiHuo(@RequestParam String processInstanceId,@RequestParam Long businessId,@RequestParam Long customerGoodsId) {
        if (ObjectUtil.isEmpty(processInstanceId) || ObjectUtil.isEmpty(businessId) || ObjectUtil.isEmpty(customerGoodsId)) {
            return R.data(false);
        }
        customerGoodsService.reActivatedGoodsJiHuo(processInstanceId,businessId,customerGoodsId);
        return R.data(true);
    }
}
