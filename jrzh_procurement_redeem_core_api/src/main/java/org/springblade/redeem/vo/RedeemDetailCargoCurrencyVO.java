/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.customer.vo.FinancingAddressVO;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.redeem.entity.RedeemExpense;
import org.springblade.redeem.entity.RedeemSend;
import org.springblade.redeem.entity.RedeemUser;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.resource.entity.Attach;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 赎货表详细信息通用实体类
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@Data
public class RedeemDetailCargoCurrencyVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品图片")
    private String goodLogo;

    @ApiModelProperty(value = "赎货数量")
    private Integer num;

    @ApiModelProperty(value = "赎货单号")
    private String redeemNo;

    @ApiModelProperty(value = "提货方式")
    private String extractType;

    @ApiModelProperty(value = "库龄")
    private Integer warehouseAge;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "库存编号")
    private String warehouseNo;

    @ApiModelProperty(value = "融资编号")
    private String financeNo;

    @ApiModelProperty(value = "入库时间")
    private LocalDate warehouseInDate;

    @ApiModelProperty(value = "约定赎货日")
    private LocalDate redemptionDate;

    public String getStatusStr() {
        return RedeemCargoStatusEnum.getValueByKey(getStatus());
    }

    @ApiModelProperty(value = "自提信息")
    private RedeemUser redeemUser;

    @ApiModelProperty(value = "第三方物流信息")
    private FinancingAddressVO financingAddress;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品规格")
    private String goodsSpec;

    @ApiModelProperty(value = "商品单位")
    private String goodsUnitValue;

    @ApiModelProperty(value = "采购单价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "融资单价(元)")
    private BigDecimal financingPrice;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "资方费用信息")
    private List<RedeemExpense> manExpenseCulationVoList;

    @ApiModelProperty(value = "资方费用信息")
    private CapitalExpenseManVO manExpenseCulationVo;

    @ApiModelProperty(value = "平台费用信息")
    private List<RedeemExpense> plaExpenseCulationVoList;

    @ApiModelProperty(value = "平台费用信息")
    private List<PlaExpenseCulationVO> plaExpenseVOList;

    @ApiModelProperty(value = "资方附件信息")
    private List<Attach> manAttachList;

    @ApiModelProperty(value = "平台附件信息")
    private List<Attach> plaAttachList;

    @ApiModelProperty(value = "发货信息")
    private RedeemSend redeemSend;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;
    /**
     * 仓储公司
     */
    private Long storageId;
    /**
     * 应还
     */
    private BigDecimal shouldTotal;
    /**
     * 利息
     */
    private BigDecimal shouldInterest;
    /**
     * 实际还
     */
    private BigDecimal realTotal;
    /**
     * 缴费信息
     */
    CostCalculusVO costCalculusVO;
}
