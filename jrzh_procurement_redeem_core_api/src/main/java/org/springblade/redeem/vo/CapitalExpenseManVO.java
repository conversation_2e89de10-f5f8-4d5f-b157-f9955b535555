package org.springblade.redeem.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.redeem.entity.RedeemExpense;
import org.springblade.resource.entity.Attach;

import java.util.List;

@Data
public class CapitalExpenseManVO {

	@ApiModelProperty(value = "资方费用信息")
	private List<RedeemExpense> manExpenseCulationVoList;

	@ApiModelProperty(value = "还款记录信息")
	private LoanManageRepayment loanManageRepayment;

	/**
	 *  支付方式 1.线下 2.线上
	 */
	private Integer payMode;

	@ApiModelProperty(value = "费用附件信息")
	private List<Attach> attachList;
}
