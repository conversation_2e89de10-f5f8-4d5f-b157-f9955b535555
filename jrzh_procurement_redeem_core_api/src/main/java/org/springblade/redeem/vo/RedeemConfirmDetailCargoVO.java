/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 赎货表视图实体类
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@Data
@ApiModel(value = "RedeemCargoVO对象", description = "赎货表")
public class RedeemConfirmDetailCargoVO {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "赎货通用信息")
	private RedeemDetailCargoCurrencyVO redeemDetailCargoCurrencyVO;

	@ApiModelProperty(value = "资方费用信息")
	private List<ExpenseInfoVo> manExpenseInfoVoList;

	@ApiModelProperty(value = "平台费用信息")
	private List<ExpenseInfoVo> plaExpenseInfoVoList;








}
