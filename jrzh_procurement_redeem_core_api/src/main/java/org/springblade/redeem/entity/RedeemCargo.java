/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 赎货表实体类
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@Data
@TableName("jrzh_redeem_cargo")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RedeemCargo对象", description = "赎货表")
public class RedeemCargo extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 库存ID
	 */
	@NotNull(message = "库存ID不能为空")
	@ApiModelProperty(value = "库存ID")
	private Long stockId;
	/**
	 * 赎货单号
	 */
	@ApiModelProperty(value = "赎货单号")
	private String redeemNo;
	/**
	 * 赎货单号
	 */
	@NotBlank(message = "融资编号不能为空")
	@ApiModelProperty(value = "融资编号")
	private String financingNo;
	/**
	 * 赎货数量
	 */
	@NotNull(message = "赎货数量不能为空")
	@ApiModelProperty(value = "赎货数量")
	private Integer num;
	/**
	 * 提货方式;1、第三方物流配送 2、自提
	 */
	@NotNull(message = "提货方式不能为空")
	@ApiModelProperty(value = "提货方式;1、第三方物流配送 2、自提")
	private Integer extractType;
	/**
	 * 提货信息Id
	 */

	@ApiModelProperty(value = "提货信息Id")
	private Long extractId;
	/**
	 * 地址Id
	 */
	@ApiModelProperty(value = "地址Id")
	private Long addressId;
	/**
	 * 费用信息Id
	 */
	@ApiModelProperty(value = "费用信息Id")
	private Long expenseInfoId;
	/**
	 * 费用订单Id
	 */
	@ApiModelProperty(value = "费用订单Id")
	private Long expenseOrderId;
	/**
	 * 还款记录Id
	 */
	@ApiModelProperty(value = "还款记录Id")
	private Long repaymentRecordId;
	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id")
	private String processInstanceId;

	/**
	 * 审核人员填写的费用信息
	 */
	@ApiModelProperty(value = "审核人员填写的费用信息")
	private String expenseInfo;

	/**
	 * 商品名称
	 */
	@NotNull(message = "商品名称不能为空")
	@ApiModelProperty(value = "商品名称")
	private String goodsName;


	/**
	 * 签收人
	 */
	@ApiModelProperty(value = "签收人")
	private String receivedPeople;

	/**
	 * 签收时间
	 */
	@ApiModelProperty(value = "签收时间")
	private LocalDateTime receivedTime;

	/**
	 * 签收凭证
	 */
	@ApiModelProperty(value = "签收凭证")
	private String receivedDocument;

	/**
	 * 申请人
	 */
	@ApiModelProperty(value = "申请人")
	private Long applyUser;

	@ApiModelProperty(value = "倒计时过期时间")
	private LocalDateTime countdownExpireTime;

	@ApiModelProperty(value = "赎货支付状态 1.待支付 2.支付中 3.已支付 4.支付失败")
	private Integer paymentStatus;
}
