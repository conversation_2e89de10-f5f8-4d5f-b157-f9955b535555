/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * 赎货费用信息表（只做显示使用）实体类
 *
 * <AUTHOR>
 * @since 2022-07-06
 */
@Data
@TableName("jrzh_redeem_expense")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RedeemExpense对象", description = "赎货费用信息表（只做显示使用）")
public class RedeemExpense extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 费用名称
	*/
		@ApiModelProperty(value = "费用名称")
		private String name;
	/**
	* 费用类别 1银行还款费用 2 平台费用
	*/
		@ApiModelProperty(value = "费用类别 1银行还款费用 2 平台费用")
		private Integer businessCategory;
	/**
	* 费用类型名称
	*/
		@ApiModelProperty(value = "费用类型名称")
		private String expenseType;
	/**
	* 计算方式;1、手填 2、公式计算 3、工具类
	*/
		@ApiModelProperty(value = "计算方式;1、手填 2、公式计算 3、工具类")
		private Integer calculation;
	/**
	* 计费公式
	*/
		@ApiModelProperty(value = "计费公式")
		private String feeFormula;
	/**
	* 应付金额
	*/
		@ApiModelProperty(value = "应付金额")
		private BigDecimal money;
	/**
	* 赎货单号
	*/
		@ApiModelProperty(value = "赎货单号")
		private String redeemNo;

	/**
	 * 账户类型
	 */
	    @ApiModelProperty(value = "账户类型")
		private Integer type;

	/**
	 * 账户id
	 */
	@ApiModelProperty(value = "账户id")
	private Long accountId;

	/**
	 *  支付方式 1.线下 2.线上
	 */
	@ApiModelProperty("支付方式")
	private Integer costPayMode;

	/**
	 *  产品id
	 */
	@ApiModelProperty("产品id")
	private Long goodsId;

	/**
	 * 费用规则id
	 */
	@ApiModelProperty(value = "费用id")
	private Long goodsExpenseId;

	/**
	 *  费用单号
	 */
	@ApiModelProperty("费用单号")
	private String expenseOrderNo;
}
