package org.springblade.finance.limit.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.finance.limit.dto.WebFinancingLimitDto;
import org.springblade.finance.limit.dto.WebQueryFinancingLimitDto;
import org.springblade.finance.limit.service.FinancingLimitService;
import org.springblade.system.entity.Dept;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 平台端水位
 * <AUTHOR>
 * @date 2024/12/21
 */
@RestController
@RequestMapping(CommonConstant.WEB_BACK + "/orderLevel/financingLimit")
@RequiredArgsConstructor
public class FinancingLimitBackController {

    private final FinancingLimitService financingLimitService;

    /**
     * 平台端水位分页
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/getFinancingLimit")
    public R<IPage<WebFinancingLimitDto>> getFinancingLimit(Query query, WebQueryFinancingLimitDto dto) {
        return R.data(financingLimitService.getFinancingLimit(query, dto, null, null));
    }

}
