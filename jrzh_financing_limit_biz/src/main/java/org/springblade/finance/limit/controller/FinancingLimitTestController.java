package org.springblade.finance.limit.controller;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsTypeEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.finance.limit.dto.InitFinancingLimitDto;
import org.springblade.finance.limit.dto.OrderDataDto;
import org.springblade.finance.limit.enums.FinancingLimitEnum;
import org.springblade.finance.limit.service.OrderLevelService;
import org.springblade.system.entity.Dept;
import org.springblade.system.service.IDeptService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/test/financingLimit")
public class FinancingLimitTestController {

    private final OrderLevelService orderLevelService;
    private final IDeptService deptService;

    // 初始化水位
    @RequestMapping("/initFinancingLimitByJob")
    public void initFinancingLimitByJob() {
        Dept dept = deptService.getById(1848268811115954177L);
        InitFinancingLimitDto initDto = new InitFinancingLimitDto();
        List<OrderDataDto> orderDataDtoList = this.buidOrderDataDtoList();
        initDto.setDept(dept);
        initDto.setOrderDataDtoList(orderDataDtoList);
        orderLevelService.initFinancingLimitByJob(initDto, GoodsTypeEnum.ORDER_FINANCING.getCode(), FinancingLimitEnum.MySqlDateFormatEnum.DAY);
    }

    // 使用水位
    @RequestMapping("/useOrderLevel")
    public void useOrderLevel() {
        Long limitId = 1869627756917678081L;
        BigDecimal useAmount = new BigDecimal("400");
        String businessNo = "bbb1";
        orderLevelService.useOrderLevel(limitId, useAmount, businessNo);
    }

    // 变更使用的水位
    @RequestMapping("/updateUseOrderLevel")
    public void updateUseOrderLevel() {
        Long limitId = 1869583890617303042L;
        BigDecimal useAmount = new BigDecimal("1200");
        String businessNo = "bbb1";
        orderLevelService.updateUseOrderLevel(limitId, useAmount, businessNo);
    }

    // 水位取消使用
    @RequestMapping("/useOrderLevelRollBack")
    public void useOrderLevelRollBack() {
        String businessNo = "bbb1";
        orderLevelService.useOrderLevelRollBack(businessNo);
    }

    // 放款
    @RequestMapping("/useEndOrderLevel")
    public void lendingOrderLevel() {
        String businessNo = "bbb1";
        BigDecimal lendingAmount = new BigDecimal("350");
//        FinancingLimitEnum.IsReuseEnum isReuseEnum = FinancingLimitEnum.IsReuseEnum.REPEAT_USE;
        orderLevelService.lendingOrderLevel(businessNo, lendingAmount);
    }

    // 还款
    @RequestMapping("/repaymentOrderLevel")
    public void repaymentOrderLevel() {
        String businessNo = "bbb1";
        BigDecimal repaymentAmount = new BigDecimal("150");
        String repaymentNo = "repayment_" + businessNo;
        orderLevelService.repaymentOrderLevel(businessNo, repaymentAmount, repaymentNo);
    }

    private List<OrderDataDto> buidOrderDataDtoList() {
        List<OrderDataDto> orderDataDtoList = new ArrayList<>();
        Long userId = AuthUtil.getUserId();
        String[] s = {"a", "b", "c", "d", "e"};
        List<String> list = Arrays.asList(s);
        BigDecimal thousand = new BigDecimal("1000");
        for (int i = 1; i <= 1; i++) {
            OrderDataDto dto = new OrderDataDto();
            dto.setOrderAmount(thousand.multiply(new BigDecimal(i)));
            dto.setOrderNo(i + "");
            dto.setOrderDate(LocalDate.now().toString());
            dto.setCompanyId(userId);
            dto.setCompanyName(list.get(i - 1));
            dto.setBuyerCreditCode(list.get(i - 1));
            dto.setSellerCreditCode(list.get(i - 1));
            orderDataDtoList.add(dto);
        }
        return orderDataDtoList;
    }

}
