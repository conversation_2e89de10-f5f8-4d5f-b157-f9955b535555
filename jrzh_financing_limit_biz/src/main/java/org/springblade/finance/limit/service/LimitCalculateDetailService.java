package org.springblade.finance.limit.service;

import org.springblade.core.mp.service.BladeService;
import org.springblade.finance.limit.dto.FinancingLimitDto;
import org.springblade.finance.limit.entity.LimitCalculateDetail;

import java.util.List;

public interface LimitCalculateDetailService extends BladeService<LimitCalculateDetail> {

    /**
     * 保存水位生成记录
     * @param financingLimitDtoList
     * @return
     */
    boolean saveLimitCalculateDetail(List<FinancingLimitDto> financingLimitDtoList);

}
