/*
 Navicat Premium Data Transfer

 Source Server         : local
 Source Server Type    : MySQL
 Source Server Version : 80012
 Source Host           : localhost:3306
 Source Schema         : jrzh_supplier_3

 Target Server Type    : MySQL
 Target Server Version : 80012
 File Encoding         : 65001

 Date: 21/02/2025 18:49:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for jrzh_trading_order_data
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_trading_order_data`;
CREATE TABLE `jrzh_trading_order_data`  (
  `id` bigint(20) NOT NULL COMMENT '唯一标识ID',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人;创建人',
  `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(11) NULL DEFAULT NULL COMMENT '是否已删除;是否删除 0-未删除 1-已删除',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态：1未使用，2使用中，3已使用',
  `tenant_id` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '订单编号',
  `order_create_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下单时间',
  `order_amount` decimal(24, 2) NULL DEFAULT NULL COMMENT '订单金额（元）',
  `sales_credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '卖方社会统一代码',
  `sales_company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '卖方公司名称',
  `buyer_credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '买方社会统一代码',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '企业id',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业名称',
  `scenario_type` tinyint(4) NULL DEFAULT NULL COMMENT '场景类型：1一对多，2多对一',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '交易订单数据表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
