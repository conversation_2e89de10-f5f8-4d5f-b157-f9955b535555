-- 业务字典
-- 增加资方编码
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1824283320654077953, '000000', 0, 'capital_number_type', '-1', '资方编码', 1, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1824283500916875266, '000000', 1824283320654077953, 'capital_number_type', 'capitalZheShang', '浙商银行', 1, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1834524403552026625, '000000', 1824283320654077953, 'capital_number_type', 'capitalGuangDa', ' 光大银行', 2, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1834524497978392578, '000000', 1824283320654077953, 'capital_number_type', 'capitalPingAn', '平安银行', 3, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1834524596888469506, '000000', 1824283320654077953, 'capital_number_type', 'capitalMinSheng', '民生银行', 4, '', 0, 0);

-- 增加收费节点和计算节点
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1869345566371168257, '000000', 1519235017519239170, 'goods_expense_rule_fee_node', '101', '水位计算', 101, '水位计算', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1869347281229135874, '000000', 1746863914454376450, 'cost_rules_calculation_nodes', '101', '水位计算', 101, '', 0, 0);

-- 费用类型新增
INSERT INTO `jrzh_expense_type`(`id`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `is_deleted`, `status`, `tenant_id`, `expense_name`, `enterprise_type`, `goods_type_id`, `goods_type`, `parent_id`, `expense_key`, `repayment_type`, `sort`, `charge_method`, `account_type`, `paren_name`) VALUES (1871085232900358146, 1568205681205551106, 1601499214133702657, '2024-12-23 14:47:37', 1568205681205551106, '2024-12-23 15:30:09', 0, 1, '000000', '水位计算器', '2', '1738039639983357953', '5', 1622793912335917057, '999', '1,2', 999, 1, NULL, '资方费用');


-- 新增表结构
CREATE TABLE `jrzh_financing_limit` (
`id` bigint NOT NULL,
`order_amount` decimal(18,2) DEFAULT NULL COMMENT '原单据金额',
`order_no` varchar(255) DEFAULT NULL COMMENT '单据编号',
`limit_amount` decimal(18,2) DEFAULT NULL COMMENT '水位金额（元）',
`limit_no` varchar(100) DEFAULT NULL COMMENT '水位编码',
`business_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '业务编码',
`business_type` int DEFAULT NULL COMMENT '业务类型',
`limit_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '水位创建时间',
`is_reuse` varchar(10) DEFAULT NULL COMMENT '是否重复使用，0：是，1：否',
`capital_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资方编号',
`company_id` bigint DEFAULT NULL COMMENT '企业ID',
`company_name` varchar(255) DEFAULT NULL COMMENT '企业名称',
`buyer_credit_code` varchar(255) DEFAULT NULL COMMENT '买方统一编码',
`seller_credit_code` varchar(255) DEFAULT NULL COMMENT '卖方统一编码',
`is_push_capital` int DEFAULT '0' COMMENT '是否推送行方 1-是 0-否 2 -',
`create_user` bigint DEFAULT NULL COMMENT '创建人;创建人',
`create_dept` bigint DEFAULT NULL COMMENT '创建部门',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_user` bigint DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`is_deleted` int DEFAULT NULL COMMENT '是否已删除;是否删除 0-未删除 1-已删除',
`status` int DEFAULT NULL COMMENT '状态',
`tenant_id` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户ID',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='融资水位表';

CREATE TABLE `jrzh_limit_calculate_detail` (
`id` bigint NOT NULL,
`limit_id` bigint NOT NULL COMMENT '水位ID',
`limit_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '水位唯一编码',
`order_amount` decimal(18,2) DEFAULT NULL COMMENT '原单据金额',
`limit_amount` decimal(18,2) DEFAULT NULL COMMENT '水位金额',
`calculate_formula` text COMMENT '计算公式',
`calculate_param` json DEFAULT NULL COMMENT '计算参数',
`create_user` bigint DEFAULT NULL COMMENT '创建人',
`create_dept` bigint DEFAULT NULL COMMENT '创建部门',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_user` bigint DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`is_deleted` int DEFAULT NULL COMMENT '是否删除 0-未删除 1-已删除',
`status` int DEFAULT NULL COMMENT '状态',
`tenant_id` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户ID',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='融资水位计算详情表';

CREATE TABLE `jrzh_limit_use_detail` (
`id` bigint NOT NULL,
`finance_no` varchar(255) DEFAULT NULL COMMENT '融资编号',
`use_amount` decimal(18,2) DEFAULT NULL COMMENT '本次使用金融',
`use_before_amount` decimal(18,2) DEFAULT NULL COMMENT '使用前水位金额',
`use_after_amount` decimal(18,2) DEFAULT NULL COMMENT '使用后水位金额',
`repayment_no` varchar(50) DEFAULT NULL COMMENT '还款编号',
`limit_key` varchar(255) DEFAULT NULL COMMENT 'key',
`create_user` bigint DEFAULT NULL COMMENT '创建人;创建人',
`create_dept` bigint DEFAULT NULL COMMENT '创建部门',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_user` bigint DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`is_deleted` int DEFAULT NULL COMMENT '是否已删除;是否删除 0-未删除 1-已删除',
`status` int DEFAULT NULL COMMENT '状态',
`tenant_id` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户ID',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='水位使用详情';

CREATE TABLE `jrzh_capital_order_limt_calculate` (
`id` bigint NOT NULL COMMENT '主键id',
`tenant_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
`create_user` bigint DEFAULT NULL COMMENT '创建人',
`create_dept` bigint DEFAULT NULL COMMENT '创建部门',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_user` bigint DEFAULT NULL COMMENT '修改人',
`update_time` datetime DEFAULT NULL COMMENT '修改时间',
`is_deleted` int DEFAULT NULL COMMENT '逻辑删除 1-删除 0-未删除',
`status` int NOT NULL DEFAULT '1' COMMENT '状态',
`dept_id` bigint DEFAULT NULL COMMENT '资金方id',
`capital_service_no` varchar(20) DEFAULT NULL COMMENT '资金方编码',
`goods_expense_rule_id` bigint DEFAULT NULL COMMENT '费用计算器id',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资方订单水位费用计算器关联';

-- 表结构新增
ALTER TABLE `jrzh_capital_order_limt_calculate`
    ADD COLUMN `goods_id` bigint(0) NULL COMMENT '产品id' AFTER `goods_expense_rule_id`;

ALTER TABLE `blade_dept`
    ADD COLUMN `capital_service_no` varchar(20) NULL COMMENT '所属资方编码' AFTER `capital_keyword`;