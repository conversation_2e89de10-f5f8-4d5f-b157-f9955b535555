zSET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `jrzh_post_invoice_attach`;
CREATE TABLE `jrzh_post_invoice_attach`
(
    `id`                  bigint NOT NULL,
    `cred_appr_seri_no`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '银行授信编号',
    `invoice_attachment`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '发票文件base64',
    `invoice_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发票名称',
    `invoice_attach_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件日期',
    `status`              int NULL DEFAULT NULL COMMENT '是否删除 0未删除 1已删除',
    `is_deleted`          int NULL DEFAULT NULL COMMENT '是否删除 0未删除 1已删除',
    `create_time`         datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `create_user`         bigint NULL DEFAULT NULL COMMENT '创建人',
    `update_user`         bigint NULL DEFAULT NULL COMMENT '创建部门',
    `tenant_id`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;
