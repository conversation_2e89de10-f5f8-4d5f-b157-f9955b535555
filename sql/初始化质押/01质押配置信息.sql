-- 动产字典-流程
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1651779849477468161, '000000', 1646335289418878977, 'pledge_process_type', '22', '融资审核', 2, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1651779958789419009, '000000', 1646335289418878977, 'pledge_process_type', '23', '放款审核', 3, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1651780008366092290, '000000', 1646335289418878977, 'pledge_process_type', '24', '赎货审核', 4, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1654788389339209729, '000000', 1646335289418878977, 'pledge_process_type', '22', '动产质押融资申请', 2, '', 0, 1);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1654788440962703362, '000000', 1646335289418878977, 'pledge_process_type', '23', '动产质押赎货确认', 3, '', 0, 1);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1654788473158180866, '000000', 1646335289418878977, 'pledge_process_type', '24', '动产质押放款申请', 4, '', 0, 1);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1654788501092245506, '000000', 1646335289418878977, 'pledge_process_type', '25', '动产质押赎货申请', 5, '', 0, 1);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1654795866084585473, '000000', 1646335289418878977, 'pledge_process_type', '25', '赎货确认', 6, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1661564008584527873, '000000', 1646335289418878977, 'pledge_process_type', '26', '货物处置', 6, '', 0, 0);

-- 签署节点字典
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1646412293468160001, '000000', 1481081963510071298, 'goods_sign_node', '21-0', '质押-产品开通补充资料', 15, '质押产品开通-补充资料', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1646449088150986753, '000000', 1481081963510071298, 'goods_sign_node', '22-1', '质押-融资用户签署节', 15, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1647857232248832002, '000000', 1481081963510071298, 'goods_sign_node', '22-2', '质押-融资仓库签署节点', 16, '', 0, 0);
INSERT INTO `blade_dict_biz`(`id`, `tenant_id`, `parent_id`, `code`, `dict_key`, `dict_value`, `sort`, `remark`, `is_sealed`, `is_deleted`) VALUES (1647857393465294850, '000000', 1481081963510071298, 'goods_sign_node', '22-3', '质押-融资审核签署节点', 16, '', 0, 0);

-- 动产流程分类
INSERT INTO `blade_wf_category`(`id`, `name`, `pid`, `sort`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`, `tenant_id`, `business_type`, `process_type`) VALUES (1644171950374809601, '产品开通', 1504001966915751949, 1, 1568205681205551106, 1601499214133702657, '2023-04-07 10:55:15', 1568205681205551106, '2023-04-07 10:55:15', '1', 0, '000000', 4, 21);
INSERT INTO `blade_wf_category`(`id`, `name`, `pid`, `sort`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`, `tenant_id`, `business_type`, `process_type`) VALUES (1647886987383918593, '动产质押融资申请', 1504001966915751949, 2, 1568205681205551106, 1601499214133702657, '2023-04-17 16:57:29', 1568205681205551106, '2023-04-17 16:58:46', '1', 0, '000000', 4, 22);
INSERT INTO `blade_wf_category`(`id`, `name`, `pid`, `sort`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`, `tenant_id`, `business_type`, `process_type`) VALUES (1648650059362881537, '动产质押放款申请', 1504001966915751949, 3, 1568205681205551106, 1601499214133702657, '2023-04-19 19:29:39', 1568205681205551106, '2023-04-19 19:29:39', '1', 0, '000000', 4, 23);
INSERT INTO `blade_wf_category`(`id`, `name`, `pid`, `sort`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`, `tenant_id`, `business_type`, `process_type`) VALUES (1651517399989542913, '动产质押赎货申请', 1504001966915751949, 4, 1568205681205551106, 1601499214133702657, '2023-04-27 17:23:27', 1568205681205551106, '2023-04-27 17:23:27', '1', 0, '000000', 4, 24);
INSERT INTO `blade_wf_category`(`id`, `name`, `pid`, `sort`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`, `tenant_id`, `business_type`, `process_type`) VALUES (1654004681824890882, '动产质押赎货确认', 1504001966915751949, 5, 1568205681205551106, 1601499214133702657, '2023-05-04 14:07:01', 1568205681205551106, '2023-05-04 14:07:01', '1', 0, '000000', 4, 25);
INSERT INTO `blade_wf_category`(`id`, `name`, `pid`, `sort`, `create_user`, `create_dept`, `create_time`, `update_user`, `update_time`, `status`, `is_deleted`, `tenant_id`, `business_type`, `process_type`) VALUES (1661579605229780993, '动产质押货物处置', 1504001966915751949, 6, 1568205681205551106, 1611264469931966466, '2023-05-25 11:47:03', 1568205681205551106, '2023-05-25 11:47:03', '1', 0, '000000', 4, 26);

-- 动产表单
INSERT INTO `jrzh_external_form`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `form_key`, `name`, `business_type`) VALUES (1568205681205551106, '2023-04-07 11:29:18', 1568205681205551106, '2023-04-07 11:33:38', 1601499214133702657, 0, 2, 1644180518280916993, '000000', 'pledge_goods_open', '动产质押产品开通', 4);
INSERT INTO `jrzh_external_form`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `form_key`, `name`, `business_type`) VALUES (1568205681205551106, '2023-04-17 16:53:47', 1568205681205551106, '2023-06-09 11:32:04', 1601499214133702657, 0, 2, 1647886058874064898, '000000', 'pledge_goods_finance_apply', '动产质押融资申请', 4);
INSERT INTO `jrzh_external_form`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `form_key`, `name`, `business_type`) VALUES (1568205681205551106, '2023-04-19 19:30:57', 1568205681205551106, '2023-06-09 11:45:38', 1601499214133702657, 0, 2, ***********65374466, '000000', 'pledge_goods_finance_loan', '动产质押放款申请', 4);
INSERT INTO `jrzh_external_form`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `form_key`, `name`, `business_type`) VALUES (1568205681205551106, '2023-04-27 17:24:55', 1568205681205551106, '2023-06-09 11:49:23', 1601499214133702657, 0, 2, 1651517769184763906, '000000', 'pledge_redeem', '动产质押赎货申请', 4);
INSERT INTO `jrzh_external_form`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `form_key`, `name`, `business_type`) VALUES (1568205681205551106, '2023-05-04 14:13:56', 1568205681205551106, '2023-06-09 16:24:40', 1601499214133702657, 0, 2, 1654006421672521730, '000000', 'pledge_confirm', '动产质押赎货确认', 4);

INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, *****************53, '000000', 0, '申请产品', 'applyProduct', 1, 1644180518280916993, '1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186181083142, '000000', 0, '补充资料(文本)', 'furtherInformationText', 1, 1644180518280916993, '2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186181083143, '000000', 0, '补充资料(附件)', 'furtherInformationAccessory', 1, 1644180518280916993, '3');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186243997698, '000000', 0, '补充信息', 'supplementaryInformation', 2, 1644180518280916993, '4');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186243997699, '000000', 1646775186243997698, '价值分析', 'supplementaryInformation_valueAnalysis', 0, 1644180518280916993, 'children1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992642, '000000', 1646775186243997698, '征信信息', 'supplementaryInformation_creditInformation', 0, 1644180518280916993, 'children2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186529210370, '000000', 0, '合同签署', 'contractSigning', 1, 1644180518280916993, '5');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124929, '000000', 0, '风控信息', 'riskControlInformation', 2, 1644180518280916993, '6');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124930, '000000', 1646775186592124929, '评分结果', 'riskControlInformation_scores', 0, 1644180518280916993, 'children1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186659233794, '000000', 1646775186592124929, '风控意见', 'riskControlInformation_riskControlAdvice', 0, 1644180518280916993, 'children2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508481, '000000', 1646775186592124929, '资方授信', 'riskControlInformation_managementCredit', 0, 1644180518280916993, 'children3');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617345, '000000', 1646775186592124929, '最终批额', 'riskControlInformation_finalBatchForehead', 0, 1644180518280916993, 'children4');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186948640770, '000000', 0, '商品信息', 'commodityInformation', 1, 1644180518280916993, '7');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209518784513, '000000', 0, '质押信息', 'financing_module', 1, 1647886058874064898, '1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074209913049089, '000000', 0, '货物信息', 'cargonformation_module', 1, 1647886058874064898, '2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210244399105, '000000', 0, '合同模块', 'contract_list', 1, 1647886058874064898, '3');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210512834561, '000000', 0, '费用试算模块', 'trial_cost_module', 1, 1647886058874064898, '4');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210579943425, '000000', 0, '评估信息', 'evaluation_module', 1, 1647886058874064898, '5');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529197744129, '000000', 0, '还款试算', 'reimbursementTrial', 2, ***********65374466, '1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529260658690, '000000', 1651787529197744129, '资方费用', 'reimbursementTrial_bankInterest', 0, ***********65374466, 'children1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529520705538, '000000', 1651787529197744129, '平台费用', 'reimbursementTrial_costPlatform', 0, ***********65374466, 'children2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529843666946, '000000', 0, '保证金', 'bondTable', 1, ***********65374466, '2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530258903041, '000000', 0, '缴纳费用', 'platformFee', 1, ***********65374466, '3');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530695110658, '000000', 0, '合同签署', 'contractSigning', 1, ***********65374466, '4');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 16579565887********, '000000', 0, '赎货单', 'redemptionNote', 1, 1651517769184763906, '1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588801044482, '000000', 0, '还款单', 'repaymentNote_bank', 2, 1651517769184763906, '2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588822016002, '000000', 1657956588801044482, '银行还款单', 'repaymentNote_bank', 0, 1651517769184763906, 'children1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588952039426, '000000', 1657956588801044482, '平台费用单', 'repaymentNote_platform', 0, 1651517769184763906, 'children2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956589052702722, '000000', 1657956588801044482, '应还总额', 'repaymentNote_totalRepayment', 0, 1651517769184763906, 'children3');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 16579565566********, '000000', 0, '赎货单', 'redemptionNote', 1, 1654006421672521730, '1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556773339137, '000000', 0, '还款单', 'repaymentNote_bank', 2, 1654006421672521730, '2');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556794310657, '000000', 1657956556773339137, '银行还款单', 'repaymentNote_bank', 0, 1654006421672521730, 'children1');
INSERT INTO `jrzh_external_form_template`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `parent_id`, `name`, `template_key`, `sort`, `external_form_id`, `template_label`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556966277121, '000000', 1657956556773339137, '平台费用单', 'repaymentNote_platform', 0, 1654006421672521730, 'children2');

INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186181083138, '000000', '最高可借', 'applyProduct_loanAmountEnd', *****************53, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186181083139, '000000', '年利率低至', 'applyProduct_annualInterestRate', *****************53, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186181083140, '000000', '最长期限', 'applyProduct_loadTermEnd', *****************53, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186181083141, '000000', '计费方式', 'applyProduct_billingMethod', *****************53, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186243997700, '000000', '去年纳税销售总额', 'valueAnalysis_talTaxSalesLastYear', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078082, '000000', '前年纳税销售总额', 'valueAnalysis_talTaxSalesOfThePreviousYear', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078083, '000000', '净利润', 'valueAnalysis_retainedProfits', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078084, '000000', '去年应纳税总额', 'valueAnalysis_talAmountOfTaxPayableLastYear', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078085, '000000', '前年应纳税总额', 'valueAnalysis_talTaxableAmountOfPreviousYear', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078086, '000000', '同比销售增长率', 'valueAnalysis_yearSalesGrowthRate', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078087, '000000', '环比销售增长率', 'valueAnalysis_sequentialSalesGrowthRate', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078088, '000000', '应收账款总额', 'valueAnalysis_talAccountsReceivable', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078089, '000000', '应还账款总额', 'valueAnalysis_talAmountOfAccountsReceivable', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186332078090, '000000', '近1年回款率', 'valueAnalysis_recentYearReturnRate', 1646775186243997699, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992643, '000000', '当前关注', 'creditInformation_currentFocusOn', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992644, '000000', '当前次级', 'creditInformation_currentSubprime', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992645, '000000', '当前可疑', 'creditInformation_currentSuspicious', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992646, '000000', '当前损失', 'creditInformation_currentLoss', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992647, '000000', '当前逾期', 'creditInformation_currentOverdue', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992648, '000000', '近24个月逾期为M1', 'creditInformation_M1', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992649, '000000', '近24个月逾期为M2', 'creditInformation_M2', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992650, '000000', '近24个月逾期为M3', 'creditInformation_M3', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992651, '000000', '近2个月审批、征信查询', 'creditInformation_nearly2MonthsForApproval', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992652, '000000', '近12个月审批、征信查询', 'creditInformation_nearly12MonthsForApproval', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992653, '000000', '企业涉诉案件', 'creditInformation_casesInvolvingEnterprisesInLitigation', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992654, '000000', '企业涉诉未结案件', 'creditInformation_unresolvedCasesInvolvingLitigationOfEnterprises', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186394992655, '000000', '逾期率', 'creditInformation_yuqilv', 1646775186394992642, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186529210371, '000000', '序号', 'contractSigning_serialNumber', 1646775186529210370, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186529210372, '000000', '合同编号', 'contractSigning_contractNumber', 1646775186529210370, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186529210373, '000000', '合同标题', 'contractSigning_contractTitle', 1646775186529210370, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186529210374, '000000', '创建时间', 'contractSigning_creationTime', 1646775186529210370, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186529210375, '000000', '签署状态', 'contractSigning_signTheState', 1646775186529210370, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186529210376, '000000', '操作', 'contractSigning_operation', 1646775186529210370, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124931, '000000', '评级得分', 'scores_strFinalScore', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124932, '000000', '信用级', 'scores_grade', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124933, '000000', '指标项总分', 'scores_strTotalScore', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124934, '000000', '风险限额', 'scores_strLimitAmount', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124935, '000000', '有效期', 'scores_validTime', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124936, '000000', '评分编号', 'scores_recordNo', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124937, '000000', '评分模板', 'scores_ratingName', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124938, '000000', '评级时间', 'scores_createTime', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124939, '000000', '到期时间', 'scores_expireTime', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124940, '000000', '评级状态', 'scores_status', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186592124941, '000000', '重新评分', 'scores_toScore', 1646775186592124930, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186659233795, '000000', '授信额度', 'riskControlAdvice_lineOfCredit', 1646775186659233794, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186659233796, '000000', '风险说明', 'riskControlAdvice_riskThat', 1646775186659233794, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508482, '000000', '授信额度', 'managementCredit_chooseEnterprise', 1646775186751508481, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508483, '000000', '年利率', 'managementCredit_annualInterestRate', 1646775186751508481, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508484, '000000', '生效日', 'managementCredit_effectiveTime', 1646775186751508481, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508485, '000000', '到期日', 'managementCredit_expireTime', 1646775186751508481, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508486, '000000', '额度类型', 'managementCredit_quotaType', 1646775186751508481, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508487, '000000', '循环类型', 'managementCredit_recycleType', 1646775186751508481, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186751508488, '000000', '保证金比例', 'managementCredit_bondProportion', 1646775186751508481, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617346, '000000', '最终批额', 'finalBatchForehead_quota', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617347, '000000', '服务费率', 'finalBatchForehead_serviceTariffing', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617348, '000000', '生效日', 'finalBatchForehead_effectiveTime', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617349, '000000', '到期日', 'finalBatchForehead_expireTime', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617350, '000000', '额度类型', 'finalBatchForehead_quotaType', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617351, '000000', '年利率', 'finalBatchForehead_financingCostRate', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617352, '000000', '可贷成数', 'finalBatchForehead_loanableInto', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617353, '000000', '循环类型', 'finalBatchForehead_recycleType', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617354, '000000', '保证金比例', 'finalBatchForehead_bondProportion', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186818617355, '000000', '最高融资占比', 'finalBatchForehead_financingProportion', 1646775186818617345, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186948640771, '000000', '商品规格', 'substituteTable_productDetail', 1646775186948640770, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186948640772, '000000', '发布单价', 'substituteTable_unitPrice', 1646775186948640770, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186948640773, '000000', '数量', 'substituteTable_quantity', 1646775186948640770, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-14 15:19:35', 1568205681205551106, '2023-04-14 15:19:35', 1601499214133702657, 0, 1, 1646775186948640774, '000000', '单价', 'substituteTable_purchasePrice', 1646775186948640770, 1644180518280916993);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699074, '000000', '序号', 'fc_m_index', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699075, '000000', '额度编号', 'fc_m_quotaNo', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699076, '000000', '产品名称', 'fc_m_goodsName', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699077, '000000', '资方名称', 'fc_m_capitalName', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699078, '000000', '可用额度', 'fc_m_availableAmount', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699079, '000000', '额度状态', 'fc_m_quotaType', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699080, '000000', '到期日', 'fc_m_expireTime', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:17', 1568205681205551106, '2023-05-04 18:43:17', 1601499214133702657, 0, 1, 1654074209581699081, '000000', '输入放款金额', 'fc_m_loanAmount', 1654074209518784513, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074209980157953, '000000', '序号', 'cr_m_index', 1654074209913049089, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074209980157954, '000000', '融资编号', 'cr_m_financeNo', 1654074209913049089, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074209980157955, '000000', '货物信息', 'cr_m_goodsInfos', 1654074209913049089, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074209980157956, '000000', '供应商', 'cr_m_supplierName', 1654074209913049089, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074209980157957, '000000', '单位值', 'cr_m_unitName', 1654074209913049089, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074209980157958, '000000', '待入库数量', 'cr_m_quantity', 1654074209913049089, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210047266817, '000000', '已入库数量', 'cr_m_warehouseInNum', 1654074209913049089, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210315702273, '000000', '序号', 'cs_m_serial', 1654074210244399105, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210315702274, '000000', '合同编号', 'cs_m_contractId', 1654074210244399105, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210315702275, '000000', '合同标题', 'cs_m_contractTitle', 1654074210244399105, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210315702276, '000000', '创建时间', 'cs_m_createTime', 1654074210244399105, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210315702277, '000000', '签署状态', 'cs_m_statusText', 1654074210244399105, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210315702278, '000000', '操作', 'cs_m_operations', 1654074210244399105, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966849, '000000', '序号', 'er_m_index', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966850, '000000', '货品信息', 'er_m_goodsInfo', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966851, '000000', '发布单价', 'er_m_prices', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966852, '000000', '评估单价', 'er_m_purchasePrice', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966853, '000000', '融资比例', 'er_m_financingRatio', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966854, '000000', '融资单价', 'er_m_financingPrice', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966855, '000000', '数量', 'er_m_quantity', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-04 18:43:18', 1568205681205551106, '2023-05-04 18:43:18', 1601499214133702657, 0, 1, 1654074210709966856, '000000', '总额', 'er_m_financingTotal', 1654074210579943425, 1647886058874064898);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529327767553, '000000', '期数', 'bankInterest_periods', 1651787529260658690, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529390682114, '000000', '还款日期', 'bankInterest_repaymentDate', 1651787529260658690, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529390682115, '000000', '应还总额', 'bankInterest_totalShouldAlso', 1651787529260658690, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529390682116, '000000', '还款本金', 'bankInterest_repaymentPrincipal', 1651787529260658690, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529390682117, '000000', '应还利息', 'bankInterest_shouldAlsoInterest', 1651787529260658690, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529650728962, '000000', '费用名称', 'payAFee_costOfName', 1651787529520705538, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529717837825, '000000', '费用类型', 'payAFee_typeOfExpense', 1651787529520705538, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529717837826, '000000', '支付节点', 'payAFee_payTheNode', 1651787529520705538, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529717837827, '000000', '计费方式', 'payAFee_chargeMode', 1651787529520705538, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:50', 1568205681205551106, '2023-04-28 11:16:50', 1601499214133702657, 0, 1, 1651787529717837828, '000000', '应付金额', 'payAFee_amountPayable', 1651787529520705538, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775810, '000000', '保证金类型', 'bondTable_type', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775811, '000000', '支付节点', 'bondTable_payNode', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775812, '000000', '计算方式', 'bondTable_chargeMode', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775813, '000000', '退还方式', 'bondTable_returnMethods', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775814, '000000', '应缴金额(元)', 'bondTable_amount', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775815, '000000', '查看支付凭证', 'bondTable_checkPaymentVoucher', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775816, '000000', '付款开户行', 'bondTable_bank', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775817, '000000', '付款账号', 'bondTable_bankCard', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787529910775818, '000000', '支付时间', 'bondTable_payTime', 1651787529843666946, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040385, '000000', '费用名称', 'platformFee_costOfName', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040386, '000000', '费用类型', 'platformFee_typeOfExpense', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040387, '000000', '支付节点', 'platformFee_payTheNode', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040388, '000000', '计费方式', 'platformFee_chargeMode', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040389, '000000', '应付金额', 'platformFee_amountPayable', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040390, '000000', '付款开户行', 'platformFee_bank', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040391, '000000', '付款账号', 'platformFee_bankCard', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040392, '000000', '支付时间', 'platformFee_payTime', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530305040393, '000000', '查看支付凭证', 'platformFee_checkPaymentVoucher', 1651787530258903041, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530753830913, '000000', '#', 'contractSigning_index', 1651787530695110658, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530753830914, '000000', '合同编号', 'contractSigning_contractNumber', 1651787530695110658, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530753830915, '000000', '合同标题', 'contractSigning_contractTitle', 1651787530695110658, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530753830916, '000000', '创建时间', 'contractSigning_creationTime', 1651787530695110658, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530753830917, '000000', '签署状态', 'contractSigning_signTheState', 1651787530695110658, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-04-28 11:16:51', 1568205681205551106, '2023-04-28 11:16:51', 1601499214133702657, 0, 1, 1651787530753830918, '000000', '操作', 'contractSigning_operation', 1651787530695110658, ***********65374466);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588733935618, '000000', '赎货单号', 'redemptionNote_redeemNo', 16579565887********, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588733935619, '000000', '约定赎货日', 'redemptionNote_redemptionDate', 16579565887********, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588733935620, '000000', '提货方式', 'redemptionNote_extractType', 16579565887********, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588733935621, '000000', '收货地址', 'redemptionNote_delivery', 16579565887********, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588838793218, '000000', '年化利率', 'repaymentNote_bank_yearRate', 1657956588822016002, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588842987521, '000000', '应还本金', 'repaymentNote_bank_repayFunds', 1657956588822016002, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588847181825, '000000', '费用名称', 'repaymentNote_bank_table_name', 1657956588822016002, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588847181826, '000000', '费用类型', 'repaymentNote_bank_table_expenseType', 1657956588822016002, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588847181827, '000000', '支付节点', 'repaymentNote_bank_table_node', 1657956588822016002, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588847181828, '000000', '计费方式', 'repaymentNote_bank_table_feeFormula', 1657956588822016002, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588847181829, '000000', '应付金额', 'repaymentNote_bank_table_money', 1657956588822016002, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588977205249, '000000', '费用名称', 'repaymentNote_bank_table_name', 1657956588952039426, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588981399554, '000000', '费用类型', 'repaymentNote_bank_table_expenseType', 1657956588952039426, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588981399555, '000000', '支付节点', 'repaymentNote_bank_table_node', 1657956588952039426, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588981399556, '000000', '计费方式', 'repaymentNote_bank_table_feeFormula', 1657956588952039426, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:29', 1568205681205551106, '2023-05-15 11:50:29', 1601499214133702657, 0, 1, 1657956588981399557, '000000', '应付金额', 'repaymentNote_bank_table_money', 1657956588952039426, 1651517769184763906);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556706230274, '000000', '赎货单号', 'redemptionNote_redeemNo', 16579565566********, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556710424578, '000000', '约定赎货日', 'redemptionNote_redemptionDate', 16579565566********, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556714618881, '000000', '提货方式', 'redemptionNote_extractType', 16579565566********, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556714618882, '000000', '收货地址', 'redemptionNote_delivery', 16579565566********, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556819476481, '000000', '年化利率', 'repaymentNote_bank_yearRate', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556823670785, '000000', '应还本金', 'repaymentNote_bank_repayFunds', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556823670786, '000000', '费用名称', 'repaymentNote_bank_table_name', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556823670787, '000000', '费用类型', 'repaymentNote_bank_table_expenseType', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556823670788, '000000', '支付节点', 'repaymentNote_bank_table_node', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556827865090, '000000', '计费方式', 'repaymentNote_bank_table_feeFormula', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556827865091, '000000', '应付金额', 'repaymentNote_bank_table_money', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556827865092, '000000', '还款状态', 'repaymentNote_bank_status', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556827865093, '000000', '申请还款按钮', 'repaymentNote_bank_status_button_apply', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556827865094, '000000', '查看余额凭证按钮', 'repaymentNote_bank_status_button_view', 1657956556794310657, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556987248642, '000000', '费用名称', 'repaymentNote_bank_table_name', 1657956556966277121, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556995637250, '000000', '费用类型', 'repaymentNote_bank_table_expenseType', 1657956556966277121, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556995637251, '000000', '支付节点', 'repaymentNote_bank_table_node', 1657956556966277121, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556995637252, '000000', '计费方式', 'repaymentNote_bank_table_feeFormula', 1657956556966277121, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556995637253, '000000', '应付金额', 'repaymentNote_bank_table_money', 1657956556966277121, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556995637254, '000000', '还款状态', 'repaymentNote_platform_status', 1657956556966277121, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556995637255, '000000', '确认还款按钮', 'repaymentNote_platform_status_button_apply', 1657956556966277121, 1654006421672521730);
INSERT INTO `jrzh_external_form_template_field`(`create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `id`, `tenant_id`, `name`, `field_key`, `external_form_template_id`, `external_form_id`) VALUES (1568205681205551106, '2023-05-15 11:50:21', 1568205681205551106, '2023-05-15 11:50:21', 1601499214133702657, 0, 1, 1657956556995637256, '000000', '查看支付凭证按钮', 'repaymentNote_platform_status_button_view', 1657956556966277121, 1654006421672521730);


-- 动产流程
INSERT INTO `ACT_DE_MODEL`(`id`, `name`, `model_key`, `form_key`, `category_id`, `description`, `model_comment`, `created`, `created_by`, `last_updated`, `last_updated_by`, `version`, `xml`, `model_editor_json`, `thumbnail`, `model_type`, `tenant_id`) VALUES ('047df85da11d0b9d837aefe3fdb61a7e', '动产质押放款申请', 'pledge_goods_loan', 'wf_ex_pledge_goods_finance_loan', 1648650059362881537, NULL, NULL, '2023-04-19 19:36:45.814000', '1568205681205551106', '2023-05-08 10:00:45.492000', '1568205681205551106', 4, '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:activiti=\"http://activiti.org/bpmn\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n  <process id=\"pledge_goods_loan\" name=\"动产质押放款申请\" isExecutable=\"true\" flowable:skipFirstNode=\"true\" flowable:rollbackNode=\"Activity_06t0e98\">\n    <startEvent id=\"startEvent_1\" name=\"开始\">\n      <extensionElements>\n        <flowable:exFormKey value=\"pledge_goods_finance_loan\" />\n        <flowable:formProperty id=\"reimbursementTrial\" name=\"还款试算\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"reimbursementTrial_bankInterest\" name=\"资方费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_periods\" name=\"期数\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_repaymentDate\" name=\"还款日期\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_totalShouldAlso\" name=\"应还总额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_repaymentPrincipal\" name=\"还款本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_shouldAlsoInterest\" name=\"应还利息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"reimbursementTrial_costPlatform\" name=\"平台费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_costOfName\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_typeOfExpense\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_payTheNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_chargeMode\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_amountPayable\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable\" name=\"保证金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_type\" name=\"保证金类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_payNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_chargeMode\" name=\"计算方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_returnMethods\" name=\"退还方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_amount\" name=\"应缴金额(元)\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_checkPaymentVoucher\" name=\"查看支付凭证\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_bank\" name=\"付款开户行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_bankCard\" name=\"付款账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_payTime\" name=\"支付时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee\" name=\"缴纳费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_costOfName\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_typeOfExpense\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_payTheNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_chargeMode\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_amountPayable\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_bank\" name=\"付款开户行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_bankCard\" name=\"付款账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_payTime\" name=\"支付时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_checkPaymentVoucher\" name=\"查看支付凭证\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning\" name=\"合同签署\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_index\" name=\"#\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_contractNumber\" name=\"合同编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_contractTitle\" name=\"合同标题\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_creationTime\" name=\"创建时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_signTheState\" name=\"签署状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_operation\" name=\"操作\" readable=\"true\" writable=\"true\" />\n      </extensionElements>\n      <outgoing>Flow_0yhobgw</outgoing>\n    </startEvent>\n    <userTask id=\"Activity_06t0e98\" name=\"发起人\" flowable:exFormKey=\"pledge_goods_finance_loan\">\n      <extensionElements>\n        <flowable:assignee type=\"custom\" value=\"applyUser\" text=\"流程发起人\" />\n        <flowable:formProperty id=\"reimbursementTrial\" name=\"还款试算\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"reimbursementTrial_bankInterest\" name=\"资方费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_periods\" name=\"期数\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_repaymentDate\" name=\"还款日期\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_totalShouldAlso\" name=\"应还总额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_repaymentPrincipal\" name=\"还款本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_shouldAlsoInterest\" name=\"应还利息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"reimbursementTrial_costPlatform\" name=\"平台费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_costOfName\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_typeOfExpense\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_payTheNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_chargeMode\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_amountPayable\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable\" name=\"保证金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_type\" name=\"保证金类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_payNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_chargeMode\" name=\"计算方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_returnMethods\" name=\"退还方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_amount\" name=\"应缴金额(元)\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_checkPaymentVoucher\" name=\"查看支付凭证\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_bank\" name=\"付款开户行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_bankCard\" name=\"付款账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_payTime\" name=\"支付时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee\" name=\"缴纳费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_costOfName\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_typeOfExpense\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_payTheNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_chargeMode\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_amountPayable\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_bank\" name=\"付款开户行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_bankCard\" name=\"付款账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_payTime\" name=\"支付时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_checkPaymentVoucher\" name=\"查看支付凭证\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning\" name=\"合同签署\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_index\" name=\"#\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_contractNumber\" name=\"合同编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_contractTitle\" name=\"合同标题\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_creationTime\" name=\"创建时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_signTheState\" name=\"签署状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_operation\" name=\"操作\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_0yhobgw</incoming>\n      <outgoing>Flow_1rjr8in</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_0yhobgw\" sourceRef=\"startEvent_1\" targetRef=\"Activity_06t0e98\" />\n    <endEvent id=\"Event_1harg1w\">\n      <extensionElements>\n        <flowable:executionListener delegateExpression=\"${pledgeLoanListener}\" event=\"end\" />\n        <flowable:executionListener delegateExpression=\"${contractProcessListener}\" event=\"end\" />\n      </extensionElements>\n      <incoming>Flow_0vhotwm</incoming>\n    </endEvent>\n    <userTask id=\"Activity_0y82hwj\" name=\"业务审批\" flowable:exFormKey=\"pledge_goods_finance_loan\">\n      <extensionElements>\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:formProperty id=\"reimbursementTrial\" name=\"还款试算\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"reimbursementTrial_bankInterest\" name=\"资方费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_periods\" name=\"期数\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_repaymentDate\" name=\"还款日期\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_totalShouldAlso\" name=\"应还总额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_repaymentPrincipal\" name=\"还款本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankInterest_shouldAlsoInterest\" name=\"应还利息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"reimbursementTrial_costPlatform\" name=\"平台费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_costOfName\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_typeOfExpense\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_payTheNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_chargeMode\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"payAFee_amountPayable\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable\" name=\"保证金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_type\" name=\"保证金类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_payNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_chargeMode\" name=\"计算方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_returnMethods\" name=\"退还方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_amount\" name=\"应缴金额(元)\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_checkPaymentVoucher\" name=\"查看支付凭证\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_bank\" name=\"付款开户行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_bankCard\" name=\"付款账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bondTable_payTime\" name=\"支付时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee\" name=\"缴纳费用\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_costOfName\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_typeOfExpense\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_payTheNode\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_chargeMode\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_amountPayable\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_bank\" name=\"付款开户行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_bankCard\" name=\"付款账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_payTime\" name=\"支付时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"platformFee_checkPaymentVoucher\" name=\"查看支付凭证\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning\" name=\"合同签署\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_index\" name=\"#\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_contractNumber\" name=\"合同编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_contractTitle\" name=\"合同标题\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_creationTime\" name=\"创建时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_signTheState\" name=\"签署状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contractSigning_operation\" name=\"操作\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_1rjr8in</incoming>\n      <outgoing>Flow_0vhotwm</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_1rjr8in\" sourceRef=\"Activity_06t0e98\" targetRef=\"Activity_0y82hwj\" />\n    <sequenceFlow id=\"Flow_0vhotwm\" sourceRef=\"Activity_0y82hwj\" targetRef=\"Event_1harg1w\" />\n  </process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_flow\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_flow\" bpmnElement=\"pledge_goods_loan\">\n      <bpmndi:BPMNEdge id=\"Flow_0vhotwm_di\" bpmnElement=\"Flow_0vhotwm\">\n        <di:waypoint x=\"600\" y=\"218\" />\n        <di:waypoint x=\"702\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1rjr8in_di\" bpmnElement=\"Flow_1rjr8in\">\n        <di:waypoint x=\"430\" y=\"218\" />\n        <di:waypoint x=\"500\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0yhobgw_di\" bpmnElement=\"Flow_0yhobgw\">\n        <di:waypoint x=\"276\" y=\"218\" />\n        <di:waypoint x=\"330\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent_1\" bpmnElement=\"startEvent_1\">\n        <dc:Bounds x=\"240\" y=\"200\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"247\" y=\"243\" width=\"23\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_06t0e98_di\" bpmnElement=\"Activity_06t0e98\">\n        <dc:Bounds x=\"330\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_1harg1w_di\" bpmnElement=\"Event_1harg1w\">\n        <dc:Bounds x=\"702\" y=\"200\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_0y82hwj_di\" bpmnElement=\"Activity_0y82hwj\">\n        <dc:Bounds x=\"500\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</definitions>\n', '{\"bounds\":{\"lowerRight\":{\"x\":1485.0,\"y\":700.0},\"upperLeft\":{\"x\":0.0,\"y\":0.0}},\"resourceId\":\"canvas\",\"stencil\":{\"id\":\"BPMNDiagram\"},\"stencilset\":{\"namespace\":\"http://b3mn.org/stencilset/bpmn2.0#\",\"url\":\"../editor/stencilsets/bpmn2.0/bpmn2.0.json\"},\"properties\":{\"process_id\":\"pledge_goods_loan\",\"name\":\"动产质押放款申请\",\"process_namespace\":\"http://bpmn.io/schema/bpmn\",\"iseagerexecutionfetch\":false,\"messages\":[],\"executionlisteners\":{\"executionListeners\":[]},\"eventlisteners\":{\"eventListeners\":[]},\"signaldefinitions\":[],\"messagedefinitions\":[],\"escalationdefinitions\":[]},\"childShapes\":[{\"bounds\":{\"lowerRight\":{\"x\":276.0,\"y\":236.0},\"upperLeft\":{\"x\":240.0,\"y\":200.0}},\"resourceId\":\"startEvent_1\",\"childShapes\":[],\"stencil\":{\"id\":\"StartNoneEvent\"},\"properties\":{\"overrideid\":\"startEvent_1\",\"name\":\"开始\",\"interrupting\":true,\"formproperties\":{\"formProperties\":[{\"id\":\"reimbursementTrial\",\"name\":\"还款试算\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"reimbursementTrial_bankInterest\",\"name\":\"资方费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_periods\",\"name\":\"期数\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_repaymentDate\",\"name\":\"还款日期\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_totalShouldAlso\",\"name\":\"应还总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_repaymentPrincipal\",\"name\":\"还款本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_shouldAlsoInterest\",\"name\":\"应还利息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"reimbursementTrial_costPlatform\",\"name\":\"平台费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_costOfName\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_typeOfExpense\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_payTheNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_chargeMode\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_amountPayable\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable\",\"name\":\"保证金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_type\",\"name\":\"保证金类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_payNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_chargeMode\",\"name\":\"计算方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_returnMethods\",\"name\":\"退还方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_amount\",\"name\":\"应缴金额(元)\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_checkPaymentVoucher\",\"name\":\"查看支付凭证\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_bank\",\"name\":\"付款开户行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_bankCard\",\"name\":\"付款账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_payTime\",\"name\":\"支付时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee\",\"name\":\"缴纳费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_costOfName\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_typeOfExpense\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_payTheNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_chargeMode\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_amountPayable\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_bank\",\"name\":\"付款开户行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_bankCard\",\"name\":\"付款账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_payTime\",\"name\":\"支付时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_checkPaymentVoucher\",\"name\":\"查看支付凭证\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning\",\"name\":\"合同签署\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_index\",\"name\":\"#\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_contractNumber\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_creationTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_signTheState\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_operation\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0yhobgw\"}]},{\"bounds\":{\"lowerRight\":{\"x\":430.0,\"y\":258.0},\"upperLeft\":{\"x\":330.0,\"y\":178.0}},\"resourceId\":\"Activity_06t0e98\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_06t0e98\",\"name\":\"发起人\",\"formproperties\":{\"formProperties\":[{\"id\":\"reimbursementTrial\",\"name\":\"还款试算\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"reimbursementTrial_bankInterest\",\"name\":\"资方费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_periods\",\"name\":\"期数\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_repaymentDate\",\"name\":\"还款日期\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_totalShouldAlso\",\"name\":\"应还总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_repaymentPrincipal\",\"name\":\"还款本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_shouldAlsoInterest\",\"name\":\"应还利息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"reimbursementTrial_costPlatform\",\"name\":\"平台费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_costOfName\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_typeOfExpense\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_payTheNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_chargeMode\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_amountPayable\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable\",\"name\":\"保证金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_type\",\"name\":\"保证金类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_payNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_chargeMode\",\"name\":\"计算方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_returnMethods\",\"name\":\"退还方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_amount\",\"name\":\"应缴金额(元)\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_checkPaymentVoucher\",\"name\":\"查看支付凭证\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_bank\",\"name\":\"付款开户行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_bankCard\",\"name\":\"付款账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_payTime\",\"name\":\"支付时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee\",\"name\":\"缴纳费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_costOfName\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_typeOfExpense\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_payTheNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_chargeMode\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_amountPayable\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_bank\",\"name\":\"付款开户行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_bankCard\",\"name\":\"付款账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_payTime\",\"name\":\"支付时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_checkPaymentVoucher\",\"name\":\"查看支付凭证\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning\",\"name\":\"合同签署\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_index\",\"name\":\"#\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_contractNumber\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_creationTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_signTheState\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_operation\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_1rjr8in\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0yhobgw\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":18.0,\"y\":18.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_06t0e98\"}],\"target\":{\"resourceId\":\"Activity_06t0e98\"},\"properties\":{\"overrideid\":\"Flow_0yhobgw\"}},{\"bounds\":{\"lowerRight\":{\"x\":738.0,\"y\":236.0},\"upperLeft\":{\"x\":702.0,\"y\":200.0}},\"resourceId\":\"Event_1harg1w\",\"childShapes\":[],\"stencil\":{\"id\":\"EndNoneEvent\"},\"properties\":{\"overrideid\":\"Event_1harg1w\",\"executionlisteners\":{\"executionListeners\":[{\"event\":\"end\",\"delegateExpression\":\"${pledgeLoanListener}\"},{\"event\":\"end\",\"delegateExpression\":\"${contractProcessListener}\"}]}},\"outgoing\":[]},{\"bounds\":{\"lowerRight\":{\"x\":600.0,\"y\":258.0},\"upperLeft\":{\"x\":500.0,\"y\":178.0}},\"resourceId\":\"Activity_0y82hwj\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_0y82hwj\",\"name\":\"业务审批\",\"formproperties\":{\"formProperties\":[{\"id\":\"reimbursementTrial\",\"name\":\"还款试算\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"reimbursementTrial_bankInterest\",\"name\":\"资方费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_periods\",\"name\":\"期数\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_repaymentDate\",\"name\":\"还款日期\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_totalShouldAlso\",\"name\":\"应还总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_repaymentPrincipal\",\"name\":\"还款本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankInterest_shouldAlsoInterest\",\"name\":\"应还利息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"reimbursementTrial_costPlatform\",\"name\":\"平台费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_costOfName\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_typeOfExpense\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_payTheNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_chargeMode\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"payAFee_amountPayable\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable\",\"name\":\"保证金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_type\",\"name\":\"保证金类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_payNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_chargeMode\",\"name\":\"计算方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_returnMethods\",\"name\":\"退还方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_amount\",\"name\":\"应缴金额(元)\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_checkPaymentVoucher\",\"name\":\"查看支付凭证\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_bank\",\"name\":\"付款开户行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_bankCard\",\"name\":\"付款账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bondTable_payTime\",\"name\":\"支付时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee\",\"name\":\"缴纳费用\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_costOfName\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_typeOfExpense\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_payTheNode\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_chargeMode\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_amountPayable\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_bank\",\"name\":\"付款开户行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_bankCard\",\"name\":\"付款账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_payTime\",\"name\":\"支付时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"platformFee_checkPaymentVoucher\",\"name\":\"查看支付凭证\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning\",\"name\":\"合同签署\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_index\",\"name\":\"#\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_contractNumber\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_creationTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_signTheState\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contractSigning_operation\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0vhotwm\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_1rjr8in\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_0y82hwj\"}],\"target\":{\"resourceId\":\"Activity_0y82hwj\"},\"properties\":{\"overrideid\":\"Flow_1rjr8in\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0vhotwm\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":18.0,\"y\":18.0}],\"outgoing\":[{\"resourceId\":\"Event_1harg1w\"}],\"target\":{\"resourceId\":\"Event_1harg1w\"},\"properties\":{\"overrideid\":\"Flow_0vhotwm\"}}]}', NULL, 0, '000000');
INSERT INTO `ACT_DE_MODEL`(`id`, `name`, `model_key`, `form_key`, `category_id`, `description`, `model_comment`, `created`, `created_by`, `last_updated`, `last_updated_by`, `version`, `xml`, `model_editor_json`, `thumbnail`, `model_type`, `tenant_id`) VALUES ('11f0439d8b0b9810d49f3ffabc146358', '动产质押赎货申请', 'pledge_redeem_apply', 'wf_ex_pledge_redeem', 1651517399989542913, NULL, NULL, '2023-04-27 17:33:57.971000', '1568205681205551106', '2023-05-15 11:51:47.990000', '1568205681205551106', 3, '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:activiti=\"http://activiti.org/bpmn\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n  <process id=\"pledge_redeem_apply\" name=\"动产质押赎货申请\" isExecutable=\"true\" flowable:skipFirstNode=\"true\" flowable:rollbackNode=\"Activity_06t0e98\">\n    <startEvent id=\"startEvent_1\" name=\"开始\">\n      <extensionElements>\n        <flowable:exFormKey value=\"pledge_redeem\" />\n        <flowable:formProperty id=\"redemptionNote\" name=\"赎货单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemNo\" name=\"赎货单号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redemptionDate\" name=\"约定赎货日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_extractType\" name=\"提货方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_delivery\" name=\"收货地址\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"银行还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_yearRate\" name=\"年化利率\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_repayFunds\" name=\"应还本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform\" name=\"平台费用单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_totalRepayment\" name=\"应还总额\" readable=\"true\" writable=\"true\" />\n      </extensionElements>\n      <outgoing>Flow_0yhobgw</outgoing>\n    </startEvent>\n    <userTask id=\"Activity_06t0e98\" name=\"发起人\" flowable:exFormKey=\"pledge_redeem\">\n      <extensionElements>\n        <flowable:assignee type=\"custom\" value=\"applyUser\" text=\"流程发起人\" />\n        <flowable:formProperty id=\"redemptionNote\" name=\"赎货单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemNo\" name=\"赎货单号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redemptionDate\" name=\"约定赎货日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_extractType\" name=\"提货方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_delivery\" name=\"收货地址\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"银行还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_yearRate\" name=\"年化利率\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_repayFunds\" name=\"应还本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform\" name=\"平台费用单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_totalRepayment\" name=\"应还总额\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_0yhobgw</incoming>\n      <outgoing>Flow_01md50k</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_0yhobgw\" sourceRef=\"startEvent_1\" targetRef=\"Activity_06t0e98\" />\n    <userTask id=\"Activity_1151kuo\" name=\"业务审核\" flowable:exFormKey=\"pledge_redeem\">\n      <extensionElements>\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:formProperty id=\"redemptionNote\" name=\"赎货单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemNo\" name=\"赎货单号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redemptionDate\" name=\"约定赎货日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_extractType\" name=\"提货方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_delivery\" name=\"收货地址\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"银行还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_yearRate\" name=\"年化利率\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_repayFunds\" name=\"应还本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform\" name=\"平台费用单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_totalRepayment\" name=\"应还总额\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_01md50k</incoming>\n      <outgoing>Flow_1j7xo8r</outgoing>\n    </userTask>\n    <endEvent id=\"Event_0vx6lpe\">\n      <extensionElements>\n        <flowable:executionListener delegateExpression=\"${pledgeRedeemApplyListener}\" event=\"end\" />\n      </extensionElements>\n      <incoming>Flow_1j7xo8r</incoming>\n    </endEvent>\n    <sequenceFlow id=\"Flow_01md50k\" sourceRef=\"Activity_06t0e98\" targetRef=\"Activity_1151kuo\" />\n    <sequenceFlow id=\"Flow_1j7xo8r\" sourceRef=\"Activity_1151kuo\" targetRef=\"Event_0vx6lpe\" />\n  </process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_flow\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_flow\" bpmnElement=\"pledge_redeem_apply\">\n      <bpmndi:BPMNEdge id=\"Flow_1j7xo8r_di\" bpmnElement=\"Flow_1j7xo8r\">\n        <di:waypoint x=\"620\" y=\"218\" />\n        <di:waypoint x=\"712\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_01md50k_di\" bpmnElement=\"Flow_01md50k\">\n        <di:waypoint x=\"430\" y=\"218\" />\n        <di:waypoint x=\"520\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0yhobgw_di\" bpmnElement=\"Flow_0yhobgw\">\n        <di:waypoint x=\"276\" y=\"218\" />\n        <di:waypoint x=\"330\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent_1\" bpmnElement=\"startEvent_1\">\n        <dc:Bounds x=\"240\" y=\"200\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"247\" y=\"243\" width=\"23\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_06t0e98_di\" bpmnElement=\"Activity_06t0e98\">\n        <dc:Bounds x=\"330\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_1151kuo_di\" bpmnElement=\"Activity_1151kuo\">\n        <dc:Bounds x=\"520\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_0vx6lpe_di\" bpmnElement=\"Event_0vx6lpe\">\n        <dc:Bounds x=\"712\" y=\"200\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</definitions>\n', '{\"bounds\":{\"lowerRight\":{\"x\":1485.0,\"y\":700.0},\"upperLeft\":{\"x\":0.0,\"y\":0.0}},\"resourceId\":\"canvas\",\"stencil\":{\"id\":\"BPMNDiagram\"},\"stencilset\":{\"namespace\":\"http://b3mn.org/stencilset/bpmn2.0#\",\"url\":\"../editor/stencilsets/bpmn2.0/bpmn2.0.json\"},\"properties\":{\"process_id\":\"pledge_redeem_apply\",\"name\":\"动产质押赎货申请\",\"process_namespace\":\"http://bpmn.io/schema/bpmn\",\"iseagerexecutionfetch\":false,\"messages\":[],\"executionlisteners\":{\"executionListeners\":[]},\"eventlisteners\":{\"eventListeners\":[]},\"signaldefinitions\":[],\"messagedefinitions\":[],\"escalationdefinitions\":[]},\"childShapes\":[{\"bounds\":{\"lowerRight\":{\"x\":276.0,\"y\":236.0},\"upperLeft\":{\"x\":240.0,\"y\":200.0}},\"resourceId\":\"startEvent_1\",\"childShapes\":[],\"stencil\":{\"id\":\"StartNoneEvent\"},\"properties\":{\"overrideid\":\"startEvent_1\",\"name\":\"开始\",\"interrupting\":true,\"formproperties\":{\"formProperties\":[{\"id\":\"redemptionNote\",\"name\":\"赎货单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemNo\",\"name\":\"赎货单号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redemptionDate\",\"name\":\"约定赎货日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_extractType\",\"name\":\"提货方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_delivery\",\"name\":\"收货地址\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"银行还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_yearRate\",\"name\":\"年化利率\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_repayFunds\",\"name\":\"应还本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform\",\"name\":\"平台费用单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_totalRepayment\",\"name\":\"应还总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0yhobgw\"}]},{\"bounds\":{\"lowerRight\":{\"x\":430.0,\"y\":258.0},\"upperLeft\":{\"x\":330.0,\"y\":178.0}},\"resourceId\":\"Activity_06t0e98\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_06t0e98\",\"name\":\"发起人\",\"formproperties\":{\"formProperties\":[{\"id\":\"redemptionNote\",\"name\":\"赎货单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemNo\",\"name\":\"赎货单号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redemptionDate\",\"name\":\"约定赎货日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_extractType\",\"name\":\"提货方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_delivery\",\"name\":\"收货地址\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"银行还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_yearRate\",\"name\":\"年化利率\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_repayFunds\",\"name\":\"应还本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform\",\"name\":\"平台费用单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_totalRepayment\",\"name\":\"应还总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_01md50k\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0yhobgw\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":18.0,\"y\":18.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_06t0e98\"}],\"target\":{\"resourceId\":\"Activity_06t0e98\"},\"properties\":{\"overrideid\":\"Flow_0yhobgw\"}},{\"bounds\":{\"lowerRight\":{\"x\":620.0,\"y\":258.0},\"upperLeft\":{\"x\":520.0,\"y\":178.0}},\"resourceId\":\"Activity_1151kuo\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_1151kuo\",\"name\":\"业务审核\",\"formproperties\":{\"formProperties\":[{\"id\":\"redemptionNote\",\"name\":\"赎货单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemNo\",\"name\":\"赎货单号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redemptionDate\",\"name\":\"约定赎货日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_extractType\",\"name\":\"提货方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_delivery\",\"name\":\"收货地址\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"银行还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_yearRate\",\"name\":\"年化利率\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_repayFunds\",\"name\":\"应还本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform\",\"name\":\"平台费用单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_totalRepayment\",\"name\":\"应还总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_1j7xo8r\"}]},{\"bounds\":{\"lowerRight\":{\"x\":748.0,\"y\":236.0},\"upperLeft\":{\"x\":712.0,\"y\":200.0}},\"resourceId\":\"Event_0vx6lpe\",\"childShapes\":[],\"stencil\":{\"id\":\"EndNoneEvent\"},\"properties\":{\"overrideid\":\"Event_0vx6lpe\",\"executionlisteners\":{\"executionListeners\":[{\"event\":\"end\",\"delegateExpression\":\"${pledgeRedeemApplyListener}\"}]}},\"outgoing\":[]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_01md50k\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_1151kuo\"}],\"target\":{\"resourceId\":\"Activity_1151kuo\"},\"properties\":{\"overrideid\":\"Flow_01md50k\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_1j7xo8r\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":18.0,\"y\":18.0}],\"outgoing\":[{\"resourceId\":\"Event_0vx6lpe\"}],\"target\":{\"resourceId\":\"Event_0vx6lpe\"},\"properties\":{\"overrideid\":\"Flow_1j7xo8r\"}}]}', NULL, 0, '000000');
INSERT INTO `ACT_DE_MODEL`(`id`, `name`, `model_key`, `form_key`, `category_id`, `description`, `model_comment`, `created`, `created_by`, `last_updated`, `last_updated_by`, `version`, `xml`, `model_editor_json`, `thumbnail`, `model_type`, `tenant_id`) VALUES ('1e112d9c5ffd38a03234a9c223e570f1', '动产质押货物处置测试版', 'pledge_cargo_solve1', 'wf_ex_cargo_solves', 1661579605229780993, NULL, NULL, '2023-06-07 17:13:51.059000', '1568205681205551106', '2023-06-07 17:16:13.346000', '1568205681205551106', 3, '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:activiti=\"http://activiti.org/bpmn\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n  <process id=\"pledge_cargo_solve\" name=\"动产质押货物处置测试版\" isExecutable=\"true\" flowable:skipFirstNode=\"true\" flowable:rollbackNode=\"Activity_06t0e98\">\n    <startEvent id=\"startEvent_1\" name=\"请先点击我初始化节点\">\n      <outgoing>Flow_0yhobgw</outgoing>\n    </startEvent>\n    <userTask id=\"Activity_06t0e98\" name=\"发起人\">\n      <extensionElements>\n        <flowable:assignee type=\"custom\" value=\"applyUser\" text=\"流程发起人\" />\n      </extensionElements>\n      <incoming>Flow_0yhobgw</incoming>\n      <outgoing>Flow_1uuzknl</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_0yhobgw\" sourceRef=\"startEvent_1\" targetRef=\"Activity_06t0e98\" />\n    <userTask id=\"Activity_172ucif\" name=\"审核人\" flowable:exFormKey=\"cargo_solves\">\n      <extensionElements>\n        <flowable:formProperty id=\"1\" name=\"1\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"1\" name=\"1\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n      </extensionElements>\n      <incoming>Flow_1uuzknl</incoming>\n      <outgoing>Flow_01zmkq4</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_1uuzknl\" sourceRef=\"Activity_06t0e98\" targetRef=\"Activity_172ucif\" />\n    <endEvent id=\"Event_01wewbs\">\n      <incoming>Flow_01zmkq4</incoming>\n    </endEvent>\n    <sequenceFlow id=\"Flow_01zmkq4\" sourceRef=\"Activity_172ucif\" targetRef=\"Event_01wewbs\" />\n  </process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_flow\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_flow\" bpmnElement=\"pledge_cargo_solve\">\n      <bpmndi:BPMNEdge id=\"Flow_01zmkq4_di\" bpmnElement=\"Flow_01zmkq4\">\n        <di:waypoint x=\"570\" y=\"218\" />\n        <di:waypoint x=\"662\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1uuzknl_di\" bpmnElement=\"Flow_1uuzknl\">\n        <di:waypoint x=\"430\" y=\"218\" />\n        <di:waypoint x=\"470\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0yhobgw_di\" bpmnElement=\"Flow_0yhobgw\">\n        <di:waypoint x=\"276\" y=\"218\" />\n        <di:waypoint x=\"330\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent_1\" bpmnElement=\"startEvent_1\">\n        <dc:Bounds x=\"240\" y=\"200\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"247\" y=\"243\" width=\"22\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_06t0e98_di\" bpmnElement=\"Activity_06t0e98\">\n        <dc:Bounds x=\"330\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_172ucif_di\" bpmnElement=\"Activity_172ucif\">\n        <dc:Bounds x=\"470\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_01wewbs_di\" bpmnElement=\"Event_01wewbs\">\n        <dc:Bounds x=\"662\" y=\"200\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</definitions>\n', '{\"bounds\":{\"lowerRight\":{\"x\":1485.0,\"y\":700.0},\"upperLeft\":{\"x\":0.0,\"y\":0.0}},\"resourceId\":\"canvas\",\"stencil\":{\"id\":\"BPMNDiagram\"},\"stencilset\":{\"namespace\":\"http://b3mn.org/stencilset/bpmn2.0#\",\"url\":\"../editor/stencilsets/bpmn2.0/bpmn2.0.json\"},\"properties\":{\"process_id\":\"pledge_cargo_solve\",\"name\":\"动产质押货物处置测试版\",\"process_namespace\":\"http://bpmn.io/schema/bpmn\",\"iseagerexecutionfetch\":false,\"messages\":[],\"executionlisteners\":{\"executionListeners\":[]},\"eventlisteners\":{\"eventListeners\":[]},\"signaldefinitions\":[],\"messagedefinitions\":[],\"escalationdefinitions\":[]},\"childShapes\":[{\"bounds\":{\"lowerRight\":{\"x\":276.0,\"y\":236.0},\"upperLeft\":{\"x\":240.0,\"y\":200.0}},\"resourceId\":\"startEvent_1\",\"childShapes\":[],\"stencil\":{\"id\":\"StartNoneEvent\"},\"properties\":{\"overrideid\":\"startEvent_1\",\"name\":\"请先点击我初始化节点\",\"interrupting\":true,\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0yhobgw\"}]},{\"bounds\":{\"lowerRight\":{\"x\":430.0,\"y\":258.0},\"upperLeft\":{\"x\":330.0,\"y\":178.0}},\"resourceId\":\"Activity_06t0e98\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_06t0e98\",\"name\":\"发起人\",\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_1uuzknl\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0yhobgw\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":18.0,\"y\":18.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_06t0e98\"}],\"target\":{\"resourceId\":\"Activity_06t0e98\"},\"properties\":{\"overrideid\":\"Flow_0yhobgw\"}},{\"bounds\":{\"lowerRight\":{\"x\":570.0,\"y\":258.0},\"upperLeft\":{\"x\":470.0,\"y\":178.0}},\"resourceId\":\"Activity_172ucif\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_172ucif\",\"name\":\"审核人\",\"formproperties\":{\"formProperties\":[{\"id\":\"1\",\"name\":\"1\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"1\",\"name\":\"1\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_01zmkq4\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_1uuzknl\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_172ucif\"}],\"target\":{\"resourceId\":\"Activity_172ucif\"},\"properties\":{\"overrideid\":\"Flow_1uuzknl\"}},{\"bounds\":{\"lowerRight\":{\"x\":698.0,\"y\":236.0},\"upperLeft\":{\"x\":662.0,\"y\":200.0}},\"resourceId\":\"Event_01wewbs\",\"childShapes\":[],\"stencil\":{\"id\":\"EndNoneEvent\"},\"properties\":{\"overrideid\":\"Event_01wewbs\",\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_01zmkq4\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":18.0,\"y\":18.0}],\"outgoing\":[{\"resourceId\":\"Event_01wewbs\"}],\"target\":{\"resourceId\":\"Event_01wewbs\"},\"properties\":{\"overrideid\":\"Flow_01zmkq4\"}}]}', NULL, 0, '000000');
INSERT INTO `ACT_DE_MODEL`(`id`, `name`, `model_key`, `form_key`, `category_id`, `description`, `model_comment`, `created`, `created_by`, `last_updated`, `last_updated_by`, `version`, `xml`, `model_editor_json`, `thumbnail`, `model_type`, `tenant_id`) VALUES ('57b192a561970a40ce4168a2c90dc978', '动产质押货物处置', 'pledge_cargo_solve', 'wf_ex_pledge_solve_cargo', 1661579605229780993, NULL, NULL, '2023-06-12 10:38:07.913000', '1568205681205551106', '2023-06-12 10:48:46.542000', '1568205681205551106', 3, '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:activiti=\"http://activiti.org/bpmn\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n  <process id=\"pledge_cargo_solve\" name=\"动产质押货物处置\" isExecutable=\"true\" flowable:skipFirstNode=\"true\" flowable:rollbackNode=\"Activity_06t0e98\">\n    <startEvent id=\"startEvent_1\" name=\"请先点击我初始化节点\">\n      <outgoing>Flow_0yhobgw</outgoing>\n    </startEvent>\n    <userTask id=\"Activity_06t0e98\" name=\"发起人\">\n      <extensionElements>\n        <flowable:assignee type=\"custom\" value=\"applyUser\" text=\"流程发起人\" />\n      </extensionElements>\n      <incoming>Flow_0yhobgw</incoming>\n      <outgoing>Flow_14cyhnc</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_0yhobgw\" sourceRef=\"startEvent_1\" targetRef=\"Activity_06t0e98\" />\n    <sequenceFlow id=\"Flow_14cyhnc\" sourceRef=\"Activity_06t0e98\" targetRef=\"Gateway_1f010tv\" />\n    <endEvent id=\"Event_0cehef7\">\n      <extensionElements>\n        <flowable:executionListener delegateExpression=\"${pledgeCargoSolveListener}\" event=\"end\" />\n      </extensionElements>\n      <incoming>Flow_0h66lry</incoming>\n    </endEvent>\n    <exclusiveGateway id=\"Gateway_1f010tv\" name=\"是否走分账审批\">\n      <incoming>Flow_14cyhnc</incoming>\n      <outgoing>Flow_1sg2yoy</outgoing>\n      <outgoing>Flow_1a4kfat</outgoing>\n    </exclusiveGateway>\n    <userTask id=\"Activity_068o3tt\" name=\"分账审批\" flowable:exFormKey=\"pledge_solve_cargo\">\n      <extensionElements>\n        <flowable:taskListener delegateExpression=\"${pledgeDivideAccountListener}\" event=\"complete\" />\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634,1601471895121731586\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员,供广业务岗\" />\n        <flowable:formProperty id=\"cargo_solve\" name=\"处置信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargo_solve_cargoSolveCompany\" name=\"处置公司\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargo_solve_cargoSolvePerson\" name=\"处置负责人\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargo_solve_cargoSolvePhone\" name=\"处置人联系电话\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard\" name=\"退回信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_bankName\" name=\"开户银行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_enterpriseName\" name=\"开户人\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_bankCardNo\" name=\"银行账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_returnAmount\" name=\"退回金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList\" name=\"处置比例\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_payable\" name=\"应付\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_paymentRatio\" name=\"分账比例\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_actualPayment\" name=\"分账金额\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_1sg2yoy</incoming>\n      <outgoing>Flow_026vinr</outgoing>\n    </userTask>\n    <userTask id=\"Activity_1tpvdch\" name=\"业务审批\" flowable:exFormKey=\"pledge_solve_cargo\">\n      <extensionElements>\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:formProperty id=\"cargo_solve\" name=\"处置信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargo_solve_cargoSolveCompany\" name=\"处置公司\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargo_solve_cargoSolvePerson\" name=\"处置负责人\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargo_solve_cargoSolvePhone\" name=\"处置人联系电话\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard\" name=\"退回信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_bankName\" name=\"开户银行\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_enterpriseName\" name=\"开户人\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_bankCardNo\" name=\"银行账号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"bankCard_returnAmount\" name=\"退回金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList\" name=\"处置比例\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_payable\" name=\"应付\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_paymentRatio\" name=\"分账比例\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargoSolveExpenseList_actualPayment\" name=\"分账金额\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_1167shz</incoming>\n      <outgoing>Flow_0h66lry</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_0h66lry\" sourceRef=\"Activity_1tpvdch\" targetRef=\"Event_0cehef7\" />\n    <exclusiveGateway id=\"Gateway_12vqbjq\">\n      <incoming>Flow_026vinr</incoming>\n      <incoming>Flow_1a4kfat</incoming>\n      <outgoing>Flow_1167shz</outgoing>\n    </exclusiveGateway>\n    <sequenceFlow id=\"Flow_1sg2yoy\" name=\"y\" sourceRef=\"Gateway_1f010tv\" targetRef=\"Activity_068o3tt\">\n      <conditionExpression xsi:type=\"tFormalExpression\">${isDivideAccounts == 1}</conditionExpression>\n    </sequenceFlow>\n    <sequenceFlow id=\"Flow_026vinr\" sourceRef=\"Activity_068o3tt\" targetRef=\"Gateway_12vqbjq\" />\n    <sequenceFlow id=\"Flow_1a4kfat\" name=\"n\" sourceRef=\"Gateway_1f010tv\" targetRef=\"Gateway_12vqbjq\">\n      <conditionExpression xsi:type=\"tFormalExpression\">${isDivideAccounts == 2} </conditionExpression>\n    </sequenceFlow>\n    <sequenceFlow id=\"Flow_1167shz\" sourceRef=\"Gateway_12vqbjq\" targetRef=\"Activity_1tpvdch\" />\n  </process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_flow\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_flow\" bpmnElement=\"pledge_cargo_solve\">\n      <bpmndi:BPMNEdge id=\"Flow_1167shz_di\" bpmnElement=\"Flow_1167shz\">\n        <di:waypoint x=\"755\" y=\"218\" />\n        <di:waypoint x=\"850\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1a4kfat_di\" bpmnElement=\"Flow_1a4kfat\">\n        <di:waypoint x=\"535\" y=\"218\" />\n        <di:waypoint x=\"705\" y=\"218\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"617\" y=\"200\" width=\"7\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_026vinr_di\" bpmnElement=\"Flow_026vinr\">\n        <di:waypoint x=\"670\" y=\"140\" />\n        <di:waypoint x=\"730\" y=\"140\" />\n        <di:waypoint x=\"730\" y=\"193\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1sg2yoy_di\" bpmnElement=\"Flow_1sg2yoy\">\n        <di:waypoint x=\"510\" y=\"193\" />\n        <di:waypoint x=\"510\" y=\"140\" />\n        <di:waypoint x=\"570\" y=\"140\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"522\" y=\"164\" width=\"7\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0h66lry_di\" bpmnElement=\"Flow_0h66lry\">\n        <di:waypoint x=\"950\" y=\"218\" />\n        <di:waypoint x=\"1052\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_14cyhnc_di\" bpmnElement=\"Flow_14cyhnc\">\n        <di:waypoint x=\"430\" y=\"218\" />\n        <di:waypoint x=\"485\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0yhobgw_di\" bpmnElement=\"Flow_0yhobgw\">\n        <di:waypoint x=\"276\" y=\"218\" />\n        <di:waypoint x=\"330\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent_1\" bpmnElement=\"startEvent_1\">\n        <dc:Bounds x=\"240\" y=\"200\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"247\" y=\"243\" width=\"22\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_06t0e98_di\" bpmnElement=\"Activity_06t0e98\">\n        <dc:Bounds x=\"330\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_0cehef7_di\" bpmnElement=\"Event_0cehef7\">\n        <dc:Bounds x=\"1052\" y=\"200\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Gateway_1f010tv_di\" bpmnElement=\"Gateway_1f010tv\" isMarkerVisible=\"true\">\n        <dc:Bounds x=\"485\" y=\"193\" width=\"50\" height=\"50\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"472\" y=\"250\" width=\"77\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_068o3tt_di\" bpmnElement=\"Activity_068o3tt\">\n        <dc:Bounds x=\"570\" y=\"100\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_1tpvdch_di\" bpmnElement=\"Activity_1tpvdch\">\n        <dc:Bounds x=\"850\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Gateway_12vqbjq_di\" bpmnElement=\"Gateway_12vqbjq\" isMarkerVisible=\"true\">\n        <dc:Bounds x=\"705\" y=\"193\" width=\"50\" height=\"50\" />\n      </bpmndi:BPMNShape>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</definitions>\n', '{\"bounds\":{\"lowerRight\":{\"x\":1485.0,\"y\":700.0},\"upperLeft\":{\"x\":0.0,\"y\":0.0}},\"resourceId\":\"canvas\",\"stencil\":{\"id\":\"BPMNDiagram\"},\"stencilset\":{\"namespace\":\"http://b3mn.org/stencilset/bpmn2.0#\",\"url\":\"../editor/stencilsets/bpmn2.0/bpmn2.0.json\"},\"properties\":{\"process_id\":\"pledge_cargo_solve\",\"name\":\"动产质押货物处置\",\"process_namespace\":\"http://bpmn.io/schema/bpmn\",\"iseagerexecutionfetch\":false,\"messages\":[],\"executionlisteners\":{\"executionListeners\":[]},\"eventlisteners\":{\"eventListeners\":[]},\"signaldefinitions\":[],\"messagedefinitions\":[],\"escalationdefinitions\":[]},\"childShapes\":[{\"bounds\":{\"lowerRight\":{\"x\":276.0,\"y\":236.0},\"upperLeft\":{\"x\":240.0,\"y\":200.0}},\"resourceId\":\"startEvent_1\",\"childShapes\":[],\"stencil\":{\"id\":\"StartNoneEvent\"},\"properties\":{\"overrideid\":\"startEvent_1\",\"name\":\"请先点击我初始化节点\",\"interrupting\":true,\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0yhobgw\"}]},{\"bounds\":{\"lowerRight\":{\"x\":430.0,\"y\":258.0},\"upperLeft\":{\"x\":330.0,\"y\":178.0}},\"resourceId\":\"Activity_06t0e98\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_06t0e98\",\"name\":\"发起人\",\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_14cyhnc\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0yhobgw\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":18.0,\"y\":18.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_06t0e98\"}],\"target\":{\"resourceId\":\"Activity_06t0e98\"},\"properties\":{\"overrideid\":\"Flow_0yhobgw\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_14cyhnc\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":25.0,\"y\":25.0}],\"outgoing\":[{\"resourceId\":\"Gateway_1f010tv\"}],\"target\":{\"resourceId\":\"Gateway_1f010tv\"},\"properties\":{\"overrideid\":\"Flow_14cyhnc\"}},{\"bounds\":{\"lowerRight\":{\"x\":1088.0,\"y\":236.0},\"upperLeft\":{\"x\":1052.0,\"y\":200.0}},\"resourceId\":\"Event_0cehef7\",\"childShapes\":[],\"stencil\":{\"id\":\"EndNoneEvent\"},\"properties\":{\"overrideid\":\"Event_0cehef7\",\"executionlisteners\":{\"executionListeners\":[{\"event\":\"end\",\"delegateExpression\":\"${pledgeCargoSolveListener}\"}]}},\"outgoing\":[]},{\"bounds\":{\"lowerRight\":{\"x\":535.0,\"y\":243.0},\"upperLeft\":{\"x\":485.0,\"y\":193.0}},\"resourceId\":\"Gateway_1f010tv\",\"childShapes\":[],\"stencil\":{\"id\":\"ExclusiveGateway\"},\"properties\":{\"overrideid\":\"Gateway_1f010tv\",\"name\":\"是否走分账审批\",\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_1sg2yoy\"},{\"resourceId\":\"Flow_1a4kfat\"}]},{\"bounds\":{\"lowerRight\":{\"x\":670.0,\"y\":180.0},\"upperLeft\":{\"x\":570.0,\"y\":100.0}},\"resourceId\":\"Activity_068o3tt\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_068o3tt\",\"name\":\"分账审批\",\"formproperties\":{\"formProperties\":[{\"id\":\"cargo_solve\",\"name\":\"处置信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargo_solve_cargoSolveCompany\",\"name\":\"处置公司\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargo_solve_cargoSolvePerson\",\"name\":\"处置负责人\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargo_solve_cargoSolvePhone\",\"name\":\"处置人联系电话\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard\",\"name\":\"退回信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_bankName\",\"name\":\"开户银行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_enterpriseName\",\"name\":\"开户人\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_bankCardNo\",\"name\":\"银行账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_returnAmount\",\"name\":\"退回金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList\",\"name\":\"处置比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_payable\",\"name\":\"应付\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_paymentRatio\",\"name\":\"分账比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_actualPayment\",\"name\":\"分账金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[{\"event\":\"complete\",\"delegateExpression\":\"${pledgeDivideAccountListener}\"}]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_026vinr\"}]},{\"bounds\":{\"lowerRight\":{\"x\":950.0,\"y\":258.0},\"upperLeft\":{\"x\":850.0,\"y\":178.0}},\"resourceId\":\"Activity_1tpvdch\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_1tpvdch\",\"name\":\"业务审批\",\"formproperties\":{\"formProperties\":[{\"id\":\"cargo_solve\",\"name\":\"处置信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargo_solve_cargoSolveCompany\",\"name\":\"处置公司\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargo_solve_cargoSolvePerson\",\"name\":\"处置负责人\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargo_solve_cargoSolvePhone\",\"name\":\"处置人联系电话\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard\",\"name\":\"退回信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_bankName\",\"name\":\"开户银行\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_enterpriseName\",\"name\":\"开户人\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_bankCardNo\",\"name\":\"银行账号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"bankCard_returnAmount\",\"name\":\"退回金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList\",\"name\":\"处置比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_payable\",\"name\":\"应付\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_paymentRatio\",\"name\":\"分账比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargoSolveExpenseList_actualPayment\",\"name\":\"分账金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0h66lry\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0h66lry\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":18.0,\"y\":18.0}],\"outgoing\":[{\"resourceId\":\"Event_0cehef7\"}],\"target\":{\"resourceId\":\"Event_0cehef7\"},\"properties\":{\"overrideid\":\"Flow_0h66lry\"}},{\"bounds\":{\"lowerRight\":{\"x\":755.0,\"y\":243.0},\"upperLeft\":{\"x\":705.0,\"y\":193.0}},\"resourceId\":\"Gateway_12vqbjq\",\"childShapes\":[],\"stencil\":{\"id\":\"ExclusiveGateway\"},\"properties\":{\"overrideid\":\"Gateway_12vqbjq\",\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_1167shz\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_1sg2yoy\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":25.0,\"y\":25.0},{\"x\":510.0,\"y\":140.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_068o3tt\"}],\"target\":{\"resourceId\":\"Activity_068o3tt\"},\"properties\":{\"overrideid\":\"Flow_1sg2yoy\",\"name\":\"y\",\"conditionsequenceflow\":\"${isDivideAccounts == 1}\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_026vinr\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":730.0,\"y\":140.0},{\"x\":25.0,\"y\":25.0}],\"outgoing\":[{\"resourceId\":\"Gateway_12vqbjq\"}],\"target\":{\"resourceId\":\"Gateway_12vqbjq\"},\"properties\":{\"overrideid\":\"Flow_026vinr\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_1a4kfat\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":25.0,\"y\":25.0},{\"x\":25.0,\"y\":25.0}],\"outgoing\":[{\"resourceId\":\"Gateway_12vqbjq\"}],\"target\":{\"resourceId\":\"Gateway_12vqbjq\"},\"properties\":{\"overrideid\":\"Flow_1a4kfat\",\"name\":\"n\",\"conditionsequenceflow\":\"${isDivideAccounts == 2}\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_1167shz\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":25.0,\"y\":25.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_1tpvdch\"}],\"target\":{\"resourceId\":\"Activity_1tpvdch\"},\"properties\":{\"overrideid\":\"Flow_1167shz\"}}]}', NULL, 0, '000000');
INSERT INTO `ACT_DE_MODEL`(`id`, `name`, `model_key`, `form_key`, `category_id`, `description`, `model_comment`, `created`, `created_by`, `last_updated`, `last_updated_by`, `version`, `xml`, `model_editor_json`, `thumbnail`, `model_type`, `tenant_id`) VALUES ('6884cbefa2e8c0288196c7027ea32cfd', '动产质押融资申请', 'pledge_goods_finance', 'wf_ex_pledge_goods_finance_apply', 1647886987383918593, NULL, NULL, '2023-04-17 16:55:38.416000', '1568205681205551106', '2023-05-04 16:47:18.786000', '1568205681205551106', 13, '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:activiti=\"http://activiti.org/bpmn\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n  <process id=\"pledge_goods_finance\" name=\"动产质押融资申请\" isExecutable=\"true\" flowable:skipFirstNode=\"true\" flowable:rollbackNode=\"Activity_06t0e98\">\n    <startEvent id=\"startEvent_1\" name=\"开始\">\n      <extensionElements>\n        <flowable:exFormKey value=\"pledge_goods_finance\" />\n        <flowable:formProperty id=\"financing_module\" name=\"质押信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_index\" name=\"序号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_quotaNo\" name=\"额度编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_goodsName\" name=\"产品名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_capitalName\" name=\"资方名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_availableAmount\" name=\"可用额度\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_quotaType\" name=\"额度状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_expireTime\" name=\"到期日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_loanAmount\" name=\"输入放款金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargonformation_module\" name=\"货物信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_index\" name=\"序号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_financeNo\" name=\"融资编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_goodsInfos\" name=\"货物信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_supplierName\" name=\"供应商\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_unitName\" name=\"单位值\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_quantity\" name=\"待入库数量\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contract_list\" name=\"合同模块\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_serial\" name=\"序号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_contractId\" name=\"合同编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_contractTitle\" name=\"合同标题\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_createTime\" name=\"创建时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_statusText\" name=\"签署状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_operations\" name=\"操作\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"trial_cost_module\" name=\"费用试算模块\" readable=\"true\" writable=\"true\" />\n      </extensionElements>\n      <outgoing>Flow_0yhobgw</outgoing>\n    </startEvent>\n    <userTask id=\"Activity_06t0e98\" name=\"发起人\" flowable:exFormKey=\"pledge_goods_finance\">\n      <extensionElements>\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:formProperty id=\"financing_module\" name=\"质押信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_index\" name=\"序号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_quotaNo\" name=\"额度编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_goodsName\" name=\"产品名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_capitalName\" name=\"资方名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_availableAmount\" name=\"可用额度\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_quotaType\" name=\"额度状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_expireTime\" name=\"到期日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"fc_m_loanAmount\" name=\"输入放款金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cargonformation_module\" name=\"货物信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_index\" name=\"序号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_financeNo\" name=\"融资编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_goodsInfos\" name=\"货物信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_supplierName\" name=\"供应商\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_unitName\" name=\"单位值\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_quantity\" name=\"待入库数量\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cr_m_warehouseInNum\" name=\"已入库数量\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"contract_list\" name=\"合同模块\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_serial\" name=\"序号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_contractId\" name=\"合同编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_contractTitle\" name=\"合同标题\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_createTime\" name=\"创建时间\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_statusText\" name=\"签署状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"cs_m_operations\" name=\"操作\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"trial_cost_module\" name=\"费用试算模块\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"evaluation_module\" name=\"评估信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_index\" name=\"序号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_goodsInfo\" name=\"货品信息\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_prices\" name=\"发布单价\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_purchasePrice\" name=\"采购单价\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_financingRatio\" name=\"融资比例\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_financingPrice\" name=\"融资单价\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_quantity\" name=\"数量\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_financingTotal\" name=\"总额\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_0yhobgw</incoming>\n      <outgoing>Flow_1wgnwfr</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_0yhobgw\" sourceRef=\"startEvent_1\" targetRef=\"Activity_06t0e98\" />\n    <sequenceFlow id=\"Flow_1wgnwfr\" sourceRef=\"Activity_06t0e98\" targetRef=\"Activity_1yp1taa\" />\n    <userTask id=\"Activity_1h57jdh\" name=\"业务终审\" flowable:exFormKey=\"pledge_goods_finance\">\n      <extensionElements>\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n        <flowable:formProperty id=\"financing_module\" name=\"质押信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_index\" name=\"序号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_quotaNo\" name=\"额度编号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_goodsName\" name=\"产品名称\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_capitalName\" name=\"资方名称\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_availableAmount\" name=\"可用额度\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_quotaType\" name=\"额度状态\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_expireTime\" name=\"到期日\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_loanAmount\" name=\"输入放款金额\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cargonformation_module\" name=\"货物信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_index\" name=\"序号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_financeNo\" name=\"融资编号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_goodsInfos\" name=\"货物信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_supplierName\" name=\"供应商\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_unitName\" name=\"单位值\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_quantity\" name=\"待入库数量\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_warehouseInNum\" name=\"已入库数量\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"contract_list\" name=\"合同模块\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_serial\" name=\"序号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_contractId\" name=\"合同编号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_contractTitle\" name=\"合同标题\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_createTime\" name=\"创建时间\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_statusText\" name=\"签署状态\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_operations\" name=\"操作\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"trial_cost_module\" name=\"费用试算模块\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"evaluation_module\" name=\"评估信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_index\" name=\"序号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_goodsInfo\" name=\"货品信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_prices\" name=\"发布单价\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_purchasePrice\" name=\"采购单价\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingRatio\" name=\"融资比例\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingPrice\" name=\"融资单价\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"er_m_quantity\" name=\"数量\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingTotal\" name=\"总额\" readable=\"true\" writable=\"false\" />\n      </extensionElements>\n      <incoming>Flow_0nm1fpk</incoming>\n      <outgoing>Flow_19b979b</outgoing>\n    </userTask>\n    <endEvent id=\"Event_16gdkwo\">\n      <extensionElements>\n        <flowable:executionListener delegateExpression=\"${pledgeFinanceProcessListener}\" event=\"end\" />\n        <flowable:executionListener delegateExpression=\"${contractProcessListener}\" event=\"end\" />\n      </extensionElements>\n      <incoming>Flow_19b979b</incoming>\n    </endEvent>\n    <sequenceFlow id=\"Flow_19b979b\" sourceRef=\"Activity_1h57jdh\" targetRef=\"Event_16gdkwo\" />\n    <userTask id=\"Activity_1yp1taa\" name=\"业务初审\" flowable:exFormKey=\"pledge_goods_finance\">\n      <extensionElements>\n        <flowable:taskListener delegateExpression=\"${pledgeFinanceBeginListener}\" event=\"complete\" />\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:formProperty id=\"financing_module\" name=\"质押信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_index\" name=\"序号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_quotaNo\" name=\"额度编号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_goodsName\" name=\"产品名称\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_capitalName\" name=\"资方名称\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_availableAmount\" name=\"可用额度\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_quotaType\" name=\"额度状态\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_expireTime\" name=\"到期日\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_loanAmount\" name=\"输入放款金额\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cargonformation_module\" name=\"货物信息\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_index\" name=\"序号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_financeNo\" name=\"融资编号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_goodsInfos\" name=\"货物信息\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_supplierName\" name=\"供应商\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_unitName\" name=\"单位值\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_quantity\" name=\"待入库数量\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_warehouseInNum\" name=\"已入库数量\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"contract_list\" name=\"合同模块\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_serial\" name=\"序号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_contractId\" name=\"合同编号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_contractTitle\" name=\"合同标题\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_createTime\" name=\"创建时间\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_statusText\" name=\"签署状态\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_operations\" name=\"操作\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"trial_cost_module\" name=\"费用试算模块\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"evaluation_module\" name=\"评估信息\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_index\" name=\"序号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_goodsInfo\" name=\"货品信息\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_prices\" name=\"发布单价\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_purchasePrice\" name=\"采购单价\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingRatio\" name=\"融资比例\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingPrice\" name=\"融资单价\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_quantity\" name=\"数量\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingTotal\" name=\"总额\" readable=\"false\" writable=\"false\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_1wgnwfr</incoming>\n      <outgoing>Flow_08kenrq</outgoing>\n    </userTask>\n    <userTask id=\"Activity_1t8xcrq\" name=\"仓库确认\" flowable:exFormKey=\"pledge_goods_finance\">\n      <extensionElements>\n        <flowable:taskListener delegateExpression=\"${pledgeWarehouseListener}\" event=\"complete\" />\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:formProperty id=\"financing_module\" name=\"质押信息\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_index\" name=\"序号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_quotaNo\" name=\"额度编号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_goodsName\" name=\"产品名称\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_capitalName\" name=\"资方名称\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_availableAmount\" name=\"可用额度\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_quotaType\" name=\"额度状态\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_expireTime\" name=\"到期日\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"fc_m_loanAmount\" name=\"输入放款金额\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"cargonformation_module\" name=\"货物信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_index\" name=\"序号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_financeNo\" name=\"融资编号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_goodsInfos\" name=\"货物信息\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_supplierName\" name=\"供应商\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_unitName\" name=\"单位值\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_quantity\" name=\"待入库数量\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cr_m_warehouseInNum\" name=\"已入库数量\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"contract_list\" name=\"合同模块\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_serial\" name=\"序号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_contractId\" name=\"合同编号\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_contractTitle\" name=\"合同标题\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_createTime\" name=\"创建时间\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_statusText\" name=\"签署状态\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"cs_m_operations\" name=\"操作\" readable=\"true\" writable=\"false\" />\n        <flowable:formProperty id=\"trial_cost_module\" name=\"费用试算模块\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"evaluation_module\" name=\"评估信息\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_index\" name=\"序号\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_goodsInfo\" name=\"货品信息\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_prices\" name=\"发布单价\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_purchasePrice\" name=\"采购单价\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingRatio\" name=\"融资比例\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingPrice\" name=\"融资单价\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_quantity\" name=\"数量\" readable=\"false\" writable=\"false\" />\n        <flowable:formProperty id=\"er_m_financingTotal\" name=\"总额\" readable=\"false\" writable=\"false\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_08kenrq</incoming>\n      <outgoing>Flow_0nm1fpk</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_08kenrq\" sourceRef=\"Activity_1yp1taa\" targetRef=\"Activity_1t8xcrq\" />\n    <sequenceFlow id=\"Flow_0nm1fpk\" sourceRef=\"Activity_1t8xcrq\" targetRef=\"Activity_1h57jdh\" />\n  </process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_flow\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_flow\" bpmnElement=\"pledge_goods_finance\">\n      <bpmndi:BPMNEdge id=\"Flow_0nm1fpk_di\" bpmnElement=\"Flow_0nm1fpk\">\n        <di:waypoint x=\"760\" y=\"218\" />\n        <di:waypoint x=\"820\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_08kenrq_di\" bpmnElement=\"Flow_08kenrq\">\n        <di:waypoint x=\"580\" y=\"218\" />\n        <di:waypoint x=\"660\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_19b979b_di\" bpmnElement=\"Flow_19b979b\">\n        <di:waypoint x=\"920\" y=\"218\" />\n        <di:waypoint x=\"1012\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_1wgnwfr_di\" bpmnElement=\"Flow_1wgnwfr\">\n        <di:waypoint x=\"410\" y=\"218\" />\n        <di:waypoint x=\"480\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0yhobgw_di\" bpmnElement=\"Flow_0yhobgw\">\n        <di:waypoint x=\"276\" y=\"218\" />\n        <di:waypoint x=\"310\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent_1\" bpmnElement=\"startEvent_1\">\n        <dc:Bounds x=\"240\" y=\"200\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"247\" y=\"243\" width=\"23\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_06t0e98_di\" bpmnElement=\"Activity_06t0e98\">\n        <dc:Bounds x=\"310\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_1h57jdh_di\" bpmnElement=\"Activity_1h57jdh\">\n        <dc:Bounds x=\"820\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_16gdkwo_di\" bpmnElement=\"Event_16gdkwo\">\n        <dc:Bounds x=\"1012\" y=\"200\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_1yp1taa_di\" bpmnElement=\"Activity_1yp1taa\">\n        <dc:Bounds x=\"480\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_1t8xcrq_di\" bpmnElement=\"Activity_1t8xcrq\">\n        <dc:Bounds x=\"660\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</definitions>\n', '{\"bounds\":{\"lowerRight\":{\"x\":1485.0,\"y\":700.0},\"upperLeft\":{\"x\":0.0,\"y\":0.0}},\"resourceId\":\"canvas\",\"stencil\":{\"id\":\"BPMNDiagram\"},\"stencilset\":{\"namespace\":\"http://b3mn.org/stencilset/bpmn2.0#\",\"url\":\"../editor/stencilsets/bpmn2.0/bpmn2.0.json\"},\"properties\":{\"process_id\":\"pledge_goods_finance\",\"name\":\"动产质押融资申请\",\"process_namespace\":\"http://bpmn.io/schema/bpmn\",\"iseagerexecutionfetch\":false,\"messages\":[],\"executionlisteners\":{\"executionListeners\":[]},\"eventlisteners\":{\"eventListeners\":[]},\"signaldefinitions\":[],\"messagedefinitions\":[],\"escalationdefinitions\":[]},\"childShapes\":[{\"bounds\":{\"lowerRight\":{\"x\":276.0,\"y\":236.0},\"upperLeft\":{\"x\":240.0,\"y\":200.0}},\"resourceId\":\"startEvent_1\",\"childShapes\":[],\"stencil\":{\"id\":\"StartNoneEvent\"},\"properties\":{\"overrideid\":\"startEvent_1\",\"name\":\"开始\",\"interrupting\":true,\"formproperties\":{\"formProperties\":[{\"id\":\"financing_module\",\"name\":\"质押信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_quotaNo\",\"name\":\"额度编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_goodsName\",\"name\":\"产品名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_capitalName\",\"name\":\"资方名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_availableAmount\",\"name\":\"可用额度\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_quotaType\",\"name\":\"额度状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_expireTime\",\"name\":\"到期日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_loanAmount\",\"name\":\"输入放款金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargonformation_module\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_financeNo\",\"name\":\"融资编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_goodsInfos\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_supplierName\",\"name\":\"供应商\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_unitName\",\"name\":\"单位值\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_quantity\",\"name\":\"待入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contract_list\",\"name\":\"合同模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_serial\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_contractId\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_createTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_statusText\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_operations\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"trial_cost_module\",\"name\":\"费用试算模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0yhobgw\"}]},{\"bounds\":{\"lowerRight\":{\"x\":410.0,\"y\":258.0},\"upperLeft\":{\"x\":310.0,\"y\":178.0}},\"resourceId\":\"Activity_06t0e98\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_06t0e98\",\"name\":\"发起人\",\"formproperties\":{\"formProperties\":[{\"id\":\"financing_module\",\"name\":\"质押信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_quotaNo\",\"name\":\"额度编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_goodsName\",\"name\":\"产品名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_capitalName\",\"name\":\"资方名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_availableAmount\",\"name\":\"可用额度\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_quotaType\",\"name\":\"额度状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_expireTime\",\"name\":\"到期日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"fc_m_loanAmount\",\"name\":\"输入放款金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cargonformation_module\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_financeNo\",\"name\":\"融资编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_goodsInfos\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_supplierName\",\"name\":\"供应商\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_unitName\",\"name\":\"单位值\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_quantity\",\"name\":\"待入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cr_m_warehouseInNum\",\"name\":\"已入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"contract_list\",\"name\":\"合同模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_serial\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_contractId\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_createTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_statusText\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"cs_m_operations\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"trial_cost_module\",\"name\":\"费用试算模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"evaluation_module\",\"name\":\"评估信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_goodsInfo\",\"name\":\"货品信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_prices\",\"name\":\"发布单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_purchasePrice\",\"name\":\"采购单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_financingRatio\",\"name\":\"融资比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_financingPrice\",\"name\":\"融资单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_quantity\",\"name\":\"数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_financingTotal\",\"name\":\"总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_1wgnwfr\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0yhobgw\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":18.0,\"y\":18.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_06t0e98\"}],\"target\":{\"resourceId\":\"Activity_06t0e98\"},\"properties\":{\"overrideid\":\"Flow_0yhobgw\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_1wgnwfr\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_1yp1taa\"}],\"target\":{\"resourceId\":\"Activity_1yp1taa\"},\"properties\":{\"overrideid\":\"Flow_1wgnwfr\"}},{\"bounds\":{\"lowerRight\":{\"x\":920.0,\"y\":258.0},\"upperLeft\":{\"x\":820.0,\"y\":178.0}},\"resourceId\":\"Activity_1h57jdh\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_1h57jdh\",\"name\":\"业务终审\",\"formproperties\":{\"formProperties\":[{\"id\":\"financing_module\",\"name\":\"质押信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_quotaNo\",\"name\":\"额度编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_goodsName\",\"name\":\"产品名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_capitalName\",\"name\":\"资方名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_availableAmount\",\"name\":\"可用额度\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_quotaType\",\"name\":\"额度状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_expireTime\",\"name\":\"到期日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_loanAmount\",\"name\":\"输入放款金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cargonformation_module\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_financeNo\",\"name\":\"融资编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_goodsInfos\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_supplierName\",\"name\":\"供应商\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_unitName\",\"name\":\"单位值\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_quantity\",\"name\":\"待入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_warehouseInNum\",\"name\":\"已入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"contract_list\",\"name\":\"合同模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_serial\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_contractId\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_createTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_statusText\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_operations\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"trial_cost_module\",\"name\":\"费用试算模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"evaluation_module\",\"name\":\"评估信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"er_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"er_m_goodsInfo\",\"name\":\"货品信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"er_m_prices\",\"name\":\"发布单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"er_m_purchasePrice\",\"name\":\"采购单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"er_m_financingRatio\",\"name\":\"融资比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"er_m_financingPrice\",\"name\":\"融资单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"er_m_quantity\",\"name\":\"数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"er_m_financingTotal\",\"name\":\"总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_19b979b\"}]},{\"bounds\":{\"lowerRight\":{\"x\":1048.0,\"y\":236.0},\"upperLeft\":{\"x\":1012.0,\"y\":200.0}},\"resourceId\":\"Event_16gdkwo\",\"childShapes\":[],\"stencil\":{\"id\":\"EndNoneEvent\"},\"properties\":{\"overrideid\":\"Event_16gdkwo\",\"executionlisteners\":{\"executionListeners\":[{\"event\":\"end\",\"delegateExpression\":\"${pledgeFinanceProcessListener}\"},{\"event\":\"end\",\"delegateExpression\":\"${contractProcessListener}\"}]}},\"outgoing\":[]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_19b979b\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":18.0,\"y\":18.0}],\"outgoing\":[{\"resourceId\":\"Event_16gdkwo\"}],\"target\":{\"resourceId\":\"Event_16gdkwo\"},\"properties\":{\"overrideid\":\"Flow_19b979b\"}},{\"bounds\":{\"lowerRight\":{\"x\":580.0,\"y\":258.0},\"upperLeft\":{\"x\":480.0,\"y\":178.0}},\"resourceId\":\"Activity_1yp1taa\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_1yp1taa\",\"name\":\"业务初审\",\"formproperties\":{\"formProperties\":[{\"id\":\"financing_module\",\"name\":\"质押信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_quotaNo\",\"name\":\"额度编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_goodsName\",\"name\":\"产品名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_capitalName\",\"name\":\"资方名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_availableAmount\",\"name\":\"可用额度\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_quotaType\",\"name\":\"额度状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_expireTime\",\"name\":\"到期日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"fc_m_loanAmount\",\"name\":\"输入放款金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cargonformation_module\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cr_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cr_m_financeNo\",\"name\":\"融资编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cr_m_goodsInfos\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cr_m_supplierName\",\"name\":\"供应商\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cr_m_unitName\",\"name\":\"单位值\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cr_m_quantity\",\"name\":\"待入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cr_m_warehouseInNum\",\"name\":\"已入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"contract_list\",\"name\":\"合同模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cs_m_serial\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cs_m_contractId\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cs_m_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cs_m_createTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cs_m_statusText\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cs_m_operations\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"trial_cost_module\",\"name\":\"费用试算模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"evaluation_module\",\"name\":\"评估信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_goodsInfo\",\"name\":\"货品信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_prices\",\"name\":\"发布单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_purchasePrice\",\"name\":\"采购单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_financingRatio\",\"name\":\"融资比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_financingPrice\",\"name\":\"融资单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_quantity\",\"name\":\"数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_financingTotal\",\"name\":\"总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[{\"event\":\"complete\",\"delegateExpression\":\"${pledgeFinanceBeginListener}\"}]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_08kenrq\"}]},{\"bounds\":{\"lowerRight\":{\"x\":760.0,\"y\":258.0},\"upperLeft\":{\"x\":660.0,\"y\":178.0}},\"resourceId\":\"Activity_1t8xcrq\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_1t8xcrq\",\"name\":\"仓库确认\",\"formproperties\":{\"formProperties\":[{\"id\":\"financing_module\",\"name\":\"质押信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_quotaNo\",\"name\":\"额度编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_goodsName\",\"name\":\"产品名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_capitalName\",\"name\":\"资方名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_availableAmount\",\"name\":\"可用额度\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_quotaType\",\"name\":\"额度状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_expireTime\",\"name\":\"到期日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"fc_m_loanAmount\",\"name\":\"输入放款金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"cargonformation_module\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_financeNo\",\"name\":\"融资编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_goodsInfos\",\"name\":\"货物信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_supplierName\",\"name\":\"供应商\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_unitName\",\"name\":\"单位值\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_quantity\",\"name\":\"待入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cr_m_warehouseInNum\",\"name\":\"已入库数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"contract_list\",\"name\":\"合同模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_serial\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_contractId\",\"name\":\"合同编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_contractTitle\",\"name\":\"合同标题\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_createTime\",\"name\":\"创建时间\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_statusText\",\"name\":\"签署状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"cs_m_operations\",\"name\":\"操作\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":false},{\"id\":\"trial_cost_module\",\"name\":\"费用试算模块\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"evaluation_module\",\"name\":\"评估信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_index\",\"name\":\"序号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_goodsInfo\",\"name\":\"货品信息\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_prices\",\"name\":\"发布单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_purchasePrice\",\"name\":\"采购单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_financingRatio\",\"name\":\"融资比例\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_financingPrice\",\"name\":\"融资单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_quantity\",\"name\":\"数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false},{\"id\":\"er_m_financingTotal\",\"name\":\"总额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":false,\"writable\":false}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[{\"event\":\"complete\",\"delegateExpression\":\"${pledgeWarehouseListener}\"}]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0nm1fpk\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_08kenrq\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_1t8xcrq\"}],\"target\":{\"resourceId\":\"Activity_1t8xcrq\"},\"properties\":{\"overrideid\":\"Flow_08kenrq\"}},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0nm1fpk\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_1h57jdh\"}],\"target\":{\"resourceId\":\"Activity_1h57jdh\"},\"properties\":{\"overrideid\":\"Flow_0nm1fpk\"}}]}', NULL, 0, '000000');
INSERT INTO `ACT_DE_MODEL`(`id`, `name`, `model_key`, `form_key`, `category_id`, `description`, `model_comment`, `created`, `created_by`, `last_updated`, `last_updated_by`, `version`, `xml`, `model_editor_json`, `thumbnail`, `model_type`, `tenant_id`) VALUES ('c28b947a602ce19a42a6d209544f1011', '动产质押赎货确认', 'pledge_redeem_confirm', 'wf_ex_pledge_confirm', 1654004681824890882, NULL, NULL, '2023-05-04 14:18:38.377000', '1568205681205551106', '2023-05-15 11:52:22.583000', '1568205681205551106', 3, '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:activiti=\"http://activiti.org/bpmn\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n  <process id=\"pledge_redeem_confirm\" name=\"动产质押赎货确认\" isExecutable=\"true\" flowable:skipFirstNode=\"true\" flowable:rollbackNode=\"Activity_06t0e98\">\n    <startEvent id=\"startEvent_1\" name=\"开始\">\n      <extensionElements>\n        <flowable:exFormKey value=\"pledge_confirm\" />\n        <flowable:formProperty id=\"redemptionNote\" name=\"赎货单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemNo\" name=\"赎货单号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemGoods\" name=\"规格型号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_purchasePrice\" name=\"采购单价\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_financingPrice\" name=\"融资单价\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemNum\" name=\"赎货数量\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_goodsUnitValue\" name=\"单位\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_stockNumber\" name=\"库存编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_financingNumber\" name=\"融资编号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_supplierName\" name=\"供应商\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_warehousingCompany\" name=\"仓储公司\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_storehouse\" name=\"仓库\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_warehouseInDate\" name=\"入库日期\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_warehouseAge\" name=\"库龄\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redemptionDate\" name=\"约定赎货日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_extractType\" name=\"提货方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_delivery\" name=\"收货地址\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"银行还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_yearRate\" name=\"年化利率\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_repayFunds\" name=\"应还本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status\" name=\"还款状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status_button_apply\" name=\"申请还款按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status_button_view\" name=\"查看余额凭证按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform\" name=\"平台费用单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status\" name=\"还款状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status_button_apply\" name=\"确认还款按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status_button_view\" name=\"查看支付凭证按钮\" readable=\"true\" writable=\"true\" />\n      </extensionElements>\n      <outgoing>Flow_0yhobgw</outgoing>\n    </startEvent>\n    <userTask id=\"Activity_06t0e98\" name=\"发起人\" flowable:exFormKey=\"pledge_confirm\">\n      <extensionElements>\n        <flowable:assignee type=\"custom\" value=\"applyUser\" text=\"流程发起人\" />\n        <flowable:formProperty id=\"redemptionNote\" name=\"赎货单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemNo\" name=\"赎货单号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redemptionDate\" name=\"约定赎货日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_extractType\" name=\"提货方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_delivery\" name=\"收货地址\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"银行还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_yearRate\" name=\"年化利率\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_repayFunds\" name=\"应还本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status\" name=\"还款状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status_button_apply\" name=\"申请还款按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status_button_view\" name=\"查看余额凭证按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform\" name=\"平台费用单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status\" name=\"还款状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status_button_apply\" name=\"确认还款按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status_button_view\" name=\"查看支付凭证按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_0yhobgw</incoming>\n      <outgoing>Flow_17e5moq</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_0yhobgw\" sourceRef=\"startEvent_1\" targetRef=\"Activity_06t0e98\" />\n    <userTask id=\"Activity_11ivarw\" name=\"业务审核\" flowable:exFormKey=\"pledge_confirm\">\n      <extensionElements>\n        <flowable:assignee type=\"role\" value=\"1123598816738675201,1123598816738675202,1123598816738675203,1123598816738675204,1485958005597007873,1485803050105876481,1570290056185262082,1560447672568012801,1569568583984455682,1570284412577349634\" text=\"超级管理员,用户,人事,经理,业务经理,资金方,管理员,demo01,demo,管理员\" />\n        <flowable:formProperty id=\"redemptionNote\" name=\"赎货单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redeemNo\" name=\"赎货单号\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_redemptionDate\" name=\"约定赎货日\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_extractType\" name=\"提货方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"redemptionNote_delivery\" name=\"收货地址\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank\" name=\"银行还款单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_yearRate\" name=\"年化利率\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_repayFunds\" name=\"应还本金\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status\" name=\"还款状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status_button_apply\" name=\"申请还款按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_status_button_view\" name=\"查看余额凭证按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform\" name=\"平台费用单\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_name\" name=\"费用名称\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_expenseType\" name=\"费用类型\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_node\" name=\"支付节点\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_feeFormula\" name=\"计费方式\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_bank_table_money\" name=\"应付金额\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status\" name=\"还款状态\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status_button_apply\" name=\"确认还款按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:formProperty id=\"repaymentNote_platform_status_button_view\" name=\"查看支付凭证按钮\" readable=\"true\" writable=\"true\" />\n        <flowable:button label=\"通过\" prop=\"wf_pass\" display=\"true\" />\n        <flowable:button label=\"驳回\" prop=\"wf_reject\" display=\"true\" />\n        <flowable:button label=\"打印\" prop=\"wf_print\" display=\"true\" />\n        <flowable:button label=\"转办\" prop=\"wf_transfer\" display=\"true\" />\n        <flowable:button label=\"委托\" prop=\"wf_delegate\" display=\"true\" />\n        <flowable:button label=\"终止\" prop=\"wf_terminate\" display=\"true\" />\n        <flowable:button label=\"加签\" prop=\"wf_add_instance\" display=\"true\" />\n        <flowable:button label=\"减签\" prop=\"wf_del_instance\" display=\"true\" />\n        <flowable:button label=\"指定回退\" prop=\"wf_rollback\" display=\"true\" />\n      </extensionElements>\n      <incoming>Flow_17e5moq</incoming>\n      <outgoing>Flow_000gmdh</outgoing>\n    </userTask>\n    <sequenceFlow id=\"Flow_17e5moq\" sourceRef=\"Activity_06t0e98\" targetRef=\"Activity_11ivarw\" />\n    <endEvent id=\"Event_0g2268o\">\n      <extensionElements>\n        <flowable:executionListener delegateExpression=\"${pledgeRedeemConfirmListener}\" event=\"end\" />\n      </extensionElements>\n      <incoming>Flow_000gmdh</incoming>\n    </endEvent>\n    <sequenceFlow id=\"Flow_000gmdh\" sourceRef=\"Activity_11ivarw\" targetRef=\"Event_0g2268o\" />\n  </process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_flow\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_flow\" bpmnElement=\"pledge_redeem_confirm\">\n      <bpmndi:BPMNEdge id=\"Flow_000gmdh_di\" bpmnElement=\"Flow_000gmdh\">\n        <di:waypoint x=\"630\" y=\"218\" />\n        <di:waypoint x=\"712\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_17e5moq_di\" bpmnElement=\"Flow_17e5moq\">\n        <di:waypoint x=\"430\" y=\"218\" />\n        <di:waypoint x=\"530\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0yhobgw_di\" bpmnElement=\"Flow_0yhobgw\">\n        <di:waypoint x=\"276\" y=\"218\" />\n        <di:waypoint x=\"330\" y=\"218\" />\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent_1\" bpmnElement=\"startEvent_1\">\n        <dc:Bounds x=\"240\" y=\"200\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"248\" y=\"243\" width=\"22\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_06t0e98_di\" bpmnElement=\"Activity_06t0e98\">\n        <dc:Bounds x=\"330\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_11ivarw_di\" bpmnElement=\"Activity_11ivarw\">\n        <dc:Bounds x=\"530\" y=\"178\" width=\"100\" height=\"80\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_0g2268o_di\" bpmnElement=\"Event_0g2268o\">\n        <dc:Bounds x=\"712\" y=\"200\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</definitions>\n', '{\"bounds\":{\"lowerRight\":{\"x\":1485.0,\"y\":700.0},\"upperLeft\":{\"x\":0.0,\"y\":0.0}},\"resourceId\":\"canvas\",\"stencil\":{\"id\":\"BPMNDiagram\"},\"stencilset\":{\"namespace\":\"http://b3mn.org/stencilset/bpmn2.0#\",\"url\":\"../editor/stencilsets/bpmn2.0/bpmn2.0.json\"},\"properties\":{\"process_id\":\"pledge_redeem_confirm\",\"name\":\"动产质押赎货确认\",\"process_namespace\":\"http://bpmn.io/schema/bpmn\",\"iseagerexecutionfetch\":false,\"messages\":[],\"executionlisteners\":{\"executionListeners\":[]},\"eventlisteners\":{\"eventListeners\":[]},\"signaldefinitions\":[],\"messagedefinitions\":[],\"escalationdefinitions\":[]},\"childShapes\":[{\"bounds\":{\"lowerRight\":{\"x\":276.0,\"y\":236.0},\"upperLeft\":{\"x\":240.0,\"y\":200.0}},\"resourceId\":\"startEvent_1\",\"childShapes\":[],\"stencil\":{\"id\":\"StartNoneEvent\"},\"properties\":{\"overrideid\":\"startEvent_1\",\"name\":\"开始\",\"interrupting\":true,\"formproperties\":{\"formProperties\":[{\"id\":\"redemptionNote\",\"name\":\"赎货单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemNo\",\"name\":\"赎货单号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemGoods\",\"name\":\"规格型号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_purchasePrice\",\"name\":\"采购单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_financingPrice\",\"name\":\"融资单价\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemNum\",\"name\":\"赎货数量\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_goodsUnitValue\",\"name\":\"单位\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_stockNumber\",\"name\":\"库存编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_financingNumber\",\"name\":\"融资编号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_supplierName\",\"name\":\"供应商\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_warehousingCompany\",\"name\":\"仓储公司\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_storehouse\",\"name\":\"仓库\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_warehouseInDate\",\"name\":\"入库日期\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_warehouseAge\",\"name\":\"库龄\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redemptionDate\",\"name\":\"约定赎货日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_extractType\",\"name\":\"提货方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_delivery\",\"name\":\"收货地址\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"银行还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_yearRate\",\"name\":\"年化利率\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_repayFunds\",\"name\":\"应还本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status\",\"name\":\"还款状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status_button_apply\",\"name\":\"申请还款按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status_button_view\",\"name\":\"查看余额凭证按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform\",\"name\":\"平台费用单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status\",\"name\":\"还款状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status_button_apply\",\"name\":\"确认还款按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status_button_view\",\"name\":\"查看支付凭证按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_0yhobgw\"}]},{\"bounds\":{\"lowerRight\":{\"x\":430.0,\"y\":258.0},\"upperLeft\":{\"x\":330.0,\"y\":178.0}},\"resourceId\":\"Activity_06t0e98\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_06t0e98\",\"name\":\"发起人\",\"formproperties\":{\"formProperties\":[{\"id\":\"redemptionNote\",\"name\":\"赎货单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemNo\",\"name\":\"赎货单号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redemptionDate\",\"name\":\"约定赎货日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_extractType\",\"name\":\"提货方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_delivery\",\"name\":\"收货地址\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"银行还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_yearRate\",\"name\":\"年化利率\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_repayFunds\",\"name\":\"应还本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status\",\"name\":\"还款状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status_button_apply\",\"name\":\"申请还款按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status_button_view\",\"name\":\"查看余额凭证按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform\",\"name\":\"平台费用单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status\",\"name\":\"还款状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status_button_apply\",\"name\":\"确认还款按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status_button_view\",\"name\":\"查看支付凭证按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_17e5moq\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_0yhobgw\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":18.0,\"y\":18.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_06t0e98\"}],\"target\":{\"resourceId\":\"Activity_06t0e98\"},\"properties\":{\"overrideid\":\"Flow_0yhobgw\"}},{\"bounds\":{\"lowerRight\":{\"x\":630.0,\"y\":258.0},\"upperLeft\":{\"x\":530.0,\"y\":178.0}},\"resourceId\":\"Activity_11ivarw\",\"childShapes\":[],\"stencil\":{\"id\":\"UserTask\"},\"properties\":{\"overrideid\":\"Activity_11ivarw\",\"name\":\"业务审核\",\"formproperties\":{\"formProperties\":[{\"id\":\"redemptionNote\",\"name\":\"赎货单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redeemNo\",\"name\":\"赎货单号\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_redemptionDate\",\"name\":\"约定赎货日\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_extractType\",\"name\":\"提货方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"redemptionNote_delivery\",\"name\":\"收货地址\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank\",\"name\":\"银行还款单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_yearRate\",\"name\":\"年化利率\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_repayFunds\",\"name\":\"应还本金\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status\",\"name\":\"还款状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status_button_apply\",\"name\":\"申请还款按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_status_button_view\",\"name\":\"查看余额凭证按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform\",\"name\":\"平台费用单\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_name\",\"name\":\"费用名称\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_expenseType\",\"name\":\"费用类型\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_node\",\"name\":\"支付节点\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_feeFormula\",\"name\":\"计费方式\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_bank_table_money\",\"name\":\"应付金额\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status\",\"name\":\"还款状态\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status_button_apply\",\"name\":\"确认还款按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true},{\"id\":\"repaymentNote_platform_status_button_view\",\"name\":\"查看支付凭证按钮\",\"type\":null,\"expression\":null,\"variable\":null,\"default\":null,\"required\":false,\"readable\":true,\"writable\":true}]},\"asynchronousdefinition\":false,\"exclusivedefinition\":true,\"isforcompensation\":false,\"tasklisteners\":{\"taskListeners\":[]},\"executionlisteners\":{\"executionListeners\":[]}},\"outgoing\":[{\"resourceId\":\"Flow_000gmdh\"}]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_17e5moq\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":50.0,\"y\":40.0}],\"outgoing\":[{\"resourceId\":\"Activity_11ivarw\"}],\"target\":{\"resourceId\":\"Activity_11ivarw\"},\"properties\":{\"overrideid\":\"Flow_17e5moq\"}},{\"bounds\":{\"lowerRight\":{\"x\":748.0,\"y\":236.0},\"upperLeft\":{\"x\":712.0,\"y\":200.0}},\"resourceId\":\"Event_0g2268o\",\"childShapes\":[],\"stencil\":{\"id\":\"EndNoneEvent\"},\"properties\":{\"overrideid\":\"Event_0g2268o\",\"executionlisteners\":{\"executionListeners\":[{\"event\":\"end\",\"delegateExpression\":\"${pledgeRedeemConfirmListener}\"}]}},\"outgoing\":[]},{\"bounds\":{\"lowerRight\":{\"x\":172.0,\"y\":212.0},\"upperLeft\":{\"x\":128.0,\"y\":212.0}},\"resourceId\":\"Flow_000gmdh\",\"childShapes\":[],\"stencil\":{\"id\":\"SequenceFlow\"},\"dockers\":[{\"x\":50.0,\"y\":40.0},{\"x\":18.0,\"y\":18.0}],\"outgoing\":[{\"resourceId\":\"Event_0g2268o\"}],\"target\":{\"resourceId\":\"Event_0g2268o\"},\"properties\":{\"overrideid\":\"Flow_000gmdh\"}}]}', NULL, 0, '000000');

-- 合同模板
INSERT INTO `jrzh_contract_template`(`id`, `template_id`, `template_name`, `template_category`, `remarks`, `doc_ids`, `sign_role`, `sign_require`, `sign_method`, `template_status`, `operate_name`, `operate_time`, `system_deleted`, `attach_id`, `create_dept`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`, `status`, `tenant_id`, `expire_day`, `force_reading_second`, `verify_type`, `api_supplier`, `contract_gen_type`, `signer_num`, `template_url`, `template_word_url`, `sign_keyword_json`, `need_customize_write`, `sign_node`, `sub_contract_no`, `need_plat_sign`) VALUES (1648979497103396865, '168198228701000001', '监管仓库协议合同', '', '', '', '', '', '', '', 'zhou1999', '2023-11-01 18:02:52', 0, NULL, 1601499214133702657, 0, '2023-04-20 17:18:43', '2023-11-01 18:02:52', 1568205681205551106, 1568205681205551106, 1, '000000', 3, 0, '', 'bestSign', 1, 2, '', '', NULL, 0, '22-2,22-3', '', 0);
INSERT INTO `jrzh_contract_template`(`id`, `template_id`, `template_name`, `template_category`, `remarks`, `doc_ids`, `sign_role`, `sign_require`, `sign_method`, `template_status`, `operate_name`, `operate_time`, `system_deleted`, `attach_id`, `create_dept`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`, `status`, `tenant_id`, `expire_day`, `force_reading_second`, `verify_type`, `api_supplier`, `contract_gen_type`, `signer_num`, `template_url`, `template_word_url`, `sign_keyword_json`, `need_customize_write`, `sign_node`, `sub_contract_no`, `need_plat_sign`) VALUES (1648979497107591169, '168198188701000001', '动产质押合同', '', '', '', '', '', '', '', 'zhou1999', '2023-11-01 18:00:56', 0, NULL, 1601499214133702657, 0, '2023-04-20 17:18:43', '2023-11-01 18:00:56', 1568205681205551106, 1568205681205551106, 1, '000000', 3, 0, '', 'bestSign', 1, 2, '', '', NULL, 0, '22-1,22-3', '', 0);

-- 签署配置
INSERT INTO `jrzh_contract_sign_config`(`id`, `keywords`, `sign_align`, `sign_no`, `status`, `is_deleted`, `tenant_id`, `create_dept`, `create_user`, `create_time`, `update_user`, `update_time`, `template_id`, `sign_type`) VALUES (1719655525936795650, '融资企业签章', '1', 1, 1, 0, '000000', 1601499214133702657, 1568205681205551106, '2023-11-01 18:00:02', 1568205681205551106, '2023-11-01 18:00:02', 168198188701000001, 2);
INSERT INTO `jrzh_contract_sign_config`(`id`, `keywords`, `sign_align`, `sign_no`, `status`, `is_deleted`, `tenant_id`, `create_dept`, `create_user`, `create_time`, `update_user`, `update_time`, `template_id`, `sign_type`) VALUES (1719655525999710210, '融资企业签名', '1', 1, 1, 0, '000000', 1601499214133702657, 1568205681205551106, '2023-11-01 18:00:02', 1568205681205551106, '2023-11-01 18:00:02', 168198188701000001, 1);
INSERT INTO `jrzh_contract_sign_config`(`id`, `keywords`, `sign_align`, `sign_no`, `status`, `is_deleted`, `tenant_id`, `create_dept`, `create_user`, `create_time`, `update_user`, `update_time`, `template_id`, `sign_type`) VALUES (1719655525999710211, '融资企业签署日期', '1', 1, 1, 0, '000000', 1601499214133702657, 1568205681205551106, '2023-11-01 18:00:02', 1568205681205551106, '2023-11-01 18:00:02', 168198188701000001, 3);
INSERT INTO `jrzh_contract_sign_config`(`id`, `keywords`, `sign_align`, `sign_no`, `status`, `is_deleted`, `tenant_id`, `create_dept`, `create_user`, `create_time`, `update_user`, `update_time`, `template_id`, `sign_type`) VALUES (1719655525999710212, '资金方签章', '1', 2, 1, 0, '000000', 1601499214133702657, 1568205681205551106, '2023-11-01 18:00:02', 1568205681205551106, '2023-11-01 18:00:02', 168198188701000001, 2);
INSERT INTO `jrzh_contract_sign_config`(`id`, `keywords`, `sign_align`, `sign_no`, `status`, `is_deleted`, `tenant_id`, `create_dept`, `create_user`, `create_time`, `update_user`, `update_time`, `template_id`, `sign_type`) VALUES (1719655525999710213, '资金方签名', '1', 2, 1, 0, '000000', 1601499214133702657, 1568205681205551106, '2023-11-01 18:00:02', 1568205681205551106, '2023-11-01 18:00:02', 168198188701000001, 1);
INSERT INTO `jrzh_contract_sign_config`(`id`, `keywords`, `sign_align`, `sign_no`, `status`, `is_deleted`, `tenant_id`, `create_dept`, `create_user`, `create_time`, `update_user`, `update_time`, `template_id`, `sign_type`) VALUES (1719655525999710214, '资金方签署日期', '1', 2, 1, 0, '000000', 1601499214133702657, 1568205681205551106, '2023-11-01 18:00:02', 1568205681205551106, '2023-11-01 18:00:02', 168198188701000001, 3);

-- 合同方案
INSERT INTO `jrzh_contract_template_config`(`id`, `tenant_id`, `create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `template_id`, `bean_clazz_path`, `template_fields_json`, `base_info`, `template_fields_config_json`, `goods_type`, `iz_built`, `sign_node`) VALUES (1719656828863778817, '000000', 1568205681205551106, '2023-11-01 18:05:12', 1568205681205551106, '2023-11-02 08:53:36', 1601499214133702657, 0, 1, '168198188701000001', 'org.springblade.customer.dto.ContractDataSourceCustomerGoodsDTO,org.springblade.customer.dto.ContractDataSourceCustomerInfoDTO,org.springblade.finance.dto.ContractDataSourceFinanceDTO,org.springblade.product.moudle.pubproduct.dto.ContractDataSourceProductDTO,org.springblade.product.moudle.pubproduct.dto.ContractDataSourceDeptDTO', '[{\"isRequired\":\"1\",\"type\":\"11\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"资方名称\",\"tag\":\"资方名称\",\"text\":\"\",\"group\":\"资方名称\"},{\"isRequired\":\"1\",\"type\":\"11\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"授信企业名称\",\"tag\":\"授信企业名称\",\"text\":\"\",\"group\":\"授信企业名称\"},{\"isRequired\":\"1\",\"type\":\"50\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"资金方签章\",\"tag\":\"盖章\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"40\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"融资企业签名\",\"tag\":\"签名\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"40\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"资金方签名\",\"tag\":\"签名\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"20\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"资金方签署日期\",\"tag\":\"签署日期\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"20\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"融资企业签署日期\",\"tag\":\"签署日期\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"50\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"融资企业签章\",\"tag\":\"盖章\",\"group\":\"\"}]', '', NULL, '4', 0, '22-1,22-3');
INSERT INTO `jrzh_contract_template_config`(`id`, `tenant_id`, `create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `template_id`, `bean_clazz_path`, `template_fields_json`, `base_info`, `template_fields_config_json`, `goods_type`, `iz_built`, `sign_node`) VALUES (1719880139564650499, '000000', 1568205681205551106, '2023-11-02 08:52:34', 1568205681205551106, '2023-11-02 08:53:00', 1601499214133702657, 0, 1, '168198228701000001', 'org.springblade.product.moudle.pubproduct.dto.ContractDataSourceProductDTO', '[{\"isRequired\":\"1\",\"type\":\"11\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"仓储方名称\",\"tag\":\"文本\",\"text\":\"\",\"group\":\"仓储方名称\"},{\"isRequired\":\"1\",\"type\":\"11\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"资方名称\",\"tag\":\"资方名称\",\"text\":\"\",\"group\":\"资方名称\"},{\"isRequired\":\"1\",\"type\":\"50\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"资方签章\",\"tag\":\"盖章\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"50\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"仓储方签章\",\"tag\":\"盖章\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"20\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"资方签章日期\",\"tag\":\"签署日期\",\"group\":\"\"},{\"isRequired\":\"1\",\"type\":\"20\",\"dateTimeFormat\":\"yyyy-MM-dd\",\"name\":\"仓储方签章日期\",\"tag\":\"签署日期\",\"group\":\"\"}]', '', NULL, '4', 0, '2-1');
INSERT INTO `jrzh_contract_template_fields_config`(`id`, `tenant_id`, `create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `template_config_id`, `parentId`, `ssq_field_name`, `field_name`, `parent_field_name`, `iz_bind`, `iz_front_field`, `clazz_name`, `config_id`, `config_json`) VALUES (1719879240016465922, '000000', 1568205681205551106, '2023-11-02 08:48:59', 1568205681205551106, '2023-11-02 08:53:36', 1601499214133702657, 0, 1, 1719656828863778817, NULL, '资方名称', 'capitalName', '', 0, 1, '', NULL, NULL);
INSERT INTO `jrzh_contract_template_fields_config`(`id`, `tenant_id`, `create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `template_config_id`, `parentId`, `ssq_field_name`, `field_name`, `parent_field_name`, `iz_bind`, `iz_front_field`, `clazz_name`, `config_id`, `config_json`) VALUES (1719879240016465923, '000000', 1568205681205551106, '2023-11-02 08:48:59', 1568205681205551106, '2023-11-02 08:53:36', 1601499214133702657, 0, 1, 1719656828863778817, NULL, '授信企业名称', 'applyUserName', '', 0, 1, '', NULL, NULL);
INSERT INTO `jrzh_contract_template_fields_config`(`id`, `tenant_id`, `create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `template_config_id`, `parentId`, `ssq_field_name`, `field_name`, `parent_field_name`, `iz_bind`, `iz_front_field`, `clazz_name`, `config_id`, `config_json`) VALUES (1719880248650108930, '000000', 1568205681205551106, '2023-11-02 08:53:00', 1568205681205551106, '2023-11-02 08:53:00', 1601499214133702657, 0, 1, 1719880139564650499, NULL, '仓储方名称', 'warehouseName', '', 0, 1, '', NULL, NULL);
INSERT INTO `jrzh_contract_template_fields_config`(`id`, `tenant_id`, `create_user`, `create_time`, `update_user`, `update_time`, `create_dept`, `is_deleted`, `status`, `template_config_id`, `parentId`, `ssq_field_name`, `field_name`, `parent_field_name`, `iz_bind`, `iz_front_field`, `clazz_name`, `config_id`, `config_json`) VALUES (1719880248650108931, '000000', 1568205681205551106, '2023-11-02 08:53:00', 1568205681205551106, '2023-11-02 08:53:00', 1601499214133702657, 0, 1, 1719880139564650499, NULL, '资方名称', 'capitalName', '', 0, 1, '', NULL, NULL);





