/*
 Navicat Premium Data Transfer

 Source Server         : local
 Source Server Type    : MySQL
 Source Server Version : 80012
 Source Host           : localhost:3306
 Source Schema         : jrzh_supplier_3

 Target Server Type    : MySQL
 Target Server Version : 80012
 File Encoding         : 65001

 Date: 03/06/2025 10:01:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for jrzh_field_relate
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_field_relate`;
CREATE TABLE `jrzh_field_relate`  (
  `id` bigint(20) NOT NULL,
  `create_dept` bigint(20) NULL DEFAULT NULL COMMENT '部门',
  `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除   0 未删除 1 已删除',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态',
  `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户id',
  `external_system_id` bigint(20) NULL DEFAULT NULL COMMENT '外部系统业务id',
  `internal_system_id` bigint(20) NULL DEFAULT NULL COMMENT '3.0系统业务id',
  `business_type` int(11) NULL DEFAULT NULL COMMENT '业务类型',
  `system_type` int(11) NULL DEFAULT NULL COMMENT '系统类型',
  `member_id` bigint(20) NULL DEFAULT NULL COMMENT '外部系统账户id（标识id）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统业务字段关联表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
