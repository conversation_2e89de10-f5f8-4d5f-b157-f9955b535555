
alter table jrzh_contract_template_config add column `sign_node` varchar(500) DEFAULT '' COMMENT '签署节点';
alter table jrzh_contract_template_config modify column `bean_clazz_path` varchar(1000) NOT NULL COMMENT '数据源（多数据源以逗号分割）';
alter table jrzh_contract_template_fields_config drop column type;
alter table jrzh_contract_template_fields_config drop column field_format;
alter table jrzh_contract_template_fields_config drop column field_suffix;
alter table jrzh_contract_template_fields_config drop column field_desensitization;
alter table jrzh_contract_template_fields_config drop column current_version;
alter table jrzh_contract_template_fields_config drop column base_info;
alter table jrzh_contract_template_fields_config add column  `clazz_name` varchar(255) DEFAULT '' COMMENT '字段映射类';
alter table jrzh_contract_template_fields_config add column  `config_id` bigint(20) DEFAULT NULL COMMENT '配置id';
alter table jrzh_contract_template_fields_config add column  `config_json` text COMMENT '配置json';
