/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 调息变更实体类
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@TableName("jrzh_adjust_interest")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AdjustInterest对象", description = "调息变更")
public class AdjustInterest extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 产品id
	*/
		@ApiModelProperty(value = "产品id")
		private Long goodsId;
	/**
	* 借据号
	*/
		@ApiModelProperty(value = "借据号")
		private String iouNo;
	/**
	* 融资编号
	*/
		@ApiModelProperty(value = "融资编号")
		private String financeNo;
	/**
	* 融资id
	*/
		@ApiModelProperty(value = "融资id")
		private Long financeId;
	/**
	* 融资用户
	*/
		@ApiModelProperty(value = "融资用户")
		private Long userId;
	/**
	* 客户产品id
	*/
		@ApiModelProperty(value = "客户产品id")
		private Long customerGoodsId;
	/**
	* 调息金额
	*/
		@ApiModelProperty(value = "调息金额")
		private BigDecimal amount;
	/**
	* 调息利率
	*/
		@ApiModelProperty(value = "调息利率")
		private BigDecimal interestRate;
	/**
	* 调息理由
	*/
		@ApiModelProperty(value = "调息理由")
		private String reason;

	@ApiModelProperty("变更次数")
	private Integer alterationFrequency;

	/**
	 * 凭证
	 */
	@ApiModelProperty(value = "凭证")
	private String adjunctProof;

	/**
	 * 调息时间
	 */
	@ApiModelProperty(value = "调息时间")
	private LocalDateTime adjustTime;

	@ApiModelProperty("旧融资id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long oldFinanceId;

	/**
	 * 还款方式
	 */
	@ApiModelProperty(value = "计费方式")
	private Integer repaymentMode;

	/**
	 * 日利率
	 */
	@ApiModelProperty(value = "日利率")
	@TableField(exist = false)
	private BigDecimal dailyInterestRate;

}
