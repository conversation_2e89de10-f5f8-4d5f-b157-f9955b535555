/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * 减免变更实体类
 *
 * <AUTHOR>
 * @since 2022-12-20
 */
@Data
@TableName("jrzh_loan_derate_alteration")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LoanDerateAlteration对象", description = "减免变更")
public class LoanDerateAlteration extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 融资编号
     */
    @ApiModelProperty(value = "融资编号")
    @NotEmpty(message = "缺少融资编号")
    private String financeNo;
    /**
     * 减免方式类型
     */
    @ApiModelProperty(value = "减免金额类型 1、本金 2、利息、2、服务费")
    @NotEmpty(message = "请选择减免类型")
    private String derateType;
    /**
     * 减免理由
     */
    @ApiModelProperty(value = "减免理由")
    private String reason;
    /**
     * 凭证
     */
    @ApiModelProperty(value = "凭证")
    private String adjunctProof;

    /**
     * 融资申请id
     */
    @ApiModelProperty(value = "融资申请id")
    private Long financeId;

    /**
     * 减免比例本金，减免金额本金
     */
    @ApiModelProperty(value = "减免比例本金，减免金额本金")
    private BigDecimal deratePrincipal;

    /**
     * 减免比例利息，减免金额利息
     */
    @ApiModelProperty(value = "减免比例利息，减免金额利息")
    private BigDecimal derateInterest;

    /**
     * 减免方式  1、比例 2、固定值
     */
    private Integer derateAmountType;

    /**
     * 减免类型
     */
    @ApiModelProperty(value = "减免类型 1 当期减免 2 剩余待还全部减免")
    private Integer type;

    /**
     * 工作流参数
     */
    @ApiModelProperty(value = "工作流参数")
    private String processInstanceId;
    /**
     * 填写的动态费用减免json
     */
    private String repaymentPlanFeeTotalListStr;
}
