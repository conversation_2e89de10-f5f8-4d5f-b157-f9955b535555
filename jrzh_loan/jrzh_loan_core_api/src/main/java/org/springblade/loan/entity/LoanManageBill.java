/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单管理实体类
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Data
@TableName("jrzh_loan_manage_bill")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LoanManageBill对象", description = "账单管理")
public class LoanManageBill extends TenantEntity {

	private static final long serialVersionUID = 1L;
	/**
	 * 还款日期
	 */
	@ApiModelProperty(value = "还款日期")
	private LocalDate billTime;
	/**
	 * 账单号
	 */
	@ApiModelProperty(value = "账单号")
	private String billNo;
	/**
	* 借据号
	*/
		@ApiModelProperty(value = "借据号")
		private String iouNo;

	/**
	* 融资用户id
	*/
		@ApiModelProperty(value = "融资用户id")
		private Long userId;
	/**
	* 期数
	*/
		@ApiModelProperty(value = "期数")
		private Integer period;
	/**
	* 还款日期
	*/
		@ApiModelProperty(value = "还款日期")
		private LocalDate repaymentTime;
	/**
	* 还款总额 =应还本金+应还利息+
	*/
		@ApiModelProperty(value = "还款总额 =应还本金+应还利息+")
		private BigDecimal repaymentTotal;
	/**
	* 应还本金
	*/
		@ApiModelProperty(value = "剩余应还本金")
		private BigDecimal surplusPrincipal;
	/**
	* 应还利息
	*/
		@ApiModelProperty(value = "剩余应还利息")
		private BigDecimal surplusInterest;

	/**
	 * 逾期罚息
	 */
		@ApiModelProperty(value = "剩余逾期罚息")
		private BigDecimal surplusPunishInterest;
	/**
	* 已还总额
	*/
		@ApiModelProperty(value = "已还总额")
		private BigDecimal repaidTotal;
	/**
	 * 已还本金
	 */
		@ApiModelProperty(value = "已还本金")
		private BigDecimal repaidPrincipal;
	/**
	 * 已还利息
	 */
		@ApiModelProperty(value = "已还利息")
		private BigDecimal repaidInterest;
	/**
	 * 已还逾期罚息
	 */
		@ApiModelProperty(value = "已还逾期罚息")
		private BigDecimal repaidPunishInterest;

	/**
	 * 期数
	 */
	@ApiModelProperty(value = "期数")
	private Integer version;
}
