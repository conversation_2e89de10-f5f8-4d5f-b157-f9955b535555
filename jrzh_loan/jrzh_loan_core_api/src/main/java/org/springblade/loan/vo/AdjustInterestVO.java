/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.loan.entity.AdjustInterest;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 调息变更视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AdjustInterestVO对象", description = "调息变更")
public class AdjustInterestVO extends AdjustInterest {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "融资用户名称")
	private String financingUser;

	@ApiModelProperty(value = "变更历史")
	private AlterationHistoryVO alterationHistoryVO;

	@ApiModelProperty(value = "借款金额")
	private BigDecimal loanAmount;

	@ApiModelProperty("还款方式")
	private String repaymentModeStr;

	/**
	 * 期数 + 单位
	 */
	private Integer periodDes;

	/**
	 * 首次还款日
	 */
	private LocalDate firstRepaymentDate;

	/**
	 * 到期日
	 */
	@ApiModelProperty(value = "到期日")
	private LocalDate expireTime;

	/**
	 * 还款日
	 */
	@JsonFormat(pattern = "每月dd日",timezone = "GMT+8")
	private LocalDate repaymentDate;

	/**
	 * 期限
	 */
	@ApiModelProperty(value = "期限单位")
	private Integer periodUnit;

	/**
	 * 期限
	 */
	@ApiModelProperty(value = "期限")
	private Integer period;

	public String getPeriodDes() {
		String unit;
		if (getPeriodUnit().equals(GoodsEnum.TERM.getCode())) {
			unit = GoodsEnum.TERM.getName();
		}else {
			unit = GoodsEnum.DAY.getName();
		}
		return getPeriod().toString() + unit;
	}

}
