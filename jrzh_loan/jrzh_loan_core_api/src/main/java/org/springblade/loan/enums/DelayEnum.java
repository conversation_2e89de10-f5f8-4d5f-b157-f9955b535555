package org.springblade.loan.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface DelayEnum {
	@AllArgsConstructor
	@Getter
	enum DelayTypeEnum {

		/**
		 * 展期类型
		 */
		CURRENT_PERIOD("当期展期",1),

		ALL_REMAINING("剩余待还全部展期",2),

		;

		private final String name;
		private final Integer code;

		public static String getNameByCode(Integer code) {
			if (code == null){
				return "";
			}
			for (DelayTypeEnum codeEnum : DelayTypeEnum.values()) {
				if (codeEnum.getCode().equals(code)) {
					return codeEnum.getName();
				}
			}
			return "";
		}
	}

	@AllArgsConstructor
	@Getter
	enum DelayModeEnum {

		/**
		 * 展期类型
		 */

		STAGES_REPAYMENT("分期还款",1),

		MINIMUM_REPAYMENT("最低还款",2),

		DELAY_REPAYMENT("延迟还款",3),


		;

		private final String name;
		private final Integer code;

		public static String getNameByCode(Integer code) {
			if (code == null){
				return "";
			}
			for (DelayModeEnum codeEnum : DelayModeEnum.values()) {
				if (codeEnum.getCode().equals(code)) {
					return codeEnum.getName();
				}
			}
			return "";
		}
	}
}
