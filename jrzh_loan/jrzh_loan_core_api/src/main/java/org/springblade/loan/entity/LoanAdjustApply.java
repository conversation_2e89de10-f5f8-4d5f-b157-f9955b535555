/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 调息申请信息实体类
 *
 * <AUTHOR>
 * @since 2022-12-08
 */
@Data
@TableName("jrzh_loan_adjust_apply")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LoanAdjustApply对象", description = "调息申请信息")
public class LoanAdjustApply extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 融资编号
	*/
		@ApiModelProperty(value = "融资编号")
		private String financeNo;

	@ApiModelProperty("融资申请id")
	private Long financeId;

	/**
	* 调息金额
	*/
		@ApiModelProperty(value = "调息金额")
		private BigDecimal amount;
	/**
	* 调息利率
	*/
	@ApiModelProperty(value = "调息利率")
	@NotNull(message = "调息利率不能为空!")
		private BigDecimal interestRate;
	/**
	* 工作流参数
	*/
		@ApiModelProperty(value = "工作流参数")
		@TableField(fill = FieldFill.UPDATE)
		private String processInstanceId;
	/**
	 * 凭证
	 */
	//@NotEmpty(message = "凭证为空")
	@ApiModelProperty(value = "凭证")
	private String adjunctProof;
	/**
	* 调息理由
	*/
		@ApiModelProperty(value = "调息理由")
		@NotNull(message = "调息理由不能为空!")
		private String reason;


}
