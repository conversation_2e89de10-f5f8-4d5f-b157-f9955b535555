/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 费用计划JSON 用于动态费用暂存地方 避免生成到计划中
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Data
@TableName("jrzh_repayment_plan_fee_json")
@EqualsAndHashCode(callSuper = true)
public class RepaymentPlanFeeJson extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 还款记录id
     */
    private Long repaymentId;
    /**
     * 费用计划json
     */
    @ApiModelProperty(value = "费用计划json")
    private String planFeeJson;
}
