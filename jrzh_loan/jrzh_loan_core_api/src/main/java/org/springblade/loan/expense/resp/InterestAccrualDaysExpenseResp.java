package org.springblade.loan.expense.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 利息计算
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InterestAccrualDaysExpenseResp implements Serializable {
    /**
     * 计息天数
     */
    private BigDecimal interestAccrualDay;
    /**
     * 计息天数日期范围
     */
    private String interestAccrualDateRange;

}
