/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.tool.utils.DateUtil;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 还款列表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data
public class LoanManageRepaymentDTO{
	private static final long serialVersionUID = 1L;

	@NotNull(message = "id不能为空")
	private Long id;
	private BigDecimal actualAmount;
	/**
	 * 支付凭证
	 */
	private String voucher;

	/**
	 * 支付方式 1、线下支付 2、线上支付
	 */
	private Integer payModeTypeface;
	/**
	 * 支付途径 BillPayNameEnum
	 */
	private Integer payMethod;
	/**
	 * 失败原因
	 */
	private String failReason;
	@NotNull(message = "状态不能为空")
	private Integer status;
	@NotNull(message = "借据单号不能为空")
	private String iouNo;
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private LocalDateTime repaymentTime;

	@ApiModelProperty("开户行")
	private String bank;

	@ApiModelProperty("付款账号")
	private String bankCardNo;
	/**
	 * 跳过金额校验
	 */
	private boolean skipAmountValid;
	/**
	 * 跳过费用订单生成
	 */
	private boolean skipExpenseOrderGen;
}
