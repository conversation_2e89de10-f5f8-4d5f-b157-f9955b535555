package org.springblade.repayment.dto;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.RepaymentFee;
import org.springblade.loan.entity.RepaymentPlanFee;

import java.math.BigDecimal;
import java.util.List;

/**
 * 还款订单信息
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-16  01:52
 * @Description: TODO
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepaymentOrderDTO {
    /**
     * 本次的还款记录
     */
    private LoanManageRepayment loanManageRepayment;
    /**
     * 本次的费用记录
     */
    List<RepaymentFee> repaymentFeeList;
    /**
     * 本次的费用计划
     */
    List<RepaymentPlanFee> repaymentPlanFeeList;

    /**
     * 总支付金额
     *
     * @return
     */
    public BigDecimal getTotalAmount() {
        return loanManageRepayment.getPrincipal()
                .add(loanManageRepayment.getInterest())
                .add(CollUtil.isNotEmpty(getRepaymentFeeList())
                        ? getRepaymentFeeList().stream().map(RepaymentFee::getShouldAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                        : BigDecimal.ZERO);
    }
}
