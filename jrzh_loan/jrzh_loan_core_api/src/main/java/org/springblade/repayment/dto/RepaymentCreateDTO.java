package org.springblade.repayment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.loan.entity.RepaymentPlanFee;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 还款订单创建参数
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-16  01:52
 * @Description: TODO
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepaymentCreateDTO {
    /**
     * 还款计划id
     */
    @NotNull(message = "还款计划id不能为空")
    private Long id;
    /**
     * 本次还款利息
     */
    private BigDecimal interest;
    /**
     * 罚息(逾期利息)
     */
    private BigDecimal penaltyInterest;
    /**
     * 服务费
     */
    private BigDecimal serverFee;
    /**
     * 还款金额
     */
    @NotNull(message = "还款金额不能为空")
    private BigDecimal amount;

    /**
     * 支付方式 1线下 2 线上 3银联 4 银联线下
     */
    private Integer payMode;
    /**
     * 本次的费用列表
     */
    private List<RepaymentPlanFee> repaymentPlanFeeList;
    /**
     * 还款类型 不填根据还款时间进行判断  RepaymentConstant.RepaymentTypeEnum
     */
    private Integer repaymentType;
    /**
     * 线上支付编号
     */
    private String onlinePayCode;
}
