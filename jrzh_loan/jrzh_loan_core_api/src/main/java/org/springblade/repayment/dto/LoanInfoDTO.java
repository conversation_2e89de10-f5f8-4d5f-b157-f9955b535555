package org.springblade.repayment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.loan.entity.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 借款情况
 */
@Data
public class LoanInfoDTO extends LoanManageIou {
    /**
     * 还款中 或已支付
     */
    private final static List<Integer> PAYING_OR_PAYED_STATUS = Arrays.asList(RepaymentConstant.RepaymentStatusEnum.PAYING.getCode()
            , RepaymentConstant.RepaymentStatusEnum.PAY.getCode());
    /**
     * 计息天数策略
     */
    private Integer interestDayStrategy;
    /**
     * 计息天数
     */
    private Integer interestDay;
    /**
     * 还款情况 包含已完结的 不包含作废的 按期数排除 从小到大
     */
    private List<RepaymentInfoDTO> repaymentInfoList;
    /**
     * 还款计划费用
     */
    private List<RepaymentPlanFee> repaymentPlanFee;
    /**
     * 待还款本金
     */
    private BigDecimal subPrincipal;
    /**
     * 待还额外利息
     */
    private BigDecimal subExInterest;
    /**
     * 待还利息
     */
    private BigDecimal subInterest;

    /**
     * 其他费用待还
     */
    private BigDecimal getSubOtherFee() {
        return listUsingRepaymentInfo().stream().flatMap(e -> e.getRepaymentPlanFeeList().stream()).map(RepaymentPlanFee::getNeedPayAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 借款天数
     */
    private Integer loanDays;

    @ApiModelProperty("年利率")
    private BigDecimal annualInterestRate;
    /**
     * 服务费率
     */
    private BigDecimal serviceFeeRate;
    /**
     * 提前还款天数
     */
    private Integer prepaymentDays;
    /**
     * 总体逾期天数 结束借款-现在 的天数
     */
    private Integer overDueDay;
    /**
     * 总剩余减免金额
     */
    private BigDecimal subReductionInterest;
    /**
     * 本次还款金额
     * 不填按当前剩余本金算
     *
     * @return
     */
    private BigDecimal currentRepaymentAmount;
    /**
     * 当前参与计算的还款计划 用于在总体节点计算每一期的费用计算参数
     */
    private RepaymentInfoDTO currentRepaymentInfoDTO;

    public BigDecimal getSubTotal() {
        return subPrincipal.add(subExInterest).add(subInterest).add(getSubOtherFee());
    }

    /**
     * 未结清还款计划列表
     *
     * @return
     */
    public List<RepaymentInfoDTO> listUsingRepaymentInfo() {
        if(repaymentInfoList==null){
            return Collections.emptyList();
        }
        return repaymentInfoList.stream().filter(e -> RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == e.getRepaymentStatus())
                .sorted(Comparator.comparing(LoanManageRepaymentPlan::getPeriod)).collect(Collectors.toList());
    }

    /**
     * 所有剩需需要付的动态费用
     *
     * @return
     */
    public List<RepaymentPlanFee> listSubRepaymentPlanFee() {
        return this.getRepaymentInfoList().stream()
                .flatMap(e -> e.getRepaymentPlanFeeList().stream()
                ).collect(Collectors.toList());
    }

    /**
     * 所有正在使用中的费用计划
     *
     * @return
     */
    public List<RepaymentPlanFee> listUsingRepaymentPlanFee() {
        return this.getRepaymentInfoList().stream()
                .flatMap(e -> e.listUsingRepaymentPlanFee().stream()
                ).collect(Collectors.toList());
    }

    /**
     * 支付中/已经支付的费用
     *
     * @return
     */
    public List<RepaymentFee> listPayOrPayingRepaymentFee() {
        return getRepaymentInfoList().stream().flatMap(e -> e.getRepaymentFeeList().stream()
                .filter(x -> PAYING_OR_PAYED_STATUS.contains(x.getStatus()))).collect(Collectors.toList());
    }

    /**
     * 还款记录
     *
     * @return
     */
    public List<LoanManageRepayment> listLoanManageRepayment() {
        return getRepaymentInfoList().stream().flatMap(e -> e.getValidRepaymentList().stream()).collect(Collectors.toList());
    }

    public List<RepaymentPlanFee> allRepaymentPlanFee() {
        return repaymentInfoList.stream().flatMap(e -> e.getAllRepaymentPlanFeeList().stream()).collect(Collectors.toList());
    }
}
