package org.springblade.repayment.handler;

import org.springblade.common.enums.GoodsEnum;
import org.springblade.loan.entity.LoanManageIou;

/**
 * 借据单结清通知
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-16  03:25
 * @Description: 借据单结清通知
 * @Version: 1.0
 */
public interface LoanManageIouSettleNotifyHandler {
    /**
     * 支持类型
     * @return
     */
    GoodsEnum support();
    /**
     * 正常结清通知
     *
     * @param loanManageIou 借据单
     */
    void normalSettle(LoanManageIou loanManageIou);

    /**
     * 逾期结清通知
     *
     * @param loanManageIou 借据单
     */
    void overdueSettled(LoanManageIou loanManageIou);

    /**
     * 提前结清通知
     *
     * @param loanManageIou 借据单
     */
    void advanceSettled(LoanManageIou loanManageIou);
}
