package org.springblade.repayment.publisher;

import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.repayment.event.RepaymentSuccessEvent;
import org.springframework.stereotype.Component;

/**
 * 还款成功事件
 * <AUTHOR>
 * @date 2025/2/19
 */
@Component
public class RepaymentSuccessPublisher {

    /**
     * 还款成功事件推送
     * @param financeApplyId
     * @param loanManageRepayment
     */
    public void pushRepaymentSuccess(Long financeApplyId, LoanManageRepayment loanManageRepayment) {
        SpringUtil.publishEvent(new RepaymentSuccessEvent(this, financeApplyId, loanManageRepayment));
    }

}
