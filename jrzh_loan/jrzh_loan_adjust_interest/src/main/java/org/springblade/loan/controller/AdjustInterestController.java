/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.loan.dto.AdjustmentInterestDTO;
import org.springblade.loan.entity.AdjustInterest;
import org.springblade.loan.service.IAdjustInterestService;
import org.springblade.loan.vo.AdjustInterestVO;
import org.springblade.loan.vo.LoanAdjustHistoryVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 调息变更 控制器
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_LOAN + CommonConstant.WEB_BACK +"/loan/adjustInterest")
@Api(value = "调息变更", tags = "调息变更接口")
public class AdjustInterestController extends BladeController {

	private final IAdjustInterestService adjustInterestService;


	/**
	 * 调息历史变更
	 */
	@GetMapping("/history")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入adjustInterest")
	@PreAuth("hasPermission('loan:adjustInterest:history') or hasRole('administrator')")
	public R<AdjustInterestVO> history(AdjustInterestVO adjustInterest) {
		adjustInterestService.adjustInterestHistory(adjustInterest);
		if(true){
			throw new UnsupportedOperationException("TODO");
		}
		return null;
	}


	/**
	 * 根据融资申请id查询调息历史记录
	 */
	@GetMapping("/getLoanAlterationHistory")
	@ApiOperation("查询调息历史记录")
	public R<Map<Integer, List<LoanAdjustHistoryVO>>> getLoanAlterationHistory(@RequestParam Long financeId) {
		return R.data(adjustInterestService.getLoanAlterationHistory(financeId));
	}

	/**
	 * 分页 调息变更
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入adjustInterest")
	@PreAuth("hasPermission('loan:adjustInterest:list') or hasRole('administrator')")
	public R<IPage<AdjustInterestVO>> list(AdjustInterestVO adjustInterest, Query query) {
		return R.data(adjustInterestService.selectAdjustInterestList(adjustInterest,query));
	}


	/**
	 * 自定义分页 调息变更
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入adjustInterest")
	@PreAuth("hasPermission('adjustInterest:adjustInterest:page') or hasRole('administrator')")
	public R<IPage<AdjustInterestVO>> page(AdjustInterestVO adjustInterest, Query query) {
		IPage<AdjustInterestVO> pages = adjustInterestService.selectAdjustInterestPage(Condition.getPage(query), adjustInterest);
		return R.data(pages);
	}

	/**
	 * 新增 调息变更
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入adjustInterest")
	@PreAuth("hasPermission('adjustInterest:adjustInterest:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody AdjustInterest adjustInterest) {
		return R.status(adjustInterestService.save(adjustInterest));
	}

	/**
	 * 开启调息申请流程
	 */
	@PostMapping("/open_adjust_interest")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "开启调息申请流程", notes = "adjustmentInterestDTO")
	public R openAdjustInterest(AdjustmentInterestDTO adjustmentInterestDTO) {
		return R.data(adjustInterestService.openAdjustInterest(adjustmentInterestDTO));
	}

	/**
	 * 修改 调息变更
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入adjustInterest")
	@PreAuth("hasPermission('adjustInterest:adjustInterest:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody AdjustInterest adjustInterest) {
		return R.status(adjustInterestService.updateById(adjustInterest));
	}

	/**
	 * 新增或修改 调息变更
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入adjustInterest")
	@PreAuth("hasPermission('adjustInterest:adjustInterest:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody AdjustInterest adjustInterest) {
		return R.status(adjustInterestService.saveOrUpdate(adjustInterest));
	}


	/**
	 * 删除 调息变更
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('adjustInterest:adjustInterest:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(adjustInterestService.deleteLogic(Func.toLongList(ids)));
	}


}
