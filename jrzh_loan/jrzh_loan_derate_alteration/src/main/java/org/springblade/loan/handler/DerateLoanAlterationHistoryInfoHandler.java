package org.springblade.loan.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.utils.BorrowAndReturnUtils;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.loan.entity.DerateAlterationHistory;
import org.springblade.loan.enums.LoanAlterationEnum;
import org.springblade.loan.service.IDerateAlterationHistoryService;
import org.springblade.loan.vo.LoanHistoryRepaymentPlanVO;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class DerateLoanAlterationHistoryInfoHandler implements LoanAlterationHistoryInfoHandler {
    private final IDerateAlterationHistoryService derateAlterationHistoryService;

    @Override
    public LoanAlterationEnum support() {
        return LoanAlterationEnum.lOAN_DERATE_ALTERATION;
    }

    @Override
    public void fillInfo(List<LoanHistoryRepaymentPlanVO> loanHistoryRepaymentPlanVOList, List<Long> financeIds) {
        List<DerateAlterationHistory> alterationHistoryList = derateAlterationHistoryService
                .list(Wrappers.<DerateAlterationHistory>lambdaQuery().in(DerateAlterationHistory::getFinanceId, financeIds));
        if (CollUtil.isEmpty(alterationHistoryList)) {
            return;
        }
        Map<Integer, DerateAlterationHistory> alterationHistoryMap = StreamUtil.toMap(alterationHistoryList, DerateAlterationHistory::getAlterationFrequency, obj -> obj);

        for (LoanHistoryRepaymentPlanVO loanHistoryRepaymentPlanVO : loanHistoryRepaymentPlanVOList) {
            DerateAlterationHistory derateAlterationHistory = alterationHistoryMap.get(loanHistoryRepaymentPlanVO.getAlterationFrequency());
            if(ObjectUtil.isNotEmpty(derateAlterationHistory)){
                List<Attach> attaches = selectAfterLoanAttaches(derateAlterationHistory.getAdjunctProof());
                loanHistoryRepaymentPlanVO.setAttachList(attaches);
                loanHistoryRepaymentPlanVO.setAnnualInterestRate(derateAlterationHistory.getAnnualInterestRate());
                loanHistoryRepaymentPlanVO.setDailyInterestRate(BorrowAndReturnUtils.calculationDayRate(derateAlterationHistory.getAnnualInterestRate(), 2));
                loanHistoryRepaymentPlanVO.setDerateAlterationHistory(derateAlterationHistory);
                loanHistoryRepaymentPlanVO.setRepaymentMode(derateAlterationHistory.getRepaymentMode());
            }
        }

    }

    private List<Attach> selectAfterLoanAttaches(String adjunctProof) {
        return SpringUtil.getBean(IAttachService.class).lambdaQuery().in(Attach::getId, Func.toLongList(adjunctProof)).list();
    }
}
