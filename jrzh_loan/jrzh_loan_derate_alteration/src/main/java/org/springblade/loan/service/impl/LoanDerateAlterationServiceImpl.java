/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.EnterpriseTypeEnum;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.enums.ProcessTypeEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.feign.RemoteEnterpriseQuotaService;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.ExpenseOrderDetailFinanceVo;
import org.springblade.loan.constant.DerateAlterationConstant;
import org.springblade.loan.dto.LoanDerateAlterationDTO;
import org.springblade.loan.dto.RepaymentPlanFeeTotal;
import org.springblade.loan.entity.LoanDerateAlteration;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.loan.enums.DerateAlterationEnum;
import org.springblade.loan.mapper.LoanDerateAlterationMapper;
import org.springblade.loan.service.*;
import org.springblade.loan.vo.*;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.resource.constant.IncomeDetailConstant;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.entity.IncomeDetail;
import org.springblade.resource.service.IAttachService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 减免变更 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-20
 */
@Service
@AllArgsConstructor
@Slf4j
public class LoanDerateAlterationServiceImpl extends BaseServiceImpl<LoanDerateAlterationMapper, LoanDerateAlteration> implements ILoanDerateAlterationService {
    private final IBusinessProcessService businessProcessService;
    private final IWfProcessService wfProcessService;
    private final IFinanceApplyService financeApplyService;
    private final ILoanManageIouService loanManageIouService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final IDerateAlterationHistoryService derateAlterationHistoryService;
    private final ILoanAlterationHistoryService loanAlterationHistoryService;
    private final IRepaymentPlanFinanceApplyCommonService repaymentPlanFinanceApplyCommonService;
    private final IRepaymentBizService repaymentBizService;

    private final IAttachService attachService;
    private final RemoteEnterpriseQuotaService enterpriseQuotaService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final IBusinessProcessProductService businessProcessProductService;

    @Override
    public IPage<LoanDerateAlterationVO> selectLoanDerateAlterationPage(IPage<LoanDerateAlterationVO> page, LoanDerateAlterationVO loanDerateAlteration) {
        return page.setRecords(baseMapper.selectLoanDerateAlterationPage(page, loanDerateAlteration));
    }

    @Override
    public void commitDerateAlterationApply(LoanDerateAlterationDTO loanDerateAlteration) {
        //检查并重置传入值
        checkLoanDerateAlteration(loanDerateAlteration);
        FinanceApply financeApply = financeApplyService.getByFinanceNo(loanDerateAlteration.getFinanceNo());
        if (Objects.isNull(financeApply)) {
            throw new ServiceException("融资订单不存在!");
        }
        LoanInfoDTO loanInfoDTO = repaymentBizService.getLoanInfoDTOByFinanceNo(financeApply.getFinanceNo());
        Boolean hasPaying = loanManageRepaymentService.hasPayingByPlanIds(loanInfoDTO.listUsingRepaymentInfo()
                .stream().map(RepaymentInfoDTO::getId).collect(Collectors.toList()));
        //减免金额不能超过当前类型金额
        checkAmountIsExceed(loanDerateAlteration, loanInfoDTO);
        if (hasPaying) {
            throw new ServiceException("当前存在支付中还款订单");
        }
        LoanDerateAlteration derateAlteration = baseMapper.selectOne(Wrappers.<LoanDerateAlteration>lambdaQuery().eq(LoanDerateAlteration::getFinanceNo, loanDerateAlteration.getFinanceNo()));
        if (derateAlteration != null) {
            loanDerateAlteration.setId(derateAlteration.getId());
            loanDerateAlteration.setCreateUser(derateAlteration.getCreateUser());
            loanDerateAlteration.setCreateDept(derateAlteration.getCreateDept());
            loanDerateAlteration.setCreateTime(derateAlteration.getCreateTime());
        }
        String processInstanceId = loanDerateAlteration.getProcessInstanceId();
        if (CollectionUtil.isNotEmpty(loanDerateAlteration.getRepaymentPlanFeeTotalList())) {
            loanDerateAlteration.setRepaymentPlanFeeTotalListStr(JSONUtil.toJsonStr(loanDerateAlteration.getRepaymentPlanFeeTotalList()));
        }
        loanDerateAlteration.setFinanceId(financeApply.getId());
        Map<String, Object> variables = MapUtil.newHashMap();
        saveOrUpdate(loanDerateAlteration);
//        ProcessDefinition processDefinition = wfProcessService.selectProcessDefinitionByKey(DerateAlterationConstant.DERATE_ALTERATION_APPLY);
        variables.put(WfProcessConstant.PROCESS_NO, businessProcessService.getProcessNo(processInstanceId));
        variables.put(DerateAlterationConstant.DERATE_ALTERATION_DELAY, loanDerateAlteration);
        variables.put(DerateAlterationConstant.REPAYMENT_PLAN_FEE_TOTAL_LIST, loanDerateAlteration.getRepaymentPlanFeeTotalList());
        //开启流程
        String processId = businessProcessProductService.startOrSubmit(financeApply.getGoodsId(), ProcessTypeEnum.RECEIVE_DERATE_ALTERATION_APPLY.getCode(), processInstanceId, variables, 1);
        loanDerateAlteration.setProcessInstanceId(processId);
        updateById(loanDerateAlteration);
        businessProcessProgressService.saveBusinessProcessProgress(financeApply.getId(), 1, ProcessTypeEnum.RECEIVE_DERATE_ALTERATION_APPLY.getCode(), loanDerateAlteration.getProcessInstanceId());
        financeApply.setStatus(FinanceApplyStatusEnum.DERATE_ALTERATION_APPLY.getCode());
        financeApplyService.updateById(financeApply);
    }

    @Override
    public void checkAmountIsExceed(LoanDerateAlterationDTO loanDerateAlteration, LoanInfoDTO loanInfoDTO) {
        //需要检查的还款计划
        List<RepaymentInfoDTO> needCheckRepaymentInfoDTO = DerateAlterationEnum.DerateAlterationTypeEnum.LOAN_DERATE_CURRENT_PERIOD.getCode().equals(loanDerateAlteration.getType())
                ? Collections.singletonList(loanInfoDTO.listUsingRepaymentInfo().get(0)) : loanInfoDTO.listUsingRepaymentInfo();
        //按比例减免检查
        if (DerateAlterationEnum.DerateAmountTypeEnum.LOAN_DERATE_RATIO.getCode().equals(loanDerateAlteration.getDerateAmountType())) {
            //1、比例不超过100
            rateCheck(loanDerateAlteration.getDerateInterest());
            rateCheck(loanDerateAlteration.getDeratePrincipal());
            if (loanDerateAlteration.getRepaymentPlanFeeTotalList() != null) {
                for (RepaymentPlanFeeTotal repaymentPlanFeeTotal : loanDerateAlteration.getRepaymentPlanFeeTotalList()) {
                    rateCheck(repaymentPlanFeeTotal.getFeeVal());
                }
            }
        } else {
            //按固定值减免检查
            //减免本金不得大于累计金额
            BigDecimal subPrincipalTotal = needCheckRepaymentInfoDTO.stream()
                    .map(RepaymentInfoDTO::getSubPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (subPrincipalTotal.compareTo(loanDerateAlteration.getDeratePrincipal()) < 0) {
                throw new ServiceException(String.format("减免金额不得大于%s元", subPrincipalTotal));
            }
            //减免利息不得大于累计利息
            BigDecimal reduceInterest = needCheckRepaymentInfoDTO.stream()
                    .map(RepaymentInfoDTO::getPlanInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (reduceInterest.compareTo(loanDerateAlteration.getDerateInterest()) < 0) {
                throw new ServiceException(String.format("减免利息不得大于%s元", reduceInterest));
            }
            //减免动态费用不得大于累计费用
            List<RepaymentPlanFeeTotal> repaymentPlanFeeTotalList = loanDerateAlteration.getRepaymentPlanFeeTotalList();
            if (loanDerateAlteration.getRepaymentPlanFeeTotalList() != null) {
                Map<Long, List<RepaymentPlanFee>> repaymentPlanFeeMap = loanInfoDTO.listUsingRepaymentPlanFee().stream().collect(Collectors.groupingBy(RepaymentPlanFee::getExpenseTypeId));
                for (RepaymentPlanFeeTotal repaymentPlanFeeTotal : repaymentPlanFeeTotalList) {
                    List<RepaymentPlanFee> repaymentPlanFeeList = repaymentPlanFeeMap.get(repaymentPlanFeeTotal.getExpenseTypeId());
                    if (CollectionUtil.isEmpty(repaymentPlanFeeList)) {
                        continue;
                    }
                    BigDecimal reduce = repaymentPlanFeeList.stream().map(RepaymentPlanFee::getPlanNeePayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (reduce.compareTo(repaymentPlanFeeTotal.getFeeVal()) < 0) {
                        throw new ServiceException(String.format("减免%s不得大于%s元", repaymentPlanFeeTotal.getFeeTypeName(), reduce));
                    }
                }
            }
        }
    }

    private void rateCheck(BigDecimal amount) {
        if (ObjectUtil.isEmpty(amount)) {
            return;
        }
        if (amount.compareTo(BigDecimal.valueOf(100)) > 0 || amount.compareTo(BigDecimal.valueOf(0)) < 0) {
            throw new ServiceException("减免比例必须在0到100之间");
        }
    }

    @Override
    public LoanDerateAlterationInfoVO getDerateAlterationInfo(String financeNo) {
        LoanDerateAlteration one = lambdaQuery().eq(LoanDerateAlteration::getFinanceNo, financeNo).one();
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financeNo);
        if (Objects.isNull(financeApply)) {
            throw new ServiceException("融资订单不存在!");
        }
        LoanDerateAlterationDTO loanDerateAlteration = null;
        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financeNo);
        LoanManageIouVO loanManageIouVO = loanManageIouService.adjustmentDetail(loanManageIou.getId());
        //查询订单基本信息
        EnterpriseQuotaVO enterpriseQuotaVO = enterpriseQuotaService
                .getByGoodsIdAndEnterpriseTypeAndEnterpriseId(financeApply.getGoodsId(), EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), financeApply.getUserId()).getData();
        //附件凭证
        List<Attach> attaches = null;
        List<LoanAdjustManageRepaymentVO> repaymentPlanList = null;
        if (Objects.nonNull(one)) {
            loanDerateAlteration = BeanUtil.copyProperties(one, LoanDerateAlterationDTO.class);
            if (StringUtil.isNotBlank(loanDerateAlteration.getRepaymentPlanFeeTotalListStr())) {
                loanDerateAlteration.setRepaymentPlanFeeTotalList(JSONUtil.toList(loanDerateAlteration.getRepaymentPlanFeeTotalListStr(), RepaymentPlanFeeTotal.class));
            }
            if (StringUtil.isNotBlank(loanDerateAlteration.getAdjunctProof())) {
                attaches = attachService.lambdaQuery().in(Attach::getId, Func.toLongList(loanDerateAlteration.getAdjunctProof())).list();
            }
            if (!loanDerateAlteration.getStatus().equals(2)) {
                loanDerateAlteration.setFinanceNo(financeNo);
                //查询减免后的还款计划
                List<RepaymentInfoDTO> derateRepaymentPlan = selectDerateRepaymentPlan(loanDerateAlteration);
                repaymentPlanList = loanHistoryRepaymentPlanList(derateRepaymentPlan);
            }
        }
        return LoanDerateAlterationInfoVO
                .builder()
                .productId(financeApply.getGoodsId())
                .productName(financeApply.getGoodsName())
                //TODO 统一获取
                //.productImg(goods.getBackground())
                .reason(loanDerateAlteration == null ? "" : loanDerateAlteration.getReason())
                .usedAmount(financeApply.getAmount())
                .billingMethod(financeApply.getRepaymentMode())
                .period(loanManageIou.getPeriod())
                .recycleType(enterpriseQuotaVO == null ? null : enterpriseQuotaVO.getQuotaType())
                .periodUnit(loanManageIou.getPeriodUnit())
                .loanUsage(financeApply.getLoanUsage())
                .attachList(attaches)
                .loanDerateAlteration(loanDerateAlteration)
                .adjustManageRepaymentList(repaymentPlanList)
                .loanManageIouVO(loanManageIouVO)
                .build();
    }

    @Override
    public LoanAdjustManageRepaymentInfoVO getDerateCalculate(LoanDerateAlterationDTO loanDerateAlteration) {
        //检查并重置传入值
        checkLoanDerateAlteration(loanDerateAlteration);
        LoanInfoDTO loanInfoDTO = repaymentBizService.getLoanInfoDTOByFinanceNo(loanDerateAlteration.getFinanceNo());
        //校验查询参数
        checkAmountIsExceed(loanDerateAlteration, loanInfoDTO);

        //查询减免后的还款计划
        List<RepaymentInfoDTO> derateRepaymentPlan = selectDerateRepaymentPlan(loanDerateAlteration);
        List<LoanAdjustManageRepaymentVO> loanAdjustManageRepaymentVOS = cn.hutool.core.bean.BeanUtil.copyToList(derateRepaymentPlan, LoanAdjustManageRepaymentVO.class);
        BigDecimal total = loanAdjustManageRepaymentVOS.stream().map(LoanAdjustManageRepaymentVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal interest = loanAdjustManageRepaymentVOS.stream().map(LoanAdjustManageRepaymentVO::getPlanInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal principal = loanAdjustManageRepaymentVOS.stream().map(LoanAdjustManageRepaymentVO::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        loanAdjustManageRepaymentVOS.forEach(e -> {
            e.setMonthlyInterest(interest);
            e.setMonthlyPrincipal(principal);
            e.setMonthlySupply(total);
        });
        LoanAdjustManageRepaymentInfoVO loanAdjustManageRepaymentInfoVO = new LoanAdjustManageRepaymentInfoVO();
        loanAdjustManageRepaymentInfoVO.setNum(0);
        loanAdjustManageRepaymentInfoVO.setLoanHistoryRepaymentPlanVO(loanAdjustManageRepaymentVOS);
        List<RepaymentPlanFee> repaymentPlanFees = derateRepaymentPlan.stream()
                .filter(e -> CollectionUtil.isNotEmpty(e.getAllRepaymentPlanFeeList()))
                .flatMap(e -> e.getAllRepaymentPlanFeeList().stream()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(repaymentPlanFees)) {
            List<ExpenseOrderDetail> expenseOrderDetails = repaymentPlanFinanceApplyCommonService.transferToExpenseDetail(repaymentPlanFees);
            List<ExpenseOrderDetailFinanceVo> expenseOrderDetailFinanceVos = repaymentPlanFinanceApplyCommonService.buildExpenseOrderDetailFinanceVo(expenseOrderDetails);
            loanAdjustManageRepaymentInfoVO.setExpenseOrderDetailFinanceVos(expenseOrderDetailFinanceVos);
        } else {
            loanAdjustManageRepaymentInfoVO.setExpenseOrderDetailFinanceVos(Collections.emptyList());
        }
        loanAdjustManageRepaymentInfoVO.setRepaymentInfoDTOList(derateRepaymentPlan);
        return loanAdjustManageRepaymentInfoVO;
    }

    private List<LoanAdjustManageRepaymentVO> loanHistoryRepaymentPlanList(List<RepaymentInfoDTO> loanManageRepaymentPlanVOS) {
//        List<LoanManageRepaymentPlanVO> manageRepaymentPlanVOList = loanManageRepaymentPlanVOS.stream().filter(loanManageRepaymentPlanVO -> loanManageRepaymentPlanVO.getRepaymentStatus().equals(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())).collect(Collectors.toList());
        BigDecimal interest = loanManageRepaymentPlanVOS.stream().map(RepaymentInfoDTO::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal principal = loanManageRepaymentPlanVOS.stream().map(RepaymentInfoDTO::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal serviceFee = loanManageRepaymentPlanVOS.stream().map(RepaymentInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAmount = loanManageRepaymentPlanVOS.stream().map(RepaymentInfoDTO::calSubTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        return loanManageRepaymentPlanVOS.stream().map(derateRepayment -> {
            LoanAdjustManageRepaymentVO loanAdjustManageRepaymentVO = new LoanAdjustManageRepaymentVO();
            BeanUtil.copyProperties(derateRepayment, loanAdjustManageRepaymentVO);
            loanAdjustManageRepaymentVO.setMonthlyServiceFee(serviceFee.setScale(2, RoundingMode.HALF_DOWN));
            loanAdjustManageRepaymentVO.setMonthlyInterest(interest.setScale(2, RoundingMode.HALF_DOWN));
            loanAdjustManageRepaymentVO.setMonthlyPrincipal(principal.setScale(2, RoundingMode.HALF_DOWN));
            loanAdjustManageRepaymentVO.setMonthlySupply(totalAmount.setScale(2, RoundingMode.HALF_DOWN));
            loanAdjustManageRepaymentVO.setInterest(loanAdjustManageRepaymentVO.getInterest().setScale(2, RoundingMode.HALF_DOWN));
            loanAdjustManageRepaymentVO.setPrincipal(loanAdjustManageRepaymentVO.getPrincipal().setScale(2, RoundingMode.HALF_DOWN));
            loanAdjustManageRepaymentVO.setServiceFee(loanAdjustManageRepaymentVO.getServiceFee().setScale(2, RoundingMode.HALF_DOWN));
            loanAdjustManageRepaymentVO.setTotalAmount(loanAdjustManageRepaymentVO.getTotalAmount().setScale(2, RoundingMode.HALF_DOWN));
            return loanAdjustManageRepaymentVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RepaymentInfoDTO> selectDerateRepaymentPlan(LoanDerateAlterationDTO loanDerateAlteration) {
        //检查并重置传入值
        checkLoanDerateAlteration(loanDerateAlteration);
        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(loanDerateAlteration.getFinanceNo());
        FinanceApply financeApply = financeApplyService.getByFinanceNo(loanDerateAlteration.getFinanceNo());
        if (Objects.isNull(financeApply)) {
            return Collections.emptyList();
        }
        LoanInfoDTO loanInfoDTO = repaymentBizService.getLoanInfoDTO(loanManageIou.getIouNo());
        //需要减免的期数等于当期或剩余所有
        List<RepaymentInfoDTO> loanManageRepaymentPlanList = DerateAlterationEnum.DerateAlterationTypeEnum.LOAN_DERATE_CURRENT_PERIOD.getCode().equals(loanDerateAlteration.getType())
                ? Collections.singletonList(loanInfoDTO.listUsingRepaymentInfo().get(0))
                : loanInfoDTO.listUsingRepaymentInfo();
        //最近一次减免次数
//        Integer lastMaxAlterationCount = loanAlterationHistoryService.getLastMaxAlterationCountFrequencyByLoanId(loanInfoDTO.getId());
        BigDecimal deratePrincipal = ObjectUtil.isNotEmpty(loanDerateAlteration.getDeratePrincipal()) ? loanDerateAlteration.getDeratePrincipal() : BigDecimal.ZERO;
        BigDecimal derateInterest = ObjectUtil.isNotEmpty(loanDerateAlteration.getDerateInterest()) ? loanDerateAlteration.getDerateInterest() : BigDecimal.ZERO;
        BigDecimal allocationedPrincipal = BigDecimal.ZERO;
        BigDecimal allocationedInterest = BigDecimal.ZERO;
        //固定值减免 减免规则 从第一期开始减 一直扣到最后
        if (DerateAlterationEnum.DerateAmountTypeEnum.LOAN_DERATE_FIXED_VALUE.getCode().equals(loanDerateAlteration.getDerateAmountType())) {
            for (RepaymentInfoDTO repaymentInfoDTO : loanManageRepaymentPlanList) {
                //本金最多减到0
                BigDecimal subPrincipalAmount = deratePrincipal.subtract(allocationedPrincipal);
                BigDecimal subedPrincipal = getSubedFee(subPrincipalAmount, repaymentInfoDTO.getSubPrincipal());
                repaymentInfoDTO.setPrincipal(repaymentInfoDTO.getPrincipal().subtract(subedPrincipal));
                allocationedPrincipal = allocationedPrincipal.add(subedPrincipal);

                //利息最多减到0
                BigDecimal subInterestAmount = derateInterest.subtract(allocationedInterest);
                BigDecimal subedInterest = getSubedFee(subInterestAmount, repaymentInfoDTO.getPlanInterest());
                //增加利息减免记录
                if (subedInterest.compareTo(BigDecimal.ZERO) > 0) {
                    IncomeDetail incomeDetail = repaymentBizService.addReductionInterest(repaymentInfoDTO, subedInterest);
                    repaymentInfoDTO.getReductionInterestList().add(incomeDetail);
                    allocationedInterest = allocationedInterest.add(subedInterest);
                }

                //动态费用依次扣取 直到扣完
                derateCurrentPeriodFixFee(loanDerateAlteration.getRepaymentPlanFeeTotalList(), repaymentInfoDTO);
            }
        } else {
            //按比例减免
            for (RepaymentInfoDTO repaymentInfoDTO : loanManageRepaymentPlanList) {
                //本金比例减免
                if (deratePrincipal.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal principalRate = deratePrincipal.divide(BigDecimal.valueOf(100));
                    BigDecimal subedPrincipal = repaymentInfoDTO.getSubPrincipal().multiply(principalRate).setScale(2, CommonConstant.NUMBER_STRATEGY);
                    repaymentInfoDTO.setPrincipal(repaymentInfoDTO.getPrincipal().subtract(subedPrincipal));
                }
                //利息比例减免
                if (derateInterest.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal interestRate = derateInterest.divide(BigDecimal.valueOf(100));
                    BigDecimal subedInterest = repaymentInfoDTO.getPlanInterest().multiply(interestRate).setScale(2, CommonConstant.NUMBER_STRATEGY);
                    //增加利息减免记录
                    if (subedInterest.compareTo(BigDecimal.ZERO) > 0) {
                        IncomeDetail incomeDetail = repaymentBizService.addReductionInterest(repaymentInfoDTO, subedInterest);
                        repaymentInfoDTO.getReductionInterestList().add(incomeDetail);
                    }
                }
                //动态费用比例减免
                derateCurrentPeriodRateFee(loanDerateAlteration.getRepaymentPlanFeeTotalList(), repaymentInfoDTO);
            }
        }
        return loanInfoDTO.getRepaymentInfoList();
    }

    /**
     * 动态费用固定减免
     */
    private void derateCurrentPeriodFixFee(List<RepaymentPlanFeeTotal> repaymentPlanFeeTotalList, RepaymentInfoDTO loanManageRepaymentPlan) {
        if (CollectionUtil.isEmpty(repaymentPlanFeeTotalList)) {
            return;
        }
        Map<Long, RepaymentPlanFee> collect = loanManageRepaymentPlan.getRepaymentPlanFeeList()
                .stream().collect(Collectors.toMap(RepaymentPlanFee::getExpenseTypeId, e -> e));

        for (RepaymentPlanFeeTotal e : repaymentPlanFeeTotalList) {
            if (collect.containsKey(e.getExpenseTypeId()) && e.getFeeVal() != null) {
                //已分配金额
                if (e.getAllocationed() == null) {
                    e.setAllocationed(BigDecimal.ZERO);
                }
                //剩余分配金额
                BigDecimal subAmount = e.getFeeVal().subtract(e.getAllocationed());
                //分配金额原则 只要剩余分配金额够 就直接扣除 不够就分配剩余所有
                RepaymentPlanFee repaymentPlanFee = collect.get(e.getExpenseTypeId());
                if (ObjectUtil.isNotEmpty(repaymentPlanFee)) {
                    BigDecimal subedFee = getSubedFee(subAmount, repaymentPlanFee.getPlanNeePayAmount());
                    //插入减免列表
                    IncomeDetail incomeDetail = repaymentBizService.addReductionRepaymentPlanFee(repaymentPlanFee, subedFee);
                    repaymentPlanFee.getReductionFeeList().add(incomeDetail);
                    //增加已分配金额
                    e.setAllocationed(e.getAllocationed().add(subedFee));
                }
            }
        }
    }

    /**
     * 分配金额
     *
     * @param subAmount 剩余分配的减免金额
     * @param amount    金额
     * @return
     */
    private static BigDecimal getSubedFee(BigDecimal subAmount, BigDecimal amount) {
        return amount.compareTo(subAmount) > 0 ? subAmount : amount;
    }

    /**
     * 动态费用比例减免
     */
    private void derateCurrentPeriodRateFee(List<RepaymentPlanFeeTotal> repaymentPlanFeeTotalList, RepaymentInfoDTO loanManageRepaymentPlan) {
        Map<Long, RepaymentPlanFee> collect = loanManageRepaymentPlan.getRepaymentPlanFeeList().stream().collect(Collectors.toMap(RepaymentPlanFee::getExpenseTypeId, e -> e));
        for (RepaymentPlanFeeTotal e : repaymentPlanFeeTotalList) {
            if (collect.containsKey(e.getExpenseTypeId()) && e.getFeeVal() != null) {
                RepaymentPlanFee repaymentPlanFee = collect.get(e.getExpenseTypeId());
                if (ObjectUtil.isNotEmpty(repaymentPlanFee)) {
                    BigDecimal feeRate = e.getFeeVal().divide(BigDecimal.valueOf(100));
                    BigDecimal subedFee = repaymentPlanFee.getPlanNeePayAmount().multiply(feeRate).setScale(2, CommonConstant.NUMBER_STRATEGY);
                    //插入减免列表
                    IncomeDetail incomeDetail = repaymentBizService.addReductionRepaymentPlanFee(repaymentPlanFee, subedFee);
                    repaymentPlanFee.getReductionFeeList().add(incomeDetail);
                }
            }
        }
    }

    /**
     * 检查值并重置
     */
    private void checkLoanDerateAlteration(LoanDerateAlterationDTO loanDerateAlteration) {
        if (!loanDerateAlteration.getDerateType().contains(DerateAlterationEnum.LoanDerateAlterationEnum.LOAN_DERATE_PRINCIPAL.getCode())) {
            loanDerateAlteration.setDeratePrincipal(BigDecimal.ZERO);
        }
        if (!loanDerateAlteration.getDerateType().contains(DerateAlterationEnum.LoanDerateAlterationEnum.LOAN_DERATE_INTEREST.getCode())) {
            loanDerateAlteration.setDerateInterest(BigDecimal.ZERO);
        }
        if (!loanDerateAlteration.getDerateType().contains(DerateAlterationEnum.LoanDerateAlterationEnum.LOAN_DERATE_SERVICE_CHARGE.getCode())) {
            loanDerateAlteration.setRepaymentPlanFeeTotalList(Collections.emptyList());
        }
        if(CollectionUtil.isNotEmpty(loanDerateAlteration.getRepaymentPlanFeeTotalList())){
            //重置分配金额
            loanDerateAlteration.getRepaymentPlanFeeTotalList().forEach(e->{
                e.setAllocationed(BigDecimal.ZERO);
            });
        }
    }
}
