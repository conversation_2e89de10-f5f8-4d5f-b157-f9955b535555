<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.loan.mapper.DerateAlterationHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="derateAlterationHistoryResultMap" type="org.springblade.loan.entity.DerateAlterationHistory">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="finance_no" property="financeNo"/>
        <result column="derate_type" property="derateType"/>
        <result column="reason" property="reason"/>
        <result column="adjunct_proof" property="adjunctProof"/>
        <result column="finance_id" property="financeId"/>
        <result column="derate_principal" property="deratePrincipal"/>
        <result column="derate_interest" property="derateInterest"/>
        <result column="derate_service_charge" property="derateServiceCharge"/>
        <result column="derate_amount_type" property="derateAmountType"/>
        <result column="type" property="type"/>
        <result column="alteration_frequency" property="alterationFrequency"/>
    </resultMap>


    <select id="selectDerateAlterationHistoryPage" resultMap="derateAlterationHistoryResultMap">
        select * from jrzh_derate_alteration_history where is_deleted = 0
    </select>

</mapper>
