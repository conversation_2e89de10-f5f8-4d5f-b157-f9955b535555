package org.springblade.loan.listener;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.enums.ProcessStatusEnum;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.constant.DerateAlterationConstant;
import org.springblade.loan.entity.LoanDerateAlteration;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Component("derateAlterationListener")
@RequiredArgsConstructor
public class LoanDerateAlterationListener implements TaskListener {

    private final IFinanceApplyService financeApplyService;
    private final IBusinessProcessProgressService businessProcessProgressService;

    @Override
    public void notify(DelegateTask delegateTask) {
        String status = delegateTask.getVariable(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, String.class);
        //查询减免申请信息
        Object derateObj = delegateTask.getVariable(DerateAlterationConstant.DERATE_ALTERATION_DELAY);
        LoanDerateAlteration loanDerateAlteration = JSONUtil.toBean(JSONUtil.toJsonStr(derateObj), LoanDerateAlteration.class);

        if (StringUtil.isNotBlank(status) && StringUtil.equals(status, WfProcessConstant.STATUS_REJECT)) {
            businessProcessProgressService.updateStatus(delegateTask.getProcessInstanceId(), ProcessStatusEnum.REJECT.getCode());
            //驳回
            FinanceApply financeApply = financeApplyService.getByFinanceNo(loanDerateAlteration.getFinanceNo());
            financeApplyService.changeStatus(Collections.singletonList(financeApply.getId()), FinanceApplyStatusEnum.DERATE_ALTERATION_REJECT.getCode());
        }
    }
}
