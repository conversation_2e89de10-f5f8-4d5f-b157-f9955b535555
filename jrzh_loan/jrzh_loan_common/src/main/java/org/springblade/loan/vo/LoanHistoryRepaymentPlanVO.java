package org.springblade.loan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.loan.entity.*;
import org.springblade.resource.entity.Attach;
import org.springblade.workflow.core.utils.ObjectUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LoanHistoryRepaymentPlanVO", description = "")
public class LoanHistoryRepaymentPlanVO extends LoanManageRepaymentPlan {

    private static final long serialVersionUID = 1L;
    /**
     * 借款期限、第几期/天数+后缀(期/天)
     */
    @ApiModelProperty(value = "借款期限、第几期/天数+后缀(期/天)")
    private String periodDes;

    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    private Integer type;

    /**
     * 本金 Principal
     */
    private String principalStr;

    /**
     * 利息
     */
    private String interestStr;

    /**
     * 服务费
     */
    private String serviceChargeStr;


    /**
     * 计费方式
     */

    private Integer repaymentMode;

    /**
     * 还款总额
     */
    private BigDecimal monthlySupply;

    /**
     * 利息
     */
    private BigDecimal monthlyInterest;

    /**
     * 本金
     */
    private BigDecimal monthlyPrincipal;

    /**
     * 日利率
     */
    private BigDecimal dailyInterestRate;

    /**
     * 服务费
     */
    private BigDecimal monthlyServiceCharge;

    /**
     * 利率(上升/下降)
     */
    private BigDecimal trendInterestRate;

    /**
     * 应还总额(上升/下降)金额
     */
    private BigDecimal trendMonthlySupply;

    /**
     * 本金(上升/下降)金额
     */
    private BigDecimal trendMonthlyPrincipal;

    /**
     * 利息(上升/下降)金额
     */
    private BigDecimal trendMonthlyInterest;

    /**
     * 服务费(上升/下降)金额
     */
    private BigDecimal trendMonthlyServiceCharge;

    /**
     * 利率趋势类型  1、下降 2、上升
     */
    private Integer InterestRateTrendType;

    /**
     * 应还总额趋势类型  1、下降 2、上升
     */
    private Integer MonthlySupplyTrendType;

    /**
     * 本金趋势类型  1、下降 2、上升
     */
    private Integer MonthlyPrincipalTrendType;

    /**
     * 利息趋势类型  1、下降 2、上升
     */
    private Integer MonthlyInterestTrendType;

    /**
     * 服务费趋势类型  1、下降 2、上升
     */
    private Integer MonthlyServiceChargeTrendType;

    /**
     * 附件url集合
     */
    @ApiModelProperty(value = "附件url集合")
    List<Attach> attachList;

    /**
     * 减免变更信息
     */
    private DerateAlterationHistory derateAlterationHistory;

    /**
     * 调息变更信息
     */
    private AdjustInterest adjustInterest;

    /**
     * 逾期协商变更信息
     */
    private LoanOverdueConsultAlteration loanOverdueConsultAlteration;

    /**
     * 展期变更信息
     */
    private DelayAlterationHistoryVO loanDelayAlterationVO;

    /**
     * 当期动态费用
     *
     * @return
     */
    private List<RepaymentPlanFee> repaymentPlanFeeList;

    /**
     * 临时处理 对同种动态费用做统计，方便前端计算
     */
    private List<AlterResult> planfeeMap;

    public Integer getType() {
        Integer overdue = getOverdue();
        Integer repaymentStatus = getRepaymentStatus();
        int compare = getRepaymentTime().compareTo(LocalDate.now());
        // 未逾期
        if (overdue.equals(RepaymentConstant.RepaymentPlanOverdueEnum.NORMAL.getCode())
                && repaymentStatus.equals(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
        ) {
            // 还款日期等于今天，就是当前还款
            if (compare == 0) {
                return RepaymentConstant.RepaymentPlanStatusEnum.CURRENT.getCode();
            } else {
                // 待还款
                return RepaymentConstant.RepaymentPlanStatusEnum.UN_PAY.getCode();
            }
        }
        // 已逾期
        if (overdue.equals(RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode())) {
            // 如果还款计划还在使用中的话，就是逾期未还，如果已结清的话就是逾期还款
            if (repaymentStatus.equals(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())) {
                return RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode();
            } else {
                return RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE.getCode();
            }
        }
        return 0;
    }

    public BigDecimal getTotalAmount() {
        return getPrincipal().add(getPlanInterest()).add(getPenaltyInterest()).add(getTotalRepaymentFee());
    }

    public BigDecimal getTotalRepaymentFee() {
        if (ObjectUtil.isEmpty(this.getRepaymentPlanFeeList())) {
            return BigDecimal.ZERO;
        }
        return this.getRepaymentPlanFeeList().stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public String getPeriodDes() {
        return this.getPeriod().toString() + "期";
    }

}
