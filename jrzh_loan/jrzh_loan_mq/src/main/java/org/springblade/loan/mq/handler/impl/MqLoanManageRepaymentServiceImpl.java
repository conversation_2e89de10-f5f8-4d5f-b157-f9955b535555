package org.springblade.loan.mq.handler.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.mq.handler.MqLoanManageRepaymentService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
@AllArgsConstructor
public class MqLoanManageRepaymentServiceImpl implements MqLoanManageRepaymentService {

    private final ILoanManageRepaymentService loanManageRepaymentService;

    /**
     * 根据查询条件获取 还款列表集合数据
     *
     * @param queryWrapper 查询条件
     * @return 还款列表集合数据
     */
    @Override
    public List<LoanManageRepayment> list(Wrapper<LoanManageRepayment> queryWrapper) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }
        return null;
    }

    /**
     * 更新还款列表数据
     *
     * @param repayment 还款列表
     * @return true成功
     */
    @Override
    public Boolean updateById(LoanManageRepayment repayment) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }
        return null;
    }

    /**
     * 更新还款列表数据
     *
     * @param updateWrapper 条件
     * @return true成功
     */
    @Override
    public Boolean update(LambdaUpdateWrapper<LoanManageRepayment> updateWrapper) {
        return loanManageRepaymentService.update(updateWrapper);
    }

    /**
     * 线上还款-修改支付状态
     *
     * @param onlinePayRepaymentCode 线上还款编号
     * @param status                 状态
     * @param actualAmount           金额
     */
    @Override
    public boolean statusOnlinePayRepaymentUpdate(String onlinePayRepaymentCode, Integer status, BigDecimal actualAmount) {
        return false;
    }
}
