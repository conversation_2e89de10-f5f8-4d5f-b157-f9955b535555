package org.springblade.loan.listener;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.feign.RemoteEnterpriseQuotaService;
import org.springblade.expense.handler.ProductExpenseOrderDetailUtils;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.loan.dto.RepaymentPlanDTO;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageOverdue;
import org.springblade.loan.entity.LoanManageOverdueConsult;
import org.springblade.loan.enums.LoanAlterationEnum;
import org.springblade.loan.service.*;
import org.springblade.loan.utils.LoanUtils;
import org.springblade.loan.vo.LoanOverdueConsultAlterationVO;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;


@Component("overDueConsultApplyListener")
@RequiredArgsConstructor
public class OverDueConsultApplyListener implements ExecutionListener {
    private final IFinanceApplyService financeApplyService;
    private final ILoanManageOverdueConsultService loanManageOverdueConsultService;
    private final ILoanManageIouService loanManageIouService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final ILoanManageOverdueService loanManageOverdueService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final RemoteEnterpriseQuotaService enterpriseQuotaService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    private final ILoanAlterationHistoryService loanAlterationHistoryService;
    private final ILoanOverdueConsultAlterationService loanOverdueConsultAlterationService;
    private final IRepaymentPlanJsonService repaymentPlanJsonService;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        Long overdueConsultId = delegateExecution.getVariable(ProcessConstant.OVERDUE_CONSULT_ID, Long.class);
        LoanManageOverdueConsult overdueConsult = loanManageOverdueConsultService.getById(overdueConsultId);
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            handlerProcessTerminal(delegateExecution, overdueConsult);
        } else {
            handlerSuccess(delegateExecution, overdueConsult);
        }
    }

    private void handlerProcessTerminal(DelegateExecution delegateExecution, LoanManageOverdueConsult overdueConsult) {
        Long financeApplyId = delegateExecution.getVariable(ProcessConstant.FINANCE_APPLY_ID, Long.class);
        String processInstanceId = delegateExecution.getVariable(ProcessConstant.PROCESS_INSTANCE_ID, String.class);
        //更新流程进度
        businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(financeApplyId, null,
                ProcessTypeEnum.RECEIVE_OVERDUE_CONSULT_APPLY.getCode(), processInstanceId, overdueConsult.getUserId(), ProcessStatusEnum.INVALID.getCode());
        //更新融资状态为逾期未结清
        financeApplyService.changeStatus(Collections.singletonList(financeApplyId), FinanceApplyStatusEnum.OVERDUE_UN_SETTLED.getCode());
        //还原逾期列表状态
        String originOverDueListJson = delegateExecution.getVariable(ProcessConstant.ORIGIN_OVERDUE_LIST, String.class);
        if (StringUtil.isNotBlank(originOverDueListJson)) {
            List<LoanManageOverdue> overdueList = JSONUtil.toList(originOverDueListJson, LoanManageOverdue.class);
            loanManageOverdueService.updateBatchById(overdueList);
        }
        //关闭逾期协商
        loanManageOverdueConsultService.update(Wrappers.<LoanManageOverdueConsult>lambdaUpdate().eq(LoanManageOverdueConsult::getId, overdueConsult.getId())
                .set(LoanManageOverdueConsult::getStatus, CommonConstant.CLOSESTATUS));
    }

    private void handlerSuccess(DelegateExecution delegateExecution, LoanManageOverdueConsult overdueConsult) {
        Long financeApplyId = delegateExecution.getVariable(ProcessConstant.FINANCE_APPLY_ID, Long.class);
        Long enterpriseQuotaId = delegateExecution.getVariable(ProcessConstant.ENTERPRISE_QUOTA_Id, Long.class);
        String processInstanceId = delegateExecution.getProcessInstanceId();
        Long iouId = delegateExecution.getVariable(ProcessConstant.IOU_ID, Long.class);
        LoanManageIou oldLoanManageIou = loanManageIouService.getById(iouId);
        FinanceApply financeApply = financeApplyService.getById(financeApplyId);
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(enterpriseQuotaId).getData();
        //作废旧的融资订单
        financeApplyService.changeStatus(Collections.singletonList(financeApplyId), FinanceApplyStatusEnum.INVALID.getCode());
        //关闭借据单 还款计划等
        repaymentPlanFinanceApplyBizService.closeIouByFinanceApplyNo(financeApply.getFinanceNo());
        //生成新的融资编号 生成新的还款计划
        // 构建融资申请对象
        BigDecimal interestRate = overdueConsult.getInterestRate();
        //设置旧融资更新为新融资
        financeApply.setOldFinanceId(financeApplyId);
        financeApply.setOldFinanceNo(financeApply.getFinanceNo());
        //设置新融资申请
        financeApply.setDailyInterestRate(LoanUtils.getDailyInterestRate(interestRate,2));
        financeApply.setAnnualInterestRate(interestRate);
        financeApply.setServiceRate(financeApply.getServiceRate());
        financeApply.setUserId(overdueConsult.getUserId());
        financeApply.setLoadTermUnit(overdueConsult.getLoanTermUnit());
        financeApply.setLoadTerm(overdueConsult.getTotalTerm());
        financeApply.setRepaymentType(overdueConsult.getRepaymentType());
        financeApply.setRepaymentMode(overdueConsult.getRepaymentMode());
        financeApply.setFinanceNo(CodeUtil.generateCode(CodeEnum.FINANCING_CODE));
        financeApply.setStatus(FinanceApplyStatusEnum.UN_SETTLED.getCode());
        financeApply.setConsultStatus(CommonConstant.CLOSESTATUS);
        financeApply.setRepaymentCalType(PlatformExpensesEnum.PLAT_TYPE_OVERDUE_CONSULT.getCode());
        financeApply.setId(null);
        financeApplyService.save(financeApply);
        CostCalculusVO costCalculusVO = JSONUtil.toBean(repaymentPlanJsonService.getPlanJson(financeApply.getOldFinanceNo(), PlatformExpensesEnum.PLAT_TYPE_OVERDUE_CONSULT.getCode()), CostCalculusVO.class);
        //保存新的还款计划
        RepaymentPlanDTO repaymentPlanDTO = repaymentPlanFinanceApplyBizService.saveRepaymentPlanByFinanceApply(financeApply, costCalculusVO.getShowRepaymentPlan(), false);
        //保存新的还款
        repaymentPlanFinanceApplyBizService.saveCalJson(costCalculusVO, PlatformExpensesEnum.PLAT_TYPE_OVERDUE_CONSULT.getCode(), financeApply.getFinanceNo());

        //更新流程进度
        businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(financeApplyId, null, ProcessTypeEnum.RECEIVE_OVERDUE_CONSULT_APPLY.getCode(), processInstanceId, overdueConsult.getUserId(), ProcessStatusEnum.FINISH.getCode());
        //更新协商信息
        loanManageOverdueConsultService.update(Wrappers.<LoanManageOverdueConsult>lambdaUpdate().eq(LoanManageOverdueConsult::getId, overdueConsult.getId())
                .set(LoanManageOverdueConsult::getStatus, CommonConstant.CLOSESTATUS)
                .set(LoanManageOverdueConsult::getNewFinanceNo, financeApply.getFinanceNo()));
        //保存变更记录
        overdueConsult.setNewFinanceNo(financeApply.getFinanceNo());
        saveOverdueConsultAlteration(overdueConsult, repaymentPlanDTO, oldLoanManageIou.getId());
    }

    private void saveOverdueConsultAlteration(LoanManageOverdueConsult loanManageOverdueConsult, RepaymentPlanDTO repaymentPlanDTO, Long beforeIouId) {
        //变更记录 获取最新变更次数
        Integer latestLogId = loanAlterationHistoryService.saveAlterationLog(beforeIouId, repaymentPlanDTO.getLoanManageRepaymentPlanList(), LoanAlterationEnum.lOAN_OVERDUE_CONSULT_CHANGE.getCode(), loanManageOverdueConsult.getReason());
        //逾期变更记录
        User user = UserUtils.getUserById(loanManageOverdueConsult.getUserId());
        LoanOverdueConsultAlterationVO loanOverdueConsultAlterationVO = new LoanOverdueConsultAlterationVO();
        BeanUtil.copyProperties(loanManageOverdueConsult, loanOverdueConsultAlterationVO);
        loanOverdueConsultAlterationVO.setAlterationFrequency(latestLogId);
        loanOverdueConsultAlterationVO.setFinanceUser(user == null ? "" : user.getName());
        loanOverdueConsultAlterationService.save(loanOverdueConsultAlterationVO);
    }
}
