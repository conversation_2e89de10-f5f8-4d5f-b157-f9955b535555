/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.finance.vo.ExpenseOrderDetailFinanceVo;
import org.springblade.loan.dto.*;
import org.springblade.loan.entity.LoanManageOverdueConsult;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.loan.feign.RemoteRepaymentBiz;
import org.springblade.loan.feign.RemoteRepaymentPlanFinanceApplyBiz;
import org.springblade.loan.service.*;
import org.springblade.loan.vo.LoanManageOverdueConsultDetailsVO;
import org.springblade.loan.vo.OverdueConsultAlterationInfoVO;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.system.utils.UserUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 逾期协商订单 控制器
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_OVERDUE_CONSULT + CommonConstant.WEB_BACK + "/loan/loanManageOverdueConsult")
@Api(value = "逾期协商订单", tags = "逾期协商订单接口")
public class LoanManageOverdueConsultController extends BladeController {

    private final ILoanManageOverdueConsultService loanManageOverdueConsultService;
    private final RemoteRepaymentPlanFinanceApplyBiz remoteRepaymentPlanFinanceApplyBiz;
    private final IFinanceApplyService financeApplyService;
    private final IRepaymentPlanJsonService repaymentPlanJsonService;
    private final ILoanManageIouService loanManageIouService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    private final RemoteRepaymentBiz remoteRepaymentBiz;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;
    private final IRepaymentPlanFinanceApplyCommonService repaymentPlanFinanceApplyCommonService;

    @GetMapping("/repaymentCalculation")
    @ApiOperation("还款试算")
    public R<RepaymentPlanCal> repaymentCalculation(@RequestParam Long overdueConsultId) {
        return R.data(loanManageOverdueConsultService.repaymentCalculation(overdueConsultId));
    }

    @GetMapping("/platList")
    @ApiOperation("平台费用 显示融资申请阶段未还的平台费")
    public R<List<ExpenseOrderDetail>> platList(@RequestParam Long overdueConsultId) {
        return R.data(loanManageOverdueConsultService.expenseOverDueList(overdueConsultId));
    }

    /**
     * 发起逾期协商变更申请流程
     */
    @PostMapping("/overdue_consult_save")
    @ApiOperation(value = "发起逾期协商变更申请", notes = "传入loanManageOverdueConsult")
    public R overdueConsultSave(@Valid @RequestBody OverdueAlterationApplyDTO overdueAlterationApplyDTO) {
        //开启逾期协商流程
        OverdueContractApplyOpenDTO overdueContractApplyOpenDTO = new OverdueContractApplyOpenDTO();
        overdueContractApplyOpenDTO.setFinanceId(overdueAlterationApplyDTO.getFinanceId());
        BusinessProcessProgress businessProcessProgress = loanManageOverdueConsultService.openOverdueConsult(overdueContractApplyOpenDTO);
        LoanManageOverdueConsultDetailsVO overdueConsultDetail = loanManageOverdueConsultService.getOverdueConsultDetail(overdueAlterationApplyDTO.getFinanceId());
        //保存逾期协商流程
        ReceiveOverdueConsultApplyDTO receiveOverdueConsultApply = new ReceiveOverdueConsultApplyDTO();
        receiveOverdueConsultApply.setUserId(AuthUtil.getUserId());
        LoanManageOverdueConsult loanManageOverdueConsult = overdueAlterationApplyDTO.getLoanManageOverdueConsult();
        loanManageOverdueConsult.setAmount(overdueConsultDetail.getSurplusPrincipal());
        loanManageOverdueConsult.setReason(overdueAlterationApplyDTO.getReason());
        loanManageOverdueConsult.setAdjunctProof(overdueAlterationApplyDTO.getAdjunctProof());
        receiveOverdueConsultApply.setLoanManageOverdueConsult(loanManageOverdueConsult);
        receiveOverdueConsultApply.setFinanceId(overdueAlterationApplyDTO.getFinanceId());
        receiveOverdueConsultApply.setCustomerGoodsId(overdueAlterationApplyDTO.getCustomerGoodsId());
        receiveOverdueConsultApply.setBusinessId(businessProcessProgress.getBusinessId());
        receiveOverdueConsultApply.setEnterpriseType(overdueAlterationApplyDTO.getEnterpriseType());
        receiveOverdueConsultApply.setGoodsId(overdueAlterationApplyDTO.getGoodsId());
        receiveOverdueConsultApply.setGoodsType(overdueAlterationApplyDTO.getGoodsType());
        receiveOverdueConsultApply.setProcessType(overdueAlterationApplyDTO.getProcessType());
        receiveOverdueConsultApply.setSponsorType(overdueAlterationApplyDTO.getSponsorType());
        loanManageOverdueConsultService.saveLoanManageOverdueConsult(receiveOverdueConsultApply);
        //提交逾期协商审批
        loanManageOverdueConsultService.commitOverdueConsultApply(receiveOverdueConsultApply);
        return R.status(true);
    }

    /**
     * 逾期协商变更申请 详情
     */
    @GetMapping("/detail")
    public R<OverdueConsultAlterationInfoVO> detail(String financeNo) {
        return R.data(loanManageOverdueConsultService.getOverdueConsultAlterationDetail(financeNo));
    }

    @PostMapping("/consult")
    @ApiOperation("协商还款试算")
    public R<CostCalculusVO> consult(@RequestBody OverDueCostCalculusDto costCalculusDto) {
        costCalculusDto.setUserId(AuthUtil.getUserId());
        costCalculusDto.setEnterpriseType(UserUtils.getEnterpriseType());
        return R.data(loanManageOverdueConsultService.consult(costCalculusDto));
    }
//	@PostMapping("/consult")
//	@ApiOperation("协商还款试算")
//	public R<CostCalculusVO> consult(@RequestBody CostCalculusDto costCalculusDto) {
//		FinanceApply financeApply = financeApplyService.getByFinanceNo(costCalculusDto.getFinanceNo());
//		LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financeApply.getFinanceNo());
//		//获取当前的借款情况、还款情况和待还的费用
//		LoanInfoDTO loanInfoDTO = remoteRepaymentBiz.getLoanInfoDTO(loanManageIou.getIouNo()).getData();
//		//设置逾期后年利率
//		loanInfoDTO.setAnnualInterestRate(costCalculusDto.getAnnualInterestRate());
//		loanManageIou.setFirstRepaymentDate(costCalculusDto.getStartTime());
//		Map<Long, BigDecimal> repaymentPlanFeeList = costCalculusDto.getRepaymentPlanFeeList();
//		costCalculusDto.setRefundType(2);
////		//动态费用设置固定值
//		List<RepaymentPlanFee> planFeeList = loanInfoDTO.listSubRepaymentPlanFee();
//			planFeeList.stream().forEach(e -> {
//               if (costCalculusDto.getRefundType()==CommonConstant.YES) {
//				   Integer totalTerm = costCalculusDto.getTotalTerm();
//				   BigDecimal term = new BigDecimal(totalTerm);
//				   repaymentPlanFeeList.forEach((key,value)->{
//					   if (key.equals(e.getId())) {
//						   BigDecimal amount = value;
//						   BigDecimal fixed=amount.max(term);
//						   e.setAmount(fixed);
//						   e.setFixAmount(fixed);
//						   e.setFix(CommonConstant.YES);
//					   }
//				   });
//			   }else {
//				   repaymentPlanFeeList.forEach((key,value)->{
//					   if (key.equals(e.getId())) {
//						   e.setAmount(value);
//						   e.setFixAmount(value);
//						   e.setFix(CommonConstant.YES);
//					   }
//				   });
//			   }
//			});
//
//		loanInfoDTO.setRepaymentPlanFee(planFeeList);
//		CostCalculusVO costCalculusVO = repaymentPlanFinanceApplyBizService.transferByLoan(loanInfoDTO);
////		repaymentPlanJsonService.saveOrUpdatePlanJson(costCalculusDto.getFinanceNo(), PlatformExpensesEnum.PLAT_TYPE_OVERDUE_CONSULT.getCode(),JSONUtil.toJsonStr(costCalculusVO));
//		return R.data(costCalculusVO);
//	}

    /**
     * 查看协商信息
     */
    @GetMapping("/details")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "查看协商信息", notes = "传入financeId")
    public R details(@RequestParam Long financeId) {
        return R.data(loanManageOverdueConsultService.getDetails(financeId));
    }


}
