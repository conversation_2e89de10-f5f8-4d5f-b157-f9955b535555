package org.springblade.loan.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.loan.entity.LoanManageOverdueConsult;
import org.springblade.loan.vo.LoanManageOverdueConsultVO;

import java.util.Objects;

/**
 * 逾期协商订单包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
public class LoanManageOverdueConsultWrapper extends BaseEntityWrapper<LoanManageOverdueConsult, LoanManageOverdueConsultVO> {

	public static LoanManageOverdueConsultWrapper build() {
		return new LoanManageOverdueConsultWrapper();
	}

	@Override
	public LoanManageOverdueConsultVO entityVO(LoanManageOverdueConsult LoanManageOverdueConsult) {
		LoanManageOverdueConsultVO LoanManageOverdueConsultVO = Objects.requireNonNull(BeanUtil.copy(LoanManageOverdueConsult, LoanManageOverdueConsultVO.class));

		//User createUser = UserCache.getUser(LoanManageOverdueConsult.getCreateUser());
		//User updateUser = UserCache.getUser(LoanManageOverdueConsult.getUpdateUser());
		//LoanManageOverdueConsultVO.setCreateUserName(createUser.getName());
		//LoanManageOverdueConsultVO.setUpdateUserName(updateUser.getName());

		return LoanManageOverdueConsultVO;
	}
}