<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.loan.mapper.LoanOverdueConsultAlterationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="loanOverdueConsultAlterationResultMap" type="org.springblade.loan.entity.LoanOverdueConsultAlteration">
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
        <result column="goods_id" property="goodsId"/>
        <result column="finance_no" property="financeNo"/>
        <result column="finance_id" property="financeId"/>
        <result column="iou_no" property="iouNo"/>
        <result column="user_id" property="userId"/>
        <result column="customer_goods_id" property="customerGoodsId"/>
        <result column="amount" property="amount"/>
        <result column="repayment_type" property="repaymentType"/>
        <result column="repayment_mode" property="repaymentMode"/>
        <result column="interest_rate" property="interestRate"/>
        <result column="refund_start_time" property="refundStartTime"/>
        <result column="penalty_interest" property="penaltyInterest"/>
        <result column="break_out_amount" property="breakOutAmount"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="new_finance_no" property="newFinanceNo"/>
        <result column="loan_term_unit" property="loanTermUnit"/>
        <result column="total_term" property="totalTerm"/>
        <result column="alteration_frequency" property="alterationFrequency"/>
    </resultMap>


    <select id="selectLoanOverdueConsultAlterationPage" resultMap="loanOverdueConsultAlterationResultMap">
        select * from jrzh_loan_overdue_consult_alteration where is_deleted = 0
    </select>

</mapper>
