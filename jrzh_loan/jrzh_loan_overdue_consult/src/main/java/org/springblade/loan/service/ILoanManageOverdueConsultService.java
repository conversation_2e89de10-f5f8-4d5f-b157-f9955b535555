/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.loan.dto.*;
import org.springblade.loan.entity.LoanManageOverdueConsult;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.vo.*;
import org.springblade.process.entity.BusinessProcessProgress;

import java.util.List;

/**
 * 逾期协商订单 服务类
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
public interface ILoanManageOverdueConsultService extends BaseService<LoanManageOverdueConsult> {

    /**
     * 自定义分页
     *
     * @param page
     * @param loanManageOverdueConsult
     * @return
     */
    IPage<LoanManageOverdueConsultVO> selectLoanManageOverdueConsultPage(IPage<LoanManageOverdueConsultVO> page, LoanManageOverdueConsultVO loanManageOverdueConsult);

    /**
     * 开启逾期协商流程
     *
     * @param overdueContractApplyOpenDTO
     * @return
     */
    BusinessProcessProgress openOverdueConsult(OverdueContractApplyOpenDTO overdueContractApplyOpenDTO);

    /**
     * 查询逾期协商信息
     *
     * @param financeId
     * @return
     */
    LoanManageOverdueConsultDetailsVO getOverdueConsultDetail(Long financeId);

    /**
     * 统计所有未还金额
     *
     * @param plans
     * @return
     */
    AddAmountVO getAllUnPayRepayment(List<LoanManageRepaymentPlan> plans);

    /**
     * 获取正在申请的订单
     *
     * @param financeId
     * @return
     */
    LoanManageOverdueConsult getRunningOrder(Long financeId);

    /**
     * 获取正在申请的订单
     *
     * @param financeId
     * @param progressId
     * @return
     */
    LoanManageOverdueConsult getRunningOrder(Long financeId, Long progressId);

    /**
     * 保存修改逾期协商信息 并更新协商进度
     *
     * @param receiveOverdueConsultApply
     * @return
     */
    LoanManageOverdueConsult saveLoanManageOverdueConsult(ReceiveOverdueConsultApplyDTO receiveOverdueConsultApply);

    /**
     * 提交逾期协商审批
     *
     * @param receiveOverdueConsultApply
     */
    void commitOverdueConsultApply(ReceiveOverdueConsultApplyDTO receiveOverdueConsultApply);

    /**
     * 还款试算
     *
     * @param overdueConsultId
     * @return
     */
    RepaymentPlanCal repaymentCalculation(Long overdueConsultId);

    /**
     * 逾期协商变更 详情
     *
     * @param financeNo 融资编号
     * @return OverdueConsultAlterationInfoVO
     */
    OverdueConsultAlterationInfoVO getOverdueConsultAlterationDetail(String financeNo);

    /**
     * 查询逾期协商详情
     *
     * @param financeId 融资id
     * @return LoanManageOverdueConsultVO
     */
    LoanManageOverdueConsultVO getDetails(Long financeId);

    /**
     * 计算逾期协商平台费用
     * 1、计算规则 资方统一清分并且费用订单处于待情分状态
     *
     * @param overdueConsultId
     * @return
     */
    List<ExpenseOrderDetail> expenseOverDueList(Long overdueConsultId);

    /**
     * 逾期试算
     *
     * @param costCalculusDto
     * @return
     */
    CostCalculusVO consult(OverDueCostCalculusDto costCalculusDto);
}
