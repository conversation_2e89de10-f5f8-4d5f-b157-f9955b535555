/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.loan.entity.LoanOverdueConsultAlteration;
import org.springblade.loan.vo.LoanOverdueConsultAlterationVO;

/**
 * 逾期协商变更 服务类
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
public interface ILoanOverdueConsultAlterationService extends BaseService<LoanOverdueConsultAlteration> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param loanOverdueConsultAlteration
	 * @return
	 */
	IPage<LoanOverdueConsultAlterationVO> selectLoanOverdueConsultAlterationPage(IPage<LoanOverdueConsultAlterationVO> page, LoanOverdueConsultAlterationVO loanOverdueConsultAlteration);

	/**
	 * 分页   逾期协商变更列表
	 * @param loanOverdueConsultAlteration 查询参数
	 * @param query 分页参数
	 * @return IPage<LoanOverdueConsultAlterationVO>
	 */
	IPage<LoanOverdueConsultAlterationVO> selectOverdueConsultAlterationList(LoanOverdueConsultAlteration loanOverdueConsultAlteration, Query query);
}
