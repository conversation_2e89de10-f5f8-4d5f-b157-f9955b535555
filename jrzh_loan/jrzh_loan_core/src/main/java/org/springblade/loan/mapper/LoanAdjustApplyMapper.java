/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.loan.entity.LoanAdjustApply;
import org.springblade.loan.vo.LoanAdjustApplyVO;

import java.util.List;

/**
 * 调息申请信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-12-08
 */
public interface LoanAdjustApplyMapper extends BaseMapper<LoanAdjustApply> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param loanAdjustApply
	 * @return
	 */
	List<LoanAdjustApplyVO> selectLoanAdjustApplyPage(IPage page,@Param("loanAdjustApply") LoanAdjustApplyVO loanAdjustApply);

}
