/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentTerm;
import org.springblade.loan.entity.RepaymentFee;
import org.springblade.loan.mapper.RepaymentFeeMapper;
import org.springblade.loan.service.ILoanManageRepaymentTermService;
import org.springblade.loan.service.IRepaymentFeeService;
import org.springblade.loan.vo.LoanManageRepaymentVO;
import org.springblade.loan.vo.RepaymentFeeVO;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.loan.wrapper.RepaymentFeeWrapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 还款费用表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Service
@AllArgsConstructor
public class RepaymentFeeServiceImpl extends BaseServiceImpl<RepaymentFeeMapper, RepaymentFee> implements IRepaymentFeeService {

    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;

    @Override
    public IPage<RepaymentFeeVO> selectRepaymentFeePage(IPage<RepaymentFeeVO> page, RepaymentFeeVO repaymentFee) {
        return page.setRecords(baseMapper.selectRepaymentFeePage(page, repaymentFee));
    }

    @Override
    public List<RepaymentFee> getRepaymentFeeByRepaymentIds(List<Long> repaymentIds) {
        if (CollUtil.isEmpty(repaymentIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<RepaymentFee>lambdaQuery()
                .in(RepaymentFee::getRepaymentId, repaymentIds));
    }

    @Override
    public List<RepaymentFee> getUnRepaymentFeeByRepaymentIds(List<Long> repaymentIds) {
        if (CollUtil.isEmpty(repaymentIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<RepaymentFee>lambdaQuery()
                .in(RepaymentFee::getRepaymentId, repaymentIds)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAYING.getCode()));
    }


    @Override
    public List<LoanManageRepaymentVO> getPayRepaymentFeeByRepayments(List<LoanManageRepayment> repaymentList) {
        if (CollUtil.isEmpty(repaymentList)) {
            return Lists.newArrayList();
        }
        List<LoanManageRepaymentVO> loanManageRepaymentVOS = LoanManageRepaymentWrapper.build().listVO(repaymentList);
        for (LoanManageRepaymentVO loanManageRepaymentVO : loanManageRepaymentVOS) {
            List<RepaymentFee> repaymentFeeList = getPayRepaymentFeeList(loanManageRepaymentVO.getId());
            loanManageRepaymentVO.setRepaymentFeeList(RepaymentFeeWrapper.build().listVO(repaymentFeeList));
        }
        return loanManageRepaymentVOS;
    }

    @Override
    public List<RepaymentFee> getPayRepaymentFeeList(Long repaymentId) {
        return baseMapper.selectList(Wrappers.<RepaymentFee>lambdaQuery()
                .eq(RepaymentFee::getRepaymentId, repaymentId)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode()));
    }

    @Override
    public List<RepaymentFee> getPayRepaymentFeeListByRepaymentIs(List<Long> repaymentIds) {
        if (CollUtil.isEmpty(repaymentIds)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<RepaymentFee>lambdaQuery()
                .in(RepaymentFee::getRepaymentId, repaymentIds)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode()));
    }

    @Override
    public BigDecimal getPayRepaymentFeeAmount(List<Long> planFeeIds) {
        if (CollUtil.isEmpty(planFeeIds)) {
            return BigDecimal.ZERO;
        }
        List<LoanManageRepaymentTerm> termList = loanManageRepaymentTermService.list(Wrappers.<LoanManageRepaymentTerm>lambdaQuery()
                .in(LoanManageRepaymentTerm::getRepaymentPlanId, planFeeIds));
        if (CollUtil.isEmpty(termList)) {
            return BigDecimal.ZERO;
        }
        List<Long> repaymentFeeId = termList.stream().map(LoanManageRepaymentTerm::getRepaymentId).collect(Collectors.toList());
        List<RepaymentFee> feeList = baseMapper.selectList(Wrappers.<RepaymentFee>lambdaQuery()
                .in(RepaymentFee::getId, repaymentFeeId)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode()));
        return feeList.stream().map(RepaymentFee::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<RepaymentFee> listByRepaymentIds(List<Long> repaymentIds) {
        return list(Wrappers.<RepaymentFee>lambdaQuery().in(RepaymentFee::getRepaymentId, repaymentIds));
    }

    @Override
    public void changeStatusByRepaymentId(Long repaymentId, Integer status) {
        update(Wrappers.<RepaymentFee>lambdaUpdate().eq(RepaymentFee::getRepaymentId, repaymentId).set(RepaymentFee::getStatus, status));
    }


}
