package org.springblade.loan.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.loan.entity.AdjustInterest;
import org.springblade.loan.vo.AdjustInterestVO;

import java.util.Objects;

/**
 * 调息变更包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
public class AdjustInterestWrapper  extends BaseEntityWrapper<AdjustInterest, AdjustInterestVO> {

	public static AdjustInterestWrapper build() {
		return new AdjustInterestWrapper();
	}

	@Override
	public AdjustInterestVO entityVO(AdjustInterest AdjustInterest) {
		AdjustInterestVO AdjustInterestVO = Objects.requireNonNull(BeanUtil.copy(AdjustInterest, AdjustInterestVO.class));

		//User createUser = UserCache.getUser(AdjustInterest.getCreateUser());
		//User updateUser = UserCache.getUser(AdjustInterest.getUpdateUser());
		//AdjustInterestVO.setCreateUserName(createUser.getName());
		//AdjustInterestVO.setUpdateUserName(updateUser.getName());

		return AdjustInterestVO;
	}
}
