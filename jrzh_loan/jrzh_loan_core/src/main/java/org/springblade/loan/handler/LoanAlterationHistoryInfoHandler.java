package org.springblade.loan.handler;

import org.springblade.loan.enums.LoanAlterationEnum;
import org.springblade.loan.vo.LoanHistoryRepaymentPlanVO;

import java.util.List;

public interface LoanAlterationHistoryInfoHandler {
    /**
     * 变更类型
     *
     * @return
     */
    LoanAlterationEnum support();

    /**
     * 填充变更信息
     *
     * @param loanHistoryRepaymentPlanVOList 需要填充的变更信息
     * @param financeIds                     本次用到的融资ids 包含变更的融资ids
     */
    void fillInfo(List<LoanHistoryRepaymentPlanVO> loanHistoryRepaymentPlanVOList, List<Long> financeIds);
}
