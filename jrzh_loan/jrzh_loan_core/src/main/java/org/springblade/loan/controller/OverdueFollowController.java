/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.loan.entity.OverdueFollow;
import org.springblade.loan.service.IOverdueFollowService;
import org.springblade.loan.vo.OverdueFollowVO;
import org.springblade.loan.wrapper.OverdueFollowWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_FOLLOW + CommonConstant.WEB_BACK + "/LoanManageFollow/overdueFollow")
@Api(value = "跟进信息", tags = "跟进信息接口")
public class OverdueFollowController extends BladeController {

	private final IOverdueFollowService overdueFollowService;




	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)

	@ApiOperation(value = "详情", notes = "传入overdueId")
    @PreAuth("hasPermission('LoanManageFollow:overdueFollow:detail') or hasRole('administrator')")
	public R<List<OverdueFollowVO>> detail(OverdueFollow overdueFollow) {
		Long overdueId = overdueFollow.getOverdueId();
		List<OverdueFollow> list = overdueFollowService.list(Wrappers.<OverdueFollow>lambdaQuery().eq(OverdueFollow::getOverdueId,overdueId));
		return R.data(OverdueFollowWrapper.build().listVO(list));
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入overdueFollow")
    @PreAuth("hasPermission('LoanManageFollow:overdueFollow:list') or hasRole('administrator')")
	public R<IPage<OverdueFollowVO>> list(OverdueFollow overdueFollow, Query query) {
		IPage<OverdueFollow> pages = overdueFollowService.page(Condition.getPage(query), Condition.getQueryWrapper(overdueFollow));
		return R.data(OverdueFollowWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入overdueFollow")
    @PreAuth("hasPermission('LoanManageFollow:overdueFollow:page') or hasRole('administrator')")
	public R<IPage<OverdueFollowVO>> page(OverdueFollowVO overdueFollow, Query query) {
		IPage<OverdueFollowVO> pages = overdueFollowService.selectOverdueFollowPage(Condition.getPage(query), overdueFollow);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入overdueId")
    //@PreAuth("hasPermission('LoanManageFollow:overdueFollow:save') or hasRole('administrator')")
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> save(@Valid @RequestBody OverdueFollow overdueFollow) {
		return R.status(overdueFollowService.saveOverdueFollowInfo(overdueFollow));
	}


	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入overdueFollow")
    @PreAuth("hasPermission('LoanManageFollow:overdueFollow:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody OverdueFollow overdueFollow) {
		return R.status(overdueFollowService.updateById(overdueFollow));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入overdueFollow")
    @PreAuth("hasPermission('LoanManageFollow:overdueFollow:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody OverdueFollow overdueFollow) {
		return R.status(overdueFollowService.saveOrUpdate(overdueFollow));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('LoanManageFollow:overdueFollow:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(overdueFollowService.deleteLogic(Func.toLongList(ids)));
	}


}
