<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.loan.mapper.AdvanceSettledApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="advanceSettledApplyResultMap" type="org.springblade.loan.entity.AdvanceSettledApply">
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="finance_no" property="financeNo"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="surplus_Principal" property="surplusPrincipal"/>
        <result column="prepayment_service_Fee" property="prepaymentServiceFee"/>
        <result column="interest" property="interest"/>
        <result column="prepayment_day" property="prepaymentDay"/>
        <result column="pass_time" property="passTime"/>
        <result column="reason" property="reason"/>
        <result column="process_instance_id" property="processInstanceId"/>
    </resultMap>


    <select id="selectAdvanceSettledApplyPage" resultMap="advanceSettledApplyResultMap">
        select * from jrzh_advance_settled_apply where is_deleted = 0
    </select>

</mapper>
