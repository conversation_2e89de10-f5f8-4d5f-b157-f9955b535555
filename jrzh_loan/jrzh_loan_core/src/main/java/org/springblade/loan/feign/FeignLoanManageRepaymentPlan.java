package org.springblade.loan.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.core.tool.api.R;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13
 * @description
 */
@RestController
@RequiredArgsConstructor
public class FeignLoanManageRepaymentPlan implements RemoteLoanManageRepaymentPlan{
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    @Override
    @PostMapping(CommonConstant.BLADE_FEIGN + CommonConstant.BLADE_FINANCE + "loanManageRepaymentPlan/getListInfo")
    public R<List<LoanManageRepaymentPlan>> getListInfo(@RequestParam(value = "financeIds")List<Long> financeIds) {
        return R.data(loanManageRepaymentPlanService.listUnPayLoanManageRepaymentPlans(financeIds));
    }

    @Override
    @GetMapping(CommonConstant.BLADE_FEIGN + CommonConstant.BLADE_FINANCE + "loanManageRepaymentPlan/listByIouNo")
    public R<List<LoanManageRepaymentPlan>> listByIouNo(@RequestParam(value = "iouNo")String iouNo) {
        List<LoanManageRepaymentPlan> list = loanManageRepaymentPlanService.list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery().eq(LoanManageRepaymentPlan::getIouNo, iouNo)
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                .orderByAsc(LoanManageRepaymentPlan::getPeriod));
        return R.data(list);
    }
}
