/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service;

import org.apache.ibatis.annotations.Param;
import org.flowable.engine.delegate.DelegateExecution;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.core.mp.base.BaseService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.loan.dto.LoanManageRepaymentPlanDTO;
import org.springblade.loan.dto.PrepaymentDTO;
import org.springblade.loan.entity.*;
import org.springblade.loan.expense.req.RepaymentExpenseReq;
import org.springblade.loan.expense.resp.RepaymentExpenseResp;
import org.springblade.loan.vo.*;
import org.springblade.loan.vo.front.PreRepaymentVO;
import org.springblade.loan.vo.front.UnPaymentListVO;
import org.springblade.product.common.entity.GoodsExpenseRule;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 还款计划表 服务类
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
public interface ILoanManageRepaymentPlanService extends BaseService<LoanManageRepaymentPlan> {


    /**
     * 还款计划列表和还款总额
     *
     * @param iouNo 借据单号
     * @return 还款计划列表和还款总额
     */
    HashMap<String, Object> getMapByIouNo(String iouNo);

    /**
     * 批量插入还款计划
     *
     * @param list 还款计划集合
     * @return 插入多少行
     */
    Integer insertBatch(List<LoanManageRepaymentPlan> list);

    /**
     * 根据借据单号查询还款计划
     *
     * @param iouNo 借据单号
     * @return 借据单号
     */
    List<LoanManageRepaymentPlan> getListByIouNo(String iouNo);

    /**
     * 根据借据单号查询还款计划
     *
     * @param iouNo 借据单号
     * @return 借据单号
     */
    List<LoanManageRepaymentPlan> getListByIou(String iouNo);


    /**
     * 查询每一期应还的金额明细（部分还款）
     *
     * @param plans 还款计划
     * @return List<RepaymentTermVO>
     */
    List<RepaymentTermVO> selectTermByIdsPartRepayment(List<LoanManageRepaymentPlan> plans);

    /**
     * 还款数据详情-给支付方式字段赋值
     *
     * @param repaymentTermVOList 还款数据详情-列表
     * @return 还款数据详情-列表
     */
    List<RepaymentTermVO> listPayModeFinanceNo(List<RepaymentTermVO> repaymentTermVOList);


    /**
     * 计算部分还款未还和新增费用
     *
     * @param loanManageRepaymentPlan 还款计划
     * @param repayment               最后一次还款成功记录
     * @param actualAmount            实还金额
     * @param addAmountVO
     * @return
     */
    AddAmountVO getAddAmountVO(LoanManageRepaymentPlan loanManageRepaymentPlan,
                               LoanManageRepayment repayment,
                               BigDecimal actualAmount,

                               AddAmountVO addAmountVO);

    /**
     * 获得未还的还款计划
     *
     * @param repayment 最近一次还款记录
     * @return
     */
    List<RepaymentPlanFee> getUnPayRepaymentPlanFees(LoanManageRepayment repayment);

    /**
     * 提前结清费用计算器（部分还款）
     *
     * @param loanManageRepaymentPlan
     * @param repaymentTime
     * @param surplusPrincipal
     * @param financeApply
     * @param expenseRuleGroupByExpenseType
     * @return
     */
    AddAmountVO diySettlement(AddAmountVO addAmountVO,
                              LoanManageRepaymentPlan loanManageRepaymentPlan,
                              LocalDate repaymentTime,
                              BigDecimal surplusPrincipal,
                              FinanceApply financeApply,
                              Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType);



    /**
     * 提前还款费用计算器（部分还款）
     *
     * @param loanManageRepaymentPlan
     * @param repaymentTime
     * @param surplusPrincipal
     * @param financeApply
     * @param expenseRuleGroupByExpenseType
     * @return
     */
    AddAmountVO diyPrepayment(AddAmountVO addAmountVO,
                              LoanManageRepaymentPlan loanManageRepaymentPlan,
                              LocalDate repaymentTime,
                              BigDecimal surplusPrincipal,
                              FinanceApply financeApply,
                              Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType);

    /**
     * 逾期费用计算器（部分还款）
     *
     * @param loanManageRepaymentPlan
     * @param repaymentTime
     * @param surplusPrincipal
     * @param financeApply
     * @param expenseRuleGroupByExpenseType
     * @return
     */
    AddAmountVO diyOverdue(AddAmountVO addAmountVO,
                           LoanManageRepaymentPlan loanManageRepaymentPlan,
                           LocalDate repaymentTime,
                           BigDecimal surplusPrincipal,
                           FinanceApply financeApply,
                           Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType);

    /**
     * 随借随还利息计算器（部分还款）,按照上次还款日期计算利息
     *
     * @param loanManageRepaymentPlan
     * @param repaymentTime
     * @param surplusPrincipal
     * @param financeApply
     * @param expenseRuleGroupByExpenseType
     * @return
     */
    AddAmountVO diyInterestBySurplus(AddAmountVO addAmountVO,
                                     LoanManageRepaymentPlan loanManageRepaymentPlan,
                                     LocalDate repaymentTime,
                                     BigDecimal surplusPrincipal,
                                     FinanceApply financeApply,
                                     Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType);

    /**
     * 获取产品对应的费用公式
     *
     * @param goodId
     * @return
     */
    Map<Integer, GoodsExpenseRule> getExpenseRuleGroupByExpenseType(Long goodId);


    /**
     * 根据融资申请id查询还款计划
     *
     * @param financeApplyId 融资申请id
     * @return List<LoanManageRepaymentPlan>
     */
    List<LoanManageRepaymentPlan> listByFinanceApplyId(Long financeApplyId);


    /**
     * 计算费用
     *
     * @param loanManageRepaymentPlans 还款计划对象
     * @return Map<Long, RepaymentExpenseResp>
     */
    Map<Long, RepaymentExpenseResp> calculateExpense(List<LoanManageRepaymentPlan> loanManageRepaymentPlans);


    /**
     * 根据借据单号查询未还款列表
     *
     * @param iouNo 借据单号
     * @return List<LoanManageRepaymentPlan>
     */
    List<LoanManageRepaymentPlan> listUnRepaymentListByIouNo(String iouNo);

    /**
     * 根据借据单号查询还款列表
     *
     * @param iouNo 借据单号
     * @return List<LoanManageRepaymentPlan>
     */
    List<LoanManageRepaymentPlan> listRepaymentListByIouNo(String iouNo);



    /**
     * 计算费用
     *
     * @param loanManageRepaymentPlans 还款计划对象
     * @return Map<Long, RepaymentExpenseResp>
     */
    Map<Long, RepaymentExpenseResp> calculateDetailExpense(List<LoanManageRepaymentPlan> loanManageRepaymentPlans, LoanManageRepayment repayment, Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap);

    /**
     * 查询已还列表
     *
     * @param iouNo 借据单号
     * @return
     */
    List<LoanManageRepaymentPlan> selectRepaymentList(String iouNo);


    /**
     * 根据融资Id查询最新还款计划
     *
     * @param financeId
     * @return
     */
    LoanManageRepaymentPlan selectRepaymentById(Long financeId);

    /**
     * 未付款的还款计划
     *
     * @param financeIds
     * @return
     */
    List<LoanManageRepaymentPlan> listUnPayLoanManageRepaymentPlans(List<Long> financeIds);

    /**
     * 逾期的还款计划
     *
     * @param financeIds
     * @return
     */
    List<LoanManageRepaymentPlan> listOverdueLoanManageRepaymentPlans(List<Long> financeIds);

    /**
     * 获取还款计划最大期数
     *
     * @param financeId
     * @return
     */
    Integer selectRepaymentPeriodMax(@Param("financeId") Long financeId);

    /**
     * 根据还款计划id查询列表
     *
     * @param repaymentPlanIdList
     * @return
     */
    List<LoanManageRepaymentPlan> selectListByIds(List<Long> repaymentPlanIdList);

    /**
     * 根据还款计划id查询
     *
     * @param repaymentPlanId
     * @return
     */
    LoanManageRepaymentPlan getRepaymentPlanById(Long repaymentPlanId);

    /**
     * 根据融资申请id查询已结清还款计划
     *
     * @param financeId
     * @return
     */
    List<LoanManageRepaymentPlan> selectSettledRepaymentPlan(Long financeId);


    /**
     * 更新额外利息字段
     *
     * @param id
     * @param add1
     */
    void updateExAmount(Long id, BigDecimal add1);

    /**
     * 查询还款列表
     *
     * @param userIds        非必传
     * @param goodsType      业务类型 非必传
     * @param unFinishStatus 非必传
     * @param status         还款计划状态
     */
    List<LoanManageRepaymentPlan> listRepaymentPlanByStatusAndUserId(List<Long> userIds, List<Integer> goodsType,
                                                                     List<Integer> unFinishStatus,
                                                                     RepaymentConstant.RepaymentPlanRepaymentStatusEnum status);

    /**
     * 还款查询
     *
     * @param userId 用户id
     * @param ids    还款id
     * @return
     */
    List<LoanManageRepaymentPlan> listByUserIdsAndIds(List<Long> userId, List<Long> ids);

    /**
     * 改变状态
     *
     * @param ids
     * @param repaymentStatus RepaymentConstant.RepaymentPlanRepaymentStatusEnum
     * @param status RepaymentConstant.RepaymentPlanStatusEnum
     */
    void changeRepaymentStatus(List<Long> ids, int repaymentStatus, Integer status);

    /**
     * 获取还款计划
     *
     * @param iouNOList
     * @return
     */
    List<LoanManageRepaymentPlan> listByIouNoList(List<String> iouNOList);

    /**
     * 是否正在使用
     *
     * @param repaymentPlanIds
     * @return
     */
    boolean isUsing(List<Long> repaymentPlanIds);

    /**
     * 计算费用
     *
     * @param loanManageRepaymentPlans 还款计划对象
     * @return Map<Long, RepaymentExpenseResp>
     */
    Map<Long, RepaymentExpenseResp> calculateOverdueExpense(List<LoanManageRepaymentPlan> loanManageRepaymentPlans);
}
