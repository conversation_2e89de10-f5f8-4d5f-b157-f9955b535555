package org.springblade.loan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.loan.vo.BankRepaymentPlanHistoryVo;
import org.springblade.loan.entity.BankRepaymentPlanHistory;
import org.springblade.loan.service.BankRepaymentPlanHistoryService;
import org.springblade.loan.wrapper.BankRepaymentPlanHistoryWrapper;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@RestController
@RequestMapping(CommonConstant.BLADE_LOAN + CommonConstant.WEB_BACK + "/loan/BankRepaymentPlanHistory")
@RequiredArgsConstructor
public class BankRepaymentPlanHistoryController {

    private final BankRepaymentPlanHistoryService bankRepaymentPlanHistoryService;

    @PostMapping("/page")
    private R<IPage<BankRepaymentPlanHistoryVo>> page(Query query, @RequestBody Map<String, Object> queryMap) {
        IPage<BankRepaymentPlanHistory> page = Condition.getMPJLambdaWrapper(queryMap, BankRepaymentPlanHistory.class)
                .orderByAsc(BankRepaymentPlanHistory::getFinanceNo, BankRepaymentPlanHistory::getPullMonth)
                .page(Condition.getPage(query));
        return R.data(BankRepaymentPlanHistoryWrapper.build().pageVO(page));
    }

}
