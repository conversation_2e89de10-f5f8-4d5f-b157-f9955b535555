/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.vo.LoanManageIouTypeVO;
import org.springblade.loan.vo.LoanManageIouVO;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 借据单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
public interface LoanManageIouMapper extends BaseMapper<LoanManageIou> {

    /**
     * 自定义分页
     *
     * @param page
     * @param loanManageIou
     * @return
     */
    List<LoanManageIouVO> selectLoanManageIouPage(IPage page, @Param("loanManageIou") LoanManageIouVO loanManageIou);

    /**
     * 根据借据单号查询借据表
     *
     * @param iouNo
     * @return
     */
    LoanManageIou selectLoan(@Param("iouNo") String iouNo);


    List<LoanManageIou> selectBy7Day();


    /**
     * 查询借据表新增贷款
     *
     * @param time
     * @param type
     * @return
     */
    List<LoanManageIou> newLoansList(
            @Param("time") String time,
            @Param("type") Integer type,
            @Param("tentId") String tentId);

    /**
     * 查询借据表待还款
     *
     * @param time
     * @param type
     * @return
     */
    List<LoanManageIou> unpay(
            @Param("time") String time,
            @Param("type") Integer type);

    /**
     * 查询借据表已还完笔数
     *
     * @param time
     * @param type
     * @return
     */
    List<LoanManageIou> pay(
            @Param("time") String time,
            @Param("type") Integer type);


    /**
     * 根据名字 查询借据表新增贷款
     *
     * @param time
     * @param type
     * @param userId
     * @return
     */
    List<LoanManageIou> newLoansByNameList(@Param("time") String time,
                                           @Param("type") Integer type,
                                           @Param("userId") Long userId,
                                           @Param("tentId") String tentId);

    /**
     * 根据名字 查询借据表待还
     *
     * @param time
     * @param type
     * @param userId
     * @return
     */
    List<LoanManageIou> unpayByName(@Param("time") String time,
                                    @Param("type") Integer type,
                                    @Param("userId") Long userId);

    List<LoanManageIouVO> selectByUserIdAndRecentDay(@Param("userId") Long userId, @Param("recentDay") Integer recentDay);

//	/**
//	 * 查询新增贷款
//	 * @param dto
//	 * @return
//	 */
//	List<LoanManageIou> newLoansDtoList(@Param("dto")CreditReportDto dto);

    /**
     * 查询还款单类型与产品展期方式
     *
     * @param iouNo
     * @return
     */
    LoanManageIouTypeVO selectType(@Param("iouNo") String iouNo);

    /**
     * 根据借据号查询列表
     *
     * @param iouNos
     * @return
     */
    List<LoanManageIou> selectLoanManageIouList(@Param("iouNos") List<String> iouNos);

    /**
     * 根据融资编号查询
     *
     * @param financeNo
     * @return
     */
    LoanManageIou selectLoanByFinanceNo(@Param("financeNo") String financeNo);

    /**
     * 查询某时间以前的放款记录
     *
     * @param time
     * @param userId
     * @return
     */
    List<LoanManageIou> listIouByLtDateStr(@Param("time") String time,
                                           @Param("userId") Long userId,
                                           @Param("tentId") String tentId);

    List<LoanManageIou> queryloanManageIous(@Param("financeNos") List<String> financeNos);

    /**
     * 根据名字 查询借据表新增贷款
     *
     * @param time
     * @param type
     * @param userId
     * @return
     */
    List<LoanManageIou> newLoansByName(@Param("time") String time,
                                       @Param("type") Integer type,
                                       @Param("userId") Long userId,
                                       @Param("tentId") String tentId);

    /**
     * 查询新增贷款
     *
     * @param time      时间点 年：2024/月:2024-01/日:2024-01-29
     * @param goodsType 产品类型 GoodsEnum
     * @param tenantId  租户id
     * @return
     */
    List<LoanManageIou> newLoansDtoList(@Param("time") String time,  @Param("type") List<Integer> goodsType, @Param("tentId") String tenantId);

    /**
     * 时间点内的放款金额汇总
     *
     * @param time      时间点 年：2024/月:2024-01/日:2024-01-29
     * @param goodsType 产品类型 GoodsEnum
     * @param tenantId  租户id
     * @return
     */
    String amountSum(@Param("time") String time, @Param("type") Integer goodsType, @Param("tentId") String tenantId);

    /**
     * 统计时间内放款笔数
     *
     * @param time      时间点 年：2024/月:2024-01/日:2024-01-29
     * @param goodsType 产品类型 GoodsEnum
     * @param tenantId  租户id
     * @return
     */
    Integer loanCount(@Param("time") String time, @Param("type") Integer goodsType, @Param("tentId") String tenantId);

    /**
     * 统计时间内新注册的用户
     *
     * @param time      时间点 年：2024/月:2024-01/日:2024-01-29
     * @param before    时间点内最早时间订单时间
     * @param goodsType 产品类型 GoodsEnum
     * @param tenantId  租户id
     * @return
     */
    Set<Long> newCustomerCount(@Param("time") String time, @Param("beforeTime") LocalDate before, @Param("type") List<Integer> goodsType, @Param("tentId") String tenantId);
    /**
     * 查询新增用户当时的贷款
     *
     * @param time      时间点 年：2024/月:2024-01/日:2024-01-29
     * @param before      时间点内最早时间订单时间
     * @param goodsType 产品类型 GoodsEnum
     * @param tenantId  租户id
     * @return
     */
    List<LoanManageIou> selectCustomerNewByTime(@Param("time") String time,@Param("beforeTime") LocalDate before, @Param("type") List<Integer> goodsType, @Param("tentId") String tenantId);

    /**
     * 连表查询返回vo
     * @param loanManageIou
     * @return
     */
    List<LoanManageIouVO> voList(@Param("iou") LoanManageIou loanManageIou);
}
