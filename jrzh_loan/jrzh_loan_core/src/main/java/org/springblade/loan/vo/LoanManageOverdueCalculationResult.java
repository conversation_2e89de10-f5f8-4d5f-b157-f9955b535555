/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.loan.expense.resp.InterestExpenseResp;

import java.io.Serializable;

/**
 * 逾期协商还款费用计算统计实体
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Data
@ApiModel(value = "LoanManageOverdueCalculationResult对象", description = "逾期协商还款费用计算统计")
public class LoanManageOverdueCalculationResult implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("费用计算结果")
	private InterestExpenseResp repaymentExpenseRespList;
}
