package org.springblade.loan.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.loan.entity.LoanAlterationHistory;
import org.springblade.loan.vo.LoanAlterationHistoryVO;

import java.util.Objects;

/**
 * 变更历史表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
public class LoanAlterationHistoryWrapper  extends BaseEntityWrapper<LoanAlterationHistory, LoanAlterationHistoryVO> {

	public static LoanAlterationHistoryWrapper build() {
		return new LoanAlterationHistoryWrapper();
	}

	@Override
	public LoanAlterationHistoryVO entityVO(LoanAlterationHistory LoanAlterationHistory) {
		LoanAlterationHistoryVO LoanAlterationHistoryVO = Objects.requireNonNull(BeanUtil.copy(LoanAlterationHistory, LoanAlterationHistoryVO.class));

		//User createUser = UserCache.getUser(LoanAlterationHistory.getCreateUser());
		//User updateUser = UserCache.getUser(LoanAlterationHistory.getUpdateUser());
		//LoanAlterationHistoryVO.setCreateUserName(createUser.getName());
		//LoanAlterationHistoryVO.setUpdateUserName(updateUser.getName());

		return LoanAlterationHistoryVO;
	}
}

