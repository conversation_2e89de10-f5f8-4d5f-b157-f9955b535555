package org.springblade.loan.expense.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.product.common.dto.ExpenseRuleDTO;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
public class RepaymentUtil {

	public static Integer getRepaymentType(LocalDate repaymentTime) {
		int compare = repaymentTime.compareTo(LocalDate.now());
		if (compare == 0) {
			// 当期还款
			return RepaymentConstant.RepaymentTypeEnum.CURRENT.getCode();
		}else if (compare < 0) {
			// 逾期还款
			return RepaymentConstant.RepaymentTypeEnum.OVERDUE_DEDUCTION.getCode();
		}else {
			// 提前还款
			return RepaymentConstant.RepaymentTypeEnum.ADVANCE.getCode();
		}
	}

	/**
	 * 还款详情接口 应还时间与还款创建时间对比
	 * @param repaymentTimePlan
	 * @param repaymentTime
	 * @return
	 */
	public static Integer getRepaymentTypeByPayDetail(LocalDate repaymentTimePlan,LocalDate repaymentTime) {
		int compare = repaymentTimePlan.compareTo(repaymentTime);
		if (compare == 0) {
			//当期还款
			return RepaymentConstant.RepaymentTypeEnum.CURRENT.getCode();
		}else if (compare < 0) {
			//逾期还款
			return RepaymentConstant.RepaymentTypeEnum.OVERDUE_DEDUCTION.getCode();
		}else {
			//还款详情提前还款
			return RepaymentConstant.RepaymentTypeEnum.ADVANCE.getCode();
		}
	}

	public static boolean isOverdue(LoanManageRepaymentPlan loanManageRepaymentPlan) {
		return loanManageRepaymentPlan.getRepaymentTime().isBefore(LocalDate.now());
	}

	public static String replaceFeeFieldOfValue(String feeFormula, ExpenseRuleDTO expenseRuleDTO) {
		JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(expenseRuleDTO));
		for (String key : jsonObject.keySet()) {
			if (feeFormula.contains(key)) {
				feeFormula = StrUtil.replace(feeFormula,key,jsonObject.getString(key));
			}
		}
		return feeFormula;
	}

	public static boolean isSettle(LoanManageRepaymentPlan loanManageRepaymentPlan) {
		return loanManageRepaymentPlan.getRepaymentStatus().equals(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
	}
}
