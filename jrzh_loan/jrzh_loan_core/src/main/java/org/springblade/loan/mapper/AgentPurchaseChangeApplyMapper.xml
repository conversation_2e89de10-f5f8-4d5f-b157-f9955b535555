<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.loan.mapper.AgentPurchaseChangeApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentPurchaseChangeApplyResultMap" type="org.springblade.loan.entity.AgentPurchaseChangeApply">
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
        <result column="finance_no" property="financeNo"/>
        <result column="finance_id" property="financeId"/>
        <result column="change_type" property="changeType"/>
        <result column="goods_unit" property="goodsUnit"/>
        <result column="goods_num" property="goodsNum"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="change_apply_frequency" property="changeApplyFrequency"/>
        <result column="refund_order_no" property="refundOrderNo"/>
        <result column="reason" property="reason"/>
        <result column="adjunct_proof" property="adjunctProof"/>
    </resultMap>


    <select id="selectAgentPurchaseChangeApplyPage" resultMap="agentPurchaseChangeApplyResultMap">
        select * from jrzh_agent_purchase_change_apply where is_deleted = 0
    </select>

</mapper>
