/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.loan.entity.RepaymentPlanFeeAlterationHistory;
import org.springblade.loan.mapper.RepaymentPlanFeeAlterationHistoryMapper;
import org.springblade.loan.service.IRepaymentPlanFeeAlterationHistoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 还款计划费用表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Service
@AllArgsConstructor
public class RepaymentPlanFeeServiceAlterationHistoryImpl extends BaseServiceImpl<RepaymentPlanFeeAlterationHistoryMapper, RepaymentPlanFeeAlterationHistory> implements IRepaymentPlanFeeAlterationHistoryService {
    @Override
    public List<RepaymentPlanFeeAlterationHistory> listByAlterPlanIds(List<Long> alterPlanIds) {
        return list(Wrappers.<RepaymentPlanFeeAlterationHistory>lambdaQuery().in(RepaymentPlanFeeAlterationHistory::getAlterationPlanId, alterPlanIds));
    }
}
