<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.loan.mapper.OverdueFollowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="overdueFollowResultMap" type="org.springblade.loan.entity.OverdueFollow">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectOverdueFollowPage" resultMap="overdueFollowResultMap">
        select * from jrzh_overdue_follow where is_deleted = 0
    </select>

</mapper>
