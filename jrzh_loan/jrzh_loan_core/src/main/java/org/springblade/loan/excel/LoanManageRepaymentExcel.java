package org.springblade.loan.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.loan.entity.RepaymentFee;
import org.springblade.loan.vo.RepaymentFeeVO;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年05月29日15:37
 */
@Data
public class LoanManageRepaymentExcel implements Serializable {

    @ExcelIgnore
    private Long id;

    /**
     * 融资用户
     */
    @ExcelIgnore
    private Long userId;
    @ColumnWidth(30)
    @ExcelProperty("融资用户")
    private String userName;

    /**
     * 所属期数
     */
    @ExcelIgnore
    private Integer period;
    @ColumnWidth(30)
    @ExcelProperty("所属期数")
    private String periodDes;

    public String getPeriodDes() {
        Integer period = getPeriod();
        if (Objects.isNull(period)) {
            return StringPool.EMPTY;
        }
        if (period == -1) {
            return "提前结清";
        }
        return "第" + period + "期";
    }

    /**
     * 本金（元）
     */
    @ExcelIgnore
    private BigDecimal principal;
    /**
     * 还款记录关联费用表
     */
    @ExcelIgnore
    private List<RepaymentFeeVO> repaymentFeeList;
    /**
     * 利息（元）
     */
    @ExcelIgnore
    private BigDecimal interest;
    /**
     * 应还金额
     */
    @ColumnWidth(30)
    @ExcelProperty("应还金额(元)")
    private String totalAmount;
    public String getTotalAmount() {
        if(CollUtil.isEmpty(repaymentFeeList)){
            repaymentFeeList = Lists.newArrayList();
        }
        return getPrincipal()
                .add(getInterest())
                .add(getRepaymentFeeList().stream().map(RepaymentFee::getShouldAmount).reduce(BigDecimal.ZERO, BigDecimal::add)).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    @ExcelIgnore
    private BigDecimal actualAmount;
    @ColumnWidth(30)
    @ExcelProperty("实还金额(元)")
    private String actualAmountStr;

    public String getActualAmountStr() {
        if (ObjectUtil.isEmpty(actualAmount)) {
            return "";
        }
        return actualAmount.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 创建日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date createTime;
    @ColumnWidth(30)
    @ExcelProperty("创建日期")
    private String createTimeStr;

    public String getCreateTimeStr() {
        return DateUtil.format(createTime, DatePattern.NORM_DATE_FORMATTER);
    }

    /**
     * 还款日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private LocalDateTime repaymentTime;
    @ColumnWidth(30)
    @ExcelProperty("创建日期")
    private String repaymentTimeStr;

    public String getRepaymentTimeStr() {
        return DateUtil.format(repaymentTime, DatePattern.NORM_DATE_PATTERN);
    }

    /**
     * 还款类型
     * @see RepaymentConstant.RepaymentTypeEnum
     */
    @ApiModelProperty(value = "还款类型 RepaymentConstant.RepaymentTypeEnum")
    @ExcelIgnore
    private Integer repaymentType;
    @ColumnWidth(30)
    @ExcelProperty("还款类型")
    private String repaymentTypeStr;

    public String getRepaymentTypeStr() {
        for (RepaymentConstant.RepaymentTypeEnum value : RepaymentConstant.RepaymentTypeEnum.values()) {
            if (ObjectUtil.isNotEmpty(repaymentType) && Integer.valueOf(value.getCode()).equals(repaymentType)) {
                return value.getName();
            }
        }
        return ObjectUtil.isNotEmpty(repaymentType) ? repaymentType.toString() : "";
    }

    /**
     * 支付状态
     */
    @ExcelIgnore
    private Integer status;
    @ColumnWidth(30)
    @ExcelProperty("状态")
    private String statusStr;

    public String getStatusStr() {
        for (RepaymentConstant.RepaymentStatusEnum value : RepaymentConstant.RepaymentStatusEnum.values()) {
            if (value.getCode().equals(status)) {
                return value.getName();
            }
        }
        return ObjectUtil.isNotEmpty(status) ? status.toString() : "";
    }

    /**
     * 创建日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date updateTime;
    @ColumnWidth(30)
    @ExcelProperty("操作时间")
    private String updateTimeStr;

    public String getUpdateTimeStr() {
        return DateUtil.format(createTime, DatePattern.NORM_DATETIME_FORMATTER);
    }

    /**
     * 操作用户
     */
    @ExcelIgnore
    private Long operatorUserId;
    @ColumnWidth(30)
    @ExcelProperty("操作人")
    private String operatorUserName;

}
