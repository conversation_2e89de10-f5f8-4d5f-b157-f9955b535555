package org.springblade.loan.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.loan.entity.AgentPurchaseChange;
import org.springblade.loan.vo.AgentPurchaseChangeVO;

import java.util.Objects;

/**
 * 代采变更表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public class AgentPurchaseChangeWrapper extends BaseEntityWrapper<AgentPurchaseChange, AgentPurchaseChangeVO> {

    public static AgentPurchaseChangeWrapper build() {
        return new AgentPurchaseChangeWrapper();
    }

    @Override
    public AgentPurchaseChangeVO entityVO(AgentPurchaseChange AgentPurchaseChangeApply) {
        AgentPurchaseChangeVO AgentPurchaseChangeApplyVO = Objects.requireNonNull(BeanUtil.copy(AgentPurchaseChangeApply, AgentPurchaseChangeVO.class));

        //User createUser = UserCache.getUser(AgentPurchaseChangeApply.getCreateUser());
        //User updateUser = UserCache.getUser(AgentPurchaseChangeApply.getUpdateUser());
        //AgentPurchaseChangeApplyVO.setCreateUserName(createUser.getName());
        //AgentPurchaseChangeApplyVO.setUpdateUserName(updateUser.getName());

        return AgentPurchaseChangeApplyVO;
    }
}
