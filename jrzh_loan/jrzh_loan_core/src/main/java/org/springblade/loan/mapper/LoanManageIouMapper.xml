<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.loan.mapper.LoanManageIouMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="loanManageIouResultMap" type="org.springblade.loan.entity.LoanManageIou">
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
        <result column="financing_no" property="financingNo"/>
        <result column="iou_no" property="iouNo"/>
        <result column="iou_type" property="iouType"/>
        <result column="financing_id" property="financingId"/>
        <result column="iou_amount" property="iouAmount"/>
        <result column="loan_time" property="loanTime"/>
        <result column="period" property="period"/>
        <result column="period_des" property="periodDes"/>
        <result column="end_loan_time" property="endLoanTime"/>
        <result column="five_level_category" property="fiveLevelCategory"/>
    </resultMap>


    <select id="newLoansDtoList" resultType="org.springblade.loan.entity.LoanManageIou">
       SELECT * FROM
            `jrzh_loan_manage_iou` as iou
            WHERE iou.loan_time like concat(concat(#{time},'%'))
            and iou.is_deleted = 0
            and iou.goods_type in
            <foreach collection="type" item="goodsType" index="index" open="(" close=")" separator=",">
                #{goodsType}
            </foreach>
            and iou.tenant_id = #{tentId}
                and status!=9
    </select>
    <select id="selectCustomerNewByTime" resultType="org.springblade.loan.entity.LoanManageIou">
        <!--    order by create_time先进行排序  GROUP BY user_id再聚合 这样拿出来的就是 每个用户最新的订单   -->
        select * from (SELECT iou.*  FROM
        `jrzh_loan_manage_iou` as iou
        WHERE iou.loan_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        and iou.goods_type in
        <foreach collection="type" item="goodsType" index="index" open="(" close=")" separator=",">
            #{goodsType}
        </foreach>
        and iou.tenant_id = #{tentId}
        <!--        不在以前的时间下过订单的用户 即为新用户-->
        and status!=9 and user_id not in(
        SELECT distinct subIou.user_id FROM
        `jrzh_loan_manage_iou` as subIou
        WHERE subIou.loan_time &lt; #{beforeTime}
        and subIou.is_deleted = 0 and subIou.goods_type in
        <foreach collection="type" item="goodsType" index="index" open="(" close=")" separator=",">
            #{goodsType}
        </foreach>
        and subIou.tenant_id = #{tentId}
        and subIou.status!=9 ) order by create_time) as m_iou group by m_iou.user_id
    </select>

    <select id="newLoansByNameList" resultType="org.springblade.loan.entity.LoanManageIou">
        SELECT iou.* FROM
        `jrzh_loan_manage_iou` as iou
        LEFT JOIN jrzh_finance_apply as finance
        ON iou.finance_apply_id = finance.id
        WHERE iou.loan_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        and iou.status != 9
        <if test="type == 1 or type == 2">
            and finance.goods_type = #{type}
        </if>
        <if test="userId != null and userId != 0">
            and iou.user_id = #{userId}
        </if>
        <if test="tentId != null and tentId != ''">
            and iou.tenant_id = #{tentId}
        </if>

    </select>

    <select id="pay" resultType="org.springblade.loan.entity.LoanManageIou">
        SELECT iou.* FROM `jrzh_loan_manage_iou` as iou
        LEFT JOIN jrzh_finance_apply as finance
        ON iou.finance_apply_id = finance.id
        WHERE iou.expire_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        <if test="type == 1 or type == 2">
            and finance.goods_type = #{type}
        </if>
        and iou.status in(3,4,5,6,7);
    </select>

    <select id="unpay" resultType="org.springblade.loan.entity.LoanManageIou">
        SELECT iou.* FROM
        `jrzh_loan_manage_iou` as iou
        LEFT JOIN jrzh_finance_apply as finance
        ON iou.finance_apply_id = finance.id
        WHERE iou.expire_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        <if test="type == 1 or type == 2">
            and finance.goods_type = #{type}
        </if>
        and iou.status in(1,2);

    </select>

    <select id="newLoansList" resultType="org.springblade.loan.entity.LoanManageIou">
        SELECT iou.* FROM
        `jrzh_loan_manage_iou` as iou
        LEFT JOIN jrzh_finance_apply as finance
        ON iou.finance_apply_id = finance.id
        WHERE iou.loan_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        and iou.status != 9
        <if test="type == 1 or type == 2">
            and finance.goods_type = #{type}
        </if>
        <if test="tentId != null and tentId != ''">
            and iou.tenant_id = #{tentId}
        </if>
    </select>

    <select id="amountSum" resultType="java.lang.String">
        <!--      status=9为已作废  -->
        SELECT sum(iou.iou_amount) FROM
        `jrzh_loan_manage_iou` as iou
        WHERE iou.loan_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        <if test="type != null">
            and goods_type = #{type}
        </if>
        and iou.tenant_id = #{tentId}
        and status!=9
    </select>

    <select id="newCustomerCount" resultType="java.lang.Long">
        SELECT distinct iou.user_id FROM
        `jrzh_loan_manage_iou` as iou
        WHERE iou.loan_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        and iou.goods_type in
        <foreach collection="type" item="goodsType" index="index" open="(" close=")" separator=",">
            #{goodsType}
        </foreach>
        and iou.tenant_id = #{tentId}
<!--        不在以前的时间下过订单的用户 即为新用户-->
        and status!=9 and user_id not in(
            SELECT distinct subIou.user_id FROM
            `jrzh_loan_manage_iou` as subIou
            WHERE subIou.loan_time &lt; #{beforeTime}
            and subIou.is_deleted = 0 and subIou.goods_type in
                <foreach collection="type" item="goodsType" index="index" open="(" close=")" separator=",">
                    #{goodsType}
                </foreach>
            and subIou.tenant_id = #{tentId}
            and subIou.status!=9 )
    </select>
    <select id="loanCount" resultType="java.lang.Integer">
        <!--      status=9为已作废  -->
        SELECT count(*) FROM
        `jrzh_loan_manage_iou` as iou
        WHERE iou.loan_time like concat(concat(#{time},'%'))
        and iou.is_deleted = 0
        <if test="type != null">
            and goods_type = #{type}
        </if>
        and iou.tenant_id = #{tentId}
        and status!=9
    </select>

    <select id="selectLoanManageIouPage" resultMap="loanManageIouResultMap">
        select *
        from jrzh_loan_manage_iou
        where is_deleted = 0
    </select>

    <select id="selectLoan" resultType="org.springblade.loan.entity.LoanManageIou">
        select *
        from jrzh_loan_manage_iou
        where iou_no = #{iouNo}
    </select>

    <select id="selectBy7Day" resultType="org.springblade.loan.entity.LoanManageIou">
        SELECT *
        FROM jrzh_loan_manage_iou
        WHERE date_sub(curdate(), interval 7 day) <![CDATA[ <= ]]> date(create_time);
    </select>


    <select id="unpayByName" resultType="org.springblade.loan.entity.LoanManageIou">
        SELECT iou.* FROM
        `jrzh_loan_manage_iou` as iou
        LEFT JOIN jrzh_finance_apply as finance
        ON iou.finance_apply_id = finance.id
        WHERE iou.expire_time like concat(concat(#{time},'%'))
        <if test="type == 1 or type == 2">
            and finance.goods_type = #{type}
        </if>
        <if test="userId != null and userId != 0">
            and iou.user_id = #{userId}
        </if>
        and iou.is_deleted = 0
        and iou.status in(2,8)

    </select>

    <select id="selectByUserIdAndRecentDay" resultType="org.springblade.loan.vo.LoanManageIouVO">
        SELECT * from (SELECT *, DATE_FORMAT(loan_time,'%Y-%m-%d')
        as times from jrzh_loan_manage_iou where DATE_SUB(CURDATE(), INTERVAL #{recentDay} DAY) <![CDATA[ <= ]]>
        date(loan_time) and user_id= #{userId} and is_deleted = 0 )
        a ORDER BY a.times desc
    </select>

    <select id="selectType" resultType="org.springblade.loan.vo.LoanManageIouTypeVO">
        SELECT
        a.finance_no financeNo,
        b.repayment_type repaymentType,
        c.delay_type delayMode
        FROM
        jrzh_finance_apply a
        INNER JOIN jrzh_loan_manage_iou b ON a.finance_no = b.finance_no
        INNER JOIN jrzh_goods c ON a.goods_id = c.id
        WHERE b.iou_no = #{iouNo}
    </select>
    <select id="selectLoanManageIouList" resultType="org.springblade.loan.entity.LoanManageIou">
        select * from jrzh_loan_manage_iou
        where( iou_no IN
        <foreach collection="iouNos" item="iouNos" index="index" open="(" close=")" separator=",">
            #{iouNos}
        </foreach>)
    </select>

    <select id="selectLoanByFinanceNo" resultType="org.springblade.loan.entity.LoanManageIou">
        select *
        from jrzh_loan_manage_iou
        where finance_no = #{financeNo}
    </select>

    <select id="listIouByLtDateStr" resultType="org.springblade.loan.entity.LoanManageIou">
        SELECT * FROM `jrzh_loan_manage_iou` WHERE loan_time &lt; #{time}
        and is_deleted = 0
        and status != 9
        <if test="userId != null and userId != 0">
            and user_id = #{userId}
        </if>
        <if test="tentId != null and tentId != ''">
            and tenant_id = #{tentId}
        </if>
    </select>
    <select id="queryloanManageIous" resultType="org.springblade.loan.entity.LoanManageIou">
        select * from jrzh_loan_manage_iou
        where ( finance_no IN
        <foreach collection="financeNos" item="financeNos" index="index" open="(" close=")" separator=",">
            #{financeNos}
        </foreach>)
    </select>

    <select id="voList" resultType="org.springblade.loan.vo.LoanManageIouVO">
        select jlmi.*, bu.name as user_name, bd.dept_name as capital_name
        from jrzh_loan_manage_iou jlmi
        left join blade_user bu on jlmi.user_id = bu.id
        left join blade_dept bd on jlmi.capital_id = bd.id
        <where>
            jlmi.is_deleted = 0 and bu.is_deleted = 0 and bd.is_deleted = 0
            <if test="iou.userId != null">
                and jlmi.user_id = #{iou.userId}
            </if>
            <if test="iou.capitalId != null">
                and jlmi.capital_id = #{iou.capitalId}
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>
