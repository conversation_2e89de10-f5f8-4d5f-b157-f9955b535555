package org.springblade.loan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.dto.LoanManageRepaymentDTO;
import org.springblade.loan.dto.RepaymentSearchDTO;
import org.springblade.loan.entity.*;
import org.springblade.loan.enums.PayModeEnum;
import org.springblade.loan.excel.LoanManageRepaymentExcel;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.mapper.LoanManageRepaymentMapper;
import org.springblade.loan.service.*;
import org.springblade.loan.vo.LoanManageRepaymentVO;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.loan.wrapper.RepaymentFeeWrapper;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-09  22:00
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class LoanManageRepaymentServiceImpl extends BaseServiceImpl<LoanManageRepaymentMapper, LoanManageRepayment> implements ILoanManageRepaymentService {
    private final RemoteUserService userService;
    private final IRepaymentFeeService repaymentFeeService;
    private final LoanManageIouMapper loanManageIouMapper;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;
    private final IAttachService attachService;

    @Override
    public IPage<LoanManageRepaymentVO> getLoanManageRepaymentVoPage(IPage<LoanManageRepayment> page) {
        List<LoanManageRepayment> repaymentList = page.getRecords();
        Page<LoanManageRepaymentVO> repaymentVoPage = LoanManageRepaymentWrapper.build().pageVO(page);
        if (Func.isEmpty(repaymentList)) {
            return repaymentVoPage;
        }

        List<LoanManageRepaymentVO> repaymentVoList = repaymentVoPage.getRecords();
        List<Long> userIds = StreamUtil.map(repaymentList, LoanManageRepayment::getOperatorUserId);
        Map<Long, User> userMap = userService.listByUser(userIds, FeignConstants.FROM_IN).getData().stream().collect(Collectors.toMap(User::getId, e -> e));
        List<Long> attachIds = repaymentVoList.stream().map(LoanManageRepayment::getVoucher)
                .filter(StringUtil::isNotBlank)
                .flatMap(e -> Func.toLongList(e).stream()).collect(Collectors.toList());
        Map<Long, Attach> attachMap = CollUtil.isNotEmpty(attachIds) ? attachService.listByIds(attachIds)
                .stream().collect(Collectors.toMap(BaseEntity::getId, e -> e))
                : MapUtil.newHashMap();
        //查询还款记录关联费用
        List<Long> repaymentIds = StreamUtil.map(repaymentList, LoanManageRepayment::getId);
        List<RepaymentFee> RepaymentFees = repaymentFeeService.getRepaymentFeeByRepaymentIds(repaymentIds);
        Map<Long, List<RepaymentFee>> repaymentFeeMap = RepaymentFees.stream().collect(Collectors.groupingBy(RepaymentFee::getRepaymentId));

        repaymentVoList.forEach(repaymentVo -> {
            //设置支付凭证
            if (ObjectUtil.isNotEmpty(repaymentVo.getVoucher())) {
                repaymentVo.setAttachList(Func.toLongList(repaymentVo.getVoucher())
                        .stream()
                        .filter(attachMap::containsKey)
                        .map(attachMap::get).collect(Collectors.toList()));
            }
            User user = userMap.get(repaymentVo.getOperatorUserId());
            if (Objects.nonNull(user)) {
                repaymentVo.setUserName(user.getName());
                repaymentVo.setCustomerName(user.getName());
            }
            List<RepaymentFee> repaymentFees = repaymentFeeMap.get(repaymentVo.getId());
            if (CollUtil.isNotEmpty(repaymentFees)) {
                repaymentVo.setRepaymentFeeList((RepaymentFeeWrapper.build().listVO(repaymentFees)));
            } else {
                repaymentVo.setRepaymentFeeList(Collections.emptyList());
            }
        });
        repaymentVoPage.setRecords(repaymentVoList);
        return repaymentVoPage;
    }

    @Override
    public List<LoanManageRepaymentExcel> getLoanManageRepaymentExcelList(List<LoanManageRepayment> dbList) {
        if (Func.isEmpty(dbList)) {
            return new ArrayList<>();
        }
        List<Long> userIds = new ArrayList<>();
        for (LoanManageRepayment loanManageRepayment : dbList) {
            // 操作人id
            userIds.add(loanManageRepayment.getOperatorUserId());
            // 借款用户id
            userIds.add(loanManageRepayment.getUserId());
        }
        userIds = userIds.stream().distinct().collect(Collectors.toList());
        Map<Long, User> userMap = userService.listByUser(userIds, FeignConstants.FROM_IN).getData().stream().collect(Collectors.toMap(User::getId, e -> e));

        //查询还款记录关联费用
        List<Long> repaymentIds = StreamUtil.map(dbList, LoanManageRepayment::getId);
        List<RepaymentFee> RepaymentFees = repaymentFeeService.getRepaymentFeeByRepaymentIds(repaymentIds);
        Map<Long, List<RepaymentFee>> repaymentFeeMap = RepaymentFees.stream().collect(Collectors.groupingBy(RepaymentFee::getRepaymentId));

        List<LoanManageRepaymentExcel> excelList = BeanUtil.copyToList(dbList, LoanManageRepaymentExcel.class);
        excelList.forEach(repaymentExcel -> {
            User user = userMap.get(repaymentExcel.getUserId());
            User operatorUser = userMap.get(repaymentExcel.getOperatorUserId());
            if (Objects.nonNull(user)) {
                repaymentExcel.setUserName(user.getName());
                repaymentExcel.setOperatorUserName(operatorUser.getName());
            }
            List<RepaymentFee> repaymentFees = repaymentFeeMap.get(repaymentExcel.getId());
            if (CollUtil.isNotEmpty(repaymentFees)) {
                repaymentExcel.setRepaymentFeeList((RepaymentFeeWrapper.build().listVO(repaymentFees)));
            } else {
                repaymentExcel.setRepaymentFeeList(Collections.emptyList());
            }
        });
        return excelList;
    }

    @Override
    public List<LoanManageRepayment> listAllRelationLoanRepayment(String financeNo) {
        IFinanceApplyService financeApplyService = SpringUtil.getBean(IFinanceApplyService.class);
        ILoanManageIouService loanManageIouService = SpringUtil.getBean(ILoanManageIouService.class);
        FinanceApply currentFinanceApply = financeApplyService.getByFinanceNo(financeNo);
        String currentFinanceNo = currentFinanceApply.getFinanceNo();
        if (ObjectUtil.isEmpty(currentFinanceApply)) {
            return CollectionUtil.newArrayList();
        }
        //将关联的还款全部查出
        List<String> reduceIouNos = new ArrayList<>();
        while (StringUtil.isNotBlank(currentFinanceNo)) {
            LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(currentFinanceNo);
            reduceIouNos.add(loanManageIou.getIouNo());
            if (StringUtil.isNotBlank(currentFinanceApply.getOldFinanceNo())) {
                currentFinanceApply = financeApplyService.getByFinanceNo(currentFinanceApply.getOldFinanceNo());
                currentFinanceNo = currentFinanceApply.getFinanceNo();
            } else {
                currentFinanceNo = "";
            }
        }
        return list(Wrappers.<LoanManageRepayment>lambdaQuery()
                .in(LoanManageRepayment::getIouNo, reduceIouNos)
                .eq(LoanManageRepayment::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode())
                .orderByAsc(LoanManageRepayment::getPeriod)
                .orderByAsc(LoanManageRepayment::getCreateTime));
    }

    @Override
    public List<LoanManageRepayment> findPayByIous(String iouNo) {
        if (StringUtil.isEmpty(iouNo)) {
            return Collections.EMPTY_LIST;
        }
        List<LoanManageRepayment> loanManageRepaymentList = baseMapper.selectList(Wrappers.<LoanManageRepayment>lambdaQuery()
                .eq(LoanManageRepayment::getIouNo, iouNo)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode())
                .orderByDesc(BaseEntity::getCreateTime));
        return loanManageRepaymentList;
    }

    @Override
    public List<LoanManageRepayment> selectOverdueHavePaid(List<Long> repaymentPlanIdList) {
        return baseMapper.selectOverdueHavePaid(repaymentPlanIdList);
    }

    @Override
    public List<LoanManageRepaymentVO> listByFinanceApplyId(Long financeApplyId) {
        LoanManageIou loanManageIou = loanManageIouMapper.selectOne(Wrappers.<LoanManageIou>lambdaQuery()
                .eq(LoanManageIou::getFinanceApplyId, financeApplyId));

        if (Objects.isNull(loanManageIou)) {
            return Collections.emptyList();
        }
        return LoanManageRepaymentWrapper.build().listVO(listByIouNo(loanManageIou.getIouNo()));
    }

    @Override
    public List<LoanManageRepayment> listByIouNo(String iouNo) {
        return baseMapper.selectList(Wrappers.<LoanManageRepayment>lambdaQuery().eq(LoanManageRepayment::getIouNo, iouNo));
    }

    /**
     * 处于支付中状态
     *
     * @param loanManageRepayments
     */
    @Override
    public Boolean hasPaying(List<LoanManageRepayment> loanManageRepayments) {
        return StreamUtil.filterToStream(loanManageRepayments,
                        loanManageRepayment -> loanManageRepayment.getStatus().equals(RepaymentConstant.RepaymentStatusEnum.PAYING.getCode()))
                .count() > 0;
    }

    /**
     * 处于支付中状态
     *
     * @param planIds
     */
    @Override
    public Boolean hasPayingByPlanIds(List<Long> planIds) {
        return count(Wrappers.<LoanManageRepayment>lambdaQuery().eq(LoanManageRepayment::getStatus, RepaymentConstant.RepaymentStatusEnum.PAYING.getCode())
                .in(LoanManageRepayment::getRepaymentPlanId, planIds)) > 0;
    }

    /**
     * 保存还款记录关联费用表
     *
     * @param repayment   还款记录
     * @param planFeeList 待还的还款计划关联费用
     */
    @Override
    @Deprecated
    public void saveRepaymentFee(LoanManageRepayment repayment, List<RepaymentPlanFee> planFeeList) {
        if (ObjectUtil.isNotEmpty(planFeeList)) {
            //还款计划关联费用表生成还款记录关联费用记录
            planFeeList.forEach(planFee -> {
                if (RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == planFee.getRepaymentStatus()) {
                    RepaymentFee repaymentFee = new RepaymentFee();
                    BeanUtil.copyProperties(repayment, repaymentFee);

                    repaymentFee.setId(null);
                    repaymentFee.setRepaymentId(repayment.getId());
                    repaymentFee.setExpenseKey(planFee.getExpenseKey());
                    repaymentFee.setFeeTypeName(planFee.getFeeTypeName());
                    repaymentFee.setFeeName(planFee.getFeeName());
                    repaymentFee.setExpenseTypeId(planFee.getExpenseTypeId());
                    repaymentFee.setShouldAmount(planFee.getAmount());
                    repaymentFee.setActualAmount(BigDecimal.ZERO);

                    if (PayModeEnum.PAY_MODE_ONLINE.getCode().equals(repayment.getPayMode())) {
                        repaymentFee.setMerchantNo(repaymentFee.getMerchantNo());
                    }

                    repaymentFeeService.save(repaymentFee);
                    //保存动态还款计划与动态还款记录关联记录
                    loanManageRepaymentTermService.saveRepaymentTermList(repaymentFee.getId(),
                            planFee.getIouNo(),
                            Collections.singletonList(planFee.getId()));
                }
            });
        }
    }

    @Override
    public void isSettle(LoanManageRepaymentDTO loanManageRepaymentDTO, LoanManageRepayment loanManageRepayment, LoanManageRepaymentPlan loanManageRepaymentPlan) {
        LocalDateTime repaymentTime = loanManageRepaymentDTO.getRepaymentTime();
        boolean before = loanManageRepaymentPlan.getRepaymentTime().isBefore(repaymentTime.toLocalDate());
        if (before) {
            loanManageRepaymentPlan.setOverdue(RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode());
        }
        //查询还款计划所有关联费用 和 实还费用
        List<RepaymentPlanFee> planFees = repaymentPlanFeeService.getAllPlanFeeByPlanIds(loanManageRepaymentPlan.getId());
        BigDecimal amountFee = planFees.stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualAmountFee = repaymentFeeService.getPayRepaymentFeeAmount(planFees.stream().map(BaseEntity::getId).collect(Collectors.toList()));

        List<LoanManageRepayment> repayment = loanManageRepaymentTermService.getPayedRepaymentByPlanId(loanManageRepaymentPlan.getId());
        BigDecimal actualPrincipal = repayment.stream().map(LoanManageRepayment::getActualPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (loanManageRepaymentPlan.getPrincipal().compareTo(actualPrincipal.add(loanManageRepayment.getActualPrincipal())) == 0 &&
                amountFee.compareTo(actualAmountFee) >= 0) {
            loanManageRepaymentPlan.setRepaymentStatus(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
            loanManageRepaymentPlan.setPenaltyInterest(loanManageRepayment.getActualPenaltyInterest());
        }
    }

    @Override
    public List<LoanManageRepayment> listByPlanIds(List<Long> planIds) {
        return list(Wrappers.<LoanManageRepayment>lambdaQuery().in(LoanManageRepayment::getRepaymentPlanId, planIds));
    }

    @Override
    public List<LoanManageRepayment> listByIouNo(List<String> iouNos) {
        return list(Wrappers.<LoanManageRepayment>lambdaQuery().in(LoanManageRepayment::getIouNo, iouNos));
    }

    @Override
    public List<RepaymentFee> saveNewRepaymentFee(LoanManageRepayment loanManageRepayment, List<RepaymentPlanFee> planFeeList) {
        if (ObjectUtil.isNotEmpty(planFeeList)) {
            //还款计划关联费用表生成还款记录关联费用记录
            List<RepaymentFee> repaymentFeeList = planFeeList.stream().filter(e -> RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == e.getRepaymentStatus())
                    .map(planFee -> {
                        RepaymentFee repaymentFee = new RepaymentFee();
                        if (PayModeEnum.PAY_MODE_ONLINE.getCode().equals(loanManageRepayment.getPayMode())
                        ) {
                            if (planFee.getExpenseOrderDetail() != null) {
                                repaymentFee.setMerchantNo(planFee.getExpenseOrderDetail().getMerchantNo());
                            } else if (planFee.getExpenseOrderDetailStr() != null) {
                                ExpenseOrderDetail expenseOrderDetail = JSONUtil.toBean
                                        (JSONUtil.parseObj(planFee.getExpenseOrderDetailStr()), ExpenseOrderDetail.class);
                                repaymentFee.setMerchantNo(expenseOrderDetail.getMerchantNo());
                            }

                        }
                        repaymentFee.setReductionAmount(planFee.getReductionAmount());
                        repaymentFee.setRepaymentId(loanManageRepayment.getId());
                        repaymentFee.setRepaymentNo(loanManageRepayment.getRepaymentNo());
                        repaymentFee.setIouNo(loanManageRepayment.getIouNo());
                        repaymentFee.setUserId(loanManageRepayment.getUserId());
                        repaymentFee.setFeeTypeName(planFee.getFeeTypeName());
                        repaymentFee.setRepaymentTime(LocalDateTime.now());
                        repaymentFee.setRepaymentType(loanManageRepayment.getRepaymentType());
                        repaymentFee.setGoodsId(loanManageRepayment.getGoodsId());
                        repaymentFee.setGoodsType(loanManageRepayment.getGoodsType());
                        repaymentFee.setFeePlanId(planFee.getId());
                        repaymentFee.setFeeName(planFee.getFeeName());
                        repaymentFee.setExpenseKey(planFee.getExpenseKey());
                        repaymentFee.setExpenseTypeId(planFee.getExpenseTypeId());
                        repaymentFee.setShouldAmount(planFee.getNeedPayAmount());
                        repaymentFee.setActualAmount(planFee.getNeedPayAmount());
                        repaymentFee.setStatus(RepaymentConstant.RepaymentStatusEnum.PAYING.getCode());

                        return repaymentFee;
                    }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(repaymentFeeList)) {
                repaymentFeeService.saveBatch(repaymentFeeList);
            }
            return repaymentFeeList;
        }
        return Collections.emptyList();
    }

    @Override
    public Integer getLastRefundTerm(String financeNo) {
        IFinanceApplyService financeApplyService = SpringUtil.getBean(IFinanceApplyService.class);
        ILoanManageIouService loanManageIouService = SpringUtil.getBean(ILoanManageIouService.class);
        FinanceApply currentFinanceApply = financeApplyService.getByFinanceNo(financeNo);
        String currentFinanceNo = currentFinanceApply.getFinanceNo();
        if (ObjectUtil.isEmpty(currentFinanceApply)) {
            return 0;
        }
        //将关联的还款全部查出
        List<String> reduceIouNos = new ArrayList<>();
        while (StringUtil.isNotBlank(currentFinanceNo)) {
            LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(currentFinanceNo);
            reduceIouNos.add(loanManageIou.getIouNo());
            if (StringUtil.isNotBlank(currentFinanceApply.getOldFinanceNo())) {
                currentFinanceApply = financeApplyService.getByFinanceNo(currentFinanceApply.getOldFinanceNo());
                currentFinanceNo = currentFinanceApply.getFinanceNo();
            } else {
                currentFinanceNo = "";
            }
        }
        //获取最后还款的期数
        LoanManageRepayment one = getOne(Wrappers.<LoanManageRepayment>lambdaQuery().in(LoanManageRepayment::getIouNo, reduceIouNos)
                .eq(LoanManageRepayment::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode()).orderByDesc(LoanManageRepayment::getPeriod).last("limit 1"));
        return Objects.isNull(one) ? 0 : one.getPeriod();
    }

    @Override
    public List<LoanManageRepaymentVO> voList(RepaymentSearchDTO dto) {
        return baseMapper.voList(dto);
    }

}
