package org.springblade.loan.expense;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.common.utils.StreamUtil;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.expense.req.BaseExpenseReq;
import org.springblade.loan.expense.resp.InterestAccrualDaysExpenseResp;
import org.springblade.loan.expense.resp.RepaymentExpenseResp;
import org.springblade.loan.expense.util.RepaymentUtil;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.GoodsExpenseRule;
import org.springblade.product.common.utils.GoodsFeeRulesUtil;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.resource.cache.DictBizCache;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public abstract class BaseExpenseCalculate implements ExpenseCalculate {

    public Map<Integer, GoodsExpenseRule> getExpenseRuleGroupByExpenseType(BaseExpenseReq baseExpenseReq) {
        List<GoodsExpenseRule> goodsExpenseRules = baseExpenseReq.getGoodsExpenseRules();
        if (CollectionUtil.isEmpty(goodsExpenseRules)) {
            return MapUtil.newHashMap();
        }
        return StreamUtil.toMap(goodsExpenseRules, GoodsExpenseRule::getExpenseType, obj -> obj);
    }

    /**
     * 已还款 提前还款天数以及日期范围
     *
     * @param repaymentExpenseResp
     * @param repaymentTime
     * @param expenseRuleDTO
     */
    public void calculatePrepaymentDayByPayDetail(RepaymentExpenseResp repaymentExpenseResp, LocalDate repaymentTime, ExpenseRuleDTO expenseRuleDTO, LocalDate payDetailCreateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        BigDecimal prepaymentDays = expenseRuleDTO.getPrepaymentDays();
        // 提前还款天数 日期范围
        String repaymentTimeStr = repaymentTime.format(formatter);
        String payDetailCreateTimeStr = payDetailCreateTime.format(formatter);
        String prepaymentDayDateRange = repaymentTimeStr.concat(StringPool.TILDA).concat(payDetailCreateTimeStr);
        repaymentExpenseResp.setPrepaymentDay(prepaymentDays);
        repaymentExpenseResp.setInterestAccrualDateRange(prepaymentDayDateRange);
    }

    public void calculatePrepaymentDay(RepaymentExpenseResp repaymentExpenseResp, LocalDate repaymentTime, ExpenseRuleDTO expenseRuleDTO) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        BigDecimal prepaymentDays = expenseRuleDTO.getPrepaymentDays();
        // 提前还款天数日期范围
        String repaymentTimeStr = repaymentTime.format(formatter);
        String prepaymentDayDateRange = LocalDate.now().format(formatter).concat(StringPool.TILDA).concat(repaymentTimeStr);
        repaymentExpenseResp.setPrepaymentDay(prepaymentDays);
        repaymentExpenseResp.setInterestAccrualDateRange(prepaymentDayDateRange);
    }

    public void calculatePrepaymentServiceFee(RepaymentExpenseResp repaymentExpenseResp, Map<Integer, GoodsExpenseRule> goodsExpenseRuleMap, ExpenseRuleDTO expenseRuleDTO) {
        // 提前还款手续费
        GoodsExpenseRule prepaymentServiceFeeRule = goodsExpenseRuleMap.get(ExpenseConstant.ExpenseTypeEnum.PREPAYMENT_SERVICE_FEE.getCode());
        if (Objects.isNull(prepaymentServiceFeeRule)) {
            repaymentExpenseResp.setPrepaymentServiceFee(BigDecimal.ZERO);
            return;
        }
        // 费用类型名称
        String expenseTypeName = DictBizCache.getValue(DictBizEnum.GOODS_RULE_EXPENSE_TYPE, prepaymentServiceFeeRule.getExpenseType());
        // 提前还款手续费公式
        String prepaymentServiceFeeFormula = expenseTypeName + StringPool.EQUALS + prepaymentServiceFeeRule.getFeeFormulaName();
        // 计算提前还款手续费
        BigDecimal prepaymentServiceFee = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(prepaymentServiceFeeRule), expenseRuleDTO).get(0);
        //提前还款手续费替换成具体值后的公式
        String prepaymentServiceFeeValueFormula = RepaymentUtil.replaceFeeFieldOfValue(prepaymentServiceFeeRule.getFeeFormula(), expenseRuleDTO) + "=" + prepaymentServiceFee.toString() + "元";

        repaymentExpenseResp.setPrepaymentServiceFee(prepaymentServiceFee);
        repaymentExpenseResp.setPrepaymentServiceFeeFormula(prepaymentServiceFeeFormula);
        repaymentExpenseResp.setPrepaymentServiceFeeValueFormula(prepaymentServiceFeeValueFormula);
    }

    /**
     * 计算利息以及利息的公式
     *
     * @param repaymentExpenseResp repaymentExpenseResp
     * @param baseExpenseReq       baseExpenseReq
     * @param goodsExpenseRuleMap  goodsExpenseRuleMap
     */
    public void calculateInterest(RepaymentExpenseResp repaymentExpenseResp, BaseExpenseReq baseExpenseReq, Map<Integer, GoodsExpenseRule> goodsExpenseRuleMap) {
        ExpenseRuleDTO expenseRuleDTO = baseExpenseReq.buildExpenseRuleDTO();
        repaymentExpenseResp.setSurplusPrincipal(expenseRuleDTO.getPrincipalRepayment());
        // 应还利息
        GoodsExpenseRule interestRule = goodsExpenseRuleMap.get(ExpenseConstant.ExpenseTypeEnum.INTEREST.getCode());
        if (Objects.isNull(interestRule)) {
            repaymentExpenseResp.setShouldInterest(BigDecimal.ZERO);
            return;
        }
        // 费用类型名称
        String expenseTypeName = DictBizCache.getValue(DictBizEnum.GOODS_RULE_EXPENSE_TYPE, interestRule.getExpenseType());
        // 应还利息的费用公式
        String shouldInterestFormula = expenseTypeName + StringPool.EQUALS + interestRule.getFeeFormulaName();
        // 计算应还利息
        BigDecimal shouldInterest = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(interestRule), expenseRuleDTO).get(0);
        // 替换成具体值后的费用公式
        String shouldInterestValueFormula = RepaymentUtil.replaceFeeFieldOfValue(interestRule.getFeeFormula(), expenseRuleDTO) + StringPool.EQUALS + shouldInterest.toString() + "元";

        repaymentExpenseResp.setShouldInterest(shouldInterest);
        repaymentExpenseResp.setShouldInterestFormula(shouldInterestFormula);
        repaymentExpenseResp.setShouldInterestValueFormula(shouldInterestValueFormula);
    }

    public void calculateInterestAccrualDays(RepaymentExpenseResp repaymentExpenseResp, ExpenseRuleDTO expenseRuleDTO, LoanManageIou loanManageIou) {
        // 计息天数
        BigDecimal interestAccrualDays = expenseRuleDTO.getInterestAccrualDays();
        // 计息天数日期范围
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        String loanTime = loanManageIou.getLoanTime().format(formatter);
        String now = LocalDate.now().format(formatter);
        String interestAccrualDateRange = loanTime.concat(StringPool.TILDA).concat(now);
        repaymentExpenseResp.setInterestAccrualDay(interestAccrualDays);
        repaymentExpenseResp.setInterestAccrualDateRange(interestAccrualDateRange);
    }

    public void calculateOverdueInterest(RepaymentExpenseResp repaymentExpenseResp, Map<Integer, GoodsExpenseRule> goodsExpenseRuleMap, ExpenseRuleDTO expenseRuleDTO) {
        // 逾期利息
        GoodsExpenseRule overdueInterestRule = goodsExpenseRuleMap.get(ExpenseConstant.ExpenseTypeEnum.OVERDUE_INTEREST.getCode());
        if (Objects.isNull(overdueInterestRule)) {
            repaymentExpenseResp.setShouldOverdueInterest(BigDecimal.ZERO);
            return;
        }
        // 费用类型名称
        String expenseTypeName = DictBizCache.getValue(DictBizEnum.GOODS_RULE_EXPENSE_TYPE, overdueInterestRule.getExpenseType());
        // 计算逾期利息
        BigDecimal overdueInterest = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(overdueInterestRule), expenseRuleDTO).get(0);
        // 逾期利息的费用公式
        String overdueInterestFormula = expenseTypeName + StringPool.EQUALS + overdueInterestRule.getFeeFormulaName();
        // 替换成具体值后的逾期利息公式
        String overdueInterestValueFormula = RepaymentUtil.replaceFeeFieldOfValue(overdueInterestRule.getFeeFormula(), expenseRuleDTO);
        repaymentExpenseResp.setShouldOverdueInterest(overdueInterest);
        repaymentExpenseResp.setShouldOverdueInterestAccrualDay(expenseRuleDTO.getInterestAccrualDays());
        repaymentExpenseResp.setShouldOverdueInterestFormula(overdueInterestFormula);
        repaymentExpenseResp.setShouldOverdueInterestValueFormula(overdueInterestValueFormula);
    }

    /**
     * 计息天数及范围
     *
     * @param interestAccrualDays 计息天数
     * @param loanTime            借款时间
     * @return
     */
    public InterestAccrualDaysExpenseResp calculateInterestAccrualDays(BigDecimal interestAccrualDays, LocalDate loanTime) {
        InterestAccrualDaysExpenseResp resp = new InterestAccrualDaysExpenseResp();
        // 计息天数
        // 计息天数日期范围
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        String loanTimeStr = loanTime.format(formatter);
        String now = LocalDate.now().format(formatter);
        String interestAccrualDateRange = loanTimeStr.concat(StringPool.TILDA).concat(now);
        resp.setInterestAccrualDay(interestAccrualDays);
        resp.setInterestAccrualDateRange(interestAccrualDateRange);
        return resp;
    }


//    public void calculatePrepaymentServiceFee(InterestExpenseResp repaymentExpenseResp, Map<Integer, GoodsExpenseRule> goodsExpenseRuleMap, ExpenseRuleDTO expenseRuleDTO) {
//        // 提前还款手续费
//        GoodsExpenseRule prepaymentServiceFeeRule = goodsExpenseRuleMap.get(ExpenseConstant.ExpenseTypeEnum.PREPAYMENT_SERVICE_FEE.getCode());
//        if (Objects.isNull(prepaymentServiceFeeRule)) {
//            repaymentExpenseResp.setPrepaymentServiceFee(BigDecimal.ZERO);
//            return;
//        }
//        // 费用类型名称
//        String expenseTypeName = DictBizCache.getValue(DictBizEnum.GOODS_RULE_EXPENSE_TYPE, prepaymentServiceFeeRule.getExpenseType());
//        // 提前还款手续费公式
//        String prepaymentServiceFeeFormula = expenseTypeName + StringPool.EQUALS + prepaymentServiceFeeRule.getFeeFormulaName();
//        // 计算提前还款手续费
//        BigDecimal prepaymentServiceFee = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(prepaymentServiceFeeRule), expenseRuleDTO).get(0);
//        //提前还款手续费替换成具体值后的公式
//        String prepaymentServiceFeeValueFormula = RepaymentUtil.replaceFeeFieldOfValue(prepaymentServiceFeeRule.getFeeFormula(), expenseRuleDTO) + "=" + prepaymentServiceFee.toString() + "元";
//        ;
//
//        repaymentExpenseResp.setPrepaymentServiceFee(prepaymentServiceFee);
//        repaymentExpenseResp.setPrepaymentServiceFeeFormula(prepaymentServiceFeeFormula);
//        repaymentExpenseResp.setPrepaymentServiceFeeValueFormula(prepaymentServiceFeeValueFormula);
//    }
//
//    public void calculatePrepaymentDay(InterestExpenseResp repaymentExpenseResp, LocalDate repaymentTime, ExpenseRuleDTO expenseRuleDTO) {
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
//        BigDecimal prepaymentDays = expenseRuleDTO.getPrepaymentDays();
//        // 提前还款天数日期范围
//        String repaymentTimeStr = repaymentTime.format(formatter);
//        String prepaymentDayDateRange = LocalDate.now().format(formatter).concat(StringPool.TILDA).concat(repaymentTimeStr);
//        repaymentExpenseResp.setPrepaymentDay(prepaymentDays);
//        repaymentExpenseResp.setInterestAccrualDateRange(prepaymentDayDateRange);
//    }
//
//    /**
//     * 已还款 提前还款天数以及日期范围
//     *
//     * @param repaymentExpenseResp
//     * @param repaymentTime
//     * @param expenseRuleDTO
//     */
//    public void calculatePrepaymentDayByPayDetail(InterestExpenseResp repaymentExpenseResp, LocalDate repaymentTime, ExpenseRuleDTO expenseRuleDTO, LocalDate payDetailCreateTime) {
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
//        BigDecimal prepaymentDays = expenseRuleDTO.getPrepaymentDays();
//        // 提前还款天数 日期范围
//        String repaymentTimeStr = repaymentTime.format(formatter);
//        String payDetailCreateTimeStr = payDetailCreateTime.format(formatter);
//        String prepaymentDayDateRange = repaymentTimeStr.concat(StringPool.TILDA).concat(payDetailCreateTimeStr);
//        repaymentExpenseResp.setPrepaymentDay(prepaymentDays);
//        repaymentExpenseResp.setInterestAccrualDateRange(prepaymentDayDateRange);
//    }
//
//    public void calculateOverdueInterest(InterestExpenseResp repaymentExpenseResp, Map<Integer, GoodsExpenseRule> goodsExpenseRuleMap, ExpenseRuleDTO expenseRuleDTO) {
//        // 逾期利息
//        GoodsExpenseRule overdueInterestRule = goodsExpenseRuleMap.get(ExpenseConstant.ExpenseTypeEnum.OVERDUE_INTEREST.getCode());
//        if (Objects.isNull(overdueInterestRule)) {
//            repaymentExpenseResp.setShouldOverdueInterest(BigDecimal.ZERO);
//            return;
//        }
//        // 费用类型名称
//        String expenseTypeName = DictBizCache.getValue(DictBizEnum.GOODS_RULE_EXPENSE_TYPE, overdueInterestRule.getExpenseType());
//        // 计算逾期利息
//        BigDecimal overdueInterest = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(overdueInterestRule), expenseRuleDTO).get(0);
//        // 逾期利息的费用公式
//        String overdueInterestFormula = expenseTypeName + StringPool.EQUALS + overdueInterestRule.getFeeFormulaName();
//        // 替换成具体值后的逾期利息公式
//        String overdueInterestValueFormula = RepaymentUtil.replaceFeeFieldOfValue(overdueInterestRule.getFeeFormula(), expenseRuleDTO);
//        repaymentExpenseResp.setShouldOverdueInterest(overdueInterest);
//        //repaymentExpenseResp.setShouldInterestFormula(overdueInterestFormula);
//        //repaymentExpenseResp.setShouldInterestValueFormula(overdueInterestValueFormula);
//        repaymentExpenseResp.setShouldOverdueInterestAccrualDay(expenseRuleDTO.getInterestAccrualDays());
//        repaymentExpenseResp.setShouldOverdueInterestFormula(overdueInterestFormula);
//        repaymentExpenseResp.setShouldOverdueInterestValueFormula(overdueInterestValueFormula);
//    }
}
