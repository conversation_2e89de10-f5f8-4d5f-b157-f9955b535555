/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.loan.entity.LoanAlterationHistory;
import org.springblade.loan.service.ILoanAlterationHistoryInfoService;
import org.springblade.loan.service.ILoanAlterationHistoryService;
import org.springblade.loan.vo.LoanAlterationHistoryVO;
import org.springblade.loan.vo.LoanHistoryRepaymentPlanInfoVO;
import org.springblade.loan.vo.LoanHistoryRepaymentPlanVO;
import org.springblade.loan.wrapper.LoanAlterationHistoryWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 变更历史表 控制器
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_LOAN + CommonConstant.WEB_BACK + "/loanAlteration/loanAlterationHistory")
@Api(value = "变更历史表", tags = "变更历史表接口")
public class LoanAlterationHistoryController extends BladeController {

    private final ILoanAlterationHistoryService loanAlterationHistoryService;
    private final ILoanAlterationHistoryInfoService loanAlterationHistoryInfoService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入loanAlterationHistory")
    @PreAuth("hasPermission('loanAlteration:loanAlterationHistory:detail') or hasRole('administrator')")
    public R<LoanAlterationHistoryVO> detail(LoanAlterationHistory loanAlterationHistory) {
        LoanAlterationHistory detail = loanAlterationHistoryService.getOne(Condition.getQueryWrapper(loanAlterationHistory));
        return R.data(LoanAlterationHistoryWrapper.build().entityVO(detail));
    }

    /**
     * 自定义分页 变更历史表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入loanAlterationHistory")
    @PreAuth("hasPermission('loanAlteration:loanAlterationHistory:page') or hasRole('administrator')")
    public R<IPage<LoanAlterationHistoryVO>> page(LoanAlterationHistoryVO loanAlterationHistory, Query query) {
        IPage<LoanAlterationHistoryVO> pages = loanAlterationHistoryService.selectLoanAlterationHistoryPage(Condition.getPage(query), loanAlterationHistory);
        return R.data(pages);
    }

    /**
     * 新增 变更历史表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入loanAlterationHistory")
    @PreAuth("hasPermission('loanAlteration:loanAlterationHistory:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody LoanAlterationHistory loanAlterationHistory) {
        return R.status(loanAlterationHistoryService.save(loanAlterationHistory));
    }

    /**
     * 修改 变更历史表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入loanAlterationHistory")
    @PreAuth("hasPermission('loanAlteration:loanAlterationHistory:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody LoanAlterationHistory loanAlterationHistory) {
        return R.status(loanAlterationHistoryService.updateById(loanAlterationHistory));
    }

    /**
     * 新增或修改 变更历史表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入loanAlterationHistory")
    @PreAuth("hasPermission('loanAlteration:loanAlterationHistory:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody LoanAlterationHistory loanAlterationHistory) {
        return R.status(loanAlterationHistoryService.saveOrUpdate(loanAlterationHistory));
    }


    /**
     * 删除 变更历史表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('loanAlteration:loanAlterationHistory:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(loanAlterationHistoryService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 根据融资申请id查询变更历史记录
     */
    @GetMapping("/getAlterationHistoryByFinanceId")
    @ApiOperation("查询变更历史记录")
    public R<Map<Integer, List<LoanHistoryRepaymentPlanVO>>> getAlterationHistoryByFinanceId(@RequestParam Long financeId, Integer alterationType) {
        return R.data(loanAlterationHistoryInfoService.getAlterationHistoryByFinanceId(financeId, alterationType));
    }

    /**
     * 根据融资申请id查询变更历史记录2
     */
    @GetMapping("/getAlterationHistoryInfoByFinanceId")
    @ApiOperation("根据融资申请id查询变更历史记录2")
    public R<List<LoanHistoryRepaymentPlanInfoVO>> getAlterationHistoryInfoByFinanceId(@RequestParam Long financeId, Integer alterationType) {
        return R.data(loanAlterationHistoryInfoService.getAlterationHistoryInfoByFinanceId(financeId, alterationType));
    }
}
