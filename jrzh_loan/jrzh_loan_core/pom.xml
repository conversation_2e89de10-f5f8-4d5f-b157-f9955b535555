<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh_loan</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>
    <artifactId>jrzh_loan_core</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_loan_core_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_expense_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_riskmana_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_flow_process_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_feign_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_business_info</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_expenserelation</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_message</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_repayment</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_loan_common</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>
