package org.springblade.extension.handler.apply;

import org.springblade.extension.service.ILoanDelayApplyService;
import org.springblade.loan.dto.LoanDelayApplyDTO;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 展期申请
 */

public abstract class AbstractLoanDelayApplyHandler implements LoanDelayApplyHandler {
    @Resource
    private ILoanDelayApplyService loanDelayApplyService;

    @Override
    public boolean saveDelayApply(LoanDelayApplyDTO loanDelayApplyDTO) {
        return loanDelayApplyService.saveDelay(loanDelayApplyDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handle(LoanDelayApplyDTO loanDelayApplyDTO) {

        applyBefore(loanDelayApplyDTO);

        boolean delayApply = saveDelayApply(loanDelayApplyDTO);
        if (delayApply) {
            applySuccess(loanDelayApplyDTO);
        } else {
            applyFail(loanDelayApplyDTO);
        }
        applyAfter(loanDelayApplyDTO);
        return delayApply;
    }
}
