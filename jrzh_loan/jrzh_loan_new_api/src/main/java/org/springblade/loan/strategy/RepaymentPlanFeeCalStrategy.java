package org.springblade.loan.strategy;

import org.springblade.expense.handler.ExpenseRuleDTONodeHandler;
import org.springblade.product.common.dto.ExpenseRuleDTO;

/**
 * 还款试算费用策略类 需要填充还款试算时需要填充的字段
 *
 * @Author: <PERSON><PERSON>gchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 还款试算策略类（仅是还款计划需要提前生成，实际还款时还需要进行额外计算）
 * @Version: 1.0
 */
public interface RepaymentPlanFeeCalStrategy extends ExpenseRuleDTONodeHandler {

    @Override
    default ExpenseRuleDTO buildLoanApply(String req) {
        return ExpenseRuleDTONodeHandler.super.buildLoanApply(req);
    }

    @Override
    default ExpenseRuleDTO buildNormalRepayment(String req) {
        return ExpenseRuleDTONodeHandler.super.buildNormalRepayment(req);
    }

    @Override
    default ExpenseRuleDTO buildOverdueRepayment(String req) {
        return ExpenseRuleDTONodeHandler.super.buildOverdueRepayment(req);
    }

    @Override
    default ExpenseRuleDTO buildAdvanceRepayment(String req) {
        return ExpenseRuleDTONodeHandler.super.buildAdvanceRepayment(req);
    }

    @Override
    default ExpenseRuleDTO buildExtensionApply(String req) {
        return ExpenseRuleDTONodeHandler.super.buildExtensionApply(req);
    }

    @Override
    default ExpenseRuleDTO buildRedemptionConfirm(String req) {
        return ExpenseRuleDTONodeHandler.super.buildRedemptionConfirm(req);
    }

    @Override
    default ExpenseRuleDTO buildAdvanceSettle(String req) {
        return ExpenseRuleDTONodeHandler.super.buildAdvanceSettle(req);
    }

    @Override
    default ExpenseRuleDTO buildFinanceApply(String req) {
        return ExpenseRuleDTONodeHandler.super.buildFinanceApply(req);
    }

    @Override
    default ExpenseRuleDTO buildRedemptionApply(String req) {
        return ExpenseRuleDTONodeHandler.super.buildRedemptionApply(req);
    }

    @Override
    default ExpenseRuleDTO buildReturnBond(String req) {
        return ExpenseRuleDTONodeHandler.super.buildReturnBond(req);
    }

    @Override
    default ExpenseRuleDTO buildOverdueConsult(String req) {
        return ExpenseRuleDTONodeHandler.super.buildOverdueConsult(req);
    }

    @Override
    default ExpenseRuleDTO buildCargoSolve(String req) {
        return ExpenseRuleDTONodeHandler.super.buildCargoSolve(req);
    }

}
