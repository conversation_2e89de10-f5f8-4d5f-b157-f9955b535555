package org.springblade.loan.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.product.common.entity.Product;

/**
 * 逾期费用参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepaymentPlanOverDueFeeCal {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 产品id
     */
    private Long goodsId;
    /**
     * 融资编号
     */
    private String financeNo;
    /**
     * 还款计划id
     */
    private Long repaymentPlanId;
    /**
     * 还款计划id
     */
    private Long iouId;
    /**
     * 产品信息
     */
    private Product product;
    private LoanManageRepaymentPlan loanManageRepaymentPlan;
}
