package org.springblade.loan.utils;

import cn.hutool.core.lang.Assert;
import io.jsonwebtoken.lang.Collections;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.UtilityClass;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.RepaymentTypeEnum;
import org.springblade.common.utils.BorrowAndReturnUtils;
import org.springblade.common.utils.InterstBeforePrincipalUtils;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.vo.StagRecord;
import org.springblade.loan.vo.StagRecordVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@UtilityClass
public class LoanUtils {
    static int DECIMAL_SCALE = 9;
    static BigDecimal YEAR = BigDecimal.valueOf(CommonConstant.YEAR_DAY_COUNT);

    /**
     * 提前还款
     *
     * @param stagRecord 当月帐单
     * @param refund     还款金额
     * @param totalTerm  总期数
     * @param refundType 借款方式
     * @return
     */
    public RepaymentPlanCal advanceRefund(StagRecord stagRecord, BigDecimal refund, Integer totalTerm, RepaymentTypeEnum refundType, BigDecimal yearRate, LocalDate refundTime) {
        //当月月供
        BigDecimal monthlySupply = stagRecord.getMonthlySupply();
        Assert.isTrue(refund.compareTo(monthlySupply) > 0, "还款金额不得小于当前月供");
        //当月本息=当月月供+剩余还款本金
        BigDecimal totalRefund = stagRecord.getPrincipalSurplus().add(monthlySupply);
        //若还款后还剩余未还 则继续分期
        if (refund.compareTo(totalRefund) < 0) {
            //计算新的本金
            BigDecimal newPrincipal = totalRefund.subtract(refund);
            //计算新的分期
            Integer sumTerm = totalTerm - stagRecord.getTerm();
            BigDecimal yearRatePercent = yearRate.divide(new BigDecimal(100));
            return calculateByRefundType(newPrincipal, yearRatePercent, sumTerm, refundTime, refundType.getCode());
        }
        return null;
    }

    /**
     * 未传默认四舍五入
     *
     * @param principal  贷款本金
     * @param yearRate   年利率
     * @param sumTerm    总期数
     * @param startTime  开始借款时间
     * @param refundType 还款方式
     * @return
     */
    public RepaymentPlanCal calculateByRefundType(BigDecimal principal, BigDecimal yearRate, Integer sumTerm, LocalDate startTime, Integer refundType) {
        return calculateByRefundType(principal, yearRate, sumTerm, startTime, refundType, CommonConstant.NUMBER_STRATEGY);
    }

    /**
     * 根据还款方式分期
     *
     * @param principal     贷款本金
     * @param yearRate      年利率
     * @param sumTerm       总期数
     * @param startTime     开始借款时间
     * @param refundType    还款方式
     * @param moneyStrategy 金额策略
     * @return
     */
    public RepaymentPlanCal calculateByRefundType(BigDecimal principal, BigDecimal yearRate, Integer sumTerm, LocalDate startTime, Integer refundType, Integer moneyStrategy) {
        BigDecimal yearRatePercent = yearRate.divide(new BigDecimal("100"));
        //1：等额本息、2：等额本金、3：先息后本
        switch (refundType) {
            case 1:
                return LoanUtils.calculationEquivalentInterest(principal, yearRatePercent, sumTerm, startTime, moneyStrategy);
            case 2:
                return LoanUtils.calculationEquivalentPrincipal(principal, yearRatePercent, sumTerm, startTime, moneyStrategy);
            case 3:
                return LoanUtils.calculationInterstBeforePrincipal(principal, yearRatePercent, sumTerm, startTime, moneyStrategy);
            default:
        }
        return null;
    }

    /**
     * 计算等额本金 计算方式
     * 每月月供=（贷款本金/还款月数）+（本金—已归还本金累计额）×每月利率
     * 每月
     *
     * @param investMoney 总贷款本金
     * @param yearRate    年利率
     * @param sumTerm     还款总月数
     * @return
     */
    public RepaymentPlanCal calculationEquivalentPrincipal(BigDecimal investMoney, BigDecimal yearRate, Integer sumTerm, LocalDate startTime, Integer moneyStrategy) {
        RepaymentPlanCal repaymentPlan = new RepaymentPlanCal();
        //月利率=年利率/12
        BigDecimal monthRate = EquivalentPrincipalUtils.calculationMonthRate(yearRate);
        //本金 向上取整
        BigDecimal investMoneyTotal = investMoney.setScale(2, moneyStrategy);
        //方便计算
        BigDecimal sum = new BigDecimal(sumTerm);
        //月供本金
        BigDecimal monthPrincipal = EquivalentPrincipalUtils.calculationMonthPrincipal(investMoneyTotal, sum, moneyStrategy);
        //已还本金 初始化0
        BigDecimal refundPrincipal = BigDecimal.ZERO;
        //已还利息
        BigDecimal refundInvest = BigDecimal.ZERO;
        //本金余额
        BigDecimal principalSurplus = investMoneyTotal;//初始值为本金
        //首月还款时间
        LocalDate firstReFundTime = getFirstRefundTime(startTime);
        //本期开始时间
        LocalDate currentStartTime = startTime;
        List<StagRecordVO> list = new ArrayList<>();
        for (Integer i = 1; i < sumTerm + 1; i++) {
            //月供
            BigDecimal monthSupply = EquivalentPrincipalUtils.calculationMonthSupply(investMoneyTotal, monthRate, sum, refundPrincipal, moneyStrategy);
            //月供利息
            BigDecimal monthInterest = EquivalentPrincipalUtils.calculationMonthInterest(monthSupply, monthPrincipal);
            //本金余额
            principalSurplus = principalSurplus.subtract(monthPrincipal);
            StagRecordVO stagRecord = new StagRecordVO();
            stagRecord.setTerm(i + "");
            stagRecord.setStartTime(currentStartTime);
            currentStartTime = firstReFundTime;
            stagRecord.setMonthlyPrincipal(monthPrincipal);
            stagRecord.setMonthlyInterest(monthInterest);
            stagRecord.setMonthlySupply(monthSupply);
            stagRecord.setPrincipalSurplus(principalSurplus.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : principalSurplus);
            stagRecord.setRefundTime(firstReFundTime);
            stagRecord.setReductionInterest(BigDecimal.ZERO);
            //计算已还本金
            refundPrincipal = refundPrincipal.add(monthPrincipal);
            refundInvest = refundInvest.add(monthInterest);
            //加一个月
            firstReFundTime = firstReFundTime.plusMonths(1);
            list.add(stagRecord);
        }

        //对最后一期进行金额调整
        StagRecordVO lastTerm = list.get(list.size() - 1);
        repaymentPlan.setExpireTime(lastTerm.getRefundTime());
        EquivalentPrincipalUtils.adjustLastTerm(lastTerm, investMoney, refundPrincipal);
        //重新统计
        StagRecordVO stagRecordVO = calTotalData(list, investMoney);
        list.add(stagRecordVO);
        BigDecimal showYear = calculateYearRate(yearRate);

        repaymentPlan.setStagRecords(list);
        repaymentPlan.setTotal(investMoneyTotal.add(stagRecordVO.getMonthlySupply()));
        repaymentPlan.setYearRate(showYear);
        repaymentPlan.setDayRate(showDayRate(showYear));
        repaymentPlan.setLoanTermUnit(GoodsEnum.TERM.getCode());
        return repaymentPlan;
    }

    /**
     * 前端展示的日利率
     *
     * @param year
     * @return
     */
    private BigDecimal showDayRate(BigDecimal year) {
        return year.divide(BigDecimal.valueOf(CommonConstant.YEAR_DAY_COUNT), 3, CommonConstant.NUMBER_STRATEGY);
    }

    /**
     * 具体规则：
     * 该产品还款日按照借据维度进行确定，每笔借据都有自己的还款日，规则如下：↵
     * 实际大小
     * （1）放款日为1-28日时，还款口米用对日原则；↵
     * （2）放款日为29-31日时，还款日固定为28日；↵
     * （3）每笔借据都按照该原则执行（即每笔独立还款日）。↵
     * （4）最后一期还款日：采用扣款日原则。↵
     * 举例：1.31放款，共3期，还款日为28日，则每期还款日分别为2.28、3.28、4.28。
     *
     * @param startTime
     * @return
     */
    public LocalDate getFirstRefundTime(LocalDate startTime) {
        return getTimeByTerm(startTime, 1);
    }

    /**
     * * 说明
     * * 借款结束日期为自动计算值
     * * 1.比如借款开始日期为3月24号，一共3期，则借款结束日期：6月24
     * * 2.比如借款开始日期为3月29号，一共3期，则借款结束日期：6月28号
     *
     * @param startTime 开始日期
     * @param term      期数
     * @return 结束时间
     */
    public LocalDate getEndTime(LocalDate startTime, Integer term) {
        return getTimeByTerm(startTime, term);
    }

    /**
     * @param startTime
     * @param term
     * @return
     */
    private LocalDate getTimeByTerm(LocalDate startTime, Integer term) {
        //判断借款时间是否大于28天 则开始为28号
        int dayOfMonth = startTime.getDayOfMonth();
        if (dayOfMonth > 28) {
            //修改为28号
            startTime = startTime.withDayOfMonth(28);
        }
        return startTime.plusMonths(term);
    }

    /**
     * 等额本息 :
     * 计算 每月还款金额= [贷款本金×月利率×（1+月利率）^还款月数]÷[（1+月利率）^还款月数－1]
     *
     * @param investMoney 总贷款本金
     * @param yearRate    年利率
     * @param sumTerm     还款总月数
     * @return
     */
    public RepaymentPlanCal calculationEquivalentInterest(BigDecimal investMoney, BigDecimal yearRate, Integer sumTerm, LocalDate startTime, Integer moneyStrategy) {
        RepaymentPlanCal repaymentPlan = new RepaymentPlanCal();
        //月利率=年利率/12
        BigDecimal monthRate = EquivalentInterestUtils.calculationMonthRate(yearRate);
        //本金 按策略
        BigDecimal investMoneyTotal = investMoney.setScale(2, moneyStrategy);
        //每月月供
        BigDecimal monthlySupply = EquivalentInterestUtils.calculationMonthSupply(investMoneyTotal, monthRate, sumTerm, moneyStrategy);
        //剩余还款本金 初始值为总本金
        BigDecimal principalSurplus = investMoneyTotal;
        //已归还的本息
        BigDecimal refunded = BigDecimal.ZERO;
        //已还利息
        BigDecimal refundIntest = BigDecimal.ZERO;
        //已归还本金
        BigDecimal refundPrincipal = BigDecimal.ZERO;
        //本期开始时间
        LocalDate currentStartTime = startTime;
        //首月还款时间
        LocalDate firstReFundTime = getFirstRefundTime(startTime);
        List<StagRecordVO> stagRecords = new ArrayList<>();
        for (Integer i = 1; i < sumTerm + 1; i++) {
            StagRecordVO stagRecord = new StagRecordVO();
            //每月利息
            BigDecimal monthInterest = EquivalentInterestUtils.calculationMonthInterest(principalSurplus, monthRate, moneyStrategy);
            //月供本金=每月本息-当月利息
            BigDecimal monthlyPrincipal = monthlySupply.subtract(monthInterest);
            //剩余待还本金=当前待还本金-月供本金
            principalSurplus = principalSurplus.subtract(monthlyPrincipal);
            stagRecord.setTerm(i + "");
            stagRecord.setStartTime(currentStartTime);
            currentStartTime = firstReFundTime;
            stagRecord.setMonthlyInterest(monthInterest);
            stagRecord.setMonthlyPrincipal(monthlyPrincipal);
            stagRecord.setMonthlySupply(monthlySupply);
            stagRecord.setReductionInterest(BigDecimal.ZERO);
            stagRecord.setPrincipalSurplus(principalSurplus.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : principalSurplus);
            stagRecord.setRefundTime(firstReFundTime);
            stagRecords.add(stagRecord);
            refundPrincipal = refundPrincipal.add(monthlyPrincipal);
            refundIntest = refundIntest.add(monthInterest);
            //当前已还
            refunded = refunded.add(monthlySupply);
            //加一个月
            firstReFundTime = firstReFundTime.plusMonths(1);
        }
        //对最后一期进行金额调整
        StagRecordVO lastTerm = stagRecords.get(stagRecords.size() - 1);
        repaymentPlan.setExpireTime(lastTerm.getRefundTime());
        EquivalentInterestUtils.adjustLastTerm(lastTerm, investMoney, refundPrincipal);

        //调整后进行汇总
        StagRecordVO recordVO = calTotalData(stagRecords, investMoney);
        stagRecords.add(recordVO);

        repaymentPlan.setStagRecords(stagRecords);
        repaymentPlan.setTotal(investMoneyTotal.add(recordVO.getMonthlySupply()));
        BigDecimal showYear = calculateYearRate(yearRate);
        repaymentPlan.setYearRate(showYear);
        repaymentPlan.setDayRate(showDayRate(showYear));
        repaymentPlan.setLoanTermUnit(GoodsEnum.TERM.getCode());
        return repaymentPlan;
    }

    /**
     * 调整最后月份值与总还款
     *
     * @param lastTerm     最后一期
     * @param adjustAmount 需要校准的金额
     * @param reduceAmount 累计金额
     */
    private AdjustAmount adjustLastTerm(StagRecordVO lastTerm, BigDecimal adjustAmount, BigDecimal reduceAmount) {
        if (true) {
            throw new UnsupportedOperationException("TODO");
        }
        return null;
    }

    /**
     * 汇总
     *
     * @param stagRecords
     * @return
     */
    public static StagRecordVO calTotalData(List<StagRecordVO> stagRecords, BigDecimal investMoney) {
        StagRecordVO stagRecordTotal = new StagRecordVO();
        BigDecimal monthlySupplyTotal = BigDecimal.ZERO;
        BigDecimal monthlyInterestTotal = BigDecimal.ZERO;
        BigDecimal monthlyPrincipalTotal = BigDecimal.ZERO;
        BigDecimal monthlyPrincipalSurplus = investMoney;
        for (StagRecordVO stagRecord : stagRecords) {
            BigDecimal surplus = investMoney.subtract(stagRecord.getMonthlyPrincipal());
            monthlySupplyTotal = monthlySupplyTotal.add(stagRecord.getMonthlySupply());
            monthlyInterestTotal = monthlyInterestTotal.add(stagRecord.getPlanInterest());
            monthlyPrincipalTotal = monthlyPrincipalTotal.add(stagRecord.getMonthlyPrincipal());
            //计算本金余额
            stagRecord.setPrincipalSurplus(surplus);
            monthlyPrincipalSurplus = monthlyPrincipalSurplus.subtract(surplus);
        }
        stagRecordTotal.setMonthlySupply(monthlySupplyTotal);
        stagRecordTotal.setMonthlyInterest(monthlyInterestTotal);
        stagRecordTotal.setMonthlyPrincipal(monthlyPrincipalTotal);
        return stagRecordTotal;
    }

    /**
     * 先息后本 :
     * 每月利息计算 贷款本金*月利息
     *
     * @param investMoney 总贷款本金
     * @param yearRate    年利率
     * @param sumTerm     还款总月数
     * @return
     */
    public RepaymentPlanCal calculationInterstBeforePrincipal(BigDecimal investMoney, BigDecimal yearRate, Integer sumTerm, LocalDate startTime, Integer moneyStrategy) {
        RepaymentPlanCal repaymentPlan = new RepaymentPlanCal();
        //本金 向上取整
        BigDecimal investMoneyTotal = investMoney.setScale(2, moneyStrategy);
        BigDecimal monthRate = InterstBeforePrincipalUtils.calculationMonthRate(yearRate);
        //每月利息
        BigDecimal monthInterest = InterstBeforePrincipalUtils.calculationMonthInterest(investMoney, monthRate).setScale(2, moneyStrategy);
        //已归还的本息
        BigDecimal refunded = new BigDecimal(0);
        //首月还款时间
        LocalDate firstReFundTime = getFirstRefundTime(startTime);
        //本期开始时间
        LocalDate currentStartTime = startTime;
        List<StagRecordVO> stagRecords = new ArrayList<>();
        for (Integer i = 1; i < sumTerm + 1; i++) {
            StagRecordVO stagRecord = new StagRecordVO();
            BigDecimal monthSupply = InterstBeforePrincipalUtils.calculationMonthSupply(investMoney, monthInterest, yearRate, sumTerm, i);
            BigDecimal monthPrincipal = null;
            BigDecimal principalSurplus = null;
            if (!i.equals(sumTerm)) {
                monthPrincipal = BigDecimal.ZERO;
                principalSurplus = investMoney;
            } else {
                monthPrincipal = investMoneyTotal;
                principalSurplus = BigDecimal.ZERO;
            }
            stagRecord.setMonthlySupply(monthSupply);
            stagRecord.setTerm(i + "");
            stagRecord.setStartTime(currentStartTime);
            currentStartTime = firstReFundTime;
            stagRecord.setMonthlyInterest(monthInterest);
            stagRecord.setPrincipalSurplus(principalSurplus);
            stagRecord.setMonthlyPrincipal(monthPrincipal);
            stagRecord.setRefundTime(firstReFundTime);
            stagRecord.setReductionInterest(BigDecimal.ZERO);
            stagRecords.add(stagRecord);
            refunded = refunded.add(monthSupply);
            //加一个月
            firstReFundTime = firstReFundTime.plusMonths(1);
        }
        StagRecordVO lastTerm = stagRecords.get(stagRecords.size() - 1);
        repaymentPlan.setExpireTime(lastTerm.getRefundTime());
        StagRecordVO stagRecord = new StagRecordVO();
        stagRecord.setMonthlySupply(refunded);
        stagRecord.setMonthlyInterest(refunded.subtract(investMoneyTotal));
        stagRecord.setMonthlyPrincipal(investMoneyTotal);
        //调整后进行汇总
        StagRecordVO recordVO = calTotalData(stagRecords, investMoney);
        stagRecords.add(recordVO);
        BigDecimal showYear = calculateYearRate(yearRate);


        repaymentPlan.setStagRecords(stagRecords);
        repaymentPlan.setTotal(investMoneyTotal.add(recordVO.getMonthlySupply()));
        repaymentPlan.setYearRate(showYear);
        repaymentPlan.setDayRate(showDayRate(showYear));
        repaymentPlan.setLoanTermUnit(GoodsEnum.TERM.getCode());
        return repaymentPlan;
    }

    private List<StagRecordVO> listVO(List<StagRecord> stagRecord) {
        if (Collections.isEmpty(stagRecord)) {
            return null;
        }
        List<StagRecordVO> list = new ArrayList<>(stagRecord.size());
        stagRecord.forEach(e -> {
            StagRecordVO vo = new StagRecordVO();
            vo.setTerm(concatTerm(e.getTerm()));
            vo.setMonthlySupply(e.getMonthlySupply());
            vo.setRefundTime(e.getRefundTime());
            vo.setMonthlyPrincipal(e.getMonthlyPrincipal());
            vo.setMonthlyInterest(e.getMonthlyInterest());
            list.add(vo);
        });
        return list;
    }

    private String concatYuan(BigDecimal value) {
        return value + "元";
    }

    private String concatTerm(Integer value) {
        return value + "期";
    }

    private static BigDecimal calculateYearRate(BigDecimal yearRate) {
        return yearRate.multiply(new BigDecimal(100)).setScale(2, CommonConstant.NUMBER_STRATEGY);
    }

    /**
     * 计算 日利率:
     * 日利率=年利率/365*100 保留16位小数 向上取整
     *
     * @param yearRate 日利率
     * @return 日利率
     */
    public static BigDecimal calculationDayRate(BigDecimal yearRate) {
        return calculationDayRate(yearRate, 5);
    }

    /**
     * 计算 日利率:
     * 日利率=年利率/365*100 保留16位小数 向上取整
     *
     * @param yearRate 日利率
     * @return 日利率
     */
    public static BigDecimal calculationDayRate(BigDecimal yearRate, Integer scale) {
        return yearRate.divide(YEAR, DECIMAL_SCALE, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 计算 日利率:
     * 日利率=年利率/365*100 保留5位小数 向上取整
     *
     * @param yearRate 日利率/100
     * @return 日利率
     */
    public static BigDecimal calculationDayRateTwo(BigDecimal yearRate) {
        return yearRate.divide(YEAR, DECIMAL_SCALE, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(5, RoundingMode.HALF_UP);
    }

    /**
     * 随借随还
     *
     * @param investMoney
     * @param yearRate
     * @return
     */
    public static Map<String, Object> calculationBorrowAndReturn(BigDecimal investMoney, BigDecimal yearRate, Integer moneyStrategy) {
        Map<String, Object> map = new HashMap<>();
//		BigDecimal monthRate = BorrowAndReturnUtils.calculationMonthRate(yearRate);
        //日利率
        BigDecimal dayRate = BorrowAndReturnUtils.calculationDayRate(yearRate, moneyStrategy);
        StagRecord stagRecord = new StagRecord();
//		//年利息=本金*年利率
//		BigDecimal refundInvest = investMoney.multiply(yearRate);
//		//月利息=本金*月利率
//		BigDecimal monthInvest = investMoney.multiply(yearRate);
        //日利息=本金*日利率
        BigDecimal dayInvest = investMoney.multiply(dayRate);
        map.put("investMoney", investMoney);
        map.put("dayInvest", dayInvest);

        return map;
    }

    /**
     * 随借随还计算 当前本息
     *
     * @param investMoney 借款本金
     * @param yearRate    年利率
     * @param startTime   开始时间
     * @param refundTime  还款时间
     * @return
     */
    public static RepaymentPlanCal calculatePrincipalAndInvest(BigDecimal investMoney, BigDecimal yearRate, LocalDate startTime, LocalDate refundTime, Integer moneyStrategy, Integer interestDay) {
        RepaymentPlanCal repaymentPlanCal = new RepaymentPlanCal();
        //保留2位小数
        BigDecimal monthlyInterest = BorrowAndReturnUtils.calculatePrincipalAndInvest(investMoney, yearRate.divide(new BigDecimal(100)), startTime, refundTime.plusDays(interestDay), 2, moneyStrategy);
        StagRecordVO stagRecordVO = new StagRecordVO();
        stagRecordVO.setRefundTime(refundTime);
        stagRecordVO.setMonthlySupply(investMoney.add(monthlyInterest));
        stagRecordVO.setMonthlyPrincipal(investMoney);
        stagRecordVO.setMonthlyInterest(monthlyInterest);
        stagRecordVO.setStartTime(startTime);
        stagRecordVO.setTerm("1");
        stagRecordVO.setReductionInterest(BigDecimal.ZERO);
        repaymentPlanCal.setStagRecords(java.util.Collections.singletonList(stagRecordVO));
        repaymentPlanCal.setYearRate(yearRate);
        repaymentPlanCal.setDayRate(showDayRate(yearRate));
        repaymentPlanCal.setTotal(stagRecordVO.getMonthlySupply());
        repaymentPlanCal.setExpireTime(refundTime);
        repaymentPlanCal.setLoanTermUnit(GoodsEnum.DAY.getCode());
        return repaymentPlanCal;
    }


    /**
     * 获取总期数
     *
     * @param stagRecords
     * @param loadTermUnit
     * @return
     */
    public static Integer getTermCount(List<StagRecordVO> stagRecords, Integer loadTermUnit) {
        return GoodsEnum.TERM.getCode().equals(loadTermUnit) ? stagRecords.size() - 1 : 1;
    }

    public static BigDecimal getDailyInterestRate(BigDecimal yearRate, Integer scale) {
       return yearRate.divide(YEAR,scale,CommonConstant.NUMBER_STRATEGY);
    }

//	public static void main(String[] args) {
//		BigDecimal 本金 = BigDecimal.valueOf(500);
//		BigDecimal 年利率 = BigDecimal.valueOf(365);
//		LocalDate 借款日 = LocalDate.now();
//		LocalDate 还款日 = LocalDate.now();
//		Map<String, Object> stringObjectMap = calculatePrincipalAndInvest(本金, 年利率, 借款日, 还款日, 5);
//		System.out.println(stringObjectMap);
//	}

    /**
     * 金额校准类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    protected class AdjustAmount {
        @ApiModelProperty(value = "校准字段1")
        private BigDecimal arg1;
        @ApiModelProperty(value = "校准字段2")
        private BigDecimal arg2;
    }
}
