package org.springblade.loan.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.resource.entity.IncomeDetail;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 分期记录
 *
 * <AUTHOR>
 */
@Data
public class StagRecordVO implements Serializable {
    /**
     * 期数
     */
    private String term;
    /**
     * 月供/应还总额
     */
    private BigDecimal monthlySupply;
    /**
     * 月供本金/应还本金
     */
    private BigDecimal monthlyPrincipal;
    /**
     * 月供利息/应还利息
     */
    private BigDecimal monthlyInterest;
    /**
     * 贴息
     */
    private BigDecimal discountAmount;

    public BigDecimal getRealMonthlyInterest() {
        if (ObjectUtil.isEmpty(discountAmount)) {
            return monthlyInterest;
        }
        return monthlyInterest.subtract(discountAmount);
    }

    /**
     * 本金余额
     */
    private BigDecimal principalSurplus;
    /**
     * 本期开始时间
     */
    private LocalDate startTime;
    /**
     * 回款日期
     */
    private LocalDate refundTime;
    /**
     * 动态费用
     */
    private List<RepaymentPlanFee> planFeeLists;
    //应还总额 包含动态费用
    private BigDecimal totalAmount;
    /**
     * 减免累计利息
     */
    private BigDecimal reductionInterest;
    private BigDecimal planInterest;

    public BigDecimal getPlanInterest() {
        if (reductionInterest == null) {
            return monthlyInterest;
        }
        BigDecimal add = monthlyInterest.add(reductionInterest);
        return add.compareTo(BigDecimal.ZERO) > 0 ? add : BigDecimal.ZERO;
    }

    /**
     * 动态费用总金额
     */
    private BigDecimal getTotalPlanFeeList() {
        if (CollUtil.isEmpty(planFeeLists)) {
            return BigDecimal.ZERO;
        }
        return planFeeLists.stream()
                .filter(e -> ObjectUtil.isNotEmpty(e.getAmount()))
                .map(RepaymentPlanFee::getPlanNeePayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalAmount() {
        if (ObjectUtil.isNull(monthlySupply)) {
            return BigDecimal.ZERO;
        }
        return monthlySupply.add(getTotalPlanFeeList());
    }

    /**
     * 对同种动态费用做统计，方便前端计算
     */
    private Map<Long, BigDecimal> planfeeMap;
    /**
     * 利息的减免的减免记录
     */
    public List<IncomeDetail> reductionInterestList;
}
