package org.springblade.loan.strategy;

import org.springblade.loan.contant.RepaymentPlanCalEnum;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanCalReq;

/**
 * 还款试算策略类
 *
 * @Author: z<PERSON>gchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 还款试算策略类
 * @Version: 1.0
 */
public interface RepaymentPlanCalStrategy {
    /**
     * 支持的还款方式
     *
     * @return
     */
    RepaymentPlanCalEnum support();

    /**
     * 还款试算节点
     */
    RepaymentPlanCal calculate(RepaymentPlanCalReq repaymentPlanCalReq);
}
