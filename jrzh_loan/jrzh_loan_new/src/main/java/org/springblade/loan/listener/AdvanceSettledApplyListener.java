package org.springblade.loan.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.enums.ProcessStatusEnum;
import org.springblade.common.enums.ProcessTypeEnum;
import org.springblade.common.enums.rabbitmq.MessageTypeEnum;
import org.springblade.common.utils.rabbitmqutils.RabbitMqCommonUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.loan.dto.AdvanceSettledApplyDTO;
import org.springblade.loan.entity.AdvanceSettledApply;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.loan.enums.AdvanceSettledEnum;
import org.springblade.loan.mapper.AdvanceSettledApplyMapper;
import org.springblade.message.enums.MessageSceneEnum;
import org.springblade.message.utils.MessageNotifyUtil;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 提前结清监听器申请流程
 *
 * <AUTHOR>
 */

@RequiredArgsConstructor
@Component("advanceSettledApplyListener")
public class AdvanceSettledApplyListener implements ExecutionListener {

    private final AdvanceSettledApplyMapper advanceSettledApplyMapper;
    private final FinanceApplyMapper financeApplyMapper;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final RabbitMsgSender rabbitMsgSender;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();

        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        Object object = delegateExecution.getVariable(WfProcessConstant.ADVANCE_SETTLED);
        AdvanceSettledApplyDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(object), AdvanceSettledApplyDTO.class);
        if (StringUtil.equals(processTerminal, Boolean.TRUE.toString()) || StringUtil.equals(processTerminal, "withdraw")) {
            //终止
            handlerProcessTerminal(delegateExecution, dto);
        } else {
            //通过
            handlerSuccess(delegateExecution, dto);
        }

    }


    private void handlerSuccess(DelegateExecution delegateExecution, AdvanceSettledApplyDTO dto) {

        String iouNo = dto.getLoanManageIouVO().getIouNo();
        AdvanceSettledApply advance = advanceSettledApplyMapper.selectOne(Wrappers.<AdvanceSettledApply>lambdaQuery().eq(AdvanceSettledApply::getIouNo, iouNo));
        BeanUtil.copyProperties(advance, dto);
        dto.setIouNo(iouNo);
        String processInstanceId = dto.getProcessInstanceId();

        Map<String, Object> variables = delegateExecution.getVariables();
        String prepaymentServiceFeeStr = String.valueOf(variables.get("prepaymentServiceFee"));
        BigDecimal serviceFee = new BigDecimal(String.valueOf(StringUtil.isBlank(prepaymentServiceFeeStr) ? "0" : prepaymentServiceFeeStr));
        String interestStr = String.valueOf(variables.get("interest"));
        BigDecimal interest = new BigDecimal(String.valueOf(StringUtil.isBlank(interestStr) ? "0" : interestStr));
        dto.setInterest(interest);
        dto.setPrepaymentServiceFee(serviceFee);
        dto.setTotalAmount(dto.getSurplusPrincipal().add(dto.getInterest()).add(serviceFee));
        dto.setStatus(AdvanceSettledEnum.PASSED.getCode());
        Object timeNum = variables.get("timeNum");
        Object unity = variables.get("unity");
        if (ObjectUtil.isEmpty(timeNum)) {
            dto.setTimeNum(null);
        } else {
            dto.setTimeNum(Integer.valueOf(String.valueOf(timeNum)));
        }
        if (ObjectUtil.isEmpty(unity)) {
            dto.setUnity(null);
        } else {
            dto.setUnity(Integer.valueOf(String.valueOf(unity)));
        }
        //订单支付时效
        LocalDateTime endPayDate = getPayDeadLine(dto);
        dto.setPayDeadLine(endPayDate);
        //动态费用 仅拿出提前结清类型的 并保存到费用计划中
        List<RepaymentPlanFee> repaymentPlanFees = dto.getRepaymentPlanFees();
        if (CollUtil.isNotEmpty(repaymentPlanFees)) {
            for (RepaymentPlanFee repaymentPlanFee : repaymentPlanFees) {
                if (ExpenseConstant.FeeNodeEnum.ADVANCE_SETTLE.getCode().equals(repaymentPlanFee.getFeeNode())) {
                    repaymentPlanFee.setFix(CommonConstant.YES);
                    repaymentPlanFee.setFixAmount(serviceFee);
                }
            }
            String repaymentPlanFeesStr = JSON.toJSON(repaymentPlanFees).toString();
            dto.setPlanFee(repaymentPlanFeesStr);
        }
        //更新提前结清列表
        advanceSettledApplyMapper.updatePassTimeAndPayDeadLineTime(dto.getId(), LocalDateTime.now(), endPayDate);
        advanceSettledApplyMapper.updateById(dto);
        String financeNo = dto.getFinanceNo();
        FinanceApply financeApply = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeNo));
        financeApply.setStatus(FinanceApplyStatusEnum.ADVANCE_SETTLED_UN_PAY.getCode());
        //更新融资表状态
        financeApplyMapper.updateById(financeApply);
        //发送mq定时任务
        sendMq(dto);
        //更新流程进度
        businessProcessProgressService.updateBusinessProcessProgress(financeApply.getId(), null, ProcessTypeEnum.RECEIVE_ADVANCE_SETTLED_APPLY.getCode(), processInstanceId, financeApply.getUserId(), ProcessStatusEnum.FINISH.getCode());
        //提前结清-审核通过
        //根据业务类型查找对应消息模板，根据模板发送消息
        MessageNotifyUtil.notifyByTemplate(financeApply.getUserId(), MessageSceneEnum.Early_Settlement_Suc.getValue());

    }

    /**
     * 发送定时任务
     *
     * @param dto
     */
    private void sendMq(AdvanceSettledApplyDTO dto) {
        if (dto.getTimeNum() != null
                && dto.getTimeNum() != 0
                && dto.getUnity() != null
                && dto.getUnity() != 0) {

            String tenantId = AuthUtil.getTenantId();
            if (StringUtil.isBlank(tenantId)) {
                throw new ServiceException("租户号不能为空");
            }
            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
                    .messageType(MessageTypeEnum.ADVANCE_SETTLED_UN_PAY.getDesc())
                    .tenantId(tenantId)
                    .msg(dto.getFinanceNo())
                    .seconds(RabbitMqCommonUtils.calculateDay(dto.getTimeNum(), dto.getUnity()))
                    .extendParam(new HashMap<>())
                    .build());
        }
    }

    /**
     * 获取最后支付时间
     *
     * @param dto
     * @return
     */
    private LocalDateTime getPayDeadLine(AdvanceSettledApplyDTO dto) {
        if (dto.getTimeNum() != null
                && dto.getTimeNum() != 0
                && dto.getUnity() != null
                && dto.getUnity() != 0) {
            Integer rabbitTime = RabbitMqCommonUtils.calculateDay(dto.getTimeNum(), dto.getUnity());
            long nowTime = System.currentTimeMillis() / 1000L;
            Long endPay = Long.valueOf(rabbitTime) + nowTime;
            LocalDateTime endPayDate = LocalDateTime.ofEpochSecond(endPay, 0, ZoneOffset.ofHours(8));
            return endPayDate;
        } else {
            return null;
        }
    }

    private void handlerProcessTerminal(DelegateExecution delegateExecution, AdvanceSettledApplyDTO dto) {
        String iouNo = dto.getLoanManageIouVO().getIouNo();
        AdvanceSettledApply advance = advanceSettledApplyMapper.selectOne(Wrappers.<AdvanceSettledApply>lambdaQuery().eq(AdvanceSettledApply::getIouNo, iouNo));
        BeanUtil.copyProperties(advance, dto);
        dto.setIouNo(iouNo);
        String processInstanceId = dto.getProcessInstanceId();

        dto.setStatus(AdvanceSettledEnum.REJECTED.getCode());
        advanceSettledApplyMapper.updateById(dto);
        String financeNo = dto.getFinanceNo();
        FinanceApply financeApply = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeNo));
        financeApply.setStatus(FinanceApplyStatusEnum.UN_SETTLED.getCode());
        financeApplyMapper.updateById(financeApply);

        //更新流程进度
        businessProcessProgressService.updateBusinessProcessProgress(financeApply.getId(), null, ProcessTypeEnum.RECEIVE_ADVANCE_SETTLED_APPLY.getCode(), processInstanceId, financeApply.getUserId(), ProcessStatusEnum.TERMINAL.getCode());
        //提前结清-审核未通过
//		MessageNotifyUtil.notifyByTemplate(financeApply.getUserId(), MessageSceneEnum.Early_Settlement_Fail.getValue());

    }
}
