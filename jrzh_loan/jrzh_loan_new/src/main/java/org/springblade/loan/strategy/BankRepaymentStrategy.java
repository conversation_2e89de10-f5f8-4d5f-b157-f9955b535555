package org.springblade.loan.strategy;

import org.springblade.loan.contant.BankRepaymentEnum;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanCalReq;

/**
 * 银联策略类
 *
 * @Author: <PERSON><PERSON>gchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 还款试算策略类
 * @Version: 1.0
 */
public interface BankRepaymentStrategy {
    /**
     * 支持的银联还款方式
     *
     * @return
     */
    BankRepaymentEnum support();

    /**
     * 还款试算
     */
    RepaymentPlanCal calculate(RepaymentPlanCalReq repaymentPlanCalReq);
}
