package org.springblade.loan.strategy;

import lombok.RequiredArgsConstructor;
import org.springblade.loan.contant.RepaymentPlanCalEnum;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanCalReq;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 银联还款试算策略类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 银联还款试算策略类
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class BankRepaymentPlanCalStrategyImpl implements RepaymentPlanCalStrategy {
    private final List<BankRepaymentStrategy> bankRepaymentStrategyList;

    @Override
    public RepaymentPlanCalEnum support() {
        return RepaymentPlanCalEnum.BANK;
    }

    @Override
    public RepaymentPlanCal calculate(RepaymentPlanCalReq repaymentPlanCalReq) {
        //判断银行以线上还是线下进行试算 当前采用系统 待接入后换成银联
        for (BankRepaymentStrategy bankRepaymentStrategy : bankRepaymentStrategyList) {
            if (bankRepaymentStrategy.support().getType().equals(repaymentPlanCalReq.getPayMethod())) {
                return bankRepaymentStrategy.calculate(repaymentPlanCalReq);
            }
        }
        throw new UnsupportedOperationException("策略未实现");
    }

}
