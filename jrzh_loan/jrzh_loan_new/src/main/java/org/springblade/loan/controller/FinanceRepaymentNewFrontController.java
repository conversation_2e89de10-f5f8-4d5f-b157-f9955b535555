package org.springblade.loan.controller;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.loan.dto.RepaymentPlanDTO;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.loan.service.IRepaymentBizService;
import org.springblade.loan.service.IRepaymentPlanFeeService;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.system.utils.UserUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 费用还款新/试算
 *
 * <AUTHOR>
 * @module 费用还款新/试算
 * @date 2022-03-30
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_BUSINESS + CommonConstant.WEB_FRONT + "/v2/finance-repayment")
@Api(value = "融资申请---还款")
public class FinanceRepaymentNewFrontController {

    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    private final IFinanceApplyService financeApplyService;
    private final IRepaymentBizService repaymentBizService;
    private final IExpenseOrderDetailService expenseOrderDetailService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;

    @GetMapping("/test2")
    public void test2() {
        Map<Long, ExpenseOrderDetail> collect = expenseOrderDetailService.list().stream()
                .collect(Collectors.toMap(e -> e.getId(), e -> e));
        List<RepaymentPlanFee> list = repaymentPlanFeeService.list();
        for (RepaymentPlanFee repaymentPlanFee : list) {
            if (collect.containsKey(repaymentPlanFee.getRelationExpensesId())) {
                ExpenseOrderDetail expenseOrderDetail = collect.get(repaymentPlanFee.getRelationExpensesId());
                repaymentPlanFee.setExpenseOrderDetailStr(JSONUtil.toJsonStr(expenseOrderDetail));
            }
        }
        repaymentPlanFeeService.saveOrUpdateBatch(list);
    }

    @GetMapping("/test")
    public void test() {
        //1试算
        CostCalculusDto costCalculusDto = JSONUtil.toBean("{\n" +
                "  \"financeAmount\": 3000,\n" +
                "  \"overdueInterest\": 0,\n" +
                "  \"penaltyInterest\": 0,\n" +
                "  \"processInterest\": 0,\n" +
                "  \"annualInterestRate\": \"12.00\",\n" +
                "  \"totalTerm\": 5,\n" +
                "  \"startTime\": \"2023-12-16\",\n" +
                "  \"refundType\": \"1\",\n" +
                "  \"loadTermUnit\": 2,\n" +
                "  \"goodsId\": \"1731492504311808002\",\n" +
                "  \"chargePoint\": \"8,2\",\n" +
                "  \"loanDay\": 5,\n" +
                "  \"goodType\": 1,\n" +
                "  \"enterpriseQuotaId\": \"1731498809948749826\"\n" +
                "}", CostCalculusDto.class);
        costCalculusDto.setType(1);
        costCalculusDto.setChargePoint("8,2");
        costCalculusDto.setUserId(AuthUtil.getUserId());
        costCalculusDto.setEnterpriseType(UserUtils.getEnterpriseType());
        CostCalculusVO costCalculusVO = repaymentPlanFinanceApplyBizService.costCalculus(costCalculusDto);
        //2保存
        repaymentPlanFinanceApplyBizService.saveCalJson(null, 1, "J29704727294905");
        //3 获取
        CostCalculusVO costCalculusVO1 = repaymentPlanFinanceApplyBizService.costCalculusByFinanceNo("J29704727294905", 1);
        //4 保存费用
//        List<ExpenseOrderDetail> expenseOrderDetailList = costCalculusVO.getExpenseOrderDetailList();
//        CreateExpenseOrderDetailDTO createExpenseOrderDetailDTO = CreateExpenseOrderDetailDTO.builder().expenseOrderDetailList(expenseOrderDetailList)
//                .financeNo("J29704727294905").type(1).build();
//        List<ExpenseOrderDetail> expenseOrderDetail = productExpenseService.createExpenseOrderDetail(createExpenseOrderDetailDTO);
        //5获取 融资节点需要支付的费用
//        expenseInfoBizService.listExpenseInfoList(financeNo, type, feeNode, bankCardId)
        //5 生成还款计划
        FinanceApply byFinanceNo = financeApplyService.getByFinanceNo("J29704727294905");
//        RepaymentPlanDTO repaymentPlanDTO = repaymentPlanFinanceApplyBizService.saveRepaymentPlanByFinanceApply(byFinanceNo, 1, "J29704727294905", null);
//        //6 查看借据情况
//        LoanInfoDTO j570918268021356 = repaymentBizService.getLoanInfoDTO("J300536386121159");
        System.out.println("1");
    }

    /**
     * 还款试算
     *
     * @param costCalculusDto 还款试算参数
     * @return
     */
    @PostMapping("/repaymentCalculation")
    public R<CostCalculusVO> repaymentCalculation(@Valid @RequestBody CostCalculusDto costCalculusDto) {
        costCalculusDto.setCurrentFeeNode(8);
        costCalculusDto.setType(1);
        costCalculusDto.setChargePoint("8,2");
        costCalculusDto.setUserId(AuthUtil.getUserId());
        costCalculusDto.setEnterpriseType(UserUtils.getEnterpriseType());
        return R.data(repaymentPlanFinanceApplyBizService.costCalculus(costCalculusDto));
    }

    /**
     * 根据融资单号和费用业务类型查看还款计划列表及费用
     *
     * @param financeNo 融资单号
     * @param type      费用类型（不传则查该融资单号下所有费用详情）
     * @return
     */
    @GetMapping("/repaymentCalculation-financeNo")
    public R<CostCalculusVO> repaymentCalculation(@RequestParam String financeNo,
                                                  @RequestParam(required = false) Integer type) {
        return R.data(repaymentPlanFinanceApplyBizService.costCalculusByFinanceNo(financeNo, type));
    }


}
