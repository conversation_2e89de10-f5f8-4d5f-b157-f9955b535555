package org.springblade.loan.dto;

import lombok.Data;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.product.expense.constant.ExpenseConstant;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 资方放款还款计划生成，随借随还模式不分期
 * <AUTHOR>
 * @date 2024/12/23
 */
@Data
public class CapitalLoanCallBackRepaymentPlan implements Serializable {

    /**
     * 融资申请记录
     */
    private FinanceApply financeApply;
    /**
     * 业务场景
     */
    private PlatformExpensesEnum platformExpensesEnum;
    /**
     * 当前计算节点
     */
    private ExpenseConstant.FeeNodeEnum feeNodeEnum;
    /**
     * 是否第一次
     */
    private boolean isFirst;
    /**
     * 放款时间
     */
    private LocalDate startTime;
    /**
     * 到期时间/还款时间
     */
    private LocalDate refundTime;
    /**
     * 是否零利率，行方砍头息的情况
     */
    private Boolean interestIsZero;
}
