package org.springblade.loan.strategy;

import lombok.RequiredArgsConstructor;
import org.springblade.loan.contant.BankRepaymentEnum;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanCalReq;
import org.springblade.loan.service.IOffLinkBankRepaymentService;
import org.springframework.stereotype.Service;

/**
 * 线上银联策略类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 还款试算策略类
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class OfflineBankRepaymentStrategy implements BankRepaymentStrategy {
    private final IOffLinkBankRepaymentService offLinkBankRepaymentService;

    @Override
    public BankRepaymentEnum support() {
        return BankRepaymentEnum.OFFLINE_BANK;
    }

    @Override
    public RepaymentPlanCal calculate(RepaymentPlanCalReq repaymentPlanCalReq) {
        return offLinkBankRepaymentService.calculate(repaymentPlanCalReq);

    }
}
