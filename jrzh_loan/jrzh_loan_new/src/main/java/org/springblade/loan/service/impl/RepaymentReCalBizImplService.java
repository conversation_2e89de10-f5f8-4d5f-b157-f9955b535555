package org.springblade.loan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.ThreadUtils;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.dto.ExpenseGoodsConfig;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.handler.ProductExpenseOrderDetailUtils;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.loan.dto.RepaymentPlanReCalParam;
import org.springblade.loan.entity.RepaymentFee;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.loan.service.IRepaymentReCalBizService;
import org.springblade.product.common.entity.GoodsExpenseRelation;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 还款服务重新试算
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-02  14:52
 * @Description: 还款服务类
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class RepaymentReCalBizImplService implements IRepaymentReCalBizService {
    private final IProductExpenseService productExpenseService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    /**
     * 还款中 或已支付
     */
    private final static List<Integer> PAYING_OR_PAYED_STATUS = Arrays.asList(RepaymentConstant.RepaymentStatusEnum.PAYING.getCode()
            , RepaymentConstant.RepaymentStatusEnum.PAY.getCode());
    /**
     * 期数类型的费用计算节点 仅在列表里的节点需要进行特殊处理
     */
    private final static List<Integer> TERM_FEE_NODE = Arrays.asList(ExpenseConstant.FeeNodeEnum.ADVANCE_REPAYMENT.getCode(),
            ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode(), ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode());

    @Override
    public List<LoanInfoDTO> reCalLoanInfoDTO(List<LoanInfoDTO> loanInfoDTOS, RepaymentPlanReCalParam reCalParam) {
        Integer currentNode = reCalParam.getCurrentNode();
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new IllegalArgumentException("当前计算节点不能为空");
        }
        //获取计算配置
        Map<Long, ExpenseGoodsConfig> expenseGoodsConfigMap = getLoanCalExpenseConfig(loanInfoDTOS, reCalParam);
        int finalI = loanInfoDTOS.size();
        List<CompletableFuture<?>> futures = new ArrayList<>();
        final String tenantId = AuthUtil.getTenantId();
        for (LoanInfoDTO loanInfoDTO : loanInfoDTOS) {
            futures.add(ThreadUtils.supplyAsync(() -> {
                TenantBroker.runAs(tenantId, e -> {
                    //重新计算费用
                    reCalLoanInfo(reCalParam, expenseGoodsConfigMap, loanInfoDTO);
                });
                return finalI;
            }));
        }
        //等待异步计算完成
        ThreadUtils.allOf(futures.toArray(new CompletableFuture[0])).join();
        return loanInfoDTOS;
    }

    private void reCalLoanInfo(RepaymentPlanReCalParam reCalParam, Map<Long, ExpenseGoodsConfig> expenseGoodsConfigMap, LoanInfoDTO loanInfoDTO) {
        //是否只收取传入的新节点 feeNode
        if (reCalParam.isCollectFeedOnly()) {
            loanInfoDTO.setRepaymentPlanFee(loanInfoDTO.getRepaymentPlanFee().stream().filter(e -> reCalParam.getFeeNode().contains(e.getFeeNode()))
                    .collect(Collectors.toList()));
        }
        ExpenseGoodsConfig originExpenseGoodsConfig = expenseGoodsConfigMap.get(loanInfoDTO.getGoodsId());
        ExpenseGoodsConfig expenseGoodsConfig = BeanUtil.copyProperties(originExpenseGoodsConfig, ExpenseGoodsConfig.class);
        expenseGoodsConfig.setUseExpenseCal(reCalParam.isUseExpenseCal());
        if (ObjectUtil.isNotEmpty(expenseGoodsConfig)) {
            //设置计算参数
            expenseGoodsConfig.setFinanceNo(loanInfoDTO.getFinanceNo());
            //计算已经生成的
            List<Long> existExpenseRuleId = loanInfoDTO.listUsingRepaymentPlanFee().stream().map(RepaymentPlanFee::getExpenseTypeId).collect(Collectors.toList());
            //借据单所有需要计算的费用关联  区分为收费在还款节点上 和不在还款节点上 （未排除掉已经存在的）
            Map<Boolean, List<GoodsExpenseRelation>> calGoodsExpenseRelationMap = expenseGoodsConfig.getGoodsExpenseRelationList().stream().collect(Collectors.partitioningBy(e -> TERM_FEE_NODE.contains(e.getCollectFeesNode())));

            //借据单所有已经生成过的费用关联 区分为收费在还款节点上 和不在还款节点上
            Map<Boolean, List<GoodsExpenseRelation>> existFeeRelationMap = expenseGoodsConfig.getReCalGoodsExpenseRelationList()
                    .stream().filter(e -> existExpenseRuleId.contains(e.getExpenseTypeId()))
                    .collect(Collectors.partitioningBy(e -> TERM_FEE_NODE.contains(e.getCollectFeesNode())));

            //对分期节点的借据单进行计算
            List<GoodsExpenseRelation> termExistGoodsExpenseRelations = existFeeRelationMap.get(true);
            List<GoodsExpenseRelation> termGoodsExpenseRelations = calGoodsExpenseRelationMap.get(true);
            Set<Long> oneTimePay = new HashSet<>();
            for (RepaymentInfoDTO repaymentInfoDTO : loanInfoDTO.getRepaymentInfoList()) {
                if (RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == repaymentInfoDTO.getRepaymentStatus()) {
                    ExpenseGoodsConfig termExpenseGoodsConfig = BeanUtil.copyProperties(originExpenseGoodsConfig, ExpenseGoodsConfig.class);
                    //设置计算参数
                    termExpenseGoodsConfig.setUseExpenseCal(reCalParam.isReCalGenFee());
                    termExpenseGoodsConfig.setFinanceNo(loanInfoDTO.getFinanceNo());
                    termExpenseGoodsConfig.setGoodsExpenseRelationList(termGoodsExpenseRelations);
                    List<Long> existPlanFeeIds = repaymentInfoDTO.listUsingRepaymentPlanFee().stream().map(RepaymentPlanFee::getExpenseTypeId).distinct().collect(Collectors.toList());
                    termExpenseGoodsConfig.setReCalGoodsExpenseRelationList(termExistGoodsExpenseRelations.stream().filter(e ->
                            existPlanFeeIds.contains(e.getExpenseTypeId()) && !oneTimePay.contains(e.getExpenseTypeId())).collect(Collectors.toList()));
                    //设置本次参与计算的
                    loanInfoDTO.setCurrentRepaymentInfoDTO(repaymentInfoDTO);
                    //排除掉一次性的费用不参与计算
                    List<RepaymentPlanFee> repaymentPlanFeeList = reCalUnPayRepaymentInfoDTOPlanFee(repaymentInfoDTO, reCalParam, termExpenseGoodsConfig, JSONUtil.parseObj(loanInfoDTO))
                            .stream().filter(e -> !oneTimePay.contains(e.getExpenseTypeId())).collect(Collectors.toList());
                    for (RepaymentPlanFee repaymentPlanFee : repaymentPlanFeeList) {
                        if (ExpenseConstant.ChargeMethodEnum.ONE_TIME_PAYMENT.getCode().equals(repaymentPlanFee.getCollectFeeMethod())) {
                            oneTimePay.add(repaymentPlanFee.getExpenseTypeId());
                        }
                    }
                    //重新设置 费用计划
                    if (CollUtil.isNotEmpty(reCalParam.getNeedSubRepaymentFeeNode())) {
                        repaymentInfoDTO.setRepaymentPlanFeeList(calUnPayRepaymentPlanFee(repaymentPlanFeeList, repaymentInfoDTO.payedOrPayingRepaymentFeeList(), reCalParam.getNeedSubRepaymentFeeNode()));
                    } else {
                        repaymentInfoDTO.setRepaymentPlanFeeList(filterPayedOneTimePlanFee(repaymentPlanFeeList, repaymentInfoDTO.payedOrPayingRepaymentFeeList()));
                    }
                }
            }
            //对整期节点的借据单进行计算
            List<GoodsExpenseRelation> allExistNodeGoodsExpenseRelations = existFeeRelationMap.get(false);
            List<GoodsExpenseRelation> allNodeGoodsExpenseRelations = calGoodsExpenseRelationMap.get(false);
            List<RepaymentPlanFee> allNodeExpenseRelations = reCalAllFeeNode(expenseGoodsConfig, allExistNodeGoodsExpenseRelations, allNodeGoodsExpenseRelations, loanInfoDTO, reCalParam);
            //合并整期和分期的
            List<RepaymentPlanFee> repaymentPlanFees = Stream.concat(allNodeExpenseRelations.stream(), loanInfoDTO.getRepaymentInfoList().stream().flatMap(e -> e.getRepaymentPlanFeeList().stream())).collect(Collectors.toList());
            loanInfoDTO.setRepaymentPlanFee(repaymentPlanFees);
        }
        //是否只收取传入的新节点 feeNode 再次去重
        if (reCalParam.isCollectFeedOnly()) {
            loanInfoDTO.setRepaymentPlanFee(loanInfoDTO.getRepaymentPlanFee().stream().filter(e -> reCalParam.getFeeNode().contains(e.getFeeNode()))
                    .collect(Collectors.toList()));
        }
    }

    private List<RepaymentPlanFee> reCalAllFeeNode(ExpenseGoodsConfig expenseGoodsConfig, List<GoodsExpenseRelation> allExistNodeGoodsExpenseRelations, List<GoodsExpenseRelation> allNodeGoodsExpenseRelations
            , LoanInfoDTO loanInfoDTO, RepaymentPlanReCalParam reCalParam) {
        RepaymentInfoDTO first = CollUtil.getFirst(loanInfoDTO.getRepaymentInfoList());
        expenseGoodsConfig.setFinanceNo(loanInfoDTO.getFinanceNo());
        expenseGoodsConfig.setUseExpenseCal(reCalParam.isReCalGenFee());
        expenseGoodsConfig.setReCalGoodsExpenseRelationList(allNodeGoodsExpenseRelations);
        //排除掉已经生成过的
        List<Long> existExpenseRuleId = allExistNodeGoodsExpenseRelations.stream().map(GoodsExpenseRelation::getExpenseTypeId).distinct().collect(Collectors.toList());
        List<GoodsExpenseRelation> goodsExpenseRelations = allNodeGoodsExpenseRelations.stream().filter(e -> !existExpenseRuleId.contains(e.getExpenseTypeId())).collect(Collectors.toList());
        expenseGoodsConfig.setGoodsExpenseRelationList(goodsExpenseRelations);
        JSONObject req = JSONUtil.parseObj(loanInfoDTO);
        if (ObjectUtil.isNotEmpty(reCalParam.getAttachVar())) {
            req.putAll(reCalParam.getAttachVar());
        }
        String jsonStr = JSONUtil.toJsonStr(req);
        //计算已经生成过的
        if (reCalParam.isReCalGenFee() && CollUtil.isNotEmpty(allExistNodeGoodsExpenseRelations)) {
            //重新计算并填充新金额
            reCalExistRepaymentPlanFeeList(loanInfoDTO.listUsingRepaymentPlanFee(), expenseGoodsConfig, jsonStr, reCalParam.getCurrentNode(), reCalParam.getReCalGenFeeNode());
        }
        //计算未生成过的
        List<ExpenseOrderDetail> expenseOrderDetailList = productExpenseService.calculateExpenseOrderDetailByRelation(expenseGoodsConfig, goodsExpenseRelations, expenseGoodsConfig.getProduct().getType(), jsonStr, reCalParam.getCurrentNode());
        List<RepaymentPlanFee> repaymentPlanFeeList = transferExpenseDetailToRepaymentPlanFee(first, new Date(), null, expenseOrderDetailList);
        if (CollUtil.isNotEmpty(reCalParam.getNeedSubRepaymentFeeNode())) {
            repaymentPlanFeeList = calUnPayRepaymentPlanFee(repaymentPlanFeeList, loanInfoDTO.listPayOrPayingRepaymentFee(), reCalParam.getNeedSubRepaymentFeeNode());
        } else {
            repaymentPlanFeeList = filterPayedOneTimePlanFee(repaymentPlanFeeList, loanInfoDTO.listPayOrPayingRepaymentFee());
        }
        //合并
        return Stream.concat(loanInfoDTO.listUsingRepaymentPlanFee().stream(), repaymentPlanFeeList.stream()).collect(Collectors.toList());
    }

    private Map<Long, ExpenseGoodsConfig> getLoanCalExpenseConfig(List<LoanInfoDTO> loanInfoDTOS, RepaymentPlanReCalParam reCalParam) {
        Map<Long, ExpenseGoodsConfig> expenseGoodsConfigMap = new HashMap<>();
        //查询出已经生成过的费用计划的费用类型ids
        List<Long> expenseRuleIds = loanInfoDTOS.stream().flatMap(e -> e.listUsingRepaymentPlanFee().stream()).map(RepaymentPlanFee::getExpenseTypeId).distinct().collect(Collectors.toList());
        Map<Long, List<GoodsExpenseRelation>> mapExistGoodsExpenseRelation = mapExistGoodsExpenseRelation(expenseRuleIds);
        //计算配置查询
        List<Integer> feeNode = reCalParam.getFeeNode();
        String financeNos = loanInfoDTOS.stream().map(LoanInfoDTO::getFinanceNo)
                .filter(StringUtil::isNotBlank).collect(Collectors.joining(StringPool.COMMA));
        for (Long goodsId : loanInfoDTOS.stream().collect(Collectors.groupingBy(LoanInfoDTO::getGoodsId)).keySet()) {
            //计算配置
            ExpenseGoodsConfig expenseGoodsConfig = productExpenseService
                    .getConfigByGoodsAndFeeNode(goodsId, feeNode, financeNos);
            List<GoodsExpenseRelation> existGoodsExpenseRelationList = mapExistGoodsExpenseRelation.getOrDefault(goodsId, Collections.emptyList());
            expenseGoodsConfig.setReCalGoodsExpenseRelationList(existGoodsExpenseRelationList);
            expenseGoodsConfigMap.put(goodsId, expenseGoodsConfig);
        }
        return expenseGoodsConfigMap;
    }

    /**
     * 重新计算整期借据单中 其中一期的费用
     *
     * @param repaymentInfoDTO   本期借款记录
     * @param loanInfoDTO        借据信息
     * @param currentNode        当前节点
     * @param expenseGoodsConfig 费用配置
     * @param termFeeNode        期数计算节点
     * @param oneTimePay         用于排除本次已生成的一次性费用
     * @return
     */
    private List<RepaymentPlanFee> reCalTermFeeNode(RepaymentInfoDTO repaymentInfoDTO, LoanInfoDTO loanInfoDTO, Integer currentNode
            , ExpenseGoodsConfig expenseGoodsConfig, List<Integer> termFeeNode, Set<Long> oneTimePay) {
        Integer term = GoodsEnum.TERM.getCode().equals(repaymentInfoDTO.getPeriodUnit()) ? repaymentInfoDTO.getPeriod() : 1;
        //设置当前参与计算的还款计划 并进行重新计算
        loanInfoDTO.setCurrentRepaymentInfoDTO(repaymentInfoDTO);
        expenseGoodsConfig.setFinanceNo(repaymentInfoDTO.getFinanceNo());
        //进行计算并排除掉已经生成过的一次性费用
        List<ExpenseOrderDetail> termExpenseOrderDetail = productExpenseService.calculateExpenseOrderDetail(expenseGoodsConfig, termFeeNode,
                        loanInfoDTO.getGoodsType(), JSONUtil.toJsonStr(loanInfoDTO), currentNode).stream()
                .filter(e -> !oneTimePay.contains(e.getExpenseTypeId())).collect(Collectors.toList());
        //加入本次一次性费用到一次性列表 用于下次进行排除
        for (ExpenseOrderDetail expenseOrderDetail : termExpenseOrderDetail) {
            expenseOrderDetail.setRepaymentTerm(term);
            if (ExpenseConstant.ChargeMethodEnum.ONE_TIME_PAYMENT.getCode().equals(expenseOrderDetail.getChargeMethod())) {
                oneTimePay.add(expenseOrderDetail.getExpenseTypeId());
            }
        }
        //费用详情转换为计划费用
        List<RepaymentPlanFee> repaymentPlanFeeList1 = transferExpenseDetailToRepaymentPlanFee(repaymentInfoDTO, new Date(), term, termExpenseOrderDetail);
        //重置查询参数
        loanInfoDTO.setCurrentRepaymentInfoDTO(null);
        //合并本次计算和已存在的
        List<RepaymentPlanFee> repaymentPlanFeeList = unionCalFeeAndExistFee(repaymentInfoDTO.getRepaymentPlanFeeList(), repaymentPlanFeeList1, loanInfoDTO.getUserId(), loanInfoDTO.getCreateDept());
        //排除已经支付过的或者正在支付一次性费用
        return filterPayedOneTimePlanFee(repaymentPlanFeeList, loanInfoDTO.listPayOrPayingRepaymentFee());
    }

//    private List<RepaymentPlanFee> reCalAllFeeNode(ExpenseGoodsConfig expenseGoodsConfig, List<Integer> allFeeNode,
//                                                   LoanInfoDTO loanInfoDTO, Integer currentNode) {
//        if (CollUtil.isEmpty(allFeeNode)) {
//            return Collections.emptyList();
//        }
//        //用于参数填充
//        RepaymentInfoDTO first = CollUtil.getFirst(loanInfoDTO.listUsingRepaymentInfo());
//        List<RepaymentPlanFee> existRepaymentPlanFeeList = loanInfoDTO.getRepaymentPlanFee()
//                .stream().filter(e -> !TERM_FEE_NODE.contains(e.getCollectFeeNode())).collect(Collectors.toList());
//        //重新进行计算费用
//        List<ExpenseOrderDetail> expenseOrderDetails = productExpenseService.calculateExpenseOrderDetail(expenseGoodsConfig, allFeeNode,
//                loanInfoDTO.getGoodsType(), JSONUtil.toJsonStr(loanInfoDTO), currentNode);
//        //转化为本次需要支付计划费用
//        List<RepaymentPlanFee> calRepaymentPlanFeeList = transferExpenseDetailToRepaymentPlanFee(first, new Date(), 1, expenseOrderDetails);
//        //合并结果 将已生成的计划费用的金额字段进行替换 未生成的计划费用则进行转换
//        List<RepaymentPlanFee> repaymentPlanFeeList = unionCalFeeAndExistFee(existRepaymentPlanFeeList, calRepaymentPlanFeeList, loanInfoDTO.getUserId(), loanInfoDTO.getCreateDept());
//        //排除已经支付过的一次性费用
//        return filterPayedOneTimePlanFee(repaymentPlanFeeList, loanInfoDTO.listPayOrPayingRepaymentFee());
//    }

    private List<RepaymentPlanFee> unionTermAndAllRepaymentPlanFee(List<RepaymentPlanFee> repaymentPlanFeeList, List<RepaymentInfoDTO> repaymentInfoDTOS, boolean isNoCompose
            , Long userId, Long createDept) {
        if (CollUtil.isEmpty(repaymentPlanFeeList)) {
            return repaymentPlanFeeList;
        }
        // 需要合并时 将所有计划费用进行合并
        if (!isNoCompose) {
            Map<Long, List<RepaymentPlanFee>> groupByExpenseTypeMap = repaymentInfoDTOS.stream().flatMap(e -> e.getRepaymentPlanFeeList()
                    .stream()).collect(Collectors.groupingBy(RepaymentPlanFee::getExpenseTypeId));
            List<RepaymentPlanFee> allRepaymentPlanFee = groupByExpenseTypeMap.keySet().stream().map(e -> {
                List<RepaymentPlanFee> planFees = groupByExpenseTypeMap.get(e);
                RepaymentPlanFee first = CollUtil.getFirst(planFees);
                //一次性收取 返回第一条即可
                if (ExpenseConstant.ChargeMethodEnum.ONE_TIME_PAYMENT.getCode().equals(first.getCollectFeeMethod())) {
                    return first;
                } else {
                    //重复收费
                    first.setAmount(planFees.stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                return first;
            }).collect(Collectors.toList());
            //合并整期的 和分期的
            return unionCalFeeAndExistFee(repaymentPlanFeeList, allRepaymentPlanFee, userId, createDept);
        } else {
            throw new UnsupportedOperationException("未实现");
        }
    }

    @Override
    public List<RepaymentInfoDTO> reCalRepaymentInfoDTO(RepaymentPlanReCalParam reCalDyFeeParam, List<RepaymentInfoDTO> repaymentInfoDTOList) {
        //费用计算配置获取 还款id v:费用配置
        Map<Long, ExpenseGoodsConfig> listExpenseOrderConfig = listExpenseGoodsConfigByRepaymentInfoDTOList(repaymentInfoDTOList, reCalDyFeeParam);
        repaymentInfoDTOList.forEach(e -> {
            //产品费用配置获取
            ExpenseGoodsConfig expenseGoodsConfig = listExpenseOrderConfig.get(e.getId());
            expenseGoodsConfig.setFinanceNo(e.getFinanceNo());
            expenseGoodsConfig.setUseExpenseCal(reCalDyFeeParam.isUseExpenseCal());
            //重新进行未结清费用 拷贝一个 避免直接修改对象引用
            ExpenseGoodsConfig copyExpenseGoodsConfig = BeanUtil.copyProperties(expenseGoodsConfig, ExpenseGoodsConfig.class);
            List<RepaymentPlanFee> usingUnPayList = reCalUnPayRepaymentInfoDTOPlanFee(e, reCalDyFeeParam, copyExpenseGoodsConfig, JSONUtil.parseObj(e));
            //重新设置未结清费用
            if (CollUtil.isNotEmpty(reCalDyFeeParam.getNeedSubRepaymentFeeNode())) {
                e.setRepaymentPlanFeeList(calUnPayRepaymentPlanFee(usingUnPayList, e.getRepaymentFeeList(), reCalDyFeeParam.getNeedSubRepaymentFeeNode()));
            } else {
                e.setRepaymentPlanFeeList(filterPayedOneTimePlanFee(usingUnPayList, e.getRepaymentFeeList()));
            }
        });
        return repaymentInfoDTOList;
    }

    @Override
    public RepaymentInfoDTO reCalRepaymentInfoDTO(RepaymentPlanReCalParam reCalDyFeeParam, RepaymentInfoDTO repaymentInfoDTO) {
        return reCalRepaymentInfoDTO(reCalDyFeeParam, Collections.singletonList(repaymentInfoDTO)).get(0);
    }

    /**
     * 计算正常情况下的待还计划费用
     *
     * @param allRepaymentPlanFee
     * @param listFee
     * @return
     */
    @Override
    public List<RepaymentPlanFee> calUnPayRepaymentPlanFee(List<RepaymentPlanFee> allRepaymentPlanFee, List<RepaymentFee> listFee) {
        return calUnPayRepaymentPlanFee(allRepaymentPlanFee, listFee, null);
    }

    @Override
    public List<RepaymentPlanFee> calUnPayRepaymentPlanFee(List<RepaymentPlanFee> allRepaymentPlanFee, List<RepaymentFee> listFee, List<Integer> repaymentFee) {
        List<RepaymentPlanFee> repaymentPlanFeeList = allRepaymentPlanFee.stream()
                .filter(e -> RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == e.getRepaymentStatus())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(repaymentPlanFeeList)) {
            return repaymentPlanFeeList;
        }
        if (CollUtil.isEmpty(listFee)) {
            return repaymentPlanFeeList;
        }
        List<RepaymentFee> payOrPayingFee = listFee.stream()
                .filter(e -> PAYING_OR_PAYED_STATUS.contains(e.getStatus())).collect(Collectors.toList());
        if (CollUtil.isEmpty(payOrPayingFee)) {
            return repaymentPlanFeeList;
        }
        BigDecimal min = new BigDecimal("0.01");
        //本次未还计划费用=计划费用-已还或还款中 最后筛选出大于0.01的还款计划费用 小于则等待下次在还
        Map<Long, List<RepaymentFee>> listMap = payOrPayingFee.stream().collect(Collectors.groupingBy(RepaymentFee::getFeePlanId));
        List<RepaymentPlanFee> planFeeList = repaymentPlanFeeList.stream().map(e -> {
            //为了不影响原本的RepaymentFee 对象需要做属性拷贝
            RepaymentPlanFee repaymentPlanFee = BeanUtil.copyProperties(e, RepaymentPlanFee.class);
            List<RepaymentFee> repaymentFees = listMap.get(e.getId());
            //扣除具体指定的费用
            if (CollUtil.isNotEmpty(repaymentFee)) {
                if (e.isNewCal() && CollUtil.isNotEmpty(repaymentFees) && repaymentFee.contains(e.getFeeNode())) {
                    BigDecimal payAmount = repaymentFees.stream().map(RepaymentFee::getShouldAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    repaymentPlanFee.setAmount(repaymentPlanFee.getAmount().subtract(payAmount));
                    //更新费用订单信息
                    ExpenseOrderDetail orderDetail = JSONUtil.toBean(repaymentPlanFee.getExpenseOrderDetailStr(), ExpenseOrderDetail.class);
                    orderDetail.setAmount(repaymentPlanFee.getAmount());
                    repaymentPlanFee.setExpenseOrderDetailStr(JSONUtil.toJsonStr(orderDetail));
                }
            } else {
                //扣除所有已经支付的费用
                if (CollUtil.isNotEmpty(repaymentFees)) {
                    BigDecimal payAmount = repaymentFees.stream().map(RepaymentFee::getShouldAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    repaymentPlanFee.setAmount(repaymentPlanFee.getAmount().subtract(payAmount));
                }
            }
            return repaymentPlanFee;
        }).filter(e -> min.compareTo(e.getAmount()) <= 0).collect(Collectors.toList());
        //排除掉已经支付过的一次性支付的费用
        return filterPayedOneTimePlanFee(planFeeList, listFee);
    }

    @Override
    public List<RepaymentPlanFee> filterByExpenseType(List<RepaymentPlanFee> repaymentPlanFees, Integer expenseType) {
        return repaymentPlanFees.stream().filter(e -> e.getExpenseKey().equals(expenseType)).collect(Collectors.toList());
    }

    @Override
    public List<RepaymentFee> filterByExpenseTypes(List<RepaymentFee> repaymentFeeList, Integer expenseType) {
        return repaymentFeeList.stream().filter(e -> e.getExpenseKey().equals(expenseType)).collect(Collectors.toList());
    }

    /**
     * 排除掉已经支付过的一次性支付的费用
     *
     * @param repaymentPlanFeeList 费用计划
     * @param listFee              费用记录
     * @return
     */
    private List<RepaymentPlanFee> filterPayedOneTimePlanFee(List<RepaymentPlanFee> repaymentPlanFeeList, List<RepaymentFee> listFee) {
        List<Long> payedPlanFeeId = listFee.stream().map(RepaymentFee::getExpenseTypeId).distinct().collect(Collectors.toList());
        //返回条件 1:重复支付性费用 2:一次性支付费用 但未支付过
        return repaymentPlanFeeList.stream().filter(e -> (ExpenseConstant.ChargeMethodEnum.ONE_TIME_PAYMENT.getCode()
                .equals(e.getCollectFeeMethod()) && !payedPlanFeeId.contains(e.getExpenseTypeId())
                || ExpenseConstant.ChargeMethodEnum.INSTALLMENT.getCode().equals(e.getCollectFeeMethod()))).collect(Collectors.toList());
    }

    /**
     * 重新进行计算动态费用
     *
     * @param repaymentInfoDTO 当前还款计划情况
     * @param reCalDyFeeParam  动态费用参数
     */
    private List<RepaymentPlanFee> reCalUnPayRepaymentInfoDTOPlanFee(RepaymentInfoDTO repaymentInfoDTO,
                                                                     RepaymentPlanReCalParam reCalDyFeeParam,
                                                                     ExpenseGoodsConfig expenseGoodsConfig, JSONObject calJSON) {
        List<Integer> feeNode = reCalDyFeeParam.getFeeNode();
        Integer currentNode = reCalDyFeeParam.getCurrentNode();
        boolean reCalGenFee = reCalDyFeeParam.isReCalGenFee();
        Date dateNow = new Date();
        Integer term = ProductExpenseOrderDetailUtils.getCurrentTerm(repaymentInfoDTO.getPeriod(), repaymentInfoDTO.getPeriodUnit());
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new IllegalArgumentException("重新计算缺少当前节点参数");
        }
        //附加的参数进行填充
        if (reCalDyFeeParam.getAttachVar() != null) {
            calJSON.putAll(reCalDyFeeParam.getAttachVar());
        }
        String calJson = JSONUtil.toJsonStr(calJSON);
        //设置参数
        expenseGoodsConfig.setFinanceNo(repaymentInfoDTO.getFinanceNo());
        expenseGoodsConfig.setUseExpenseCal(reCalDyFeeParam.isReCalGenFee());
        List<Long> expenseTypeIds = repaymentInfoDTO.listUsingRepaymentPlanFee().stream().map(RepaymentPlanFee::getExpenseTypeId)
                .filter(ObjectUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<GoodsExpenseRelation> existGoodsExpenseRelationList = expenseGoodsConfig.getReCalGoodsExpenseRelationList().stream().filter(e -> expenseTypeIds.contains(e.getExpenseTypeId())).collect(Collectors.toList());
        //设置需要重新计算的费用详情
        expenseGoodsConfig.setReCalGoodsExpenseRelationList(existGoodsExpenseRelationList);
        //本次计算 需要排除掉已经生成的费用详情
        List<Long> existExpenseTypeIds = existGoodsExpenseRelationList.stream().map(GoodsExpenseRelation::getExpenseTypeId).collect(Collectors.toList());
        expenseGoodsConfig.setGoodsExpenseRelationList(expenseGoodsConfig.getGoodsExpenseRelationList().stream().filter(e ->
                !existExpenseTypeIds.contains(e.getExpenseTypeId())).collect(Collectors.toList()));
        //重新计算已经生成过的费用
        List<RepaymentPlanFee> existRepaymentPlanFeeList = repaymentInfoDTO.listUsingRepaymentPlanFee();
        if (reCalGenFee && CollUtil.isNotEmpty(existRepaymentPlanFeeList)) {
            existRepaymentPlanFeeList = reCalExistRepaymentPlanFeeList(existRepaymentPlanFeeList, expenseGoodsConfig, calJson, currentNode, reCalDyFeeParam.getReCalGenFeeNode());
        }
        //计算本次未生成的费用
        List<RepaymentPlanFee> unGenRepaymentPlanFeeList = calUnGenRepaymentPlanFeeList(feeNode, expenseGoodsConfig, calJson, repaymentInfoDTO, currentNode, dateNow, term);
        //合并已经存在的费用 和本次未生成的费用
        List<RepaymentPlanFee> repaymentPlanFees = Stream.concat(existRepaymentPlanFeeList.stream(), unGenRepaymentPlanFeeList.stream()).collect(Collectors.toList());
        //是否只收取传入的新节点 feeNode
        if (reCalDyFeeParam.isCollectFeedOnly()) {
            repaymentPlanFees = repaymentPlanFees.stream().filter(e -> reCalDyFeeParam.getFeeNode().contains(e.getFeeNode()))
                    .collect(Collectors.toList());
        }
        return repaymentPlanFees;
    }

    private List<RepaymentPlanFee> calUnGenRepaymentPlanFeeList(List<Integer> feeNode, ExpenseGoodsConfig expenseGoodsConfig, String calJSON,
                                                                RepaymentInfoDTO repaymentInfoDTO, Integer currentNode, Date dateNow, Integer term) {
        Integer goodsType = expenseGoodsConfig.getProduct().getType();
        //费用计算
        List<ExpenseOrderDetail> expenseOrderDetailList = productExpenseService.calculateExpenseOrderDetailByRelation(expenseGoodsConfig, expenseGoodsConfig.getGoodsExpenseRelationList(), goodsType, calJSON, currentNode);
        //费用转化为还款计划费用
        List<RepaymentPlanFee> repaymentPlanFeeList = transferExpenseDetailToRepaymentPlanFee(repaymentInfoDTO, dateNow, term, expenseOrderDetailList);
        return repaymentPlanFeeList;
    }

    private List<RepaymentPlanFee> reCalExistRepaymentPlanFeeList(List<RepaymentPlanFee> existRepaymentPlanFeeList, ExpenseGoodsConfig expenseGoodsConfig, String calJSON, Integer currentNode, List<Integer> reCalNode) {
        Integer goodsType = expenseGoodsConfig.getProduct().getType();
        //费用计算 k:费用类型 v:费用详情
        List<GoodsExpenseRelation> reCalGoodsExpenseRelationList = CollUtil.isNotEmpty(reCalNode) ?
                expenseGoodsConfig.getReCalGoodsExpenseRelationList().stream()
                        .filter(e -> reCalNode.contains(e.getFeeNode())).collect(Collectors.toList())
                : expenseGoodsConfig.getReCalGoodsExpenseRelationList();
        if (CollUtil.isEmpty(reCalGoodsExpenseRelationList)) {
            return existRepaymentPlanFeeList;
        }
        Map<Long, ExpenseOrderDetail> expenseOrderDetailMap = productExpenseService
                .calculateExpenseOrderDetailByRelation(expenseGoodsConfig, reCalGoodsExpenseRelationList, goodsType, calJSON, currentNode)
                .stream().collect(Collectors.toMap(ExpenseOrderDetail::getExpenseTypeId, e -> e));        //替换旧费用计划的计算金额字段
        for (RepaymentPlanFee repaymentPlanFee : existRepaymentPlanFeeList) {

            ExpenseOrderDetail expenseOrderDetail = expenseOrderDetailMap.get(repaymentPlanFee.getExpenseTypeId());
            if (ObjectUtil.isNotEmpty(expenseOrderDetail)) {
                repaymentPlanFee.setAmount(expenseOrderDetail.getAmount());
                repaymentPlanFee.setExpenseOrderDetailStr(JSONUtil.toJsonStr(expenseOrderDetail));
                repaymentPlanFee.setNewCal(true);
                repaymentPlanFee.setCollectFeeMethod(expenseOrderDetail.getChargeMethod());
            }
        }
        return existRepaymentPlanFeeList;
    }

    private Map<Long, ExpenseGoodsConfig> listExpenseGoodsConfigByRepaymentInfoDTOList(List<RepaymentInfoDTO> repaymentInfoDTOList, RepaymentPlanReCalParam reCalDyFeeParam) {
        //本期正在使用中的还款计划 需要重新计算的费用关联费用
        List<Long> needReCalExpenseTypeIds = repaymentInfoDTOList.stream().flatMap(e -> e.listUsingRepaymentPlanFee().stream())
                .map(RepaymentPlanFee::getExpenseTypeId)
                .collect(Collectors.toList());
        Map<Long, List<GoodsExpenseRelation>> existGoodsRelationMap = mapExistGoodsExpenseRelation(needReCalExpenseTypeIds);
//        List<Integer> neeCalFeeNode = unionNeedCalFeeByExistRepaymentPlanFee(usingRepaymentPlanFee, reCalDyFeeParam.getFeeNode(), reCalDyFeeParam.getFilterFeeNode());

        List<Integer> feeNode = reCalDyFeeParam.getFeeNode();
        String financeNo = repaymentInfoDTOList.stream().map(RepaymentInfoDTO::getFinanceNo).distinct().collect(Collectors.joining(StringPool.COMMA));
        //按产品拿费用配置信息
        List<Long> goodsId = repaymentInfoDTOList.stream().map(RepaymentInfoDTO::getGoodsId).distinct().collect(Collectors.toList());
        //k:产品id v:产品费用配置
        Map<Long, ExpenseGoodsConfig> goodsExpenseConfigMap = goodsId.stream()
                .map(e -> {
                    ExpenseGoodsConfig configByGoodsAndFeeNode = productExpenseService.getConfigByGoodsAndFeeNode(e, feeNode, financeNo);
                    //设置该产品本次需要重新计算的费用关联
                    configByGoodsAndFeeNode.setReCalGoodsExpenseRelationList(existGoodsRelationMap.getOrDefault(e, Collections.emptyList()));
                    return configByGoodsAndFeeNode;
                })
                .collect(Collectors.toMap(e -> e.getProduct().getId(), e -> e));
        //返回 k:还款计划id v:产品费用配置
        return repaymentInfoDTOList.stream().collect(Collectors.toMap(BaseEntity::getId, e -> goodsExpenseConfigMap.get(e.getGoodsId())));
    }

    /**
     * k:产品id v:类型id
     *
     * @param needReCalExpenseRuleId
     * @return
     */
    private Map<Long, List<GoodsExpenseRelation>> mapExistGoodsExpenseRelation(List<Long> needReCalExpenseRuleId) {
        if (CollUtil.isEmpty(needReCalExpenseRuleId)) {
            return MapUtil.newHashMap();
        }
        //k:产品id v:计算费用关联
        Map<Long, List<GoodsExpenseRelation>> existGoodsRelationMap = productExpenseService.listGoodsExpenseRelationByExpenseTypeId(needReCalExpenseRuleId).stream()
                .collect(Collectors.groupingBy(GoodsExpenseRelation::getGoodsId));
        return existGoodsRelationMap;
    }

    private List<RepaymentPlanFee> transferExpenseDetailToRepaymentPlanFee(RepaymentInfoDTO repaymentInfoDTO, Date dateNow, Integer term, List<ExpenseOrderDetail> expenseOrderDetails) {
        List<RepaymentPlanFee> calRepaymentPlanFeeList = expenseOrderDetails.stream().map(expenseOrderDetail -> {
            expenseOrderDetail.setAmount(expenseOrderDetail.getAmount());
            expenseOrderDetail.setRepaymentTerm(term);
            RepaymentPlanFee repaymentPlanFee = repaymentPlanFinanceApplyBizService
                    .buildRepaymentPlanFee(expenseOrderDetail.getAmount(), expenseOrderDetail, term, repaymentInfoDTO);
            repaymentPlanFee.setCreateUser(repaymentInfoDTO.getUserId());
            repaymentPlanFee.setUpdateUser(repaymentInfoDTO.getUserId());
            repaymentPlanFee.setCreateTime(dateNow);
            repaymentPlanFee.setCreateDept(repaymentInfoDTO.getCreateDept());
            repaymentPlanFee.setNewCal(true);
            //先给定id 后续保存
            repaymentPlanFee.setId(IdWorker.getId());
            return repaymentPlanFee;
        }).collect(Collectors.toList());
        return calRepaymentPlanFeeList;
    }

    /**
     * 合并已存在的费用计划和本次需要计算的费用
     *
     * @param existRepaymentPlanFeeList 已存在的费用计划
     * @param feeNode                   费用节点
     * @param filterFeeNode             精准费用节点
     * @return
     */
    private List<Integer> unionNeedCalFeeByExistRepaymentPlanFee(List<RepaymentPlanFee> existRepaymentPlanFeeList, List<Integer> feeNode
            , List<Integer> filterFeeNode) {
        if (ObjectUtil.isNotEmpty(filterFeeNode)) {
            return filterFeeNode;
        }
        if (existRepaymentPlanFeeList == null) {
            existRepaymentPlanFeeList = Collections.emptyList();
        }
        List<Integer> existFeeNode = existRepaymentPlanFeeList.stream().map(RepaymentPlanFee::getFeeNode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(feeNode)) {
            existFeeNode.addAll(feeNode);
        }
        return existFeeNode.stream()
                .distinct().collect(Collectors.toList());
    }

    /**
     * 合并已存在的费用计划 和计算的费用
     *
     * @param existRepaymentPlanFeeList 已存在的费用计划
     * @param calRepaymentPlanFeeList   计算后
     * @param userId                    预生成计划费用时填充的用户id
     * @param deptId                    预生成计划费用时填充的部门id
     * @return
     */
    private List<RepaymentPlanFee> unionCalFeeAndExistFee(List<RepaymentPlanFee> existRepaymentPlanFeeList,
                                                          List<RepaymentPlanFee> calRepaymentPlanFeeList, Long userId, Long deptId) {
        Date createTime = new Date();
        List<RepaymentPlanFee> allRepaymentPlanFee = new ArrayList<>();
        // k:费用类型id v:已存在的费用计划
        Map<Long, RepaymentPlanFee> existRepaymentPlanFeeListMap = existRepaymentPlanFeeList.stream()
                .collect(Collectors.toMap(RepaymentPlanFee::getExpenseTypeId, e -> e));
        //转化成本次需要支付的费用
        Map<Long, RepaymentPlanFee> calRepaymentPlanMap = calRepaymentPlanFeeList.stream().map(calRepaymentPlanFee -> {
            //费用已存在的情况下 旧费用替换金额, 即可
            if (existRepaymentPlanFeeListMap.containsKey(calRepaymentPlanFee.getExpenseTypeId())) {
                BigDecimal amount = calRepaymentPlanFee.getAmount();
                RepaymentPlanFee repaymentPlanFee = existRepaymentPlanFeeListMap.get(calRepaymentPlanFee.getExpenseTypeId());
                repaymentPlanFee.setAmount(amount);
                allRepaymentPlanFee.add(repaymentPlanFee);
                return repaymentPlanFee;
            } else {
                //先给定id 后续保存
                calRepaymentPlanFee.setId(IdWorker.getId());
                calRepaymentPlanFee.setCreateTime(createTime);
                calRepaymentPlanFee.setUserId(userId);
                calRepaymentPlanFee.setCreateDept(deptId);
                allRepaymentPlanFee.add(calRepaymentPlanFee);
                return calRepaymentPlanFee;
            }
        }).collect(Collectors.toMap(BaseEntity::getId, e -> e));
        //由于精准排除计算节点问题 还存在被过滤掉的费用需要加入
        for (RepaymentPlanFee repaymentPlanFee : existRepaymentPlanFeeList) {
            if (!calRepaymentPlanMap.containsKey(repaymentPlanFee.getId())) {
                allRepaymentPlanFee.add(repaymentPlanFee);
            }
        }
        return allRepaymentPlanFee;
    }
}
