package org.springblade.loan.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.GoodsConstant;
import org.springblade.common.constant.OverdueConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.*;
import org.springblade.expense.dto.ExpenseGoodsConfig;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.finance.vo.ExpenseOrderDetailFinanceVo;
import org.springblade.loan.contant.RepaymentPlanCalEnum;
import org.springblade.loan.dto.*;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageOverdue;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.loan.enums.IouEnum;
import org.springblade.loan.service.*;
import org.springblade.loan.utils.LoanUtils;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.product.common.dto.CapitalPayMethodAndCapitalType;
import org.springblade.product.common.entity.GoodsExpenseRelation;
import org.springblade.product.common.entity.GoodsExpenseRule;
import org.springblade.product.common.entity.Product;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense_relation.service.IBillBankCardaRelationService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.resource.entity.IncomeDetail;
import org.springblade.resource.service.IIncomeDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 融资还款试算
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-02  14:52
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class RepaymentPlanFinanceApplyBizServiceImpl implements IRepaymentPlanFinanceApplyBizService {
    private final IRepaymentPlanBizService repaymentPlanBizService;
    private final ProductDirector productDirector;
    private final IProductExpenseService productExpenseService;

    private final ILoanManageIouService loanManageIouService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;
    private final IIncomeDetailService incomeDetailService;
    private final IRepaymentPlanJsonService repaymentPlanJsonService;
    private final IBillBankCardaRelationService billBankCardaRelationService;
    private final ILoanAlterationHistoryService loanAlterationHistoryService;
    private final ILoanManageOverdueService loanManageOverdueService;
    private final IRepaymentPlanFinanceApplyCommonService repaymentPlanFinanceApplyCommonService;
    /**
     * 可分期节点
     */
    private final List<Integer> repaymentNode = Collections.singletonList(
            ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode());
    /**
     * 期数类型的费用计算节点 仅在列表里的节点需要进行特殊处理
     */
    private final static List<Integer> TERM_FEE_NODE = Arrays.asList(ExpenseConstant.FeeNodeEnum.ADVANCE_REPAYMENT.getCode(),
            ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode(), ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode(), ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode());

    /**
     * 还款试算（未添加费用的情况下）
     *
     * @param costCalculusDto
     * @return
     */
    private RepaymentPlanCal calRepayment(CostCalculusDto costCalculusDto) {
        RepaymentPlanCalReq repaymentPlanCalReq = BeanUtil.copyProperties(costCalculusDto, RepaymentPlanCalReq.class);
        Product product = productDirector.detailBase(costCalculusDto.getGoodsId());
        CapitalPayMethodAndCapitalType capitalPayModeByGoodsId = billBankCardaRelationService.getCapitalPayModeByGoodsId(product);
        //试算采用
        Integer repaymentPlanCalType = getRepaymentPlanCalType(capitalPayModeByGoodsId.getIsBank());
        repaymentPlanCalReq.setRepaymentPlanCalType(repaymentPlanCalType);
        return repaymentPlanBizService.repaymentCal(repaymentPlanCalReq);
    }

    @Override
    public List<ExpenseOrderDetail> calExpenseOrderDetail(CostCalculusDto costCalculusDto, RepaymentPlanCal repaymentPlanCal) {
        ExpenseGoodsConfig expenseGoodsConfig = productExpenseService.getConfigByGoodsAndFeeNode(costCalculusDto.getGoodsId(), Func.toIntList(costCalculusDto.getChargePoint()), costCalculusDto.getFinanceNo());
        return calExpenseOrderDetail(costCalculusDto, repaymentPlanCal, expenseGoodsConfig);
    }

    @Override
    public List<ExpenseOrderDetail> calExpenseOrderDetail(CostCalculusDto costCalculusDto, RepaymentPlanCal repaymentPlanCal, ExpenseGoodsConfig expenseGoodsConfig) {
        //Todo 暂时默认8等待其他业务修改完成后去掉
        if (costCalculusDto.getCurrentFeeNode() == null) {
            costCalculusDto.setCurrentFeeNode(8);
        }
        List<ExpenseOrderDetail> allExpenseDetail = new ArrayList<>();
        List<GoodsExpenseRelation> goodsExpenseRelationList = expenseGoodsConfig.getGoodsExpenseRelationList();
        if (CollUtil.isEmpty(goodsExpenseRelationList)) {
            return new ArrayList<>();
        }

        List<ExpenseOrderDetail> otherGoodsExpenseDetail = calOtherExpenseDetail(goodsExpenseRelationList, expenseGoodsConfig,
                costCalculusDto, repaymentPlanCal);
        allExpenseDetail.addAll(otherGoodsExpenseDetail);
        return allExpenseDetail;
    }

    /**
     * 是否为还款节点
     *
     * @param node
     * @return
     */
    private boolean isRepaymentNode(Integer node) {
        return repaymentNode.contains(node);
    }

    private List<GoodsExpenseRelation> filterByExpenseRuleCalType(ExpenseGoodsConfig expenseGoodsConfig, List<GoodsExpenseRelation> goodsExpenseRelationList, Integer expenseRuleFeeCalType) {
        return
                goodsExpenseRelationList.stream().filter(e -> {
                    GoodsExpenseRule goodsExpenseRule = expenseGoodsConfig.getGoodsExpenseRuleMap().get(e.getGoodsExpenseRuleId());
                    return ObjectUtil.isNotEmpty(goodsExpenseRule);
                }).collect(Collectors.toList());
    }

    @Override
    public List<ExpenseOrderDetailFinanceVo> costCalculus(CostCalculusDto costCalculusDto, RepaymentPlanCal repaymentPlanCal) {
        //费用试算
        List<ExpenseOrderDetail> expenseOrderDetailList = calExpenseOrderDetail(costCalculusDto, repaymentPlanCal);
        //构建费用组
        return buildExpenseOrderDetailFinanceVo(expenseOrderDetailList);
    }

    private List<ExpenseOrderDetail> calOtherExpenseDetail(List<GoodsExpenseRelation> otherGoodsExpenseRelations
            , ExpenseGoodsConfig expenseGoodsConfig, CostCalculusDto costCalculusDto, RepaymentPlanCal repaymentPlanCal) {
        if (CollUtil.isEmpty(otherGoodsExpenseRelations)) {
            return new ArrayList<>();
        }
        Integer currentFeeNode = costCalculusDto.getCurrentFeeNode();
        if (ObjectUtil.isEmpty(currentFeeNode)) {
            throw new IllegalArgumentException("缺少当前计算节点");
        }
        Long userId = AuthUtil.getUserId();
        Long deptId = ObjectUtil.isNotEmpty(AuthUtil.getDeptId()) ? Func.firstLong(AuthUtil.getDeptId()) : -1L;
        Date date = new Date();
        //收费节点 是否在还款节点进行区分
        Map<Boolean, List<GoodsExpenseRelation>> termAndAllExpenseRelations = otherGoodsExpenseRelations.stream()
                .collect(Collectors.partitioningBy(e -> TERM_FEE_NODE.contains(e.getCollectFeesNode())));
        //收费节点不在还款节点的费用关联
        List<GoodsExpenseRelation> allExpenseRelationList = termAndAllExpenseRelations.get(false);
        List<ExpenseOrderDetail> allExpenseOrderDetailList = calByAllExpenseRelationList(allExpenseRelationList, expenseGoodsConfig, costCalculusDto);
        //收费节点在还款节点的费用关联
        List<GoodsExpenseRelation> termExpenseRelationList = termAndAllExpenseRelations.get(true);
        List<ExpenseOrderDetail> termExpenseOrderDetailList = calByTermExpenseRelationList(termExpenseRelationList, expenseGoodsConfig, costCalculusDto, repaymentPlanCal);
        //合并结果
        List<ExpenseOrderDetail> expenseOrderDetailList = Stream.concat(termExpenseOrderDetailList.stream(), allExpenseOrderDetailList.stream()).collect(Collectors.toList());
        //初始化
        initFeedParam(costCalculusDto, userId, deptId, date, expenseOrderDetailList);
        return expenseOrderDetailList;
    }

    /**
     * 计算收费节点不在还款节点的费用关联
     *
     * @param expenseRelationList 收费节点不在还款节点上的费用关联
     * @param expenseGoodsConfig  费用配置
     * @param costCalculusDto     计算参数
     * @return
     */
    private List<ExpenseOrderDetail> calByAllExpenseRelationList(List<GoodsExpenseRelation> expenseRelationList, ExpenseGoodsConfig expenseGoodsConfig, CostCalculusDto costCalculusDto) {
        if (CollUtil.isEmpty(expenseRelationList)) {
            return Collections.emptyList();
        }
        return productExpenseService.calculateExpenseOrderDetailByRelation(expenseGoodsConfig, expenseRelationList, costCalculusDto.getGoodType(), JSONUtil.toJsonStr(costCalculusDto), costCalculusDto.getCurrentFeeNode());
    }

    /**
     * 计算收费节点在还款节点的费用关联
     *
     * @param expenseRelationList 收费节点在还款节点上的费用关联
     * @param expenseGoodsConfig  费用配置
     * @param costCalculusDto     计算参数
     * @param repaymentPlanCal    还款计划
     * @return
     */
    private List<ExpenseOrderDetail> calByTermExpenseRelationList(List<GoodsExpenseRelation> expenseRelationList, ExpenseGoodsConfig expenseGoodsConfig, CostCalculusDto costCalculusDto, RepaymentPlanCal repaymentPlanCal) {
        if (CollUtil.isEmpty(expenseRelationList)) {
            return Collections.emptyList();
        }
        List<ExpenseOrderDetail> otherExpenseDetailAll = new ArrayList<>();
        List<StagRecordVO> stagRecords = repaymentPlanCal.getStagRecords();
        Integer termSize = LoanUtils.getTermCount(stagRecords, costCalculusDto.getLoadTermUnit());
        // 一次性收取的费用类型
        Set<Long> oneTimePay = new HashSet<>();
        for (int i = 0; i < termSize; i++) {
            StagRecordVO stagRecordVO = stagRecords.get(i);
            costCalculusDto.setStagRecordVO(stagRecordVO);
            //计算后排除掉已经计算过的一次性费用
            List<ExpenseOrderDetail> termExpenseOrderDetail = productExpenseService
                    .calculateExpenseOrderDetailByRelation(expenseGoodsConfig, expenseRelationList, costCalculusDto.getGoodType()
                            , JSONUtil.toJsonStr(costCalculusDto), costCalculusDto.getCurrentFeeNode())
                    .stream().filter(e -> !oneTimePay.contains(e.getExpenseTypeId())).collect(Collectors.toList());
            final int term = i + 1;
            for (ExpenseOrderDetail expenseOrderDetail : termExpenseOrderDetail) {
                expenseOrderDetail.setRepaymentTerm(term);
                if (ExpenseConstant.ChargeMethodEnum.ONE_TIME_PAYMENT.getCode().equals(expenseOrderDetail.getChargeMethod())) {
                    oneTimePay.add(expenseOrderDetail.getExpenseTypeId());
                }
            }
            //重置
            costCalculusDto.setStagRecordVO(null);
            otherExpenseDetailAll.addAll(termExpenseOrderDetail);
        }
        return otherExpenseDetailAll;
    }

    private static void initFeedParam(CostCalculusDto costCalculusDto, Long userId, Long deptId, Date date, List<ExpenseOrderDetail> orderDetailList) {
        for (ExpenseOrderDetail expenseOrderDetail : orderDetailList) {
            //提前设置id及创建人 方便后续生成还款计划费用时进行关联
            expenseOrderDetail.setId(IdWorker.getId());
            expenseOrderDetail.setCreateUser(userId);
            expenseOrderDetail.setCreateDept(deptId);
            expenseOrderDetail.setCreateTime(date);
        }
    }


    @Override
    public CostCalculusVO costCalculus(CostCalculusDto costCalculusDto) {
        //还款计划试算
        RepaymentPlanCal repaymentPlanCalReq = getRepaymentPlanCalReq(costCalculusDto);
        //费用配置
        List<Integer> feeNode = Func.toIntList(costCalculusDto.getChargePoint());
        ExpenseGoodsConfig expenseGoodsConfig = productExpenseService.getConfigByGoodsAndFeeNode(costCalculusDto.getGoodsId(), feeNode, costCalculusDto.getFinanceNo());
        //费用试算
        List<ExpenseOrderDetail> expenseOrderDetails = calExpenseOrderDetail(costCalculusDto, repaymentPlanCalReq, expenseGoodsConfig);
        //将费用试算结果加入到还款计划费用中
        return costCalculus(expenseOrderDetails, repaymentPlanCalReq);
    }

    @Override
    public RepaymentPlanCal getRepaymentPlanCalReq(CostCalculusDto costCalculusDto) {
        Product product = productDirector.detailBase(costCalculusDto.getGoodsId());
        RepaymentPlanCalReq repaymentPlanCalReq = BeanUtil.copyProperties(costCalculusDto, RepaymentPlanCalReq.class);
        CapitalPayMethodAndCapitalType capitalPayMethod = getCapitalPayMethod(costCalculusDto.getGoodsId());
        repaymentPlanCalReq.setPayMethod(capitalPayMethod.getPlatformCostPayMode());
        //1、获取计算方式进行还款试算
        Integer repaymentPlanCalType = getRepaymentPlanCalType(capitalPayMethod.getIsBank());
        repaymentPlanCalReq.setRepaymentPlanCalType(repaymentPlanCalType);
        repaymentPlanCalReq.setInterestDay(ObjectUtil.isEmpty(product.getInterestDay()) ? 0 : product.getInterestDay());
        return repaymentPlanBizService.repaymentCal(repaymentPlanCalReq);
    }

    @Override
    public CostCalculusVO costCalculus(CostCalculusDto costCalculusDto, List<ExpenseOrderDetail> expenseOrderDetailList) {
        //还款试算
        RepaymentPlanCal repaymentPlanCalReq = getRepaymentPlanCalReq(costCalculusDto);
        if (CollUtil.isEmpty(expenseOrderDetailList)) {
            CostCalculusVO costCalculusVO = new CostCalculusVO();
            costCalculusVO.setShowRepaymentPlan(repaymentPlanCalReq);
            costCalculusVO.setExpenseOrderDetailFinanceVos(Collections.emptyList());
            return costCalculusVO;
        }
        //费用及还款计划费用试算
        return costCalculus(expenseOrderDetailList, repaymentPlanCalReq);
    }

    @Override
    public CostCalculusVO costCalculus(String financeNo, Integer type, List<ExpenseOrderDetail> expenseOrderDetailList) {
        CostCalculusVO oldCostCalculusVO = costCalculusByFinanceNo(financeNo, type);
        RepaymentPlanCal showRepaymentPlan = oldCostCalculusVO.getShowRepaymentPlan();
        if (CollUtil.isEmpty(expenseOrderDetailList)) {
            CostCalculusVO costCalculusVO = new CostCalculusVO();
            costCalculusVO.setShowRepaymentPlan(showRepaymentPlan);
            costCalculusVO.setExpenseOrderDetailFinanceVos(Collections.emptyList());
            return costCalculusVO;
        }
        //费用及还款计划费用试算
        return costCalculus(expenseOrderDetailList, showRepaymentPlan);
    }

    private CostCalculusVO costCalculus(List<ExpenseOrderDetail> expenseOrderDetailList, RepaymentPlanCal repaymentPlanCal) {
        CostCalculusVO costCalculusVO = new CostCalculusVO();
        List<ExpenseOrderDetail> newExpenseDetail = new ArrayList<>();
        if (CollUtil.isNotEmpty(expenseOrderDetailList)) {
            newExpenseDetail.addAll(expenseOrderDetailList);
        }
        //还款节点计划费用处理
        Map<Integer, List<ExpenseOrderDetail>> termExpenseOrderDetail = expenseOrderDetailList.stream()
                .filter(e -> ObjectUtil.isNotEmpty(e.getRepaymentTerm())).collect(Collectors.groupingBy((ExpenseOrderDetail::getRepaymentTerm)));
        List<StagRecordVO> stagRecords = repaymentPlanCal.getStagRecords();
        Integer termCount = LoanUtils.getTermCount(stagRecords, repaymentPlanCal.getLoanTermUnit());
        for (int i = 0; i < termCount; i++) {
            List<ExpenseOrderDetail> expenseOrderDetails = termExpenseOrderDetail.get(i + 1);
            if (CollUtil.isNotEmpty(expenseOrderDetails)) {
                StagRecordVO stagRecordVO = stagRecords.get(i);
                final Integer term = i + 1;
                stagRecordVO.setPlanFeeLists(expenseOrderDetails.stream()
                        .map(e -> buildBaseRepaymentPlanFee(e.getAmount(), e, term)).collect(Collectors.toList()));
            }
        }
        costCalculusVO.setShowRepaymentPlan(repaymentPlanCal);
        costCalculusVO.setExpenseOrderDetailFinanceVos(buildExpenseOrderDetailFinanceVo(newExpenseDetail));
        return costCalculusVO;
    }

    private RepaymentPlanFee buildBaseRepaymentPlanFee(BigDecimal amount, ExpenseOrderDetail expenseOrderDetail, Integer term) {
        RepaymentPlanFee repaymentPlanFee = new RepaymentPlanFee();
        repaymentPlanFee.setFeeTypeName(expenseOrderDetail.getFeeNameType());
        repaymentPlanFee.setFeeName(expenseOrderDetail.getName());
        repaymentPlanFee.setExpenseKey(expenseOrderDetail.getExpenseType());
        repaymentPlanFee.setExpenseTypeId(expenseOrderDetail.getExpenseTypeId());
        repaymentPlanFee.setAmount(amount);
        repaymentPlanFee.setCollectFeeMethod(expenseOrderDetail.getChargeMethod());
        repaymentPlanFee.setPeriod(term);
        repaymentPlanFee.setRelationExpensesId(expenseOrderDetail.getId());
        repaymentPlanFee.setFeeNode(expenseOrderDetail.getFeeNode());
        repaymentPlanFee.setCollectFeeNode(expenseOrderDetail.getCollectFeesNode());
        repaymentPlanFee.setExpenseOrderDetail(expenseOrderDetail);
        repaymentPlanFee.setFixAmount(BigDecimal.ZERO);
        //手填 则赋值
        if (GoodsConstant.CALCULATION_ONE == expenseOrderDetail.getCalculation()) {
            repaymentPlanFee.setFixAmount(expenseOrderDetail.getAmount());
            repaymentPlanFee.setFix(CommonConstant.YES);
        } else {
            repaymentPlanFee.setFix(CommonConstant.NO);
        }
        repaymentPlanFee.setExpenseOrderDetailStr(JSONUtil.toJsonStr(expenseOrderDetail));
        return repaymentPlanFee;
    }

    @Override
    public RepaymentPlanFee buildRepaymentPlanFee(BigDecimal amount, ExpenseOrderDetail expenseOrderDetail, Integer term, LoanManageRepaymentPlan loanManageRepaymentPlan) {
        RepaymentPlanFee repaymentPlanFee = buildBaseRepaymentPlanFee(amount, expenseOrderDetail, term);
        repaymentPlanFee.setPlanId(loanManageRepaymentPlan.getId());
        repaymentPlanFee.setIouNo(loanManageRepaymentPlan.getIouNo());
        repaymentPlanFee.setIouId(loanManageRepaymentPlan.getIouId());
        repaymentPlanFee.setUserId(loanManageRepaymentPlan.getUserId());
        repaymentPlanFee.setFinanceApplyId(loanManageRepaymentPlan.getFinanceApplyId());
        repaymentPlanFee.setCustomerGoodsId(loanManageRepaymentPlan.getCustomerGoodsId());
        repaymentPlanFee.setPeriod(term);
        repaymentPlanFee.setRepaymentTime(loanManageRepaymentPlan.getRepaymentTime());
        repaymentPlanFee.setGoodsType(loanManageRepaymentPlan.getGoodsType());
        repaymentPlanFee.setGoodsId(loanManageRepaymentPlan.getGoodsId());
        repaymentPlanFee.setRepaymentStatus(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode());
        repaymentPlanFee.setPlanReductionAmount(BigDecimal.ZERO);
        return repaymentPlanFee;
    }

    @Override
    public BigDecimal getAmountTotalByRepaymentPlanFee(List<RepaymentPlanFee> repaymentPlanFeeList, Integer feeNode) {
        if (CollUtil.isEmpty(repaymentPlanFeeList)) {
            return BigDecimal.ZERO;
        }
        return repaymentPlanFeeList.stream().filter(e -> feeNode.equals(e.getFeeNode())).map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public Boolean saveCalJson(CostCalculusVO costCalculusVO, Integer type, String financeNo) {
        return repaymentPlanJsonService.saveOrUpdatePlanJson(financeNo, type, JSONUtil.toJsonStr(costCalculusVO));
    }


    @Override
    public String getCalJson(Integer type, String financeNo) {
        return repaymentPlanJsonService.getPlanJson(financeNo, type);
    }

    @Override
    public String getCalJson(Integer type, String bizNo, Boolean ignoreNull) {
        return repaymentPlanJsonService.getPlanJson(bizNo, type, ignoreNull);
    }

    @Override
    public RepaymentPlanDTO saveRepaymentPlanByFinanceApply(FinanceApply financeApply, RepaymentPlanCal repaymentPlanCal, boolean isFirst) {
        Product product = productDirector.detailBase(financeApply.getGoodsId());
        //创建借款订单
        LoanManageIou loanManageIou = loanManageIouService.saveLoanManageIou(financeApply);
        //创建还款计划订单
        List<LoanManageRepaymentPlanDTO> repaymentPlanDTOS = buildRepaymentPlanList(loanManageIou, repaymentPlanCal, product, financeApply.getUserId()
                , financeApply.getId(), financeApply.getCustomerGoodsId(), financeApply.getAnnualInterestRate(), financeApply.getServiceRate());
        //创建还款计划订单
        RepaymentPlanDTO repaymentPlanDTO = savePlan(loanManageIou, repaymentPlanDTOS);
        //如果是第一次保存还款记录 则进行保存还款记录
        if (isFirst) {
            loanAlterationHistoryService.createFirstAlterationLog(repaymentPlanDTO.getLoanManageRepaymentPlanList(), loanManageIou.getId());
        }
        return repaymentPlanDTO;
    }

    @Override
    public RepaymentPlanDTO saveRepaymentPlanByFinanceApply(FinanceApply financeApply, List<LoanManageRepaymentPlanDTO> repaymentPlanDTOS, boolean isFirst) {
        //创建借款订单
        LoanManageIou loanManageIou = loanManageIouService.saveLoanManageIou(financeApply);
        //创建还款计划订单
        RepaymentPlanDTO repaymentPlanDTO = savePlan(loanManageIou, repaymentPlanDTOS);
        //如果是第一次保存还款记录 则进行保存还款记录
        if (isFirst) {
            loanAlterationHistoryService.createFirstAlterationLog(repaymentPlanDTO.getLoanManageRepaymentPlanList(), loanManageIou.getId());
        }
        return repaymentPlanDTO;
    }


    @Override
    public RepaymentPlanDTO saveRepaymentPlan(LoanManageIou loanManageIou, RepaymentPlanCal repaymentPlanCal, Long goodsId, Long financeApplyId
            , Long customerGoodsId, BigDecimal annualInterestRate) {
        Product product = productDirector.detailBase(goodsId);
        //创建还款计划订单
        List<LoanManageRepaymentPlanDTO> repaymentPlanDTOS = buildRepaymentPlanList(loanManageIou, repaymentPlanCal, product, loanManageIou.getUserId()
                , financeApplyId, customerGoodsId, annualInterestRate, BigDecimal.ZERO);
        return savePlan(loanManageIou, repaymentPlanDTOS);
    }

    private RepaymentPlanDTO savePlan(LoanManageIou loanManageIou, List<LoanManageRepaymentPlanDTO> repaymentPlanDTOS) {
        for (LoanManageRepaymentPlanDTO repaymentPlanDTO : repaymentPlanDTOS) {
            loanManageRepaymentPlanService.save(repaymentPlanDTO);
        }
        //创建还款计划费用单
        List<RepaymentPlanFee> repaymentPlanFeeList = buildRepaymentPlanFeeList(repaymentPlanDTOS);
        if (CollUtil.isNotEmpty(repaymentPlanFeeList)) {
            repaymentPlanFeeService.saveBatch(repaymentPlanFeeList);
        }
        RepaymentPlanDTO repaymentPlanDTO = new RepaymentPlanDTO();
        repaymentPlanDTO.setLoanManageRepaymentPlanList(repaymentPlanDTOS);
        repaymentPlanDTO.setLoanManageIou(loanManageIou);
        return repaymentPlanDTO;
    }

    @Override
    public RepaymentPlanDTO saveRepaymentPlanByFinanceApply(FinanceApply financeApply, Integer type, String financeNo, Integer feeNode, boolean isFirst) {
        return saveRepaymentPlanByFinanceApply(financeApply, type, financeNo, feeNode, isFirst, CostCalculusDto.class);
    }

    @Override
    public RepaymentPlanDTO saveRepaymentPlanByFinanceApply(FinanceApply financeApply, Integer type, String financeNo, Integer feeNode, boolean isFirst, Class<? extends CostCalculusDto> clazz) {
        //1、获取本场景所有费用详情
        List<ExpenseOrderDetail> expenseOrderDetails = productExpenseService.listExpenseOrderDetailByFinanceAndType(financeNo, type);
        //2、还款计算试算 将原还款计划中添加当前收费节点的分期费用
        CostCalculusVO costCalculusVO = costCalculus(financeNo, type, expenseOrderDetails);
        //3、使用已生成的费用详情进行还款试算 并生成结局单、还款计划、还款计划费用
        RepaymentPlanCal showRepaymentPlan = costCalculusVO.getShowRepaymentPlan();
        RepaymentPlanDTO repaymentPlanDTO = saveRepaymentPlanByFinanceApply(financeApply, showRepaymentPlan, isFirst);
        //4、拼凑返回体 返回所有费用及未包含计算节点=收费节点的费用
        List<ExpenseOrderDetail> expenseOrderDetailList = costCalculusVO.getExpenseOrderDetailList();
        if (CollUtil.isNotEmpty(expenseOrderDetailList)) {
            repaymentPlanDTO.setExpenseOrderDetailList(expenseOrderDetailList);
            List<ExpenseOrderDetail> unContainNowExpenseOrderDetailList = expenseOrderDetailList.stream()
                    .filter(e -> !e.getCollectFeesNode().equals(feeNode)).collect(Collectors.toList());
            repaymentPlanDTO.setUnContainNowExpenseOrderDetailList(unContainNowExpenseOrderDetailList);
        }
        return repaymentPlanDTO;
    }

    private List<RepaymentPlanFee> buildRepaymentPlanFeeList(List<LoanManageRepaymentPlanDTO> repaymentPlanDTOS) {
        List<RepaymentPlanFee> repaymentPlanFeeList = new LinkedList<>();
        for (LoanManageRepaymentPlanDTO repaymentPlanDTO : repaymentPlanDTOS) {
            if (CollUtil.isNotEmpty(repaymentPlanDTO.getRepaymentPlanFeeList())) {
                for (RepaymentPlanFee repaymentPlanFee : repaymentPlanDTO.getRepaymentPlanFeeList()) {
                    //填充信息
                    fillRepaymentPlanFeeByPlan(repaymentPlanDTO, repaymentPlanFee);
                    repaymentPlanFeeList.add(repaymentPlanFee);
                }
            }
        }
        return repaymentPlanFeeList;
    }

    private void fillRepaymentPlanFeeByPlan(LoanManageRepaymentPlanDTO repaymentPlanDTO, RepaymentPlanFee repaymentPlanFee) {
        repaymentPlanFee.setPlanId(repaymentPlanDTO.getId());
        repaymentPlanFee.setIouNo(repaymentPlanDTO.getIouNo());
        repaymentPlanFee.setRepaymentTime(repaymentPlanDTO.getRepaymentTime());
        repaymentPlanFee.setUserId(repaymentPlanDTO.getUserId());
        repaymentPlanFee.setFinanceApplyId(repaymentPlanDTO.getFinanceApplyId());
        repaymentPlanFee.setCustomerGoodsId(repaymentPlanDTO.getCustomerGoodsId());
        repaymentPlanFee.setGoodsId(repaymentPlanDTO.getGoodsId());
        repaymentPlanFee.setRepaymentStatus(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode());
        repaymentPlanFee.setIouId(repaymentPlanDTO.getIouId());
        repaymentPlanFee.setPlanReductionAmount(BigDecimal.ZERO);
        repaymentPlanFee.setGoodsType(repaymentPlanDTO.getGoodsType());
    }


    @Override
    public CostCalculusVO costCalculusByFinanceNo(String financeNo, Integer type) {
        return JSONUtil.toBean(repaymentPlanJsonService.getPlanJson(financeNo, type), CostCalculusVO.class);
    }

    @Override
    public CostCalculusVO costCalculusByFinanceNo(String financeNo, Integer type, Class<? extends CostCalculusDto> clazz) {
        CostCalculusDto costCalculusDto = JSONUtil.toBean(repaymentPlanJsonService.getPlanJson(financeNo, type)
                , clazz);
        if (costCalculusDto.isExpenseOrderDetailPass()) {
            List<ExpenseOrderDetail> expenseOrderDetails = productExpenseService.listExpenseOrderDetailByIds(Func.toLongList(costCalculusDto.getExpenseOrderDetailPassIds()));
            return costCalculus(costCalculusDto, expenseOrderDetails);
        } else {
            return costCalculus(costCalculusDto);
        }
    }

    @Override
    public CostCalculusVO costCalculus(RepaymentPlanCal repaymentPlanCal, List<RepaymentPlanFee> repaymentPlanFee) {
        List<ExpenseOrderDetail> expenseOrderDetails = repaymentPlanFinanceApplyCommonService.transferToExpenseDetail(repaymentPlanFee);
        CostCalculusVO costCalculusVO = new CostCalculusVO();
        //还款节点计划费用处理
        Map<Integer, List<RepaymentPlanFee>> repaymentPlanFeeMap = repaymentPlanFee.stream()
                .filter(e -> ObjectUtil.isNotEmpty(e.getPeriod())).collect(Collectors.groupingBy((RepaymentPlanFee::getPeriod)));
        List<StagRecordVO> stagRecords = repaymentPlanCal.getStagRecords();
        Integer termCount = LoanUtils.getTermCount(stagRecords, repaymentPlanCal.getLoanTermUnit());
        for (int i = 0; i < termCount; i++) {
            List<RepaymentPlanFee> repaymentPlanFeeList = repaymentPlanFeeMap.get(i + 1);
            if (CollUtil.isNotEmpty(repaymentPlanFeeList)) {
                StagRecordVO stagRecordVO = stagRecords.get(i);
                stagRecordVO.setPlanFeeLists(repaymentPlanFeeList);
            }
        }
        costCalculusVO.setShowRepaymentPlan(repaymentPlanCal);
        costCalculusVO.setExpenseOrderDetailFinanceVos(buildExpenseOrderDetailFinanceVo(expenseOrderDetails));
        return costCalculusVO;
    }

    @Override
    public CapitalPayMethodAndCapitalType getCapitalPayMethod(Long goodsId) {
        Product product = productDirector.detailBase(goodsId);
        return billBankCardaRelationService.getCapitalPayModeByGoodsId(product);
    }

    /**
     * 创建还款计划
     *
     * @param loanManageIou      借据单
     * @param repaymentPlanCal   还款试算结果
     * @param product            产品
     * @param userId             申请用户id
     * @param financeApplyId     融资编号
     * @param customerGoodsId    客户产品id
     * @param annualInterestRate 年利率
     * @return
     */
    @Override
    public List<LoanManageRepaymentPlanDTO> buildRepaymentPlanList(LoanManageIou loanManageIou, RepaymentPlanCal repaymentPlanCal
            , Product product, Long userId
            , Long financeApplyId, Long customerGoodsId, BigDecimal annualInterestRate, BigDecimal serviceFeeRate) {
        /**
         * 默认不开启提前还款
         */
        List<StagRecordVO> stagRecords = repaymentPlanCal.getStagRecords();
        List<LoanManageRepaymentPlanDTO> repaymentPlanList = stagRecords.stream()
                .filter(stagRecordVO -> Objects.nonNull(stagRecordVO.getRefundTime()))
                .filter(stagRecordVO -> StringUtil.isNotBlank(stagRecordVO.getTerm())).map(stagRecord -> {
                    LoanManageRepaymentPlan plan = LoanManageRepaymentPlan.builder()
                            .iouNo(loanManageIou.getIouNo())
                            .repaymentTime(stagRecord.getRefundTime())
                            .periodUnit(loanManageIou.getPeriodUnit())
                            .period(Integer.valueOf(stagRecord.getTerm()))
                            .userId(userId)
                            .principal(stagRecord.getMonthlyPrincipal())
                            .capitalId(loanManageIou.getCapitalId())
                            .financeApplyId(financeApplyId)
                            .interest(stagRecord.getMonthlyInterest())
                            .customerGoodsId(customerGoodsId)
                            .prepaymentType(ObjectUtil.isEmpty(product.getPrepaymentType()) ? 1 : product.getPrepaymentType())
                            .repaymentType(loanManageIou.getRepaymentType())
                            .goodsId(product.getId())
                            .iouId(loanManageIou.getId())
                            .goodsType(product.getType())
                            .annualInterestRate(annualInterestRate)
                            .penaltyInterest(BigDecimal.ZERO)
                            .overdueDay(0)
                            .overdue(RepaymentConstant.RepaymentPlanOverdueEnum.NORMAL.getCode())
                            .serviceFee(BigDecimal.ZERO)
                            .serviceFeeRate(serviceFeeRate)
                            .interestDay(ObjectUtil.isEmpty(product.getInterestDay()) ? 0 : product.getInterestDay())
                            .expenseDay(0)
                            .financeNo(loanManageIou.getFinanceNo())
                            .startTime(stagRecord.getStartTime())
                            .originRepaymentTime(stagRecord.getRefundTime())
                            .expenseRate(BigDecimal.ZERO)
                            .reductionInterest(BigDecimal.ZERO)
                            .build();
                    LoanManageRepaymentPlanDTO planDTO = BeanUtil.copyProperties(plan, LoanManageRepaymentPlanDTO.class);
                    planDTO.setRepaymentPlanFeeList(stagRecord.getPlanFeeLists());
                    return planDTO;
                }).collect(Collectors.toList());
        return repaymentPlanList;
    }

    private LocalDate getFirstRepaymentDate(Integer loadTermUnit, LocalDate startTime, Integer loanTerm) {
        LocalDate firstRepaymentDate;
        if (GoodsEnum.TERM.getCode().equals(loadTermUnit)) {
            firstRepaymentDate = startTime.plusMonths(1);
            if (firstRepaymentDate.getDayOfMonth() > 28) {
                return firstRepaymentDate.withDayOfMonth(28);
            }
        } else {
            firstRepaymentDate = startTime.plusDays(loanTerm);
        }
        return firstRepaymentDate;
    }

    @Override
    public List<ExpenseOrderDetailFinanceVo> buildExpenseOrderDetailFinanceVo(List<ExpenseOrderDetail> expenseOrderDetailList) {
        return repaymentPlanFinanceApplyCommonService.buildExpenseOrderDetailFinanceVo(expenseOrderDetailList);
    }

    @Override
    public RepaymentPlanCal buildRepaymentPlanCal(BigDecimal principal, BigDecimal interest, BigDecimal yearRate, BigDecimal dayRate) {
        RepaymentPlanCal repaymentPlanCal = new RepaymentPlanCal();
        repaymentPlanCal.setTotal(principal.add(interest));
        repaymentPlanCal.setInterest(interest);
        repaymentPlanCal.setPrincipal(principal);
        repaymentPlanCal.setYearRate(yearRate);
        repaymentPlanCal.setDayRate(dayRate);
        return repaymentPlanCal;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<LoanManageRepaymentPlan> delayRepaymentTime(Integer delayDay, List<Long> repaymentPlanIds, BigDecimal delayRate, BigDecimal interest) {
        if (!loanManageRepaymentPlanService.isUsing(repaymentPlanIds)) {
            throw new IllegalArgumentException("含有完结单,延迟还款计划失败");
        }
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listByIds(repaymentPlanIds);
        for (LoanManageRepaymentPlan loanManageRepaymentPlan : loanManageRepaymentPlans) {
            loanManageRepaymentPlan.setRepaymentTime(loanManageRepaymentPlan.getRepaymentTime().plusDays(delayDay));
            loanManageRepaymentPlan.setExpenseDay(delayDay);
            loanManageRepaymentPlan.setInterest(interest);
            loanManageRepaymentPlan.setExpenseRate(delayRate);
        }
        loanManageRepaymentPlanService.updateBatchById(loanManageRepaymentPlans);
        return loanManageRepaymentPlans;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delayDelayRate(List<Long> repaymentPlanIds, BigDecimal delayRate) {
        loanManageRepaymentPlanService.update(Wrappers.<LoanManageRepaymentPlan>lambdaUpdate().in(LoanManageRepaymentPlan::getId, repaymentPlanIds)
                .set(LoanManageRepaymentPlan::getExpenseRate, delayRate));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeRepaymentPlan(FinanceApply financeApply, List<Long> planIds) {
        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financeApply.getFinanceNo());
        if (ObjectUtil.isEmpty(loanManageIou)) {
            throw new ServiceException("借据单不存在");
        }
        closeRepaymentPlan(loanManageIou, planIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeRepaymentPlan(LoanManageIou loanManageIou, List<Long> planIds) {
        if (!loanManageIouService.canClose(loanManageIou)) {
            throw new ServiceException("借据单处于不可关闭状态");
        }
        List<LoanManageRepaymentPlan> loanManageRepaymentPlanList = loanManageRepaymentPlanService.list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery().select(LoanManageRepaymentPlan::getId)
                .in(CollUtil.isNotEmpty(planIds), LoanManageRepaymentPlan::getId, planIds)
                .eq(LoanManageRepaymentPlan::getIouNo, loanManageIou.getIouNo())
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode()));
        List<Long> needCloseRepaymentPlanIds = loanManageRepaymentPlanList.stream().map(LoanManageRepaymentPlan::getId).collect(Collectors.toList());
        //关闭还款计划
        if (CollUtil.isNotEmpty(needCloseRepaymentPlanIds)) {
            //是否有正在还款中
            if (loanManageRepaymentService.hasPayingByPlanIds(needCloseRepaymentPlanIds)) {
                throw new ServiceException("存在正在还款中订单，请等待还款后继续业务");
            }
            loanManageRepaymentPlanService.changeRepaymentStatus(needCloseRepaymentPlanIds, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.INVALID.getCode(),
                    RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode());
            //完结还款计划
            repaymentPlanFeeService.changeRepaymentStatusByRepaymentId(needCloseRepaymentPlanIds, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeIouByFinanceApplyNo(String financeNo) {
        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financeNo);
        //关闭借据单
        loanManageIouService.changeStatus(Collections.singletonList(loanManageIou.getId()), IouEnum.INVALID.getCode());
        //关闭还款计划
        closeRepaymentPlan(loanManageIou, null);
        //关闭逾期列表
        closeOverDueList(loanManageIou);
    }

    private void closeOverDueList(LoanManageIou loanManageIou) {
        List<Integer> unPayStatus = Arrays.asList(OverdueConstant.OverdueStatus.OVERDUE_CONSULTING.getCode()
                , OverdueConstant.OverdueStatus.ARTIFICIAL.getCode()
                , OverdueConstant.OverdueStatus.SMS.getCode());
        loanManageOverdueService.update(Wrappers.<LoanManageOverdue>lambdaUpdate().eq(LoanManageOverdue::getIouNo, loanManageIou.getIouNo())
                .in(LoanManageOverdue::getStatus, unPayStatus).set(LoanManageOverdue::getStatus, OverdueConstant.OverdueStatus.OVERDUE_CONSULT_INVALID.getCode()));
    }

    @Override
    public CostCalculusVO transferByLoan(LoanInfoDTO loanInfoDTO) {
        CostCalculusVO costCalculusVO = new CostCalculusVO();
        RepaymentPlanCal repaymentPlanCal = buildRepaymentPlanCalByRepaymentPlan(loanInfoDTO.getRepaymentInfoList(), loanInfoDTO);
        List<RepaymentPlanFee> repaymentPlanFeeList = loanInfoDTO.listUsingRepaymentPlanFee();
        List<ExpenseOrderDetail> collect = repaymentPlanFinanceApplyCommonService.transferToExpenseDetail(repaymentPlanFeeList);
        costCalculusVO.setShowRepaymentPlan(repaymentPlanCal);
        if (CollUtil.isNotEmpty(collect)) {
            List<ExpenseOrderDetailFinanceVo> expenseOrderDetailFinanceVos = buildExpenseOrderDetailFinanceVo(collect);
            costCalculusVO.setExpenseOrderDetailFinanceVos(expenseOrderDetailFinanceVos);
        } else {
            costCalculusVO.setExpenseOrderDetailFinanceVos(Collections.emptyList());
        }
        return costCalculusVO;
    }


    @Override
    public CostCalculusVO transferByLoan(List<RepaymentInfoDTO> repaymentInfoDTOList, LoanManageIou loanInfoDTO) {
        CostCalculusVO costCalculusVO = new CostCalculusVO();
        RepaymentPlanCal repaymentPlanCal = buildRepaymentPlanCalByRepaymentPlan(repaymentInfoDTOList, loanInfoDTO);
        List<RepaymentPlanFee> repaymentPlanFeeList = repaymentInfoDTOList.stream().flatMap(e -> e.getRepaymentPlanFeeList().stream()).collect(Collectors.toList());
        List<ExpenseOrderDetail> collect = repaymentPlanFinanceApplyCommonService.transferToExpenseDetail(repaymentPlanFeeList);
        costCalculusVO.setShowRepaymentPlan(repaymentPlanCal);
        if (CollUtil.isNotEmpty(collect)) {
            List<ExpenseOrderDetailFinanceVo> expenseOrderDetailFinanceVos = buildExpenseOrderDetailFinanceVo(collect);
            costCalculusVO.setExpenseOrderDetailFinanceVos(expenseOrderDetailFinanceVos);
        } else {
            costCalculusVO.setExpenseOrderDetailFinanceVos(Collections.emptyList());
        }
        return costCalculusVO;
    }

    @Override
    public List<RepaymentPlanFee> getRepaymentPlanFeeByExpenseOrderList(CostCalculusVO costCalculusVO, Integer feeNode) {
        return costCalculusVO.getShowRepaymentPlan().getStagRecords().stream().filter(e -> CollUtil.isNotEmpty(e.getPlanFeeLists())).flatMap(e -> e.getPlanFeeLists().stream())
                .filter(e -> e.getFeeNode().equals(feeNode)).collect(Collectors.toList());
    }

    @Override
    public void appendRepaymentFee(List<RepaymentPlanFee> repaymentPlanFeeList, LoanInfoDTO loanInfoDTO) {
        if (GoodsEnum.TERM.getCode().equals(loanInfoDTO.getPeriodUnit())) {
            Map<Integer, RepaymentInfoDTO> repaymentInfoDTOMap = loanInfoDTO.listUsingRepaymentInfo()
                    .stream().collect(Collectors.toMap(RepaymentInfoDTO::getPeriod, e -> e));
            repaymentPlanFeeList.forEach(e -> {
                RepaymentInfoDTO repaymentInfoDTO = repaymentInfoDTOMap.get(e.getPeriod());
                fillAppendRepaymentPlanFeeInfo(e, repaymentInfoDTO);
            });
        } else {
            RepaymentInfoDTO repaymentInfoDTO = loanInfoDTO.getRepaymentInfoList().get(0);
            repaymentPlanFeeList.forEach(e -> {
                fillAppendRepaymentPlanFeeInfo(e, repaymentInfoDTO);
            });
        }
        repaymentPlanFeeService.saveOrUpdateBatch(repaymentPlanFeeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepaymentInfo(List<RepaymentInfoDTO> repaymentInfoDTOS) {
        //更新还款计划
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = cn.hutool.core.bean.BeanUtil.copyToList(repaymentInfoDTOS, LoanManageRepaymentPlan.class);
        loanManageRepaymentPlanService.updateBatchById(loanManageRepaymentPlans);
        List<IncomeDetail> allInterestList = new LinkedList<>();
        //增加新的利息减免记录
        List<IncomeDetail> interestList = repaymentInfoDTOS.stream()
                .flatMap(e -> e.getReductionInterestList().stream())
                .filter(IncomeDetail::isNewRegen)
                .collect(Collectors.toList());
        allInterestList.addAll(interestList);
        //更新还款计划
        List<RepaymentPlanFee> repaymentPlanFees = repaymentInfoDTOS.stream()
                .filter(e -> CollUtil.isNotEmpty(e.getRepaymentPlanFeeList()))
                .flatMap(e -> e.getRepaymentPlanFeeList().stream()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(repaymentPlanFees)) {
            List<Long> repaymentPlanIds = repaymentPlanFees.stream().map(RepaymentPlanFee::getId).collect(Collectors.toList());
            List<Long> existIds = repaymentPlanFeeService.listExistIds(repaymentPlanIds);
            Map<Boolean, List<RepaymentPlanFee>> collect = repaymentPlanFees.stream()
                    .collect(Collectors.partitioningBy(e -> !existIds.contains(e.getId())));
            //新生成的还款计划费用处理
            List<RepaymentPlanFee> newRepaymentPlanFeeList = collect.get(true);
            if (CollUtil.isNotEmpty(newRepaymentPlanFeeList)) {
                repaymentPlanFeeService.saveBatch(newRepaymentPlanFeeList);
                //增加新的减免记录
                allInterestList.addAll(newRepaymentPlanFeeList.stream().flatMap(e -> e.getReductionFeeList().stream())
                        .filter(IncomeDetail::isNewRegen).collect(Collectors.toList()));
            }
            //非新生成的还款计划费用处理
            List<RepaymentPlanFee> repaymentPlanFeeList = collect.get(false);
            if (CollUtil.isNotEmpty(repaymentPlanFeeList)) {
                repaymentPlanFeeService.updateBatchById(repaymentPlanFeeList);
                //增加新的减免记录
                allInterestList.addAll(repaymentPlanFeeList.stream().flatMap(e -> e.getReductionFeeList().stream())
                        .filter(IncomeDetail::isNewRegen).collect(Collectors.toList()));
            }
        }
        //减免增加
        if (CollUtil.isNotEmpty(allInterestList)) {
            incomeDetailService.saveAll(allInterestList);
        }
    }

    /**
     * 资方放款还款计划生成，随借随还模式
     * @param capitalLoanCallBackRepaymentPlan
     * @return
     */
    @Override
    public RepaymentPlanDTO saveRepaymentPlanByFinanceApply(CapitalLoanCallBackRepaymentPlan capitalLoanCallBackRepaymentPlan) {
        FinanceApply financeApply = capitalLoanCallBackRepaymentPlan.getFinanceApply();
        LocalDate startTime = capitalLoanCallBackRepaymentPlan.getStartTime();
        LocalDate refundTime = capitalLoanCallBackRepaymentPlan.getRefundTime();
        Boolean interestIsZero = capitalLoanCallBackRepaymentPlan.getInterestIsZero();
        Integer feeNode = capitalLoanCallBackRepaymentPlan.getFeeNodeEnum().getCode();
        Integer type = capitalLoanCallBackRepaymentPlan.getPlatformExpensesEnum().getCode();
        //1、获取本场景所有费用详情
        List<ExpenseOrderDetail> expenseOrderDetails = productExpenseService.listExpenseOrderDetailByFinanceAndType(financeApply.getFinanceNo(), type);
        //2、还款计算试算
        CostCalculusVO costCalculusVO = this.costCalculus(financeApply, expenseOrderDetails, startTime, refundTime, interestIsZero);
        //3、更新还款试算参数
        this.saveCalJson(costCalculusVO, type, financeApply.getFinanceNo());
        //4、使用已生成的费用详情进行还款试算 并生成结局单、还款计划、还款计划费用
        RepaymentPlanCal showRepaymentPlan = costCalculusVO.getShowRepaymentPlan();
        RepaymentPlanDTO repaymentPlanDTO = saveRepaymentPlanByFinanceApply(financeApply, showRepaymentPlan, true, startTime, refundTime);
        //5、更新
        //6、拼凑返回体 返回所有费用及未包含计算节点=收费节点的费用
        List<ExpenseOrderDetail> expenseOrderDetailList = costCalculusVO.getExpenseOrderDetailList();
        if (CollUtil.isNotEmpty(expenseOrderDetailList)) {
            repaymentPlanDTO.setExpenseOrderDetailList(expenseOrderDetailList);
            List<ExpenseOrderDetail> unContainNowExpenseOrderDetailList = expenseOrderDetailList.stream()
                    .filter(e -> !e.getCollectFeesNode().equals(feeNode)).collect(Collectors.toList());
            repaymentPlanDTO.setUnContainNowExpenseOrderDetailList(unContainNowExpenseOrderDetailList);
        }
        return repaymentPlanDTO;
    }

    /**
     * 更新还款计划
     * @param financeApply
     * @param repaymentPlanCal
     */
    @Override
    public void updateRepaymentPlan(FinanceApply financeApply, RepaymentPlanCal repaymentPlanCal) {
        Product product = productDirector.detailBase(financeApply.getGoodsId());
        // 查询借款订单
        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financeApply.getFinanceNo());
        List<LoanManageRepaymentPlan> loanManageRepaymentPlanList = loanManageRepaymentPlanService.list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .select(LoanManageRepaymentPlan::getId, LoanManageRepaymentPlan::getRepaymentStatus)
                .eq(LoanManageRepaymentPlan::getIouNo, loanManageIou.getIouNo())
                .ne(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.INVALID.getCode())
        );
        // 根据还款状态进行分组
        Map<Integer, List<LoanManageRepaymentPlan>> repaymentStatusDataListMap = loanManageRepaymentPlanList.stream().collect(Collectors.groupingBy(LoanManageRepaymentPlan::getRepaymentStatus));
        // 使用中的还款计划
        List<LoanManageRepaymentPlan> usingList = repaymentStatusDataListMap.get(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode());
        // 结清的还款计划
        List<LoanManageRepaymentPlan> settleList = repaymentStatusDataListMap.get(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
        if (CollectionUtil.isNotEmpty(usingList)) {
            List<Long> needCloseRepaymentPlanIds = usingList.stream().map(LoanManageRepaymentPlan::getId).collect(Collectors.toList());
            // 使用中的还款计划状态变更为作废
            loanManageRepaymentPlanService.changeRepaymentStatus(
                    needCloseRepaymentPlanIds,
                    RepaymentConstant.RepaymentPlanRepaymentStatusEnum.INVALID.getCode(),
                    RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode()
            );
        }
        // 加的期数
        Integer addPerid = CollectionUtil.isEmpty(settleList) ? 0 : settleList.size();
        //创建新的还款计划订单
        List<LoanManageRepaymentPlanDTO> repaymentPlanDTOS = buildRepaymentPlanList(loanManageIou, repaymentPlanCal, product, financeApply.getUserId()
                , financeApply.getId(), financeApply.getCustomerGoodsId(), financeApply.getAnnualInterestRate(), financeApply.getServiceRate());
        for (LoanManageRepaymentPlanDTO repaymentPlanDTO : repaymentPlanDTOS) {
            repaymentPlanDTO.setPeriod(repaymentPlanDTO.getPeriod() + addPerid);
            loanManageRepaymentPlanService.save(repaymentPlanDTO);
        }
    }

    /**
     * 生成还款计划
     * @param financeApply
     * @param expenseOrderDetailList
     * @param startTime
     * @param refundTime
     * @param interestIsZero
     * @return
     */
    public CostCalculusVO costCalculus(FinanceApply financeApply, List<ExpenseOrderDetail> expenseOrderDetailList, LocalDate startTime, LocalDate refundTime, Boolean interestIsZero) {
        // 会存在银行部分放款的情况，使用银行放款的金额
        BigDecimal investMoney = financeApply.getAmount();
        BigDecimal yearRate = financeApply.getAnnualInterestRate();
        if (interestIsZero) {
            yearRate = BigDecimal.ZERO;
        }
        Integer moneyStrategy = CommonConstant.NUMBER_STRATEGY;
        Integer interestDay = 0;
        RepaymentPlanCal showRepaymentPlan = LoanUtils.calculatePrincipalAndInvest(investMoney, yearRate, startTime, refundTime, moneyStrategy, interestDay);
        if (CollUtil.isEmpty(expenseOrderDetailList)) {
            CostCalculusVO costCalculusVO = new CostCalculusVO();
            costCalculusVO.setShowRepaymentPlan(showRepaymentPlan);
            costCalculusVO.setExpenseOrderDetailFinanceVos(Collections.emptyList());
            return costCalculusVO;
        }
        //费用及还款计划费用试算
        return costCalculus(expenseOrderDetailList, showRepaymentPlan);
    }

    public RepaymentPlanDTO saveRepaymentPlanByFinanceApply(FinanceApply financeApply, RepaymentPlanCal repaymentPlanCal, boolean isFirst, LocalDate startTime, LocalDate refundTime) {
        Product product = productDirector.detailBase(financeApply.getGoodsId());
        //创建借款订单
        LoanManageIou loanManageIou = loanManageIouService.saveLoanManageIou(financeApply, startTime, refundTime);
        //创建还款计划订单
        List<LoanManageRepaymentPlanDTO> repaymentPlanDTOS = buildRepaymentPlanList(loanManageIou, repaymentPlanCal, product, financeApply.getUserId()
                , financeApply.getId(), financeApply.getCustomerGoodsId(), financeApply.getAnnualInterestRate(), financeApply.getServiceRate());
        //创建还款计划订单
        RepaymentPlanDTO repaymentPlanDTO = savePlan(loanManageIou, repaymentPlanDTOS);
        //如果是第一次保存还款记录 则进行保存还款记录
        if (isFirst) {
            loanAlterationHistoryService.createFirstAlterationLog(repaymentPlanDTO.getLoanManageRepaymentPlanList(), loanManageIou.getId());
        }
        return repaymentPlanDTO;
    }

    private static void fillAppendRepaymentPlanFeeInfo(RepaymentPlanFee repaymentPlanFee, RepaymentInfoDTO repaymentInfoDTO) {
        //存在更新旧的 不存在创建新的
        Optional<RepaymentPlanFee> oldRepaymentPlanFeeOptional = repaymentInfoDTO.getRepaymentPlanFeeList().stream()
                .filter(e -> repaymentPlanFee.getExpenseTypeId().equals(e.getExpenseTypeId())).findFirst();
        if (oldRepaymentPlanFeeOptional.isPresent()) {
            RepaymentPlanFee old = oldRepaymentPlanFeeOptional.get();
            repaymentPlanFee.setId(old.getId());
            repaymentPlanFee.setCreateUser(old.getCreateUser());
            repaymentPlanFee.setCreateTime(old.getCreateTime());
            repaymentPlanFee.setCreateDept(old.getCreateDept());
        }
        repaymentPlanFee.setPlanId(repaymentInfoDTO.getId());
        repaymentPlanFee.setIouNo(repaymentInfoDTO.getIouNo());
        repaymentPlanFee.setIouId(repaymentInfoDTO.getIouId());
        repaymentPlanFee.setGoodsType(repaymentInfoDTO.getGoodsType());
        repaymentPlanFee.setRepaymentTime(repaymentInfoDTO.getRepaymentTime());
        repaymentPlanFee.setUserId(repaymentInfoDTO.getUserId());
        repaymentPlanFee.setFinanceApplyId(repaymentInfoDTO.getFinanceApplyId());
        repaymentPlanFee.setCustomerGoodsId(repaymentInfoDTO.getCustomerGoodsId());
        repaymentPlanFee.setGoodsId(repaymentInfoDTO.getGoodsId());
        repaymentPlanFee.setPlanReductionAmount(BigDecimal.ZERO);
    }

    private RepaymentPlanCal buildRepaymentPlanCalByRepaymentPlan(List<RepaymentInfoDTO> repaymentInfoList, LoanManageIou loanInfoDTO) {
        RepaymentPlanCal repaymentPlanCal = new RepaymentPlanCal();
        RepaymentInfoDTO first = repaymentInfoList.get(0);
        List<StagRecordVO> stagRecordVOList = repaymentInfoList.stream().map(repaymentInfoDTO -> {
            StagRecordVO stagRecordVO = new StagRecordVO();
            stagRecordVO.setTerm(repaymentInfoDTO.getPeriod().toString());
            stagRecordVO.setMonthlySupply(repaymentInfoDTO.getPrincipal().add(repaymentInfoDTO.getPlanInterest()));
            stagRecordVO.setMonthlyPrincipal(repaymentInfoDTO.getPrincipal());
            stagRecordVO.setMonthlyInterest(repaymentInfoDTO.getInterest());
            stagRecordVO.setDiscountAmount(BigDecimal.ZERO);
            stagRecordVO.setReductionInterest(repaymentInfoDTO.getReductionInterest());
            stagRecordVO.setStartTime(repaymentInfoDTO.getStartTime());
            stagRecordVO.setRefundTime(repaymentInfoDTO.getRepaymentTime());
            stagRecordVO.setPlanFeeLists(repaymentInfoDTO.getRepaymentPlanFeeList());
            stagRecordVO.setReductionInterestList(repaymentInfoDTO.getReductionInterestList());
            return stagRecordVO;
        }).collect(Collectors.toList());
        if (GoodsEnum.TERM.getCode().equals(loanInfoDTO.getPeriodUnit())) {
            StagRecordVO stagRecordVO = LoanUtils.calTotalData(stagRecordVOList, loanInfoDTO.getIouAmount());
            stagRecordVOList.add(stagRecordVO);
        }
        repaymentPlanCal.setYearRate(first.getAnnualInterestRate());
        repaymentPlanCal.setDayRate(first.getAnnualInterestRate().divide(BigDecimal.valueOf(CommonConstant.YEAR_DAY_COUNT), 2, RoundingMode.HALF_UP));
        repaymentPlanCal.setStagRecords(stagRecordVOList);
        repaymentPlanCal.setLoanTermUnit(loanInfoDTO.getPeriodUnit());
        return repaymentPlanCal;
    }

    private Integer getRepaymentPlanCalType(Boolean isBank) {
        return isBank ? RepaymentPlanCalEnum.BANK.getType() : RepaymentPlanCalEnum.SYSTEM.getType();
    }
}
