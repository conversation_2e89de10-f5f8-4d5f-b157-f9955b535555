package org.springblade.loan.strategy;

import lombok.RequiredArgsConstructor;
import org.springblade.loan.contant.BankRepaymentEnum;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanCalReq;
import org.springblade.loan.service.IOnlineBankRepaymentService;
import org.springframework.stereotype.Service;

/**
 * 线上银联策略类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 还款试算策略类
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class OnlineBankRepaymentStrategy implements BankRepaymentStrategy {
    private final IOnlineBankRepaymentService onlineBankRepaymentService;
    @Override
    public BankRepaymentEnum support() {
        return BankRepaymentEnum.ONLINE_BANK;
    }

    @Override
    public RepaymentPlanCal calculate(RepaymentPlanCalReq repaymentPlanCalReq) {
        return onlineBankRepaymentService.calculate(repaymentPlanCalReq);
    }
}
