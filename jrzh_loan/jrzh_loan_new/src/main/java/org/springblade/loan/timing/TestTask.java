package org.springblade.loan.timing;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.contract.entity.Contract;
import org.springblade.modules.contract.service.impl.ContractServiceImpl;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@Component("testTask")
@RequiredArgsConstructor
@Slf4j
public class TestTask {

    private final ContractServiceImpl contractService;

    @Scheduled(cron = "0/2 * * * * ?")
    public void test() {
//        Contract contract = contractService.getOne(Wrappers.<Contract>lambdaQuery()
//                .eq(Contract::getId, 1887424771793743875L)
//        );
//        contract.setFinanceApplyId(123321L);
//        contractService.updateById(contract);
    }

}
