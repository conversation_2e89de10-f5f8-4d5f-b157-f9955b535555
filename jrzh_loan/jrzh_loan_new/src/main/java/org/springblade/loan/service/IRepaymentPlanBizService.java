package org.springblade.loan.service;

import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanCalReq;
import org.springblade.loan.dto.RepaymentPlanSaveDTO;

/**
 * 还款计划
 *
 * @Author: <PERSON>hengchuangkai
 * @CreateTime: 2023-12-02  14:52
 * @Description: TODO
 * @Version: 1.0
 */
public interface IRepaymentPlanBizService {
    /**
     * 还款试算
     *
     * @param repaymentPlanCalReq 还款试算信息
     * @return
     */
    RepaymentPlanCal repaymentCal(RepaymentPlanCalReq repaymentPlanCalReq);

    /**
     * 还款计划保存
     *
     * @param repaymentPlanSaveDTO 还款计划
     * @return
     */
    RepaymentPlanSaveDTO repaymentSave(RepaymentPlanSaveDTO repaymentPlanSaveDTO);
}
