/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.block.blockchain.cloud;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.block.entity.BlockChainInformation;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 区块链入参数据：云信开单
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BlockChainCloudApply对象", description = "BlockChainVO对象")
public class BlockChainCloudApply extends BlockChainInformation {
	private static final long serialVersionUID = 1L;

	/**
	 * 开单金额
	 */
	@ApiModelProperty(value = "开单金额")
	@NotNull(message = "开单金额不能为空")
	private BigDecimal billAmount;

	/**
	 * 承若付款日
	 */
	@ApiModelProperty(value = "承若付款日")
	@NotNull(message = "承若付款日不能为空")
	private LocalDate endDate;

	@ApiModelProperty(value = "融资付息模式:0、开单企业付息 1、融资企业付息")
	@NotNull(message = "融资付息模式不能为空")
	private Integer financingModel;

	/**
	 * 收单企业id
	 */
	@ApiModelProperty(value = "收单企业id")
	@NotNull(message = "收单企业id不能为空")
	private Long upstreamSupplierId;
}
