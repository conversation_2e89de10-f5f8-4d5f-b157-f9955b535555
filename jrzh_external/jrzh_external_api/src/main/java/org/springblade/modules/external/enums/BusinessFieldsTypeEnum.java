package org.springblade.modules.external.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务字段类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessFieldsTypeEnum {

    CUSTOMER_PERSON_INFO_FIELD_CUSTOMER_ID(1,"个人客户信息字段：customerId"),

    CUSTOMER_INFO_FIELD_COMPANY_ID(2,"企业客户信息字段：companyId"),

    CUSTOMER_PERSON_INFO_SIGN_SEAL_ACCOUNT(3,"个人客户上上签账号"),

    CUSTOMER_INFO_SIGN_SEAL_ACCOUNT(4,"企业客户上上签账号"),


    ;

    private final Integer code;
    private final String name;
}
