package org.springblade.modules.external.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderInfoConnectionDTO {
    /**
     * 外部用户ID
     */
    private Long memberId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;
    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private String orderCreateTime;
    /**
     * 订单金额（元）
     */
    @ApiModelProperty(value = "订单金额（元）")
    private BigDecimal orderAmount;
    /**
     * 卖方社会统一代码
     */
    @ApiModelProperty(value = "卖方社会统一代码")
    private String salesCreditCode;
    /**
     * 卖方公司名称
     */
    @ApiModelProperty(value = "卖方公司名称")
    private String salesCompanyName;
    /**
     * 买方社会统一代码
     */
    @ApiModelProperty(value = "买方社会统一代码")
    private String buyerCreditCode;
    /**
     * 企业id
     */
    @ApiModelProperty(value = "企业id")
    private Long companyId;
    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String companyName;
    /**
     * 场景类型：1一对多，2多对一
     */
    @ApiModelProperty(value = "场景类型：1一对多，2多对一")
    private Integer scenarioType;

    @ApiModelProperty(value = "结算银行开户行名称")
    private String settlementBankAccountName;

    @ApiModelProperty(value = "结算银行开户账号")
    private String settlementBankAccountNum;

    @ApiModelProperty(value = "供方联系方式")
    private String salesPhone;

    @ApiModelProperty(value = "供方联系人")
    private String salesName;
}
