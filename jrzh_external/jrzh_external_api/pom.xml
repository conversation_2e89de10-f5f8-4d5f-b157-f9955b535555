<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh_external</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>jrzh_external_api</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_contract_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_plan_core_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


</project>