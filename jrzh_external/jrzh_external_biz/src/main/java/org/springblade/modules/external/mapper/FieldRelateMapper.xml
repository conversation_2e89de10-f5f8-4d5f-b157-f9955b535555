<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.external.mapper.FieldRelateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fieldRelateResultMap" type="org.springblade.modules.external.entity.FieldRelate">
        <id column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="external_system_id" property="externalSystemId"/>
        <result column="internal_system_id" property="internalSystemId"/>
        <result column="business_type" property="businessType"/>
    </resultMap>


    <select id="selectFieldRelatePage" resultMap="fieldRelateResultMap">
        select * from jrzh_field_relate where is_deleted = 0
    </select>

</mapper>
