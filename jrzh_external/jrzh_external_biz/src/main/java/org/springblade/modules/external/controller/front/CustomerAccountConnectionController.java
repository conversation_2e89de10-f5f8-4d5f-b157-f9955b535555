package org.springblade.modules.external.controller.front;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.modules.external.dto.CustomerAccountConnectionDTO;
import org.springblade.modules.external.service.ICustomerAccountConnectionService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping( "external/front/customerAccountConnection")
@Api(value = "用户账号打通", tags = "用户账号打通接口")
@Slf4j
public class CustomerAccountConnectionController extends BladeController {

    private ICustomerAccountConnectionService customerAccountConnectionService;


    /**
     * 用户账号打通
     */
    @PostMapping("/accountConnection")
    @ApiOperation(value = "用户账号打通")
//    public R<Boolean> accountConnection(@RequestBody CustomerAccountConnectionDTO connectionDTO) {
    public R accountConnection(@RequestBody String connectionDtoStr) {
//        return R.data(true);
        return customerAccountConnectionService.postCustomerAccountConnection(connectionDtoStr);

    }

    @PostMapping("/getToken")
    @ApiOperation(value = "用户账号打通")
    public R getToken(@RequestBody CustomerAccountConnectionDTO connectionDTO) {
        Long memberId = connectionDTO.getMemberId();
        return customerAccountConnectionService.getToken(memberId);
    }

//    @PostMapping("/orderPush")
//    @ApiOperation(value = "订单推送")
//    public R orderPush(@RequestBody OrderInfoConnectionDTO orderInfoConnectionDTO) {
//        return customerAccountConnectionService.orderPush(orderInfoConnectionDTO);
//    }
    @PostMapping("/orderPush")
    @ApiOperation(value = "订单推送")
    public R orderPush(@RequestBody String signBody) {
        return customerAccountConnectionService.orderPush(signBody);
    }

    @PostMapping("/orderDel")
    @ApiOperation(value = "订单删除")
    public R orderDel(@RequestBody String signBody) {
        return customerAccountConnectionService.orderDel(signBody);
    }

    @PostMapping("/getGoodsAndQuota")
    @ApiOperation(value = "获取客户的融资产品和额度")
    public R getGoodsAndQuota(@RequestBody String memberId) {
        return customerAccountConnectionService.getGoodsAndQuota(memberId);
    }
}
