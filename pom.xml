<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh-parent</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <artifactId>jrzh_order_financing</artifactId>
    <version>3.3.0-SNAPSHOT</version>
    <modules>
       <module>jrzh_order_financing_goods</module>
        <module>jrzh_order_financing_financed</module>
        <module>jrzh_order_financing_financed_api</module>
        <module>jrzh_order_financing_goods_api</module>
    </modules>
    <packaging>pom</packaging>
    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-http</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-api-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-datascope</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-develop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-social</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-auto</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-boot</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springblade</groupId>
                    <artifactId>blade-core-cloud</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_common</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>


    <repositories>
        <repository>
            <id>release</id>
            <name>jrzh-release</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/repositories/public/</url>
        </repository>
    </repositories>

</project>
