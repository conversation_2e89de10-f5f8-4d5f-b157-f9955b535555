/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.contract.dto.DynamicContractDTO;
import org.springblade.modules.contract.dto.SignParam;
import org.springblade.modules.contract.entity.Contract;
import org.springblade.modules.contract.entity.ContractTemplate;
import org.springblade.modules.contract.entity.TemplateFieldsConfig;
import org.springblade.modules.contract.handler.ElecSignHandlerFactory;
import org.springblade.modules.contract.service.IContractDataSource;
import org.springblade.modules.contract.service.IContractType;
import org.springblade.modules.contract.vo.ContractTemplateConfigVO;
import org.springblade.othersapi.bestsign.constant.ContractEnum;
import org.springblade.othersapi.bestsign.dto.ContractReturnData;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同模板类型 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Service
@RequiredArgsConstructor
public class TemplateContractTypeImpl implements IContractType {
    final private ElecSignHandlerFactory elecSignHandlerFactory;

    @Override
    public String preViewTemplate(ContractTemplate contractTemplate) {
        return elecSignHandlerFactory.template().preview(contractTemplate.getTemplateId());
    }

    @Override
    public void loadingDataSource(DynamicContractDTO dynamicContractDTO) {
        Map<String, Object> dataSource = new HashMap<>();
        dynamicContractDTO.getContractDataSource().forEach(dataSourceService -> {
            final DynamicContractDTO contractDTO = dynamicContractDTO;
            Object source = dataSourceService.getDataSource(contractDTO);
            dataSource.put(source.getClass().getName(), source);
        });
        dynamicContractDTO.setOriginBackSource(dataSource);
    }

    @Override
    public ContractReturnData gen(DynamicContractDTO dynamicContractDTO) {
        ContractTemplateConfigVO config = dynamicContractDTO.getContractTemplateConfigVO();
        if (ObjectUtil.isEmpty(config)) {
            throw new ServiceException("合同数据源未配置,请联系工作人员");
        }
        return elecSignHandlerFactory.template().contractCreate(dynamicContractDTO);
    }

    @Override
    public Contract sign(SignParam signParam) {
        elecSignHandlerFactory.template().templateAutoSign(signParam);
        return signParam.getContract();
    }

    @Override
    public Integer support() {
        return ContractEnum.ContractGenType.TEMPLATE.getType();
    }

    @Override
    public List<IContractDataSource> getConfigDataSource(ContractTemplateConfigVO templateConfigVO,List<IContractDataSource> contractDataSourceList) {
        List<TemplateFieldsConfig> templateFieldsConfigs = templateConfigVO.getTemplateFieldsConfigs();
        if (CollUtil.isEmpty(templateFieldsConfigs)) {
            return Collections.emptyList();
        }
        List<String> beanClazzPathList = templateFieldsConfigs.stream()
                .map(TemplateFieldsConfig::getClazzName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<IContractDataSource> contractDataSources = new LinkedList<>();
        contractDataSourceList.forEach(dataSourceService -> {
            if (beanClazzPathList.contains(dataSourceService.support())) {
                contractDataSources.add(dataSourceService);
            }
        });
        return contractDataSources;
    }

}
