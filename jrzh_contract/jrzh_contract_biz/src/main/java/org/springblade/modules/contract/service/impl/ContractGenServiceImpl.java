package org.springblade.modules.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.CustomerTypeEnum;
import org.springblade.common.utils.ThreadUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.contract.dto.DynamicContractDTO;
import org.springblade.modules.contract.dto.SignParam;
import org.springblade.modules.contract.entity.*;
import org.springblade.modules.contract.service.*;
import org.springblade.modules.contract.vo.ContractTemplateConfigVO;
import org.springblade.othersapi.bestsign.constant.ContractEnum;
import org.springblade.othersapi.bestsign.dto.ContractReturnData;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 合同生成服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractGenServiceImpl implements IContractGenService {
    private final IContractTemplateService contractTemplateService;
    private final IContractOperatorService contractOperatorService;
    private final ContractStrategy contractStrategy;
    private final IContractTemplateConfigService templateConfigService;
    private final IContractService contractService;
    private final IContractSignService contractSignService;
    private final IContractSignConfigService contractSignConfigService;
    private final List<IContractDataSource> contractDataSourceList;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Contract genContract(DynamicContractDTO dynamicContractDTO) {
        //参数检查 数据组装
        validAndComposeDynamicContract(dynamicContractDTO);

        //按传入的合同方式进行生成
        log.info("开始生成合同 方法{} 参数：params：{}", "genContract()", dynamicContractDTO);
        Integer contractGenType = dynamicContractDTO.getContractTemplate().getContractGenType();
        IContractType genContractService = contractStrategy.genContractInstance(contractGenType);
        ContractReturnData genContract = genContractService.gen(dynamicContractDTO);
        log.info("生成合同结束 方法{} 返回参数：params：{}", "genContract()", genContract);
        //合同保存
        saveNewContract(dynamicContractDTO, genContract);
        //添加合同签署人
        addSigner(dynamicContractDTO.getContractOperatorList(), dynamicContractDTO.getContractTemplate(), genContract.getContractId());
        //使用平台进行签署
        Long platSigner = dynamicContractDTO.getPlatSigner();
        if (ObjectUtil.isNotEmpty(platSigner)) {
            platSign(platSigner, dynamicContractDTO);
        }
        return dynamicContractDTO;
    }

    private void platSign(Long platSignerId, DynamicContractDTO dynamicContractDTO) {
        //获取平台签署人
        List<ContractOperator> contractOperatorList = dynamicContractDTO.getContractOperatorList();
        ContractOperator platSigne = null;
        for (ContractOperator contractOperator : contractOperatorList) {
            if (platSignerId.equals(contractOperator.getSignerId())) {
                platSigne = contractOperator;
                break;
            }
        }
        if (platSigne == null) {
            throw new ServiceException("平台方签署人未设置");
        }
        final ContractOperator finalPlatSigne = platSigne;
        //异步签署
        String tenantId = AuthUtil.getTenantId();
        ThreadUtils.runAsync(() -> {
            TenantBroker.runAs(tenantId, e -> {
                //签署
                SignParam signParam = SignParam.builder()
                        .signerId(finalPlatSigne.getSignerId())
                        .customerType(CustomerTypeEnum.ENTERPRISE.getCode())
                        .signerName(finalPlatSigne.getSignerCompanyName())
                        .personalName(finalPlatSigne.getSignerName())
                        .personalUserId(null)
                        .code(null)
                        .contractId(dynamicContractDTO.getContractId())
                        .customizeWriteBase64(null)
                        .build();
                contractSignService.sign(signParam);
                //更新签署后的合同
                contractService.downLoadAndUpdateContract(signParam.getContractId());
            });
        });
    }

    /**
     * 合同保存
     *
     * @param dynamicContractDTO
     * @param gen
     */
    private void saveNewContract(DynamicContractDTO dynamicContractDTO, ContractReturnData gen) {
        dynamicContractDTO.setSignKeywordJson(gen.getSignKeyWordJson());
        dynamicContractDTO.setContractId(gen.getContractId());
        dynamicContractDTO.setFileUrl(gen.getUrl());
        dynamicContractDTO.setContractVar(gen.getContractVar());
        contractService.save(dynamicContractDTO);
    }

    private void addSigner(List<ContractOperator> contractOperatorList, ContractTemplate contractTemplate, String contractId) {
        for (ContractOperator contractOperator : contractOperatorList) {
            contractOperator.setStatus(ContractEnum.CONTRACT_OPERATOR_STATUS.NOT_START.getStatus());
            contractOperator.setContractId(contractId);
            contractOperatorService.saveOrUpdateContractOperator(contractOperator, contractTemplate);
        }
    }

    /**
     * 参数检查
     *
     * @param dynamicContractDTO
     */
    private void validAndComposeDynamicContract(DynamicContractDTO dynamicContractDTO) {
        boolean allNotEmpty = cn.hutool.core.util.ObjectUtil.isAllNotEmpty(
                dynamicContractDTO.getSignNode(),
                dynamicContractDTO.getContractTemplateId(),
                dynamicContractDTO.getProcessType()
                , dynamicContractDTO.getGoodsId()
                , dynamicContractDTO.getGoodType()
                , dynamicContractDTO.getGoodName()
                , dynamicContractDTO.getContractBizNo());
        if (!allNotEmpty) {
            throw new ServiceException("合同参数缺失");
        }
        //合同模板信息
        String templateId = dynamicContractDTO.getContractTemplateId().toString();
        ContractTemplate template = contractTemplateService.getByTemplateId(templateId);
        if (ObjectUtil.isEmpty(template)) {
            throw new ServiceException("合同模板不存在,请检查产品是否配置该合同模板");
        }
        //校验签署人数
        List<ContractOperator> signerList = dynamicContractDTO.getContractOperatorList();
        if (signerList.size() > template.getSignerNum()) {
            throw new ServiceException("签署人数设置过多");
        }
        //如果没有指定数据源 则从匹配方案中获取配置的匹配方案 装载合同数据源
        ContractTemplateConfigVO templateConfigVO = null;
        List<IContractDataSource> contractDataSource = dynamicContractDTO.getContractDataSource();
        if (CollUtil.isEmpty(contractDataSource)) {
            templateConfigVO = templateConfigService.getByTemplateIdAndSignNodeAndGoodsType(templateId,
                    dynamicContractDTO.getSignNode(), dynamicContractDTO.getGoodType());
            if(ObjectUtil.isEmpty(templateConfigVO)){
                throw new ServiceException("合同方案未配置");
            }
            //获取方案配置的数据源
            dynamicContractDTO.setContractDataSource(contractStrategy.genContractInstance(template.getContractGenType())
                    .getConfigDataSource(templateConfigVO,contractDataSourceList));
            //加载数据源
            contractStrategy.genContractInstance(template.getContractGenType())
                    .loadingDataSource(dynamicContractDTO);
        }
        //获取签署人配置
        List<ContractSignConfig> contractSignConfigs = contractSignConfigService.listByTemplateId(templateId);
        //数据组装
        if (ObjectUtil.isEmpty(dynamicContractDTO.getContractNo())) {
            dynamicContractDTO.setContractNo("J" + IdWorker.getIdStr());
        }
        dynamicContractDTO.setContractTitle(template.getTemplateName());
        LocalDateTime now = LocalDateTime.now();
        dynamicContractDTO.setSendTime(now);
        dynamicContractDTO.setSignDeadLine(now.plusDays(template.getExpireDay()));
        dynamicContractDTO.setContractTemplate(template);
        dynamicContractDTO.setContractTemplateConfigVO(templateConfigVO);
        dynamicContractDTO.setContractSignConfigList(contractSignConfigs);
        dynamicContractDTO.setNeedPlatSign(template.getNeedPlatSign());
    }

    /**
     * 获取方案配置的数据源
     *
     * @param templateConfigVO 方案配置
     * @return
     */
    private List<IContractDataSource> getConfigDataSource(ContractTemplateConfigVO templateConfigVO) {
        List<TemplateFieldsConfig> templateFieldsConfigs = templateConfigVO.getTemplateFieldsConfigs();
        if (CollUtil.isEmpty(templateFieldsConfigs)) {
            return Collections.emptyList();
        }
        List<String> beanClazzPathList = templateFieldsConfigs.stream()
                .map(TemplateFieldsConfig::getClazzName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<IContractDataSource> contractDataSources = new LinkedList<>();
        contractDataSourceList.forEach(dataSourceService -> {
            if (beanClazzPathList.contains(dataSourceService.support())) {
                contractDataSources.add(dataSourceService);
            }
        });
        return contractDataSources;
    }
}
