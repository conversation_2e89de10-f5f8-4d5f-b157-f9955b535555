/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.utils.ImgUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.modules.contract.constant.ContractConfigEnum;
import org.springblade.modules.contract.dto.ContractConfigDTO;
import org.springblade.modules.contract.dto.SsqCreateSignImg;
import org.springblade.modules.contract.entity.ContractConfig;
import org.springblade.modules.contract.entity.ContractSignSeal;
import org.springblade.modules.contract.handler.ElecSignHandler;
import org.springblade.modules.contract.handler.ElecSignHandlerFactory;
import org.springblade.modules.contract.mapper.ContractConfigMapper;
import org.springblade.modules.contract.service.IContractConfigService;
import org.springblade.modules.contract.service.IContractSignSealService;
import org.springblade.modules.contract.vo.ContractConfigVO;
import org.springblade.modules.contract.wrapper.ContractConfigWrapper;
import org.springblade.othersapi.bestsign.constant.ElecSignEnum;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 合同配置 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Service
@RequiredArgsConstructor
public class ContractConfigServiceImpl extends BaseServiceImpl<ContractConfigMapper, ContractConfig> implements IContractConfigService {
    /**
     * 电子签构建类
     */
    private final ElecSignHandlerFactory elecSignFactory;
    private final IAttachService attachService;
    private final IContractSignSealService contractSignSealService;

    @Override
    public IPage<ContractConfigVO> selectContractConfigPage(IPage<ContractConfigVO> page, ContractConfigVO contractConfig) {
        return page.setRecords(baseMapper.selectContractConfigPage(page, contractConfig));
    }

    /**
     * 创建签章图片
     *
     * @param type    操作类型
     * @param request 请求数据
     * @return 签章图片
     */
    @Override
    public BladeFile createSignImg(Integer type, Map<String, Object> request, String companyId, Integer customerType) {
        ElecSignHandler elecSignHandler = elecSignFactory.template();
        SsqCreateSignImg ssqCreateSignImg = BeanUtil.copyProperties(request, SsqCreateSignImg.class);
        ssqCreateSignImg.setType(type);
        ssqCreateSignImg.setCustomerType(customerType);
        ssqCreateSignImg.setAccountId(companyId);
        //上传
        elecSignHandler.createSignImg(ssqCreateSignImg);
        //下载
        return elecSignHandler.downloadSignImg(ssqCreateSignImg.getAccountId(), ssqCreateSignImg.getImageName());
    }

    /**
     * 提交申请
     *
     * @param contractConfig 合同配置
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submit(ContractConfigDTO contractConfig, String companyId, Integer customerType) {
        Map<String, Object> request = new HashMap<>(16);
        // 是否首次提交
        boolean first = ObjectUtils.isEmpty(contractConfig.getId());
        ContractSignSeal contractSignSeal = null;
        if (first) {
            contractConfig.setUserId(Long.valueOf(companyId));
            Optional.of(contractConfig)
                    .map(ContractConfigDTO::getAttachId)
                    // 填充上传印章图片数据
                    .ifPresent(attachId -> fillImageParam(attachId, request));
            contractSignSeal = new ContractSignSeal();
            contractSignSeal.setCategory(ContractConfigEnum.CATEGORY.SEAL.getCode());
            contractSignSealService.save(contractSignSeal);
            contractConfig.setSealId(contractSignSeal.getId());
        } else {
            ContractConfig old = getByCompanyId(Long.valueOf(companyId));
            contractSignSeal = contractSignSealService.getById(old.getSealId());
            Long attachId = contractConfig.getAttachId();
            if (null != attachId) {
                if (attachId.equals(contractSignSeal.getAttachId())) {
                    return this.updateById(contractConfig);
                } else {
                    fillImageParam(attachId, request);
                }
            }
        }
        BladeFile signImg = createSignImg(contractConfig.getImgType(), request, companyId, customerType);
        // 如果是上传需要重新设置附件id
        if (ContractConfigEnum.IMGTYPE.UPLOAD.getCode().equals(contractConfig.getImgType())) {
            contractSignSeal.setAttachId(buildAttach(signImg));
        } else {
            contractSignSeal.setGenerateLink(signImg.getLink());
        }
        contractSignSealService.updateById(contractSignSeal);
        return this.saveOrUpdate(contractConfig);
    }

    /**
     * 添加 账户签名/印章设置
     *
     * @param customerId   账户id
     * @param customerType 账户类型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSignSealConfig(String customerId, Integer customerType) {
        final Integer izPersonal = 1;
        HashMap<String, Object> request = new HashMap<>(16);
        BladeFile bladeFile = createSignImg(ContractConfigEnum.IMGTYPE.GENERATE.getCode(), request, customerId, customerType);
        ContractSignSeal contractSignSeal = new ContractSignSeal();
        contractSignSeal.setGenerateLink(bladeFile.getLink());
        boolean izSignSeal = izPersonal.equals(customerType);
        // 判断账户类型
        ContractConfigEnum.CATEGORY category = izSignSeal ? ContractConfigEnum.CATEGORY.SIGN : ContractConfigEnum.CATEGORY.SEAL;
        contractSignSeal.setCategory(category.getCode());
        contractSignSealService.save(contractSignSeal);
        Long userId = Long.valueOf(customerId);
        // 先查出看是否已经添加过
        ContractConfig contractConfig = getByCompanyId(userId);
        if (null == contractConfig) {
            contractConfig = new ContractConfig();
            contractConfig.setUserId(userId);
        }
        contractConfig.setImgType(ContractConfigEnum.IMGTYPE.GENERATE.getCode());
        Long signSealId = contractSignSeal.getId();
        if (izSignSeal) {
            contractConfig.setSignId(signSealId);
        } else {
            contractConfig.setSealId(signSealId);
        }
        this.saveOrUpdate(contractConfig);
    }

    /**
     * 封装印章图片Base64编码、名称
     *
     * @param attachId 附件id
     */
    public void fillImageParam(Long attachId, Map<String, Object> request) {
        Attach attach = attachService.getById(attachId);
        String imageData;
        imageData = Base64Utils.encodeToString(ImgUtils.downLoadFromUrl(attach.getLink()));
        request.put(ElecSignEnum.IMAGE_DATA, imageData);
        request.put(ElecSignEnum.IMAGE_NAME, attach.getOriginalName());
    }

    /**
     * 构建附件表
     *
     * @param bladeFile 对象存储文件
     * @return 附件id
     */
    private Long buildAttach(BladeFile bladeFile) {
        Attach attach = new Attach();
        attach.setDomain(bladeFile.getDomain());
        attach.setLink(bladeFile.getLink());
        attach.setName(bladeFile.getName());
        attach.setOriginalName(bladeFile.getOriginalName());
        attachService.save(attach);
        return attach.getId();
    }

    /**
     * 详情
     *
     * @return 详情信息
     */
    @Override
    public ContractConfigVO detail(Long companyId) {
        ContractConfig contractConfig = getByCompanyId(companyId);
        ContractConfigVO contractConfigVO = ContractConfigWrapper.build().entityVO(contractConfig);
        Optional.ofNullable(contractConfig)
                .map(ContractConfig::getSealId)
                .ifPresent(sealId -> {
                    // 根据附件id查出附件
                    ContractSignSeal contractSignSeal = contractSignSealService.getById(sealId);
                    Attach attach = attachService.getById(contractSignSeal.getAttachId());
                    contractConfigVO.setAttach(attach);
                    contractConfigVO.setGenerateLink(contractSignSeal.getGenerateLink());
                });
        return contractConfigVO;
    }

    /**
     * 根据企业id获取签名/印章名称
     *
     * @param companyId 企业id
     * @return 签章/印章名称
     */
    @Override
    public String getImgNameByCompanyId(Long companyId, Integer category) {
        ContractConfig entity = getByCompanyId(companyId);
        boolean izSignSeal = ContractConfigEnum.CATEGORY.SEAL.getCode().equals(category);
        // todo 除了获取印章名称其他现在暂时返回 ""
        if (entity == null || !izSignSeal || ContractConfigEnum.IMGTYPE.GENERATE.getCode().equals(entity.getImgType())) {
            return "";
        }
        Long contractSignSealId = entity.getSealId();
        ContractSignSeal signSeal = contractSignSealService.getById(contractSignSealId);
        Attach attach = attachService.getById(signSeal.getAttachId());
        return Optional.ofNullable(attach).map(Attach::getOriginalName).orElseGet(() -> "");
    }

    /**
     * 根据企业id获取合同配置
     *
     * @param companyId 企业id
     * @return 企业的合同配置
     */
    @Override
    public ContractConfig getByCompanyId(Long companyId) {
        return this.getOne(Wrappers.<ContractConfig>lambdaQuery()
                .eq(ContractConfig::getUserId, companyId), false);
    }

}
