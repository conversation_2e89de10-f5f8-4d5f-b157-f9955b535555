package org.springblade.modules.contract.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.contract.entity.ContractTemplateSignNode;
import org.springblade.modules.contract.mapper.ContractTemplateSignNodeMapper;
import org.springblade.modules.contract.service.IContractTemplateSignNodeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同签署节点配置 服务实现类
 */
@Service
@RequiredArgsConstructor
public class ContractTemplateSignNodeServiceImpl extends BaseServiceImpl<ContractTemplateSignNodeMapper, ContractTemplateSignNode> implements IContractTemplateSignNodeService {


    /**
     * 根据模板id查询合同模板签署节点
     * @param contractTemplateIds 模板ID
     * @return 合同模板签署节点
     */
    @Override
    public List<ContractTemplateSignNode> getByContractTemplateId(List<Long> contractTemplateIds) {
        return this.list(Wrappers.<ContractTemplateSignNode>lambdaQuery()
                .in(ContractTemplateSignNode::getTemplateId, contractTemplateIds));
    }

    /**
     * 根据产品类型、模板ID查询
     * @param goodType 产品类型
     * @param templateId 模板ID
     * @return 查询结果
     */
    @Override
    public List<ContractTemplateSignNode> getByGoodTypeAndTemplateId(Integer goodType, String templateId) {
        return this.list(Wrappers.<ContractTemplateSignNode>lambdaQuery()
                .eq(ContractTemplateSignNode::getTemplateId, templateId)
                .eq(ContractTemplateSignNode::getGoodsType, goodType));
    }
}
