/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.contract.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Delete;
import org.springblade.common.config.mybatis.batch.BatchMapper;
import org.springblade.modules.contract.entity.TemplateFieldsConfig;
import org.springblade.modules.contract.vo.TemplateFieldsConfigVO;

import java.util.List;

/**
 * 合同模板字段表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
public interface TemplateFieldsConfigMapper extends BatchMapper<TemplateFieldsConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param templateFieldsConfig
	 * @return
	 */
	List<TemplateFieldsConfigVO> selectTemplateFieldsConfigPage(IPage page, TemplateFieldsConfigVO templateFieldsConfig);

	/**
	 * 物理删除 条件：配置id
	 * @param configId 配置id
	 * @return
	 */
	@Delete("delete from jrzh_contract_template_fields_config where template_config_id =#{configId}")
	Boolean realDeleteByConfigIds(Long configId);
}
