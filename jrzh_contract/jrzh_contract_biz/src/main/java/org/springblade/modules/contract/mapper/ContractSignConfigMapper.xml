<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.contract.mapper.ContractSignConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractSignConfigResultMap" type="org.springblade.modules.contract.entity.ContractSignConfig">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="keywords" property="keywords"/>
        <result column="sign_align" property="signAlign"/>
        <result column="sign_x" property="signX"/>
        <result column="sign_y" property="signY"/>
        <result column="sign_no" property="signNo"/>
    </resultMap>


    <select id="selectContractSignConfigPage" resultMap="contractSignConfigResultMap">
        select * from jrzh_contract_sign_config where is_deleted = 0
    </select>

</mapper>
