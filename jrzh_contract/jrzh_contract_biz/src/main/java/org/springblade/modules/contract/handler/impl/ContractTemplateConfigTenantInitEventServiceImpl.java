package org.springblade.modules.contract.handler.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.enums.TenantChangeEnum;
import org.springblade.common.utils.ThreadUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.contract.entity.ContractSignConfig;
import org.springblade.modules.contract.entity.ContractTemplate;
import org.springblade.modules.contract.entity.ContractTemplateConfig;
import org.springblade.modules.contract.entity.TemplateFieldsConfig;
import org.springblade.modules.contract.service.IContractSignConfigService;
import org.springblade.modules.contract.service.IContractTemplateConfigService;
import org.springblade.modules.contract.service.IContractTemplateService;
import org.springblade.modules.contract.service.ITemplateFieldsConfigService;
import org.springblade.otherapi.core.entity.OtherApiEntityDataSource;
import org.springblade.othersapi.core.utils.field.BusinessFieldUtil;
import org.springblade.system.dto.TenantInitEventServiceDto;
import org.springblade.system.entity.Role;
import org.springblade.system.service.TenantInitEventService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static org.springblade.common.constant.TenantConstant.*;

/**
 * 租户初始化 合同方案模板相关数据实现类
 * <AUTHOR>
 * @date 2025/1/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractTemplateConfigTenantInitEventServiceImpl implements TenantInitEventService<Map<String, List>> {

    private final IContractTemplateConfigService contractTemplateConfigService;
    private final ITemplateFieldsConfigService templateFieldsConfigService;
    private final IContractTemplateService contractTemplateService;
    private final IContractSignConfigService contractSignConfigService;


    @Override
    @SneakyThrows
    public TenantInitEventServiceDto<Map<String, List>> getData(Role role, String tenantId) {
        try {
            //获取模板方案
            List<ContractTemplateConfig> configList = contractTemplateConfigService.list(Wrappers.<ContractTemplateConfig>lambdaQuery()
                    .eq(ContractTemplateConfig::getTenantId, BladeConstant.ADMIN_TENANT_ID));
            List<TemplateFieldsConfig> configFields = new ArrayList<>();
            for (ContractTemplateConfig config : configList) {
                Long configId = IdWorker.getId();
                List<TemplateFieldsConfig> formFieldsClass = new CopyOnWriteArrayList<>();

                for (String beanClazzPath : Func.toStrList(config.getBeanClazzPath())) {
                    Class<?> obj = Class.forName(beanClazzPath);
                    formFieldsClass.addAll(contractTemplateConfigService.getFormFieldsClass(obj, configId));
                }
                // formFieldsClass去重一下
                List<TemplateFieldsConfig> entryFormFieldsClassList = formFieldsClass.stream()
                        .collect(Collectors.groupingBy(TemplateFieldsConfig::getFieldName))
                        .entrySet().stream().map(entry -> entry.getValue().get(0))
                        .collect(Collectors.toList());

//                Class<?> obj = Class.forName(config.getBeanClazzPath());
//                List<TemplateFieldsConfig> formFieldsClass = contractTemplateConfigService.getFormFieldsClass(obj, configId);

                List<TemplateFieldsConfig> list = templateFieldsConfigService.list(Wrappers.<TemplateFieldsConfig>lambdaQuery()
                        .eq(TemplateFieldsConfig::getTemplateConfigId, config.getId()));
                for (TemplateFieldsConfig templateField : list) {
                    for (TemplateFieldsConfig fieldsClass : entryFormFieldsClassList) {
                        if (fieldsClass.getFieldName().equals(templateField.getFieldName()) && fieldsClass.getParentFieldName().equals(templateField.getParentFieldName())) {
                            templateField.setTemplateConfigId(configId);
                            templateField.setId(fieldsClass.getId());
                            templateField.setParentId(fieldsClass.getParentId());
                            templateField.setTenantId(tenantId);
                            // 数据库表中无此字段，注释掉
//                        templateField.setFieldSuffix(fieldsClass.getFieldSuffix());
//                        templateField.setFieldFormat(fieldsClass.getFieldFormat());
//                        templateField.setFieldDesensitization(fieldsClass.getFieldDesensitization());
//                        templateField.setType(fieldsClass.getType());
                            configFields.add(templateField);
                        }
                    }
                }
                config.setTemplateFieldsConfigJson(JSONUtil.toJsonStr(entryFormFieldsClassList));
                config.setTenantId(tenantId);
                config.setId(configId);
            }
            // 合同模板
            List<ContractTemplate> templateList = contractTemplateService.list(Wrappers.<ContractTemplate>lambdaQuery()
                    .eq(ContractTemplate::getTenantId, BladeConstant.ADMIN_TENANT_ID)
                    .eq(ContractTemplate::getIsDeleted, BladeConstant.DB_NOT_DELETED)
            );
            List<String> templateIds = templateList.stream().map(ContractTemplate::getTemplateId).collect(Collectors.toList());
            List<ContractSignConfig> signConfigList = contractSignConfigService.list(Wrappers.<ContractSignConfig>lambdaQuery()
                    .in(ContractSignConfig::getTemplateId, templateIds)
            );
            for (ContractTemplate contractTemplate : templateList) {
                contractTemplate.setId(null);
                contractTemplate.setTenantId(tenantId);
                contractTemplate.setCreateTime(null);
                contractTemplate.setUpdateTime(null);
            }
            for (ContractSignConfig contractSignConfig : signConfigList) {
                contractSignConfig.setId(null);
                contractSignConfig.setTenantId(tenantId);
                contractSignConfig.setCreateTime(null);
                contractSignConfig.setUpdateTime(null);
            }


            Map<String, List> map = new ConcurrentHashMap<>();
            map.put(TenantConstant.CONTRACT_TEMPLATE_CONFIG, configList);
            map.put(TenantConstant.CONTRACT_TEMPLATE_FIELD_CONFIG, configFields);
            map.put(TenantConstant.CONTRACT_TEMPLATE, templateList);
            map.put(TenantConstant.CONTRACT_TEMPLATE_SIGN_CONFIG, signConfigList);

            TenantInitEventServiceDto dto = new TenantInitEventServiceDto();
            dto.setSupport(this.support());
            CopyOnWriteArrayList<Map<String, List>> dataList = new CopyOnWriteArrayList<>();
            dataList.add(map);
            dto.setDataList(dataList);
            return dto;
        } catch (Exception e) {
            log.info("租户初始化 合同方案模板相关数据实现类错误:", e);
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void saveData(List<Map<String, List>> dataList) {
        if (CollectionUtil.isEmpty(dataList)) {
            throw new ServiceException("数据初始化异常,合同方案模板相关数据为空");
        }
        Map<String, List> contractConfigTemplateMap = dataList.get(0);
        List<ContractTemplateConfig> templateConfigs = contractConfigTemplateMap.get(TenantConstant.CONTRACT_TEMPLATE_CONFIG);
        List<TemplateFieldsConfig> templateFields = contractConfigTemplateMap.get(TenantConstant.CONTRACT_TEMPLATE_FIELD_CONFIG);
        List<ContractTemplate> templates = contractConfigTemplateMap.get(TenantConstant.CONTRACT_TEMPLATE);
        List<ContractSignConfig> signConfigs = contractConfigTemplateMap.get(TenantConstant.CONTRACT_TEMPLATE_SIGN_CONFIG);
        contractTemplateConfigService.insertBatchSomeColumn(templateConfigs);
        templateFieldsConfigService.insertBatchSomeColumn(templateFields);
        contractTemplateService.saveBatch(templates);
        contractSignConfigService.saveBatch(signConfigs);
    }

    @Override
    public Integer support() {
        return TenantChangeEnum.TENANT_CHANGE_CONTRACT_TEMPLATE.getCode();
    }
}
