package org.springblade.modules.contract.controller.back;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.sms.model.SmsCode;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.contract.dto.ContractSignParam;
import org.springblade.modules.contract.entity.Contract;
import org.springblade.modules.contract.service.IContractBackUserService;
import org.springblade.modules.contract.service.IContractService;
import org.springblade.modules.contract.service.IContractSignService;
import org.springblade.modules.contract.vo.ContractSignVO;
import org.springblade.modules.contract.vo.ContractVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CommonConstant.BLADE_CONTRACT + CommonConstant.WEB_BACK + "/contractlist")
@AllArgsConstructor
public class ContractListController {
    private final IContractService contractService;
    private final IContractBackUserService contractBackUserService;
    private final IContractSignService contractSignService;

    @PostMapping("/getContractDetails")
    /*
     * 获取合同详情
     */
    public IPage<ContractVO> getContractDetails(Query query, Contract contract) {
        IPage<ContractVO> pages = contractService.getContractList(contract, query);
        return pages;
    }

    @GetMapping("/getDetails")
    public R getDetails(@RequestParam String contractId) {
        return R.data(contractService.getDetails(contractId));
    }

    @SneakyThrows
    @PostMapping("/contractRemind")
    public R contractRemind(@RequestBody String requestData) {
        return R.success("提醒成功");
    }

    @PostMapping("/resend")
    public R resend(@RequestParam String contractId) {
        contractService.resend(contractId);
        return R.status(true);
    }

    @PostMapping("/delay")
    public R delay(@RequestParam String contractId) {
        contractService.delay(contractId);
        return R.status(true);
    }
    /**
     * 后台 使用当前用户进行签署 (自动获取部门id)
     *
     * @return
     */
    @PostMapping("/skipToSignByDeptId")
    public R skipToSignByDeptId(@RequestBody ContractSignVO contractSignVO) {
        SmsCode code = BeanUtil.copy(contractSignVO.getCode(), SmsCode.class);
        String deptId = AuthUtil.getDeptId();
        Assert.isTrue(StrUtil.isNotBlank(deptId),"账户部门配置不能为空");
        List<Long> deptIdList = Func.toLongList(deptId);
        contractSignVO.setDeptId(deptIdList.get(0));
        //当前用户签署信息
        ContractSignParam signParam = ContractSignParam.builder()
                .contractIds(contractSignVO.getContractId())
                .code(code)
                .customizeWriteBase64(contractSignVO.getCustomizeWriteBase64())
                .verifyType(contractSignVO.getVerifyType())
                .autoLock(contractSignVO.getAutoLock())
                .deptId(contractSignVO.getDeptId())
                .build();
        contractBackUserService.signByCurrentUserBack(signParam);
        return R.data("");
    }
    /**
     * 合同撤销
     *
     * @return
     */
    @SneakyThrows
    @PostMapping("/contractRevoke")
    public R contractRevoke(@RequestBody Map<String, String> param) {
        contractService.contractRevoke(param.get("contractId"));
        return R.status(Boolean.TRUE);
    }

    @PostMapping("/contractDownload")
    public R contractDownload(@RequestParam String contractId) {
        return R.data(contractService.downLoadPDF(contractId));
    }

    /**
     * 获取预览合同链接
     *
     * @param contractId
     * @return
     */
    @PostMapping("skipToPreview")
    public R skipToPreview(@RequestParam String contractId) {
        return R.data(contractService.preViewWithDevAccount(contractId));
    }

    /**
     * 使用当前用户进行签署
     *
     * @return
     */
    @PostMapping("/skipToSign")
    public R skipToSign(@RequestBody ContractSignVO contractSignVO) {
        SmsCode code = BeanUtil.copy(contractSignVO.getCode(), SmsCode.class);
        //当前用户签署信息
        ContractSignParam signParam = ContractSignParam.builder()
                .contractIds(contractSignVO.getContractId())
                .code(code)
                .customizeWriteBase64(contractSignVO.getCustomizeWriteBase64())
                .verifyType(contractSignVO.getVerifyType())
                .autoLock(contractSignVO.getAutoLock())
                .deptId(contractSignVO.getDeptId())
                .build();
        contractBackUserService.signByCurrentUserBack(signParam);
        return R.data(true);
    }


    @GetMapping("/listByFinanceApplyId")
    @ApiOperation("根据融资申请id查询合同")
    public R<List<Contract>> listByFinanceApplyId(@RequestParam Long financeApplyId) {
        return R.data(contractService.lambdaQuery().eq(Contract::getFinanceApplyId, financeApplyId)
                .orderByDesc(Contract::getCreateTime).list());
    }
    @GetMapping("/getContract")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "合同列表", notes = "传入companyId")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:getContract') or hasRole('administrator')")
    public R<IPage<ContractVO>> getContract(Long  companyId, Query query) {
        return R.data(contractService.getContract(companyId, query));
    }
}
