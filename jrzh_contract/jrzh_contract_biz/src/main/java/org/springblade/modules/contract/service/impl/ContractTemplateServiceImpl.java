/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.contract.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.word.PoiTiUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.modules.contract.dto.ContractTemplateDTO;
import org.springblade.modules.contract.dto.UpdateStatusDTO;
import org.springblade.modules.contract.entity.ContractSignConfig;
import org.springblade.modules.contract.entity.ContractTemplate;
import org.springblade.modules.contract.handler.ElecSignHandlerFactory;
import org.springblade.modules.contract.mapper.ContractTemplateMapper;
import org.springblade.modules.contract.service.IContractSignConfigService;
import org.springblade.modules.contract.service.IContractTemplateOperator;
import org.springblade.modules.contract.service.IContractTemplateService;
import org.springblade.modules.contract.vo.ContractTemplateVO;
import org.springblade.modules.contract.wrapper.ContractTemplateWrapper;
import org.springblade.otherapi.core.constant.ApiSupplier;
import org.springblade.otherapi.core.constant.OtherApiTypeEnum;
import org.springblade.otherapi.core.dto.ApiParamDTO;
import org.springblade.othersapi.bestsign.constant.ContractEnum;
import org.springblade.othersapi.core.service.IOthersApiService;
import org.springblade.resource.builder.oss.OssBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Service
@RequiredArgsConstructor
public class ContractTemplateServiceImpl extends BaseServiceImpl<ContractTemplateMapper, ContractTemplate> implements IContractTemplateService {
    private final IOthersApiService othersApiService;
    private final IContractSignConfigService contractSignConfigService;
    private final OssBuilder ossBuilder;
    private final ElecSignHandlerFactory elecSignHandlerFactory;
    private final ContractStrategy contractStrategy;
    private final List<IContractTemplateOperator> contractTemplateOperators;

    @Override
    public IPage<ContractTemplateVO> selectContractTemplatePage(IPage<ContractTemplateVO> page, ContractTemplateVO contractTemplate) {
        return page.setRecords(baseMapper.selectContractTemplatePage(page, contractTemplate));
    }

    /***
     * 同步合同模板
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncBestSign(Query query) {
        //拉取三方模板
        List<ContractTemplate> supplierTemplates = elecSignHandlerFactory.template().listContractTemplate(query);
        //查询系统所有模板 k:三方模板Id v:模板信息
        Map<String, ContractTemplate> sysTemplateMap = listTemplateBySupplier(ApiSupplier.BEST_SIGN.getCode()).stream().collect(HashMap::new, (m, v) -> m.put(v.getTemplateId(), v), HashMap::putAll);
        //建立待插入列表
        List<ContractTemplate> needInsertTemplates = new ArrayList<>(supplierTemplates.size());
        Long userId = AuthUtil.getUserId();
        for (ContractTemplate supplierTemplate : supplierTemplates) {
            //存在更新 不存在插入三方供应商模板
            String templateId = supplierTemplate.getTemplateId();
            if (sysTemplateMap.containsKey(templateId)) {
                ContractTemplate sysTemplate = sysTemplateMap.get(templateId);
                sysTemplate.setTemplateName(supplierTemplate.getTemplateName());
                sysTemplate.setTemplateCategory(supplierTemplate.getTemplateCategory());
                needInsertTemplates.add(sysTemplate);
            } else {
                supplierTemplate.setStatus(CommonConstant.CLOSESTATUS);
                supplierTemplate.setCreateUser(userId);
                supplierTemplate.setCreateTime(new Date());
                needInsertTemplates.add(supplierTemplate);
            }
            //从map中移除最后剩下三方不存在的
            sysTemplateMap.remove(templateId);
        }
        //保存
        saveOrUpdateBatch(needInsertTemplates);
        //删除不存在的
        if (CollectionUtils.isNotEmpty(sysTemplateMap)) {
            Set<String> needDeleteTemplates = sysTemplateMap.keySet();
            deleteByTemplateIds(new ArrayList<>(needDeleteTemplates));
        }
        return true;
    }

    /**
     * 根据code获取三方供应商远程模板
     *
     * @param code
     * @return
     */
    private List<ContractTemplate> listTemplateBySupplier(String code) {
        return list(Wrappers.<ContractTemplate>lambdaQuery()
                .eq(ContractTemplate::getApiSupplier, code)
                .eq(ContractTemplate::getContractGenType, 1));
    }

    private void deleteByTemplateIds(ArrayList<String> templateIds) {
        remove(Wrappers.<ContractTemplate>lambdaQuery().in(ContractTemplate::getTemplateId, templateIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(String ids) {
        List<Long> idList = Func.toLongList(ids);
        List<String> contractTemplateIds = this.lambdaQuery().in(ContractTemplate::getId, idList).list()
                .stream().map(ContractTemplate::getTemplateId).collect(Collectors.toList());
        if (contractTemplateIds.size() > 0) {
            contractTemplateOperators.parallelStream().forEach(e -> e.deleteWith(contractTemplateIds));
        }
        return this.deleteLogic(idList);
    }

    @Override
    public ContractTemplate getByTemplateName(String name) {
        return getOne(Wrappers.<ContractTemplate>lambdaQuery().eq(ContractTemplate::getTemplateName, name).last("limit 1"));
    }

    @Override
    public ContractTemplate getByTemplateId(String templateId) {
        return getOne(Wrappers.<ContractTemplate>lambdaQuery().eq(ContractTemplate::getTemplateId, templateId).orderByDesc(ContractTemplate::getCreateTime).last("limit 1"));
    }

    @Override
    public List<ContractTemplate> listByTemplates(List<String> contractTemplateIds) {
        List<ContractTemplate> list = list(Wrappers.<ContractTemplate>lambdaQuery()
                .in(ContractTemplate::getTemplateId, contractTemplateIds));
        return list;
    }

    @Override
    public List<ContractTemplate> listEnableAll() {
        return list(Wrappers.<ContractTemplate>lambdaQuery().eq(ContractTemplate::getStatus, CommonConstant.OPENSTATUS));
    }

    @Override
    public ContractTemplateVO details(String templateId) {
        ContractTemplate contractTemplate = getById(templateId);
        ContractTemplateVO contractTemplateVO = ContractTemplateWrapper.build().entityVO(contractTemplate);
        //获取签署集合
        List<ContractSignConfig> signConfigList = contractSignConfigService.listByTemplateId(contractTemplate.getTemplateId());
        contractTemplateVO.setSignConfigList(signConfigList);
        return contractTemplateVO;
    }

    @Override
    public String skipToTemplate() {
        return elecSignHandlerFactory.template().skipTemplatePage();
    }

    @Override
    public String preView(String templateId) {
        ContractTemplate contractTemplate = getByTemplateId(templateId);
        Optional.ofNullable(contractTemplate).orElseThrow(() -> new ServiceException("模板不存在"));
        return contractStrategy.genContractInstance(contractTemplate.getContractGenType()).preViewTemplate(contractTemplate);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableDisable(UpdateStatusDTO updateStatusDTO) {
        List<Long> ids = updateStatusDTO.getIds();
        List<ContractTemplate> contractTemplateList = this.lambdaQuery().in(ids.size() > 0, ContractTemplate::getId, ids).list();
        contractTemplateList.forEach(e -> {
            e.setOperateName(AuthUtil.getUser().getUserName());
            e.setOperateTime(new Date());
            e.setStatus(updateStatusDTO.getStatus());
        });
        return this.updateBatchById(contractTemplateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateContractTemplateDTO(ContractTemplateDTO contractTemplate) {
        //默认合同时效
        contractTemplate.setExpireDay(365);
        Long id = contractTemplate.getId();
        if (ContractEnum.ContractGenType.CUSTOMIZE_TEMPLATE.getType().equals(contractTemplate.getContractGenType())) {
            //设置自定义文件参数
            settingCustomizerParam(contractTemplate);
        }
        //检查是否需要平台签署
        if (CommonConstant.YES.equals(contractTemplate.getNeedPlatSign())) {
            checkNeedPlatSign(contractTemplate);
        }
        //检查签署人数
        checkSignNumber(contractTemplate);
        //为空时证明是自定义模板
        if (ObjectUtil.isEmpty(id)) {
            //设为平台合同引擎 平台模板
            contractTemplate.setApiSupplier("");
            save(contractTemplate);
            id = contractTemplate.getId();
        } else {
            contractSignConfigService.removeByTemplateId(contractTemplate.getTemplateId());
            saveOrUpdate(contractTemplate);
        }
        //检查是否需要平台签署
        if (CommonConstant.YES.equals(contractTemplate.getNeedPlatSign())) {
            checkNeedPlatSign(contractTemplate);
        }
        //检查签署人数
        checkSignNumber(contractTemplate);
        List<ContractSignConfig> signConfigList = contractTemplate.getSignConfigList();
        for (ContractSignConfig signConfig : signConfigList) {
            signConfig.setTemplateId(contractTemplate.getTemplateId());
            signConfig.setId(null);
        }
        //保存
        contractSignConfigService.saveBatch(signConfigList);
        return true;
    }

    private void checkSignNumber(ContractTemplateDTO contractTemplate) {
        Integer signerNum = contractTemplate.getSignerNum();
        List<ContractSignConfig> signConfigList = contractTemplate.getSignConfigList();
        List<Integer> nums = signConfigList.stream().map(ContractSignConfig::getSignNo).distinct().sorted().collect(Collectors.toList());
        Integer maxNums = nums.get(nums.size() - 1);
        if (signerNum.compareTo(maxNums) != 0 || signerNum.compareTo(nums.size()) != 0) {
            throw new ServiceException("签署关键字错误，请根据配置的签署人数依次填入");
        }
    }

    private void checkNeedPlatSign(ContractTemplateDTO contractTemplate) {
//       //查看平台方是否存在
        String code = OtherApiTypeEnum.ELEC_SIGN.getCode();
        ApiParamDTO singleParam = othersApiService.getSingleParamByTypeCode(code);
        if (ObjectUtil.isEmpty(singleParam)) {
            throw new ServiceException("未配置电子签接口配置,请前往接口列表模块进行配置");
        }
//        if (!ApiSupplier.JR_SIGN.getCode().equals(singleParam.getCode())) {
        Map<String, String> paramMap = singleParam.getParamMap();
        String platAccount = paramMap.get("platAccount");
        if (ObjectUtil.isEmpty(platAccount)) {
            throw new ServiceException("未设置平台签署账户,请前往接口列表模块进行配置");
        }
//
        Integer signerNum = contractTemplate.getSignerNum();
        if (signerNum < 2) {
            throw new ServiceException("设置平台方签署人数不得小于2人");
        }

//        }
    }

    /**
     * 设置其自定义模板word文件base64
     *
     * @param contractTemplate
     */
    @SneakyThrows
    private void settingCustomizerParam(ContractTemplateDTO contractTemplate) {
        Long id = contractTemplate.getId();
        //若模板文件已上传并无变化 则跳过
        if (ObjectUtil.isNotEmpty(id)) {
            ContractTemplate template = getById(id);
            if (ObjectUtil.isNotEmpty(template.getTemplateWordUrl()) && template.getTemplateWordUrl().equals(contractTemplate.getTemplateWordUrl())) {
                return;
            }
        } else {
            contractTemplate.setTemplateId(IdWorker.getIdStr());
        }
        //转流后获取合同模板文件的pdfbase64
        InputStream fileStream = PoiTiUtils.pictureUrlToInputStream(contractTemplate.getTemplateWordUrl());
        File file = PoiTiUtils.wordToPdf(fileStream);
        fileStream.close();
        FileInputStream stream = new FileInputStream(file);
        BladeFile bladeFile = ossBuilder.template().putFile(IdWorker.getIdStr() + ".pdf", stream);
        stream.close();
        //设置参数
        contractTemplate.setTemplateUrl(bladeFile.getLink());
    }

    @Override
    public boolean saveBatch(Collection<ContractTemplate> entityList) {
        return super.saveBatch(entityList);
    }
}
