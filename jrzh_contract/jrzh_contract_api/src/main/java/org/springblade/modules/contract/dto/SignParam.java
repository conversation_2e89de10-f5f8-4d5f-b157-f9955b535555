package org.springblade.modules.contract.dto;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.modules.contract.entity.*;

import java.util.List;

/**
 * 签署参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignParam {
    /**
     * 用户id 必填
     */
    private Long signerId;
    /**
     * 签署者 必填
     */
    private String signerName;
    /**
     * 签署个人名称 必填
     */
    private String personalName;
    /**
     * 操作人id 必填
     */
    private Long personalUserId;
    /**
     * 用户类型 必填
     */
    private Integer customerType;
    /**
     * 合同id 必填
     */
    private String contractId;
    /**
     * 模板id 必填
     */
    private String templateId;
    /**
     * 返回地址
     */
    private String returnUrl;
    /**
     * 签署人
     */
    private ContractOperator contractOperator;
    /**
     * 签署配置 无需填写
     */
    private ContractConfig contractConfig;
    /**
     * 签署人配置 无需填写
     */
    private List<ContractSignConfig> contractSignConfigs;
    /**
     * 签署印章/签名 无需填写
     */
    private String signOrStampName;
    /**
     * 合同信息 无需填写
     */
    private Contract contract;
    /**
     * 自动签署字段 无需填写
     */
    private String vars;
    /**
     * 自动签署字段jSON 无需填写
     */
    private JSONObject varsJSON;
    /**
     * 手写版签名base64 无需填写
     */
    private String customizeWriteBase64;
    /**
     * 合同模板信息 无需填写
     */
    private ContractTemplate contactTemplate;
    /**
     * 校验类型
     */
    private Integer verifyType;
    /**
     * 校验码
     */
    private String code;
    /**
     * 是否自动锁定 默认达到签署上限自动锁定
     */
    private Integer autoLock;
    /**
     * 是否最后一份签署 当签署多份合同时 并且配置了校验方式 只需要校验最后一份合同是否校验即可 0 否 1 是
     */
    private Boolean isLast;

    public SignParam(Long signerId, String contractId, String templateId, ContractOperator contractOperator, ContractConfig contractConfig, Contract contract) {
        this.signerId = signerId;
        this.contractId = contractId;
        this.templateId = templateId;
        this.contractOperator = contractOperator;
        this.contractConfig = contractConfig;
        this.contract = contract;

    }

    public SignParam(Long signerId, String contractId, String templateId, String vars) {
        this.signerId = signerId;
        this.contractId = contractId;
        this.templateId = templateId;
        this.vars = vars;
    }
}
