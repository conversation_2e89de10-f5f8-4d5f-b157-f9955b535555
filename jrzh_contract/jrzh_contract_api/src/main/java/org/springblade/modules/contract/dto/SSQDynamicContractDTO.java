package org.springblade.modules.contract.dto;


import cn.hutool.json.JSONArray;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SSQDynamicContractDTO {


	/**
	 * 模板id
	 */
	private String templateId;

	/**
	 * 合同签署人
	 */
	private JSONArray roles;


	/**
	 * 合同模板动态字段
	 */
	private List<BusinessFeildSimpleDTO> textLabels;


	private String bizNo;


}
