package org.springblade.modules.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 上上签合同签署回调结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BestSignContractSignCallBack implements Serializable {
	@ApiModelProperty(value = "合同编号")
	private String contractId;
	@ApiModelProperty(value = "签署者账号")
	private String account;
	@ApiModelProperty(value = "签署状态：（2：已完成；3：已取消")
	private String signerStatus;
	@ApiModelProperty(value = "签署成功为success，签署失败为相应的错误信息")
	private String errMsg;
	@ApiModelProperty(value = "错误码，对照错误码列表")
	private String code;
	@ApiModelProperty(value = "业务流水号，如果在send接口中提交了则会返回，没提交则不返回")
	private String sid;
}
