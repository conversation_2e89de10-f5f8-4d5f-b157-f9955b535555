package org.springblade.modules.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParticipantsDTO {
	/**
	 * 接收方名称
	 */
	private String participantName;
	/**
	 * 用户类型
	 */
	private String userType;
	/**
	 * 接收者类型
	 */
	private String receiverType;
	/**
	 * 名字
	 */
	private String name;

}
