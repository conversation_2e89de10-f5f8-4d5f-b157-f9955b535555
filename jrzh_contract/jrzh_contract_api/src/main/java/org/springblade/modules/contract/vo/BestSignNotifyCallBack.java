package org.springblade.modules.contract.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 上上签异步回调结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BestSignNotifyCallBack implements Serializable {
	/**
	 * 事件
	 */
	private String action;
	/**
	 * 结果
	 */
	private String params;
}
