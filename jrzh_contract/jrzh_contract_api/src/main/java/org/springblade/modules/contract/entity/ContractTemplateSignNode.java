/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 合同模板签署节点表实体类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@TableName("jrzh_contract_template_sign_node")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractTemplateSignNode对象", description = "合同模板签署节点表")
public class ContractTemplateSignNode extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板配置id")
    private Long templateConfigId;

    /**
     * 产品类型
     */
    private Integer goodsType;

    /**
     * 签署节点
     */
    private String signNode;
}
