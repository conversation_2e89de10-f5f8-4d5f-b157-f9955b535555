/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 合同业务表实体类
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@TableName("jrzh_contract_business")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ContractBusiness对象", description = "合同业务表")
public class ContractBusiness extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 合同id
	 */
	@ApiModelProperty(value = "合同id")
	private String contractId;
	/**
	 * 业务号
	 */
	@ApiModelProperty(value = "业务号")
	private Long bizNo;
	/**
	 * 合同生成参数
	 */
	@ApiModelProperty(value = "合同生成参数")
	private String contractParams;
	/**
	 * 合同模板id
	 */
	@ApiModelProperty(value = "合同模板id")
	private String templateId;


	@ApiModelProperty(value = "签署节点")
	private String signNode;


	@ApiModelProperty(value = "流程id")
	private Long businessId;

	@ApiModelProperty(value = "流程类型")
	private Integer processType;

	@ApiModelProperty(value = "产品id")
	private Long goodsId;

	@ApiModelProperty(value = "客户id")
	private Long customerId;


	@ApiModelProperty(value = "核心企业id")
	private Long deptId;


	@ApiModelProperty(value = "资金方id")
	private Long capitalId;


	@ApiModelProperty(value = "blade_user用户表id")
	private  Long userId;



	@ApiModelProperty(value = "用户类型id")
	private  Long typeId;



	@ApiModelProperty(value = "融资申请id")
	private  Long  financeApplyId;
}
