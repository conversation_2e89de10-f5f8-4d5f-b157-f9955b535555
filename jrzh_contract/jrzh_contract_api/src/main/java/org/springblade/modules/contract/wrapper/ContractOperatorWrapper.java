package org.springblade.modules.contract.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.contract.entity.ContractOperator;
import org.springblade.modules.contract.vo.ContractOperatorVO;

import java.util.Objects;

/**
 * 合同签署表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
public class ContractOperatorWrapper  extends BaseEntityWrapper<ContractOperator, ContractOperatorVO> {
	/**
	 * 获取mapper对象
	 *
	 * @return
	 */
	public static ContractOperatorWrapper build() {
		return new ContractOperatorWrapper();
	}

	@Override
	public ContractOperatorVO entityVO(ContractOperator ContractOperator) {
		ContractOperatorVO ContractOperatorVO = Objects.requireNonNull(BeanUtil.copy(ContractOperator, ContractOperatorVO.class));
		return ContractOperatorVO;
	}
}