/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 合同模板签署配置实体类
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@TableName("jrzh_contract_sign_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractSignConfig对象", description = "合同模板签署配置")
public class ContractSignConfig extends TenantEntity {

	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "签署关键字")
	private String keywords;

	@ApiModelProperty(value = "对齐方式")
	private String signAlign;

	@ApiModelProperty(value = "签署人顺序编号")
	private Integer signNo;

	@ApiModelProperty(value = "模板id")
	private String templateId;

	@ApiModelProperty(value = "签署类型 1、签名 2、印章 3、日期")
	private Integer signType;
}
