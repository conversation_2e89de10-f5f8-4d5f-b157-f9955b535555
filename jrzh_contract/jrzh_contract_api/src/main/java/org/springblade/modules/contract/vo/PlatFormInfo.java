package org.springblade.modules.contract.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlatFormInfo implements Serializable {
	/**
	 * 合同id
	 */
	private String contractId;
	/**
	 * 有效时间
	 */
	private String effectTime;
	/**
	 * 结束时间
	 */
	private LocalDateTime finishTime;
	/**
	 * 发送时间
	 */
	private LocalDateTime sendTime;
	/**
	 * 签署截止时间
	 */
	private String signDeadline;
	/**
	 * 签署状态
	 */
	private Integer status;
}

