package org.springblade.modules.contract.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.contract.entity.ContractTemplateConfig;
import org.springblade.modules.contract.vo.ContractTemplateConfigVO;

import java.util.Objects;

/**
 * 合同模板方案表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
public class ContractTemplateConfigWrapper  extends BaseEntityWrapper<ContractTemplateConfig, ContractTemplateConfigVO> {
	/**
	 * 获取mapper对象
	 *
	 * @return
	 */
	public static ContractTemplateConfigWrapper build() {
		return new ContractTemplateConfigWrapper();
	}

	@Override
	public ContractTemplateConfigVO entityVO(ContractTemplateConfig ContractTemplateConfig) {
		ContractTemplateConfigVO ContractTemplateConfigVO = Objects.requireNonNull(BeanUtil.copy(ContractTemplateConfig, ContractTemplateConfigVO.class));
		return ContractTemplateConfigVO;
	}
}