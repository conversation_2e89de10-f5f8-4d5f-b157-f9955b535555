package org.springblade.plan.service;

import org.springblade.customer.entity.CustomerGoods;
import org.springblade.plan.dto.FinancingPlanBasicDTO;
import org.springblade.plan.entity.FinancingPlanBasic;
import org.springblade.plan.entity.FinancingPlanQuota;
import org.springblade.plan.entity.TradingOrderData;

import java.math.BigDecimal;
import java.util.List;

/**
 * 融资方案一对多类型
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface IFinancingPlanOneToManyService {
    /**
     * 融资方案计算-一对多
     *
     */
    FinancingPlanBasicDTO planCalculateOneToMany(CustomerGoods customerGroup, TradingOrderData orderDataList, Long userId, List<CustomerGoods> customerGoodsList, Integer sortType, BigDecimal customerEnterAmount1);

    FinancingPlanBasicDTO planCalculateOneToManyByAmount(List<FinancingPlanQuota> quotas, BigDecimal customerEnterAmount1);
}
