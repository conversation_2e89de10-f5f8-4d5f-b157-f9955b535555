package org.springblade.plan.listener;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.plan.event.CreateOrderSuccessEvent;
import org.springblade.plan.service.IFinancingPlanBasicService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 新订单生成或同步监听
 *
 * <AUTHOR>
 * @since 2025/02/26
 */
@Slf4j
@AllArgsConstructor
@Component
public class CreateOrderListener {
    private final IFinancingPlanBasicService financingPlanBasicService;


    @EventListener(value = {CreateOrderSuccessEvent.class})
    @Async
    public void handleCreateOrder(CreateOrderSuccessEvent event) {
        log.info("创建订单事件参数：{}", JSONUtil.toJsonStr(event));
        if (ObjectUtil.isEmpty(event.getTenantId())) {
            return;
        }
        TenantBroker.runAs(event.getTenantId(), e -> {
            financingPlanBasicService.createLimitAndPlan(event.getOrderList(), event.getScenarioType());
        });
    }
}
