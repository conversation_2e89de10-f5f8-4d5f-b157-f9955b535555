/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.service.impl;

import lombok.RequiredArgsConstructor;
import org.springblade.plan.entity.FinancingPlanQuotaBind;
import org.springblade.plan.vo.FinancingPlanQuotaBindVO;
import org.springblade.plan.mapper.FinaningPlanQuotaBindMapper;
import org.springblade.plan.service.IFinancingPlanQuotaBindService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 配额绑定订单表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
@RequiredArgsConstructor
public class FinancingPlanQuotaBindServiceImpl extends BaseServiceImpl<FinaningPlanQuotaBindMapper, FinancingPlanQuotaBind> implements IFinancingPlanQuotaBindService {

	@Override
	public IPage<FinancingPlanQuotaBindVO> selectFinaningPlanQuotaBindPage(IPage<FinancingPlanQuotaBindVO> page, FinancingPlanQuotaBindVO finaningPlanQuotaBind) {
		return page.setRecords(baseMapper.selectFinaningPlanQuotaBindPage(page, finaningPlanQuotaBind));
	}

}
