/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.vo;

import lombok.Data;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.plan.dto.ExpenseInfoDTO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 缴纳费用页面信息DTO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ExpenseInfoPlanVO {
	private static final long serialVersionUID = 1L;

	private String financeNo;
	private Long goodsId;

	private List<ExpenseInfoExpenseVO> expenseInfoExpenseVOs;

}
