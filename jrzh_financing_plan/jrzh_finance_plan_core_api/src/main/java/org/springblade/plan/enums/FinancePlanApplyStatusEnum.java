package org.springblade.plan.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 方案申请状态枚举     用于融资列表状态展示
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FinancePlanApplyStatusEnum {
    UN_SUBMIT("待提交", 0),
    APPLICATION("放款申请审核中", 1),
    UN_SETTLED("待结清", 2),
    SETTLED("已结清",3),
    OTHER("其他", 4),
    ADJUST_APPROVE("融资申请审核中", 5),
    ADJUST_REJECT("融资申请被驳回", 6),
    ADJUST_TERMINAL("融资申请已终止", 7),
    ADJUST_REFUSE("融资申请已拒绝", 8),
    ;


    final String name;
    final Integer code;
}
