package org.springblade.plan.dto;

import lombok.Data;
import org.springblade.product.common.vo.GoodsContractTemplateVO;

import java.util.List;

/**
 * 合同模版DTO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ContractTemplateQueryDTO {

    private Long goodsId;
    private String signNode;
    private String processType;
    private Integer signUser;
    private Long financeApplyId;
    private Integer needUpdate;
    private String contractIds;
    private String bizNos;
    private String bigBizNos;

}
