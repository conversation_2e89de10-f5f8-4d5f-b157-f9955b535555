
package org.springblade.plan.vo;

import org.springblade.plan.entity.TradingOrderData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 测试订单数据表视图实体类
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TradingOrderDataVO对象", description = "测试订单数据表")
public class TradingOrderDataVO extends TradingOrderData {
	private static final long serialVersionUID = 1L;

}
