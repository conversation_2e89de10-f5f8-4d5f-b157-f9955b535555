/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import oracle.net.ns.Message;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.plan.entity.FinancingPlanQuota;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 缴纳费用页面信息DTO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ExpenseInfoDTO {
	private static final long serialVersionUID = 1L;

	private String financeNo;
	@NotNull(message="类型不能为空")
	private Integer type;
	@NotNull(message="费用节点不能为空")
	private Integer feeNode;
	private Long bankCardId;
	@NotNull(message="产品id不能为空")
	private Long goodsId;

	private  List<ExpenseInfoExpenseVO> expenseInfoExpenseVOs;


}
