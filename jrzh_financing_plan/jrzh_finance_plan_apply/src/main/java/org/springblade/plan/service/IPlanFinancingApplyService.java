package org.springblade.plan.service;

import org.springblade.core.tool.api.R;
import org.springblade.customer.vo.CustomerGoodsVO;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.plan.dto.ExpenseInfoDTO;
import org.springblade.plan.dto.FinanceConfirmPlanDTO;
import org.springblade.plan.dto.FinancingPlanBasicDTO;
import org.springblade.plan.dto.LoanApplyPlanDTO;
import org.springblade.plan.entity.FinancingPlanBasic;
import org.springblade.plan.vo.CostCalculusPlanVO;
import org.springblade.plan.vo.ExpenseInfoPlanVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 方案融资申请 服务接口
 *
 * <AUTHOR>
 * @since 2025-2-20
 */
public interface IPlanFinancingApplyService {

    /**
     * 融资校验
     * 校验当前客户是否存在可融资的资方产品，不存在则提示：当前无资方产品可使用
     * 校验当前客户是否存在融资方案数据，不存在则提示：当前无订单可融资
     * 校验当前客户是否存在正在融资的订单，存在则提示：当前有一笔融资正在进行
     */
    R financeCheck(Long groupId,Long userId);

    /**
     * 还款试算-方案
     *
     * @param costCalculusDtoList 还款试算参数
     * @return
     */
    CostCalculusPlanVO repaymentCalculation(@Valid List<CostCalculusDto> costCalculusDtoList);

    /**
     * 融资确认审批提交
     *
     * @param financingPlanBasicDTO 融资方案基础数据DTO
     */
    Long submitConfirm(@Valid FinancingPlanBasicDTO financingPlanBasicDTO);

    /**
     * 方案融资申请提交
     *
     * @param financingPlanBasicDTO 融资方案基础数据DTO
     * @return
     */
    List<Long> planApplySubmit(@Valid FinancingPlanBasicDTO financingPlanBasicDTO);

    /**
     * 方案融资申请提交新
     *
     * @param financingPlanBasicDTO 融资方案基础数据DTO
     * @return
     */
    List<Long> planApplySubmitNew(FinancingPlanBasicDTO financingPlanBasicDTO);

    /**
     * 查询缴纳费用页面信息
     * <p>
     * financeNo 业务编号 当前是使用融资编号 并非一定要融资编号
     * type      费用业务类型 PlatformExpensesEnum
     * feeNode   当前节点 ExpenseConstant.FeeNodeEnum
     */
    List<ExpenseInfoPlanVO> expenseList(List<ExpenseInfoDTO> expenseInfoDtos);

    /**
     * 提交自动放款融资申请流程
     *
     * @param loanApplyPlanDTO 方案费用提交DTO
     * @return
     */
    Boolean submitAutoLoanProcess(LoanApplyPlanDTO loanApplyPlanDTO);

    /**
     * 方案融资确认
     *
     * @param financeConfirmPlanDTO 方案融资确认DTO
     * @return
     */
    Boolean financeConfirm(FinanceConfirmPlanDTO financeConfirmPlanDTO);

    /**
     * 根据产品组id查询可融资产品详情
     * @param groupId 融资产品id
     * @return
     */
//    List<CustomerGoodsVO> detailListByGroupId(Long groupId, Long userId);
}
