package org.springblade.finance.listener;

import lombok.RequiredArgsConstructor;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.otherapi.core.constant.OtherApiTypeEnum;
import org.springblade.otherapi.core.dto.PayManagerConfig;
import org.springblade.othersapi.core.utils.MD5Utils;
import org.springblade.othersapi.core.utils.OtherApiUtils;
import org.springblade.othersapi.paymanager.dto.TransferReceiptDTO;
import org.springblade.othersapi.paymanager.service.IPayManagerService;
import org.springblade.pay.constant.PayOnlineEnum;
import org.springblade.product.common.dto.CapitalPayMethodAndCapitalType;
import org.springblade.product.common.entity.Product;
import org.springblade.product.expense_relation.service.IBillBankCardaRelationService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;


/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023/1/10 18:24
 * @Description: 代付监听 作用：1、分配任务至指定审批人 2、提交代付订单。
 * @Version: 1.0
 */
@Component("transferListener")
@RequiredArgsConstructor
public class TransferListener implements org.flowable.engine.delegate.TaskListener {
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final ProductDirector productDirector;

    private final IPayManagerService payManagerService;
    private final IBillBankCardaRelationService billBankCardaRelationService;

    private final RemoteUserService remoteUserService;

    private final static String PAYED_NOTIFY_FORMAT = "%s/pay/external/pay-counter/pay/notify/%s/%s/%s/%s";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateTask delegateTask) {
        String status = delegateTask.getVariable(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, String.class);
        Boolean hasNonCapitalBank = delegateTask.getVariable(ProcessConstant.HAS_NON_CAPITAL_BANK, Boolean.class);
        if (!hasNonCapitalBank) {
            return;
        }
        if (StringUtil.isNotBlank(status) && StringUtil.equals(status, WfProcessConstant.STATUS_REJECT)) {
            return;
        }
        String eventName = delegateTask.getEventName();
        switch (eventName) {
            case EVENTNAME_CREATE:
                System.err.println("任务创建");
                break;
            case EVENTNAME_ASSIGNMENT:
                System.err.println("任务分配审核人");
                break;
            case EVENTNAME_COMPLETE:
                transferComplete(delegateTask);
                break;
        }
    }

    private void transferComplete(DelegateTask delegateTask) {
        //参数检查
        String transferAmount = delegateTask.getVariable(ProcessConstant.TRANSFER_AMOUNT, String.class);
        String financeNo = delegateTask.getVariable(ProcessConstant.FINANCE_NO, String.class);
        Long goodsId = delegateTask.getVariable(ProcessConstant.GOODS_ID, Long.class);
        Long userId = delegateTask.getVariable(ProcessConstant.USER_ID, Long.class);
        String transferBizType = delegateTask.getVariable(ProcessConstant.TRANSFER_BIZ_TYPE, String.class);
        String transferPassword = delegateTask.getVariable(ProcessConstant.TRANSFER_PASSWORD, String.class);

        //接口配置信息
        PayManagerConfig params = OtherApiUtils.getParams(OtherApiTypeEnum.POLYMERIZATION_PAY.getCode(), PayManagerConfig.class);
        EnterpriseQuotaVO quotaVO = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, 1, userId);
        if (ObjectUtil.isEmpty(quotaVO)) {
            throw new ServiceException("代付失败，对方的额度信息不存在，请联系管理员");
        }
        //关联的资方信息
        Product product = productDirector.detailBase(goodsId);
        CapitalPayMethodAndCapitalType capitalPayModeByGoodsId = billBankCardaRelationService.getCapitalPayModeByGoodsId(product);
        User user = remoteUserService.getUserById(userId, FeignConstants.FROM_IN).getData();
        TransferReceiptDTO transferReceipt = new TransferReceiptDTO();
        transferReceipt.setMerchantNo(capitalPayModeByGoodsId.getMerchantNo());
        transferReceipt.setPayPassword(MD5Utils.stringMD5(transferPassword));
        transferReceipt.setAmount(new BigDecimal(transferAmount));
        transferReceipt.setBankAccountNo(quotaVO.getBankCardNo());
        transferReceipt.setBankName(quotaVO.getBank());
        transferReceipt.setBankAccountName(user.getName());
        transferReceipt.setTransferType(transferBizType);
        transferReceipt.setOrderNo(Collections.singletonList(financeNo));
        transferReceipt.setBankUnionCode(quotaVO.getBankUnionCode());
        transferReceipt.setOuterNotifyUrl(String.format(PAYED_NOTIFY_FORMAT,
                params.getNotifyUrl(),
                AuthUtil.getTenantId(),
                PlatformExpensesEnum.ONLINE_LOAN.getCode(),
                financeNo, PayOnlineEnum.TRANSFER_SERVICE.getCode()));
//        PayTransferOrder transfer = payManagerService.transfer(transferReceipt);
//        //打款不是处理中或者成功 直接报错
//        if (!(TransferPayEnum.OrderStatus.SUCCESS.getStatus().equals(transfer.getOrderStatus()) ||
//                TransferPayEnum.OrderStatus.CREATE.getStatus().equals(transfer.getOrderStatus()))) {
//            throw new ServiceException("代付失败，请稍后再试");
//        }
    }
}
