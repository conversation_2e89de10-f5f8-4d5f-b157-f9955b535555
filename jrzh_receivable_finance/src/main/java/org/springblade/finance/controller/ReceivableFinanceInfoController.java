package org.springblade.finance.controller;

import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.finance.service.ReceivableFinanceInfoService;
import org.springblade.finance.vo.BackApplyInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/14
 * @description 应收账款相关信息查询
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_BUSINESS + CommonConstant.WEB_BACK + "/receivable-finance-apply")
public class ReceivableFinanceInfoController extends BladeController {
    private final ReceivableFinanceInfoService receivableFinanceInfoService;

    @GetMapping("/applyInfo")
    @ApiOperation("申请信息")
    public R<BackApplyInfo> applyInfo(@RequestParam Long id) {
        return R.data(receivableFinanceInfoService.selectBackFinanceApplyInfo(id));
    }

    @GetMapping("/availableFinanceAmount")
    @ApiOperation("客户产品最多可融资金额")
    public R<BigDecimal> availableFinanceAmount(@RequestParam Long customerGoodsId, Long enterpriseQuotaId) {
        return R.data(receivableFinanceInfoService.availableFinanceAmount(customerGoodsId,enterpriseQuotaId));
    }
}
