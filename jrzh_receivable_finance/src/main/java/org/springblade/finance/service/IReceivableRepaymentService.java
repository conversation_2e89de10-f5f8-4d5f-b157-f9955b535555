
package org.springblade.finance.service;

import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.vo.CostCalculusVO;

/**
 * 应收账款-还款接口
 *
 * @Author: <PERSON>hengchuangkai
 * @CreateTime: 2023/8/30 15:50
 * @Description: 应收账款-还款接口
 * @Version: 1.0
 */
public interface IReceivableRepaymentService {
    /**
     * 融资申请还款试算
     * @param costCalculusDto 融资--还款试算查询条件
     * @return
     */
    CostCalculusVO costCalculus(CostCalculusDto costCalculusDto);

    CostCalculusVO costCalculusByFinanceId(Long financeApplyId);
}
