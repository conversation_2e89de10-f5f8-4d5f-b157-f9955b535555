package org.springblade.finance.service.impl;

import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.service.IExpenseInfoBizService;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.finance.dto.FinanceApplyDTO;
import org.springblade.finance.dto.FinanceConfirmDTO;
import org.springblade.finance.dto.LoanApplyDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.handler.ReceivableFinanceService;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.finance.service.IReceivableFinanceApplyService;
import org.springblade.finance.wrapper.FinanceApplyWrapper;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springblade.loan.service.ILoanManageRepaymentTermService;
import org.springblade.loan.service.IRepaymentPlanJsonService;
import org.springblade.modules.contract.service.IContractService;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.product.expense_relation.service.IBillBankCardaRelationService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.product.process_relation.service.IGoodsProcessService;
import org.springblade.product.receivable.entity.Goods;
import org.springblade.product.receivable.service.IGoodsService;
import org.springblade.product.receivable.vo.GoodsVO;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.system.utils.UserUtils;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/31
 * @description
 */
@Service
@RequiredArgsConstructor
public class ReceivableFinanceApplyServiceImpl extends BaseServiceImpl<FinanceApplyMapper, FinanceApply> implements IReceivableFinanceApplyService {
    /**
     * 业务流程
     */
    private final IBusinessProcessService businessProcessService;
    /**
     * 产品进度
     */
    private final IBusinessProcessProgressService businessProcessProgressService;
    /**
     * 产品流程
     */
    private final IGoodsProcessService goodsProcessService;
    /**
     * 费用订单
     */
    private final IExpenseOrderService expenseOrderService;
    /**
     * 费用订单详情
     */
    private final IExpenseOrderDetailService expenseOrderDetailService;
    /**
     * 合同
     */
    private final IContractService contractService;

    private final ReceivableFinanceService receivableFinanceService;
    private final ProductDirector productDirector;

    private final IBillBankCardaRelationService billBankCardaRelationService;
    private final IExpenseInfoBizService expenseInfoBizService;
    /**
     * 应收账款产品
     */
    private final IGoodsService goodsService;

    private final RemoteUserService remoteUserService;
    private final LoanManageIouMapper loanManageIouMapper;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final IBusinessProcessProductService businessProcessProductService;
    private final IProductExpenseService productExpenseService;
    private final IRepaymentPlanJsonService repaymentPlanJsonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean financeConfirm(FinanceConfirmDTO financeConfirmDTO) {
        // 检查合同是否签署
        List<String> contractIdList = financeConfirmDTO.getContractIdList();
        String processNo = businessProcessService.getProcessNo(financeConfirmDTO.getProcessInstanceId());
        if (!CollectionUtils.isEmpty(contractIdList)) {
            contractService.checkContractIsSign(contractIdList, AuthUtil.getUserId());

        }
        FinanceApply financeApply = baseMapper.selectById(financeConfirmDTO.getFinanceApplyId());
        // 检查融资申请是否处于可操作阶段
        receivableFinanceService.checkApplyFinanceApply(financeApply.getId());
        Goods goods = goodsService.getById(financeApply.getGoodsId());
        Integer processType = ProcessTypeEnum.AUTO_LOAN_FINANCE_APPLY.getCode();
        String processInstanceId = null;
        // 如果是资方统一收取费用的话，提交流程
        Integer frontOperateAbility = CommonConstant.YES;
        if (goods.getChargeMethod().equals(GoodsEnum.UNIFIED.getCode())) {
            frontOperateAbility = CommonConstant.NO;
            //改为融资申请中
            changeStatus(Func.toLongList(financeApply.getId().toString()), FinanceApplyStatusEnum.APPLICATION.getCode());
            Map<String, Object> variables = getVariables(FinanceApplyWrapper.build().entityDTO(financeApply));
            variables.put(ProcessConstant.CONTRACT_ID, Func.join(contractIdList));
            variables.put(ProcessConstant.FINANCE_APPLY_ID, financeConfirmDTO.getFinanceApplyId());
            variables.put(WfProcessConstant.PROCESS_NO, processNo);
            variables.put(ProcessConstant.EXPENSE_INFO_LIST, financeConfirmDTO.getExpenseInfoExpenseVOList());
            //设置资方变量
            businessProcessProgressService.settingCapitalVarByCapitalId(variables, financeApply.getFinanceNo(), financeApply.getGoodsType(), financeApply.getUserId(), financeApply.getCapitalId(), financeApply.getGoodsId());
            if (StringUtil.isBlank(financeConfirmDTO.getProcessInstanceId())) {
                String processDefinitionKey = goodsProcessService.selectProcessKey(goods.getId(), processType);
                processInstanceId = businessProcessService.startProcess(processDefinitionKey, variables);
            } else {
                businessProcessService.completeTask(financeConfirmDTO.getProcessInstanceId(), variables);
            }
        }
        // 保存流程进度
        businessProcessProgressService
                .updateBusinessProcessProgressUnContainInvalid(financeConfirmDTO.getFinanceApplyId(), 2,
                        processType, processInstanceId, MyAuthUtil.getUserId(), ProcessStatusEnum.APPROVING.getCode(), frontOperateAbility, true);
        return true;
    }

    @Override
    public boolean submitAutoLoanProcess(LoanApplyDTO loanApplyDTO) {
        int platType = PlatformExpensesEnum.PLAT_TYPE_RECEIVABLE.getCode();
        Long financeApplyId = loanApplyDTO.getFinanceApplyId();
        FinanceApply financeApply = baseMapper.selectById(financeApplyId);
        // 检查融资申请是否处于可操作阶段
        receivableFinanceService.checkApplyFinanceApply(financeApply.getId());
        // 检查合同是否签署
        List<String> contractIdList = loanApplyDTO.getContractIds();
        Map<String, Object> variables = getVariables(FinanceApplyWrapper.build().entityDTO(financeApply));
        if (!CollectionUtils.isEmpty(contractIdList)) {
            contractService.checkContractIsSign(contractIdList, AuthUtil.getUserId());
            variables.put(ProcessConstant.CONTRACT_ID, Func.join(contractIdList));
        }
        variables.put(ProcessConstant.ATTACH_ID, loanApplyDTO.getAttachIds());
        int processType = ProcessTypeEnum.AUTO_LOAN_FINANCE_APPLY.getCode();
        variables.put(ProcessConstant.PROCESS_TYPE, processType);
        variables.put(ProcessConstant.GOODS_ID, financeApply.getGoodsId());
        variables.put(ProcessConstant.ENTERPRISE_TYPE, UserUtils.getEnterpriseType());
        String processInstanceId = loanApplyDTO.getProcessInstanceId();
        //设置基础产品信息
        //设置资方变量
        businessProcessProgressService.settingCapitalVarByCapitalId(variables, financeApply.getFinanceNo(), financeApply.getGoodsType(), financeApply.getUserId(), financeApply.getCapitalId(), financeApply.getGoodsId());
        Boolean hasCapitalBank = billBankCardaRelationService.selectIsFictitiousAccount(financeApply.getGoodsId());
        if (hasCapitalBank) {
            //设置非资方银行变量
            variables.put(org.springblade.process.constant.ProcessConstant.HAS_NON_CAPITAL_BANK, true);
            Integer customerType = MyAuthUtil.getCustomerType();
            //设置对公与对私
            if (CustomerTypeEnum.PERSONAL.getCode().equals(customerType)) {
                variables.put(org.springblade.process.constant.ProcessConstant.TRANSFER_BIZ_TYPE, CommonConstant.B2C);
            } else {
                variables.put(org.springblade.process.constant.ProcessConstant.TRANSFER_BIZ_TYPE, CommonConstant.B2B);
            }
            //设置流程需要的变量
            variables.put(org.springblade.process.constant.ProcessConstant.FINANCE_NO, financeApply.getFinanceNo());
            variables.put(org.springblade.process.constant.ProcessConstant.GOODS_TYPE, financeApply.getGoodsType());
            variables.put(org.springblade.process.constant.ProcessConstant.GOODS_ID, financeApply.getGoodsId());
            variables.put(org.springblade.process.constant.ProcessConstant.USER_ID, financeApply.getUserId());
        } else {
            variables.put(org.springblade.process.constant.ProcessConstant.HAS_NON_CAPITAL_BANK, false);
        }
        variables.put(ProcessConstant.EXPENSE_INFO_LIST,loanApplyDTO.getExpenseInfoExpenseList());
        processInstanceId = businessProcessProductService.startProcess(financeApply.getGoodsId(), ProcessTypeEnum.AUTO_LOAN_FINANCE_APPLY.getCode(), variables);
        // 更新流程进度
        businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(financeApplyId,
                ProcessProgressEnum.AUTO_LOAN_FINANCE_APPROVE_2.getCode(),
                processType,
                processInstanceId,
                MyAuthUtil.getUserId(),
                ProcessStatusEnum.APPROVING.getCode(), CommonConstant.NO, true);
        financeApply.setStatus(FinanceApplyStatusEnum.LOAN_APPLICATION.getCode());
        financeApply.setProcessInstanceId(processInstanceId);
        boolean update = updateById(financeApply);
        return update;
    }


    private Map<String, Object> getVariables(FinanceApplyDTO financeApplyDTO) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ProcessConstant.FINANCE_APPLY_ID, financeApplyDTO.getId());
        map.put(ProcessConstant.FINANCE_NO, financeApplyDTO.getFinanceNo());
        map.put(ProcessConstant.CUSTOMER_GOODS_ID, financeApplyDTO.getCustomerGoodsId());
        map.put(ProcessConstant.BUSINESS_ID, financeApplyDTO.getGoodsId());
        map.put(ProcessConstant.CUSTOMER_ID, MyAuthUtil.getCustomerId());
        map.put(ProcessConstant.USER_ID, MyAuthUtil.getUserId());
        map.put(ProcessConstant.PROCESS_TYPE, ProcessTypeEnum.FINANCE_APPLY.getCode());
        map.put(ProcessConstant.FINANCE_APPLY, financeApplyDTO);
        map.put(ProcessConstant.CHARGE_METHOD, financeApplyDTO.getChargeMethod());
        map.put(WfProcessConstant.PROCESS_NO, businessProcessService.getProcessNo(financeApplyDTO.getProcessInstanceId()));
        GoodsVO goodsVO = goodsService.processGoodsInfo(financeApplyDTO.getGoodsId());
        financeApplyDTO.setGoodsName(goodsVO.getGoodsName());
        financeApplyDTO.setGoodsType(goodsVO.getType());
        financeApplyDTO.setCapitalId(goodsVO.getCapitalId());
        financeApplyDTO.setApplyUser(MyAuthUtil.getSubUserId());
        financeApplyDTO.setRepaymentType(goodsVO.getRepaymentType());
        financeApplyDTO.setLendingMethod(goodsVO.getLendingMethod());
        financeApplyDTO.setChargeMethod(goodsVO.getChargeMethod());
        map.put(ProcessConstant.PROCESS_GOODS_INFO, goodsVO);
        return map;
    }
}
