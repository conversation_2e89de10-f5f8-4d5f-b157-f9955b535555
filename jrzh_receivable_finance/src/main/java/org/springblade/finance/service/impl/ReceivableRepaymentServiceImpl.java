package org.springblade.finance.service.impl;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.service.IReceivableRepaymentService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.loan.service.IRepaymentPlanJsonService;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;

/**
 * 应收账款-还款实现类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-09-05  09:51
 * @Description: 应收账款-还款接口
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ReceivableRepaymentServiceImpl implements IReceivableRepaymentService {
    private final IFinanceApplyService financeApplyService;
    private final IRepaymentPlanFinanceApplyBizService financeApplyBizService;
    private final IRepaymentPlanJsonService repaymentPlanJsonService;

    @Override
    public CostCalculusVO costCalculus(CostCalculusDto costCalculusDto) {
//        EnterpriseQuotaSerchDTO enterpriseQuotaSerchDTO = financeEnterpriseService
//                .selectEnterpriseQuota(costCalculusDto.getGoodsId(),
//                        EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(),AuthUtil.getUserId());
//        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
//                .loanPrincipal(costCalculusDto.getFinanceAmount())
//                .serviceRate(enterpriseQuotaSerchDTO.getServiceRate().divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_EVEN))
//                .yearRate(enterpriseQuotaSerchDTO.getAnnualInterestRate().divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_EVEN))
//                .loanDays(BigDecimal.valueOf(costCalculusDto.getLoanDay()))
//                .build();
//        return financeRepaymentService.costCalculus(costCalculusDto, expenseRuleDTO);
        costCalculusDto.setExpenseOrderDetailPass(false);
        costCalculusDto.setExpenseOrderDetailPassIds("");
        costCalculusDto.setUserId(AuthUtil.getUserId());
        costCalculusDto.setEnterpriseType(UserUtils.getEnterpriseType());
        return financeApplyBizService.costCalculus(costCalculusDto);
    }

    @Override
    public CostCalculusVO costCalculusByFinanceId(Long financeApplyId) {
        FinanceApply financeApply = financeApplyService.getById(financeApplyId);
        return financeApplyBizService.costCalculusByFinanceNo(financeApply.getFinanceNo(),financeApply.getRepaymentCalType());
    }
}
