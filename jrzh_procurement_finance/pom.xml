<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh_product_proxy_procurement</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>jrzh_procurement_finance</artifactId>


    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_expense_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_proxy_procurement_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_loan_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_procurement_product</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_flow_process_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_common_mq</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_expense_deposit</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_warehouse_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_refund_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_loan_new</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>