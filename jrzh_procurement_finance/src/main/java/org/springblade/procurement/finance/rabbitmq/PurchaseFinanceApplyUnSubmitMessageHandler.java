package org.springblade.procurement.finance.rabbitmq;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.QuotaUseDetailsEnum;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.service.IQuotaUseDetailsService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.handler.MessageHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * 融资申请超时未提交流程，进行关闭操作
 *
 * <AUTHOR>
 */
@Component("PURCHASE_FINANCE_APPLY_UN_SUBMIT")
@RequiredArgsConstructor
@TenantIgnore
public class PurchaseFinanceApplyUnSubmitMessageHandler implements MessageHandler {

    private final IFinanceApplyService financeApplyService;
    private final IQuotaUseDetailsService quotaUseDetailsService;
    /**
     * 已开通产品额度
     */
    private final IEnterpriseQuotaService enterpriseQuotaService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handler(DelayMessage delayMessage) {
        String msg = delayMessage.getMsg();
        agentGoodsHandler(msg);
    }


    void agentGoodsHandler(String msg) {
        //融资申请待提交
        HashMap<String, Object> map = JSONUtil.toBean(msg, HashMap.class);
        String financeNo = map.get("financeNo").toString();
        EnterpriseQuota enterpriseQuota = JSONUtil.toBean(map.get("enterpriseQuota").toString(), EnterpriseQuota.class);
        BigDecimal amount = new BigDecimal(map.get("amount").toString());
        boolean update = financeApplyService.lambdaUpdate()
                .set(FinanceApply::getStatus, PurchaseEnum.PURCHASE_STATUS_THREE.getCode())
                .eq(FinanceApply::getFinanceNo, financeNo)
                .eq(FinanceApply::getStatus, PurchaseEnum.PURCHASE_STATUS_TWELVE.getCode())
                .update();
        if (update) {
            // 额度返回
            enterpriseQuotaService.subtractReceivableAmount(amount, enterpriseQuota, PurchaseEnum.PURCHASE_PASS_TYPE_TWO.getCode());

            //设置额度历史使用状态失败
            FinanceApply financeApply = financeApplyService.getOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeNo));
            Long quotaUseDetailsId = financeApply.getQuotaUseDetailsId();
            Integer statusCode = QuotaUseDetailsEnum.APPLY_FAIL.getCode();
            quotaUseDetailsService.updateStatus(quotaUseDetailsId, statusCode);
        }
    }

}
