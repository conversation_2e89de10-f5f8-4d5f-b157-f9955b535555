package org.springblade.procurement.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.bill.BillPayNameEnum;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.enums.rabbitmq.RabbitMqStatusEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.entity.QuotaUseDetails;
import org.springblade.customer.mapper.CustomerGoodsMapper;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.ICustomerMaterialService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.service.IQuotaUseDetailsService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.dto.CreateExpenseOrderDetailDTO;
import org.springblade.expense.dto.ExpenseOrderDTO;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.enums.FinanceFeeStatusEnums;
import org.springblade.expense.service.IExpenseInfoBizService;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.entity.LoanApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.service.IFinanceRepaymentService;
import org.springblade.finance.service.ILoanApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.IRepaymentPlanFeeService;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.procurement.finance.constant.DepositYNEnum;
import org.springblade.procurement.finance.constant.PurchaseConstant;
import org.springblade.procurement.finance.constant.PurchaseMessageTypeEnum;
import org.springblade.procurement.finance.dto.PurchaseInformationApplyDTO;
import org.springblade.procurement.finance.dto.PurchaseInformationDTO;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.service.IPurchaseCommodityService;
import org.springblade.procurement.finance.service.IPurchaseFinanceApplyService;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springblade.procurement.finance.vo.PurchaseBackApplyInfo;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;
import org.springblade.procurement.finance.wrapper.PurchaseInformationWrapper;
import org.springblade.procurement.service.IAgentCustomerGoodsService;
import org.springblade.procurement.service.IAgentGoodsService;
import org.springblade.procurement.vo.AgentGoodsVO;
import org.springblade.product.common.constant.AccountTypeEnum;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense_relation.service.IPlatformAccountService;
import org.springblade.product.moudle.timing.service.IGoodsTimingService;
import org.springblade.resource.cache.DictBizCache;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代采融资申请实现类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-21  17:00
 * @Description: 代采融资申请实现类
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class PurchaseFinanceApplyServiceImpl implements IPurchaseFinanceApplyService {

    /**
     * 客户产品
     */
    private final CustomerGoodsMapper customerGoodsMapper;

    /**
     * 已开通产品额度
     */
//    private final IEnterpriseQuotaService enterpriseQuotaService;

    /**
     * 客户资料
     */
    private final ICustomerMaterialService customerMaterialService;
    private final IFinanceRepaymentService financeRepaymentService;

    /**
     * 采购商品
     */
    private final IPurchaseCommodityService purchaseCommodityService;

    /**
     * 融资申请 服务类
     */
    private final IFinanceApplyService financeApplyService;
    private final IProductExpenseService productExpenseService;
    private final IExpenseInfoBizService expenseInfoBizService;
    /**
     * 业务流程 服务类
     */
//    private final IBusinessProcessService businessProcessService;
    /**
     * 产品-代采 服务类
     */
    private final IAgentGoodsService agentGoodsService;
    private final IAttachService attachService;
    /**
     * 平台费用 服务类
     */
//    private final IExpenseOrderDetailService expenseOrderDetailService;
    private final IPurchaseInformationService purchaseInformationService;
    private final ICustomerGoodsService customerGoodsService;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final IAgentCustomerGoodsService agentCustomerGoodsService;
    private final IExpenseOrderDetailService expenseOrderDetailService;
    private final IExpenseOrderService expenseOrderService;
    private final IPlatformAccountService platformAccountService;
    private final RabbitMsgSender rabbitMsgSender;
    private final IGoodsTimingService goodsTimingService;
    private final IExpenseDepositService expenseDepositService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;
    private final IRepaymentPlanFinanceApplyBizService financeApplyBizService;
//    /**
//     * 附件 服务类
//     */
//    private final IAttachService attachService;
//
//    /**
//     * 费用订单 服务类
//     */
//    private final IExpenseOrderService billExpenseOrderService;
//
//
//    /**
//     * 平台账户服务类
//     */
//    private final IPlatformAccountService platformAccountService;


    private final IQuotaUseDetailsService quotaUseDetailsService;

//    private final IGoodsProcessService goodsProcessService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseInformationApplyDTO purchaseApplySubmitOne(PurchaseInformationApplyDTO purchaseInformationApplyDTO) {
        FinanceApply financeApply = new FinanceApply();
        financeApply.setRepaymentCalType(PlatformExpensesEnum.PLAT_TYPE_PURCHASE.getCode());
        PurchaseInformation purchaseInformation = new PurchaseInformation();
        if (StrUtil.isBlank(purchaseInformationApplyDTO.getFinanceNo())) {
            purchaseInformationApplyDTO.setFinanceNo(CodeUtil.generateCode(CodeEnum.FINANCING_CODE));
        } else {
            List<Long> ids = purchaseCommodityService
                    .lambdaQuery().eq(PurchaseCommodity::getFinanceNo, purchaseInformationApplyDTO.getFinanceNo())
                    .list().stream()
                    .map(PurchaseCommodity::getId).collect(Collectors.toList());
            purchaseCommodityService.deleteLogic(ids);

            financeApply = financeApplyService.getByFinanceNo(purchaseInformationApplyDTO.getFinanceNo());
            purchaseInformation = purchaseInformationService.lambdaQuery().eq(PurchaseInformation::getFinanceNo, purchaseInformationApplyDTO.getFinanceNo()).one();
        }
        for (PurchaseCommodity purchaseCommodity : purchaseInformationApplyDTO.getPurchaseCommodityList()) {
            purchaseCommodity.setFinanceNo(purchaseInformationApplyDTO.getFinanceNo());
        }
        //PurchaseInformation purchaseInformation = new PurchaseInformation();
        purchaseInformation.setFinanceNo(purchaseInformationApplyDTO.getFinanceNo());
        purchaseInformation.setNode(PurchaseEnum.PURCHASE_NODE_ONE.getCode());
        purchaseInformation.setProductId(purchaseInformationApplyDTO.getProductId());
        purchaseInformation.setCustomerMaterialId(customerMaterialService.saveCustomerMaterial(purchaseInformationApplyDTO.getCustomerMaterial()));
        purchaseCommodityService.saveBatch(purchaseInformationApplyDTO.getPurchaseCommodityList());
        //FinanceApply financeApply = new FinanceApply();
        financeApply.setFinanceNo(purchaseInformationApplyDTO.getFinanceNo());
        financeApply.setUserId(MyAuthUtil.getUserId());
        financeApply.setAmount(purchaseInformationApplyDTO.getAmount());
        financeApply.setGoodsName(purchaseInformationApplyDTO.getGoodsName());
        financeApply.setGoodsId(purchaseInformationApplyDTO.getProductId());
        financeApply.setGoodsType(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
        financeApply.setStatus(PurchaseEnum.PURCHASE_STATUS_TWELVE.getCode());
        AgentGoods agentGoods = agentGoodsService.getById(purchaseInformationApplyDTO.getProductId());
        financeApply.setChargeMethod(agentGoods.getChargeMethod());
        financeApply.setLendingMethod(agentGoods.getLendingMethod());
        //保存到额度使用历史表
        Integer statusCode = QuotaUseDetailsEnum.WAITRE_DEDUCT_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = quotaUseDetailsService.getDetailsByFinanceApply(financeApply, statusCode);
        boolean first = false;
        if (ObjectUtil.isEmpty(financeApply.getId())) {
            quotaUseDetailsService.save(quotaUseDetails);
            financeApply.setQuotaUseDetailsId(quotaUseDetails.getId());
            first = true;
        } else {
            FinanceApply byId = financeApplyService.getById(financeApply.getId());
            QuotaUseDetails oldQuotaUseDetails = quotaUseDetailsService.getById(byId.getQuotaUseDetailsId());
            quotaUseDetails.setId(oldQuotaUseDetails.getId());
            quotaUseDetailsService.updateById(quotaUseDetails);
        }
        financeApplyService.saveOrUpdate(financeApply);
        purchaseInformationApplyDTO.setFinanceId(financeApply.getId());
        purchaseInformationService.saveOrUpdate(purchaseInformation);
        return purchaseInformationApplyDTO;
    }

    /**
     * 根号 融资编号,查询出代采信息，根据返回节点，来判断进入哪个页面
     *
     * @param financeNo 融资编号
     * @return 代采---基础信息
     */
    @Override
    public PurchaseInformationVO purchaseOneDetail(String financeNo) {
        //根据融资编号 查询 代采数据
        PurchaseInformation purchaseInformation = purchaseInformationService.getOne(Wrappers.<PurchaseInformation>lambdaQuery().eq(PurchaseInformation::getFinanceNo, financeNo));
        if (ObjectUtil.isEmpty(purchaseInformation)) {
            throw new ServiceException("查询不到数据");
        }
        FinanceApply financeApply = financeApplyService.getOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeNo));
        CustomerMaterial customerMaterial = customerMaterialService.getById(purchaseInformation.getCustomerMaterialId());
        List<PurchaseCommodity> purchaseCommodityList = purchaseCommodityService.list(Wrappers.<PurchaseCommodity>lambdaQuery().eq(PurchaseCommodity::getFinanceNo, financeNo));
        CustomerGoods customerGoods = customerGoodsMapper.selectById(financeApply.getCustomerGoodsId());
        //封装数据
        PurchaseInformationVO purchaseInformationVO = PurchaseInformationWrapper.build().entityVO(purchaseInformation);
        purchaseInformationVO.setCustomerMaterial(customerMaterial);
        purchaseInformationVO.setPurchaseCommodityList(purchaseCommodityList);
        purchaseInformationVO.setFinanceApply(financeApply);
        if (customerGoods != null) {
            purchaseInformationVO.setCapitalName(customerGoods.getCapitalName());
            purchaseInformationVO.setCapitalAvatar(customerGoods.getCapitalAvatar());
        }
        AgentGoodsVO goodsVO = agentGoodsService.processGoodsInfo(purchaseInformationVO.getFinanceApply().getGoodsId());
        if (goodsVO != null) {
            purchaseInformationVO.setBondReleaseMode(goodsVO.getBondReleaseMode());
        }
        return purchaseInformationVO;
    }

    @Override
    public CostCalculusVO repaymentFinanceApplyCalculation(CostCalculusDto costCalculusDto) {
        //构建融资申请还款参数
        //ExpenseRuleDTO expenseRuleDTO = buildPurchaseApply(costCalculusDto);
        costCalculusDto.setUserId(AuthUtil.getUserId());
        costCalculusDto.setEnterpriseType(UserUtils.getEnterpriseType());
        costCalculusDto.setGoodType(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
        costCalculusDto.setCurrentFeeNode(ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode());
        costCalculusDto.setExpenseOrderDetailPass(false);
        costCalculusDto.setExpenseOrderDetailPassIds("");
        List<Integer> chargePoint = Arrays.asList(ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode());
        costCalculusDto.setChargePoint(chargePoint.stream().map(Object::toString).collect(Collectors.joining(StringPool.COMMA)));
        return financeApplyBizService.costCalculus(costCalculusDto);
    }

    /**
     * 根据 客户产品id判断 采购商品总额是否超过该产品可用额度
     *
     * @param purchaseInformationDTO 代采信息
     * @return 额度信息
     */
    private EnterpriseQuota checkQuota(PurchaseInformationDTO purchaseInformationDTO, CustomerGoods customerGoods) {
        Assert.isTrue(Objects.nonNull(customerGoods) && customerGoods.getStatus().equals(CustomerGoodsEnum.FINANCING.getCode()), "该产品不在可融资状态，无法申请融资");
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
        BigDecimal applyAmount = purchaseInformationDTO.getPurchaseCommodityList().stream().map(PurchaseCommodity::getFinancingTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(applyAmount.compareTo(BigDecimal.ZERO) != 0, "融资金额不能为零");
        BigDecimal availableAmount = enterpriseQuota.getAvailableAmount();
        Assert.isTrue(applyAmount.compareTo(availableAmount) <= 0, "可申请额度不足");
        purchaseInformationDTO.getFinanceApply().setAmount(applyAmount);
        return enterpriseQuota;
    }

    /**
     * 代采申请提交
     *
     * @param purchaseInformationDTO 代采信息 dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(value = "financeApplyLock", param = "#purchaseInformationDTO.financeApply.customerGoodsId")
    public PurchaseInformationVO purchaseApplySubmitNew(PurchaseInformationDTO purchaseInformationDTO) {
        CustomerGoods customerGoods = customerGoodsMapper.selectById(purchaseInformationDTO.getFinanceApply().getCustomerGoodsId());
        // 检查额度是否正确
        EnterpriseQuota enterpriseQuota = checkQuota(purchaseInformationDTO, customerGoods);
        // 扣减额度
        enterpriseQuotaService.subtractReceivableAmount(purchaseInformationDTO.getFinanceApply().getAmount(), enterpriseQuota, PurchaseEnum.PURCHASE_PASS_TYPE_ONE.getCode());
        //生成融资编号
        String financeNo = CodeUtil.generateCode(CodeEnum.FINANCING_CODE);
        String financeCheck = null;

        // 判断 传入进来的值是否存在融资编号
        if (StrUtil.isNotBlank(purchaseInformationDTO.getFinanceNo())) {
            financeCheck = purchaseInformationDTO.getFinanceNo();
        } else {
            purchaseInformationDTO.setFinanceNo(financeNo);
            // 新增 采购商品
            //采购商品赋值
            for (PurchaseCommodity purchaseCommodity : purchaseInformationDTO.getPurchaseCommodityList()) {
                purchaseCommodity.setFinanceNo(purchaseInformationDTO.getFinanceNo());
            }
            purchaseCommodityService.saveBatch(purchaseInformationDTO.getPurchaseCommodityList());
        }
        //给代采数据赋值
        selectPurchaseCommodityFinanceApply(purchaseInformationDTO, enterpriseQuota);

        //判断是 1、自动放款 还是 2、手动放款
        if (GoodsEnum.AUTO_LENDING.getCode().equals(purchaseInformationDTO.getLendingMethod())) {
            //保存返回节点
            purchaseInformationDTO.setNode(PurchaseEnum.PURCHASE_NODE_TWO.getCode());
            //新增倒计时时间
            LocalDateTime countdownExpireTime = agentGoodsService.getByExpireTime(purchaseInformationDTO.getFinanceApply().getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_FINANCING_APPLY_SUBMIT.getCode());
            purchaseInformationDTO.getFinanceApply().setCountdownExpireTime(countdownExpireTime);
            // 1 资方统一收费（展示保证金） 2 平台资方单独收取（保证金和平台费用都要计算）
            if (GoodsEnum.ALONE.getCode().equals(purchaseInformationDTO.getFinanceApply().getChargeMethod())) {
                //保存保证金监管账户
                platformAccountService.savePlatformAccount(purchaseInformationDTO.getFinanceApply().getGoodsId(), purchaseInformationDTO.getFinanceApply().getFinanceNo(), AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());
                //保存平台账户
                platformAccountService.savePlatformAccount(purchaseInformationDTO.getFinanceApply().getGoodsId(), purchaseInformationDTO.getFinanceApply().getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
            } else {
                //保存保证金监管账户
                platformAccountService.savePlatformAccount(purchaseInformationDTO.getFinanceApply().getGoodsId(), purchaseInformationDTO.getFinanceApply().getFinanceNo(), AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());
            }
        } else {
//            // 保存平台费用
//            savePlatformExpenses(purchaseInformationDTO.getFinanceApply(), enterpriseQuota, PurchaseEnum.PURCHASE_GOODS__FEE_NODE_EIGHT.getCode(), PlatformExpensesEnum.PLAT_TYPE_PURCHASE.getCode());
            //保存保证金监管账户
            platformAccountService.savePlatformAccount(purchaseInformationDTO.getFinanceApply().getGoodsId(), purchaseInformationDTO.getFinanceApply().getFinanceNo(), AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());
            //保存平台账户
            platformAccountService.savePlatformAccount(purchaseInformationDTO.getFinanceApply().getGoodsId(), purchaseInformationDTO.getFinanceApply().getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
        }

        //保存资料 TODO 该接口需要更新
        Long customerMaterialId = customerMaterialService.saveCustomerMaterial(purchaseInformationDTO.getCustomerMaterial());
        purchaseInformationDTO.setCustomerMaterialId(customerMaterialId);
        FinanceApply financeApplyOne = null;
        if (StrUtil.isNotBlank(financeCheck)) {
            //更新额度使用历史状态
            financeApplyOne = financeApplyService.getOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeCheck));
            String quotaUseDetailsId = financeApplyOne.getQuotaUseDetailsId().toString();
            Integer code = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
            quotaUseDetailsService.changeStatus(Func.toLongList(quotaUseDetailsId), code);

            FinanceApply financeApply = purchaseInformationDTO.getFinanceApply();
            financeApply.setId(financeApplyOne.getId());
            financeApply.setBondPayProportion(enterpriseQuota.getBondProportion());
            financeApplyOne.setBondPayProportion(enterpriseQuota.getBondProportion());
            //更新 代采 -- 融资主数据
            UpdateWrapper<FinanceApply> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(FinanceApply::getFinanceNo, financeCheck);
            financeApplyService.update(financeApply, updateWrapper);

            //更新 代采---基础信息
            UpdateWrapper<PurchaseInformation> updateWrapperPurchase = new UpdateWrapper<>();
            updateWrapperPurchase.lambda().eq(PurchaseInformation::getFinanceNo, financeCheck);
            purchaseInformationService.update(purchaseInformationDTO, updateWrapperPurchase);
            purchaseRabbitApply(purchaseInformationDTO, enterpriseQuota);
        } else {
            //保存代采申请记录
            financeApplyOne = purchaseInformationDTO.getFinanceApply();
            financeApplyOne.setBondPayProportion(enterpriseQuota.getBondProportion());
            //新增 代采 -- 融资主数据
            financeApplyService.save(financeApplyOne);
            // 新增 代采---基础信息
            purchaseInformationService.save(purchaseInformationDTO);
            purchaseInformationDTO.setFinanceApply(financeApplyOne);
//			purchaseRabbitApply(purchaseInformationDTO, enterpriseQuota);
        }
        //保存当前的还款试算信息
        CostCalculusDto costCalculusDto = purchaseInformationDTO.getCostCalculusDto();
        costCalculusDto.setFinanceNo(financeApplyOne.getFinanceNo());
        //还款试算
        CostCalculusVO costCalculusVO = repaymentFinanceApplyCalculation(costCalculusDto);
        //取消未付款的费用订单 和未关联的费用详情
        productExpenseService.cancelUnPayExpenseOrder(costCalculusDto.getFinanceNo(), costCalculusDto.getType());
        //删除保证金
        expenseDepositService.removeDeposit(financeApplyOne.getFinanceNo());
        AgentGoods agentGoods = agentGoodsService.getById(financeApplyOne.getGoodsId());
        //判断产品是否需要缴纳保证金
        if (agentGoods.getIsPayBond().equals(DepositYNEnum.DEPOSIT_PAY_YES.getCode())){
            //生成保证金
            reGenExpenseDeposit(financeApplyOne);
        }
        List<ExpenseOrderDetail> expenseOrderDetailList = costCalculusVO.getExpenseOrderDetailList();
        //自动申请保存费用
        if (GoodsEnum.AUTO_LENDING.getCode().equals(purchaseInformationDTO.getLendingMethod())) {
            //创建费用订单详情
            if (CollUtil.isNotEmpty(expenseOrderDetailList)) {
                CreateExpenseOrderDetailDTO createExpenseOrderDetailDTO = new CreateExpenseOrderDetailDTO();
                createExpenseOrderDetailDTO.setExpenseOrderDetailList(expenseOrderDetailList);
                createExpenseOrderDetailDTO.setFinanceNo(costCalculusDto.getFinanceNo());
                createExpenseOrderDetailDTO.setType(costCalculusDto.getType());
                expenseOrderDetailList = productExpenseService.createExpenseOrderDetail(createExpenseOrderDetailDTO);
                //创建费用订单
                expenseInfoBizService.genExpenseOrderList(expenseOrderDetailList, financeApplyOne.getFinanceNo(), ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode(), AuthUtil.getUserId(), UserUtils.getEnterpriseType());
            }
        }
        financeApplyBizService.saveCalJson(costCalculusVO, PlatformExpensesEnum.PLAT_TYPE_PURCHASE.getCode(), financeApplyOne.getFinanceNo());
        //返回封装的数据
        PurchaseInformationVO purchaseInformationVO = nodePurchaseInformationVO(purchaseInformationDTO, customerGoods);
        return purchaseInformationVO;
    }

    private void reGenExpenseDeposit(FinanceApply financeApply) {
        AgentGoods agentGoods = agentGoodsService.getById(financeApply.getGoodsId());
        ExpenseDeposit cashDeposit = new ExpenseDeposit();
        cashDeposit.setFinancingNo(financeApply.getFinanceNo());
        cashDeposit.setCashDepositType(1);
        cashDeposit.setFinancingUserId(financeApply.getUserId());
        BigDecimal bondPayProportion = NumberUtil.div(financeApply.getBondPayProportion(), 100);
        BigDecimal payableAmount = NumberUtil.mul(financeApply.getAmount(), bondPayProportion).setScale(2, BigDecimal.ROUND_HALF_EVEN);
        cashDeposit.setPayableAmount(payableAmount);
        cashDeposit.setPayType(BillPayNameEnum.OFFLINE_PAY.getStatus());
        cashDeposit.setCashDepositRate(financeApply.getBondPayProportion());
        cashDeposit.setRefundType(agentGoods.getBondReleaseMode());
        cashDeposit.setPayNode(1);
        cashDeposit.setGoodsId(financeApply.getGoodsId());
        cashDeposit.setStatus(CashDepositTypeEnum.ONE_PAID.getCode());
        expenseDepositService.toPaidSave(cashDeposit);
    }

    /**
     * 代采--放款提交
     *
     * @param financeNo 融资编号
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> purchaseLastSubmitNew(String financeNo, List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList) {
        Map<String, Object> variables = new LinkedHashMap<>();
        //根据融资编号 查询 代采数据
        PurchaseInformationVO purchaseInformationVO = purchaseOneDetail(financeNo);
//        Map<String, List<Attach>> attachList = null;
        if (ObjectUtil.isEmpty(purchaseInformationVO)) {
            throw new ServiceException("融资编号有误");
        }
        FinanceApply financeApply = purchaseInformationVO.getFinanceApply();
//        if (MapUtil.isEmpty(map)) {
//            throw new ServiceException("请传入附件信息");
//        } else {
        // 资方统一收费只有保证金附件  平台资方单独收取有 保证金附件和平台 费用附件
        if (GoodsEnum.UNIFIED.getCode().equals(financeApply.getChargeMethod())) {
//                //判断是否存在附件信息
//                if (map.containsKey(PurchaseConstant.PURCHASE_ATTACH_BAIL)) {
//                    attachList = attachDecide(map, financeApply.getChargeMethod());
//                } else {
//                    throw new ServiceException("请上传保证金支付凭证");
//                }
            //1、自动放款则开启融资申请流程 2、手动放款则开启融资确认流程
            if (GoodsEnum.AUTO_LENDING.getCode().equals(purchaseInformationVO.getLendingMethod())) {
                //1、主数据-融资状态变更
//                    //2、创建保证金订单
//                    toPaidSave(map, financeApply);
                //3、启动 代采---融资申请流程
                Integer processType = PurchaseEnum.PURCHASE_AUTO_LOAN.getCode();
                // 设置流程变量
                variables = getVariables(purchaseInformationVO, processType);
                //变量新增附件信息
//                    variables.put(PurchaseConstant.PURCHASE_ATTACH_BAIL, attachList.get(PurchaseConstant.PURCHASE_ATTACH_BAIL));
                //更新返回节点
                purchaseInformationVO.setNode(PurchaseEnum.PURCHASE_NODE_LOAN.getCode());
                purchaseInformationService.updateById(purchaseInformationVO);
            } else {
                //TODO 目前没有 状态是手动放款 然后 收费方式是 资方统一收费 ，为了后续的扩展性 先预留 ：手动放款,收费方式是:资方统一收费，然后走融资确认流程
                //1、主数据-融资状态变更
                //2、启动代采确认流程
                Integer processType = PurchaseEnum.PURCHASE_LOAN_CONFIRM.getCode();
                // 设置流程变量
                variables = getVariables(purchaseInformationVO, processType);
                //变量新增附件信息
//                    variables.put(PurchaseConstant.PURCHASE_ATTACH_BAIL, attachList.get(PurchaseConstant.PURCHASE_ATTACH_BAIL));
                //3、保存放款申请
                ILoanApplyService loanApplyService = SpringUtil.getBean(ILoanApplyService.class);
                LoanApply loanApply = new LoanApply();
                loanApply.setFinanceNo(financeApply.getFinanceNo());
                loanApply.setFinanceApplyId(financeApply.getId());
                loanApply.setOperatorUser(MyAuthUtil.getUserId());
                loanApplyService.save(loanApply);
            }
        } else {
//                //判断是否存在附件信息
//                if (map.containsKey(PurchaseConstant.PURCHASE_ATTACH_BAIL) && map.containsKey(PurchaseConstant.PURCHASE_ATTACH_PLATFORM)) {
//                    attachList = attachDecide(map, financeApply.getChargeMethod());
//                } else {
//                    throw new ServiceException("请上传保证金支付凭证和平台费用支付凭证");
//                }
            //1、自动放款则开启融资申请流程 2、手动放款则开启融资确认流程
            if (GoodsEnum.AUTO_LENDING.getCode().equals(purchaseInformationVO.getLendingMethod())) {
                if (PurchaseEnum.PURCHASE_STATUS_ONE.getCode().equals(financeApply.getStatus())) {
                    throw new ServiceException("该状态不能提交");
                }
                //1、主数据-融资状态变更
                //2、创建费用订单
                List<ExpenseOrderDetail> platformExpensesList = expenseOrderDetailService.getByFinanceNo(financeApply.getFinanceNo());
                //费用单号
                String billExpenseNo = CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO);
                for (ExpenseOrderDetail platformExpenses : platformExpensesList) {
                    platformExpenses.setBillExpenseNo(billExpenseNo);
                }
                expenseOrderDetailService.updateBatchById(platformExpensesList);
//                    ExpenseOrder billExpenseOrderCheck = new ExpenseOrder();
//                    billExpenseOrderCheck.setCustomerId(financeApply.getUserId());
//                    billExpenseOrderCheck.setBillExpenseNo(billExpenseNo);
//                    //获取费用附件
//                    List<Long> attachPlatformList = map.get(PurchaseConstant.PURCHASE_ATTACH_PLATFORM);
//                    String attachPlatform = CollUtil.join(attachPlatformList, ",");
//                    billExpenseOrderCheck.setPayAttachId(attachPlatform);
//                    billExpenseOrderCheck.setFinanceApplyId(financeApply.getId());
//                    billExpenseOrderCheck.setFinanceNo(financeNo);
//                    billExpenseOrderCheck.setPaymentStatus(BillPayStatusEnum.BILL_PENDING.getCode());
//                    expenseOrderService.saveExpenseOrder(billExpenseOrderCheck, platformExpensesList);

//                    //3、创建保证金订单
//                    toPaidSave(map, financeApply);
                //4、启动 代采---融资申请流程
                Integer processType = PurchaseEnum.PURCHASE_FINANCE_APPLY.getCode();
                // 设置流程变量
                variables = getVariables(purchaseInformationVO, processType);
                //变量新增附件信息
//                    variables.put(PurchaseConstant.PURCHASE_ATTACH_BAIL, attachList.get(PurchaseConstant.PURCHASE_ATTACH_BAIL));
//                    variables.put(PurchaseConstant.PURCHASE_ATTACH_PLATFORM, attachList.get(PurchaseConstant.PURCHASE_ATTACH_PLATFORM));
                //purchasePurchase(financeApply.getGoodsId(), financeApply, variables, processType);
                //更新返回节点
                purchaseInformationVO.setNode(PurchaseEnum.PURCHASE_NODE_LOAN.getCode());
                purchaseInformationService.updateById(purchaseInformationVO);
            } else {
                //1、主数据-融资状态变更
                //TODO
                //2、启动代采确认流程
                Integer processType = PurchaseEnum.PURCHASE_LOAN_CONFIRM.getCode();
                // 设置流程变量
                variables = getVariables(purchaseInformationVO, processType);
                //变量新增附件信息
//                    variables.put(PurchaseConstant.PURCHASE_ATTACH_BAIL, attachList.get(PurchaseConstant.PURCHASE_ATTACH_BAIL));
//                    variables.put(PurchaseConstant.PURCHASE_ATTACH_PLATFORM, attachList.get(PurchaseConstant.PURCHASE_ATTACH_PLATFORM));
                //当前操作人修改
                variables.put(ProcessConstant.CUSTOMER_ID, MyAuthUtil.getPersonalUserId());
                //发起代采确认流程
                //String processInstanceId = businessProcessService.startProcess(financeApply.getGoodsId(), processType, variables);
                //更新返回节点
                purchaseInformationVO.setNode(PurchaseEnum.PURCHASE_NODE_LOAN.getCode());
                purchaseInformationService.updateById(purchaseInformationVO);
                //3、保存放款申请
                ILoanApplyService loanApplyService = SpringUtil.getBean(ILoanApplyService.class);
                LoanApply loanApply = new LoanApply();
                loanApply.setFinanceNo(financeApply.getFinanceNo());
                loanApply.setFinanceApplyId(financeApply.getId());
                loanApply.setOperatorUser(MyAuthUtil.getUserId());
                loanApplyService.save(loanApply);
                // 4、融资申请状态修改
                financeApplyService.updateById(financeApply);
            }
        }
//        }
        variables.put(ProcessConstant.FINANCE_APPLY_ID, financeApply.getId());
        //收费信息
        variables.put(org.springblade.common.constant.ProcessConstant.EXPENSE_INFO_LIST, expenseInfoExpenseVOList);
        variables.put(org.springblade.common.constant.ProcessConstant.USER_ID, AuthUtil.getUserId());
        variables.put(org.springblade.common.constant.ProcessConstant.ENTERPRISE_TYPE, UserUtils.getEnterpriseType());
        variables.put(org.springblade.common.constant.ProcessConstant.GOODS_ID, financeApply.getGoodsId());
        return variables;
    }

    /**
     * 填充融资申请 变量 vo
     *
     * @param purchaseInformationVO
     * @return
     */
    private Map<String, Object> getVariables(PurchaseInformationVO purchaseInformationVO, Integer processType) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ProcessConstant.FINANCE_APPLY_ID, purchaseInformationVO.getFinanceApply().getId());
        map.put(ProcessConstant.FINANCE_NO, purchaseInformationVO.getFinanceNo());
        map.put(ProcessConstant.CUSTOMER_GOODS_ID, purchaseInformationVO.getFinanceApply().getCustomerGoodsId());
        map.put(ProcessConstant.BUSINESS_ID, purchaseInformationVO.getFinanceApply().getGoodsId());
        map.put(ProcessConstant.CUSTOMER_ID, purchaseInformationVO.getFinanceApply().getApplyUser());
        map.put(ProcessConstant.USER_ID, purchaseInformationVO.getFinanceApply().getUserId());
        map.put(ProcessConstant.PROCESS_TYPE, processType);
        map.put(ProcessConstant.GOODS_TYPE, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
        map.put(ProcessConstant.CUSTOMER_MATERIAL, purchaseInformationVO.getCustomerMaterial());
        map.put(ProcessConstant.FINANCE_APPLY, purchaseInformationVO);
        map.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        map.put(PurchaseConstant.LENDING_METHOD, purchaseInformationVO.getLendingMethod());
        map.put(PurchaseConstant.CHARGE_METHOD, purchaseInformationVO.getFinanceApply().getChargeMethod());
        AgentGoodsVO goodsVO = agentGoodsService.processGoodsInfo(purchaseInformationVO.getFinanceApply().getGoodsId());
        purchaseInformationVO.getFinanceApply().setGoodsName(goodsVO.getGoodsName());
        map.put(ProcessConstant.PROCESS_GOODS_INFO, goodsVO);
        return map;
    }

    /**
     * 填充融资申请 变量
     *
     * @param purchaseInformationDTO
     * @return
     */
    private Map<String, Object> getVariables(PurchaseInformationDTO purchaseInformationDTO, Integer processType) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ProcessConstant.FINANCE_APPLY_ID, purchaseInformationDTO.getFinanceApply().getId());
        map.put(ProcessConstant.FINANCE_NO, purchaseInformationDTO.getFinanceNo());
        map.put(ProcessConstant.CUSTOMER_GOODS_ID, purchaseInformationDTO.getFinanceApply().getCustomerGoodsId());
        map.put(ProcessConstant.BUSINESS_ID, purchaseInformationDTO.getFinanceApply().getGoodsId());
        map.put(ProcessConstant.CUSTOMER_ID, purchaseInformationDTO.getFinanceApply().getApplyUser());
        map.put(ProcessConstant.USER_ID, purchaseInformationDTO.getFinanceApply().getUserId());
        map.put(ProcessConstant.PROCESS_TYPE, processType);
        map.put(ProcessConstant.CUSTOMER_MATERIAL, purchaseInformationDTO.getCustomerMaterial());
        map.put(ProcessConstant.FINANCE_APPLY, purchaseInformationDTO);
        map.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        map.put(PurchaseConstant.LENDING_METHOD, purchaseInformationDTO.getLendingMethod());
        map.put(PurchaseConstant.CHARGE_METHOD, purchaseInformationDTO.getFinanceApply().getChargeMethod());
        AgentGoodsVO goodsVO = agentGoodsService.processGoodsInfo(purchaseInformationDTO.getFinanceApply().getGoodsId());
        purchaseInformationDTO.getFinanceApply().setGoodsName(goodsVO.getGoodsName());
        map.put(ProcessConstant.PROCESS_GOODS_INFO, goodsVO);
        return map;
    }

    /**
     * 进入融资确认最后一个页面,或者返回到 融资确认页面
     *
     * @param financeNo 融资编号
     * @param node      节点 3、缴纳保证金 4、缴纳保证金及费用
     * @return 代采---基础信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseInformationVO purchaseLastNode(String financeNo, Integer node) {
        //根据融资编号 查询 代采数据
        PurchaseInformationVO purchaseInformationVO = purchaseOneDetail(financeNo);
        //节点改变
        purchaseInformationVO.setNode(node);
        purchaseInformationService.updateById(purchaseInformationVO);
        AgentGoodsVO goodsVO = agentGoodsService.processGoodsInfo(purchaseInformationVO.getFinanceApply().getGoodsId());
        purchaseInformationVO.setBondReleaseMode(goodsVO.getBondReleaseMode());
        return purchaseInformationVO;
    }

    @Override
    public List<ExpenseOrderDetail> expenseList(Long goodsId, Integer chargePoint, BigDecimal financeAmount, Integer loanDay) {
        CustomerGoods customerGoods = customerGoodsService.getByGoodsIdAndCustomerId(goodsId, MyAuthUtil.getUserId());
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
        CostCalculusDto costCalculusDto = new CostCalculusDto();
        costCalculusDto.setFinanceAmount(financeAmount);
        costCalculusDto.setLoanDay(loanDay);
        costCalculusDto.setGoodsId(goodsId);
        costCalculusDto.setUserId(MyAuthUtil.getUserId());
        costCalculusDto.setChargePoint(chargePoint.toString());
        ExpenseRuleDTO expenseRuleDTO = buildPurchaseApply(costCalculusDto, enterpriseQuota);
        ExpenseOrderDTO expenseOrderDTO = financeRepaymentService.getExpenseOrderDTO(costCalculusDto, expenseRuleDTO);
        List<ExpenseOrderDetail> expenseOrderDetails = expenseOrderService.selectExpenseOrderDetail(expenseOrderDTO, FinanceFeeStatusEnums.FeeStatusEnum.select.getCode());
        return expenseOrderDetails;
    }

    @Override
    public void savePurchaseRepaymentPlan(LoanManageIou loanManageIou, FinanceApply financeApply, DelegateExecution delegateExecution) {
        CostCalculusVO costCalculusVO = financeRepaymentService.costCalculusByFinanceNo(financeApply.getFinanceNo());

        List<StagRecordVO> stagRecords = costCalculusVO.getShowRepaymentPlan().getStagRecords();
        AgentGoodsVO goodsVO = JSONUtil.toBean(JSONUtil.parseObj(delegateExecution.getVariable(ProcessConstant.PROCESS_GOODS_INFO)), AgentGoodsVO.class);
        List<LoanManageRepaymentPlan> repaymentPlanList = stagRecords.stream()
                .filter(stagRecordVO -> Objects.nonNull(stagRecordVO.getRefundTime()))
                .filter(stagRecordVO -> StringUtil.isNotBlank(stagRecordVO.getTerm()))
                .map(stagRecord ->
                        LoanManageRepaymentPlan.builder()
                                .iouNo(loanManageIou.getIouNo())
                                .repaymentTime(stagRecord.getRefundTime())
                                .period(Integer.valueOf(stagRecord.getTerm()))
                                .userId(financeApply.getUserId())
                                .principal(stagRecord.getMonthlyPrincipal())
                                .financeApplyId(financeApply.getId())
                                .interest(stagRecord.getMonthlyInterest())
                                .customerGoodsId(financeApply.getCustomerGoodsId())
                                .prepaymentType(goodsVO.getPrepaymentType())
                                .repaymentType(financeApply.getRepaymentType())
                                .goodsId(financeApply.getGoodsId())
                                .iouId(loanManageIou.getId())
                                .goodsType(financeApply.getGoodsType())
                                .penaltyInterest(BigDecimal.ZERO)
                                .annualInterestRate(financeApply.getAnnualInterestRate())
                                .serviceFee(ObjectUtil.isEmpty(financeApply.getServiceRate()) ? BigDecimal.ZERO : financeApply.getServiceRate())
                                .build()).collect(Collectors.toList());

        loanManageRepaymentPlanService.saveBatch(repaymentPlanList);


        //保存还款计划费用表
        AgentGoods agentGoods = agentGoodsService.getById(financeApply.getGoodsId());
        if (GoodsEnum.UNIFIED.getCode().equals(agentGoods.getChargeMethod())) {
            //查询平台费用
            List<ExpenseOrderDetail> platformExpensesList = expenseOrderDetailService.list(Wrappers.<ExpenseOrderDetail>lambdaQuery()
                    .eq(ExpenseOrderDetail::getFinanceNo, financeApply.getFinanceNo())
                    .eq(ExpenseOrderDetail::getFeeNode, ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode()));
            //封装还款计划费用表
            if (CollUtil.isNotEmpty(platformExpensesList)) {
                platformExpensesList.forEach(platformExpenses -> {
                    repaymentPlanFeeService.savePlanFeeByPlatformExpenses(platformExpenses, repaymentPlanList);
                });

            }
        }
    }

    @Override
    public PurchaseBackApplyInfo selectPurchaseBackFinanceApplyInfo(String financeNo) {
        //获取代采---基础信息 服务类
        PurchaseInformationVO purchaseInformationVO = purchaseOneDetail(financeNo);
        // 还款试算
        RepaymentPlanCal repaymentPlanCal = financeRepaymentService.repaymentCalculationFinanceNo(financeNo);
        // 查询融资企业
        User user = UserUtils.getUserById(purchaseInformationVO.getFinanceApply().getUserId());
        // 查询代采产品信息
        AgentGoodsVO goodsVO = agentGoodsService.processGoodsInfo(purchaseInformationVO.getFinanceApply().getGoodsId());

        // 平台费用
        List<ExpenseOrderDetail> platformExpenses = expenseOrderDetailService.getByFinanceNo(financeNo);
        return PurchaseBackApplyInfo.builder()
                .purchaseInformationVO(purchaseInformationVO)
                .repaymentCalculation(repaymentPlanCal)
                .capitalAvatar(purchaseInformationVO.getCapitalAvatar())
                .userName(user.getName())
                .platformExpenses(platformExpenses)
                .build();
    }

    @Override
    public CostCalculusVO getRepaymentPlanAndFeeByFinanceNoAndType(String financeNo, Integer type) {
        return financeApplyBizService.costCalculusByFinanceNo(financeNo, type);
    }

    private void purchaseRabbitApply(PurchaseInformationDTO purchaseInformationDTO, EnterpriseQuota enterpriseQuota) {
        if (GoodsEnum.AUTO_LENDING.getCode().equals(purchaseInformationDTO.getLendingMethod())) {
            Map<String, Object> map = new HashMap<>();
            map.put("financeNo", purchaseInformationDTO.getFinanceApply().getFinanceNo());
            map.put("enterpriseQuota", enterpriseQuota);
            map.put("amount", purchaseInformationDTO.getFinanceApply().getAmount());
            map.put("updateTime", purchaseInformationDTO.getFinanceApply().getUpdateTime());
            String message = JSONUtil.toJsonStr(map);
            // 发送mq延时消息，超时未申请放款关闭该申请
            Integer seconds = goodsTimingService.getSecondsByGoodsIdAndType(purchaseInformationDTO.getFinanceApply().getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PLEDGE_FINANCING_APPLY_LOAN.getCode());
            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
                    .messageType(PurchaseMessageTypeEnum.FINANCE_APPLY_UN_SUBMIT.getCode())
                    .msg(message)
                    .seconds(seconds)
                    .extendParam(new HashMap<>())
                    .status(RabbitMqStatusEnum.PLEDGE_REDEEMCARGO_ACCEPTANCE.getCode())
                    .build());
        }
    }

    private PurchaseInformationVO nodePurchaseInformationVO(PurchaseInformationDTO purchaseInformationDTO, CustomerGoods customerGoods) {
        PurchaseInformationVO purchaseInformationVO = new PurchaseInformationVO();
        // 代采基础信息
        purchaseInformationVO.setFinanceNo(purchaseInformationDTO.getFinanceNo());
        return purchaseInformationVO;
    }


    /**
     * 采购信息赋值
     *
     * @param purchaseInformationDTO 采购 dto
     * @param enterpriseQuota        额度信息
     */
    private void selectPurchaseCommodityFinanceApply(PurchaseInformationDTO purchaseInformationDTO, EnterpriseQuota enterpriseQuota) {
        purchaseInformationDTO.setDay(Integer.parseInt(DictBizCache.getValue(DictBizEnum.TASK_DAYS.getCode(), CommonConstant.TASK_BILL_DAY)));
        //代采主数据赋值
        purchaseInformationDTO.getFinanceApply().setApplyUser(MyAuthUtil.getPersonalUserId());
        purchaseInformationDTO.getFinanceApply().setUserId(MyAuthUtil.getUserId());
        purchaseInformationDTO.getFinanceApply().setFinanceNo(purchaseInformationDTO.getFinanceNo());
        //日利率
        purchaseInformationDTO.getFinanceApply().setDailyInterestRate(enterpriseQuota.getDailyInterestRate());
        //年利率
        purchaseInformationDTO.getFinanceApply().setAnnualInterestRate(enterpriseQuota.getAnnualInterestRate());
        //服务利率
        purchaseInformationDTO.getFinanceApply().setServiceRate(enterpriseQuota.getServiceRate());
        //资方id
        purchaseInformationDTO.getFinanceApply().setCapitalId(enterpriseQuota.getCapitalId());
        //产品id
        purchaseInformationDTO.getFinanceApply().setGoodsId(enterpriseQuota.getGoodsId());
        //放款方式
        purchaseInformationDTO.getFinanceApply().setLendingMethod(purchaseInformationDTO.getLendingMethod());
        //资料表赋值 融资编号
        purchaseInformationDTO.getCustomerMaterial().setFinanceNo(purchaseInformationDTO.getFinanceNo());
    }

    private ExpenseRuleDTO buildPurchaseApply(CostCalculusDto costCalculusDto, EnterpriseQuota enterpriseQuota) {
        ExpenseRuleDTO expenseRuleDTO = new ExpenseRuleDTO();
        expenseRuleDTO.setPrincipalRepayment(costCalculusDto.getFinanceAmount());//应还本金
        expenseRuleDTO.setLoanPrincipal(costCalculusDto.getFinanceAmount());//借款本金
        expenseRuleDTO.setLoanDays(BigDecimal.valueOf(costCalculusDto.getLoanDay()));//借款天数
        expenseRuleDTO.setLoanPeriods(BigDecimal.valueOf(1));//借款期数
        expenseRuleDTO.setInterestAccrualDays(BigDecimal.valueOf(costCalculusDto.getLoanDay()));//拟计息天数--->还款试算阶段 该参数应是未来还款的天数
        expenseRuleDTO.setServiceRate(enterpriseQuota.getServiceRate().divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
        expenseRuleDTO.setDayRate(enterpriseQuota.getDailyInterestRate().divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
        expenseRuleDTO.setYearRate(enterpriseQuota.getAnnualInterestRate().divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
        expenseRuleDTO.setMarginRatio(enterpriseQuota.getBondProportion().divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
        return expenseRuleDTO;
    }

    private ExpenseRuleDTO buildPurchaseApply(CostCalculusDto costCalculusDto, Long userId) {
        CustomerGoods customerGoods = customerGoodsService.getByGoodsIdAndCustomerId(costCalculusDto.getGoodsId(), userId);
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
        return buildPurchaseApply(costCalculusDto, enterpriseQuota);
    }

    private ExpenseRuleDTO buildPurchaseApply(CostCalculusDto costCalculusDto) {
        return buildPurchaseApply(costCalculusDto, MyAuthUtil.getUserId());
    }
}
