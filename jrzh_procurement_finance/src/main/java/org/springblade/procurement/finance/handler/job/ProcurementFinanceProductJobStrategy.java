package org.springblade.procurement.finance.handler.job;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.PurchaseStateEnum;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.handler.IProductFinanceJobStrategy;
import org.springblade.finance.service.IFinanceApplyService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-18  14:31
 * @Description: TODO
 * @Version: 1.0
 */
@Service("PURCHASE_FINANCE_PRODUCT_JOB_STRATEGY")
@RequiredArgsConstructor
public class ProcurementFinanceProductJobStrategy implements IProductFinanceJobStrategy {
    private final IFinanceApplyService financeApplyService;

    @Override
    public void overdueFinanceDeal(FinanceApply financeApply, LocalDate repaymentTime) {
        financeApplyService.changeStatus(Collections.singletonList(financeApply.getId()), PurchaseStateEnum.PURCHASE_STATUS_TEN.getCode());
    }

    @Override
    public GoodsEnum support() {
        return GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE;
    }
}
