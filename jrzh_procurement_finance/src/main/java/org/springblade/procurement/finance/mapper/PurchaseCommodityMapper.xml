<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.procurement.finance.mapper.PurchaseCommodityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="purchaseCommodityResultMap" type="org.springblade.procurement.finance.entity.PurchaseCommodity">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="purchase_code" property="purchaseCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="description" property="description"/>
        <result column="name" property="name"/>
        <result column="commodity_id" property="commodityId"/>
        <result column="unit_name" property="unitName"/>
        <result column="spec" property="spec"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="quantity" property="quantity"/>
        <result column="purchase_price" property="purchasePrice"/>
        <result column="financing_price" property="financingPrice"/>
        <result column="purchase_total" property="purchaseTotal"/>
        <result column="financing_total" property="financingTotal"/>
        <result column="commodity_url" property="commodityUrl"/>
    </resultMap>


    <select id="selectPurchaseCommodityPage" resultMap="purchaseCommodityResultMap">
        select *
        from jrzh_purchase_commodity
        where is_deleted = 0
    </select>

</mapper>
