
package org.springblade.procurement.finance.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.service.IPurchaseCommodityService;
import org.springblade.procurement.finance.vo.PurchaseCommodityVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 代采--采购商品 控制器
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_PURCHASE + CommonConstant.WEB_FRONT + "/purchase/purchaseCommodity")
@Api(value = "代采--采购商品", tags = "代采--采购商品接口")
public class PurchaseCommodityFrontController extends BladeController {

    private final IPurchaseCommodityService purchaseCommodityService;

//	/**
//	 * 详情
//	 */
////	@GetMapping("/detail")
////	@ApiOperationSupport(order = 1)
////	@ApiOperation(value = "详情", notes = "传入purchaseCommodity")
//////    @PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:detail') or hasRole('administrator')")
////	public R<PurchaseCommodityVO> detail(PurchaseCommodity purchaseCommodity) {
////		PurchaseCommodity detail = purchaseCommodityService.getOne(Condition.getQueryWrapper(purchaseCommodity));
////		return R.data(PurchaseCommodityWrapper.build().entityVO(detail));
////	}
////
////	/**
////	 * 分页 代采--采购商品
////	 */
////	@GetMapping("/list")
////	@ApiOperationSupport(order = 2)
////	@ApiOperation(value = "分页", notes = "传入purchaseCommodity")
////	@PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:list') or hasRole('administrator')")
////	public R<IPage<PurchaseCommodityVO>> list(PurchaseCommodity purchaseCommodity, Query query) {
////		IPage<PurchaseCommodity> pages = purchaseCommodityService.page(Condition.getPage(query), Condition.getQueryWrapper(purchaseCommodity));
////		return R.data(PurchaseCommodityWrapper.build().pageVO(pages));
////	}


    /**
     * 自定义分页 代采--采购商品
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入purchaseCommodity")
    @PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:page') or hasRole('administrator')")
    public R<IPage<PurchaseCommodityVO>> page(PurchaseCommodityVO purchaseCommodity, Query query) {
        String tenantId = purchaseCommodity.getTenantId();
        IPage<PurchaseCommodityVO> pages = purchaseCommodityService.selectPurchaseCommodityPage(Condition.getPage(query), purchaseCommodity);
        return R.data(pages);
    }

    /**
     * 新增 代采--采购商品
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入purchaseCommodity")
    @PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody PurchaseCommodity purchaseCommodity) {
        return R.status(purchaseCommodityService.save(purchaseCommodity));
    }

    /**
     * 修改 代采--采购商品
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入purchaseCommodity")
    @PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody PurchaseCommodity purchaseCommodity) {
        return R.status(purchaseCommodityService.updateById(purchaseCommodity));
    }

    /**
     * 新增或修改 代采--采购商品
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入purchaseCommodity")
    @PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody PurchaseCommodity purchaseCommodity) {
        return R.status(purchaseCommodityService.saveOrUpdate(purchaseCommodity));
    }


    /**
     * 删除 代采--采购商品
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(purchaseCommodityService.deleteLogic(Func.toLongList(ids)));
    }


}
