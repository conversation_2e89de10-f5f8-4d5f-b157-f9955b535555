
package org.springblade.procurement.finance.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.procurement.finance.service.IPurchaseFinanceApplyService;
import org.springblade.procurement.finance.vo.PurchaseBackApplyInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 代采---基础信息 控制器
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_BUSINESS + CommonConstant.WEB_BACK + "/finance-apply")
@Api(value = "代采---基础信息", tags = "代采---基础信息接口")
public class PurchaseInformationController extends BladeController {

    private final IPurchaseFinanceApplyService purchaseFinanceApplyService;

    @GetMapping("/purchase-applyInfo")
    @ApiOperation("代采--申请信息")
    public R<PurchaseBackApplyInfo> applyInfo(@RequestParam String financeNo) {
        return R.data(purchaseFinanceApplyService.selectPurchaseBackFinanceApplyInfo(financeNo));
    }
}
