package org.springblade.procurement.finance.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;

import java.util.Objects;

/**
 * 代采---基础信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
public class PurchaseInformationWrapper extends BaseEntityWrapper<PurchaseInformation, PurchaseInformationVO> {

    public static PurchaseInformationWrapper build() {
        return new PurchaseInformationWrapper();
    }

    @Override
    public PurchaseInformationVO entityVO(PurchaseInformation PurchaseInformation) {
        PurchaseInformationVO PurchaseInformationVO = Objects.requireNonNull(BeanUtil.copy(PurchaseInformation, PurchaseInformationVO.class));

        //User createUser = UserCache.getUser(PurchaseInformation.getCreateUser());
        //User updateUser = UserCache.getUser(PurchaseInformation.getUpdateUser());
        //PurchaseInformationVO.setCreateUserName(createUser.getName());
        //PurchaseInformationVO.setUpdateUserName(updateUser.getName());

        return PurchaseInformationVO;
    }
}

