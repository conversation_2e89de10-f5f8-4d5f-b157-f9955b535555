/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.finance.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.process.dto.BusinessProcessProcessDTO;
import org.springblade.process.listener.FrontTaskListener;
import org.springblade.process.vo.BusinessProcessTaskDataVO;
import org.springblade.procurement.finance.constant.PurchaseConstant;
import org.springblade.procurement.finance.dto.PurchaseInformationDTO;
import org.springblade.procurement.finance.service.IPurchaseFinanceApplyService;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;
import org.springblade.procurement.service.IAgentGoodsService;
import org.springblade.procurement.vo.AgentGoodsVO;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 手动融资申请、放款申请监听
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Component("purchaseFrontTaskListener")
@RequiredArgsConstructor
public class PurchaseFrontTaskListener implements FrontTaskListener {
    private final IPurchaseFinanceApplyService purchaseFinanceApplyService;
    private final IAgentGoodsService agentGoodsService;

    /**
     * 确认代采单
     */
    private final static String GENERATE_APPLY = "GeneratApply";
    /**
     * 融资确认
     */
    private final static String CONFIRM = "GeneratHandLoan@R1";
    /**
     * 缴纳保证金及费用
     */
    private final static String FEE_NO_TWO = "GeneratHandLoan@R2";


    @Override
    public void complete(BusinessProcessProcessDTO businessProcessProgress, BusinessProcessTaskDataVO taskDataVO) {
        String nodeKey = taskDataVO.getNodeKey();
        if (businessProcessProgress.isPass()) {
            if (GENERATE_APPLY.equals(nodeKey)) {
                doGenerateApply(businessProcessProgress, taskDataVO);
            }
            if (CONFIRM.equals(nodeKey)) {
                doConfirm(businessProcessProgress, taskDataVO);
            }
            if (FEE_NO_TWO.equals(nodeKey)) {
                doFeeNoTwo(businessProcessProgress, taskDataVO);
            }
        }

    }

    private void doFeeNoTwo(BusinessProcessProcessDTO businessProcessProgress, BusinessProcessTaskDataVO taskDataVO) {
        Map<String, Object> taskDataVOVariable = taskDataVO.getVariable();
        JSONObject req = JSONUtil.parseObj(taskDataVOVariable);
        String financeNo = (String) req.get(ProcessConstant.FINANCE_NO);
        String expenseInfoListStr = req.getStr(org.springblade.common.constant.ProcessConstant.EXPENSE_INFO_LIST);
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList = StringUtil.isNotBlank(expenseInfoListStr) ?
                JSONUtil.toList(expenseInfoListStr, ExpenseInfoExpenseVO.class) : Collections.emptyList();
//        JSONObject data = req.getJSONObject("data");
//        Map<String, List<Long>> map = new LinkedHashMap<>();
//        if (ObjectUtil.isNotEmpty(data)) {
//            JSONArray bail = data.getJSONArray("bail");
//            JSONArray platform = data.getJSONArray("platform");
//            if (CollUtil.isNotEmpty(bail)) {
//                map.put("bail", JSONUtil.toList(bail, Long.class));
//            }
//            if (CollUtil.isNotEmpty(platform)) {
//                map.put("platform", JSONUtil.toList(platform, Long.class));
//            }
//        }
        Map<String, Object> variable = purchaseFinanceApplyService.purchaseLastSubmitNew(financeNo, expenseInfoExpenseVOList);
        businessProcessProgress.appendVariable(variable);
    }

    /**
     * 执行代采事件
     *
     * @param businessProcessProgress
     * @param taskDataVO
     */
    private void doConfirm(BusinessProcessProcessDTO businessProcessProgress, BusinessProcessTaskDataVO taskDataVO) {
        Map<String, Object> taskDataVOVariable = taskDataVO.getVariable();
        String financeNo = (String) taskDataVOVariable.get(ProcessConstant.FINANCE_NO);
        Integer node = (Integer) taskDataVOVariable.get("node");
        PurchaseInformationVO purchaseInformationVO = purchaseFinanceApplyService.purchaseLastNode(financeNo, node);
        taskDataVO.setVariable(JSONUtil.parseObj(purchaseInformationVO));
    }

    @Override
    public void created(BusinessProcessProcessDTO businessProcessProgress, BusinessProcessTaskDataVO taskDataVO) {

    }

    /**
     * 执行代采事件
     *
     * @param businessProcessProgress
     * @param taskDataVO
     */
    private void doGenerateApply(BusinessProcessProcessDTO businessProcessProgress, BusinessProcessTaskDataVO taskDataVO) {
        Map<String, Object> taskDataVOVariable = taskDataVO.getVariable();
        PurchaseInformationDTO purchaseInformationDTO = JSONUtil.toBean(JSONUtil.toJsonStr(taskDataVOVariable), PurchaseInformationDTO.class);
        //执行代采业务逻辑
        purchaseFinanceApplyService.purchaseApplySubmitNew(purchaseInformationDTO);

        //若手动 放入流程变量中
        if (GoodsEnum.MANUAL_LENDING.getCode().equals(purchaseInformationDTO.getLendingMethod())) {
            // 设置流程变量
            Integer processType = PurchaseEnum.PURCHASE_FINANCE_APPLY.getCode();
            Map<String, Object> variables = getVariables(purchaseInformationDTO, processType);
            businessProcessProgress.appendVariable(variables);
            businessProcessProgress.appendVariable(PurchaseConstant.PURCHASE_INFORMATION_ID, purchaseInformationDTO.getId());
        }
    }

    /**
     * 填充融资申请 变量
     *
     * @param purchaseInformationDTO
     * @return
     */
    private Map<String, Object> getVariables(PurchaseInformationDTO purchaseInformationDTO, Integer processType) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(org.springblade.common.constant.ProcessConstant.PLATFORM_EXPENSES_TYPE, PlatformExpensesEnum.PLAT_TYPE_PURCHASE.getCode());
        map.put(ProcessConstant.FINANCE_APPLY_ID, purchaseInformationDTO.getFinanceApply().getId());
        map.put(ProcessConstant.FINANCE_NO, purchaseInformationDTO.getFinanceNo());
        map.put(ProcessConstant.CUSTOMER_GOODS_ID, purchaseInformationDTO.getFinanceApply().getCustomerGoodsId());
        map.put(ProcessConstant.BUSINESS_ID, purchaseInformationDTO.getFinanceApply().getId());
        map.put(ProcessConstant.CUSTOMER_ID, purchaseInformationDTO.getFinanceApply().getApplyUser());
        map.put(ProcessConstant.USER_ID, purchaseInformationDTO.getFinanceApply().getUserId());
        map.put(ProcessConstant.PROCESS_TYPE, processType);
        map.put(ProcessConstant.CUSTOMER_MATERIAL, purchaseInformationDTO.getCustomerMaterial());
        map.put(ProcessConstant.FINANCE_APPLY, purchaseInformationDTO);
        map.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        map.put(PurchaseConstant.LENDING_METHOD, purchaseInformationDTO.getLendingMethod());
        map.put(PurchaseConstant.CHARGE_METHOD, purchaseInformationDTO.getFinanceApply().getChargeMethod());
        AgentGoodsVO goodsVO = agentGoodsService.processGoodsInfo(purchaseInformationDTO.getFinanceApply().getGoodsId());
        purchaseInformationDTO.getFinanceApply().setGoodsName(goodsVO.getGoodsName());
        map.put(ProcessConstant.PROCESS_GOODS_INFO, goodsVO);
        map.put(ProcessConstant.GOODS_TYPE, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
        map.put(org.springblade.common.constant.ProcessConstant.EXPENSE_FEE_NODE, ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode());
        return map;
    }
}
