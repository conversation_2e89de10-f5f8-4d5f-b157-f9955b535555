package org.springblade.procurement.finance.rabbitmq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.BillPayStatusEnum;
import org.springblade.common.enums.QuotaUseDetailsEnum;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.service.IQuotaUseDetailsService;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.handler.MessageHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * 融资申请超时未提交流程，进行关闭操作
 *
 * <AUTHOR>
 */
@Component("PURCHASE_FINANCE_APPLY_UN_LOAN")
@RequiredArgsConstructor
@TenantIgnore
public class PurchaseFinanceApplyUnLoanMessageHandler implements MessageHandler {

    private final IFinanceApplyService financeApplyService;
    private final IQuotaUseDetailsService quotaUseDetailsService;


    /**
     * 已开通产品额度
     */
    private final IEnterpriseQuotaService enterpriseQuotaService;
    /**
     * 费用订单
     */
    private final IExpenseOrderService billExpenseOrderService;

    /**
     * 保证金 服务类
     */
    private final IExpenseDepositService cashDepositService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handler(DelayMessage delayMessage) {
        String msg = delayMessage.getMsg();
        agentGoodsHandler(msg);
    }

    void agentGoodsHandler(String msg) {
        //代采申请待确认
        HashMap<String, Object> map = JSONUtil.toBean(msg, HashMap.class);
        Long financeApplyId = (Long) map.get("financeApplyId");
        EnterpriseQuota enterpriseQuota = JSONUtil.toBean(map.get("enterpriseQuota").toString(), EnterpriseQuota.class);
        BigDecimal amount = new BigDecimal(map.get("amount").toString());
        //费用单号
        String billExpenseNo = (String) map.get("billExpenseNo");
        List<String> billExpenseNoStr = Func.toStrList(billExpenseNo);

        String financeNo = (String) map.get("financeNo");

        boolean update = financeApplyService.lambdaUpdate()
                .set(FinanceApply::getStatus, PurchaseEnum.PURCHASE_STATUS_THREE.getCode())
                .eq(FinanceApply::getId, financeApplyId)
                .eq(FinanceApply::getStatus, PurchaseEnum.PURCHASE_STATUS_FOUR.getCode())
                .update();
        if (update) {
            // 额度返回
            enterpriseQuotaService.subtractReceivableAmount(amount, enterpriseQuota, PurchaseEnum.PURCHASE_PASS_TYPE_TWO.getCode());
            if (CollUtil.isNotEmpty(billExpenseNoStr)) {
                //修改 费用订单
                billExpenseOrderService.update(Wrappers.<ExpenseOrder>lambdaUpdate()
                        .in(ExpenseOrder::getBillExpenseNo, billExpenseNoStr)
                        .set(ExpenseOrder::getPaymentStatus, BillPayStatusEnum.BILL_CLOSED.getCode()));
            }
            //修改保证金
            cashDepositService.closureUpdate(financeNo);

            //设置额度历史使用状态失败
            FinanceApply financeApply = financeApplyService.getById(financeApplyId);
            Long quotaUseDetailsId = financeApply.getQuotaUseDetailsId();
            Integer statusCode = QuotaUseDetailsEnum.APPLY_FAIL.getCode();
            quotaUseDetailsService.updateStatus(quotaUseDetailsId, statusCode);
        }
    }

}
