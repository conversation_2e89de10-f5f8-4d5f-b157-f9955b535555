package org.springblade.procurement.finance.handler;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.utils.GoodsTiminUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.customer.entity.QuotaUseDetails;
import org.springblade.expense.dto.ExpenseOrderDTO;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.dto.financeApplyHandler.FinanceApplyHandlerDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.enums.financeApply.FinanceApplyEnums;
import org.springblade.finance.external.dto.EnterpriseQuotaSerchDTO;
import org.springblade.finance.external.handler.businessProcess.FinanceBusinessProcessService;
import org.springblade.finance.external.handler.customerMaterial.FinanceCustomerMaterialService;
import org.springblade.finance.external.handler.enterpriseQuota.FinanceEnterpriseService;
import org.springblade.finance.external.handler.platformAccount.FinancePlatformAccountService;
import org.springblade.finance.external.handler.quotaUseDetails.FinanceQuotaUseDetailService;
import org.springblade.finance.handler.impl.AbstractFinanceHandler;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.procurement.finance.constant.PurchaseConstant;
import org.springblade.procurement.finance.dto.AgentFinanceDTO;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.service.IPurchaseCommodityService;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springblade.product.common.constant.AccountTypeEnum;
import org.springblade.product.common.constant.GoodsTimingEnum;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.GoodsTiming;
import org.springblade.product.common.entity.Product;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.resource.cache.DictBizCache;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 代采---融资实现类
 *
 * <AUTHOR>
 * @date 2023-04-19
 */
@Service(FinanceApplyEnums.FinanceApplyType.FINANCE_APPLY_AGENT)
@RequiredArgsConstructor
public class AgentFinanceService extends AbstractFinanceHandler {

    /**
     * 融资申请服务类接口
     */
    private final IFinanceApplyService financeApplyService;

    /**
     * 融资-额度接口
     */
    private final FinanceEnterpriseService financeEnterpriseService;

    /**
     * 代采--采购商品 接口
     */
    private final IPurchaseCommodityService purchaseCommodityService;

    /**
     * 代采---基础信息 接口
     */
    private final IPurchaseInformationService purchaseInformationService;

    /**
     * 费用订单 接口
     */
    private final IExpenseOrderService expenseOrderService;

    /**
     * 客户资料 接口
     */
    private final FinanceCustomerMaterialService financeCustomerMaterialService;

    /**
     * 流程发起
     */
    private final FinanceBusinessProcessService financeBusinessProcessService;
    private final IBusinessProcessProductService businessProcessProductService;

    /**
     * 产品配置 接口
     */
    private final ProductDirector productDirector;

    /**
     * 额度使用明细 接口
     */
    private final FinanceQuotaUseDetailService financeQuotaUseDetailService;

    /**
     * 平台账户 接口
     */
    private final FinancePlatformAccountService financePlatformAccountService;


    /**
     * 保存融资申请
     */
    @Override
    public void saveFinanceApply(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //获取代采数据
        AgentFinanceDTO agentFinanceDTO = (AgentFinanceDTO) financeApplyHandlerDTO.getAgentFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setStatus(PurchaseEnum.PURCHASE_STATUS_TWELVE.getCode());

        // 1、代采业务处理
        getDetails(enterpriseQuotaSearchDTO, product, agentFinanceDTO, financeApply);
        //新增倒计时时间
        LocalDateTime countdownExpireTime = getByExpireTime(financeApply.getGoodsId(),
                GoodsTimingEnum.GOODS_TIMING_PURCHASE_FINANCING_APPLY_SUBMIT.getCode());
        financeApply.setCountdownExpireTime(countdownExpireTime);

        // 1 资方统一收费（展示保证金） 2 平台资方单独收取（保证金和平台费用都要计算）
        if (GoodsEnum.UNIFIED.getCode().equals(financeApply.getChargeMethod())) {
            //TODO 需要判断资方账户为线上还是线下
        } else {
            //保存保证金监管账户
            financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
        }

        //保存保证金监管账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());
        //融资数据新增
        financeSave(agentFinanceDTO, financeApply);
    }

    /**
     * 保存融资申请 ---修改
     *
     * @param financeApplyHandlerDTO   融资数据
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     */
    @Override
    public void saveFinanceApplyUpdate(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //获取代采数据
        AgentFinanceDTO agentFinanceDTO = (AgentFinanceDTO) financeApplyHandlerDTO.getAgentFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setStatus(PurchaseEnum.PURCHASE_STATUS_TWELVE.getCode());

        //1、代采业务处理
        businessProcessing(enterpriseQuotaSearchDTO, product, agentFinanceDTO, financeApply);
        // 5、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        quotaUseDetails.setId(financeApply.getQuotaUseDetailsId());
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);

        //新增倒计时时间
        LocalDateTime countdownExpireTime = getByExpireTime(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_FINANCING_APPLY_SUBMIT.getCode());
        financeApply.setCountdownExpireTime(countdownExpireTime);

        //6、修改融资数据
        financeUpdate(agentFinanceDTO, financeApply);
    }

    /**
     * 提交融资申请-新增
     *
     * @param financeApplyHandlerDTO   融资申请数据
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     */
    @Override
    public void submitFinanceApplySave(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //获取代采数据
        AgentFinanceDTO agentFinanceDTO = (AgentFinanceDTO) financeApplyHandlerDTO.getAgentFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setStatus(PurchaseEnum.PURCHASE_STATUS_ONE.getCode());
        financeApply.setId(IdWorker.getId());
        // 1、新增 采购商品 采购商品赋值
        List<ExpenseOrderDetail> expenseOrderDetailList = getDetails(enterpriseQuotaSearchDTO, product, agentFinanceDTO, financeApply);

        // 6、发起融资申请，开启流程
        Integer processType = PurchaseEnum.PURCHASE_FINANCE_APPLY.getCode();
        // 设置流程变量
        Map<String, Object> variables = getVariables(agentFinanceDTO, financeApply, processType, expenseOrderDetailList, product);
        String processInstanceId = startProcess(financeApply.getGoodsId(), processType, variables);
        financeApply.setProcessInstanceId(processInstanceId);

        //保存平台账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
        //保存保证金监管账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());
        //融资数据新增
        financeSave(agentFinanceDTO, financeApply);
    }

    /**
     * 提交融资申请-修改
     *
     * @param financeApplyHandlerDTO   融资数据
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     */
    @Override
    public void submitFinanceApplyUpdate(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //获取代采数据
        AgentFinanceDTO agentFinanceDTO = (AgentFinanceDTO) financeApplyHandlerDTO.getAgentFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setStatus(PurchaseEnum.PURCHASE_STATUS_ONE.getCode());
        //1、代采业务处理
        List<ExpenseOrderDetail> expenseOrderDetailList = businessProcessing(enterpriseQuotaSearchDTO, product, agentFinanceDTO, financeApply);
        // 5、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        quotaUseDetails.setId(financeApply.getQuotaUseDetailsId());
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);
        Integer processType = PurchaseEnum.PURCHASE_FINANCE_APPLY.getCode();
        // 设置流程变量
        Map<String, Object> variables = getVariables(agentFinanceDTO, financeApply, processType, expenseOrderDetailList, product);
        //流程任务完成
        businessProcessProductService.startOrSubmit(financeApply.getGoodsId(), processType, financeApply.getProcessInstanceId(), variables, ProcessProgressEnum.FINANCING_REVIEW);

        //保存保证金监管账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
        //保存保证金监管账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());


        //修改融资数据
        financeUpdate(agentFinanceDTO, financeApply);
    }

    private List<ExpenseOrderDetail> getDetails(EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product, AgentFinanceDTO agentFinanceDTO, FinanceApply financeApply) {
        //代采商品处理
        purchaseCommoditySave(agentFinanceDTO, financeApply);
        //业务处理
        List<ExpenseOrderDetail> expenseOrderDetailList = businessProcessing(enterpriseQuotaSearchDTO, product, agentFinanceDTO, financeApply);
        // 5、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);
        financeApply.setQuotaUseDetailsId(quotaUseDetails.getId());
        return expenseOrderDetailList;
    }

    /**
     * 代采业务处理
     *
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     * @param agentFinanceDTO          代采数据
     * @param financeApply             融资数据
     * @return 费用订单详情数据
     */
    private List<ExpenseOrderDetail> businessProcessing(EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product, AgentFinanceDTO agentFinanceDTO, FinanceApply financeApply) {
        // 2、数据赋值
        financeData(enterpriseQuotaSearchDTO, product, agentFinanceDTO, financeApply);
        // 3、费用订单详情新增
        List<ExpenseOrderDetail> expenseOrderDetailList = expenseOrderDetailSave(enterpriseQuotaSearchDTO, financeApply);
        // 4、保存资料
        Long customerMaterialId = financeCustomerMaterialService.saveCustomerMaterial(agentFinanceDTO.getCustomerMaterial());
        agentFinanceDTO.setCustomerMaterialId(customerMaterialId);
        return expenseOrderDetailList;
    }

    /**
     * 新增代采产品
     *
     * @param agentFinanceDTO 代采数据
     * @param financeApply    融资数据
     */
    private void purchaseCommoditySave(AgentFinanceDTO agentFinanceDTO, FinanceApply financeApply) {
        for (PurchaseCommodity purchaseCommodity : agentFinanceDTO.getPurchaseCommodityList()) {
            purchaseCommodity.setFinanceNo(financeApply.getFinanceNo());
        }
        purchaseCommodityService.saveBatch(agentFinanceDTO.getPurchaseCommodityList());
    }

    /**
     * 新增融资数据
     *
     * @param agentFinanceDTO 代采数据
     * @param financeApply    融资数据
     */
    private void financeSave(AgentFinanceDTO agentFinanceDTO, FinanceApply financeApply) {
        financeApply.setCreateUser(AuthUtil.getUser().getUserId());
        financeApply.setCreateDept(Func.firstLong(AuthUtil.getUser().getDeptId()));
        financeApply.setUpdateUser(AuthUtil.getUser().getUserId());
        financeApply.setCreateTime(DateUtil.now());
        // 7、新增 代采 -- 融资主数据
        financeApplyService.save(financeApply);
        // 新增 代采---基础信息
        purchaseInformationService.save(agentFinanceDTO);
    }


    /**
     * 修改融资数据
     *
     * @param agentFinanceDTO 代采数据
     * @param financeApply    融资主数据
     */
    private void financeUpdate(AgentFinanceDTO agentFinanceDTO, FinanceApply financeApply) {
        // 7、修改 代采 -- 融资主数据
        financeApplyService.updateById(financeApply);
        // 修改 代采---基础信息
        UpdateWrapper<PurchaseInformation> updateWrapperPurchase = new UpdateWrapper<>();
        updateWrapperPurchase.lambda().eq(PurchaseInformation::getFinanceNo, financeApply.getFinanceNo());
        purchaseInformationService.update(agentFinanceDTO, updateWrapperPurchase);
    }

    /**
     * 数据赋值
     *
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品数据
     * @param agentFinanceDTO          代采数据
     * @param financeApply             融资申请数据
     */
    private void financeData(EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product, AgentFinanceDTO agentFinanceDTO, FinanceApply financeApply) {
        agentFinanceDTO.setFinanceNo(financeApply.getFinanceNo());
        agentFinanceDTO.setLendingMethod(financeApply.getLendingMethod());
        agentFinanceDTO.setDay(Integer.parseInt(DictBizCache.getValue(DictBizEnum.TASK_DAYS.getCode(), CommonConstant.TASK_BILL_DAY)));
        agentFinanceDTO.setProductId(financeApply.getGoodsId());
        agentFinanceDTO.getCustomerMaterial().setFinanceNo(financeApply.getFinanceNo());

        financeApply.setUserId(AuthUtil.getUserId());
        financeApply.setGoodsName(product.getGoodsName());
        financeApply.setDailyInterestRate(enterpriseQuotaSearchDTO.getDailyInterestRate());
        financeApply.setAnnualInterestRate(enterpriseQuotaSearchDTO.getAnnualInterestRate());
        financeApply.setServiceRate(enterpriseQuotaSearchDTO.getServiceRate());
        financeApply.setCapitalId(product.getCapitalId());
        Long personalUserId = AuthUtil.getClaimsParam(WebUtil.getRequest(), "personalUserId", Long.class);
        financeApply.setApplyUser(personalUserId);
        financeApply.setGoodsType(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
    }

    /**
     * 开启流程
     *
     * @param goodsId     产品id
     * @param processType 流程类型
     * @param variables   流程变量
     * @return 流程实例id
     */
    private String startProcess(Long goodsId, Integer processType, Map<String, Object> variables) {
        //发起融资申请流程
        return businessProcessProductService.startProcess(goodsId, processType, variables);
    }

    /**
     * 获取倒计时时间
     *
     * @param goodsId 产品id
     * @param type    流程类型
     * @return 流程实例id
     */
    private LocalDateTime getByExpireTime(Long goodsId, Integer type) {
        LambdaQueryWrapper<GoodsTiming> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(GoodsTiming::getGoodsId, goodsId).eq(GoodsTiming::getType, type);
        GoodsTiming goodsTiming = productDirector.getConfig(goodsId, GoodsEnum.PRODUCT_CONFIG_TIMING.getCode(), GoodsTiming.class, lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(goodsTiming)) {
            throw new ServiceException("查询不到定时数据，请联系管理员！");
        }
        return GoodsTiminUtils.getByExpireTime(goodsTiming.getUnit(), goodsTiming.getNum());
    }

    /**
     * 费用订单详情新增
     *
     * @param enterpriseQuotaSearchDTO 获取企业额度
     * @param financeApply             融资数据
     */
    private List<ExpenseOrderDetail> expenseOrderDetailSave(EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, FinanceApply financeApply) {

        ExpenseOrderDTO expenseOrderDTO = new ExpenseOrderDTO();
        expenseOrderDTO.setGoodsId(financeApply.getGoodsId());
        expenseOrderDTO.setBizNo(financeApply.getFinanceNo());
        expenseOrderDTO.setAmount(financeApply.getAmount());
        expenseOrderDTO.setLoadTerm(financeApply.getLoadTerm());
        expenseOrderDTO.setComputeNode(ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode());
        BigDecimal marginRatio = enterpriseQuotaSearchDTO.getBondProportion().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        BigDecimal margin = marginRatio.multiply(financeApply.getAmount());
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().loanPrincipal(financeApply.getAmount()).serviceRate(enterpriseQuotaSearchDTO.getServiceRate().divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_EVEN)).yearRate(enterpriseQuotaSearchDTO.getAnnualInterestRate().divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_EVEN)).loanDays(BigDecimal.valueOf(financeApply.getLoadTerm()))
                .marginRatio(marginRatio).margin(margin).build();
        expenseOrderDTO.setExpenseRuleDTO(expenseRuleDTO);

        return expenseOrderService.saveExpenseOrderDetail(expenseOrderDTO);
    }


    /**
     * 检查当前融资订单可否提交申请
     *
     * @param financeApplyId 融资申请id
     */
    @Override
    public void checkApplyFinanceApply(Long financeApplyId) {
        //可发起融资的状态
        List<Integer> canApplyFinanceStatus = Arrays.asList(PurchaseEnum.PURCHASE_STATUS_TWELVE.getCode(), PurchaseEnum.PURCHASE_STATUS_FOUR.getCode());
        int count = financeApplyService.count(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getId, financeApplyId).in(FinanceApply::getStatus, canApplyFinanceStatus));
        if (count == 0) {
            throw new ServiceException("当前订单状态不可操作");
        }
    }

    /**
     * 额度扣减
     *
     * @param applyAmount 申请金额
     * @param amount      已经存在得额度（查询融资申请数据，如果不存在则没有额度:默认0）
     * @param goodsId     产品id
     */
    @Override
    public void limitDeduction(BigDecimal applyAmount, BigDecimal amount, Long goodsId) {
        //企业类型可能发生改变，为了后续扩展额度扣减接口各自模块重写
        int enterpriseType = EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode();
        financeEnterpriseService.subtractCreditAmount(applyAmount, amount, goodsId, enterpriseType);
    }

    /**
     * 获取融资申请金额
     *
     * @param financeApplyHandlerDTO 融资申请条件
     * @return 融资申请金额
     */
    @Override
    public BigDecimal accessQuota(FinanceApplyHandlerDTO financeApplyHandlerDTO) {
        AgentFinanceDTO agentFinanceDTO = (AgentFinanceDTO) financeApplyHandlerDTO.getAgentFinanceDTO();
        List<PurchaseCommodity> purchaseCommodityList = agentFinanceDTO.getPurchaseCommodityList();
        return purchaseCommodityList.stream().map(PurchaseCommodity::getFinancingTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取企业额度
     *
     * @param goodsId 产品id
     * @return 企业额度信息
     */
    @Override
    public EnterpriseQuotaSerchDTO selectEnterpriseQuota(Long goodsId) {
        // 查询额度
        Long userId = AuthUtil.getUserId();
        int enterpriseType = EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode();
        return financeEnterpriseService.selectEnterpriseQuota(goodsId, enterpriseType, userId);
    }


    /**
     * 填充融资申请 变量
     *
     * @param agentFinanceDTO        代采数据
     * @param financeApply           融资数据
     * @param processType            流程类型
     * @param expenseOrderDetailList 费用订单详情数据
     * @return 流程变量数据
     */
    private Map<String, Object> getVariables(AgentFinanceDTO agentFinanceDTO, FinanceApply financeApply, Integer processType, List<ExpenseOrderDetail> expenseOrderDetailList, Product product) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ProcessConstant.FINANCE_APPLY_ID, financeApply.getId());
        map.put(ProcessConstant.FINANCE_NO, financeApply.getFinanceNo());
        map.put(ProcessConstant.CUSTOMER_GOODS_ID, financeApply.getCustomerGoodsId());
        map.put(ProcessConstant.BUSINESS_ID, financeApply.getGoodsId());
        map.put(ProcessConstant.CUSTOMER_ID, financeApply.getApplyUser());
        map.put(ProcessConstant.USER_ID, financeApply.getUserId());
        map.put(ProcessConstant.PROCESS_TYPE, processType);
        map.put(ProcessConstant.CUSTOMER_MATERIAL, agentFinanceDTO.getCustomerMaterial());
        map.put(ProcessConstant.FINANCE_APPLY, financeApply);
        map.put(WfProcessConstant.PROCESS_NO, financeBusinessProcessService.getProcessNo(financeApply.getProcessInstanceId()));
        map.put(PurchaseConstant.LENDING_METHOD, financeApply.getLendingMethod());
        map.put(PurchaseConstant.CHARGE_METHOD, financeApply.getChargeMethod());
        map.put(ProcessConstant.PROCESS_GOODS_INFO, agentFinanceDTO);
        map.put(WfProcessConstant.EXPENSE_FEE, expenseOrderDetailList);
        map.put(WfProcessConstant.PRODUCT_DATA, product);
        return map;
    }

}
