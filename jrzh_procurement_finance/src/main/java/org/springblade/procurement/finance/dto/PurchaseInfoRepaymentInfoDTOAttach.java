/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.finance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 代采-计算信息
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseInfoRepaymentInfoDTOAttach implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 保证金
     */
    private BigDecimal margin;
    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;
    /**
     * 融资单价
     */
    private BigDecimal financingPrice;
}
