package org.springblade.procurement.finance.controller.applet;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 *  小程序 代采---基础信息 控制器
 *
 * <AUTHOR>
 * @date 2023-04-18
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_PURCHASE + CommonConstant.WEB_APPLET + "/purchase/purchaseInformation")
@Api(value = "代采---基础信息", tags = "代采---基础信息接口")
public class PurchaseInformationAppletController {

	private final IPurchaseInformationService purchaseInformationService;



}
