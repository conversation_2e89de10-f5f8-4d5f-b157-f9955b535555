package org.springblade.procurement.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;


/**
 * 代采---基础信息 服务类
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface IPurchaseInformationService extends BaseService<PurchaseInformation> {

    /**
     * 自定义分页
     *
     * @param page
     * @param purchaseInformation
     * @return
     */
    IPage<PurchaseInformationVO> selectPurchaseInformationPage(IPage<PurchaseInformationVO> page, PurchaseInformationVO purchaseInformation);

}
