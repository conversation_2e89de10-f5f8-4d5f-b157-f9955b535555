/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.finance.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.entity.PurchaseInformation;

import java.util.List;

/**
 * 代采---基础信息数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseInformationDTO extends PurchaseInformation {
    private static final long serialVersionUID = 1L;

    /**
     * 代采--采购商品 集合
     */
    private List<PurchaseCommodity> purchaseCommodityList;

    /**
     * 融资申请数据（主数据）
     */
    private FinanceApply financeApply;

    /**
     * 补充资料
     */
    private CustomerMaterial customerMaterial;
    /**
     * 还款参数
     */
    private CostCalculusDto costCalculusDto;
}
