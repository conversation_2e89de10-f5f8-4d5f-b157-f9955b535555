package org.springblade.procurement.finance.strategy;

import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.mapper.LoanManageOverdueMapper;
import org.springblade.loan.mapper.LoanManageRepaymentMapper;
import org.springblade.loan.mapper.LoanManageRepaymentPlanMapper;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.IRepaymentBizService;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.report.dto.CreditReportDto;
import org.springblade.report.dto.LoanReport;
import org.springblade.report.dto.RepaymentReport;
import org.springblade.report.handler.IProductLoanReportStrategy;
import org.springblade.report.vo.CreditPassVO;
import org.springblade.report.vo.CreditReport;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品信贷策略类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-16  13:43
 * @Description: 产品信贷策略类
 * @Version: 1.0
 */
@Service("PURCHASE_PRODUCT_LOAN_REPORT_STRATEGY")
@RequiredArgsConstructor
public class ProcurementProductLoanReportStrategy implements IProductLoanReportStrategy {
    private final ILoanManageIouService loanManageIouService;
    private final LoanManageRepaymentPlanMapper loanManageRepaymentPlanMapper;
    private final LoanManageRepaymentMapper loanManageRepaymentMapper;
    private final LoanManageOverdueMapper loanManageOverdueMapper;
    private final IRepaymentBizService repaymentBizService;
    private final LoanManageIouMapper loanManageIouMapper;


    @Override
    public GoodsEnum support() {
        return GoodsEnum.AGENT_PURCHASE_FINANCING;
    }

    @Override
    public CreditReport creditReport(String time, String tentId) {
        String tenantId = AuthUtil.getTenantId();
        // 新增放款笔数 时间点内的放款笔数汇总
        Integer loanCount = loanManageIouMapper.loanCount(time, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode(), tenantId);
        //放款金额汇总 时间点内的放款金额汇总
        String val = loanManageIouMapper.amountSum(time, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode(), tenantId);
        BigDecimal loanAmount = new BigDecimal(val == null ? "0" : val);
        //约定还款笔数
        Integer needPayCount = loanManageRepaymentPlanMapper.needPayCount(time, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode(), tenantId);
        //未还款笔数=到了时间点应该还 但是还没还的
        Integer payableCount = loanManageRepaymentPlanMapper.payableCount(time, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode(), tenantId);
        //已还款笔数=统计时间点有多少笔还款
        List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentMapper.payLoanList(time, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode(), tenantId);
        //实还利息
        BigDecimal payedInterest = BigDecimal.ZERO;
        //实还本金
        BigDecimal payedPrincipal = BigDecimal.ZERO;
        for (LoanManageRepayment loanManageRepayment : loanManageRepayments) {
            payedInterest = payedInterest.add(loanManageRepayment.getActualInterest());
            payedPrincipal = payedPrincipal.add(loanManageRepayment.getActualAmount());
        }
        Integer payedCount = loanManageRepayments.size();
        CreditReport build = CreditReport.builder()
                .queryTime(time)//时间
                .numberOfLoans(loanCount)//新增放款笔数
                .numberOfPay(payedCount)//已还笔数
                .numberOfUnPay(payableCount)//未还款笔数
                .loanAmount(loanAmount)//放款金额汇总
                .payInterest(payedInterest)//实还利息
                .payAmount(payedPrincipal)//实还金额
                .tenantId(tentId)
                .overDueMoney(BigDecimal.ZERO)
                .needPayCount(needPayCount)
                .build();
        return build;
    }

    /**
     * 拼接时间 -01 格式
     * 2022 -> 2022-01-01
     * 2022-08 -> 2022-08-01
     * 2022-08-06 -> 2022-08-06
     *
     * @param time
     * @return
     */
    private String getDateStr(String time) {
        String[] split = time.split("-");
        int length = split.length;
        Integer num = 3 - (split.length);
        String str = "";
        if (num > 0) {
            for (Integer integer = 0; integer < num; integer++) {
                str = str + "-01";
            }
        }
        String timeDate = time + str;
        return timeDate;
    }

    @Override
    public List<CreditPassVO> actualRepaymentReport(CreditReportDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<CreditPassVO> agreedRepaymentReport(CreditReportDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public CreditReport customerCreditReport(String time, Long userId, String tentId) {
        CreditReport creditReport = creditReport(time, tentId);
        //逾期金额
        String overDueAmountStr = loanManageOverdueMapper.overDueAmount(time, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode(), userId, tentId);
        BigDecimal overDueAmount = overDueAmountStr == null ? BigDecimal.ZERO : new BigDecimal(overDueAmountStr);
        creditReport.setOverDueMoney(overDueAmount);
        return creditReport;
    }

    @Override
    public List<CreditPassVO> creditNewCreditReport(CreditReportDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Integer creditNewCustomerReport(String dateStr, Long newId, String tenantId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public CreditReport repaymentAnalysisReport(String time, Integer year, String tentId) {
        List<Integer> goodsTypeList = Arrays.asList(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanMapper.payableByDtoList(time, goodsTypeList, tentId);
        List<RepaymentInfoDTO> repaymentInfoDTOS = repaymentBizService.getRepaymentInfoDTOS(LocalDate.now(), loanManageRepaymentPlans);
        BigDecimal payableAmount = BigDecimal.ZERO;
        BigDecimal payableInterest = BigDecimal.ZERO;
        BigDecimal payedAmount = BigDecimal.ZERO;
        BigDecimal payedInterest = BigDecimal.ZERO;
        BigDecimal overDueAmount = BigDecimal.ZERO;
        Integer overDueCount = 0;
        Integer normalCount = 0;
        for (RepaymentInfoDTO repaymentInfoDTO : repaymentInfoDTOS) {
            payableAmount = payableAmount.add(repaymentInfoDTO.getPrincipal());
            payableInterest = payableInterest.add(repaymentInfoDTO.getPlanInterest());
            if (RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode().equals(repaymentInfoDTO.getOverdue())) {
                overDueAmount = overDueAmount.add(repaymentInfoDTO.getSubPrincipal());
                overDueCount = overDueCount + 1;
            }
            if (RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode().equals(repaymentInfoDTO.getRepaymentStatus())) {
                normalCount = normalCount + 1;
            }
            List<LoanManageRepayment> loanManageRepayments = repaymentInfoDTO.getValidRepaymentList().stream().filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus())).collect(Collectors.toList());
            BigDecimal actualPrincipal = loanManageRepayments.stream().map(LoanManageRepayment::getActualPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal actualInterest = loanManageRepayments.stream().map(LoanManageRepayment::getActualInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
            payedAmount = payedAmount.add(actualPrincipal);
            payedInterest = payedInterest.add(actualInterest);
        }
        return CreditReport.builder()
                .queryTime(time)//时间
                .payableAmount(payableAmount)//应还本金
                .payableInterest(payableInterest)//应还利息
                .payInterest(payedInterest)//实还利息
                .payAmount(payedAmount)//实还本金
                .overDueMoney(overDueAmount)//逾期待还金额
                .repaymentNum(repaymentInfoDTOS.size())//还款计划数
                .normalNum(normalCount)//正常还款件数
                .overDueNum(overDueCount)//逾期件数
                .build();
    }

    @Override
    public List<LoanReport> getLoanReport() {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<RepaymentReport> repaymentReportMethod() {
        throw new UnsupportedOperationException();
    }
}
