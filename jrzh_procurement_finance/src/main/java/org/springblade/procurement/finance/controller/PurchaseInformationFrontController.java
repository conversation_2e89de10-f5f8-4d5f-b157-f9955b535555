
package org.springblade.procurement.finance.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.dto.FinanceApplyDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.finance.vo.FinanceApplyVO;
import org.springblade.finance.wrapper.FinanceApplyWrapper;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.procurement.finance.dto.PurchaseInformationApplyDTO;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.service.IPurchaseFinanceApplyService;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 代采---基础信息 控制器
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_PURCHASE + CommonConstant.WEB_FRONT + "/purchase/purchaseInformation")
@Api(value = "代采---基础信息", tags = "代采---基础信息接口")
public class PurchaseInformationFrontController extends BladeController {

    private final IPurchaseFinanceApplyService purchaseFinanceApplyService;
    private final IFinanceApplyService financeApplyService;
    private final IPurchaseInformationService purchaseInformationService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final String SUBSTITUTE_PURCHASE_PROCESS_TYPE = "substitute_purchase_process_type";

    /**
     * 分页 代采融资列表
     */
    @GetMapping("/list-purchase")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入financeApply")
    @PreAuth("hasPermission('finance-apply:list-purchase') or hasRole('admin') or hasRole('financing_admin')")
    public R<IPage<FinanceApplyVO>> listPurchase(FinanceApplyDTO financeApplyDTO, Query query) {
        Long userId = AuthUtil.getUserId();
        financeApplyDTO.setUserIdEqual(userId);
        financeApplyDTO.setUserIdEqual(AuthUtil.getUserId());
        //默认展示代采数据
        financeApplyDTO.setGoodsTypeEqual(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
        IPage<FinanceApply> page = financeApplyService.page(org.springblade.common.utils.Condition.getPage(query), org.springblade.common.utils.Condition.getQueryWrapper(financeApplyDTO, FinanceApply.class).orderByDesc("create_time"));
        IPage<FinanceApplyVO> financeApplyVOPage = FinanceApplyWrapper.build().pageVO(page);
        List<FinanceApplyVO> records = financeApplyVOPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<String> financeNoList = records.parallelStream().map(FinanceApply::getFinanceNo).collect(Collectors.toList());
            //根据融资编号查询 代采--基础数据
            List<PurchaseInformation> purchaseInformationList = purchaseInformationService.list(Wrappers.<PurchaseInformation>lambdaQuery().in(PurchaseInformation::getFinanceNo, financeNoList));
            List<String> ids = financeApplyVOPage.getRecords().parallelStream().map(e -> e.getId().toString()).collect(Collectors.toList());
            Map<String, PurchaseInformation> purchaseInformationMap = purchaseInformationList.parallelStream().collect(Collectors.toMap(PurchaseInformation::getFinanceNo, e -> e));
            Map<Long, Map<Integer, List<BusinessProcessProgress>>> busMap = businessProcessProgressService.listByDicBiz(SUBSTITUTE_PURCHASE_PROCESS_TYPE, userId, ids);
            for (FinanceApplyVO record : records) {
                if (purchaseInformationMap.containsKey(record.getFinanceNo())) {
                    PurchaseInformation purchaseInformation = purchaseInformationMap.get(record.getFinanceNo());
                    record.setDay(purchaseInformation.getDay());
                }
                if (busMap.containsKey(record.getId())) {
                    record.setBusinessProcessProgressMap(busMap.get(record.getId()));
                }
            }

        }

        return R.data(financeApplyVOPage);
    }

    /**
     * 代采申请提交
     * liulei
     */
    @PostMapping("/purchase-apply-submit-one")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "代采申请提交第一步", notes = "传入purchaseCommodity")
//	@PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:detail') or hasRole('administrator')")
    public R purchaseApplySubmit(@Valid @RequestBody PurchaseInformationApplyDTO purchaseInformationApplyDTO) {

        return R.data(purchaseFinanceApplyService.purchaseApplySubmitOne(purchaseInformationApplyDTO));
    }

    /**
     * 待提交，根据融资编号获取代采信息
     */
    @GetMapping("/purchase-wait-sumbit")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "待提交，根据融资编号获取代采信息", notes = "传入financeNo")
//	@PreAuth("hasPermission('purchaseCommodity:purchaseCommodity:detail') or hasRole('administrator')")
    public R<PurchaseInformationVO> purchaseWaitSumbit(@ApiParam(value = "融资编号", required = true) @RequestParam String financeNo) {
        return R.data(purchaseFinanceApplyService.purchaseOneDetail(financeNo));
    }

    /**
     * 融资申请还款试算还款试算
     */
    @PostMapping("/repaymentFinanceApplyCalculation")
    public R<CostCalculusVO> repaymentFinanceApplyCalculation(@RequestBody CostCalculusDto costCalculusDto) {
        return R.data(purchaseFinanceApplyService.repaymentFinanceApplyCalculation(costCalculusDto));
    }
    /**
     * 根据融资单号和费用业务类型查看还款计划列表及费用
     *
     * @param financeNo 融资单号
     * @param type      费用类型（不传则查该融资单号下所有费用详情）
     * @return
     */
    @GetMapping("/repaymentCalculation-financeNo")
    public R<CostCalculusVO> repaymentCalculation(@RequestParam String financeNo, @RequestParam(required = false) Integer type) {
        return R.data(purchaseFinanceApplyService.getRepaymentPlanAndFeeByFinanceNoAndType(financeNo, type));
    }
    /**
     * 融资申请还款试算费用
     */
    @GetMapping("/expenseList")
    public R<List<ExpenseOrderDetail>> expenseList(@RequestParam Long goodsId,
                                                   @RequestParam Integer chargePoint,
                                                   @RequestParam BigDecimal financeAmount,
                                                   @RequestParam Integer loanDay) {
        return R.data(purchaseFinanceApplyService.expenseList(goodsId, chargePoint, financeAmount, loanDay));
    }

    /**
     * 获取服务器当前时间
     */
    @GetMapping("/currentTime")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取服务器当前时间", notes = "传入purchaseInformation")
    public R<LocalDateTime> currentTime() {
        return R.data(LocalDateTime.now());
    }


}
