
package org.springblade.procurement.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseMapper;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;

import java.util.List;


/**
 * 代采---基础信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
public interface PurchaseInformationMapper extends BaseMapper<PurchaseInformation>, MPJBaseMapper<PurchaseInformation> {

    /**
     * 自定义分页
     *
     * @param page
     * @param purchaseInformation
     * @return
     */
    List<PurchaseInformationVO> selectPurchaseInformationPage(IPage page, PurchaseInformationVO purchaseInformation);

}
