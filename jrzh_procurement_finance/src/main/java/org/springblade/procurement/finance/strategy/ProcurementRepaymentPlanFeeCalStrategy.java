package org.springblade.procurement.finance.strategy;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.expense.handler.ProductExpenseOrderDetailUtils;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.strategy.RepaymentPlanFeeCalStrategy;
import org.springblade.loan.utils.LoanUtils;
import org.springblade.procurement.finance.dto.PurchaseInfoRepaymentInfoDTO;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/12/18 20:58
 */
@Service
@RequiredArgsConstructor
public class ProcurementRepaymentPlanFeeCalStrategy implements RepaymentPlanFeeCalStrategy {
    private final ProductDirector productDirector;
    private final IEnterpriseQuotaService enterpriseQuotaService;

    @Override
    public GoodsEnum support() {
        return GoodsEnum.AGENT_PURCHASE_FINANCING;
    }

    @Override
    public ExpenseRuleDTO buildNormalRepayment(String req) {
        CostCalculusDto costCalculusDto = JSONUtil.toBean(req, CostCalculusDto.class);
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(costCalculusDto.getGoodsId(), costCalculusDto.getUserId(), costCalculusDto.getEnterpriseType());
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), costCalculusDto.getStartTime(), costCalculusDto.getTotalTerm());
        Integer interestDay = ObjUtil.isNotEmpty(product.getInterestDay()) ? product.getInterestDay() + loanDay : loanDay;
        Integer loanPeriods = GoodsEnum.TERM.getCode().equals(costCalculusDto.getLoadTermUnit()) ? costCalculusDto.getTotalTerm() : 1;
        return ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(BigDecimal.valueOf(interestDay))
                .loanDays(BigDecimal.valueOf(loanDay))
                .loanPeriods(BigDecimal.valueOf(loanPeriods))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getServiceRate()))
                .dayRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getDailyInterestRate()))
                .margin(enterpriseQuota.getBondProportion())
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getAnnualInterestRate())).build();

    }

    @Override
    public ExpenseRuleDTO buildOverdueRepayment(String req) {
        return RepaymentPlanFeeCalStrategy.super.buildOverdueRepayment(req);
    }

    @Override
    public ExpenseRuleDTO buildRedemptionConfirm(String req) {
        PurchaseInfoRepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, PurchaseInfoRepaymentInfoDTO.class);
        LoanManageIou loanManageIou = repaymentInfoDTO.getLoanManageIou();
        BigDecimal yearRate = ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getAnnualInterestRate());
        BigDecimal dayRate = LoanUtils.calculationDayRate(yearRate);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(BigDecimal.valueOf(repaymentInfoDTO.getInterestAccrualDays()))
                .loanDays(BigDecimal.valueOf(repaymentInfoDTO.getLoanDay()))
                .loanPeriods(BigDecimal.valueOf(repaymentInfoDTO.getTerm()))
                .principalRepayment(loanManageIou.getIouAmount())
                .loanPrincipal(loanManageIou.getIouAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getServiceFeeRate()))
                .dayRate(dayRate)
                .margin(repaymentInfoDTO.getMargin())
                .yearRate(yearRate)
                .purchasePrices(repaymentInfoDTO.getPurchasePrice())
                .financingPrices(repaymentInfoDTO.getFinancingPrice())
                .build();
        expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(repaymentInfoDTO.getLoanDay()));
        expenseRuleDTO.setTermSurplusPrincipalRepayment(repaymentInfoDTO.getSubPrincipal());
        expenseRuleDTO.setOverdueDays(BigDecimal.ZERO);
        expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
        expenseRuleDTO.setTermPrincipalRepayment(repaymentInfoDTO.getPrincipal());
        expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(repaymentInfoDTO.getInterestAccrualDays()));
        return expenseRuleDTO;
//        CostCalculusDto costCalculusDto = JSONUtil.toBean(req, CostCalculusDto.class);
//        LocalDate startTime = costCalculusDto.getStartTime();
//        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
//        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(costCalculusDto.getGoodsId(), costCalculusDto.getUserId(), costCalculusDto.getEnterpriseType());
//        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), startTime, costCalculusDto.getTotalTerm());
//        Integer accumulateLoanDay = ProductExpenseOrderDetailUtils.getLoanDay(startTime, LocalDate.now());
//        BigDecimal interestDay = ProductExpenseOrderDetailUtils.getInterestDayByLoanDay(product, accumulateLoanDay);
//        Integer loanPeriods = GoodsEnum.TERM.getCode().equals(costCalculusDto.getLoadTermUnit()) ? costCalculusDto.getTotalTerm() : 1;
//        StagRecordVO stagRecordVO = costCalculusDto.getStagRecordVO();
//        //填充总体
//        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
//                .feeAmount(BigDecimal.ZERO)
//                .interestAccrualDays(interestDay)
//                .loanDays(BigDecimal.valueOf(loanDay))
//                .loanPeriods(BigDecimal.valueOf(loanPeriods))
//                .principalRepayment(costCalculusDto.getFinanceAmount())
//                .loanPrincipal(costCalculusDto.getFinanceAmount())
//                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getServiceRate()))
//                .dayRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getDailyInterestRate()))
//                .margin(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getBondProportion()))
//                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getAnnualInterestRate())).build();
//        //填充本期参数
//        if (ObjectUtil.isNotEmpty(stagRecordVO)) {
//            Integer termLoanDays = ProductExpenseOrderDetailUtils.getLoanDay(stagRecordVO.getStartTime(), stagRecordVO.getRefundTime());
//            //填充本期还款参数
//            expenseRuleDTO.setCurrentRepaymentAmount(stagRecordVO.getMonthlySupply());
//            expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(termLoanDays));
//            expenseRuleDTO.setTermSurplusPrincipalRepayment(stagRecordVO.getMonthlySupply());
//            expenseRuleDTO.setOverdueDays(BigDecimal.ZERO);
//            expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
//            expenseRuleDTO.setTermPrincipalRepayment(stagRecordVO.getMonthlySupply());
//            expenseRuleDTO.setTermInterestAccrualDays(ProductExpenseOrderDetailUtils.getInterestDay(product, stagRecordVO.getStartTime()));
//        }
//        return expenseRuleDTO;
    }

    @Override
    public ExpenseRuleDTO buildFinanceApply(String req) {
        CostCalculusDto costCalculusDto = JSONUtil.toBean(req, CostCalculusDto.class);
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(costCalculusDto.getGoodsId(), costCalculusDto.getUserId(), costCalculusDto.getEnterpriseType());
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), costCalculusDto.getStartTime(), costCalculusDto.getTotalTerm());
        Integer interestDay = ObjUtil.isNotEmpty(product.getInterestDay()) ? product.getInterestDay() + loanDay : loanDay;
        Integer loanPeriods = GoodsEnum.TERM.getCode().equals(costCalculusDto.getLoadTermUnit()) ? costCalculusDto.getTotalTerm() : 1;
        return ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(BigDecimal.valueOf(interestDay))
                .loanDays(BigDecimal.valueOf(loanDay))
                .loanPeriods(BigDecimal.valueOf(loanPeriods))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getServiceRate()))
                .dayRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getDailyInterestRate()))
                .marginRatio(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getBondProportion()))
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getAnnualInterestRate())).build();
    }

    private Product getProductByGoodsId(Long goodsId) {
        return productDirector.detailBase(goodsId);
    }

    private EnterpriseQuota getEnterpriseQuota(Long goodsId, Long userId, Integer enterpriseType) {
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseIdBase(goodsId, enterpriseType, userId);
        return enterpriseQuota;

    }

    @Override
    public ExpenseRuleDTO buildRedemptionApply(String req) {
        PurchaseInfoRepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, PurchaseInfoRepaymentInfoDTO.class);
        LoanManageIou loanManageIou = repaymentInfoDTO.getLoanManageIou();
        BigDecimal yearRate = ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getAnnualInterestRate());
        BigDecimal dayRate = LoanUtils.calculationDayRate(yearRate);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(BigDecimal.valueOf(repaymentInfoDTO.getInterestAccrualDays()))
                .loanDays(BigDecimal.valueOf(repaymentInfoDTO.getLoanDay()))
                .loanPeriods(BigDecimal.valueOf(repaymentInfoDTO.getTerm()))
                .principalRepayment(loanManageIou.getIouAmount())
                .loanPrincipal(loanManageIou.getIouAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getServiceFeeRate()))
                .dayRate(dayRate)
                .margin(repaymentInfoDTO.getMargin())
                .yearRate(yearRate)
                .surplusPrincipalRepayment(repaymentInfoDTO.getSubPrincipal())
                .purchasePrices(repaymentInfoDTO.getPurchasePrice())
                .financingPrices(repaymentInfoDTO.getFinancingPrice())
                .build();
        expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(repaymentInfoDTO.getLoanDay()));
        expenseRuleDTO.setTermSurplusPrincipalRepayment(repaymentInfoDTO.getSubPrincipal());
        expenseRuleDTO.setOverdueDays(BigDecimal.ZERO);
        expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
        expenseRuleDTO.setTermPrincipalRepayment(repaymentInfoDTO.getPrincipal());
        expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(repaymentInfoDTO.getInterestAccrualDays()));
        return expenseRuleDTO;
    }
}
