package org.springblade.procurement.finance.handler.repayment;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.finance.handler.FinanceApplyRepaymentPayNotifyHandler;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.RepaymentFee;
import org.springblade.repayment.handler.LoanManageIouSettleNotifyHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 借据单结清通知
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-16  03:25
 * @Description: 借据单结清通知
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ProcurementFinanceApplyLoanManageIouSettleNotifyHandler implements LoanManageIouSettleNotifyHandler , FinanceApplyRepaymentPayNotifyHandler {
    private final IFinanceApplyService financeApplyService;

    @Override
    public GoodsEnum support() {
        return GoodsEnum.AGENT_PURCHASE_FINANCING;
    }

    @Override
    public void payed(LoanManageRepayment loanManageRepayment, List<RepaymentFee> repaymentFeeList, LoanManageIou loanManageIou) {

    }

    @Override
    public void hasOtherOverDuePayed(LoanManageRepayment loanManageRepayment, List<RepaymentFee> repaymentFeeList, LoanManageIou loanManageIou) {

    }

    @Override
    public void normalSettle(LoanManageIou loanManageIou) {
//        financeApplyService.changeFinanceStatus(loanManageIou.getFinanceNo(), PurchaseEnum.PURCHASE_STATUS_NINE.getCode());
    }

    @Override
    public void overdueSettled(LoanManageIou loanManageIou) {
//        financeApplyService.changeFinanceStatus(loanManageIou.getFinanceNo(), PurchaseEnum.PURCHASE_STATUS_ELEVEN.getCode());
    }

    @Override
    public void advanceSettled(LoanManageIou loanManageIou) {
//        financeApplyService.changeFinanceStatus(loanManageIou.getFinanceNo(), PurchaseEnum.PURCHASE_STATUS_EIGHT.getCode());
    }
}
