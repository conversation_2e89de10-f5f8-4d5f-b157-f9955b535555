/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.finance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.mapper.PurchaseInformationMapper;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;
import org.springframework.stereotype.Service;

/**
 * 代采---基础信息 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Service
@RequiredArgsConstructor
public class PurchaseInformationServiceImpl extends BaseServiceImpl<PurchaseInformationMapper, PurchaseInformation> implements IPurchaseInformationService {

    /**
     * 自定义分页
     *
     * @param page
     * @param purchaseInformation
     * @return
     */
    @Override
    public IPage<PurchaseInformationVO> selectPurchaseInformationPage(IPage<PurchaseInformationVO> page, PurchaseInformationVO purchaseInformation) {
        return null;
    }

}
