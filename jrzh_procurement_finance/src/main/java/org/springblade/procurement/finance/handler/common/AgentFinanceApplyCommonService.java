package org.springblade.procurement.finance.handler.common;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.enums.financeApply.FinanceApplyEnums;
import org.springblade.finance.external.handler.customerMaterial.FinanceCustomerMaterialService;
import org.springblade.finance.external.handler.enterpriseQuota.FinanceEnterpriseService;
import org.springblade.finance.handler.FinanceApplyCommonService;
import org.springblade.finance.vo.FinanceApplyVO;
import org.springblade.finance.vo.financeCommon.FinanceApplyCommonVo;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.service.IPurchaseCommodityService;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springblade.procurement.finance.vo.AgentFinanceCommonVo;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.system.entity.User;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service(FinanceApplyEnums.FinanceCommonType.FINANCE_COMMON_AGENT)
@RequiredArgsConstructor
public class AgentFinanceApplyCommonService implements FinanceApplyCommonService {


    /**
     * 产品配置 接口
     */
    private final ProductDirector productDirector;

    /**
     * 融资-额度接口
     */
    private final FinanceEnterpriseService financeEnterpriseService;

    /**
     * 代采--采购商品 接口
     */
    private final IPurchaseCommodityService purchaseCommodityService;

    /**
     * 代采---基础信息 接口
     */
    private final IPurchaseInformationService purchaseInformationService;
    /**
     * 客户资料 接口
     */
    private final FinanceCustomerMaterialService financeCustomerMaterialService;


    /**
     * 根据融资主数据，查询融资详情数据
     *
     * @param financeApply 融资主数据
     * @return 融资详情数据
     */
    @Override
    public FinanceApplyCommonVo financeDetail(FinanceApply financeApply) {
        //定义主体返回类型
        FinanceApplyCommonVo financeApplyCommonVo = new FinanceApplyCommonVo();
        financeApplyCommonVo.setFinanceApply(financeApply);

        //获取代采数据
        PurchaseInformation purchaseInformation = purchaseInformationService.getOne(Wrappers.<PurchaseInformation>lambdaQuery().eq(PurchaseInformation::getFinanceNo, financeApply.getFinanceNo()));
        //获取客户资料数据
        CustomerMaterial customerMaterial = financeCustomerMaterialService.getByIdData(purchaseInformation.getCustomerMaterialId());
        //获取代采商品集合数据
        List<PurchaseCommodity> purchaseCommodityList = purchaseCommodityService.list(Wrappers.<PurchaseCommodity>lambdaQuery().eq(PurchaseCommodity::getFinanceNo, financeApply.getFinanceNo()));
        //查询产品数据
        Product product = productDirector.detailBase(financeApply.getGoodsId());

        //定义代采接收类型,数据赋值
        AgentFinanceCommonVo agentFinanceCommonVo = BeanUtil.toBean(purchaseInformation, AgentFinanceCommonVo.class);
        agentFinanceCommonVo.setPurchaseCommodityList(purchaseCommodityList);
        agentFinanceCommonVo.setCustomerMaterial(customerMaterial);
        agentFinanceCommonVo.setCapitalAvatar(product.getCapitalLogo());
        agentFinanceCommonVo.setCapitalName(product.getCapitalName());
        agentFinanceCommonVo.setBondReleaseMode(product.getBondReleaseMode());
        return financeApplyCommonVo;
    }

    @Override
    public void dealFinanceApply(List<FinanceApplyVO> financeApplyVOList, Map<String, List<LoanManageRepaymentPlan>> allUnRepaymentMap, Map<String, List<LoanManageRepaymentPlan>> overdueRepaymentMap, Map<Long, User> userMap) {
        for (FinanceApplyVO financeApplyVO : financeApplyVOList) {
            User user = userMap.get(financeApplyVO.getUserId());
            if (Objects.nonNull(user)) {
                financeApplyVO.setCustomerName(user.getName());
            }
            financeApplyVO.setCanOpenDelayApply(false);
            financeApplyVO.setCanOpenOverdueConsult(false);
        }
    }
}
