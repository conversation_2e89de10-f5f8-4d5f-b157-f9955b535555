package org.springblade.procurement.finance.service;

import org.flowable.engine.delegate.DelegateExecution;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.procurement.finance.dto.PurchaseInformationApplyDTO;
import org.springblade.procurement.finance.dto.PurchaseInformationDTO;
import org.springblade.procurement.finance.vo.PurchaseBackApplyInfo;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 代采融资申请
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023/11/21 16:59
 * @Description: 代采融资申请
 * @Version: 1.0
 */
public interface IPurchaseFinanceApplyService {
    /**
     * 代采申请提交第一步
     *
     * @param purchaseInformationApplyDTO 代采信息 dto
     * @return 封装数据
     * liulei
     */
    PurchaseInformationApplyDTO purchaseApplySubmitOne(PurchaseInformationApplyDTO purchaseInformationApplyDTO);

    /**
     * 传入 融资编号，返回代采信息
     *
     * @param financeNo 融资编号
     * @return 代采信息
     */
    PurchaseInformationVO purchaseOneDetail(String financeNo);

    /**
     * 融资节点进行还款试算
     *
     * @param costCalculusDto
     * @return
     */
    CostCalculusVO repaymentFinanceApplyCalculation(CostCalculusDto costCalculusDto);

    /**
     * 代采申请提交
     *
     * @param purchaseInformationDTO 代采信息 dto
     * @return 封装数据
     */
    PurchaseInformationVO purchaseApplySubmitNew(PurchaseInformationDTO purchaseInformationDTO);

    /**
     * 代采放款提交
     *
     * @param financeNo
     * @param expenseInfoExpenseVOList
     * @return
     */
    Map<String, Object> purchaseLastSubmitNew(String financeNo, List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList);


    /**
     * 进入融资确认最后一个页面,或者返回到 融资确认页面
     *
     * @param financeNo 融资编号
     * @param node      节点 2、融资确认 3、缴纳保证金 4、缴纳保证金及费用
     * @return
     */
    PurchaseInformationVO purchaseLastNode(String financeNo, Integer node);

    /**
     * 试算费用
     *
     * @param goodsId
     * @param chargePoint
     * @param financeAmount
     * @param loanDay
     * @return
     */
    List<ExpenseOrderDetail> expenseList(Long goodsId, Integer chargePoint, BigDecimal financeAmount, Integer loanDay);

    /**
     * 保存还款计划
     *
     * @param loanManageIou
     * @param financeApply
     * @param delegateExecution
     */
    void savePurchaseRepaymentPlan(LoanManageIou loanManageIou, FinanceApply financeApply, DelegateExecution delegateExecution);

    /**
     * 平台端代采--融资订单详情申请信息
     *
     * @param financeNo 融资编号
     * @return
     */
    PurchaseBackApplyInfo selectPurchaseBackFinanceApplyInfo(String financeNo);

    CostCalculusVO getRepaymentPlanAndFeeByFinanceNoAndType(String financeNo, Integer type);
}
