/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.vo.PurchaseCommodityVO;

import java.util.List;

/**
 * 代采--采购商品 服务类
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface IPurchaseCommodityService extends BaseService<PurchaseCommodity> {

    /**
     * 自定义分页
     *
     * @param page
     * @param purchaseCommodity
     * @return
     */
    IPage<PurchaseCommodityVO> selectPurchaseCommodityPage(IPage<PurchaseCommodityVO> page, PurchaseCommodityVO purchaseCommodity);

    /**
     * 本次采购商品
     *
     * @param financeNo
     * @return
     */
    List<PurchaseCommodity> listByFinanceNo(String financeNo);
}
