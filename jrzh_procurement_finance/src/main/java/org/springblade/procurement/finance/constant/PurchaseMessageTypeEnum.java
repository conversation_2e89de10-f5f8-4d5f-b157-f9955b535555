package org.springblade.procurement.finance.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description:
 * @date 2023年08月16日 16:45
 */
@Getter
public enum PurchaseMessageTypeEnum {
    /**
     * 代采融资申请未提交
     */
    FINANCE_APPLY_UN_SUBMIT("finance_apply_un_submit", "代采融资申请未提交"),

    /**
     * 融资申请未申请放款
     */
    FINANCE_APPLY_UN_LOAN("finance_apply_un_loan", "融资申请未申请放款"),
    /**
     * 代采--赎货申请待确认
     */
    PURCHASE_REDEEM_CARGO_APPLY("purchase_redeemCargo_apply", "代采--赎货申请待确认"),
    ;

    PurchaseMessageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    private final String code;
    private final String desc;

}
