/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.finance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.mapper.PurchaseCommodityMapper;
import org.springblade.procurement.finance.service.IPurchaseCommodityService;
import org.springblade.procurement.finance.vo.PurchaseCommodityVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 代采--采购商品 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Service
@RequiredArgsConstructor
public class PurchaseCommodityServiceImpl extends BaseServiceImpl<PurchaseCommodityMapper, PurchaseCommodity> implements IPurchaseCommodityService {


    /**
     * 自定义分页
     *
     * @param page
     * @param purchaseCommodity
     * @return
     */
    @Override
    public IPage<PurchaseCommodityVO> selectPurchaseCommodityPage(IPage<PurchaseCommodityVO> page, PurchaseCommodityVO purchaseCommodity) {
        return null;
    }

    /**
     * 本次采购商品
     *
     * @param financeNo
     * @return
     */
    @Override
    public List<PurchaseCommodity> listByFinanceNo(String financeNo) {
        return null;
    }
}
