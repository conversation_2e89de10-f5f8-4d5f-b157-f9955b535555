<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.procurement.finance.mapper.PurchaseInformationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="purchaseInformationResultMap" type="org.springblade.procurement.finance.entity.PurchaseInformation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="purchase_code" property="purchaseCode"/>
        <result column="pick_up_manner" property="pickUpManner"/>
        <result column="receive_company_name" property="receiveCompanyName"/>
        <result column="receive_address" property="receiveAddress"/>
        <result column="receive_name" property="receiveName"/>
        <result column="receive_number" property="receiveNumber"/>
        <result column="deliver_time" property="deliverTime"/>
        <result column="receipt_standard" property="receiptStandard"/>
        <result column="payment_type" property="paymentType"/>
        <result column="remark" property="remark"/>
        <result column="open_goods_id" property="openGoodsId"/>
        <result column="goods_name" property="goodsName"/>
        <result column="repayment" property="repayment"/>
        <result column="financing_amount" property="financingAmount"/>
        <result column="apr" property="apr"/>
        <result column="day_apr" property="dayApr"/>
        <result column="repayment_date" property="repaymentDate"/>
        <result column="financing_term" property="financingTerm"/>
        <result column="user_id" property="userId"/>
        <result column="day" property="day"/>
        <result column="reject_reason" property="rejectReason"/>
    </resultMap>


    <select id="selectPurchaseInformationPage" resultMap="purchaseInformationResultMap">
        select * from jrzh_purchase_information where is_deleted = 0
    </select>

</mapper>
