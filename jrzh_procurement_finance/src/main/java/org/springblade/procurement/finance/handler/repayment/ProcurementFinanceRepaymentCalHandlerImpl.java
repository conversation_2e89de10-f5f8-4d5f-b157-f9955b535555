package org.springblade.procurement.finance.handler.repayment;

import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.EnterpriseTypeEnum;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.handler.IFinanceRepaymentCalHandler;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 代采融资-还款试算
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-27  14:02
 * @Description: 代采融资-还款试算
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ProcurementFinanceRepaymentCalHandlerImpl implements IFinanceRepaymentCalHandler {
    private final IEnterpriseQuotaService enterpriseQuotaService;

    @Override
    public PlatformExpensesEnum supportType() {
        return PlatformExpensesEnum.PLAT_TYPE_PURCHASE;
    }

    @Override
    public ExpenseConstant.FeeNodeEnum supportFee() {
        return ExpenseConstant.FeeNodeEnum.FINANCE_APPLY;
    }

    @Override
    public void fullFieldExpenseRuleDTO(CostCalculusDto costCalculusDto, ExpenseRuleDTO expenseRuleDTO) {
        Long goodsId = costCalculusDto.getGoodsId();
        EnterpriseQuotaVO enterpriseQuotaVO = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), costCalculusDto.getUserId());
        expenseRuleDTO.setPrincipalRepayment(costCalculusDto.getFinanceAmount());
        expenseRuleDTO.setLoanPrincipal(costCalculusDto.getFinanceAmount());
        //计算时 按正常还款时间算 也就是到结束时
        expenseRuleDTO.setInterestAccrualDays(BigDecimal.valueOf(costCalculusDto.getLoanDay()));
        expenseRuleDTO.setLoanDays(BigDecimal.valueOf(costCalculusDto.getLoanDay()));
        //随借随还仅有一期
        expenseRuleDTO.setLoanPeriods(BigDecimal.valueOf(1));
        expenseRuleDTO.setYearRate(enterpriseQuotaVO.getAnnualInterestRate()
                .divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
        expenseRuleDTO.setServiceRate(enterpriseQuotaVO.getServiceRate()
                .divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
        expenseRuleDTO.setDayRate(enterpriseQuotaVO.getDailyInterestRate()
                .divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
        expenseRuleDTO.setMarginRatio(enterpriseQuotaVO.getBondProportion()
                .divide(CommonConstant.ONE_HUNDRED, 5, CommonConstant.NUMBER_STRATEGY));
    }

    @Override
    public void fullFieldExpenseRuleDTOTerm(CostCalculusDto costCalculusDto, ExpenseRuleDTO expenseRuleDTO, StagRecordVO stagRecordVO) {

    }
}
