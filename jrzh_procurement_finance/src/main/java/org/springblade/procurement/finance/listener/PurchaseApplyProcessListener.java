package org.springblade.procurement.finance.listener;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springblade.common.enums.*;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.mapper.CustomerGoodsMapper;
import org.springblade.customer.service.ICustomerCertificateInfoService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IBillFinancialFlowService;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.message.enums.MessageSceneEnum;
import org.springblade.message.utils.MessageNotifyUtil;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.procurement.finance.constant.PurchaseConstant;
import org.springblade.procurement.finance.constant.PurchaseMessageTypeEnum;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.service.IPurchaseFinanceApplyService;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.moudle.timing.service.IGoodsTimingService;
import org.springblade.refund.service.IRefundBizService;
import org.springblade.warehouse.entity.RedemptionWarehouseEntering;
import org.springblade.warehouse.service.IRedemptionWarehouseEnteringService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代采--融资申请监听
 *
 * <AUTHOR>
 */
@Component("purchaseApplyProcessListener")
@RequiredArgsConstructor
public class PurchaseApplyProcessListener implements ExecutionListener {


    private final IFinanceApplyService financeApplyService;
    private final ILoanManageIouService loanManageIouService;
    private final IExpenseOrderDetailService platformExpensesService;
    private final IExpenseOrderService billExpenseOrderService;
    private final IBillFinancialFlowService billFinancialFlowService;
    /**
     * 客户产品
     */
    private final CustomerGoodsMapper customerGoodsMapper;
    /**
     * 已开通产品额度
     */
    private final IEnterpriseQuotaService enterpriseQuotaService;

    /**
     * 保证金 服务类
     */
    private final IExpenseDepositService cashDepositService;

    /**
     * 待入库 服务类
     */
    private final IRedemptionWarehouseEnteringService redemptionWarehouseEnteringService;
    private final IBusinessProcessProgressService businessProcessProgressService;

    private final IWfProcessService processService;

    private final ICustomerCertificateInfoService customerCertificateInfoService;

    private final RuntimeService runtimeService;

    private final RabbitMsgSender rabbitMsgSender;

    private final IGoodsTimingService goodsTimingService;

    private final IRefundBizService refundService;
    private final IPurchaseFinanceApplyService purchaseFinanceApplyService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        String processInstanceId = delegateExecution.getProcessInstanceId();
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            businessProcessProgressService.updateStatus(processInstanceId, ProcessStatusEnum.TERMINAL.getCode());
            handlerProcessTerminal(delegateExecution);
        } else {
            businessProcessProgressService.updateStatus(processInstanceId, ProcessStatusEnum.FINISH.getCode());
            handlerSuccess(delegateExecution);
        }
    }

    private void handlerSuccess(DelegateExecution delegateExecution) {

        //获取融资编号
        String financeNo = delegateExecution.getVariable(ProcessConstant.FINANCE_NO, String.class);
        //放款方式
        Integer lendingMethod = delegateExecution.getVariable(PurchaseConstant.LENDING_METHOD, Integer.class);
        //收费方式
        Integer chargeMethod = delegateExecution.getVariable(PurchaseConstant.CHARGE_METHOD, Integer.class);
        //客户产品id
        Long customerGoodsId = delegateExecution.getVariable(ProcessConstant.CUSTOMER_GOODS_ID, Long.class);
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financeNo);

        if (GoodsEnum.AUTO_LENDING.getCode().equals(lendingMethod)) {
            // 1、额度扣除
            int type = PurchaseEnum.PURCHASE_PASS_TYPE_THREE.getCode();
            purchaseDeductionAmount(customerGoodsId, financeApply, type);


            //2、主数据状态改变、创建借款记录
            int status = PurchaseEnum.PURCHASE_STATUS_SIX.getCode();
            updateFinanceApplyStatus(financeApply, status);

//            // 保存借据单
//            LoanManageIou loanManageIou = loanManageIouService.saveLoanManageIou(financeApply);
//            // 保存还款计划
//            purchaseFinanceApplyService.savePurchaseRepaymentPlan(loanManageIou, financeApply, delegateExecution);
            repaymentPlanFinanceApplyBizService.saveRepaymentPlanByFinanceApply(financeApply,
                    PlatformExpensesEnum.PLAT_TYPE_PURCHASE.getCode(), financeApply.getFinanceNo(), ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode(), true);
            //3、保证金数据赋值
//            cashDepositExtracted(delegateExecution, financeNo, financeApply);

            //4、创建待入库记录
            PurchaseInformationVO purchaseInformationVO = JSONUtil.toBean(JSONUtil.toJsonStr(delegateExecution.getVariable(ProcessConstant.FINANCE_APPLY)), PurchaseInformationVO.class);
            redemptionWarehouseEnteringSave(financeApply, purchaseInformationVO);

            //保存补充资料 、status 0、保存 1、作废
            saveCertificateInfo(delegateExecution, 0);
            //自动放款，判断是 1 资方统一收费 还是 2 平台资方单独收取
//            if (GoodsEnum.ALONE.getCode().equals(chargeMethod)) {
//                //财务明细 新增 平台费用
//                saveFinancialFlow(financeApply, delegateExecution);
//                //费用订单状态修改
//                updateExpenseOrder(financeApply, delegateExecution, 1);
//            }
            //代采-自动放款-成功通知
            MessageNotifyUtil.notifyByTemplate(financeApply.getUserId(), MessageSceneEnum.PURCHASE_LoanSuc.getValue());
        } else {
            //手动放款
            // 保存平台费用
            Object variable = delegateExecution.getVariable(ProcessConstant.PLATFORM_EXPENSES);
            //保存费用订单
			/*String noStr = "";
			if (Objects.nonNull(variable)) {
				List<ExpenseOrder> billExpenseOrders = billExpenseOrderService.savebillExpenseOrder(financeApply);
				noStr = billExpenseOrders.stream().map(ExpenseOrder::getBillExpenseNo).collect(Collectors.joining(","));
				//保存费用单号给 流程变量
				runtimeService.setVariable(delegateExecution.getProcessInstanceId(), PurchaseConstant.BILL_EXPENSE_NO, noStr);
			}*/

//            //费用单号
//            String billExpenseNo = CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO);
//            if (Objects.nonNull(variable)) {
//                List<ExpenseOrderDetail> expensesList = JSONUtil.toList(JSONUtil.toJsonStr(variable), ExpenseOrderDetail.class);
//                for (ExpenseOrderDetail platformExpenses : expensesList) {
//                    platformExpenses.setBillExpenseNo(billExpenseNo);
//                }
//                platformExpensesService.updateBatchById(expensesList);
//                // 保存费用订单
//                ExpenseOrder billExpenseOrderCheck = new ExpenseOrder();
//                billExpenseOrderCheck.setCustomerId(financeApply.getUserId());
//                billExpenseOrderCheck.setFinanceNo(financeApply.getFinanceNo());
//                billExpenseOrderCheck.setBillExpenseNo(billExpenseNo);
//                billExpenseOrderCheck.setPaymentMethod(BillConstant.PAYMENT_METHOD_ONE);
//                billExpenseOrderCheck.setPaymentStatus(BillConstant.PAYMENT_STATUS_ONE);
//                billExpenseOrderService.saveExpenseOrder(billExpenseOrderCheck, expensesList);
//                //保存费用单号给 流程变量
//                runtimeService.setVariable(delegateExecution.getProcessInstanceId(), PurchaseConstant.BILL_EXPENSE_NO, billExpenseNo);
//                // 保存交易流水
////				saveFinancialFlow(financeApply,expensesList,delegateExecution);
//            }

//            //创建保证金订单
//            toPaidSave(financeApply);
            int status = PurchaseEnum.PURCHASE_STATUS_FOUR.getCode();
            updateFinanceApplyStatus(financeApply, status);
            // 发送mq延时消息，超时未代采申请待确认作废该申请
            Map<String, Object> map = new HashMap<>();
            EnterpriseQuota enterpriseQuota = getEnterpriseQuota(customerGoodsId);
            map.put("enterpriseQuota", enterpriseQuota);
            map.put("financeApplyId", financeApply.getId());
            map.put("amount", financeApply.getAmount());
//            map.put("billExpenseNo", billExpenseNo);
            map.put("financeNo", financeApply.getFinanceNo());
            String message = JSONUtil.toJsonStr(map);
            // 发送mq延时消息，超时未申请放款关闭该申请
            Integer seconds = goodsTimingService.getSecondsByGoodsIdAndType(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_FINANCING_APPLY.getCode());
            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
                    .messageType(PurchaseMessageTypeEnum.FINANCE_APPLY_UN_LOAN.getCode())
                    .msg(message)
                    .seconds(seconds)
                    .extendParam(new HashMap<>())
                    .build());
        }
    }


    /**
     * 补充资料保存
     *
     * @param delegateExecution
     * @param status            0保存资料 1、作废资料
     */
    private void saveCertificateInfo(DelegateExecution delegateExecution, int status) {
        Map<String, Object> variables = delegateExecution.getVariables();
        // 保存证件资料
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoricTaskInstance historicTaskInstance = processService.selectCurrentTask(processInstanceId);
        variables.put(ProcessConstant.TASK_ID, historicTaskInstance.getId());
        variables.put(ProcessConstant.PROCESS_INSTANCE_ID, processInstanceId);
        customerCertificateInfoService.saveCertificateInfo(variables, status);
    }

    /**
     * 创建待入库数据
     *
     * @param financeApply          主数据
     * @param purchaseInformationVO 代采信息
     */
    private void redemptionWarehouseEnteringSave(FinanceApply financeApply, PurchaseInformationVO purchaseInformationVO) {
        RedemptionWarehouseEntering redemptionWarehouseEntering = null;
        List<RedemptionWarehouseEntering> redemptionWarehouseEnteringList = new ArrayList<>();
        for (PurchaseCommodity purchaseCommodity : purchaseInformationVO.getPurchaseCommodityList()) {
            redemptionWarehouseEntering = new RedemptionWarehouseEntering();
            //融资编号
            redemptionWarehouseEntering.setFinanceNo(purchaseInformationVO.getFinanceNo());
            //供应商id
            redemptionWarehouseEntering.setSupplierId(purchaseCommodity.getSupplierId());
            //单位
            redemptionWarehouseEntering.setGoodsUnitValue(purchaseCommodity.getUnitName());
            //待入库数量
            redemptionWarehouseEntering.setReadyToStorage(purchaseCommodity.getQuantity());
            //采购单价(元)
            redemptionWarehouseEntering.setPurchasePrice(purchaseCommodity.getPurchasePrice());
            //融资单价(元)
            redemptionWarehouseEntering.setFinancingPrice(purchaseCommodity.getFinancingPrice());
            //最迟交付日
            redemptionWarehouseEntering.setLatestDelivery(purchaseInformationVO.getDeliverTime());
            //商品图片地址
            redemptionWarehouseEntering.setLogo(purchaseCommodity.getCommodityUrl());
            //商品id
            redemptionWarehouseEntering.setGoodsId(purchaseCommodity.getCommodityId());
            //融资企业id
            redemptionWarehouseEntering.setCompanyId(financeApply.getUserId());
            //规格型号
            redemptionWarehouseEntering.setGoodsSpec(purchaseCommodity.getSpec());
            //单价
            redemptionWarehouseEntering.setUnitPrice(purchaseCommodity.getUnitPrice());
            //添加
            redemptionWarehouseEnteringList.add(redemptionWarehouseEntering);
        }
        redemptionWarehouseEnteringService.saveList(redemptionWarehouseEnteringList);
    }

    /**
     * 修改主数据 状态
     *
     * @param financeApply
     * @param status
     */
    private void updateFinanceApplyStatus(FinanceApply financeApply, int status) {
        financeApply.setStatus(status);
        financeApply.setPassTime(LocalDateTime.now());
        financeApply.setExpireTime(LocalDateTime.now().plusDays(financeApply.getLoadTerm()));
        LocalDateTime countdownExpireTime = goodsTimingService.getByExpireTime(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_FINANCING_APPLY.getCode());
        financeApply.setCountdownExpireTime(countdownExpireTime);
        financeApplyService.updateById(financeApply);
    }

    /**
     * 融资申请终止后进行业务处理
     *
     * @param delegateExecution
     */
    private void handlerProcessTerminal(DelegateExecution delegateExecution) {
        //获取融资编号
        String financeNo = delegateExecution.getVariable(ProcessConstant.FINANCE_NO, String.class);
        //放款方式
        Integer lendingMethod = delegateExecution.getVariable(PurchaseConstant.LENDING_METHOD, Integer.class);
        //收费方式
        Integer chargeMethod = delegateExecution.getVariable(PurchaseConstant.CHARGE_METHOD, Integer.class);
        //客户产品id
        Long customerGoodsId = delegateExecution.getVariable(ProcessConstant.CUSTOMER_GOODS_ID, Long.class);
//        Integer cashDepositPaymentStatus = delegateExecution.getVariable("cashDepositPaymentStatus", Integer.class);
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financeNo);
        //判断是手动放款 还是自动放款的融资申请
        if (GoodsEnum.AUTO_LENDING.getCode().equals(lendingMethod)) {
			/*
			  自动放款 终止流程
			  1、判断是 1 资方统一收费（没有费用订单的修改） 2 平台资方单独收取
			  	1、扣减额度
			  	2、保证金订单状态修改为 已关闭
			  	3、费用订单状态修改为 已关闭（平台资方单独收取 才会执行这个流程）
			  	4、代采主数据状态修改为已作废
			 */
//            if (GoodsEnum.UNIFIED.getCode().equals(chargeMethod)) {
//                cashDepositExtracted(delegateExecution, financeNo, cashDepositPaymentStatus, financeApply);
//            } else {
//                //保证金订单状态修改为 已关闭
//                cashDepositExtracted(delegateExecution, financeNo, cashDepositPaymentStatus, financeApply);
//                //费用订单关闭
////				billExpenseOrderService.updateExpenseClosure(financeNo);
//                updateExpenseOrder(financeApply, delegateExecution, 2);
//            }
            MessageNotifyUtil.notifyByTemplate(financeApply.getUserId(), MessageSceneEnum.PURCHASE_LoanFail.getValue());
        }
        // 额度扣除
        int type = PurchaseEnum.PURCHASE_PASS_TYPE_TWO.getCode();
        purchaseDeductionAmount(customerGoodsId, financeApply, type);
        //补充资料作废
        saveCertificateInfo(delegateExecution, 1);
        // 更新融资申请状态
        financeApply.setStatus(PurchaseEnum.PURCHASE_STATUS_TWO.getCode());
        financeApplyService.updateById(financeApply);
    }

//    private void cashDepositExtracted(DelegateExecution delegateExecution, String financeNo, Integer cashDepositPaymentStatus, FinanceApply financeApply) {
//        if (cashDepositPaymentStatus.equals(CashDepositTypeEnum.TWO_GUARANTRR.getCode())) {
//            ExpenseDeposit cashDepositDTO = cashDepositExtracted(delegateExecution, financeNo, financeApply);
//            Refund refund = new Refund();
//            refund.setRefundOrderNo(CodeUtil.generateCode(CodeEnum.REFUND_NO));
//            refund.setRefundType(2);
//            refund.setBillExpenseNo(cashDepositDTO.getCashDepositNo());
//            refund.setRefundAmount(cashDepositDTO.getPayableAmount());
//            refund.setUserId(cashDepositDTO.getFinancingUserId());
//            refund.setCashDepositRate(new BigDecimal(100));
//            refund.setPaymentMethod(cashDepositDTO.getPayType().toString());
//            refundService.save(refund);
//        } else {
//            //保证金订单状态修改为 已关闭
//            cashDepositService.closureUpdate(financeNo);
//        }
//    }

    /**
     * 额度
     *
     * @param customerGoodsId 客户产品id
     * @param financeApply    融资主数据
     * @param type            1、代采申请中：可用额度   减去 融资金额 然后  申请中的额度 加上 融资金额
     *                        2、驳回：     申请中额度 减去 融资金额 然后 可用额度 加上 融资金额
     *                        3、通过：     申请中额度 减去 融资金额 然后 已用额度 加上 融资金额
     */
    private void purchaseDeductionAmount(Long customerGoodsId, FinanceApply financeApply, int type) {
        // 扣减额度
        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(customerGoodsId);
        enterpriseQuotaService.subtractReceivableAmount(financeApply.getAmount(), enterpriseQuota, type);
    }

    private EnterpriseQuota getEnterpriseQuota(Long customerGoodsId) {
        CustomerGoods customerGoods = customerGoodsMapper.selectById(customerGoodsId);
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
        return enterpriseQuota;
    }

    /**
     * 创建保证金订单
     *
     * @param financeApply 主数据信息
     */
    private void toPaidSave(FinanceApply financeApply) {
        ExpenseDeposit cashDeposit = new ExpenseDeposit();
        cashDeposit.setFinancingNo(financeApply.getFinanceNo());
        cashDeposit.setCashDepositType(1);
        cashDeposit.setFinancingUserId(financeApply.getUserId());
        BigDecimal bondPayProportion = NumberUtil.div(financeApply.getBondPayProportion(), 100);
        BigDecimal payableAmount = NumberUtil.mul(financeApply.getAmount(), bondPayProportion).setScale(2, BigDecimal.ROUND_HALF_EVEN);
        cashDeposit.setPayableAmount(payableAmount);
        cashDeposit.setPayNode(1);
        cashDeposit.setCashDepositRate(financeApply.getBondPayProportion());
        cashDeposit.setRefundType(1);
        cashDeposit.setPayType(1);
        cashDeposit.setGoodsId(financeApply.getGoodsId());
        cashDeposit.setStatus(CashDepositTypeEnum.ONE_PAID.getCode());
        cashDepositService.toPaidSave(cashDeposit);
    }

    /**
     * 平台费用财务明细
     *
     * @param financeApply      主数据
     * @param delegateExecution 变量
     */
    private void saveFinancialFlow(FinanceApply financeApply, DelegateExecution delegateExecution) {
        //billExpenseOrderService.getByFinanceApplyNoAndType(financeApply.getFinanceNo(),ProcessTypeEnum.)
        List<ExpenseOrderDetail> platformExpenses = platformExpensesService.getByFinanceNo(financeApply.getFinanceNo());
        String outerPayNo = delegateExecution.getVariable(ProcessConstant.OUTER_PAY_NO, String.class);
        BigDecimal amount = platformExpenses.stream()
                .map(ExpenseOrderDetail::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        billFinancialFlowService.saveFinancialFlow(financeApply, amount, outerPayNo, null);
    }

    /**
     * 保证金财务明细
     *
     * @param financeApply 主数据
     * @param cashDeposit  保证金数据
     */
    private void saveCashDepositFlow(FinanceApply financeApply, ExpenseDeposit cashDeposit) {
        billFinancialFlowService.saveFinancialFlow(financeApply, cashDeposit.getPayedAmount(), null, null);
    }
}
