package org.springblade.procurement.finance.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.entity.LoanApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.service.ILoanApplyService;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;

/**
 * 流程发起监听器
 *
 * <AUTHOR>
 */
@Component("financeApplyStartListener")
@RequiredArgsConstructor
public class ProcessStartListener implements TaskListener {
    private final IFinanceApplyService financeApplyService;
    private final ILoanApplyService loanApplyService;

    @Override
    public void notify(DelegateTask delegateTask) {
        String status = delegateTask.getVariable(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, String.class);
        Integer processType = delegateTask.getVariable(ProcessConstant.PROCESS_TYPE, Integer.class);
        Integer goodsType = delegateTask.getVariable(ProcessConstant.GOODS_TYPE, Integer.class);
        if (!WfProcessConstant.STATUS_REJECT.equals(status)) {
            doPass(delegateTask, processType, goodsType);
        }
    }

    private void doPass(DelegateTask delegateTask, Integer processType, Integer goodsType) {
        //代采业务
        if (GoodsEnum.AGENT_PURCHASE_FINANCING.getCode().equals(goodsType)) {
            Long businessId = delegateTask.getVariable(ProcessConstant.FINANCE_APPLY_ID, Long.class);
            String processInstanceId = delegateTask.getProcessInstanceId();
            if (PurchaseEnum.PURCHASE_FINANCE_APPLY.getCode().equals(processType)) {
                updateFinance(businessId, PurchaseEnum.PURCHASE_STATUS_ONE.getCode(), processInstanceId);
                updateLoanApplyPorcessInstance(businessId, processInstanceId);
            } else if (PurchaseEnum.PURCHASE_LOAN_CONFIRM.getCode().equals(processType)) {
                updateFinance(businessId, PurchaseEnum.PURCHASE_STATUS_FIVE.getCode(), processInstanceId);
                updateLoanApplyPorcessInstance(businessId, processInstanceId);
            } else if (PurchaseEnum.PURCHASE_AUTO_LOAN.getCode().equals(processType)) {
                updateLoanApplyPorcessInstance(businessId, processInstanceId);
                updateFinance(businessId, PurchaseEnum.PURCHASE_STATUS_ONE.getCode(), processInstanceId);
            }
        }
    }

    private void updateLoanApplyPorcessInstance(Long businessId, String processInstanceId) {
        loanApplyService.update(Wrappers.<LoanApply>lambdaUpdate().eq(LoanApply::getFinanceApplyId, businessId)
                .orderByDesc(LoanApply::getCreateTime).last("limit 1").set(LoanApply::getProcessInstanceId, processInstanceId));
    }

    private boolean updateFinance(Long businessId, Integer typeStatus, String processInstanceId) {
        return financeApplyService.update(Wrappers.<FinanceApply>lambdaUpdate().set(FinanceApply::getProcessInstanceId, processInstanceId).set(FinanceApply::getStatus, typeStatus).eq(FinanceApply::getId, businessId));
    }

}
