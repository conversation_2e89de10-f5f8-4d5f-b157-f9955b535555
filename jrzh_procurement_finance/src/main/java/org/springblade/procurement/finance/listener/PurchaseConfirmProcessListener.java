package org.springblade.procurement.finance.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.mapper.CustomerGoodsMapper;
import org.springblade.customer.service.ICustomerCertificateInfoService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.service.IQuotaUseDetailsService;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.constant.BillConstant;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IBillFinancialFlowService;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.message.enums.MessageSceneEnum;
import org.springblade.message.service.MessageSendService;
import org.springblade.message.utils.MessageNotifyUtil;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.utils.WorkflowVariableUtil;
import org.springblade.procurement.finance.constant.PurchaseConstant;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.service.IPurchaseFinanceApplyService;
import org.springblade.procurement.finance.vo.PurchaseInformationVO;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.service.IRefundBizService;
import org.springblade.resource.entity.Attach;
import org.springblade.warehouse.entity.RedemptionWarehouseEntering;
import org.springblade.warehouse.service.IRedemptionWarehouseEnteringService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 代采--融资确认监听
 */
@Component("purchaseConfirmProcessListener")
@RequiredArgsConstructor
public class PurchaseConfirmProcessListener implements ExecutionListener {


    private final IFinanceApplyService financeApplyService;
    private final ILoanManageIouService loanManageIouService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IExpenseOrderDetailService platformExpensesService;
    private final IExpenseOrderService billExpenseOrderService;
    private final IBillFinancialFlowService billFinancialFlowService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final IPurchaseFinanceApplyService purchaseFinanceApplyService;

    /**
     * 客户产品
     */
    private final CustomerGoodsMapper customerGoodsMapper;
    /**
     * 已开通产品额度
     */
    private final IEnterpriseQuotaService enterpriseQuotaService;

    /**
     * 保证金 服务类
     */
    private final IExpenseDepositService cashDepositService;

    /**
     * 待入库 服务类
     */
    private final IRedemptionWarehouseEnteringService redemptionWarehouseEnteringService;

    private final IWfProcessService processService;

    private final ICustomerCertificateInfoService customerCertificateInfoService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;

    private final IRefundBizService refundService;

    private final IQuotaUseDetailsService quotaUseDetailsService;
    private final MessageSendService messageSendService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        String processInstanceId = delegateExecution.getProcessInstanceId();
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            businessProcessProgressService.updateStatus(processInstanceId, ProcessStatusEnum.TERMINAL.getCode());
            handlerProcessTerminal(delegateExecution);
        } else {
            businessProcessProgressService.updateStatus(processInstanceId, ProcessStatusEnum.FINISH.getCode());
            handlerSuccess(delegateExecution);
        }
    }

    private void handlerSuccess(DelegateExecution delegateExecution) {

        //获取融资编号
        String financeNo = delegateExecution.getVariable(ProcessConstant.FINANCE_NO, String.class);
        //客户产品id
        Long customerGoodsId = delegateExecution.getVariable(ProcessConstant.CUSTOMER_GOODS_ID, Long.class);
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financeNo);
        // 1、额度扣除
        int type = PurchaseEnum.PURCHASE_PASS_TYPE_THREE.getCode();
        purchaseDeductionAmount(customerGoodsId, financeApply, type);


        //2、主数据状态改变、创建借款记录
        int status = PurchaseEnum.PURCHASE_STATUS_SIX.getCode();
        updateFinanceApplyStatus(financeApply, status);
//        // 保存借据单
//        LoanManageIou loanManageIou = loanManageIouService.saveLoanManageIou(financeApply);
        // 保存还款计划
        repaymentPlanFinanceApplyBizService.saveRepaymentPlanByFinanceApply(financeApply, PlatformExpensesEnum.PLAT_TYPE_PURCHASE.getCode(), financeApply.getFinanceNo(), null,true);
//        purchaseFinanceApplyService.savePurchaseRepaymentPlan(loanManageIou, financeApply, delegateExecution);

//        //3、保证金数据赋值
//        cashDepositExtracted(delegateExecution, financeNo, financeApply);

        //4、创建待入库记录
        PurchaseInformationVO purchaseInformationVO = JSONUtil.toBean(JSONUtil.toJsonStr(delegateExecution.getVariable(ProcessConstant.FINANCE_APPLY)), PurchaseInformationVO.class);
        redemptionWarehouseEnteringSave(financeApply, purchaseInformationVO);
//        //财务明细 新增 平台费用
//        saveFinancialFlow(financeApply, delegateExecution);
//        //费用订单状态修改
//        updateExpenseOrder(financeApply, delegateExecution, 1);
        //流程通过，新增补充资料
        saveCertificateInfo(delegateExecution, 0);
        //更新额度使用历史表
        Integer code = QuotaUseDetailsEnum.APPLY_QUOTA.getCode();
        Long quotaUseDetailsId = financeApply.getQuotaUseDetailsId();
        quotaUseDetailsService.updateStatus(quotaUseDetailsId, code);
        //代采-手动放款-成功通知
        MessageNotifyUtil.notifyByTemplate(financeApply.getUserId(), MessageSceneEnum.PURCHASE_LoanSuc.getValue());

    }



    /**
     * 补充资料保存
     *
     * @param delegateExecution
     * @param status            0保存资料 1、作废资料
     */
    private void saveCertificateInfo(DelegateExecution delegateExecution, int status) {
        Map<String, Object> variables = delegateExecution.getVariables();
        // 保存证件资料
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoricTaskInstance historicTaskInstance = processService.selectCurrentTask(processInstanceId);
        variables.put(ProcessConstant.TASK_ID, historicTaskInstance.getId());
        variables.put(ProcessConstant.PROCESS_INSTANCE_ID, processInstanceId);
        customerCertificateInfoService.saveCertificateInfo(variables, status);
    }

    /**
     * 创建待入库数据
     *
     * @param financeApply          主数据
     * @param purchaseInformationVO 代采信息
     */
    private void redemptionWarehouseEnteringSave(FinanceApply financeApply, PurchaseInformationVO purchaseInformationVO) {
        RedemptionWarehouseEntering redemptionWarehouseEntering = null;
        List<RedemptionWarehouseEntering> redemptionWarehouseEnteringList = new ArrayList<>();
        for (PurchaseCommodity purchaseCommodity : purchaseInformationVO.getPurchaseCommodityList()) {
            redemptionWarehouseEntering = new RedemptionWarehouseEntering();
            //融资编号
            redemptionWarehouseEntering.setFinanceNo(purchaseInformationVO.getFinanceNo());
            //供应商id
            redemptionWarehouseEntering.setSupplierId(purchaseCommodity.getSupplierId());
            //单位
            redemptionWarehouseEntering.setGoodsUnitValue(purchaseCommodity.getUnitName());
            //待入库数量
            redemptionWarehouseEntering.setReadyToStorage(purchaseCommodity.getQuantity());
            //采购单价(元)
            redemptionWarehouseEntering.setPurchasePrice(purchaseCommodity.getPurchasePrice());
            //融资单价(元)
            redemptionWarehouseEntering.setFinancingPrice(purchaseCommodity.getFinancingPrice());
            //最迟交付日
            redemptionWarehouseEntering.setLatestDelivery(purchaseInformationVO.getDeliverTime());
            //商品图片地址
            redemptionWarehouseEntering.setLogo(purchaseCommodity.getCommodityUrl());
            //商品id
            redemptionWarehouseEntering.setGoodsId(purchaseCommodity.getCommodityId());
            //融资企业id
            redemptionWarehouseEntering.setCompanyId(financeApply.getUserId());
            //规格型号
            redemptionWarehouseEntering.setGoodsSpec(purchaseCommodity.getSpec());
            //规格型号
            redemptionWarehouseEntering.setGoodsSpec(purchaseCommodity.getSpec());
            //单价
            redemptionWarehouseEntering.setUnitPrice(purchaseCommodity.getUnitPrice());
            //添加
            redemptionWarehouseEnteringList.add(redemptionWarehouseEntering);
        }
        redemptionWarehouseEnteringService.saveList(redemptionWarehouseEnteringList);
    }

    /**
     * 修改主数据 状态
     *
     * @param financeApply
     * @param status
     */
    private void updateFinanceApplyStatus(FinanceApply financeApply, int status) {
        financeApply.setStatus(status);
        financeApply.setPassTime(LocalDateTime.now());
        financeApply.setExpireTime(LocalDateTime.now().plusDays(financeApply.getLoadTerm()));
        financeApplyService.updateById(financeApply);
    }

    /**
     * 融资申请终止后进行业务处理
     *
     * @param delegateExecution
     */
    private void handlerProcessTerminal(DelegateExecution delegateExecution) {
        //获取融资编号
        String financeNo = delegateExecution.getVariable(ProcessConstant.FINANCE_NO, String.class);
        //客户产品id
        Long customerGoodsId = delegateExecution.getVariable(ProcessConstant.CUSTOMER_GOODS_ID, Long.class);
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financeNo);
//        Integer paymentStatus = (Integer) delegateExecution.getVariable("paymentStatus");
//        Integer cashDepositPaymentStatus = delegateExecution.getVariable("cashDepositPaymentStatus", Integer.class);
//        // 如果还款状态是已还款，保存退款订单
//        updateExpenseOrder(financeApply, delegateExecution, 2);
//
//        if (cashDepositPaymentStatus.equals(CashDepositTypeEnum.TWO_GUARANTRR.getCode())) {
//            ExpenseDepositDTO cashDepositDTO = cashDepositExtracted(delegateExecution, financeNo, financeApply);
//            Refund refund = new Refund();
//            refund.setRefundOrderNo(CodeUtil.generateCode(CodeEnum.REFUND_NO));
//            refund.setRefundType(2);
//            refund.setBillExpenseNo(cashDepositDTO.getCashDepositNo());
//            refund.setRefundAmount(cashDepositDTO.getPayableAmount());
//            refund.setUserId(cashDepositDTO.getFinancingUserId());
//            refund.setCashDepositRate(new BigDecimal(100));
//            refund.setPaymentMethod(cashDepositDTO.getPayType().toString());
//            refundService.save(refund);
//        } else {
//            //保证金订单状态修改为 已关闭
//            cashDepositService.closureUpdate(financeNo);
//        }

        int type = PurchaseEnum.PURCHASE_PASS_TYPE_TWO.getCode();
        purchaseDeductionAmount(customerGoodsId, financeApply, type);

        //流程终止，作废补充资料
        saveCertificateInfo(delegateExecution, 1);
        // 更新融资申请状态
        financeApply.setStatus(PurchaseEnum.PURCHASE_STATUS_TWO.getCode());
        financeApplyService.updateById(financeApply);

        //更新额度使用历史表
        Integer code = QuotaUseDetailsEnum.APPLY_FAIL.getCode();
        Long quotaUseDetailsId = financeApply.getQuotaUseDetailsId();
        quotaUseDetailsService.updateStatus(quotaUseDetailsId, code);
        //代采-手动放款-审核未通过通知
        MessageNotifyUtil.notifyByTemplate(financeApply.getUserId(), MessageSceneEnum.PURCHASE_LoanFail.getValue());
    }

    /**
     * 额度
     *
     * @param customerGoodsId 客户产品id
     * @param financeApply    融资主数据
     * @param type            1、代采申请中：可用额度   减去 融资金额 然后  申请中的额度 加上 融资金额
     *                        2、驳回：     申请中额度 减去 融资金额 然后 可用额度 加上 融资金额
     *                        3、通过：     申请中额度 减去 融资金额 然后 已用额度 加上 融资金额
     */
    private void purchaseDeductionAmount(Long customerGoodsId, FinanceApply financeApply, int type) {
        CustomerGoods customerGoods = customerGoodsMapper.selectById(customerGoodsId);
        // 扣减额度
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
        enterpriseQuotaService.subtractReceivableAmount(financeApply.getAmount(), enterpriseQuota, type);
    }

    /**
     * 平台费用财务明细
     *
     * @param financeApply      主数据
     * @param delegateExecution 变量
     */
    private void saveFinancialFlow(FinanceApply financeApply, DelegateExecution delegateExecution) {
        List<ExpenseOrderDetail> platformExpenses = platformExpensesService.getByFinanceNo(financeApply.getFinanceNo());
        String outerPayNo = delegateExecution.getVariable(ProcessConstant.OUTER_PAY_NO, String.class);
        BigDecimal amount = platformExpenses.stream()
                .map(ExpenseOrderDetail::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        billFinancialFlowService.saveFinancialFlow(financeApply, amount, outerPayNo, null);
    }

    /**
     * 保证金财务明细
     *
     * @param financeApply 主数据
     * @param cashDeposit  保证金数据
     */
    private void saveCashDepositFlow(FinanceApply financeApply, ExpenseDeposit cashDeposit) {
        billFinancialFlowService.saveFinancialFlow(financeApply, cashDeposit.getPayedAmount(), null, null);
    }
}
