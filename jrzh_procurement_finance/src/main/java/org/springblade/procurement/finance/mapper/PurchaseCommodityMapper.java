
package org.springblade.procurement.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseMapper;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.procurement.finance.vo.PurchaseCommodityVO;

import java.util.List;

/**
 * 代采--采购商品 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface PurchaseCommodityMapper extends BaseMapper<PurchaseCommodity>, MPJBaseMapper<PurchaseCommodity> {

    /**
     * 自定义分页
     *
     * @param page
     * @param purchaseCommodity
     * @return
     */
    List<PurchaseCommodityVO> selectPurchaseCommodityPage(IPage page, PurchaseCommodityVO purchaseCommodity);

}
