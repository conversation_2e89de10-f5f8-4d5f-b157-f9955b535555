/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.jrzh_order_financing_goods_api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.product.common.entity.BaseProduct;

import java.math.BigDecimal;

/**
 * 产品表实体类
 *
 * <AUTHOR>
 * @since 2021-12-24
 */
@Data
@TableName("jrzh_goods")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "orderFinancingGoods对象", description = "订单融资产品类")
public class OrderFinancingGoods extends BaseProduct {

	private static final long serialVersionUID = 1L;

	/**
	 * 计费方式
	 */
	@ApiModelProperty(value = "计费方式  1等额本息 2等额本金 3先息后本")
	private String billingMethod;

	/**
	 * 核心企业授信表单id
	 */
	@ApiModelProperty(value = "核心企业授信表单")
	private Long coreCreditFormId;

	/**
	 * 保证金支付比例
	 */
	@ApiModelProperty(value = "保证金支付比例")
	private BigDecimal bondPayProportion;

	@ApiModelProperty(value = "银行卡代收类型")
	private Integer bankCardCollectionType;

	@ApiModelProperty("最低可贷成数")
	private BigDecimal loanableStart;
	@ApiModelProperty("最高可贷成数")
	private BigDecimal loanableEnd;

	/**
	 * 商品白名单id
	 */
	private Long commodityWhiteListId;
	/**
	 * GoodsEnum.UNIFIED
	 */
	@ApiModelProperty("收费方式 1 资方统一收费 2 平台资方单独收取")
	private Integer chargeMethod;

	@TableField(exist = false)
	private BigDecimal bondPayProportionStart;

	@ApiModelProperty("随借随还-计息天数")
	private Integer interestDay;

	@ApiModelProperty(value = "是否支持展期 0不支持 1支持")
	private Integer isDelay;

	@ApiModelProperty(value = "展期类型 1 分期 2最低 3延迟 多选逗号隔开")
	private String delayType;

	@ApiModelProperty(value = "展期最低利率")
	private BigDecimal delayInterestRateMin;

	@ApiModelProperty(value = "展期最高利率")
	private BigDecimal delayInterestRateMax;

	@ApiModelProperty(value = "展期申请最低提前天数")
	private Integer delayBeforeDayMin;

	@ApiModelProperty(value = "展期申请最高提前天数")
	private Integer delayBeforeDayMax;

	@ApiModelProperty(value = "规则场景id")
	private Long ruleSceneId;
	/**
	 * 清分时费用承担方是否为资方 0非 1是
	 */
	private Integer unifiedFeePay;
}

