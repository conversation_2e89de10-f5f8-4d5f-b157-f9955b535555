/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.jrzh_order_financing_goods_api.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.jrzh_order_financing_goods_api.entity.OrderFinancingGoods;
import org.springblade.product.common.entity.BaseProduct;
import org.springblade.product.common.entity.GoodsOpeningProcess;
import org.springblade.product.common.entity.GoodsQuestion;
import org.springblade.product.common.entity.GoodsTiming;
import org.springblade.product.common.vo.LabelVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品表实体类
 *
 * <AUTHOR>
 * @since 2021-12-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OrderFinancingGoodsVo", description = "订单融资vo对象")
public class OrderFinancingGoodsVo extends OrderFinancingGoods {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "资金方名称")
	private String capitalName;

	@ApiModelProperty(value = "资金方Logo")
	private String capitalLogo;

	@ApiModelProperty(value = "操作人")
	private String operator;

	@ApiModelProperty(value = "标签id")
	private List<Long> labelIds;

	@ApiModelProperty(value = "产品开通流程")
	private List<GoodsOpeningProcess> goodsOpeningProcesses;

	@ApiModelProperty(value = "产品常见问题")
	private List<GoodsQuestion> goodsQuestions;

	@ApiModelProperty(value = "定时器管理")
	private List<GoodsTiming> goodsTimingList;

	@ApiModelProperty(value = "标签列表")
	private List<LabelVO> labelList;

	@ApiModelProperty(value = "客户产品id")
	private Long customerGoodsId;

	@ApiModelProperty(value = "当前可用额度，单位万元，需转化为元")
	private BigDecimal currentAvailableAmount;

	/**
	 * 客户产品状态，1-待申请额度，2-未激活，3-可融资，4-开通失败，5-已过期，6-已禁用，7-额度调整
	 */
	@ApiModelProperty(value = "客户产品状态内容")
	private String customerGoodsStatusStr;
	/**
	 * 产品日利率
	 */
	@ApiModelProperty(value = "产品日利率")
	private String goodsDailyInterestRateStr;
	/**
	 * 产品年利率
	 */
	@ApiModelProperty(value = "产品年利率")
	private String goodsAnnualInterestRateStr;
	/**
	 * 产品到期日
	 */
	@ApiModelProperty(value = "产品到期日")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private LocalDateTime goodsExpireTime;

	/**
	 * 借款最高期限+单位
	 */
	@ApiModelProperty(value = "借款最高期限+单位")
	private String loadTermEndAndUnit;
	/**
	 * 额度id
	 */
	private Long enterpriseQuotaId;

	/**
	 * 拼接借款最高期限+单位
	 *
	 * @return
	 */
	public String getLoadTermEndAndUnit() {
		// 获取最高借款期限数值
		Integer loadTermEnd = getLoadTermEnd();
		if (ObjectUtil.isEmpty(loadTermEnd) || loadTermEnd.intValue() <= 0) {
			return "";
		}

		// 获取借款期限单位数值，1-天，2-期
		Integer loadTermUnit = getLoadTermUnit();
		// 根据借款单位数值，得到对应的单位字符串
		String loadTermUnitStr = getLoadTermUnitStr(loadTermUnit);

		return loadTermEnd + loadTermUnitStr;
	}

	/**
	 * 根据借款单位数值，得到对应的单位，1-天，2-期，其他情况返回空字符串
	 *
	 * @param loadTermUnit
	 * @return
	 */
	private String getLoadTermUnitStr(Integer loadTermUnit) {
		if (ObjectUtil.isEmpty(loadTermUnit) || loadTermUnit <= 0) {
			return "";
		}

		if (GoodsEnum.TERM.getCode().equals(loadTermUnit)) {
			return GoodsEnum.TERM.getName();
		} else if (GoodsEnum.DAY.getCode().equals(loadTermUnit)) {
			return GoodsEnum.DAY.getName();
		} else {
			return "";
		}
	}

	/**
	 * 获取借款金额最低-最高区间，字符串形式
	 *
	 * @return
	 */
	public String getLoanAmountStr() {
		BigDecimal loanAmountStart = getLoanAmountStart();
		BigDecimal loanAmountEnd = getLoanAmountEnd();
		if (Func.hasEmpty(loanAmountStart, loanAmountEnd) ||
				loanAmountStart.compareTo(BigDecimal.ZERO) == 0 ||
				loanAmountEnd.compareTo(BigDecimal.ZERO) == 0) {
			return "";
		}
		return loanAmountStart.toString().concat("~").concat(loanAmountEnd.toString()).concat("万");
	}

	/**
	 * 获取借款期限最低-最高区间（单位）
	 *
	 * @return
	 */
	public String getLoadTermStr() {
		Integer loadTermEnd = getLoadTermEnd();
		Integer loadTermStart = getLoadTermStart();
		Integer loadTermUnit = getLoadTermUnit();
		if (Func.hasEmpty(loadTermStart, loadTermEnd, loadTermUnit) ||
				loadTermStart == 0 || loadTermEnd == 0) {
			return "";
		}
		String unit;
		if (loadTermUnit.equals(GoodsEnum.TERM.getCode())) {
			unit = GoodsEnum.TERM.getName();
		} else {
			unit = GoodsEnum.DAY.getName();
		}
		return String.valueOf(loadTermStart).concat("~").concat(String.valueOf(loadTermEnd)).concat(unit);
	}
	/**
	 * 可开通标识
	 */
	private Boolean canOpenStatus;
}

