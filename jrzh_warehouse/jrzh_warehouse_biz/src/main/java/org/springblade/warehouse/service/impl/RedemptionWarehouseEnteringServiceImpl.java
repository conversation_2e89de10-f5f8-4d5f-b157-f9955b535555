/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.warehouse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.customer.entity.CustomerSupplier;
import org.springblade.customer.feign.ICustomerSupplierClient;
import org.springblade.product.common.entity.CommodityList;
import org.springblade.product.moudle.goods.service.ICommodityListService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserSearchService;
import org.springblade.warehouse.dto.GoodsDetailsDTO;
import org.springblade.warehouse.dto.WarehouseDetailsDTO;
import org.springblade.warehouse.entity.RedemptionWarehouseEntering;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.warehouse.mapper.RedemptionWarehouseEnteringMapper;
import org.springblade.warehouse.service.IRedemptionWarehouseEnteringService;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.warehouse.vo.RedemptionWarehouseEnteringVO;
import org.springblade.warehouse.wrapper.RedemptionWarehouseEnteringWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 待入库列表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-21
 */
@Service
@RequiredArgsConstructor
public class RedemptionWarehouseEnteringServiceImpl extends BaseServiceImpl<RedemptionWarehouseEnteringMapper, RedemptionWarehouseEntering> implements IRedemptionWarehouseEnteringService {
    private final ICustomerSupplierClient supplierService;
    private final RemoteUserSearchService userService;
    private final ICommodityListService commodityListService;

    @Override
    public IPage<RedemptionWarehouseEnteringVO> selectRedemptionWarehouseEnteringPage(IPage<RedemptionWarehouseEnteringVO> page, RedemptionWarehouseEnteringVO redemptionWarehouseEntering) {
        return page.setRecords(baseMapper.selectRedemptionWarehouseEnteringPage(page, redemptionWarehouseEntering));
    }

    @Override
    public List<RedemptionWarehouseEnteringVO> listByCompanyId(Long companyId) {
        List<RedemptionWarehouseEntering> list = list(Wrappers.<RedemptionWarehouseEntering>lambdaQuery()
                .eq(RedemptionWarehouseEntering::getCompanyId, companyId));
        return RedemptionWarehouseEnteringWrapper.build().listVO(list);
    }

    @Override
    public List<RedemptionWarehouseEnteringVO> listByFinancingCode(String financeNo) {
        if (StrUtil.isEmpty(financeNo)) {
            throw new ServiceException("融资编号不能为空");
        }
        List<RedemptionWarehouseEntering> list = list(Wrappers.<RedemptionWarehouseEntering>lambdaQuery()
                .eq(RedemptionWarehouseEntering::getFinanceNo, financeNo)
                .gt(RedemptionWarehouseEntering::getReadyToStorage, 0));
        return RedemptionWarehouseEnteringWrapper.build().listVO(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveList(List<RedemptionWarehouseEntering> list) {
        if (CollectionUtil.isEmpty(list)) {
            throw new ServiceException("生成失败，待入库列表为空");
        }
        //获取供应商
        List<Long> supplierIds = StreamUtil.map(list, RedemptionWarehouseEntering::getSupplierId);
        List<CustomerSupplier> customerSuppliers = supplierService.listByIds(supplierIds).getData();
        //k:id v:供应商名称
        Map<Long, String> suppliersMap = StreamUtil.toMap(customerSuppliers, CustomerSupplier::getId, CustomerSupplier::getSupperName);
        //获取融资用户
        List<Long> companyIds = StreamUtil.map(list, RedemptionWarehouseEntering::getCompanyId);
        List<User> customerInfos = userService.listByUserForInner(companyIds, FeignConstants.FROM_IN).getData();
        //k:id v:融资用户名称
        Map<Long, String> companyInfoMap = StreamUtil.toMap(customerInfos, User::getId, User::getName);
        if (supplierIds.size() != customerSuppliers.size()) {
            throw new ServiceException("供应商不存在，请检查供应商列表");
        }
        //设置商品名称
        List<Long> goodsIds = StreamUtil.map(list, RedemptionWarehouseEntering::getGoodsId);
        List<CommodityList> commodityLists = commodityListService.listByIds(goodsIds);
        //k:id v:商品名称
        Map<Long, String> commodityMap = StreamUtil.toMap(commodityLists, CommodityList::getId, CommodityList::getName);
        list.forEach(e -> {
            e.setSupplierName(suppliersMap.getOrDefault(e.getSupplierId(), ""));
            e.setCompanyName(companyInfoMap.getOrDefault(e.getCompanyId(), ""));
            e.setGoodsName(commodityMap.getOrDefault(e.getGoodsId(), ""));
            List<String> split = StrUtil.split(e.getUnitPrice().trim(), "~");
            e.setMinUnitPrice(new BigDecimal(split.get(0).trim()));
            e.setMaxUnitPrice(new BigDecimal(split.get(1).trim()));
            GoodsDetailsDTO goodsDetails = BeanUtil.copy(e, GoodsDetailsDTO.class);
            e.setGoodsInfo(JSONUtil.toJsonStr(goodsDetails));
        });
        return saveBatch(list);
    }


    @Override
    public Boolean subtractInNum(Long id, Integer warehouseInNum) {
        RedemptionWarehouseEntering entering = getById(id);
        final Integer readyToStorage = entering.getReadyToStorage();
        if (readyToStorage - warehouseInNum < 0) {
            throw new ServiceException("入库数量大于待入库数量");
        }
        entering.setReadyToStorage(readyToStorage - warehouseInNum);
        return updateById(entering);
    }

    @Override
    public Boolean subtractInNumList(List<WarehouseDetailsDTO> warehouseDetailsList) {
        Map<Long, WarehouseDetailsDTO> warehouseDetailsDTOMap = warehouseDetailsList.stream().collect(Collectors.toMap(WarehouseDetails::getEnteringId, e -> e));
        Set<Long> enterIds = warehouseDetailsDTOMap.keySet();
        List<RedemptionWarehouseEntering> warehouseEnterList = listByIds(enterIds);

        for (RedemptionWarehouseEntering warehouseEnter : warehouseEnterList) {
            WarehouseDetailsDTO warehouseDetails = warehouseDetailsDTOMap.get(warehouseEnter.getId());
            Integer warehouseInNum = warehouseDetails.getWarehouseInNum();
            Integer readyToStorage = warehouseEnter.getReadyToStorage();
            if (readyToStorage - warehouseInNum < 0) {
                throw new ServiceException("入库数量大于待入库数量");
            }
            Integer newReadyToStorage = readyToStorage - warehouseInNum;
            warehouseEnter.setReadyToStorage(readyToStorage - warehouseInNum);
        }
        return updateBatchById(warehouseEnterList);
    }

    @Override
    public Boolean existReadyToStorageNum(String financeNo) {
        return count(Wrappers.<RedemptionWarehouseEntering>lambdaQuery().eq(RedemptionWarehouseEntering::getFinanceNo, financeNo)
                .gt(RedemptionWarehouseEntering::getReadyToStorage, 0)) > 0;
    }

    @Override
    public RedemptionWarehouseEntering getDetailsById(Long id) {
        RedemptionWarehouseEntering warehouse = getById(id);
        RedemptionWarehouseEnteringVO warehouseVO = RedemptionWarehouseEnteringWrapper.build().entityVO(warehouse);
        CommodityList commodityList = commodityListService.getById(warehouseVO.getGoodsId());
        //获取检测节点
        if (Objects.nonNull(commodityList)) {
            warehouseVO.setTestNode(commodityList.getTestNode());
        }
        return warehouseVO;
    }

    @Override
    public List<RedemptionWarehouseEnteringVO> getDetailsByFinanceNo(String financeNo) {
        IWarehouseDetailsService warehouseDetailsService = SpringUtil.getBean(IWarehouseDetailsService.class);
        List<RedemptionWarehouseEntering> WarehouseEnterList = list(Wrappers.<RedemptionWarehouseEntering>lambdaQuery()
                .eq(RedemptionWarehouseEntering::getFinanceNo, financeNo));
        List<RedemptionWarehouseEnteringVO> warehouseEnteringVOS = RedemptionWarehouseEnteringWrapper.build().listVO(WarehouseEnterList);
        if (CollUtil.isEmpty(warehouseEnteringVOS)) {
            return warehouseEnteringVOS;
        }
        //获取待入库记录的已入库数量
        List<Long> enteringIds = warehouseEnteringVOS.stream().map(BaseEntity::getId).collect(Collectors.toList());
        List<WarehouseDetails> warehouseDetailsList = warehouseDetailsService.lambdaQuery().in(WarehouseDetails::getEnteringId, enteringIds).list();
        //判断是否有入库
        if (CollUtil.isNotEmpty(warehouseDetailsList)) {
            Map<Long, List<WarehouseDetails>> warehouseDetailsMap = warehouseDetailsList.stream().collect(Collectors.groupingBy(WarehouseDetails::getEnteringId));
            for (RedemptionWarehouseEnteringVO redemptionWarehouseEntering : warehouseEnteringVOS) {
                List<WarehouseDetails> warehouseDetails = warehouseDetailsMap.get(redemptionWarehouseEntering.getId());
                int sum = 0;
                if (CollUtil.isNotEmpty(warehouseDetails)) {
                    sum = warehouseDetails.stream().mapToInt(WarehouseDetails::getWarehouseInNum).sum();
                }
                redemptionWarehouseEntering.setWarehouseInNum(sum);
            }
        } else {
            warehouseEnteringVOS.stream().forEach(e -> e.setWarehouseInNum(0));
        }
        return warehouseEnteringVOS;
    }

    @Override
    public boolean updateToStorage(String financeNo) {
        return update(Wrappers.<RedemptionWarehouseEntering>lambdaUpdate()
                .set(RedemptionWarehouseEntering::getReadyToStorage, 0)
                .eq(RedemptionWarehouseEntering::getFinanceNo, financeNo));
    }

}
