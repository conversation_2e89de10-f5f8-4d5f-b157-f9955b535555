/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.warehouse.controller.font;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.warehouse.entity.RedemptionWarehouseEntering;
import org.springblade.warehouse.service.IRedemptionWarehouseEnteringService;
import org.springblade.warehouse.vo.RedemptionWarehouseEnteringVO;
import org.springblade.warehouse.wrapper.RedemptionWarehouseEnteringWrapper;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 待入库列表 控制器
 *
 * <AUTHOR>
 * @since 2022-06-21
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_REDEMPTION + CommonConstant.WEB_FRONT + "/redemption/WarehouseEntering")
@Api(value = "待入库列表", tags = "待入库列表接口")
public class RedemptionWarehouseEnteringFrontController extends BladeController {

    private final IRedemptionWarehouseEnteringService redemptionWarehouseEnteringService;

    /**
     * 自定义分页 待赎货列表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入redemptionWarehouseEntering")
    public R<IPage<RedemptionWarehouseEnteringVO>> list(@RequestBody Map<String, Object> queryMap, Query query) {
        IPage<RedemptionWarehouseEntering> page = redemptionWarehouseEnteringService
                .page(Condition.getPage(query), Condition.getQueryWrapper(queryMap, RedemptionWarehouseEntering.class)
                        .lambda().eq(RedemptionWarehouseEntering::getCompanyId, AuthUtil.getUserId())
                        .orderByDesc(RedemptionWarehouseEntering::getCreateTime));
        return R.data(RedemptionWarehouseEnteringWrapper.build().pageVO(page));
    }

    /**
     * 根据融资编号查询待入库列表
     */
    @GetMapping("/listFinancingCode")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入redemptionWarehouseEntering")
    public R<List<RedemptionWarehouseEnteringVO>> listFinancingCode(String financeNo) {
        return R.data(redemptionWarehouseEnteringService.listByFinancingCode(financeNo));
    }

    @PostMapping("/save")
    public R save(@RequestBody List<RedemptionWarehouseEntering> list) {
        return R.status(redemptionWarehouseEnteringService.saveList(list));
    }
}
