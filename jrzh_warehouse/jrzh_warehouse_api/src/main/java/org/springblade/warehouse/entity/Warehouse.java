/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.warehouse.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 仓库列表实体类
 *
 * <AUTHOR>
 * @since 2022-03-11
 */
@Data
@TableName("jrzh_warehouse")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PurchaseWarehouse对象", description = "仓库列表")
public class Warehouse extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 仓库编号
	 */
	@ApiModelProperty(value = "仓库编号")
	private String warehouseNo;
	/**
	 * 联系人
	 */
	@ApiModelProperty(value = "联系人")
	private String contacts;
	/**
	 * 电话
	 */
	@ApiModelProperty(value = "电话")
	private String phone;
	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址")
	private String address;
	/**
	 * 所属区域
	 */
	@ApiModelProperty(value = "所属区域")
	private String regionCode;
	/**
	 * 仓库名称
	 */
	@ApiModelProperty(value = "仓库名称")
	private String warehouseName;
	/**
	 * 仓储公司
	 */
	private Long storageId;
}
