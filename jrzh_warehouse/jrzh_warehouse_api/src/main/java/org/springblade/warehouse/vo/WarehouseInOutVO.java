/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.warehouse.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.warehouse.entity.WarehouseInOut;
import org.springblade.warehouse.enums.WareHouseDetailsEnum;

/**
 * 商品出入库视图实体类
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WarehouseInOutVO对象", description = "商品出入库")
public class WarehouseInOutVO extends WarehouseInOut {
    private static final long serialVersionUID = 1L;
    private String operatorName;

    public String getWarehouseTypeStr() {
        if (WareHouseDetailsEnum.WAREHOUSE_TYPE.UN_OVERDUE.getCode().equals(getWarehouseType())) {
            return "入库";
        }
        if (WareHouseDetailsEnum.WAREHOUSE_TYPE.OVERDUE.getCode().equals(getWarehouseType())) {
            return "出库";
        }
        return "";
    }
}
