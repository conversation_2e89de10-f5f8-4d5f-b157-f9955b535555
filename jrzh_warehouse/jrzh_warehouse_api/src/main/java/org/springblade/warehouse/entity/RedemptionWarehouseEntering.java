/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.warehouse.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 待入库列表实体类
 *
 * <AUTHOR>
 * @since 2022-06-21
 */
@Data
@TableName("jrzh_redemption_warehouse_entering")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RedemptionWarehouseEntering对象", description = "待入库列表")
public class RedemptionWarehouseEntering extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 融资编号
	 */
	@ApiModelProperty(value = "融资编号")
	private String financeNo;
	/**
	 * 货品信息 json快照
	 */
	@ApiModelProperty(value = "货品信息 json快照")
	private String goodsInfo;
	/**
	 * 供应商id
	 */
	@ApiModelProperty(value = "供应商id")
	private Long supplierId;
	/**
	 * 供应商
	 */
	private String supplierName;
	/**
	 * 单位
	 */
	@ApiModelProperty(value = "单位")
	private String goodsUnitValue;
	/**
	 * 待入库数量
	 */
	@ApiModelProperty(value = "待入库数量")
	private Integer readyToStorage;
	/**
	 * 采购单价(元)
	 */
	@ApiModelProperty(value = "采购单价(元)")
	private BigDecimal purchasePrice;
	/**
	 * 融资单价(元)
	 */
	@ApiModelProperty(value = "融资单价(元)")
	private BigDecimal financingPrice;
	/**
	 * 最迟交付日
	 */
	@ApiModelProperty(value = "最迟交付日")
	private LocalDate latestDelivery;
	/**
	 * 商品图片地址
	 */
	@ApiModelProperty(value = "货品图片地址")
	private String logo;
	/**
	 * 商品
	 */
	private Long goodsId;
	/**
	 * 商品名称
	 */
	private String goodsName;
	/**
	 * 企业id
	 */
	private Long companyId;
	/**
	 * 规格型号
	 */
	private String goodsSpec;
	/**
	 * 发布单价左区间
	 */
	private BigDecimal minUnitPrice;
	/**
	 * 发布单价右区间
	 */
	private BigDecimal maxUnitPrice;
	/**
	 * 融资用户名称
	 */
	private String companyName;
	/**
	 * 发布单价
	 */
	private String unitPrice;
	/**
	 * 类型  0正常 1换货
	 */
	private Integer type;

	/**
	 * 原赎货单号
	 */
	private String originalRedeemNo;

	/**
	 * 代采申请变更id
	 */
	private Long changeApplyId;
	/**
	 * 业务类型  2代采   4动产质押
	 */
	private Integer businessType;
}
