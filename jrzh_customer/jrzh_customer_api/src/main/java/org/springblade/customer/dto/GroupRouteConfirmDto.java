package org.springblade.customer.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description: 确认产品开通
 * @date 2024年08月15日 14:08
 */
@Data
public class GroupRouteConfirmDto implements Serializable {

    /**
     * 产品组id
     */
    @NotNull
    private Long groupId;

    /**
     * 产品ID
     */
    @NotNull
    private Long productId;

    /**
     * 客户补充资料ID
     */
    @NotNull
    private Long customerMaterialId;
}
