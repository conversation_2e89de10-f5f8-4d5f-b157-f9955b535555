/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.customer.entity.SalesContract;

/**
 * 销售合同数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SalesContractDTO extends SalesContract {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("唯一编号")
	private String contractNoEqual;

	@ApiModelProperty("贸易背景id")
	private Long backIdEqual;

	@ApiModelProperty("过期时间开始时间")
	private String expireTimeDateGe;

	@ApiModelProperty("过期时间结束时间")
	private String expireTimeDateLe;

	@ApiModelProperty("凭证类型")
	private String proofTypeIn;

}
