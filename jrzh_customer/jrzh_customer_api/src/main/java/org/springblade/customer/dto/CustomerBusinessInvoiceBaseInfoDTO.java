/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务发票数据基础接受类
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Data
public class CustomerBusinessInvoiceBaseInfoDTO implements Serializable {

	/**
	 * 发票类型
	 */
	@ApiModelProperty(value = "发票类型")
	private String type;
	/**
	 * 发票校验码
	 */
	@ApiModelProperty(value = "发票校验码")
	private String checkCode;
	/**
	 * 发票号码
	 */
	@ApiModelProperty(value = "发票号码")
	private String number;
	/**
	 * 开票日期
	 */
	@ApiModelProperty(value = "开票日期")
	private String issueDate;
	/**
	 * 合计金额
	 */
	@ApiModelProperty(value = "合计金额")
	private String subtotalAmount;
	/**
	 * 合计税额
	 */
	@ApiModelProperty(value = "合计税额")
	private String subtotalTax;
	/**
	 * 价税合计
	 */
	@ApiModelProperty(value = "价税合计")
	private String total;
	/**
	 * 流程类型
	 */
	@ApiModelProperty(value = "流程类型")
	private String processType;
	/**
	 * 产品id
	 */
	@ApiModelProperty(value = "产品id")
	private String goodId;
	/**
	 * 客户id
	 */
	@ApiModelProperty(value = "客户id")
	private String customerId;
	/**
	 * 上传节点
	 */
	@ApiModelProperty(value = "上传节点")
	private String uploadNode;
	/**
	 * 验真;0  没有验证  1 验真成功   2  验真失败
	 */
	@ApiModelProperty(value = "验真;0  没有验证  1 验真成功   2  验真失败")
	private Integer isVertify;


	/**
	 * 发票代码
	 */
	private String code;
	/**
	 * 图片地址
	 */
	private String proof;
	/**
	 * 是否上传 0:未上传 1：已上传
	 */
	private Integer uploadStatus;
	/**
	 * 购买方名称
	 */
	private String buyerName;
	/**
	 * 购买方纳税人识别号
	 */
	private String buyerId;
	/**
	 * 购买方地址、电话
	 */
	private String buyerAddress;
	/**
	 * 购买方开户行及帐号
	 */
	private String buyerBank;
	/**
	 * 销售方名称
	 */
	private String sellerName;
	/**
	 * 销售方纳税人识别号
	 */
	private String sellerId;
	/**
	 * 销售方地址、电话
	 */
	private String sellerAddress;
	/**
	 * 销售方开户行及帐号
	 */
	private String sellerBank;
}
