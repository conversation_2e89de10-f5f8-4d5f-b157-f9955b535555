/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.customer.entity.CustomerGoods;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客户产品视图实体类
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Data
@ApiModel(value = "CustomerGoodsVO对象", description = "客户产品")
public class CustomerGoodsVO extends CustomerGoods {
	private static final long serialVersionUID = 1L;


	/**
	 * 过期时间
	 */
	@ApiModelProperty(value = "过期时间")
	private LocalDateTime expireTime;
	/**
	 * 年利率
	 */
	@ApiModelProperty(value = "年利率")
	private BigDecimal annualInterestRate;
	/**
	 * 年利率
	 */
	@ApiModelProperty(value = "产品最低年利率")
	private BigDecimal annualInterestRateStart;
	/**
	 * 日利率
	 */
	@ApiModelProperty(value = "日利率")
	private BigDecimal dailyInterestRate;

	@ApiModelProperty(value = "授信额度")
	private BigDecimal creditAmount;

	@ApiModelProperty(value = "禁用原因")
	private String disableReason;
	/**
	 * 待还本金
	 */
	@ApiModelProperty(value = "待还本金")
	private BigDecimal repaidPrincipal;
	/**
	 * 可用额度
	 */
	@ApiModelProperty(value = "可用额度")
	private BigDecimal availableCredit;
	/**
	 * 当前申请金额
	 */
	@ApiModelProperty(value = "当前申请金额")
	private BigDecimal applyAmount;
	/**
	 * 待还利息
	 */
	@ApiModelProperty(value = "待还利息")
	private BigDecimal repaidInterest;
	/**
	 * 逾期金额
	 */
	@ApiModelProperty(value = "逾期金额")
	private BigDecimal overdueAmount;


	@ApiModelProperty(value = "标签")
	private List<Map<String,Object>> labelList;

	@ApiModelProperty(value = "借款金额")
	private BigDecimal loanAmountEnd;

	@ApiModelProperty(value = "借款期限")
	private Integer loadTermEnd;

	@ApiModelProperty(value = "借款期限单位")
	private Integer loadTermUnit;

	@ApiModelProperty(value = "产品绑定贸易背景")
	private List<CustomerGoodsTradeBackgroundVO> customerGoodsTradeBackgrounds;

	private LocalDate firstTradeTime;

	@ApiModelProperty("利率类型")
	private Integer annualInterestRateType;

	private String bank;

	private String bankCardNo;

	private Integer chargeMethod;

	private Integer lendingMethod;
	
	@ApiModelProperty("存在逾期未还订单")
	private Boolean hasOverdueNoPay;

	@ApiModelProperty("最高融资比例")
	private BigDecimal financingProportion;
}
