package org.springblade.customer.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.sms.model.SmsCode;


/***
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSignIn {
	private  String account;
	private String password;
	private Integer type;
	private String tenantId;
	private SmsCode smsCode;
}
