package org.springblade.customer.dto;


import lombok.Data;
import lombok.experimental.Accessors;
import org.springblade.customer.vo.CustomerInfoVO;
import org.springblade.customer.vo.CustomerPersonInfoVO;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AuthResponseDTO {
	/**
	 * 实名跳转链接
	 */
	private  String  skipLink;


	/**
	 * 实名状态   0  实名未成功    1  实名成功   -1 上上签实名成功 但未同步数据
	 */
	private  Integer  sysAuthStatus;


	/**
	 * 实名成功后  个人数据
	 */
	private CustomerPersonInfoVO customerPersonInfoVO;


	/**
	 *实名成功后 企业数据
	 */
	private CustomerInfoVO customerInfoVO;

	/**
	 * 实名信息
	 */
	private  String  authMsg;
}
