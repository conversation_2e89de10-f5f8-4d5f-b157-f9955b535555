/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 补充资料实体类
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Data
@TableName("jrzh_customer_front_additional_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AdditionalInfo对象", description = "补充资料")
public class AdditionalInfo extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 企业id
	 */
	@NotNull
	@ApiModelProperty(value = "企业id")
	private Long companyId;
	/**
	 * 户口本照片
	 */
	@NotNull
	@ApiModelProperty(value = "户口本照片")
	private String imgFamilyRegister;
	/**
	 * 婚姻状况
	 */
	@NotNull
	@ApiModelProperty(value = "婚姻状况")
	private String marriageStatus;
	/**
	 * 婚姻证名原件
	 */
	@NotNull
	@ApiModelProperty(value = "婚姻证名原件")
	private String imgMarriageCertificate;
	/**
	 * 学历
	 */
	@NotNull
	@ApiModelProperty(value = "学历")
	private String education;
	/**
	 * 学历照片
	 */
	@NotNull
	@ApiModelProperty(value = "学历照片")
	private String imgEducation;
	/**
	 * 毕业学校
	 */
	@NotNull
	@ApiModelProperty(value = "毕业学校")
	private String graduation;
	/**
	 * 毕业时间
	 */
	@NotNull
	@ApiModelProperty(value = "毕业时间")
	private LocalDateTime graduatedTime;
	/**
	 * 公司章程照片
	 */
	@NotNull
	@ApiModelProperty(value = "公司章程照片")
	private String imgCompanyBylaw;
	/**
	 * 驳回意见
	 */
	@Size(max = 500, message = "字数不得超过500")
	@ApiModelProperty(value = "驳回意见")
	private String remark;

}
