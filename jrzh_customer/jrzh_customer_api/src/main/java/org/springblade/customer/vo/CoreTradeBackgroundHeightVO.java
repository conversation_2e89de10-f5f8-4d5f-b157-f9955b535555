/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.customer.entity.TradeBackground;

import java.math.BigDecimal;

/**
 * 核心企业贸易详细视图实体类
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TradeBackgroundVO对象", description = "核心企业贸易详细")
public class CoreTradeBackgroundHeightVO extends TradeBackground {
	private static final long serialVersionUID = 1L;
	/**
	 * 上游企业名称
	 */
	private String companyHeightName;
	/**
	 * 下游企业名称
	 */
	private String companyLowerName;
	/**
	 * 合作年限
	 */
	private BigDecimal cooperateTime;
	/**
	 * 总确权额度
	 */
	private BigDecimal confirmAmount;
	/**
	 * 待结清金额
	 */
	private BigDecimal nonPaymentAmount;
	/**
	 * 当前授予额度
	 */
	private BigDecimal creditedAmount;
	/**
	 * 待确权金额
	 */
	private BigDecimal unConfirmAmount;
	/**
	 * 已结清金额
	 */
	private BigDecimal settledAmount;
	/**
	 * 已回款金额
	 */
	private BigDecimal refundAmount;
	/**
	 * 到期金额
	 */
	private BigDecimal expireAmount;
	/**
	 * 在途应付笔数
	 */
	private Integer needPayCount;
	/**
	 * 近3月交易数据
	 */
	private TransactionVO transactionThree;
	/**
	 * 近6月交易数据
	 */
	private TransactionVO transactionSix;
	/**
	 * 近12月交易数据
	 */
	private TransactionVO transactionTwelve;

}
