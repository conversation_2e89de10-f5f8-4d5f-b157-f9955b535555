/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.customer.entity.TradeBackground;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PastOrPresent;
import java.time.LocalDate;

/**
 * 绑定下游贸易背景
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HeightTradeBackgroundDTO extends TradeBackground {
	private static final long serialVersionUID = 1L;
	/**
	 * 上游供应商名称
	 */
	@NotNull(message = "上游供应商名称不能为空")
	@ApiModelProperty(value = "供应商名称")
	private String coreEnterpriseName;
	/**
	 * 初次合作时间
	 */
	@NotNull(message = "初次合作时间不能为空")
	@PastOrPresent(message = "初次合作时间不得是一个未来的时间")
	@ApiModelProperty(value = "初次合作时间")
	private LocalDate firstTradeTime;



}
