package org.springblade.customer.feign;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.CustomerBankCardPerson;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/11/6
 * @description
 */
@FeignClient(name = "FeignCustomerBankCardPerson", url = "${feign.url}")
public interface RemoteCustomerBankCardPersonService {

    @GetMapping(CommonConstant.BLADE_FEIGN + CommonConstant.CUSTOMER_SERVICE + "customerBankCardPerson/getOneInfo")
    R<CustomerBankCardPerson> getOneInfo(@RequestParam("goodsId") Long goodsId,@RequestParam("userId") Long userId);
}
