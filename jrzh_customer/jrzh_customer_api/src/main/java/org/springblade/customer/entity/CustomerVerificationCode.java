/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 企业邀请用户设置角色实体类
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
@Data
@TableName("jrzh_customer_verification_code")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerVerificationCode对象", description = "企业邀请用户设置角色")
public class CustomerVerificationCode extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 企业id
	*/
		@ApiModelProperty(value = "邀请人企业id")
		private Long companyId;
	/**
	* 邀请code
	*/
		@ApiModelProperty(value = "邀请code")
		private String code;
	/**
	* 邀请人用户id
	*/
		@ApiModelProperty(value = "邀请人用户id")
		private Long customerId;
	/**
	* 邀请角色信息
	*/
		@ApiModelProperty(value = "邀请角色信息")
		private String role;

		@ApiModelProperty(value = "被邀请人用户id")
		private Long coverCodeId;

		 @ApiModelProperty(value = "被邀请人手机号")
		 @Pattern(regexp = "^[1][3-9][0-9]{9}$", message = "手机号格式错误")
	     private String inviteesPhone;
	     @ApiModelProperty(value = "被邀请人名称")
	     private String inviteesName;

	     @ApiModelProperty(value = "邀请人企业名称")
	     private  String enterpriseName;

	    @ApiModelProperty(value = "邀请人名称")
	    private  String inviteeName;

	    @ApiModelProperty(value = "开始时间")
		private  Date startTime;
}
