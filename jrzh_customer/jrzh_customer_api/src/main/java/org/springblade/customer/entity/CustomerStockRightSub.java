/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springblade.core.tenant.mp.TenantEntity;

import java.time.LocalDateTime;

/**
 * 客户股权表实体类
 *
 * <AUTHOR>
 * @since 2022-02-15
 */
@Data
@TableName("jrzh_customer_stock_right_sub")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerStockRightSub对象", description = "客户股权历史信息表")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerStockRightSub extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 股东名称
	*/
		@ApiModelProperty(value = "股东名称")
		private String name;
	/**
	* 出资金额
	*/
		@ApiModelProperty(value = "出资金额")
		private String amomon;
	/**
	* 持股比例
	*/
		@ApiModelProperty(value = "持股比例")
		private String percent;
	/**
	* 更新时间
	*/
		@ApiModelProperty(value = "更新时间")
		private LocalDateTime changeDate;
	/**
	* 变更表ID
	*/
		@ApiModelProperty(value = "变更表ID")
		private Long recordId;

	    @ApiModelProperty(value = "1-原数据 2-变更后的数据")
	    private Integer type;

}
