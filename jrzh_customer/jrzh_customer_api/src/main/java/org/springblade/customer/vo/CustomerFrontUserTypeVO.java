/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.customer.entity.CustomerFrontUserType;

import java.util.Date;

/**
 * 融资企业用户类型视图实体类
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerFrontUserTypeVO对象", description = "融资企业用户类型")
public class CustomerFrontUserTypeVO extends CustomerFrontUserType {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "企业名称")
	private String name;
	@ApiModelProperty(value = "个人名称")
	private String personalName;
	@ApiModelProperty(value = "个人手机号")
	private String personalPhone;
	@ApiModelProperty(value = "个人头像")
	private  String logoSrc;
	@ApiModelProperty(value = "角色名称")
	private String roleName;
	@ApiModelProperty(value ="统一社会信用代码")
	private String creditCode;
	@ApiModelProperty(value ="邀请人名称")
	private String inviteeName;
	@ApiModelProperty(value ="企业头像")
	private String 	corporateAvatar;
	@ApiModelProperty(value ="角色别名")
	private String 	roleAlias;
	@ApiModelProperty(value="绑定时间")
	private Date bindingTime;
	@ApiModelProperty(value="用户名称")
	private String  accountUser;
	@ApiModelProperty(value="企业类型")
	private Integer label;
}
