package org.springblade.customer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TurnToBoxAuthDTO {
	/**
	 * 用户类型
	 */
	private String userType;

	/**
	 * 档案柜id
	 */
	private String archiveId;

	/**
	 * 个人认证采集
	 */
	private List<PersonAuthDTO> personAuthDTOS;

	/**
	 * 企业认证采集
	 */
	private List<EntAuthDTO> entAuthDTOS;

	/**
	 * 返回地址
	 */
	private  String  returnUrl;


}
