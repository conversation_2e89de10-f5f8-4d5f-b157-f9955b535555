/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 客户信息变更记录实体类
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@TableName("jrzh_customer_info_record")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerInfoRecord对象", description = "客户信息变更记录")
public class CustomerInfoRecord extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 客户表id
	*/
		@ApiModelProperty(value = "客户表id")
		private String customerId;
	/**
	* 工商信息
	*/
		@ApiModelProperty(value = "工商信息")
		private String businessInfo;
	/**
	* 股权信息
	*/
		@ApiModelProperty(value = "股权信息")
		private String stockInfo;


}
