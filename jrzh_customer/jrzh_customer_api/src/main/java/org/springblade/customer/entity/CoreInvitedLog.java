/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 核心企业邀请记录实体类
 *
 * <AUTHOR>
 * @since 2022-02-26
 */
@Data
@TableName("jrzh_core_invited_log")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CoreInvitedLog对象", description = "核心企业邀请记录")
public class CoreInvitedLog extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 邀请人id
	 */
	@ApiModelProperty(value = "邀请人id")
	private Long coreEnterpriseId;
	/**
	 * 邀请人
	 */
	@ApiModelProperty(value = "邀请人")
	private String coreEnterpriseName;
	/**
	 * 受邀人
	 */
	@ApiModelProperty(value = "受邀人")
	private String frontEnterpriseName;
	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String phone;
	/**
	 * 联系人
	 */
	@ApiModelProperty(value = "联系人")
	private String linkMan;
	/**
	 * 邀请码
	 */
	@ApiModelProperty(value = "邀请码")
	private String code;
	/**
	 * 邀请有效期
	 */
	@ApiModelProperty(value = "邀请有效期")
	private String expireDate;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

}
