/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 流程开通进度
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@TableName("jrzh_product_route_group_apply")
public class ProductRouteGroupApply extends TenantEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 产品组id
     */
    private Long groupId;
    /**
     * 路由的具体产品id 如果通过的话会返回
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long goodsId;
    /**
     * 推荐产品ID
     */
    private Long recommendGoodsId;
    /**
     * 路由申请的产品ids
     */
    private String applyGoodsIds;
    /**
     * 路由申请状态 0申请失败 1申请中 2申请成功 3终止
     * 失败需要让用户重新填写资料
     * 成功直接拿goodsId进行产品开通
     */
    private Integer status;
    /**
     * 驳回原因
     */
    private String rejectReason;
    /**
     * 流程id
     */
    private String processInstanceId;
    /**
     * 申请用户
     */
    private Long applyUser;
    /**
     * 产品类型
     */
    private Integer goodsType;
    /**
     * 用户类型
     */
    private Integer userType;
    /**
     * 运行风控资料
     */
    @JsonIgnore
    private String productRouteRunRiskResultList;

    /**
     * 客户补充资料ID
     */
    private Long customerMaterialId;
}
