/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.time.LocalDateTime;

/**
 * 客户证件资料表实体类
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@TableName("jrzh_customer_certificate_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerCertificateInfo对象", description = "客户证件资料表")
public class CustomerCertificateInfo extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 证件类型
	 */
	@ApiModelProperty(value = "证件类型")
	private Long type;
	/**
	 * 证件图片id
	 */
	@ApiModelProperty(value = "证件图片id")
	private String attachId;
	/**
	 * 有效时间
	 */
	@ApiModelProperty(value = "有效时间")
	private LocalDateTime validTime;

	/**
	 * 企业名称
	 */
	@ApiModelProperty(value = "企业名称")
	private String companyName;



	/**
	 * 公司统一信用代码
	 */
	@ApiModelProperty(value = "公司信用代码")
	private String companyCreditCode;


	/***
	 * 文件类型
	 */
	@ApiModelProperty(value = "文件类型")
	private  String  fileType;
	/***
	 * 企业ID
	 */
	@ApiModelProperty(value = "企业ID")
	private  String  companyId;

	/***
	 * 客户ID
	 */
	@ApiModelProperty(value = "客户ID")
	private  String  customerId;

	/***
	 * 任务名称
	 */
	@ApiModelProperty(value = "任务名称")
	private  String taskName;
	/***
	 * 任务编号
	 */
	@ApiModelProperty(value = "任务编号")
	private  String 	taskNo;
	/***
	 * 任务实例ID
	 */
	@ApiModelProperty(value = "流程实例ID")
	private String  processId;
	/***
	 * 任务ID
	 */
	@ApiModelProperty(value = "任务ID")
	private String taskId;
}
