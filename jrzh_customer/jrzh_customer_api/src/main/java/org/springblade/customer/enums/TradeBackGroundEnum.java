package org.springblade.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 贸易背景枚举
 *
 * <AUTHOR>
 */
public interface TradeBackGroundEnum {
	@Getter
	@AllArgsConstructor
	enum CONFIRM_STATUS implements TradeBackGroundEnum {
		/**
		 * 确权失败
		 */
		WAIT_CONFIRM(0, "确权失败"),
		/**
		 * 未确权
		 */
		UN_CONFIRM(1, "未确权"),
		/**
		 * 已确权
		 */
		CONFIRM(2, "已确权");
		final Integer status;
		final String desc;
	}

	@Getter
	@AllArgsConstructor
	enum PROOF_STATUS implements TradeBackGroundEnum {
		/**
		 * 待确权
		 */
		UN_CONFIRMED(1, "待确权"),
		/**
		 * 已作废
		 */
		INVALID(2, "已作废"),
		/**
		 * 可使用
		 */
		USEABLE(3, "可使用"),
		/**
		 * 已使用
		 */
		USED(4, "已使用"),
		/**
		 * 到期未结清
		 */
		EXPIRD_UN_SETTLE(5, "到期未结清"),
		/**
		 * 已结清
		 */
		SETTLE(6, "已结清");
		final Integer status;
		final String desc;

		public static String getAssetsValueByKey(Integer key) {
			for (PROOF_STATUS s : PROOF_STATUS.values()) {
				if (s.getStatus().equals(key)) {
					return s.getDesc();
				}
			}
			return "";
		}

	}

	@Getter
	@AllArgsConstructor
	enum SETTLE_STATUS implements TradeBackGroundEnum {
		/**
		 * 未结清
		 */
		UNSETTLE(0, "未结清"),

		/**
		 * 已结清
		 */
		SETTLED(1, "已结清");
		final Integer status;
		final String desc;
	}

	@Getter
	@AllArgsConstructor
	enum VERIFY_STATUS implements TradeBackGroundEnum {
		/**
		 * 未验证
		 */
		UN_VERIFY(0),

		/**
		 * 已验证
		 */
		VERIFY(1);
		final Integer status;
	}

	@Getter
	@AllArgsConstructor
	enum ACTIVITY_STATUS implements TradeBackGroundEnum {
		/**
		 * 失效
		 */
		UN_ACTIVITY(0),

		/**
		 * 活动
		 */
		ACTIVITY(1);
		final Integer status;
	}

	/**
	 * 发起用户位置
	 */
	@Getter
	@AllArgsConstructor
	enum COMPANY_POSITION implements TradeBackGroundEnum {
		/**
		 * 下游
		 */
		LOWER("下游", 0),
		/**
		 * 上游
		 */
		HEIGHT("上游", 1);
		private final String name;
		private final Integer code;
	}

	/**
	 * 贸易背景关联状态
	 */
	@Getter
	@AllArgsConstructor
	enum TRADE_BACKGROUND_STATUS implements TradeBackGroundEnum {
		/**
		 * 背景关联状态-已关闭
		 */
		CLOSE("已关闭", 0),
		/**
		 * 背景关联状态-审批中
		 */
		APPLYING("审批中", 1),
		/**
		 * 背景关联状态-已关联
		 */
		RELATED("已关联", 2);
		private final String name;
		private final Integer code;
	}
	/**
	 * 邀请箱进度
	 */
	@Getter
	@AllArgsConstructor
	enum INVITE_STATUS implements TradeBackGroundEnum {
		/**
		 * 邀请中
		 */
		INVITING(1,"邀请中"),
		/**
		 * 邀请成功
		 */
		INVITED(2,"邀请成功"),
		/**
		 * 邀请失败
		 */
		FAIL(3,"邀请失败");
		private final Integer code;
		private final String desc;
	}
}
