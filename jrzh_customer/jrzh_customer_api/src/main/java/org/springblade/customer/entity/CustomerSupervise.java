/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.enums.StorageEnum;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.util.StringUtils;

/**
 * 监管方实体类
 *
 * <AUTHOR>
 * @since 2022-06-11
 */
@Data
@TableName("jrzh_customer_supervise")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerSupervise对象", description = "监管方")
public class CustomerSupervise extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 统一社会信用代码
	*/
		@ApiModelProperty(value = "统一社会信用代码")
		private String unifiedCode;
	/**
	* 仓储方名称
	*/
		@ApiModelProperty(value = "仓储方名称")
		private String superviseName;
	/**
	* 银行账户
	*/
		@ApiModelProperty(value = "银行账户")
		private String bankAccount;
	/**
	* 开户银行
	*/
		@ApiModelProperty(value = "开户银行")
		private Long depositBankId;
	/**
	* 联系人
	*/
		@ApiModelProperty(value = "联系人")
		private String contacts;
	/**
	* 手机号码
	*/
		@ApiModelProperty(value = "手机号码")
		private String supervisePhone;

	public boolean isOnShelf() {
		return !StringUtils.isEmpty(getStatus()) && StorageEnum.ENABLE.getCode().equals(getStatus());
	}
}
