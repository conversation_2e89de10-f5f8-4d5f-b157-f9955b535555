/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.time.LocalDateTime;

/**
 * 退款订单表实体类
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
@Data
@TableName("jrzh_pay_refund_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PayRefundOrder对象", description = "退款订单表")
public class PayRefundOrder extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 支付订单号
	 */
	@ApiModelProperty(value = "支付订单号")
	private String payOrderNo;
	/**
	 * 渠道支付单号
	 */
	@ApiModelProperty(value = "渠道支付单号")
	private String channelPayOrderNo;
	/**
	 * 商户ID
	 */
	@ApiModelProperty(value = "商户ID")
	private String mchId;
	/**
	 * 商户退款单号
	 */
	@ApiModelProperty(value = "商户退款单号")
	private String mchRefundNo;
	/**
	 * 渠道ID
	 */
	@ApiModelProperty(value = "渠道ID")
	private String channelId;
	/**
	 * 支付金额,单位元
	 */
	@ApiModelProperty(value = "支付金额,单位元")
	private String payAmount;
	/**
	 * 退款金额,单位分
	 */
	@ApiModelProperty(value = "退款金额,单位分")
	private Long refundAmount;
	/**
	 * 三位货币代码,人民币:cny
	 */
	@ApiModelProperty(value = "三位货币代码,人民币:cny")
	private String currency;
	/**
	 * 退款结果:0-不确认结果,1-等待手动处理,2-确认成功,3-确认失败
	 */
	@ApiModelProperty(value = "退款结果:0-不确认结果,1-等待手动处理,2-确认成功,3-确认失败")
	private Integer result;
	/**
	 * 客户端IP
	 */
	@ApiModelProperty(value = "客户端IP")
	private String clientIp;
	/**
	 * 设备
	 */
	@ApiModelProperty(value = "设备")
	private String device;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 渠道用户标识,如微信openId,支付宝账号
	 */
	@ApiModelProperty(value = "渠道用户标识,如微信openId,支付宝账号")
	private String channelUser;
	/**
	 * 用户姓名
	 */
	@ApiModelProperty(value = "用户姓名")
	private String username;
	/**
	 * 渠道商户ID
	 */
	@ApiModelProperty(value = "渠道商户ID")
	private String channelMchId;
	/**
	 * 渠道订单号
	 */
	@ApiModelProperty(value = "渠道订单号")
	private String channelOrderNo;
	/**
	 * 渠道错误码
	 */
	@ApiModelProperty(value = "渠道错误码")
	private String channelErrCode;
	/**
	 * 渠道错误描述
	 */
	@ApiModelProperty(value = "渠道错误描述")
	private String channelErrMsg;
	/**
	 * 特定渠道发起时额外参数
	 */
	@ApiModelProperty(value = "特定渠道发起时额外参数")
	private String extra;
	/**
	 * 通知地址
	 */
	@ApiModelProperty(value = "通知地址")
	@TableField("notifyUrl")
	private String notifyurl;
	/**
	 * 扩展参数1
	 */
	@ApiModelProperty(value = "扩展参数1")
	private String param1;
	/**
	 * 扩展参数2
	 */
	@ApiModelProperty(value = "扩展参数2")
	private String param2;
	/**
	 * 订单失效时间
	 */
	@ApiModelProperty(value = "订单失效时间")
	private LocalDateTime expireTime;
	/**
	 * 订单退款成功时间
	 */
	@ApiModelProperty(value = "订单退款成功时间")
	private LocalDateTime refundSuccTime;


}
