/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 产品路由返回结果
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Data
public class RouteCustomerGoodsRespVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 产品组id
     */
    private Long groupId;
    /**
     * 路由的具体产品id 如果通过的话会返回
     */
    private Long goodsId;
    /**
     * 路由申请状态 0申请失败 1申请中 2申请成功 3终止
     * 失败需要让用户重新填写资料
     * 成功直接拿goodsId进行产品开通
     */
    private Integer status;
    /**
     * 客户补充资料ID
     */
    private Long customerMaterialId;
}
