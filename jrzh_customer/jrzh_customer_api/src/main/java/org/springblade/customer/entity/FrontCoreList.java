/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * 核心企业详情列表信息表实体类
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Data
@TableName("jrzh_customer_front_core_list")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FrontCoreList对象", description = "核心企业详情列表信息表")
public class FrontCoreList extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 白名单 0-是 1-否;添加之后可以直接设置为白名单
	 */
	@ApiModelProperty(value = "白名单 0-否 1-是;添加之后可以直接设置为白名单")
	private Integer customerWhite;
	/**
	 * 企业类型 0-集团 1-子公司;默认为0
	 */
	@ApiModelProperty(value = "企业类型 0-集团 1-子公司;默认为0")
	private Integer enterpriseType;
	/**
	 * 授信额度(万)
	 */
	@ApiModelProperty(value = "授信额度(万)")
	private BigDecimal creditLine;
	/**
	 * 父公司ID;有父公司则有父公司ID 否则 为空
	 */
	@ApiModelProperty(value = "父公司ID;有父公司则有父公司ID 否则 为空")
	private Long fatherCompanyId;
	/**
	 * 核心企业评分
	 */
	@ApiModelProperty(value = "核心企业评分")
	private BigDecimal customerScore;
	/**
	 * 企业ID
	 */
	@ApiModelProperty(value = "企业ID")
	private Long companyId;
	/**
	 * 企业编码
	 */
	@ApiModelProperty(value = "企业编码")
	private String customerCode;

	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private Long userId;

	@ApiModelProperty(value = "(大中小微企业)企业类型")
	private String  companyType;

}
