package org.springblade.customer.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerCheckVo  implements Serializable {
	private static final long serialVersionUID = 1L;
	/***
	 * id 登录账号
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long  id;

	/***
	 * ip地址
	 */
	private String addr;

	/***
	 * uuid 校验信息
	 */
	private String  uuid;
	/**
	 * 用户登录进去之后的id
	 */
	private  Long  typeId;
}
