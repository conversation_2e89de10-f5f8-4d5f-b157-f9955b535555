package org.springblade.customer.vo;

/**
 * <AUTHOR>
 */

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.customer.entity.QuotaUseDetails;

/**
 * 额度使用明细表视图实体类
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "QuotaUseDetailsVO对象", description = "额度使用明细表")
public class QuotaUseDetailsVO extends QuotaUseDetails {
	private static final long serialVersionUID = 1L;
}
