/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * 房产抵押贷款实体类
 *
 * <AUTHOR>
 * @since 2022-02-08
 */
@Data
@TableName("jrzh_customer_liabilities_loan")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LiabilitiesLoan对象", description = "资产负债-房产抵押贷款")
public class LiabilitiesLoan extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 融资机构
	*/
		@ApiModelProperty(value = "融资机构")
		private String institution;
	/**
	* 信贷品种
	*/
		@ApiModelProperty(value = "信贷品种")
		private String varieties;
	/**
	* 授信期限
	*/
		@ApiModelProperty(value = "授信期限")
		private Integer term;
	/**
	* 授信余额
	*/
		@ApiModelProperty(value = "授信余额")
		private BigDecimal balance;
	/**
	* 担保条件
	*/
		@ApiModelProperty(value = "担保条件")
		private String conditionalGuaranty;


	/***
	 * 企业ID
	 */
	@ApiModelProperty(value = "融资企业企业ID")
	private Long companyId;

	/***
	 *  负债 类型
	 */
	@ApiModelProperty(value = "1-企业 2-法人 （1-借款人 2-直系亲属）")
	private Integer type ;
}
