package org.springblade.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.common.enums.GoodsEnum;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-18  14:12
 * @Description: 信贷策略类
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum ProductLoanReportStrategyEnum {
    /**
     * 应收融资信贷策略类
     */
    RECEIVABLE_PRODUCT_LOAN_REPORT_STRATEGY("RECEIVABLE_PRODUCT_LOAN_REPORT_STRATEGY", GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE),
    /**
     * 代采融资信贷策略类
     */
    PURCHASE_PRODUCT_LOAN_REPORT_STRATEGY("PURCHASE_PRODUCT_LOAN_REPORT_STRATEGY", GoodsEnum.AGENT_PURCHASE_FINANCING),
    /**
     * 云信融资信贷策略类
     */

    CLOUD_CREDIT_PRODUCT_LOAN_REPORT_STRATEGY("CLOUD_CREDIT_PRODUCT_LOAN_REPORT_STRATEGY", GoodsEnum.CLOUD_CREDIT),
    /**
     * 质押融资信贷策略类
     */

    PLEDGE_PRODUCT_LOAN_REPORT_STRATEGY("PLEDGE_PRODUCT_LOAN_REPORT_STRATEGY", GoodsEnum.GOODS_PLEDGE),
    /**
     * 订单融资策略类
     */
    ORDER_FINANCING_PRODUCT_LOAN_REPORT_STRATEGY("ORDER_FINANCING_PRODUCT_LOAN_REPORT_STRATEGY", GoodsEnum.ORDER_FINANCING),
    ;

    /**
     * 服务名
     */
    private String service;
    /**
     * 产品类型
     */
    private GoodsEnum goodsType;

    public static String getServiceName(Integer goodsType) {
        for (ProductLoanReportStrategyEnum value : ProductLoanReportStrategyEnum.values()) {
            if (goodsType.equals(value.getGoodsType().getCode())) {
                return value.getService();
            }
        }
        return null;
    }
}
