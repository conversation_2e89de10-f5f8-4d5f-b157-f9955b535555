package org.springblade.report.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessReport {

	@ApiModelProperty("融资编号")
	private String financeNo;

	@ApiModelProperty("融资申请时间")
	private String financeApplyDate;

	@ApiModelProperty("审批通过时间")
	private String passTime;

	@ApiModelProperty("经办人")
	private String approveUser;

	@ApiModelProperty("业务类型")
	private String businessType;

	@ApiModelProperty("客户名称")
	private String userName;

	@ApiModelProperty("放款时间")
	private String loanDate;

	@ApiModelProperty("放款金额")
	private BigDecimal loanAmount;

	@ApiModelProperty("年利率")
	private String annualInterestRate;

	@ApiModelProperty("到期日")
	private String expireTime;

	@ApiModelProperty("已还本金")
	private BigDecimal repaidPrincipal;

	@ApiModelProperty("已还利息")
	private BigDecimal repaidInterest;

	@ApiModelProperty("状态")
	private String status;

	@ApiModelProperty("逾期金额")
	private BigDecimal overdueAmount;

	@ApiModelProperty("结清时间")
	private LocalDateTime settleDate;
}
