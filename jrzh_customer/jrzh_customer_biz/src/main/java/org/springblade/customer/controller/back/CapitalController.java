/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.dto.CapitalListDTO;
import org.springblade.customer.entity.Capital;
import org.springblade.customer.service.ICapitalService;
import org.springblade.customer.vo.CapitalVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * jrzh_capital 控制器
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_BACK + "/customer/capital")
@Api(tags = "资金方接口")
public class CapitalController extends BladeController {

	private final ICapitalService capitalService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入capital")
	public R<CapitalVO> detail(Long id) {
		return R.data(capitalService.detail(id));
	}

	/**
	 * 分页 jrzh_capital
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入capital")
	public R<IPage<CapitalVO>> list(CapitalListDTO capital, Query query) {
		return R.data(capitalService.selectCapitalPage(capital, query));
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入frontCoreList")
	@PreAuth("hasPermission('customer:frontcorelist:list') or hasRole('administrator')")
	public R<IPage<CapitalVO>> page(CapitalListDTO capital,Query query) {
		IPage<CapitalVO> pages = capitalService.getPageCapitalist(capital,query);
		return R.data(pages);
	}

	@GetMapping("/lazy-list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入frontCoreList")
	@PreAuth("hasPermission('customer:frontcorelist:list') or hasRole('administrator')")
	public R<List<CapitalVO>> lazyList(CapitalListDTO capital,Query query) {
		List<CapitalVO> pages = capitalService.getPageCapitaLazyList(capital,query);
		return R.data(pages);
	}

	@GetMapping("/all")
	@ApiOperation("查询所有资方")
	public R<List<CapitalVO>> all(String tenantId) {
		if (StringUtil.isNotBlank(tenantId) && StringUtil.isBlank(AuthUtil.getTenantId())) {
			List<CapitalVO> list = TenantBroker.applyAs(tenantId, e -> {
				return capitalService.selectAllCapitalList();
			});
			return R.data(list);
		}
		return R.data(capitalService.selectAllCapitalList());
	}

	@GetMapping("/getByCompanyId")
	@ApiOperation("根据部门id查询资方")
	public R<Capital> getByDeptId(@RequestParam Long companyId) {
		return R.data(capitalService.getByCompanyId(companyId));
	}


	/*@GetMapping("/tree")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<CapitalVO>> tree( ) {
		List<CapitalVO> tree = capitalService.tree();
		return R.data(tree);
	}*/

	@GetMapping("/getCapitalNameAll")
	@ApiOperation("根据名称查询所有资方")
	public R<List<CapitalVO>> getCapitalNameAll(String name ) {
		return R.data(capitalService.getCapitalNameAll(name));
	}

	}


