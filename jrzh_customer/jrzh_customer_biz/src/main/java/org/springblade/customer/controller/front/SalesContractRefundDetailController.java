/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.front;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.SalesContractRefundDetail;
import org.springblade.customer.service.ISalesContractRefundDetailService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.SalesContractRefundDetailVO;
import org.springblade.customer.wrapper.SalesContractRefundDetailWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 应收账款回款信息 控制器
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/customer/salesContractRefundDetail")
@Api(value = "应收账款回款信息", tags = "应收账款回款信息接口")
public class SalesContractRefundDetailController extends BladeController {

	private final ISalesContractRefundDetailService salesContractRefundDetailService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入salesContractRefundDetail")
	@PreAuth("hasPermission('customer:salesContractRefundDetail:detail') or hasRole('administrator')")
	public R<SalesContractRefundDetailVO> detail(SalesContractRefundDetail salesContractRefundDetail) {
		SalesContractRefundDetail detail = salesContractRefundDetailService.getOne(Condition.getQueryWrapper(salesContractRefundDetail));
		return R.data(SalesContractRefundDetailWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 应收账款回款信息
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入salesContractRefundDetail")
	@PreAuth("hasPermission('customer:salesContractRefundDetail:list') or hasRole('administrator')")
	public R<IPage<SalesContractRefundDetailVO>> list(SalesContractRefundDetail salesContractRefundDetail, Query query) {
		IPage<SalesContractRefundDetail> pages = salesContractRefundDetailService.page(Condition.getPage(query), Condition.getQueryWrapper(salesContractRefundDetail));
		return R.data(SalesContractRefundDetailWrapper.build().pageVO(pages));
	}

	/**
	 * 新增 应收账款回款信息
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入salesContractRefundDetail")
	@PreAuth( "hasPermission('customer:salesContractRefundDetail:save') or hasRole('administrator') or hasRole('admin') or hasRole('financing_admin')")
	public R<Boolean> save(@Valid @RequestBody SalesContractRefundDetail salesContractRefundDetail) {
		salesContractRefundDetail.setOperatorId(MyAuthUtil.getPersonalUserId());
		return R.status(salesContractRefundDetailService.saveDetail(salesContractRefundDetail));
	}
}
