/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.entity.FrontValue;
import org.springblade.customer.vo.FrontValueVO;

/**
 * 企业价值分析 服务类
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
public interface IFrontValueService extends BaseService<FrontValue> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param frontValue
	 * @return
	 */
	IPage<FrontValueVO> selectFrontValuePage(IPage<FrontValueVO> page, FrontValueVO frontValue);

	/**
	 * 根据企业id查询
	 * @param companyId
	 * @return
	 */
    FrontValue getByCompanyId(Long companyId);

	/**
	 * 更新价值分析
	 * @param frontValue
	 * @param userId
	 */
	Long updateFrontValue(FrontValue frontValue, Long userId);


	/****
	 * 更具ID进行更新
	 */
    boolean	updateFrontValueById(FrontValue frontValue);
}
