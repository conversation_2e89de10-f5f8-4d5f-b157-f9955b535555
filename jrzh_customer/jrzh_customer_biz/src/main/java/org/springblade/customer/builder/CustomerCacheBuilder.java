package org.springblade.customer.builder;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.FIFOCache;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2022年05月10日 10:32
 */
@Configuration
public class CustomerCacheBuilder {

	FIFOCache<String,Object> customerCache= CacheUtil.newFIFOCache(50, Duration.ofMinutes(10).toMillis());

	@Bean
	public  FIFOCache<String,Object> init(){
		return  customerCache;
	}

	public  void  putKeyValue(String key,Object value){
		customerCache.put(key,value);
	}


	public  Object  getKeyValue(String key){
		if(customerCache.containsKey(key)){
			return  customerCache.get(key);
		}
		return  null;
	}


	public  boolean containsKey(String key){
        return customerCache.containsKey(key);
    }

}
