package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.EnterpriseAdopt;
import org.springblade.customer.vo.EnterpriseAdoptVO;

import java.util.Objects;

/**
 * 企业实名状态包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
public class EnterpriseAdoptWrapper extends BaseEntityWrapper<EnterpriseAdopt, EnterpriseAdoptVO> {

    public static EnterpriseAdoptWrapper build() {
        return new EnterpriseAdoptWrapper();
    }

    @Override
    public EnterpriseAdoptVO entityVO(EnterpriseAdopt EnterpriseAdopt) {
        EnterpriseAdoptVO EnterpriseAdoptVO = Objects.requireNonNull(BeanUtil.copy(EnterpriseAdopt, EnterpriseAdoptVO.class));

        //User createUser = UserCache.getUser(  EnterpriseAdopt.getCreateUser());
        //User updateUser = UserCache.getUser(  EnterpriseAdopt.getUpdateUser());
        //  EnterpriseAdoptVO.setCreateUserName(createUser.getName());
        //  EnterpriseAdoptVO.setUpdateUserName(updateUser.getName());

        return EnterpriseAdoptVO;
    }
}