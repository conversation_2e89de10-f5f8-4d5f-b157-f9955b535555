package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.QuotaHistory;
import org.springblade.customer.service.IQuotaHistoryService;
import org.springblade.customer.vo.QuotaHistoryVO;
import org.springblade.customer.wrapper.QuotaHistoryWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 额度变化表 控制器
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_RISKMANA + CommonConstant.WEB_BACK + "/riskmana/quotaHistory")
@Api(value = "额度变化表", tags = "额度变化表接口")
public class QuotaHistoryController extends BladeController {

	private final IQuotaHistoryService quotaHistoryService;

	/**
	 * 分页 额度变化表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入quotaHistory")
	@PreAuth("hasPermission('quotaHistory:quotaHistory:list') or hasRole('administrator')")
	public R<IPage<QuotaHistoryVO>> list(QuotaHistory quotaHistory, Query query) {
		LambdaQueryWrapper<QuotaHistory> lwq = new LambdaQueryWrapper<>();
		lwq.eq(QuotaHistory::getEnterpriseQuotaId,quotaHistory.getEnterpriseQuotaId());
		lwq.orderByDesc(BaseEntity::getUpdateTime);
		IPage<QuotaHistory> pages = quotaHistoryService.page(Condition.getPage(query), lwq);
		return R.data(QuotaHistoryWrapper.build().pageVO(pages));
	}
}
