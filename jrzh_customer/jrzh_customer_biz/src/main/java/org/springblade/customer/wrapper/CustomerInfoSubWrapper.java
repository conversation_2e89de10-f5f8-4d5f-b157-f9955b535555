package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.CustomerInfoSub;
import org.springblade.customer.vo.CustomerInfoSubVO;

import java.util.Objects;

/**
 * 企业基本认证信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-02-15
 */
public class CustomerInfoSubWrapper extends BaseEntityWrapper<CustomerInfoSub, CustomerInfoSubVO> {

    public static CustomerInfoSubWrapper build() {
        return new CustomerInfoSubWrapper();
    }

    @Override
    public CustomerInfoSubVO entityVO(CustomerInfoSub CustomerInfoSub) {
        CustomerInfoSubVO CustomerInfoSubVO = Objects.requireNonNull(BeanUtil.copy(CustomerInfoSub, CustomerInfoSubVO.class));

        //User createUser = UserCache.getUser(  CustomerInfoSub.getCreateUser());
        //User updateUser = UserCache.getUser(  CustomerInfoSub.getUpdateUser());
        //  CustomerInfoSubVO.setCreateUserName(createUser.getName());
        //  CustomerInfoSubVO.setUpdateUserName(updateUser.getName());

        return CustomerInfoSubVO;
    }
}
