package org.springblade.customer.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025年04月25日 10:22
 */
@Data
public class FinanceCustomerVo {
    /**
     *  企业法人姓名
     */
    private String legalPersonName;
    /**
     * 企业名称
     */
    private String name;
    /**
     * 所属人
     */
    private String personalName;
    /**
     * 企业用户ID  company_id
     */
    private String userId;

    /**
     * 统一社会信用代码
     */
    private String creditCode;
    /**
     * 用户类型ID
     */
    private Long userTypeId;
    /**
     * 创建日期
     */
    private String createTime;
    /**
     * 企业编码
     */
    private String enterpriseCode;

}
