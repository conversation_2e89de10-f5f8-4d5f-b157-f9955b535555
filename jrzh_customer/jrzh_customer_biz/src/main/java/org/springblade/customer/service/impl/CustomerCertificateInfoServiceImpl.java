/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.ProcessTypeEnum;
import org.springblade.common.utils.DateUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.dto.CetificateAttachDTO;
import org.springblade.customer.dto.CustomerCertificateInfoDTO;
import org.springblade.customer.entity.CustomerCertificateInfo;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.customer.mapper.CustomerCertificateInfoMapper;
import org.springblade.customer.service.ICustomerCertificateInfoService;
import org.springblade.customer.service.ICustomerCertificateInfoSubService;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerCertificateInfoVO;
import org.springblade.othersapi.sky.service.ISkyEyeService;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户证件资料表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Service
@RequiredArgsConstructor
public class CustomerCertificateInfoServiceImpl extends BaseServiceImpl<CustomerCertificateInfoMapper, CustomerCertificateInfo> implements ICustomerCertificateInfoService {

    private final ICustomerBusinessInfoService customerBusinessInfoService;
    private final ICustomerInfoService customerInfoService;


    private final ISkyEyeService skyEyeService;

    @Override
    public IPage<CustomerCertificateInfoVO> selectCustomerCertificateInfoPage(IPage<CustomerCertificateInfoVO> page, CustomerCertificateInfoVO customerCertificateInfo) {

        if (ObjectUtil.isNotEmpty(customerCertificateInfo.getYear())) {
            customerCertificateInfo.setStartTime(DateUtils.getYearFirst(Integer.parseInt(customerCertificateInfo.getYear())));
            customerCertificateInfo.setEndTime(DateUtils.getYearLast(Integer.parseInt(customerCertificateInfo.getYear())));
        }
        List<CustomerCertificateInfo> customerCertificateInfos = baseMapper.selectCustomerCertificateInfoPage(page, customerCertificateInfo);
        List<CustomerCertificateInfoVO> customerCertificateInfoVOs = BeanUtil.copy(customerCertificateInfos, CustomerCertificateInfoVO.class);
        return page.setRecords(customerCertificateInfoVOs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadMultiFile(List<CustomerCertificateInfoDTO> customerCertificateInfoDTOS) {
        String companyId = MyAuthUtil.getCompanyId();

        customerCertificateInfoDTOS = customerCertificateInfoDTOS.stream().filter(e -> e.getCompanyName() != null || e.getCompanyCreditCode() != null).collect(Collectors.toList());
        Assert.isFalse(ObjectUtil.isEmpty(customerCertificateInfoDTOS), "公司名称与公司统一信用代码必传一个");

        if (ObjectUtil.isEmpty(companyId) || "null".equals(companyId)) {
            CustomerCertificateInfoDTO customerCertificateInfoDTO = customerCertificateInfoDTOS.get(0);
            companyId = customerCertificateInfoDTO.getCompanyId();
            if (ObjectUtil.isEmpty(companyId)) {
                companyId = customerCertificateInfoDTO.getCustomerId();
            }
        }
        if (ObjectUtil.isNotEmpty(companyId)) {
            SpringUtil.getBean(ICustomerCertificateInfoSubService.class).toCompareCertificateInfoType(customerCertificateInfoDTOS, Long.valueOf(companyId));
        }

        Set<String> companyName = customerCertificateInfoDTOS.stream().map(CustomerCertificateInfo::getCompanyName).collect(Collectors.toSet());
        Set<String> companyCreditCode = customerCertificateInfoDTOS.stream().map(CustomerCertificateInfo::getCompanyCreditCode).collect(Collectors.toSet());
        Assert.isFalse(companyName.size() > 1 || companyCreditCode.size() > 1, "只能上传当前页面公司的文件");
        List<CustomerCertificateInfo> customerCertificateInfos = BeanUtil.copy(customerCertificateInfoDTOS, CustomerCertificateInfo.class);
        List<CustomerCertificateInfo> newCustomerCertificate = new ArrayList<>();
        List<CustomerCertificateInfo> splitCustomerCertificate = new ArrayList<>();
        newCustomerCertificate.addAll(customerCertificateInfos);

        String finalCompanyId = companyId;
        newCustomerCertificate = newCustomerCertificate.stream().map(e -> {
            List<Attach> attach = SpringUtil.getBean(IAttachService.class).lambdaQuery().in(Attach::getId, Func.toLongList(e.getAttachId())).list();
            if (attach.size() > 1) {
                attach.stream().forEach(s -> {
                    CustomerCertificateInfo customerCertificateInfo = new CustomerCertificateInfo();
                    customerCertificateInfo = BeanUtil.copy(e, CustomerCertificateInfo.class);
                    customerCertificateInfo.setAttachId(String.valueOf(s.getId()));
                    customerCertificateInfo.setFileType(s.getExtension());
                    customerCertificateInfo.setStatus(0);
                    splitCustomerCertificate.add(customerCertificateInfo);

                });
            } else {
                e.setFileType(Func.join(attach.stream().map(Attach::getExtension).collect(Collectors.toSet())));
            }
            e.setStatus(0);
            e.setCompanyId(finalCompanyId);
            return e;
        }).collect(Collectors.toList());
        List<Long> filetype = splitCustomerCertificate.stream().map(CustomerCertificateInfo::getType).collect(Collectors.toList());
        newCustomerCertificate = newCustomerCertificate.stream().filter(e -> !filetype.contains(e.getType())).collect(Collectors.toList());
        newCustomerCertificate.addAll(splitCustomerCertificate);
        return this.saveOrUpdateBatch(newCustomerCertificate);
    }

    @Override
    public Boolean cancel(CustomerCertificateInfoDTO customerCertificateInfoDTO) {
        CustomerCertificateInfo customerCertificateInfo = this.lambdaQuery().eq(CustomerCertificateInfo::getId, customerCertificateInfoDTO.getId()).one();
        customerCertificateInfo.setStatus(1);
        return this.saveOrUpdate(customerCertificateInfo);
    }

    @Override
    public R downLoadFile(Long id) {
        CustomerCertificateInfo customerCertificateInfo = this.lambdaQuery().eq(CustomerCertificateInfo::getId, id).one();
        Assert.isFalse(ObjectUtil.isEmpty(customerCertificateInfo), "证件信息不存在");

        Attach attach = SpringUtil.getBean(IAttachService.class).getById(Func.toLongList(customerCertificateInfo.getAttachId()).get(0));
        Assert.isFalse(ObjectUtil.isEmpty(attach), "证件信息不存在");
        Kv kv = Kv.create().set("name", attach.getOriginalName()).set("url", attach.getLink()).set("attachId", attach.getId());
        return R.data(kv);
    }

    @Override
    public List<CetificateAttachDTO> downLoadMutiFile(String ids) {
        List<Long> attachIds = Func.toLongList(ids);
        if (attachIds.size() == 0) {
            return null;
        }
        List<Attach> attach = SpringUtil.getBean(IAttachService.class).lambdaQuery().in(attachIds.size() > 0, Attach::getId, attachIds).list();
        Assert.isFalse(attach.size() == 0, "证件信息不存在");
        return BeanUtil.copy(attach, CetificateAttachDTO.class);
    }

    @Override
    public CustomerCertificateInfo uploadSingleCertificate(CustomerCertificateInfo customerCertificateInfo) {

        if (ObjectUtil.isEmpty(customerCertificateInfo.getCompanyId())) {
            customerCertificateInfo.setCompanyId(MyAuthUtil.getCompanyId());
        }
        Assert.isFalse(Objects.isNull(customerCertificateInfo.getCompanyName()) && Objects.isNull(customerCertificateInfo.getCompanyCreditCode()),
                "公司名称和信用代码必须传入一个");

        String companyName = customerCertificateInfo.getCompanyName();
        if (ObjectUtil.isEmpty(companyName)) {
            customerCertificateInfo.setCompanyCreditCode(customerCertificateInfo.getCompanyCreditCode());
            //TODO 等待接口
            //companyName = skyEyeService.getCompanyNameByCreditCode(customerCertificateInfo.getCompanyCreditCode());
            customerCertificateInfo.setCompanyName(companyName);
        }

        if (ObjectUtil.isNotEmpty(customerCertificateInfo.getAttachId())) {
            Attach attach = SpringUtil.getBean(IAttachService.class).lambdaQuery().eq(Attach::getId, Func.toLongList(customerCertificateInfo.getAttachId()).get(0)).one();
            customerCertificateInfo.setFileType(attach.getExtension());
        }
        customerCertificateInfo.setStatus(0);
        return customerCertificateInfo;
    }

    @Override
    public boolean batchInsert(List<CustomerCertificateInfo> certificateInfos) {
        return baseMapper.batchInsert(certificateInfos) == certificateInfos.size();
    }

    @Override
    public void saveCertificateInfo(Map<String, Object> variables, Integer status) {
        Object customerVar = variables.get(ProcessConstant.CUSTOMER_MATERIAL);
        CustomerMaterial customerMaterial=ObjectUtil.isNotEmpty(customerVar)? JSONUtil.toBean(JSONUtil.toJsonStr(customerVar),CustomerMaterial.class) :null;
        Long userId = (Long) variables.get(ProcessConstant.USER_ID);
        String taskId = (String) variables.get(ProcessConstant.TASK_ID);
        String processInstanceId = (String) variables.get(ProcessConstant.PROCESS_INSTANCE_ID);
        String processNo = (String) variables.get(WfProcessConstant.PROCESS_NO);
        Integer processType = (Integer) variables.get(ProcessConstant.PROCESS_TYPE);
        JSONArray jsonArray = JSON.parseArray(customerMaterial.getSupplementMaterial());

        CustomerBusinessInfo customerBusinessInfo = customerInfoService.getCustomerBusinessInfoByCompanyId(userId.toString());
        List<CustomerCertificateInfo> customerCertificateInfoList = Lists.newArrayList();
        for (Object object : jsonArray) {
            JSONObject obj = (JSONObject) object;
            JSONArray uploadArr = obj.getJSONArray("uploadArr");
            for (Object o : uploadArr) {
                JSONObject material = (JSONObject) o;
                String attachId = material.getString("attachId");
                if (StringUtil.isBlank(attachId)) {
                    continue;
                }
                Long materialId = obj.getLong("materialId");
                String url = material.getString("url");
                String fileSuffix = url.substring(url.lastIndexOf(".") + 1);
                CustomerCertificateInfo certificateInfo = new CustomerCertificateInfo();
                certificateInfo.setAttachId(attachId);
                certificateInfo.setCompanyId(userId.toString());
                certificateInfo.setType(materialId);
                certificateInfo.setFileType(fileSuffix);
                certificateInfo.setTaskId(taskId);
                certificateInfo.setProcessId(processInstanceId);
                certificateInfo.setTaskNo(processNo);
                certificateInfo.setTaskName(ProcessTypeEnum.getByCode(processType).getName());
                certificateInfo.setCompanyName(Objects.nonNull(customerBusinessInfo)?customerBusinessInfo.getCompanyName():"");
                certificateInfo.setCompanyCreditCode(Objects.nonNull(customerBusinessInfo)?customerBusinessInfo.getCreditCode():"");
                customerCertificateInfoList.add(certificateInfo);
            }
        }
        if (!CollectionUtils.isEmpty(customerCertificateInfoList)) {
            customerCertificateInfoList.forEach(customerCertificateInfo -> customerCertificateInfo.setStatus(status));
            baseMapper.batchInsert(customerCertificateInfoList);
        }
    }
}
