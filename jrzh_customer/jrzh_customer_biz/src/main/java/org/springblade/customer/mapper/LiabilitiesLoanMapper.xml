<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.LiabilitiesLoanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="liabilitiesLoanResultMap" type="org.springblade.customer.entity.LiabilitiesLoan">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="institution" property="institution"/>
        <result column="varieties" property="varieties"/>
        <result column="term" property="term"/>
        <result column="balance" property="balance"/>
        <result column="conditional_guaranty" property="conditionalGuaranty"/>
    </resultMap>


    <select id="selectLiabilitiesLoanPage" resultMap="liabilitiesLoanResultMap">
        select * from jrzh_customer_liabilities_loan where is_deleted = 0
    </select>

</mapper>
