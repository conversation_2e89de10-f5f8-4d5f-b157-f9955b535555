/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.entity.CoreInvitedLog;
import org.springblade.customer.entity.CoreTradeBackground;
import org.springblade.customer.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 贸易背景 服务类
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
public interface ICoreTradeBackgroundService extends BaseService<CoreTradeBackground> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param coreTradeBackground
	 * @return
	 */
	IPage<CoreTradeBackgroundVO> selectCoreTradeBackgroundPage(IPage<CoreTradeBackgroundVO> page, CoreTradeBackgroundVO coreTradeBackground);

	/**
	 * 通过核心企业id查询关联的上下游企业
	 *
	 * @param id
	 * @return
	 */
	Map<String, List<TradeBackgroundVO>> getList(Long id);

	/**
	 * 获取下游详情
	 *
	 * @param id
	 * @return
	 */
	CoreTradeBackgroundLowerVO lowerDetails(Long id);

	/**
	 * 获取与上游的贸易明细
	 *
	 * @param backId
	 * @return
	 */
	CoreTradeBackgroundHeightVO heightDetails(Long backId);

	/**
	 * 根据背景id 删除贸易背景
	 *
	 * @param id
	 * @return
	 */
	boolean deleteBack(Long id);

	/**
	 * 保存邀请信息
	 *
	 * @param coreInvitedLog
	 * @return
	 */
	CoreInvitedLog saveInviteCode(CoreInvitedLog coreInvitedLog);

	/**
	 * 短信邀请
	 *
	 * @param coreInvitedLog
	 * @return
	 */
	boolean sendInvite(CoreInvitedLog coreInvitedLog);

	/**
	 * 根据核心id获取上游贸易背景
	 * companyHeightId为上游id
	 *
	 * @param id
	 * @return
	 */
	List<TradeBackgroundVO> listHeightByCompanyId(Long id);

	/**
	 * 根据核心id获取下游贸易背景
	 * companyLowerId为下游id
	 *
	 * @param id
	 * @return
	 */
	List<TradeBackgroundVO> listLowerByCompanyId(Long id);

	/**
	 * 获取上游云信贸易背景详情
	 * @param backId 背景id
	 * @return
	 */
	CoreTradeBackgroundPayHeightVO heightBillDetails(Long backId);
}
