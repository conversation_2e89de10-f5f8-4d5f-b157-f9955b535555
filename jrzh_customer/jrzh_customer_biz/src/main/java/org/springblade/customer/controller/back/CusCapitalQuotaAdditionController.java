/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.RedisLockUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.customer.dto.CusCapitalQuotaAdditionDTO;
import org.springblade.customer.entity.CusCapitalQuota;
import org.springblade.customer.entity.CusCapitalQuotaAddition;
import org.springblade.customer.service.ICusCapitalQuotaAdditionService;
import org.springblade.customer.service.ICusCapitalQuotaService;
import org.springblade.customer.vo.CusCapitalQuotaAdditionVO;
import org.springblade.customer.vo.SalesContractVO;
import org.springblade.customer.wrapper.CusCapitalQuotaAdditionWrapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 添加额度 控制器
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_BACK + "/customer/cusCapitalQuotaAddition")
@Api(value = "添加额度", tags = "添加额度接口")
public class CusCapitalQuotaAdditionController extends BladeController {

    private final ICusCapitalQuotaAdditionService cusCapitalQuotaAdditionService;

    private final ICusCapitalQuotaService cusCapitalQuotaService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入cusCapitalQuotaAddition")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:detail') or hasRole('administrator')")
    public R<CusCapitalQuotaAdditionVO> detail(Long companyId) {
        CusCapitalQuotaAddition detail = cusCapitalQuotaAdditionService.getOne(Wrappers.<CusCapitalQuotaAddition>lambdaQuery().eq(CusCapitalQuotaAddition::getCompanyId, companyId));
        if (ObjectUtil.isEmpty(detail)) {
            CusCapitalQuotaAdditionVO data = new CusCapitalQuotaAdditionVO();
            data.setCompanyId(companyId);
            return R.data(data);
        }
        CusCapitalQuotaAdditionVO cusCapitalQuotaAdditionVO = CusCapitalQuotaAdditionWrapper.build().entityVO(detail);
        cusCapitalQuotaAdditionVO.setAnnualInterestRate(cusCapitalQuotaAdditionVO.getAnnualInterestRate().setScale(3));
        if (!StringUtils.isEmpty(detail)) {
            CusCapitalQuota one = cusCapitalQuotaService.getOne(Wrappers.<CusCapitalQuota>lambdaQuery().eq(CusCapitalQuota::getCompanyId, detail.getCompanyId()));
            BigDecimal setScale = one.getCreditTotal().setScale(4);
            cusCapitalQuotaAdditionVO.setCreditTotal(setScale);
        }
        return R.data(cusCapitalQuotaAdditionVO);
    }

    /**
     * 分页 添加额度
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入cusCapitalQuotaAddition")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:list') or hasRole('administrator')")
    public R<IPage<CusCapitalQuotaAdditionVO>> list(CusCapitalQuotaAddition cusCapitalQuotaAddition, Query query) {
        IPage<CusCapitalQuotaAddition> pages = cusCapitalQuotaAdditionService.page(Condition.getPage(query), Condition.getQueryWrapper(cusCapitalQuotaAddition));
        return R.data(CusCapitalQuotaAdditionWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 添加额度
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入cusCapitalQuotaAddition")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:page') or hasRole('administrator')")
    public R<IPage<CusCapitalQuotaAdditionVO>> page(CusCapitalQuotaAdditionVO cusCapitalQuotaAddition, Query query) {
        IPage<CusCapitalQuotaAdditionVO> pages = cusCapitalQuotaAdditionService.selectCusCapitalQuotaAdditionPage(Condition.getPage(query), cusCapitalQuotaAddition);
        return R.data(pages);
    }

    /**
     * 新增 添加额度
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入cusCapitalQuotaAddition")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody CusCapitalQuotaAdditionDTO cusCapitalQuotaAddition) {
        CusCapitalQuota cusCapitalQuota = new CusCapitalQuota();
        cusCapitalQuota.setCreditTotal(cusCapitalQuotaAddition.getCreditTotal());
        cusCapitalQuotaService.save(cusCapitalQuota);
        return R.status(cusCapitalQuotaAdditionService.save(cusCapitalQuotaAddition));
    }

    /**
     * 修改 添加额度
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入cusCapitalQuotaAddition")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody CusCapitalQuotaAddition cusCapitalQuotaAddition) {

        return R.status(cusCapitalQuotaAdditionService.updateById(cusCapitalQuotaAddition));
    }

    /**
     * 新增或修改 添加额度
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入cusCapitalQuotaAddition")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody CusCapitalQuotaAdditionDTO cusCapitalQuotaAddition) {
        Long companyId = cusCapitalQuotaAddition.getCompanyId();
        LocalDateTime fromValidity = cusCapitalQuotaAddition.getFromValidity();
        LocalDateTime fxpirationDate = cusCapitalQuotaAddition.getFxpirationDate();
        if (fromValidity.compareTo(fxpirationDate) > 0) {
            throw new ServiceException("生效日不能大于终止日!");
        }
        //获取授信总额
        BigDecimal creditTotal = cusCapitalQuotaAddition.getCreditTotal();
        if (StringUtils.isEmpty(companyId)) {
            throw new ServiceException("资金方不存在");
        }
        CusCapitalQuota oldQuota = cusCapitalQuotaService.getOne(Wrappers.<CusCapitalQuota>lambdaQuery()
                .eq(CusCapitalQuota::getCompanyId, companyId));
        //不存在新建额度 存在进行额度调整
        if (ObjectUtil.isEmpty(oldQuota)) {
            //额度新增
            newCapitalQuota(cusCapitalQuotaAddition, companyId, creditTotal);
        } else {
            //额度调整
            adjustCapitalQuota(cusCapitalQuotaAddition, oldQuota, creditTotal);
        }
        return R.status(cusCapitalQuotaAdditionService.saveOrUpdate(cusCapitalQuotaAddition));
    }

    /**
     * 创建额度
     *
     * @param cusCapitalQuotaAddition
     * @param companyId
     * @param creditTotal
     */
    private void newCapitalQuota(CusCapitalQuotaAdditionDTO cusCapitalQuotaAddition, Long companyId, BigDecimal creditTotal) {
        CusCapitalQuota oldQuota;
        oldQuota = new CusCapitalQuota();
        oldQuota.setCompanyId(companyId);
        oldQuota.setAcGranted(new BigDecimal(0));
        oldQuota.setAmountReleased(new BigDecimal(0));
        oldQuota.setPenPrincipal(new BigDecimal(0));
        oldQuota.setPenCost(new BigDecimal(0));
        oldQuota.setOverdueAmount(new BigDecimal(0));
        oldQuota.setTotalExpenses(new BigDecimal(0));
        oldQuota.setTotalPrincipal(new BigDecimal(0));
        oldQuota.setUnderCredit(new BigDecimal(0));
        oldQuota.setCreditTotal(creditTotal);
        oldQuota.setAcAmount(cusCapitalQuotaAddition.getCreditTotal());
        cusCapitalQuotaService.save(oldQuota);
    }

    /**
     * 额度调整
     *
     * @param cusCapitalQuotaAddition
     * @param oldQuota
     * @param creditTotal
     */
    private void adjustCapitalQuota(CusCapitalQuotaAdditionDTO cusCapitalQuotaAddition, CusCapitalQuota oldQuota, BigDecimal creditTotal) {
        //根据策略进行金额调整
        CapitalStrategyContext strategy = getStrategy(1);
        strategy.executeStrategy(cusCapitalQuotaAddition, oldQuota);
    }

    /**
     * 删除 额度
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(cusCapitalQuotaAdditionService.deleteLogic(Func.toLongList(ids)));
    }

    @GetMapping("/totalLoanLimit")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "总授信记录", notes = "传入ids")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:totalLoanLimit') or hasRole('administrator')")
    public R<Map<String, Object>> totalLoanLimit(@ApiParam(value = "主键集合", required = true) @RequestParam Long companyId, Query query) {
        return R.data(cusCapitalQuotaAdditionService.totalLoanLimit(companyId, query));
    }

    @GetMapping("getReceivable")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "应收账款", notes = "传入companyId")
    @PreAuth("hasPermission('customer:cusCapitalQuotaAddition:getReceivable') or hasRole('administrator')")
    public R<IPage<SalesContractVO>> getReceivable(Long companyId, Query query) {
        return R.data(cusCapitalQuotaAdditionService.getReceivable(companyId, query));
    }

    /**
     * 策略模式
     *
     * @param strategy
     * @return
     */
    private CapitalStrategyContext getStrategy(Integer strategy) {
        switch (strategy) {
            case 1:
                return new CapitalStrategyContext(SpringUtil.getBean(CapitalNormStrategy.class));
            default:
                throw new ServiceException("调整策略未找到");
        }
    }
}

@Data
class CapitalStrategyContext {
    private CapitalStrategy capitalStrategy;

    private CapitalStrategyContext() {

    }

    public CapitalStrategyContext(CapitalStrategy capitalStrategy) {
        this.capitalStrategy = capitalStrategy;
    }

    /**
     * 执行调整任务
     *
     * @param cusCapitalQuotaAddition
     * @param capitalQuota
     */
    public void executeStrategy(CusCapitalQuotaAdditionDTO cusCapitalQuotaAddition, CusCapitalQuota capitalQuota) {
        capitalStrategy.adjust(cusCapitalQuotaAddition, capitalQuota);
    }
}

/**
 * 调整策略接口
 */
interface CapitalStrategy {
    /**
     * 额度调整
     *
     * @param cusCapitalQuotaAddition
     * @param capitalQuota
     */
    void adjust(CusCapitalQuotaAdditionDTO cusCapitalQuotaAddition, CusCapitalQuota capitalQuota);
}

/**
 * 调整策略实体类
 */
@RequiredArgsConstructor
@Service
class CapitalNormStrategy implements CapitalStrategy {
    private final ICusCapitalQuotaService cusCapitalQuotaService;
    private final String LOCK_PRE_KEY = "CusCapitalQuota:adjust:";

    @Override
    public void adjust(CusCapitalQuotaAdditionDTO cusCapitalQuotaAddition, CusCapitalQuota capitalQuota) {
        Long quotaId = capitalQuota.getId();
        String LOCK_KEY = LOCK_PRE_KEY + quotaId;
        RedisLockUtils.redisLock(() -> {
            //重新获取最新的额度信息
            final CusCapitalQuota finalCapitalQuota = cusCapitalQuotaService.getById(quotaId);
            BigDecimal oldCreditTotal = finalCapitalQuota.getCreditTotal();
            BigDecimal useTotal = finalCapitalQuota.getAcGranted().add(finalCapitalQuota.getUnderCredit());
            BigDecimal newCreditTotal = cusCapitalQuotaAddition.getCreditTotal();
            if (useTotal.compareTo(newCreditTotal) > 0) {
                throw new ServiceException("调整失败，最低只能调整至:" + useTotal + "元");
            }
            //根据调整策略进行调整
            BigDecimal subVal = newCreditTotal.subtract(oldCreditTotal);
            BigDecimal oldAcAmount = finalCapitalQuota.getAcAmount();
            BigDecimal acAmountChange = null;
            BigDecimal creditAmountChange = null;
            BigDecimal newAcAmount = null;
            // 如果小于0 证明向下调整
            if (subVal.compareTo(BigDecimal.ZERO) < 0) {
                newAcAmount = newCreditTotal.subtract(useTotal);
                creditAmountChange = newCreditTotal.subtract(oldCreditTotal);
                acAmountChange = newAcAmount.subtract(oldAcAmount);
                boolean update = cusCapitalQuotaService.update(Wrappers.<CusCapitalQuota>lambdaUpdate().eq(CusCapitalQuota::getId, quotaId)
                        .set(CusCapitalQuota::getAcAmount, newAcAmount).set(CusCapitalQuota::getCreditTotal, newCreditTotal));
            }
            // 如果大于0，证明向上调整
            if (subVal.compareTo(BigDecimal.ZERO) > 0) {
                newAcAmount = newCreditTotal.subtract(useTotal);
                creditAmountChange = newCreditTotal.subtract(oldCreditTotal);
                acAmountChange = newAcAmount.subtract(oldAcAmount);
                boolean update = cusCapitalQuotaService.update(Wrappers.<CusCapitalQuota>lambdaUpdate().eq(CusCapitalQuota::getId, quotaId)
                        .set(CusCapitalQuota::getAcAmount, newAcAmount).set(CusCapitalQuota::getCreditTotal, newCreditTotal));
            }
        }, LOCK_KEY, ResultCode.FAILURE.getMessage());
    }
}