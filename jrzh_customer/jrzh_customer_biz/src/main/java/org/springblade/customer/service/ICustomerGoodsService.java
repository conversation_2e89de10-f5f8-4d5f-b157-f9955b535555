/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.dto.CustomerGoodsCoreDTO;
import org.springblade.customer.dto.CustomerGoodsDTO;
import org.springblade.customer.dto.CustomerGoodsUnFrozenDTO;
import org.springblade.customer.dto.CustomerProductParam;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.vo.CustomerGoodsVO;
import org.springblade.customer.vo.CustomerSupGoodsVO;
import org.springblade.customer.vo.TradeBackgroundVO;
import org.springblade.process.entity.BusinessProcessProgress;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 客户产品 服务类
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
public interface ICustomerGoodsService extends BaseService<CustomerGoods> {


//    /**
//     * 保存客户产品，以及客户产品绑定贸易背景
//     *
//     * @param goods          产品对象
//     * @param enterpriseId   企业id 融资企业 userId 核心企业 deptId
//     * @param enterpriseType 企业类型 1 融资 2 核心
//     * @return 客户产品id
//     */
//    Long saveCustomerGoods(Goods goods, Long enterpriseId, Integer enterpriseType);
//
//    /**
//     * 保存客户云信产品，以及客户产品绑定贸易背景
//     *
//     * @param cloudProduct   产品对象
//     * @param enterpriseId   企业id 融资企业 userId 核心企业 deptId
//     * @param enterpriseType 企业类型 1 融资 2 核心
//     * @return 客户产品id
//     */
//    Long saveCustomerCloudProduct(CloudProduct cloudProduct, Long enterpriseId, Integer enterpriseType);

    /**
     * 关闭客户产品
     *
     * @param customerGoodsId   客户产品id
     * @param processInstanceId 流程实例id
     * @param goodsId           产品id
     * @param customerId        客户id
     * @param type              类型
     */
    void close(Long customerGoodsId, Long goodsId, String processInstanceId, Long customerId, Integer type);

    /**
     * 根据产品id和客户id查询
     *
     * @param goodsId 产品id
     * @return 客户产品对象
     */
    CustomerGoods getByGoodsIdAndCustomerId(Long goodsId, Long userId);


    /**
     * 设置额度
     *
     * @param variables 流程变量
     */
    void settingAmount(Map<String, Object> variables);

//    /**
//     * 保存客户产品
//     *
//     * @param goodsId            产品id
//     * @param tradeBackgroundIds 贸易背景idList
//     * @return 客户产品id
//     */
//    Long saveCustomerGoods(Long goodsId, List<Long> tradeBackgroundIds);

    /**
     * 查询核心企业绑定产品
     *
     * @param tradeBackgroundId 贸易背景id
     * @return 客户产品VO对象
     */
    CustomerGoodsVO selectEnterpriseBindGoods(Long tradeBackgroundId);

    /**
     * 更新客户产品状态
     *
     * @param customerGoodsId   客户产品id
     * @param status            要更新的状态
     * @param processInstanceId 流程实例id
     */
    void updateStatus(Long customerGoodsId, Integer status, String processInstanceId);

    /**
     * 更新客户产品状态
     *
     * @param customerGoodsId 客户产品id
     * @param status          要更新的状态
     */
    void updateStatus(Long customerGoodsId, Integer status);

    /**
     * 查询产品绑定贸易背景
     *
     * @param goodsId 产品id
     * @return 贸易背景列表
     */
    List<TradeBackgroundVO> selectGoodsTradeBackground(Long goodsId);


    /**
     * 根据客户id和企业类型查询
     *
     * @param customerId     客户id
     * @param enterpriseType 企业类型 1 融资 2 核心
     * @return 客户产品列表
     */
    List<CustomerGoodsVO> getByCustomerAndEnterpriseType(Long customerId, Integer enterpriseType);

    /**
     * 列表查询
     *
     * @param customerId     客户id
     * @param enterpriseType 企业类型 1 融资 2 核心
     * @param goodsIds       产品id
     * @return 客户产品列表
     */
    List<CustomerGoods> listCustomerGoods(Long customerId, Integer enterpriseType, List<Long> goodsIds);

    /**
     * 列表查询
     *
     * @param status         状态
     * @param enterpriseType 企业类型 1 融资 2 核心
     * @param userId
     * @return
     */
    List<CustomerGoods> listCustomerGoods(String status, Integer enterpriseType, Long userId);

    /**
     * 查看我的产品-融资产品列表(产品组)
     * @param status
     * @param enterpriseType
     * @param userId
     * @return
     */
    List<CustomerGoods> getCustomerGoodsList(String status, Integer enterpriseType, Long userId);

    /**
     * 产品到期
     *
     * @param enterpriseQuotaIdList 企业额度id
     */
    void expire(List<Long> enterpriseQuotaIdList);

    /**
     * 删除开通信息
     *
     * @param goodsId        产品id
     * @param userId         用户id
     * @param customerGoodId 客户产品id
     */
    void removeOpenInfo(Long goodsId, Long userId, Long customerGoodId);

    /***
     * 根据企业额度id查询客户产品的
     * @param enterpriseQuotaId 企业额度id
     * @return CustomerGoods
     */
    CustomerGoods getByEnterpriseQuotaId(Long enterpriseQuotaId);

    /**
     * 保存我的产品
     *
     * @param customerGoodsDTO customerGoodsDTO
     * @return customerGoodsId
     */
    Long saveCustomerGoods(CustomerGoodsDTO customerGoodsDTO);

    /**
     * 根据id查询客户产品，并根据id分组
     *
     * @param customerGoodsIds 客户产品id
     * @return 《客户产品id，客户产品》
     */
    Map<Long, CustomerGoods> getMapInId(List<Long> customerGoodsIds);

//    /**
//     * 保存我的代采产品
//     *
//     * @param agentGoods     代采产品
//     * @param enterpriseId   企业id
//     * @param enterpriseType 企业类型
//     * @return Long
//     */
//    Long saveCustomerAgentGoods(AgentGoods agentGoods, Long enterpriseId, Integer enterpriseType);

    /**
     * 根据条件获取符合的商品产品
     *
     * @param financeProportion 融资比例
     * @param collectionType    代付类型 非必填
     * @param commodityId       商品id
     * @return
     */
    List<CustomerSupGoodsVO> listBySearchArg(BigDecimal financeProportion, Integer collectionType, Long commodityId);

    /**
     * 客户产品是否可开通
     *
     * @param goodsId        产品id
     * @param enterpriseId   企业id
     * @param enterpriseType 企业类型
     * @return
     */
    boolean isOpen(Long goodsId, Long enterpriseId, Integer enterpriseType);

    /**
     * 获取客户产品
     *
     * @param id
     * @return
     */
    CustomerGoodsVO getCustomerGoodsVoById(Long id);

    /**
     * 冻结客户相关产品
     *
     * @param ids 客户产品ids
     */
    void frozenValidCustomerGoodsByIds(List<Long> ids);

    /**
     * 根据产品id，查询产品信息
     * @param id 产品id
     * @return
     */
    CustomerGoodsVO processGoodsInfo(Long id);

    /**
     * 产品解冻申请开启
     *
     * @param customerGoodsUnFrozenDTO
     * @return
     */
    BusinessProcessProgress getCustomerGoodsUnFrozenProcessProgress(CustomerGoodsUnFrozenDTO customerGoodsUnFrozenDTO);

    /**
     * 查询客户所有除关闭以外的产品
     *
     * @param userId
     * @return
     */
    List<CustomerGoods> listValidByUserId(Long userId);


    /**
     * 回滚核心企业额度
     *
     * @param
     * @param totalRefundAmount 总已还款金额
     * @param refundAmount 当次还款金额
     * @param totalLoanAmount 总需还款金额
     */
    void returnCoreEnterpriseQuota(Long goodsId, Long companyLowerId, BigDecimal totalRefundAmount, BigDecimal refundAmount,BigDecimal totalLoanAmount);


    /**
     * 根据产品id查询列表
     *
     * @param customerGoodsIds
     * @return
     */
    List<CustomerGoods> selectListByIds(@Param("customerGoodsIds") List<Long> customerGoodsIds);

    /**
     * 保存核心企业客户产品
     *
     * @param customerGoodsDTO
     * @return
     */
    Long saveCustomerOpenGoods(CustomerGoodsCoreDTO customerGoodsDTO);

    /**
     * @param customerProductParam
     * @return java.lang.Long
     * @Description: 新增客户产品
     * @Author: wujm
     * @Date: 2023/4/7 15:51
     **/
    Long saveCustomerProduct(CustomerProductParam customerProductParam);

    /**
     * 更新融资产品-客户产品状态
     * @param groupId
     * @param goodsType
     * @param enterpriseType
     * @param enterpriseId
     * @param status
     * @return
     */
    Boolean updateMultiFundingProductStatus(Long groupId, Integer goodsType, Integer enterpriseType, Long enterpriseId, Integer status);

    /**
     * 客户产品查询
     * @param enterpriseType
     * @param status
     * @param groupId
     * @return
     */
    List<CustomerGoods> getListByEnterpriseTypeAndStatusAndGroupId(Integer enterpriseType, Integer status, Long groupId);

    /**
     * 根据企业id、产品id、企业类型、状态 获取客户产品信息
     * @param enterpriseId
     * @param goodsId
     * @param enterpriseType
     * @param status
     * @return
     */
    CustomerGoods getOneByEnterpriseIdAndGoodsIdAndEnterpriseTypeAndStatus(Long enterpriseId, Long goodsId, Integer enterpriseType, Integer status);

    /**
     * 获取有效状态的客户产品信息
     * @param validStatus
     * @param enterpriseId
     * @param goodsId
     * @return
     */
    List<CustomerGoods> getListByStatusAndEnterpriseIdAndGoodsId(List<Integer> validStatus, Long enterpriseId, Long goodsId);

    /**
     * 查看详情-融资产品  关联资金产品中已开通的产品
     * @param userId
     * @param enterpriseType
     * @param groupId
     * @param goodsIdList
     * @return
     */
    List<CustomerGoods> getListByEnterpriseIdAndEnterpriseTypeAndGroupIdAndGoodsIds(Long userId, Integer enterpriseType, Long groupId, List<Long> goodsIdList);

    /**
     * 查询其它融资产品所关联的资金产品
     * @param userId
     * @param enterpriseType
     * @param groupId
     * @param goodsIdList
     * @return
     */
    List<CustomerGoods> getByEnterpriseIdAndEnterpriseTypeAndGroupIdAndGoodsIdsList(Long userId, Integer enterpriseType, Long groupId, List<Long> goodsIdList);

    /**
     * 检查流程是否存在 不存在则保存进度
     * @param goodsId
     * @param goodsType
     * @param enterpriseId
     * @param enterpriseType
     * @param groupId
     * @return
     */
    Integer getCountByGoodsIdAndGoodsTypeAndEnterpriseIdAndEnterpriseTypeAndGroupId(Long goodsId, Integer goodsType, Long enterpriseId, Integer enterpriseType, Long groupId);

    /**
     * 查询客户产品表中是否存在客户开通该融资产品
     * @param groupId
     * @param goodsId
     * @return
     */
    Integer getCountByGroupIdAndGoodsId(Long groupId, Long goodsId);

    /**
     * 根据产品id、企业id(用户id) 获取客户产品信息
     * @param goodsId
     * @param enterpriseId
     * @return
     */
    CustomerGoods getOneByGoodsIdAndEnterpriseId(Long goodsId, Long enterpriseId);

    /**
     * 应收账款激活额度重新开通
     * @param processInstanceId 流程实例ID
     * @param businessId 产品ID
     * @param customerGoodsId 我的开通产品ID
     * @return
     */
    void reActivatedGoodsJiHuo(String processInstanceId, Long businessId, Long customerGoodsId);
}
