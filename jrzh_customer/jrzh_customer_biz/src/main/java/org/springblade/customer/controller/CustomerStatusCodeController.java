/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.CustomerStatusCode;
import org.springblade.customer.service.ICustomerStatusCodeService;
import org.springblade.customer.vo.CustomerStatusCodeVO;
import org.springblade.customer.wrapper.CustomerStatusCodeWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 注册用户code 中间表 控制器
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("customer/customerStatusCode")
@Api(value = "注册用户code 中间表", tags = "注册用户code 中间表接口")
public class CustomerStatusCodeController extends BladeController {

	private final ICustomerStatusCodeService customerStatusCodeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerStatusCode")
   @PreAuth( "hasPermission('customer:customerStatusCode:detail') or hasRole('administrator')")
	public R<CustomerStatusCodeVO> detail(CustomerStatusCode customerStatusCode) {
		CustomerStatusCode detail = customerStatusCodeService.getOne(Condition.getQueryWrapper(customerStatusCode));
		return R.data(CustomerStatusCodeWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 注册用户code 中间表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerStatusCode")
   @PreAuth( "hasPermission('customer:customerStatusCode:list') or hasRole('administrator')")
	public R<IPage<CustomerStatusCodeVO>> list(CustomerStatusCode customerStatusCode, Query query) {
		IPage<CustomerStatusCode> pages = customerStatusCodeService.page(Condition.getPage(query), Condition.getQueryWrapper(customerStatusCode));
		return R.data(CustomerStatusCodeWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 注册用户code 中间表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerStatusCode")
   @PreAuth( "hasPermission('customer:customerStatusCode:page') or hasRole('administrator')")
	public R<IPage<CustomerStatusCodeVO>> page(CustomerStatusCodeVO customerStatusCode, Query query) {
		IPage<CustomerStatusCodeVO> pages = customerStatusCodeService.selectCustomerStatusCodePage(Condition.getPage(query), customerStatusCode);
		return R.data(pages);
	}

	/**
	 * 新增 注册用户code 中间表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerStatusCode")
   @PreAuth( "hasPermission('customer:customerStatusCode:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody CustomerStatusCode customerStatusCode) {
		return R.status(customerStatusCodeService.save(customerStatusCode));
	}

	/**
	 * 修改 注册用户code 中间表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerStatusCode")
   @PreAuth( "hasPermission('customer:customerStatusCode:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody CustomerStatusCode customerStatusCode) {
		return R.status(customerStatusCodeService.updateById(customerStatusCode));
	}

	/**
	 * 新增或修改 注册用户code 中间表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerStatusCode")
   @PreAuth( "hasPermission('customer:customerStatusCode:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody CustomerStatusCode customerStatusCode) {
		return R.status(customerStatusCodeService.saveOrUpdate(customerStatusCode));
	}


	/**
	 * 删除 注册用户code 中间表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
   @PreAuth( "hasPermission('customer:customerStatusCode:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerStatusCodeService.deleteLogic(Func.toLongList(ids)));
	}


}
