/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.FinancingContacts;
import org.springblade.customer.service.IFinancingContactsService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.FinancingContactsVO;
import org.springblade.customer.wrapper.FinancingContactsWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 融资客户联系人信息 控制器
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK+CommonConstant.WEB_BACK+"/customer/financingContacts")
@Api(value = "融资客户联系人信息", tags = "融资客户联系人信息接口")
public class FinancingContactsController extends BladeController {

	private final IFinancingContactsService financingContactsService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入financingContacts")
   @PreAuth( "hasPermission('customer:financingContacts:detail') or hasRole('administrator')")
	public R<FinancingContactsVO> detail(FinancingContacts financingContacts) {
		FinancingContacts detail = financingContactsService.getOne(Condition.getQueryWrapper(financingContacts));
		return R.data(FinancingContactsWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 融资客户联系人信息
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入financingContacts")
   @PreAuth( "hasPermission('customer:financingContacts:list') or hasRole('administrator')")
	public R<IPage<FinancingContactsVO>> list(FinancingContacts financingContacts, Query query) {
		IPage<FinancingContacts> pages = financingContactsService.page(Condition.getPage(query), Condition.getQueryWrapper(financingContacts));
		return R.data(FinancingContactsWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 融资客户联系人信息
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入financingContacts")
   @PreAuth( "hasPermission('customer:financingContacts:page') or hasRole('administrator')")
	public R<IPage<FinancingContactsVO>> page(FinancingContactsVO financingContacts, Query query) {
		IPage<FinancingContactsVO> pages = financingContactsService.selectFinancingContactsPage(Condition.getPage(query), financingContacts);
		return R.data(pages);
	}

	/**
	 * 新增 融资客户联系人信息
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入financingContacts")
   @PreAuth( "hasPermission('customer:financingContacts:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody FinancingContacts financingContacts) {
		Long customerId = MyAuthUtil.getCustomerId();
		financingContacts.setCustomerId(customerId);
		return R.status(financingContactsService.save(financingContacts));
	}

	/**
	 * 修改 融资客户联系人信息
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入financingContacts")
   @PreAuth( "hasPermission('customer:financingContacts:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody FinancingContacts financingContacts) {
		Long customerId = MyAuthUtil.getCustomerId();
		financingContacts.setCustomerId(customerId);
		return R.status(financingContactsService.updateById(financingContacts));
	}

	/**
	 * 新增或修改 融资客户联系人信息
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入financingContacts")
   @PreAuth( "hasPermission('customer:financingContacts:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody FinancingContacts financingContacts) {
		return R.status(financingContactsService.saveOrUpdate(financingContacts));
	}


	/**
	 * 删除 融资客户联系人信息
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
   @PreAuth( "hasPermission('customer:financingContacts:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(financingContactsService.deleteLogic(Func.toLongList(ids)));
	}

	/***
	 * 查询所有融资联系人信息
	 */
	@PostMapping("/getFinancingList")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "所有联系人信息", notes = "")
	public R<List<FinancingContacts>> list(Long customerId) {
		return R.data(financingContactsService.list(Wrappers.<FinancingContacts>lambdaQuery().eq(BaseEntity::getStatus, CommonConstant.OPENSTATUS).eq(FinancingContacts::getCustomerId,customerId)));
	}

}
