package org.springblade.customer.listener;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.CustomerTypeEnum;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.constant.AuthStatus;
import org.springblade.customer.entity.CustomerFrontUserType;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.CustomerPersonInfo;
import org.springblade.customer.service.ICustomerFrontUserTypeService;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.service.ICustomerPersonInfoService;
import org.springblade.othersapi.sky.dto.CompanyBasicInfo;
import org.springblade.othersapi.sky.dto.SupplierBusinessInfo;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.riskmana.api.dto.RatingRecordDTO;
import org.springblade.riskmana.api.emum.RiskListenerRunDataEnum;
import org.springblade.riskmana.api.entity.RiskmanaApply;
import org.springblade.riskmana.api.listener.IRiskListenerRunDataSource;
import org.springblade.riskmana.core.service.IRiskmanaApplyService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 产品运行风控评分监听
 *
 * <AUTHOR>
 */
@Component("riskListener")
@RequiredArgsConstructor
public class RiskListener implements TaskListener {

    private final IRiskmanaApplyService riskmanaApplyService;
    private final ProductDirector productDirector;
    private final ICustomerFrontUserTypeService customerFrontUserTypeService;
    private final ICustomerBusinessInfoService customerBusinessInfoService;
    private final RemoteUserService remoteUserService;
    private final ICustomerInfoService customerInfoService;
    private final ICustomerPersonInfoService customerPersonInfoService;
    private final Map<String, IRiskListenerRunDataSource> riskListenerRunDataSourceMap;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateTask delegateTask) {
        String status = delegateTask.getVariable(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, String.class);
        if (StringUtil.isNotBlank(status) && StringUtil.equals(status, WfProcessConstant.STATUS_REJECT)) {
            return;
        }
        Map<String, Object> variables = delegateTask.getVariables();
        RatingRecordDTO ratingRecord = new RatingRecordDTO();
        Map<String, Object> riskObj = new HashMap<>();
        //若流程驳回后重新评分 设置原风控记录id
        Long ratingRecordId = (Long) variables.get(ProcessConstant.RATING_RECORD_ID);
        if (ObjectUtil.isNotEmpty(ratingRecordId)) {
            ratingRecord.setId(ratingRecordId);
        }
        //设置产品风控配置
        Long goodsId = (Long) variables.get(ProcessConstant.BUSINESS_ID);
        Product product = productDirector.detailBase(goodsId);
        Long scoreTemplateId = product.getScoreTemplateId();
        ratingRecord.setRatingId(scoreTemplateId);
        //设置用户基础信息
        Long userId = (Long) variables.get(ProcessConstant.USER_ID);
        User user = remoteUserService.getUserById(userId, FeignConstants.FROM_IN).getData();
        CustomerFrontUserType userType = customerFrontUserTypeService.getByRoleId(userId.toString());
        ratingRecord.setEnterpriseType(delegateTask.getVariable(ProcessConstant.ENTERPRISE_TYPE, Integer.class));
        ratingRecord.setProcessInstanceId(delegateTask.getProcessInstanceId());
        ratingRecord.setCustomerGoodsId(delegateTask.getVariable(ProcessConstant.CUSTOMER_GOODS_ID, Long.class));
        settingUserBaseInfo(ratingRecord, riskObj, user, userType);
        //设置其余运行风控参数
        RiskListenerRunDataEnum.Type riskListenerRunDataEnum = EnumUtil.likeValueOf(RiskListenerRunDataEnum.Type.class, variables.get(ProcessConstant.RISK_LISTENER_DATA_SOURCE));
        if (ObjectUtil.isNotEmpty(riskListenerRunDataEnum)) {
            IRiskListenerRunDataSource runDataSource = riskListenerRunDataSourceMap.get(riskListenerRunDataEnum.getServiceName());
            Map<String, Object> var = runDataSource.getVar(delegateTask);
            riskObj.putAll(var);
        }
        ratingRecord.setObj(riskObj);
        RiskmanaApply riskmanaApply = riskmanaApplyService.riskApply(ratingRecord);
        delegateTask.setVariable(org.springblade.process.constant.ProcessConstant.RATING_RECORD_ID, riskmanaApply.getRecordId());
        delegateTask.setVariable(org.springblade.process.constant.ProcessConstant.SCORE_TEMPLATE_ID, ratingRecord.getRatingId());
        delegateTask.setVariable(org.springblade.process.constant.ProcessConstant.RATING_NEED_AUTH, riskmanaApply.isNeedAuth());
    }


    /**
     * 设置用户基本信息
     *
     * @param ratingRecord
     * @param riskObj
     * @param user
     * @param userType
     */
    private void settingUserBaseInfo(RatingRecordDTO ratingRecord, Map<String, Object> riskObj, User user, CustomerFrontUserType userType) {
        String userName = user.getName();
        Long userId = user.getId();
        ratingRecord.setClientId(userId);
        if (CustomerTypeEnum.ENTERPRISE.getCode().equals(userType.getType())) {
            //获取工商信息
            SupplierBusinessInfo supplierBusinessInfo = customerBusinessInfoService.getSupplierBusinessInfo(userName);
            CompanyBasicInfo companyBasicInfo = supplierBusinessInfo.getCompanyBasicInfo();
            CustomerInfo customerInfo = customerInfoService.getByCustomerId(user.getId());
            //设置企业信息
            ratingRecord.setEnpName(userName);
            ratingRecord.setUserInfoJson(JSONUtil.toJsonStr(supplierBusinessInfo));
            ratingRecord.setUserType(CustomerTypeEnum.ENTERPRISE.getCode());
            ratingRecord.setEnpLegalMan(companyBasicInfo.getLegalPersonName());
            ratingRecord.setUserName(userName);
            if (ObjectUtil.isNotEmpty(customerInfo)) {
                if (AuthStatus.LegalPersonFlag.LEGAL_PERSON.getStatus().equals(customerInfo.getLegalPersonFlag())) {
                    ratingRecord.setEnpPhoneContact(customerInfo.getMobile());
                } else {
                    ratingRecord.setEnpPhoneContact(customerInfo.getOperatorPhone());
                }
            }
            ratingRecord.setBusinessInfo(JSONUtil.toJsonStr(companyBasicInfo));
            ratingRecord.setEnpSocialCode(companyBasicInfo.getCreditCode());
            //将工商信息放入风控参数中
            riskObj.put("customerBusinessInfo", companyBasicInfo);
        } else {
//            //设置个人信息
            CustomerPersonInfo personInfo = customerPersonInfoService.getByCustomerId(userId);
            ratingRecord.setUserInfoJson("");
            ratingRecord.setPerIdentity(personInfo.getIdentity());
            ratingRecord.setPerPhone(personInfo.getAccount());
            ratingRecord.setUserType(CustomerTypeEnum.PERSONAL.getCode());
            ratingRecord.setPerName(userName);
            ratingRecord.setUserName(userName);
        }
    }
}
