/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.dto.HeightTradeBackgroundDTO;
import org.springblade.customer.dto.LowerTradeBackgroundDTO;
import org.springblade.customer.entity.SalesContract;
import org.springblade.customer.entity.TradeBackground;
import org.springblade.customer.vo.TradeBackgroundLowerVO;
import org.springblade.customer.vo.TradeBackgroundProcessLowerVO;
import org.springblade.customer.vo.TradeBackgroundVO;
import org.springblade.customer.vo.TransactionVO;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.User;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 贸易背景 服务类
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
public interface ITradeBackgroundService extends BaseService<TradeBackground> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param tradeBackground
	 * @return
	 */
	IPage<TradeBackgroundVO> selectTradeBackgroundPage(IPage<TradeBackgroundVO> page, TradeBackgroundVO tradeBackground);

	/**
	 * 根据客户id获取上下游贸易伙伴
	 *
	 * @param companyId
	 * @return
	 */
	Map<String, List<TradeBackgroundVO>> getList(Long companyId);

	/**
	 * 融资端获取下游详情
	 *
	 * @param id
	 * @return
	 */
	TradeBackgroundLowerVO lowerDetails(Long id);

	/**
	 * 查询未被使用的贸易背景
	 *
	 * @param goodsType 产品类型
	 * @param goodsId   产品id
	 * @return List<TradeBackgroundVO>
	 */
	List<TradeBackgroundVO> selectUnUsedTradeBackgroundList(Integer goodsType, Long goodsId);

	/**
	 * 查询企业名称
	 *
	 * @param goodsType
	 * @param tradeBackgrounds
	 * @return
	 */
	List<TradeBackgroundVO> queryCompanyName(Integer goodsType, List<TradeBackground> tradeBackgrounds);

	/**
	 * 绑定融资端下游交易背景流程
	 *
	 * @param /**             绑定融资端下游交易背景流程
	 * @param tradeBackground
	 * @return
	 */
	boolean bindLowerTradeBackground(LowerTradeBackgroundDTO tradeBackground);

	/**
	 * 绑定融资端上游供应商
	 *
	 * @param tradeBackground
	 * @return
	 */
	boolean bindHeightTradeBackground(HeightTradeBackgroundDTO tradeBackground);

	/**
	 * 根据融资id和核心名称判断是否存在该贸易背景
	 *
	 * @param tradeBackground 贸易背景信息
	 * @param processId       流程实例id
	 * @return
	 */
	boolean existBackGroundByIdAndLowerName(LowerTradeBackgroundDTO tradeBackground, String processId);

	/**
	 * 通过流程id获取详情
	 *
	 * @param processId 流程实例id
	 * @return
	 */
	TradeBackgroundProcessLowerVO lowerDetailsByProcessId(String processId);

	/**
	 * 驳回重新提交下游核心企业流程
	 *
	 * @param tradeBackground
	 * @param variables
	 */
	void reCompete(LowerTradeBackgroundDTO tradeBackground, JSONObject variables);

	/**
	 * 驳回重新提交上游供应商流程
	 *
	 * @param tradeBackground
	 */
	boolean reCompeteHeight(HeightTradeBackgroundDTO tradeBackground);

	/**
	 * 根据客户id和供应商名称查看是否存在该贸易背景
	 *
	 * @param tradeBackground
	 * @return
	 */
	boolean existBackGroundByIdAndHeightName(HeightTradeBackgroundDTO tradeBackground, String processId);


	/**
	 * 根据融资企业id查看核心企业名称列表
	 *
	 * @param companyId
	 * @return
	 */
	Map<Long, String> listCoreName(Long companyId);

	/**
	 * 获取融资企业名称
	 *
	 * @param collect
	 * @param id
	 * @return
	 */
	String getCustomerNameById(Map<Long, User> collect, Long id);

	/**
	 * 获取所有融资企业
	 *
	 * @return
	 */
	Map<Long, User> getCompany(List<Long> companyIds);

	/**
	 * 获取所有核心企业
	 *
	 * @return
	 */
	Map<Long, Dept> getCoreCompany();

	/**
	 * 统计有效金额
	 *
	 * @param list
	 * @return
	 */
	BigDecimal calculateEffectiveAmount(List<SalesContract> list);

	/**
	 * 统计账目金额
	 *
	 * @param list
	 * @return
	 */
	BigDecimal calculateAccountAmount(List<SalesContract> list);

	/**
	 * 统计回款金额
	 */
	BigDecimal calculateRefundAmount(List<SalesContract> list);

	/**
	 * 计算交易数据
	 *
	 * @param confirmSalesContracts
	 * @param period
	 * @return
	 */
	TransactionVO calculateTransaction(List<SalesContract> confirmSalesContracts, Integer period);

	/**
	 * 根据客户id和供应商id集合查询重复
	 *
	 * @param companyId
	 * @param heightIds
	 * @return
	 */
	List<TradeBackground> existBackGroundByIdAndHeightIds(Long companyId, Set<Long> heightIds);

	/**
	 * 查询是否存在供应ids的贸易背景
	 *
	 * @param ids
	 * @return
	 */
	boolean existSupplierByIds(List<Long> ids);

	/**
	 * 根据公司id查询及查询条件查询该公司上游贸易背景列表
	 *
	 * @param companyId 公司id（userId）
	 * @param ability   权利 1代采 2融资转让 3应收质押
	 * @param relation  关联状态 0 已关闭 1审批中 2 已关联
	 * @return
	 */
	List<TradeBackgroundVO> listHeightByCompanyId(Long companyId, Integer ability, Integer relation);

	/**
	 * 根据公司id查询及查询条件查询该公司下游游贸易背景列表
	 *
	 * @param companyId 公司id（userId）
	 * @param ability   权利 1代采 2融资转让 3应收质押
	 * @param relation  关联状态 0 已关闭 1审批中 2 已关联
	 * @return
	 */
	List<TradeBackgroundVO> listLowerByCompanyId(Long companyId, Integer ability, Integer relation);

	/**
	 * 关联贸易背景
	 *
	 * @param id
	 * @param customerId 被关联的客户id
	 */
	void tradeRelationPass(Long id, Long customerId);

	/**
	 * 拒绝关联
	 *
	 * @param id
	 */
	void tradeRelationReject(Long id);

	/**
	 * 根据上游名称与下游名称判断贸易背景是否正在审批中或已关联
	 *
	 * @param heightName
	 * @param lowerName
	 * @param id
	 * @return
	 */
	Boolean existBackGroundByHeightAndLower(String heightName, String lowerName, Long id);

	/**
	 * 判断贸易背景可否删除
	 *
	 * @param ids
	 * @return
	 */
	Boolean canDelete(String ids);

	/**
	 * 根据上下游id，查询贸易背景
	 *
	 * @param heightId 上游企业id
	 * @param lowerId  下游企业id
	 * @return
	 */
	TradeBackground getByHeightIdAndLowerId(Long heightId, Long lowerId);

	/**
	 * 计算合作年数
	 *
	 * @param firstTime 初次合作时间
	 */
	BigDecimal calculateCooperatorTime(LocalDate firstTime);

	/**
	 * 取消邀请
	 *
	 * @param id        背景id
	 * @param companyId 邀请人id
	 * @return
	 */
	Boolean cancelInvite(Long id, Long companyId);

	/**
	 * 查询贸易双方是否有权利进行业务
	 *
	 * @param heightId 上游id
	 * @param lowerId  下游id
	 * @param ability  权利 1代采 2融资转让 3应收质押
	 */
	Boolean hashAuthByType(Long heightId, Long lowerId, Integer ability);

	/**
	 * 代采权利控制
	 *
	 * @param ids
	 * @param status 0关闭 1开启
	 */
	Boolean changePurchase(List<Long> ids, Integer status);

	/**
	 * 融资转让权利控制
	 *
	 * @param ids
	 * @param status 0关闭 1开启
	 */
	Boolean changeTransferAbility(List<Long> ids, Integer status);

	/**
	 * 根据企业id获取其上下游 条件未作废
	 *
	 * @param companyId 企业id
	 */
	List<TradeBackground> listAllByCompanyId(Long companyId);
}
