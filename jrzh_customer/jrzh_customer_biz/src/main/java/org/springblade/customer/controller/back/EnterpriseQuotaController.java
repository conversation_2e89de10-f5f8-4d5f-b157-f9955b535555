/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.customer.dto.EnterpriseQuotaDTO;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.excel.EnterpriseQuotaExcel;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.service.IQuotaUseDetailsService;
import org.springblade.customer.service.ITradeBackgroundService;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 核心企业额度 控制器
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_RISKMANA + CommonConstant.WEB_BACK + "/quota/enterpriseQuota")
@Api(value = "核心企业额度", tags = "核心企业额度接口")
public class EnterpriseQuotaController extends BladeController {

	private final IEnterpriseQuotaService enterpriseQuotaService;
	private final ITradeBackgroundService tradeBackgroundService;
	private final IQuotaUseDetailsService quotaUseDetailsService;

	/**
	 * 分页 核心企业额度
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "核心企业额度分页", notes = "传入EnterpriseQuota")
	@PreAuth("hasPermission('quota:EnterpriseQuota:list') or hasRole('administrator')")
	public R<IPage<EnterpriseQuotaVO>> list(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query) {
		return R.data(enterpriseQuotaService.selectEnterpriseQuotaPage(enterpriseQuotaDTO, query));
	}

	/**
	 * 分页 融资企业额度
	 */
	@GetMapping("/financingList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "融资企业额度分页", notes = "传入EnterpriseQuota")
	@PreAuth("hasPermission('quota:EnterpriseQuota:financingList') or hasRole('administrator')")
	public R<IPage<EnterpriseQuotaVO>> financingList(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query) {
		return R.data(enterpriseQuotaService.selectFinancingEnterpriseQuotaPage(enterpriseQuotaDTO, query));
	}

	/**
	 * 申请额度
	 */
	@PostMapping("/apply")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "申请额度", notes = "传入EnterpriseQuota")
	@PreAuth("hasPermission('quota:EnterpriseQuota:save') or hasRole('administrator')")
	public R<Boolean> apply(@Valid @RequestBody EnterpriseQuotaDTO enterpriseQuota) {
		return R.status(enterpriseQuotaService.applyQuota(enterpriseQuota));
	}


	/**
	 * 核心企业调整额度
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "调整额度", notes = "传入EnterpriseQuota")
	@PreAuth("hasPermission('quota:EnterpriseQuota:update') or hasRole('administrator')")
	public R<Boolean> update(@RequestBody EnterpriseQuotaDTO enterpriseQuota) {
		//return R.status(enterpriseQuotaService.modifyQuota(enterpriseQuota));
		return R.status(enterpriseQuotaService.coreModifyQuota(enterpriseQuota));
	}

	/**
	 * 平台融资企业调整额度
	 */
	@PostMapping("/financingUpdate")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "平台融资企业调整额度", notes = "传入EnterpriseQuota")
	@PreAuth("hasPermission('quota:EnterpriseQuota:update') or hasRole('administrator')")
	public R<Boolean> financingUpdate(@RequestBody EnterpriseQuotaDTO enterpriseQuota) {
		return R.status(enterpriseQuotaService.financingModifyQuota(enterpriseQuota));
	}

	@GetMapping("/detail")
	@ApiOperation("详情")
	public R<EnterpriseQuotaVO> detail(@RequestParam Long id) {
		return R.data(enterpriseQuotaService.detail(id));
	}


	@GetMapping("/quotaHistory")
	@ApiOperation("核心企业额度历史")
	public R<EnterpriseQuotaVO> quotaHistory(@RequestParam Long id) {
		EnterpriseQuotaVO enterpriseQuotaVO = enterpriseQuotaService.quotaHistory(id);
		return R.data(enterpriseQuotaVO);
	}

	@GetMapping("/financingQuotaHistory")
	@ApiOperation("融资企业额度历史")
	public R<EnterpriseQuotaVO> financingQuotaHistory(@RequestParam Long id) {
		EnterpriseQuotaVO enterpriseQuotaVO = enterpriseQuotaService.financingQuotaHistory(id);
		return R.data(enterpriseQuotaVO);
	}


	@ApiOperation("冻结金额")
	@PostMapping("/frozenAmount")
	public R<Boolean> frozenAmount(@RequestParam Long id, @RequestParam String frozenReason) {
		return R.status(enterpriseQuotaService.frozenAmount(id, frozenReason));
	}

	@ApiOperation("解冻金额")
	@PostMapping("/thawAmount")
	public R<Boolean> thawAmount(@RequestParam Long id, @RequestParam String thawReason) {
		return R.status(enterpriseQuotaService.thawAmount(id, thawReason));
	}

	@ApiOperation("禁用")
	@PostMapping("/disable")
	public R<Boolean> disable(@RequestParam Long id, @RequestParam String disableReason) {
		return R.status(enterpriseQuotaService.disable(id, disableReason));
	}

	@ApiOperation("激活")
	@PostMapping("/active")
	public R<Boolean> active(@RequestParam Long id) {
		return R.status(enterpriseQuotaService.active(id));
	}

	@ApiOperation("续期")
	@PostMapping("/renewal")
	public R<Boolean> renewal(@RequestBody EnterpriseQuotaDTO enterpriseQuota) {
		return R.status(enterpriseQuotaService.renewal(enterpriseQuota));
	}

	@ApiOperation("启用")
	@PostMapping("/enable")
	public R<Boolean> enable(@RequestParam Long id) {
		return R.status(enterpriseQuotaService.enable(id));
	}

	@ApiOperation("通过产品id查询额度信息")
	@GetMapping("/getByBackIdAndGoodsId")
	public R<EnterpriseQuota> getByBackIdAndGoodsId(@RequestParam Long id, @RequestParam Long backId) {
		Long companyLowerId = tradeBackgroundService.getById(backId).getCompanyLowerId();
		return R.data(enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(id, 2, companyLowerId));
	}

	@ApiOperation("获取额度信息")
	@GetMapping("/getEnterpriseQuota")
	public R<EnterpriseQuota> getByBackIdAndGoodsId(@RequestParam Long goodsId, @RequestParam Integer enterpriseType, @RequestParam Long userId) {
		return R.data(enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, enterpriseType, userId));
	}

	/**
	 * 分页 核心企业额度(云信)
	 */
	@GetMapping("/cloud/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "核心企业额度(云信)", notes = "传入EnterpriseQuota")
	@PreAuth("hasPermission('quota:EnterpriseQuota:list') or hasRole('administrator')")
	public R<IPage<EnterpriseQuotaVO>> cloudList(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query) {
		return R.data(enterpriseQuotaService.selectEnterpriseQuotaCloudPage(enterpriseQuotaDTO, query));
	}

	/**
	 * 融资企业额度导出
	 */
	@GetMapping("/export")
	@PreAuth("hasPermission('quota:EnterpriseQuota:export') or hasRole('administrator')")
	public void export(HttpServletResponse response, EnterpriseQuotaDTO enterpriseQuotaDTO) {
		List<EnterpriseQuotaExcel> list = enterpriseQuotaService.export(enterpriseQuotaDTO);
		ExcelUtil.export(response, "融资企业额度" + DateUtil.time(), "融资企业额度", list, EnterpriseQuotaExcel.class);
	}

}
