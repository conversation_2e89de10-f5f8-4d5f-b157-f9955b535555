<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.FrontCreditInformationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="frontCreditInformationResultMap" type="org.springblade.customer.entity.FrontCreditInformation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="current_overdue" property="currentOverdue"/>
        <result column="current_times" property="currentTimes"/>
        <result column="current_loss" property="currentLoss"/>
        <result column="current_suspicious" property="currentSuspicious"/>
        <result column="current_concerns" property="currentConcerns"/>
        <result column="overdue_mone" property="overdueMone"/>
        <result column="overdue_mtwo" property="overdueMtwo"/>
        <result column="overdue_mthree" property="overdueMthree"/>
        <result column="approve_letter_query_two" property="approveLetterQueryTwo"/>
        <result column="approve_letter_query_tetracosa" property="approveLetterQueryTetracosa"/>
        <result column="closed_cases" property="closedCases"/>
        <result column="open_cases" property="openCases"/>
        <result column="overdue_rate" property="overdueRate"/>
        <result column="credit_report" property="creditReport"/>
        <result column="company_id" property="companyId"/>

    </resultMap>


    <select id="selectFrontCreditInformationPage" resultMap="frontCreditInformationResultMap">
        select * from jrzh_customer_front_credit_information where is_deleted = 0
    </select>

</mapper>
