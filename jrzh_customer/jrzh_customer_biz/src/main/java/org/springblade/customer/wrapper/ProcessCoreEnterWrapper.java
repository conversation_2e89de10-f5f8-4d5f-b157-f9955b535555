package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.dto.ProcessCoreEnterDTO;
import org.springblade.customer.entity.ProcessCoreEnter;
import org.springblade.customer.vo.ProcessCoreEnterVO;

import java.util.Objects;

/**
 * 销售合同包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
public class ProcessCoreEnterWrapper extends BaseEntityWrapper<ProcessCoreEnter, ProcessCoreEnterVO> {

    public static ProcessCoreEnterWrapper build() {
        return new ProcessCoreEnterWrapper();
    }

    public ProcessCoreEnterDTO entityDTO(ProcessCoreEnter ProcessCoreEnter) {
        ProcessCoreEnterDTO ProcessCoreEnterVO = Objects.requireNonNull(BeanUtil.copy(ProcessCoreEnter, ProcessCoreEnterDTO.class));

        //User createUser = UserCache.getUser(   ProcessCoreEnter.getCreateUser());
        //User updateUser = UserCache.getUser(   ProcessCoreEnter.getUpdateUser());
        //   ProcessCoreEnterVO.setCreateUserName(createUser.getName());
        //   ProcessCoreEnterVO.setUpdateUserName(updateUser.getName());

        return ProcessCoreEnterVO;
    }

    @Override
    public ProcessCoreEnterVO entityVO(ProcessCoreEnter processCoreEnter) {
        if(ObjectUtil.isEmpty(processCoreEnter)){
            return null;
        }
        ProcessCoreEnterVO processCoreEnterVO = Objects.requireNonNull(BeanUtil.copy(processCoreEnter, ProcessCoreEnterVO.class));

        //User createUser = UserCache.getUser(   ProcessCoreEnter.getCreateUser());
        //User updateUser = UserCache.getUser(   ProcessCoreEnter.getUpdateUser());
        //   ProcessCoreEnterVO.setCreateUserName(createUser.getName());
        //   ProcessCoreEnterVO.setUpdateUserName(updateUser.getName());

        return processCoreEnterVO;
    }
}