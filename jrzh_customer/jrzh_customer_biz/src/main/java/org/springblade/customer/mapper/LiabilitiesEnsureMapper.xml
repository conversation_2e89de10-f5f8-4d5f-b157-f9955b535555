<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.LiabilitiesEnsureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="liabilitiesEnsureResultMap" type="org.springblade.customer.entity.LiabilitiesEnsure">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="name" property="name"/>
        <result column="mechanism" property="mechanism"/>
        <result column="varieties" property="varieties"/>
        <result column="origin" property="origin"/>
    </resultMap>


    <select id="selectLiabilitiesEnsurePage" resultMap="liabilitiesEnsureResultMap">
        select * from jrzh_customer_liabilities_ensure where is_deleted = 0
    </select>

</mapper>
