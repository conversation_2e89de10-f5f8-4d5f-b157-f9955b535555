package org.springblade.customer.wrapper;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.vo.CustomerGoodsInfoVO;
import org.springblade.customer.vo.CustomerGoodsVO;
import org.springblade.customer.vo.CustomerProductGroupInfoVO;
import org.springblade.product.common.entity.ProductGroupRoute;
import org.springblade.product.common.vo.ProductVO;

import java.util.List;
import java.util.Objects;

/**
 * 客户产品包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
public class CustomerGoodsWrapper extends BaseEntityWrapper<CustomerGoods, CustomerGoodsVO> {

    public static CustomerGoodsWrapper build() {
        return new CustomerGoodsWrapper();
    }

    @Override
    public CustomerGoodsVO entityVO(CustomerGoods CustomerGoods) {
        CustomerGoodsVO CustomerGoodsVO = Objects.requireNonNull(org.springblade.core.tool.utils.BeanUtil.copy(CustomerGoods, CustomerGoodsVO.class));


        return CustomerGoodsVO;
    }

    public IPage<CustomerGoodsInfoVO> pageCustomerGoodsInfoVO(IPage<ProductVO> page) {
        Page<CustomerGoodsInfoVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(BeanUtil.copyToList(page.getRecords(), CustomerGoodsInfoVO.class));
        return pageVO;
    }

    public IPage<CustomerProductGroupInfoVO> pageCustomerProductGroupInfoVO(IPage<ProductGroupRoute> page) {
        Page<CustomerProductGroupInfoVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<CustomerProductGroupInfoVO> records = BeanUtil.copyToList(page.getRecords(), CustomerProductGroupInfoVO.class);
        for (CustomerProductGroupInfoVO record : records) {
            record.setGroupId(record.getId());
        }
        pageVO.setRecords(records);
        return pageVO;
    }
}
