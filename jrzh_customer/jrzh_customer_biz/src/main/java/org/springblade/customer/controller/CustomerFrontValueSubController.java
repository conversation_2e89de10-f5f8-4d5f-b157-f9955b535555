/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.CustomerFrontValueSub;
import org.springblade.customer.service.ICustomerFrontValueSubService;
import org.springblade.customer.vo.CustomerFrontValueSubVO;
import org.springblade.customer.wrapper.CustomerFrontValueSubWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 融资企业价值分析变更 控制器
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("customer/customerFrontValueSub")
@Api(value = "融资企业价值分析变更", tags = "融资企业价值分析变更接口")
public class CustomerFrontValueSubController extends BladeController {

	private final ICustomerFrontValueSubService customerFrontValueSubService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerFrontValueSub")
   @PreAuth( "hasPermission('customer:customerFrontValueSub:detail') or hasRole('administrator')")
	public R<CustomerFrontValueSubVO> detail(CustomerFrontValueSub customerFrontValueSub) {
		CustomerFrontValueSub detail = customerFrontValueSubService.getOne(Condition.getQueryWrapper(customerFrontValueSub));
		return R.data(CustomerFrontValueSubWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 融资企业价值分析变更
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerFrontValueSub")
   @PreAuth( "hasPermission('customer:customerFrontValueSub:list') or hasRole('administrator')")
	public R<IPage<CustomerFrontValueSubVO>> list(CustomerFrontValueSub customerFrontValueSub, Query query) {
		IPage<CustomerFrontValueSub> pages = customerFrontValueSubService.page(Condition.getPage(query), Condition.getQueryWrapper(customerFrontValueSub));
		return R.data(CustomerFrontValueSubWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 融资企业价值分析变更
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerFrontValueSub")
   @PreAuth( "hasPermission('customer:customerFrontValueSub:page') or hasRole('administrator')")
	public R<IPage<CustomerFrontValueSubVO>> page(CustomerFrontValueSubVO customerFrontValueSub, Query query) {
		IPage<CustomerFrontValueSubVO> pages = customerFrontValueSubService.selectCustomerFrontValueSubPage(Condition.getPage(query), customerFrontValueSub);
		return R.data(pages);
	}

	/**
	 * 新增 融资企业价值分析变更
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerFrontValueSub")
   @PreAuth( "hasPermission('customer:customerFrontValueSub:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody CustomerFrontValueSub customerFrontValueSub) {
		return R.status(customerFrontValueSubService.save(customerFrontValueSub));
	}

	/**
	 * 修改 融资企业价值分析变更
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerFrontValueSub")
   @PreAuth( "hasPermission('customer:customerFrontValueSub:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody CustomerFrontValueSub customerFrontValueSub) {
		return R.status(customerFrontValueSubService.updateById(customerFrontValueSub));
	}

	/**
	 * 新增或修改 融资企业价值分析变更
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerFrontValueSub")
   @PreAuth( "hasPermission('customer:customerFrontValueSub:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody CustomerFrontValueSub customerFrontValueSub) {
		return R.status(customerFrontValueSubService.saveOrUpdate(customerFrontValueSub));
	}


	/**
	 * 删除 融资企业价值分析变更
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
   @PreAuth( "hasPermission('customer:customerFrontValueSub:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerFrontValueSubService.deleteLogic(Func.toLongList(ids)));
	}


}
