/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.entity.QuotaHistory;
import org.springblade.customer.mapper.QuotaHistoryMapper;
import org.springblade.customer.service.IQuotaHistoryService;
import org.springblade.customer.vo.QuotaHistoryVO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 额度变化表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Service
@RequiredArgsConstructor
public class QuotaHistoryServiceImpl extends BaseServiceImpl<QuotaHistoryMapper, QuotaHistory> implements IQuotaHistoryService {




	@Override
	public IPage<QuotaHistoryVO> selectQuotaHistoryPage(IPage<QuotaHistoryVO> page, QuotaHistoryVO quotaHistory) {
		return page.setRecords(baseMapper.selectQuotaHistoryPage(page, quotaHistory));
	}

	@Override
	@Async
	public Boolean saveHistory(EnterpriseQuota enterprise, int code) {
		QuotaHistory quotaHistory = new QuotaHistory();
		quotaHistory.setEnterpriseQuotaId(enterprise.getId());
		quotaHistory.setEnterpriseId(enterprise.getEnterpriseId());
		quotaHistory.setChangeTime(LocalDateTime.now());
		quotaHistory.setChangeType(code);
		quotaHistory.setCreditAmount(enterprise.getCreditAmount());
		quotaHistory.setEnterpriseType(enterprise.getEnterpriseType());
		quotaHistory.setProductType(enterprise.getProductType());
		return saveOrUpdate(quotaHistory);
	}


}
