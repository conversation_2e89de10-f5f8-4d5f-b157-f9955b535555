/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.authapi.entity.AuthTypeInfo;
import org.springblade.customer.authapi.service.IAuthTypeInfoService;
import org.springblade.customer.authapi.vo.AuthTypeInfoVO;
import org.springblade.customer.authapi.wrapper.AuthTypeInfoWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 认证方式信息表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("customer/authTypeInfo")
@Api(value = "认证方式信息表", tags = "认证方式信息表接口")
public class AuthTypeInfoController extends BladeController {

	private final IAuthTypeInfoService authTypeInfoService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入authTypeInfo")
    @PreAuth("hasPermission('customer:authTypeInfo:detail') or hasRole('administrator')")
	public R<AuthTypeInfoVO> detail(AuthTypeInfo authTypeInfo) {
		AuthTypeInfo detail = authTypeInfoService.getOne(Condition.getQueryWrapper(authTypeInfo));
		return R.data(AuthTypeInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 认证方式信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入authTypeInfo")
    @PreAuth("hasPermission('customer:authTypeInfo:list') or hasRole('administrator')")
	public R<IPage<AuthTypeInfoVO>> list(AuthTypeInfo authTypeInfo, Query query) {
		IPage<AuthTypeInfo> pages = authTypeInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(authTypeInfo));
		return R.data(AuthTypeInfoWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 认证方式信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入authTypeInfo")
    @PreAuth("hasPermission('customer:authTypeInfo:page') or hasRole('administrator')")
	public R<IPage<AuthTypeInfoVO>> page(AuthTypeInfoVO authTypeInfo, Query query) {
		IPage<AuthTypeInfoVO> pages = authTypeInfoService.selectAuthTypeInfoPage(Condition.getPage(query), authTypeInfo);
		return R.data(pages);
	}

	/**
	 * 新增 认证方式信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入authTypeInfo")
    @PreAuth("hasPermission('customer:authTypeInfo:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody AuthTypeInfo authTypeInfo) {
		return R.status(authTypeInfoService.save(authTypeInfo));
	}

	/**
	 * 修改 认证方式信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入authTypeInfo")
    @PreAuth("hasPermission('customer:authTypeInfo:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody AuthTypeInfo authTypeInfo) {
		return R.status(authTypeInfoService.updateById(authTypeInfo));
	}

	/**
	 * 新增或修改 认证方式信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入authTypeInfo")
    @PreAuth("hasPermission('customer:authTypeInfo:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody AuthTypeInfo authTypeInfo) {
		return R.status(authTypeInfoService.saveOrUpdate(authTypeInfo));
	}

	
	/**
	 * 删除 认证方式信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('customer:authTypeInfo:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(authTypeInfoService.deleteLogic(Func.toLongList(ids)));
	}

	
}
