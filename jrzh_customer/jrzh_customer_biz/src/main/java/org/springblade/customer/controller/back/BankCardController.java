/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import cn.hutool.core.util.DesensitizedUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.BankCard;
import org.springblade.customer.service.IBankCardService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.vo.BankCardVO;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.customer.wrapper.BankCardWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 添加账户 控制器
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_BACK + "/customerCertificateInfo/bankCard")
@Api(value = "添加账户", tags = "添加账户接口")
public class BankCardController extends BladeController {

	private final IBankCardService bankCardService;

	private final IEnterpriseQuotaService enterpriseQuotaService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入bankCard")
	@PreAuth("hasPermission('customerCertificateInfo:bankCard:detail') or hasRole('administrator')")
	public R<BankCardVO> detail(BankCard bankCard) {
		BankCard detail = bankCardService.getOne(Condition.getQueryWrapper(bankCard));
		return R.data(BankCardWrapper.build().entityVO(detail));
	}

	@GetMapping("/backCardFinAll")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "查询当前企业所有卡号信息", notes = "传入ids")
	public R<List<BankCardVO>> backCardFinAll(Long userId) {
		return R.data(bankCardService.backCardFinAll(userId));
	}

	/**
	 * 查询银行号信息脱敏
	 *
	 * @param userId
	 * @return
	 */
	@GetMapping("/getDeByBankCard")
	@ApiOperationSupport(order = 9)
	public R<BankCard> getDeByBankCard(@RequestParam Long goodsId, @RequestParam Integer userType, @RequestParam Long userId) {
		BankCard bankCard = new BankCard();

		EnterpriseQuotaVO quotaVO = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, userType, userId);
		bankCard.setBankCardNo(DesensitizedUtil.bankCard(quotaVO.getBankCardNo()));
		bankCard.setEnterpriseName(quotaVO.getEnterpriseName());
		bankCard.setBankDeposit(quotaVO.getBank());
		return R.data(bankCard);
	}

	/**
	 * 根据产品id和融资用户查询账户
	 *
	 * @return
	 */
	@GetMapping("/getByGoodsIdAndUserId")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据产品id和融资用户查询信息", notes = "传入ids")
	public R<BankCard> getByGoodsIdAndUserId(@RequestParam Long goodsId, @RequestParam Long userId) {
		BankCard bankCard = bankCardService.getByGoodsIdAndUserId(goodsId, userId);
		return R.data(bankCard);
	}

	@GetMapping("/enterprisePhone")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "获取当前企业的手机号码与企业名称")
	public R<BankCardVO> enterprisePhoneAndName() {
		return R.data(bankCardService.enterprisePhoneAndName());
	}


}
