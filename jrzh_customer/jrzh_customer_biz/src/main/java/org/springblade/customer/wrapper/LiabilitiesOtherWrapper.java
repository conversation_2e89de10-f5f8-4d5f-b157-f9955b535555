package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.LiabilitiesOther;
import org.springblade.customer.vo.LiabilitiesOtherVO;

import java.util.Objects;

/**
 * 其他负债情况包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-02-08
 */
public class LiabilitiesOtherWrapper extends BaseEntityWrapper<LiabilitiesOther, LiabilitiesOtherVO> {

    public static LiabilitiesOtherWrapper build() {
        return new LiabilitiesOtherWrapper();
    }

    @Override
    public LiabilitiesOtherVO entityVO(LiabilitiesOther LiabilitiesOther) {
        LiabilitiesOtherVO LiabilitiesOtherVO = Objects.requireNonNull(BeanUtil.copy(LiabilitiesOther, LiabilitiesOtherVO.class));

        //User createUser = UserCache.getUser(   LiabilitiesOther.getCreateUser());
        //User updateUser = UserCache.getUser(   LiabilitiesOther.getUpdateUser());
        //   LiabilitiesOtherVO.setCreateUserName(createUser.getName());
        //   LiabilitiesOtherVO.setUpdateUserName(updateUser.getName());

        return LiabilitiesOtherVO;
    }
}