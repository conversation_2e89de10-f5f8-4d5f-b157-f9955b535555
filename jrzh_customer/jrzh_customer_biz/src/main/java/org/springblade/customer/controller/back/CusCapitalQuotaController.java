/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.CusCapitalQuota;
import org.springblade.customer.service.ICusCapitalQuotaService;
import org.springblade.customer.vo.CusCapitalQuotaVO;
import org.springblade.customer.wrapper.CusCapitalQuotaWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 资金方额度信息 控制器
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_BACK + "/customer/cusCapitalQuota")
@Api(value = "资金方额度信息", tags = "资金方额度信息接口")
public class CusCapitalQuotaController extends BladeController {

    private final ICusCapitalQuotaService cusCapitalQuotaService;

    /**
     * 分页 资金方额度信息
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入cusCapitalQuota")
    @PreAuth("hasPermission('customer:cusCapitalQuota:list') or hasRole('administrator')")
    public R<IPage<CusCapitalQuotaVO>> list(CusCapitalQuota cusCapitalQuota, Query query) {
        IPage<CusCapitalQuota> pages = cusCapitalQuotaService.page(Condition.getPage(query), Condition.getQueryWrapper(cusCapitalQuota));
        return R.data(CusCapitalQuotaWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 资金方额度信息
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入cusCapitalQuota")
    @PreAuth("hasPermission('customer:cusCapitalQuota:page') or hasRole('administrator')")
    public R<IPage<CusCapitalQuotaVO>> page(CusCapitalQuotaVO cusCapitalQuota, Query query) {
        IPage<CusCapitalQuotaVO> pages = cusCapitalQuotaService.selectCusCapitalQuotaPage(Condition.getPage(query), cusCapitalQuota);
        //	List<CusCapitalQuotaVO> records = pages.getRecords();
        //records.stream().map()
        return R.data(pages);
    }

    /**
     * 新增 资金方额度信息
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入cusCapitalQuota")
    @PreAuth("hasPermission('customer:cusCapitalQuota:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody CusCapitalQuota cusCapitalQuota) {
        return R.status(cusCapitalQuotaService.save(cusCapitalQuota));
    }

    /**
     * 修改 资金方额度信息
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入cusCapitalQuota")
    @PreAuth("hasPermission('customer:cusCapitalQuota:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody CusCapitalQuota cusCapitalQuota) {
        return R.status(cusCapitalQuotaService.updateById(cusCapitalQuota));
    }

    /**
     * 新增或修改 资金方额度信息
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入cusCapitalQuota")
    @PreAuth("hasPermission('customer:cusCapitalQuota:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody CusCapitalQuota cusCapitalQuota) {
        return R.status(cusCapitalQuotaService.saveOrUpdate(cusCapitalQuota));
    }


    /**
     * 删除 资金方额度信息
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('customer:cusCapitalQuota:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(cusCapitalQuotaService.deleteLogic(Func.toLongList(ids)));
    }


}
