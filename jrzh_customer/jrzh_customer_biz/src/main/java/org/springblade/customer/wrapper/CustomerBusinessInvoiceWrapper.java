package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.CustomerBusinessInvoice;
import org.springblade.customer.vo.CustomerBusinessInvoiceVO;

import java.util.Objects;

/**
 * 业务发票包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public class CustomerBusinessInvoiceWrapper extends BaseEntityWrapper<CustomerBusinessInvoice, CustomerBusinessInvoiceVO> {

    public static CustomerBusinessInvoiceWrapper build() {
        return new CustomerBusinessInvoiceWrapper();
    }

    @Override
    public CustomerBusinessInvoiceVO entityVO(CustomerBusinessInvoice CustomerBusinessInvoice) {
        CustomerBusinessInvoiceVO CustomerBusinessInvoiceVO = Objects.requireNonNull(BeanUtil.copy(CustomerBusinessInvoice, CustomerBusinessInvoiceVO.class));

        //User createUser = UserCache.getUser(  CustomerBusinessInvoice.getCreateUser());
        //User updateUser = UserCache.getUser(  CustomerBusinessInvoice.getUpdateUser());
        //  CustomerBusinessInvoiceVO.setCreateUserName(createUser.getName());
        //  CustomerBusinessInvoiceVO.setUpdateUserName(updateUser.getName());

        return CustomerBusinessInvoiceVO;
    }
}

