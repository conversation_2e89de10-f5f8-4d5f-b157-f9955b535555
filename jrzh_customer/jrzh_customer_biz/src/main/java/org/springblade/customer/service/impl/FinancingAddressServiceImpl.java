/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.FinancingAddressEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.customer.entity.FinancingAddress;
import org.springblade.customer.mapper.FinancingAddressMapper;
import org.springblade.customer.service.IFinancingAddressService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.FinancingAddressVO;
import org.springblade.customer.wrapper.FinancingAddressWrapper;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteRegionService;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 融资客户收货地址 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Service
@RequiredArgsConstructor
public class FinancingAddressServiceImpl extends BaseServiceImpl<FinancingAddressMapper, FinancingAddress> implements IFinancingAddressService {
private final RemoteRegionService regionService;
private final RemoteUserService userService;
	@Override
	public IPage<FinancingAddressVO> selectFinancingAddressPage(IPage<FinancingAddressVO> page, FinancingAddressVO financingAddress) {
		return page.setRecords(baseMapper.selectFinancingAddressPage(page, financingAddress));
	}

	@Override
	public boolean saveAddress(FinancingAddress financingAddress) {
		isDefault(financingAddress);
		return save(financingAddress);
	}

	@Override
	public boolean updateAddress(FinancingAddress financingAddress) {
		isDefault(financingAddress);
		return updateById(financingAddress);
	}

	@Override
	public boolean updateDefault(Long id) {
		FinancingAddress byId = getById(id);
		byId.setType(FinancingAddressEnum.ON_DEFAULT.getCode());
		isDefault(byId);
		return updateById(byId);
	}

	@Override
	public List<FinancingAddressVO> financingAddressList() {
		Long companyId = MyAuthUtil.getUserId();
		User one =userService.getUserById(companyId, FeignConstants.FROM_IN).getData();
		List<FinancingAddress> list = list(Wrappers.<FinancingAddress>lambdaQuery().eq(FinancingAddress::getCompanyId, companyId));
		List<FinancingAddressVO> financingAddress = FinancingAddressWrapper.build().listVO(list);
		if(ObjectUtil.isEmpty(one)){
			return financingAddress;
		}
		List<String> collect = list.stream().map(FinancingAddress::getLocation).distinct().collect(Collectors.toList());
		if(ObjectUtil.isEmpty(collect)){
			return financingAddress;
		}
		Map<String, Region> map = regionService.getList(collect).getData().stream().collect(Collectors.toMap(Region::getCode, e -> e));
		return financingAddress.stream().peek(financingAddressVO -> {
			financingAddressVO.setEnterpriseName(one.getName());
			Region region = map.get(financingAddressVO.getLocation());
			if(ObjectUtil.isEmpty(region)){
				financingAddressVO.setUrbanAreas(null);
			}else{
				String provinceName = region.getProvinceName();
				String cityName = region.getCityName();
				String districtName = region.getDistrictName();
				financingAddressVO.setUrbanAreas(provinceName+" "+cityName+" "+districtName);

			}
		}).collect(Collectors.toList());
	}

	@Override
	public FinancingAddressVO selectFinancingUserDefaultAddress() {
		String companyId = MyAuthUtil.getCompanyId();
		FinancingAddress one = SpringUtil.getBean(IFinancingAddressService.class).lambdaQuery().eq(FinancingAddress::getCompanyId, companyId).eq(FinancingAddress::getType, 1).one();
		if(ObjectUtil.isEmpty(one)){
			return null;
		}
		String location = one.getLocation();
		//TODO 重构
		//Region one1 = SpringUtil.getBean(IRegionService.class).lambdaQuery().eq(Region::getCode, location).one();
		FinancingAddressVO financingAddressVO = FinancingAddressWrapper.build().entityVO(one);
//		String provinceName = one1.getProvinceName();
//		String cityName = one1.getCityName();
//		String districtName = one1.getDistrictName();
//		financingAddressVO.setUrbanAreas(provinceName+" "+cityName+" "+districtName);
		return financingAddressVO;
	}

	private void isDefault(FinancingAddress financingAddress){
		//判断是否默认地址 0-否 1-是
		if(FinancingAddressEnum.ON_DEFAULT.getCode()==financingAddress.getType()){
			//将已经设置为默认地址的设置为不是默认地址
			FinancingAddress financingAddress1 = baseMapper.selectOne(Wrappers.<FinancingAddress>lambdaQuery().eq(FinancingAddress::getType, FinancingAddressEnum.ON_DEFAULT.getCode()).eq(FinancingAddress::getCompanyId, MyAuthUtil.getCompanyId()));
			//避免没有设置为默认地址的,直接抛出异常
			if(!StringUtils.isEmpty(financingAddress1)){
				//设置为不是默认地址
				financingAddress1.setType(FinancingAddressEnum.OFF_DEFAULT.getCode());
				updateById(financingAddress1);
			}
		}
	}



}
