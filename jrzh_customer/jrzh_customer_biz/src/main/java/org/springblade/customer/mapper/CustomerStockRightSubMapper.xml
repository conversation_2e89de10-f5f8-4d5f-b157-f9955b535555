<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerStockRightSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerStockRightSubResultMap" type="org.springblade.customer.entity.CustomerStockRightSub">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="name" property="name"/>
        <result column="amomon" property="amomon"/>
        <result column="percent" property="percent"/>
        <result column="change_date" property="changeDate"/>
        <result column="record_id" property="recordId"/>
    </resultMap>


    <select id="selectCustomerStockRightSubPage" resultMap="customerStockRightSubResultMap">
        select * from jrzh_customer_stock_right_sub where is_deleted = 0
    </select>

</mapper>
