/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.CustomerSupplier;
import org.springblade.customer.mapper.CustomerSupplierMapper;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.service.ICustomerSupplierService;
import org.springblade.customer.service.ITradeBackgroundService;
import org.springblade.customer.vo.CustomerSupplierVO;
import org.springblade.customer.vo.TradeBackgroundVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 供应商 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
@RequiredArgsConstructor
public class CustomerSupplierServiceImpl extends BaseServiceImpl<CustomerSupplierMapper, CustomerSupplier> implements ICustomerSupplierService {

	private static final Pattern bank = Pattern.compile("^[1-9]\\d{12,18}$");

	private static final Pattern p = Pattern.compile("([0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\\d{14})");

	private static final Pattern par = Pattern.compile("^[1][3,4,5,7,8,9][0-9]{9}$");
	private final ICustomerInfoService customerInfoService;

	@Override
	public IPage<CustomerSupplierVO> selectCustomerSupplierPage(IPage<CustomerSupplierVO> page, CustomerSupplierVO customerSupplier) {
		return page.setRecords(baseMapper.selectCustomerSupplierPage(page, customerSupplier));
	}

	@Override
	public Boolean conlistput(Long id) {
		CustomerSupplier byId = getById(id);
		if (!checkSupplierComplete(byId)) {
			throw new ServiceException("信息未填写完整,请填写完成之后进行启用操作！");
		}
		return update(Wrappers.<CustomerSupplier>lambdaUpdate().eq(BaseEntity::getId, byId.getId()).set(BaseEntity::getStatus, CommonConstant.OPENSTATUS));
	}

	@Override
	public Boolean conlistoff(List<Long> toLongList) {
		return update(Wrappers.<CustomerSupplier>lambdaUpdate().in(BaseEntity::getId, toLongList).set(BaseEntity::getStatus, CommonConstant.CLOSESTATUS));
	}

	@Override
	public Integer handlerOnShelf(List<Long> toLongList) {
		List<CustomerSupplier> customerSuppliers = listByIds(toLongList);
		List<CustomerSupplier> collect = customerSuppliers.parallelStream().filter(customerSupplier -> {
			return checkSupplierComplete(customerSupplier);
		}).map(customerSupplier -> {
			customerSupplier.setStatus(CommonConstant.OPENSTATUS);
			return customerSupplier;
		}).collect(Collectors.toList());
		updateBatchById(collect);
		return collect.size();
	}

	@Override
	public CustomerSupplier getByName(String name) {
		return getOne(Wrappers.<CustomerSupplier>lambdaQuery()
			.eq(CustomerSupplier::getSupperName, name)
			.eq(CustomerSupplier::getStatus, CommonConstant.OPENSTATUS)
			.last("limit 1"));
	}

	@Override
	public List<CustomerSupplier> getList() {
		return list(Wrappers.<CustomerSupplier>lambdaQuery().eq(BaseEntity::getStatus, CommonConstant.OPENSTATUS));
	}

	@Override
	public List<CustomerSupplier> getListByStatus(Integer status) {
		LambdaQueryWrapper<CustomerSupplier> lambdaQueryWrapper = Wrappers.lambdaQuery();
		if (status != null) {
			lambdaQueryWrapper.eq(BaseEntity::getStatus, status);
		}
		return list(lambdaQueryWrapper);
	}

	@Override
	public Map<Long, CustomerSupplier> getMapInId(List<Long> ids) {
		return baseMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(BaseEntity::getId, customerSupplier -> customerSupplier));
	}

	@Override
	public CustomerSupplier getByUnifiedCode(String unifiedCode) {
		return getOne(Wrappers.<CustomerSupplier>lambdaQuery().eq(CustomerSupplier::getUnifiedCode, unifiedCode)
			.orderByDesc(CustomerSupplier::getCreateTime).last("limit 1"));
	}

	private Boolean checkSupplierComplete(CustomerSupplier customerSupplier) {
		String bankAccount = customerSupplier.getBankAccount();
		//联系人
		String contacts = customerSupplier.getContacts();
		String supperName = customerSupplier.getSupperName();
		String depositBankId = customerSupplier.getDepositBankId();
		String supperPhone = customerSupplier.getSupperPhone();
		String unifiedCode = customerSupplier.getUnifiedCode();
		if (StringUtils.isEmpty(bankAccount) || StringUtils.isEmpty(contacts) || StringUtils.isEmpty(supperName) || StringUtils.isEmpty(depositBankId) || StringUtils.isEmpty(supperPhone) || StringUtils.isEmpty(unifiedCode)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	@Override
	public boolean deleteLogic(@NotEmpty List<Long> ids) {
		Integer integer = baseMapper.selectCount(Wrappers.<CustomerSupplier>lambdaQuery().in(BaseEntity::getId, ids).eq(BaseEntity::getStatus, CommonConstant.OPENSTATUS));
		if (integer > 0) {
			throw new ServiceException("存在已发布的数据,请先取消发布后，在删除!");
		}
		List<CustomerSupplier> supplierList = listByIds(ids);
		if (CollectionUtils.isNotEmpty(supplierList)) {
			StreamUtil.map(supplierList, CustomerSupplier::getUnifiedCode).forEach(unifiedCode -> {
				//查询是否已经入驻--->入驻赋予用户id
				CustomerInfo customerInfo = customerInfoService.getByCompanyCode(unifiedCode, null);
				if (ObjectUtil.isNotEmpty(customerInfo)) {
					Long companyId = customerInfo.getCompanyId();
					if (ObjectUtil.isNotEmpty(companyId)) {
						openOrCloseBackAuthByCompanyId(companyId, CommonConstant.CLOSESTATUS);
					}
				}
			});
		}
		return baseMapper.deleteReal(StringUtil.join(ids, ","));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean save(CustomerSupplier entity) {
		dealField(entity);
		checkSupperName(entity);
		checkSupplierEnable(entity);
		judgmentCredit(entity);
		entity.setSupperCode(CodeUtil.generateCode(CodeEnum.SUPPLY_CODE));
		//查询是否已经入驻--->入驻赋予用户id
		CustomerInfo customerInfo = customerInfoService.getByCompanyCode(entity.getUnifiedCode(), null);
		if (ObjectUtil.isNotEmpty(customerInfo)) {
			Long companyId = customerInfo.getCompanyId();
			entity.setId(companyId);
			entity.setCreateTime(new Date());
			entity.setTenantId(AuthUtil.getTenantId());
			entity.setCreateUser(AuthUtil.getUserId());
			entity.setCreateDept(Func.toLong(AuthUtil.getDeptId()));
			if (ObjectUtil.isNotEmpty(companyId)) {
				openOrCloseBackAuthByCompanyId(companyId, CommonConstant.OPENSTATUS);
			}
		}
		return super.save(entity);
	}

	/**
	 * 通过社会统一代码查询贸易背景并控制代采权利
	 *
	 * @param companyId 社会统一代码
	 * @param status    0关闭 1开启
	 */
	private void openOrCloseBackAuthByCompanyId(Long companyId, Integer status) {
		//作为供应商的贸易背景赋予代采权利
		ITradeBackgroundService tradeBackgroundService = SpringUtil.getBean(ITradeBackgroundService.class);
		List<TradeBackgroundVO> tradeBackgrounds = tradeBackgroundService.listLowerByCompanyId(companyId, null, null);
		if (CollectionUtils.isNotEmpty(tradeBackgrounds)) {
			List<Long> ids = StreamUtil.map(tradeBackgrounds, TradeBackgroundVO::getId);
			//代采权限设置
			tradeBackgroundService.changePurchase(ids, status);
		}
	}

	private void dealField(CustomerSupplier entity) {
		entity.setSupperName(entity.getSupperName().replaceAll(" ", ""));
		entity.setUnifiedCode(entity.getUnifiedCode().replaceAll(" ", "").toUpperCase());
		entity.setBankAccount(entity.getBankAccount().replaceAll(" ", ""));
		entity.setContacts(entity.getContacts().replaceAll(" ", ""));
	}

	@Override
	public boolean updateById(CustomerSupplier entity) {
		dealField(entity);
		checkSupperName(entity);
		checkSupplierEnable(entity);
		judgmentCredit(entity);
		return super.updateById(entity);
	}

	private Boolean checkSupplierEnable(CustomerSupplier customerSupplier) {
		if (customerSupplier.isOnShelf()) {
			if (!checkSupplierComplete(customerSupplier)) {
				throw new ServiceException("信息未填写完整！");
			}
		}
		return Boolean.TRUE;
	}

	private void checkSupperName(CustomerSupplier entity) {
		CustomerSupplier one = this.lambdaQuery().eq(CustomerSupplier::getSupperName, entity.getSupperName()).one();
		if (!ObjectUtils.isEmpty(one)) {
			if (!one.getId().equals(entity.getId())) {
				throw new ServiceException("已存在相同的供应商名称!");
			}
		}

	}

	/**
	 * 判断统一社会信用代码各种信息是否正确
	 *
	 * @param customerSupplier
	 * @return
	 */
	private Boolean judgmentCredit(CustomerSupplier customerSupplier) {
		String unifiedCode = customerSupplier.getUnifiedCode();
		String bankAccount = customerSupplier.getBankAccount();
		if (!StringUtils.isEmpty(bankAccount)) {
			Matcher bankMatcher = bank.matcher(bankAccount);
			if (!bankMatcher.matches()) {
				throw new ServiceException("银行号码输入错误!");
			}
		}
		String supperPhone = customerSupplier.getSupperPhone();
		if (!StringUtils.isEmpty(supperPhone)) {
			Matcher matcher = par.matcher(supperPhone);
			if (!matcher.matches()) {
				throw new ServiceException("手机输入不匹配,输入错误!");
			}
		}
//		boolean b = CustomerUtils.companyThreeElementsIsTure(unifiedCode, customerSupplier.getSupperName(), customerSupplier.getContacts());
//		if (!b) {
//			throw new ServiceException("统一社会信用代码与公司,法人信息不匹配!");
//		}
		return Boolean.TRUE;
	}

}
