/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.entity.CustomerVerificationCode;
import org.springblade.customer.vo.CustomerVerificationCodeVO;

import java.text.ParseException;
import java.util.List;

/**
 * 企业邀请用户设置角色 服务类
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
public interface ICustomerVerificationCodeService extends BaseService<CustomerVerificationCode> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param customerVerificationCode
	 * @return
	 */
	IPage<CustomerVerificationCodeVO> selectCustomerVerificationCodePage(IPage<CustomerVerificationCodeVO> page, CustomerVerificationCodeVO customerVerificationCode);

	/***
	 * 企業邀請用戶 blade_role , coverCodeId
	 * @param bladeRole 角色信息
	 * @param inviteesPhone 被邀请人 手机号
	 * @param inviteesName 被邀请人 姓名
	 * @return  Boolean CustomerVerificationCode
	 */
	CustomerVerificationCodeVO sendInvite(String bladeRole, String inviteesPhone,String inviteesName) throws ParseException;

	/***
	 * 企業邀請用戶 code , frontName
	 * @param code 邀请码
	 * @param frontName  邀请公司名称
	 * @return
	 */
	 boolean checkInviteCodeIsValid( String code, String frontName);
	/***
	 * 企业邀请用戶 code , frontName
	 * @param inviteCode 邀请码
	 * @param frontName  邀请公司名称
	 * @return
	 */
	boolean removeInviteCode(String inviteCode, String frontName);

	/***
	 * 查询当前企业所有邀请人
	 * @return
	 */
	List<CustomerVerificationCodeVO> getList();
}
