<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerFrontCoreValueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerFrontCoreValueResultMap" type="org.springblade.customer.entity.CustomerFrontCoreValue">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="total_tax_last_year" property="totalTaxLastYear"/>
        <result column="total_tax_previous_year" property="totalTaxPreviousYear"/>
        <result column="net_profit" property="netProfit"/>
        <result column="total_tax_payable_last_year" property="totalTaxPayableLastYear"/>
        <result column="total_tax_payable_previous_year" property="totalTaxPayablePreviousYear"/>
        <result column="year_growth" property="yearGrowth"/>
        <result column="annulus_growth" property="annulusGrowth"/>
        <result column="total_accounts_receivable" property="totalAccountsReceivable"/>
        <result column="total_still" property="totalStill"/>
        <result column="collection_rate" property="collectionRate"/>
        <result column="company_id" property="companyId"/>
        <result column="finance_form" property="financeForm"/>
    </resultMap>


    <select id="selectCustomerFrontCoreValuePage" resultMap="customerFrontCoreValueResultMap">
        select * from jrzh_customer_front_core_value where is_deleted = 0
    </select>

</mapper>
