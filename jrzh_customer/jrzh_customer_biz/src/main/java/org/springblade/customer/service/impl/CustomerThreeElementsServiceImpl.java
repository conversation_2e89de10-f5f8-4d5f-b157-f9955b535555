/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.customer.entity.CustomerThreeElements;
import org.springblade.customer.mapper.CustomerThreeElementsMapper;
import org.springblade.customer.service.ICustomerThreeElementsService;
import org.springblade.customer.vo.CustomerThreeElementsVO;
import org.springframework.stereotype.Service;

/**
 * 企业客户三要素 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-07
 */
@Service
public class CustomerThreeElementsServiceImpl extends BaseServiceImpl<CustomerThreeElementsMapper, CustomerThreeElements> implements ICustomerThreeElementsService {

	@Override
	public IPage<CustomerThreeElementsVO> selectCustomerThreeElementsPage(IPage<CustomerThreeElementsVO> page, CustomerThreeElementsVO customerThreeElements) {
		return page.setRecords(baseMapper.selectCustomerThreeElementsPage(page, customerThreeElements));
	}

	@Override
	public CustomerThreeElements selectByThreeElements(String creditCode, String companyName, String legalPersonName) {
		LambdaQueryWrapper<CustomerThreeElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(CustomerThreeElements::getCode, creditCode);
		lambdaQueryWrapper.eq(CustomerThreeElements::getName, companyName);
		lambdaQueryWrapper.eq(CustomerThreeElements::getLegalPersonName, legalPersonName);
		CustomerThreeElements customerThreeElements = this.baseMapper.selectOne(lambdaQueryWrapper);
		return customerThreeElements;
	}

}
