/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.entity.CustomerSupervise;
import org.springblade.customer.mapper.CustomerSuperviseMapper;
import org.springblade.customer.service.ICustomerSuperviseService;
import org.springblade.customer.vo.CustomerSuperviseVO;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 监管方 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-11
 */
@Service
public class CustomerSuperviseServiceImpl extends BaseServiceImpl<CustomerSuperviseMapper, CustomerSupervise> implements ICustomerSuperviseService {
	private static final Pattern bank = Pattern.compile("^[1-9]\\d{12,18}$");

	private static final Pattern par = Pattern.compile("^[1][3,4,5,7,8,9][0-9]{9}$");

	@Override
	public IPage<CustomerSuperviseVO> selectCustomerSupervisePage(IPage<CustomerSuperviseVO> page, CustomerSuperviseVO customerSupervise) {
		return page.setRecords(baseMapper.selectCustomerSupervisePage(page, customerSupervise));
	}

	@Override
	public boolean conlistoff(List<Long> toLongList) {
		return update(Wrappers.<CustomerSupervise>lambdaUpdate().in(BaseEntity::getId, toLongList).set(BaseEntity::getStatus, CommonConstant.CLOSESTATUS));
	}

	@Override
	public Integer handlerOnShelf(List<Long> toLongList) {
		List<CustomerSupervise> customerSuppliers = listByIds(toLongList);
		List<CustomerSupervise> collect = customerSuppliers.parallelStream().filter(customerSupplier -> {
			return checkSuperviseComplete(customerSupplier);
		}).map(customerSupplier -> {
			customerSupplier.setStatus(CommonConstant.OPENSTATUS);
			return customerSupplier;
		}).collect(Collectors.toList());
		updateBatchById(collect);
		return collect.size();
	}

	@Override
	public List<CustomerSupervise> getList() {
		return list(Wrappers.<CustomerSupervise>lambdaQuery().eq(BaseEntity::getStatus, CommonConstant.OPENSTATUS));
	}

	@Override
	public List<CustomerSupervise> getListByStatus(Integer status) {
		LambdaQueryWrapper<CustomerSupervise> lambdaQueryWrapper = Wrappers.lambdaQuery();
		if (status != null){
			lambdaQueryWrapper.eq(BaseEntity::getStatus, status);
		}
		return list(lambdaQueryWrapper);
	}

	@Override
	public boolean conlistput(Long id) {
		CustomerSupervise byId = getById(id);
		if (!checkSuperviseComplete(byId)) {
			throw new ServiceException("信息未填写完整,请填写完成之后进行启用操作！");
		}
		return update(Wrappers.<CustomerSupervise>lambdaUpdate().eq(BaseEntity::getId, byId.getId()).set(BaseEntity::getStatus, CommonConstant.OPENSTATUS));

	}


	private Boolean checkSuperviseComplete(CustomerSupervise customerSupervise ) {
		String bankAccount = customerSupervise.getBankAccount();
		//联系人
		String contacts = customerSupervise.getContacts();
		String supperName = customerSupervise.getSuperviseName();
		Long depositBankId = customerSupervise.getDepositBankId();
		String supperPhone = customerSupervise.getSupervisePhone();
		String unifiedCode = customerSupervise.getUnifiedCode();
		if (StringUtils.isEmpty(bankAccount) || StringUtils.isEmpty(contacts) || StringUtils.isEmpty(supperName) || StringUtils.isEmpty(depositBankId) || StringUtils.isEmpty(supperPhone) || StringUtils.isEmpty(unifiedCode)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}


	@Override
	public boolean deleteLogic(@NotEmpty List<Long> ids) {
		Integer integer = baseMapper.selectCount(Wrappers.<CustomerSupervise>lambdaQuery().in(BaseEntity::getId, ids).eq(BaseEntity::getStatus, CommonConstant.OPENSTATUS));
		if (integer > 0) {
			throw new ServiceException("存在已发布的数据,请先取消发布后，在进行删除!");
		}

		return super.deleteLogic(ids);
	}

	@Override
	public boolean save(CustomerSupervise entity) {
		checkSuperviseName(entity);
		checkSupplierEnable(entity);
		judgmentCredit(entity);
		//entity.set(CodeUtil.generateCode(CodeEnum.SUPPLY_CODE));
		return super.save(entity);
	}

	@Override
	public boolean updateById(CustomerSupervise entity) {
		CustomerSupervise customerSupervise = getById(entity.getId());
		if(ObjectUtil.isNotEmpty(customerSupervise)&&customerSupervise.getStatus().equals(1)){
			throw new ServiceException("该监管方已启用,请刷新当前页面禁用后进行修改!");
		}
		if(ObjectUtil.isEmpty(customerSupervise)){
			throw new ServiceException("该监管方已被删除!");
		}
		checkSuperviseName(entity);
		checkSupplierEnable(entity);
		judgmentCredit(entity);
		return super.updateById(entity);
	}

	private void checkSuperviseName(CustomerSupervise entity) {
		CustomerSupervise	 one = this.lambdaQuery().eq(CustomerSupervise::getSuperviseName, entity.getSuperviseName()).one();
		if (!ObjectUtils.isEmpty(one)) {
			if (!one.getId().equals(entity.getId())) {
				throw new ServiceException("已存在相同的供应商名称!");
			}
		}
	}

	private Boolean checkSupplierEnable(CustomerSupervise customerSupplier) {
		if (customerSupplier.isOnShelf()) {
			if (!checkSuperviseComplete(customerSupplier)) {
				throw new ServiceException("信息未填写完整！");
			}
		}
		return Boolean.TRUE;
	}


	/**
	 * 判断统一社会信用代码各种信息是否正确
	 *
	 * @param customerSupplier
	 * @return
	 */
	private Boolean judgmentCredit(CustomerSupervise customerSupplier) {
		String unifiedCode = customerSupplier.getUnifiedCode();
		String bankAccount = customerSupplier.getBankAccount();
		if (!StringUtils.isEmpty(bankAccount)) {
			Matcher bankMatcher = bank.matcher(bankAccount);
			if (!bankMatcher.matches()) {
				throw new ServiceException("银行号码输入错误!");
			}
		}
		String supperPhone = customerSupplier.getSupervisePhone();
		if (!StringUtils.isEmpty(supperPhone)) {
			Matcher matcher = par.matcher(supperPhone);
			if (!matcher.matches()) {
				throw new ServiceException("手机输入不匹配,输入错误!");
			}
		}
		// TODO  三要素校验
//		boolean b = CustomerUtils.companyThreeElementsIsTure(unifiedCode, customerSupplier.getSuperviseName(), customerSupplier.getContacts());
//		if (!b) {
//			throw new ServiceException("统一社会信用代码与公司,法人信息不匹配!");
//		}
		return Boolean.TRUE;
	}

}
