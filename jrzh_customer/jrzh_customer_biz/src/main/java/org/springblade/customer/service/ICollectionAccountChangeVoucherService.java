/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.entity.CollectionAccountChangeVoucher;
import org.springblade.customer.vo.CollectionAccountChangeVoucherDTO;
import org.springblade.customer.vo.CollectionAccountChangeVoucherVO;

import java.util.List;

/**
 * 回款账户 服务类
 *
 * <AUTHOR>
 * @since 2022-01-24
 */
public interface ICollectionAccountChangeVoucherService extends BaseService<CollectionAccountChangeVoucher> {


	/**
	 * 保存回款账户变更凭证
	 * @param goodsId 产品id
	 * @param collectionAccounts collectionAccounts
	 * @return List<Long>
	 */
	List<Long> saveCollectionAccountChangeVoucher(List<CollectionAccountChangeVoucherDTO> collectionAccounts, Long goodsId);

	/**
	 * 批量更新
	 * @param collectionAccountChangeVouchers collectionAccountChangeVouchers
	 */
	void batchUpdate(List<CollectionAccountChangeVoucherDTO> collectionAccountChangeVouchers);

	/**
	 * 根据产品id查询
	 * @param goodsId 产品id
	 * @return List<CollectionAccountChangeVoucher>
	 */
	List<CollectionAccountChangeVoucherVO> getByGoodsId(Long goodsId);

	/**
	 * 根据企业id和产品id删除
	 * @param enterpriseId 企业id
	 * @param goodsId 产品id
	 */
	void removeByUserIdAndGoodsId(Long enterpriseId, Long goodsId);
}
