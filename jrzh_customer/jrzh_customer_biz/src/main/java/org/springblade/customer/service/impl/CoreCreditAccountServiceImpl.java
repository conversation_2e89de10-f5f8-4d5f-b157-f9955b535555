/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.customer.entity.CoreCreditAccount;
import org.springblade.customer.entity.CoreCreditAccountLog;
import org.springblade.customer.enums.TradeBackGroundEnum.VERIFY_STATUS;
import org.springblade.customer.mapper.CoreCreditAccountMapper;
import org.springblade.customer.service.ICoreCreditAccountLogService;
import org.springblade.customer.service.ICoreCreditAccountService;
import org.springblade.customer.vo.CoreCreditAccountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 回款账户 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Service
public class CoreCreditAccountServiceImpl extends BaseServiceImpl<CoreCreditAccountMapper, CoreCreditAccount> implements ICoreCreditAccountService {
	@Autowired
	private ICoreCreditAccountLogService logService;

	@Override
	public IPage<CoreCreditAccountVO> selectCoreCreditAccountPage(IPage<CoreCreditAccountVO> page, CoreCreditAccountVO coreCreditAccount) {
		return page.setRecords(baseMapper.selectCoreCreditAccountPage(page, coreCreditAccount));
	}

	@Override
	public boolean saveAccount(CoreCreditAccount coreCreditAccount) {
		//账户状态 默认为失效 没验证
		coreCreditAccount.setVerifyStatus(VERIFY_STATUS.UN_VERIFY.getStatus());
		return save(coreCreditAccount);
	}

	@Override
	public CoreCreditAccount getOneById(Long companyId, Long companyLowerId) {
		return baseMapper.selectOne(new QueryWrapper<CoreCreditAccount>().lambda()
			.eq(CoreCreditAccount::getCompanyId, companyId).eq(CoreCreditAccount::getCompanyLowerId, companyLowerId));
	}

	@Override
	public boolean updateAccount(CoreCreditAccount coreCreditAccount) {
		//设置验证通过
		coreCreditAccount.setVerifyStatus(VERIFY_STATUS.VERIFY.getStatus());
		updateById(coreCreditAccount);
		//记录变更历史
		CoreCreditAccountLog log = Objects.requireNonNull(BeanUtil.copyProperties(coreCreditAccount, CoreCreditAccountLog.class));
		log.setCreditCardId(coreCreditAccount.getId());
		return logService.save(log);
	}

}
