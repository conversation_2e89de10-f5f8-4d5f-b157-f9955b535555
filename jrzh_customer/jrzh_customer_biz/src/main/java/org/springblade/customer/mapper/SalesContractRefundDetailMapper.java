/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.customer.entity.SalesContractRefundDetail;
import org.springblade.customer.vo.SalesContractRefundDetailVO;

import java.util.List;

/**
 * 应收账款回款信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
public interface SalesContractRefundDetailMapper extends BaseMapper<SalesContractRefundDetail> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param salesContractRefundDetail
	 * @return
	 */
	List<SalesContractRefundDetailVO> selectSalesContractRefundDetailPage(IPage page, SalesContractRefundDetailVO salesContractRefundDetail);

}
