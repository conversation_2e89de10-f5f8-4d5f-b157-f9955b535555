package org.springblade.customer.controller;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.dto.FrontCoreListDTO;
import org.springblade.customer.handler.IndexHandler;
import org.springblade.customer.service.ICustomerFrontUserTypeService;
import org.springblade.customer.service.IFrontCoreListService;
import org.springblade.customer.service.IFrontFinancingListService;
import org.springblade.customer.service.IIndexService;
import org.springblade.customer.vo.CustomerFrontUserTypeBackVO;
import org.springblade.customer.vo.FrontCoreListVO;
import org.springblade.expense.service.IBillFinancialFlowService;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springblade.product.common.constant.CommodityEnum;
import org.springblade.resource.entity.MenuCommonly;
import org.springblade.resource.service.IMenuCommonlyService;
import org.springblade.resource.vo.MenuCommonlyVO;
import org.springblade.resource.wrapper.MenuCommonlyWrapper;
import org.springblade.system.entity.Menu;
import org.springblade.system.entity.RoleMenu;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteMenuService;
import org.springblade.system.feign.RemotePostService;
import org.springblade.system.feign.RemoteRoleService;
import org.springblade.system.utils.UserUtils;
import org.springblade.system.vo.GiveTreeVO;
import org.springblade.system.vo.MenuVO;
import org.springblade.system.vo.UserVO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.WEL_HOME + CommonConstant.WEB_BACK + "/index")
@Api(value = "首页", tags = "首页接口")
public class IndexController extends BladeController {

    private final IMenuCommonlyService menuCommonlyService;
    private final RemoteMenuService menuService;


    private final IFrontCoreListService frontCoreListService;

    private final IBillFinancialFlowService billFinancialFlowService;


    private final IFinanceApplyService financeApplyService;


    private final ILoanManageRepaymentService loanManageRepaymentService;


    private final IFrontFinancingListService frontFinancingListService;

    private final IIndexService indexService;

    private final ICustomerFrontUserTypeService customerFrontUserTypeService;
    private final List<IndexHandler> indexHandlers;
    private RemoteRoleService remoteRoleService;
    private RemoteDeptSearchService deptSearchService;
    private RemotePostService remotePostService;

    /**
     * 常用功能列表
     */
    @GetMapping("commonly/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "常用功能列表", notes = "传入menuCommonly")
    public R<List<MenuCommonlyVO>> list(MenuCommonly menuCommonly, BladeUser user) {
        Long userId = user.getUserId();
        String roleId = user.getRoleId();
        List<MenuCommonly> commonly = menuCommonlyService.list(Wrappers.<MenuCommonly>lambdaQuery().eq(MenuCommonly::getUserId, userId).last("limit 20").orderByAsc(MenuCommonly::getCreateTime));
        if (AuthUtil.isAdministrator()) {
            if (Func.toStrList(CommonConstant.PERMIT_OPERA_EXCLUDE_MENU_ACCOUNT).contains(AuthUtil.getUserAccount())) {
                return R.data(MenuCommonlyWrapper.build().listVO(commonly));
            }
        }
        List<RoleMenu> menuList = remoteRoleService.getRoleMenusByRoleId(roleId).getData();
        List<Long> menuCollect = menuList.stream().map(RoleMenu::getMenuId).collect(Collectors.toList());
        List<MenuCommonly> collect = commonly.stream().filter(o -> !menuCollect.contains(o.getMenuId())).collect(Collectors.toList());
        List<Long> collect1 = collect.stream().map(MenuCommonly::getMenuId).collect(Collectors.toList());
        List<MenuCommonly> collect2 = commonly.stream().filter(j -> !collect1.contains(j.getMenuId())).collect(Collectors.toList());
        //判断如果角色下没有该菜单则删除对应数据
        if (CollectionUtils.isNotEmpty(collect1)) {
            for (Long menuId : collect1) {
                menuCommonlyService.deleteByMenuIdAndUserId(menuId, userId);
            }
        }
        return R.data(MenuCommonlyWrapper.build().listVO(collect2));
    }

    /**
     * 新增常用功能
     */
    @PostMapping("commonly/submit")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "新增", notes = "传入menuIds")
    public R<Boolean> save(@Valid @RequestBody MenuCommonlyVO menuCommonly, BladeUser user) {
        List<Long> menuIds = menuCommonly.getMenuIds();
        if (CollectionUtils.isEmpty(menuIds)) {
            return R.status(true);
        }
        return R.status(menuCommonlyService.saveMenuCommonly(menuIds));
    }

    /**
     * 前端菜单数据
     */
    @GetMapping("/routes")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "前端菜单数据", notes = "传入菜单名称")
    public R<List<MenuVO>> routes(BladeUser user, Long topMenuId, String name) {
        List<MenuVO> list = menuService.routes1((user == null) ? null : user.getRoleId(), topMenuId, name).getData();
        return R.data(list);
    }

    /**
     * 获取权限分配树形结构
     */
    @GetMapping("/role-tree-keys")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "角色所分配的树", notes = "角色所分配的树")
    public R<GiveTreeVO> roleTreeKeys(BladeUser user) {
        Long userId = user.getUserId();
        GiveTreeVO vo = new GiveTreeVO();
        vo.setMenu(menuCommonlyService.roleTreeKeys(userId));
        return R.data(vo);
    }


    /**
     * 查询核心企业季度排名
     */
    //@GetMapping("core/list")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入frontCoreList")
    public R<List<FrontCoreListVO>> list(FrontCoreListDTO frontCoreList) {

        List<FrontCoreListVO> pages = frontCoreListService.getCoreist(frontCoreList);
        return R.data(pages);
    }

    /**
     * 分页 核心企业列表信息表ranking/core
     */
    @GetMapping("core/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入frontFinancingList")
    public R<List<CustomerFrontUserTypeBackVO>> coreEnterpriseList() {
        List<CustomerFrontUserTypeBackVO> customerFrontUserTypeBackVOS = customerFrontUserTypeService.selectCoreEnterpriseQuarterRanking();
        return R.data(customerFrontUserTypeBackVOS);
    }

    /**
     * 昨日收入金额
     *
     * @return
     */
    @GetMapping("income/money")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "昨日收入金额")
    public R<Map<String, Object>> income() {
        Map<String, Object> map = new HashMap<>();
        Integer income = billFinancialFlowService.yesTerDayIncome();
        Integer dayexpend = billFinancialFlowService.yesTerDayexpenditure();
        if (ObjectUtil.isEmpty(income)) {
            map.put("income", 0);
        } else {
            map.put("income", income);
        }
        if (ObjectUtil.isEmpty(dayexpend)) {
            map.put("dayexpend", 0);
        } else {
            map.put("dayexpend", dayexpend);
        }
        //昨日融资金额
        BigDecimal financingIncome = BigDecimal.ZERO;
        for (IndexHandler indexHandler : indexHandlers) {
            financingIncome = financingIncome.add(indexHandler.yesTerDayFinancingAmount());
        }
        map.put("financing", financingIncome);

        //昨日融资笔数
        Integer financingCount = 0;
        for (IndexHandler indexHandler : indexHandlers) {
            financingCount = financingCount + indexHandler.yesTerDayFinanceStrokeCount();
        }
        map.put("financingCount", financingCount);


        //昨日还款金额
        BigDecimal repaymentCount = BigDecimal.ZERO;
        for (IndexHandler indexHandler : indexHandlers) {
            repaymentCount = repaymentCount.add(indexHandler.yesTerDayRepaymentAmount());
        }
        map.put("repaymentCount", repaymentCount);
        Integer repaymentStrokeCount = 0;
        for (IndexHandler indexHandler : indexHandlers) {
            repaymentStrokeCount = repaymentStrokeCount + indexHandler.yesTerDayRepaymentStrokeCount();
        }
        map.put("repaymentStrokeCount", repaymentStrokeCount);
        return R.data(map);

    }

    @GetMapping("finance/showChart")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "融资金额报表统计")
    public R<Map<String, Map<String, Object>>> financeShowChart1(Integer incomeStatus) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        Map<String, Object> financeShowChartMap = indexService.financeShowChart();
        Map<String, Object> financeStrokeMap = indexService.financeStrokeCountShowChart();
        Map<String, Object> repaymentCountMap = indexService.selectRepaymentCountShowChart();
        Map<String, Object> showChartListMap = indexService.showChartList(incomeStatus);
        resultMap.put("financeMoney", financeShowChartMap);
        resultMap.put("financeStroke", financeStrokeMap);
        resultMap.put("repaymentCount", repaymentCountMap);
        resultMap.put("yesterdayMoney", showChartListMap);
        return R.data(resultMap);

    }

    /**
     * 查询用户个人信息
     */
    @GetMapping("user/centre")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "用户个人信息", notes = "用户个人信息")
    public R list() {
        Long userId = AuthUtil.getUserId();
        if (ObjectUtil.isEmpty(userId)) {
            return R.status(true);
        }
        User user = UserUtils.getUserById(userId);
        if (ObjectUtil.isEmpty(user)) {
            return R.status(true);
        }
        return R.data(parseUserVO(user));
    }

    public UserVO parseUserVO(User user) {
        UserVO userVO = Objects.requireNonNull(BeanUtil.copy(user, UserVO.class));
        List<String> roleName = remoteRoleService.getRoleNames(user.getRoleId()).getData();
        List<String> deptName =deptSearchService.getDeptNames(user.getDeptId()).getData();
        List<String> postName =remotePostService.getPostNames(user.getPostId()).getData();
        userVO.setRoleName(Func.join(roleName));
        userVO.setDeptName(Func.join(deptName));
        userVO.setPostName(Func.join(postName));
        return userVO;
    }

}
