<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.InviteCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inviteCodeResultMap" type="org.springblade.customer.entity.InviteCode">
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="core_enterprise_id" property="coreEnterpriseId"/>
        <result column="code" property="code"/>
    </resultMap>


    <select id="selectInviteCodePage" resultMap="inviteCodeResultMap">
        select * from jrzh_invite_code where is_deleted = 0
    </select>

</mapper>
