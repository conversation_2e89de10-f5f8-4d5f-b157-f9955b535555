package org.springblade.customer.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springblade.common.annotation.MappingIgnore;
import org.springblade.customer.dto.CustomerFrontValueSubDTO;
import org.springblade.customer.entity.CustomerFrontValueSub;
import org.springblade.customer.entity.FrontValue;
import org.springblade.customer.vo.CustomerFrontValueSubVO;

import java.util.List;

/**
 * 融资企业价值分析变更包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface CustomerFrontValueSubWrapper  {
    /**
      * 获取mapper对象
      * @return
      */
	static CustomerFrontValueSubWrapper build() {
		return Mappers.getMapper(CustomerFrontValueSubWrapper.class);
 	}

    /**
	 * 实体类转vo
	 * @param customerFrontValueSub
	 * @return
	 */
	CustomerFrontValueSubVO entityVO(CustomerFrontValueSub customerFrontValueSub);
    /**
	 * 实体类转dto
	 * @param customerFrontValueSub
	 * @return
	 */
    CustomerFrontValueSubDTO entityDTO(CustomerFrontValueSub customerFrontValueSub);
    /**
	 * 实体类List转VOList
	 * @param customerFrontValueSubs
	 * @return
	 */
    List<CustomerFrontValueSubVO> listVO(List<CustomerFrontValueSub> customerFrontValueSubs);
    /**
	 * 实体类Page转VOPage
	 * @param page
	 * @return
	 */
    default Page<CustomerFrontValueSubVO> pageVO(IPage<CustomerFrontValueSub> page) {
        List<CustomerFrontValueSubVO> customerFrontValueSubVOList = this.listVO(page.getRecords());
        Page<CustomerFrontValueSubVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(customerFrontValueSubVOList);
        pageVO.setPages(page.getPages());
        return pageVO;
    }
    @MappingIgnore
	CustomerFrontValueSub toCustomerFrontValutSub(FrontValue front);
}
