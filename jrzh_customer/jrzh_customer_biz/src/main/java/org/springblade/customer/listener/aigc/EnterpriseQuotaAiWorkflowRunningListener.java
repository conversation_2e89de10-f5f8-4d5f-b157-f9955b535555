package org.springblade.customer.listener.aigc;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.aigc.dto.WorkflowRunningDto;
import org.springblade.aigc.handler.AiWorkflowRunningHandler;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.design.entity.ExternalFormTemplateField;
import org.springblade.workflow.design.service.IExternalFormTemplateFieldService;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 企业额度AI工作流执行监听器
 * @date 2025年05月29日 18:43
 */
@Slf4j
@Component("enterpriseQuotaAiWorkflowRunningListener")
@RequiredArgsConstructor
public class EnterpriseQuotaAiWorkflowRunningListener implements TaskListener {
    private final List<AiWorkflowRunningHandler> aiWorkflowRunningHandlerList;

    private final IExternalFormTemplateFieldService externalFormTemplateFieldService;

    @Override
    public void notify(DelegateTask delegateTask) {
        String status = delegateTask.getVariable(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, String.class);
        if (StringUtil.isNotBlank(status) && StringUtil.equals(status, WfProcessConstant.STATUS_REJECT)) {
            return;
        }

        Map<String, Object> variables = delegateTask.getVariables();

        Long productId = Func.toLong(variables.get(ProcessConstant.BUSINESS_ID));
        Integer productType = Func.toInt(variables.get(ProcessConstant.PRODUCT_TYPE));
        String applyUserName = Func.toStr(variables.get("applyUserName"));

        Long enterpriseQuotaId = Func.toLong(variables.get(ProcessConstant.ENTERPRISE_QUOTA_Id));
        String processNo = Func.toStr(variables.get(WfProcessConstant.PROCESS_NO));
        AiWorkflowRunningHandler workflowRunningHandler = getAiWorkflowRunningHandler(productType);

        Map<String, Object> frontFinancialData = (Map<String, Object>) variables.get("frontFinancialData");
        WorkflowRunningDto workflowRunningDto = new WorkflowRunningDto();
        workflowRunningDto.setProductId(productId);
        workflowRunningDto.setProcessNo(processNo);
        workflowRunningDto.setEnterpriseQuotaId(enterpriseQuotaId);
        workflowRunningDto.setCustomerName(applyUserName);
        workflowRunningDto.setVariables(getWorkflowRunningParam(frontFinancialData, applyUserName));

        workflowRunningHandler.handler(workflowRunningDto);
    }

    /**
     * 获取AI工作流执行处理器
     *
     * @param productType 产品类型
     * @return AiWorkflowRunningHandler   执行处理器
     */
    private AiWorkflowRunningHandler getAiWorkflowRunningHandler(Integer productType) {
        AiWorkflowRunningHandler defaultHandler = null;
        for (AiWorkflowRunningHandler aiWorkflowRunningHandler : aiWorkflowRunningHandlerList) {
            if (aiWorkflowRunningHandler.support().equals(productType)) {
                return aiWorkflowRunningHandler;
            }
            // 记录默认实现
            if (aiWorkflowRunningHandler.support() == 0) {
                defaultHandler = aiWorkflowRunningHandler;
            }
        }
        if (defaultHandler == null) {
            throw new ServiceException("未找到对应产品AI工作流");
        }
        return defaultHandler;
    }

    private Map<String, Object> getWorkflowRunningParam(Map<String, Object> frontFinancialData, String applyUserName) {
        Map<String, Object> result = new HashMap<>();
        result.put("companyName", applyUserName);
        List<String> list = new ArrayList<>();
        try {
            List<ExternalFormTemplateField> externalFormTemplateFields =
                    externalFormTemplateFieldService.list();
            Map<String, String> map = CollStreamUtil.toMap(externalFormTemplateFields, ExternalFormTemplateField::getFieldKey, ExternalFormTemplateField::getName);
            for (Map.Entry<String, Object> entry : frontFinancialData.entrySet()) {
                String value = Func.toStr(entry.getValue());
                if(StringUtil.isBlank(value)){
                    list.add(map.get(entry.getKey()) + ":" + BigDecimal.ZERO);
                }else if(NumberUtil.isNumber(value)){
                    list.add(map.get(entry.getKey()) + ":" + new BigDecimal(value)
                            .multiply(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_EVEN));
                }
            }
            result.put("workflowParam", list.toString());
        } catch (Exception e) {
            log.error("授信征信数据解析失败：{}", e.getMessage());
        }
        return result;
    }

}
