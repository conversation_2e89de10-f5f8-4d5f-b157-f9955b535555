/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.entity.CoreSalesContract;
import org.springblade.customer.enums.TradeBackGroundEnum.CONFIRM_STATUS;
import org.springblade.customer.enums.TradeBackGroundEnum.PROOF_STATUS;
import org.springblade.customer.enums.TradeBackGroundEnum.SETTLE_STATUS;
import org.springblade.customer.mapper.CoreSalesContractMapper;
import org.springblade.customer.service.ICoreSalesContractService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CoreSalesContractVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 销售合同 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Service
public class CoreSalesContractServiceImpl extends BaseServiceImpl<CoreSalesContractMapper, CoreSalesContract> implements ICoreSalesContractService {

	@Override
	public IPage<CoreSalesContractVO> selectCoreSalesContractPage(IPage<CoreSalesContractVO> page, CoreSalesContractVO coreSalesContract) {
		return page.setRecords(baseMapper.selectCoreSalesContractPage(page, coreSalesContract));
	}

	@Override
	public boolean saveSalesContract(CoreSalesContract coreSalesContract) {
		if (ObjectUtil.isEmpty(coreSalesContract.getId())) {
			//生成唯一标识
			coreSalesContract.setContractNo(CodeUtil.generateCode(CodeEnum.SALE_CONTRACT_NO));
			//默认上、下游未确权
			coreSalesContract.setConfirmStatus(CONFIRM_STATUS.UN_CONFIRM.getStatus());
			coreSalesContract.setCompanyComfire(CONFIRM_STATUS.UN_CONFIRM.getStatus());
			coreSalesContract.setCompanyLowerComfire(CONFIRM_STATUS.UN_CONFIRM.getStatus());
			//默认待确权
			coreSalesContract.setProofStatus(PROOF_STATUS.UN_CONFIRMED.getStatus());
			//默认未结清
			coreSalesContract.setSettle(SETTLE_STATUS.UNSETTLE.getStatus());
		}
		//若凭证上传 生成凭证编号
		if (ObjectUtil.isNotEmpty(coreSalesContract.getProof())) {
			coreSalesContract.setProofNo(CodeUtil.generateCode(CodeEnum.PROOF_NO));
		}
		return saveOrUpdate(coreSalesContract);
	}

	@Override
	public boolean confirm(Long id) {
		CoreSalesContract contract = getById(id);
		Long userId = MyAuthUtil.getUserId();
		//判断是否为合同中的确权人
		Boolean isCompanyId = null;
		isCompanyId = userId.equals(contract.getCompanyId()) ? true : null;
		//判断非上游确权时
		if (isCompanyId == null) {
			isCompanyId = userId.equals(contract.getCompanyLowerId()) ? false : null;
		}
		//是合同中的确权人时
		if (isCompanyId != null) {
			if (isCompanyId) {
				contract.setCompanyComfire(CONFIRM_STATUS.CONFIRM.getStatus());
			} else {
				contract.setCompanyLowerComfire(CONFIRM_STATUS.CONFIRM.getStatus());
			}
			//确认双方是否都已确权
			if(bothConfirm(contract)){
				contract.setConfirmStatus(CONFIRM_STATUS.CONFIRM.getStatus());
			}
			return updateById(contract);
		}
		return false;
	}

	@Override
	public List<CoreSalesContract> listContractById(Long companyId, Long companyLowerId) {
		return list(new QueryWrapper<CoreSalesContract>().lambda().eq(CoreSalesContract::getCompanyId, companyId)
			.eq(CoreSalesContract::getCompanyLowerId, companyLowerId));
	}

	private boolean bothConfirm(CoreSalesContract contract){
		return CONFIRM_STATUS.CONFIRM.getStatus().equals(contract.getCompanyComfire())
			&&CONFIRM_STATUS.CONFIRM.getStatus().equals(contract.getCompanyLowerComfire());
	}
}
