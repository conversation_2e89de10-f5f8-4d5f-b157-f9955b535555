<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerThreeElementsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerThreeElementsResultMap" type="org.springblade.customer.entity.CustomerThreeElements">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="legal_person_name" property="legalPersonName"/>
        <result column="vertify_status" property="vertifyStatus"/>
    </resultMap>


    <select id="selectCustomerThreeElementsPage" resultMap="customerThreeElementsResultMap">
        select * from jrzh_customer_three_elements where is_deleted = 0
    </select>

</mapper>
