package org.springblade.customer.controller.front;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.customer.service.ICustomerMaterialService;
import org.springblade.customer.util.MyAuthUtil;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/customer/material")
@Api(value = "客户资料", tags = "客户资料接口")
public class CustomerMaterialController {


	private final ICustomerMaterialService customerMaterialService;

	/**
	 * 新增 客户资料
	 */
	@PostMapping("/save")
	@ApiOperation(value = "新增资料")
	public R<Long> save(@RequestBody CustomerMaterial customerMaterial) {
		customerMaterialService.saveCustomerMaterial(customerMaterial);
		return R.data(customerMaterial.getId());
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情")
	public R<CustomerMaterial> detail(Long goodsId) {
		return R.data(customerMaterialService.detail(goodsId));
	}


	@GetMapping("/getById")
	@ApiOperation("根据id查询")
	public R<CustomerMaterial> getById(@RequestParam Long id) {
		return R.data(customerMaterialService.getById(id));
	}

	/**
	 * 详情 冻结流程资料
	 */
	@GetMapping("/detail_with_business")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情 冻结流程资料")
	public R<CustomerMaterial> detailByBusinessId(@RequestParam Long businessId) {
		return R.data(customerMaterialService.detailByBusinessId(businessId));
	}

	/**
	 * 保存 冻结流程资料
	 */
	@PostMapping("/save_unfrozen_material")
	@ApiOperation(value = "保存 冻结流程资料")
	public R<Long> saveUnfrozenMaterial(@RequestBody CustomerMaterial customerMaterial) {
		customerMaterial.setUserId(MyAuthUtil.getUserId());
		return R.data(customerMaterialService.saveUnFrozenCustomerMaterial(customerMaterial));
	}

	/**
	 * 多资方产品对应的 资金产品客户资料详情
	 * @return
	 */
	@GetMapping("/multi_funding_material-detail")
	@ApiOperation(value = "多资方产品对应的-资金产品客户资料详情")
	public R<List<CustomerMaterial>> getMultiFundingCustomerMaterialById(@RequestParam @Valid String goodsIds) {
		return R.data(customerMaterialService.getMultiFundingCustomerMaterialById(goodsIds));
	}
}
