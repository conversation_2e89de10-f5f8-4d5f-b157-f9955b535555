/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.CustomerServiceConfig;
import org.springblade.customer.service.ICustomerServiceConfigService;
import org.springblade.customer.vo.CustomerServiceConfigVO;
import org.springblade.customer.wrapper.CustomerServiceConfigWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客服配置表 控制器
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_BACK + "/customerService/customerServiceConfig")
@Api(value = "客服配置表", tags = "客服配置表接口")
public class CustomerServiceConfigController extends BladeController {

	private final ICustomerServiceConfigService customerServiceConfigService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerServiceConfig")
	@PreAuth("hasPermission('customerService:customerServiceConfig:detail') or hasRole('administrator')")
	public R<CustomerServiceConfigVO> detail(CustomerServiceConfig customerServiceConfig) {
		CustomerServiceConfig detail = customerServiceConfigService.getOne(Condition.getQueryWrapper(customerServiceConfig));
		return R.data(CustomerServiceConfigWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 客服配置表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerServiceConfig")
	@PreAuth("hasPermission('customerService:customerServiceConfig:list') or hasRole('administrator')")
	public R<IPage<CustomerServiceConfigVO>> list(CustomerServiceConfig customerServiceConfig, Query query) {
		IPage<CustomerServiceConfig> pages = customerServiceConfigService.page(Condition.getPage(query), Condition.getQueryWrapper(customerServiceConfig));

		return R.data(CustomerServiceConfigWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 客服配置表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerServiceConfig")
	@PreAuth("hasPermission('customerService:customerServiceConfig:page') or hasRole('administrator')")
	public R<IPage<CustomerServiceConfigVO>> page(CustomerServiceConfigVO customerServiceConfig, Query query) {
		IPage<CustomerServiceConfigVO> pages = customerServiceConfigService.selectCustomerServiceConfigPage(Condition.getPage(query), customerServiceConfig);
		return R.data(pages);
	}

	/**
	 * 新增 客服配置表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerServiceConfig")
	@PreAuth("hasPermission('customerService:customerServiceConfig:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody CustomerServiceConfig customerServiceConfig) {
		List<CustomerServiceConfig> customerServiceConfigList = customerServiceConfigService.lambdaQuery().eq(CustomerServiceConfig::getStatus, CommonConstant.ENABLE).list();
		List<String> hasEnableWebSite = customerServiceConfigList.stream().map(CustomerServiceConfig::getWebSite).collect(Collectors.toList());
		Assert.isFalse(hasEnableWebSite.contains(customerServiceConfig.getWebSite()), "该网址已有相应的配置，请勿重复添加");
		return R.status(customerServiceConfigService.save(customerServiceConfig));
	}

	/**
	 * 修改 客服配置表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerServiceConfig")
	@PreAuth("hasPermission('customerService:customerServiceConfig:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody CustomerServiceConfig customerServiceConfig) {
		if (CommonConstant.ENABLE.equals(customerServiceConfig.getStatus())) {
			List<CustomerServiceConfig> customerServiceConfigList = customerServiceConfigService.lambdaQuery().eq(CustomerServiceConfig::getStatus, CommonConstant.ENABLE).list();
			List<String> hasEnableWebSite = customerServiceConfigList.stream().map(CustomerServiceConfig::getWebSite).collect(Collectors.toList());
			Assert.isFalse(hasEnableWebSite.contains(customerServiceConfig.getWebSite()), "该网址已有相应的配置，请勿重复添加");
		}
		return R.status(customerServiceConfigService.updateById(customerServiceConfig));
	}

	/**
	 * 新增或修改 客服配置表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerServiceConfig")
	@PreAuth("hasPermission('customerService:customerServiceConfig:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody CustomerServiceConfig customerServiceConfig) {
		if (CommonConstant.ENABLE.equals(customerServiceConfig.getStatus())) {
			List<CustomerServiceConfig> customerServiceConfigList = customerServiceConfigService.lambdaQuery().eq(CustomerServiceConfig::getStatus, CommonConstant.ENABLE).list();
			List<String> hasEnableWebSite = customerServiceConfigList.stream().map(CustomerServiceConfig::getWebSite).collect(Collectors.toList());
			Assert.isFalse(hasEnableWebSite.contains(customerServiceConfig.getWebSite()), "该网址已有相应的配置，请勿重复添加");
		}
		return R.status(customerServiceConfigService.saveOrUpdate(customerServiceConfig));
	}


	/**
	 * 删除 客服配置表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('customerService:customerServiceConfig:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<CustomerServiceConfig> customerServiceConfigs = customerServiceConfigService.lambdaQuery().in(CustomerServiceConfig::getId, Func.toLongList(ids)).list();
		List<Long> enableIds = customerServiceConfigs.stream().filter(e -> CommonConstant.ENABLE.equals(e.getStatus())).map(CustomerServiceConfig::getId).collect(Collectors.toList());
		Assert.isFalse(enableIds.size() > 0, "请先禁用后在删除");
		return R.status(customerServiceConfigService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 启用客服
	 */
	@PostMapping("/enable")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "启用客服", notes = "传入ids")
	@PreAuth("hasPermission('customerService:customerServiceConfig:remove') or hasRole('administrator')")
	public R<Boolean> enable(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerServiceConfigService.enable(ids));
	}

	/**
	 * 禁用客服
	 */
	@PostMapping("/disable")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "启用客服", notes = "传入ids")
	@PreAuth("hasPermission('customerService:customerServiceConfig:remove') or hasRole('administrator')")
	public R<Boolean> disable(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerServiceConfigService.disable(ids));
	}
}
