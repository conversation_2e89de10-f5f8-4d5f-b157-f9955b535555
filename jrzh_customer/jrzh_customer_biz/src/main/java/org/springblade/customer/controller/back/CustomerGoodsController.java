/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.ICustomerGoodsTradeBackgroundService;
import org.springblade.customer.vo.CustomerGoodsTradeBackgroundVO;
import org.springblade.customer.vo.CustomerGoodsVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客户产品 控制器
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK+CommonConstant.WEB_BACK +"/customer/goods")
@Api(value = "客户产品", tags = "平台客户产品接口")
public class CustomerGoodsController extends BladeController {

	private final ICustomerGoodsService customerGoodsService;
	private final ICustomerGoodsTradeBackgroundService customerGoodsTradeBackgroundService;
	/**
	 * 分页 客户产品
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerGoods")
	public R<List<CustomerGoodsVO>> list(@RequestParam Long enterpriseId, @RequestParam Integer enterpriseType) {
		return R.data(customerGoodsService.getByCustomerAndEnterpriseType(enterpriseId,enterpriseType));
	}


	@GetMapping("/selectGoodsTradeBackground")
	@ApiOperation("查询产品绑定贸易背景")
	public R<List<CustomerGoodsTradeBackgroundVO>> selectGoodsTradeBackground(Long customerGoodsId) {
		return R.data(customerGoodsTradeBackgroundService.selectByCustomerGoodsId(customerGoodsId));
	}

	@GetMapping("/selectTradeBackgroundBindGoods")
	@ApiOperation("查询贸易背景绑定产品")
	public R<CustomerGoodsVO> selectTradeBackgroundBindGoods(@RequestParam Long tradeBackgroundId) {
		return R.data(customerGoodsService.selectEnterpriseBindGoods(tradeBackgroundId));
	}

}
