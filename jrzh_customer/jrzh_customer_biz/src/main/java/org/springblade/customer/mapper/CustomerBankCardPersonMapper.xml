<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerBankCardPersonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerBankCardPersonResultMap" type="org.springblade.customer.entity.CustomerBankCardPerson">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="user_id" property="userId"/>
        <result column="bank_id" property="bankId"/>
        <result column="bank_card_no" property="bankCardNo"/>
        <result column="bank_deposit" property="bankDeposit"/>
        <result column="goods_id" property="goodsId"/>
        <result column="goods_type" property="goodsType"/>
    </resultMap>


    <select id="selectCustomerBankCardPersonPage" resultMap="customerBankCardPersonResultMap">
        select *
        from jrzh_customer_bank_card_person
        where is_deleted = 0
    </select>

</mapper>
