/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.customer.dto.CapitalListDTO;
import org.springblade.customer.entity.Capital;
import org.springblade.customer.vo.CapitalVO;

import java.util.List;

/**
 * jrzh_capital Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
public interface CapitalMapper extends BaseMapper<Capital> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param capital
	 * @return
	 */
	List<CapitalVO> selectCapitalPage(IPage page, CapitalVO capital);

	/***
	 * 懒加载
	 * @param capital
	 * @return
	 */
    List<CapitalVO> getCusCapital(@Param("capital") CapitalListDTO capital);

	/**
	 * 资金方企业id
	 * @param companyId
	 * @return
	 */
    CapitalVO getOneCapitaVo(@Param("companyId") Long companyId);

	/***
	 * 根据资金方名称 查询资金方信息
	 * @param name
	 * @return
	 */
	List<CapitalVO> getCapitalNameAll(@Param("name")String name);

	/***
	 * 懒加载
	 * @param capital
	 * @return
	 */
	List<CapitalVO> getCusCapitaLazyList(@Param("capital")CapitalListDTO capital);
}
