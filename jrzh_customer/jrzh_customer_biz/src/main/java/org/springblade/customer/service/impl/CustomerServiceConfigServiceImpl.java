/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.CustomerServiceConfig;
import org.springblade.customer.mapper.CustomerServiceConfigMapper;
import org.springblade.customer.service.ICustomerServiceConfigService;
import org.springblade.customer.vo.CustomerServiceConfigVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 客服配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
public class CustomerServiceConfigServiceImpl extends BaseServiceImpl<CustomerServiceConfigMapper, CustomerServiceConfig> implements ICustomerServiceConfigService {

	@Override
	public IPage<CustomerServiceConfigVO> selectCustomerServiceConfigPage(IPage<CustomerServiceConfigVO> page, CustomerServiceConfigVO customerServiceConfig) {
		return page.setRecords(baseMapper.selectCustomerServiceConfigPage(page, customerServiceConfig));
	}

	@Override
	public Boolean enable(String ids) {
		List<Long> longList = Func.toLongList(ids);
		Assert.isFalse(longList.size() == 0, "传入id不能为空");
		/***
		 * 查出所有要启用的
		 */
		List<CustomerServiceConfig> customerServiceConfigs = this.lambdaQuery().in(CustomerServiceConfig::getId, longList).list();
		/**
		 * 过滤出已经禁用的
		 */
		customerServiceConfigs = customerServiceConfigs.stream().filter(e -> CommonConstant.DIAABLE.equals(e.getStatus())).collect(Collectors.toList());

		/***
		 * 查出所有已经启用的
		 */
		List<CustomerServiceConfig> customerServiceConfigsEnable = this.lambdaQuery().eq(CustomerServiceConfig::getStatus, CommonConstant.ENABLE).list();
		/***
		 * 过滤出已经启用的网址
		 */
		List<String> hasEnableWebSite = customerServiceConfigsEnable.stream().map(CustomerServiceConfig::getWebSite).collect(Collectors.toList());


		List<String> noEnableWebSite = customerServiceConfigs.stream().map(CustomerServiceConfig::getWebSite).collect(Collectors.toList());

		Assert.isFalse(CollUtil.containsAny(hasEnableWebSite,noEnableWebSite), "请先禁用相应网站的配置");
		return changeStatus(customerServiceConfigs.stream().map(CustomerServiceConfig::getId).collect(Collectors.toList()), CommonConstant.ENABLE);
	}

	@Override
	public Boolean disable(String ids) {
		return changeStatus(Func.toLongList(ids), CommonConstant.DIAABLE);
	}

	@Override
	public CustomerServiceConfig getCustomerServiceConfigByWebSite(String webSite) {

		List <CustomerServiceConfig> customerServiceConfigs=this.lambdaQuery().eq(CustomerServiceConfig::getWebSite,webSite)
			.eq(CustomerServiceConfig::getStatus,CommonConstant.ENABLE).list();
		Assert.isFalse(customerServiceConfigs.size()==0,"没有配置相应的数据信息,请联系后台管理员");
		return customerServiceConfigs.get(0);
	}

}
