package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.CustomerFrontCoreValue;
import org.springblade.customer.vo.CustomerFrontCoreValueVO;

import java.util.Objects;

/**
 * 核心企业价值分析包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
public class CustomerFrontCoreValueWrapper extends BaseEntityWrapper<CustomerFrontCoreValue, CustomerFrontCoreValueVO> {

    public static CustomerFrontCoreValueWrapper build() {
        return new CustomerFrontCoreValueWrapper();
    }

    @Override
    public CustomerFrontCoreValueVO entityVO(CustomerFrontCoreValue CustomerFrontCoreValue) {
        CustomerFrontCoreValueVO CustomerFrontCoreValueVO = Objects.requireNonNull(BeanUtil.copy(CustomerFrontCoreValue, CustomerFrontCoreValueVO.class));

        //User createUser = UserCache.getUser(  CustomerFrontCoreValue.getCreateUser());
        //User updateUser = UserCache.getUser(  CustomerFrontCoreValue.getUpdateUser());
        //  CustomerFrontCoreValueVO.setCreateUserName(createUser.getName());
        //  CustomerFrontCoreValueVO.setUpdateUserName(updateUser.getName());

        return CustomerFrontCoreValueVO;
    }
}

