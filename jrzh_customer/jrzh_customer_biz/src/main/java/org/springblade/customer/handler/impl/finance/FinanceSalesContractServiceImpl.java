package org.springblade.customer.handler.impl.finance;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.customer.entity.SalesContract;
import org.springblade.customer.entity.SalesContractDetail;
import org.springblade.customer.enums.SaleContractDetailsEnum;
import org.springblade.customer.service.ISalesContractDetailService;
import org.springblade.customer.service.ISalesContractService;
import org.springblade.customer.vo.SalesContractVO;
import org.springblade.finance.dto.FinanceApplyDTO;
import org.springblade.finance.external.dto.SalesContractDTO;
import org.springblade.finance.external.handler.salesContract.FinanceSalesContractService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

@Service
@AllArgsConstructor
public class FinanceSalesContractServiceImpl implements FinanceSalesContractService {
    private final ISalesContractService salesContractService;
    private final ISalesContractDetailService salesContractDetailService;

    /**
     * 融资申请-扣除销售合同额度
     *
     * @param financeApplyId 融资申请id（可以为空）
     * @param salesContractDetailList 销售合同列表数据
     */
    @Override
    public void subtractReceivableAmount(Long financeApplyId, List<SalesContractDTO> salesContractDetailList) {
        // 如果融资申请id不为空，说明不是首次提交
        if (Objects.nonNull(financeApplyId)) {
            // 将上次扣除的销售合同金额全部返还
            List<SalesContractDetail> salesContractDetailLists = salesContractDetailService.getByFinanceApplyId(financeApplyId);
            salesContractDetailLists.forEach(salesContractDetail -> {
                salesContractService.addFinancingAvailableAmount(salesContractDetail.getSaleContractId(), salesContractDetail.getAmount());
            });
        }
        // 再扣除新提交的金额
        salesContractDetailList.forEach(salesContractDetail -> {
            boolean result = salesContractService.subtractFinancingAvailableAmount(salesContractDetail.getSaleContractId(), salesContractDetail.getAmount());
            Assert.isTrue(result, "销售合同额度不足");
        });
    }


    /**
     * 保存销售合同明细
     *
     * @param financeApplyDTO 融资信息
     */
    @Override
    public void saveSaleContractDetail(FinanceApplyDTO financeApplyDTO) {
        Long financeApplyId = financeApplyDTO.getId();
        List<SalesContractDetail> salesContractDetails = financeApplyDTO.getSalesContractDetails();
        salesContractDetailService.removeByFinanceApplyId(financeApplyId);
        //应收账款质押使用明细保存
        saveReceiveDetails(salesContractDetails, financeApplyDTO);
        //保存使用明细
        salesContractDetailService.saveBatch(salesContractDetails);
    }

    @Override
    public List<SalesContractDetail> getByFinanceApplyId(List<Long> financeApplyId) {
        return salesContractDetailService.list(Wrappers.<SalesContractDetail>lambdaQuery()
                .in(SalesContractDetail::getFinanceApplyId, financeApplyId));
    }

    @Override
    public List<SalesContractDetail> getByFinanceApplyId(Long financeApplyId) {
        return salesContractDetailService.list(Wrappers.<SalesContractDetail>lambdaQuery()
                .eq(SalesContractDetail::getFinanceApplyId, financeApplyId));
    }

    @Override
    public List<SalesContractVO> queryByIds(List<Long> ids) {
        return salesContractService.queryByIds(ids);

    }

    /**
     * 根据ids查询销售合同列表
     *
     * @param ids 销售合同ids
     * @return 销售合同合同列表数据
     */
    @Override
    public List<SalesContract> selectListByIds(List<Long> ids) {
        return salesContractService.listByIds(ids);
    }

    /**
     * 更新销售合同数据
     *
     * @param list 销售合同列表数据
     * @return true成功
     */
    @Override
    public boolean updateBatchById(List<SalesContract> list) {
        return salesContractService.updateBatchById(list);
    }

    private void saveReceiveDetails(List<SalesContractDetail> salesContractDetails, FinanceApplyDTO financeApplyDTO) {
        Long capitalId = financeApplyDTO.getCapitalId();
        String processInstanceId = financeApplyDTO.getProcessInstanceId();
        String financeNo = financeApplyDTO.getFinanceNo();
        Long financeApplyId = financeApplyDTO.getId();
        salesContractDetails.forEach(salesContractDetail -> {
            salesContractDetail.setFinanceNo(financeNo);
            salesContractDetail.setFinanceApplyId(financeApplyId);
            salesContractDetail.setUseType(SaleContractDetailsEnum.USE_TYPE.IMPAWN.getStatus());
            salesContractDetail.setCapitalId(capitalId);
            salesContractDetail.setApplyProcessId(processInstanceId);
            salesContractDetail.setHolderId(capitalId);
            salesContractDetail.setId(null);
        });
    }
}
