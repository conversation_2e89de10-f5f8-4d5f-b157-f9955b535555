package org.springblade.customer.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.customer.dto.FinancingRoleDTO;
import org.springblade.customer.entity.FinancingRole;
import org.springblade.customer.vo.FinancingRoleVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 融资企业角色信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface FinancingRoleWrapper  {
    /**
      * 获取mapper对象
      * @return
      */
	static FinancingRoleWrapper build() {
		return Mappers.getMapper(FinancingRoleWrapper.class);
 	}

    /**
	 * 实体类转vo
	 * @param financingRole
	 * @return
	 */
	FinancingRoleVO entityVO(FinancingRole financingRole);
    /**
	 * 实体类转dto
	 * @param financingRole
	 * @return
	 */
    FinancingRoleDTO entityDTO(FinancingRole financingRole);
    /**
	 * 实体类List转VOList
	 * @param financingRoles
	 * @return
	 */
    List<FinancingRoleVO> listVO(List<FinancingRole> financingRoles);
    /**
	 * 实体类Page转VOPage
	 * @param page
	 * @return
	 */
    default Page<FinancingRoleVO> pageVO(IPage<FinancingRole> page) {
        List<FinancingRoleVO> financingRoleVOList = this.listVO(page.getRecords());
        Page<FinancingRoleVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(financingRoleVOList);
        pageVO.setPages(page.getPages());
        return pageVO;
    }

	default List<FinancingRoleVO> listNodeVO(List<FinancingRole> list) {
		List<FinancingRoleVO> collect = list.stream().map(this::entityVO).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}
}
