<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerResultMap" type="org.springblade.customer.entity.Customer">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="logo_src" property="logoSrc"/>
    <result column="last_visit_time" property="lastVisitTime"/>
        <result column="phone" property="phone"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
        <result column="email" property="email"/>
       <result column="type" property="type"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>



    <select id="selectCustomerPage" resultMap="customerResultMap">
        select * from jrzh_customer where is_deleted = 0
    </select>
    <select id="exportCustomer" resultType="org.springblade.customer.excel.CustomerExcel" >
        select account , ifnull(enterprise_name,"-----") enterprise_name , ifnull(customer_manager,'-----')customer_manager, phone , case is_white_list when 0 then '否' else '是' end  is_white_list  ,ifnull(score,"-----")score, case status when 0 then '未实名'  when 1 then '已实名' when 2 then '已认证'  end as status ,last_visit_time  from jrzh_customer  ${ew.customSqlSegment}
</select>


</mapper>
