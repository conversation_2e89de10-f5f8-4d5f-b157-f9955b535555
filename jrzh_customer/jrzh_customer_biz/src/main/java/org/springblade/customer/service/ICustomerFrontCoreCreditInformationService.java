/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.entity.CustomerFrontCoreCreditInformation;
import org.springblade.customer.vo.CustomerFrontCoreCreditInformationVO;

/**
 * 核心企业企业征信信息 服务类
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
public interface ICustomerFrontCoreCreditInformationService extends BaseService<CustomerFrontCoreCreditInformation> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param customerFrontCoreCreditInformation
	 * @return
	 */
	IPage<CustomerFrontCoreCreditInformationVO> selectCustomerFrontCoreCreditInformationPage(IPage<CustomerFrontCoreCreditInformationVO> page, CustomerFrontCoreCreditInformationVO customerFrontCoreCreditInformation);

}
