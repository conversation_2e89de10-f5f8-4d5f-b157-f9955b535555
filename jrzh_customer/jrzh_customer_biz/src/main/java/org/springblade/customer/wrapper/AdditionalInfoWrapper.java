package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.AdditionalInfo;
import org.springblade.customer.vo.AdditionalInfoVO;

import java.util.Objects;

/**
 * 补充资料包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
public class AdditionalInfoWrapper extends BaseEntityWrapper<AdditionalInfo, AdditionalInfoVO> {

    public static AdditionalInfoWrapper build() {
        return new AdditionalInfoWrapper();
    }

    @Override
    public AdditionalInfoVO entityVO(AdditionalInfo AdditionalInfo) {
        AdditionalInfoVO AdditionalInfoVO = Objects.requireNonNull(BeanUtil.copy(AdditionalInfo, AdditionalInfoVO.class));

        //User createUser = UserCache.getUser(AdditionalInfo.getCreateUser());
        //User updateUser = UserCache.getUser(AdditionalInfo.getUpdateUser());
        //AdditionalInfoVO.setCreateUserName(createUser.getName());
        //AdditionalInfoVO.setUpdateUserName(updateUser.getName());

        return AdditionalInfoVO;
    }
}
