package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.BankCard;
import org.springblade.customer.vo.BankCardVO;

import java.util.Objects;

/**
 * 添加账户包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
public class BankCardWrapper extends BaseEntityWrapper<BankCard, BankCardVO> {

    public static BankCardWrapper build() {
        return new BankCardWrapper();
    }

    @Override
    public BankCardVO entityVO(BankCard BankCard) {
        BankCardVO BankCardVO = Objects.requireNonNull(BeanUtil.copy(BankCard, BankCardVO.class));

        //User createUser = UserCache.getUser(BankCard.getCreateUser());
        //User updateUser = UserCache.getUser(BankCard.getUpdateUser());
        //BankCardVO.setCreateUserName(createUser.getName());
        //BankCardVO.setUpdateUserName(updateUser.getName());

        return BankCardVO;
    }
}
