<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.FrontCoreListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="frontCoreListResultMap" type="org.springblade.customer.entity.FrontCoreList">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="customer_white" property="customerWhite"/>
        <result column="enterprise_type" property="enterpriseType"/>
        <result column="credit_line" property="creditLine"/>
        <result column="father_company_id" property="fatherCompanyId"/>
        <result column="customer_score" property="customerScore"/>
        <result column="company_id" property="companyId"/>
        <result column="customer_code" property="customerCode"/>
        <result column="tenant_id" property="tenantId"></result>
        <result column="user_id" property="userId"></result>
    </resultMap>


    <select id="selectFrontCoreListPage" resultMap="frontCoreListResultMap">
        select * from jrzh_customer_front_core_list where is_deleted = 0
    </select>

    <select id="selectCoreist" resultType="org.springblade.customer.vo.FrontCoreListVO">
        SELECT
        dept.dept_name as name ,dept.logo_src as logoSrc,dept.id as userId,dept.id as companyId,dept.unified_social_code as unifiedSocialCode  ,dept.phone as phone  ,dept.contacts as realName,  (
        SELECT
        CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
        FROM
        blade_dept
        WHERE
        blade_dept.parent_id = dept.id and is_deleted = 0
        ) AS hasChildren,
        core.*,core.father_company_id as parentId
        FROM
        blade_dept dept   join  jrzh_customer_front_core_list  core    on  core.company_id=dept.id
        WHERE dept.is_deleted = 0 and core.is_deleted=0
          <if test="parentId!=null and parentId !=''">
              and core.father_company_id = #{parentId}
          </if>

    </select>
    <select id="queryCoreist" resultType="org.springblade.customer.vo.FrontCoreListVO">
        SELECT
            dept.dept_name as name ,dept.logo_src as logoSrc,dept.id as userId,dept.id as companyId,dept.unified_social_code as unifiedSocialCode  ,dept.phone as phone  ,dept.contacts as realName,  (
            SELECT
                CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
            FROM
                blade_dept
            WHERE
                blade_dept.parent_id = dept.id and is_deleted = 0
        ) AS hasChildren,
            core.*,core.father_company_id as parentId
        FROM
            blade_dept dept   join  jrzh_customer_front_core_list  core    on  core.company_id=dept.id
        WHERE dept.is_deleted = 0 and core.is_deleted=0

        order by customer_score desc
    </select>

</mapper>
