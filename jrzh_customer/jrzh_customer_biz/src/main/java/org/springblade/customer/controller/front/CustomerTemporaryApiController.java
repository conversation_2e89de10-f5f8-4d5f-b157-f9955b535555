/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.front;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.customer.service.ICustomerTemporaryService;
import org.springblade.customer.vo.CustomerTemporaryVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 存储企业名称临时表 控制器
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/customer/customerTemporary")
@Api(value = "存储企业名称临时表", tags = "存储企业名称临时表接口")
public class CustomerTemporaryApiController extends BladeController {

	private final ICustomerTemporaryService customerTemporaryService;


	@PostMapping("/addEnerpriseName")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "存储企业名称信息", notes = "传入 enterpriseName")
	public R<Boolean> addEnerpriseName(String enterpriseName) {
		return  R.status(customerTemporaryService.addEnerpriseName(enterpriseName));
	}

	@GetMapping("/getEnerpriseName")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取存储企业信息", notes = "传入 enterpriseName")
	public R<CustomerTemporaryVO> getEnerpriseName() {
		return  R.data(customerTemporaryService.getEnerpriseName());
	}


}
