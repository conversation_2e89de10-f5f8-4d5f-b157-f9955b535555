/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.common.enums.ApprovalStatusEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.AdditionalInfo;
import org.springblade.customer.entity.AdditionalInfoLog;
import org.springblade.customer.mapper.AdditionalInfoMapper;
import org.springblade.customer.service.IAdditionalInfoLogService;
import org.springblade.customer.service.IAdditionalInfoService;
import org.springblade.customer.vo.AdditionalInfoVO;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 补充资料 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
@AllArgsConstructor
public class AdditionalInfoServiceImpl extends BaseServiceImpl<AdditionalInfoMapper, AdditionalInfo> implements IAdditionalInfoService {
	private IAdditionalInfoLogService infoLogService;

	@Override
	public IPage<AdditionalInfoVO> selectAdditionalInfoPage(IPage<AdditionalInfoVO> page, AdditionalInfoVO additionalInfo) {
		return page.setRecords(baseMapper.selectAdditionalInfoPage(page, additionalInfo));
	}

	@Override
	public boolean submitAdditionalInfo(AdditionalInfo additionalInfo) {
		//进入审批状态
		additionalInfo.setStatus(ApprovalStatusEnum.APPROVING.getStatus());
		//保存审批后记录历史
		if (updateById(additionalInfo)) {
			AdditionalInfoLog log = Objects.requireNonNull(BeanUtil.copy(additionalInfo, AdditionalInfoLog.class));
			log.setAdditionalInfoId(additionalInfo.getId());
			log.setId(null);
			return infoLogService.save(log);
		}
		return false;
	}

	@Override
	public boolean approvedAdditionalInfo(AdditionalInfo additionalInfo) {
		//进入审批通过
		additionalInfo.setStatus(ApprovalStatusEnum.APPROVED.getStatus());
		//保存审批后记录历史
		if (updateById(additionalInfo)) {
			AdditionalInfoLog log = Objects.requireNonNull(BeanUtil.copy(additionalInfo, AdditionalInfoLog.class));
			log.setAdditionalInfoId(additionalInfo.getId());
			log.setId(null);
			return infoLogService.save(log);
		}
		return false;
	}

	@Override
	public boolean rejectAdditionalInfo(AdditionalInfo additionalInfo) {
		//进入审批驳回
		additionalInfo.setStatus(ApprovalStatusEnum.REJECT.getStatus());
		//保存审批后记录历史
		if (updateById(additionalInfo)) {
			AdditionalInfoLog log = Objects.requireNonNull(BeanUtil.copy(additionalInfo, AdditionalInfoLog.class));
			log.setAdditionalInfoId(additionalInfo.getId());
			log.setId(null);
			return infoLogService.save(log);
		}
		return false;
	}
}
