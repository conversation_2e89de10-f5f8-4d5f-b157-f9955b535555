/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.LiabilitiesLoan;
import org.springblade.customer.mapper.LiabilitiesLoanMapper;
import org.springblade.customer.service.ILiabilitiesLoanService;
import org.springblade.customer.vo.LiabilitiesLoanVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 房产抵押贷款 服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-08
 */
@Service
public class LiabilitiesLoanServiceImpl extends BaseServiceImpl<LiabilitiesLoanMapper, LiabilitiesLoan> implements ILiabilitiesLoanService {

	@Override
	public IPage<LiabilitiesLoanVO> selectLiabilitiesLoanPage(IPage<LiabilitiesLoanVO> page, LiabilitiesLoanVO liabilitiesLoan) {
		return page.setRecords(baseMapper.selectLiabilitiesLoanPage(page, liabilitiesLoan));
	}

	@Override
	public void addliabilitiesLoanList(Long companyId, List<LiabilitiesLoan> liabilitiesLoanList,Integer type) {
		if(CollectionUtils.isEmpty(liabilitiesLoanList)){
			return;
		}
		List<LiabilitiesLoan> collect = liabilitiesLoanList.stream().map(liabilitiesLoan -> {
			liabilitiesLoan.setCompanyId(companyId);
			liabilitiesLoan.setType(type);
			return liabilitiesLoan;
		}).collect(Collectors.toList());
		saveBatch(collect);
	}

	@Override
	public boolean checkLoanListComplete(List<LiabilitiesLoan> liabilitiesLoanList) {
		if(CollectionUtils.isEmpty(liabilitiesLoanList)){
			return  Boolean.FALSE;
		}
		AtomicReference<Boolean> isTrue= new AtomicReference<>(Boolean.TRUE);
		liabilitiesLoanList.parallelStream().forEach(liabilitiesLoan ->{
			Boolean aBoolean = checkLoanComplete(liabilitiesLoan);
			if(!aBoolean){
				isTrue.set(false);
			}
		});
		return isTrue.get();
	}


	public boolean checkLoanComplete(LiabilitiesLoan liabilitiesLoanList) {
		String mechanism = liabilitiesLoanList.getVarieties();
		BigDecimal balance = liabilitiesLoanList.getBalance();
		String conditionalGuaranty = liabilitiesLoanList.getConditionalGuaranty();
		String institution = liabilitiesLoanList.getInstitution();
		Integer term = liabilitiesLoanList.getTerm();

		if(Func.isEmpty(mechanism)||Func.isEmpty(balance)||Func.isEmpty(conditionalGuaranty)||Func.isEmpty(institution)||Func.isEmpty(term)){
			return  Boolean.FALSE;
		}
		return  Boolean.TRUE;
	}
}
