/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.CustomerBusinessInvoiceEnum;
import org.springblade.common.enums.ProcessProgressEnum;
import org.springblade.common.enums.ProcessStatusEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.dto.CustomerBusinessInvoiceManagerDTO;
import org.springblade.customer.dto.SalesContractDTO;
import org.springblade.customer.dto.SalesContractProcessDTO;
import org.springblade.customer.entity.*;
import org.springblade.customer.enums.TradeBackGroundEnum;
import org.springblade.customer.enums.TradeBackGroundEnum.CONFIRM_STATUS;
import org.springblade.customer.enums.TradeBackGroundEnum.PROOF_STATUS;
import org.springblade.customer.enums.TradeBackGroundEnum.SETTLE_STATUS;
import org.springblade.customer.mapper.SalesContractMapper;
import org.springblade.customer.mapper.TradeBackgroundMapper;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.*;
import org.springblade.customer.wrapper.ProcessSalesContractWrapper;
import org.springblade.customer.wrapper.SalesContractWrapper;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.resource.service.IParamService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.common.enums.CodeEnum.SALESCONTRACT_CODE;

/**
 * 销售合同 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Service
@AllArgsConstructor
public class SalesContractServiceImpl extends BaseServiceImpl<SalesContractMapper, SalesContract> implements ISalesContractService {
    private static final Integer RELATION = 2;
    private final TaskService taskService;
    private final IEnterpriseQuotaService quotaService;
    private final ICustomerBusinessInvoiceService customerBusinessInvoiceService;
    private final ICustomerGoodsTradeBackgroundService customerGoodsTradeBackgroundService;
    private final IBusinessProcessService businessProcessService;
    private final IProcessSalesContractService processSalesContractService;
    private final IAttachService attachService;
    private final TradeBackgroundMapper tradeBackgroundMapper;
    private final ISalesContractDetailService salesContractDetailService;
    private final ICustomerBusinessInvoiceService businessInvoiceService;
    private final IParamService paramService;

    private final RemoteUserService remoteUserService;
    private final ProductDirector productDirector;

    private final RemoteDeptSearchService remoteDeptSearchService;
    private final static String GOODS_ID = "goodsId";
    private final static String AUTO_PASS_KEY = "sale_confirm:auto";

    @Override
    public IPage<SalesContractVO> selectSalesContractPage(Query query, SalesContractDTO salesContractDTO, Long customerGoodsId) {
        List<CustomerGoodsTradeBackground> customerGoodsTradeBackgroundList = customerGoodsTradeBackgroundService.getByCustomerGoodsId(customerGoodsId);

        List<Long> tradeBackgroundIdList = StreamUtil.map(customerGoodsTradeBackgroundList, CustomerGoodsTradeBackground::getTradeBackgroundId);
        QueryWrapper<SalesContract> queryWrapper = org.springblade.common.utils.Condition.getQueryWrapper(salesContractDTO, SalesContract.class);
        if (!CollectionUtils.isEmpty(tradeBackgroundIdList)) {
            queryWrapper.in("back_id", tradeBackgroundIdList);
        }
        //确权、未过期并且可用额度大于0
        queryWrapper.eq("confirm_status", CONFIRM_STATUS.CONFIRM.getStatus());
        queryWrapper.gt("financing_available_amount", new BigDecimal("0"));
        queryWrapper.eq("settle", SETTLE_STATUS.UNSETTLE.getStatus());
        queryWrapper.eq("proof_status", PROOF_STATUS.USEABLE.getStatus());
        queryWrapper.ge("expire_time", LocalDate.now());
        IPage<SalesContract> page = baseMapper.selectPage(Condition.getPage(query), queryWrapper);
        if (page.getTotal() <= 0) {
            return Condition.getPage(query);
        }
        Map<Long, String> backMap = getBackMap(StreamUtil.map(page.getRecords(), SalesContract::getBackId));
        IPage<SalesContractVO> pageVO = SalesContractWrapper.build().pageVO(page);
        List<SalesContractVO> records = pageVO.getRecords();
        records = records.stream().peek(salesContract -> salesContract.setEnterpriseName(backMap.get(salesContract.getBackId()))).collect(Collectors.toList());
        pageVO.setRecords(records);
        return pageVO;
    }


    @Override
    public boolean saveSalesContract(SalesContract salesContract) {
        if (ObjectUtil.isEmpty(salesContract.getId())) {
            //生成唯一标识
            salesContract.setContractNo(CodeUtil.generateCode(CodeEnum.SALE_CONTRACT_NO));
            //默认待确权
            salesContract.setProofStatus(PROOF_STATUS.UN_CONFIRMED.getStatus());
            salesContract.setConfirmStatus(CONFIRM_STATUS.UN_CONFIRM.getStatus());
            salesContract.setSettle(SETTLE_STATUS.UNSETTLE.getStatus());
        }
        //若凭证上传 生成凭证编号
        if (ObjectUtil.isNotEmpty(salesContract.getProof())) {
            salesContract.setProofNo(CodeUtil.generateCode(CodeEnum.PROOF_NO));
        }
        return saveOrUpdate(salesContract);
    }

    @Override
    public boolean confirm(Long id) {
        return false;
    }

    @Override
    public SalesContractProcessLowerVO saleDetailsByProcessId(String processId) {
        //通过流程实例Id任务表中查找 保存当前taskid 和实例id
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processId).orderByTaskCreateTime().desc().list();
        SalesContractProcessLowerVO vo = new SalesContractProcessLowerVO();
        List<SalesContract> list = listByProcessId(processId);
        if (ObjectUtil.isNotEmpty(tasks)) {
            Task task = tasks.get(0);
            vo.setTaskId(task.getId());
        }
        vo.setProcessInstanceId(processId);
        //销售合同信息
        vo.setSalesContractList(list);
        return vo;
    }

    @Override
    public boolean bindLowerSalesContract(SalesContractProcessDTO salesContract, TradeBackground background) {
        //添加发票信息
        addInvoiceInfo(salesContract);
        User user = null;
        Long companyLowerId = background.getCompanyLowerId();
        if (ObjectUtil.isNotEmpty(companyLowerId)) {
            user = CustomerUserCache.getUserById(companyLowerId);
            //根据业务类型查找对应消息模板，根据模板发送消息
            //TODO 暂未引入消息
            //MessageNotifyUtil.notifyByTemplate(background.getCompanyLowerId(),MessageSceneEnum.MESSAGE_SCENE_Enum_ConfirmSubmit.getValue());
        }
        //流程变量
        JSONObject variables = setFlowVariables(background);
        if (ObjectUtil.isEmpty(salesContract.getId())) {
            return startBindLowerSalesContract(salesContract, background, variables, user);
        } else {
            return reCompete(salesContract, variables);
        }
    }

    private void addInvoiceInfo(SalesContractProcessDTO salesContract) {
        List<SalesContractVO> salesContractList = salesContract.getSalesContractList();
        //获取发票信息
        List<Long> invoiceIds = salesContractList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getInvoiceId())).map(SalesContract::getInvoiceId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(invoiceIds)) {
            Assert.isTrue(this.checkInvoice(invoiceIds), "页面上存在重复发票，请不要添加重复发票");
            //更新发票状态为使用中
            customerBusinessInvoiceService.changeUploadStatus(invoiceIds, CustomerBusinessInvoiceEnum.UPLOADING.getCode());
        }
    }

    /**
     * 查询是否发票是否已确权或重复提交
     *
     * @param invoiceIds
     * @return
     */
    private boolean checkInvoice(List<Long> invoiceIds) {
        Set<Long> ids = new HashSet(invoiceIds);
        return customerBusinessInvoiceService.uploadBefore(invoiceIds) && ids.size() == invoiceIds.size();
    }

    private boolean startBindLowerSalesContract(SalesContractProcessDTO salesContract, TradeBackground background, JSONObject variables, User user) {
        //保存合同流程
        //保存流程信息到中间表
        ProcessSalesContract processSalesContract = new ProcessSalesContract();
        processSalesContract.setBackId(background.getId());
        processSalesContract.setProcess(ProcessProgressEnum.APPLY_CONFIRMING.getCode());
        String uncheckLowerCoreCompanyName = background.getUncheckLowerCoreCompanyName();
        String coreName = ObjectUtil.isNotEmpty(uncheckLowerCoreCompanyName) ? uncheckLowerCoreCompanyName : user.getName();
        processSalesContract.setCoreName(coreName);
        //設置流程狀態為审批中
        processSalesContract.setStatus(ProcessStatusEnum.APPROVING.getCode());
        //申请编号
        String code = CodeUtil.generateCode(CodeEnum.PROCESS_NO);
        variables.put("processNo", code);
        processSalesContract.setProcessNo(code);
        processSalesContractService.save(processSalesContract);
        String autoPassKey = paramService.getValue(AUTO_PASS_KEY);
        Boolean autoPass = StringUtil.isNotBlank(autoPassKey) ? autoPassKey.contains(AuthUtil.getTenantId()) : Boolean.FALSE;
//		Boolean autoPass = Boolean.FALSE;
        //初始化销售合同
        List<SalesContract> contractList = initSaleContract(salesContract, processSalesContract, variables.getLong(GOODS_ID), autoPass);
        variables.put("salesFormData", salesContract);
        //开启流程并更新
        //startProcess(saleProcessId, variables);
        if (autoPass) {
            //扣取核心企業額度
            passSaleContractList(contractList, background.getId(), background.getCompanyLowerId());
        }
        //保存
        return saveBatch(contractList);
    }

    private void startProcess(Long saleProcessId, JSONObject variables) {
        // 发起流程
        String simpleProcessId = businessProcessService.startProcessByDefinitionKey(WfProcessConstant.PROCESS_LOWER_SALES_CONTRACT, variables);
        //将流程实例id保存到销售合同中
        update(Wrappers.<SalesContract>lambdaUpdate()
                .set(SalesContract::getProcessInstanceId, simpleProcessId)
                .eq(SalesContract::getProcessId, saleProcessId));
        //更新合同流程数据
        processSalesContractService.update(Wrappers.<ProcessSalesContract>lambdaUpdate()
                .set(ProcessSalesContract::getProcessId, simpleProcessId)
                .eq(ProcessSalesContract::getId, saleProcessId));
    }

    private JSONObject setFlowVariables(TradeBackground background) {
        JSONObject variables = new JSONObject();
        // 判断贸易背景是否关联
        Assert.isFalse(!RELATION.equals(background.getStatus()), "贸易背景未关联，请先关联");
        Long backId = background.getId();
        variables.put("backId", backId);
        //判断贸易背景是否存在于系统中并设置流程变量 true指派核心企业 false指派平台
        Long companyLowerId = background.getCompanyLowerId();
        if (ObjectUtil.isEmpty(background.getUncheckLowerCoreCompanyName())) {
            variables.put("isExsit", true);
            variables.put("coreEnterpriseId", companyLowerId.toString());
        } else {
            variables.put("isExsit", false);
        }
        //将关联产品信息放入流程变量 并后续附给所有应收账款
        CustomerGoodsTradeBackground goodsBackGround = customerGoodsTradeBackgroundService.getByBackId(backId);
        ProductVO productVO = productDirector.detail(goodsBackGround.getGoodsId());
        variables.put("goods", productVO);
        variables.put(GOODS_ID, goodsBackGround.getGoodsId());
//        GoodsVO goodsVO = goodsService.processGoodsInfo(goodsBackGround.getGoodsId());
//        variables.put("goods", goodsVO);
//        variables.put(GOODS_ID, goodsVO.getId());
        //确权总额
        variables.put("confirmAmount", BigDecimal.ZERO);
        return variables;
    }

    @Override
    public List<SalesContract> listByProcessId(String processId) {
        return list(new QueryWrapper<SalesContract>().lambda().eq(SalesContract::getProcessId, processId));

    }

    @Override
    public IPage<ProcessSalesContractVO> getSaleContractProcess(Query query, @RequestParam Map<String, Object> map) {
        //条件拼接
        LambdaQueryWrapper<ProcessSalesContract> wrapper = getQw(map);
        IPage<ProcessSalesContract> pages = processSalesContractService.page(Condition.getPage(query), wrapper);
        return ProcessSalesContractWrapper.build().page(pages);
    }

    private LambdaQueryWrapper<ProcessSalesContract> getQw(Map<String, Object> map) {
        LambdaQueryWrapper<ProcessSalesContract> wrapper = Wrappers.lambdaQuery();
        int status = Integer.parseInt(map.get("status").toString());
        if (map.containsKey("status") && status != 0) {
            if (status == 1) {
                wrapper.eq(ProcessSalesContract::getStatus, ProcessStatusEnum.REJECT.getCode()).or().eq(ProcessSalesContract::getStatus, ProcessStatusEnum.APPROVING.getCode());
            } else {
                wrapper.eq(ProcessSalesContract::getStatus, status);
            }
        }
        if (map.containsKey("start")) {
            wrapper.ge(ProcessSalesContract::getCreateTime, map.get("start"));
        }
        if (map.containsKey("end")) {
            wrapper.le(ProcessSalesContract::getCreateTime, map.get("end"));
        }
        if (map.containsKey("processNo")) {
            wrapper.like(ProcessSalesContract::getProcessNo, "%".concat(map.get("processNo").toString()).concat("%"));
        }
        if (map.containsKey("name")) {
            wrapper.like(ProcessSalesContract::getCoreName, "%".concat(map.get("name").toString()).concat("%"));
        }
        Long userId = Func.toLong(MyAuthUtil.getCompanyId());
        wrapper.eq(ProcessSalesContract::getCreateUser, ObjectUtil.isNotEmpty(userId) ? userId : CommonConstant.NO_EXIST);
        wrapper.orderByDesc(ProcessSalesContract::getCreateTime);
        return wrapper;
    }

    @Override
    public List<SalesContract> listContractByBackId(Long backId) {
        return list(Wrappers.<SalesContract>lambdaQuery().eq(SalesContract::getBackId, backId));
    }

    @Override
    public IPage<SalesContract> lowerSalesContractsByStatus(Long backId, Query query, String proofStatus, Integer settleStatus) {
        //构建confirmstatus和proofstatus多选查询条件 数量大于1时or拼接
        LambdaQueryWrapper<SalesContract> wrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(proofStatus)) {
            List<Integer> proofStatusList = Func.toIntList(proofStatus);
            wrapper.in(SalesContract::getProofStatus, proofStatusList);
        }
        if (ObjectUtil.isNotEmpty(settleStatus)) {
            wrapper.eq(SalesContract::getSettle, settleStatus);
        }
        return page(Condition.getPage(query), wrapper
                .eq(SalesContract::getBackId, backId)
                .orderByDesc(SalesContract::getCreateTime));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reCompete(SalesContractProcessDTO salesContract, JSONObject variables) {
        Long saleProcessId = salesContract.getId();
        ProcessSalesContract processSalesContract = processSalesContractService.getById(saleProcessId);
        String processId = processSalesContract.getProcessId();
        //判断该用户是否为发起者
        Assert.isTrue(businessProcessService.startByWith(processId), "非法操作,请查看属于自己的流程");
        Map<String, Object> detailVariables = businessProcessService.getVariables(processId);
        //通过合同id查询到所有合同 删除旧数据存入新数据
        baseMapper.realDelete(saleProcessId);
        //初始化销售合同
        List<SalesContract> salesContracts = initSaleContract(salesContract, processSalesContract, variables.getLong(GOODS_ID), false);
        updateBatchById(salesContracts);
        //保存流程变量
        detailVariables.putAll(variables);
        SalesContractProcessDTO formDto = (SalesContractProcessDTO) detailVariables.get("salesFormData");
        formDto = salesContract;
        detailVariables.put("salesFormData", formDto);
        detailVariables.put("wf_process_terminate", "unfinished");
        businessProcessService.completeTask(processId, detailVariables);
        return true;
    }

    @Override
    public R getProofInfo(SalesContract one) {
        return null;
    }

    @Override
    public boolean existContract(List<String> contractNoCollect, Long processId) {
        if (ObjectUtil.isNotEmpty(contractNoCollect)) {
            LambdaQueryWrapper<SalesContract> wrapper = Wrappers.<SalesContract>lambdaQuery()
                    .in(SalesContract::getProofNo, contractNoCollect).ne(SalesContract::getConfirmStatus, CONFIRM_STATUS.WAIT_CONFIRM.getStatus());
            if (ObjectUtil.isNotEmpty(processId)) {
                wrapper.ne(SalesContract::getConfirmStatus, CONFIRM_STATUS.UN_CONFIRM.getStatus()).eq(SalesContract::getCreateUser, Func.toLong(MyAuthUtil.getCompanyId()));
            }
            return count(wrapper) > 0;
        }
        return false;
    }

    @Override
    public List<SalesContract> getByProcessId(Long processId) {
        return list(Wrappers.<SalesContract>lambdaQuery().eq(SalesContract::getProcessId, processId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCascadeByBackId(List<Long> ids) {
        processSalesContractService.remove(Wrappers.<ProcessSalesContract>lambdaQuery().in(ProcessSalesContract::getBackId, ids));
        remove(Wrappers.<SalesContract>lambdaQuery().in(SalesContract::getBackId, ids));
        return true;
    }

    @Override
    public List<SalesContractVO> queryByIds(List<Long> ids) {
        List<SalesContract> salesContracts = listByIds(ids);
        if (CollectionUtils.isEmpty(salesContracts)) {
            return Collections.emptyList();
        }
        Map<Long, String> backMap = getBackMap(StreamUtil.map(salesContracts, SalesContract::getBackId));
        return salesContracts.stream().map(salesContract -> {
            SalesContractVO salesContractVO = SalesContractWrapper.build().entityVO(salesContract);
            salesContractVO.setEnterpriseName(backMap.get(salesContractVO.getBackId()));
            return salesContractVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SalesContract> listByHeightCompanyId(Long companyId) {
        return list(Wrappers.<SalesContract>lambdaQuery().eq(SalesContract::getCompanyId, companyId));
    }

    @Override
    public List<SalesContract> listByLowerCompanyId(Long companyId) {
        return list(Wrappers.<SalesContract>lambdaQuery().eq(SalesContract::getCompanyLowerId, companyId));
    }

    @Override
    public IPage<SalesContractAssetsVO> selectPageByCompanyId(IPage<SalesContractAssetsVO> page, SalePageParamVO params) {
        return page.setRecords(baseMapper.selectFrontPageByCompanyId(page, params, AuthUtil.getTenantId()));
    }

    @Override
    public IPage<SalesContractAssetsVO> selectBackPage(IPage<SalesContractAssetsVO> page, SalePageParamVO params) {
        return page.setRecords(baseMapper.selectBackPage(page, params, AuthUtil.getTenantId()));
    }

    @Override
    public IPage<SalesContractVO> selectCorePageByCompanyId(IPage<SalesContractVO> page, SalePageParamVO paramVO) {
        return page.setRecords(baseMapper.selectCorePageByCompanyId(page, paramVO, AuthUtil.getTenantId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoPassSaleContract(List<SalesContract> saleContract, Long coreId, Long goodsId, BigDecimal needSubtract, List<Long> invoiceIds) {
        //批量保存应收账款
        saveBatch(saleContract);
        //将发票改成已上传
        customerBusinessInvoiceService.changeUploadStatus(invoiceIds, CustomerBusinessInvoiceEnum.UPLOADED.getCode());
        //扣取额度
        if (needSubtract.compareTo(BigDecimal.ZERO) > 0) {
            quotaService.subtractQuota(goodsId, coreId, 2, needSubtract);
        }
    }

    @Override
    public Boolean operateSaleContract(Long id, Boolean confirmStatus, String companyIdStr) {
        SalesContract salesContract = getById(id);
        Long companyId = Func.toLong(companyIdStr);
        if (ObjectUtil.isEmpty(salesContract)) {
            throw new ServiceException("该应收账款不存在");
        }
        if (!salesContract.getCompanyLowerId().equals(companyId)) {
            throw new ServiceException("该应收账款不属于您");
        }
        if (CONFIRM_STATUS.CONFIRM.getStatus().equals(salesContract.getConfirmStatus())) {
            throw new ServiceException("该应收账款已确权");
        }
        if (confirmStatus) {
            //根据业务类型查找对应消息模板，根据模板发送消息
            //TODO 暂未引入消息
            //MessageNotifyUtil.notifyByTemplate(salesContract.getCompanyId(),MessageSceneEnum.MESSAGE_SCENE_ENUM_ConfirmSuc.getValue());
            return passSaleContract(salesContract);
        }
        //根据业务类型查找对应消息模板，根据模板发送消息
        //MessageNotifyUtil.notifyByTemplate(salesContract.getCompanyId(),MessageSceneEnum.MESSAGE_SCENE_ENUM_ConfirmFail.getValue());
        return rejectSaleContract(salesContract);
    }


    @Override
    public void addUsedAmount(Long financeApplyId) {
        // 根据融资申请id查询销售合同使用明细
        ISalesContractDetailService salesContractDetailService = SpringUtil.getBean(ISalesContractDetailService.class);
        List<SalesContractDetail> salesContractDetailList = salesContractDetailService.getByFinanceApplyId(financeApplyId);
        if (CollectionUtils.isEmpty(salesContractDetailList)) {
            return;
        }
        List<Long> saleContractIds = StreamUtil.map(salesContractDetailList, SalesContractDetail::getSaleContractId);
        // 查询融资申请使用的销售合同
        Map<Long, SalesContract> salesContractMap = StreamUtil.toMap(listByIds(saleContractIds), SalesContract::getId, obj -> obj);
        for (SalesContractDetail salesContractDetail : salesContractDetailList) {
            SalesContract salesContract = salesContractMap.get(salesContractDetail.getSaleContractId());
            BigDecimal financingApplyAmount = salesContract.getFinancingApplyAmount();
            if (ObjectUtil.isEmpty(financingApplyAmount)) {
                financingApplyAmount = BigDecimal.ZERO;
            }
            salesContract.setFinancingApplyAmount(financingApplyAmount.subtract(salesContractDetail.getAmount()));
            if (ObjectUtil.isEmpty(salesContract.getFinancingUsedAmount())) {
                salesContract.setFinancingUsedAmount(BigDecimal.ZERO);
            }
            salesContract.setFinancingUsedAmount(salesContract.getFinancingUsedAmount().add(salesContractDetail.getAmount()));
            if (salesContract.getFinancingAvailableAmount().compareTo(salesContract.getFinancingApplyAmount()) == 0 &&
                    salesContract.getFinancingAvailableAmount().compareTo(BigDecimal.ZERO) == 0) {
                salesContract.setStatus(PROOF_STATUS.USED.getStatus());
            }
            updateById(salesContract);
        }
    }

    @Override
    public void addFinancingAvailableAmount(Long id, BigDecimal amount) {
        String sql = "financing_available_amount = financing_available_amount + " + amount.toString()
                + ",financing_apply_amount = financing_apply_amount - " + amount;
        lambdaUpdate().eq(SalesContract::getId, id)
                .setSql(sql)
                .update();
    }

    @Override
    public boolean subtractFinancingAvailableAmount(Long saleContractId, BigDecimal amount) {
        String sql = "financing_available_amount = financing_available_amount - " + amount.toString()
                + ",financing_apply_amount = financing_apply_amount + " + amount;
        return lambdaUpdate().eq(SalesContract::getId, saleContractId)
                .setSql(sql)
                .update();
    }

    //确权多份合同
    public Boolean passSaleContractList(List<SalesContract> salesContracts, Long backId, Long companyId) {
        //获取打折率
        CustomerGoodsTradeBackground goodsBackGround = customerGoodsTradeBackgroundService.getByBackId(backId);
        Long goodsId = goodsBackGround.getGoodsId();
        EnterpriseQuotaVO quota = quotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, 2, companyId);
        BigDecimal discount = quota.getLoanable();
        //扣取额度
        BigDecimal subAmount = getSubAmount(salesContracts);
        if (subAmount.compareTo(BigDecimal.ZERO) > 0) {
            quotaService.subtractQuota(goodsId, companyId, 2, subAmount);
        }
        for (SalesContract salesContract : salesContracts) {
            //通过处理
            handlePass(salesContract, discount);
        }
        return true;
    }

    private void handlePass(SalesContract salesContract, BigDecimal discount) {
        salesContract.setLoanable(discount);
        //判断状态是否确权成功 确权成功将凭证状态改为可使用
        if (CONFIRM_STATUS.CONFIRM.getStatus().equals(salesContract.getConfirmStatus())) {
            //设置可融资额度=有效金额*产品打折率
            salesContract.setFinancingAvailableAmount(salesContract.getAccountAmount().multiply(discount.divide(new BigDecimal(100))).setScale(2, CommonConstant.NUMBER_STRATEGY));
            salesContract.setProofStatus(PROOF_STATUS.USEABLE.getStatus());
            salesContract.setConfirmStatus(CONFIRM_STATUS.CONFIRM.getStatus());
            salesContract.setRefund(BigDecimal.ZERO);
        } else {
            //未勾选操作的也确权失败
            salesContract.setConfirmStatus(CONFIRM_STATUS.WAIT_CONFIRM.getStatus());
            salesContract.setProofStatus(PROOF_STATUS.INVALID.getStatus());
        }
    }

    private BigDecimal getSubAmount(List<SalesContract> salesContracts) {
        if (CollectionUtil.isEmpty(salesContracts)) {
            return BigDecimal.ZERO;
        }
        List<SalesContract> collect = salesContracts.stream().filter(e ->
                CONFIRM_STATUS.CONFIRM.getStatus().equals(e.getConfirmStatus())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return BigDecimal.ZERO;
        }
        return collect.stream().map(SalesContract::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public AssertFrontDetailVO detailsCoreSaleContract(Long id) {
        SalesContract sales = getById(id);
        if (ObjectUtil.isNotEmpty(sales)) {
            ProcessSalesContract processSalesContract = processSalesContractService.getByProcessId(sales.getProcessInstanceId());
            AssertFrontDetailVO detailVO = BeanUtil.copy(sales, AssertFrontDetailVO.class);
            TradeBackground background = tradeBackgroundMapper.selectById(sales.getBackId());
            detailVO.setEnterpriseName(org.springblade.core.tool.utils.ObjectUtil.isNotEmpty(background) ? background.getCompanyHeightName() : null);
            detailVO.setComment(processSalesContract.getComment());
            //确权则展示使用记录与付款记录
            if (TradeBackGroundEnum.CONFIRM_STATUS.CONFIRM.getStatus().equals(sales.getConfirmStatus())) {
                //设置使用记录
                detailVO.setUseInfo(salesContractDetailService.listVOBySalesId(id));
                //设置付款记录
                ISalesContractRefundDetailService salesContractRefundDetailService = SpringUtil.getBean(ISalesContractRefundDetailService.class);
                detailVO.setReceiveInfo(salesContractRefundDetailService.listBySaleContractVoId(id));
            }
            return detailVO;
        }
        return null;
    }

    @Override
    public void batchUploadInvoice(String companyId, Long operatorId, CustomerBusinessInvoiceManagerDTO invoiceManager) {
        Long coreId = Func.toLong(companyId);
        Long backId = invoiceManager.getBackId();
        TradeBackground background = tradeBackgroundMapper.selectById(backId);
        if (org.springblade.core.tool.utils.ObjectUtil.isEmpty(background)) {
            throw new ServiceException("该贸易背景不存在");
        }
        String companyHeightCode = background.getCompanyHeightCode();
        List<CustomerBusinessInvoice> invoiceList = businessInvoiceService.listUnUploadByCompanyIdAndSellerId(companyId, companyHeightCode);
        EnterpriseQuotaVO quotaVO = quotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(invoiceManager.getGoodsId(), 2, coreId);
        //发票校验 并返回需要扣减的金额
        BigDecimal needSubtract = checkParamAndReturnSubtract(invoiceList, quotaVO);
        //创建应收账款集合
        List<SalesContract> saleContract = createSaleContract(invoiceManager, invoiceList, background, quotaVO, operatorId);
        //获取发票ids
        List<Long> invoiceIds = StreamUtil.map(invoiceList, CustomerBusinessInvoice::getId);
        //扣减额度
        autoPassSaleContract(saleContract, coreId, invoiceManager.getGoodsId(), needSubtract, invoiceIds);
    }

    @Override
    public IPage<SalesContractVO> selectSalesContractAppletPage(Query query, SalesContractDTO salesContractDTO, Long customerGoodsId, Integer sortType) {
        List<CustomerGoodsTradeBackground> customerGoodsTradeBackgroundList = customerGoodsTradeBackgroundService.getByCustomerGoodsId(customerGoodsId);

        List<Long> tradeBackgroundIdList = StreamUtil.map(customerGoodsTradeBackgroundList, CustomerGoodsTradeBackground::getTradeBackgroundId);
        QueryWrapper<SalesContract> queryWrapper = org.springblade.common.utils.Condition.getQueryWrapper(salesContractDTO, SalesContract.class);
        if (!CollectionUtils.isEmpty(tradeBackgroundIdList)) {
            queryWrapper.in("back_id", tradeBackgroundIdList);
        }
        //确权、未过期并且可用额度大于0
        queryWrapper.eq("confirm_status", CONFIRM_STATUS.CONFIRM.getStatus());
        queryWrapper.gt("financing_available_amount", new BigDecimal("0"));
        queryWrapper.eq("settle", SETTLE_STATUS.UNSETTLE.getStatus());
        queryWrapper.eq("proof_status", PROOF_STATUS.USEABLE.getStatus());
        if (sortType == null || sortType == 1) {
            queryWrapper.orderByAsc("create_time");
        } else {
            queryWrapper.orderByDesc("create_time");
        }
        IPage<SalesContract> page = baseMapper.selectPage(Condition.getPage(query), queryWrapper);
        if (page.getTotal() <= 0) {
            return Condition.getPage(query);
        }
        Map<Long, String> backMap = getBackMap(StreamUtil.map(page.getRecords(), SalesContract::getBackId));
        IPage<SalesContractVO> pageVO = SalesContractWrapper.build().pageVO(page);
        List<SalesContractVO> records = pageVO.getRecords();
        records = records.stream().peek(salesContract -> salesContract.setEnterpriseName(backMap.get(salesContract.getBackId()))).collect(Collectors.toList());
        pageVO.setRecords(records);
        return pageVO;
    }

    @Override
    public IPage<SalesContractVO> getReceivable(Long companyId, Query query) {
        List<CustomerGoodsTradeBackground> list = cn.hutool.extra.spring.SpringUtil.getBean(ICustomerGoodsTradeBackgroundService.class).lambdaQuery()
                .eq(CustomerGoodsTradeBackground::getCapitalId, companyId).list();
        List<Long> map = StreamUtil.map(list, CustomerGoodsTradeBackground::getTradeBackgroundId);
        if (cn.hutool.core.util.ObjectUtil.isEmpty(map)) {
            return org.springblade.common.utils.Condition.getPage(query);
        }
        IPage<SalesContract> page = cn.hutool.extra.spring.SpringUtil.getBean(ISalesContractService.class).lambdaQuery().in(SalesContract::getBackId, map).page(org.springblade.common.utils.Condition.getPage(query));

        IPage<SalesContractVO> salesContractVOPage = SalesContractWrapper.build().pageVO(page);
        List<SalesContractVO> records = salesContractVOPage.getRecords();

        List<Long> companyLowerIds = StreamUtil.map(records, SalesContract::getCompanyLowerId);

        if (cn.hutool.core.util.ObjectUtil.isNotEmpty(companyLowerIds)) {
            Map<Long, User> mapInId = remoteUserService.getMapInId(companyLowerIds).getData();
            List<SalesContractVO> collect = records.stream().map(record -> {
                User user = mapInId.get(record.getCompanyLowerId());
                if (cn.hutool.core.util.ObjectUtil.isNotEmpty(user)) {
                    record.setPaymentEmp(user.getName());
                }
                return record;
            }).collect(Collectors.toList());
            salesContractVOPage.setRecords(collect);
        }


        return salesContractVOPage;
    }

    /**
     * 创建应收账款
     *
     * @param invoiceManager 发票分组管理信息
     * @param invoiceList    发票集合
     * @param background     贸易背景
     * @param quotaVO        产品额度信息
     * @param operatorId     操作人id
     */
    private List<SalesContract> createSaleContract(CustomerBusinessInvoiceManagerDTO invoiceManager, List<CustomerBusinessInvoice> invoiceList, TradeBackground background, EnterpriseQuotaVO quotaVO, Long operatorId) {
        Long backId = invoiceManager.getBackId();
        Integer gracePeriod = invoiceManager.getGracePeriod();
        BigDecimal paymentDays = invoiceManager.getPaymentDays();
        Long goodsId = invoiceManager.getGoodsId();
        BigDecimal loanable = quotaVO.getLoanable();
        List<SalesContract> salesContracts = new ArrayList<>();
        //获取发票附件 k:url v:attachId
        List<String> stringList = invoiceList.stream().filter(e -> org.springblade.core.tool.utils.ObjectUtil.isNotEmpty(e.getProof()))
                .map(CustomerBusinessInvoice::getProof).distinct().collect(Collectors.toList());
        Map<String, String> attachLinkMap = CollectionUtil.isNotEmpty(stringList)
                ? attachService.listByLinks(stringList)
                .stream().collect(Collectors.toMap(Attach::getLink, e -> Func.toStr(e.getId())))
                : new HashMap<>();
        //设置应收账款
        for (CustomerBusinessInvoice invoice : invoiceList) {
            //开票日
            String issueDate = invoice.getIssueDate();
            LocalDate startTime = LocalDate.parse(issueDate, DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            LocalDate expireTime = startTime.plusDays(paymentDays.longValue());
            SalesContract salesContract = new SalesContract();
            salesContract.setOperatorId(operatorId);
            salesContract.setProofNo(invoice.getCode() + invoice.getNumber());
            salesContract.setContractNo(CodeUtil.generateCode(SALESCONTRACT_CODE));
            salesContract.setInvoiceId(invoice.getId());
            salesContract.setGracePeriod(gracePeriod);
            salesContract.setPaymentDays(paymentDays);
            salesContract.setBackId(backId);
            salesContract.setLoanable(loanable);
            salesContract.setProofStatus(TradeBackGroundEnum.PROOF_STATUS.USEABLE.getStatus());
            salesContract.setConfirmStatus(TradeBackGroundEnum.CONFIRM_STATUS.CONFIRM.getStatus());
            salesContract.setStartTime(startTime);
            salesContract.setExpireTime(expireTime);
            salesContract.setProofType(1);
            salesContract.setProof(attachLinkMap.getOrDefault(invoice.getProof(), "-1"));
            salesContract.setCompanyId(background.getCompanyHeightId());
            salesContract.setCompanyLowerId(background.getCompanyLowerId());
            salesContract.setGoodsId(goodsId);
            BigDecimal subtotalDealAmount = invoice.getSubtotalDealAmount();
            BigDecimal dealTotal = invoice.getDealTotal();
            salesContract.setOrderAmount(dealTotal);
            salesContract.setAccountAmount(subtotalDealAmount);
            salesContract.setFinancingApplyAmount(BigDecimal.ZERO);
            salesContract.setFinancingUsedAmount(BigDecimal.ZERO);
            salesContract.setEffectiveAmount(dealTotal);
            salesContract.setFinancingAvailableAmount(dealTotal.multiply(loanable.divide(BigDecimal.valueOf(100))).setScale(2, CommonConstant.NUMBER_STRATEGY));
            salesContracts.add(salesContract);
        }
        return salesContracts;
    }

    /**
     * 参数检查
     *
     * @param list
     * @param quotaVO
     * @return
     */
    private BigDecimal checkParamAndReturnSubtract(List<CustomerBusinessInvoice> list, EnterpriseQuotaVO quotaVO) {
        if (CollectionUtil.isEmpty(list)) {
            throw new ServiceException("不存在需要上传的发票");
        }
        //存在未验真的发票
        boolean hasUnVerify = list.stream().anyMatch(e -> !e.getIsVertify().equals(1));
        if (hasUnVerify) {
            throw new ServiceException("存在未校验成功的发票，请检查");
        }
        BigDecimal totalAmount = list.stream().map(CustomerBusinessInvoice::getSubtotalDealAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (quotaVO.getAvailableAmount().compareTo(totalAmount) < 0) {
            throw new ServiceException("可用额度不足，请删除一些发票，或申请更多的可用额度");
        }
        return totalAmount;
    }

    /**
     * 确权通过
     *
     * @param salesContract
     * @return
     */
    private Boolean passSaleContract(SalesContract salesContract) {
        //获取打折率
        CustomerGoodsTradeBackground goodsBackGround = customerGoodsTradeBackgroundService.getByBackId(salesContract.getBackId());
        Long customerId = salesContract.getCompanyLowerId();
        Long goodsId = goodsBackGround.getGoodsId();
        EnterpriseQuotaVO quota = quotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, 2, customerId);
        BigDecimal discount = quota.getLoanable();
        //设置可融资额度=有效金额*产品打折率
        BigDecimal accountAmount = salesContract.getAccountAmount();
        salesContract.setFinancingAvailableAmount(accountAmount.multiply(discount.divide(new BigDecimal(100))).setScale(2, CommonConstant.NUMBER_STRATEGY));
        salesContract.setRefund(BigDecimal.ZERO);
        salesContract.setFinancingApplyAmount(BigDecimal.ZERO);
        salesContract.setFinancingUsedAmount(BigDecimal.ZERO);
        salesContract.setProofStatus(PROOF_STATUS.USEABLE.getStatus());
        salesContract.setConfirmStatus(CONFIRM_STATUS.CONFIRM.getStatus());
        salesContract.setLoanable(discount);
        if (accountAmount.compareTo(BigDecimal.ZERO) > 0) {
            quotaService.subtractQuota(goodsId, customerId, 2, accountAmount);
        }
        return updateById(salesContract);
    }

    /**
     * 拒绝确权
     *
     * @param salesContract
     * @return
     */
    private Boolean rejectSaleContract(SalesContract salesContract) {
        //设置业务系统 结点为填用状态
        salesContract.setConfirmStatus(CONFIRM_STATUS.WAIT_CONFIRM.getStatus());
        salesContract.setProofStatus(PROOF_STATUS.INVALID.getStatus());
        //发票作废
        Long invoiceId = salesContract.getInvoiceId();
        if (ObjectUtil.isNotEmpty(invoiceId)) {
            customerBusinessInvoiceService.changeUploadStatus(Collections.singletonList(invoiceId), CustomerBusinessInvoiceEnum.UN_UPLOAD.getCode());
        }
        return updateById(salesContract);
    }

    private Map<Long, String> getBackMap(List<Long> backIds) {
        List<TradeBackground> tradeBackgrounds = tradeBackgroundMapper.selectBatchIds(backIds);
        Map<Long, Long> backMap = StreamUtil.toMap(tradeBackgrounds, TradeBackground::getId, TradeBackground::getCompanyLowerId);
        Map<Long, User> userMap = remoteUserService.getMapInId(StreamUtil.map(tradeBackgrounds, TradeBackground::getCompanyLowerId)).getData();
        //userService.getMapInId(StreamUtil.map(tradeBackgrounds, TradeBackground::getCompanyLowerId));
        Map<Long, String> map = Maps.newHashMap();
        for (Long backId : backIds) {
            if (backMap.containsKey(backId)) {
                User user = userMap.get(backMap.get(backId));
                if (user == null) {
                    continue;
                }
                map.put(backId, user.getName());
            }
        }
        return map;
    }

    /**
     * @param salesContract
     * @param processSalesContract
     * @param goodsId              绑定的产品id
     */
    private List<SalesContract> initSaleContract(SalesContractProcessDTO salesContract, ProcessSalesContract processSalesContract, Long goodsId, Boolean autoPass) {
        List<SalesContractVO> salesContractList = salesContract.getSalesContractList();
        Long backId = processSalesContract.getBackId();
        ITradeBackgroundService backgroundService = SpringUtil.getBean(ITradeBackgroundService.class);
        TradeBackground tradeBackground = backgroundService.getById(backId);

        Long processSalesId = processSalesContract.getId();
        salesContract.setId(processSalesId);
        //流程实例id
        String processId = processSalesContract.getProcessId();
        List<SalesContract> contractList = Objects.requireNonNull(BeanUtil.copy(salesContractList, SalesContract.class));
        for (SalesContract e : contractList) {
            e.setOperatorId(salesContract.getOperatorId());
            if (ObjectUtil.isNotEmpty(processId)) {
                e.setProcessInstanceId(processId);
            }
            //设置为 待确权
            e.setGoodsId(goodsId);
            //設置直接通過
            if (autoPass) {
                e.setConfirmStatus(CONFIRM_STATUS.CONFIRM.getStatus());
                e.setProofStatus(PROOF_STATUS.USEABLE.getStatus());
                e.setSettle(SETTLE_STATUS.UNSETTLE.getStatus());
            } else {
                e.setConfirmStatus(CONFIRM_STATUS.UN_CONFIRM.getStatus());
                e.setProofStatus(PROOF_STATUS.UN_CONFIRMED.getStatus());
                e.setSettle(SETTLE_STATUS.UNSETTLE.getStatus());
            }
            e.setFinancingUsedAmount(BigDecimal.ZERO);
            e.setFinancingApplyAmount(BigDecimal.ZERO);
            e.setBackId(backId);
            e.setProcessId(processSalesId);
            e.setCompanyId(tradeBackground.getCompanyHeightId());
            e.setCompanyLowerId(tradeBackground.getCompanyLowerId());
            if (ObjectUtil.isEmpty(e.getId())) {
                //设置资产编号
                String code = CodeUtil.generateCode(CodeEnum.SALESCONTRACT_CODE);
                e.setContractNo(code);
            }
        }
        salesContractList = SalesContractWrapper.build().listVO(contractList);
        //获取发票信息
        List<Long> invoiceIds = salesContractList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getInvoiceId())).map(SalesContract::getInvoiceId).collect(Collectors.toList());
        Map<Long, CustomerBusinessInvoice> invoiceMap = new HashMap<>(salesContractList.size());
        //获取附件信息
        List<String> attachIds = salesContractList.stream().map(SalesContract::getProof).collect(Collectors.toList());
        Map<Long, Attach> attachMap = attachService.listByIds(attachIds).stream().collect(Collectors.toMap(Attach::getId, e -> e));
        if (CollectionUtil.isNotEmpty(invoiceIds)) {
            invoiceMap = getInvoiceByIds(invoiceIds);
        }
        for (SalesContractVO e : salesContractList) {
            Long invoiceId = e.getInvoiceId();
            if (ObjectUtil.isNotEmpty(invoiceId)) {
                e.setCustomerBusinessInvoice(invoiceMap.get(invoiceId));
            }
            e.setAttach(attachMap.get(Func.toLong(e.getProof())));
        }
        salesContract.setSalesContractList(salesContractList);
        return contractList;
    }

    private Map<Long, CustomerBusinessInvoice> getInvoiceByIds(List<Long> ids) {
        return customerBusinessInvoiceService.listByIds(ids).stream().collect(Collectors.toMap(CustomerBusinessInvoice::getId, e -> e));
    }
}
