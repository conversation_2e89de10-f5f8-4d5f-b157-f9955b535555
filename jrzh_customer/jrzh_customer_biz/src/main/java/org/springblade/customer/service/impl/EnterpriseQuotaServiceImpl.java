/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springblade.bank.entity.Bank;
import org.springblade.bank.service.IBankService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.lock.LockType;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.constant.QuotaHistoryEnum;
import org.springblade.customer.dto.CorporateAccountDTO;
import org.springblade.customer.dto.EnterpriseQuotaDTO;
import org.springblade.customer.entity.*;
import org.springblade.customer.enums.TradeBackGroundEnum;
import org.springblade.customer.excel.EnterpriseQuotaExcel;
import org.springblade.customer.mapper.EnterpriseQuotaMapper;
import org.springblade.customer.mapper.QuotaHistoryMapper;
import org.springblade.customer.mapper.SalesContractMapper;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerGoodsVO;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.customer.vo.QuotaUseVo;
import org.springblade.customer.wrapper.EnterpriseQuotaWrapper;
import org.springblade.loan.service.ILoanManageOverdueService;
import org.springblade.modules.contract.service.IContractService;
import org.springblade.othersapi.riskorderapi.dto.FinalApproveAmount;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.process.dto.ReceiveUnfreezeApplyDTO;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.product.common.entity.GoodsProcess;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.product.process_relation.service.IGoodsProcessService;
import org.springblade.riskmana.core.service.IRiskmanaApplyService;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 核心企业额度 服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Service
@RequiredArgsConstructor
public class EnterpriseQuotaServiceImpl extends BaseServiceImpl<EnterpriseQuotaMapper, EnterpriseQuota> implements IEnterpriseQuotaService {

    // 还款列表 服务类
    private final ILoanManageOverdueService loanManageOverdueService;

    private final IFrontCoreListService frontCoreListService;

    private final IGoodsProcessService goodsProcessService;
    private final IBusinessProcessService businessProcessService;
    private final RemoteDeptSearchService remoteDeptSearchService;
    private final ICusCapitalQuotaService cusCapitalQuotaService;
    private final RemoteUserService remoteUserService;

    private final IBusinessProcessProgressService businessProcessProgressService;
    private final RedisLockClient redisLockClient;
    private final QuotaHistoryMapper quotaHistoryMapper;
    private final SalesContractMapper salesContractMapper;
    private final IQuotaHistoryService quotaHistoryService;
    private final IBankCardService bankCardService;
    private final IBankService bankService;

    private final ICustomerGoodsService customerGoodsService;
    private final ProductDirector productDirector;
    private final IContractService contractService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean applyQuota(EnterpriseQuotaDTO enterpriseQuota) {
        User user = remoteUserService.getUserById(enterpriseQuota.getEnterpriseId(), FeignConstants.FROM_IN).getData();
        Assert.notNull(user, "企业不存在");
        // 检查是否申请过额度
        checkIsApply(enterpriseQuota);
        // 预留资方额度
        cusCapitalQuotaService.capitalSubtract(enterpriseQuota.getCapitalId(), CommonUtil.wanToYuan(enterpriseQuota.getCreditAmount()));
        // 生成任务编号
        Map<String, Object> variables = Maps.newHashMap();

        String value = CodeUtil.generateCode(CodeEnum.PROCESS_NO);
        variables.put(WfProcessConstant.PROCESS_NO, value);
        enterpriseQuota.setTaskNo(value);
        Long customerGoodsId = 0L;
        //TODO 编写方式需要调整
//		if (GoodsEnum.CLOUD_CREDIT.getCode().equals(enterpriseQuota.getProductType())) {
//			customerGoodsId = customerGoodsService.saveCustomerCloudProduct(cloudProductService.getById(enterpriseQuota.getGoodsId())
//				, enterpriseQuota.getEnterpriseId(), enterpriseQuota.getEnterpriseType());
//		} else {
//			customerGoodsId = customerGoodsService.saveCustomerGoods(goodsMapper.selectById(enterpriseQuota.getGoodsId())
//				, enterpriseQuota.getEnterpriseId(), enterpriseQuota.getEnterpriseType());
//		}
        variables.put(ProcessConstant.PRODUCT_TYPE, enterpriseQuota.getProductType());
        variables.put(ProcessConstant.CORE_ENTERPRISE_QUOTA, enterpriseQuota);
        getVariables(enterpriseQuota, variables);
        variables.put(ProcessConstant.CUSTOMER_GOODS_ID, customerGoodsId);
        Integer processType = ProcessTypeEnum.CORE_APPLY_QUOTA.getCode();
        String processKey = goodsProcessService.selectProcessKey(enterpriseQuota.getGoodsId(), processType);

        String processInstanceId = businessProcessService.startProcess(processKey, variables);

        //TODO
        //保存申请管理
//		businessProcessProgressService.saveBusinessProcessProgressUnContainInvalid(enterpriseQuota.getGoodsId(),
//			ProcessProgressEnum.CORE_APPLY_QUOTA_APPROVE.getCode(),
//			ProcessTypeEnum.CORE_QUOTA_APPLY_MANAGE.getCode(), null, CommonConstant.NO, enterpriseQuota.getEnterpriseId());
//		//保存核心企业额度申请
//		businessProcessProgressService.saveBusinessProcessProgressUnContainInvalid(enterpriseQuota.getGoodsId(),
//			ProcessProgressEnum.CORE_APPLY_QUOTA_APPROVE.getCode(), processType, processInstanceId, CommonConstant.NO, enterpriseQuota.getEnterpriseId());
//

        return true;
    }

    @Override
    public List<EnterpriseQuota> listValidQuota(Long userId, Integer enterpriseType) {
        List<EnterpriseQuota> enterpriseQuotas = baseMapper.selectList(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getEnterpriseType, enterpriseType)
                .eq(EnterpriseQuota::getEnterpriseId, userId)
                .eq(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.VALID.getCode()));
        if (Objects.isNull(enterpriseQuotas)) {
            return Collections.emptyList();
        }
        return enterpriseQuotas;
    }

    private void getVariables(EnterpriseQuota enterpriseQuota, Map<String, Object> variables) {
        variables.put(ProcessConstant.PROCESS_TYPE, ProcessTypeEnum.CORE_APPLY_QUOTA.getCode());
        variables.put(ProcessConstant.BUSINESS_ID, enterpriseQuota.getGoodsId());
        variables.put(ProcessConstant.CUSTOMER_ID, MyAuthUtil.getCustomerId());
        variables.put(ProcessConstant.USER_ID, enterpriseQuota.getEnterpriseId());
        variables.put(ProcessConstant.ENTERPRISE_TYPE, enterpriseQuota.getEnterpriseType());
        variables.put(ProcessConstant.PRODUCT_TYPE, enterpriseQuota.getProductType());
    }

    private void checkIsApply(EnterpriseQuota enterpriseQuota) {
        Integer count = baseMapper.selectCount(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getEnterpriseId, enterpriseQuota.getEnterpriseId())
                .eq(EnterpriseQuota::getGoodsId, enterpriseQuota.getGoodsId()));

        Assert.isTrue(count <= 0, "该企业已申请过该产品额度");

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modifyQuota(EnterpriseQuotaDTO enterpriseQuota) {
        Assert.isTrue(checkStatus(enterpriseQuota.getId(), EnterpriseQuotaStatusEnum.VALID.getCode()), "生效中状态才能调整");
        String value = CodeUtil.generateCode(CodeEnum.PROCESS_NO);
        Map<String, Object> variables = Maps.newHashMap();
        variables.put(ProcessConstant.CORE_ENTERPRISE_QUOTA, enterpriseQuota);
        variables.put("id", enterpriseQuota.getId());
        variables.put(WfProcessConstant.PROCESS_NO, value);
        getVariables(enterpriseQuota, variables);

        update(Wrappers.<EnterpriseQuota>lambdaUpdate()
                .eq(EnterpriseQuota::getId, enterpriseQuota.getId())
                .set(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.CHANGING.getCode()));

        String processKey = goodsProcessService.selectProcessKey(enterpriseQuota.getGoodsId(), ProcessTypeEnum.CORE_APPLY_QUOTA.getCode());
        businessProcessService.startProcess(processKey, variables);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized boolean coreModifyQuota(EnterpriseQuotaDTO enterpriseQuota) {

        Assert.isTrue(checkStatus(enterpriseQuota.getId(), EnterpriseQuotaStatusEnum.VALID.getCode()), "生效中状态才能调整");

        String value = CodeUtil.generateCode(CodeEnum.PROCESS_NO);
        Map<String, Object> variables = Maps.newHashMap();
        //根据id查询原有额度
        EnterpriseQuota enterprise = baseMapper.selectById(enterpriseQuota.getId());
        EnterpriseQuotaDTO enterpriseDto = new EnterpriseQuotaDTO();
        BeanUtil.copyProperties(enterprise, enterpriseDto);

        User user = CustomerUserCache.getUserById(enterprise.getEnterpriseId());
        enterpriseDto.setEnterpriseName(user.getName());
        Dept dept = remoteDeptSearchService.getDeptById(enterprise.getCapitalId()).getData();
        enterpriseDto.setCapitalName(dept.getDeptName());


        //设置新额度
        enterpriseDto.setNewCreditAmount(enterpriseQuota.getNewCreditAmount());
        enterpriseDto.setNewQuotaType(enterpriseQuota.getNewQuotaType());
        enterpriseDto.setNewAnnualInterestRate(enterpriseQuota.getNewAnnualInterestRate());
        enterpriseDto.setNewExpireTime(enterpriseQuota.getNewExpireTime());
        enterpriseDto.setReason(enterpriseQuota.getReason());

        variables.put(ProcessConstant.CORE_ENTERPRISE_QUOTA, enterpriseDto);
        variables.put("id", enterpriseQuota.getId());
        variables.put(WfProcessConstant.PROCESS_NO, value);
        //对比调整后金额
        BigDecimal newCreditAmount = enterpriseQuota.getNewCreditAmount();
        BigDecimal creditAmount = enterprise.getCreditAmount();
        int i = creditAmount.compareTo(newCreditAmount);
        //i==1需要不风控 i!=1需要风控审核
        if (i == 1) {
            variables.put("needRisk", false);
        } else {
            variables.put("needRisk", true);
        }
        getVariables(enterpriseDto, variables);

        update(Wrappers.<EnterpriseQuota>lambdaUpdate()
                .eq(EnterpriseQuota::getId, enterpriseQuota.getId())
                .set(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.CHANGING.getCode()));
        //14 核心企业额度调整
        String processKey = goodsProcessService.selectProcessKey(enterprise.getGoodsId(), ProcessTypeEnum.CORE_QUOTA_UPDATE.getCode());


        String processInstanceId = businessProcessService.startProcess(processKey, variables);

        //记录本次流程信息
        businessProcessProgressService.saveBusinessProcessProgress(enterpriseQuota.getId(),
                ProcessProgressEnum.QUOTA_CHANGING_APPLY.getCode(),
                ProcessTypeEnum.CORE_QUOTA_UPDATE.getCode(),
                processInstanceId);
        return true;
    }


    @Override
    public List<EnterpriseQuota> selectEnterpriseQuota(Long companyId, Integer type) {
        return baseMapper.selectList(buildQueryWrapper(companyId, type));
    }

    @Override
    public Boolean saveBankAccount(CorporateAccountDTO corporateAccountDTO) {
        Long userId = MyAuthUtil.getUserId();
        EnterpriseQuota enterpriseQuota = getByGoodsIdAndEnterpriseTypeAndEnterpriseId(
                corporateAccountDTO.getGoodsId(),
                corporateAccountDTO.getEnterpriseType(),
                userId);
        BankCard bankCard = bankCardService.getByCardNo(corporateAccountDTO.getBankCardNo(), userId);
        Long bankId = bankCard.getBankId();
        if (ObjectUtil.isNotEmpty(bankId)) {
            Bank bank = bankService.getById(bankId);
            enterpriseQuota.setBankUnionCode(bank.getBankNo());
        } else {
            enterpriseQuota.setBankUnionCode(bankCard.getBankCode());
        }
        enterpriseQuota.setBank(corporateAccountDTO.getBank());
        enterpriseQuota.setBankId(bankId);
        enterpriseQuota.setBankCardNo(corporateAccountDTO.getBankCardNo());
        enterpriseQuota.setCity(corporateAccountDTO.getCity());
        return updateById(enterpriseQuota);
    }

    private LambdaQueryWrapper<EnterpriseQuota> buildQueryWrapper(Long companyId, Integer type) {
        LambdaQueryWrapper<EnterpriseQuota> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EnterpriseQuota::getCapitalId, companyId);
        if (Objects.nonNull(type) && type != 2) {
            wrapper.notIn(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.DISABLE.getCode(), EnterpriseQuotaStatusEnum.EXPIRE.getCode());
        }
        return wrapper;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized boolean financingModifyQuota(EnterpriseQuotaDTO enterpriseQuota) {
        Assert.isTrue(checkStatus(enterpriseQuota.getId(), EnterpriseQuotaStatusEnum.VALID.getCode()), "生效中状态才能调整");

        String value = CodeUtil.generateCode(CodeEnum.PROCESS_NO);
        Map<String, Object> variables = Maps.newHashMap();
        //根据id查询原有额度
        EnterpriseQuota enterprise = baseMapper.selectById(enterpriseQuota.getId());
        EnterpriseQuotaDTO enterpriseDto = new EnterpriseQuotaDTO();
        BeanUtil.copyProperties(enterprise, enterpriseDto);

        User user = CustomerUserCache.getUserById(enterprise.getEnterpriseId());
        enterpriseDto.setEnterpriseName(user.getName());
        Dept dept = remoteDeptSearchService.getDeptById(enterprise.getCapitalId()).getData();
        enterpriseDto.setCapitalName(dept.getDeptName());


        //设置新额度
        enterpriseDto.setNewCreditAmount(enterpriseQuota.getNewCreditAmount());
        enterpriseDto.setNewQuotaType(enterpriseQuota.getNewQuotaType());
        enterpriseDto.setNewAnnualInterestRate(enterpriseQuota.getNewAnnualInterestRate());
        enterpriseDto.setNewExpireTime(enterpriseQuota.getNewExpireTime());
        enterpriseDto.setReason(enterpriseQuota.getReason());

        variables.put(ProcessConstant.CORE_ENTERPRISE_QUOTA, enterpriseDto);
        variables.put("id", enterpriseQuota.getId());
        variables.put("type", UserTypeEnum.WEB.getCode());
        variables.put(WfProcessConstant.PROCESS_NO, value);
        //对比调整后金额
        BigDecimal newCreditAmount = enterpriseQuota.getNewCreditAmount();
        BigDecimal creditAmount = enterprise.getCreditAmount();
        int i = creditAmount.compareTo(newCreditAmount);
        //i==1需要不风控 i!=1需要风控审核
        if (i == 1) {
            variables.put("needRisk", false);
        } else {
            variables.put("needRisk", true);
        }
        getVariables(enterpriseDto, variables);

        update(Wrappers.<EnterpriseQuota>lambdaUpdate()
                .eq(EnterpriseQuota::getId, enterpriseQuota.getId())
                .set(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.CHANGING.getCode()));

        String processKey = goodsProcessService.selectProcessKey(enterprise.getGoodsId(), ProcessTypeEnum.FINANCING_QUOTA_UPDATE.getCode());

        //14 核心企业额度调整
        String processInstanceId = businessProcessService.startProcess(processKey, variables);
        //记录本次流程信息
        businessProcessProgressService.saveBusinessProcessProgress(enterpriseQuota.getId(),
                ProcessProgressEnum.QUOTA_CHANGING_APPLY.getCode(),
                ProcessTypeEnum.CORE_QUOTA_UPDATE.getCode(),
                processInstanceId);
        return true;
    }

    private boolean checkStatus(Long id, int code) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        return Objects.nonNull(enterpriseQuota) && enterpriseQuota.getStatus().equals(code);
    }

    @Override
    public boolean frozenAmount(Long id, String frozenReason) {
        // 通过企业额度id -> 企业额度实体类
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        // 判空
        Assert.notNull(enterpriseQuota, "找不到该记录");
        // 判断是否为生效中
        Assert.isTrue(enterpriseQuota.getStatus().equals(EnterpriseQuotaStatusEnum.VALID.getCode()), "只能在生效中状态执行该操作");
        // 设置冻结原因
        enterpriseQuota.setFrozenReason(frozenReason);
        // 设置状态为已冻结
        enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.FROZEN.getCode());
        // 企业额度信息
        CustomerGoods customerGoods = customerGoodsService.getByEnterpriseQuotaId(enterpriseQuota.getId());
        // 设置产品状态为已冻结
        customerGoods.setStatus(CustomerGoodsEnum.FROZEN.getCode());
        customerGoodsService.updateById(customerGoods);
        // 记录冻结信息（保存到额度变化表）
        quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.FREEZE_QUOTA.getCode());
        // 更新企业额度表
        return updateById(enterpriseQuota);
    }

    @Override
    public boolean thawAmount(Long id, String thawReason) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        Assert.notNull(enterpriseQuota, "找不到该记录");
        CustomerGoods customerGoods = customerGoodsService.getByEnterpriseQuotaId(id);
        //查询是否存在解冻申请
        boolean canUnFreeze = businessProcessProgressService.platCanUnFreeze(customerGoods.getId(), ProcessTypeEnum.RECEIVE_UNFREEZE_GOODS_APPLY.getCode(), customerGoods.getEnterpriseId());
        if (!canUnFreeze) {
            throw new ServiceException("客户正在申请解冻，请等待客户结束申请后发起");
        }
        //产品解冻
        customerGoodsService.updateStatus(customerGoods.getId(), CustomerGoodsEnum.FINANCING.getCode());
        Assert.isTrue(enterpriseQuota.getStatus().equals(EnterpriseQuotaStatusEnum.FROZEN.getCode()), "只能在已冻结状态执行该操作");
        enterpriseQuota.setThawReason(thawReason);
        enterpriseQuota.setAvailableAmount(enterpriseQuota.getAvailableAmount().add(enterpriseQuota.getFrozenAmount()));
        enterpriseQuota.setFrozenAmount(BigDecimal.ZERO);
        enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.VALID.getCode());
        //记录本次请求信息
        Boolean aBoolean = quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.UNFREEZE_QUOTA.getCode());
        return updateById(enterpriseQuota);
    }

    @Override
    public boolean disable(Long id, String disableReason) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        Assert.notNull(enterpriseQuota, "找不到该记录");
        Assert.isTrue(enterpriseQuota.getStatus().equals(EnterpriseQuotaStatusEnum.VALID.getCode()), "只能在生效中状态执行该操作");
        enterpriseQuota.setDisableReason(disableReason);
        enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.DISABLE.getCode());

        customerGoodsService.update(Wrappers.<CustomerGoods>lambdaUpdate()
                .eq(CustomerGoods::getEnterpriseQuotaId, id)
                .set(CustomerGoods::getStatus, CustomerGoodsEnum.DISABLE.getCode()));
        //保存记录禁用
        quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.DISABLE_QUOTA.getCode());
        return updateById(enterpriseQuota);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveEnterpriseQuota(Map<String, Object> variables, Integer status) {

        FinalApproveAmount finalApproveAmount = JSON.parseObject(JSON.toJSONString(variables.get(ProcessConstant.FINAL_APPROVE_AMOUNT)), FinalApproveAmount.class);

        EnterpriseQuotaDTO enterpriseQuota = (EnterpriseQuotaDTO) variables.get(ProcessConstant.CORE_ENTERPRISE_QUOTA);
        if (Objects.isNull(enterpriseQuota)) {
            enterpriseQuota = new EnterpriseQuotaDTO();
            enterpriseQuota.setGoodsId((Long) variables.get(ProcessConstant.BUSINESS_ID));
            enterpriseQuota.setProductType((Integer) variables.get(ProcessConstant.PRODUCT_TYPE));
        }
        // 申请金额
        BigDecimal creditAmount = enterpriseQuota.getCreditAmount();
        // 最终批额
        BigDecimal finalAmount = finalApproveAmount.getFinalAmount().multiply(BigDecimal.valueOf(10000));
        enterpriseQuota.setQuotaNo(CodeUtil.generateCode(CodeEnum.QUOTA_CODE));
        enterpriseQuota.setCreditAmount(finalAmount);
        enterpriseQuota.setQuotaType(finalApproveAmount.getQuotaType());
        enterpriseQuota.setServiceRate(finalApproveAmount.getServiceRate());
        enterpriseQuota.setEffectiveTime(LocalDateTime.now());
        enterpriseQuota.setExpireTime(finalApproveAmount.getExpireTime().withHour(23).withMinute(59).withSecond(50));
        enterpriseQuota.setAnnualInterestRate(finalApproveAmount.getAnnualInterestRate());
        enterpriseQuota.setFrozenAmount(BigDecimal.ZERO);
        enterpriseQuota.setAvailableAmount(finalAmount);
        enterpriseQuota.setLoanable(finalApproveAmount.getLoanable());
        enterpriseQuota.setRecycleType(finalApproveAmount.getRecycleType());
        enterpriseQuota.setUsedAmount(BigDecimal.ZERO);
        enterpriseQuota.setStatus(status);
        enterpriseQuota.setProcessInstanceId((String) variables.getOrDefault(ProcessConstant.PROCESS_INSTANCE_ID, ""));
        enterpriseQuota.setTaskId((String) variables.getOrDefault(ProcessConstant.TASK_ID, ""));
        enterpriseQuota.setTaskNo((String) variables.getOrDefault(WfProcessConstant.PROCESS_NO, ""));
        enterpriseQuota.setApplyAmount(BigDecimal.ZERO);
        enterpriseQuota.setDailyInterestRate(finalApproveAmount.getAnnualInterestRate().divide(BigDecimal.valueOf(CommonConstant.YEAR_DAY_COUNT), 3, CommonConstant.NUMBER_STRATEGY));
        enterpriseQuota.setEnterpriseType((Integer) variables.getOrDefault(ProcessConstant.ENTERPRISE_TYPE, -1));
        enterpriseQuota.setBondProportion(finalApproveAmount.getBondProportion());
        enterpriseQuota.setFinancingProportion(finalApproveAmount.getFinancingProportion());
        Product product = productDirector.detail(enterpriseQuota.getGoodsId());
        enterpriseQuota.setCapitalId(product.getCapitalId());
        enterpriseQuota.setGoodsName(product.getGoodsName());

//		if (GoodsEnum.CLOUD_CREDIT.getCode().equals(enterpriseQuota.getProductType())) {
//			CloudProduct cloudProduct = cloudProductService.getById(enterpriseQuota.getGoodsId());
//			enterpriseQuota.setGoodsName(cloudProduct.getGoodsName());
//
//		} else {
//			Goods goods = goodsMapper.selectById(enterpriseQuota.getGoodsId());
//			enterpriseQuota.setGoodsName(goods.getGoodsName());
//			enterpriseQuota.setCapitalId(goods.getCapitalId());
//		}


        save(enterpriseQuota);
        //记录本次请求信息
        Boolean aBoolean = quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.GET_QUOTA.getCode());

        // 给核心企业设置评分
        frontCoreListService.settingScore(enterpriseQuota.getEnterpriseId(), new BigDecimal((String) variables.get("score")));
        // 资方扣减额度
        cusCapitalQuotaService.capitalCreditSuccess(enterpriseQuota.getCapitalId(), creditAmount, finalAmount);
        // 更新开通产品状态
        String processInstanceId = (String) variables.get(ProcessConstant.PROCESS_INSTANCE_ID);
        CustomerGoods customerGoods = CustomerGoods.builder()
                .enterpriseId(enterpriseQuota.getEnterpriseId())
                .enterpriseType(enterpriseQuota.getEnterpriseType())
                .goodsName(enterpriseQuota.getGoodsName())
                .goodsType(enterpriseQuota.getProductType())
                .capitalId(enterpriseQuota.getCapitalId())
                .capitalName(product.getCapitalName())
                .capitalAvatar(product.getCapitalLogo())
                .goodsId(enterpriseQuota.getGoodsId())
                .openNo(CodeUtil.generateCode(CodeEnum.OPEN_NO))
                .build();
        customerGoods.setStatus(CustomerGoodsEnum.FINANCING.getCode());
        customerGoods.setProcessInstanceId(processInstanceId);
        customerGoods.setRatingRecordId((Long) variables.getOrDefault(ProcessConstant.RATING_RECORD_ID, -1L));
        customerGoods.setEnterpriseQuotaId(enterpriseQuota.getId());
        customerGoodsService.saveOrUpdate(customerGoods);

        return enterpriseQuota.getId();
    }

    @Override
    public Long saveFinancingEnterpriseQuota(Map<String, Object> variables) {
        FinalApproveAmount finalApproveAmount = JSON.parseObject(JSON.toJSONString(variables.get(ProcessConstant.FINAL_APPROVE_AMOUNT)), FinalApproveAmount.class);

        Long goodsId = (Long) variables.getOrDefault(ProcessConstant.BUSINESS_ID, -1L);

        Long userId = (Long) variables.get(ProcessConstant.USER_ID);

        Integer enterpriseType = (Integer) variables.get(ProcessConstant.ENTERPRISE_TYPE);

        Integer productType = (Integer) variables.get(ProcessConstant.PRODUCT_TYPE);


        EnterpriseQuota oldEnterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getGoodsId, goodsId)
                .eq(EnterpriseQuota::getEnterpriseType, enterpriseType)
                .eq(EnterpriseQuota::getEnterpriseId, userId));


        EnterpriseQuota enterpriseQuota = EnterpriseQuota.builder()
                .annualInterestRate(finalApproveAmount.getAnnualInterestRate())
                .availableAmount(CommonUtil.wanToYuan(finalApproveAmount.getFinalAmount()))
                .creditAmount(CommonUtil.wanToYuan(finalApproveAmount.getFinalAmount()))
                .effectiveTime(LocalDateTime.now())
                .loanable(finalApproveAmount.getLoanable())
                .expireTime(finalApproveAmount.getExpireTime().withHour(23).withMinute(59).withSecond(50))
                .serviceRate(finalApproveAmount.getServiceRate())
                .frozenAmount(BigDecimal.ZERO)
                .recycleType(finalApproveAmount.getRecycleType())
                .quotaNo(CodeUtil.generateCode(CodeEnum.QUOTA_CODE))
                .taskNo((String) variables.getOrDefault(WfProcessConstant.PROCESS_NO, ""))
                .taskId((String) variables.getOrDefault(ProcessConstant.TASK_ID, ""))
                .processInstanceId((String) variables.getOrDefault(ProcessConstant.PROCESS_INSTANCE_ID, ""))
                .goodsId(goodsId)
                .goodsName((String) variables.get(ProcessConstant.GOODS_NAME))
                .enterpriseId(userId)
                .capitalId((Long) variables.get(ProcessConstant.CAPITAL_ID))
                .enterpriseType(enterpriseType)
                .productType(productType)
                .quotaType(finalApproveAmount.getQuotaType())
                .dailyInterestRate(finalApproveAmount.getAnnualInterestRate().divide(BigDecimal.valueOf(CommonConstant.YEAR_DAY_COUNT), 3, CommonConstant.NUMBER_STRATEGY))
                .usedAmount(BigDecimal.ZERO)
                .bondProportion(finalApproveAmount.getBondProportion())
                .applyAmount(BigDecimal.ZERO)
                .financingProportion(finalApproveAmount.getFinancingProportion())
                .build();

        enterpriseQuota.setId(Objects.nonNull(oldEnterpriseQuota) ? oldEnterpriseQuota.getId() : null);
        saveOrUpdate(enterpriseQuota);
        //记录本次请求信息
        Boolean aBoolean = quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.GET_QUOTA.getCode());
        return enterpriseQuota.getId();
    }

    @Override
    public List<Long> getByGoodsIdAndEnterpriseId(List<Long> companyLowerId, Long goodsId) {
        return getByGoodsIdAndEnterpriseId(companyLowerId, Collections.singletonList(goodsId));
    }

    @Override
    public List<Long> getByGoodsIdAndEnterpriseId(List<Long> companyLowerId, List<Long> goodsId) {
        List<EnterpriseQuota> enterpriseQuotas = baseMapper.selectList(Wrappers.<EnterpriseQuota>lambdaQuery()
                .in(EnterpriseQuota::getEnterpriseId, companyLowerId)
                .in(EnterpriseQuota::getGoodsId, goodsId)
                .eq(EnterpriseQuota::getEnterpriseType, EnterpriseTypeEnum.CORE_ENTERPRISE.getCode())
                .eq(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.VALID.getCode()));

        return enterpriseQuotas.stream().map(EnterpriseQuota::getEnterpriseId).collect(Collectors.toList());
    }

    @Override
    public boolean active(Long id) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        Assert.notNull(enterpriseQuota, "找不到该记录");
        Integer status = enterpriseQuota.getStatus();
        Assert.isTrue(
                status.equals(EnterpriseQuotaStatusEnum.UN_VALID.getCode()) ||
                        status.equals(EnterpriseQuotaStatusEnum.DISABLE.getCode()), "只能在待激活和禁用状态执行该操作");
        Map<String, Object> variables = Maps.newHashMap();

        variables.put(ProcessConstant.CORE_ENTERPRISE_QUOTA, enterpriseQuota);
        variables.put(ProcessConstant.MOVE_TYPE, 1);
        variables.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        String processKey = goodsProcessService.selectProcessKey(enterpriseQuota.getGoodsId(), ProcessTypeEnum.QUOTA_ACTIVE.getCode());


        businessProcessService.startProcess(processKey, variables);
        return true;
    }

    @Override
    public boolean renewal(EnterpriseQuotaDTO enterpriseQuota) {
        Integer status = enterpriseQuota.getStatus();
        Assert.isTrue(status.equals(EnterpriseQuotaStatusEnum.EXPIRE.getCode()), "只能在已过期状态执行该操作");
        enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.RENEWAL_ING.getCode());
        Map<String, Object> variables = Maps.newHashMap();
        enterpriseQuota.setTaskNo(CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        variables.put(ProcessConstant.CORE_ENTERPRISE_QUOTA, enterpriseQuota);
        getVariables(enterpriseQuota, variables);
        variables.put("id", enterpriseQuota.getId());
        variables.put(WfProcessConstant.PROCESS_NO, enterpriseQuota.getTaskNo());
        String processKey = goodsProcessService.selectProcessKey(enterpriseQuota.getGoodsId(), ProcessTypeEnum.CORE_APPLY_QUOTA.getCode());

        businessProcessService.startProcess(processKey, variables);
        boolean b = updateById(enterpriseQuota);
        //记录本次请求信息
        quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.EXTEND_QUOTA.getCode());
        return b;
    }

    @Override
    public boolean subtractQuota(Long goodsId, Long enterpriseId, Integer enterpriseType, BigDecimal amount) {
        updateAmount(() -> {
            EnterpriseQuota enterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                    .eq(EnterpriseQuota::getGoodsId, goodsId)
                    .eq(EnterpriseQuota::getEnterpriseId, enterpriseId)
                    .eq(EnterpriseQuota::getEnterpriseType, enterpriseType));
            Assert.notNull(enterpriseId, "该企业没有申请该产品的额度");
            BigDecimal availableAmount = enterpriseQuota.getAvailableAmount();
            Assert.isTrue(availableAmount.compareTo(amount) >= 0, "扣减额度不能大于可用额度");
            enterpriseQuota.setAvailableAmount(availableAmount.subtract(amount));
            enterpriseQuota.setUsedAmount(enterpriseQuota.getUsedAmount().add(amount));
            return updateById(enterpriseQuota);
        }, getRedisLockKey(goodsId, enterpriseId, enterpriseType));

        return true;
    }

    @Override
    public IPage<EnterpriseQuotaVO> selectEnterpriseQuotaPage(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query) {
        IPage<EnterpriseQuotaVO> iPage = baseMapper.selectCoreEnterpriseQuotaPage(Condition.getPage(query), enterpriseQuotaDTO);
        //添加已使用循环额度字段
        SpringUtil.getBean(IQuotaUseDetailsService.class).getUseCircleQuota(iPage);
        return iPage;
    }

    @Override
    public IPage<EnterpriseQuotaVO> selectFinancingEnterpriseQuotaPage(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query) {
        IPage<EnterpriseQuotaVO> iPage = baseMapper.selectFinancingEnterpriseQuotaPage(Condition.getPage(query), enterpriseQuotaDTO);
        //添加已使用循环额度字段
        SpringUtil.getBean(IQuotaUseDetailsService.class).getUseCircleQuota(iPage);
        return iPage;
    }

    @Override
    public Map<Long, BigDecimal> selectAmountByEnterpriseId(List<Long> enterpriseIdList) {
        Map<Long, List<EnterpriseQuota>> map = getByEnterpriseId(enterpriseIdList).stream().collect(Collectors.groupingBy(EnterpriseQuota::getEnterpriseId));
        return map.entrySet().stream().
                collect(Collectors.toMap(Map.Entry::getKey, entry ->
                        entry.getValue().stream().map(EnterpriseQuota::getCreditAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)));

    }

    @Override
    public EnterpriseQuota getByGoodsIdAndEnterpriseTypeAndEnterpriseIdBase(Long goodsId, Integer enterpriseType, Long enterpriseId) {
        return baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getGoodsId, goodsId)
                .eq(EnterpriseQuota::getEnterpriseType, enterpriseType)
                .eq(EnterpriseQuota::getEnterpriseId, enterpriseId));
    }

    @Override
    public EnterpriseQuotaVO getByGoodsIdAndEnterpriseTypeAndEnterpriseId(Long goodsId, Integer enterpriseType, Long enterpriseId) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getGoodsId, goodsId)
                .eq(EnterpriseQuota::getEnterpriseType, enterpriseType)
                .eq(EnterpriseQuota::getEnterpriseId, enterpriseId));

        if (Objects.isNull(enterpriseQuota)) {
            return null;
        }
        List<Dept> data = remoteDeptSearchService.selectDeptByIdsF(Collections.singletonList(enterpriseQuota.getCapitalId()), FeignConstants.FROM_IN).getData();
        EnterpriseQuotaVO enterpriseQuotaVO = EnterpriseQuotaWrapper.build().entityVO(enterpriseQuota);
        enterpriseQuotaVO.setCapitalName(CollUtil.isNotEmpty(data) ? data.get(0).getDeptName() : "");
        return enterpriseQuotaVO;
    }

    @Override
    public Map<Long, EnterpriseQuota> getMapInId(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        List<EnterpriseQuota> enterpriseQuotas = baseMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(enterpriseQuotas)) {
            return Collections.emptyMap();
        }
        return enterpriseQuotas.stream().collect(Collectors.toMap(BaseEntity::getId, obj -> obj));
    }

    @Override
    public EnterpriseQuotaVO detail(Long id) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        if (Objects.isNull(enterpriseQuota)) {
            return null;
        }
        EnterpriseQuotaVO enterpriseQuotaVO = EnterpriseQuotaWrapper.build().entityVO(enterpriseQuota);
        User user = CustomerUserCache.getUserById(enterpriseQuota.getEnterpriseId());
        if (Objects.nonNull(user)) {
            enterpriseQuotaVO.setEnterpriseName(user.getName());
            enterpriseQuotaVO.setAvatar(user.getAvatar());
            enterpriseQuotaVO.setCapitalName(remoteDeptSearchService.getDeptById(enterpriseQuota.getCapitalId()).getData().getDeptName());
        }
        return enterpriseQuotaVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enable(Long id) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        if (Objects.isNull(enterpriseQuota) || !enterpriseQuota.getStatus().equals(EnterpriseQuotaStatusEnum.DISABLE.getCode())) {
            return false;
        }
        LocalDateTime expireTime = enterpriseQuota.getExpireTime();
        CustomerGoods customerGoods = customerGoodsService.getByEnterpriseQuotaId(id);
        if (expireTime.compareTo(LocalDateTime.now()) > 0) {
            enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.VALID.getCode());
            customerGoods.setStatus(CustomerGoodsEnum.FINANCING.getCode());
        }

        //记录本次请求信息
        Boolean aBoolean = quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.RECOVERY_QUOTA.getCode());
        customerGoodsService.updateById(customerGoods);
        return updateById(enterpriseQuota);
    }

    @Override
    public void returnApplyAmount(BigDecimal amount, Long goodsId, Long userId, int type) {
        updateAmount(() -> {
            EnterpriseQuota enterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                    .eq(EnterpriseQuota::getGoodsId, goodsId)
                    .eq(EnterpriseQuota::getEnterpriseId, userId)
                    .eq(EnterpriseQuota::getEnterpriseType, type));
            Assert.notNull(enterpriseQuota, "该企业没有申请该产品的额度");
            BigDecimal creditAmount = enterpriseQuota.getCreditAmount();
            BigDecimal adjustAvailableAmount = enterpriseQuota.getAvailableAmount().add(amount);
            BigDecimal adjustApplyAmount = enterpriseQuota.getApplyAmount().subtract(amount);
            BigDecimal usedAmount = enterpriseQuota.getUsedAmount();
            //调整后的可用+调整后的申请中<授信 则按调整后的 若>授信 超过部分进行扣除 扣除后<0则按0算
            if (adjustAvailableAmount.add(adjustApplyAmount).compareTo(creditAmount) > 0) {
                BigDecimal overAmount = adjustAvailableAmount.add(adjustApplyAmount).subtract(creditAmount);
                adjustAvailableAmount = adjustAvailableAmount.subtract(overAmount);
                if (adjustAvailableAmount.compareTo(BigDecimal.ZERO) < 0) {
                    adjustAvailableAmount = BigDecimal.ZERO;
                }
            }
            enterpriseQuota.setAvailableAmount(adjustAvailableAmount);
            enterpriseQuota.setApplyAmount(adjustApplyAmount);
            return updateById(enterpriseQuota);
        }, getRedisLockKey(goodsId, userId, type));
    }

    private String getRedisLockKey(Long goodsId, Long userId, int type) {
        return "quota:" + goodsId + ":" + userId + ":" + type;
    }

    @Override
    public String getRepaymentAccountSuffix(Long goodsId, Long userId) {
        EnterpriseQuotaVO enterpriseQuotaVO = getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), userId);
        String capitalName = enterpriseQuotaVO.getBank();
        String bankCardNo = enterpriseQuotaVO.getBankCardNo();
        String bankCardNoSuffix = StringUtil.subSuf(bankCardNo, bankCardNo.length() - 4);
        return capitalName.concat(StringPool.LEFT_BRACKET).concat(bankCardNoSuffix == null ? "" : bankCardNoSuffix).concat(StringPool.RIGHT_BRACKET);
    }

    @Override
    public IPage<EnterpriseQuotaVO> selectEnterpriseQuotaCloudPage(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query) {
        enterpriseQuotaDTO.setProductType(GoodsEnum.CLOUD_CREDIT.getCode());
        return baseMapper.selectCoreEnterpriseQuotaPage(Condition.getPage(query), enterpriseQuotaDTO);
    }

    @Override
    public Map<Long, String> getRepaymentAccountSuffixMap(List<Long> goodsIdList, Long userId) {
        List<EnterpriseQuota> enterpriseQuotaList = lambdaQuery().in(EnterpriseQuota::getGoodsId, goodsIdList)
                .eq(EnterpriseQuota::getEnterpriseId, userId).list();
        List<Long> capitalIdList = StreamUtil.map(enterpriseQuotaList, EnterpriseQuota::getCapitalId);

        Map<Long, Dept> deptMap = CollectionUtil.isEmpty(capitalIdList) ? Collections.emptyMap() : remoteDeptSearchService.selectDeptByIds(capitalIdList).getData().stream()
                .collect(Collectors.toMap(Dept::getId, e -> e));
        if (ObjectUtil.isEmpty(deptMap)) {
            return Collections.emptyMap();
        }
        return StreamUtil.toMap(enterpriseQuotaList, EnterpriseQuota::getGoodsId, enterpriseQuota -> {
            Dept dept = deptMap.get(enterpriseQuota.getCapitalId());
            String bankCardNo = enterpriseQuota.getBankCardNo();
            String bankCardNoSuffix = StringUtil.subSuf(bankCardNo, bankCardNo.length() - 4);
            return dept.getDeptName().concat(StringPool.LEFT_BRACKET).concat(bankCardNoSuffix != null ? bankCardNoSuffix : "").concat(StringPool.RIGHT_BRACKET);
        });
    }

    public List<EnterpriseQuota> getByEnterpriseId(List<Long> enterpriseIdList) {
        return baseMapper.selectList(Wrappers.<EnterpriseQuota>lambdaQuery().in(EnterpriseQuota::getEnterpriseId, enterpriseIdList));
    }


    @Override
    public void addQuota(Long goodsId, Integer enterpriseType, Long enterpriseId, BigDecimal amount) {
        updateAmount(() -> {
            EnterpriseQuota enterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                    .eq(EnterpriseQuota::getGoodsId, goodsId)
                    .eq(EnterpriseQuota::getEnterpriseId, enterpriseId)
                    .eq(EnterpriseQuota::getEnterpriseType, enterpriseType));
            Assert.notNull(enterpriseQuota, "该企业没有申请该产品的额度");
            //增加后额度不能大于授信额度
            BigDecimal availableAmount = enterpriseQuota.getAvailableAmount().add(amount);
            if (availableAmount.compareTo(enterpriseQuota.getCreditAmount()) == 1) {
                enterpriseQuota.setAvailableAmount(enterpriseQuota.getCreditAmount());
            } else {
                enterpriseQuota.setAvailableAmount(availableAmount);
            }
            enterpriseQuota.setUsedAmount(enterpriseQuota.getUsedAmount().subtract(amount));
            return updateById(enterpriseQuota);
        }, getRedisLockKey(goodsId, enterpriseId, enterpriseType));
    }

    @Override
    public void addApplyAmount(Long goodsId, Long enterpriseId, Integer enterpriseType, BigDecimal amount) {
        updateAmount(() -> {
            EnterpriseQuota enterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                    .eq(EnterpriseQuota::getGoodsId, goodsId)
                    .eq(EnterpriseQuota::getEnterpriseId, enterpriseId)
                    .eq(EnterpriseQuota::getEnterpriseType, enterpriseType));
            Assert.notNull(enterpriseQuota, "该企业没有申请该产品的额度");
            Assert.isTrue(enterpriseQuota.getAvailableAmount().compareTo(amount) >= 0, "授信额度不足");
            enterpriseQuota.setAvailableAmount(enterpriseQuota.getAvailableAmount().subtract(amount));
            enterpriseQuota.setApplyAmount(enterpriseQuota.getApplyAmount().add(amount));
            return updateById(enterpriseQuota);
        }, getRedisLockKey(goodsId, enterpriseId, enterpriseType));
    }


    @Override
    public void addUsedAmount(Long goodsId, Long enterpriseId, int enterpriseType, BigDecimal amount) {

        updateAmount(() -> {
            EnterpriseQuota enterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                    .eq(EnterpriseQuota::getGoodsId, goodsId)
                    .eq(EnterpriseQuota::getEnterpriseId, enterpriseId)
                    .eq(EnterpriseQuota::getEnterpriseType, enterpriseType));
            Assert.notNull(enterpriseQuota, "该企业没有申请该产品的额度");
            enterpriseQuota.setApplyAmount(enterpriseQuota.getApplyAmount().subtract(amount));
            enterpriseQuota.setUsedAmount(enterpriseQuota.getUsedAmount().add(amount));
            return updateById(enterpriseQuota);
        }, getRedisLockKey(goodsId, enterpriseId, enterpriseType));

    }

    @Override
    public void subtractApplyQuota(BigDecimal amount, Long goodsId, int enterpriseType, Long enterpriseId) {
        updateAmount(() -> {
            EnterpriseQuota enterpriseQuota = baseMapper.selectOne(Wrappers.<EnterpriseQuota>lambdaQuery()
                    .eq(EnterpriseQuota::getGoodsId, goodsId)
                    .eq(EnterpriseQuota::getEnterpriseId, enterpriseId)
                    .eq(EnterpriseQuota::getEnterpriseType, enterpriseType));
            Assert.notNull(enterpriseQuota, "该企业没有申请该产品的额度");
            enterpriseQuota.setApplyAmount(enterpriseQuota.getApplyAmount().subtract(amount));
            enterpriseQuota.setAvailableAmount(enterpriseQuota.getAvailableAmount().add(amount));
            return updateById(enterpriseQuota);
        }, getRedisLockKey(goodsId, enterpriseId, enterpriseType));
    }

    private <T> void updateAmount(Supplier<T> supplier, String key) {
        try {
            boolean tryLock = redisLockClient.tryLock(key, LockType.FAIR, 30, 30, TimeUnit.SECONDS);
            if (tryLock) {
                supplier.get();
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            log.error("扣减额度失败");
        } finally {
            redisLockClient.unLock(key, LockType.FAIR);
        }
    }

    /**
     * 额度扣减
     *
     * @param amount          融资金额
     * @param enterpriseQuota 已开通产品 额度
     * @param type            1、代采申请中：可用额度   减去 融资金额 然后  申请中的额度 加上 融资金额
     *                        2、驳回：     申请中额度 减去 融资金额 然后 可用额度 加上 融资金额
     *                        3、通过：     申请中额度 减去 融资金额 然后 已用额度 加上 融资金额
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void subtractReceivableAmount(BigDecimal amount, EnterpriseQuota enterpriseQuota, int type) {

        //可用额度
        BigDecimal availableAmount;
        //申请中额度
        BigDecimal applyAmount;
        //已使用额度
        BigDecimal usedAmount;
        if (type == PurchaseEnum.PURCHASE_PASS_TYPE_ONE.getCode()) {
            //可用额度 减去 融资金额
            availableAmount = NumberUtil.sub(enterpriseQuota.getAvailableAmount(), amount).setScale(2, RoundingMode.HALF_EVEN);
            enterpriseQuota.setAvailableAmount(availableAmount);
            //申请中的额度 加上 融资金额
            applyAmount = NumberUtil.add(enterpriseQuota.getApplyAmount(), amount).setScale(2, RoundingMode.HALF_EVEN);
            enterpriseQuota.setApplyAmount(applyAmount);
        } else if (type == PurchaseEnum.PURCHASE_PASS_TYPE_TWO.getCode()) {
            //申请中的额度 减去 融资金额
            applyAmount = NumberUtil.sub(enterpriseQuota.getApplyAmount(), amount).setScale(2, RoundingMode.HALF_EVEN);
            enterpriseQuota.setApplyAmount(applyAmount);
            //可用额度 加上 融资金额
            availableAmount = NumberUtil.add(enterpriseQuota.getAvailableAmount(), amount).setScale(2, RoundingMode.HALF_EVEN);
            enterpriseQuota.setAvailableAmount(availableAmount);
        } else {
            //申请中额度 减去 融资金额
            applyAmount = NumberUtil.sub(enterpriseQuota.getApplyAmount(), amount).setScale(2, RoundingMode.HALF_EVEN);
            enterpriseQuota.setApplyAmount(applyAmount);
            //已用额度 加上 融资金额
            usedAmount = NumberUtil.add(enterpriseQuota.getUsedAmount(), amount).setScale(2, RoundingMode.HALF_EVEN);
            enterpriseQuota.setUsedAmount(usedAmount);
        }
        this.updateById(enterpriseQuota);
    }

    @Override
    public void frozenValidAmountByQuotaIds(List<Long> ids, List<CustomerGoods> customerGoods) {

    }

    @Override
    public EnterpriseQuotaVO financingQuotaHistory(Long id) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        if (Objects.isNull(enterpriseQuota)) {
            return null;
        }

        List<QuotaHistory> quotaHistories = quotaHistoryMapper.selectList(Wrappers.<QuotaHistory>lambdaQuery()
                .eq(QuotaHistory::getEnterpriseQuotaId, id)
                .orderByDesc(QuotaHistory::getChangeTime));

        EnterpriseQuotaVO vo = EnterpriseQuotaWrapper.build().entityVO(enterpriseQuota);
        User user = CustomerUserCache.getUserById(vo.getEnterpriseId());
        //使用明细
        Integer type = enterpriseQuota.getProductType();
        Long goodsId = enterpriseQuota.getGoodsId();
        Long enterpriseId = enterpriseQuota.getEnterpriseId();

        List<QuotaUseVo> quotaUseDetailsVos = new ArrayList<>();
        //TODO 待业务引入
//		if (GoodsEnum.CLOUD_CREDIT.getCode().equals(type)) {
//			List<CloudFinancing> cloudFinancings = cloudFinancingMapper.selectList(Wrappers.<CloudFinancing>lambdaQuery()
//				.eq(CloudFinancing::getCloudProductId, goodsId)
//				.eq(CloudFinancing::getApplyUser, enterpriseId));
//			quotaUseDetailsVos = cloudFinancings.stream().map(cloudFinancing -> {
//				QuotaUseVo quotaUseDetailsVo = new QuotaUseVo();
//				quotaUseDetailsVo.setAmount(cloudFinancing.getFinancingMoney());
//				quotaUseDetailsVo.setStatus(CloudFinanceStatusEnum.getValueByKey(cloudFinancing.getStatus()));
//				quotaUseDetailsVo.setUpdateTime(cloudFinancing.getUpdateTime());
//
//				List<Integer> list = Func.toIntList("1,3,4,6,9,10");
//				if (list.contains(cloudFinancing.getStatus())) {
//					quotaUseDetailsVo.setSign(-1);
//				} else {
//					quotaUseDetailsVo.setSign(1);
//				}
//				return quotaUseDetailsVo;
//			}).collect(Collectors.toList());
//
//		} else {
//
//			List<FinanceApply> financeApplyList = financeApplyMapper.selectList(Wrappers.<FinanceApply>lambdaQuery()
//				.eq(FinanceApply::getGoodsId, goodsId)
//				.eq(FinanceApply::getUserId, enterpriseId)
//				.orderByDesc(BaseEntity::getUpdateTime));
//
//			quotaUseDetailsVos = financeApplyList.stream().map(financeApply -> {
//				QuotaUseVo details = new QuotaUseVo();
//				details.setUpdateTime(financeApply.getUpdateTime());
//				details.setAmount(financeApply.getAmount());
//				Integer goodsType = financeApply.getGoodsType();
//				Integer status = financeApply.getStatus();
//				if (goodsType == 1) {
//					details.setStatus(FinanceApplyStatusEnum.getName(status));
//					List<Integer> list = Func.toIntList("0,1,4,5,8,9");
//					if (list.contains(status)) {
//						details.setSign(-1);
//					} else {
//						details.setSign(1);
//					}
//				} else {
//					details.setStatus(PurchaseStateEnum.getName(status));
//					List<Integer> list = Func.toIntList("1,4,5,6,7,10,12");
//					if (list.contains(status)) {
//						details.setSign(-1);
//					} else {
//						details.setSign(1);
//					}
//				}
//				return details;
//			}).collect(Collectors.toList());
//
//		}
        vo.setEnterpriseName(user.getName());
        vo.setQuotaHistoryList(quotaHistories);
        vo.setQuotaUseDetailList(quotaUseDetailsVos);
        return vo;
    }

    @Override
    public EnterpriseQuotaVO quotaHistory(Long id) {
        EnterpriseQuota enterpriseQuota = baseMapper.selectById(id);
        if (Objects.isNull(enterpriseQuota)) {
            return null;
        }
        EnterpriseQuotaVO vo = EnterpriseQuotaWrapper.build().entityVO(enterpriseQuota);
        User user = CustomerUserCache.getUserById(vo.getEnterpriseId());

        //额度变化
        List<QuotaHistory> quotaHistories = quotaHistoryMapper.selectList(Wrappers.<QuotaHistory>lambdaQuery()
                .eq(QuotaHistory::getEnterpriseQuotaId, id)
                .orderByDesc(QuotaHistory::getChangeTime));

        //使用明细
        Integer type = enterpriseQuota.getProductType();
        Long goodsId = enterpriseQuota.getGoodsId();
        Long enterpriseId = enterpriseQuota.getEnterpriseId();
        List<QuotaUseVo> quotaUseDetailsVos = new ArrayList<>();
        if (type == 3) {
            //TODO 待业务引入
//			//查询云信核心企业数据
//			List<CloudPay> cloudPays = cloudPayMapper.selectList(Wrappers.<CloudPay>lambdaQuery()
//				.eq(CloudPay::getEnterpriseId, id)
//				.orderByDesc(BaseEntity::getUpdateTime));
//			//quotaUseDetailsVos = cloudPays.stream().map(cloudPay -> {
//			for (CloudPay cloudPay : cloudPays) {
//				QuotaUseVo details = new QuotaUseVo();
//				details.setUpdateTime(cloudPay.getUpdateTime());
//				details.setAmount(cloudPay.getAmount());
//				String status = CloudPayEnum.getAssetsValueByKey(cloudPay.getStatus());
//				details.setStatus(status);
//				//根据状态判断额度正负
//				List<Integer> list = Func.toIntList("1,2,4,5,6,7");
//				if (list.contains(cloudPay.getStatus())) {
//					details.setSign(-1);
//				} else {
//					details.setSign(1);
//				}
//				quotaUseDetailsVos.add(details);
//			}
            //	return details;
            //	}).collect(Collectors.toList());

        } else {

            List<SalesContract> salesContracts = salesContractMapper.selectList(Wrappers.<SalesContract>lambdaQuery()
                    .eq(SalesContract::getCompanyLowerId, enterpriseId)
                    .eq(SalesContract::getGoodsId, goodsId));

            List<Integer> list = Func.toIntList("1,3,4,5");
            for (SalesContract salesContract : salesContracts) {
                QuotaUseVo detailsVo = new QuotaUseVo();
                BigDecimal effectiveAmount = salesContract.getEffectiveAmount();
                BigDecimal loanable = salesContract.getLoanable();
                BigDecimal divide = (loanable == null ? BigDecimal.ZERO : loanable).divide(new BigDecimal(100));
                detailsVo.setAmount(effectiveAmount.multiply(divide));
                detailsVo.setUpdateTime(salesContract.getUpdateTime());
                detailsVo.setStatus(TradeBackGroundEnum.PROOF_STATUS.getAssetsValueByKey(salesContract.getProofStatus()));

                if (list.contains(salesContract.getProofStatus())) {
                    detailsVo.setSign(-1);
                } else {
                    detailsVo.setSign(1);
                }
                quotaUseDetailsVos.add(detailsVo);
            }

            /*quotaUseDetailsVos = salesContracts.stream().map(salesContract -> {
                QuotaUseVo detailsVo = new QuotaUseVo();
                BigDecimal effectiveAmount = salesContract.getEffectiveAmount();
                BigDecimal divide = salesContract.getLoanable().divide(new BigDecimal(100));

                detailsVo.setAmount(effectiveAmount.multiply(divide));
                detailsVo.setUpdateTime(salesContract.getUpdateTime());
                detailsVo.setStatus(TradeBackGroundEnum.PROOF_STATUS.getAssetsValueByKey(salesContract.getProofStatus()));
                List<Integer> list = Func.toIntList("1,3,4,5");
                if (list.contains(salesContract.getProofStatus())) {
                    detailsVo.setSign(-1);
                } else {
                    detailsVo.setSign(1);
                }
                return detailsVo;
            }).collect(Collectors.toList());*/
        }
        vo.setEnterpriseName(user.getName());
        vo.setQuotaHistoryList(quotaHistories);
        vo.setQuotaUseDetailList(quotaUseDetailsVos);
        return vo;
    }


    /**
     * 查询融资用户生效中的客户产品
     *
     * @param userIds
     * @return
     */
    private List<EnterpriseQuota> listValidFinanceEntValidByUserIds(List<Long> userIds) {
        return list(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.VALID.getCode())
                .in(EnterpriseQuota::getEnterpriseId, userIds)
                .eq(EnterpriseQuota::getEnterpriseType, EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode()));
    }

    /**
     * 产品解冻申请(应收产品解冻申请)
     *
     * @param receiveUnfreezeApply
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveUnfreezeApply(ReceiveUnfreezeApplyDTO receiveUnfreezeApply) {
        // 流程编号
        String processNo = receiveUnfreezeApply.getProcessNo();
        // 产品类型
        Integer goodsType = receiveUnfreezeApply.getGoodsType();
        // 产品id
        Long goodsId = receiveUnfreezeApply.getGoodsId();
        // 流程类型
        Integer processType = receiveUnfreezeApply.getProcessType();
        // 企业类型
        Integer enterpriseType = receiveUnfreezeApply.getEnterpriseType();
        // userId
        Long userId = receiveUnfreezeApply.getUserId();
        // 客户产品id
        Long customerGoodsId = receiveUnfreezeApply.getCustomerGoodsId();
        // 流程进度id
        Long progressId = receiveUnfreezeApply.getProgressId();

        // 检查是否可提交
        BusinessProcessProgress businessProcessProgress = SpringUtil.getBean(IBusinessProcessProgressService.class)
                .getValidByBusinessIdAndType(customerGoodsId, processType, userId);
        if (!canCommitNoContainInvalid(businessProcessProgress)) {
            throw new ServiceException("请勿重复提交申请流程");
        }

        //获取客户产品
        CustomerGoodsVO customerGoods = customerGoodsService.getCustomerGoodsVoById(customerGoodsId);
        if (ObjectUtil.isEmpty(customerGoods) || !CustomerGoodsEnum.FROZEN.getCode().equals(customerGoods.getStatus())) {
            throw new ServiceException("非冻结状态不可进行解冻");
        }

        if (!loanManageOverdueService.canUnRunFrozen(customerGoods)) {
            throw new ServiceException("请先进行逾期还款");
        }

        // 检查合同签署
        List<String> contractIdList = receiveUnfreezeApply.getContractIdList();
        // 检查合同是否签署，并设置流程编号
        checkContractSign(contractIdList, receiveUnfreezeApply.getUserId());

        // 检查流程变量
        // 获取客户资料 服务类
        ICustomerMaterialService customerMaterialService = SpringUtil.getBean(ICustomerMaterialService.class);
        // 定义一个Map，用来组装数据
        Map<String, Object> variables = Maps.newHashMap();
        // 流程编号
        variables.put(WfProcessConstant.PROCESS_NO, processNo);
        // 产品id
        variables.put(ProcessConstant.BUSINESS_ID, goodsId);
        // 流程类型
        variables.put(ProcessConstant.PROCESS_TYPE, processType);
        // 登录用户ID
        variables.put(ProcessConstant.CUSTOMER_ID, MyAuthUtil.getCustomerId());
        // 后台ID 父级id ,获取的是父企业ID
        variables.put(ProcessConstant.USER_ID, MyAuthUtil.getUserId());
        // 产品类型
        variables.put(ProcessConstant.PRODUCT_TYPE, goodsType);
        // 客户产品id
        variables.put(ProcessConstant.CUSTOMER_GOODS_ID, customerGoodsId);
        // 客户产品信息
        variables.put(ProcessConstant.CUSTOMER_GOODS, customerGoods);
        // (额度id) -> 融资企业额度信息
        variables.put(ProcessConstant.ENTERPRISE_QUOTA, SpringUtil.getBean(IEnterpriseQuotaService.class).getById(customerGoods.getEnterpriseQuotaId()));
        // 企业类型
        variables.put(ProcessConstant.ENTERPRISE_TYPE, enterpriseType);
        // 合同集合
        variables.put(ProcessConstant.CONTRACT_ID, Func.join(contractIdList));
        // 产品id
        variables.put(ProcessConstant.GOODS_ID, goodsId);

        // 获取应收产品的信息
        ICustomerGoodsService goodsService = SpringUtil.getBean(ICustomerGoodsService.class);
        CustomerGoodsVO processGoodsInfo = goodsService.processGoodsInfo(goodsId);
        // 产品表视图实体类
        variables.put(ProcessConstant.PROCESS_GOODS_INFO, processGoodsInfo);
        // 资金方id
        variables.put(ProcessConstant.CAPITAL_ID, processGoodsInfo.getCapitalId());
        // 产品名称
        variables.put(ProcessConstant.GOODS_NAME, processGoodsInfo.getGoodsName());
        // 流程进度id
        variables.put(ProcessConstant.PROGRESS_ID, progressId);

        // 设置资方信息
        settingCapitalVar(variables, customerGoods.getCapitalId(), customerGoods.getId(),
                ProcessTypeEnum.RECEIVE_UNFREEZE_GOODS_APPLY_CAPITAL_OUTER.getCode());
        // 查询企业信息及相关资料(客户资料信息)
        variables.put(ProcessConstant.CUSTOMER_MATERIAL, customerMaterialService.detailByBusinessId(customerGoodsId));
        // 开启流程
        newSubmitProcess(receiveUnfreezeApply, businessProcessProgress.getProcessInstanceId(), variables, ProcessProgressEnum.RECEIVE_UNFREEZE_GOODS_APPLY_APPLYING.getCode());
    }

    protected void newSubmitProcess(ReceiveUnfreezeApplyDTO receiveUnfreezeApply, String processInstanceId, Map<String, Object> variables, Integer processProgress) {
        // 获取流程
        GoodsProcess goodsProcess = getGoodsProcess(receiveUnfreezeApply.getGoodsId(), receiveUnfreezeApply.getProcessType(), productDirector);
        // 业务id
        Long businessId = receiveUnfreezeApply.getCustomerGoodsId();
        // 流程类型
        Integer type = receiveUnfreezeApply.getProcessType();
        // 流程进度
        Integer progress = ProcessProgressEnum.RECEIVE_UNFREEZE_GOODS_APPLY_APPLYING.getCode();
        // 业务进度
        Integer status = ProcessStatusEnum.APPROVING.getCode();
        // 流程实例id判空
        if (StringUtil.isNotBlank(processInstanceId)) {
            // 检查审批人是否为当前用户
            Assert.isTrue(businessProcessService.startByWith(processInstanceId), "审批人不是当前用户");
            // 有流程实例id -> 完成任务
            businessProcessService.completeTaskAndUpdateProcessProgress(processProgress, processInstanceId, variables);
            // 更新数据库
            businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(businessId, progress, type, processInstanceId, MyAuthUtil.getUserId(), status);
        } else {
            // 没有流程实例id -> 提交流程(不包含已作废状态)
            submitOrReCommit(receiveUnfreezeApply, variables, processProgress);
        }
    }

    /**
     * 提交或重提
     *
     * @return
     */
    public String submitOrReCommit(ReceiveUnfreezeApplyDTO receiveUnfreezeApply, Map<String, Object> variables, Integer processProgress) {
        // 获取流程
        GoodsProcess goodsProcess = getGoodsProcess(receiveUnfreezeApply.getGoodsId(), receiveUnfreezeApply.getProcessType(), productDirector);
        // 业务id
        Long businessId = receiveUnfreezeApply.getCustomerGoodsId();
        // 流程类型
        Integer type = receiveUnfreezeApply.getProcessType();
        // 流程进度
        Integer progress = ProcessProgressEnum.RECEIVE_UNFREEZE_GOODS_APPLY_APPLYING.getCode();
        // 业务进度
        Integer status = ProcessStatusEnum.APPROVING.getCode();
        /**
         * 提交或重提（startProcess）
         * 判断该业务流程id是否存在
         * 存在则完成任务->修改流程状态和进度
         * 不存在就开启一个业务流程
         * 开启流程 默认提交完成任务
         */
        if(ObjectUtil.isEmpty(goodsProcess)){
            throw new ServiceException("未配置对应流程,请联系管理员进行操作");
        }

        String processInstanceId = businessProcessService.startOrSubmit(goodsProcess.getProcessKey(), receiveUnfreezeApply.getProcessInstanceId(), variables, status);
        // 更新数据库
         businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(businessId, progress, type, processInstanceId, MyAuthUtil.getUserId(), status);
        // 返回一个流程实例id
        return processInstanceId;
    }

    /**
     * 获取流程配置信息
     *
     * @param goodsId         产品id
     * @param processType     产品类型
     * @param productDirector 产品操作者
     * @return
     */
    public GoodsProcess getGoodsProcess(Long goodsId, Integer processType, ProductDirector productDirector) {
        LambdaQueryWrapper<GoodsProcess> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        GoodsProcess config = productDirector.getConfig(goodsId, GoodsEnum.PRODUCT_CONFIG_PROCESS.getCode(), GoodsProcess.class,
                lambdaQueryWrapper.eq(GoodsProcess::getProcessType, processType));
        return config;
    }


    /**
     * 设置资方变量
     *
     * @param variables
     * @param capitalId
     * @param customerGoodsId
     * @param processType
     */
    private void settingCapitalVar(Map<String, Object> variables, Long capitalId, Long customerGoodsId, Integer processType) {

        // 查询是否接入资方 接入情况
        boolean capitalReal = hasCapitalReal(capitalId);
        if (capitalReal) {
            // 开启一条外部资方的审批进度
            Long capitalOuterBusinessProcessId = openOtherCapitalApplyBusinessProcess(variables, customerGoodsId, processType);
            variables.put(ProcessConstant.FROM_EDIT, true);
            variables.put(ProcessConstant.HAS_CAPITAL, true);
            variables.put(ProcessConstant.OTHER_APPLY_BUSINESS_ID, capitalOuterBusinessProcessId);
        } else {
            variables.put(ProcessConstant.FROM_EDIT, false);
            variables.put(ProcessConstant.HAS_CAPITAL, false);
        }
    }

    /**
     * 开启一条资方审批进度流程
     *
     * @param variables
     * @param customerGoodsId
     * @param processType
     * @return
     */
    private Long openOtherCapitalApplyBusinessProcess(Map<String, Object> variables, Long customerGoodsId, Integer processType) {
        BusinessProcessProgress businessProcessProgress = businessProcessProgressService.saveBusinessProcessProgressUnContainInvalid(customerGoodsId, null, processType, null);
        return businessProcessProgress.getId();
    }

    /**
     * 是否接入资方
     *
     * @param capitalId
     * @return
     */
    private Boolean hasCapitalReal(Long capitalId) {
        return false;
    }


    /**
     * 检查合同是否签署，并设置流程编号
     *
     * @param contractIdList 合同id
     * @param userId         流程编号
     */
    protected void checkContractSign(List<String> contractIdList, Long userId) {
        //Assert.isTrue(!CollectionUtils.isEmpty(contractIdList),"合同id不能为空");
        if (!CollectionUtils.isEmpty(contractIdList)) {
            contractService.checkContractIsSign(contractIdList, userId);
        }
    }

    /**
     * 判断冻结流程是否可提交 不包含作废
     *
     * @param businessProcessProgress
     */
    protected Boolean canCommitNoContainInvalid(BusinessProcessProgress businessProcessProgress) {
        Integer status = businessProcessProgress.getStatus();
        // 如果业务是驳回或者流程开启，返回true
        if (ProcessStatusEnum.REJECT.getCode() == status || ProcessStatusEnum.PROCESS_OPEN.getCode() == status) {
            return true;
        }
        return false;
    }

    /**
     * 解冻额度-业务审批终止
     *
     * @param processInstanceId
     * @param variables
     */
    @Override
    public void handlerUnfreezeGoodsApplyProcessTerminate(String processInstanceId, Map<String, Object> variables) {
        // 获取客户产品id
        Long customerGoodsId = (Long) variables.get(ProcessConstant.CUSTOMER_GOODS_ID);
        // userId
        Long userId = (Long) variables.get(ProcessConstant.USER_ID);
        // 流程进度type
        Integer processType = (Integer) variables.get(ProcessConstant.PROCESS_TYPE);
        // 合同作废
        // 获取合同id
        String contractIds = (String) variables.get(ProcessConstant.CONTRACT_ID);
        if (StringUtil.isNotBlank(contractIds)) {
            // 合同id不为空 -> 撤销合同
            SpringUtil.getBean(IContractService.class).cancelContracts(Func.toStrList(contractIds));
        }
        // 更新流程进度 为终止
        businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(customerGoodsId, ProcessProgressEnum.RECEIVE_UNFREEZE_GOODS_APPLY_FINISH.getCode(),
                processType, processInstanceId, userId, ProcessStatusEnum.TERMINAL.getCode());

        // 通知风控系统关闭
        Long ratingRecordId = (Long) variables.get(org.springblade.common.constant.ProcessConstant.RATING_RECORD_ID);
        IRiskmanaApplyService riskmanaApplyService = SpringUtil.getBean(IRiskmanaApplyService.class);
        riskmanaApplyService.terminateApplyQuota(ratingRecordId, processInstanceId);
    }

    /**
     * 额度解冻-业务审批通过
     *
     * @param processInstanceId
     * @param variables
     */
    @Override
    public void handlerUnfreezeGoodsApplyProcessCompleted(String processInstanceId, Map<String, Object> variables) {
        // 企业征信信息 服务类
        IFrontCreditInformationService frontCreditInformationService = SpringUtil.getBean(IFrontCreditInformationService.class);
        // 企业价值分析 服务类
        IFrontValueService frontValueService = SpringUtil.getBean(IFrontValueService.class);
        // 资金方额度信息 服务类
        ICusCapitalQuotaService cusCapitalQuotaService = SpringUtil.getBean(ICusCapitalQuotaService.class);
        // 风险应用 服务
        IRiskmanaApplyService applyService = SpringUtil.getBean(IRiskmanaApplyService.class);
        // 获取客户产品id
        Long customerGoodsId = (Long) variables.get(ProcessConstant.CUSTOMER_GOODS_ID);
        // 获取userId
        Long userId = (Long) variables.get(ProcessConstant.USER_ID);
        // 获取流程进度type
        Integer processType = (Integer) variables.get(ProcessConstant.PROCESS_TYPE);
        // 客户产品id -> 客户产品信息实体类
        CustomerGoods customerGoods = customerGoodsService.getById(customerGoodsId);
        // 获取资方id
        Long capitalId = (Long) variables.get(ProcessConstant.CAPITAL_ID);
        // 获取 企业征信信息
        FrontCreditInformation frontCreditInformation = JSONUtil.toBean(JSONUtil.toJsonStr(variables.get("frontCreditInformation")), FrontCreditInformation.class);
        // 获取 企业价值分析信息
        FrontValue frontValue = JSONUtil.toBean(JSONUtil.toJsonStr(variables.get("frontValue")), FrontValue.class);
        // 获取风控信息
        FinalApproveAmount finalApproveAmount = JSON.parseObject(JSON.toJSONString(variables.get(ProcessConstant.FINAL_APPROVE_AMOUNT)), FinalApproveAmount.class);


        // 更新流程进度
        businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(customerGoodsId, null, processType, processInstanceId, userId, ProcessStatusEnum.FINISH.getCode());
        // 更新价值分析信息 recordId(返回历史变更表ID)
        Long recordId = frontValueService.updateFrontValue(frontValue, userId);
        // 更新征信信息
        frontCreditInformationService.updateFrontCreditInformation(frontCreditInformation, userId, recordId);
        // 最终批额
        BigDecimal finalAmountWanToYuan = finalApproveAmount.getFinalAmount().multiply(BigDecimal.valueOf(10000));
        // 获取旧的授信额度
        BigDecimal creditAmount = getById(customerGoods.getEnterpriseQuotaId()).getCreditAmount();
        // 资方修改的额度   (新额度 - 旧额度) ->  小于0 资方增加   大于0 资方减少
        BigDecimal newCreditAmount = finalAmountWanToYuan.subtract(creditAmount);
        // (资方id,资方扣减额度) -> 资方扣减额度
        cusCapitalQuotaService.subtractQuota(capitalId, newCreditAmount);

        // 更新客户产品状态 -> 可融资
        customerGoodsService.updateStatus(customerGoodsId, CustomerGoodsEnum.FINANCING.getCode());

        // 企业额度信息（最终批额，客户产品信息，风控信息）
        unFreezeQuota(finalAmountWanToYuan, customerGoods, finalApproveAmount, variables, newCreditAmount);

        // 获取ratingRecord(评级记录id)
        Long ratingRecordId = (Long) variables.get(ProcessConstant.RATING_RECORD_ID);
        // 通知风控系统通过
        applyService.passApplyQuota(ratingRecordId, finalApproveAmount, processInstanceId);
    }

    @Override
    public List<EnterpriseQuotaExcel> export(EnterpriseQuotaDTO enterpriseQuotaDTO) {
        List<EnterpriseQuotaVO> list = baseMapper.selectExportList(enterpriseQuotaDTO);
        //添加已使用循环额度字段
        for (EnterpriseQuotaVO vo : list) {
            if(vo.getQuotaType().equals(QuotaTypeEnum.RECYCLING.getCode())) {
                BigDecimal circleQuota = vo.getUsedAmount();
                //前端展示用 根据循环类型展示 已用额度 或 已用循环额度
                vo.setUsedCircleQuota(circleQuota);
                vo.setUsedAmount(BigDecimal.ZERO);
            }
        }
        List<EnterpriseQuotaExcel> enterpriseQuotaExcels = cn.hutool.core.bean.BeanUtil.copyToList(list, EnterpriseQuotaExcel.class);
        return enterpriseQuotaExcels;
    }

    /**
     * 额度解冻 并赋予新额度
     *
     * @param finalAmountWanToYuan 最终批额
     * @param customerGoods
     * @param finalApproveAmount
     */
    private void unFreezeQuota(BigDecimal finalAmountWanToYuan, CustomerGoods customerGoods, FinalApproveAmount finalApproveAmount, Map<String, Object> variables, BigDecimal newCreditAmount) {
        // 企业额度信息实体类
        EnterpriseQuota enterpriseQuota = getById(customerGoods.getEnterpriseQuotaId());
        // 旧可用额度
        BigDecimal availableAmount = enterpriseQuota.getAvailableAmount();
        // 申请中金额
        BigDecimal applyAmount = enterpriseQuota.getApplyAmount();

        // 将风控审批信息 更新到 企业额度信息
        BeanUtil.copyProperties(finalApproveAmount, enterpriseQuota, "finalAmount");
        // 修改状态为 生效中
        enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.VALID.getCode());
        // 新的授信额度(最终批额)
        enterpriseQuota.setCreditAmount(finalAmountWanToYuan);
        // 修改可用额度（新可用额度） 差值为零时，不用做修改
        if(newCreditAmount.compareTo(BigDecimal.ZERO) > 0){
            // 当差值大于零时，新授信额度比旧授信额度大，增加可用额度，无需考虑申请中额度、贷中额度、额度类型
            enterpriseQuota.setAvailableAmount(availableAmount.add(newCreditAmount));
        } else if (newCreditAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 当差值小于零时，减少可用额度，新授信额度比旧授信额度小，需要考虑申请中额度、贷中额度、额度类型
            // 申请中额度比新额度要大 || 可用额度不够扣掉差值
            if ((finalAmountWanToYuan.subtract(applyAmount)).compareTo(BigDecimal.ZERO) < 0 || (availableAmount.add(newCreditAmount)).compareTo(BigDecimal.ZERO) < 0) {
                /**
                 * 1.循环
                 * 申请中额度比新授信大 -> 减少差值(增加授信)/处理申请中额度
                 * 可用额度不够扣差值 -> 还款/处理申请中额度/减少差值(增加授信)
                 * 2.一次性
                 * 申请中额度比新授信大 -> 减少差值(增加授信)/终止申请中额度
                 * 可用额度不够扣差值 -> 处理申请中额度/减少差值(增加授信)
                 */
                throw new ServiceException("授信额度或可用额度不足, 请增加授信额度/处理申请中额度/还款");
            }
            // 减少可用额度（可用额度+(-差值)）
            enterpriseQuota.setAvailableAmount(availableAmount.add(newCreditAmount));
        }
        // 更新企业额度信息
        updateById(enterpriseQuota);
        // 记录解冻信息
        quotaHistoryService.saveHistory(enterpriseQuota, QuotaHistoryEnum.UNFREEZE_QUOTA.getCode());
    }

    /**
     * 根据产品列表、企业类型、企业id 获取额度列表
     * @param goodsIdList
     * @param enterpriseType
     * @param enterpriseId
     * @return
     */
    @Override
    public List<EnterpriseQuota> getListByGoodsIdsAndEnterpriseTypeAndgEnterpriseId(List<Long> goodsIdList, Integer enterpriseType, Long enterpriseId) {
        return list(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getEnterpriseId, enterpriseId)
                .eq(EnterpriseQuota::getEnterpriseType, enterpriseType)
                .in(EnterpriseQuota::getGoodsId, goodsIdList)
                .eq(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.VALID.getCode()));
    }
}

