/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.customer.entity.AdditionalInfoLog;
import org.springblade.customer.mapper.AdditionalInfoLogMapper;
import org.springblade.customer.service.IAdditionalInfoLogService;
import org.springblade.customer.vo.AdditionalInfoLogVO;
import org.springframework.stereotype.Service;

/**
 * 补充资料历史记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
public class AdditionalInfoLogServiceImpl extends BaseServiceImpl<AdditionalInfoLogMapper, AdditionalInfoLog> implements IAdditionalInfoLogService {

	@Override
	public IPage<AdditionalInfoLogVO> selectAdditionalInfoLogPage(IPage<AdditionalInfoLogVO> page, AdditionalInfoLogVO additionalInfoLog) {
		return page.setRecords(baseMapper.selectAdditionalInfoLogPage(page, additionalInfoLog));
	}

}
