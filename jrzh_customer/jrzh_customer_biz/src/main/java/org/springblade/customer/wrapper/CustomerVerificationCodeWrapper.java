package org.springblade.customer.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.CustomerVerificationCode;
import org.springblade.customer.vo.CustomerVerificationCodeVO;

import java.util.Objects;

/**
 * 企业邀请用户设置角色包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
public class CustomerVerificationCodeWrapper extends BaseEntityWrapper<CustomerVerificationCode, CustomerVerificationCodeVO> {

    public static CustomerVerificationCodeWrapper build() {
        return new CustomerVerificationCodeWrapper();
    }

    @Override
    public CustomerVerificationCodeVO entityVO(CustomerVerificationCode CustomerVerificationCode) {
        CustomerVerificationCodeVO CustomerVerificationCodeVO = Objects.requireNonNull(BeanUtil.copy(CustomerVerificationCode, CustomerVerificationCodeVO.class));

        //User createUser = UserCache.getUser(  CustomerVerificationCode.getCreateUser());
        //User updateUser = UserCache.getUser(  CustomerVerificationCode.getUpdateUser());
        //  CustomerVerificationCodeVO.setCreateUserName(createUser.getName());
        //  CustomerVerificationCodeVO.setUpdateUserName(updateUser.getName());

        return CustomerVerificationCodeVO;
    }
}
