package org.springblade.customer.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import io.jsonwebtoken.lang.Collections;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.task.Comment;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CustomerBusinessInvoiceEnum;
import org.springblade.common.enums.ProcessProgressEnum;
import org.springblade.common.enums.ProcessStatusEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.dto.SalesContractProcessDTO;
import org.springblade.customer.entity.*;
import org.springblade.customer.enums.TradeBackGroundEnum;
import org.springblade.customer.service.*;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.customer.vo.SalesContractVO;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("lowerSalesContractProgressListener")
@AllArgsConstructor
public class LowerSalesContractProgressListener implements ExecutionListener {
    private final ISalesContractService salesContractService;
    private final IProcessSalesContractService processSalesContractService;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final ICustomerGoodsTradeBackgroundService goodsTradeBackgroundService;
    private final TaskService taskService;
    private final ITradeBackgroundService tradeBackgroundService;
    private final ICustomerBusinessInvoiceService invoiceService;

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        // 获取流程变量 判断流程变量 更新流程进度 为通过 还是中止
        Boolean variable = delegateExecution.getVariable(WfProcessConstant.PASS_KEY, Boolean.class);
        Object variable2 = delegateExecution.getVariable(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE);
        //根据流程id找合同流程表
        String processInstanceId = delegateExecution.getProcessInstanceId();
        ProcessSalesContract processSale = processSalesContractService.getByProcessId(processInstanceId);
        //设置终审人与最新评语
        List<Comment> processInstanceComments = taskService.getProcessInstanceComments(processInstanceId);
        if (CollectionUtil.isNotEmpty(processInstanceComments)) {
            processSale.setComment(processInstanceComments.get(0).getFullMessage());
            processSale.setFinalAssignee(AuthUtil.getUserId());
        }
        Map<String, Object> variables = delegateExecution.getVariables();
        SalesContractProcessDTO salesContractProcessDTO = JSONUtil.toBean(JSONUtil.parseObj(delegateExecution.getVariable("salesFormData")), SalesContractProcessDTO.class);
        List<SalesContractVO> list = salesContractProcessDTO.getSalesContractList();
        List<SalesContract> salesContracts = BeanUtil.copy(list, SalesContract.class);
        //获取背景关联产品信息-打折率
        Long backId = Func.toLong(variables.get("backId").toString());
        TradeBackground background = tradeBackgroundService.getById(backId);
        CustomerGoodsTradeBackground goodsBackGround = goodsTradeBackgroundService.getByBackId(backId);
        Long customerId = background.getCompanyLowerId();
        Long goodsId = goodsBackGround.getGoodsId();
        EnterpriseQuotaVO quota = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsId, 2, customerId);
        BigDecimal discount = quota.getLoanable();
        //通过
        if (Func.isNotEmpty(variable) && Boolean.TRUE.equals(variable) && ObjectUtil.isEmpty(variable2)) {
            //更新合同流程表
            passSaleContract(processSale);
            //扣取额度
            BigDecimal subAmount = getSubAmount(salesContracts);
            synchronized (this) {
                if (subAmount.compareTo(BigDecimal.ZERO) > 0) {
                    enterpriseQuotaService.subtractQuota(goodsId, customerId, 2, subAmount);
                }
            }
            for (SalesContract salesContract : salesContracts) {
                //通过处理
                handlePass(salesContract, discount);
            }

        } else if (Func.isNotEmpty(variable2)) {
            //更新合同流程表
            rejectSaleContract(processSale);
            //终止处理
            handleTerminate(salesContracts);
        } else {
            throw new FlowableException("录入错误，请联系管理员");
        }
        salesContractService.updateBatchById(salesContracts);
        //发票处理
        handleInvoice(salesContracts);
    }

    private void handleInvoice(List<SalesContract> salesContracts) {
        Map<Long, Integer> invoiceSalesMap = salesContracts.stream().filter(e -> ObjectUtil.isNotEmpty(e.getInvoiceId())).collect(Collectors.toMap(SalesContract::getInvoiceId, SalesContract::getConfirmStatus));
        if (CollectionUtil.isEmpty(invoiceSalesMap)) {
            return;
        }
        List<CustomerBusinessInvoice> invoiceList = invoiceService.listByIds(new ArrayList<Long>(invoiceSalesMap.keySet()));
        for (CustomerBusinessInvoice invoice : invoiceList) {
            Integer confirmStatus = invoiceSalesMap.get(invoice.getId());
            Integer confirmed = TradeBackGroundEnum.CONFIRM_STATUS.CONFIRM.getStatus();
            invoice.setUploadStatus(confirmStatus.equals(confirmed) ? CustomerBusinessInvoiceEnum.UPLOADED.getCode() : CustomerBusinessInvoiceEnum.UN_UPLOAD.getCode());
        }
        invoiceService.saveOrUpdateBatch(invoiceList);
    }

    private BigDecimal getSubAmount(List<SalesContract> salesContracts) {
        if (Collections.isEmpty(salesContracts)) {
            return BigDecimal.ZERO;
        }
        List<SalesContract> collect = salesContracts.stream().filter(e ->
                TradeBackGroundEnum.CONFIRM_STATUS.CONFIRM.getStatus().equals(e.getConfirmStatus())).collect(Collectors.toList());
        if (Collections.isEmpty(collect)) {
            return BigDecimal.ZERO;
        }
        return collect.stream().map(SalesContract::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 通过处理
     */
    private void handlePass(SalesContract salesContract, BigDecimal discount) {
        salesContract.setLoanable(discount);
        //判断状态是否确权成功 确权成功将凭证状态改为可使用
        if (TradeBackGroundEnum.CONFIRM_STATUS.CONFIRM.getStatus().equals(salesContract.getConfirmStatus())) {
            //设置可融资额度=有效金额*产品打折率
            salesContract.setFinancingAvailableAmount(salesContract.getAccountAmount().multiply(discount.divide(new BigDecimal(100))).setScale(2, CommonConstant.NUMBER_STRATEGY));
            salesContract.setProofStatus(TradeBackGroundEnum.PROOF_STATUS.USEABLE.getStatus());
            salesContract.setConfirmStatus(TradeBackGroundEnum.CONFIRM_STATUS.CONFIRM.getStatus());
        } else {
            //未勾选操作的也确权失败
            salesContract.setConfirmStatus(TradeBackGroundEnum.CONFIRM_STATUS.WAIT_CONFIRM.getStatus());
            salesContract.setProofStatus(TradeBackGroundEnum.PROOF_STATUS.INVALID.getStatus());
        }
    }

    /**
     * 终止处理
     */
    private void handleTerminate(List<SalesContract> salesContracts) {
        salesContracts.forEach(e -> {
            //设置业务系统 结点为填用状态
            e.setConfirmStatus(TradeBackGroundEnum.CONFIRM_STATUS.WAIT_CONFIRM.getStatus());
            e.setProofStatus(TradeBackGroundEnum.PROOF_STATUS.INVALID.getStatus());
        });
    }

    /**
     * 拒绝确权
     *
     * @param processSale 业务流程
     */
    private void rejectSaleContract(ProcessSalesContract processSale) {
        if (ObjectUtil.isNotEmpty(processSale)) {
            processSale.setProcess(ProcessProgressEnum.APPLY_REJECT.getCode());
            processSale.setStatus(ProcessStatusEnum.TERMINAL.getCode());
            processSalesContractService.saveOrUpdate(processSale);
        }
    }

    /**
     * 成功确权
     *
     * @param processSale 业务流程
     */
    private void passSaleContract(ProcessSalesContract processSale) {
        if (ObjectUtil.isNotEmpty(processSale)) {
            processSale.setProcess(ProcessProgressEnum.APPLY_CONFIRM.getCode());
            processSale.setStatus(ProcessStatusEnum.FINISH.getCode());
            processSalesContractService.saveOrUpdate(processSale);
        }
    }
}
