/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.entity.VerifyPhone;
import org.springblade.customer.vo.VerifyPhoneVO;

/**
 * 校验当前手机号码 服务类
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
public interface IVerifyPhoneService extends BaseService<VerifyPhone> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param verifyPhone
	 * @return IPage<VerifyPhoneVO>
	 */
	IPage<VerifyPhoneVO> selectVerifyPhonePage(IPage<VerifyPhoneVO> page, VerifyPhoneVO verifyPhone);

	/***
	 * 存储前手机号码与手机号
	 * @param verifyPhone
	 * @return Boolean
	 */
	Boolean saveVerifyPhone(VerifyPhone verifyPhone);
}
