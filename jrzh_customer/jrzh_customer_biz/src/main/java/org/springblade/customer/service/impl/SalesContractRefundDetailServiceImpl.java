/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.entity.SalesContract;
import org.springblade.customer.entity.SalesContractRefundDetail;
import org.springblade.customer.enums.TradeBackGroundEnum;
import org.springblade.customer.mapper.SalesContractRefundDetailMapper;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.ISalesContractRefundDetailService;
import org.springblade.customer.service.ISalesContractService;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.vo.SalesContractRefundDetailVO;
import org.springblade.customer.wrapper.SalesContractRefundDetailWrapper;
import org.springblade.system.entity.User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 应收账款回款信息 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Service
@RequiredArgsConstructor
public class SalesContractRefundDetailServiceImpl extends BaseServiceImpl<SalesContractRefundDetailMapper, SalesContractRefundDetail> implements ISalesContractRefundDetailService {
    private final ISalesContractService salesContractService;
    private final ICustomerGoodsService customerGoodsService;

    @Override
    public IPage<SalesContractRefundDetailVO> selectSalesContractRefundDetailPage(IPage<SalesContractRefundDetailVO> page, SalesContractRefundDetailVO salesContractRefundDetail) {
        return page.setRecords(baseMapper.selectSalesContractRefundDetailPage(page, salesContractRefundDetail));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDetail(SalesContractRefundDetail detail) {
        Long saleContractId = detail.getSaleContractId();
        //校验填写信息
        check(detail);
        boolean success = false;
        SalesContract salesContract = null;
        //还款
        BigDecimal refundAmount = detail.getRefundAmount();
        synchronized (this) {
            salesContract = salesContractService.getById(saleContractId);

            //增加应收账款还款
            success = addRefundAmount(salesContract, refundAmount, detail.getRefundTime());
        }
        if (success) {
            //添加回款人
            detail.setCustomerId(salesContract.getCompanyLowerId());
            //添加还款信息
            save(detail);
            customerGoodsService.returnCoreEnterpriseQuota(salesContract.getGoodsId(), salesContract.getCompanyLowerId(), salesContract.getRefund(), detail.getRefundAmount(), salesContract.getAccountAmount());
        } else {
            throw new ServiceException("还款失败,请联系管理员");
        }
        return true;
    }

    @Override
    public List<SalesContractRefundDetail> listBySaleContractId(Long saleContractId) {
        return list(Wrappers.<SalesContractRefundDetail>lambdaQuery().eq(SalesContractRefundDetail::getSaleContractId, saleContractId));
    }

    @Override
    public List<SalesContractRefundDetailVO> listBySaleContractVoId(Long saleContractId) {
        List<SalesContractRefundDetail> list = listBySaleContractId(saleContractId);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<SalesContractRefundDetailVO> listVO = SalesContractRefundDetailWrapper.build().listVO(list);
        listVO.forEach(e -> {
            User user = CustomerUserCache.getUserById(e.getOperatorId());
            e.setOperator(ObjectUtil.isNotEmpty(user) ? user.getName() : "");
        });
        return listVO;
    }

    @Override
    public List<SalesContractRefundDetail> listBySaleContractIds(List<Long> saleIds) {
        return list(Wrappers.<SalesContractRefundDetail>lambdaQuery().in(SalesContractRefundDetail::getSaleContractId, saleIds));
    }

    /**
     * 增加回款金额
     *
     * @param salesContract 应收账款
     * @param refundAmount  回款金额
     * @param refundTime    回款日期 记录最新一次
     * @return
     */
    private boolean addRefundAmount(SalesContract salesContract, BigDecimal refundAmount, LocalDate refundTime) {
        //还款后的金额
        BigDecimal refunded = salesContract.getRefund().add(refundAmount);
        salesContract.setRefundTime(refundTime);
        if (TradeBackGroundEnum.SETTLE_STATUS.SETTLED.getStatus().equals(salesContract.getSettle())
                || salesContract.getAccountAmount().compareTo(refunded) >= 0) {
            salesContract.setRefund(refunded);
            //判断结清 改结清状态
            if (refunded.compareTo(salesContract.getAccountAmount()) >= 0) {
                salesContract.setProofStatus(TradeBackGroundEnum.PROOF_STATUS.SETTLE.getStatus());
                salesContract.setSettle(TradeBackGroundEnum.SETTLE_STATUS.SETTLED.getStatus());
            }
            return salesContractService.updateById(salesContract);
        } else {
            throw new ServiceException("还款金额大于未还金额或已结清");
        }
    }

    private void check(SalesContractRefundDetail salesContractRefundDetail) {
        int count = salesContractService.count(Wrappers.<SalesContract>lambdaQuery().eq(SalesContract::getId, salesContractRefundDetail.getSaleContractId()));
        if (count <= 0 || ObjectUtil.isNotEmpty(salesContractRefundDetail.getId())) {
            throw new ServiceException(ResultCode.PARAM_VALID_ERROR);
        }
    }
}
