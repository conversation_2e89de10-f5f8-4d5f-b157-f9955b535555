/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.customer.entity.CreditAccountLog;
import org.springblade.customer.mapper.CreditAccountLogMapper;
import org.springblade.customer.service.ICreditAccountLogService;
import org.springblade.customer.vo.CreditAccountLogVO;
import org.springframework.stereotype.Service;

/**
 * 回款账户变更历史 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Service
public class CreditAccountLogServiceImpl extends BaseServiceImpl<CreditAccountLogMapper, CreditAccountLog> implements ICreditAccountLogService {

	@Override
	public IPage<CreditAccountLogVO> selectCreditAccountLogPage(IPage<CreditAccountLogVO> page, CreditAccountLogVO creditAccountLog) {
		return page.setRecords(baseMapper.selectCreditAccountLogPage(page, creditAccountLog));
	}

}
