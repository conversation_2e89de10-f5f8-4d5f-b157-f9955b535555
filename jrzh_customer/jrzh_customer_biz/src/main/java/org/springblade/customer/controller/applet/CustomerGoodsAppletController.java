package org.springblade.customer.controller.applet;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CustomerGoodsEnum;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.TradeBackgroundVO;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 客户产品 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_APPLET + "/customerGoods")
@Api(value = "客户产品", tags = "客户产品接口")
public class CustomerGoodsAppletController {

	private final ICustomerGoodsService customerGoodsService;

//	@PostMapping("/save")
//	@ApiOperation("保存我的产品和贸易背景")
//	public R<Long> save(@Valid @RequestBody CustomerGoodsDTO customerGoodsDTO) {
//		return R.data(customerGoodsService.saveCustomerGoods(customerGoodsDTO));
//	}

	@GetMapping("/selectGoodsTradeBackground")
	@ApiOperation("查询产品绑定贸易背景")
	public R<List<TradeBackgroundVO>> selectGoodsTradeBackground(@RequestParam Long goodsId) {
		return R.data(customerGoodsService.selectGoodsTradeBackground(goodsId));
	}

	@GetMapping("/goodsId/{goodsId}/status/{status}")
	public R<Boolean> get(@PathVariable("goodsId") Long goodsId, @PathVariable("status") Integer status) {

		Integer count = customerGoodsService.lambdaQuery()
			.eq(CustomerGoods::getGoodsId, goodsId)
			.eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
			.notIn(CustomerGoods::getStatus, Arrays.asList(CustomerGoodsEnum.EXPIRE.getCode(), CustomerGoodsEnum.FAILURE.getCode()))
			.count();
		return R.data(count > 0);
	}
}
