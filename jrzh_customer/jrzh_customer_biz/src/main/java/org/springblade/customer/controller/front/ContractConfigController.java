/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.front;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.contract.dto.ContractConfigDTO;
import org.springblade.modules.contract.entity.ContractConfig;
import org.springblade.modules.contract.service.IContractConfigService;
import org.springblade.modules.contract.vo.ContractConfigVO;
import org.springblade.modules.contract.wrapper.ContractConfigWrapper;
import org.springblade.system.utils.UserUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 合同配置 控制器
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_CONTRACT + CommonConstant.WEB_FRONT + "/contractConfig")
@Api(value = "合同配置", tags = "合同配置接口")
public class ContractConfigController extends BladeController {

    private final IContractConfigService contractConfigService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入contractConfig")
    public R<ContractConfigVO> detail() {
        return R.data(contractConfigService.detail(AuthUtil.getUserId()));
    }

    /**
     * 分页 合同配置
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入contractConfig")
    @PreAuth("hasPermission('blade-contract:contractConfig:list') or hasRole('administrator')")
    public R<IPage<ContractConfigVO>> list(ContractConfig contractConfig, Query query) {
        IPage<ContractConfig> pages = contractConfigService.page(Condition.getPage(query), Condition.getQueryWrapper(contractConfig));
        return R.data(ContractConfigWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 合同配置
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入contractConfig")
    @PreAuth("hasPermission('blade-contract:contractConfig:page') or hasRole('administrator')")
    public R<IPage<ContractConfigVO>> page(ContractConfigVO contractConfig, Query query) {
        IPage<ContractConfigVO> pages = contractConfigService.selectContractConfigPage(Condition.getPage(query), contractConfig);
        return R.data(pages);
    }

    /**
     * 新增 合同配置
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入contractConfig")
    @PreAuth("hasPermission('blade-contract:contractConfig:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody ContractConfig contractConfig) {
        return R.status(contractConfigService.save(contractConfig));
    }

    /**
     * 修改 合同配置
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入contractConfig")
    @PreAuth("hasPermission('blade-contract:contractConfig:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody ContractConfig contractConfig) {
        return R.status(contractConfigService.updateById(contractConfig));
    }

    /**
     * 新增或修改 合同配置
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入contractConfig")
    public R<Boolean> submit(@Valid @RequestBody ContractConfigDTO contractConfig) {
        return R.status(contractConfigService.submit(contractConfig, AuthUtil.getUserId().toString(), UserUtils.getEnterpriseType()));
    }


    /**
     * 删除 合同配置
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('blade-contract:contractConfig:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(contractConfigService.deleteLogic(Func.toLongList(ids)));
    }

}
