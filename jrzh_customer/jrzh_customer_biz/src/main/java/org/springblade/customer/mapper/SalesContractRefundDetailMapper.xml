<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.SalesContractRefundDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="salesContractRefundDetailResultMap" type="org.springblade.customer.entity.SalesContractRefundDetail">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="sale_contract_id" property="saleContractId"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="proof" property="proof"/>
        <result column="customer_id" property="customerId"/>
    </resultMap>


    <select id="selectSalesContractRefundDetailPage" resultMap="salesContractRefundDetailResultMap">
        select * from jrzh_customer_front_sales_contract_refund_detail where is_deleted = 0
    </select>

</mapper>
