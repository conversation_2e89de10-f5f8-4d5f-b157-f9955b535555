/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.Customer;
import org.springblade.customer.entity.CustomerFrontUserType;
import org.springblade.customer.entity.CustomerVerificationCode;
import org.springblade.customer.entity.FinancingRole;
import org.springblade.customer.mapper.CustomerVerificationCodeMapper;
import org.springblade.customer.service.ICustomerFrontUserTypeService;
import org.springblade.customer.service.ICustomerService;
import org.springblade.customer.service.ICustomerVerificationCodeService;
import org.springblade.customer.service.IFinancingRoleService;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerVerificationCodeVO;
import org.springblade.customer.wrapper.CustomerVerificationCodeWrapper;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业邀请用户设置角色 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
@Service
@AllArgsConstructor
public class CustomerVerificationCodeServiceImpl extends BaseServiceImpl<CustomerVerificationCodeMapper, CustomerVerificationCode> implements ICustomerVerificationCodeService {
    private final RemoteUserService remoteUserService;

    @Override
    public IPage<CustomerVerificationCodeVO> selectCustomerVerificationCodePage(IPage<CustomerVerificationCodeVO> page, CustomerVerificationCodeVO customerVerificationCode) {
        return page.setRecords(baseMapper.selectCustomerVerificationCodePage(page, customerVerificationCode));
    }

    @Override
    public CustomerVerificationCodeVO sendInvite(String bladeRole, String inviteesPhone, String inviteesName) throws ParseException {

        List<Long> longs = Func.toLongList(bladeRole);
        List<FinancingRole> list = SpringUtil.getBean(IFinancingRoleService.class).lambdaQuery().eq(FinancingRole::getCompanyId, MyAuthUtil.getCompanyId()).in(FinancingRole::getId, longs).list();
        List<String> collect = list.stream().map(FinancingRole::getRoleAlias).collect(Collectors.toList());
        if (collect.contains("admin")) {
            throw new ServiceException("不可以赋予超级管理员权限!");
        }

        String inviteCode = RandomUtil.randomNumbers(10);
        //获取当前邀请人
        Long customerId = MyAuthUtil.getCustomerId();
        //获取企业ID
        String companyId = MyAuthUtil.getCompanyId();

        //CustomerFrontUserType one = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getType,VerificationCodeSupertube.ENTERPRISE.getCode()).eq(CustomerFrontUserType::getUserId, companyId).one();

        //Long userId = one.getRoleUserId();
        //获取发送人的企业名称
        User user = CustomerUserCache.getUserById(Func.toLong(companyId));
        //企业名称
        String name = user.getName();

        //获取邀请人名称
        Customer one1 = SpringUtil.getBean(ICustomerService.class).lambdaQuery().eq(BaseEntity::getId, customerId).one();
        CustomerVerificationCode customerVerificationCode = new CustomerVerificationCode();
        customerVerificationCode.setCode(inviteCode);
        //新增角色信息
        customerVerificationCode.setRole(bladeRole);
        //发送人的用户ID
        customerVerificationCode.setCustomerId(customerId);
        //发送人的企业ID
        customerVerificationCode.setCompanyId(Long.valueOf(companyId));
        //被邀请人用户姓名
        customerVerificationCode.setInviteesName(inviteesName);
        //被邀请人手机号
        customerVerificationCode.setInviteesPhone(inviteesPhone);
        //邀请人企业名称
        customerVerificationCode.setEnterpriseName(name);
        //邀请人姓名
        customerVerificationCode.setInviteeName(one1.getName());
        //默认为未删除状态
        customerVerificationCode.setStatus(0);
        //开始时间
        //String formatDateTime = DateUtil.formatDateTime(new Date());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = simpleDateFormat.format(new Date());
        Date parse = simpleDateFormat.parse(format);

        customerVerificationCode.setStartTime(parse);
        save(customerVerificationCode);
        Date newDate = cn.hutool.core.date.DateUtil.offset(parse, DateField.DAY_OF_MONTH, 15);
        CustomerVerificationCodeVO customerVerificationCodeVO = CustomerVerificationCodeWrapper.build().entityVO(customerVerificationCode);
        customerVerificationCodeVO.setEndTime(newDate);

        return customerVerificationCodeVO;
    }

    @Override
    public boolean checkInviteCodeIsValid(String code, String frontName) {
        //查询是否有启用数据 0-未删除 1-已删除
        CustomerVerificationCode customerVerificationCode = baseMapper.selectOne(Wrappers.<CustomerVerificationCode>lambdaQuery()
                .eq(CustomerVerificationCode::getCode, code).eq(CustomerVerificationCode::getEnterpriseName, frontName).eq(BaseEntity::getStatus, 0));
        if (StringUtil.isEmpty(customerVerificationCode)) {
            return false;
        }
        Long customerId = MyAuthUtil.getCustomerId();
        Customer customer = SpringUtil.getBean(ICustomerService.class).getById(customerId);
        if (!customerVerificationCode.getInviteesPhone().equals(customer.getPhone())) {
            throw new ServiceException("您的手机号码与邀请信息不匹配!");
        }

        //查找发送人的企业ID
        Long companyId = customerVerificationCode.getCompanyId();
        //根据当前人的企业ID 查找申请过的企业
        CustomerFrontUserType one1 = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getUserId, companyId).eq(CustomerFrontUserType::getCustomerFrontUserId, MyAuthUtil.getCustomerId()).one();

        if (ObjectUtil.isNotEmpty(one1)) {
            throw new ServiceException("已在该企业中!");
        }
        //获取邀请人信息
        CustomerFrontUserType one = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getUserId, companyId).eq(CustomerFrontUserType::getCustomerFrontUserId, customerVerificationCode.getCustomerId()).one();
        //获取指定邀请人 用户ID 可以进行指定的用户ID进行比较 必须是指定的邀请人 才能邀请成功   也可以不进行比较 只要得到这个邀请码 和 公司名称就算能邀请成功
        if (StringUtil.isEmpty(one)) {
            return false;
        }
        User users = CustomerUserCache.getUserById(one.getRoleUserId());
        if (ObjectUtil.isNull(users)) {
            return false;
        }
        if (ObjectUtil.isNull(customerVerificationCode)) {
            return false;
        }
        Date expireTime = DateUtil.plusDays(customerVerificationCode.getStartTime(), 60 * 24 * 15);
        boolean isExpire = DateUtil.now().compareTo(expireTime) >= 0;
        return !isExpire;
    }

    @Override
    public boolean removeInviteCode(String inviteCode, String frontName) {
        //TODO 需要重构
//        List<User> list = SpringUtil.getBean(IUserService.class).lambdaQuery().eq(User::getName, frontName).list();
//        if (CollectionUtils.isEmpty(list)) {
//            return false;
//        }
//        List<Long> collect = list.stream().map(BaseEntity::getId).collect(Collectors.toList());
//        CustomerFrontUserType one = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().in(CustomerFrontUserType::getRoleUserId, collect).eq(CustomerFrontUserType::getType, CustomerTypeEnum.ENTERPRISE.getCode()).one();
//        if (StringUtil.isEmpty(one)) {
//            return false;
//        }
//        //更改状态
//        return update(Wrappers.<CustomerVerificationCode>lambdaUpdate()
//                .eq(CustomerVerificationCode::getCode, inviteCode).eq(CustomerVerificationCode::getCompanyId, one.getUserId()).set(BaseEntity::getStatus, 1));
//
        return true;
    }

    @Override
    public List<CustomerVerificationCodeVO> getList() {
        String companyId = MyAuthUtil.getCompanyId();
        List<CustomerVerificationCode> list = list(Wrappers.<CustomerVerificationCode>lambdaQuery().eq(CustomerVerificationCode::getCompanyId, companyId));
        List<CustomerVerificationCodeVO> customerVerificationCodeVOS = CustomerVerificationCodeWrapper.build().listVO(list);
        return customerVerificationCodeVOS;
    }

}
