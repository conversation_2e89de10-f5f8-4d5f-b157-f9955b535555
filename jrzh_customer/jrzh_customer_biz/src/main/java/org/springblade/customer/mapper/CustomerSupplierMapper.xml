<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerSupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerSupplierResultMap" type="org.springblade.customer.entity.CustomerSupplier">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="supper_code" property="supperCode"/>
        <result column="supper_name" property="supperName"/>
        <result column="deposit_bank_id" property="depositBankId"/>
        <result column="bank_account" property="bankAccount"/>
        <result column="contacts" property="contacts"/>
        <result column="supper_phone" property="supperPhone"/>
        <result column="unified_code" property="unifiedCode"/>
        <result column="tenant_id" property="tenantId"></result>
    </resultMap>


    <select id="selectCustomerSupplierPage" resultMap="customerSupplierResultMap">
        select * from jrzh_customer_supplier where is_deleted = 0
    </select>

</mapper>
