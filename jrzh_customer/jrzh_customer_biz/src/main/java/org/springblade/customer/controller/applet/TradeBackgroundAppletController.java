package org.springblade.customer.controller.applet;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CustomerTypeEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.dto.HeightTradeBackgroundDTO;
import org.springblade.customer.entity.Customer;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.CustomerPersonInfo;
import org.springblade.customer.entity.CustomerSupplier;
import org.springblade.customer.enums.CustomerUserType;
import org.springblade.customer.enums.TradeBackGroundEnum;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerGoodsTradeBackgroundVO;
import org.springblade.customer.vo.TradeBackgroundInviteVO;
import org.springblade.customer.vo.TradeBackgroundVO;
import org.springblade.customer.wrapper.TradeBackgroundInviteWrapper;
import org.springblade.system.entity.User;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_APPLET + "/customer/tradeBackground")
@Api(value = "小程序贸易背景", tags = "小程序贸易背景接口")
public class TradeBackgroundAppletController extends BladeController {

	private final ITradeBackgroundService tradeBackgroundService;
	private final ICustomerService customerService;
	private final ITradeBackgroundInviteService tradeBackgroundInviteService;
	private final ICustomerInfoService customerInfoService;
	private final ICustomerGoodsTradeBackgroundService customerGoodsTradeBackgroundService;
	private final ICustomerSupplierService supplierService;
	private final ICustomerPersonInfoService customerPersonInfoService;


	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入tradeBackground")
	//@PreAuth("hasPermission('customer:tradeBackground:list') or hasRole('admin') or hasRole('financing_admin')")
	public R<Map<String, List<TradeBackgroundVO>>> list() {
		if (!customerService.isRealName()) {
			return R.fail("请去认证后再过来吧");
		}
		return R.data(tradeBackgroundService.getList(Func.toLong(MyAuthUtil.getCompanyId())));
	}


	/**
	 * 贸易背景邀请箱
	 *
	 * @param height 查看上游或下游
	 * @return
	 */
	@GetMapping("/inviteList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerPersonInfo")
	public R<List<TradeBackgroundInviteVO>> list(Integer height) {
		Long customerId = Func.toLong(MyAuthUtil.getCompanyId());
		Integer customerType = MyAuthUtil.getCustomerType();
		List<TradeBackgroundInviteVO> inviteVO = null;
		//融资个体户用户查询邀请箱
		if (CustomerTypeEnum.PERSONAL.getCode().equals(customerType)) {
			Customer customer = customerService.getByCompanyId(customerId);
			String name = customer.getName();
			String phone = customer.getPhone();
			inviteVO = TradeBackgroundInviteWrapper.build()
				.listVO(tradeBackgroundInviteService.listByNameAndPhone(name, phone, height, customerType));
			//融资企业用户查询邀请箱
		} else {
			CustomerInfo customerInfo = customerInfoService.getByCompanyId(customerId);
			inviteVO = TradeBackgroundInviteWrapper.build()
				.listVO(tradeBackgroundInviteService.listByNameAndCompanyCode(customerInfo.getCorpName(), customerInfo.getBusinessLicenceNumber(), height));
		}
		//收集发起方信息显示给前端展示
		return R.data(tradeBackgroundInviteService.collectInitiatorInfo(inviteVO));
	}

	@GetMapping("/selectByCustomerGoodsId")
	@ApiOperation("查询客户产品绑定贸易背景")
	public R<List<CustomerGoodsTradeBackgroundVO>> selectByCustomerGoodsId(Long customerGoodsId) {
		return R.data(customerGoodsTradeBackgroundService.selectByCustomerGoodsId(customerGoodsId));
	}

	@PostMapping("/bindHeightTradeBackground")
	@ApiOperation("绑定融资端上游供应商流程")
	public R bindHeightTradeBackground(@Valid @RequestBody HeightTradeBackgroundDTO tradeBackground) {
		//参数检查及参数赋值
		R errorMsg = checkHeightBackGroundParams(tradeBackground);
		if (ObjectUtil.isNotEmpty(errorMsg)) {
			return errorMsg;
		}
		return R.status(tradeBackgroundService.bindHeightTradeBackground(tradeBackground));
	}

	private R checkHeightBackGroundParams(HeightTradeBackgroundDTO tradeBackground) {
		String companyId = MyAuthUtil.getCompanyId();
		Long userId = Func.toLong(companyId);
		if (ObjectUtil.isEmpty(userId)) {
			return R.fail(ResultCode.CLIENT_UN_AUTHORIZED, ResultCode.CLIENT_UN_AUTHORIZED.getMessage());
		}
//		if (customerInfoService.existByName(tradeBackground.getCoreEnterpriseName())) {
//			throw new ServiceException("该供应商已入驻，请前往贸易伙伴进行邀请绑定");
//		}
		//判断是否有该供应商
		CustomerSupplier customerSupplier = supplierService.getByName(tradeBackground.getCoreEnterpriseName());
		if (ObjectUtil.isEmpty(customerSupplier)) {
			return R.fail("该供应商不存在");
		}

		//设置下游发起者参数
		tradeBackground.setCompanyId(userId);
		tradeBackground.setCompanyLowerId(userId);
		Integer customerType = MyAuthUtil.getCustomerType();
		tradeBackground.setCustomerPosition(TradeBackGroundEnum.COMPANY_POSITION.LOWER.getCode());
		//区分发起者为个人还是企业
		Integer launchUserType = null;
		String launchCompanyLowerName = null;
		String launchCompanyLowerCode = null;
		if (CustomerTypeEnum.PERSONAL.getCode().equals(customerType)) {
			CustomerPersonInfo personInfo = customerPersonInfoService.getByCustomerId(userId);
			launchUserType = CustomerTypeEnum.PERSONAL.getCode();
			launchCompanyLowerName = personInfo.getName();
			launchCompanyLowerCode = personInfo.getAccount();
		} else {
			CustomerInfo companyInfo = customerInfoService.getByCompanyId(userId);
			launchUserType = companyInfo.getEntAuthType();
			launchCompanyLowerName = companyInfo.getCorpName();
			launchCompanyLowerCode = companyInfo.getBusinessLicenceNumber();
		}
		tradeBackground.setLowerUserType(launchUserType);
		tradeBackground.setCompanyLowerName(launchCompanyLowerName);
		tradeBackground.setCompanyLowerCode(launchCompanyLowerCode);
		//设置上游供应商参数
		tradeBackground.setCompanyHeightId(customerSupplier.getId());
		tradeBackground.setHeightUserType(CustomerUserType.SUPPLIER.getType());
		tradeBackground.setCompanyHeightName(customerSupplier.getSupperName());
		tradeBackground.setCompanyHeightCode(customerSupplier.getUnifiedCode());
		//设置业务权限
		tradeBackground.setPurchaseAbility(CommonConstant.OPENSTATUS);
		tradeBackground.setFinancingTransferAbility(CommonConstant.CLOSESTATUS);
		tradeBackground.setAccountsReceivableAbility(CommonConstant.CLOSESTATUS);
		//根据判断是否已存在该贸易背景
		boolean exsit = tradeBackgroundService.existBackGroundByIdAndHeightName(tradeBackground, tradeBackground.getProcessId());
		if (exsit) {
			return R.fail("該貿易背景已存在,或正在审批中，请勿重复提交");
		}
		User user = CustomerUserCache.getUserById(userId);
		if (ObjectUtil.isEmpty(user.getName())) {
			return R.fail("用户平台方关联的名称为空，请联系管理员进行修改");
		}
		return null;
	}

	@GetMapping("/selectUnUsedTradeBackgroundList")
	@ApiOperation("查询未被使用的贸易背景")
	public R<List<TradeBackgroundVO>> selectUnUsedTradeBackgroundList(
		@RequestParam Integer goodsType,
		@RequestParam Long goodsId) {
		return R.data(tradeBackgroundService.selectUnUsedTradeBackgroundList(goodsType, goodsId));
	}

}
