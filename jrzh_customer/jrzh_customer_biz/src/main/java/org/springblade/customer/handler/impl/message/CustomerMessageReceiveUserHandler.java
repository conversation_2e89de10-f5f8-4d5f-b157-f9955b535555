package org.springblade.customer.handler.impl.message;

import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.UserTypeEnum;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.Customer;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.service.ICustomerService;
import org.springblade.message.constant.MessageConstant;
import org.springblade.message.handler.user.MessageReceiveUserHandler;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.system.vo.UserVO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description:
 * @date 2023年04月25日 16:29
 */
@Service(MessageConstant.MESSAGE_SERVICE_PREFIX + CommonConstant.CUSTOMER_SERVICE)
@AllArgsConstructor
public class CustomerMessageReceiveUserHandler implements MessageReceiveUserHandler {

    private final RemoteUserService remoteUserService;

    private final ICustomerInfoService customerInfoService;

    private final ICustomerService customerService;

    @Override
    public UserVO getReceiveUserByUserId(String userId) {
        User user = remoteUserService.getUserById(Long.valueOf(userId), FeignConstants.FROM_IN).getData();
        if (UserTypeEnum.CORE.getCode().equals(user.getUserType()) || UserTypeEnum.FINANCING.getCode().equals(user.getUserType())) {
            CustomerInfo customerInfo = customerInfoService.lambdaQuery().eq(CustomerInfo::getCompanyId, userId).one();
            if (customerInfo != null) {
                Customer customer = customerService.getById(customerInfo.getCustomerId());
                return  BeanUtil.copyProperties(customer, UserVO.class);
            }
        } else {
            return BeanUtil.copyProperties(user, UserVO.class);
        }
        return null;
    }
}
