/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.FinancingRole;
import org.springblade.customer.service.IFinancingRoleService;
import org.springblade.customer.vo.FinancingRoleVO;
import org.springblade.customer.wrapper.FinancingRoleWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 融资企业角色信息表 控制器
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("customer/financingRole")
@Api(value = "融资企业角色信息表", tags = "融资企业角色信息表接口")
public class FinancingRoleController extends BladeController {

	private final IFinancingRoleService financingRoleService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入financingRole")
   @PreAuth( "hasPermission('customer:financingRole:detail') or hasRole('administrator')")
	public R<FinancingRoleVO> detail(FinancingRole financingRole) {
		FinancingRole detail = financingRoleService.getOne(Condition.getQueryWrapper(financingRole));
		return R.data(FinancingRoleWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 融资企业角色信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入financingRole")
   @PreAuth( "hasPermission('customer:financingRole:list') or hasRole('administrator')")
	public R<IPage<FinancingRoleVO>> list(FinancingRole financingRole, Query query) {
		IPage<FinancingRole> pages = financingRoleService.page(Condition.getPage(query), Condition.getQueryWrapper(financingRole));
		return R.data(FinancingRoleWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 融资企业角色信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入financingRole")
   @PreAuth( "hasPermission('customer:financingRole:page') or hasRole('administrator')")
	public R<IPage<FinancingRoleVO>> page(FinancingRoleVO financingRole, Query query) {
		IPage<FinancingRoleVO> pages = financingRoleService.selectFinancingRolePage(Condition.getPage(query), financingRole);
		return R.data(pages);
	}

	/**
	 * 新增 融资企业角色信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入financingRole")
   @PreAuth( "hasPermission('customer:financingRole:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody FinancingRole financingRole) {
		return R.status(financingRoleService.save(financingRole));
	}

	/**
	 * 修改 融资企业角色信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入financingRole")
   @PreAuth( "hasPermission('customer:financingRole:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody FinancingRole financingRole) {
		return R.status(financingRoleService.updateById(financingRole));
	}

	/**
	 * 新增或修改 融资企业角色信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入financingRole")
   @PreAuth( "hasPermission('customer:financingRole:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody FinancingRole financingRole) {
		return R.status(financingRoleService.saveOrUpdate(financingRole));
	}


	/**
	 * 删除 融资企业角色信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
   @PreAuth( "hasPermission('customer:financingRole:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(financingRoleService.deleteLogic(Func.toLongList(ids)));
	}


}
