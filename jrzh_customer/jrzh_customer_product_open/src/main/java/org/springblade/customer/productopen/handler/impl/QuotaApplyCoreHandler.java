package org.springblade.customer.productopen.handler.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.dto.EnterpriseQuotaDTO;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.entity.FrontCreditInformation;
import org.springblade.customer.entity.FrontValue;
import org.springblade.customer.mapper.CustomerCertificateInfoMapper;
import org.springblade.customer.productopen.dto.QuotaApplyDTO;
import org.springblade.customer.productopen.handler.BusinessProcessCoreHandler;
import org.springblade.customer.productopen.handler.IBusinessProcessOperation;
import org.springblade.customer.productopen.handler.QuotaApplyHandler;
import org.springblade.customer.service.*;
import org.springblade.modules.contract.service.IContractService;
import org.springblade.othersapi.riskorderapi.dto.FinalApproveAmount;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.product.common.entity.GoodsProcess;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.riskmana.api.emum.RiskListenerRunDataEnum;
import org.springblade.riskmana.core.service.IRiskmanaApplyService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023/4/25 15:25
 * @Description: 核心企业额度申请
 * @Version: 1.0
 */
@RequiredArgsConstructor
@Service
public class QuotaApplyCoreHandler implements QuotaApplyHandler, BusinessProcessCoreHandler<QuotaApplyDTO, QuotaApplyDTO>, IBusinessProcessOperation {
    private final IBusinessProcessService businessProcessService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final ICustomerGoodsService customerGoodsService;
    private final IContractService contractService;
    private final ProductDirector productDirector;
    private final ICustomerMaterialService customerMaterialService;
    private final IFrontCoreListService frontCoreListService;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final ICusCapitalQuotaService cusCapitalQuotaService;
    private final IWfProcessService processService;
    private final CustomerCertificateInfoMapper certificateInfoMapper;
    private final IFrontValueService frontValueService;
    private final ICustomerInfoService customerInfoService;
    private final IFrontCreditInformationService frontCreditInformationService;
    private final IRiskmanaApplyService applyService;
    private final IRiskmanaApplyService riskmanaApplyService;

    @Override
    public QuotaApplyDTO prepareParams(QuotaApplyDTO quotaApplyDTO) {
        String processNo = businessProcessService.getProcessNo(quotaApplyDTO.getProcessInstanceId());
        quotaApplyDTO.setProcessNo(processNo);
        // 设置流程变量
        quotaApplyDTO.setProcessType(ProcessTypeEnum.CORE_AUTONOMY_APPLY_QUOTA.getCode());
        quotaApplyDTO.setEnterpriseType(EnterpriseTypeEnum.CORE_ENTERPRISE.getCode());
        // 检查合同签署
        List<String> contractIdList = quotaApplyDTO.getContractIdList();
        contractService.checkContractIsSign(contractIdList, quotaApplyDTO.getUserId());
        //流程检查
        if (!canCommit(quotaApplyDTO.getGoodsId(), quotaApplyDTO.getProcessType(), quotaApplyDTO.getUserId())) {
            throw new ServiceException("流程已提交，请勿重复提交流程");
        }
        return quotaApplyDTO;
    }

    @Override
    public void fillTaskVar(QuotaApplyDTO preParam, QuotaApplyDTO param, Map<String, Object> variables) {
        CustomerGoods customerGoods = customerGoodsService.getByGoodsIdAndCustomerId(param.getGoodsId(), param.getUserId());

        if(customerGoods==null){
            throw new ServiceException("客户产品不存在,请核对后重试");
        }
        //查询产品信息 设置公用信息
        Product product = productDirector.detailBase(param.getGoodsId());
        Map<String, Object> commonVariables = getCommonVariables(param.getProcessNo(), customerGoods.getId(), param.getProcessType(), param.getEnterpriseType(), product);
        variables.putAll(commonVariables);
        //设置合同信息
        variables.put(ProcessConstant.CONTRACT_ID, Func.join(param.getContractIdList()));
        // 查询企业信息及相关资料
        variables.put(ProcessConstant.CUSTOMER_MATERIAL, customerMaterialService.detail(param.getGoodsId()));
        //设置风控填充参数服务
        variables.put(ProcessConstant.RISK_LISTENER_DATA_SOURCE, RiskListenerRunDataEnum.RISK_QUOTA_CREDIT_SERVICE);
    }


    @Override
    public void before(QuotaApplyDTO preParam, QuotaApplyDTO param) {

    }


    @Override
    public String submitOrReCommit(QuotaApplyDTO preParam, QuotaApplyDTO param, Map<String, Object> variable) {
        //获取流程
        GoodsProcess goodsProcess = getGoodsProcess(param.getGoodsId(), param.getProcessType(), productDirector);
        //流程提交
        return businessProcessService.startOrSubmit(goodsProcess.getProcessKey(), param.getProcessInstanceId(), variable, null);
    }


    @Override
    public void after(QuotaApplyDTO preParam, QuotaApplyDTO param, String processInstanceId) {
        //设置为流程状态为业务审批
        businessProcessProgressService
                .updateBusinessProcessProgressUnContainInvalid(param.getGoodsId(),
                        ProcessProgressEnum.APPLY_QUOTA_APPROVE.getCode(), param.getProcessType(), processInstanceId,
                        param.getUserId(), ProcessStatusEnum.APPROVING.getCode(), CommonConstant.NO, true);
    }

    @Override
    public Integer support() {
        return ProcessTypeEnum.CORE_AUTONOMY_APPLY_QUOTA.getCode();
    }

    @Override
    public void close(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        Integer type = (Integer) variables.getOrDefault(ProcessConstant.PROCESS_TYPE, -1);
        if (type == ProcessTypeEnum.APPLY_QUOTA.getCode()
                || type == ProcessTypeEnum.QUOTA_ACTIVE.getCode()
                || type == ProcessTypeEnum.CORE_AUTONOMY_APPLY_QUOTA.getCode()
                || type == ProcessTypeEnum.CORE_AUTONOMY_QUOTA_ACTIVE.getCode()) {
            Long customerGoodsId = (Long) variables.getOrDefault(ProcessConstant.CUSTOMER_GOODS_ID, -1);
            Long businessId = (Long) variables.getOrDefault(ProcessConstant.BUSINESS_ID, -1);
            Long userId = (Long) variables.getOrDefault(ProcessConstant.USER_ID, -1);
            customerGoodsService.close(customerGoodsId, businessId, processInstanceId, userId, type);
            //通知风控系统驳回
            Long ratingRecordId = (Long) variables.get(ProcessConstant.RATING_RECORD_ID);
            riskmanaApplyService.terminateApplyQuota(ratingRecordId, processInstanceId);
        }
        // 保存证件资料
        CustomerBusinessInfo businessInfo = customerInfoService.getCustomerBusinessInfoByCompanyId(variables.get(ProcessConstant.USER_ID).toString());
        String companyName = ObjectUtil.isNotEmpty(businessInfo.getCompanyName()) ? businessInfo.getCompanyName() : "";
        String creditCode = ObjectUtil.isNotEmpty(businessInfo.getCreditCode()) ? businessInfo.getCreditCode() : "";
        saveCertificateInfo(delegateExecution, 1, certificateInfoMapper, companyName, creditCode);
    }

    @Override
    public void pass(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        Long goodsId = (Long) variables.get(ProcessConstant.BUSINESS_ID);
        Long userId = (Long) variables.get(ProcessConstant.USER_ID);
        // 更新流程进度
        businessProcessProgressService.updateBusinessProcessProgress(
                goodsId,
                ProcessProgressEnum.APPLY_SUCCESS.getCode(),
                ProcessTypeEnum.CORE_AUTONOMY_APPLY_QUOTA.getCode(),
                processInstanceId,
                userId,
                ProcessStatusEnum.FINISH.getCode());
        // 更新价值分析信息
        FrontValue frontValue = JSONUtil.toBean(JSONUtil.toJsonStr(variables.get("frontValue")), FrontValue.class);
        Long recordId = frontValueService.updateFrontValue(frontValue, userId);
        // 更新征信信息
        FrontCreditInformation frontCreditInformation = JSONUtil.toBean(JSONUtil.toJsonStr(variables.get("frontCreditInformation")), FrontCreditInformation.class);
        frontCreditInformationService.updateFrontCreditInformation(frontCreditInformation, userId, recordId);
        // 保存融资企业额度
        variables.put(ProcessConstant.PROCESS_INSTANCE_ID, processInstanceId);
        int status = EnterpriseQuotaStatusEnum.UN_VALID.getCode();
        Long enterpriseQuotaId = enterpriseQuotaService.saveFinancingEnterpriseQuota(variables);
        // 更新客户产品
        variables.put(ProcessConstant.ENTERPRISE_QUOTA_Id, enterpriseQuotaId);
        customerGoodsService.settingAmount(variables);
        // 资方扣减额度
        Long capitalId = (Long) variables.get(ProcessConstant.CAPITAL_ID);
        FinalApproveAmount finalApproveAmount = JSON.parseObject(JSON.toJSONString(variables.get(ProcessConstant.FINAL_APPROVE_AMOUNT)), FinalApproveAmount.class);
        cusCapitalQuotaService.subtractQuota(capitalId, CommonUtil.wanToYuan(finalApproveAmount.getFinalAmount()));
        //通知风控系统通过
        Long ratingRecord = (Long) variables.get(ProcessConstant.RATING_RECORD_ID);
        applyService.passApplyQuota(ratingRecord, finalApproveAmount, processInstanceId);

        // 保存证件资料
        CustomerBusinessInfo businessInfo = customerInfoService.getCustomerBusinessInfoByCompanyId(variables.get(ProcessConstant.USER_ID).toString());
        String companyName = ObjectUtil.isNotEmpty(businessInfo.getCompanyName()) ? businessInfo.getCompanyName() : "";
        String creditCode = ObjectUtil.isNotEmpty(businessInfo.getCreditCode()) ? businessInfo.getCreditCode() : "";
        saveCertificateInfo(delegateExecution, 0, certificateInfoMapper, companyName, creditCode);
    }

    private void updateEnterpriseQuota(Map<String, Object> variables) {
        FinalApproveAmount finalApproveAmount = JSON.parseObject(JSON.toJSONString(variables.get(org.springblade.process.constant.ProcessConstant.FINAL_APPROVE_AMOUNT)), FinalApproveAmount.class);
        EnterpriseQuotaDTO enterpriseQuota = (EnterpriseQuotaDTO) variables.get(org.springblade.process.constant.ProcessConstant.CORE_ENTERPRISE_QUOTA);
        BigDecimal finalAmount = CommonUtil.wanToYuan(finalApproveAmount.getFinalAmount());
        enterpriseQuota.setCreditAmount(finalAmount);
        enterpriseQuota.setQuotaType(finalApproveAmount.getQuotaType());
        enterpriseQuota.setServiceRate(finalApproveAmount.getServiceRate());
        enterpriseQuota.setEffectiveTime(LocalDateTime.now());
        enterpriseQuota.setExpireTime(finalApproveAmount.getExpireTime());
        enterpriseQuota.setAnnualInterestRate(finalApproveAmount.getAnnualInterestRate());
        enterpriseQuota.setProcessInstanceId((String) variables.getOrDefault(org.springblade.process.constant.ProcessConstant.PROCESS_INSTANCE_ID, ""));
        enterpriseQuota.setTaskId((String) variables.getOrDefault(org.springblade.process.constant.ProcessConstant.TASK_ID, ""));
        enterpriseQuota.setTaskNo((String) variables.getOrDefault(WfProcessConstant.PROCESS_NO, ""));
        enterpriseQuota.setLoanable(finalApproveAmount.getLoanable());
        enterpriseQuota.setRecycleType(finalApproveAmount.getRecycleType());
        CustomerGoods customerGoods = customerGoodsService.getByEnterpriseQuotaId(enterpriseQuota.getId());
        if (enterpriseQuota.getEnterpriseType().equals(EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode())) {
            enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.UN_VALID.getCode());
            customerGoods.setStatus(CustomerGoodsEnum.QUOTA_CHANGE.getCode());
        } else {
            enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.VALID.getCode());
            customerGoods.setStatus(CustomerGoodsEnum.FINANCING.getCode());
        }
        enterpriseQuota.setAvailableAmount(finalAmount.subtract(CommonUtil.wanToYuan(enterpriseQuota.getUsedAmount())));
        enterpriseQuota.setDailyInterestRate(finalApproveAmount.getAnnualInterestRate().divide(BigDecimal.valueOf(CommonConstant.YEAR_DAY_COUNT), 3, CommonConstant.NUMBER_STRATEGY));
        enterpriseQuota.setBondProportion(finalApproveAmount.getBondProportion());
        enterpriseQuota.setFinancingProportion(finalApproveAmount.getFinancingProportion());
        enterpriseQuotaService.updateById(enterpriseQuota);

        // 判断是上调还是下调
        EnterpriseQuota oldEnterpriseQuota = enterpriseQuotaService.getById(enterpriseQuota.getId());
        // 实际上调的金额
        BigDecimal changeAmount = finalAmount.subtract(oldEnterpriseQuota.getCreditAmount());
        if (changeAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 申请上调的金额
            BigDecimal additionAmount = CommonUtil.wanToYuan(enterpriseQuota.getCreditAmount()).subtract(oldEnterpriseQuota.getCreditAmount());
            // 资方扣减额度
            cusCapitalQuotaService.capitalCreditSuccess(enterpriseQuota.getCapitalId(), additionAmount, changeAmount);
        }
        if (changeAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 资方增加额度
            cusCapitalQuotaService.addAmount(enterpriseQuota.getCapitalId(), changeAmount.multiply(BigDecimal.valueOf(-1)));
        }

        frontCoreListService.settingScore(enterpriseQuota.getEnterpriseId(), new BigDecimal((String) variables.get("score")));

        // 更新我的产品状态
        customerGoods.setStatus(CustomerGoodsEnum.FINANCING.getCode());
        customerGoods.setProcessInstanceId(enterpriseQuota.getProcessInstanceId());
        customerGoods.setRatingRecordId((Long) variables.getOrDefault(org.springblade.process.constant.ProcessConstant.RATING_RECORD_ID, -1L));
        customerGoods.setEnterpriseQuotaId(enterpriseQuota.getId());
        customerGoodsService.updateById(customerGoods);
    }

    private void terminalProcessProgress(DelegateExecution delegateExecution) {
        Long businessId = delegateExecution.getVariable(ProcessConstant.BUSINESS_ID, Long.class);
        Long deptId = delegateExecution.getVariable(ProcessConstant.USER_ID, Long.class);
        businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(
                businessId,
                ProcessProgressEnum.CORE_APPLY_SUCCESS.getCode(),
                ProcessTypeEnum.CORE_APPLY_QUOTA.getCode(),
                delegateExecution.getProcessInstanceId(),
                deptId,
                ProcessStatusEnum.TERMINAL.getCode());
        businessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(
                businessId,
                null,
                ProcessTypeEnum.CORE_QUOTA_APPLY_MANAGE.getCode(),
                delegateExecution.getProcessInstanceId(),
                deptId,
                ProcessStatusEnum.TERMINAL.getCode(), CommonConstant.YES, false);
    }

    /**
     * 判读是否可提交资料开启主流程 条件 ：流程状态为驳回或开启
     *
     * @param businessId
     * @param type
     * @param userId
     */
    private Boolean canCommit(Long businessId, Integer type, Long userId) {
        BusinessProcessProgress businessProcessProgress = businessProcessProgressService.getByBusinessIdAndType(businessId, type, userId);
        Integer status = businessProcessProgress.getStatus();
        return ProcessStatusEnum.REJECT.getCode() == status || ProcessStatusEnum.PROCESS_OPEN.getCode() == status;
    }

}
