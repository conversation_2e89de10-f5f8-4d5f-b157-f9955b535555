package org.springblade.customer.productopen.listener;

import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.productopen.handler.BusinessProcessFactory;
import org.springblade.customer.productopen.handler.QuotaActivateHandler;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 额度激活流程监听
 *
 * <AUTHOR>
 */
@Component("quotaActiveProcessListener")
@RequiredArgsConstructor
public class QuotaActiveProcessListener implements ExecutionListener {

    private final BusinessProcessFactory businessProcessFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {

        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        Integer enterpriseType = (Integer) variables.get(ProcessConstant.ENTERPRISE_TYPE);
        Integer processType = (Integer) variables.get(ProcessConstant.PROCESS_TYPE);
        QuotaActivateHandler businessProcessHandler = businessProcessFactory.instanceOf(processType, enterpriseType, QuotaActivateHandler.class);
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            //执行额度激活终止操作
            businessProcessHandler.close(delegateExecution);
        } else {
            //执行额度激活通过操作
            businessProcessHandler.pass(delegateExecution);
        }
    }
}
