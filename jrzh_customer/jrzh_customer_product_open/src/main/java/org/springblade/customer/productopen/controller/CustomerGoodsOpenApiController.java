/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.productopen.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.customer.dto.CustomerGoodsDTO;
import org.springblade.customer.dto.GroupRouteApplyDTO;
import org.springblade.customer.dto.GroupRouteConfirmDto;
import org.springblade.customer.dto.ProductRouteRunRiskResultDTO;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.customer.entity.ProductRouteGroupApply;
import org.springblade.customer.enums.ProductRouteGroupApplyEnum;
import org.springblade.customer.productopen.dto.QuotaActiveDTO;
import org.springblade.customer.productopen.dto.QuotaApplyDTO;
import org.springblade.customer.productopen.service.IBusinessProcessProductOpenService;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.IProductRouteGroupApplyService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.RouteCustomerGoodsRespVO;
import org.springblade.customer.vo.RouteProductRecommendVo;
import org.springblade.system.utils.UserUtils;
import org.springblade.product.common.entity.Product;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户产品开通业务 控制器
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/customerGoods/opening/business")
@Api(value = "客户产品开通业务", tags = "客户产品开通业务")
public class CustomerGoodsOpenApiController extends BladeController {
    private final IBusinessProcessProductOpenService businessProcessProductOpenService;
    private final ICustomerGoodsService customerGoodsService;
    private final IProductRouteGroupApplyService productRouteGroupApplyService;

    /**
     * 产品清除
     *
     * @param goodsId
     * @param customerGoodsId
     * @return
     */
    @GetMapping("/clear")
    public R<Boolean> clear(@RequestParam Long goodsId, @RequestParam Long customerGoodsId) {
        businessProcessProductOpenService.close(goodsId, customerGoodsId);
        return R.status(true);
    }

    @PostMapping("/save")
    @ApiOperation("保存我的产品和贸易背景")
    public R<Long> save(@Valid @RequestBody CustomerGoodsDTO customerGoodsDTO) {
        return R.data(customerGoodsService.saveCustomerGoods(customerGoodsDTO));
    }
    /**
     * 提交额度申请流程
     *
     * @param quotaApplyDTO
     * @return
     */
    @PostMapping("/submitQuotaApplyProcess")
    @ApiOperation("提交额度申请流程")
    public R<Boolean> submitQuotaApplyProcess(@Valid @RequestBody QuotaApplyDTO quotaApplyDTO) {
        quotaApplyDTO.setUserId(MyAuthUtil.getUserId());
        return R.data(businessProcessProductOpenService.submitQuotaApplyProcess(quotaApplyDTO));
    }

    /**
     * 保存额度申请的资料申请
     */
    @PostMapping("/saveCustomerMaterial")
    @ApiOperation(value = "保存额度申请的资料申请")
    public R<Long> save(@RequestBody CustomerMaterial customerMaterial) {
        customerMaterial.setUserId(MyAuthUtil.getUserId());
        businessProcessProductOpenService.saveCustomerMaterial(customerMaterial);
        return R.data(customerMaterial.getId());
    }

    /**
     * 保存多资方产品对应-资金产品客户资料
     * @param customerMaterialList
     * @return
     */
    @PostMapping ("/save-multi-funding-customer-material")
    @ApiOperation(value = "保存多资方产品对应-资金产品客户资料")
    public R<Boolean> saveMultiFundingCustomerMaterial(@RequestBody List<CustomerMaterial> customerMaterialList) {
        return R.data(businessProcessProductOpenService.saveMultiFundingCustomerMaterial(customerMaterialList));
    }

    /**
     * 提交额度激活流程
     *
     * @param quotaActiveDTO
     * @return
     */
    @PostMapping("/submitQuotaActiveProcess")
    @ApiOperation("提交额度激活流程")
    public R<Boolean> submitQuotaActiveProcess(@Valid @RequestBody QuotaActiveDTO quotaActiveDTO) {
        quotaActiveDTO.setUserId(MyAuthUtil.getUserId());
        return R.data(businessProcessProductOpenService.submitQuotaActiveProcess(quotaActiveDTO));
    }

    @GetMapping("/get-goods-id")
    @ApiOperation("获取产品组路由申请关联的产品id")
    public R<JSONObject> getGoodsIdByGroupId(@RequestParam Long groupId, Long goodId) {
//        return R.data(businessProcessProductOpenService.getGoodsIdByGroupId(groupId, goodId, AuthUtil.getUserId()));
        return businessProcessProductOpenService.getGoodsIdByGroupId(groupId, goodId, AuthUtil.getUserId());
    }

    @GetMapping("/get-product-route-status")
    @ApiOperation("获取产品组路由申请关联的产品状态")
    public R<RouteCustomerGoodsRespVO> getGoodsIdByGroupStatus(@RequestParam Long groupId) {
        return R.data(businessProcessProductOpenService.getGoodsIdByGroupStatus(groupId, AuthUtil.getUserId()));
    }

    @PostMapping("/routeCustomerGoodsApply")
    @ApiOperation("产品路由申请")
    public R<RouteCustomerGoodsRespVO> routeCustomerGoodsApply(@RequestBody GroupRouteApplyDTO groupRouteApplyDTO) {
        groupRouteApplyDTO.setUserId(AuthUtil.getUserId());
        groupRouteApplyDTO.setGroupId(groupRouteApplyDTO.getGoodsId());
        groupRouteApplyDTO.setUserType(UserUtils.getEnterpriseType());
        groupRouteApplyDTO.setIdempotentKey(groupRouteApplyDTO.getUserId() + "_" + groupRouteApplyDTO.getGroupId());
        return R.data(businessProcessProductOpenService.routeCustomerGoodsApply(groupRouteApplyDTO));
    }


    @PostMapping("/confirm/product")
    @ApiOperation("客户确认并开通所选择产品")
    public R confirmProduct(@Valid @RequestBody GroupRouteConfirmDto groupRouteConfirm) {
        return R.data(businessProcessProductOpenService.confirmProduct(groupRouteConfirm));
    }


    @GetMapping("/confirm/product/list")
    @ApiOperation("客户允许开通产品列表")
    public R<RouteProductRecommendVo> confirmProductList(Long groupId) {
        RouteProductRecommendVo routeProductRecommendVo = new RouteProductRecommendVo();
        ProductRouteGroupApply productRouteGroupApply = productRouteGroupApplyService.getByUserIdAndGroupId(AuthUtil.getUserId(),groupId);
        if(productRouteGroupApply==null){
            return R.fail("未找到对应产品开通记录");
        }
        if(ProductRouteGroupApplyEnum.APPLY_STATUS.SUCCESS.getStatus()
                .equals(productRouteGroupApply.getStatus())){
            String productRouteRunRiskResultList = productRouteGroupApply.getProductRouteRunRiskResultList();
            List<ProductRouteRunRiskResultDTO> productRouteDTOS = JSONUtil.toList(productRouteRunRiskResultList, ProductRouteRunRiskResultDTO.class);
            List<Product> productList = productRouteDTOS.stream().map(ProductRouteRunRiskResultDTO::getProduct).collect(Collectors.toList());
            List<Product> collect = productList.stream()
                    .filter(product -> product.getIsShow() == 1)
                    .sorted(Comparator.comparing(Product::getSort))
                    .collect(Collectors.toList());
            routeProductRecommendVo.setProducts(collect);
        }
        routeProductRecommendVo.setStatus(productRouteGroupApply.getStatus());
        routeProductRecommendVo.setGroupId(groupId);
        routeProductRecommendVo.setCustomerMaterialId(productRouteGroupApply.getCustomerMaterialId());
        routeProductRecommendVo.setRecommendGoodsId(productRouteGroupApply.getRecommendGoodsId());
        return R.data(routeProductRecommendVo);
    }

}
