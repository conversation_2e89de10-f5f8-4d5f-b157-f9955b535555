package org.springblade.customer.authapi.handler;

import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.otherapi.core.constant.OtherApiTypeEnum;
import org.springblade.otherapi.core.dto.ApiParamDTO;
import org.springblade.othersapi.core.service.IOthersApiService;

/**
 * 实名接口抽象类
 *
 * <AUTHOR>
 */
public abstract class AuthApiAbsHandler implements AuthApiHandler {
	final static String CODE = OtherApiTypeEnum.AUTH_API.getCode();

	/**
	 * 获取Api链接参数
	 *
	 * @return
	 */
	public ApiParamDTO getApiParams() {
		return SpringUtil.getBean(IOthersApiService.class).getSingleParamByTypeCode(CODE);
	}

}
