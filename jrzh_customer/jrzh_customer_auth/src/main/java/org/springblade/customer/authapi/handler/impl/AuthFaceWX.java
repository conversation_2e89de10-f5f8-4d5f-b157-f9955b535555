package org.springblade.customer.authapi.handler.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.CustomerTypeEnum;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.customer.authapi.handler.AuthFace;
import org.springblade.customer.authapi.parambuilder.BestSignAuthApiParamBuilder;
import org.springblade.customer.constant.AuthStatus;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.CustomerPersonInfo;
import org.springblade.othersapi.bestsign.connector.BestSignConnector;
import org.springblade.othersapi.bestsign.dto.SSQResult;
import org.springblade.othersapi.bestsign.dto.SSqPersonAuthFaceH5Param;
import org.springblade.othersapi.bestsign.service.IBestSignService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 微信人脸识别
 */
@Service("authFaceWX")
@Slf4j
@RequiredArgsConstructor
public class AuthFaceWX implements AuthFace {
    private final BladeRedis bladeRedis;
    private final BestSignConnector connector;
    private final IBestSignService bestSignService;

    @Override
    public String gen(Long userId, Integer type, Boolean reGen, CustomerInfo customerInfo, CustomerPersonInfo customerPersonInfo) {

        AuthStatus.AUTH_TYPE h5Face = AuthStatus.AUTH_TYPE.H5_FACE;
        if (CustomerTypeEnum.PERSONAL.getCode().equals(type)) {
            //缓存人脸链接
            String key = H5_FACE_KEY + userId;
            if (!reGen) {
                if (bladeRedis.exists(CacheUtil.formatCacheName(key, true))) {
                    return bladeRedis.get(CacheUtil.formatCacheName(key, true));
                }
            }
            // 构建请求数据
            SSqPersonAuthFaceH5Param param = BestSignAuthApiParamBuilder.buildCommitPersonH5FaceContrast(customerPersonInfo);
            SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnH5FaceContrast(param);
            dealH5FaceResult(ssqResult);
            JSONObject ssqData = JSONUtil.parseObj(ssqResult.getData());
            String orderNo = ssqData.getStr("orderNo");
            //保存到实名方式信息表中
            saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(param), h5Face.getStatus(), customerPersonInfo.getCustomerId(), PERSON_TYPE, orderNo);
            // 缓存人脸链接
            String url = ssqData.getStr("url");
            bladeRedis.setEx(CacheUtil.formatCacheName(key, true), url, Duration.ofSeconds(60));
            return url;
        } else {
            //缓存人脸链接
            String key = H5_FACE_KEY + userId;
            if (!reGen) {
                if (bladeRedis.exists(CacheUtil.formatCacheName(key, true))) {
                    return bladeRedis.get(CacheUtil.formatCacheName(key, true));
                }
            }
            // 构建请求数据
            Map<String, Object> query = new HashMap<>();
            query.put("returnUrl", "https://www.bestsign.cn");
            SSqPersonAuthFaceH5Param param = BestSignAuthApiParamBuilder.buildCommitEntH5FaceContrast(query, customerInfo,userId);
            SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnH5FaceContrast(param);
            //
            String orderNo = JSONUtil.parseObj(ssqResult.getData()).getStr("orderNo");
            //保存到实名方式信息表中
            saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(param), h5Face.getStatus(), customerInfo.getCompanyId(), ENT_TYPE, orderNo);
            JSONObject data = JSONUtil.parseObj(ssqResult.getData());
            //放入緩衝池
            String url = data.getStr("url");
            bladeRedis.setEx(CacheUtil.formatCacheName(key, true), url, Duration.ofSeconds(60));
            return url;
        }

    }

    private void dealH5FaceResult(SSQResult ssqResult) {
        if (ssqResult.getErrno() != 0) {
            throw new ServiceException("实名信息有误，请核实");
        }
    }

    @Override
    public Integer getAuthType() {
        return AuthStatus.AUTH_TYPE.H5_FACE.getStatus();
    }

    @Override
    public Boolean support(String userAgent) {
        return userAgent.contains("MicroMessenger");
    }
}
