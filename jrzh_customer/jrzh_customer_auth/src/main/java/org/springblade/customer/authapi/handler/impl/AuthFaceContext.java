package org.springblade.customer.authapi.handler.impl;

import lombok.RequiredArgsConstructor;
import org.springblade.customer.authapi.handler.AuthFace;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 人脸接口上下文
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AuthFaceContext {

	private final Map<String, AuthFace> authFaceMap;

	/**
	 * 获取服务类
	 *
	 * @return
	 */
	public AuthFace template(String us) {
		for (String key : authFaceMap.keySet()) {
			AuthFace authFace = authFaceMap.get(key);
			Boolean support = authFace.support(us);
			if (support) {
				return authFace;
			}
		}
		return null;
	}

	/**
	 * 聚合服务类所有扫码的类型
	 */
	public List<Integer> allAuthType() {
		List<Integer> authType = new ArrayList<>();
		for (String key : authFaceMap.keySet()) {
			AuthFace authFace = authFaceMap.get(key);
			authType.add(authFace.getAuthType());
		}
		return authType;
	}
}
