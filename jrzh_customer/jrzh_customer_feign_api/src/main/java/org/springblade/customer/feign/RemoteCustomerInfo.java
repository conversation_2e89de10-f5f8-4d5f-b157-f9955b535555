package org.springblade.customer.feign;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.CustomerInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/11/15
 * @description
 */
@FeignClient(name = "remoteCustomerInfo", url = "${feign.url}")
public interface RemoteCustomerInfo {
    @GetMapping(CommonConstant.BLADE_FEIGN + CommonConstant.BLADE_FINANCE + "customerInfo/getPayRepaymentFeeList")
    R<CustomerInfo> getByCompanyId (@RequestParam(value = "companyId") Long companyId);
}
