/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 客户中心实体类
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Data
@TableName("jrzh_front_cec")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Cec对象", description = "客户中心")
public class Cec extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 标题
	*/
		@ApiModelProperty(value = "标题")
		private String title;
	/**
	* 类型 1-qq服务群  2-企业客服专线 3-微信公众号
	*/
		@ApiModelProperty(value = "类型 1-qq服务群  2-企业客服专线 3-微信公众号")
		private Integer type;
	/**
	* 说明文字
	*/
		@ApiModelProperty(value = "说明文字")
		private String description;
	/**
	* 类型为3 时 上传微信二维码
	*/
		@ApiModelProperty(value = "类型为3 时 上传微信二维码")
		private String qrCode;


}
