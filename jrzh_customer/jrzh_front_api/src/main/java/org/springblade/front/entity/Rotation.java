/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.util.StringUtils;

/**
 * 轮播图实体类
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Data
@TableName("jrzh_front_rotation")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Rotation对象", description = "轮播图")
public class Rotation extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 轮播图名称
	*/
		@ApiModelProperty(value = "轮播图名称")
		private String rotationName;
	/**
	* 轮播图图片
	*/
		@ApiModelProperty(value = "轮播图图片")
		private String logoSrc;
	/**
	* 轮播图类型
	*/
		@ApiModelProperty(value = "轮播图类型 0-静态 1-动态")
		private Integer rotationType;
	/**
	* 产品ID
	*/
		@ApiModelProperty(value = "产品ID")
		private Long goodsId;
	/**
	* 排序
	*/
		@ApiModelProperty(value = "排序")
		private Integer sort;

		@ApiModelProperty
		private  Integer goodType;

		@ApiModelProperty
		private Long capitalId;

	public boolean isOnShelf() {
		return !StringUtils.isEmpty(getStatus()) && CommonConstant.OPENSTATUS.equals(getStatus());
	}

	public boolean isType(){
		return !StringUtils.isEmpty(getRotationType())&&CommonConstant.OPENSTATUS.equals(getRotationType());
	}

}
