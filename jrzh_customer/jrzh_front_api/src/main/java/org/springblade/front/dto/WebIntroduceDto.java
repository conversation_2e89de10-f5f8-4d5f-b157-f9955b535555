package org.springblade.front.dto;

import lombok.Data;
import org.springblade.front.enums.IntroduceTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月29日19:01
 */
@Data
public class WebIntroduceDto implements Serializable {

    private Long id;

    /**
     * 平台介绍类型
     * @see IntroduceTypeEnum
     */
    private String introduceType;
    /**
     * banner图
     */
    private String bannerUrl;
    /**
     * 配置字段
     */
    private WebIntroduceData webIntroduceData;
    private List<WebIntroduceData> webIntroduceDataList;

    @Data
    public static class WebIntroduceData implements Serializable {

        /**
         * 平台介绍文字
         */
        private String introduceStr;
        /**
         * 产品类型
         */
        private String goodsTypeStr;
        /**
         * 产品图片
         */
        private String goodsImgUrl;
        /**
         * 产品描述
         */
        private String goodsDesc;
        /**
         * 地图平台key
         */
        private String key;
        /**
         * 联系电话
         */
        private String phone;
        /**
         * 公司地址
         */
        private String address;
        /**
         * 经度
         */
        private String longitude;
        /**
         * 纬度
         */
        private String latitude;
        /**
         * 合作银行url
         */
        private String bangImgUrl;
    }

}
