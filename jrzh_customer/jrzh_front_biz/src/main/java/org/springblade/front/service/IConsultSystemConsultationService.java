/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.front.entity.ConsultSystemConsultation;
import org.springblade.front.entity.ConsultSystemConsultationLog;
import org.springblade.front.vo.ConsultSystemConsultationVO;

import java.util.List;

/**
 * 系统咨询表 服务类
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
public interface IConsultSystemConsultationService extends BaseService<ConsultSystemConsultation> {

    /**
     * 自定义分页
     *
     * @param page
     * @param consultSystemConsultation
     * @return
     */
    IPage<ConsultSystemConsultationVO> selectConsultSystemConsultationPage(IPage<ConsultSystemConsultationVO> page, ConsultSystemConsultationVO consultSystemConsultation);

    /**
     * 获取热门榜单
     *
     * @param num 条数
     * @return 热门表单
     */
    List<ConsultSystemConsultation> listHotRank(Integer num);

    /**
     * 根据id查找问题
     *
     * @param id  问题id
     * @param num 条数
     * @return 问题详情
     */
    ConsultSystemConsultation findById(Long id, Integer num);

    /**
     * 根据名称模糊查询
     *
     * @param name 问题名称
     * @return
     */
    List<ConsultSystemConsultation> listByName(String name);

    /**
     * 根据分类查询问题列表
     *
     * @param id 分类id
     * @return
     */
    List<ConsultSystemConsultation> listById(Long id);

    /**
     * 更新解决数
     *
     * @param id 问题id
     * @return
     */
    boolean resolved(Long id);

    /**
     * 添加未解决记录
     *
     * @param log 系统咨询表实体类
     * @return
     */
    boolean unresolved(ConsultSystemConsultationLog log);

    /**
     * 获取分类树
     *
     * @return
     */
    List<ConsultSystemConsultationVO> listType();

    /**
     * 根据用户id查看浏览记录
     *
     * @param userId 用户id
     * @param num    获取条数
     * @return
     */
    List<ConsultSystemConsultation> history(Long userId, Integer num);

    /**
     * 获取头部信息
     *
     * @param num 获取条数
     * @return
     */
    List<ConsultSystemConsultationVO> listHead(Integer num);

}
