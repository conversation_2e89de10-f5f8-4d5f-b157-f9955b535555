<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.front.mapper.RotationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="rotationResultMap" type="org.springblade.front.entity.Rotation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="rotation_name" property="rotationName"/>
        <result column="logo_src" property="logoSrc"/>
        <result column="rotation_type" property="rotationType"/>
        <result column="goods_id" property="goodsId"/>
        <result column="sort" property="sort"/>
        <result column="tenant_id" property="tenantId"></result>
    </resultMap>


    <select id="selectRotationPage" resultMap="rotationResultMap">
        select * from jrzh_front_rotation where is_deleted = 0
    </select>

</mapper>
