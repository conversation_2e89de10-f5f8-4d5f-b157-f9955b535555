/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.controller.back;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.front.entity.Partner;
import org.springblade.front.service.IPartnerService;
import org.springblade.front.vo.PartnerVO;
import org.springblade.front.wrapper.PartnerWrapper;
import org.springblade.system.utils.UserUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合作企业 控制器
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_FRONT + CommonConstant.WEB_BACK + "/front/partner")
@Api(value = "合作企业", tags = "合作企业接口")
public class PartnerController extends BladeController {

    private final IPartnerService partnerService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入partner")
    @PreAuth("hasPermission('front:partner') or hasRole('administrator')")
    public R<PartnerVO> detail(Partner partner) {
        Partner detail = partnerService.getOne(Condition.getQueryWrapper(partner));
        return R.data(PartnerWrapper.build().entityVO(detail));
    }

    /**
     * 分页 合作企业
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入partner")
    @PreAuth("hasPermission('front:partner') or hasRole('administrator')")
    public R<IPage<PartnerVO>> list(Partner partner, Query query) {
        IPage<Partner> pages = partnerService.page(Condition.getPage(query), Condition.getQueryWrapper(partner).lambda().orderByAsc(Partner::getSort));
        IPage<PartnerVO> parthenerVo = PartnerWrapper.build().pageVO(pages);
        if (CollUtil.isEmpty(parthenerVo.getRecords())) {
            return R.data(new Page<>());
        }
        List<Long> userIds = parthenerVo.getRecords().stream().map(Partner::getUpdateUser).collect(Collectors.toList());

        Map<Long, String> userNameMap = UserUtils.mapUserName(userIds);
        List<PartnerVO> collect = parthenerVo.getRecords().stream().map(rotation1 -> {
            rotation1.setRealName(userNameMap.get(rotation1.getUpdateUser()));
            return rotation1;
        }).collect(Collectors.toList());
        parthenerVo.setRecords(collect);
        return R.data(parthenerVo);
    }


    /**
     * 自定义分页 合作企业
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入partner")
    @PreAuth("hasPermission('front:partner') or hasRole('administrator')")
    public R<IPage<PartnerVO>> page(PartnerVO partner, Query query) {
        IPage<PartnerVO> pages = partnerService.selectPartnerPage(Condition.getPage(query), partner);
        return R.data(pages);
    }

    /**
     * 新增 合作企业
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入partner")
    @PreAuth("hasPermission('front:partner') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody Partner partner) {
        if (StringUtils.isEmpty(partner.getStatus())) {
            partner.setStatus(0);
        }
        return R.status(partnerService.savePartner(partner));
    }

    /**
     * 修改 合作企业
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入partner")
    @PreAuth("hasPermission('front:partner') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody Partner partner) {
        if (StringUtils.isEmpty(partner.getStatus())) {
            partner.setStatus(0);
        }
        return R.status(partnerService.updateById(partner));
    }

    /**
     * 新增或修改 合作企业
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入partner")
    @PreAuth("hasPermission('front:partner') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody Partner partner) {
        if (StringUtils.isEmpty(partner.getStatus())) {
            partner.setStatus(0);
        }
        return R.status(partnerService.saveOrUpdate(partner));
    }


    /**
     * 删除 合作企业
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('front:partner') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        List<Long> longs = Func.toLongList(ids);
        int count = partnerService.count(Wrappers.<Partner>lambdaQuery().in(BaseEntity::getId, longs).eq(BaseEntity::getStatus, CommonConstant.OPENSTATUS));
        if (count > 0) {
            return R.fail("存在已发布的数据,请检查后删除！");
        }
        return R.status(partnerService.deleteLogic(longs));
    }


    /**
     * 发布
     */
    @PostMapping("/conlistput")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "发布", notes = "传入ID")
    public R conlistput(Long id) {
        return R.status(partnerService.rotationput(id));
    }

    /**
     * 批量禁用
     */
    @PostMapping("/conlistoff")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "批量禁用", notes = "传入ids")
    public R conlistoff(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(partnerService.rotationOff(Func.toLongList(ids)));
    }

    /**
     * 批量启用
     */
    @PostMapping("/handlerOnShelf")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "批量启用", notes = "传入ids")
    public R<Integer> handlerOnShelf(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.data(partnerService.handlerOnShelf(Func.toLongList(ids)));
    }


}
