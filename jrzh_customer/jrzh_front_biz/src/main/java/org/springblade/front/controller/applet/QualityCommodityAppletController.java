/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.controller.applet;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.front.service.IQualityCommodityService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 优质商品 控制器
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_FRONT+CommonConstant.WEB_APPLET +"/front/qualityCommodity")
@Api(value = "优质商品", tags = "优质商品接口")
public class QualityCommodityAppletController extends BladeController {

	private final IQualityCommodityService qualityCommodityService;

	@GetMapping("/list")
	@ApiOperation("查询首页优质商品")
	public R<Object> list() {
		return R.data(qualityCommodityService.selectCommdityList());
	}

}
