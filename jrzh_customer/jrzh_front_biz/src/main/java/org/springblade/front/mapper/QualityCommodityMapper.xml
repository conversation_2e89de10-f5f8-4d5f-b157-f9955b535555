<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.front.mapper.QualityCommodityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="qualityCommodityResultMap" type="org.springblade.front.entity.QualityCommodity">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="goods_id" property="goodsId"/>
    </resultMap>


    <select id="selectQualityCommodityPage" resultMap="qualityCommodityResultMap">
        select * from jrzh_quality_commodity where is_deleted = 0
    </select>

</mapper>
