/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.front.entity.Partner;
import org.springblade.front.mapper.PartnerMapper;
import org.springblade.front.service.IPartnerService;
import org.springblade.front.vo.PartnerVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 合作企业 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Service
public class PartnerServiceImpl extends BaseServiceImpl<PartnerMapper, Partner> implements IPartnerService {

	@Override
	public IPage<PartnerVO> selectPartnerPage(IPage<PartnerVO> page, PartnerVO partner) {
		return page.setRecords(baseMapper.selectPartnerPage(page, partner));
	}

	@Override
	public boolean rotationput(Long id) {
		Partner byId = getById(id);
		byId.setStatus(CommonConstant.OPENSTATUS);
		return updateById(byId);
	}

	@Override
	public boolean rotationOff(List<Long> toLongList) {
		List<Partner> partners = listByIds(toLongList);
		partners.parallelStream().forEach(partner -> {
			partner.setStatus(CommonConstant.CLOSESTATUS);
		});
		return updateBatchById(partners);
	}

	@Override
	public Integer handlerOnShelf(List<Long> toLongList) {
		List<Partner> partners = listByIds(toLongList);
		List<Partner> collect = partners.stream().map(partner -> {
			partner.setStatus(CommonConstant.OPENSTATUS);
			return partner;
		}).collect(Collectors.toList());
		updateBatchById(collect);
		return collect.size();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean savePartner(Partner partner) {
		List<Partner> list = list();
		if (list.size() >= 30) {
			throw new ServiceException("合作企业最多30个,请先删除之后，在新增!");
		}
		return save(partner);
	}

}
