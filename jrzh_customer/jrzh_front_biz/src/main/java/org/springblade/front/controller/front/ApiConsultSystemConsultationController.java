/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.controller.front;

import cn.hutool.core.util.ObjectUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.front.entity.ConsultSystemConsultation;
import org.springblade.front.entity.ConsultSystemConsultationLog;
import org.springblade.front.service.IConsultSystemConsultationService;
import org.springblade.front.service.IQuestionCatalogueService;
import org.springblade.front.vo.ConsultSystemConsultationVO;
import org.springblade.front.vo.QuestionCatalogueVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统咨询表 控制器
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_HELP + CommonConstant.WEB_FRONT + "/consult/consultsystemconsultation")
@Api(value = "系统咨询表", tags = "系统咨询表接口")
public class ApiConsultSystemConsultationController extends BladeController {

    private final IConsultSystemConsultationService consultSystemConsultationService;
    private final IQuestionCatalogueService catalogueService;
    private final static Integer OTHER = 4;

    /**
     * 获取热点问题列表
     */
    @GetMapping("/listHotRank")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R<List<ConsultSystemConsultation>> listHotRank() {
        return R.data(consultSystemConsultationService.listHotRank(30));
    }

    /**
     * 获取问题头部分类
     */
    @GetMapping("/listHead")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R<List<ConsultSystemConsultationVO>> listHead() {
        return R.data(consultSystemConsultationService.listHead(20));
    }

    /**
     * 获取问题分类
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R<List<QuestionCatalogueVO>> listType() {
        return R.data(catalogueService.listType());
    }

    /**
     * 通过id获取集合
     */
    @GetMapping("/listByTree")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R<List<ConsultSystemConsultation>> listByTree(Long id) {
        return R.data(consultSystemConsultationService.listById(id));
    }

    /**
     * 问题搜索
     */
    @GetMapping("/findQuestion")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R<List<ConsultSystemConsultation>> detail(String name) {
        return R.data(consultSystemConsultationService.listByName(name));
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R<ConsultSystemConsultation> detail(Long id) {
        return R.data(consultSystemConsultationService.findById(id, 1));
    }

    /**
     * 已解决
     */
    @GetMapping("/resolved")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R resolved(Long id) {
        return R.data(consultSystemConsultationService.resolved(id));
    }

    /**
     * 未解决
     */
    @PostMapping("/unresolved")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R unresolved(@RequestBody ConsultSystemConsultationLog log) {
        if (!OTHER.equals(log.getType()) && StringUtil.isNotBlank(log.getOther())) {
            return R.fail("类型非其他原因不可填写");
        }
        return R.data(consultSystemConsultationService.unresolved(log));
    }

    /**
     * 历史浏览
     */
    @GetMapping("/history")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入consultSystemConsultation")
    public R<List<ConsultSystemConsultation>> history() {
        Long userId = Func.toLong(AuthUtil.getUserId());
        if (ObjectUtil.isEmpty(userId)) {
            return null;
        }
        return R.data(consultSystemConsultationService.history(userId, 5));
    }
}
