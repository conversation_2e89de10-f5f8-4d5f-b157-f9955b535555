/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.front.entity.ConsultSystemConsultation;
import org.springblade.front.entity.QuestionCatalogue;
import org.springblade.front.service.IQuestionCatalogueService;
import org.springblade.front.vo.ConsultSystemConsultationVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;
import java.util.stream.Collectors;

/**
 * 系统咨询表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
public class ConsultSystemConsultationWrapper extends BaseEntityWrapper<ConsultSystemConsultation, ConsultSystemConsultationVO> {

    public static ConsultSystemConsultationWrapper build() {
        return new ConsultSystemConsultationWrapper();
    }

    @Override
    public ConsultSystemConsultationVO entityVO(ConsultSystemConsultation consultSystemConsultation) {
        ConsultSystemConsultationVO consultSystemConsultationVO = Objects.requireNonNull(BeanUtil.copy(consultSystemConsultation, ConsultSystemConsultationVO.class));


        return consultSystemConsultationVO;
    }

    @Override
    public IPage<ConsultSystemConsultationVO> pageVO(IPage<ConsultSystemConsultation> pages) {
        List<ConsultSystemConsultationVO> records = this.listVO(pages.getRecords());
        IPage<ConsultSystemConsultationVO> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        //设置分类名
        setCatalogueName(records);
        pageVo.setRecords(records);
        return pageVo;
    }

    private void setCatalogueName(List<ConsultSystemConsultationVO> records) {
        IQuestionCatalogueService service = SpringUtil.getBean(IQuestionCatalogueService.class);
        Map<Long, QuestionCatalogue> stringMap = service.list().parallelStream().collect(Collectors.toMap(QuestionCatalogue::getId, e -> e));
        for (ConsultSystemConsultationVO record : records) {
            StringBuilder builder = new StringBuilder();
            Stack<Long> stack = new Stack();
            Long parentId = record.getTypeId();
            while (!CommonConstant.TOP_PARENT_ID.equals(parentId)) {
                if(!stringMap.containsKey(parentId)){
                    break;
                }
                stack.push(parentId);
                parentId = stringMap.get(parentId).getParentId();
            }
            while (!stack.isEmpty()) {
                Long popId = stack.pop();
                String directName = stringMap.get(popId).getDirectName();
                builder.append(directName + " <");
            }
            if(builder.length()>1){
                builder.deleteCharAt(builder.length() - 1);
            }
            record.setCategory(builder.toString());
        }
    }
}
