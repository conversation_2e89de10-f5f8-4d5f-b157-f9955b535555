/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.front.entity.Protocol;
import org.springblade.front.vo.ProtocolVO;

/**
 * 用户协议 服务类
 *
 * <AUTHOR>
 * @since 2022-01-26
 */
public interface IProtocolService extends BaseService<Protocol> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param protocol
	 * @return
	 */
	IPage<ProtocolVO> selectProtocolPage(IPage<ProtocolVO> page, ProtocolVO protocol);

	/***
	 * 新增用户协议
	 * @param protocol
	 * @return
	 */
	Boolean saveProtocol(Protocol protocol);

	/***
	 * 更新用户协议
	 * @param protocol
	 * @return
	 */
	Boolean updateProtocolById(Protocol protocol);

	/**
	 * 根据名称获取用户协议
	 * @param name
	 * @return
	 */
	Protocol getByNameAndType(String name, String type);

	/**
	 * 更新修改
	 * @param protocol
	 * @return
	 */
	Boolean saveOrUpdateProtocol(Protocol protocol);
}
