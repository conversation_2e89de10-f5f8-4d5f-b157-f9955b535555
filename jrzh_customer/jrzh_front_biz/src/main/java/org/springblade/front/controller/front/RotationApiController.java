package org.springblade.front.controller.front;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.front.entity.Rotation;
import org.springblade.front.service.IRotationService;
import org.springblade.front.vo.RotationVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_FRONT + CommonConstant.WEB_FRONT + "/front/rotation")
@Api(value = "", tags = "轮播图客户端接口")
@Slf4j
public class RotationApiController {

	private final IRotationService rotationService;

	/**
	 * 前台详情
	 */
	@GetMapping("/deskDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "轮播图前台详情", notes = "传入rotation")
	public R<Rotation> deskdetail(@RequestParam Long id) {
		Rotation detail = rotationService.getById(id);
		return R.data(detail);
	}


	@GetMapping("/listRecep")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "轮播图前端展示查询已启用数据", notes = "")
	public R<List<RotationVO>> listreception(String tenantId) {
		if (StringUtil.isNotBlank(tenantId) && StringUtil.isBlank(AuthUtil.getTenantId())) {
			List<RotationVO> data = TenantBroker.applyAs(tenantId, e -> {
				return rotationService.selectReceptionIsrAll();
			});
			return R.data(data);
		}
		List<RotationVO> rotationVos = rotationService.selectReceptionIsrAll();
		return R.data(rotationVos);
	}


}
