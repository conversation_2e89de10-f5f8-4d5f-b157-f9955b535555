package org.springblade.report.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.CreditReportEnum;
import org.springblade.core.tool.utils.Func;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.report.service.IReportLoanManageRepaymentPlanService;
import org.springblade.report.vo.CreditReport;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 回款分析表
 *
 * <AUTHOR>
 * @data 2022/10/8
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class RepaymentAnalysisService {
    private final IReportLoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final ProductDirector productDirector;

    public List<CreditReport> repaymentAnalysisMethod(String dsName, String datasetName, Map<String, Object> parameters) {
        List<Integer> type = Func.toInt(parameters.get("type")) == -1 ? productDirector.getAllType()
                : Arrays.asList(Func.toInt(parameters.get("type")));
        String month = Func.toStr(parameters.get(CreditReportEnum.MONTH.getName()));
        Integer year = Func.toInt(parameters.get(CreditReportEnum.YEAR.getName()));
        String tentId = Func.toStr(parameters.get(CreditReportEnum.TENANT_ID.getName()));
        List<CreditReport> reports = loanManageRepaymentPlanService.repaymentAnalysisReport(type, year, month, tentId);
        return reports;
    }
}
