package org.springblade.report.service;

import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.report.vo.BusinessReport;
import org.springblade.report.vo.FinanceReport;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ReportService {


    private final Map<Long, List<BusinessReport>> businessReportData = Maps.newHashMap();
    private final Map<Long, List<FinanceReport>> financeReportData = Maps.newHashMap();

    @PostConstruct
    public void initData() {
        BusinessReport businessReport = BusinessReport.builder()
                .financeNo(CodeUtil.generateCode(CodeEnum.FINANCING_CODE))
                .financeApplyDate("2022-03-25")
                .passTime("2022-03-27 12:06:30")
                .businessType("应收账款")
                .userName("深圳市企链通科技有限公司")
                .loanDate("2022-04-01 12:06:30")
                .loanAmount(BigDecimal.valueOf(100000000))
                .annualInterestRate("5%")
                .expireTime("2022-10-01")
                .repaidPrincipal(BigDecimal.ZERO)
                .repaidInterest(BigDecimal.ZERO)
                .status("待还款")
                .overdueAmount(BigDecimal.ZERO)
                .settleDate(null)
                .build();
        BusinessReport businessReport1 = BusinessReport.builder()
                .financeNo(CodeUtil.generateCode(CodeEnum.FINANCING_CODE))
                .financeApplyDate("2022-03-25")
                .passTime("2022-03-27 12:06:30")
                .businessType("应收账款")
                .userName("深圳市精锐纵横网络技术有限公司")
                .loanDate("2022-04-01 12:06:30")
                .loanAmount(BigDecimal.valueOf(100000000))
                .annualInterestRate("5%")
                .expireTime("2022-10-01")
                .repaidPrincipal(BigDecimal.ZERO)
                .repaidInterest(BigDecimal.ZERO)
                .status("待还款")
                .overdueAmount(BigDecimal.ZERO)
                .settleDate(null)
                .build();

        businessReportData.put(1486571526772764673L, Collections.singletonList(businessReport));
        businessReportData.put(1486251529940783106L, Collections.singletonList(businessReport1));

        FinanceReport financeReport = FinanceReport.builder()
                .financeNo(CodeUtil.generateCode(CodeEnum.FINANCING_CODE))
                .financeApplyDate("2022-03-25")
                .passTime("2022-03-27 12:06:30")
                .businessType("应收账款")
                .userName("深圳市精锐纵横网络技术有限公司")
                .loanDate("2022-04-01 12:06:30")
                .loanAmount(BigDecimal.valueOf(100000000))
                .annualInterestRate("5%")
                .expireTime("2022-10-01")
                .repaidPrincipal(BigDecimal.ZERO)
                .repaidInterest(BigDecimal.ZERO)
                .repaidTotalAmount(BigDecimal.ZERO)
                .unRepaidPrincipal(BigDecimal.valueOf(100000000))
                .unRepaidInterest(BigDecimal.ZERO)
                .isSettle("未结清")
                .isOverdue("未逾期")
                .overdueDay(0)
                .status("待还款")
                .overdueAmount(BigDecimal.ZERO)
                .settleDate(null)
                .build();

        FinanceReport financeReport1 = FinanceReport.builder()
                .financeNo(CodeUtil.generateCode(CodeEnum.FINANCING_CODE))
                .financeApplyDate("2020-03-25")
                .passTime("2022-03-27 12:06:30")
                .businessType("应收账款")
                .userName("深圳市精锐纵横网络技术有限公司")
                .loanDate("2022-04-01 12:06:30")
                .loanAmount(BigDecimal.valueOf(100000000))
                .annualInterestRate("5%")
                .expireTime("2022-10-01")
                .repaidPrincipal(BigDecimal.ZERO)
                .repaidInterest(BigDecimal.ZERO)
                .repaidTotalAmount(BigDecimal.ZERO)
                .unRepaidPrincipal(BigDecimal.valueOf(100000000))
                .unRepaidInterest(BigDecimal.ZERO)
                .isSettle("未结清")
                .isOverdue("未逾期")
                .overdueDay(0)
                .status("待还款")
                .overdueAmount(BigDecimal.ZERO)
                .settleDate(null)
                .build();

        financeReportData.put(1486571526772764673L, Collections.singletonList(financeReport));
        financeReportData.put(1486251529940783106L, Collections.singletonList(financeReport1));
    }


    public List<BusinessReport> businessReport(String dsName, String datasetName, Map<String, Object> parameters) {
        Object o = parameters.get("userId");

        List<BusinessReport> list;

        if (Objects.nonNull(o)) {
            Long userId = Long.valueOf((String) o);
            list = businessReportData.get(userId);
        } else {
            list = businessReportData.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Object startTime = parameters.get("startTime");
        Object endTime = parameters.get("endTime");
        if (Objects.nonNull(startTime)) {
            list = list.stream().filter(businessReport -> {
                LocalDate financeApplyDate = LocalDate.parse(businessReport.getFinanceApplyDate());
                LocalDate startDate = LocalDate.parse(String.valueOf(startTime));
                return financeApplyDate.isAfter(startDate);
            }).collect(Collectors.toList());
        }
        if (Objects.nonNull(endTime)) {
            list = list.stream().filter(businessReport -> {
                LocalDate financeApplyDate = LocalDate.parse(businessReport.getFinanceApplyDate());
                LocalDate endDate = LocalDate.parse(String.valueOf(endTime));
                return financeApplyDate.isBefore(endDate);
            }).collect(Collectors.toList());
        }
        return list;
    }


    public List<FinanceReport> financeReport(String dsName, String datasetName, Map<String, Object> param) {
        Object o = param.get("userId");

        List<FinanceReport> list;

        if (Objects.nonNull(o)) {
            Long userId = Long.valueOf((String) o);
            list = financeReportData.get(userId);
        } else {
            list = financeReportData.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Object startTime = param.get("startTime");
        Object endTime = param.get("endTime");
        if (Objects.nonNull(startTime)) {
            list = list.stream().filter(businessReport -> {
                LocalDate financeApplyDate = LocalDate.parse(businessReport.getFinanceApplyDate());
                LocalDate startDate = LocalDate.parse(String.valueOf(startTime));
                return financeApplyDate.isAfter(startDate);
            }).collect(Collectors.toList());
        }
        if (Objects.nonNull(endTime)) {
            list = list.stream().filter(businessReport -> {
                LocalDate financeApplyDate = LocalDate.parse(businessReport.getFinanceApplyDate());
                LocalDate endDate = LocalDate.parse(String.valueOf(endTime));
                return financeApplyDate.isBefore(endDate);
            }).collect(Collectors.toList());
        }
        return list;
    }
}
