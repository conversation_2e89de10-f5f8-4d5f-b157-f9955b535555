package org.springblade.report.service;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jmreport.api.data.IDataSetFactory;
import org.jeecg.modules.jmreport.desreport.model.JmPage;
import org.springblade.common.enums.CreditReportEnum;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.Func;
import org.springblade.plugin.jimureport.config.JimuReportTokenService;
import org.springblade.plugin.jimureport.config.JimuUtils;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.report.vo.CreditReport;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 回款分析报表 积木
 *
 * <AUTHOR>
 */

@Component("jiMuRepaymentAnalysisService")
@RequiredArgsConstructor
@Slf4j
public class JiMuRepaymentAnalysisService implements IDataSetFactory {

    private final IReportLoanManageRepaymentPlanService reportLoanManageRepaymentPlanService;
    private final JimuReportTokenService jimuReportTokenService;
    private final ProductDirector productDirector;

    @Override
    public JmPage createPageData(Map<String, Object> parameters) {
        CreditReport creditReport = new CreditReport();
        List<Integer> type = Func.toInt(parameters.get("type")) == -1 ? productDirector.getAllType()
                : Arrays.asList(Func.toInt(parameters.get("type")));
        String month = Func.toStr(parameters.get(CreditReportEnum.MONTH.getName()));
        Integer year = Func.toInt(parameters.get(CreditReportEnum.YEAR.getName()));
        //获取租户信息
        String token = jimuReportTokenService.getToken();
        Map<String, Object> userInfo = jimuReportTokenService.getUserInfo(token);
        User user = (User) userInfo.get("user");
        UserUtils.saveToRequest(user);
        String tentId = user.getTenantId();
        List<CreditReport> reports =TenantBroker.applyAs(tentId,e-> reportLoanManageRepaymentPlanService.repaymentAnalysisReport(type, year, month, tentId));
        CollUtil.reverse(reports);
        List<Map<String, Object>> creditReportMap = null;
        try {
            creditReportMap = creditReport.getCreditReportMap(reports);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        int pageNo = Integer.parseInt(parameters.get("pageNo").toString());
        int pageSize = Integer.parseInt(parameters.get("pageSize").toString());
        JmPage page = JimuUtils.getJmPage(creditReportMap, pageNo, pageSize);
        return page;
    }

    @Override
    public List<Map<String, Object>> createData(Map<String, Object> parameters) {
        CreditReport creditReport = new CreditReport();
        List<Integer> type = Func.toInt(parameters.get("type")) == -1 ? productDirector.getAllType()
                : Arrays.asList(Func.toInt(parameters.get("type")));
        String month = Func.toStr(parameters.get(CreditReportEnum.MONTH.getName()));
        Integer year = Func.toInt(parameters.get(CreditReportEnum.YEAR.getName()));
        //获取租户信息
        String token = jimuReportTokenService.getToken();
        Map<String, Object> userInfo = jimuReportTokenService.getUserInfo(token);
        User user = (User) userInfo.get("user");
        UserUtils.saveToRequest(user);
        String tentId = user.getTenantId();
        List<CreditReport> reports =TenantBroker.applyAs(tentId,e-> reportLoanManageRepaymentPlanService.repaymentAnalysisReport(type, year, month, tentId));
        CollUtil.reverse(reports);
        List<Map<String, Object>> creditReportMap = null;
        try {
            return creditReport.getCreditReportMap(reports);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
