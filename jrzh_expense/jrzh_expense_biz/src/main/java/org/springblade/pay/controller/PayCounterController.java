package org.springblade.pay.controller;

import cn.hutool.core.bean.BeanUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.constant.AuthConstant;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.otherapi.core.dto.PayGoodsOrderNotify;
import org.springblade.pay.constant.PayOnlineEnum;
import org.springblade.pay.dto.PayGoodsOrderNotifyResp;
import org.springblade.pay.dto.PayOnlineBankParam;
import org.springblade.pay.dto.PayOnlineScanParam;
import org.springblade.pay.service.IPayOnlineService;
import org.springblade.pay.vo.PayGoodsOrderVO;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 费用还款新/收银台
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023/7/3 11:10
 * @Description: 收银台
 * @Version: 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/pay/external/pay-counter")
@Api(value = "支付接口", tags = "支付接口")
public class PayCounterController extends BladeController {
    private final IPayOnlineService payOnlineService;

    /**
     * 支付订单(带业务)创建并返回二维码
     *
     * @param goods
     * @param payOnlineEnum 指定业务
     * @return
     */
    @PostMapping("/getQrCode/{onlineEnum}")
    public R getQrCode(PayGoodsOrderVO goods, @PathVariable(value = "onlineEnum") PayOnlineEnum payOnlineEnum) {
        PayOnlineScanParam payOnlineScanParam = BeanUtil.copyProperties(goods, PayOnlineScanParam.class);
        return R.data(payOnlineService.getQrCode(payOnlineScanParam, payOnlineEnum));
    }

    /**
     * 支付订单(带业务)创建并返回二维码(新)
     *
     * @param payOnlineScanParam
     * @param payOnlineEnum 指定业务
     * @return
     */
    @PostMapping("/v2/getQrCode/{onlineEnum}")
    public R getQrCodeV2(@RequestBody PayOnlineScanParam payOnlineScanParam, @PathVariable(value = "onlineEnum")
     PayOnlineEnum payOnlineEnum) {
        return R.data(payOnlineService.getQrCodeV2(payOnlineScanParam, payOnlineEnum));
    }

    /**
     * 费用订单支付订单创建并返回二维码
     *
     * @param goods
     * @return
     */
    @PostMapping("/getQrCode")
    public R getQrCode(PayGoodsOrderVO goods) {
        PayOnlineScanParam payOnlineScanParam = BeanUtil.copyProperties(goods, PayOnlineScanParam.class);
        return R.data(payOnlineService.getQrCode(payOnlineScanParam, PayOnlineEnum.BILL_ONLINE_SERVICE));
    }

    /**
     * 费用订单网银下单接口
     *
     * @param bank        银行编码
     * @param callbackUrl 回调到前端的路径
     * @param financeNo   融资编号
     * @param type        类型
     * @param business    业务类型 B2C:个人支付 B2B:企业支付
     * @return
     */
    @PostMapping("/onlinePay")
    public R<Map<String, Object>> onlinePay(@RequestParam String bank, @RequestParam String callbackUrl,
                                            @RequestParam String financeNo, @RequestParam Integer type,
                                            @RequestParam String business) {
        PayOnlineBankParam payOnlineBankParam = new PayOnlineBankParam();
        payOnlineBankParam.setBank(bank);
        payOnlineBankParam.setCallbackUrl(callbackUrl);
        payOnlineBankParam.setFinanceNo(financeNo);
        payOnlineBankParam.setType(type);
        payOnlineBankParam.setBusiness(business);
        return R.data(payOnlineService.onlinePay(payOnlineBankParam, PayOnlineEnum.BILL_ONLINE_SERVICE));
    }

    /**
     * 网银下单(带业务)接口
     *
     * @param bank          银行编码
     * @param callbackUrl   回调到前端的路径
     * @param financeNo     融资编号
     * @param type          类型
     * @param business      业务类型 B2C:个人支付 B2B:企业支付
     * @param payOnlineEnum 指定业务
     * @return
     */
    @PostMapping("/onlinePay/{onlineEnum}")
    public R<Map<String, Object>> onlinePay(@RequestParam String bank, @RequestParam String callbackUrl,
                                            @RequestParam String financeNo, @RequestParam Integer type,
                                            @RequestParam String business, @PathVariable(value = "onlineEnum") PayOnlineEnum payOnlineEnum) {
        PayOnlineBankParam payOnlineBankParam = new PayOnlineBankParam();
        payOnlineBankParam.setBank(bank);
        payOnlineBankParam.setCallbackUrl(callbackUrl);
        payOnlineBankParam.setFinanceNo(financeNo);
        payOnlineBankParam.setType(type);
        payOnlineBankParam.setBusiness(business);
        return R.data(payOnlineService.onlinePay(payOnlineBankParam, payOnlineEnum));
    }

    /**
     * 费用订单网银下单接口(新)
     *
     * @param bank        银行编码
     * @param callbackUrl 回调到前端的路径
     * @param financeNo   融资编号
     * @param type        类型
     * @param ids         需要支付的缴纳订单ids
     * @param business    业务类型 B2C:个人支付 B2B:企业支付
     * @return
     */
    @PostMapping("/v2/onlinePay")
    public R<Map<String, Object>> v2onlinePay(@RequestParam String bank, @RequestParam String callbackUrl,
                                              @RequestParam String financeNo, @RequestParam Integer type,
                                              @RequestParam String business, @RequestParam String ids) {
        PayOnlineBankParam payOnlineBankParam = new PayOnlineBankParam();
        payOnlineBankParam.setBank(bank);
        payOnlineBankParam.setCallbackUrl(callbackUrl);
        payOnlineBankParam.setFinanceNo(financeNo);
        payOnlineBankParam.setType(type);
        payOnlineBankParam.setBusiness(business);
        payOnlineBankParam.setNewVersion(true);
        return R.data(payOnlineService.onlinePayV2(payOnlineBankParam, PayOnlineEnum.BILL_ONLINE_SERVICE));
    }

    /**
     * 网银下单(带业务)接口(新)
     *
     * @param payOnlineEnum 指定业务
     * @return
     */
    @PostMapping("/v2/onlinePay/{onlineEnum}")
    public R<Map<String, Object>> v2onlinePay(@RequestBody PayOnlineBankParam payOnlineBankParam, @PathVariable(value = "onlineEnum") PayOnlineEnum payOnlineEnum) {
        return R.data(payOnlineService.onlinePayV2(payOnlineBankParam, payOnlineEnum));
    }


    /**
     * 支付回调
     *
     * @param payGoodsOrderNotify 支付回调信息
     * @param type                类型
     * @param financeNo           融资单号
     * @return
     */
    @PostMapping("/pay/notify/{tenantId}/{type}/{financeNo}/{service}")
    public PayGoodsOrderNotifyResp payNotify(@RequestBody PayGoodsOrderNotify payGoodsOrderNotify,
                                             @PathVariable(value = "tenantId") String tenantId,
                                             @PathVariable(value = "type") String type, @PathVariable(value = "financeNo") String financeNo, @PathVariable(value = "service") Integer service) {
        PayGoodsOrderNotifyResp payGoodsOrderNotifyResp = TenantBroker.applyAs(tenantId, e -> payOnlineService.payNotify(payGoodsOrderNotify, type, financeNo, PayOnlineEnum.getEnumByCode(service)));
        return payGoodsOrderNotifyResp;
    }

    /**
     * 申请-支付退款
     *
     * @param orderNo 商户订单号
     * @param amount  退款金额
     */
    @PostMapping(value = "/refundApply")
    public R<Boolean> refundApply(@RequestParam String orderNo, @RequestParam BigDecimal amount) {
//		return R.status(payService.refundApply(orderNo, amount));
        return R.status(false);
    }

    /**
     * 获取银行列表编码并分组
     *
     * @return org.springblade.core.tool.api.R
     * @Description: 获取银行列表编码并分组
     * @Author: wujm
     * @Date: 2023/6/3 17:17
     **/
    @GetMapping("/select/bank/list")
    @ApiOperationSupport(order = 8)
    @PreAuth(AuthConstant.PERMIT_ALL)
    @ApiOperation(value = "获取银行列表编码并分组", notes = "获取银行列表编码并分组")
    public R listGroupCode(
    ) {
        return R.data(payOnlineService.listGroupCode());
    }
}
