package org.springblade.pay.service.impl;

import lombok.AllArgsConstructor;
import org.springblade.othersapi.paymanager.dto.ProductFeeVO;
import org.springblade.othersapi.paymanager.service.IPayManagerService;
import org.springblade.pay.service.IPayMergeService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 三方支付
 */
@Service
@AllArgsConstructor
public class PayMergeServiceImpl implements IPayMergeService {

    private final IPayManagerService payManagerService;

    /**
     * 查询代付手续费
     *
     * @param amount 付款金额
     * @return 手续费对象
     */
    @Override
    public ProductFeeVO queryProductTransfer(BigDecimal amount, String merchantNo) {
        return payManagerService.queryProductTransfer(amount,merchantNo);
    }
}
