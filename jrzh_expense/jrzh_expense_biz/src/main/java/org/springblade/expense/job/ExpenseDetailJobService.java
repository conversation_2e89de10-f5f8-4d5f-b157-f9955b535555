package org.springblade.expense.job;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.handler.ExpenseOrderDetailCalHandler;
import org.springblade.expense.service.IExpenseOrderDetailBaseService;
import org.springblade.expense.service.impl.ProductExpenseServiceImpl;
import org.springblade.product.expense.service.IGoodsExpenseRuleService;
import org.springblade.resource.cache.ParamCache;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 费用详情定时任务
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-25  11:49
 * @Description: 费用定时任务
 * @Version: 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ExpenseDetailJobService {
    private final IExpenseOrderDetailBaseService expenseOrderDetailService;
    private final IGoodsExpenseRuleService goodsExpenseRuleService;
    private final List<ExpenseOrderDetailCalHandler> expenseOrderDetailCreatedHandlers;
    private final ProductExpenseServiceImpl productExpenseService;

    /**
     * 执行定时任务 进行重新计算费用详情
     */
    public void doJob() {
        //1、查询系统中需要进行重新计算的费用详情
        List<ExpenseOrderDetail> expenseOrderDetailList = expenseOrderDetailService.listNormalAndDynamics()
                .stream().filter(e -> ObjectUtil.isNotEmpty(e.getUserId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(expenseOrderDetailList)) {
            return;
        }
        //2、进行归类 按用户
        Map<Long, List<ExpenseOrderDetail>> userExpenseOrderDetail =
                expenseOrderDetailList.stream().collect(Collectors.groupingBy(ExpenseOrderDetail::getUserId));
        //3、TODO 发送到mq中让其均匀消费 暂时量不多 一次性解决
//        for (Long userId : userExpenseOrderDetail.keySet()) {
//            String needReCalExpenseId = userExpenseOrderDetail.get(userId).stream().map(e -> e.getId().toString())
//                    .collect(Collectors.joining(StringPool.COMMA));
//            sendMsg(needReCalExpenseId);
//        }
        //查询是否需要重新拿规则来计算
        Boolean needRegen = Boolean.parseBoolean(ParamCache.getValue("EXPENSE_RULE_RE"));
        //3、按用户进行消费 避免一个用户异常导致所有用户都异常的情况
        for (Map.Entry<Long, List<ExpenseOrderDetail>> userExpense : userExpenseOrderDetail.entrySet()) {
            Long userId = userExpense.getKey();
            List<ExpenseOrderDetail> expenseOrderDetailsCal = userExpense.getValue();
            try {
                //重新计算费用详情
                productExpenseService.reCalExpenseOrderDetailRecalculate(expenseOrderDetailsCal, needRegen);
            } catch (Exception e) {
                log.error("用户userId:" + userId + "执行费用详情计算时出现异常");
                e.printStackTrace();
            }
        }
    }

}
