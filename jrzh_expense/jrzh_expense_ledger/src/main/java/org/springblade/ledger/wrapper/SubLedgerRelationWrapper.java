package org.springblade.ledger.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ledger.entity.SubLedgerRelation;
import org.springblade.ledger.vo.SubLedgerRelationVO;

import java.util.Objects;

/**
 * 分账关系表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-02-19
 */
public class SubLedgerRelationWrapper extends BaseEntityWrapper<SubLedgerRelation, SubLedgerRelationVO> {

    public static SubLedgerRelationWrapper build() {
        return new SubLedgerRelationWrapper();
    }

    @Override
    public SubLedgerRelationVO entityVO(SubLedgerRelation SubLedgerRelation) {
        SubLedgerRelationVO SubLedgerRelationVO = Objects.requireNonNull(BeanUtil.copy(SubLedgerRelation, SubLedgerRelationVO.class));

        //User createUser = UserCache.getUser(SubLedgerRelation.getCreateUser());
        //User updateUser = UserCache.getUser(SubLedgerRelation.getUpdateUser());
        //SubLedgerRelationVO.setCreateUserName(createUser.getName());
        //SubLedgerRelationVO.setUpdateUserName(updateUser.getName());

        return SubLedgerRelationVO;
    }
}
