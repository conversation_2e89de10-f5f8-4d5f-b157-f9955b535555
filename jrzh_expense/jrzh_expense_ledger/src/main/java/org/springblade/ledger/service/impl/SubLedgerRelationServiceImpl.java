/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.ledger.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.ledger.entity.SubLedgerRelation;
import org.springblade.ledger.mapper.SubLedgerRelationMapper;
import org.springblade.ledger.service.ISubLedgerRelationService;
import org.springblade.ledger.vo.SubLedgerRelationVO;
import org.springframework.stereotype.Service;

/**
 * 分账关系表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-19
 */
@Service
public class SubLedgerRelationServiceImpl extends BaseServiceImpl<SubLedgerRelationMapper, SubLedgerRelation> implements ISubLedgerRelationService {

	@Override
	public IPage<SubLedgerRelationVO> selectSubLedgerRelationPage(IPage<SubLedgerRelationVO> page, SubLedgerRelationVO subLedgerRelation) {
		return page.setRecords(baseMapper.selectSubLedgerRelationPage(page, subLedgerRelation));
	}

}
