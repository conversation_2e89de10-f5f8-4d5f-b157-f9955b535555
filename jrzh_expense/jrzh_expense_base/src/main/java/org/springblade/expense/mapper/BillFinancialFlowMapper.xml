<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.expense.mapper.BillFinancialFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="billFinancialFlowResultMap" type="org.springblade.expense.entity.BillFinancialFlow">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="transaction_flow_no" property="transactionFlowNo"/>
        <result column="financial_type" property="financialType"/>
        <result column="payment_channel_no" property="paymentChannelNo"/>
        <result column="customer_id" property="customerId"/>
        <result column="finance_apply_id" property="financeApplyId"/>
        <result column="income_status" property="incomeStatus"/>
        <result column="amount" property="amount"/>
        <result column="finance_no" property="financeNo"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectBillFinancialFlowPage" resultMap="billFinancialFlowResultMap">
        select * from jrzh_bill_financial_flow where is_deleted = 0
    </select>
    <select id="queryShowChart" resultType="org.springblade.expense.entity.BillFinancialFlow">
        SELECT DATE(create_time) as create_time,SUM(amount) as amount FROM jrzh_bill_financial_flow where DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[ <= ]]> date(create_time)
        and is_deleted = 0
        <if test="incomeStatus!=null">
            and income_status = #{incomeStatus}
        </if>
        GROUP BY DATE(create_time) order by create_time
    </select>
    <select id="yesTerDayIncomeCount" resultType="java.lang.Integer">
        SELECT sum(amount) as amount FROM jrzh_bill_financial_flow WHERE TO_DAYS( NOW( ) ) - TO_DAYS( create_time) = 1 and income_status =1 and is_deleted = 0
    </select>

    <select id="yesTerDayExpenditureCount" resultType="java.lang.Integer">
        SELECT sum(amount) as shozhi FROM jrzh_bill_financial_flow WHERE TO_DAYS( NOW( ) ) - TO_DAYS( create_time) = 1 and income_status =2 and is_deleted = 0
    </select>

</mapper>
