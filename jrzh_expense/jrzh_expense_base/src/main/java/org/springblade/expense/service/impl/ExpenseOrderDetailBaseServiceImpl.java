
package org.springblade.expense.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.constant.ExpenseOrderDetailStatusEnum;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.mapper.ExpenseOrderDetailMapper;
import org.springblade.expense.service.IExpenseOrderDetailBaseService;
import org.springblade.resource.constant.IncomeDetailConstant;
import org.springblade.resource.entity.IncomeDetail;
import org.springblade.resource.service.IIncomeDetailService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 费用订单详情 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Service
@RequiredArgsConstructor
public class ExpenseOrderDetailBaseServiceImpl extends BaseServiceImpl<ExpenseOrderDetailMapper, ExpenseOrderDetail> implements IExpenseOrderDetailBaseService {
    /**
     * 费用类型id,业务编号
     */
    private final static String INCOME_DETAIL_FORMAT = "%s,%s";
    private final IIncomeDetailService incomeDetailService;

    @Override
    public List<ExpenseOrderDetail> saveExpenseOrderDetail(List<ExpenseOrderDetail> expenseOrderDetails) {
        saveOrUpdateBatch(expenseOrderDetails);
        return expenseOrderDetails;
    }

    @Override
    public List<ExpenseOrderDetail> listByBillExpenseNo(String billExpenseNo) {
        return list(Wrappers.<ExpenseOrderDetail>lambdaQuery().ne(ExpenseOrderDetail::getStatus, ExpenseOrderDetailStatusEnum.INVALID.getStatus()).eq(ExpenseOrderDetail::getBillExpenseNo, billExpenseNo));
    }

    @Override
    public List<ExpenseOrderDetail> listByFinanceNoAndType(String financeNo, Integer type) {
        return list(new LambdaQueryWrapper<ExpenseOrderDetail>().eq(ExpenseOrderDetail::getFinanceNo, financeNo)
                .ne(ExpenseOrderDetail::getStatus, ExpenseOrderDetailStatusEnum.INVALID.getStatus())
                .eq(ObjectUtil.isNotEmpty(type), ExpenseOrderDetail::getType, type));
    }

    @Override
    public List<ExpenseOrderDetail> listNormalAndDynamics() {
        return list(Wrappers.<ExpenseOrderDetail>lambdaQuery()
                .eq(ExpenseOrderDetail::getStatus, ExpenseOrderDetailStatusEnum.NORMAL.getStatus()));
    }

    @Override
    public boolean changeStatusByBillExpenseNo(String billExpenseNo, Integer status) {
        return update(Wrappers.<ExpenseOrderDetail>lambdaUpdate()
                .set(ExpenseOrderDetail::getStatus, status)
                .eq(ExpenseOrderDetail::getBillExpenseNo, billExpenseNo));
    }

    @Override
    public boolean changeStatusByIds(List<Long> ids, Integer status, List<Integer> feeNode, List<Integer> collectFee) {
        return update(Wrappers.<ExpenseOrderDetail>lambdaUpdate()
                .set(ExpenseOrderDetail::getStatus, status)
                .in(CollUtil.isNotEmpty(feeNode), ExpenseOrderDetail::getFeeNode, feeNode)
                .in(CollUtil.isNotEmpty(collectFee), ExpenseOrderDetail::getCollectFeesNode, collectFee)
                .ne(ExpenseOrderDetail::getStatus, ExpenseOrderDetailStatusEnum.CLOSE.getStatus())
                .in(ExpenseOrderDetail::getId, ids));
    }

    @Override
    public boolean closeByIds(List<Long> ids) {
        return update(Wrappers.<ExpenseOrderDetail>lambdaUpdate()
                .set(ExpenseOrderDetail::getStatus, ExpenseOrderDetailStatusEnum.CLOSE.getStatus())
                .in(ExpenseOrderDetail::getId, ids));
    }

    @Override
    public List<ExpenseOrderDetail> calPayedIncomeDetail(List<ExpenseOrderDetail> expenseOrderDetails) {
        if (CollUtil.isEmpty(expenseOrderDetails)) {
            return expenseOrderDetails;
        }
        //初始化
        for (ExpenseOrderDetail expenseOrderDetail : expenseOrderDetails) {
            expenseOrderDetail.setPayed(BigDecimal.ZERO);
        }
        //统计
        List<String> list = expenseOrderDetails.stream().map(e -> String.format(INCOME_DETAIL_FORMAT, e.getRuleId(), e.getFinanceNo()))
                .collect(Collectors.toList());
        List<IncomeDetail> incomeDetails = incomeDetailService.listByTypeAndBizNo(IncomeDetailConstant.EXPENSE_ORDER_DETAIL_USED_AMOUNT, list);
        if (CollUtil.isNotEmpty(incomeDetails)) {
            Map<String, List<IncomeDetail>> incomeDetailMap = incomeDetails.stream().collect(Collectors.groupingBy(IncomeDetail::getBizNo));
            Map<String, List<ExpenseOrderDetail>> expenseOrderDetailsMap = expenseOrderDetails.stream()
                    .collect(Collectors.groupingBy(e -> String.format(INCOME_DETAIL_FORMAT, e.getRuleId(), e.getFinanceNo())));
            for (String key : expenseOrderDetailsMap.keySet()) {
                if (incomeDetailMap.containsKey(key)) {
                    for (ExpenseOrderDetail expenseOrderDetail : expenseOrderDetailsMap.get(key)) {
                        //计算金额
                        BigDecimal payedAmount = calPayedAmount(incomeDetailMap.get(key));
                        expenseOrderDetail.setPayed(payedAmount);
                    }
                }
            }
        }
        return expenseOrderDetails;
    }

    @Override
    public void lockAndPay(String billExpenseNo, Integer status) {
        //费用详情收入记录
        incomeDetailSave(billExpenseNo, true);
        //状态锁定
        changeStatusByBillExpenseNo(billExpenseNo, status);
    }

    @Override
    public void lockAndRefund(String billExpenseNo, Integer status) {
        //费用详情退款记录
        incomeDetailSave(billExpenseNo, false);
        //状态锁定
        changeStatusByBillExpenseNo(billExpenseNo, status);
    }

    private void incomeDetailSave(String billExpenseNo, Boolean add) {
        IncomeDetailConstant type = IncomeDetailConstant.EXPENSE_ORDER_DETAIL_USED_AMOUNT;
        List<ExpenseOrderDetail> expenseOrderDetails = listByBillExpenseNo(billExpenseNo);
        if (CollUtil.isEmpty(expenseOrderDetails)) {
            return;
        }
        List<IncomeDetail> incomeDetailList = new LinkedList<>();
        for (ExpenseOrderDetail expenseOrderDetail : expenseOrderDetails) {
            //添加收入
            IncomeDetail incomeDetail;
            if (add) {
                incomeDetail = incomeDetailService.add(expenseOrderDetail.getUserId(), expenseOrderDetail.getAmount(), type,
                        String.format(INCOME_DETAIL_FORMAT, expenseOrderDetail.getRuleId(), expenseOrderDetail.getFinanceNo()), "");
            } else {
                incomeDetail = incomeDetailService.sub(expenseOrderDetail.getUserId(), expenseOrderDetail.getAmount(), type,
                        String.format(INCOME_DETAIL_FORMAT, expenseOrderDetail.getRuleId(), expenseOrderDetail.getFinanceNo()), "");
            }
            incomeDetailList.add(incomeDetail);
        }
        incomeDetailService.saveAll(incomeDetailList);
    }

    @Override
    public void removeUnRelationOrderDetail(String financeNo, Integer type) {
        List<Long> needRemoveIds = list(Wrappers.<ExpenseOrderDetail>lambdaQuery()
                .select(ExpenseOrderDetail::getId, ExpenseOrderDetail::getBillExpenseNo)
                .eq(ExpenseOrderDetail::getFinanceNo, financeNo)
                .eq(ExpenseOrderDetail::getType, type)).stream()
                .filter(e -> StringUtil.isBlank(e.getBillExpenseNo()))
                .map(ExpenseOrderDetail::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(needRemoveIds)) {
            removeByIds(needRemoveIds);
        }
    }

    private BigDecimal calPayedAmount(List<IncomeDetail> incomeDetails) {
        BigDecimal val = incomeDetails.stream().map(IncomeDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return val.compareTo(BigDecimal.ZERO) > 0 ? val : BigDecimal.ZERO;
    }
}
