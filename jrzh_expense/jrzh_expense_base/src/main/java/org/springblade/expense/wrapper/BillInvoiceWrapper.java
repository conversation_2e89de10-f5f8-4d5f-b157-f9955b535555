package org.springblade.expense.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.expense.entity.BillInvoice;
import org.springblade.expense.vo.BillInvoiceDetailedVO;
import org.springblade.expense.vo.BillInvoiceVO;

import java.util.Objects;

/**
 * 发票基本信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public class BillInvoiceWrapper extends BaseEntityWrapper<BillInvoice, BillInvoiceVO> {

    public static BillInvoiceWrapper build() {
        return new BillInvoiceWrapper();
    }

    @Override
    public BillInvoiceVO entityVO(BillInvoice BillInvoice) {
        BillInvoiceVO BillInvoiceVO = Objects.requireNonNull(BeanUtil.copy(BillInvoice, BillInvoiceVO.class));

        //User createUser = UserCache.getUser(BillInvoice.getCreateUser());
        //User updateUser = UserCache.getUser(BillInvoice.getUpdateUser());
        //BillInvoiceVO.setCreateUserName(createUser.getName());
        //BillInvoiceVO.setUpdateUserName(updateUser.getName());

        return BillInvoiceVO;
    }

    public BillInvoiceDetailedVO entityDetailedVO(BillInvoice billInvoice) {
        if (ObjectUtil.isEmpty(billInvoice)) {
            return null;
        }
        return BeanUtil.copyProperties(billInvoice, BillInvoiceDetailedVO.class);
    }
}
