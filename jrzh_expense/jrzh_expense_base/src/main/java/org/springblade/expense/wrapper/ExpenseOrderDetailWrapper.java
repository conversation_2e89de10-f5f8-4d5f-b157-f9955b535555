package org.springblade.expense.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.vo.ExpenseOrderDetailVO;

import java.util.Objects;

/**
 * 费用订单详情包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public class ExpenseOrderDetailWrapper extends BaseEntityWrapper<ExpenseOrderDetail, ExpenseOrderDetailVO> {

    public static ExpenseOrderDetailWrapper build() {
        return new ExpenseOrderDetailWrapper();
    }

    @Override
    public ExpenseOrderDetailVO entityVO(ExpenseOrderDetail ExpenseOrderDetail) {
        ExpenseOrderDetailVO ExpenseOrderDetailVO = Objects.requireNonNull(BeanUtil.copy(ExpenseOrderDetail, ExpenseOrderDetailVO.class));

        //User createUser = UserCache.getUser(ExpenseOrderDetail.getCreateUser());
        //User updateUser = UserCache.getUser(ExpenseOrderDetail.getUpdateUser());
        //ExpenseOrderDetailVO.setCreateUserName(createUser.getName());
        //ExpenseOrderDetailVO.setUpdateUserName(updateUser.getName());

        return ExpenseOrderDetailVO;
    }
}
