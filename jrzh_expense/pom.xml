<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jrzh_financing</artifactId>
        <groupId>org.springblade</groupId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>
       <modelVersion>4.0.0</modelVersion>


    <artifactId>jrzh_expense</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>jrzh_expense_core</module>
        <module>jrzh_expense_deposit</module>
        <module>jrzh_expense_repayment</module>
        <module>jrzh_expense_common</module>
        <module>jrzh_expense_ledger</module>
        <module>jrzh_expense_core_api</module>
        <module>jrzh_expense_biz</module>
        <module>jrzh_expense_base</module>
    </modules>

    <dependencies>
        <dependency>
            <artifactId>jrzh_system_api</artifactId>
            <groupId>org.springblade</groupId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>jrzh_resource</artifactId>
            <groupId>org.springblade</groupId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>



</project>