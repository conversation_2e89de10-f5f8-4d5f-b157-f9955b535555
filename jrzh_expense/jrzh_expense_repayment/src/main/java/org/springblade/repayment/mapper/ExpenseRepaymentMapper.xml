<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.repayment.mapper.ExpenseRepaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="loanManageRepaymentResultMap" type="org.springblade.repayment.entity.ExpenseRepayment">
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
        <result column="repayment_no" property="repaymentNo"/>
        <result column="iou_no" property="iouNo"/>
        <result column="financing_id" property="financingId"/>
        <result column="period" property="period"/>
        <result column="period_des" property="periodDes"/>
        <result column="should_amount" property="shouldAmount"/>
        <result column="actual_amount" property="actualAmount"/>
        <result column="repayment_time" property="repaymentTime"/>
        <result column="repayment_type" property="repaymentType"/>
        <result column="fail_reason" property="failReason"/>
    </resultMap>
    <!-- 通用查询映射结果 -->
<!--    <resultMap id="LoanManageOverdueJoinFinanceApplyVOMap" type="org.springblade.repayment.vo.LoanManageOverdueJoinFinanceApplyVO">-->
<!--        <result column="create_user" property="createUser"/>-->
<!--        <result column="create_dept" property="createDept"/>-->
<!--        <result column="create_time" property="createTime"/>-->
<!--        <result column="update_user" property="updateUser"/>-->
<!--        <result column="update_time" property="updateTime"/>-->
<!--        <result column="status" property="status"/>-->
<!--        <result column="is_deleted" property="isDeleted"/>-->
<!--        <result column="id" property="id"/>-->
<!--        <result column="repayment_no" property="repaymentNo"/>-->
<!--        <result column="iou_no" property="iouNo"/>-->
<!--        <result column="financing_id" property="financingId"/>-->
<!--        <result column="period" property="period"/>-->
<!--        <result column="period_des" property="periodDes"/>-->
<!--        <result column="should_amount" property="shouldAmount"/>-->
<!--        <result column="actual_amount" property="actualAmount"/>-->
<!--        <result column="repayment_time" property="repaymentTime"/>-->
<!--        <result column="repayment_type" property="repaymentType"/>-->
<!--        <result column="fail_reason" property="failReason"/>-->
<!--        <result column="process_instance_id" property="processInstanceId"/>-->
<!--        <result column="capital_id" property="capitalId"/>-->
<!--    </resultMap>-->

    <select id="selectLoanManageRepaymentPage" resultMap="loanManageRepaymentResultMap">
        select *
        from jrzh_loan_manage_repayment
        where is_deleted = 0
    </select>
    <select id="queryRepaymentShowChart" resultType="org.springblade.repayment.vo.ExpenseRepaymentVO">
        SELECT DATE(create_time) as create_time, SUM(actual_amount) as actualAmount
        FROM jrzh_loan_manage_repayment
        where DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[ <= ]]> date(create_time)
          and is_deleted = 0
          and status = 3
        GROUP BY DATE(create_time)
        order by create_time
    </select>
    <select id="queryRepaymentCount" resultType="java.lang.Integer">
        SELECT sum(actual_amount) as actualAmount
        FROM jrzh_loan_manage_repayment
        WHERE TO_DAYS(NOW()) - TO_DAYS(create_time) = 1
          and is_deleted = 0
          and status = 3
    </select>
    <select id="queryRepaymentStrokeCount" resultType="java.lang.Integer">
        SELECT count(1) as stroke
        FROM jrzh_loan_manage_repayment
        WHERE TO_DAYS(NOW()) - TO_DAYS(create_time) = 1
          and is_deleted = 0
          and status = 3
    </select>

    <select id="payLoanList" resultType="org.springblade.repayment.entity.ExpenseRepayment">
        SELECT * FROM `jrzh_loan_manage_repayment`
        WHERE repayment_time like concat(concat(#{time},'%'))
        and `status` = 3
        and is_deleted = 0
        <if test="type == 1 or type == 2">
            and goods_type = #{type}
        </if>
        <if test="tentId != null and tentId != ''">
            and tenant_id = #{tentId}
        </if>
    </select>

    <select id="payLoanByNameList" resultType="org.springblade.repayment.entity.ExpenseRepayment">
        SELECT * FROM `jrzh_loan_manage_repayment`
        WHERE repayment_time like concat(concat(#{time},'%'))
        and `status` = 3
        and is_deleted = 0
        <if test="type == 1 or type == 2">
            and goods_type = #{type}
        </if>
        <if test="userId != null and userId != 0">
            and user_id = #{userId}
        </if>
        <if test="tentId != null and tentId != ''">
            and tenant_id = #{tentId}
        </if>
    </select>

    <select id="queryRepaymentByUserIdAndRecentDay" resultType="org.springblade.repayment.vo.ExpenseRepaymentVO">
        SELECT * from (SELECT *, DATE_FORMAT(repayment_time,'%Y-%m-%d') as times
            from jrzh_loan_manage_repayment
                where DATE_SUB(CURDATE(), INTERVAL #{recentDay} DAY) <![CDATA[ <= ]]> date(repayment_time)
                  and user_id= #{userId}
                  and status=3
                  and is_deleted = 0 ) a ORDER BY a.times desc
    </select>

<!--    <select id="selectListOverdueJoinFinanceApplyVO" resultMap="LoanManageOverdueJoinFinanceApplyVOMap">-->
<!--        select *from jrzh_loan_manage_repayment_plan p left join jrzh_finance_apply f on p.finance_apply_id =f.id left join jrzh_customer_goods g on p.customer_goods_id =g.id  where f.status in(8,9)-->
<!--                                                                                                                    and p.is_deleted=0 and f.goods_type=1 and g.status=3-->

<!--    </select>-->

<!--    <select id="actualRepaymentByDtoList" resultType="org.springblade.modules.loan.entity.LoanManageRepayment">-->
<!--        SELECT * FROM `jrzh_loan_manage_repayment`-->
<!--            WHERE repayment_time like concat(concat(#{dto.queryTime},'%'))-->
<!--            and `status` = 3-->
<!--            and is_deleted = 0-->
<!--        <if test="dto.type == 1 and dto.type != -1 or dto.type == 2 and dto.type != -1">-->
<!--            and goods_type = #{dto.type}-->
<!--        </if>-->
<!--        <if test="dto.userId != null and dto.userId != 0">-->
<!--            and user_id = #{dto.userId}-->
<!--        </if>-->
<!--        <if test="dto.tenantId != null and dto.tenantId != ''">-->
<!--            and tenant_id = #{dto.tenantId}-->
<!--        </if>-->
<!--    </select>-->
    <select id="selectOverdueHavePaid" resultType="org.springblade.repayment.entity.ExpenseRepayment">
        select repayment.*,term.repayment_plan_id from jrzh_loan_manage_repayment_term term join jrzh_loan_manage_repayment repayment on term.repayment_id = repayment.id
        and repayment.status = 3 and repayment.repayment_type = 3
        <if test="repaymentPlanIdList!=null and repaymentPlanIdList.size>0" >
            and( repayment.repayment_plan_id IN
            <foreach collection="repaymentPlanIdList" item="repaymentPlanIdList" index="index" open="(" close=")" separator=",">
                #{repaymentPlanIdList}
            </foreach>)
        </if>
    </select>

    <select id="selectRepaymentByCustomerGoods" resultType="org.springblade.repayment.entity.ExpenseRepayment">
        SELECT
            c.*
        FROM
            jrzh_finance_apply a
            INNER JOIN jrzh_loan_manage_iou b ON a.id = b.finance_apply_id
            INNER JOIN jrzh_loan_manage_repayment c ON b.iou_no = c.iou_no
        WHERE
            a.customer_goods_id = #{customerGoodsId}
            ORDER BY create_time desc
    </select>

</mapper>
