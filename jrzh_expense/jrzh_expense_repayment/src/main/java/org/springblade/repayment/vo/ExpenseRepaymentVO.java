/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.repayment.vo;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.repayment.entity.ExpenseRepayment;
import org.springblade.repayment.entity.ExpenseRepaymentFee;
import org.springblade.resource.entity.Attach;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 还款列表视图实体类
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LoanManageRepaymentVO对象", description = "还款列表")
public class ExpenseRepaymentVO extends ExpenseRepayment {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("用户名")
	private String userName;

	@ApiModelProperty("还款用户")
	private String customerName;

	/**
	 * 借据号
	 */
	@ApiModelProperty(value = "所属期数+后缀")
	private String periodDes;

	@ApiModelProperty("总金额")
	private BigDecimal totalAmount;

	@ApiModelProperty("实还总金额")
	private BigDecimal actualTotalAmount;

	@ApiModelProperty(value = "支付凭证url集合")
	List<Attach> attachList;

	/**
	 * 还款总额
	 */
	private BigDecimal monthlySupply;
	/**
	 * 利息
	 */
	private BigDecimal monthlyInterest;
	/**
	 * 本金
	 */
	private BigDecimal monthlyPrincipal;
	/**
	 * 服务费
	 */
	private BigDecimal monthlyServiceFee;
	/**
	 * 罚息
	 */
	private BigDecimal monthlyPenaltyInterest;

	/**
	 * 提前结清对应期数
	 *
	 */
	private String settlementPeriodStr;

	/**
	 * 还款记录关联费用表
	 */
	private List<ExpenseRepaymentFeeVO> repaymentFeeList;


	public BigDecimal getTotalAmount() {
		if(CollUtil.isEmpty(repaymentFeeList)){
			repaymentFeeList = Lists.newArrayList();
		}
		return getPrincipal()
			.add(getServiceCharge())
			.add(getPenaltyInterest())
			.add(getInterest())
			.add(getRepaymentFeeList().stream().map(ExpenseRepaymentFee::getShouldAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
	}

	public BigDecimal getActualTotalAmount() {
		return getActualPrincipal().add(getActualServiceCharge()).add(getActualPenaltyInterest()).add(getActualInterest());
	}

	public String getPeriodDes() {
		Integer period = getPeriod();
		if (Objects.isNull(period)) {
			return StringPool.EMPTY;
		}
		if (period == -1) {
			return "提前结清";
		}
		return "第" + period + "期";
	}

	private String times;
}
