package org.springblade.repayment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.repayment.entity.ExpenseRepayment;
import org.springblade.repayment.entity.ExpenseRepaymentFee;
import org.springblade.repayment.entity.ExpenseRepaymentTerm;
import org.springblade.repayment.mapper.ExpenseRepaymentFeeMapper;
import org.springblade.repayment.service.IExpenseRepaymentFeeService;
import org.springblade.repayment.service.IExpenseRepaymentTermService;
import org.springblade.repayment.vo.ExpenseRepaymentFeeVO;
import org.springblade.repayment.vo.ExpenseRepaymentVO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 还款费用表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Service
@AllArgsConstructor
public class ExpenseRepaymentFeeServiceImpl extends BaseServiceImpl<ExpenseRepaymentFeeMapper, ExpenseRepaymentFee> implements IExpenseRepaymentFeeService {


    /**
     * 还款记录和还款计划中间表 服务类
     */
    private final IExpenseRepaymentTermService expenseRepaymentTermService;

    /**
     * 自定义分页
     *
     * @param page 分页
     * @param repaymentFee 条件
     * @return
     */
    @Override
    public IPage<ExpenseRepaymentFeeVO> selectRepaymentFeePage(IPage<ExpenseRepaymentFeeVO> page, ExpenseRepaymentFeeVO repaymentFee) {
        return page.setRecords(baseMapper.selectRepaymentFeePage(page, repaymentFee));
    }

    /**
     * 根据还款记录id查询 所有的还款记录关联费用
     *
     * @param repaymentIds  还款记录id集合
     * @return  还款记录关联费用集合
     */
    @Override
    public List<ExpenseRepaymentFee> getRepaymentFeeByRepaymentIds(List<Long> repaymentIds) {
        if (CollUtil.isEmpty(repaymentIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<ExpenseRepaymentFee>lambdaQuery()
                .in(ExpenseRepaymentFee::getRepaymentId, repaymentIds));
    }

    /**
     * 根据还款记录id查询 未还款的还款记录关联费用
     *
     * @param repaymentIds
     * @return
     */
    @Override
    public List<ExpenseRepaymentFee> getUnRepaymentFeeByRepaymentIds(List<Long> repaymentIds) {
        if (CollUtil.isEmpty(repaymentIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<ExpenseRepaymentFee>lambdaQuery()
                .in(ExpenseRepaymentFee::getRepaymentId, repaymentIds)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAYING.getCode()));
    }

    /**
     * 根据还款记录查询 已支付的还款记录关联费用
     *
     * @param repaymentList 还款记录集合
     * @return
     */
    @Override
    public List<ExpenseRepaymentVO> getPayRepaymentFeeByRepayments(List<ExpenseRepayment> repaymentList) {

        if(CollUtil.isEmpty(repaymentList)){
            return Lists.newArrayList();
        }
        //获取还款记录id集合
        List<Long> expenseRepaymentIds = repaymentList.parallelStream().map(ExpenseRepayment::getId).collect(Collectors.toList());
        // 还款记录关联费用集合
        List<ExpenseRepaymentFee> expenseRepaymentFeeList = baseMapper.selectList(Wrappers.<ExpenseRepaymentFee>lambdaQuery().in(ExpenseRepaymentFee::getRepaymentId, expenseRepaymentIds));
        if (CollUtil.isNotEmpty(expenseRepaymentFeeList)){
            throw new ServiceException("查询不到还款记录关联费用数据！");
        }
        //根据还款记录id分组
        Map<Long, List<ExpenseRepaymentFee>> expenseRepaymentFeeMap = expenseRepaymentFeeList.parallelStream().collect(Collectors.groupingBy(ExpenseRepaymentFee::getRepaymentId));

        //转换vo
        List<ExpenseRepaymentVO> expenseRepaymentVOList = BeanUtil.copyToList(repaymentList, ExpenseRepaymentVO.class);
        for (ExpenseRepaymentVO expenseRepaymentVO : expenseRepaymentVOList) {
            List<ExpenseRepaymentFeeVO> expenseRepaymentFeeVOList= BeanUtil.copyToList(expenseRepaymentFeeMap.get(expenseRepaymentVO.getId()), ExpenseRepaymentFeeVO.class);
            expenseRepaymentVO.setRepaymentFeeList(expenseRepaymentFeeVOList);
        }
        return expenseRepaymentVOList;
    }

    /**
     * 根据还款记录id查询 已支付的还款记录关联费用
     *
     * @param repaymentId
     * @return
     */
    @Override
    public List<ExpenseRepaymentFee> getPayRepaymentFeeList(Long repaymentId) {
        return baseMapper.selectList(Wrappers.<ExpenseRepaymentFee>lambdaQuery()
                .eq(ExpenseRepaymentFee::getRepaymentId, repaymentId)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode()));
    }

    /**
     * 根据还款记录ids查询 已支付的还款记录关联费用
     *
     * @param repaymentIds
     * @return
     */
    @Override
    public List<ExpenseRepaymentFee> getPayRepaymentFeeListByRepaymentIs(List<Long> repaymentIds) {
        if(CollUtil.isEmpty(repaymentIds)){
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<ExpenseRepaymentFee>lambdaQuery()
                .in(ExpenseRepaymentFee::getRepaymentId, repaymentIds)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode()));
    }

    /**
     * 根据还款计划关联费用ids查询 已支付的还款记录关联费用金额
     *
     * @param planFeeIds 还款计划关联费用ids
     * @return
     */
    @Override
    public BigDecimal getPayRepaymentFeeAmount(List<Long> planFeeIds) {
        if(CollUtil.isEmpty(planFeeIds)){
            return BigDecimal.ZERO;
        }
        List<ExpenseRepaymentTerm> termList = expenseRepaymentTermService.list(Wrappers.<ExpenseRepaymentTerm>lambdaQuery()
                .in(ExpenseRepaymentTerm::getRepaymentPlanId, planFeeIds));
        if(CollUtil.isEmpty(termList)){
            return BigDecimal.ZERO;
        }
        List<Long> repaymentFeeId = termList.stream().map(ExpenseRepaymentTerm::getRepaymentId).collect(Collectors.toList());
        List<ExpenseRepaymentFee> feeList = baseMapper.selectList(Wrappers.<ExpenseRepaymentFee>lambdaQuery()
                .in(ExpenseRepaymentFee::getId, repaymentFeeId)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode()));
        return feeList.stream().map(ExpenseRepaymentFee::getActualAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
    }
}
