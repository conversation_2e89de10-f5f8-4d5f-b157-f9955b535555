package org.springblade.repayment.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.repayment.entity.ExpenseRepayment;
import org.springblade.repayment.entity.ExpenseRepaymentPlanFee;
import org.springblade.repayment.entity.ExpenseRepaymentTerm;
import org.springblade.repayment.mapper.ExpenseRepaymentPlanFeeMapper;
import org.springblade.repayment.mapper.ExpenseRepaymentTermMapper;
import org.springblade.repayment.service.IExpenseRepaymentTermService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 还款记录和还款计划中间表 服务类
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Service
@AllArgsConstructor
public class ExpenseRepaymentTermServiceImpl extends BaseServiceImpl<ExpenseRepaymentTermMapper, ExpenseRepaymentTerm> implements IExpenseRepaymentTermService {


    /**
     * 还款计划费用 服务类接口
     */
    private final ExpenseRepaymentPlanFeeMapper expenseRepaymentPlanFeeMapper;

    /**
     * 根据还款计划id查询还款记录
     *
     * @param repaymentPlanIdList 还款计划id
     * @return 还款记录
     */
    @Override
    public List<ExpenseRepayment> selectRepaymentListByRepaymentPlanIds(List<Long> repaymentPlanIdList) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }
        return null;
    }

    /**
     * 根据还款计划id查询列表
     *
     * @param repaymentPlanIdList 还款计划id
     * @return LoanManageRepaymentTerm
     */
    @Override
    public List<ExpenseRepaymentTerm> selectByRepaymentPlanIds(List<Long> repaymentPlanIdList) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }
        return null;
    }

    /**
     * 保存还款记录和还款计划的关系
     *
     * @param repaymentId         还款id
     * @param iouNo
     * @param repaymentPlanIdList 还款计划id
     */
    @Override
    public void saveRepaymentTermList(Long repaymentId, String iouNo, List<Long> repaymentPlanIdList) {

    }

    /**
     * 根据还款计划id查询 还款成功记录
     *
     * @param id 还款计划id
     * @return
     */
    @Override
    public List<ExpenseRepayment> getRepaymentByPlanId(Long id) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }
        return null;
    }

    /**
     * 根据还款计划id集合查询还款记录
     *
     * @param ids 还款计划id
     * @return
     */
    @Override
    public List<ExpenseRepayment> getRepaymentByPlanIds(List<Long> ids) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }
        return null;
    }

    /**
     * 根据还款关联记录id查询 动态还款计划
     *
     * @param id 还款记录id
     * @return
     */
    @Override
    public List<ExpenseRepaymentPlanFee> getRepaymentPlanByRepaymentId(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        List<ExpenseRepaymentTerm> termList = baseMapper.selectList(Wrappers.<ExpenseRepaymentTerm>lambdaQuery()
                .eq(ExpenseRepaymentTerm::getRepaymentId, id));
        List<Long> planFeeIds = termList.stream().map(ExpenseRepaymentTerm::getRepaymentPlanId).collect(Collectors.toList());
        if (CollUtil.isEmpty(planFeeIds)) {
            return Collections.emptyList();
        }
        return expenseRepaymentPlanFeeMapper.selectBatchIds(planFeeIds);
    }
}
