
package org.springblade.repayment.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.config.mybatis.batch.BatchMapper;
import org.springblade.repayment.entity.ExpenseRepaymentTerm;
import org.springblade.repayment.vo.ExpenseRepaymentTermVO;

import java.util.List;


/**
 * 还款记录和还款计划中间表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
public interface ExpenseRepaymentTermMapper extends BatchMapper<ExpenseRepaymentTerm> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param expenseRepaymentTermVO
	 * @return
	 */
	List<ExpenseRepaymentTermVO> selectLoanManageRepaymentTermPage(IPage page,@Param("expenseRepaymentTermVO") ExpenseRepaymentTermVO expenseRepaymentTermVO);

}
