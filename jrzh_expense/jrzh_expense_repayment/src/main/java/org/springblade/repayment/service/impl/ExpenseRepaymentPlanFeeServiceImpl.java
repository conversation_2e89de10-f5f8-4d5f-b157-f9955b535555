package org.springblade.repayment.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.product.common.entity.ExpenseType;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense.service.IExpenseTypeService;
import org.springblade.repayment.entity.ExpenseRepaymentPlan;
import org.springblade.repayment.entity.ExpenseRepaymentPlanFee;
import org.springblade.repayment.entity.ExpenseRepaymentTerm;
import org.springblade.repayment.mapper.ExpenseRepaymentPlanFeeMapper;
import org.springblade.repayment.mapper.ExpenseRepaymentTermMapper;
import org.springblade.repayment.service.IExpenseRepaymentPlanFeeService;
import org.springblade.repayment.vo.ExpenseRepaymentPlanFeeVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 还款计划费用表 服务类
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Service
@AllArgsConstructor
public class ExpenseRepaymentPlanFeeServiceImpl extends BaseServiceImpl<ExpenseRepaymentPlanFeeMapper, ExpenseRepaymentPlanFee> implements IExpenseRepaymentPlanFeeService {


    /**
     * 还款记录和还款计划中间表 mapper
     */
    private final ExpenseRepaymentTermMapper expenseRepaymentTermMapper;

    /**
     * 费用类型 服务类
     */
    private final IExpenseTypeService expenseTypeService;

    /**
     * 自定义分页
     *
     * @param page             分页
     * @param repaymentPlanFee 查询条件
     * @return 还款计划费用分页
     */
    @Override
    public IPage<ExpenseRepaymentPlanFeeVO> selectRepaymentPlanFeePage(IPage<ExpenseRepaymentPlanFeeVO> page, ExpenseRepaymentPlanFeeVO repaymentPlanFee) {
        return page.setRecords(baseMapper.selectRepaymentPlanFeePage(page, repaymentPlanFee));
    }

    /**
     * 根据还款计划id查询 正在使用的计划关联费用表
     *
     * @param planId 还款计划id
     * @return 还款计划费用集合
     */
    @Override
    public List<ExpenseRepaymentPlanFee> getPlanFeeByPlanIds(Long planId) {
        if (Objects.isNull(planId)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<ExpenseRepaymentPlanFee>lambdaQuery()
                .eq(ExpenseRepaymentPlanFee::getPlanId, planId)
                .eq(ExpenseRepaymentPlanFee::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode()));
    }

    /**
     * 根据还款计划id查询 所有的计划关联费用表
     *
     * @param planId 还款计划id
     * @return 还款计划费用集合
     */
    @Override
    public List<ExpenseRepaymentPlanFee> getAllPlanFeeByPlanIds(Long planId) {
        if (Objects.isNull(planId)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<ExpenseRepaymentPlanFee>lambdaQuery()
                .in(ExpenseRepaymentPlanFee::getPlanId, planId));
    }

    /**
     * 根据还款记录关联费用id 查询所有还款计划关联费用
     *
     * @param feeId 还款记录id
     * @return 还款计划费用集合
     */
    @Override
    public List<ExpenseRepaymentPlanFee> getRepaymentFeeListByFeeId(Long feeId) {
        if (Objects.isNull(feeId)) {
            return Collections.emptyList();
        }
        List<ExpenseRepaymentTerm> termList = expenseRepaymentTermMapper.selectList(Wrappers.<ExpenseRepaymentTerm>lambdaQuery()
                .in(ExpenseRepaymentTerm::getRepaymentId, feeId));
        if (CollUtil.isEmpty(termList)) {
            return Collections.emptyList();
        }
        List<Long> feePlanId = termList.stream().map(ExpenseRepaymentTerm::getRepaymentPlanId).collect(Collectors.toList());
        return baseMapper.selectList(Wrappers.<ExpenseRepaymentPlanFee>lambdaQuery().in(BaseEntity::getId, feePlanId));
    }


    /**
     * 根据费用订单详情保存到计划费用表
     *
     * @param expenseOrderDetail 费用订单详情
     * @param repaymentPlanList  还款计划
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePlanFeeByPlatformExpenses(ExpenseOrderDetail expenseOrderDetail, List<ExpenseRepaymentPlan> repaymentPlanList) {
        if (Objects.isNull(expenseOrderDetail) || CollUtil.isEmpty(repaymentPlanList)) {
            return;
        }
        Assert.isTrue(Objects.nonNull(expenseOrderDetail.getExpenseTypeId()), "未查询到费用类型id");
        ExpenseType expenseType = expenseTypeService.getById(expenseOrderDetail.getExpenseTypeId());
        ExpenseType parentExpenseType = expenseTypeService.getById(expenseType.getParentId());

        List<ExpenseRepaymentPlanFee> repaymentPlanFees = new ArrayList<>();
        //判断收费方式
        if (ExpenseConstant.ChargeMethodEnum.ONE_TIME_PAYMENT.getCode().equals(expenseOrderDetail.getChargeMethod())) {
            //一次性支付
            ExpenseRepaymentPlanFee planFee = new ExpenseRepaymentPlanFee();
            ExpenseRepaymentPlan plan = repaymentPlanList.get(0);
            planFee.setCollectFeeMethod(ExpenseConstant.ChargeMethodEnum.ONE_TIME_PAYMENT.getCode());
            builderPlanFee(plan, planFee, expenseOrderDetail.getAmount(), expenseType, parentExpenseType, expenseOrderDetail);
            repaymentPlanFees.add(planFee);
        } else {
            BigDecimal amount = expenseOrderDetail.getAmount();
            BigDecimal periods = new BigDecimal(String.valueOf(repaymentPlanList.size()));
            BigDecimal average = expenseOrderDetail.getAmount().divide(periods, 2, CommonConstant.NUMBER_STRATEGY);
            //分期支付
            repaymentPlanList.forEach(plan -> {
                ExpenseRepaymentPlanFee planFee = new ExpenseRepaymentPlanFee();
                planFee.setCollectFeeMethod(ExpenseConstant.ChargeMethodEnum.INSTALLMENT.getCode());
                builderPlanFee(plan, planFee, average, expenseType, parentExpenseType, expenseOrderDetail);
                repaymentPlanFees.add(planFee);
            });
            //如果总金额与分期费用总金额不等，最后一期做调整     最终最后一期费用 = 总数 - 实际总数 + 最后一期费用
            BigDecimal actualAmount = average.multiply(periods);
            if (amount.compareTo(actualAmount) != 0) {
                ExpenseRepaymentPlanFee repaymentPlanFee = repaymentPlanFees.get(repaymentPlanFees.size() - 1);
                BigDecimal finalAmount = repaymentPlanFee.getAmount().add((amount.subtract(actualAmount)));
                repaymentPlanFee.setAmount(finalAmount);
            }
        }
        saveBatch(repaymentPlanFees);
    }


    private void builderPlanFee(ExpenseRepaymentPlan plan, ExpenseRepaymentPlanFee planFee, BigDecimal amount, ExpenseType expenseType, ExpenseType parentExpenseType, ExpenseOrderDetail expenseOrderDetail) {
        BeanUtil.copyProperties(plan, planFee);
        planFee.setId(null);
        planFee.setPlanId(plan.getId());
        planFee.setAmount(amount);
        planFee.setExpenseTypeId(expenseType.getId());
        planFee.setFeeName(expenseType.getExpenseName());
        planFee.setFeeTypeName(parentExpenseType.getExpenseName());
        planFee.setExpenseKey(parentExpenseType.getExpenseKey());
        planFee.setRelationExpensesId(expenseOrderDetail.getId());
    }
}
