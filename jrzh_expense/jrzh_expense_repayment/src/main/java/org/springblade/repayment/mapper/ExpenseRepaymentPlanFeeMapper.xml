<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.repayment.mapper.ExpenseRepaymentPlanFeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="repaymentPlanFeeResultMap" type="org.springblade.repayment.entity.ExpenseRepaymentPlanFee">
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="id" property="id"/>
        <result column="plan_id" property="planId"/>
        <result column="iou_no" property="iouNo"/>
        <result column="period" property="period"/>
        <result column="repayment_time" property="repaymentTime"/>
        <result column="user_id" property="userId"/>
        <result column="finance_apply_id" property="financeApplyId"/>
        <result column="customer_goods_id" property="customerGoodsId"/>
        <result column="goods_id" property="goodsId"/>
        <result column="repayment_status" property="repaymentStatus"/>
        <result column="iou_id" property="iouId"/>
        <result column="goods_type" property="goodsType"/>
        <result column="amount" property="amount"/>
        <result column="fee_name" property="feeName"/>
        <result column="expense_type" property="expenseType"/>
    </resultMap>


    <select id="selectRepaymentPlanFeePage" resultMap="repaymentPlanFeeResultMap">
        select * from jrzh_repayment_plan_fee where is_deleted = 0
    </select>

</mapper>
