/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.repayment.vo;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.repayment.entity.ExpenseRepaymentPlan;
import org.springblade.repayment.entity.ExpenseRepaymentPlanFee;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 还款计划表视图实体类
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ExpenseRepaymentPlanVO对象", description = "还款计划表")
public class ExpenseRepaymentPlanVO extends ExpenseRepaymentPlan {
	private static final long serialVersionUID = 1L;
	/**
	 * 借款期限、第几期/天数+后缀(期/天)
	 */
	@ApiModelProperty(value = "借款期限、第几期/天数+后缀(期/天)")
	private String periodDes;

	@ApiModelProperty("总金额")
	private BigDecimal totalAmount;

	private Integer type;

	/**
	 * 实还总额
	 */
	private BigDecimal actualAmount;

	/**
	 * 待还总额
	 */
	private BigDecimal unPayAmount;

	/**
	 * 还款记录
	 */
	private List<ExpenseRepaymentVO> loanManageRepaymentVOList;

	/**
	 * 还款计划关联费用
	 */
	private List<ExpenseRepaymentPlanFeeVO> repaymentPlanFeeVOList;

	/**
	 * 提前结清记录
	 * @return
	 */
	private List<ExpenseRepaymentVO> settlementRepaymentVOList;

	public Integer getType() {
		Integer overdue = getOverdue();
		Integer repaymentStatus = getRepaymentStatus();
		int compare = getRepaymentTime().compareTo(LocalDate.now());
		// 未逾期
		if (overdue.equals(RepaymentConstant.RepaymentPlanOverdueEnum.NORMAL.getCode())
			&& repaymentStatus.equals(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
		) {
			// 还款日期等于今天，就是当前还款
			if (compare == 0) {
				return RepaymentConstant.RepaymentPlanStatusEnum.CURRENT.getCode();
			}else {
				// 待还款
				return RepaymentConstant.RepaymentPlanStatusEnum.UN_PAY.getCode();
			}
		}
		// 已逾期
		if (overdue.equals(RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode())) {
			// 如果还款计划还在使用中的话，就是逾期未还，如果已结清的话就是逾期还款
			if (repaymentStatus.equals(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())) {
				return RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode();
			}else {
				return RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE.getCode();
			}
		}
		return 0;
	}

	public BigDecimal getTotalAmount() {
		BigDecimal planFeeAmount = BigDecimal.ZERO;
		if(CollUtil.isNotEmpty(getRepaymentPlanFeeVOList())){
			 planFeeAmount = getRepaymentPlanFeeVOList().stream().map(ExpenseRepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		return getPrincipal().add(getInterest()).add(getPenaltyInterest()).add(getServiceFee()).add(planFeeAmount);
	}


	public String getPeriodDes() {
		return this.getPeriod().toString() + "期";
	}

}
