/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.repayment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 还款计划费用表实体类
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Data
@TableName("jrzh_expense_repayment_plan_fee")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RepaymentPlanFee对象", description = "还款计划关联费用表")
public class ExpenseRepaymentPlanFee extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 还款计划id
	*/
		@ApiModelProperty(value = "还款计划id")
		private Long planId;
	/**
	* 借据单号
	*/
		@ApiModelProperty(value = "借据单号")
		private String iouNo;
	/**
	* 借款期限、第几期/天数
	*/
		@ApiModelProperty(value = "借款期限、第几期/天数")
		private Integer period;
	/**
	* 还款日期
	*/
		@ApiModelProperty(value = "还款日期")
		private LocalDate repaymentTime;
	/**
	* 用户id
	*/
		@ApiModelProperty(value = "用户id")
		private Long userId;
	/**
	* 融资申请id
	*/
		@ApiModelProperty(value = "融资申请id")
		private Long financeApplyId;
	/**
	* 我的产品id
	*/
		@ApiModelProperty(value = "我的产品id")
		private Long customerGoodsId;
	/**
	* 产品id
	*/
		@ApiModelProperty(value = "产品id")
		private Long goodsId;
	/**
	* 还款状态（1 使用中 2 已结清）
	*/
		@ApiModelProperty(value = "还款状态（1 使用中 2 已结清）")
		private Integer repaymentStatus;
	/**
	* 借据单id
	*/
		@ApiModelProperty(value = "借据单id")
		private Long iouId;
	/**
	* 产品类型
	*/
		@ApiModelProperty(value = "产品类型 1应收 2代采")
		private Integer goodsType;
	/**
	* 金额（元）
	*/
		@ApiModelProperty(value = "金额（元）")
		private BigDecimal amount;
	/**
	* 费用名称
	*/
		@ApiModelProperty(value = "费用名称")
		private String feeName;
	/**
	 * 费用分类名称
	 */
	@ApiModelProperty(value = "费用分类名称")
	private String feeTypeName;

	/**
	 * 费用名称id （费用类型表）
	 */
	@ApiModelProperty(value = "费用名称id")
	private Long expenseTypeId;

	/**
	 * 收费方式
	 */
	@ApiModelProperty(value = "收费方式 1一次性付清 2重复支付 ExpenseConstant.ChargeMethodEnum")
	private Integer collectFeeMethod;

	/**
	 * 费用分类键值
	 */
	@ApiModelProperty(value = "费用分类键值  1资方 2平台")
	private Integer expenseKey;

	/**
	 * 关联费用id (平台费用表)
	 */
	@ApiModelProperty(value = "生成的关联id")
	private Long relationExpensesId;



}
