
package org.springblade.deposit.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.deposit.entity.ExpenseDepositBill;
import org.springblade.deposit.vo.ExpenseDepositBillVO;

import java.util.List;

/**
 * 保证金账单 服务类
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
public interface IExpenseDepositBillService extends BaseService<ExpenseDepositBill> {

    /***
     * 根据id查询所有的保证金账单信息
     * @param id 保证金id
     * @return 保证金账单数据集合
     */
    List<ExpenseDepositBillVO> getExpenseDepositBillList(Long id);

}
