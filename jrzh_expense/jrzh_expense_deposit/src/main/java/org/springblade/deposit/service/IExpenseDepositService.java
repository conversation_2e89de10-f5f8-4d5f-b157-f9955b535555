
package org.springblade.deposit.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.deposit.dto.ExpenseDepositConditionDTO;
import org.springblade.deposit.dto.ExpenseDepositDTO;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.entity.ExpenseDepositBill;
import org.springblade.deposit.vo.ExpenseDepositGeneralDetailsVO;
import org.springblade.deposit.vo.ExpenseDepositTotalVo;
import org.springblade.deposit.vo.ExpenseDepositVO;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.refund.dto.RefundDTO;
import org.springblade.refund.entity.Refund;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 保证金 服务类
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
public interface IExpenseDepositService extends BaseService<ExpenseDeposit> {


    /**
     * 保证金分页查询
     *
     * @param query                      分页数量
     * @param expenseDepositConditionDTO 查询条件
     * @return 保证金数据
     */
    IPage<ExpenseDepositVO> selectExpenseDepositPage(Query query, ExpenseDepositConditionDTO expenseDepositConditionDTO);


    /***
     * 新增追保证金
     * @param expenseDepositDTO 追加保证金数据
     * @return true成功 false失败
     */
    Boolean cashDepositSave(ExpenseDepositDTO expenseDepositDTO);

    /***
     * 获取保证金-融资编号列表
     * @return 保证金-融资编号列表
     */
    List<String> getfinanceApplyList();


    /***
     * 查看保证金详情信息
     * @param id 保证金id
     * @return 保证金vo数据
     */
    ExpenseDepositVO getDetail(Long id);

    /***
     * 待缴费 新增
     * @param expenseDeposit  保证金数据
     * @return true成功 false失败
     */
    Boolean toPaidSave(ExpenseDeposit expenseDeposit);

    /***
     * 将带缴费 变为担保中 生成账单数据
     * @param expenseDepositDTO  保证金数据
     * @return true成功 false失败
     */
    Boolean toPaidUpdate(ExpenseDepositDTO expenseDepositDTO);

    /**
     * 判断是否退款
     *
     * @param expenseDeposit 保证金数据
     * @param amount         退款金额
     */
    void judgmentRefundOfDeposit(ExpenseDeposit expenseDeposit, BigDecimal amount);

    /***
     * 根据融资编号 获取初始保证金
     * @param financingNo 融资编号
     * @return 保证金数据
     */
    ExpenseDeposit getCashDeposit(String financingNo);


    /***
     * 关闭保证金接口
     * @param id 保证金id
     */
    void expenseDepositClose(Long id);

    /**
     * 根据融资编号查询保证金 状态修改为 已关闭
     *
     * @param financingNo 融资编号
     * @return true成功 false失败
     */
    Boolean expenseDepositFinancingNoClose(String financingNo);

    /***
     * 根据融资编号查询数据
     * @param financeNo 融资编号
     * @return 返回保证金指定数据
     */
    ExpenseDepositGeneralDetailsVO getExpenseDepositGeneralDetailsVO(String financeNo);


    /***
     * 根据融资编号获取 实缴金额 总额
     * @param financeNo 融资编号
     * @return 保证金总额数据
     */
    ExpenseDepositTotalVo getExpenseDepositTotal(String financeNo);

    /**
     * 生成代缴纳保证金
     *
     * @param expenseDeposit 保证金数据
     * @return true成功 false失败
     */
    Boolean generateCashSave(ExpenseDeposit expenseDeposit);

    /**
     * 根据保证金编号查询 分组
     *
     * @param expenseDepositNos 保证金编号集合
     * @return Map<String, ExpenseDeposit>
     */
    Map<String, ExpenseDeposit> getMapExpenseDepositNo(List<String> expenseDepositNos);

    /**
     * 根据 缴/退类型 查询除 保证金账单数据
     *
     * @param depositId
     * @param payRefundType 缴/退类型 0-退款，1-缴费
     * @return 保证金账单数据
     */
    ExpenseDepositBill selectByType(Long depositId, Integer payRefundType);

    /**
     * 新增保证金账单数据
     *
     * @param expenseDepositBill 保证金账单数据
     * @return true 成功
     */
    Boolean expenseDepositBillSave(ExpenseDepositBill expenseDepositBill);

    /**
     * 根据融资编号查询担保中的保证金
     *
     * @param financeNo
     * @return
     */
    List<ExpenseDeposit> getDepositList(String financeNo);

    /**
     * 保证金 状态修改为 已关闭
     *
     * @param financingNo 融资编号
     * @return
     */
    Boolean closureUpdate(String financingNo);

    /**
     * 保证金退款
     *
     * @param refundDTO 退款信息
     */
    void refundOfDeposit(RefundDTO refundDTO);

    /**
     * 删除保证金
     *
     * @param financeNo 融资编号
     * @return
     */
    boolean removeDeposit(String financeNo);


}
