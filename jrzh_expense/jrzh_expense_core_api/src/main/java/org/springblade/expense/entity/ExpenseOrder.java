/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.expense.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 费用订单实体类
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@TableName("jrzh_expense_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BillExpenseOrder对象", description = "费用订单")
public class ExpenseOrder extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 业务编号 可以是任何
     */
    @ApiModelProperty(value = "业务编号")
    @NotBlank(message = "业务编号不能为空")
    private String bizNo;
    /**
     * 融资编号 后台搜索用
     */
    private String financeNo;
    /**
     * 费用单号
     */
    @ApiModelProperty(value = "费用单号")
    private String billExpenseNo;
    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal amount;
    /**
     * 额外金额 (不在费用详情内的)
     */
    private BigDecimal exAmount;
    /**
     * 费用金额 （仅计算详情）
     */
    private BigDecimal expenseOrderFee;
    /**
     * 额外金额收款的商户号
     */
    private String exAmountMerchantNo;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户id不能为空")
    private Long customerId;
    /**
     * 1、个人 2、企业
     */
    @ApiModelProperty(value = "用户类型")
    @NotNull(message = "用户类型不能为空")
    private Integer customerType;

    /**
     * 支付方式;BillConstant.BillPayNameEnum
     */
    @ApiModelProperty(value = "支付方式;BillPayNameEnum...")
    private Integer paymentMethod;
    /**
     * 支付状态;
     */
    @ApiModelProperty(value = "支付状态;BillPayStatusEnum")
    @NotNull(message = "支付状态不能为空")
    private Integer paymentStatus;
    /**
     * 融资id
     */
    @ApiModelProperty(value = "融资id")
    @NotNull(message = "融资id不能为空")
    private Long financeApplyId;
    /**
     * 支付凭证id
     */
    @ApiModelProperty(value = "支付凭证id")
    private String payAttachId;

    /**
     * 支付流水号
     */
    @ApiModelProperty(value = "支付流水号")
    private String billPaySerialNo;
    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime payTime;
    /**
     * 发票状态 BillConstant.INVOICE_STATUS_...
     */
    @ApiModelProperty(value = "发票状态")
    private Integer invoiceStatus;
    /**
     * 平台费用名称集合
     */
    @ApiModelProperty(value = "平台费用名称集合")
    private String platName;
    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal copeAmount;
    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    private String bank;
    /**
     * 开户账号
     */
    @ApiModelProperty(value = "开户账号")
    private String account;

    @ApiModelProperty("失败原因")
    private String failReason;

    @ApiModelProperty("付款需要确认 0 不需要 1需要 不确认是否已付款情况下设置")
    private String needConfirm;
    /**
     * 支付单号
     */
    private String payOrderNo;
    /**
     * 支付方式 PayModeEnum
     */
    private Integer payMode;
    /**
     * 是否有费用详情 0非 1是
     */
    private Integer hasDetail;
    /**
     * 产品id
     */
    private Long goodsId;
    /**
     * 还款记录id
     */
    private Long repaymentId;
}
