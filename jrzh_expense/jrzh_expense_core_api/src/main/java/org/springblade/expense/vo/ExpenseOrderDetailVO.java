/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.expense.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.expense.entity.ExpenseOrderDetail;

/**
 * 费用订单详情视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ExpenseOrderDetailVO对象", description = "费用订单详情")
public class ExpenseOrderDetailVO extends ExpenseOrderDetail {
    private static final long serialVersionUID = 1L;

    /**
     * 计算节点名称
     */
    String computeName;

    /**
     * 收费节点名称
     */
    String chargeName;

    /**
     * 费用类型名称
     */
    private String expenseName;

    /**
     * 费用类型父级id
     */
    private Long expenseParentId;

    /**
     * 费用类型父级键值
     */
    private Integer expenseParentKey;
}
