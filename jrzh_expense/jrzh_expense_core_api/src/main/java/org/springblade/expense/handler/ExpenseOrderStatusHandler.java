package org.springblade.expense.handler;

import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;

import java.util.List;

/**
 * 费用状态变更处理器
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 费用状态变更处理器
 * @Version: 1.0
 */
public interface ExpenseOrderStatusHandler {
    /**
     * 支持的费用类型
     *
     * @return
     */
    PlatformExpensesEnum support();

    /**
     * 订单创建后调用 可能会重复创建 需要自行做幂等
     *
     * @param expenseOrder 费用订单
     */
    void created(ExpenseOrder expenseOrder, List<ExpenseOrderDetail> expenseOrderDetailList);

    /**
     * 订单关闭后调用 可能会重复创建 需要自行做幂等
     *
     * @param expenseOrder           费用订单
     * @param expenseOrderDetailList 费用订单详情
     */
    void close(ExpenseOrder expenseOrder, List<ExpenseOrderDetail> expenseOrderDetailList);

    /**
     * 订单支付后调用
     *
     * @param expenseOrder           费用订单
     * @param expenseOrderDetailList 费用订单详情
     */
    void payed(ExpenseOrder expenseOrder, List<ExpenseOrderDetail> expenseOrderDetailList);

    /**
     * 订单生成退款订单后调用
     *
     * @param expenseOrder           费用订单
     * @param expenseOrderDetailList 费用订单详情
     */
    void refunded(ExpenseOrder expenseOrder, List<ExpenseOrderDetail> expenseOrderDetailList);
}
