/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.expense.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发票明细数据--前端传入数据参数
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
public class BillInvoiceDetailSaveDTO {
	private static final long serialVersionUID = 1L;

	/**
	 * 税率
	 */
	@NotBlank(message = "税率不能为空")
	private String taxRate;

	/**
	 * 发票附件
	 */
	@NotBlank(message = "发票附件不能为空")
	private String invoiceAttach;

	/**
	 * 邮寄单附件
	 */
	private String sendAttach;

	/**
	 * 融资编号
	 */
	@NotBlank(message = "融资编号不能为空")
	private String financeNo;

	/**
	 * 费用编号
	 */
	@ApiModelProperty(value = "费用编号")
	@NotBlank(message = "费用编号不能为空")
	private String billExpenseNo;

	/**
	 * 快递公司
	 */
	private String logisticsCompanies;

	/**
	 * 运单编号
	 */
	private String waybillNumber;

	/**
	 * 运费
	 */
	private BigDecimal freight;

	/**
	 * 寄件人
	 */
	private String sender;

	/**
	 * 寄件时间
	 */
	private LocalDateTime sendTime;


}
