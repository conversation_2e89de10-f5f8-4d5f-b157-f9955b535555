/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.expense.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 发票基本信息实体类
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@TableName("jrzh_bill_invoice")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BillInvoice对象", description = "发票基本信息")
public class BillInvoice extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 发票编号
	*/
		@ApiModelProperty(value = "发票编号")
		@NotBlank(message = "发票编号不能为空")
		private String invoiceNo;
	/**
	* 发票金额
	*/
		@ApiModelProperty(value = "发票金额")
		@NotNull(message = "发票金额不能为空")
		private BigDecimal amount;
	/**
	* 发票类型;1、增值税专用发票 2、普通发票
	*/
		@ApiModelProperty(value = "发票类型;1、增值税专用发票 2、普通发票")
		@NotNull(message = "发票类型不能为空")
		private Integer type;
	/**
	* 用户id
	*/
		@ApiModelProperty(value = "用户id")
		@NotNull(message = "用户id不能为空")
		private Long customerId;
	/**
	* 用户名称
	*/
		@ApiModelProperty(value = "用户名称")
		@NotBlank(message = "用户名称不能为空")
		private String customerName;
	/**
	* 交付状态;1、待开票 2、已寄出 3、待收票 4、已收票 5、已取消
	*/
		@ApiModelProperty(value = "交付状态;1、待开票 2、已寄出 3、待收票 4、已收票 5、已取消")
		@NotNull(message = "交付状态不能为空")
		private Integer deliveryStatus;
	/**
	* 融资编号
	*/
		@ApiModelProperty(value = "融资编号")
		@NotBlank(message = "融资编号不能为空")
		private String financeNo;
	/**
	* 费用编号
	*/
		@ApiModelProperty(value = "费用编号")
		@NotBlank(message = "费用编号不能为空")
		private String billExpenseNo;

	/**
	 * 发票倒计时天数
	 */
		@ApiModelProperty(value = "发票倒计时天数")
		@NotBlank(message = "发票倒计时天数不能为空")
		private Integer day;

}
