package org.springblade.expense.dto;

import lombok.Data;
import org.springblade.product.common.dto.CapitalPayMethodAndCapitalType;
import org.springblade.product.common.entity.ExpenseType;
import org.springblade.product.common.entity.GoodsExpenseRelation;
import org.springblade.product.common.entity.GoodsExpenseRule;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.vo.BillBankCardaRelationVO;
import org.springblade.resource.entity.IncomeDetail;

import java.util.List;
import java.util.Map;

/**
 * 产品费用配置
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-02  16:23
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class ExpenseGoodsConfig {
    /**
     * 费用配置
     */
    private List<GoodsExpenseRelation> goodsExpenseRelationList;
    /**
     * 产品账号关联 k:费用类型键 v:绑定的银行账号
     */
    private Map<Integer, BillBankCardaRelationVO> billBankCardRelationMap;
    /**
     * 费用规则 k:费用规则id v:费用规则
     */
    private Map<Long, GoodsExpenseRule> goodsExpenseRuleMap;
    /**
     * 费用类型 k:费用类型id v:费用类型
     */
    private Map<Long, ExpenseType> expenseTypeMap;
    /**
     * 支付记录 k:费用类型id,业务编号 v:支付记录列表
     */
    Map<String, List<IncomeDetail>> incomeDetailMap;
    /**
     * 产品信息
     */
    private Product product;
    /**
     *
     */
    private CapitalPayMethodAndCapitalType capitalPayMethodAndCapitalType;
    /**
     * 业务编号
     */
    private String financeNo;
    /**
     * 平台端线上默认账号
     */
    private BillBankCardaRelationVO platOnlineAccount;
    /**
     * 收费节点不在当前节点时 是否全部使用费用计算策略
     * true 全部使用费用计算参数进行计算
     * false 收费节点不在当前节点时 使用试算策略 收费节点在当前节点 使用费用计算策略
     */
    private boolean useExpenseCal;
    /**
     * 重新计算的费用关联列表
     */
    private List<GoodsExpenseRelation> reCalGoodsExpenseRelationList;
}
