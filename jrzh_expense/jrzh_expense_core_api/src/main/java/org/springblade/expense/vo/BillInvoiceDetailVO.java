/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.expense.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.expense.entity.BillInvoiceDetail;
import org.springblade.resource.entity.Attach;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 发票明细视图实体类
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BillInvoiceDetailVO对象", description = "发票明细")
public class BillInvoiceDetailVO extends BillInvoiceDetail {
    private static final long serialVersionUID = 1L;


    /**
     * 邮寄单url集合
     */
    @ApiModelProperty(value = "邮寄单url集合")
    List<Attach> sendAttachList;

    /**
     * 发票凭证url集合
     */
    @ApiModelProperty(value = "发票凭证url集合")
    List<Attach> invoiceAttachList;

    @ApiModelProperty(value = "发票类型;1、增值税专用发票 2、普通发票")
    @NotNull(message = "发票类型不能为空")
    private Integer type;

    /**
     * 发票金额（含税）
     */
    private BigDecimal invoiceAmount;

    /**
     * 用户id
     */
    private Long customerId;
    /**
     * 用户类型：1、个人 2、企业
     */
    private Integer customerType;

}
