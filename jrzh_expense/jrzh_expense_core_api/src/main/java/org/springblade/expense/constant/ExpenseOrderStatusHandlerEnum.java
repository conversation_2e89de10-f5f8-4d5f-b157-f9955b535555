package org.springblade.expense.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.common.enums.PlatformExpensesEnum;

/**
 * 费用状态变更处理器
 *
 * @Author: <PERSON>hengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description: 费用状态变更处理器
 * @Version: 1.0
 */
public interface ExpenseOrderStatusHandlerEnum {
    @Getter
    @AllArgsConstructor
    enum SERVICE {
        /**
         * 应收账款类型融资申请
         */
        PLAT_TYPE_RECEIVABLE(PlatformExpensesEnum.PLAT_TYPE_RECEIVABLE, ""),
        /**
         * 代采融资申请
         */
        PLAT_TYPE_PURCHASE(PlatformExpensesEnum.PLAT_TYPE_PURCHASE, ""),
        /**
         * 云信类型
         */
        PLAT_TYPE_CLOUD(PlatformExpensesEnum.PLAT_TYPE_CLOUD, ""),
        /**
         * 逾期协商类型
         */
        PLAT_TYPE_OVERDUE_CONSULT(PlatformExpensesEnum.PLAT_TYPE_OVERDUE_CONSULT, ""),
        /**
         * 动产质押融资申请
         */
        PLAT_TYPE_PLEDGE(PlatformExpensesEnum.PLAT_TYPE_PLEDGE, ""),
        ;
        /**
         * 业务类型
         */
        private final PlatformExpensesEnum type;
        /**
         * 服务名
         */
        private final String serviceName;

        public static String getServiceName(Integer type) {
            for (SERVICE value : SERVICE.values()) {
                if (value.getType().getCode() == type) {
                    return value.getServiceName();
                }
            }
            return null;
        }
    }

}
