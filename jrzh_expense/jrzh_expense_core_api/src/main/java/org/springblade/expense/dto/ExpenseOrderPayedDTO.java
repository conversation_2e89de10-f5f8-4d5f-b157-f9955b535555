
package org.springblade.expense.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.expense.entity.ExpenseOrder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 费用订单支付参数
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExpenseOrderPayedDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 费用订单 可不填 不填从费用订单号中获取
     */
    private ExpenseOrder expenseOrder;
    /**
     * 费用订单号
     */
    private String billExpenseNo;
    /**
     * 支付订单号
     */
    private String payOrderNo;
    /**
     * 付款银行
     */
    private String bank;
    /**
     * 付款银行卡号
     */
    private String account;
    /**
     * 费用金额
     */
    private BigDecimal amount;
    /**
     * 附件ids 可不填
     */
    private String payAttachId;
    /**
     * 流水号 可不填
     */
    private String billPaySerialNo;
    /**
     * BillConstant.BillPayNameEnum 不传则使用已经创建好的 线上支付方式在支付之前不确定 需要进行支付回调后进行确认
     */
    private Integer paymentMethod;
    /**
     * 支付时间 不填取当前时间
     */
    private LocalDateTime payTime;
    /**
     * 收入流水备注 可不填
     */
    private String flowRemark;
}
