package org.springblade.deposit.feign;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/6
 * @description
 */
@FeignClient(name = "expenseDepositFeign", url = "${feign.url}")
public interface IExpenseDepositFeign {
    String PRE_URL = CommonConstant.BLADE_FEIGN + CommonConstant.BLADE_EXPENSE + "deposit";
    String MAP_IN_DEPOSIT_NO = PRE_URL + "/map";

    @GetMapping(MAP_IN_DEPOSIT_NO)
    R<Map<String, ExpenseDeposit>> getMapExpenseDepositNo(@RequestParam("depositNo") String depositNo);

}
