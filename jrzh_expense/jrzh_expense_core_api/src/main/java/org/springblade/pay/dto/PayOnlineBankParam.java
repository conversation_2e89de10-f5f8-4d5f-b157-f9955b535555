/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pay.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 网银支付参数
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023/8/7 12:05
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class PayOnlineBankParam implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 支付金额
	 */
	private String payAmount;
	/**
	 * 融资订单号 也可以是业务单号
	 */
	private String financeNo;
	/**
	 * 类型 必传：0、应收账款类型融资申请节点 1、代采融资申请节点 2、云信类型 3、逾期协商类型 枚举：PlatformExpensesEnum
	 */
	private Integer type;
	/**
	 * 银行
	 *
	 */
	private String bank;
	/**
	 * 前端回调地址
	 */
	private String callbackUrl;
	/**
	 * 业务类型 B2C:个人支付 B2B:企业支付
	 */
	private String business;
	/**
	 * 支付单号
	 */
	private String payOrderNo;

	private boolean newVersion;

	/**
	 * 选择还款类型：1、正常还款和逾期还款 2、提前还款接口 3、提前结清
	 */
	private Integer repaymentType;

	/**
	 * 支付方式1、线下 2、线上
	 */
	private Integer payMode;

	/**
	 * 还款Ids
	 */
	private String repaymentIds;

	/**
	 * 提前还款id
	 */
	private Long advanceRepaymentId;

	/**
	 * 还款本金
	 */
	private BigDecimal principalAmount;

	/**
	 * 借据单号
	 */
	private String iouNo;


}
