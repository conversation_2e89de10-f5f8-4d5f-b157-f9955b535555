package org.springblade.pay.dto;

import com.google.common.collect.ImmutableSet;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 商品订单通知
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-07-06  11:03
 * @Description: 商品订单通知
 * @Version: 1.0
 */
@Data
public class PayGoodsOrderRefundNotify {
    /**
     * 商品订单号
     */
    private String goodsNo;
    /**
     * 退款订单号
     */
    private String payRefundOrderNo;
    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 商户ID
     */
    private String mchId;

    /**
     * 支付金额,单位元
     */
    private String payAmount;
    /**
     * 退款金额,单位元
     */
    private String refundAmount;
    /**
     * 三位货币代码,人民币:cny
     */
    private String currency;
    /**
     * 订单退款成功时间
     */
    private LocalDateTime refundSuccTime;
    /**
     * 渠道类型
     */
    private String channelType;
    /**
     * 退款备注
     */
    private String orderDesc;
    /**
     * 流水号
     */
    private String systemSerial;
    /**
     * 通知类型
     */
    private String type;
    /**
     * 退款结果:1 退款中 2退款成功 3 退款失败
     */
    private Integer status;
    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("goodsNo", "payRefundOrderNo",
            "payOrderNo", "mchId", "payAmount", "refundAmount", "currency", "refundSuccTime", "channelType", "desc", "systemSerial", "type");
}
