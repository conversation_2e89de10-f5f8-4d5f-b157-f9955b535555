/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pay.dto;

import lombok.Data;
import org.springblade.otherapi.core.vo.SplitBillRuleVO;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 线上分账参数
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023/8/7 12:05
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class PaySplitOrderParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 支付金额（元）
     */
    private String amount;
    /**
     * 分账规则
     * 交易分账规则串必须包含如下参数，
     * 分别是分账商户splitBillMerchantNo和对应分账合法金额splitBillAmount，
     * 分账金额单位:元,最多保留两位小数，
     * 分账规则串中不可以重复出现同一分账商户号，不可以传分账发起方自己
     */
    private List<SplitBillRuleVO> splitBillRules;
    /**
     * 是否已经支付
     */
    private boolean isPayed;
    /**
     * 扩展参数
     */
    private Map<String, Object> extendedParameters;
}
