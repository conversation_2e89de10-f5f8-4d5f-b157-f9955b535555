package org.springblade.pay.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.log.exception.ServiceException;

/**
 * 线上支付业务类型枚举
 *
 * @Author: <PERSON><PERSON>gchuang<PERSON>
 * @CreateTime: 2023/8/7 17:22
 * @Description: 线上支付业务类型枚举 name必须对应实现类service名称 code 不可一样
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum PayOnlineEnum {
    BILL_ONLINE_SERVICE("费用订单服务", "BILL_ONLINE_SERVICE", 1),
    REPAYMENT_ONLINE_SERVICE("还款订单服务", "REPAYMENT_ONLINE_SERVICE", 2),
    PLEDGE_REDEEM_CARGO_SERVICE("动产赎货订单服务", "PLEDGE_REDEEM_CARGO_SERVICE", 3),
    TRANSFER_SERVICE("代付订单服务", "TRANSFER_SERVICE", 4),
    REPAYMENT_ONLINE_EXPENSE_SERVICE("费用还款订单服务", "REPAYMENT_ONLINE_EXPENSE_SERVICE", 5),
    ;
    private final String desc;
    private final String name;
    private final Integer code;


    public interface ServiceConstant {
        String BILL_ONLINE_SERVICE = "BILL_ONLINE_SERVICE";
        String REPAYMENT_ONLINE_EXPENSE_SERVICE = "REPAYMENT_ONLINE_EXPENSE_SERVICE";
        String REPAYMENT_ONLINE_SERVICE = "REPAYMENT_ONLINE_SERVICE";
        String PLEDGE_REDEEM_CARGO_SERVICE = "PLEDGE_REDEEM_CARGO_SERVICE";
        String TRANSFER_SERVICE = "transfer_service";
    }

    /**
     * 根据传入的code态获取desc
     *
     * @param code 状态
     * @return desc
     */
    public static PayOnlineEnum getEnumByCode(Integer code) {
        for (PayOnlineEnum s : PayOnlineEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        throw new ServiceException("未找到线上支付实现类");
    }

    /**
     * 根据传入的code态获取desc
     *
     * @param code 状态
     * @return desc
     */
    public static String getDescByCode(Integer code) {
        for (PayOnlineEnum s : PayOnlineEnum.values()) {
            if (s.getCode().equals(code)) {
                return s.getName();
            }
        }
        throw new ServiceException("未找到线上支付实现类");
    }
}
