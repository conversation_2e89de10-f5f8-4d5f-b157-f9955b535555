
package org.springblade.expense.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.mapper.ExpenseOrderDetailMapper;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 费用订单详情 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Service
@RequiredArgsConstructor
public class ExpenseOrderDetailServiceImpl extends BaseServiceImpl<ExpenseOrderDetailMapper, ExpenseOrderDetail> implements IExpenseOrderDetailService {


    /**
     * 根据融资编号查询 费用订单详情
     *
     * @param financeNo 融资编号
     * @return 费用订单详情列表
     */
    @Override
    public List<ExpenseOrderDetail> getByFinanceNo(String financeNo) {
        return baseMapper.selectList(Wrappers.<ExpenseOrderDetail>lambdaQuery().eq(ExpenseOrderDetail::getFinanceNo, financeNo));
    }

    @Override
    public List<ExpenseOrderDetail> getByFinanceNo(String financeNo, Integer type) {
        return baseMapper.selectList(Wrappers.<ExpenseOrderDetail>lambdaQuery().eq(ExpenseOrderDetail::getFinanceNo, financeNo)
                .eq(ExpenseOrderDetail::getType, type));
    }

    /**
     * 根据 融资编号 + 计算节点 查询 费用订单详情数据集合
     *
     * @param financeNo   融资编号
     * @param computeNode 计算节点
     * @return 费用订单详情集合
     */
    @Override
    public List<ExpenseOrderDetail> getExpenseOrderDetailByFinanceNoAndComputeNode(String financeNo, Integer computeNode) {
        return baseMapper.selectList(Wrappers.<ExpenseOrderDetail>lambdaQuery().eq(ExpenseOrderDetail::getFinanceNo, financeNo).eq(ExpenseOrderDetail::getFeeNode, computeNode));
    }


    /**
     * 根据 融资编号和计算节点 来删除 费用订单详情数据
     *
     * @param financeNo   融资编号
     * @param computeNode 计算节点
     */
    @Override
    public void removeByFinanceNoFeeNode(String financeNo, Integer computeNode) {
        remove(Wrappers.<ExpenseOrderDetail>lambdaQuery().eq(ExpenseOrderDetail::getFinanceNo, financeNo).eq(ExpenseOrderDetail::getFeeNode, computeNode));
    }


    /**
     * 根据融资编号 + 收费节点 查询 费用订单详情数据
     *
     * @param financeNo  融资编号
     * @param chargeNode 收费节点
     * @return 费用订单详情列表
     */
    @Override
    public List<ExpenseOrderDetail> getExpenseOrderDetailByFinanceNoAndChargeNode(String financeNo, Integer chargeNode) {
        return baseMapper.selectList(Wrappers.<ExpenseOrderDetail>lambdaQuery().eq(ExpenseOrderDetail::getFinanceNo, financeNo).eq(ExpenseOrderDetail::getCollectFeesNode, chargeNode));
    }

    /**
     * 支付数据
     */
    @Override
    public void payData() {

    }

    /**
     * 根据 融资编号 修改费用详情的费用编号
     *
     * @param financeNo
     * @param billExpenseNo
     */
    @Override
    public void updateExpenseOrderNo(String financeNo, int platType, String billExpenseNo) {
        update(Wrappers.<ExpenseOrderDetail>lambdaUpdate().eq(ExpenseOrderDetail::getFinanceNo, financeNo).eq(ExpenseOrderDetail::getType, platType).set(ExpenseOrderDetail::getBillExpenseNo, billExpenseNo));
    }

    @Override
    public void removeByFinanceNo(String financeNo, Integer platType) {
        remove(Wrappers.<ExpenseOrderDetail>lambdaQuery().eq(ExpenseOrderDetail::getFinanceNo, financeNo).eq(ExpenseOrderDetail::getType, platType));
    }

    @Override
    public List<ExpenseOrderDetail> getByExpenseOrderNo(String billExpenseNo) {
        return list(Wrappers.<ExpenseOrderDetail>lambdaQuery().eq(ExpenseOrderDetail::getBillExpenseNo, billExpenseNo));
    }
    @Override
    public List<ExpenseOrderDetail> listByArgPlatExpense(String financeNo, Integer payType, Integer billStatus) {
        IExpenseOrderService expenseOrderService = SpringUtil.getBean(IExpenseOrderService.class);
        List<ExpenseOrderDetail> expenseOrderDetails = getByFinanceNo(financeNo, payType);
        if (CollectionUtil.isEmpty(expenseOrderDetails)) {
            return ListUtil.empty();
        }
        String billExpenseNo = expenseOrderDetails.get(0).getBillExpenseNo();
        return Collections.emptyList();
//        if (StringUtil.isBlank(billExpenseNo)) {
//            throw new ServiceException("费用单号不存在");
//        }
//        int count = expenseOrderService.count(Wrappers.<ExpenseOrder>lambdaQuery().eq(ExpenseOrder::getBillExpenseNo, billExpenseNo)
//                .eq(ExpenseOrder::getPaymentStatus, billStatus));
//
//        return count > 0 ? expenseOrderDetails : ListUtil.empty();
    }
}
