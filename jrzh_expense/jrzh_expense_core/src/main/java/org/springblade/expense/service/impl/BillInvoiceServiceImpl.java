/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.expense.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.BillInvoiceOpenTypeEnum;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.common.utils.word.PoiTiUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.constant.BillConstant;
import org.springblade.expense.dto.BillInvoiceDetailSaveDTO;
import org.springblade.expense.entity.BillInvoice;
import org.springblade.expense.entity.BillInvoiceDetail;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.mapper.BillInvoiceMapper;
import org.springblade.expense.service.IBillInvoiceDetailService;
import org.springblade.expense.service.IBillInvoiceService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.vo.BillInvoiceDetailVO;
import org.springblade.expense.vo.BillInvoiceDetailedVO;
import org.springblade.expense.vo.BillInvoiceVO;
import org.springblade.expense.wrapper.BillInvoiceDetailWrapper;
import org.springblade.expense.wrapper.BillInvoiceWrapper;
import org.springblade.resource.cache.DictBizCache;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.entity.ResourceMail;
import org.springblade.resource.factory.EmailApiFactory;
import org.springblade.resource.service.IAttachService;
import org.springblade.resource.service.IResourceMailService;
import org.springblade.resource.vo.MailAttachVO;
import org.springblade.resource.vo.MailVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 发票基本信息 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Service
@RequiredArgsConstructor
public class BillInvoiceServiceImpl extends BaseServiceImpl<BillInvoiceMapper, BillInvoice> implements IBillInvoiceService {

    private static final Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$");

    private final IBillInvoiceDetailService billInvoiceDetailService;

    private final IExpenseOrderService billExpenseOrderService;

    //    private final ICustomerBusinessInfoService customerBusinessInfoService;
    private final EmailApiFactory emailApiFactory;
    private final IResourceMailService resourceMailService;

    @Override
    public IPage<BillInvoiceVO> selectBillInvoicePage(IPage<BillInvoiceVO> page, BillInvoiceVO billInvoice) {
        return page.setRecords(baseMapper.selectBillInvoicePage(page, billInvoice));
    }

//    /**
//     * 企业基本发票信息
//     *
//     * @return
//     */
//    @Override
//    public BillInvoiceDetailVO detailInvoiceCompany() {
//        String companyId = MyAuthUtil.getCompanyId();
//        //根据用户id查询客户工商信息表，获取到单位税号和注册地址
//        CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoService.getByCompanyId(companyId);
//
//        BillInvoiceDetailVO billInvoiceDetailVO = new BillInvoiceDetailVO();
//        if (ObjectUtil.isNotEmpty(customerBusinessInfo)) {
//            //发票抬头
//            billInvoiceDetailVO.setCompanyName(customerBusinessInfo.getCompanyName());
//            //单位税号
//            billInvoiceDetailVO.setUnitTax(customerBusinessInfo.getCreditCode());
//            //注册地址
//            billInvoiceDetailVO.setRegisteredAddress(customerBusinessInfo.getRegLocation());
//        }
//        return billInvoiceDetailVO;
//    }


    /**
     * 修改发票状态
     *
     * @param id            费用id
     * @param invoiceStatus 费用-发票状态：0、可开票 1、已申请 2、已寄出 3、待收票 4、已收票 5、已取消 6、已收票（自动收票的时候是该值）
     *                      目前前端传入的值只有两个，确认收票和已取消这两个状态，只需要判断一下传入的数据是否一致即可
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInvoiceStatus(Long id, int invoiceStatus) {

        if (invoiceStatus != BillConstant.INVOICE_STATUS_FOUR && invoiceStatus != BillConstant.INVOICE_STATUS_FIVE) {
            throw new ServiceException("传入的发票状态不符合要求！");
        }

        ExpenseOrder billExpenseOrder = billExpenseOrderService.getById(id);
        if (ObjectUtil.isEmpty(billExpenseOrder)) {
            throw new ServiceException("费用id不存在");
        }

        String invoiceStatusName = DictBizCache.getValue(DictBizEnum.BILL_INVOICE_STATUS.getCode(), invoiceStatus);
        if (StrUtil.isBlank(invoiceStatusName)) {
            throw new ServiceException(DictBizEnum.BILL_INVOICE_STATUS.getName() + "没有查询到 请填写正确的值！");
        }

        //根据费用的融资编号查询出发票基本信息，然后在修改发票基本信息的交付状态
        BillInvoice billInvoice = this.getOne(Wrappers.<BillInvoice>lambdaQuery()
                .eq(BillInvoice::getBillExpenseNo, billExpenseOrder.getBillExpenseNo()));

        if (ObjectUtil.isEmpty(billInvoice)) {
            throw new ServiceException("发票信息不存在！");
        }

        //给状态赋值
        if (invoiceStatus == BillConstant.INVOICE_STATUS_FIVE && billExpenseOrder.getInvoiceStatus() == BillConstant.INVOICE_STATUS_ONE) {
            billExpenseOrder.setInvoiceStatus(BillConstant.INVOICE_STATUS_FIVE);
            billInvoice.setDeliveryStatus(BillConstant.DELIVERY_STATUS_FIVE);

        } else if (invoiceStatus == BillConstant.INVOICE_STATUS_FOUR && (billExpenseOrder.getInvoiceStatus() == BillConstant.INVOICE_STATUS_TWO || billExpenseOrder.getInvoiceStatus() == BillConstant.INVOICE_STATUS_THREE)) {
            billExpenseOrder.setInvoiceStatus(BillConstant.INVOICE_STATUS_FOUR);
            billInvoice.setDeliveryStatus(BillConstant.DELIVERY_STATUS_FOUR);
        } else {
            throw new ServiceException("数据有误！");
        }

        //同时修改费用-发票状态和 发票基础信息交付状态
        billExpenseOrderService.saveOrUpdate(billExpenseOrder);

        return this.saveOrUpdate(billInvoice);
    }


    /**
     * 后端---企业详细发票信息
     *
     * @param id 发票基础信息id
     * @return
     */
    @Override
    public BillInvoiceDetailedVO detailBackInvoice(Long id) {

        BillInvoice billInvoice = this.getById(id);

        if (ObjectUtil.isEmpty(billInvoice)) {
            throw new ServiceException("查询不到该发票信息");
        }

        BillInvoiceDetail billInvoiceDetail = billInvoiceDetailService.getOne(Wrappers.<BillInvoiceDetail>lambdaQuery()
                .eq(BillInvoiceDetail::getBillExpenseNo, billInvoice.getBillExpenseNo()));

        //发票基础信息转换
        BillInvoiceDetailedVO billInvoiceDetailedVO = BillInvoiceWrapper.build().entityDetailedVO(billInvoice);

        billInvoiceDetailedVO.setBillInvoiceDetail(billInvoiceDetail);
        billInvoiceDetailedVO.setDeliveryStatusName(DictBizCache.getValue(DictBizEnum.BILL_INVOICE_DELIVER_STATUS.getCode(), billInvoice.getDeliveryStatus()));

        return billInvoiceDetailedVO;
    }


    /**
     * 后端---开票并寄出详细数据并展示
     * 根据融资编号查询出 开票并寄出详细数据并展示
     *
     * @param billExpenseNo 费用编号
     * @return
     */
    @Override
    public BillInvoiceDetailVO detailBackFinanceNo(String billExpenseNo) {


        BillInvoiceDetail billInvoiceDetail = billInvoiceDetailService.getOne(Wrappers.<BillInvoiceDetail>lambdaQuery()
                .eq(BillInvoiceDetail::getBillExpenseNo, billExpenseNo));

        BillInvoice billInvoice = this.getOne(Wrappers.<BillInvoice>lambdaQuery()
                .eq(BillInvoice::getBillExpenseNo, billExpenseNo));

        ExpenseOrder billExpenseOrder = billExpenseOrderService.getOne(Wrappers.<ExpenseOrder>lambdaQuery()
                .eq(ExpenseOrder::getBillExpenseNo, billExpenseNo));


        if (ObjectUtil.isEmpty(billInvoiceDetail) || ObjectUtil.isEmpty(billInvoice) || ObjectUtil.isEmpty(billExpenseOrder)) {
            throw new ServiceException("查询不到该发票信息");
        }

        BillInvoiceDetailVO billInvoiceDetailVO = BillInvoiceDetailWrapper.build().entityVO(billInvoiceDetail);

        if (StringUtil.isNotBlank(billInvoiceDetailVO.getInvoiceAttach())) {
            IAttachService attachService = SpringUtil.getBean(IAttachService.class);
            List<Attach> invoiceAttachList = attachService
                    .lambdaQuery()
                    .in(Attach::getId, Func.toLongList(billInvoiceDetailVO.getInvoiceAttach())).list();
            billInvoiceDetailVO.setInvoiceAttachList(invoiceAttachList);
            if (ObjectUtil.isNotEmpty(billInvoiceDetailVO.getSendAttach())) {
                List<Attach> sendAttachList = attachService
                        .lambdaQuery()
                        .in(Attach::getId, Func.toLongList(billInvoiceDetailVO.getSendAttach())).list();

                billInvoiceDetailVO.setSendAttachList(sendAttachList);
            }
        }

        //发票金额
        billInvoiceDetailVO.setInvoiceAmount(billInvoice.getAmount());

        //用户id
        billInvoiceDetailVO.setCustomerId(billExpenseOrder.getCustomerId());
        //用户类型
        billInvoiceDetailVO.setCustomerType(billExpenseOrder.getCustomerType());

        return billInvoiceDetailVO;
    }


    /**
     * 后端---修改开票明细数据
     *
     * @param billInvoiceDetailSaveDTO 修改发票-开票数据
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInvoiceDetail(BillInvoiceDetailSaveDTO billInvoiceDetailSaveDTO) {
        Matcher isNum = pattern.matcher(billInvoiceDetailSaveDTO.getTaxRate());
        if (!isNum.matches()) {
            throw new ServiceException("税率填写错误，请填写小数或者整数");
        }

        //查询发票详细信息
        BillInvoiceDetail billInvoiceDetail = billInvoiceDetailService.getOne(Wrappers.<BillInvoiceDetail>lambdaQuery()
                .eq(BillInvoiceDetail::getBillExpenseNo, billInvoiceDetailSaveDTO.getBillExpenseNo()));

        BillInvoice billInvoice = this.getOne(Wrappers.<BillInvoice>lambdaQuery()
                .eq(BillInvoice::getBillExpenseNo, billInvoiceDetailSaveDTO.getBillExpenseNo()));

        ExpenseOrder billExpenseOrderServiceOne = billExpenseOrderService.getOne(Wrappers.<ExpenseOrder>lambdaQuery()
                .eq(ExpenseOrder::getBillExpenseNo, billInvoiceDetailSaveDTO.getBillExpenseNo()));

        if (ObjectUtil.isEmpty(billInvoiceDetail) || ObjectUtil.isEmpty(billInvoice) || ObjectUtil.isEmpty(billExpenseOrderServiceOne)) {
            throw new ServiceException("查询不到该发票信息！");
        }

        if (billInvoice.getDeliveryStatus() == BillConstant.DELIVERY_STATUS_FIVE || billInvoice.getDeliveryStatus() == BillConstant.DELIVERY_STATUS_FOUR) {
            throw new ServiceException("发票状态不正确！");
        }
        //检查参数
        checkInvoiceDetail(billInvoiceDetailSaveDTO, billInvoiceDetail);
        BigDecimal amount = billInvoice.getAmount();

        //不含税的发票金额 ：含税发票金额 减去 含税发票金额*税率 保留两位小数，四舍五入
        BigDecimal taxRate = amount.subtract(NumberUtil.round(billInvoiceDetailSaveDTO.getTaxRate(), 2)
                        .divide(new BigDecimal("100"))
                        .multiply(amount))
                .setScale(2, RoundingMode.HALF_UP);

        billInvoiceDetail.setLogisticsCompanies(billInvoiceDetailSaveDTO.getLogisticsCompanies());
        billInvoiceDetail.setWaybillNumber(billInvoiceDetailSaveDTO.getWaybillNumber());
        billInvoiceDetail.setSender(billInvoiceDetailSaveDTO.getSender());
        billInvoiceDetail.setSendTime(billInvoiceDetailSaveDTO.getSendTime());
        billInvoiceDetail.setSendAttach(billInvoiceDetailSaveDTO.getSendAttach());
        billInvoiceDetail.setFreight(billInvoiceDetailSaveDTO.getFreight());
        billInvoiceDetail.setTaxRate(billInvoiceDetailSaveDTO.getTaxRate());
        billInvoiceDetail.setTaxAmount(taxRate);
        billInvoiceDetail.setInvoiceAttach(billInvoiceDetailSaveDTO.getInvoiceAttach());
        billInvoiceDetail.setFinanceNo(billInvoiceDetailSaveDTO.getFinanceNo());
        //修改发票基础信息交付状态为---已寄出

        if (billInvoice.getDeliveryStatus() == BillConstant.DELIVERY_STATUS_ONE) {
            if (BillInvoiceOpenTypeEnum.emailType.equals(billInvoiceDetail.getSendType())) {
                billInvoice.setDeliveryStatus(BillConstant.DELIVERY_STATUS_SEVEN);
                billExpenseOrderServiceOne.setInvoiceStatus(BillConstant.INVOICE_STATUS_SEVEN);
            } else {
                billInvoice.setDeliveryStatus(BillConstant.DELIVERY_STATUS_TWO);
                billExpenseOrderServiceOne.setInvoiceStatus(BillConstant.INVOICE_STATUS_TWO);
            }

        }
        //财务流水新增 运费数据 financialFlow 后续修改

//		billFinancialFlowService.saveFinancialFlow()
        this.saveOrUpdate(billInvoice);
        //修改费用 发票状态为---已寄出
        billExpenseOrderService.saveOrUpdate(billExpenseOrderServiceOne);
        billInvoiceDetailService.saveOrUpdate(billInvoiceDetail);
        //如果是邮件方式 发送邮件后改变其状态为已收票
        if (BillInvoiceOpenTypeEnum.emailType.equals(billInvoiceDetail.getSendType())) {
            sendBillInvoiceEmail(billInvoice, billExpenseOrderServiceOne, billInvoiceDetail);
        }
        return true;
    }

    private void checkInvoiceDetail(BillInvoiceDetailSaveDTO billInvoiceDetailSaveDTO, BillInvoiceDetail billInvoiceDetail) {
        if (BillInvoiceOpenTypeEnum.postType.equals(billInvoiceDetail.getSendType())) {
            if (StringUtil.isEmpty(billInvoiceDetailSaveDTO.getSendAttach())
                    || StringUtil.isEmpty(billInvoiceDetailSaveDTO.getLogisticsCompanies())
                    || StringUtil.isEmpty(billInvoiceDetailSaveDTO.getWaybillNumber())
                    || StringUtil.isEmpty(billInvoiceDetailSaveDTO.getFreight())
                    || StringUtil.isEmpty(billInvoiceDetailSaveDTO.getSender())
                    || StringUtil.isEmpty(billInvoiceDetailSaveDTO.getSendTime())) {
                throw new ServiceException(ResultCode.PARAM_VALID_ERROR);
            }
        }
    }

    /**
     * 发送邮件并更新状态
     *
     * @param billInvoice
     * @param billExpenseOrderServiceOne
     * @param billInvoiceDetail
     */
    private void sendBillInvoiceEmail(BillInvoice billInvoice, ExpenseOrder billExpenseOrderServiceOne, BillInvoiceDetail billInvoiceDetail) {
        ResourceMail openResource = resourceMailService.getOpenResource();
        //使用qq邮箱进行邮件发送
        emailApiFactory.template(openResource.getMailType())
                .sendEmail(buildSendInvoiceMailVO(billInvoiceDetail),
                        () -> doSendEmailSuccessAction(billInvoice, billExpenseOrderServiceOne),
                        () -> doSendEmailExceptionAction(billInvoice, billExpenseOrderServiceOne));
    }

    /**
     * 构建邮件内容
     *
     * @param billInvoiceDetail
     * @return
     */
    private MailVO buildSendInvoiceMailVO(BillInvoiceDetail billInvoiceDetail) {
        IAttachService attachService = SpringUtil.getBean(IAttachService.class);
        MailVO mailVO = new MailVO();
        mailVO.setTo(billInvoiceDetail.getEmail());
        mailVO.setSubject("【深圳市精锐纵横网络有限公司-发票确认】");
        StringBuilder stringBuilder = new StringBuilder();
        List<Long> attachIds = Func.toLongList(billInvoiceDetail.getInvoiceAttach());
        MailAttachVO[] mailAttachVO = new MailAttachVO[attachIds.size()];
        for (int i = 0; i < attachIds.size(); i++) {
            //添加附件进行发送
            Attach attach = attachService.getById(attachIds.get(i));
            InputStream fileInputStream = PoiTiUtils.getFileInputStream(attach.getLink());
            mailAttachVO[i] = new MailAttachVO("发票附件" + i + StringPool.DOT + attach.getExtension(), fileInputStream);
        }
        mailVO.setMailAttachVO(mailAttachVO);
        stringBuilder.append("请通过附件查看发票！");
        mailVO.setContent(stringBuilder.toString());
        return mailVO;
    }

    /**
     * 邮件发送成功处理
     *
     * @param billInvoice
     * @param billExpenseOrderServiceOne
     */
    private void doSendEmailSuccessAction(BillInvoice billInvoice, ExpenseOrder billExpenseOrderServiceOne) {
        update(Wrappers.<BillInvoice>lambdaUpdate()
                .set(BillInvoice::getDeliveryStatus, BillConstant.DELIVERY_STATUS_FOUR)
                .eq(BillInvoice::getId, billInvoice.getId()));
        billExpenseOrderService.update(Wrappers.<ExpenseOrder>lambdaUpdate()
                .set(ExpenseOrder::getInvoiceStatus, BillConstant.INVOICE_STATUS_FOUR)
                .eq(ExpenseOrder::getId, billExpenseOrderServiceOne.getId()));
    }

    /**
     * 邮件发送异常处理
     *
     * @param billInvoice
     * @param billExpenseOrderServiceOne
     */
    private void doSendEmailExceptionAction(BillInvoice billInvoice, ExpenseOrder billExpenseOrderServiceOne) {
        update(Wrappers.<BillInvoice>lambdaUpdate()
                .set(BillInvoice::getDeliveryStatus, BillConstant.DELIVERY_STATUS_ONE)
                .eq(BillInvoice::getId, billInvoice.getId()));
        billExpenseOrderService.update(Wrappers.<ExpenseOrder>lambdaUpdate()
                .set(ExpenseOrder::getInvoiceStatus, BillConstant.INVOICE_STATUS_ONE)
                .eq(ExpenseOrder::getId, billExpenseOrderServiceOne.getId()));
    }

    /**
     * 后端---已达目的地
     *
     * @param dispatcherName   派件员姓名
     * @param dispatcherNumber 派件人手机号码
     * @param billExpenseNo    费用编号
     * @return
     */
    @Override
    public boolean updateInvoiceDispatcherDetail(String dispatcherName, String dispatcherNumber, String billExpenseNo) {

        //查询发票详细信息
        BillInvoiceDetail billInvoiceDetail = billInvoiceDetailService.getOne(Wrappers.<BillInvoiceDetail>lambdaQuery()
                .eq(BillInvoiceDetail::getBillExpenseNo, billExpenseNo));

        BillInvoice billInvoice = this.getOne(Wrappers.<BillInvoice>lambdaQuery()
                .eq(BillInvoice::getBillExpenseNo, billExpenseNo));

        ExpenseOrder billExpenseOrderServiceOne = billExpenseOrderService.getOne(Wrappers.<ExpenseOrder>lambdaQuery()
                .eq(ExpenseOrder::getBillExpenseNo, billExpenseNo));

        if (ObjectUtil.isEmpty(billInvoiceDetail) || ObjectUtil.isEmpty(billInvoice) || ObjectUtil.isEmpty(billExpenseOrderServiceOne)) {
            throw new ServiceException("查询不到该发票信息！");
        }

        if (billInvoice.getDeliveryStatus() != BillConstant.DELIVERY_STATUS_TWO) {
            throw new ServiceException("发票状态不正确！");
        }

        //发票基础信息状态改变---待收票
        billInvoice.setDeliveryStatus(BillConstant.DELIVERY_STATUS_THREE);

        //费用数据 状态改变---待收票
        billExpenseOrderServiceOne.setInvoiceStatus(BillConstant.INVOICE_STATUS_THREE);


        //派件员姓名
        billInvoiceDetail.setDispatcherName(dispatcherName);
        //派件人手机号码
        billInvoiceDetail.setDispatcherNumber(dispatcherNumber);

        this.saveOrUpdate(billInvoice);
        billExpenseOrderService.saveOrUpdate(billExpenseOrderServiceOne);
        return billInvoiceDetailService.saveOrUpdate(billInvoiceDetail);
    }

    @Override
    public BillInvoice getByFinanceNo(String financeNo) {
        return lambdaQuery().eq(BillInvoice::getFinanceNo, financeNo).one();
    }
}
