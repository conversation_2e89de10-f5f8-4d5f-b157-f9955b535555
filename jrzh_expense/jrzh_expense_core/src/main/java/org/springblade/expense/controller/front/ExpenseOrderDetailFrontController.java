package org.springblade.expense.controller.front;

import cn.hutool.core.collection.CollUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/30
 * @description 平台费用 控制器
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_BUSINESS + CommonConstant.WEB_FRONT + "/platform-expenses")
@Api(value = "平台费用", tags = "平台费用接口")
public class ExpenseOrderDetailFrontController extends BladeController {

    private final IExpenseOrderDetailService expenseOrderDetailService;

    /**
     * 分页 平台费用
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页", notes = "传入platformExpenses")
    public R<List<ExpenseOrderDetail>> list(@RequestParam String financeNo) {
        List<ExpenseOrderDetail> byFinanceNo = expenseOrderDetailService.getByFinanceNo(financeNo);
        if (CollUtil.isEmpty(byFinanceNo)) {
            return R.data(Collections.emptyList());
        }
        return R.data(byFinanceNo.stream()
                .filter(e -> ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode() != e.getCollectFeesNode()).collect(Collectors.toList()));
    }

    /**
     * 分页 平台费用
     */
    @GetMapping("/platList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入platformExpenses")
    public R<List<ExpenseOrderDetail>> list(@RequestParam String financeNo, @RequestParam Integer type) {
        return R.data(expenseOrderDetailService.getByFinanceNo(financeNo, type));
    }
}
