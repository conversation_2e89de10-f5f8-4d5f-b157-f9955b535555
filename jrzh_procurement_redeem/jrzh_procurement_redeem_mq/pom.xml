<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
       <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh_procurement_redeem</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>jrzh_procurement_redeem_mq</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_common_mq</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_warehouse_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_redeem_base</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_procurement_redeem_core_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>
