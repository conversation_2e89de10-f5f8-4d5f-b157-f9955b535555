package org.springblade.redeem.mq.pay;


import org.springblade.product.common.entity.MergePay;
import org.springframework.stereotype.Service;

import java.util.List;

/***
 * 赎货---线上支付接口
 */
@Service
public class RedeemPayCombineMergeServiceImpl implements RedeemPayCombineMergeService {

    @Override
    public List<MergePay> listByIds(List<Long> ids) {
        throw new UnsupportedOperationException("TODO");
    }
}
