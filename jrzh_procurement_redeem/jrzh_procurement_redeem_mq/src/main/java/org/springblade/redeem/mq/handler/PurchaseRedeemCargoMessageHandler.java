package org.springblade.redeem.mq.handler;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.BillPayStatusEnum;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.handler.MessageHandler;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.mq.loan.MqLoanManageRepaymentService;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

/**
 * 赎货流程操作
 *
 * <AUTHOR>
 */
@Component("purchase_redeemCargo_apply")
@RequiredArgsConstructor
@TenantIgnore
public class PurchaseRedeemCargoMessageHandler implements MessageHandler {
    /**
     * 库存明细
     */
    private final IWarehouseDetailsService warehouseDetailsService;

    private final IRedeemCargoService iRedeemCargoService;

    private final IExpenseOrderService billExpenseOrderService;
    private final MqLoanManageRepaymentService loanManageRepaymentService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handler(DelayMessage delayMessage) {
        String msg = delayMessage.getMsg();
        Integer status = delayMessage.getStatus();
        switch (status) {
            case 3:
                purchaseRedeemCargoApply(msg);
                break;
            case 4:
                changOrderStatus(delayMessage);
                break;
            default:
        }
    }

    /**
     * 赎货申请待确认，根据赎货id以及赎货数量，更新库存信息（主要给赎货申请待确认状态超时操作）
     *
     * @param msg
     */
    void purchaseRedeemCargoApply(String msg) {
        HashMap<String, Object> map = JSONUtil.toBean(msg, HashMap.class);
        Long redeemCargoId = (Long) map.get("redeemCargoId");
        Long stockId = (Long) map.get("stockId");
        Integer num = (Integer) map.get("num");
        Long expenseOrderId = (Long) map.get("expenseOrderId");
        Long loanManageId = (Long) map.get("loanManageId");
        RedeemCargo redeemCargo = iRedeemCargoService.getById(redeemCargoId);
        if (RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_CONFIRM.getKey().equals(redeemCargo.getStatus())) {
            warehouseDetailsService.operateFormInToRedemption(stockId, num, true);
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_SUSPEND.getKey());
            iRedeemCargoService.updateById(redeemCargo);
            //修改 费用订单
            billExpenseOrderService.update(Wrappers.<ExpenseOrder>lambdaUpdate()
                    .eq(ExpenseOrder::getId, expenseOrderId)
                    .set(ExpenseOrder::getPaymentStatus, BillPayStatusEnum.BILL_CLOSED.getCode()));
            //修改 还款列表
            LambdaUpdateWrapper<LoanManageRepayment> updateWrapper = Wrappers.<LoanManageRepayment>lambdaUpdate()
                    .eq(LoanManageRepayment::getId, loanManageId)
                    .set(LoanManageRepayment::getStatus, RepaymentConstant.RepaymentStatusEnum.CANCEL.getCode());

            loanManageRepaymentService.update(updateWrapper);
        }
    }

    /**
     * 赎货待验收
     *
     * @param delayMessage
     */

    void changOrderStatus(DelayMessage delayMessage) {
        Long redeemCargoId = Long.valueOf(delayMessage.getMsg());
        RedeemCargo redeemCargo = iRedeemCargoService.getById(redeemCargoId);
        if (RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey().equals(redeemCargo.getStatus())) {
            iRedeemCargoService.changOrderStatus(redeemCargo);
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey());
            iRedeemCargoService.updateById(redeemCargo);
        }

    }

}
