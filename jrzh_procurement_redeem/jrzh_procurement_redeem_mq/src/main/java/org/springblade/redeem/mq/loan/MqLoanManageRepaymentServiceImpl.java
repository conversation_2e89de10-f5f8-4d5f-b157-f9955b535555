package org.springblade.redeem.mq.loan;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * mq---还款列表
 */
@Service
public class MqLoanManageRepaymentServiceImpl implements MqLoanManageRepaymentService{

    @Override
    public List<LoanManageRepayment> list(Wrapper<LoanManageRepayment> queryWrapper) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public Boolean updateById(LoanManageRepayment repayment) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public Boolean update(LambdaUpdateWrapper<LoanManageRepayment> updateWrapper) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public boolean statusOnlinePayRepaymentUpdate(String onlinePayRepaymentCode, Integer status, BigDecimal actualAmount) {
        throw new UnsupportedOperationException("TODO");
    }
}
