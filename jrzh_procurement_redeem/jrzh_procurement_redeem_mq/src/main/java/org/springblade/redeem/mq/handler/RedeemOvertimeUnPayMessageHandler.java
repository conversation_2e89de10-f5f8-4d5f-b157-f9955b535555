package org.springblade.redeem.mq.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.handler.MessageHandler;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.enums.RedeemCargoPayEnum;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springframework.stereotype.Component;

/**
 * 赎货确认超时未支付操作
 *
 * <AUTHOR>
 */
@Component("redemption_overtime_un_pay")
@RequiredArgsConstructor
@TenantIgnore
public class RedeemOvertimeUnPayMessageHandler implements MessageHandler {
    private final IRedeemCargoService redeemCargoService;

    @Override
    public void handler(DelayMessage delayMessage) {
        String msg = delayMessage.getMsg();
        RedeemCargo redeemCargo = redeemCargoService.getOne(Wrappers.<RedeemCargo>lambdaQuery().eq(RedeemCargo::getRedeemNo, msg));
        if (RedeemCargoPayEnum.IN_PAYMENT_STATUS.getCode().equals(redeemCargo.getPaymentStatus())) {
            redeemCargo.setPaymentStatus(RedeemCargoPayEnum.PAYMENT_FAILED_STATUS.getCode());
            redeemCargoService.updateById(redeemCargo);
        }
    }
}
