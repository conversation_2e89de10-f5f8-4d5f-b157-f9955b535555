package org.springblade.redeem.mq.loan;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springblade.loan.entity.LoanManageRepayment;

import java.math.BigDecimal;
import java.util.List;

/**
 * mq---还款列表
 */
public interface MqLoanManageRepaymentService {

    /**
     * 根据查询条件获取 还款列表集合数据
     * @param queryWrapper 查询条件
     * @return 还款列表集合数据
     */
    List<LoanManageRepayment> list(Wrapper<LoanManageRepayment> queryWrapper);

    /**
     * 更新还款列表数据
     * @param repayment 还款列表
     * @return true成功
     */
    Boolean updateById(LoanManageRepayment repayment);

    /**
     * 更新还款列表数据
     * @param updateWrapper 条件
     * @return true成功
     */
    Boolean update(LambdaUpdateWrapper<LoanManageRepayment> updateWrapper);

    /**
     * 线上还款-修改支付状态
     * @param onlinePayRepaymentCode 线上还款编号
     * @param status 状态
     * @param actualAmount 金额
     */
    boolean statusOnlinePayRepaymentUpdate(String onlinePayRepaymentCode, Integer status, BigDecimal actualAmount);
}
