package org.springblade.redeem.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.loan.enums.PayModeEnum;
import org.springblade.loan.service.IManualOperationLoanService;
import org.springblade.loan.service.IRepaymentBizService;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.procurement.finance.constant.PurchaseMessageTypeEnum;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.moudle.timing.service.IGoodsTimingService;
import org.springblade.redeem.constant.RedeemConstant;
import org.springblade.redeem.dto.RedeemCargoCalculationDTO;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.entity.RedeemExpense;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.redeem.service.IRedeemExpenseService;
import org.springblade.redeem.vo.PlaExpenseCulationVO;
import org.springblade.redeem.vo.RedeemCargoCalculationVO;
import org.springblade.repayment.dto.RepaymentCreateDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.repayment.dto.RepaymentOrderDTO;
import org.springblade.resource.service.IDictBizService;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 赎货申请监听
 *
 * <AUTHOR>
 */
@Component("redeemApplyListener")
@RequiredArgsConstructor
public class RedeemApplyListener implements ExecutionListener {

    private final IRedeemCargoService redeemCargoService;
    private final IFinanceApplyService financeApplyService;
    private final IWarehouseDetailsService warehouseDetailsService;
    private final IRedeemExpenseService redeemExpenseService;
    private final IGoodsTimingService goodsTimingService;
    private final RabbitMsgSender rabbitMsgSender;
    private final IRepaymentBizService repaymentBizService;
    private final IManualOperationLoanService manualOperationLoanService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        String redeemNo = delegateExecution.getVariable(RedeemConstant.REDEEMNO, String.class);
        CostCalculusVO costCalculusVO = JSONUtil.toBean(JSONUtil.toJsonStr(delegateExecution.getVariable(ProcessConstant.COST_CALCULUS_VO)), CostCalculusVO.class);
        RedeemCargo redeemCargo = redeemCargoService.lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemNo).one();
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            //拒绝
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_SUSPEND.getKey());
            warehouseDetailsService.operateFormInToRedemption(redeemCargo.getStockId(), redeemCargo.getNum(), true);
            //关闭费用
            manualOperationLoanService.close(costCalculusVO.getExpenseOrderDetailFinanceVos());
        } else {
            //通过
            String variable = delegateExecution.getVariable(RedeemConstant.EXPENSE_INFO, String.class);
            List<String> moneyList = Func.toStrList(variable);

            //保存赎货费用信息关联账户信息
            List<Map> accountList = delegateExecution.getVariable(RedeemConstant.ACCOUNT_INFO, List.class);
            Map capitalExpenseInfo = delegateExecution.getVariable(RedeemConstant.CAPITAL_EXPENSE_INFO, Map.class);

            List<RedeemExpense> manExpenseCulationVoList = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(capitalExpenseInfo)) {
                manExpenseCulationVoList = JSON.parseObject(JSON.toJSONString(capitalExpenseInfo.get("manExpenseCulationVoList")), new TypeReference<ArrayList<RedeemExpense>>() {
                });
            }
            Map<Long, RedeemExpense> expenseRuleMap = Collections.emptyMap();
            if (CollectionUtil.isNotEmpty(accountList)) {
                List<PlaExpenseCulationVO> plaExpenseVOList = StreamUtil.map(accountList, o -> JSONUtil.toBean(JSONUtil.toJsonStr(o), PlaExpenseCulationVO.class));
                List<RedeemExpense> redeemExpenses = Lists.newArrayList();
                for (PlaExpenseCulationVO plaExpenseCulationVO : plaExpenseVOList) {
                    List<RedeemExpense> redeemExpenseList = plaExpenseCulationVO.getPlaExpenseCulationVoList();
                    redeemExpenses.addAll(redeemExpenseList);
                }
                redeemExpenses.addAll(manExpenseCulationVoList);
                Map<Long, RedeemExpense> redeemExpenseMap = StreamUtil.toMap(redeemExpenses, RedeemExpense::getId, redeemExpense -> redeemExpense);
                expenseRuleMap = StreamUtil.toMap(redeemExpenses, RedeemExpense::getGoodsExpenseId, redeemExpense -> redeemExpense);

                List<RedeemExpense> redeemExpensesList = redeemExpenseService.lambdaQuery().eq(RedeemExpense::getRedeemNo, redeemCargo.getRedeemNo()).eq(RedeemExpense::getCalculation, 1).list();
                if (!redeemExpensesList.isEmpty()) {
                    //int i = 0;
                    for (RedeemExpense redeemExpense : redeemExpensesList) {
                        RedeemExpense expense = redeemExpenseMap.get(redeemExpense.getId());
                        redeemExpense.setMoney(expense.getMoney());
                        //i++;
                    }
                    redeemExpenseService.updateBatchById(redeemExpensesList);
                }

                List<RedeemExpense> redeemExpenseList = redeemExpenseService.lambdaQuery().eq(RedeemExpense::getRedeemNo, redeemCargo.getRedeemNo()).list();
                for (RedeemExpense redeemExpense : redeemExpenseList) {
                    RedeemExpense expense = redeemExpenseMap.get(redeemExpense.getId());
                    if (ObjectUtil.isNotEmpty(expense)) {
                        redeemExpense.setAccountId(expense.getAccountId());
                        redeemExpense.setType(expense.getType());
                    }
                }
                redeemExpenseService.updateBatchById(redeemExpenseList);
            }


            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_CONFIRM.getKey());
            //	redeemCargo.setExpenseInfo(expenseInfo);
            //创建费用订单
            FinanceApply financeApply = financeApplyService.getByFinanceNo(redeemCargo.getFinancingNo());
            String expensesNo = CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO);
            //计算费用
            //平台总费用
            List<RedeemExpense> redeemExpenseList = redeemExpenseService.lambdaQuery()
                    .eq(RedeemExpense::getRedeemNo, redeemCargo.getRedeemNo())
                    //.eq(RedeemExpense::getBusinessCategory, GoodsEnum.ALONE.getCode())
                    .list();
            BigDecimal moneySum = redeemExpenseList.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            //------------------------------------------
            //线上支付费用和线下支付费用分组生成费用订单
            //过滤线下支付的费用
            List<RedeemExpense> belowRedeemExpenses = redeemExpenseList.stream()
                    .filter(redeemExpense -> PayModeEnum.PAY_MODE_BELOW.getCode()
                            .equals(redeemExpense.getCostPayMode()))
                    .collect(Collectors.toList());
            Map<Integer, List<RedeemExpense>> belowListMap = StreamUtil.groupBy(belowRedeemExpenses, RedeemExpense::getType);
            //过滤线上支付的费用
            List<RedeemExpense> onlineRedeemExpenses = redeemExpenseList.stream()
                    .filter(redeemExpense -> PayModeEnum.PAY_MODE_ONLINE.getCode()
                            .equals(redeemExpense.getCostPayMode()))
                    .collect(Collectors.toList());
            Map<Integer, List<RedeemExpense>> onlineListMap = StreamUtil.groupBy(onlineRedeemExpenses, RedeemExpense::getType);

            //------------------------------------------
            //资方总费用
            List<RedeemExpense> maRedeemExpenseList = redeemExpenseService.lambdaQuery()
                    .eq(RedeemExpense::getRedeemNo, redeemCargo.getRedeemNo())
                    .eq(RedeemExpense::getBusinessCategory, GoodsEnum.UNIFIED.getCode()).list();
            BigDecimal maMoneySum = BigDecimal.ZERO;
            BigDecimal advanceSum = BigDecimal.ZERO;
            BigDecimal delaySum = BigDecimal.ZERO;
            for (RedeemExpense expense : maRedeemExpenseList) {
                if (ExpenseConstant.ExpenseTypeEnum.INTEREST.getName().equals(expense.getExpenseType())) {
                    maMoneySum = maMoneySum.add(expense.getMoney());
                }
                if (ExpenseConstant.ExpenseTypeEnum.PREPAYMENT_SERVICE_FEE.getName().equals(expense.getExpenseType())) {
                    advanceSum = advanceSum.add(expense.getMoney());
                }
                if (ExpenseConstant.ExpenseTypeEnum.OVERDUE_INTEREST.getName().equals(expense.getExpenseType())) {
                    delaySum = delaySum.add(expense.getMoney());
                }
            }
            //还款试算
            RedeemCargoCalculationDTO redeemCargoCalculationDTO = new RedeemCargoCalculationDTO();
            redeemCargoCalculationDTO.setNum(redeemCargo.getNum());
            redeemCargoCalculationDTO.setWarehouseId(redeemCargo.getStockId());
            redeemCargoCalculationDTO.setFeeNode(ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode());
            RedeemCargoCalculationVO redeemCargoCalculationVO = redeemCargoService.calculationRedeem(redeemCargoCalculationDTO);
            //创建还款订单
            RepaymentInfoDTO repaymentInfoDTO = redeemCargoCalculationVO.getRepaymentInfoDTO();
            RepaymentCreateDTO build = RepaymentCreateDTO.builder()
                    .id(repaymentInfoDTO.getId())
                    .amount(redeemCargoCalculationVO.getPrincipal())
                    .interest(repaymentInfoDTO.getShouldInterest())
                    .repaymentPlanFeeList(repaymentInfoDTO.getRepaymentPlanFeeList())
                    .repaymentType(RepaymentTypeEnum.BORROW_AND_RETURN.getCode())
                    .onlinePayCode(IdWorker.getIdStr()).build();
            RepaymentOrderDTO repayment = repaymentBizService.createRepayment(build, repaymentInfoDTO);
            //创建本次还款费用订单
            List<ExpenseOrder> expenseOrderByRepaymentPlanFeeList = repaymentBizService.createExpenseOrderByRepaymentPlanFeeList(costCalculusVO.getExpenseOrderDetailFinanceVos(), repayment, financeApply.getFinanceNo(), redeemCargo.getRedeemNo(), PlatformExpensesEnum.PLAT_TYPE_REDEEM.getCode()
                    , ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode());

            redeemCargo.setRepaymentRecordId(repayment.getLoanManageRepayment().getId());
            //修改倒计时时间
            LocalDateTime countdownExpireTime = goodsTimingService.getByExpireTime(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_RANSOM_APPLY.getCode());
            redeemCargo.setCountdownExpireTime(countdownExpireTime);
            Map<String, Object> map = new HashMap<>();
            map.put("redeemCargoId", redeemCargo.getId());
            map.put("stockId", redeemCargo.getStockId());
            map.put("num", redeemCargo.getNum());
//            map.put("loanManageId", loanManageRepayment.getId());
            String message = JSONUtil.toJsonStr(map);
            Map<String, Object> param = new HashMap<>();
            Integer seconds = goodsTimingService.getSecondsByGoodsIdAndType(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_RANSOM_APPLY.getCode());
            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
                    .messageType(PurchaseMessageTypeEnum.PURCHASE_REDEEM_CARGO_APPLY.getCode())
                    .msg(message)
                    .seconds(seconds)
                    .extendParam(new HashMap<>())
                    .status(3)
                    .build());

//            // 发送mq延时消息，超时未操作 代采-赎货申请待确认状态 则关闭该申请
//            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
//                    .messageType(PledgeMessageTypeEnum.PURCHASE_REDEEMCARGO_APPLY.getValue())
//                    .status(RabbitMqStatusEnum.PURCHASE_REDEEMCARGO_APPLY.getCode())
//                    .extendParam(param)
//                    .msg(message)
//                    .build());
        }
        redeemCargoService.updateById(redeemCargo);
    }


}


