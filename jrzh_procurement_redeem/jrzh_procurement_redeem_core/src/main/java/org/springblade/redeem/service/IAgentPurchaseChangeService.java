/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.loan.entity.AgentPurchaseChange;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.loan.vo.AgentPurchaseChangeVO;

import java.util.List;
import java.util.Map;

/**
 * 代采变更表 服务类
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface IAgentPurchaseChangeService extends BaseService<AgentPurchaseChange> {

    /**
     * 自定义分页
     *
     * @param page
     * @param agentPurchaseChange
     * @return
     */
    IPage<AgentPurchaseChangeVO> selectAgentPurchaseChangePage(IPage<AgentPurchaseChangeVO> page, AgentPurchaseChangeVO agentPurchaseChange);

    /**
     * 根据代采变更记录保存代采变更
     *
     * @param apply
     */
    void saveByAgentPurchaseChangeApply(AgentPurchaseChangeApply apply);

    /**
     * 根据赎货单号查询退换货数量
     *
     * @param redeemNoList
     * @return
     */
    Map<String, AgentPurchaseChangeVO> getChangeGoodsNum(List<String> redeemNoList);

    /**
     * 根据赎货单号查询变更集合
     *
     * @param redeemNo
     * @return
     */
    List<AgentPurchaseChange> getListByRedeemNo(String redeemNo);

    /**
     * 代采变更分页
     *
     * @param query
     * @param agentPurchaseChange
     * @return
     */
    IPage<AgentPurchaseChangeVO> agentPurchaseChangePage(Query query, AgentPurchaseChange agentPurchaseChange);

    /**
     * 根据融资id查询历史变更记录
     *
     * @param id
     * @return
     */
    Map<Integer, AgentPurchaseChangeVO> getAgentPurchaseChangeHistoryByFinanceId(Long id);
}
