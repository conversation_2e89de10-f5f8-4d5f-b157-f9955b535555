package org.springblade.redeem.listener;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.service.IBillFinancialFlowService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.dto.LoanManageRepaymentDTO;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.service.*;
import org.springblade.loan.utils.LoanUtils;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.loan.wrapper.LoanManageRepaymentPlanWrapper;
import org.springblade.message.enums.MessageSceneEnum;
import org.springblade.message.utils.MessageNotifyUtil;
import org.springblade.redeem.constant.RedeemConstant;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.enums.ExtractTypeEnum;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 赎货确认申请监听
 *
 * <AUTHOR>
 */
@Component("redeemConfirmListener")
@RequiredArgsConstructor
public class RedeemConfirmListener implements ExecutionListener {

    private final IRedeemCargoService redeemCargoService;
    private final IExpenseOrderService expenseOrderService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final ILoanManageIouService loanManageIouService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final IFinanceApplyService financeApplyService;
    private final IWarehouseDetailsService warehouseDetailsService;
    private final IBillFinancialFlowService billFinancialFlowService;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;
    private final IRepaymentFeeService repaymentFeeService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        String redeemNo = delegateExecution.getVariable(RedeemConstant.REDEEMNO, String.class);
        RedeemCargo redeemCargo = redeemCargoService.lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemNo).one();
//		ExpenseOrder expenseOrder = expenseOrderService.getById(redeemCargo.getExpenseOrderId());
        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
//		Object expenseOrderObj = delegateExecution.getVariable(RedeemConstant.BILL_EXPENSE_ORDER);
//		RedeemExamineConfirmDTO billExpenseOrderDto = JSONUtil.toBean(JSONUtil.toJsonStr(expenseOrderObj), RedeemExamineConfirmDTO.class);
        //获取所有线下支付的费用订单
//		List<Map> expenseOrderList = delegateExecution.getVariable(RedeemConstant.BILL_EXPENSE_ORDER_List, List.class);
//		List<RedeemExamineConfirmDTO> examineConfirmDTOList = Collections.emptyList();
//		Map<String, ExpenseOrder> expenseOrderMap = Collections.emptyMap();
//		if (CollectionUtil.isNotEmpty(expenseOrderList)){
//			examineConfirmDTOList = StreamUtil.map(expenseOrderList, o -> JSONUtil.toBean(JSONUtil.toJsonStr(o), RedeemExamineConfirmDTO.class));
//			List<String> expenseNos = StreamUtil.map(examineConfirmDTOList, RedeemExamineConfirmDTO::getBillExpenseNo);
//			expenseOrderMap = expenseOrderService.getMapInBillExpenseNo(expenseNos);
//		}
//		Object loanManageRepaymentObj = delegateExecution.getVariable(RedeemConstant.LOAN_MANAGE_REPAYMENT);
//		RedeemExamineConfirmDTO loanManageRepaymentDto = JSONUtil.toBean(JSONUtil.toJsonStr(loanManageRepaymentObj), RedeemExamineConfirmDTO.class);
        //
        List<ExpenseOrder> billExpenseOrders = Lists.newArrayList();
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            //中止
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_SUSPEND.getKey());
//			expenseOrder.setPaymentStatus(billExpenseOrderDto.getStatus());
//			loanManageRepayment.setStatus(loanManageRepaymentDto.getStatus());
            //修改支付状态
//			for (RedeemExamineConfirmDTO redeemExamineConfirmDTO : examineConfirmDTOList){
//				ExpenseOrder billExpenseOrder = expenseOrderMap.get(redeemExamineConfirmDTO.getBillExpenseNo());
//				billExpenseOrder.setPaymentStatus(redeemExamineConfirmDTO.getStatus());
//				billExpenseOrders.add(billExpenseOrder);
//			}
            warehouseDetailsService.operateFormInToRedemption(redeemCargo.getStockId(), redeemCargo.getNum(), true);
            //代采-还款赎货审核失败通知
            MessageNotifyUtil.notifyByTemplate(loanManageRepayment.getUserId(), MessageSceneEnum.PURCHASE_Repayment_Fail.getValue());
        } else {
            //通过
            //第三方物流状态更改为待发货
            if (ExtractTypeEnum.LOGISTICS_EXTRACT.getKey().equals(redeemCargo.getExtractType())) {
                redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_SEND.getKey());
            } else {
                //自提状态更改为待提货
                redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_EXTRACT.getKey());
            }
            //修改费用订单、还款单状态
//			if (expenseOrder != null) {
//				expenseOrder.setPaymentStatus(billExpenseOrderDto.getStatus());
//				expenseOrder.setAmount(billExpenseOrderDto.getActualAmount());
//				expenseOrder.setBank(billExpenseOrderDto.getBank());
//				expenseOrder.setAccount(billExpenseOrderDto.getBankCardNo());
//				expenseOrder.setPayAttachId(billExpenseOrderDto.getVoucher());
//				expenseOrder.setPayTime(new Date());
//				expenseOrder.setBillPaySerialNo(CodeUtil.generateCode(CodeEnum.BILL_PAY_SERIAL_NO));
//			}
            FinanceApply financeApply = financeApplyService.getByFinanceNo(redeemCargo.getFinancingNo());
            //修改支付状态
//			for (RedeemExamineConfirmDTO redeemExamineConfirmDTO : examineConfirmDTOList){
//				ExpenseOrder billExpenseOrder = expenseOrderMap.get(redeemExamineConfirmDTO.getBillExpenseNo());
//				billExpenseOrder.setPaymentStatus(redeemExamineConfirmDTO.getStatus());
//				billExpenseOrder.setAmount(redeemExamineConfirmDTO.getActualAmount());
//				billExpenseOrder.setBank(redeemExamineConfirmDTO.getBank());
//				billExpenseOrder.setAccount(redeemExamineConfirmDTO.getBankCardNo());
//				billExpenseOrder.setPayAttachId(redeemExamineConfirmDTO.getVoucher());
//				billExpenseOrder.setPayTime(LocalDateTime.now());
//				billExpenseOrder.setBillPaySerialNo(CodeUtil.generateCode(CodeEnum.BILL_PAY_SERIAL_NO));
//				billExpenseOrders.add(billExpenseOrder);
//				//新增财务流水
//				BillFinancialFlow billFinancialFlow = new BillFinancialFlow();
//				billFinancialFlow.setAmount(redeemExamineConfirmDTO.getActualAmount());
//				billFinancialFlow.setTransactionFlowNo(CodeUtil.generateCode(CodeEnum.BILL_TRANSACTION_FLOW_NO));
//				billFinancialFlow.setFinancialType(1);
//				billFinancialFlow.setCustomerId(redeemCargo.getCreateUser());
//				billFinancialFlow.setFinanceApplyId(financeApply.getId());
//				billFinancialFlow.setIncomeStatus(1);
//				billFinancialFlow.setFinanceNo(financeApply.getFinanceNo());
//				billFinancialFlowService.save(billFinancialFlow);
//			}

            LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(redeemCargo.getFinancingNo());
            LoanManageRepaymentPlan repaymentPlan = loanManageRepaymentPlanService.lambdaQuery().eq(LoanManageRepaymentPlan::getIouNo, loanManageIou.getIouNo()).one();
            //LoanManageRepaymentPlan loanManageRepaymentPlan = generateNewRepaymentPlan(repaymentPlan, loanManageRepayment);

            if (!RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(loanManageRepayment.getStatus())) {
//				loanManageRepayment.setStatus(loanManageRepaymentDto.getStatus());
//				loanManageRepayment.setActualAmount(loanManageRepaymentDto.getActualAmount());
//				loanManageRepayment.setRepaymentTime(loanManageRepaymentDto.getRepaymentTime());
//				loanManageRepayment.setBank(loanManageRepaymentDto.getBank());
//				loanManageRepayment.setBankCardNo(loanManageRepaymentDto.getBankCardNo());
//				loanManageRepayment.setVoucher(loanManageRepaymentDto.getVoucher());
//				loanManageRepayment.setActualPrincipal(loanManageRepayment.getPrincipal());
//				loanManageRepayment.setActualInterest(loanManageRepayment.getInterest());
//				loanManageRepayment.setActualPenaltyInterest(loanManageRepayment.getPenaltyInterest());
//				loanManageRepayment.setServiceCharge(loanManageRepayment.getServiceCharge());
//				loanManageRepayment.setPayMode(PayModeEnum.PAY_MODE_BELOW.getCode());
//				loanManageRepayment.setOperatorUserId(MyAuthUtil.getUserId());
//				//额度回滚
//				WarehouseDetails warehouseDetails = warehouseDetailsService.getById(redeemCargo.getStockId());
//				EnterpriseQuotaVO qouat = enterpriseQuotaService
//						.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(financeApply.getGoodsId(), EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), redeemCargo.getCreateUser());
//				if (qouat != null && QuotaTypeEnum.RECYCLING.getCode().equals(qouat.getQuotaType())) {
//					if (RecycleTypeEnum.REPAYMENT_RECYCLE.getCode().equals(qouat.getRecycleType())) {
//						enterpriseQuotaService.addQuota(financeApply.getGoodsId(),
//								EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(),
//								redeemCargo.getCreateUser(),
//								warehouseDetails.getFinancingPrice().multiply(new BigDecimal(redeemCargo.getNum())));
//					} else {
//						//查询已支付的还款本金
//						List<LoanManageRepayment> repaymentList = loanManageRepaymentTermService.getPayedRepaymentByPlanId(repaymentPlan.getId());
//						BigDecimal beforeActualPrincipal = repaymentList.stream().map(LoanManageRepayment::getActualPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//						if (BigDecimal.ZERO.equals(repaymentPlan.getPrincipal().subtract(beforeActualPrincipal).subtract(loanManageRepayment.getActualPrincipal()))) {
//							enterpriseQuotaService.addQuota(financeApply.getGoodsId(),
//									EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(),
//									redeemCargo.getCreateUser(),
//									financeApply.getAmount());
//						}
//					}
//				}
            }
//			//更新还款记录费用状态
//			List<RepaymentFee> repaymentFees = repaymentFeeService.getUnRepaymentFeeByRepaymentIds(Collections.singletonList(loanManageRepayment.getId()));
//			if(CollUtil.isNotEmpty(repaymentFees)){
//				for (RepaymentFee repaymentFee : repaymentFees) {
//					repaymentFee.setStatus(RepaymentConstant.RepaymentStatusEnum.PAY.getCode());
//					repaymentFee.setActualAmount(repaymentFee.getShouldAmount());
//				}
//				repaymentFeeService.saveOrUpdateBatch(repaymentFees);
//
//				List<RepaymentPlanFee> planFees = repaymentPlanFeeService.getPlanFeeByPlanIds(repaymentPlan.getId());
//				BigDecimal payFeeAmount = repaymentFeeService.getPayRepaymentFeeAmount(planFees.stream().map(BaseEntity::getId).collect(Collectors.toList()));
//				BigDecimal planFeeAmount = planFees.stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//				if(payFeeAmount.compareTo(planFeeAmount) >= 0){
//					//更新还款计划费用表
//					planFees.forEach(planFee->planFee.setRepaymentStatus(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode()));
//					repaymentPlanFeeService.saveOrUpdateBatch(planFees);
//				}
//			}
            //判断还款计划是否已结清
            LoanManageRepaymentDTO loanManageRepaymentDTO = new LoanManageRepaymentDTO();
            loanManageRepaymentDTO.setRepaymentTime(loanManageRepayment.getRepaymentTime());
//			loanManageRepaymentService.isSettle(loanManageRepaymentDTO,loanManageRepayment,repaymentPlan);
//			loanManageRepaymentPlanService.saveOrUpdate(repaymentPlan);

            //代采-还款赎货成功通知
            MessageNotifyUtil.notifyByTemplate(loanManageRepayment.getUserId(), MessageSceneEnum.PURCHASE_Repayment_Suc.getValue());
        }
        //更新费用订单状态
//		if (CollectionUtil.isNotEmpty(billExpenseOrders)){
//			expenseOrderService.updateBatchById(billExpenseOrders);
//		}
//		loanManageRepaymentService.updateById(loanManageRepayment);
        redeemCargoService.updateById(redeemCargo);

    }

    private LoanManageRepaymentPlan generateNewRepaymentPlan(LoanManageRepaymentPlan loanManageRepaymentPlan, LoanManageRepayment loanManageRepayment) {
        LoanManageRepaymentPlan newLoanManageRepaymentPlan = LoanManageRepaymentPlanWrapper.build().newEntity(loanManageRepaymentPlan);
        BigDecimal principal = loanManageRepayment.getPrincipal();
        newLoanManageRepaymentPlan.setPrincipal(loanManageRepaymentPlan.getPrincipal().subtract(principal));
        IFinanceApplyService financeApplyService = SpringUtil.getBean(IFinanceApplyService.class);
        FinanceApply financeApply = financeApplyService.getById(loanManageRepaymentPlan.getFinanceApplyId());
        RepaymentPlanCal repaymentPlanCal = LoanUtils.calculatePrincipalAndInvest(newLoanManageRepaymentPlan.getPrincipal(), financeApply.getAnnualInterestRate(), LocalDate.now(), loanManageRepaymentPlan.getRepaymentTime(), CommonConstant.NUMBER_STRATEGY, 0);
        List<StagRecordVO> stagRecords = repaymentPlanCal.getStagRecords();
        newLoanManageRepaymentPlan.setInterest(stagRecords.get(0).getMonthlyInterest());
        loanManageRepaymentPlanService.removeById(loanManageRepaymentPlan.getId());
        if (BigDecimal.ZERO.compareTo(newLoanManageRepaymentPlan.getPrincipal()) != 0) {
            loanManageRepaymentPlanService.save(newLoanManageRepaymentPlan);
        }
        return newLoanManageRepaymentPlan;
    }
}


