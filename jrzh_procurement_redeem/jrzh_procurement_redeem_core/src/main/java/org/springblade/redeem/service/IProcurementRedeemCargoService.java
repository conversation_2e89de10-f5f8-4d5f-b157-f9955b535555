/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.service;

import org.springblade.redeem.dto.RedeemCargoConfirmDTO;
import org.springblade.redeem.vo.RedeemDetailCargoVO;
import org.springblade.redeem.vo.RedeemDetailFontCargoVO;
import org.springblade.warehouse.vo.WarehouseDetailsChildVO;

/**
 * 赎货表 服务类
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
public interface IProcurementRedeemCargoService{
    /**
     * 融资端发起赎货申请详细信息
     *
     * @param warehouseId
     */
    WarehouseDetailsChildVO fontWarehousDetail(Long warehouseId);
    /**
     * 平台端赎货申请详细信息
     *
     * @param redemmNo
     * @return
     */
    RedeemDetailCargoVO backDetail(String redemmNo);

    RedeemDetailFontCargoVO fontDetail(String redeemNo);

    /**
     * 融资端赎货确认发起
     * @param redeemCargoConfirmDTO
     */
    Boolean redeemConfirm(RedeemCargoConfirmDTO redeemCargoConfirmDTO);
}
