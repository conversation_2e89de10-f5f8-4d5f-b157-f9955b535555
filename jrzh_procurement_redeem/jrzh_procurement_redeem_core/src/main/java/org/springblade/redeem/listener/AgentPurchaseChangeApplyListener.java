package org.springblade.redeem.listener;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.enums.*;
import org.springblade.common.enums.bill.BillPayNameEnum;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.loan.enums.AgentPurchaseChangeApplyStatusEnum;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.IAgentPurchaseChangeApplyService;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.service.IRefundBizService;
import org.springblade.warehouse.entity.RedemptionWarehouseEntering;
import org.springblade.warehouse.service.IRedemptionWarehouseEnteringService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 代采变更申请监听器
 *
 * <AUTHOR>
 */

@RequiredArgsConstructor
@Component("agentPurchaseChangeApplyListener")
public class AgentPurchaseChangeApplyListener implements ExecutionListener {

    private final IAgentPurchaseChangeApplyService agentPurchaseChangeApplyService;
    private final IFinanceApplyService financeApplyService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final IRedemptionWarehouseEnteringService redemptionWarehouseEnteringService;
    private final IRefundBizService refundService;
    private final IRedeemCargoService redeemCargoService;


    @Override
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();

        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        Object object = delegateExecution.getVariable(WfProcessConstant.AGENT_PURCHASE_CHANGE_APPLY);
        AgentPurchaseChangeApply apply = JSONUtil.toBean(JSONUtil.toJsonStr(object), AgentPurchaseChangeApply.class);
        if (StringUtil.equals(processTerminal, Boolean.TRUE.toString()) || StringUtil.equals(processTerminal, "withdraw")) {
            //终止
            handlerProcessTerminal(delegateExecution, apply);
        } else {
            //通过
            handlerSuccess(delegateExecution, apply);
        }
    }

    private void handlerSuccess(DelegateExecution delegateExecution, AgentPurchaseChangeApply apply) {
        //更新变更记录表
        apply.setStatus(AgentPurchaseChangeApplyStatusEnum.PASSED.getCode());
        apply.setApprovalTime(LocalDateTime.now());
        agentPurchaseChangeApplyService.updateById(apply);
        switch (apply.getChangeType()) {
            case 1:
                purchaseReturnGoodsRefund(delegateExecution, apply);
                break;
            case 2:
                purchaseRefund(delegateExecution, apply);
                break;
            case 3:
                purchaseExchangeGoods(delegateExecution, apply);
                break;
            default:
                break;
        }
        //更新融资单记录
        FinanceApply financeNo = financeApplyService.getById(apply.getFinanceId());
        financeNo.setStatus(PurchaseStateEnum.PURCHASE_STATUS_SEVEN.getCode());
        financeApplyService.updateById(financeNo);
        //更新流程进度
        businessProcessProgressService.updateBusinessProcessProgress(apply.getId(), null, ProcessTypeEnum.AGENT_PURCHASE_CHANGE_APPLY.getCode(), apply.getProcessInstanceId(), apply.getUserId(), ProcessStatusEnum.FINISH.getCode());
    }

    /**
     * 退货退款
     */
    private void purchaseReturnGoodsRefund(DelegateExecution delegateExecution, AgentPurchaseChangeApply apply) {
        //新增退款记录，保存退款单号
        Refund refund = builderRefund(apply);
        refundService.save(refund);
        //保存退款单号
        apply.setRefundOrderNo(refund.getRefundOrderNo());
        agentPurchaseChangeApplyService.updateById(apply);
    }


    /**
     * 退款
     *
     * @param apply
     */
    private void purchaseRefund(DelegateExecution delegateExecution, AgentPurchaseChangeApply apply) {
        //新增退款记录，保存退款单号
        Refund refund = builderRefund(apply);
        refundService.save(refund);
        //保存退款单号
        apply.setRefundOrderNo(refund.getRefundOrderNo());
        agentPurchaseChangeApplyService.updateById(apply);
    }

    /**
     * 换货
     *
     * @param apply
     */
    private void purchaseExchangeGoods(DelegateExecution delegateExecution, AgentPurchaseChangeApply apply) {
        //创建一条新的待入库记录
        RedemptionWarehouseEntering warehouse = redemptionWarehouseEnteringService.getOne(Wrappers.<RedemptionWarehouseEntering>lambdaQuery()
                .eq(RedemptionWarehouseEntering::getFinanceNo, apply.getFinanceNo())
                .orderByAsc(BaseEntity::getCreateTime).last("limit 1"));
        warehouse.setId(null);
        warehouse.setReadyToStorage(apply.getGoodsNum());
        warehouse.setType(PurchaseEnum.EXCHANGE_GOODS.getCode());
        warehouse.setOriginalRedeemNo(apply.getRedeemNo());
        warehouse.setChangeApplyId(apply.getId());
        redemptionWarehouseEnteringService.save(warehouse);
    }


    private void handlerProcessTerminal(DelegateExecution delegateExecution, AgentPurchaseChangeApply apply) {
        //更新变更记录表
        apply.setStatus(AgentPurchaseChangeApplyStatusEnum.REJECTED.getCode());
        agentPurchaseChangeApplyService.updateById(apply);
        //更新融资单号
        FinanceApply financeNo = financeApplyService.getById(apply.getFinanceId());
        financeNo.setStatus(PurchaseStateEnum.PURCHASE_STATUS_SEVEN.getCode());
        financeApplyService.updateById(financeNo);
        //更新赎货订单状态
        RedeemCargo redeemCargo = redeemCargoService.getOne(Wrappers.<RedeemCargo>lambdaQuery()
                .eq(RedeemCargo::getRedeemNo, apply.getRedeemNo()));
        redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey());
        redeemCargoService.updateById(redeemCargo);
        //更新流程进度
        businessProcessProgressService.updateBusinessProcessProgress(apply.getId(), null, ProcessTypeEnum.AGENT_PURCHASE_CHANGE_APPLY.getCode(), apply.getProcessInstanceId(), apply.getUserId(), ProcessStatusEnum.TERMINAL.getCode());
    }

    /**
     * 新增一条退款记录
     *
     * @param apply
     */
    private Refund builderRefund(AgentPurchaseChangeApply apply) {
        Refund refund = new Refund();
        refund.setStatus(RefundEnum.RefundStatusEnum.STATUS_NOT.getCode());
        refund.setRefundOrderNo(CodeUtil.generateCode(CodeEnum.REFUND_NO));
        refund.setRefundAmount(apply.getRefundAmount());

        refund.setUserId(apply.getUserId());
        refund.setPaymentMethod(BillPayNameEnum.OFFLINE_PAY.getStatus().toString());
        refund.setRefundType(RefundEnum.RefundTypeEnum.PAYMENT_FOR_GOODS.getCode());
        refund.setCashDepositRate(BigDecimal.ZERO);
        refund.setBondRefundType(null);
        refund.setFinanceNo(apply.getFinanceNo());
        return refund;
    }
}
