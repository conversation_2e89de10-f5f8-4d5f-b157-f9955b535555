/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.loan.entity.AgentPurchaseChange;
import org.springblade.loan.vo.AgentPurchaseChangeVO;
import org.springblade.loan.wrapper.AgentPurchaseChangeWrapper;
import org.springblade.redeem.service.IAgentPurchaseChangeService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 代采变更表 控制器
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_LOAN + CommonConstant.WEB_BACK + "/loan/agentPurchaseChange")
@Api(value = "代采变更表", tags = "代采变更表接口")
public class AgentPurchaseChangeController extends BladeController {

    private final IAgentPurchaseChangeService agentPurchaseChangeService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入agentPurchaseChange")
    @PreAuth("hasPermission('agentPurchaseChange:agentPurchaseChange:detail') or hasRole('administrator')")
    public R<AgentPurchaseChangeVO> detail(AgentPurchaseChange agentPurchaseChange) {
        AgentPurchaseChange detail = agentPurchaseChangeService.getOne(Condition.getQueryWrapper(agentPurchaseChange));
        return R.data(AgentPurchaseChangeWrapper.build().entityVO(detail));
    }

    /**
     * 分页 代采变更表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入agentPurchaseChange")
    //@PreAuth("hasPermission('agentPurchaseChange:agentPurchaseChange:list') or hasRole('administrator')")
    public R<IPage<AgentPurchaseChangeVO>> list(AgentPurchaseChange agentPurchaseChange, Query query) {
        return R.data(agentPurchaseChangeService.agentPurchaseChangePage(query, agentPurchaseChange));
    }


    /**
     * 自定义分页 代采变更表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入agentPurchaseChange")
    @PreAuth("hasPermission('agentPurchaseChange:agentPurchaseChange:page') or hasRole('administrator')")
    public R<IPage<AgentPurchaseChangeVO>> page(AgentPurchaseChangeVO agentPurchaseChange, Query query) {
        IPage<AgentPurchaseChangeVO> pages = agentPurchaseChangeService.selectAgentPurchaseChangePage(Condition.getPage(query), agentPurchaseChange);
        return R.data(pages);
    }

    /**
     * 新增 代采变更表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入agentPurchaseChange")
    @PreAuth("hasPermission('agentPurchaseChange:agentPurchaseChange:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody AgentPurchaseChange agentPurchaseChange) {
        return R.status(agentPurchaseChangeService.save(agentPurchaseChange));
    }

    /**
     * 修改 代采变更表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入agentPurchaseChange")
    @PreAuth("hasPermission('agentPurchaseChange:agentPurchaseChange:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody AgentPurchaseChange agentPurchaseChange) {
        return R.status(agentPurchaseChangeService.updateById(agentPurchaseChange));
    }

    /**
     * 新增或修改 代采变更表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入agentPurchaseChange")
    @PreAuth("hasPermission('agentPurchaseChange:agentPur传入agentPurchaseChangechaseChange:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody AgentPurchaseChange agentPurchaseChange) {
        return R.status(agentPurchaseChangeService.saveOrUpdate(agentPurchaseChange));
    }


    /**
     * 删除 代采变更表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('agentPurchaseChange:agentPurchaseChange:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(agentPurchaseChangeService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 根据融资id查询历史变更记录
     */
    @GetMapping("/getHistoryByFinanceId")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "根据融资id查询历史变更记录", notes = "传入融资单s")
    public R<Map<Integer, AgentPurchaseChangeVO>> getAgentPurchaseChangeHistoryByFinanceId(@RequestParam String id) {
        Long ids = Long.valueOf(id);
        return R.data(agentPurchaseChangeService.getAgentPurchaseChangeHistoryByFinanceId(ids));
    }


}
