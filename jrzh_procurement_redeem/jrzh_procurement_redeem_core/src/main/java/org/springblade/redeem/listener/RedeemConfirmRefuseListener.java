package org.springblade.redeem.listener;

import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.redeem.constant.RedeemConstant;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;

/**
 * 赎货拒绝
 *
 * <AUTHOR>
 */
@Component("redeemConfirmRefuseListener")
@RequiredArgsConstructor
public class RedeemConfirmRefuseListener implements TaskListener {


	private final IRedeemCargoService redeemCargoService;

	@Override
	public void notify(DelegateTask delegateTask) {
		String status = delegateTask.getVariable(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, String.class);

		if (StringUtil.isNotBlank(status) && StringUtil.equals(status, WfProcessConstant.STATUS_REJECT)) {
			//驳回
			String redeemNo = delegateTask.getVariable(RedeemConstant.REDEEMNO, String.class);
			RedeemCargo redeemCargo = redeemCargoService.lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemNo).one();
			redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_REJECT.getKey());
			redeemCargoService.updateById(redeemCargo);
		} else {

		}
	}


}


