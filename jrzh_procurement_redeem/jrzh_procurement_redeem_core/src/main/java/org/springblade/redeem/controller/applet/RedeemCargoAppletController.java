package org.springblade.redeem.controller.applet;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.redeem.dto.RedeemCargoQueryDTO;
import org.springblade.redeem.service.IProcurementRedeemCargoService;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.redeem.vo.RedeemCargoVO;
import org.springblade.redeem.vo.RedeemDetailFontCargoVO;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.warehouse.vo.WarehouseDetailsChildVO;
import org.springblade.warehouse.vo.WarehouseDetailsVO;
import org.springblade.warehouse.wrapper.WarehouseDetailsWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_REDEEM + CommonConstant.WEB_APPLET + "/cargo")
@Api(value = "赎货表", tags = "小程序赎货表接口")
public class RedeemCargoAppletController {

    private final IRedeemCargoService redeemCargoService;
    private final IProcurementRedeemCargoService procurementRedeemCargoService;
    private final IWarehouseDetailsService warehouseDetailsService;


    /**
     * 待赎列表
     */
    @GetMapping("/beforeList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入commodityList")
    public R<IPage<WarehouseDetailsVO>> list(@RequestParam Map<String, Object> queryMap, Query query) {

        String companyId = AuthUtil.getClaimsParam(WebUtil.getRequest(), "companyId", String.class);
        if (StringUtil.isBlank(companyId)) {
            throw new ServiceException("登录失效");
        }
        LambdaQueryWrapper<WarehouseDetails> queryWrapper = Condition.getQueryWrapper(queryMap, WarehouseDetails.class).lambda();
        IPage<WarehouseDetails> pages = warehouseDetailsService.page(Condition.getPage(query), queryWrapper
                .gt(WarehouseDetails::getWarehouseNum, 0).orderByDesc(WarehouseDetails::getCreateTime)
                .eq(WarehouseDetails::getCompanyId, companyId));
        return R.data(WarehouseDetailsWrapper.build().pageVO(pages));
    }


    /**
     * 发起赎货页面信息
     */
    @GetMapping("/applyInfo")
    public R<WarehouseDetailsChildVO> applyInfo(Long warehouseId) {
        return R.data(redeemCargoService.fontWarehousDetail(warehouseId));
    }


    /**
     * 分页 赎货表 赎货列表
     */
    @GetMapping("/afterList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入redeemCargo")
    public R<IPage<RedeemCargoVO>> list(RedeemCargoQueryDTO redeemCargo, Query query) {
        redeemCargo.setCreateUser(AuthUtil.getUserId());
        return R.data(redeemCargoService.queryPage(redeemCargo, query));
    }


    /**
     * 详情
     */
    @GetMapping("/Detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入redeemCargo")
    public R<RedeemDetailFontCargoVO> detail(String redeemNo) {
        return R.data(procurementRedeemCargoService.fontDetail(redeemNo));
    }


}
