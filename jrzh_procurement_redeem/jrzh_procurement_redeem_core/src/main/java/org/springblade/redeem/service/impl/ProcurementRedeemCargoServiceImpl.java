package org.springblade.redeem.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.common.enums.ProcessAgencyTypeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.entity.CustomerSupervise;
import org.springblade.customer.entity.FinancingAddress;
import org.springblade.customer.feign.ICustomerSuperviseClient;
import org.springblade.customer.service.IFinancingAddressService;
import org.springblade.customer.vo.FinancingAddressVO;
import org.springblade.customer.wrapper.FinancingAddressWrapper;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseInfoBizService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.expense.vo.ExpenseInfoVO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.finance.vo.ExpenseOrderDetailFinanceVo;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springblade.loan.vo.LoanManageRepaymentVO;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springblade.product.common.constant.AccountTypeEnum;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.redeem.constant.RedeemConstant;
import org.springblade.redeem.dto.GoodsDetailsDTO;
import org.springblade.redeem.dto.RedeemCargoConfirmDTO;
import org.springblade.redeem.dto.RedeemCargoVoucherDTO;
import org.springblade.redeem.entity.*;
import org.springblade.redeem.enums.ExtractTypeEnum;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.*;
import org.springblade.redeem.vo.*;
import org.springblade.redeem.wrapper.RedeemObjectionWrapper;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteRegionService;
import org.springblade.system.utils.UserUtils;
import org.springblade.warehouse.entity.Warehouse;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.warehouse.service.IWarehouseService;
import org.springblade.warehouse.vo.WarehouseDetailsChildVO;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-23  20:43
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ProcurementRedeemCargoServiceImpl implements IProcurementRedeemCargoService {
    private final IPurchaseInformationService purchaseInformationService;
    private final IWarehouseDetailsService warehouseDetailsService;
    private final IAttachService attachService;
    private final IExpenseOrderService billExpenseOrderService;
    private final IBusinessProcessProductService businessProcessProductService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    /**
     * 产品配置 接口
     */
    private final IFinanceApplyService financeApplyService;
    private final IRedeemExpenseService redeemExpenseService;
    private final IRedeemSendService redeemSendService;
    private final RemoteRegionService regionService;
    private final IRedeemObjectionService redeemObjectionService;
    private final IWarehouseService warehouseService;
    private final IExpenseOrderService expenseOrderService;
    private final IRedeemUserService redeemUserService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final ICustomerSuperviseClient customerSuperviseClient;
    private final IRedeemCargoService redeemCargoService;
    private final IFinancingAddressService financingAddressService;
    private final IExpenseInfoBizService expenseInfoBizService;
    private final IProductExpenseService productExpenseService;

    @Override
    public WarehouseDetailsChildVO fontWarehousDetail(Long warehouseId) {
        WarehouseDetailsChildVO warehouseDetailsChildVO = getWarehousDetail(warehouseId);
        PurchaseInformation purchaseInformation = purchaseInformationService
                .lambdaQuery()
                .eq(PurchaseInformation::getFinanceNo, warehouseDetailsChildVO.getFinanceNo())
                .one();
        Optional.ofNullable(purchaseInformation).ifPresent(e -> {
            warehouseDetailsChildVO.setExtractType(e.getPickUpManner());
            warehouseDetailsChildVO.setAddress(e.getReceiveAddress());
            warehouseDetailsChildVO.setExtractId(e.getId());
        });
        //查询质检报告


        return warehouseDetailsChildVO;
    }

    @Override
    public RedeemDetailCargoVO backDetail(String redemmNo) {
        RedeemDetailCargoVO redeemDetailCargoVO = new RedeemDetailCargoVO();
        RedeemCargo redeemCargo = redeemCargoService.lambdaQuery().eq(RedeemCargo::getRedeemNo, redemmNo).one();
        redeemDetailCargoVO.setYearRate(financeApplyService.getByFinanceNo(redeemCargo.getFinancingNo()).getAnnualInterestRate());
        redeemDetailCargoVO.setRedeemDetailCargoCurrencyVO(redeemDetailCargoCurrency(redeemCargo));
        redeemDetailCargoVO.setCreateTime(redeemCargo.getCreateTime());
        User user = UserUtils.getUserById(redeemCargo.getApplyUser());
        redeemDetailCargoVO.setCreateUser(user != null ? user.getName() : "");
        if (redeemCargo.getExpenseOrderId() != null) {
            ExpenseOrder expenseOrder = billExpenseOrderService.getById(redeemCargo.getExpenseOrderId());
            redeemDetailCargoVO.setBillPayStatus(expenseOrder.getPaymentStatus());
        }
        if (redeemCargo.getRepaymentRecordId() != null) {
            LoanManageRepayment repayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
            redeemDetailCargoVO.setRepaymentStatus(repayment.getStatus());
        }
        //仓库名称、仓储公司名称
        CustomerSupervise customerSupervise = customerSuperviseClient.getById(redeemDetailCargoVO.getRedeemDetailCargoCurrencyVO().getStorageId()).getData();
        if (customerSupervise != null) {
            redeemDetailCargoVO.setStorageCompany(customerSupervise.getSuperviseName());
        }
        Warehouse warehouse = warehouseService.getById(redeemDetailCargoVO.getRedeemDetailCargoCurrencyVO().getWarehouseId());
        if (warehouse != null) {
            redeemDetailCargoVO.setWarehouseName(warehouse.getWarehouseName());
        }
        //处理记录
        if (RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey().equals(redeemCargo.getStatus())) {
            List<RedeemObjection> redeemObjectionList = redeemObjectionService.lambdaQuery().eq(RedeemObjection::getRedeemNo, redemmNo).list();
            if (!redeemObjectionList.isEmpty()) {
                List<Long> userList = redeemObjectionList.stream().map(RedeemObjection::getUpdateUser).collect(Collectors.toList());
                Map<Long, String> userNameMap = UserUtils.mapUserName(userList);
                List<RedeemObjectionVO> redeemObjectionVOS = RedeemObjectionWrapper.build().listVO(redeemObjectionList);
                redeemObjectionVOS.forEach(e -> e.setUserName(userNameMap.get(e.getUpdateUser())));
                redeemDetailCargoVO.setRedeemObjectionVoList(redeemObjectionVOS);
            }
        }

        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
        LoanManageRepaymentVO loanManageRepaymentVO = LoanManageRepaymentWrapper.build().entityVO(loanManageRepayment);
        if (loanManageRepaymentVO != null && StrUtil.isNotBlank(loanManageRepaymentVO.getVoucher())) {
            loanManageRepaymentVO.setAttachList(attachService.listByIds(Func.toLongList(loanManageRepaymentVO.getVoucher())));
        }
        redeemDetailCargoVO.setLoanManageRepayment(loanManageRepaymentVO);
        return redeemDetailCargoVO;
    }

    @Override
    public RedeemDetailFontCargoVO fontDetail(String redeemNo) {
        //基础信息
        RedeemDetailFontCargoVO redeemDetailFontCargoVO = new RedeemDetailFontCargoVO();
        RedeemCargo redeemCargo = redeemCargoService.lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemNo).one();
        redeemDetailFontCargoVO.setRedeemDetailCargoCurrencyVO(redeemDetailCargoCurrency(redeemCargo));
        List<RedeemExpense> manExpenseCulationVoList = redeemDetailFontCargoVO.getRedeemDetailCargoCurrencyVO().getManExpenseCulationVoList();

        redeemDetailFontCargoVO.setCreateTime(redeemCargo.getCreateTime());
        User user = UserUtils.getUserById(redeemCargo.getApplyUser());
        redeemDetailFontCargoVO.setCreateUser(user != null ? user.getName() : "");
        redeemDetailFontCargoVO.setCountdownExpireTime(redeemCargo.getCountdownExpireTime());
        //待发货等后续状态展示实付金额
        if (RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_SUSPEND.getKey() < redeemCargo.getStatus()) {
            ExpenseOrder billExpenseOrder = expenseOrderService.getById(redeemCargo.getExpenseOrderId());
            LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
            redeemDetailFontCargoVO.
                    setActualAmount(NumberUtil.add(billExpenseOrder != null ? billExpenseOrder.getAmount() : BigDecimal.ZERO,
                            loanManageRepayment != null ? loanManageRepayment.getActualAmount() : BigDecimal.ZERO));
            redeemDetailFontCargoVO.setLoanManageRepayment(loanManageRepayment);
        }
        //已完成显示签收人
        if (RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey().equals(redeemCargo.getStatus())) {
            redeemDetailFontCargoVO.setReceivedPeople(redeemCargo.getReceivedPeople());
            redeemDetailFontCargoVO.setReceivedTime(redeemCargo.getReceivedTime());
        }
        //质检报告
        WarehouseDetails warehouseDetails = warehouseDetailsService.getById(redeemCargo.getStockId());
        String testReportAttachId = warehouseDetails.getTestReportAttachId();
        if (StringUtils.isNotBlank(testReportAttachId)) {
            List<Attach> attachListByIds = attachService.getAttachListByIds(Func.toLongList(testReportAttachId));
            redeemDetailFontCargoVO.setReportList(attachListByIds);
        }
        return redeemDetailFontCargoVO;
    }

    @Override
    public Boolean redeemConfirm(RedeemCargoConfirmDTO redeemCargoConfirmDTO) {
        //费用订单添加支付凭证
        List<RedeemCargoVoucherDTO> cargoVoucherDTOList = redeemCargoConfirmDTO.getRedeemCargoVoucherDTOS();
        //根据费用单号分组
//        Map<String, ExpenseOrder> expenseOrderMap = expenseOrderService.getMapInBillExpenseNo(StreamUtil.map(cargoVoucherDTOList, RedeemCargoVoucherDTO::getExpenseOrderNo));
//        String bankRepaymentVoucher = redeemCargoConfirmDTO.getBankRepaymentVoucher();
        //线下支付给对应的费用添加凭证
//        if (CollectionUtil.isNotEmpty(cargoVoucherDTOList)) {
//            for (RedeemCargoVoucherDTO cargoVoucherDTO : cargoVoucherDTOList) {
//                if (Objects.isNull(cargoVoucherDTO.getExpenseOrderNo())) {
//                    bankRepaymentVoucher = cargoVoucherDTO.getRepaymentVoucher();
//                } else {
//                    ExpenseOrder billExpenseOrder = expenseOrderMap.get(cargoVoucherDTO.getExpenseOrderNo());
//                    billExpenseOrder.setPayAttachId(cargoVoucherDTO.getRepaymentVoucher());
//                    expenseOrderService.updateById(billExpenseOrder);
//                }
//            }
//        }
        //费用订单添加支付凭证
        RedeemCargo redeemCargo = redeemCargoService.lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemCargoConfirmDTO.getRedeemNo()).one();
        RedeemExpense redeemExpense = redeemExpenseService.getOne(Wrappers.<RedeemExpense>lambdaUpdate()
                .eq(RedeemExpense::getRedeemNo, redeemCargoConfirmDTO.getRedeemNo())
                .eq(RedeemExpense::getType, GoodsEnum.GOODS_TYPE_FUND.getCode())
                .last("limit 1"));

//		BillExpenseOrder expenseOrder = expenseOrderService.getById(redeemCargo.getExpenseOrderId());
//		if (expenseOrder != null && !StrUtil.isNullOrUndefined(redeemCargoConfirmDTO.getPlatformFeeVoucher())) {
//			expenseOrder.setPayAttachId(redeemCargoConfirmDTO.getPlatformFeeVoucher());
//			expenseOrderService.updateById(expenseOrder);
//		}
        //还款记录添加支付凭证
        //资方是线上支付
//        if (PayModeEnum.PAY_MODE_BELOW.getCode().equals(redeemExpense.getCostPayMode())) {
//            LoanManageRepayment manageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
//            if (manageRepayment != null && !StrUtil.isNullOrUndefined(bankRepaymentVoucher)) {
//                manageRepayment.setVoucher(bankRepaymentVoucher);
//                loanManageRepaymentService.updateById(manageRepayment);
//            }
//        }
        FinanceApply financeApply = financeApplyService.getByFinanceNo(redeemCargo.getFinancingNo());
        //发起赎货流程
        HashMap<String, Object> variables = MapUtil.newHashMap();
        variables.put(RedeemConstant.REDEEMNO, redeemCargo.getRedeemNo());
        variables.put(ProcessConstant.GOODS_ID, financeApply.getGoodsId());
        variables.put(ProcessConstant.USER_ID, financeApply.getUserId());
        variables.put(ProcessConstant.ENTERPRISE_TYPE, UserUtils.getEnterpriseType());
        variables.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        variables.put(ProcessConstant.EXPENSE_INFO_LIST, redeemCargoConfirmDTO.getExpenseInfoExpenseList());
        redeemCargo.setProcessInstanceId(businessProcessProductService.startProcess(financeApply.getGoodsId(), ProcessAgencyTypeEnum.AGENCY_CONFIRM.getCode(), variables));
        //赎货状态修改
        redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_EXAMINEING.getKey());
        return redeemCargoService.updateById(redeemCargo);
    }

    /**
     * 赎货通用信息
     *
     * @param redeemCargo 赎货单
     * @return RedeemDetailCargoCurrencyVO
     */
    private RedeemDetailCargoCurrencyVO redeemDetailCargoCurrency(RedeemCargo redeemCargo) {
        WarehouseDetails warehouseDetails = warehouseDetailsService.getById(redeemCargo.getStockId());
        RedeemDetailCargoCurrencyVO detailCargoCurrencyVO = new RedeemDetailCargoCurrencyVO();
        detailCargoCurrencyVO.setNum(redeemCargo.getNum());
        detailCargoCurrencyVO.setRedeemNo(redeemCargo.getRedeemNo());
        detailCargoCurrencyVO.setExtractType(ExtractTypeEnum.getValueByKey(redeemCargo.getExtractType()));
        detailCargoCurrencyVO.setWarehouseAge(DateUtil.between(warehouseDetails.getWarehouseInDate(), LocalDate.now()).getDays());
        detailCargoCurrencyVO.setGoodLogo(StrUtil.isNotBlank(warehouseDetails.getGoodsInfo()) ?
                JsonUtil.parse(warehouseDetails.getGoodsInfo(), GoodsDetailsDTO.class).getLogo() : "");
        detailCargoCurrencyVO.setGoodsName(warehouseDetails.getGoodsName());
        detailCargoCurrencyVO.setGoodsSpec(warehouseDetails.getGoodsSpec());
        detailCargoCurrencyVO.setGoodsUnitValue(warehouseDetails.getGoodsUnitValue());
        detailCargoCurrencyVO.setPurchasePrice(warehouseDetails.getPurchasePrice());
        detailCargoCurrencyVO.setFinancingPrice(warehouseDetails.getFinancingPrice());
        detailCargoCurrencyVO.setSupplierName(warehouseDetails.getSupplierName());
        detailCargoCurrencyVO.setWarehouseNo(warehouseDetails.getWarehouseNo());
        detailCargoCurrencyVO.setFinanceNo(warehouseDetails.getFinanceNo());
        detailCargoCurrencyVO.setWarehouseInDate(warehouseDetails.getWarehouseInDate());
        detailCargoCurrencyVO.setRedemptionDate(warehouseDetails.getRedemptionDate());
        detailCargoCurrencyVO.setStatus(redeemCargo.getStatus());
        detailCargoCurrencyVO.setStorageId(warehouseDetails.getStorageId());
        detailCargoCurrencyVO.setWarehouseId(warehouseDetails.getWarehouseId());
        detailCargoCurrencyVO.setShouldInterest(BigDecimal.ZERO);
        //收货地址信息
        if (ExtractTypeEnum.WOM_EXTRACT.getKey().equals(redeemCargo.getExtractType())) {
            detailCargoCurrencyVO.setRedeemUser(redeemUserService.lambdaQuery().eq(RedeemUser::getCargoId, redeemCargo.getId()).one());
        } else {
            PurchaseInformation purchaseInformation = purchaseInformationService.getById(redeemCargo.getExtractId());
            FinancingAddressVO financingAddressVO = new FinancingAddressVO();
            //TODO 改成查一个地址
            if (purchaseInformation != null) {
                financingAddressVO.setEnterpriseName(purchaseInformation.getReceiveCompanyName());
                financingAddressVO.setUrbanAreas(purchaseInformation.getReceiveAddress());
                financingAddressVO.setContacts(purchaseInformation.getReceiveName());
                financingAddressVO.setAddressPhone(purchaseInformation.getReceiveNumber());
                financingAddressVO.setCreateTime(purchaseInformation.getCreateTime());

            } else {
                FinancingAddress financingAddress = financingAddressService.getById(redeemCargo.getExtractId());

                financingAddressVO = FinancingAddressWrapper.build().entityVO(financingAddress);
                Region region = CollUtil.getFirst(regionService.getList(Collections.singletonList(financingAddressVO.getLocation())).getData());
                financingAddressVO.setEnterpriseName(financingAddressVO.getAddressTarget());
                financingAddressVO.setAddressTarget(region.getProvinceName() + region.getCityName() + region.getDistrictName() + financingAddress.getAddressDetailed());
                //financingAddressVO.setUrbanAreas(region.getProvinceName() + region.getCityName() + region.getDistrictName());
            }

            detailCargoCurrencyVO.setFinancingAddress(financingAddressVO);
        }
        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
        //缴费信息
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS = expenseInfoBizService
                .listExpenseInfoList(redeemCargo.getRedeemNo(), PlatformExpensesEnum.PLAT_TYPE_REDEEM.getCode(), ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode(), -1L);
        Map<String, ExpenseInfoExpenseVO> expenseInfoExpenseVOSMap = expenseInfoExpenseVOS.stream().collect(Collectors.toMap(ExpenseInfoVO::getBillExpenseNo, e -> e));
        // 资方费用 = 还款利息+本金 资方类型的费用
        Optional<ExpenseInfoExpenseVO> first = expenseInfoExpenseVOS.stream().filter(e -> e.getExAmount().compareTo(BigDecimal.ZERO) > 0).findFirst();
        if (first.isPresent()) {
            ExpenseInfoExpenseVO expenseInfoExpenseVO = first.get();
            CapitalExpenseManVO capitalExpenseManVO = new CapitalExpenseManVO();
            capitalExpenseManVO.setLoanManageRepayment(loanManageRepayment);
            capitalExpenseManVO.setPayMode(expenseInfoExpenseVO.getPayMode());
            capitalExpenseManVO.setAttachList(expenseInfoExpenseVO.getOriginAttachList());
            //资方费用转化
            List<ExpenseOrderDetail> expenseOrderDetails = expenseInfoExpenseVOS.stream().flatMap(e -> e.getExpenseOrderDetail().stream()
                    .filter(ex -> AccountTypeEnum.CAPITAL_ACCOUNT.getCode().equals(ex.getAccountType()))).collect(Collectors.toList());
            List<RedeemExpense> collect = expenseOrderDetails.stream().map(e -> redeemCargoService.transferToRedeemExpensesList(e, warehouseDetails.getGoodsId(), e.getAmount(), -1L))
                    .collect(Collectors.toList());
            capitalExpenseManVO.setManExpenseCulationVoList(collect);
            detailCargoCurrencyVO.setManExpenseCulationVo(capitalExpenseManVO);
            detailCargoCurrencyVO.setManExpenseCulationVoList(collect);
            detailCargoCurrencyVO.setShouldInterest(loanManageRepayment.getInterest());
            //费用信息
            LoanManageRepaymentPlan repaymentPlan = loanManageRepaymentPlanService.getById(loanManageRepayment.getRepaymentPlanId());
            List<ExpenseOrderDetail> collect1 = expenseInfoExpenseVOS.stream().flatMap(e -> e.getExpenseOrderDetail().stream()).collect(Collectors.toList());
            CostCalculusVO costCalculusVO = redeemCargoService.buildCostCalculusVO(loanManageRepayment.getPrincipal(), loanManageRepayment.getInterest(), repaymentPlan.getAnnualInterestRate(), repaymentPlan.getDayRate(), collect1);
            //费用订单填充
            if (costCalculusVO.getExpenseOrderDetailFinanceVos() != null) {
                for (ExpenseOrderDetailFinanceVo expenseOrderDetailFinanceVo : costCalculusVO.getExpenseOrderDetailFinanceVos()) {
                    if (expenseOrderDetailFinanceVo.getBillExpenseNo() != null) {
                        ExpenseInfoExpenseVO infoExpenseVO = expenseInfoExpenseVOSMap.get(expenseOrderDetailFinanceVo.getBillExpenseNo());
                        expenseOrderDetailFinanceVo.setPaymentStatus(infoExpenseVO.getPaymentStatus());
                        expenseOrderDetailFinanceVo.setAttachInfoDTOList(infoExpenseVO.getAttachList());
                    }
                }
            }
            detailCargoCurrencyVO.setCostCalculusVO(costCalculusVO);
        }
        //其他费用转化
        List<PlaExpenseCulationVO> plaExpenseVOList = Lists.newArrayList();
        Map<Integer, List<ExpenseOrderDetail>> redeemExpenseMap = expenseInfoExpenseVOS.stream().flatMap(e -> e.getExpenseOrderDetail().stream()
                        .filter(ex -> !AccountTypeEnum.CAPITAL_ACCOUNT.getCode().equals(ex.getAccountType())))
                .collect(Collectors.groupingBy(ExpenseOrderDetail::getAccountType));
        for (Map.Entry<Integer, List<ExpenseOrderDetail>> expenseList : redeemExpenseMap.entrySet()) {
            List<ExpenseOrderDetail> expenseOrderDetails = expenseList.getValue();
            ExpenseOrderDetail expenseOrderDetail = CollUtil.getFirst(expenseOrderDetails);
            ExpenseInfoExpenseVO expenseInfoExpenseVO = expenseInfoExpenseVOSMap.get(expenseOrderDetail.getBillExpenseNo());
            PlaExpenseCulationVO plaExpenseCulationVO = new PlaExpenseCulationVO();
            plaExpenseCulationVO.setAccountId(expenseOrderDetail.getAccountId());
            plaExpenseCulationVO.setExpenseTypeStr(expenseOrderDetail.getParenExpenseName());
            List<RedeemExpense> collect = expenseOrderDetails.stream().map(e -> redeemCargoService.transferToRedeemExpensesList(e, warehouseDetails.getGoodsId(), e.getAmount(), -1L))
                    .collect(Collectors.toList());
            plaExpenseCulationVO.setPlaExpenseCulationVoList(collect);
            plaExpenseCulationVO.setAccountName(expenseOrderDetail.getAccountName());
            plaExpenseCulationVO.setExpenseStatus(expenseInfoExpenseVO.getPaymentStatus());
            plaExpenseCulationVO.setPayMode(expenseInfoExpenseVO.getPayMode());
            ExpenseOrder expenseOrder = BeanUtil.copyProperties(expenseInfoExpenseVO, ExpenseOrder.class);
            plaExpenseCulationVO.setBillExpenseOrder(expenseOrder);
            plaExpenseCulationVO.setAttachList(expenseInfoExpenseVO.getOriginAttachList());
            plaExpenseVOList.add(plaExpenseCulationVO);
        }
        detailCargoCurrencyVO.setPlaExpenseVOList(plaExpenseVOList);
        if (ObjectUtil.isNotEmpty(detailCargoCurrencyVO.getPlaExpenseVOList())) {
            detailCargoCurrencyVO.setPlaExpenseCulationVoList(detailCargoCurrencyVO.getPlaExpenseVOList()
                    .stream().flatMap(e -> e.getPlaExpenseCulationVoList().stream()).collect(Collectors.toList()));
        }
        //附件信息
        if (RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_CONFIRM.getKey() < redeemCargo.getStatus()) {
            if (ObjectUtil.isNotEmpty(detailCargoCurrencyVO.getPlaExpenseVOList())) {
                List<Attach> collect = detailCargoCurrencyVO.getPlaExpenseVOList().stream()
                        .filter(e -> CollUtil.isNotEmpty(e.getAttachList()))
                        .flatMap(e -> e.getAttachList().stream()).collect(Collectors.toList());
                detailCargoCurrencyVO.setPlaAttachList(collect);
            }
            if (loanManageRepayment != null && StrUtil.isNotBlank(loanManageRepayment.getVoucher())) {
                detailCargoCurrencyVO.setManAttachList(attachService.listByIds(Func.toLongList(loanManageRepayment.getVoucher())));
            }

        }

        //发货信息
        if (RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_SEND.getKey() < redeemCargo.getStatus()) {
            detailCargoCurrencyVO.setRedeemSend(redeemSendService.lambdaQuery().eq(RedeemSend::getRedeemNo, redeemCargo.getRedeemNo()).one());
        }
        detailCargoCurrencyVO.setShouldTotal(getShouldTotal(detailCargoCurrencyVO.getFinancingPrice().multiply(BigDecimal.valueOf(detailCargoCurrencyVO.getNum())), detailCargoCurrencyVO.getManExpenseCulationVoList()
                , detailCargoCurrencyVO.getPlaExpenseCulationVoList(), detailCargoCurrencyVO.getShouldInterest()));
        detailCargoCurrencyVO.setRealTotal(getRealTotal(expenseInfoExpenseVOS));
        return detailCargoCurrencyVO;
    }

    private BigDecimal getRealTotal(List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS) {
        return expenseInfoExpenseVOS.stream().filter(e -> ObjectUtil.isNotEmpty(e.getPayAmount())).map(ExpenseInfoVO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getShouldTotal(BigDecimal principal, List<RedeemExpense> manExpenseCulationVoList, List<RedeemExpense> plaExpenseVOList, BigDecimal interest) {
        if (CollUtil.isNotEmpty(plaExpenseVOList)) {
            BigDecimal platAmount = plaExpenseVOList.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            principal = principal.add(platAmount);
        }
        if (CollUtil.isNotEmpty(manExpenseCulationVoList)) {
            BigDecimal platAmount = manExpenseCulationVoList.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            principal = principal.add(platAmount);
        }
        return principal.add(interest);
    }

    /**
     * 获取库存订单信息
     *
     * @param warehouseId 库存id
     * @return WarehouseDetailsChildVO
     */
    public WarehouseDetailsChildVO getWarehousDetail(Long warehouseId) {
        WarehouseDetails warehouseDetails = warehouseDetailsService.getById(warehouseId);
        WarehouseDetailsChildVO warehouseDetailsChildVO = BeanUtil.copy(warehouseDetails, WarehouseDetailsChildVO.class);
        //查询质检报告
        String testReportAttachId = warehouseDetails.getTestReportAttachId();
        if (StringUtils.isNotBlank(testReportAttachId)) {
            List<Attach> attachListByIds = attachService.getAttachListByIds(Func.toLongList(testReportAttachId));
            warehouseDetailsChildVO.setReportList(attachListByIds);
        }
        //TODO 提货信息
        return warehouseDetailsChildVO
                .setWarehouseAge(DateUtil.between(warehouseDetails.getWarehouseInDate(), LocalDate.now()).getDays())
                .setPurchasePriceSum(NumberUtil.mul(warehouseDetails.getPurchasePrice(), warehouseDetails.getWarehouseNum()))
                .setFinancingPriceSum(NumberUtil.mul(warehouseDetails.getFinancingPrice(), warehouseDetails.getWarehouseNum()))
                .setPrincipalPayable(warehouseDetailsChildVO.getFinancingPriceSum())
                .setGoodLogo(StrUtil.isNotBlank(warehouseDetails.getGoodsInfo()) ? JsonUtil.parse(warehouseDetails.getGoodsInfo(), GoodsDetailsDTO.class).getLogo() : "");
    }
}
