package org.springblade.redeem.handler.refund;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.IAgentPurchaseChangeApplyService;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.handler.BillLoanRefund;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-01  18:43
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class BillRefundRefundPayHandler implements BillLoanRefund {


    @Override
    public void notify(Refund refundDTO) {
        IRedeemCargoService redeemCargoService = SpringUtil.getBean(IRedeemCargoService.class);
        IAgentPurchaseChangeApplyService agentPurchaseChangeApplyService = SpringUtil.getBean(IAgentPurchaseChangeApplyService.class);

        //保存代采变更记录
        AgentPurchaseChangeApply agentPurchaseChangeApply = agentPurchaseChangeApplyService.getOne(Wrappers.<AgentPurchaseChangeApply>lambdaQuery()
                .eq(AgentPurchaseChangeApply::getRefundOrderNo, refundDTO.getRefundOrderNo()));
        if (Objects.nonNull(agentPurchaseChangeApply)) {
            //保存变更记录
            agentPurchaseChangeApplyService.saveByAgentPurchaseChange(agentPurchaseChangeApply);
            //更新赎货单状态
            RedeemCargo redeemCargo = redeemCargoService.getOne(Wrappers.<RedeemCargo>lambdaQuery()
                    .eq(RedeemCargo::getRedeemNo, agentPurchaseChangeApply.getRedeemNo()));
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey());
            redeemCargoService.changOrderStatus(redeemCargo);
            redeemCargoService.updateById(redeemCargo);
        }
    }
}
