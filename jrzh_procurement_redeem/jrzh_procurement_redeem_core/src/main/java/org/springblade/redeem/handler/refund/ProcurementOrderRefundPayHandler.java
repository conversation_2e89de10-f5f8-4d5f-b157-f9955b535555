package org.springblade.redeem.handler.refund;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.RefundEnum;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.IAgentPurchaseChangeApplyService;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.refund.dto.RefundDTO;
import org.springblade.refund.handler.RefundPayHandler;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-01  18:43
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ProcurementOrderRefundPayHandler implements RefundPayHandler {
    private final IRedeemCargoService redeemCargoService;
    private final IAgentPurchaseChangeApplyService agentPurchaseChangeApplyService;

    @Override
    public RefundEnum.RefundTypeEnum support() {
        return RefundEnum.RefundTypeEnum.PAYMENT_FOR_GOODS;
    }

    @Override
    public void refundSuccess(RefundDTO refundDTO) {
        //保存代采变更记录
        AgentPurchaseChangeApply agentPurchaseChangeApply = agentPurchaseChangeApplyService.getOne(Wrappers.<AgentPurchaseChangeApply>lambdaQuery()
                .eq(AgentPurchaseChangeApply::getRefundOrderNo, refundDTO.getRefundOrderNo()));
        if (Objects.nonNull(agentPurchaseChangeApply)) {
            //保存变更记录
            agentPurchaseChangeApplyService.saveByAgentPurchaseChange(agentPurchaseChangeApply);
            //更新赎货单状态
            RedeemCargo redeemCargo = redeemCargoService.getOne(Wrappers.<RedeemCargo>lambdaQuery()
                    .eq(RedeemCargo::getRedeemNo, agentPurchaseChangeApply.getRedeemNo()));
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey());
            redeemCargoService.updateById(redeemCargo);
        }
    }
}
