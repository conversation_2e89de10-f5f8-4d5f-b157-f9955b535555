package org.springblade.redeem.rewriteInterface;

import lombok.AllArgsConstructor;
import org.springblade.procurement.finance.entity.PurchaseInformation;
import org.springblade.procurement.finance.service.IPurchaseInformationService;
import org.springblade.redeem.handle.purchase.RedeemPurchaseInformationService;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class RedeemPurchaseInformationServiceImpl implements RedeemPurchaseInformationService {

    private final IPurchaseInformationService purchaseInformationService;

    /**
     * 根据融资编号获取代采--基础信息
     *
     * @param financeNo 融资编号
     * @return 代采--基础信息
     */
    @Override
    public PurchaseInformation getByFinanceNo(String financeNo) {
        return purchaseInformationService.lambdaQuery()
                .eq(PurchaseInformation::getFinanceNo,financeNo)
                .one();
    }

    /**
     * 根据id获取代采--基础信息
     *
     * @param id 代采--基础信息 id
     * @return 代采--基础信息
     */
    @Override
    public PurchaseInformation getById(Long id) {
        return purchaseInformationService.getById(id);
    }
}
