/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.loan.enums.AgentPurchaseChangeApplyStatusEnum;
import org.springblade.loan.vo.AgentPurchaseChangeApplyVO;
import org.springblade.loan.wrapper.AgentPurchaseChangeApplyWrapper;
import org.springblade.redeem.dto.AgentPurchaseChangeDetailVO;
import org.springblade.redeem.service.IAgentPurchaseChangeApplyService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 代采变更申请记录表 控制器
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_LOAN + CommonConstant.WEB_BACK + "/loan/agentPurchaseChangeApply")
@Api(value = "代采变更申请记录表", tags = "代采变更申请记录表接口")
public class AgentPurchaseChangeApplyController extends BladeController {

    private final IAgentPurchaseChangeApplyService agentPurchaseChangeApplyService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入agentPurchaseChangeApply")
    @PreAuth("hasPermission('agentPurchaseChangeApply:agentPurchaseChangeApply:detail') or hasRole('administrator')")
    public R<AgentPurchaseChangeApplyVO> detail(AgentPurchaseChangeApply agentPurchaseChangeApply) {
        AgentPurchaseChangeApply detail = agentPurchaseChangeApplyService.getOne(Condition.getQueryWrapper(agentPurchaseChangeApply));
        return R.data(AgentPurchaseChangeApplyWrapper.build().entityVO(detail));
    }

    /**
     * 分页 代采变更申请记录表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入agentPurchaseChangeApply")
    @PreAuth("hasPermission('agentPurchaseChangeApply:agentPurchaseChangeApply:list') or hasRole('administrator')")
    public R<IPage<AgentPurchaseChangeApplyVO>> list(AgentPurchaseChangeApply agentPurchaseChangeApply, Query query) {
        IPage<AgentPurchaseChangeApply> pages = agentPurchaseChangeApplyService.page(Condition.getPage(query), Condition.getQueryWrapper(agentPurchaseChangeApply));
        return R.data(AgentPurchaseChangeApplyWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 代采变更申请记录表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入agentPurchaseChangeApply")
    @PreAuth("hasPermission('agentPurchaseChangeApply:agentPurchaseChangeApply:page') or hasRole('administrator')")
    public R<IPage<AgentPurchaseChangeApplyVO>> page(AgentPurchaseChangeApplyVO agentPurchaseChangeApply, Query query) {
        IPage<AgentPurchaseChangeApplyVO> pages = agentPurchaseChangeApplyService.selectAgentPurchaseChangeApplyPage(Condition.getPage(query), agentPurchaseChangeApply);
        return R.data(pages);
    }

    /**
     * 新增 代采变更申请记录表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入agentPurchaseChangeApply")
    @PreAuth("hasPermission('agentPurchaseChangeApply:agentPurchaseChangeApply:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody AgentPurchaseChangeApply agentPurchaseChangeApply) {
        return R.status(agentPurchaseChangeApplyService.save(agentPurchaseChangeApply));
    }

    /**
     * 修改 代采变更申请记录表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入agentPurchaseChangeApply")
    @PreAuth("hasPermission('agentPurchaseChangeApply:agentPurchaseChangeApply:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody AgentPurchaseChangeApply agentPurchaseChangeApply) {
        return R.status(agentPurchaseChangeApplyService.updateById(agentPurchaseChangeApply));
    }

    /**
     * 新增或修改 代采变更申请记录表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入agentPurchaseChangeApply")
    @PreAuth("hasPermission('agentPurchaseChangeApply:agentPurchaseChangeApply:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody AgentPurchaseChangeApply agentPurchaseChangeApply) {
        return R.status(agentPurchaseChangeApplyService.saveOrUpdate(agentPurchaseChangeApply));
    }


    /**
     * 删除 代采变更申请记录表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('agentPurchaseChangeApply:agentPurchaseChangeApply:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(agentPurchaseChangeApplyService.deleteLogic(Func.toLongList(ids)));
    }


    /**
     * 用户端发起代采变更流程 代采变更申请记录表
     */
    @PostMapping("/changeApply")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "发起代采变更流程", notes = "传入agentPurchaseChangeApply")
    public R changeApply(@Valid @RequestBody AgentPurchaseChangeApply agentPurchaseChangeApply) {
        return R.data(agentPurchaseChangeApplyService.changeApply(agentPurchaseChangeApply));
    }

    /**
     * 根据id查询代采变更记录
     */
    @GetMapping("/getById")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "根据id查询代采变更记录", notes = "id")
    public R<AgentPurchaseChangeApplyVO> getById(@RequestParam Long id) {
        return R.data(agentPurchaseChangeApplyService.getDetailById(id));
    }

    /**
     * 根据赎货单号查询代采变更记录
     */
    @GetMapping("/getByRedeemNo")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "根据赎货单号查询代采变更记录", notes = "redeemNo")
    public R<AgentPurchaseChangeApplyVO> getById(@RequestParam String redeemNo) {
        List<Integer> statusList = Arrays.asList(AgentPurchaseChangeApplyStatusEnum.REJECTED.getCode()
                , AgentPurchaseChangeApplyStatusEnum.CHANGE_COMPLETED.getCode());
        //根据赎货单号查询最新代采变更记录
        AgentPurchaseChangeApply agentPurchaseChangeApply = agentPurchaseChangeApplyService.getOne(Wrappers.<AgentPurchaseChangeApply>lambdaQuery()
                .eq(AgentPurchaseChangeApply::getRedeemNo, redeemNo)
                .notIn(BaseEntity::getStatus, statusList)
                .orderByDesc(BaseEntity::getCreateTime)
                .last("limit 1"));
        if (Objects.nonNull(agentPurchaseChangeApply)) {
            return R.data(agentPurchaseChangeApplyService.getDetailById(agentPurchaseChangeApply.getId()));
        }
        return R.data(null);
    }

    /**
     * 根据赎货单号 查询所有代采变更记录
     */
    @GetMapping("/getApplyVOList")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "根据赎货单号查询所有代采变更记录", notes = "redeemNo")
    public R<List<AgentPurchaseChangeApplyVO>> getApplyVOList(@RequestParam String redeemNo) {
        return R.data(agentPurchaseChangeApplyService.getApplyVOList(redeemNo));
    }

    /**
     * 查询代采变更详情
     */
    @GetMapping("/getApplyDetail")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "查询代采变更详情", notes = "id")
    public R<AgentPurchaseChangeDetailVO> getApplyDetail(@RequestParam String id) {
        return R.data(agentPurchaseChangeApplyService.getApplyDetail(Func.toLong(id)));
    }

}
