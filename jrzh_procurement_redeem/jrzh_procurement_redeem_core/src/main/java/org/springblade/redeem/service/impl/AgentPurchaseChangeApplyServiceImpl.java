/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.flowable.engine.repository.ProcessDefinition;
import org.springblade.common.enums.PurchaseStateEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.constant.LoanConstant;
import org.springblade.loan.entity.AgentPurchaseChange;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.enums.AgentPurchaseChangeApplyStatusEnum;
import org.springblade.loan.mapper.AgentPurchaseChangeApplyMapper;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.vo.AgentPurchaseChangeApplyVO;
import org.springblade.loan.wrapper.AgentPurchaseChangeApplyWrapper;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.redeem.dto.AgentPurchaseChangeDetailVO;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.entity.RedeemSend;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.service.IAgentPurchaseChangeApplyService;
import org.springblade.redeem.service.IAgentPurchaseChangeService;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.redeem.service.IRedeemSendService;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.process.service.impl.WfProcessService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 代采变更申请记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
@AllArgsConstructor
public class AgentPurchaseChangeApplyServiceImpl extends BaseServiceImpl<AgentPurchaseChangeApplyMapper, AgentPurchaseChangeApply> implements IAgentPurchaseChangeApplyService {

    private final IFinanceApplyService financeApplyService;
    private final AgentPurchaseChangeApplyMapper agentPurchaseChangeApplyMapper;
    private final WfProcessService wfProcessService;
    private final IBusinessProcessService businessProcessService;
    private final IAttachService attachService;
    private final IRedeemCargoService redeemCargoService;
    private final LoanManageIouMapper loanManageIouMapper;
    private final IRedeemSendService redeemSendService;
    private final IAgentPurchaseChangeService agentPurchaseChangeService;

    @Override
    public IPage<AgentPurchaseChangeApplyVO> selectAgentPurchaseChangeApplyPage(IPage<AgentPurchaseChangeApplyVO> page, AgentPurchaseChangeApplyVO agentPurchaseChangeApply) {
        return page.setRecords(baseMapper.selectAgentPurchaseChangeApplyPage(page, agentPurchaseChangeApply));
    }

    @Override
    public Boolean changeApply(AgentPurchaseChangeApply apply) {
        //校验是否可以发起代采变更
        checkApply(apply);
        //获取本次申请记录次数
        Integer applyFrequency = ChangeApplyFrequency(apply);
        apply.setChangeApplyFrequency(applyFrequency);
        //完善代采变更记录信息
        buildChangeApply(apply);
        save(apply);
        //封装审核资料
        Map<String, Object> variables = new HashMap<>();
        variables.put(WfProcessConstant.AGENT_PURCHASE_CHANGE_APPLY_ID, apply.getId());
        variables.put(WfProcessConstant.PROCESS_NO, businessProcessService.getProcessNo(null));
        variables.put(WfProcessConstant.AGENT_PURCHASE_CHANGE_APPLY, apply);
        //发起流程
        ProcessDefinition processDefinition = wfProcessService.selectProcessDefinitionByKey(LoanConstant.AGENT_PURCHASE_CHANGE_APPLY);
        String processInstanceId = wfProcessService.startProcessInstanceById(processDefinition.getId(), variables);

        //保存流程id
        apply.setProcessInstanceId(processInstanceId);
        updateById(apply);
        //更新代采融资单和赎货单状态
        updateFinanceStatus(apply.getFinanceId(), apply.getChangeType());
        RedeemCargo redeemCargo = redeemCargoService.getOne(Wrappers.<RedeemCargo>lambdaQuery().eq(RedeemCargo::getRedeemNo, apply.getRedeemNo()));
        redeemCargoService.changeStatus(Collections.singletonList(redeemCargo.getId()), RedeemCargoStatusEnum.REDEEM_CARGO_DISSENT.getKey());
        return true;
    }

    @Override
    public AgentPurchaseChangeApplyVO getDetailById(Long id) {
        AgentPurchaseChangeApply apply = getById(id);
        if (Objects.isNull(apply)) {
            return null;
        }
        AgentPurchaseChangeApplyVO vo = AgentPurchaseChangeApplyWrapper.build().entityVO(apply);
        //获取凭证照片
        List<Attach> attachListByIds = attachService.getAttachListByIds(Func.toLongList(vo.getAdjunctProof()));
        vo.setAdjunctProofList(attachListByIds);
        //获取检测报告
        WarehouseDetails warehouseDetails = redeemCargoService.getWarehouseByRedeemNo(apply.getRedeemNo());
        if (Objects.nonNull(warehouseDetails)) {
            vo.setReportList(attachService.getAttachListByIds(Func.toLongList(warehouseDetails.getTestReportAttachId())));
        }
        return vo;
    }

    @Override
    public void saveByAgentPurchaseChange(AgentPurchaseChangeApply apply) {
        AgentPurchaseChange change = new AgentPurchaseChange();
        change.setFinanceNo(apply.getFinanceNo());
        change.setFinanceId(apply.getFinanceId());
        change.setRedeemNo(apply.getRedeemNo());
        LoanManageIou iou = loanManageIouMapper.selectLoanByFinanceNo(apply.getFinanceNo());
        change.setIouNo(iou.getIouNo());
        change.setUserId(apply.getUserId());
        User user = UserUtils.getUserById(apply.getUserId());
        if (Objects.nonNull(user)) {
            change.setUserName(user.getName());
        }
        change.setChangeType(apply.getChangeType());
        change.setGoodsUnit(apply.getGoodsUnit());
        change.setGoodsNum(apply.getGoodsNum());
        change.setRefundAmount(apply.getRefundAmount());
        change.setRefundOrderNo(apply.getRefundOrderNo());
        change.setReason(apply.getReason());
        change.setAdjunctProof(apply.getAdjunctProof());
        change.setLinkMan(apply.getLinkMan());
        change.setLinkPhone(apply.getLinkPhone());
        change.setRedemptionAmount(apply.getRedemptionAmount());
        WarehouseDetails warehouseDetails = redeemCargoService.getWarehouseByRedeemNo(apply.getRedeemNo());
        if (Objects.nonNull(warehouseDetails)) {
            change.setTestReportAttachId(warehouseDetails.getTestReportAttachId());
        }
        change.setPurchaseChangeApplyId(apply.getId());
        //获取变更次数
        List<AgentPurchaseChange> oldChangesList = agentPurchaseChangeService.list(Wrappers.<AgentPurchaseChange>lambdaQuery()
                .eq(AgentPurchaseChange::getFinanceNo, apply.getFinanceNo()));
        change.setChangeApplyFrequency(oldChangesList.size() + 1);
        agentPurchaseChangeService.save(change);
    }

    @Override
    public List<AgentPurchaseChangeApplyVO> getApplyVOList(String redeemNo) {
        Assert.isTrue(StringUtil.isNotBlank(redeemNo), "赎货单号不能为空");
        List<AgentPurchaseChangeApply> list = list(Wrappers.<AgentPurchaseChangeApply>lambdaQuery()
                .eq(AgentPurchaseChangeApply::getRedeemNo, redeemNo)
                .orderByDesc(BaseEntity::getUpdateTime));
        List<AgentPurchaseChangeApplyVO> listVO = AgentPurchaseChangeApplyWrapper.build().listVO(list);
        return listVO;
    }

    @Override
    public AgentPurchaseChangeDetailVO getApplyDetail(Long id) {
        AgentPurchaseChangeDetailVO vo = new AgentPurchaseChangeDetailVO();
        if (Objects.isNull(id)) {
            return vo;
        }
        AgentPurchaseChangeApply changeApply = getById(id);
        switch (changeApply.getChangeType()) {
            case 1:
                break;
            case 2:
                break;
            case 3:
                changeGoods(changeApply, vo);
                break;
            default:
                break;
        }
        return vo;
    }

    /**
     * 换货详情
     *
     * @param changeApply
     */
    private void changeGoods(AgentPurchaseChangeApply changeApply, AgentPurchaseChangeDetailVO vo) {
        List<RedeemSend> redeemSends = redeemSendService.getListByChangeApplyId(changeApply.getId());
        vo.setRedeemSendList(redeemSends);
    }


    /**
     * 更新融资单状态
     *
     * @param financeId  融资单id
     * @param changeType 代采变更类型
     */
    public void updateFinanceStatus(Long financeId, Integer changeType) {
        FinanceApply financeApply = financeApplyService.getById(financeId);
        Integer financeStatus = financeApply.getStatus();
        switch (changeType) {
            case 1:
                financeStatus = PurchaseStateEnum.PURCHASE_RETURN_GOODS_REFUND.getCode();
                break;
            case 2:
                financeStatus = PurchaseStateEnum.PURCHASE_REFUND.getCode();
                break;
            case 3:
                financeStatus = PurchaseStateEnum.PURCHASE_EXCHANGE_GOODS.getCode();
                break;
            default:
                break;
        }
        financeApply.setStatus(financeStatus);
        financeApplyService.updateById(financeApply);
    }


    /**
     * 校验是否可以发起代采变更
     *
     * @param apply
     */
    private void checkApply(AgentPurchaseChangeApply apply) {
        Assert.isTrue(StringUtil.isNotBlank(apply.getFinanceNo()), "融资编号不能为空");
        Assert.isTrue(StringUtil.isNotBlank(apply.getRedeemNo()), "赎货单号不能为空");

        RedeemCargo redeemCargo = redeemCargoService.getOne(Wrappers.<RedeemCargo>lambdaQuery().eq(RedeemCargo::getRedeemNo, apply.getRedeemNo()));
        Assert.isTrue(Objects.nonNull(redeemCargo), "未查询到赎货信息");

        //赎货单状态需要是待验收
        Assert.isTrue(redeemCargo.getStatus().equals(RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey()), "当前赎货状态无法发起异议流程");

        //一个赎货订单不能有两个异议处理中
        List<Integer> status = Arrays.asList(AgentPurchaseChangeApplyStatusEnum.CHANGE_COMPLETED.getCode()
                , AgentPurchaseChangeApplyStatusEnum.REJECTED.getCode());
        List<AgentPurchaseChangeApply> statusList = agentPurchaseChangeApplyMapper.selectList(Wrappers.<AgentPurchaseChangeApply>lambdaQuery()
                .eq(AgentPurchaseChangeApply::getRedeemNo, apply.getRedeemNo()).notIn(BaseEntity::getStatus, status));
        if (CollUtil.isNotEmpty(statusList)) {
            throw new ServiceException("已经有正在处理的异议");
        }
    }

    /**
     * 获取本次申请记录次数
     *
     * @param apply
     * @return
     */
    private Integer ChangeApplyFrequency(AgentPurchaseChangeApply apply) {
        List<AgentPurchaseChangeApply> applyList = agentPurchaseChangeApplyMapper.selectList(Wrappers.<AgentPurchaseChangeApply>lambdaQuery()
                .eq(AgentPurchaseChangeApply::getFinanceNo, apply.getFinanceNo()));
        if (CollUtil.isNotEmpty(applyList)) {
            return applyList.size() + 1;
        } else {
            return 1;
        }
    }

    /**
     * 完善代采变更记录信息
     *
     * @param agentPurchaseChangeApply
     * @return
     */
    private AgentPurchaseChangeApply buildChangeApply(AgentPurchaseChangeApply agentPurchaseChangeApply) {
        FinanceApply finance = financeApplyService.getByFinanceNo(agentPurchaseChangeApply.getFinanceNo());
        Assert.isTrue(Objects.nonNull(finance), "没有查询到融资信息");
        RedeemCargo redeemCargo = redeemCargoService.getOne(Wrappers.<RedeemCargo>lambdaQuery()
                .eq(RedeemCargo::getRedeemNo, agentPurchaseChangeApply.getRedeemNo())
                .orderByDesc(BaseEntity::getCreateTime).last("limit 1"));
        agentPurchaseChangeApply.setRedemptionAmount(redeemCargo.getNum());
        agentPurchaseChangeApply.setFinanceId(finance.getId());
        agentPurchaseChangeApply.setUserId(MyAuthUtil.getUserId());
        agentPurchaseChangeApply.setStatus(AgentPurchaseChangeApplyStatusEnum.APPLYING.getCode());
        return agentPurchaseChangeApply;
    }

}
