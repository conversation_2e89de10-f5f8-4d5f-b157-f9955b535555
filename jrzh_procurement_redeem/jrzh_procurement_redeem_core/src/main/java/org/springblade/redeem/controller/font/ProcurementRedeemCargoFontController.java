/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.controller.font;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.redeem.dto.RedeemCargoConfirmDTO;
import org.springblade.redeem.service.IProcurementRedeemCargoService;
import org.springblade.redeem.vo.RedeemDetailFontCargoVO;
import org.springblade.warehouse.vo.WarehouseDetailsChildVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 赎货表 控制器
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_REDEEM + CommonConstant.WEB_FRONT + "/cargo")
@Api(value = "赎货表", tags = "赎货表接口")
public class ProcurementRedeemCargoFontController extends BladeController {

    private final IProcurementRedeemCargoService procurementRedeemCargoService;
    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入redeemCargo")
    public R<RedeemDetailFontCargoVO> detail(String redeemNo) {
        return R.data(procurementRedeemCargoService.fontDetail(redeemNo));
    }
    /**
     * 发起申请页面信息
     */
    @GetMapping("/applyInfo")
    public R<WarehouseDetailsChildVO> applyInfo(Long warehouseId) {
        return R.data(procurementRedeemCargoService.fontWarehousDetail(warehouseId));
    }
    /**
     * 发起赎货申请确认
     */
    @PostMapping("/redeemConfirm")
    @ApiOperationSupport(order = 4)
    public R<Boolean> redeemConfirm(@Valid @RequestBody RedeemCargoConfirmDTO redeemCargoConfirmDTO) {
        return R.status(procurementRedeemCargoService.redeemConfirm(redeemCargoConfirmDTO));
    }
//
//    /**
//     * 有异议/确认收货
//     */
//    @PostMapping("/changeStatus")
//    public R<Boolean> changeStatus(@Valid @RequestBody ChangStatusDTO changStatusDTO) {
//        return R.status(redeemCargoService.changeStatus(changStatusDTO));
//    }

}
