/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.loan.vo.AgentPurchaseChangeApplyVO;
import org.springblade.redeem.dto.AgentPurchaseChangeDetailVO;

import java.util.List;

/**
 * 代采变更申请记录表 服务类
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface IAgentPurchaseChangeApplyService extends BaseService<AgentPurchaseChangeApply> {

    /**
     * 自定义分页
     *
     * @param page
     * @param agentPurchaseChangeApply
     * @return
     */
    IPage<AgentPurchaseChangeApplyVO> selectAgentPurchaseChangeApplyPage(IPage<AgentPurchaseChangeApplyVO> page, AgentPurchaseChangeApplyVO agentPurchaseChangeApply);

    /**
     * 发起代采变更申请
     *
     * @param agentPurchaseChangeApply
     */
    Boolean changeApply(AgentPurchaseChangeApply agentPurchaseChangeApply);

    /**
     * 根据id查询代采变更记录
     *
     * @param id 代采变更记录id
     * @return
     */
    AgentPurchaseChangeApplyVO getDetailById(Long id);

    /**
     * 保存变更记录
     *
     * @param apply 变更记录申请
     */
    void saveByAgentPurchaseChange(AgentPurchaseChangeApply apply);


    /**
     * 根据赎货单号 查询所有代采变更记录
     *
     * @param redeemNo
     * @return
     */
    List<AgentPurchaseChangeApplyVO> getApplyVOList(String redeemNo);

    /**
     * 查询代采变更记录详情
     *
     * @param id 代采变更记录id
     * @return
     */
    AgentPurchaseChangeDetailVO getApplyDetail(Long id);
}
