/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.entity.AgentPurchaseChange;
import org.springblade.loan.entity.AgentPurchaseChangeApply;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.enums.AgentPurchaseChangeTypeEnum;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.vo.AgentPurchaseChangeVO;
import org.springblade.loan.wrapper.AgentPurchaseChangeWrapper;
import org.springblade.redeem.mapper.AgentPurchaseChangeMapper;
import org.springblade.redeem.service.IAgentPurchaseChangeService;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代采变更表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
@AllArgsConstructor
public class AgentPurchaseChangeServiceImpl extends BaseServiceImpl<AgentPurchaseChangeMapper, AgentPurchaseChange> implements IAgentPurchaseChangeService {

    private final LoanManageIouMapper loanManageIouMapper;
    private final IRedeemCargoService redeemCargoService;
    private final AgentPurchaseChangeMapper agentPurchaseChangeMapper;
    private final IWarehouseDetailsService warehouseDetailsService;
    private final IAttachService attachService;
    private final IFinanceApplyService financeApplyService;
    private final FinanceApplyMapper financeApplyMapper;


    @Override
    public IPage<AgentPurchaseChangeVO> selectAgentPurchaseChangePage(IPage<AgentPurchaseChangeVO> page, AgentPurchaseChangeVO agentPurchaseChange) {
        return page.setRecords(baseMapper.selectAgentPurchaseChangePage(page, agentPurchaseChange));
    }

    @Override
    public void saveByAgentPurchaseChangeApply(AgentPurchaseChangeApply apply) {
        AgentPurchaseChange change = new AgentPurchaseChange();
        change.setFinanceNo(apply.getFinanceNo());
        change.setFinanceId(apply.getFinanceId());
        change.setRedeemNo(apply.getRedeemNo());
        LoanManageIou iou = loanManageIouMapper.selectLoanByFinanceNo(apply.getFinanceNo());
        change.setIouNo(iou.getIouNo());
        change.setUserId(apply.getUserId());
        User user = UserUtils.getUserById(apply.getUserId());
        if (Objects.nonNull(user)) {
            change.setUserName(user.getName());
        }
        change.setChangeType(apply.getChangeType());
        change.setGoodsUnit(apply.getGoodsUnit());
        change.setGoodsNum(apply.getGoodsNum());
        change.setRefundOrderNo(apply.getRefundOrderNo());
        change.setReason(apply.getReason());
        change.setAdjunctProof(apply.getAdjunctProof());
        WarehouseDetails warehouseDetails = redeemCargoService.getWarehouseByRedeemNo(apply.getRedeemNo());
        if (Objects.nonNull(warehouseDetails)) {
            change.setTestReportAttachId(warehouseDetails.getTestReportAttachId());
        }
        change.setPurchaseChangeApplyId(apply.getId());
        //获取变更次数
        List<AgentPurchaseChange> oldChangesList = agentPurchaseChangeMapper.selectList(Wrappers.<AgentPurchaseChange>lambdaQuery()
                .eq(AgentPurchaseChange::getFinanceNo, apply.getFinanceNo()));
        change.setChangeApplyFrequency(oldChangesList.size() + 1);
        save(change);
    }

    @Override
    public Map<String, AgentPurchaseChangeVO> getChangeGoodsNum(List<String> redeemNoList) {
        Map<String, AgentPurchaseChangeVO> changeGoodsMap = new HashMap<>();
        redeemNoList.forEach(redeemNo -> {
            AgentPurchaseChangeVO vo = new AgentPurchaseChangeVO();
            List<AgentPurchaseChange> agentPurchaseChanges = getListByRedeemNo(redeemNo);
            //换货数量
            Integer changeNum = agentPurchaseChanges.stream().filter(e -> e.getChangeType()
                            .equals(AgentPurchaseChangeTypeEnum.PURCHASE_EXCHANGE_GOODS.getCode()))
                    .map(AgentPurchaseChange::getGoodsNum)
                    .reduce(Integer::sum).orElse(0);
            //退货数量
            Integer backNum = agentPurchaseChanges.stream().filter(e -> e.getChangeType()
                            .equals(AgentPurchaseChangeTypeEnum.PURCHASE_RETURN_GOODS_REFUND.getCode()))
                    .map(AgentPurchaseChange::getGoodsNum)
                    .reduce(Integer::sum).orElse(0);
            vo.setChangeGoodsNum(changeNum);
            vo.setBackGoodsNum(backNum);
            changeGoodsMap.put(redeemNo, vo);

        });
        return changeGoodsMap;
    }


    /**
     * 根据赎货单号查询变更集合
     *
     * @param redeemNo
     * @return
     */
    @Override
    public List<AgentPurchaseChange> getListByRedeemNo(String redeemNo) {
        return agentPurchaseChangeMapper.selectList(Wrappers.<AgentPurchaseChange>lambdaQuery()
                .eq(AgentPurchaseChange::getRedeemNo, redeemNo));
    }

    @Override
    public IPage<AgentPurchaseChangeVO> agentPurchaseChangePage(Query query, AgentPurchaseChange agentPurchaseChange) {
        IPage<AgentPurchaseChange> pages = baseMapper.selectPage(Condition.getPage(query), getWrapper(agentPurchaseChange));
        if (pages.getTotal() <= 0) {
            return Condition.getPage(query);
        }
        IPage<AgentPurchaseChangeVO> pagesVO = AgentPurchaseChangeWrapper.build().pageVO(pages);
        if(CollUtil.isNotEmpty(pagesVO.getRecords())){
            pagesVO.getRecords().forEach(vo -> {
                String financeNo = vo.getFinanceNo();
                List<AgentPurchaseChange> agentPurchaseChanges = list(Wrappers.<AgentPurchaseChange>lambdaQuery().eq(AgentPurchaseChange::getFinanceNo, financeNo));
                if (CollUtil.isNotEmpty(agentPurchaseChanges)) {
                    //换货数量
                    Integer changeGoodsNum = agentPurchaseChanges.stream()
                            .filter(e -> e.getChangeType().equals(AgentPurchaseChangeTypeEnum.PURCHASE_EXCHANGE_GOODS.getCode()))
                            .map(AgentPurchaseChange::getGoodsNum)
                            .reduce(Integer::sum).orElse(0);
                    //退货数量
                    Integer backGoodsNum = agentPurchaseChanges.stream()
                            .filter(e -> e.getChangeType().equals(AgentPurchaseChangeTypeEnum.PURCHASE_RETURN_GOODS_REFUND.getCode()))
                            .map(AgentPurchaseChange::getGoodsNum)
                            .reduce(Integer::sum).orElse(0);
                    //变更金额
                    BigDecimal backMoney = agentPurchaseChanges.stream()
                            .filter(e -> !e.getChangeType().equals(AgentPurchaseChangeTypeEnum.PURCHASE_EXCHANGE_GOODS.getCode()))
                            .map(AgentPurchaseChange::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //商品信息
                    WarehouseDetails warehouseDetail = warehouseDetailsService.getOne(Wrappers.<WarehouseDetails>lambdaQuery().eq(WarehouseDetails::getFinanceNo, financeNo).last("limit 1"));
                    //变更总次数
                    Integer totalChange = agentPurchaseChanges.size();
                    vo.setChangeGoodsNum(changeGoodsNum);
                    vo.setBackGoodsNum(backGoodsNum);
                    vo.setBackMoney(backMoney);
                    vo.setGoodsInfo(warehouseDetail.getGoodsInfo());
                    vo.setTotalChange(totalChange);
                    //赎货总数
                    Map<String, List<AgentPurchaseChange>> redeemNoMap = agentPurchaseChanges.stream().collect(Collectors.groupingBy(AgentPurchaseChange::getRedeemNo));
                    Integer totalRedemptionAmount = 0;
                    for (String redeemNo : redeemNoMap.keySet()) {
                        totalRedemptionAmount = redeemNoMap.get(redeemNo).get(0).getRedemptionAmount() + totalRedemptionAmount;
                    }
                    vo.setTotalRedemptionAmount(totalRedemptionAmount);
                    //融资金额
                    vo.setFinanceAmount(financeApplyMapper.getByFinanceNoApply(vo.getFinanceNo()).getAmount());
                }
            });
        }

        return pagesVO;
    }

    @Override
    public Map<Integer, AgentPurchaseChangeVO> getAgentPurchaseChangeHistoryByFinanceId(Long id) {
        //查询变更记录
        List<AgentPurchaseChange> changes = list(Wrappers.<AgentPurchaseChange>lambdaQuery().eq(AgentPurchaseChange::getFinanceId, id));
        if (CollUtil.isEmpty(changes)) {
            return Collections.EMPTY_MAP;
        }
        List<AgentPurchaseChangeVO> changeVOS = AgentPurchaseChangeWrapper.build().listVO(changes);
        changeVOS.forEach(vo -> {
            //变更附件
            if (StringUtil.isNotBlank(vo.getAdjunctProof())) {
                List<Attach> attachList = attachService.getAttachListByIds(Func.toLongList(vo.getAdjunctProof()));
                vo.setAttachList(attachList);
            }
        });
        Map<Integer, AgentPurchaseChangeVO> mapVo = changeVOS.stream().collect(Collectors.toMap(AgentPurchaseChange::getChangeApplyFrequency, e -> e));
        return mapVo;
    }

    /**
     * 分页搜索条件
     *
     * @param agentPurchaseChange
     * @return
     */
    private LambdaQueryWrapper<AgentPurchaseChange> getWrapper(AgentPurchaseChange agentPurchaseChange) {
        LambdaQueryWrapper<AgentPurchaseChange> lqw = Wrappers.lambdaQuery();
        //融资单号搜索
        lqw.eq(StringUtil.isNotBlank(agentPurchaseChange.getFinanceNo()), AgentPurchaseChange::getFinanceNo, agentPurchaseChange.getFinanceNo());
        //借据号搜索
        lqw.eq(StringUtil.isNotBlank(agentPurchaseChange.getIouNo()), AgentPurchaseChange::getIouNo, agentPurchaseChange.getIouNo());
        //用户名称
        lqw.like(StringUtil.isNotBlank(agentPurchaseChange.getUserName()), AgentPurchaseChange::getUserName, agentPurchaseChange.getUserName());
        //联系人
        lqw.like(StringUtil.isNotBlank(agentPurchaseChange.getLinkMan()), AgentPurchaseChange::getLinkMan, agentPurchaseChange.getLinkMan());
        //电话
        lqw.like(StringUtil.isNotBlank(agentPurchaseChange.getLinkPhone()), AgentPurchaseChange::getLinkPhone, agentPurchaseChange.getLinkPhone());
        //仅查询第一条记录
        lqw.eq(AgentPurchaseChange::getChangeApplyFrequency, 1);
        lqw.orderByDesc(BaseEntity::getCreateTime);
        return lqw;
    }

}
