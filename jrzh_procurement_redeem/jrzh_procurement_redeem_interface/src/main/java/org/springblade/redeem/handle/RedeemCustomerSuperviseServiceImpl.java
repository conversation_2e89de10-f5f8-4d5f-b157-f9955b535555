package org.springblade.redeem.handle;

import org.springblade.customer.entity.CustomerSupervise;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 赎货-- 监管方
 */
@Service
public class RedeemCustomerSuperviseServiceImpl implements RedeemCustomerSuperviseService{

    @Override
    public Map<Long, String> listByIds(List<Long> storageIds) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public CustomerSupervise getById(Long id) {
        throw new UnsupportedOperationException("TODO");
    }
}
