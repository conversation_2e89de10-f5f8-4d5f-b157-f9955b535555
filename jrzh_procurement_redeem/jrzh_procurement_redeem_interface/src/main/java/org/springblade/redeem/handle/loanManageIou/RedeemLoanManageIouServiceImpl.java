package org.springblade.redeem.handle.loanManageIou;

import lombok.RequiredArgsConstructor;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.service.ILoanManageIouService;
import org.springframework.stereotype.Service;

/**
 * 赎货--- 还款借据单接口
 */
@Service
@RequiredArgsConstructor
public class RedeemLoanManageIouServiceImpl implements RedeemLoanManageIouService{
    private final ILoanManageIouService loanManageIouService;

    @Override
    public LoanManageIou getByFinancingNo(String financeNo) {
        return loanManageIouService.lambdaQuery().eq(LoanManageIou::getFinanceNo, financeNo).one();
    }
}
