package org.springblade.redeem.handle.loan;


import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.RepaymentPlanFee;

import java.util.List;

/**
 * 赎货--还款列表接口
 */
public interface RedeemLoanManageRepaymentService {

    /**
     * 根据还款列表id查询还款数据
     * @param id 还款列表id
     * @return 还款列表数据
     */
    LoanManageRepayment getById(Long id);

    /**
     * 更新还款列表
     * @param manageRepayment 更新数据
     * @return true 成功
     */
    Boolean updateById(LoanManageRepayment manageRepayment);

    /**
     * 根据还款列表状态和借据号查询 还款列表数据集合
     * @param status 状态
     * @param iouNo 借据号
     * @return 还款列表集合
     */
    List<LoanManageRepayment> listByStatusByIouNo(Integer status,String iouNo);

    /**
     * 新增还款列表
     * @param loanManageRepayment 还款列表数据
     * @return true成功
     */
    Boolean save(LoanManageRepayment loanManageRepayment);

    /**
     * 保存还款记录关联费用表
     *
     * @param id 还款列表id
     * @param feePlanList 待还款计划关联费用
     */
    Boolean saveRepaymentFee(Long id,List<RepaymentPlanFee> feePlanList);

}
