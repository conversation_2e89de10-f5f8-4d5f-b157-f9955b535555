package org.springblade.redeem.handle.finance;

import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Author: z<PERSON><PERSON>chuang<PERSON>
 * @CreateTime: 2023-11-20  11:50
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class RedeemFinanceRepaymentServiceImpl implements RedeemFinanceRepaymentService {
    @Override
    public Map<String, Object> repaymentCalculationFinanceNo(String financeNo) {
        throw new UnsupportedOperationException("TODO");
    }
}
