package org.springblade.redeem.handle.purchase;


import org.springblade.procurement.finance.entity.PurchaseInformation;

/**
 * 赎货---代采---基础信息
 */
public interface RedeemPurchaseInformationService {

    /**
     * 根据融资编号获取代采--基础信息
     *
     * @param financeNo 融资编号
     * @return 代采--基础信息
     */
    PurchaseInformation getByFinanceNo(String financeNo);

    /**
     * 根据id获取代采--基础信息
     *
     * @param id 代采--基础信息 id
     * @return 代采--基础信息
     */
    PurchaseInformation getById(Long id);
}
