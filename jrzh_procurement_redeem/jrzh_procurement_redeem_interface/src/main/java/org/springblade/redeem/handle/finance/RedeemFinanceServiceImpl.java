package org.springblade.redeem.handle.finance;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springframework.stereotype.Service;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-20  11:50
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class RedeemFinanceServiceImpl implements RedeemFinanceService{
    private final FinanceApplyMapper financeApplyMapper;
    @Override
    public FinanceApply getByFinanceNo(String financeNo) {
        return financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeNo));
    }

    @Override
    public boolean update(Wrapper<FinanceApply> queryWrapper) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public boolean updateById(FinanceApply financeApply) {
        throw new UnsupportedOperationException("TODO");
    }
}
