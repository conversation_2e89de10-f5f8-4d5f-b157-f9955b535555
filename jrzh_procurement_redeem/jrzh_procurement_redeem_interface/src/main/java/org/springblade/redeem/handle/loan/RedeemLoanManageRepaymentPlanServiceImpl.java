package org.springblade.redeem.handle.loan;

import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 赎货--还款计划接口
 */
@Service
public class RedeemLoanManageRepaymentPlanServiceImpl implements RedeemLoanManageRepaymentPlanService{

    @Override
    public List<LoanManageRepaymentPlan> loanManageRepaymentPlanList(Long financeApplyId) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public List<RepaymentPlanFee> calculateShouldAmount(LoanManageRepaymentPlan loanManageRepaymentPlan, BigDecimal amount) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public List<LoanManageRepaymentPlan> getListByIou(String iouNo) {
        throw new UnsupportedOperationException("TODO");
    }
}
