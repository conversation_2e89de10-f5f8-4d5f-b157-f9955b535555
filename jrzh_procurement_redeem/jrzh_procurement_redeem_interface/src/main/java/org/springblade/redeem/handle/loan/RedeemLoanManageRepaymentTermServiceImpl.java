package org.springblade.redeem.handle.loan;

import org.springblade.loan.entity.LoanManageRepayment;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 赎货--还款记录和还款计划中间表接口
 */

@Service
public class RedeemLoanManageRepaymentTermServiceImpl implements RedeemLoanManageRepaymentTermService{

    @Override
    public List<LoanManageRepayment> getRepaymentByPlanId(Long id) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public void saveRepaymentTermList(Long repaymentId, String iouNo, List<Long> repaymentPlanIdList) {
        throw new UnsupportedOperationException("TODO");
    }
}
