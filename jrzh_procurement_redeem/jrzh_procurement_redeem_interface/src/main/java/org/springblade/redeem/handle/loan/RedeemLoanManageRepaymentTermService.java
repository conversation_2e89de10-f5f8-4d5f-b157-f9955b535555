package org.springblade.redeem.handle.loan;

import org.springblade.loan.entity.LoanManageRepayment;

import java.util.List;

/**
 * 赎货--还款记录和还款计划中间表接口
 */
public interface RedeemLoanManageRepaymentTermService {

    /**
     * 根据还款计划id查询 还款成功记录
     * @param id 还款计划id
     * @return
     */
    List<LoanManageRepayment> getRepaymentByPlanId(Long id);

    /**
     * 保存还款记录和还款计划的关系
     * @param repaymentId 还款id
     * @param iouNo
     * @param repaymentPlanIdList 还款计划id
     */
    void saveRepaymentTermList(Long repaymentId,String iouNo, List<Long> repaymentPlanIdList);
}
