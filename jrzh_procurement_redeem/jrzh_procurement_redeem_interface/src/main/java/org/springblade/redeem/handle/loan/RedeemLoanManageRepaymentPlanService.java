package org.springblade.redeem.handle.loan;

import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.entity.RepaymentPlanFee;

import java.math.BigDecimal;
import java.util.List;

/**
 * 赎货--还款计划接口
 */
public interface RedeemLoanManageRepaymentPlanService {

    /**
     * 根据融资申请id获取还款计划列表数据
     * @param financeApplyId 融资申请id
     * @return 还款计划列表数据
     */
    List<LoanManageRepaymentPlan> loanManageRepaymentPlanList(Long financeApplyId);

    /**
     * 随借随还计算本次还款动态费用
     * @param loanManageRepaymentPlan  还款计划
     * @param amount				本次还款金额
     * @return
     */
    List<RepaymentPlanFee> calculateShouldAmount(LoanManageRepaymentPlan loanManageRepaymentPlan, BigDecimal amount);

    /**
     * 根据借据单号查询还款计划
     *
     * @param iouNo 借据单号
     * @return 借据单号
     */
    List<LoanManageRepaymentPlan> getListByIou(String iouNo);
}
