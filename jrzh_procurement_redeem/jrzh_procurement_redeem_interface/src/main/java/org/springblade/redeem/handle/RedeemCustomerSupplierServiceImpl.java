package org.springblade.redeem.handle;

import org.springblade.customer.entity.CustomerSupplier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 赎货--供应商
 */
@Service
public class RedeemCustomerSupplierServiceImpl implements RedeemCustomerSupplierService{

    @Override
    public List<CustomerSupplier> listByIds(List<Long> supplierIds) {
        throw new UnsupportedOperationException("TODO");
    }
}
