package org.springblade.redeem.handle.loan;


import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 赎货--还款列表接口
 */
@Service
public class RedeemLoanManageRepaymentServiceImpl implements RedeemLoanManageRepaymentService{


    @Override
    public LoanManageRepayment getById(Long id) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public Boolean updateById(LoanManageRepayment manageRepayment) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public List<LoanManageRepayment> listByStatusByIouNo(Integer status, String iouNo) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public Boolean save(LoanManageRepayment loanManageRepayment) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public Boolean saveRepaymentFee(Long id, List<RepaymentPlanFee> feePlanList) {
        throw new UnsupportedOperationException("TODO");
    }
}
