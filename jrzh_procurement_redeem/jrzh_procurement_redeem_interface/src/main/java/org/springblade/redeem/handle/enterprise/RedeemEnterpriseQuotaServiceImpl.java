package org.springblade.redeem.handle.enterprise;

import org.springblade.customer.entity.EnterpriseQuota;
import org.springframework.stereotype.Service;

/**
 * 赎货---核心企业额度
 */
@Service
public class RedeemEnterpriseQuotaServiceImpl implements RedeemEnterpriseQuotaService{


    @Override
    public EnterpriseQuota selectGoodsIdByUserList(Long goodsId, Long enterpriseId) {
        throw new UnsupportedOperationException("TODO");
    }
}
