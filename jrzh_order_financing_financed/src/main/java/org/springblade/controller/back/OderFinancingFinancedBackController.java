package org.springblade.controller.back;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.bank.entity.Bank;
import org.springblade.bank.service.IBankService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.CustomerSupplier;
import org.springblade.customer.service.ICustomerSupplierService;
import org.springblade.finance.dto.OrderData;
import org.springblade.finance.vo.BackApplyInfo;
import org.springblade.jrzh_order_financing_goods.service.IOrderFinancingGoodsService;
import org.springblade.jrzh_order_financing_goods_api.entity.OrderFinancingGoods;
import org.springblade.product.moudle.billbankcard.service.IBillBankCardaService;
import org.springblade.service.IOrderDataService;
import org.springblade.service.OrderFinanceInfoService;
import org.springblade.vo.AccountInformationVo;
import org.springblade.vo.OrderFinancingBackApplyInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_BUSINESS + CommonConstant.WEB_BACK + "/orderFinancing/financing")
@Api(value = "融资申请", tags = "融资申请接口")
public class OderFinancingFinancedBackController {
    private final ICustomerSupplierService customerSupplierService;
    private final OrderFinanceInfoService orderFinanceInfoService;
    private final IOrderDataService orderDataService;
    private final IOrderFinancingGoodsService orderFinancingGoodsService;
    private final IBankService bankService;
    /**
     * 获取受托支付账户
     */
    @GetMapping("/getEntrustedPaymentAccount")
    @ApiOperation("获取平台受托支付监管账户")
    public R getEntrustedPaymentAccount(@RequestParam Long financeApplyId,@RequestParam Long goodsId) {
        OrderFinancingGoods orderFinancingGoods = orderFinancingGoodsService.getById(goodsId);
        if (GoodsEnum.ENTRUSTED_PAYMENT.getCode().equals(orderFinancingGoods.getBankCardCollectionType())){
            OrderData orderData = orderDataService.getOne(Wrappers.<OrderData>lambdaQuery().eq(OrderData::getFinanceApplyId, financeApplyId).last("limit 1"));
            CustomerSupplier customerSupplier = customerSupplierService.getByName(orderData.getSupplierNo());
            String depositBankId = customerSupplier.getDepositBankId();
            Bank bank = bankService.getById(depositBankId);
            String name = bank.getName();
            AccountInformationVo accountInformationVo = new AccountInformationVo();
            accountInformationVo.setBankCardNo(customerSupplier.getBankAccount());
            accountInformationVo.setBankDeposit(name);
            accountInformationVo.setEnterpriseName(customerSupplier.getSupperName());
            return R.data(accountInformationVo);
        }
        return null;

    }

    @GetMapping("/applyInfo")
    @ApiOperation("申请信息")
    public R<OrderFinancingBackApplyInfo> applyInfo(@RequestParam Long id) {
        return R.data(orderFinanceInfoService.selectBackFinanceApplyInfo(id));
    }
}
