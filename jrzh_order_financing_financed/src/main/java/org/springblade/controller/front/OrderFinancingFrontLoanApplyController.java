package org.springblade.controller.front;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.finance.dto.LoanApplyDTO;
import org.springblade.service.OrderFinancingLoanApplyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_BUSINESS + CommonConstant.WEB_FRONT + "/orderFinancing_loan-apply")
@Api(value = "融资申请", tags = "放款申请接口")
public class OrderFinancingFrontLoanApplyController {
    private final OrderFinancingLoanApplyService orderFinancingLoanApplyService;

    @PostMapping("/submit")
    @ApiOperation("提交手动放款申请流程")
    public R<Boolean> submit(@Valid @RequestBody LoanApplyDTO loanApplyDTO) {
        loanApplyDTO.setUserId(AuthUtil.getUserId());
        return R.status(orderFinancingLoanApplyService.submit(loanApplyDTO));
    }
}
