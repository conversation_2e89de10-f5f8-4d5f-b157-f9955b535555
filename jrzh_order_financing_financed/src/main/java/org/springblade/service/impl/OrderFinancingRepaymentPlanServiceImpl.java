package org.springblade.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.product.expense.constant.ExpenseConstant;;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.finance.entity.FinanceApply;

import org.springblade.jrzh_order_financing_goods.service.IOrderFinancingGoodsService;
import org.springblade.jrzh_order_financing_goods_api.entity.OrderFinancingGoods;
import org.springblade.loan.dto.RepaymentCalculationDTO;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.mapper.LoanManageRepaymentPlanMapper;
import org.springblade.loan.service.IRepaymentPlanFeeService;
import org.springblade.loan.utils.LoanUtils;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.service.OrderFinancingRepaymentPlanService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/10
 * @description
 */
@Service
@AllArgsConstructor
@Slf4j
public class OrderFinancingRepaymentPlanServiceImpl extends BaseServiceImpl<LoanManageRepaymentPlanMapper, LoanManageRepaymentPlan> implements OrderFinancingRepaymentPlanService {
    private final IOrderFinancingGoodsService orderFinancingGoodsService;
    private final IExpenseOrderDetailService expenseOrderDetailService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;



    @Override
    public List<LoanManageRepaymentPlan> saveRepaymentPlan(LoanManageIou loanManageIou, FinanceApply financeApply) {
        RepaymentCalculationDTO repaymentCalculationDTO = RepaymentCalculationDTO.builder()
                .annualInterestRate(financeApply.getAnnualInterestRate())
                .financeAmount(financeApply.getAmount())
                .loadTermUnit(financeApply.getLoadTermUnit())
                .refundType(financeApply.getRepaymentMode())
                .startTime(LocalDate.now())
                .totalTerm(financeApply.getLoadTerm())
                .build();
        IBusinessProcessService businessProcessService = SpringUtil.getBean(IBusinessProcessService.class);
        OrderFinancingGoods goods = orderFinancingGoodsService.getById(financeApply.getGoodsId());
        RepaymentPlanCal repaymentPlanCal = repaymentCalculation(repaymentCalculationDTO);
        List<StagRecordVO> stagRecords = repaymentPlanCal.getStagRecords();


        List<LoanManageRepaymentPlan> repaymentPlanList = stagRecords.stream()
                .filter(stagRecordVO -> Objects.nonNull(stagRecordVO.getRefundTime()))
                .filter(stagRecordVO -> StringUtil.isNotBlank(stagRecordVO.getTerm())).map(stagRecord ->
                        LoanManageRepaymentPlan.builder()
                                .iouNo(loanManageIou.getIouNo())
                                .repaymentTime(stagRecord.getRefundTime())
                                .period(Integer.valueOf(stagRecord.getTerm()))
                                .userId(financeApply.getUserId())
                                .principal(stagRecord.getMonthlyPrincipal())
                                .financeApplyId(financeApply.getId())
                                .interest(stagRecord.getMonthlyInterest())
                                .customerGoodsId(financeApply.getCustomerGoodsId())
                                .prepaymentType(goods.getPrepaymentType())
                                .repaymentType(financeApply.getRepaymentType())
                                .goodsId(financeApply.getGoodsId())
                                .iouId(loanManageIou.getId())
                                .goodsType(financeApply.getGoodsType())
                                .annualInterestRate(financeApply.getAnnualInterestRate())
                                .penaltyInterest(BigDecimal.ZERO)
                                .serviceFee(BigDecimal.ZERO)
                                .build()).collect(Collectors.toList());
        saveBatch(repaymentPlanList);
        return repaymentPlanList;
    }
    private void savePlanFeeByPlatformExpenses(List<LoanManageRepaymentPlan> repaymentPlanList,FinanceApply financeApply){
        //保存还款计划费用表
        OrderFinancingGoods goods = orderFinancingGoodsService.getById(financeApply.getGoodsId());
        if (goods.getChargeMethod().equals(GoodsEnum.UNIFIED.getCode())) {
            //查询平台费用
            List<ExpenseOrderDetail> platformExpensesList = expenseOrderDetailService.list(Wrappers.<ExpenseOrderDetail>lambdaQuery()
                    .eq(ExpenseOrderDetail::getFinanceNo, financeApply.getFinanceNo())
                    .eq(ExpenseOrderDetail::getFeeNode, ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode()));
            //封装还款计划费用表
            if (CollUtil.isNotEmpty(platformExpensesList)) {
                platformExpensesList.forEach(platformExpenses -> {
                    repaymentPlanFeeService.savePlanFeeByPlatformExpenses(platformExpenses, repaymentPlanList);
                });

            }
        }
    }

    private RepaymentPlanCal repaymentCalculation(RepaymentCalculationDTO repaymentCalculationDTO) {
        BigDecimal financeAmount = repaymentCalculationDTO.getFinanceAmount();
        Assert.isTrue(Objects.isNull(financeAmount) || financeAmount.compareTo(BigDecimal.ZERO) > 0, "融资金额不能为零");
        Integer loadTermUnit = repaymentCalculationDTO.getLoadTermUnit();
        BigDecimal annualInterestRate = repaymentCalculationDTO.getAnnualInterestRate();
        LocalDate startTime = repaymentCalculationDTO.getStartTime();
        Integer totalTerm = repaymentCalculationDTO.getTotalTerm();
        if (loadTermUnit.equals(GoodsEnum.TERM.getCode())) {
            return LoanUtils.calculateByRefundType(financeAmount, annualInterestRate, totalTerm, startTime,
                    repaymentCalculationDTO.getRefundType());
        }
        return LoanUtils.calculatePrincipalAndInvest(financeAmount, annualInterestRate, startTime, startTime.plusDays(totalTerm), CommonConstant.NUMBER_STRATEGY,0);
    }

    @Override
    public void saveRepaymentPlanByFee(LoanManageIou loanManageIou, FinanceApply financeApply) {
        List<LoanManageRepaymentPlan> repaymentPlanList= this.saveRepaymentPlan(loanManageIou, financeApply);
        this.savePlanFeeByPlatformExpenses(repaymentPlanList,financeApply);
    }
}
