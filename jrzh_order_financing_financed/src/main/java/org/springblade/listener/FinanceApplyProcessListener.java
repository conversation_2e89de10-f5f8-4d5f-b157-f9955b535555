package org.springblade.listener;

import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.core.tool.utils.StringUtil;

import org.springblade.handler.OrderFinanceApplyHandler;
import org.springblade.handler.OrderFinancingLoanApplyHandler;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/6
 * @description 手动放款-融资申请
 */
@Component("orderFinancingFinanceApplyProcessListener")
@RequiredArgsConstructor
public class FinanceApplyProcessListener implements ExecutionListener {
    private final OrderFinancingLoanApplyHandler orderFinancingLoanApplyHandler;
    private final OrderFinanceApplyHandler orderFinanceApplyHandler;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            //执行手动放款融资申请终止
            orderFinanceApplyHandler.close(delegateExecution);
        } else {
            //执行手动放款融资申请通过
            orderFinanceApplyHandler.pass(delegateExecution);
        }
    }
}
