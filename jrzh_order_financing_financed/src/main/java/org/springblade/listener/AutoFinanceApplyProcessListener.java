package org.springblade.listener;

import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.core.tool.utils.StringUtil;

import org.springblade.handler.OrderFinancingAutoFinanceApplyHandler;
import org.springblade.handler.OrderFinancingLoanApplyHandler;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/2
 * @description 订单融资自动放款融资申请监听
 */
@Component("orderFinancingAutoFinanceApplyProcessListener")
@RequiredArgsConstructor
public class AutoFinanceApplyProcessListener implements ExecutionListener {

    private final OrderFinancingAutoFinanceApplyHandler orderFinancingAutoFinanceApplyHandler;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            //执行自动放款融资申请终止
            orderFinancingAutoFinanceApplyHandler.close(delegateExecution);
        }else {
            //执行自动放款融资申请通过
            orderFinancingAutoFinanceApplyHandler.pass(delegateExecution);
        }
    }
}
