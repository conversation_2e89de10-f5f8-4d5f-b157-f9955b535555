package org.springblade.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.constant.CommonConstant;
import org.springblade.product.expense.constant.ExpenseConstant;;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.BillPayStatusEnum;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.RefundEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.dto.ExpenseOrderPayedDTO;
import org.springblade.expense.dto.ExpenseOrderRefundDTO;
import org.springblade.expense.entity.BillFinancialFlow;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IBillFinancialFlowService;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.service.IRefundService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 费用订单处理监听
 *
 * <AUTHOR>
 */
@Component("orderFinancingBillExpenseProcessListener")
@RequiredArgsConstructor
public class BillExpenseProcessListener implements ExecutionListener {
    private final IExpenseOrderService expenseOrderService;
    private final IRefundService refundService;
    private final IExpenseOrderDetailService expenseOrderDetailService;
    private final IBillFinancialFlowService billFinancialFlowService;
    private final IProductExpenseService productExpenseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {

        Map<String, Object> variables = delegateExecution.getVariables();
        Integer PLAT_TYPE = delegateExecution.getVariable(ProcessConstant.BILL_PLAT_TYPE, Integer.class);
        String FINANCE_NO = delegateExecution.getVariable(ProcessConstant.FINANCE_NO, String.class);
        Integer CHARGE_METHOD = delegateExecution.getVariable(ProcessConstant.CHARGE_METHOD, Integer.class);
        Long FINANCE_APPLY_ID = delegateExecution.getVariable(ProcessConstant.FINANCE_APPLY_ID, Long.class);
        Map<String, Object> billFrom = delegateExecution.getVariable(ProcessConstant.BILL_EXPENSE_ORDER, Map.class);
        Object platformExpensesObj = delegateExecution.getVariable(ProcessConstant.PLATFORM_EXPENSES);
        //先判断流程变量中是否存在平台费 若没有查询是否存在平台费 有则使用费用订单 有则证明平台费存在手填
        List<ExpenseOrderDetail> expensesList = null;
        if (ObjectUtil.isEmpty(platformExpensesObj)) {
            expensesList = expenseOrderDetailService.getByFinanceNo(FINANCE_NO, PLAT_TYPE);
            if (CollUtil.isEmpty(expensesList)) {
                return;
            }
        } else {
            List<Map> platformExpensesList = (List<Map>) platformExpensesObj;
            expensesList = StreamUtil.map(platformExpensesList, o -> JSONUtil.toBean(JSONUtil.toJsonStr(o), ExpenseOrderDetail.class));
        }
        //排除
        expensesList = expensesList.stream().filter(e -> ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode() == e.getCollectFeesNode()).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(expensesList)) {
            return;
        }
        //获取费用订单数据 若支付方式为线上支付 则跳过
        ExpenseOrder billExpenseOrder = expenseOrderService.getByFinanceNoAndType(FINANCE_NO, PLAT_TYPE);
        if (expenseOrderService.isOnlinePayMethod(billExpenseOrder.getPaymentMethod())) {
            return;
        }
        //参数检查
        if (ObjectUtil.isEmpty(PLAT_TYPE) ||
                ObjectUtil.isEmpty(FINANCE_NO) ||
                ObjectUtil.isEmpty(CHARGE_METHOD) ||
                ObjectUtil.isEmpty(FINANCE_APPLY_ID)) {
            throw new RuntimeException("费用订单监听参数缺失");
        }
        //处理
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            handlerTerminalExpenseOrder(billFrom, billExpenseOrder, expensesList, FINANCE_NO, PLAT_TYPE, CHARGE_METHOD, FINANCE_APPLY_ID, variables);
        } else {
            handlerSuccess(billFrom, billExpenseOrder, expensesList, FINANCE_NO, CHARGE_METHOD, FINANCE_APPLY_ID, variables);
        }
    }

    private void handlerSuccess(Map<String, Object> billFrom, ExpenseOrder billExpenseOrder, List<ExpenseOrderDetail> expenseOrderDetailList,
                                String financeNo, Integer chargeMethod, Long financeApplyId, Map<String, Object> variables) {
        Integer originPaymentStatus = billExpenseOrder.getPaymentStatus();
        Integer paymentStatus = (Integer) billFrom.get("paymentStatus");

        //若为原状态为待付款并且已付款 则保存账户明细流水
        if (paymentStatus.equals(BillPayStatusEnum.BILL_ALREADY.getCode()) && BillPayStatusEnum.BILL_PENDING.getCode() == originPaymentStatus) {
            payExpenseOrder(billFrom, billExpenseOrder, variables);

        }
        // 保存平台费用
        //若为统一清分 则改为待清分
        if (chargeMethod.equals(GoodsEnum.UNIFIED.getCode())) {
            expenseOrderService
                    .changeStatus(Collections.singletonList(billExpenseOrder.getId()), BillPayStatusEnum.BILL_BE_SORTED_OUT.getCode());
        }
//        expenseOrderDetailService.updateBatchById(expenseOrderDetailList);
    }

    private ExpenseOrder payExpenseOrder(Map<String, Object> billFrom, ExpenseOrder billExpenseOrder, Map<String, Object> variables) {
        String account = (String) billFrom.get("account");
        String bank = (String) billFrom.get("bank");
        String payTime = (String) billFrom.get("payTime");
        String amount = billFrom.get("amount").toString();
        String attachId = variables.get("attach_id").toString();
        ExpenseOrderPayedDTO build = ExpenseOrderPayedDTO.builder()
                .billExpenseNo(billExpenseOrder.getBillExpenseNo())
                .expenseOrder(billExpenseOrder)
                .bank(bank)
                .account(account)
                .payAttachId(attachId)
                .amount(new BigDecimal(amount))
                .payTime(LocalDateTimeUtil.parse(payTime, "yyyy-MM-dd HH:mm:ss")).build();
        return productExpenseService.expenseOrderPay(build);
    }

    /**
     * 费用订单流程终止
     *
     * @param billFrom         费用单填写
     * @param billExpenseOrder 原费用订单信息
     * @param platformExpenses 平台费用
     * @param financeNo        融资编号
     * @param platType         平台费用类型
     * @param changeMethod     收费方式
     */
    private void handlerTerminalExpenseOrder(Map<String, Object> billFrom,
                                             ExpenseOrder billExpenseOrder,
                                             List<ExpenseOrderDetail> platformExpenses,
                                             String financeNo, Integer platType, Integer changeMethod, Long financeApplyId, Map<String, Object> variables) {
        Integer originPaymentStatus = billExpenseOrder.getPaymentStatus();
        //若为资方统一收费 则直接关闭
        if (ObjectUtil.isNotEmpty(billExpenseOrder) && changeMethod.equals(GoodsEnum.UNIFIED.getCode())) {
            billExpenseOrder.setPaymentStatus(BillPayStatusEnum.BILL_CLOSED.getCode());
            expenseOrderService.updateById(billExpenseOrder);
            return;
        }
        //终止时未填写费用信息且为待付款状态 则设为待确认费用订单
        if (CollectionUtils.isEmpty(billFrom) && BillPayStatusEnum.BILL_PENDING.getCode() == originPaymentStatus) {
            expenseOrderService.update(Wrappers.<ExpenseOrder>lambdaUpdate()
                    .eq(ExpenseOrder::getId, billExpenseOrder.getId())
                    .eq(ExpenseOrder::getPaymentStatus, BillPayStatusEnum.BILL_PENDING.getCode())
                    .set(ExpenseOrder::getNeedConfirm, CommonConstant.YES));
            return;
        }
        Integer paymentStatus = (Integer) billFrom.get("paymentStatus");
        billExpenseOrder.setPaymentStatus(paymentStatus);
        // 如果还款状态是已还款，生成一条退款中订单
        if (paymentStatus.equals(BillPayStatusEnum.BILL_ALREADY.getCode())) {
            //若为待付款 则保存账户明细流水
            if (BillPayStatusEnum.BILL_PENDING.getCode() == originPaymentStatus) {
                //支付
                payExpenseOrder(billFrom, billExpenseOrder, variables);
            }
            //退款
            String amount = billFrom.get("amount").toString();
            ExpenseOrderRefundDTO expenseOrderRefundDTO = ExpenseOrderRefundDTO.builder()
                    .billExpenseNo(billExpenseOrder.getBillExpenseNo())
                    .failReason((String) billFrom.getOrDefault("failReason", ""))
                    .amount(new BigDecimal(amount)).build();
            productExpenseService.expenseOrderRefund(expenseOrderRefundDTO);
        }
    }

    /**
     * 生成待退款订单
     *
     * @param billExpenseOrder
     */
    private void saveRefund(ExpenseOrder billExpenseOrder) {
        Refund refund = new Refund();
        refund.setRefundOrderNo(CodeUtil.generateCode(CodeEnum.REFUND_NO));
        refund.setRefundType(1);
        refund.setStatus(RefundEnum.RefundStatusEnum.STATUS_NOT.getCode());
        refund.setBillExpenseNo(billExpenseOrder.getBillExpenseNo());
        refund.setRefundAmount(billExpenseOrder.getAmount());
        refund.setUserId(billExpenseOrder.getCustomerId());
        refund.setCashDepositRate(BigDecimal.ZERO);
        refund.setPaymentMethod(billExpenseOrder.getPaymentMethod().toString());
        refundService.save(refund);
    }

    /**
     * 保存财务流水
     *
     * @param billExpenseOrder
     * @param financeNo
     * @param financeApplyId
     */
    private void saveFollow(ExpenseOrder billExpenseOrder, List<ExpenseOrderDetail> expenseOrderDetailList, String financeNo, Long financeApplyId) {
        BigDecimal amount = expenseOrderDetailList.stream()
                .map(ExpenseOrderDetail::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BillFinancialFlow billFinancialFlow = new BillFinancialFlow();
        billFinancialFlow.setBillExpenseNo(billExpenseOrder.getBillExpenseNo());
        billFinancialFlow.setFinancialType(1);
        billFinancialFlow.setFinanceApplyId(financeApplyId);
        billFinancialFlow.setFinanceNo(financeNo);
        billFinancialFlow.setAmount(amount);
        billFinancialFlow.setCustomerId(billExpenseOrder.getCustomerId());
        billFinancialFlow.setPaymentChannelNo(null);
        billFinancialFlow.setIncomeStatus(1);
        billFinancialFlow.setTransactionFlowNo(CodeUtil.generateCode(CodeEnum.BILL_TRANSACTION_FLOW_NO));
        billFinancialFlowService.saveFinancialFlow(billFinancialFlow);
    }
}
