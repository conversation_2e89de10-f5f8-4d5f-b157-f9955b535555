package org.springblade.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.EnterpriseTypeEnum;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.customer.entity.SalesContractDetail;
import org.springblade.customer.vo.SalesContractVO;
import org.springblade.finance.dto.OrderData;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.enums.financeApply.FinanceApplyEnums;
import org.springblade.finance.external.dto.EnterpriseQuotaSerchDTO;
import org.springblade.finance.external.handler.enterpriseQuota.FinanceEnterpriseService;
import org.springblade.finance.external.handler.salesContract.FinanceSalesContractService;
import org.springblade.finance.handler.FinanceApplyCommonService;
import org.springblade.finance.limit.entity.FinancingLimit;
import org.springblade.finance.limit.service.FinancingLimitService;
import org.springblade.finance.vo.FinanceApplyVO;
import org.springblade.finance.vo.financeCommon.FinanceApplyCommonVo;
import org.springblade.finance.vo.financeCommon.OrderDataVO;
import org.springblade.finance.vo.financeCommon.orderFinancing.OrderFinancingFinanceCommonVo;
import org.springblade.finance.vo.financeCommon.receivable.ReceivableFinanceCommonVo;
import org.springblade.jrzh_order_financing_goods.service.IOrderFinancingGoodsService;
import org.springblade.jrzh_order_financing_goods_api.entity.OrderFinancingGoods;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springblade.loan.service.ILoanManageRepaymentTermService;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;

import org.springblade.service.IOrderDataService;
import org.springblade.system.entity.User;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 订单融资通用接口实现类
 */
@Service(FinanceApplyEnums.FinanceCommonType.FINANCE_COMMON_ORDERFINANCING)
@RequiredArgsConstructor
public class OrderFinanceApplyCommonService implements FinanceApplyCommonService {


    /**
     * 产品配置 接口
     */
    private final ProductDirector productDirector;

    /**
     * 融资-额度接口
     */
    private final FinanceEnterpriseService financeEnterpriseService;

    /**
     * 销售合同 接口
     */
    private final FinanceSalesContractService financeSalesContractService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IOrderFinancingGoodsService orderFinancingGoodsService;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final IOrderDataService orderDataService;
    private final FinancingLimitService financingLimitService;
    /**
     * 根据融资主数据，查询融资详情数据
     *
     * @param financeApply 融资主数据
     * @return 融资详情数据
     */
    @Override
    public FinanceApplyCommonVo financeDetail(FinanceApply financeApply) {
        //定义主体返回类型
        FinanceApplyCommonVo financeApplyCommonVo = new FinanceApplyCommonVo();
        //定义应收账款接收类型

        financeApplyCommonVo.setFinanceApply(financeApply);

        //查询销售合同使用明细
        List<SalesContractDetail> salesContractDetailList = financeSalesContractService.getByFinanceApplyId(financeApply.getId());
        List<Long> saleContractIdList = StreamUtil.map(salesContractDetailList, SalesContractDetail::getSaleContractId);
        // 查询销售合同
//        List<OrderData> orderData = orderDataService.listByIds(saleContractIdList);
        List<FinancingLimit> financingLimitList = financingLimitService.listByIds(saleContractIdList);
        //查询企业额度
        EnterpriseQuotaSerchDTO enterpriseQuotaSerchDTO = financeEnterpriseService.selectEnterpriseQuota(financeApply.getGoodsId(), EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), AuthUtil.getUserId());
        //查询产品数据
        Product product = productDirector.detailBase(financeApply.getGoodsId());
        //获取最高可借金额
        BigDecimal maxAmount = NumberUtil.max(enterpriseQuotaSerchDTO.getAvailableAmount(), product.getLoanAmountEnd());
        //数据赋值
        OrderFinancingFinanceCommonVo orderFinancingFinanceCommonVo = new OrderFinancingFinanceCommonVo();
        orderFinancingFinanceCommonVo.setSalesContractDetails(salesContractDetailList);
//        orderFinancingFinanceCommonVo.setOrderDatas(orderData);
        orderFinancingFinanceCommonVo.setFinancingLimits(financingLimitList);
        orderFinancingFinanceCommonVo.setMaxAmount(maxAmount);
        orderFinancingFinanceCommonVo.setLoadTermEnd(product.getLoadTermEnd());
        orderFinancingFinanceCommonVo.setLoadTermStart(product.getLoadTermStart());
        orderFinancingFinanceCommonVo.setBillingMethod(product.getBillingMethod());
        financeApplyCommonVo.setOrderFinancingFinanceCommonVo(orderFinancingFinanceCommonVo);
        return financeApplyCommonVo;
    }

    @Override
    public void dealFinanceApply(List<FinanceApplyVO> list, Map<String,
            List<LoanManageRepaymentPlan>> allUnRepaymentMap, Map<String,
            List<LoanManageRepaymentPlan>> overdueRepaymentMap,
                                 Map<Long, User> userMap) {
        for (FinanceApplyVO financeApplyVO : list) {
            User user = userMap.get(financeApplyVO.getUserId());
            if (Objects.nonNull(user)) {
                financeApplyVO.setCustomerName(user.getName());
            }
            if (FinanceApplyStatusEnum.UN_SETTLED.getCode().equals(financeApplyVO.getStatus())
                    && GoodsEnum.IS_DELAY.getCode().equals(financeApplyVO.getIsDelay())
                    && financeApplyVO.getOldFinanceNo() == null) {
                OrderFinancingGoods goods = orderFinancingGoodsService.getById(financeApplyVO.getGoodsId());
                LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.selectRepaymentById(financeApplyVO.getId());
                long day = loanManageRepaymentPlan.getRepaymentTime().toEpochDay() - LocalDate.now().toEpochDay();
                if (day >= goods.getDelayBeforeDayMin() && day <= goods.getDelayBeforeDayMax()) {
                    financeApplyVO.setCanOpenDelayApply(true);
                }
            }else {
                financeApplyVO.setCanOpenDelayApply(false);
            }
            //处于可逾期协商阶段并且状态为未结清显示协商按钮
            boolean canOverdueConsult = FinanceApplyStatusEnum.OVERDUE_UN_SETTLED.getCode().equals(financeApplyVO.getStatus())
                    && canRunConsult(financeApplyVO, allUnRepaymentMap.get(financeApplyVO.getId().toString()), overdueRepaymentMap.get(financeApplyVO.getId().toString()));
            financeApplyVO.setCanOpenOverdueConsult(canOverdueConsult);
        }
    }

    public boolean canRunConsult(FinanceApply financeApply, List<LoanManageRepaymentPlan> unRepaymentList, List<LoanManageRepaymentPlan> overdueList) {
        if (CommonConstant.CLOSESTATUS.equals(financeApply.getConsultStatus()) || CollectionUtil.isEmpty(unRepaymentList) || CollectionUtil.isEmpty(overdueList)) {
            return false;
        }
        //订单全部逾期并且订单不处于支付中节点
        return unRepaymentList.size() == overdueList.size() && !hasPayingLoanRepaying(unRepaymentList);
    }

    /**
     * 存在付款中的订单
     *
     * @param unRepaymentList
     * @return
     */
    private boolean hasPayingLoanRepaying(List<LoanManageRepaymentPlan> unRepaymentList) {
        List<Long> repaymentPlanIds = StreamUtil.map(unRepaymentList, LoanManageRepaymentPlan::getId);
        List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentTermService.selectRepaymentListByRepaymentPlanIds(repaymentPlanIds);
        if (CollectionUtil.isNotEmpty(loanManageRepayments)) {
            return loanManageRepaymentService.hasPaying(loanManageRepayments);
        }
        return false;
    }

}
