package org.springblade.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.rabbitmq.MessageTypeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.GoodsTiminUtils;
import org.springblade.constant.OrderFinancingConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.customer.entity.QuotaUseDetails;
import org.springblade.customer.entity.SalesContractDetail;
import org.springblade.expense.dto.CreateExpenseOrderDetailDTO;
import org.springblade.expense.dto.ExpenseOrderDTO;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseInfoBizService;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.dto.FinanceApplyDTO;
import org.springblade.finance.dto.OrderData;
import org.springblade.finance.dto.financeApplyHandler.FinanceApplyHandlerDTO;
import org.springblade.finance.dto.financeApplyHandler.receivable.ReceivableFinanceDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.external.dto.EnterpriseQuotaSerchDTO;
import org.springblade.finance.external.dto.SalesContractDTO;
import org.springblade.finance.external.handler.businessProcess.FinanceBusinessProcessService;
import org.springblade.finance.external.handler.businessProcessProgress.FinanceBusinessProcessProgressService;
import org.springblade.finance.external.handler.enterpriseQuota.FinanceEnterpriseService;
import org.springblade.finance.external.handler.platformAccount.FinancePlatformAccountService;
import org.springblade.finance.external.handler.quotaUseDetails.FinanceQuotaUseDetailService;
import org.springblade.finance.external.handler.salesContract.FinanceSalesContractService;
import org.springblade.finance.factory.OrderLevelServerFactory;
import org.springblade.finance.handler.impl.AbstractFinanceHandler;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.service.OrderLevelServerService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.jrzh_order_financing_goods.service.IOrderFinancingGoodsService;
import org.springblade.jrzh_order_financing_goods_api.entity.OrderFinancingGoods;
import org.springblade.loan.service.IRepaymentPlanFinanceApplyBizService;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.otherapi.core.constant.ApiSupplier;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.product.common.constant.AccountTypeEnum;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.GoodsTiming;
import org.springblade.product.common.entity.Product;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.product.moudle.timing.service.IGoodsTimingService;
import org.springblade.service.IOrderDataService;
import org.springblade.system.utils.UserUtils;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单融资---融资实现类
 *
 * <AUTHOR>
 * @date 2023-04-19
 */
@Service("orderFinance")
@RequiredArgsConstructor
public class OrderFinanceService extends AbstractFinanceHandler {
    private final TradeApiHandlerFactory tradeApiHandlerFactory;
    @Resource
    private FinanceApplyMapper financeApplyMapper;


    /**
     * 额度
     */
    private final FinanceEnterpriseService financeEnterpriseService;

    /**
     * 销售合同
     */
    private final FinanceSalesContractService financeSalesContractService;

    /**
     * 融资申请服务类接口
     */
    private final IFinanceApplyService financeApplyService;

    /**
     * 费用订单 接口
     */
    private final IExpenseOrderService expenseOrderService;
    private final IExpenseOrderDetailService expenseOrderDetailService;

    /**
     * 额度使用明细 接口
     */
    private final FinanceQuotaUseDetailService financeQuotaUseDetailService;

    /**
     * 产品配置 接口
     */
    private final ProductDirector productDirector;

    /**
     * 流程发起
     */
    private final FinanceBusinessProcessService financeBusinessProcessService;

    /**
     * 平台账户 接口
     */
    private final FinancePlatformAccountService financePlatformAccountService;

    /**
     * 产品开通--进度
     */
    private final FinanceBusinessProcessProgressService financeBusinessProcessProgressService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    private final IProductExpenseService productExpenseService;
    private final IExpenseInfoBizService expenseInfoBizService;

    private final IOrderDataService orderDataService;
    private final IBusinessProcessProductService businessProcessProductService;

    private final RabbitMsgSender rabbitMsgSender;
    private final IGoodsTimingService goodsTimingService;
    private final IOrderFinancingGoodsService goodsService;
    private final OrderLevelServerFactory orderLevelServerFactory;

    /**
     * 检查当前融资订单可否提交申请
     *
     * @param financeApplyId 融资申请id
     */
    @Override
    public void checkApplyFinanceApply(Long financeApplyId) {
        //可发起融资的状态
        List<Integer> canApplyFinanceStatus = Arrays.asList(FinanceApplyStatusEnum.UN_SUBMIT.getCode()
                , FinanceApplyStatusEnum.REJECT.getCode()
                , FinanceApplyStatusEnum.UN_LOAN.getCode()
                , FinanceApplyStatusEnum.LOAN_REJECT.getCode());
        int count = financeApplyService.count(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getId, financeApplyId)
                .in(FinanceApply::getStatus, canApplyFinanceStatus));
        if (count == 0) {
            throw new ServiceException("当前订单状态不可操作");
        }
    }

    /**
     * 模板方法-扣除销售合同额度
     *
     * @return 需要执行的方法
     */
    @Override
    public boolean templateMethod() {
        return false;
    }


    /**
     * 额度扣减
     *
     * @param applyAmount 申请金额
     * @param amount      已经存在得额度（查询融资申请数据，如果不存在则没有额度:默认0）
     * @param goodsId     产品id
     */
    @Override
    public void limitDeduction(BigDecimal applyAmount, BigDecimal amount, Long goodsId) {
        //企业类型可能发生改变，为了后续扩展额度扣减接口各自模块重写
        int enterpriseType = EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode();
        financeEnterpriseService.subtractCreditAmount(applyAmount, amount, goodsId, enterpriseType);
    }

    /**
     * 扣除销售合同额度(应收账款核心)
     *
     * @param financeApplyId          融资申请id
     * @param salesContractDetailList 销售合同列表
     */
    @Override
    public void subtractReceivableAmount(Long financeApplyId, List<SalesContractDetail> salesContractDetailList) {
        //销售合同数据转换
        List<SalesContractDTO> salesContractDTOS = BeanUtil.copyToList(salesContractDetailList, SalesContractDTO.class);
        financeSalesContractService.subtractReceivableAmount(financeApplyId, salesContractDTOS);
    }


    /**
     * 获取融资申请金额
     *
     * @param financeApplyHandlerDTO 融资申请条件
     * @return
     */
    @Override
    public BigDecimal accessQuota(FinanceApplyHandlerDTO financeApplyHandlerDTO) {
        ReceivableFinanceDTO receivableFinanceDTO = financeApplyHandlerDTO.getReceivableFinanceDTO();
        List<SalesContractDetail> salesContractDetails = receivableFinanceDTO.getSalesContractDetails();
        BigDecimal applyAmount = salesContractDetails.stream().map(SalesContractDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return applyAmount;
    }

    /**
     * 获取企业额度
     *
     * @param goodsId
     * @return
     */
    @Override
    public EnterpriseQuotaSerchDTO selectEnterpriseQuota(Long goodsId) {
        // 查询额度
        Long userId = AuthUtil.getUserId();
        int enterpriseType = EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode();
        return financeEnterpriseService.selectEnterpriseQuota(goodsId, enterpriseType, userId);
    }


    /**
     * 自动放款---保存融资申请--新增
     *
     * @param financeApplyHandlerDTO
     * @param enterpriseQuotaSearchDTO
     * @param product
     */
    @Override
    public void saveFinanceApply(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //应收账款数据
        ReceivableFinanceDTO receivableFinanceDTO = financeApplyHandlerDTO.getReceivableFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setId(IdWorker.getId());
        financeApply.setUserId(AuthUtil.getUserId());
        financeExpense(enterpriseQuotaSearchDTO, product, financeApply);
        //2、费用订单详情新增
        //构建试算参数
        List<ExpenseOrderDetail> expenseOrderDetails = buildCostCalculusDto(financeApplyHandlerDTO);
        //创建费用订单
        expenseInfoBizService.genExpenseOrderList(expenseOrderDetails, financeApply.getFinanceNo(), ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode(), AuthUtil.getUserId(), UserUtils.getEnterpriseType());
        // 3、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);
        financeApply.setQuotaUseDetailsId(quotaUseDetails.getId());
        //新增倒计时时间
        LocalDateTime countdownExpireTime = getByExpireTime(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_ORDER_FINANCING_APPLY.getCode());
        financeApply.setCountdownExpireTime(countdownExpireTime);
        financeApply.setGoodsName(product.getGoodsName());
        financeApply.setCapitalId(product.getCapitalId());
        financeApply.setRepaymentType(product.getRepaymentType());
        //判断是否为资方统一收取
//        if (product.getChargeMethod().equals(GoodsEnum.UNIFIED.getCode())) {
//            // 4、开启流程、设置流程变量
//            Integer processType = ProcessTypeEnum.AUTO_LOAN_FINANCE_APPLY.getCode();
//            Map<String, Object> variables = getVariables(receivableFinanceDTO, financeApply, processType, expenseOrderDetailList, product);
//            String processInstanceId = startProcess(financeApply.getGoodsId(), processType, variables);
//            financeApply.setProcessInstanceId(processInstanceId);
//            financeApply.setStatus(FinanceApplyStatusEnum.APPLICATION.getCode());
//        }
        financeBusinessProcessProgressService.saveBusinessProcessProgressUnContainInvalid(financeApply.getId(), ProcessProgressEnum.AUTO_LOAN_FINANCE_CONFIRM_2.getCode(), ProcessTypeEnum.AUTO_LOAN_FINANCE_APPLY.getCode(), null);
        // 5、新增 -- 融资数据
        financeSave(receivableFinanceDTO, financeApply);

//        tradeApiHandlerFactory.template(ApiSupplier.LIAN_QIAO_API.getCode()).financeChangeMsgSend(financeApply, null);
        // 发送融资订单未提交的延迟消息
        if (ObjectUtil.isNotEmpty(countdownExpireTime)) {
            this.rabbitFinaceApplyUnSubmmit(financeApply);
        }
    }

    private CostCalculusVO calCostCalculusVO(FinanceApplyHandlerDTO financeApplyHandlerDTO) {
        CostCalculusDto costCalculusDto = buildCostCalculusDtoParam(financeApplyHandlerDTO);
        return repaymentPlanFinanceApplyBizService.costCalculus(costCalculusDto);
    }

    private static CostCalculusDto buildCostCalculusDtoParam(FinanceApplyHandlerDTO financeApplyHandlerDTO) {
        Integer loadTerm = financeApplyHandlerDTO.getFinanceApply().getLoadTerm();
        CostCalculusDto costCalculusDto = financeApplyHandlerDTO.getCostCalculusDto();
        costCalculusDto.setAnnualInterestRate(financeApplyHandlerDTO.getFinanceApply().getAnnualInterestRate());
        costCalculusDto.setFinanceAmount(financeApplyHandlerDTO.getFinanceApply().getAmount());
        costCalculusDto.setGoodType(financeApplyHandlerDTO.getFinanceApply().getGoodsType());
        costCalculusDto.setGoodsId(financeApplyHandlerDTO.getFinanceApply().getGoodsId());
        costCalculusDto.setLoadTermUnit(financeApplyHandlerDTO.getFinanceApply().getLoadTermUnit());
        costCalculusDto.setRefundType(financeApplyHandlerDTO.getFinanceApply().getRepaymentType());
        costCalculusDto.setUserId(AuthUtil.getUserId());
        costCalculusDto.setEnterpriseType(UserUtils.getEnterpriseType());
        costCalculusDto.setFinanceNo(financeApplyHandlerDTO.getFinanceApply().getFinanceNo());
        costCalculusDto.setType(PlatformExpensesEnum.PLAT_TYPE_ORDER_FINANCING.getCode());
        costCalculusDto.setChargePoint("8,2");
        costCalculusDto.setExpenseOrderDetailPass(false);
        costCalculusDto.setTotalTerm(loadTerm);
        costCalculusDto.setExpenseOrderDetailPassIds("");
        financeApplyHandlerDTO.setCostCalculusDto(costCalculusDto);
        return costCalculusDto;
    }

    private List<ExpenseOrderDetail> buildCostCalculusDto(FinanceApplyHandlerDTO financeApplyHandlerDTO) {

        CostCalculusDto costCalculusDto = buildCostCalculusDtoParam(financeApplyHandlerDTO);
        //取消未付款的费用订单 和未关联的费用详情
        productExpenseService.cancelUnPayExpenseOrder(costCalculusDto.getFinanceNo(), costCalculusDto.getType());
        //进行费用计算
        CostCalculusVO costCalculusVO = repaymentPlanFinanceApplyBizService.costCalculus(costCalculusDto);
        List<ExpenseOrderDetail> expenseOrderDetailList = costCalculusVO.getExpenseOrderDetailList();
        //创建费用订单详情
        if (CollUtil.isNotEmpty(expenseOrderDetailList)) {
            CreateExpenseOrderDetailDTO createExpenseOrderDetailDTO = new CreateExpenseOrderDetailDTO();
            createExpenseOrderDetailDTO.setExpenseOrderDetailList(expenseOrderDetailList);
            createExpenseOrderDetailDTO.setFinanceNo(costCalculusDto.getFinanceNo());
            createExpenseOrderDetailDTO.setType(costCalculusDto.getType());
            expenseOrderDetailList = productExpenseService.createExpenseOrderDetail(createExpenseOrderDetailDTO);
        }
        //保存还款试算参数
        repaymentPlanFinanceApplyBizService.saveCalJson(costCalculusVO, PlatformExpensesEnum.PLAT_TYPE_ORDER_FINANCING.getCode(), financeApplyHandlerDTO.getFinanceApply().getFinanceNo());
        return expenseOrderDetailList;
    }

    /**
     * 自动放款---保存融资申请 ---修改
     *
     * @param financeApplyHandlerDTO
     * @param enterpriseQuotaSearchDTO
     * @param product
     */
    @Override
    public void saveFinanceApplyUpdate(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //应收账款数据
        ReceivableFinanceDTO receivableFinanceDTO = financeApplyHandlerDTO.getReceivableFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        Long userId = AuthUtil.getUserId();
        //业务处理
        // List<ExpenseOrderDetail> expenseOrderDetailList = financeExpense(enterpriseQuotaSearchDTO, product, financeApply);
        financeApply.setUserId(userId);
        financeExpense(enterpriseQuotaSearchDTO, product, financeApply);
        //2、费用订单详情新增
        //构建试算参数
        List<ExpenseOrderDetail> expenseOrderDetails = buildCostCalculusDto(financeApplyHandlerDTO);
        //创建费用订单
        expenseInfoBizService.genExpenseOrderList(expenseOrderDetails, financeApply.getFinanceNo(), ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode(), AuthUtil.getUserId(), AuthUtil.getUserType());
        // 3、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        quotaUseDetails.setId(financeApply.getQuotaUseDetailsId());
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);

        //新增倒计时时间
        LocalDateTime countdownExpireTime = getByExpireTime(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_ORDER_FINANCING_APPLY.getCode());
        financeApply.setCountdownExpireTime(countdownExpireTime);

        //判断是否为资方统一收取
//        if (product.getChargeMethod().equals(GoodsEnum.UNIFIED.getCode())) {
//            // 4、修改审批流程、设置流程变量
//            Integer processType = ProcessTypeEnum.FINANCE_APPLY.getCode();
//            Map<String, Object> variables = getVariables(receivableFinanceDTO, financeApply, processType, expenseOrderDetailList, product);
//            financeBusinessProcessService.completeTask(financeApply.getProcessInstanceId(),variables);
//            financeApply.setStatus(FinanceApplyStatusEnum.APPLICATION.getCode());
//        }

        financeBusinessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(financeApply.getId(), ProcessProgressEnum.AUTO_LOAN_FINANCE_CONFIRM_2.getCode(), ProcessTypeEnum.AUTO_LOAN_FINANCE_APPLY.getCode(), null, userId, null, null, true);
        // 5、融资数据修改
        financeApplyService.updateById(financeApply);
        // 使用的水位id
        List<Long> orderDataIds = financeApplyHandlerDTO.getReceivableFinanceDTO().getSalesContractDetails().stream().map(SalesContractDetail::getSaleContractId).collect(Collectors.toList());
        // 订单融资的情况只会用到一条水位
        Long limitId = orderDataIds.get(0);
        OrderLevelServerService orderLevelServerService = orderLevelServerFactory.getServer(financeApply.getGoodsType());
        orderLevelServerService.updateUseOrderLevel(limitId, financeApply.getAmount(), String.valueOf(financeApply.getId()));

        //保存平台账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());

        // 6、保存应收账款使用明细
        saveSaleContractDetail(receivableFinanceDTO, financeApply);
    }


    /**
     * 手动放款----提交融资申请-新增
     *
     * @param financeApplyHandlerDTO   融资申请数据
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     */
    @Override
    public void submitFinanceApplySave(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //应收账款数据
        ReceivableFinanceDTO receivableFinanceDTO = financeApplyHandlerDTO.getReceivableFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setId(IdWorker.getId());
        financeApply.setStatus(FinanceApplyStatusEnum.APPLICATION.getCode());
        // 1、数据赋值
        financeData(enterpriseQuotaSearchDTO, product, financeApply);
        // 2、费用订单详情新增
        CostCalculusDto costCalculusDto = buildCostCalculusDtoParam(financeApplyHandlerDTO);
        //取消未付款的费用订单 和未关联的费用详情
        productExpenseService.cancelUnPayExpenseOrder(costCalculusDto.getFinanceNo(), costCalculusDto.getType());
        CostCalculusVO costCalculusVO = repaymentPlanFinanceApplyBizService.costCalculus(costCalculusDto);

        //保存还款试算参数
        repaymentPlanFinanceApplyBizService.saveCalJson(costCalculusVO, PlatformExpensesEnum.PLAT_TYPE_ORDER_FINANCING.getCode(), financeApplyHandlerDTO.getFinanceApply().getFinanceNo());
        // 3、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);
        financeApply.setQuotaUseDetailsId(quotaUseDetails.getId());

        // 4、开启流程、设置流程变量
        Integer processType = ProcessTypeEnum.FINANCE_APPLY.getCode();
        Map<String, Object> variables = getVariables(receivableFinanceDTO, financeApply, processType, null, product);
        variables.put(ProcessConstant.EXPENSE_INFO_LIST, financeApplyHandlerDTO.getExpenseInfoExpenseVOList());
        String processInstanceId = startProcess(financeApply.getGoodsId(), processType, variables);
        financeApply.setProcessInstanceId(processInstanceId);
        // 保存流程进度(后续可进行改造)
        financeBusinessProcessProgressService.saveBusinessProcessProgressUnContainInvalid(financeApply.getId(), ProcessProgressEnum.FINANCE_APPROVE.getCode(), processType, processInstanceId);
        // 5、新增 -- 融资数据
        financeSave(receivableFinanceDTO, financeApply);
//        tradeApiHandlerFactory.template(ApiSupplier.LIAN_QIAO_API.getCode()).financeChangeMsgSend(financeApply, null);
    }

    /**
     * 新保存
     *
     * @param financeApplyHandlerDTO
     * @param enterpriseQuotaSearchDTO
     * @param product
     */
    private void submitFinanceApplySaveV2(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //应收账款数据
        ReceivableFinanceDTO receivableFinanceDTO = financeApplyHandlerDTO.getReceivableFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setId(IdWorker.getId());
        financeApply.setStatus(FinanceApplyStatusEnum.APPLICATION.getCode());
        // 1、数据赋值
        financeData(enterpriseQuotaSearchDTO, product, financeApply);

        // 2、费用订单详情新增
//      List<ExpenseOrderDetail> expenseOrderDetailList = expenseOrderDetailSave(enterpriseQuotaSearchDTO, financeApply);
        CostCalculusDto costCalculusDto = financeApplyHandlerDTO.getCostCalculusDto();
        CostCalculusVO costCalculusVO = repaymentPlanFinanceApplyBizService.costCalculus(costCalculusDto);
        List<ExpenseOrderDetail> expenseOrderDetailList = costCalculusVO.getExpenseOrderDetailList();
        //取消未付款的费用订单 和未关联的费用详情
        productExpenseService.cancelUnPayExpenseOrder(costCalculusDto.getFinanceNo(), costCalculusDto.getType());
        //创建费用订单详情
        if (CollUtil.isNotEmpty(expenseOrderDetailList)) {
            CreateExpenseOrderDetailDTO createExpenseOrderDetailDTO = new CreateExpenseOrderDetailDTO();
            createExpenseOrderDetailDTO.setExpenseOrderDetailList(expenseOrderDetailList);
            createExpenseOrderDetailDTO.setFinanceNo(costCalculusDto.getFinanceNo());
            createExpenseOrderDetailDTO.setType(costCalculusDto.getType());
            expenseOrderDetailList = productExpenseService.createExpenseOrderDetail(createExpenseOrderDetailDTO);
        }
        // 3、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);
        financeApply.setQuotaUseDetailsId(quotaUseDetails.getId());

        // 4、开启流程、设置流程变量
        Integer processType = ProcessTypeEnum.FINANCE_APPLY.getCode();
        Map<String, Object> variables = getVariables(receivableFinanceDTO, financeApply, processType, expenseOrderDetailList, product);
        String processInstanceId = startProcess(financeApply.getGoodsId(), processType, variables);
        financeApply.setProcessInstanceId(processInstanceId);
        // 保存流程进度(后续可进行改造)
        financeBusinessProcessProgressService.saveBusinessProcessProgressUnContainInvalid(financeApply.getId(), ProcessProgressEnum.FINANCE_APPROVE.getCode(), processType, processInstanceId);
        // 5、新增 -- 融资数据
        financeSave(receivableFinanceDTO, financeApply);
    }

    /**
     * 手动放款----提交融资申请-修改
     *
     * @param financeApplyHandlerDTO   融资申请数据
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     */
    @Override
    public void submitFinanceApplyUpdate(FinanceApplyHandlerDTO financeApplyHandlerDTO, EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product) {
        //应收账款数据
        ReceivableFinanceDTO receivableFinanceDTO = financeApplyHandlerDTO.getReceivableFinanceDTO();
        //获取融资数据
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        financeApply.setStatus(FinanceApplyStatusEnum.APPLICATION.getCode());

        // 1、数据赋值
        financeData(enterpriseQuotaSearchDTO, product, financeApply);

        // 2、费用订单详情新增
        CostCalculusDto costCalculusDto = buildCostCalculusDtoParam(financeApplyHandlerDTO);
        //取消未付款的费用订单 和未关联的费用详情
        productExpenseService.cancelUnPayExpenseOrder(costCalculusDto.getFinanceNo(), costCalculusDto.getType());
        //保存还款试算参数
        CostCalculusVO costCalculusVO = repaymentPlanFinanceApplyBizService.costCalculus(costCalculusDto);
        repaymentPlanFinanceApplyBizService.saveCalJson(costCalculusVO, PlatformExpensesEnum.PLAT_TYPE_ORDER_FINANCING.getCode(), financeApplyHandlerDTO.getFinanceApply().getFinanceNo());
        // 3、保存记录到额度使用表
        Integer statusCode = QuotaUseDetailsEnum.FREEZE_QUOTA.getCode();
        QuotaUseDetails quotaUseDetails = financeEnterpriseService.getDetailsByFinanceApply(financeApply, statusCode);
        quotaUseDetails.setId(financeApply.getQuotaUseDetailsId());
        financeQuotaUseDetailService.quotaUseDetailSave(quotaUseDetails);

        // 4、流程任务完成
        Integer processType = ProcessTypeEnum.FINANCE_APPLY.getCode();
        // 设置流程变量
        Map<String, Object> variables = getVariables(receivableFinanceDTO, financeApply, processType, null, product);
        variables.put(ProcessConstant.EXPENSE_INFO_LIST, financeApplyHandlerDTO.getExpenseInfoExpenseVOList());
        businessProcessProductService.startOrSubmit(financeApply.getGoodsId(), ProcessTypeEnum.FINANCE_APPLY, financeApply.getProcessInstanceId(), variables, ProcessProgressEnum.FINANCING_REVIEW);
        // 5、融资数据修改
        financeApplyService.updateById(financeApply);
        // 使用的水位id
        List<Long> orderDataIds = financeApplyHandlerDTO.getReceivableFinanceDTO().getSalesContractDetails().stream().map(SalesContractDetail::getSaleContractId).collect(Collectors.toList());
        // 订单融资的情况只会用到一条水位
        Long limitId = orderDataIds.get(0);
        OrderLevelServerService orderLevelServerService = orderLevelServerFactory.getServer(financeApply.getGoodsType());
        orderLevelServerService.updateUseOrderLevel(limitId, financeApply.getAmount(), String.valueOf(financeApply.getId()));

        // 修改流程进度
        financeBusinessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(financeApply.getId(),ProcessProgressEnum.FINANCE_APPROVE.getCode(), processType,financeApply.getProcessInstanceId(),AuthUtil.getUserId(),null,null,true);

        //保存平台账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());

        // 6、保存应收账款使用明细
        saveSaleContractDetail(receivableFinanceDTO, financeApply);
    }

    /**
     * 保存融资--业务处理
     *
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品信息
     * @param financeApply             融资数据
     * @return 额度使用明细数据
     */
    private void financeExpense(EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product, FinanceApply financeApply) {
        financeApply.setStatus(FinanceApplyStatusEnum.UN_SUBMIT.getCode());
        // 1、数据赋值
        financeData(enterpriseQuotaSearchDTO, product, financeApply);
        financeApply.setExpireTime(LocalDateTime.now().plusDays(1));
        financeApply.setInterestDay(product.getInterestDay());

    }

    /**
     * 保存--融资数据
     *
     * @param receivableFinanceDTO
     * @param financeApply
     */
    private void financeSave(ReceivableFinanceDTO receivableFinanceDTO, FinanceApply financeApply) {
        financeApply.setCreateUser(AuthUtil.getUser().getUserId());
        financeApply.setCreateDept(Func.firstLong(AuthUtil.getUser().getDeptId()));
        financeApply.setUpdateUser(AuthUtil.getUser().getUserId());
        financeApply.setCreateTime(DateUtil.now());
        financeApplyService.save(financeApply);
        /**
         * 填充订单融资订单表的融资编号
         */
        List<Long> orderDataIds = receivableFinanceDTO.getSalesContractDetails().stream()
                .map(SalesContractDetail::getSaleContractId).collect(Collectors.toList());
//        orderDataService.update(Wrappers.<OrderData>lambdaUpdate().in(OrderData::getId, orderDataIds).set(OrderData::getStatus, OrderFinancingConstant.ORDER_DATA_STATUS_ONE).set(OrderData::getFinanceApplyId, financeApply.getId()));
        // 使用的水位id
        // 订单融资的情况只会用到一条水位
        Long limitId = orderDataIds.get(0);
        OrderLevelServerService orderLevelServerService = orderLevelServerFactory.getServer(financeApply.getGoodsType());
        orderLevelServerService.useOrderLevel(limitId, financeApply.getAmount(), String.valueOf(financeApply.getId()));
        //保存平台账户
        financePlatformAccountService.savePlatformAccount(financeApply.getGoodsId(), financeApply.getFinanceNo(), AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
        // 6、保存应收账款使用明细
        saveSaleContractDetail(receivableFinanceDTO, financeApply);
    }

    /**
     * 获取倒计时时间
     *
     * @param goodsId 产品id
     * @param type    流程类型
     * @return 流程实例id
     */
    private LocalDateTime getByExpireTime(Long goodsId, Integer type) {
        LambdaQueryWrapper<GoodsTiming> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(GoodsTiming::getGoodsId, goodsId).eq(GoodsTiming::getType, type);
        GoodsTiming goodsTiming = productDirector.getConfig(goodsId, GoodsEnum.PRODUCT_CONFIG_TIMING.getCode(), GoodsTiming.class, lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(goodsTiming)) {
            throw new ServiceException("查询不到定时数据，请联系管理员！");
        }
        return GoodsTiminUtils.getByExpireTime(goodsTiming.getUnit(), goodsTiming.getNum());
    }

    /**
     * 保存应收账款使用明细
     *
     * @param receivableFinanceDTO 应收账款数据
     * @param financeApply         融资数据
     */
    private void saveSaleContractDetail(ReceivableFinanceDTO receivableFinanceDTO, FinanceApply financeApply) {
        FinanceApplyDTO financeApplyDTO = new FinanceApplyDTO();
        financeApplyDTO.setId(financeApply.getId());
        financeApplyDTO.setSalesContractDetails(receivableFinanceDTO.getSalesContractDetails());
        financeApplyDTO.setCapitalId(financeApply.getCapitalId());
        financeApplyDTO.setProcessInstanceId(financeApply.getProcessInstanceId());
        financeApplyDTO.setFinanceNo(financeApply.getFinanceNo());
        financeSalesContractService.saveSaleContractDetail(financeApplyDTO);
    }


    /**
     * 开启流程
     *
     * @param goodsId     产品id
     * @param processType 流程类型
     * @param variables   流程变量
     * @return 流程实例id
     */
    private String startProcess(Long goodsId, Integer processType, Map<String, Object> variables) {
        return businessProcessProductService.startProcess(goodsId, processType, variables);
    }

    /**
     * 填充融资申请 变量
     *
     * @param receivableFinanceDTO   应收账款数据
     * @param financeApply           融资数据
     * @param processType            流程类型
     * @param expenseOrderDetailList 费用订单详情数据
     * @return
     */
    private Map<String, Object> getVariables(ReceivableFinanceDTO receivableFinanceDTO, FinanceApply financeApply, Integer processType, List<ExpenseOrderDetail> expenseOrderDetailList, Product product) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ProcessConstant.FINANCE_APPLY_ID, financeApply.getId());
        map.put(ProcessConstant.FINANCE_NO, financeApply.getFinanceNo());
        map.put(ProcessConstant.CUSTOMER_GOODS_ID, financeApply.getCustomerGoodsId());
        map.put(ProcessConstant.BUSINESS_ID, financeApply.getGoodsId());
        map.put(ProcessConstant.CUSTOMER_ID, financeApply.getApplyUser());
        map.put(ProcessConstant.USER_ID, financeApply.getUserId());
        map.put(ProcessConstant.PROCESS_TYPE, processType);
        map.put(ProcessConstant.FINANCE_APPLY, financeApply);
        map.put(ProcessConstant.CHARGE_METHOD, financeApply.getChargeMethod());
        map.put(WfProcessConstant.PROCESS_NO, financeBusinessProcessService.getProcessNo(financeApply.getProcessInstanceId()));
        map.put(ProcessConstant.PROCESS_GOODS_INFO, product);
        map.put(WfProcessConstant.EXPENSE_FEE, expenseOrderDetailList);
        map.put(WfProcessConstant.PRODUCT_DATA, product);
        map.put(ProcessConstant.PLATFORM_EXPENSES_TYPE, PlatformExpensesEnum.PLAT_TYPE_ORDER_FINANCING.getCode());
        map.put(ProcessConstant.EXPENSE_FEE_NODE, ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode());
        return map;
    }

    /**
     * 数据赋值
     *
     * @param enterpriseQuotaSearchDTO 企业额度
     * @param product                  产品数据
     * @param financeApply             融资申请数据
     */
    private void financeData(EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, Product product, FinanceApply financeApply) {
        financeApply.setUserId(AuthUtil.getUserId());
        financeApply.setGoodsName(product.getGoodsName());
        financeApply.setDailyInterestRate(enterpriseQuotaSearchDTO.getDailyInterestRate());
        financeApply.setAnnualInterestRate(enterpriseQuotaSearchDTO.getAnnualInterestRate());
        financeApply.setServiceRate(enterpriseQuotaSearchDTO.getServiceRate());
        financeApply.setCapitalId(product.getCapitalId());
        Long personalUserId = AuthUtil.getClaimsParam(WebUtil.getRequest(), "personalUserId", Long.class);
        financeApply.setApplyUser(personalUserId);
        financeApply.setGoodsType(GoodsEnum.ORDER_FINANCING.getCode());
        financeApply.setIsDelay(product.getIsDelay());
        financeApply.setRepaymentType(product.getRepaymentType());
    }

    /**
     * 费用订单详情新增
     *
     * @param enterpriseQuotaSearchDTO 获取企业额度
     * @param financeApply             融资数据
     */
    private List<ExpenseOrderDetail> expenseOrderDetailSave(EnterpriseQuotaSerchDTO enterpriseQuotaSearchDTO, FinanceApply financeApply) {

        ExpenseOrderDTO expenseOrderDTO = new ExpenseOrderDTO();
        expenseOrderDTO.setGoodsId(financeApply.getGoodsId());
        expenseOrderDTO.setFinanceNo(financeApply.getFinanceNo());
        expenseOrderDTO.setAmount(financeApply.getAmount());
        expenseOrderDTO.setLoadTerm(financeApply.getLoadTerm());
        expenseOrderDTO.setComputeNode(ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode());
        expenseOrderDTO.setType(PlatformExpensesEnum.PLAT_TYPE_ORDER_FINANCING);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .loanPrincipal(financeApply.getAmount())
                .serviceRate(enterpriseQuotaSearchDTO.getServiceRate().divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_EVEN))
                .yearRate(enterpriseQuotaSearchDTO.getAnnualInterestRate().divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_EVEN))
                .loanDays(BigDecimal.valueOf(financeApply.getLoadTerm()))
                .build();
        expenseOrderDTO.setCustomerId(financeApply.getUserId());
        expenseOrderDTO.setBillExpenseNo(CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO));
        expenseOrderDTO.setExpenseRuleDTO(expenseRuleDTO);
        expenseOrderDTO.setPaymentStatus(BillPayStatusEnum.BILL_PENDING.getCode());
        return expenseOrderService.saveExpenseOrderDetail(expenseOrderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long loanMiddle(FinanceApplyHandlerDTO financeApplyHandlerDTO) {
        FinanceApply financeApply = financeApplyHandlerDTO.getFinanceApply();
        //判断是否已经存在融资金额
        BigDecimal amount = new BigDecimal(0);
        //修改下订单融资状态，防止一条融资订单被多次使用
        List<Long> orderDataIds = financeApplyHandlerDTO.getReceivableFinanceDTO().getSalesContractDetails().stream().map(SalesContractDetail::getSaleContractId).collect(Collectors.toList());
//        orderDataService.changeStatus(orderDataIds, OrderFinancingConstant.ORDER_DATA_STATUS_ONE);
        //判断是否能够执行贷中接口
        if (null != financeApply.getId()) {
            checkApplyFinanceApply(financeApply.getId());
            FinanceApply finance = financeApplyMapper.selectById(financeApply.getId());
            financeApply.setFinanceNo(finance.getFinanceNo());
            financeApply.setProcessInstanceId(finance.getProcessInstanceId());
            financeApply.setQuotaUseDetailsId(finance.getQuotaUseDetailsId());
            amount = new BigDecimal(finance.getAmount().toString());
        }
        //获取融资申请额度
        BigDecimal applyAmount = accessQuota(financeApplyHandlerDTO);
        //扣除授信额度
        limitDeduction(applyAmount, amount, financeApply.getGoodsId());
        //根据模板方法返回不同的值，来执行不同的方法
        if (templateMethod()) {
            //扣除销售合同额度
            subtractReceivableAmount(financeApply.getId(), financeApplyHandlerDTO.getReceivableFinanceDTO().getSalesContractDetails());
        }
        //查询企业额度信息
        EnterpriseQuotaSerchDTO enterpriseQuotaSerchDTO = selectEnterpriseQuota(financeApply.getGoodsId());
        //获取产品信息
        Product product = productDirector.detailBase(financeApply.getGoodsId());
        financeApply.setAmount(applyAmount);
        //判断是否已经存在融资数据，然后根据不同的放款方式走手动和自动融资申请
        if (null != financeApply.getId()) {
            if (GoodsEnum.AUTO_LENDING.getCode().equals(financeApplyHandlerDTO.getLendingMethod())) {
                //自动融资申请-修改
                saveFinanceApplyUpdate(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            } else {
                //手动融资申请-修改
                submitFinanceApplyUpdate(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            }
        } else {
            //生成融资编号
            String financeNo = CodeUtil.generateCode(CodeEnum.FINANCING_CODE);
            financeApply.setFinanceNo(financeNo);
            if (GoodsEnum.AUTO_LENDING.getCode().equals(financeApplyHandlerDTO.getLendingMethod())) {
                //自动融资申请-新增
                saveFinanceApply(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            } else {
                //手动融资申请-新增
                submitFinanceApplySave(financeApplyHandlerDTO, enterpriseQuotaSerchDTO, product);
            }
        }
        return financeApply.getId();
    }

    /**
     * 订单融资融资申请未提交
     * @param financeApply
     */
    private void rabbitFinaceApplyUnSubmmit(FinanceApply financeApply) {
        OrderFinancingGoods goods = goodsService.getById(financeApply.getGoodsId());
        Integer seconds = goodsTimingService.getSecondsByGoodsIdAndType(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_ORDER_FINANCING_APPLY.getCode());
        Map<String, Object> extendParam = new HashMap<>();
        extendParam.put("goodsType", goods.getType());
        rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
//                .bizNo("finace_apply_un_submmit_" + financeApply.getId().toString())
                .messageType(MessageTypeEnum.FINANCE_APPLY_UN_SUBMIT.getDesc())
                .seconds(seconds)
                .extendParam(extendParam)
                .msg(financeApply.getId().toString())
                .build());
    }

}

