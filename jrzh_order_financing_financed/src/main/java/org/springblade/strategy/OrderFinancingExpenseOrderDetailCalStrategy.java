package org.springblade.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.expense.handler.ExpenseOrderDetailCalHandler;
import org.springblade.expense.handler.ProductExpenseOrderDetailUtils;
import org.springblade.extension.dto.DelayCostCalculusDto;
import org.springblade.finance.dto.CostCalculusDto;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.utils.LoanUtils;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 费用条件构建策略类
 *
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-24  19:27
 * @Description:
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class OrderFinancingExpenseOrderDetailCalStrategy implements ExpenseOrderDetailCalHandler {
    @Override
    public ExpenseRuleDTO buildAdjustInterest(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfo(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTerm(repaymentInfoDTO, expenseRuleDTO);
        expenseRuleDTO.setRolloverDays(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setRolloverPeriods(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getExpenseRate()));
        return expenseRuleDTO;
    }

    /**
     * 分钟
     */
    private final static long ONE_MINUTE = 1000 * 60;
    /**
     * 天
     */
    private final static long ONE_DAY = ONE_MINUTE * 60 * 24;
    /**
     * 缓存，默认过期为1天 避免计算时多次调用
     * 还款计划缓存 用户id:<还款计划id:还款计划>
     */
//    private TimedCache<Long, Map<Long, LoanManageRepaymentPlan>> loanManageRepaymentPlanMap = CacheUtil.newTimedCache(ONE_DAY);
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final ProductDirector productDirector;
    private final IFinanceApplyService financeApplyService;

    @Override
    public GoodsEnum support() {
        return GoodsEnum.ORDER_FINANCING;
    }

    @Override
    public ExpenseRuleDTO buildAdvanceRepayment(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfo(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTerm(repaymentInfoDTO, expenseRuleDTO);
        expenseRuleDTO.setRolloverDays(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setRolloverPeriods(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getExpenseRate()));
        return expenseRuleDTO;
    }

    @Override
    public ExpenseRuleDTO buildAdvanceSettle(String req) {
        LoanInfoDTO loanInfoDTO = JSONUtil.toBean(req, LoanInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfo(loanInfoDTO, expenseRuleDTO);
        RepaymentInfoDTO currentRepaymentInfoDTO = loanInfoDTO.getCurrentRepaymentInfoDTO();
        if (ObjectUtil.isNotEmpty(currentRepaymentInfoDTO)) {
            //填充期数参数
            fullFieldLoanInfoTerm(currentRepaymentInfoDTO, expenseRuleDTO);
            expenseRuleDTO.setRolloverDays(BigDecimal.valueOf(currentRepaymentInfoDTO.getExpenseDay()));
            expenseRuleDTO.setRolloverPeriods(BigDecimal.valueOf(currentRepaymentInfoDTO.getExpenseDay()));
            expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(currentRepaymentInfoDTO.getExpenseRate()));
        }

        return expenseRuleDTO;
    }

    @Override
    public ExpenseRuleDTO reCalculate(String paramJson) {
        return null;
    }


    @Override
    public ExpenseRuleDTO buildFinanceApply(String req) {
        CostCalculusDto costCalculusDto = JSONUtil.toBean(req, CostCalculusDto.class);
        LocalDate startTime = costCalculusDto.getStartTime();
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        EnterpriseQuota enterpriseQuota = getEnterpriseQuota(costCalculusDto.getGoodsId(),
                costCalculusDto.getUserId(), costCalculusDto.getEnterpriseType());
        Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(), startTime, costCalculusDto.getTotalTerm());
//        Integer accumulateLoanDay = ProductExpenseOrderDetailUtils.getLoanDay(startTime, LocalDate.now());
        Integer accumulateLoanDay = loanDay;

        BigDecimal interestDay = ProductExpenseOrderDetailUtils.getInterestDayByLoanDay(product, accumulateLoanDay);
        Integer loanPeriods = GoodsEnum.TERM.getCode().equals(costCalculusDto.getLoadTermUnit()) ? costCalculusDto.getTotalTerm() : 1;
        StagRecordVO stagRecordVO = costCalculusDto.getStagRecordVO();
        //填充总体
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(interestDay)
                .loanDays(BigDecimal.valueOf(loanDay))
                .loanPeriods(BigDecimal.valueOf(loanPeriods))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getServiceRate()))
                .dayRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getDailyInterestRate()))
                .surplusPrincipalRepayment(costCalculusDto.getFinanceAmount())
                .overdueDays(BigDecimal.ZERO)
                .prepaymentDays(BigDecimal.ZERO)
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(enterpriseQuota.getAnnualInterestRate())).build();
        //填充本期参数
        if (ObjectUtil.isNotEmpty(stagRecordVO)) {
            Integer loanDay1 = ProductExpenseOrderDetailUtils.getLoanDay(stagRecordVO.getStartTime(), stagRecordVO.getRefundTime());
            //填充本期还款参数
            expenseRuleDTO.setTermSurplusPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(product.getInterestDay() + loanDay1));
            expenseRuleDTO.setCurrentRepaymentAmount(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(loanDay1));
            expenseRuleDTO.setTermOverDueDay(BigDecimal.ZERO);
            expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
        }
        return expenseRuleDTO;
    }

    private Product getProductByGoodsId(Long goodsId) {
        return productDirector.detailBase(goodsId);
    }

    private EnterpriseQuota getEnterpriseQuota(Long goodsId, Long userId, Integer enterpriseType) {
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseIdBase(goodsId, enterpriseType, userId);
        return enterpriseQuota;

    }

    @Override
    public ExpenseRuleDTO buildNormalRepayment(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfo(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTerm(repaymentInfoDTO, expenseRuleDTO);
        expenseRuleDTO.setRolloverDays(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setRolloverPeriods(BigDecimal.valueOf(repaymentInfoDTO.getExpenseDay()));
        expenseRuleDTO.setDelayRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getExpenseRate()));
        return expenseRuleDTO;
    }


    @Override
    public ExpenseRuleDTO buildExtensionApply(String req) {
        DelayCostCalculusDto costCalculusDto = JSONUtil.toBean(req, DelayCostCalculusDto.class);
        Integer accumulateLoanDay =ProductExpenseOrderDetailUtils.getLoanDay(costCalculusDto.getLoadTermUnit(),costCalculusDto.getStartTime(),costCalculusDto.getTotalTerm());
        Product product = getProductByGoodsId(costCalculusDto.getGoodsId());
        BigDecimal interestDay = ProductExpenseOrderDetailUtils.
                getInterestDayByLoanDay(product, accumulateLoanDay);

        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder()
                .feeAmount(BigDecimal.ZERO)
                .interestAccrualDays(interestDay)
                .loanDays(BigDecimal.valueOf(costCalculusDto.getOldLoanDays()))
                .loanPeriods(BigDecimal.valueOf(costCalculusDto.getTotalTerm()))
                .principalRepayment(costCalculusDto.getFinanceAmount())
                .loanPrincipal(costCalculusDto.getFinanceAmount())

                .rolloverDays(BigDecimal.valueOf(costCalculusDto.getTotalTerm()))
                .rolloverPeriods(BigDecimal.valueOf(costCalculusDto.getTotalTerm()))
                .serviceRate(ProductExpenseOrderDetailUtils.percentToBigNum(costCalculusDto.getServiceRate()))
                .dayRate(LoanUtils.calculationDayRate(costCalculusDto.getAnnualInterestRate()))
                .surplusPrincipalRepayment(costCalculusDto.getFinanceAmount())
                .overdueDays(BigDecimal.ZERO)
                .prepaymentDays(BigDecimal.ZERO)
                .delayRate(ProductExpenseOrderDetailUtils.percentToBigNum(costCalculusDto.getAnnualInterestRate()))
                .yearRate(ProductExpenseOrderDetailUtils.percentToBigNum(costCalculusDto.getOldLoanRate())).build();
        //填充本期参数
        StagRecordVO stagRecordVO = costCalculusDto.getStagRecordVO();
        if (ObjectUtil.isNotEmpty(stagRecordVO)) {
            Integer loanDay1 = ProductExpenseOrderDetailUtils.getLoanDay(stagRecordVO.getStartTime(), stagRecordVO.getRefundTime());
            //填充本期还款参数
            expenseRuleDTO.setTermSurplusPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermPrincipalRepayment(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(product.getInterestDay() + loanDay1));
            expenseRuleDTO.setCurrentRepaymentAmount(stagRecordVO.getMonthlyPrincipal());
            expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(loanDay1));
            expenseRuleDTO.setTermOverDueDay(BigDecimal.ZERO);
            expenseRuleDTO.setTermPrepaymentDays(BigDecimal.ZERO);
        }
        return expenseRuleDTO;
    }

    @Override
    public ExpenseRuleDTO buildOverdueRepayment(String req) {
        RepaymentInfoDTO repaymentInfoDTO = JSONUtil.toBean(req, RepaymentInfoDTO.class);
        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO.builder().build();
        //填充整体借据参数
        fullFieldLoanInfo(repaymentInfoDTO, expenseRuleDTO);
        //填充期数参数
        fullFieldLoanInfoTerm(repaymentInfoDTO, expenseRuleDTO);
        return expenseRuleDTO;
    }

    /**
     * 填充期数字段 动态填充
     *
     * @param repaymentInfoDTO
     * @param expenseRuleDTO
     */
    public void fullFieldLoanInfoTerm(RepaymentInfoDTO repaymentInfoDTO, ExpenseRuleDTO expenseRuleDTO) {
        expenseRuleDTO.setTermPrincipalRepayment(repaymentInfoDTO.getPrincipal());
        expenseRuleDTO.setCurrentRepaymentAmount(repaymentInfoDTO.getAmount());
        expenseRuleDTO.setTermInterestAccrualDays(BigDecimal.valueOf(repaymentInfoDTO.getInterestAccrualDays()));
        expenseRuleDTO.setTermLoanDays(BigDecimal.valueOf(repaymentInfoDTO.getLoanDay()));
        expenseRuleDTO.setTermSurplusPrincipalRepayment(repaymentInfoDTO.getSubPrincipal());
        expenseRuleDTO.setTermOverDueDay(BigDecimal.valueOf(repaymentInfoDTO.getOverdueDays()));
        expenseRuleDTO.setTermPrepaymentDays(BigDecimal.valueOf(repaymentInfoDTO.getPrepaymentDays()));

    }

    /**
     * 填充总体借据字段 动态填充
     */
    public void fullFieldLoanInfo(RepaymentInfoDTO repaymentInfoDTO, ExpenseRuleDTO expenseRuleDTO) {
        LoanManageIou loanManageIou = repaymentInfoDTO.getLoanManageIou();
        BigDecimal yearRate = ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getAnnualInterestRate());
        BigDecimal dayRate = LoanUtils.calculationDayRate(yearRate);

        expenseRuleDTO.setPrincipalRepayment(loanManageIou.getIouAmount());
        expenseRuleDTO.setLoanPrincipal(loanManageIou.getIouAmount());
//        expenseRuleDTO.setPurchasePrices(null);
        expenseRuleDTO.setSurplusPrincipalRepayment(repaymentInfoDTO.getSubTotalPrincipal());
        expenseRuleDTO.setInterestAccrualDays(BigDecimal.valueOf(repaymentInfoDTO.getTotalInterestAccrualDays()));
        expenseRuleDTO.setLoanDays(BigDecimal.valueOf(repaymentInfoDTO.getLoanDay()));
        expenseRuleDTO.setLoanPeriods(BigDecimal.valueOf(repaymentInfoDTO.getTerm()));
        expenseRuleDTO.setPrepaymentDays(BigDecimal.valueOf(repaymentInfoDTO.getTotalPrepaymentDays()));
        expenseRuleDTO.setServiceRate(ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getServiceFeeRate()));
        expenseRuleDTO.setDayRate(dayRate);
        expenseRuleDTO.setYearRate(yearRate);
//        expenseRuleDTO.setMarginRatio(null);
        expenseRuleDTO.setTaxRate(BigDecimal.ONE);
        expenseRuleDTO.setOverdueDays(BigDecimal.valueOf(repaymentInfoDTO.getTotalOverdueDays()));
        expenseRuleDTO.setCurrentRepaymentAmount(repaymentInfoDTO.getAmount());
    }

    /**
     * 填充总体借据字段 动态填充
     *
     * @param loanInfoDTO    借款信息
     * @param expenseRuleDTO 填充字段
     */
    public void fullFieldLoanInfo(LoanInfoDTO loanInfoDTO, ExpenseRuleDTO expenseRuleDTO) {
        BigDecimal yearRate = ProductExpenseOrderDetailUtils.percentToBigNum(loanInfoDTO.getAnnualInterestRate());
        BigDecimal dayRate = LoanUtils.calculationDayRate(yearRate);

        expenseRuleDTO.setPrincipalRepayment(loanInfoDTO.getIouAmount());
        expenseRuleDTO.setLoanPrincipal(loanInfoDTO.getIouAmount());
//        expenseRuleDTO.setPurchasePrices(null);
        expenseRuleDTO.setSurplusPrincipalRepayment(loanInfoDTO.getSubPrincipal());
        expenseRuleDTO.setInterestAccrualDays(BigDecimal.valueOf(loanInfoDTO.getInterestDay()));
        expenseRuleDTO.setLoanDays(BigDecimal.valueOf(loanInfoDTO.getLoanDays()));
        expenseRuleDTO.setLoanPeriods(BigDecimal.valueOf(loanInfoDTO.getRepaymentInfoList().size()));
        expenseRuleDTO.setPrepaymentDays(BigDecimal.valueOf(loanInfoDTO.getPrepaymentDays()));
        expenseRuleDTO.setServiceRate(ProductExpenseOrderDetailUtils.percentToBigNum(loanInfoDTO.getServiceFeeRate()));
        expenseRuleDTO.setDayRate(dayRate);
        expenseRuleDTO.setYearRate(yearRate);
//        expenseRuleDTO.setMarginRatio(null);
        expenseRuleDTO.setTaxRate(BigDecimal.ONE);
        expenseRuleDTO.setOverdueDays(BigDecimal.valueOf(loanInfoDTO.getOverDueDay()));
        expenseRuleDTO.setCurrentRepaymentAmount(loanInfoDTO.getCurrentRepaymentAmount());
    }

}
