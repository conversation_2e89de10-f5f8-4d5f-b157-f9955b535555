/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pledge.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.mapper.BladeMapper;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.procurement.vo.AgentGoodsVO;

import java.util.List;

/**
 * 代采产品表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
public interface PledgeGoodsMapper extends BladeMapper<AgentGoods> {

    /**
     * 自定义分页
     *
     * @param page
     * @param agentGoods
     * @return
     */
    List<AgentGoodsVO> selectAgentGoodsPage(IPage page, AgentGoodsVO agentGoods);

}
