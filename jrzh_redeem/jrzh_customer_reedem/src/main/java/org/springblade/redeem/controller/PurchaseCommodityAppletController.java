package org.springblade.redeem.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.product.common.vo.CommodityListVO;
import org.springblade.redeem.service.ICustomerCommodityListService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序 融资端-商品列表 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_COMMODITY + CommonConstant.WEB_APPLET + "/purchaseCommodity")
@Api(value = "商品列表", tags = "接口")
public class PurchaseCommodityAppletController {

    private final ICustomerCommodityListService customerCommodityListService;

    @GetMapping("/pageCommodity")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "", notes = "传入commodityList")
    public R<IPage<CommodityListVO>> pageCommodity(Long goodsId, Query query, CommodityListVO commodityListVO) {
        IPage<CommodityListVO> pages = customerCommodityListService.pageCommodity(goodsId, query, commodityListVO);
        return R.data(pages);
    }
}
