/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.redeem.entity.RedeemExpense;
import org.springblade.redeem.mapper.RedeemExpenseMapper;
import org.springblade.redeem.service.IRedeemExpenseService;
import org.springblade.redeem.vo.RedeemExpenseVO;
import org.springframework.stereotype.Service;

/**
 * 赎货费用信息表（只做显示使用） 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-06
 */
@Service
public class RedeemExpenseServiceImpl extends BaseServiceImpl<RedeemExpenseMapper, RedeemExpense> implements IRedeemExpenseService {

	@Override
	public IPage<RedeemExpenseVO> selectRedeemExpensePage(IPage<RedeemExpenseVO> page, RedeemExpenseVO redeemExpense) {
		return page.setRecords(baseMapper.selectRedeemExpensePage(page, redeemExpense));
	}

}
