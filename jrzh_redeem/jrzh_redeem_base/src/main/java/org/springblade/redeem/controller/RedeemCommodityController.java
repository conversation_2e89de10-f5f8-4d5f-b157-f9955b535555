/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.redeem.entity.RedeemCommodity;
import org.springblade.redeem.service.IRedeemCommodityService;
import org.springblade.redeem.vo.RedeemCommodityVO;
import org.springblade.redeem.wrapper.RedeemCommodityWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 赎货关联货物表 控制器
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_REDEEM + CommonConstant.WEB_FRONT + "/redeemCommodity")
@Api(value = "赎货关联货物表", tags = "赎货关联货物表接口")
public class RedeemCommodityController extends BladeController {

    private final IRedeemCommodityService redeemCommodityService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入redeemCommodity")
    @PreAuth("hasPermission('redeemCommodity:redeemCommodity:detail') or hasRole('administrator')")
    public R<RedeemCommodityVO> detail(RedeemCommodity redeemCommodity) {
        RedeemCommodity detail = redeemCommodityService.getOne(Condition.getQueryWrapper(redeemCommodity));
        return R.data(RedeemCommodityWrapper.build().entityVO(detail));
    }

    /**
     * 分页 赎货关联货物表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入redeemCommodity")
    @PreAuth("hasPermission('redeemCommodity:redeemCommodity:list') or hasRole('administrator')")
    public R<IPage<RedeemCommodityVO>> list(RedeemCommodity redeemCommodity, Query query) {
        IPage<RedeemCommodity> pages = redeemCommodityService.page(Condition.getPage(query), Condition.getQueryWrapper(redeemCommodity));
        return R.data(RedeemCommodityWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 赎货关联货物表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入redeemCommodity")
    @PreAuth("hasPermission('redeemCommodity:redeemCommodity:page') or hasRole('administrator')")
    public R<IPage<RedeemCommodityVO>> page(RedeemCommodityVO redeemCommodity, Query query) {
        IPage<RedeemCommodityVO> pages = redeemCommodityService.selectRedeemCommodityPage(Condition.getPage(query), redeemCommodity);
        return R.data(pages);
    }

    /**
     * 新增 赎货关联货物表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入redeemCommodity")
    @PreAuth("hasPermission('redeemCommodity:redeemCommodity:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody RedeemCommodity redeemCommodity) {
        return R.status(redeemCommodityService.save(redeemCommodity));
    }

    /**
     * 修改 赎货关联货物表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入redeemCommodity")
    @PreAuth("hasPermission('redeemCommodity:redeemCommodity:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody RedeemCommodity redeemCommodity) {
        return R.status(redeemCommodityService.updateById(redeemCommodity));
    }

    /**
     * 新增或修改 赎货关联货物表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入redeemCommodity")
    @PreAuth("hasPermission('redeemCommodity:redeemCommodity:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody RedeemCommodity redeemCommodity) {
        return R.status(redeemCommodityService.saveOrUpdate(redeemCommodity));
    }


    /**
     * 删除 赎货关联货物表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('redeemCommodity:redeemCommodity:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(redeemCommodityService.deleteLogic(Func.toLongList(ids)));
    }


}
