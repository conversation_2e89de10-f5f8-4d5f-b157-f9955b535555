<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.redeem.mapper.RedeemCargoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="redeemCargoResultMap" type="org.springblade.redeem.entity.RedeemCargo">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="stock_id" property="stockId"/>
        <result column="redeem_no" property="redeemNo"/>
        <result column="financing_no" property="financingNo"/>
        <result column="num" property="num"/>
        <result column="extract_type" property="extractType"/>
        <result column="extract_id" property="extractId"/>
        <result column="address_id" property="addressId"/>
        <result column="expense_info_id" property="expenseInfoId"/>
        <result column="expense_order_id" property="expenseOrderId"/>
        <result column="repayment_record_id" property="repaymentRecordId"/>
    </resultMap>


    <select id="selectRedeemCargoPage" resultMap="redeemCargoResultMap">
        select * from jrzh_redeem_cargo where is_deleted = 0
    </select>

</mapper>
