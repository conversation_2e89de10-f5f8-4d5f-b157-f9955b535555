/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.controller.font;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.redeem.dto.ChangStatusDTO;
import org.springblade.redeem.dto.RedeemCargoCalculationDTO;
import org.springblade.redeem.dto.RedeemCargoDTO;
import org.springblade.redeem.dto.RedeemCargoQueryDTO;
import org.springblade.redeem.service.IRedeemCargoService;
import org.springblade.redeem.vo.RedeemCargoCalculationVO;
import org.springblade.redeem.vo.RedeemCargoVO;
import org.springblade.redeem.vo.SubLedgerInfoVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 赎货表 控制器
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_REDEEM + CommonConstant.WEB_FRONT + "/cargo")
@Api(value = "赎货表", tags = "赎货表接口")
public class RedeemCargoFontController extends BladeController {

    private final IRedeemCargoService redeemCargoService;


    /**
     * 查询赎货费用单及关联相关的账户
     */
    @GetMapping("/getRedeemExpenseAndAccount")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入redeemCargo")
    public R<SubLedgerInfoVO> getRedeemExpenseAndAccount(String redeemNo) {
        return R.data(redeemCargoService.getRedeemExpenseAndAccount(redeemNo));
    }

    /**
     * 分页 赎货表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入redeemCargo")
    public R<IPage<RedeemCargoVO>> list(RedeemCargoQueryDTO redeemCargo, Query query) {
        redeemCargo.setCreateUser(AuthUtil.getUserId());
        return R.data(redeemCargoService.queryPage(redeemCargo, query));
    }

    /**
     * 发起赎货申请
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    public R<Boolean> saveRedeem(@Valid @RequestBody RedeemCargoDTO redeemCargoDTO) {
        return R.status(redeemCargoService.saveRedeem(redeemCargoDTO));
    }

//    /**
//     * 发起赎货申请确认
//     */
//    @PostMapping("/redeemConfirm")
//    @ApiOperationSupport(order = 4)
//    public R<Boolean> redeemConfirm(@Valid @RequestBody RedeemCargoConfirmDTO redeemCargoConfirmDTO) {
//        return R.status(redeemCargoService.redeemConfirm(redeemCargoConfirmDTO));
//    }


    /**
     * 发起赎货申请计算费用
     */
    @PostMapping("/calculationRedeem")
    public R<RedeemCargoCalculationVO> calculationRedeem(@Valid @RequestBody RedeemCargoCalculationDTO redeemCargoCalculationDTO) {
        return R.data(redeemCargoService.calculationRedeem(redeemCargoCalculationDTO));
    }


//    /**
//     * 发起申请页面信息
//     */
//    @GetMapping("/applyInfo")
//    public R<WarehouseDetailsChildVO> applyInfo(Long warehouseId) {
//        return R.data(redeemCargoService.fontWarehousDetail(warehouseId));
//    }
//
//

    /**
     * 有异议/确认收货
     */
    @PostMapping("/changeStatus")
    public R<Boolean> changeStatus(@Valid @RequestBody ChangStatusDTO changStatusDTO) {
        return R.status(redeemCargoService.changeStatus(changStatusDTO));
    }

}
