<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.redeem.mapper.RedeemUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="redeemUserResultMap" type="org.springblade.redeem.entity.RedeemUser">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="cargo_id" property="cargoId"/>
        <result column="username" property="username"/>
        <result column="id_card" property="idCard"/>
        <result column="phone" property="phone"/>
        <result column="license_plate" property="licensePlate"/>
        <result column="arrive_time" property="arriveTime"/>
    </resultMap>


    <select id="selectRedeemUserPage" resultMap="redeemUserResultMap">
        select * from jrzh_redeem_user where is_deleted = 0
    </select>

</mapper>
