/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.redeem.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.redeem.entity.RedeemObjection;
import org.springblade.redeem.vo.RedeemObjectionVO;

/**
 * 赎货异议处理表 服务类
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
public interface IRedeemObjectionService extends BaseService<RedeemObjection> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param redeemObjection
	 * @return
	 */
	IPage<RedeemObjectionVO> selectRedeemObjectionPage(IPage<RedeemObjectionVO> page, RedeemObjectionVO redeemObjection);

}
