package org.springblade.lianQiaoapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/9
 * @description
 */
@Data
public class lianQiaoOrderQueryParam {
	@ApiModelProperty(value = "客户唯一标识")
	private String creditCode;

	@ApiModelProperty(value = "客户名称")
	private String enterpriseName;

	@ApiModelProperty(value = "订单状态")
	private String orderState;

	@ApiModelProperty(value = "支付方式")
	private String paymentCode;


}
