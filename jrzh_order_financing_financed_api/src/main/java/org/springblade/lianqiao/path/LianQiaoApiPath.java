package org.springblade.lianqiao.path;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/6/3
 * @description
 */
@Getter
@AllArgsConstructor
public enum LianQiaoApiPath {
	/**
	 * 支付结果通知
	 */
	PAY_RESULT("/v3/business/admin/pay_result","支付结果通知"),
	/**
	 * B2B订单信息拉取
	 */
	ORDER_INFO_DETAIL("/openapi/order","B2B订单信息拉取"),
	/**
	 * 查询订单接口
	 */
	QUERY_ORDER("/openapi/order/order", "查询订单接口"),
	/**
	 * 融资状态变更推送
	 */
	FINANCE_STATUS_CHANGE_SEND("/openapi/order/finance/callback", "融资状态变更推送"),

	;
	private final String path;

	private final String desc;

	public static String getDescByPath(String path) {
		for (LianQiaoApiPath pathEnum : LianQiaoApiPath.values()) {
			if (pathEnum.getPath().equals(path)) {
				return pathEnum.getDesc();
			}
		}
		return null;
	}
}
