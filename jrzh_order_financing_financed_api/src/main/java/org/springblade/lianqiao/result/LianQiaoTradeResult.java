package org.springblade.lianqiao.result;

import lombok.Data;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-07-18  15:06
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class LianQiaoTradeResult {
	/**
	 * 数据
	 */
	private String result;
	/**
	 * 消息
	 */
	private String message;
	/**
	 * 状态码
	 */
	private Integer code;
	/**
	 * 时间戳
	 */
	private Integer timestamp;
	/**
	 * 是否成功
	 */
	private Boolean success;
}
