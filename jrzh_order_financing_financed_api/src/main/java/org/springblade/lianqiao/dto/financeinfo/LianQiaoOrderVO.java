package org.springblade.lianqiao.dto.financeinfo;

import lombok.Data;

import java.util.List;

/**
 * @Author: z<PERSON>gchuangkai
 * @CreateTime: 2023-07-18  18:11
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class LianQiaoOrderVO {
	private String createTime; // 下单时间
	private double flowPrice; // 总价格
	private String memberName; // 买家名称
	private List<LianQiaoOrderItem> orderItems; // 订单商品
	private String orderStatus; // 订单状态
	private String payStatus; // 付款状态
	private String paymentMethod; // 支付方式
	private String paymentTime; // 支付时间
	private String sn; // 订单编号
	private String storeId; // 供应商ID
	private String storeName; // 供应商名称
}
