package org.springblade.lianqiao.dto.financeresult;

import lombok.Data;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-07-18  18:29
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class FinancingInfoResult {
	private String mchid; // 商户ID
	private String out_order_no; // B2B平台订单号
	private String status;
	private String total_amount; // 总融资金额
	private String service_introduction; // 融资信息，失败成功等
	private String paid_time; // 融资成功时间戳
	private String transaction_id; // 融资交易ID
}
