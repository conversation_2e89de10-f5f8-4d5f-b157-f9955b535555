package org.springblade.lianqiao.dto.orderinfo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-07-18  17:52
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class TradeOrderInfo {
	/**
	 * ID
	 */
	private String id;

	/**
	 * 创建者
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 更新者
	 */
	private String updateBy;

	/**
	 * 更新时间
	 */
	private String updateTime;

	/**
	 * 删除标记
	 */
	private boolean deleteFlag;

	/**
	 * 订单号
	 */
	private String sn;

	/**
	 * 商家ID
	 */
	private String storeId;

	/**
	 * 商家名称
	 */
	private String storeName;

	/**
	 * 买家ID
	 */
	private String memberId;

	/**
	 * 买家名称
	 */
	private String memberName;

	/**
	 * 订单状态
	 */
	private String orderStatus;

	/**
	 * 付款状态
	 */
	private String payStatus;

	/**
	 * 发货状态
	 */
	private String deliverStatus;

	/**
	 * 付款方式
	 */
	private String paymentMethod;

	/**
	 * 付款时间
	 */
	private String paymentTime;

	/**
	 * 总价格
	 */
	private BigDecimal flowPrice;

	/**
	 * 商品价格
	 */
	private BigDecimal goodsPrice;

	/**
	 * 运费价格
	 */
	private BigDecimal freightPrice;

	/**
	 * 商品数量
	 */
	private Integer goodsNum;

	/**
	 * 备注信息
	 */
	private String remark;
//	/**
//	 * 活动优惠总金额
//	 */
//	private BigDecimal activityDiscountAmount;
//
//	/**
//	 * 信用账单信息
//	 */
//	private LianQiaoBillInfo billInfo;
//
//	/**
//	 * 取消原因
//	 */
//	private String cancelReason;
//
//	/**
//	 * 取消备注
//	 */
//	private String cancelRemark;
//
//	/**
//	 * 发起时间
//	 */
//	private String createTime;
//
//	/**
//	 * 发货类型
//	 */
//	private int deliverType;
//
//	/**
//	 * 发货类型，1-齐套发货；2-分批发货（若选择分批发货，则运费需要到付）
//	 */
//	private String deliverTypeValue;
//
//	/**
//	 * 是否评价:1.未评价,2.部分评价,3.全部评价
//	 */
//	private int evaluateState;
//
//	/**
//	 * 是否评价:1.未评价,2.部分评价,3.全部评价
//	 */
//	private String evaluateStateValue;
//
//	/**
//	 * 期望交货日期
//	 */
//	private String expectDeliverTime;
//
//	/**
//	 * 物流费用
//	 */
//	private BigDecimal expressFee;
//
//	/**
//	 * 产品金额，产品单价*数量
//	 */
//	private BigDecimal goodsAmount;
//
//	/**
//	 * 是否已生成电子面单 1 - 已生成 2 - 未生成
//	 */
//	private int isGenerateFacesheet;
//
//	/**
//	 * 锁定状态：0-是正常, 大于0是锁定状态，用户申请退款或退货时锁定状态加1，处理完毕减1。锁定后不能操作订单
//	 */
//	private int lockState;
//
//	/**
//	 * 买家ID
//	 */
//	private int memberId;
//
//	/**
//	 * 买家name
//	 */
//	private String memberName;
//
//	/**
//	 * 真实姓名
//	 */
//	private String memberTrueName;
//
//	/**
//	 * 订单金额（不包含运费）
//	 */
//	private BigDecimal moneyAmount;
//
//	/**
//	 * 订单总金额(用户需要支付的金额)，等于产品总金额＋运费-优惠金额
//	 */
//	private BigDecimal orderAmount;
//
//	/**
//	 * 订单发货信息(齐套发货)
//	 */
//	private LianQiaoOrderDeliver orderDeliver;
//
//	/**
//	 * 订单发货信息(分批发货)
//	 */
//	private List<LianQiaoOrderDeliver> orderDeliverList;
//
//	/**
//	 * 订单ID
//	 */
//	private int orderId;
//
//	/**
//	 * 订单日志信息
//	 */
//	private List<LianQiaoOrderLog> orderLogList;
//
//	/**
//	 * 订单操作信息（备注、星级、改价）
//	 */
//	private List<LianQiaoOrderLog> orderOperateList;
//
//	/**
//	 * 订单货品列表
//	 */
//	private List<LianQiaoOrderProductListVO> orderProductList;
//
//	/**
//	 * 用户订单备注
//	 */
//	private String orderRemark;
//
//	/**
//	 * 订单号
//	 */
//	private String orderSn;
//
//	/**
//	 * 订单状态：0-已取消；10-提交订单待审核；20-审核通过待支付；21-审核拒绝订单结束；30-待发货；40-部分发货待收货；41-全部发货待收货；50-已完成；60-已关闭
//	 */
//	private int orderState;
//
//	/**
//	 * 订单状态：0-已取消；10-提交订单待审核；20-审核通过待支付；21-审核拒绝订单结束；30-待发货；40-部分发货待收货；41-全部发货待收货；50-已完成；60-已关闭
//	 */
//	private String orderStateValue;
//
//	/**
//	 * 支付单号
//	 */
//	private String paySn;
//
//	/**
//	 * 支付成功时间
//	 */
//	private String payTime;
//
//	/**
//	 * 支付方式code, 参考OrderPaymentConst类
//	 */
//	private String paymentCode;
//
//	/**
//	 * 支付方式名称，参考OrderPaymentConst类
//	 */
//	private String paymentName;
//
//	/**
//	 * 汇款凭证
//	 */
//	private String paymentVoucher;
//
//	/**
//	 * 促销信息
//	 */
//	private List<LianQiaoPromotionInfo> promotionInfo;
//
//	/**
//	 * 采购单号
//	 */
//	private String purchaseSn;
//
//	/**
//	 * 收货人详细地址
//	 */
//	private String receiverAddress;
//
//	/**
//	 * 省市区组合
//	 */
//	private String receiverAreaInfo;
//
//	/**
//	 * 收货人手机号
//	 */
//	private String receiverMobile;
//
//	/**
//	 * 收货人
//	 */
//	private String receiverName;
//
//	/**
//	 * 审核拒绝原因
//	 */
//	private String refuseReason;
//
//	/**
//	 * 剩余支付时间
//	 */
//	private String remainderPayTime;
//
//	/**
//	 * 供应商客服电话
//	 */
//	private String servicePhone;
//
//	/**
//	 * 供应商ID
//	 */
//	private String storeId;
//
//	/**
//	 * 供应商名称
//	 */
//	private String storeName;
//
//	/**
//	 * 第三方支付交易流水号
//	 */
//	private String tradeSn;
}
