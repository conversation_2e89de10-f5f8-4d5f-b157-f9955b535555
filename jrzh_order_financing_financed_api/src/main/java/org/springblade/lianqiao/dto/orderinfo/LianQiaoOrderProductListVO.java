package org.springblade.lianqiao.dto.orderinfo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-07-18  17:57
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class LianQiaoOrderProductListVO {
	/**
	 * 售后按钮，100-退款（供应商未发货），200-退款（供应商发货,买家未收货），300-申请售后，401-退款中，402-退款完成,403-换货中，404-换货完成
	 */
	private int afsButton;

	/**
	 * 售后按钮，100-退款（供应商未发货），200-退款（供应商发货,买家未收货），300-申请售后，401-退款中，402-退款完成,403-换货中，404-换货完成
	 */
	private String afsButtonValue;

	/**
	 * 售后单号,查看售后详情用
	 */
	private String afsSn;

	/**
	 * 售后状态：100-买家申请仅退款；101-买家申请退货退款；102-买家退货给供应商；200-供应商处理仅退款申请；201-供应商处理退货退款申请；300-退款完成；301-退款关闭（供应商拒绝退款申请）
	 */
	private int afsState;

	/**
	 * 售后状态：100-买家申请仅退款；101-买家申请退货退款；102-买家退货给供应商；200-供应商处理仅退款申请；201-供应商处理退货退款申请；300-退款完成；301-退款关闭（供应商拒绝退款申请）
	 */
	private String afsStateValue;

	/**
	 * 购买数量
	 */
	private int buyNum;

	/**
	 * 已签合同数量
	 */
	private int contractNum;

	/**
	 * 订单创建时间
	 */
	private String createTime;

	/**
	 * 会员自定义产品名称
	 */
	private String customProductName;

	/**
	 * 会员自定义产品型号
	 */
	private String customSpec;

	/**
	 * 发货状态，1-未发货；2-部分发货；3-全部发货
	 */
	private int deliverState;

	/**
	 * 发货状态，1-未发货；2-部分发货；3-全部发货
	 */
	private String deliverStateValue;

	/**
	 * 最终购买价格
	 */
	private BigDecimal finalPrice;

	/**
	 * 订单完成时间
	 */
	private String finishTime;

	/**
	 * 产品id
	 */
	private String goodsId;

	/**
	 * 产品图片
	 */
	private String goodsImage;

	/**
	 * 产品名称
	 */
	private String goodsName;

	/**
	 * 货品单价，与订单表中goods_amount对应
	 */
	private BigDecimal goodsPrice;

	/**
	 * 规格型号
	 */
	private String goodsSpec;

	/**
	 * 已开票数量
	 */
	private int invoiceNum;

	/**
	 * 是否评价:0-未评价，1-已评价
	 */
	private int isComment;

	/**
	 * 会员ID
	 */
	private String memberId;

	/**
	 * 价格红线
	 */
	private BigDecimal minPrice;

	/**
	 * 订单货品明细金额
	 */
	private BigDecimal moneyAmount;

	/**
	 * 订货编码，后台生成，全局唯一
	 */
	private String orderCode;

	/**
	 * 订单货品id
	 */
	private String orderProductId;

	/**
	 * 订单号
	 */
	private String orderSn;

	/**
	 * 物料编码
	 */
	private String productCode;

	/**
	 * 货品id
	 */
	private String productId;

	/**
	 * 采购单号
	 */
	private String purchaseSn;

	/**
	 * 退货数量，默认为0
	 */
	private int returnNumber;

	/**
	 * 已发货数量
	 */
	private int shippedNum;

	/**
	 * 规格值的ID，用逗号分隔
	 */
	private String specValueIds;

	/**
	 * 规格值
	 */
	private String specValues;

	/**
	 * 供应商ID
	 */
	private String storeId;

	/**
	 * 供应商名称
	 */
	private String storeName;
}
