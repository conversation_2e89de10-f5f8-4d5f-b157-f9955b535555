/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 经销商历史订单数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2022-11-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinanceHeightCompanyOrderHistoryDTO extends FinanceHeightCompanyOrderHistory {
	private static final long serialVersionUID = 1L;

	/**
	 * 历史商品信息原始
	 */
	@JsonIgnore
	@JSONField(name = "orderProductList")
	@ApiModelProperty(value = "历史商品信息JSON")
	private String products;

}
