/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.pubproduct.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantContextHolder;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.entity.ProductManager;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.moudle.pubproduct.service.IProductConfigInfoHandler;
import org.springblade.product.moudle.pubproduct.service.IProductConfigSearchHandler;
import org.springblade.product.moudle.pubproduct.service.IProductManagerService;
import org.springblade.resource.cache.ParamCache;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductDirector {
    private final ProductFactory productFactory;
    private final List<IProductConfigInfoHandler> productConfigInfoHandlers;
    private final List<IProductConfigSearchHandler> productConfigSearchHandlers;
    private final IProductManagerService productManagerService;


    /**
     * 获取配置类
     *
     * @param goodsId
     * @param configType GoodsEnum
     * @param clazz
     * @param <T>        查询条件
     * @return
     */
    public <T, Q> List<T> listConfig(List<Long> goodsId, Integer configType, Class<T> clazz, LambdaQueryWrapper<Q> queryWrapper) {
        for (IProductConfigInfoHandler productConfigInfoHandler : productConfigInfoHandlers) {
            if (configType.equals(productConfigInfoHandler.support())) {
                return JSONUtil.toList(listConfig(goodsId, configType, queryWrapper), clazz);
            }
        }
        return null;
    }

    /**
     * 获取配置类
     *
     * @param goodsId
     * @param configType GoodsEnum
     * @param clazz
     * @param <T>
     * @return
     */
    public <T, Q> T getConfig(Long goodsId, Integer configType, Class<T> clazz, LambdaQueryWrapper<Q> queryWrapper) {
        for (IProductConfigSearchHandler productSearchHandler : productConfigSearchHandlers) {
            if (configType.equals(productSearchHandler.support())) {
                JSONArray array = listConfig(Collections.singletonList(goodsId), configType, queryWrapper);
                if (CollUtil.isEmpty(array)) {
                    return null;
                }
                if (array.size() > 1) {
                    throw new TooManyResultsException();
                }
                return BeanUtil.copy(array.get(0), clazz);
            }
        }
        return null;
    }

    /**
     * 获取配置类
     *
     * @param goodsId
     * @param configType GoodsEnum
     * @param clazz
     * @param <T>
     * @return
     */
    public <T, Q> List<T> listConfig(Long goodsId, Integer configType, Class<T> clazz, LambdaQueryWrapper<Q> queryWrapper) {

        for (IProductConfigSearchHandler productSearchHandler : productConfigSearchHandlers) {
            if (configType.equals(productSearchHandler.support())) {
                return JSONUtil.toList(listConfig(Collections.singletonList(goodsId), configType, queryWrapper), clazz);
            }
        }
        return null;
    }

    /**
     * 获取配置类
     *
     * @param goodsId
     * @param configType   Goods
     * @param queryWrapper 查询条件
     * @return
     */
    private JSONArray listConfig(List<Long> goodsId, Integer configType, LambdaQueryWrapper queryWrapper) {
        for (IProductConfigSearchHandler productSearchHandler : productConfigSearchHandlers) {
            if (configType.equals(productSearchHandler.support())) {
                return JSONUtil.parseArray(productSearchHandler.configInfo(goodsId, queryWrapper));
            }
        }
        return null;
    }

    /**
     * 根据产品集合获取所有产品
     *
     * @param goodsId
     * @return
     */
    public List<Product> selectList(List<Long> goodsId) {
        return selectList(new HashMap<>(), goodsId);
    }

    /**
     * 根据产品集合获取所有产品
     *
     * @param goodsId
     * @return
     */
    public List<Product> selectList(List<Long> goodsId, String tenantId) {
        return selectList(new HashMap<>(), goodsId, tenantId);
    }

    /**
     * 根据产品集合获取所有产品
     *
     * @param product 查询条件
     * @param goodsId 产品id
     * @return
     */
    public List<Product> selectList(Map<String, Object> product, List<Long> goodsId) {
        List<Product> list = new ArrayList<>();
        Map<Integer, List<Long>> typeMap = productManagerService.mapInType(goodsId, getAllType());
        for (Integer type : typeMap.keySet()) {
            List<Product> productList = null;
            try {
                productList = productFactory.instance(type).selectList(product, typeMap.get(type));
            } catch (Exception e) {
                //产品查询不到 不抛出异常
                e.printStackTrace();
            }
            if (CollUtil.isNotEmpty(productList)) {
                list.addAll(productList);
            }
        }
        return list;
    }

    public List<Product> selectList(Map<String, Object> product, List<Long> goodsId, String tenantId) {
        List<Product> list = new ArrayList<>();
        Map<Integer, List<Long>> typeMap = productManagerService.mapInType(goodsId, getAllType(tenantId));
        for (Integer type : typeMap.keySet()) {
            List<Product> productList = null;
            try {
                productList = productFactory.instance(type).selectList(product, typeMap.get(type));
            } catch (Exception e) {
                //产品查询不到 不抛出异常
                e.printStackTrace();
            }
            if (CollUtil.isNotEmpty(productList)) {
                list.addAll(productList);
            }
        }
        return list;
    }

    /**
     * 根据产品集合获取所有产品
     *
     * @param product   查询条件
     * @param goodsType 产品类型
     * @return
     */
    public List<Product> selectList(Map<String, Object> product, Integer goodsType) {
        return productFactory.instance(goodsType).selectList(product, null);
    }

    /**
     * 分页
     *
     * @param product
     * @param query
     * @return
     */
    public IPage<ProductVO> selectGoodsPage(Map<String, Object> product, Query query, Integer goodsType) {
        if (!getAllType().contains(goodsType)) {
            return new Page<>();
        }
        return productFactory.instance(goodsType).selectGoodsPage(product, query);
    }

    /**
     * 产品基础信息
     *
     * @param id
     * @return
     */
    public Product detailBase(Long id) {
        ProductManager productManager = productManagerService.getByProductId(id);
        Product product = productFactory.instance(productManager.getType()).detailBase(id);
        product.setCapitalName(productManager.getCapitalName());
        product.setCapitalLogo(productManager.getCapitalLogo());
        return product;
    }

    /**
     * 产品详情
     *
     * @param id 产品id
     * @return GoodsVO
     */
    public ProductVO detail(Long id) {
        Integer type = productManagerService.getTypeByProductId(id);
        return productFactory.instance(type).detail(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBase(ProductDTO product, Integer goodsType) {
        productFactory.instance(goodsType).saveBaseProduct(product);
        return true;
    }

    /**
     * 保存产品
     *
     * @param productDTO goods
     * @return Long
     */
    ProductDTO saveGoods(ProductDTO productDTO, Integer goodsType) {
        return productFactory.instance(goodsType).saveGoods(productDTO);
    }

    /**
     * 更新产品
     *
     * @param goods goods
     * @return true
     */
    ProductDTO updateGoods(ProductDTO goods, Integer goodsType) {
        return productFactory.instance(goodsType).updateGoods(goods);
    }

    /**
     * 上架
     *
     * @param id 产品id
     * @return boolean
     */
    List<Long> onShelf(Long id, Integer goodsType) {
        return productFactory.instance(goodsType).onShelf(id);
    }

    /**
     * 产品下架
     *
     * @param ids 产品id
     * @return boolean
     */
    List<Long> offShelf(List<Long> ids, Integer goodsType) {
        return productFactory.instance(goodsType).offShelf(ids);
    }

    /**
     * 批量上架
     *
     * @param ids 产品id
     * @return Integer
     */
    List<Long> batchOnShelf(List<Long> ids, Integer goodsType) {
        return productFactory.instance(goodsType).batchOnShelf(ids);
    }

    /**
     * 查询优质产品列表
     *
     * @return List<GoodsVO>
     */
    List<ProductVO> selectHighQualityGoodsList(Integer goodsType) {
        return productFactory.instance(goodsType).selectHighQualityGoodsList();
    }

    public List<Integer> getAllType() {
        String tenantId = Func.toStr(AuthUtil.getTenantId(), Func.toStr(TenantContextHolder.getTenantId(), "000000"));

        //如果系统配置 优先系统配置
        String value = ParamCache.getValue("GOODS_TYPE_LIST");
        Map<String, String> map = JSONUtil.toBean(value, Map.class);
        String s = map.get(tenantId);
        List<Integer> configVal = Func.toIntList(s);

        List<Integer> allType = productFactory.getAllType();
        return StringUtil.isBlank(s) ? allType : configVal.stream().filter(allType::contains).collect(Collectors.toList());
    }

    public List<Integer> getAllType(String tenantId) {
        //如果系统配置 优先系统配置
        String value = ParamCache.getValue("GOODS_TYPE_LIST");
        log.info("系统配置的产品类型:{}", value);
        Map<String, String> map = JSONUtil.toBean(value, Map.class);
        String s = map.get(tenantId);
        List<Integer> configVal = Func.toIntList(s);

        List<Integer> allType = productFactory.getAllType();
        return StringUtil.isBlank(s) ? allType : configVal.stream().filter(allType::contains).collect(Collectors.toList());
    }

    /**
     * 根据产品类型获取系统内的产品id
     *
     * @param goodsType
     */
    public List<Long> listIdsByGoodsType(List<Integer> goodsType) {
        return productManagerService.listIdsByGoodsType(goodsType);
    }

    /**
     * 根据产品类型获取系统内的产品
     * 存在返回对应产品
     * 不存在为返回空
     * 空时返回所有
     *
     * @param goodsType
     * @return
     */
    public List<Integer> getAllType(Integer goodsType) {
        List<Integer> allType = getAllType();
        if (goodsType == null) {
            return allType;
        }
        if (allType.contains(goodsType)) {
            return Collections.singletonList(goodsType);
        }
        return Collections.emptyList();
    }
}
