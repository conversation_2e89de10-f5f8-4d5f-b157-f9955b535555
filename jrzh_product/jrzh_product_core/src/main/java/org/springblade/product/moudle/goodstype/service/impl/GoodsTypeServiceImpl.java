/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.goodstype.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.product.common.entity.GoodsType;
import org.springblade.product.common.vo.GoodsTypeVO;
import org.springblade.product.moudle.goodstype.mapper.GoodsTypeMapper;
import org.springblade.product.moudle.goodstype.service.GoodsTypeOperationInterceptor;
import org.springblade.product.moudle.goodstype.service.IGoodsTypeService;
import org.springblade.product.moudle.goodstype.wrapper.GoodsTypeWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 分类管理 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-11
 */
@Service
@RequiredArgsConstructor
public class GoodsTypeServiceImpl extends BaseServiceImpl<GoodsTypeMapper, GoodsType> implements IGoodsTypeService {
    private final List<GoodsTypeOperationInterceptor> goodsTypeOperationInterceptors;

    @Override
    public List<GoodsTypeVO> getList(Map<String, Object> goodsType) {
        List<GoodsType> list = baseMapper.selectList(Condition.getQueryWrapper(goodsType, GoodsType.class)
                .lambda().orderByAsc(GoodsType::getSort));
        List<GoodsTypeVO> data = GoodsTypeWrapper.build().listVO(list);
        return GoodsTypeWrapper.build().listNodeVO(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> removeGoodsType(String ids) {
        List<Long> arrId = Func.toLongList(ids);
        List<GoodsType> list = list(new QueryWrapper<GoodsType>().lambda().in(GoodsType::getParentId, arrId).last("limit 1"));
        if (ObjectUtil.isNotEmpty(list)) {
            throw new ServiceException("请先删除子类");
        }
        deleteLogic(arrId);
        goodsTypeOperationInterceptors.forEach(e -> {
            e.deleteWithGoodsType(arrId);
        });
        return arrId;
    }
}
