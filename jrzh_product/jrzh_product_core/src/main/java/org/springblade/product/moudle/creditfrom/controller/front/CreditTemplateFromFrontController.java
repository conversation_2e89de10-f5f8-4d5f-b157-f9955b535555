/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.creditfrom.controller.front;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.EnterpriseTypeEnum;
import org.springblade.common.enums.GoodsTypeEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.vo.CreditFromTemplateVO;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.moudle.creditfrom.service.ICreditFromTemplateService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_RISKMANA + CommonConstant.WEB_FRONT + "/riskmana/creditfromtemplate")
@Api(value = "授信表单", tags = "授信表单接口")
public class CreditTemplateFromFrontController extends BladeController {
    private final ProductDirector productDirector;
    private final ICreditFromTemplateService creditFromTemplateService;

    @GetMapping("/getCreditFormTemplateList")
    @ApiOperation("获取授信表单")
    public R<List<CreditFromTemplateVO>> getCreditFromTemplateList(@RequestParam Long goodsId,
                                                                   @RequestParam Integer goodsType,
                                                                   @RequestParam Integer enterpriseType) {
        ProductVO detail = productDirector.detail(goodsId);
        Long creditFormId = detail.getCreditFormId();
        if (GoodsTypeEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode().equals(goodsType) && EnterpriseTypeEnum.CORE_ENTERPRISE.getCode() == enterpriseType) {
            creditFormId = detail.getCoreCreditFormId();
        }
        return R.data(creditFromTemplateService.listByFormId(creditFormId));
    }

    @GetMapping("/getCreditFormTemplateListBase")
    @ApiOperation("获取授信表单-基础")
    public R<List<CreditFromTemplateVO>> getCreditFromTemplateListBase(@RequestParam Long goodsId,
                                                                   @RequestParam Integer goodsType,
                                                                   @RequestParam Integer enterpriseType) {
        //潍柴产品组也是getCreditFormTemplateList路径，但调用detailBase方法
        Product detail = productDirector.detailBase(goodsId);
        Long creditFormId = detail.getCreditFormId();
        if (GoodsTypeEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode().equals(goodsType) && EnterpriseTypeEnum.CORE_ENTERPRISE.getCode() == enterpriseType) {
            creditFormId = detail.getCoreCreditFormId();
        }
        return R.data(creditFromTemplateService.listByFormId(creditFormId));
    }
}
