/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.openprocess.service.impl;

import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.product.common.entity.GoodsOpeningProcess;
import org.springblade.product.moudle.openprocess.mapper.GoodsOpeningProcessMapper;
import org.springblade.product.moudle.openprocess.service.IGoodsOpeningProcessService;
import org.springframework.stereotype.Service;

/**
 * 产品开通流程 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Service
public class GoodsOpeningProcessServiceImpl extends BaseServiceImpl<GoodsOpeningProcessMapper, GoodsOpeningProcess> implements IGoodsOpeningProcessService {

}
