package org.springblade.product.moudle.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.TenantChangeEnum;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.product.common.entity.Material;
import org.springblade.product.moudle.material.mapper.MaterialMapper;
import org.springblade.system.dto.TenantInitEventServiceDto;
import org.springblade.system.entity.Role;
import org.springblade.system.service.TenantInitEventService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户初始化 资料管理数据实现类
 * <AUTHOR>
 * @date 2025/1/23
 */
@Service
@RequiredArgsConstructor
public class MaterialTenantInitEventServiceImpl implements TenantInitEventService<Material> {

    private final MaterialMapper materialMapper;

    @Override
    public TenantInitEventServiceDto<Material> getData(Role role, String tenantId) {
        // 获取超级租户数据，然后替换租户
        List<Material> list = materialMapper.selectList(Wrappers.<Material>lambdaQuery().eq(Material::getTenantId, BladeConstant.ADMIN_TENANT_ID).eq(Material::getIsDeleted, BladeConstant.DB_NOT_DELETED));
        for (Material material : list) {
            material.setTenantId(tenantId);
            material.setId(null);
        }
        TenantInitEventServiceDto dto = new TenantInitEventServiceDto();
        dto.setSupport(this.support());
        dto.setDataList(list);
        return dto;
    }

    @Override
    public void saveData(List<Material> dataList) {
        materialMapper.insertBatchSomeColumn(dataList);
    }

    @Override
    public Integer support() {
        return TenantChangeEnum.TENANT_CHANGE_MATERIAL.getCode();
    }
}
