<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.product.moudle.labelrelation.mapper.GoodsLabelRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="goodsLabelRelationResultMap" type="org.springblade.product.common.entity.GoodsLabelRelation">
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="goods_id" property="goodsId"/>
        <result column="label_id" property="labelId"/>
    </resultMap>


    <select id="selectLabelInGoodsId" resultType="org.springblade.product.common.vo.GoodsLabelRelationVO">
        select jgl.id,jgl.id label_id,jglr.goods_id,jgl.name,jgl.sort,jgl.label_color,jgl.text_color
        from jrzh_goods_label_relation jglr inner join
        jrzh_goods_label jgl on jglr.label_id = jgl.id
        where jglr.goods_id in
        <foreach collection="goodsIds" open="(" close=")" separator="," item="goodsId">
            #{goodsId}
        </foreach>
        and jglr.is_deleted = 0
        order by jgl.sort asc
    </select>

</mapper>
