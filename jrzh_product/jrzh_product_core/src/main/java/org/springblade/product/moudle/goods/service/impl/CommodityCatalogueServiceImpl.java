/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.product.common.constant.CommodityEnum;
import org.springblade.product.common.entity.CommodityCatalogue;
import org.springblade.product.common.entity.CommodityList;
import org.springblade.product.common.vo.CommodityCatalogueVO;
import org.springblade.product.moudle.goods.mapper.CommodityCatalogueMapper;
import org.springblade.product.moudle.goods.service.ICommodityCatalogueService;
import org.springblade.product.moudle.goods.service.ICommodityListService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Service
@AllArgsConstructor
public class CommodityCatalogueServiceImpl extends BaseServiceImpl<CommodityCatalogueMapper, CommodityCatalogue> implements ICommodityCatalogueService {

	private final ICommodityListService iCommodityListService;

	@Override
	public IPage<CommodityCatalogueVO> selectCommodityCataloguePage(IPage<CommodityCatalogueVO> page, CommodityCatalogueVO commodityCatalogue) {
		return page.setRecords(baseMapper.selectCommodityCataloguePage(page, commodityCatalogue));
	}

	/**
	 * 新增分类
	 *
	 * @param commodityCatalogue
	 * @return
	 */
	@Override
	public boolean submit(CommodityCatalogue commodityCatalogue) {
		// 校验参数
		checkValid(commodityCatalogue);

		synchronized (CommodityCatalogueServiceImpl.class) {
			String code = null;

			// 如果父id为空，则新增一级分类
			if (Func.isEmpty(commodityCatalogue.getParentId())) {
				// 获取一级分类的总数，并以0开头拼接，作为编号code
				int counter = this.count(Wrappers.<CommodityCatalogue>lambdaQuery().eq(CommodityCatalogue::getParentId, 0));
				counter = counter + 1;
				code = "0" + counter;
			} else {
				// 根据前端传过来的商品分类id（可能是一级、二级、三级分类）、分类名称、分类主键id（不在数据库中），查询分类总数，TODO 新增操作，主键id一定为空，此处，查询的是该商品分类下的子分类同名的总数
				int countSum = this.count(Wrappers.<CommodityCatalogue>lambdaQuery().eq(CommodityCatalogue::getParentId, commodityCatalogue.getParentId())
					.eq(CommodityCatalogue::getDirectName, commodityCatalogue.getDirectName())
					.ne(ObjectUtil.isNotEmpty(commodityCatalogue.getId()), CommodityCatalogue::getId, commodityCatalogue.getId()));
				if (countSum > 0) {
					throw new ServiceException("同分类下面已存在相同名称!");
				}

				// 根据前端传过来的商品分类id（可能是一级、二级、三级分类），作为主键id，查询对应的商品分类对象
				CommodityCatalogue goodsTypeParent = this.getOne(Wrappers.<CommodityCatalogue>lambdaQuery().eq(CommodityCatalogue::getId, commodityCatalogue.getParentId()));
				// 根据前端传过来的商品分类id（可能是一级、二级、三级分类）,作为parentId，查询其孩子的总数，并以0开头拼接，作为编号code
				int count = this.count(Wrappers.<CommodityCatalogue>lambdaQuery().eq(CommodityCatalogue::getParentId, commodityCatalogue.getParentId()));
				count = count + 1;
				// 如果该商品分类（可能是一级、二级、三级分类）不为空，就获取该商品分类原有编号，再次拼接0和count，作为新的编号code
				if (null != goodsTypeParent) {
					code = goodsTypeParent.getCode() + "0" + count;
				}
			}
			commodityCatalogue.setCode(code);
			return this.saveOrUpdate(commodityCatalogue);
		}
	}

	public void checkValid(CommodityCatalogue commodityCatalogue) {
		// 查询父id为0，并且该商品目录主键id在数据库中不存在的数据，TODO 新增操作，主键id为空，此处，只查询了所有的二级目录列表
		List<CommodityCatalogue> commodityCatalogues = this.lambdaQuery().eq(CommodityCatalogue::getParentId, 0).ne(ObjectUtil.isNotEmpty(commodityCatalogue.getId()), CommodityCatalogue::getId, commodityCatalogue.getId()).list();

		Set<String> commodittCatalogueName = commodityCatalogues.stream().map(CommodityCatalogue::getDirectName).collect(Collectors.toSet());

		Assert.isFalse(commodittCatalogueName.contains(commodityCatalogue.getDirectName()), "已存在相同的分类");

	}

	/**
	 * 获取商品分类树形结构的列表
	 *
	 * @return
	 */
	@Override
	public List<CommodityCatalogueVO> tree() {
		List<CommodityCatalogueVO> commodityCatalogueVOList = baseMapper.tree();
		// 将commodityCatalogueVOList作为参数，传给merge方法，得到最终列表集合
		return ForestNodeMerger.merge(commodityCatalogueVOList);
	}

	/**
	 * 获取已启用商品分类树形结构的列表
	 *
	 * @return
	 */
	@Override
	public List<CommodityCatalogueVO> enableTree() {
		List<CommodityCatalogueVO> commodityCatalogueVOList = baseMapper.secondTree();
		return ForestNodeMerger.merge(commodityCatalogueVOList);
	}

	/**
	 * 根据parentId，查询其孩子列表
	 *
	 * @param parentId
	 * @return
	 */
	@Override
	public List<CommodityCatalogue> getListByParentId(Long parentId) {
		return baseMapper.selectList(Wrappers.<CommodityCatalogue>lambdaQuery().eq(CommodityCatalogue::getParentId, parentId).orderByAsc(CommodityCatalogue::getSort));
	}

	/**
	 * 删除商品分类
	 *
	 * @param toLongList
	 * @return
	 */
	@Override
	public Boolean removeCommodityType(List<Long> toLongList) {
		// 前端传过来的商品分类主键ids，作为parentId，查询其孩子分类列表
		List<CommodityCatalogue> list = baseMapper.selectList(Wrappers.<CommodityCatalogue>lambdaQuery().in(CommodityCatalogue::getParentId, toLongList));
		if (!CollectionUtils.isEmpty(list)) {
			throw new ServiceException("请先删除子节点");
		}
		return removeByIds(toLongList);
	}

	/**
	 * 更新产品状态
	 *
	 * @param ids
	 * @param status
	 * @return
	 */
	@SneakyThrows
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> changeGoodsStatus(List<Long> ids, Integer status) {
		// 根据ids，作为主键id，查询商品分类列表，转成stream流，并过滤得到，状态跟status不同的数据，收集后转为List
		List<CommodityCatalogue> commodityCatalogueList = this.lambdaQuery().in(CommodityCatalogue::getId, ids).list().stream()
			.filter(e -> !status.equals(e.getStatus())).collect(Collectors.toList());

		// commodityCatalogueList列表为空，说明，没有数据需要更新状态
		if (commodityCatalogueList.size() == 0) {
			return R.success(CommodityEnum.ABLE.getCode().equals(status) ? "启用成功" : "禁用成功");
		}

		// 收集commodityCatalogueList列表中的id，并转为Set<Long>集合
		Set<Long> realIds = commodityCatalogueList.stream().map(CommodityCatalogue::getId).collect(Collectors.toSet());

		// 收集commodityCatalogueList列表中的parentId，并转为Set<Long>集合
		Set<Long> parentIds = commodityCatalogueList.stream().map(CommodityCatalogue::getParentId).collect(Collectors.toSet());

		// 根据前端传过来的ids，作为catalogueId，查询商品表，得到商品列表
		List<CommodityList> commodityLists = iCommodityListService.lambdaQuery().in(ids.size() > 0, CommodityList::getCatalogueId, ids).list();

		// 根据收集得到的parentIds，作为主键id，查询商品分类表，得到禁用状态的商品分类列表
		List<CommodityCatalogue> commodityParentCatalogues = this.lambdaQuery().eq(CommodityCatalogue::getStatus, CommodityEnum.DISABLE.getCode())
			.in(parentIds.size() > 0, CommodityCatalogue::getId, parentIds).list();

		// 将商品分类列表commodityParentCatalogues转成stream流，如果有一个商品分类对象的状态是禁用，则返回true，TODO 此处，直接判断集合不为空即可
		boolean isExistDisStatus = commodityParentCatalogues.stream().anyMatch(e -> CommodityEnum.DISABLE.getCode().equals(e.getStatus()));

		// 如果其父目录是禁用状态，并且前端传过来的状态是启用，则提示，错误信息
		if (isExistDisStatus && CommodityEnum.ABLE.getCode().equals(status)) {
			return R.fail("父级目录为禁用状态,不能启用");
		}

		// 根据收集commodityCatalogueList列表中的id，作为parentId，查询其孩子，没有禁用，并且前端传过来的状态是禁用，则提示，错误消息
		if (Boolean.FALSE.equals(judgeDisable(realIds)) && CommodityEnum.DISABLE.getCode().equals(status)) {
			return R.fail("该目录下有子集目录,请先禁用子集目录");

		} else if (commodityLists.size() > 0) {
			// 如果商品列表commodityLists不为空，获取到商品ids和前端传过来status，更新商品表
			SpringUtil.getBean(ICommodityListService.class).changeStatus(commodityLists.stream().map(CommodityList::getId).collect(Collectors.toList()),
				CommodityEnum.DOWN_SHELF.getCode().equals(status) ? CommodityEnum.DOWN_SHELF.getCode() : CommodityEnum.UP_SHELF.getCode());
			// 根据前端传过来的ids和status，更新商品分类状态
			return updateStatus(ids, status);

		} else {
			// 其他情况，根据前端传过来的ids和status，更新商品分类状态
			return updateStatus(ids, status);
		}
	}

	/**
	 * 更新商品分类状态
	 *
	 * @param ids
	 * @param status
	 * @return
	 */
	public R<Boolean> updateStatus(List<Long> ids, Integer status) {
		// 根据ids查询商品分类列表
		List<CommodityCatalogue> toupdateList = this.lambdaQuery().in(ids.size() > 0, CommodityCatalogue::getId, ids).list();
		toupdateList.forEach(e -> {
//			e.setOperatorName(MyAuthUtil.getRealName());
			e.setStatus(status);
		});

		// 批量更新
		return R.status(this.updateBatchById(toupdateList));
	}

	@Override
	public List<CommodityCatalogue> findByParentId(Long parentId) {
		return this.lambdaQuery().eq(CommodityCatalogue::getParentId, parentId).list();
	}

	/**
	 * 根据parentId，查询其所有孩子列表
	 *
	 * @param parentIds
	 * @return
	 */
	public List<CommodityCatalogue> findAllChildCatalogueByParentId(Set<Long> parentIds) {
		List<Long> parentIdList = new ArrayList<>(parentIds);

		// 如果parentIdList为空
		//if (BigDecimal.ZERO.equals(parentIdList)) {
		if (CollUtil.isEmpty(parentIdList)) {
			// 根据parentIds，作为parentId，查询商品分类列表，转成stream，提取主键id，填充到parentIdList中
			parentIdList.addAll(this.lambdaQuery().eq(CommodityCatalogue::getParentId, parentIds).list().stream().map(CommodityCatalogue::getId).collect(Collectors.toList()));
		}

		// 如果parentIdList不为空，则作为parentId，查询其孩子列表
		return this.lambdaQuery().in(parentIdList.size() > 0, CommodityCatalogue::getParentId, parentIdList).list();
	}

	/**
	 * 根据parentId，判断商品分类是否是禁用状态
	 *
	 * @param parentIds
	 * @return
	 */
	public boolean judgeDisable(Set<Long> parentIds) {
		// 根据parentId，作为parentId查询其所有孩子列表
		List<CommodityCatalogue> commodityCatalogues = findAllChildCatalogueByParentId(parentIds);
		return commodityCatalogues.stream().filter(e -> CommodityEnum.ABLE.getCode().equals(e.getStatus())).count() == 0;
	}

	/**
	 * 获取所有商品分类树形结构的列表
	 *
	 * @return
	 */
	@Override
	public List<CommodityCatalogueVO> secondTree() {
		List<CommodityCatalogueVO> list = this.baseMapper.tree();
		return ForestNodeMerger.merge(list);
	}

	@Override
	/**
	 * 获取顶级和二级菜单目录，并合并
	 */
	public List<CommodityCatalogue> getTopAndSencond() {
		// 获取一级目录
		List<CommodityCatalogue> topCatalogue = this.lambdaQuery().eq(CommodityCatalogue::getParentId, 0).list();

		// 获取一级目录的ids集合
		List<Long> topIds = topCatalogue.stream().map(CommodityCatalogue::getId).collect(Collectors.toList());

		// 查询父id在一级目录ids列表中的，所有二级目录列表
		List<CommodityCatalogue> secondCatalogue = this.lambdaQuery().in(topIds.size() > 0, CommodityCatalogue::getParentId, topIds).list();

		// 将二级目录列表和一级目录列表中，合并后返回
		topCatalogue.addAll(secondCatalogue);
		return topCatalogue;
	}
}
