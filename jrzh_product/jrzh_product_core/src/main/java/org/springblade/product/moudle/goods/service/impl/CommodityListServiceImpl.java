/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.PriceStatusEnum;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.*;
import org.springblade.product.common.constant.CommodityEnum;
import org.springblade.product.common.dto.AttachDTO;
import org.springblade.product.common.dto.CommodityListDTO;
import org.springblade.product.common.dto.TreeDTO;
import org.springblade.product.common.entity.CommodityCatalogue;
import org.springblade.product.common.entity.CommodityList;
import org.springblade.product.common.entity.CommoditySpec;
import org.springblade.product.common.entity.CommoditySpecLog;
import org.springblade.product.common.vo.CommodityListVO;
import org.springblade.product.common.vo.CommoditySpecVO;
import org.springblade.product.moudle.goods.mapper.CommodityListMapper;
import org.springblade.product.moudle.goods.service.*;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-17
 */
@Service
@AllArgsConstructor
public class CommodityListServiceImpl extends BaseServiceImpl<CommodityListMapper, CommodityList> implements ICommodityListService {

    private final IAttachService attachService;
    private final ICommoditySpecService commoditySpecService;
    private final ICommodityWhiteListCommodityService commodityWhiteListCommodityService;
    private final ICommoditySpecLogService commoditySpecLogService;
    private final CommodityListMapper commodityListMapper;
    //private final ICustomerSupplierClient customerSupplierClient;
    private final RemoteUserService remoteUserService;
    private final ProductDirector productDirector;


    @Override
    public IPage<CommodityListVO> selectCommodityListPage(IPage<CommodityListVO> page, CommodityListVO commodityList) {
        // 准备空Set集合存储分类id
        Set<Long> catalogueIds = new HashSet<>();
        // 准备空List集合
        List<Long> allIds = new ArrayList<>();
        // 将分类id存入Set集合
        catalogueIds.add(commodityList.getCatalogueId());

        // 此处，将前端传过来commodityCatalogueName，转成Long，设置到分类id上
        if (StrUtil.isNotBlank(commodityList.getCommodityCatalogueName())) {
            commodityList.setCatalogueId(Long.valueOf(commodityList.getCommodityCatalogueName()));
        }

        if (ObjectUtil.isNotEmpty(commodityList.getCatalogueId())) {
            // 查找所有分类及子分类
            List<TreeDTO> treeDTOS = this.findCategoryAndChild(commodityList.getCatalogueId());
            catalogueIds.addAll(treeDTOS.stream().map(TreeDTO::getId).collect(Collectors.toSet()));
            catalogueIds.addAll(treeDTOS.stream().map(TreeDTO::getParentId).collect(Collectors.toSet()));
            List<Long> child = new ArrayList<>();
            treeDTOS.forEach(e -> {
                child.addAll(new ArrayList<>(e.getChild().stream().map(TreeDTO::getParentId).collect(Collectors.toSet())));
                child.addAll(new ArrayList<>(e.getChild().stream().map(TreeDTO::getId).collect(Collectors.toSet())));
            });
            catalogueIds.addAll(child);
            allIds.addAll(new ArrayList<>(catalogueIds));
            allIds.add(commodityList.getCatalogueId());
        }


        String type = commodityList.getType();
        String agentType = null;
        String pledgeType = null;
        if (StringUtil.isNotBlank(type)) {
            List<Integer> list = Func.toIntList(type);
            if (list.contains(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode())) {
                agentType = String.valueOf(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode());
            }
            if (list.contains(GoodsEnum.GOODS_PLEDGE.getCode())) {
                pledgeType = String.valueOf(GoodsEnum.GOODS_PLEDGE.getCode());
            }
        }
        List<CommodityListVO> commodityListVOS = baseMapper.selectCommodityListPage(page, commodityList, allIds, agentType, pledgeType);


        // 如果commodityListVOS为空，返回空列表
        if (CollUtil.isEmpty(commodityListVOS)) {
            return page.setRecords(Collections.EMPTY_LIST);
        }

        commodityListVOS = BeanUtil.copyWithConvert(commodityListVOS, CommodityListVO.class);

        // 设置所有分类
        setAllCatalogueName(commodityListVOS);

        // 设置供应商名称
        //setSupplierName(commodityListVOS);

        // 设置操作类型
        if (commodityListVOS.size() > 0) {
            commodityListVOS = commodityListVOS.stream().map(e -> {
                if (null != e.getAttachCommodityId()) {
                    List<Long> ids = Func.toLongList(e.getAttachCommodityId());
                    List<Attach> attachList = attachService.lambdaQuery().in(ids.size() > 0, Attach::getId, ids).list();
                    List<AttachDTO> attachDTOList = BeanUtil.copyWithConvert(attachList, AttachDTO.class);
                    e.setAttachList(attachDTOList);
                    if (CollectionUtil.isNotEmpty(attachDTOList)) {
                        e.setImg(attachDTOList.get(0).getLink());
                    }
                }
                e.setOperatorName( remoteUserService.getUserById(e.getUpdateUser(), FeignConstants.FROM_IN).getData().getName());
                return e;
            }).collect(Collectors.toList());
        }

        /**/
        return page.setRecords(commodityListVOS);
    }
//    /**
//     * 设置供应商名称
//     *
//     * @param listVO
//     */
//    private void setSupplierName(List<CommodityListVO> listVO) {
//        List<Long> map = StreamUtil.map(listVO, CommodityListVO::getSupplierId);
//        Map<Long, String> customerSupplierMap = StreamUtil.toMap(customerSupplierClient.listByIds(map).getData(), CustomerSupplier::getId, CustomerSupplier::getSupperName);
//        for (CommodityListVO vo : listVO) {
//            Long commoditySupplierId = vo.getSupplierId();
//            if (customerSupplierMap.containsKey(commoditySupplierId)) {
//                vo.setSupplierName(customerSupplierMap.get(commoditySupplierId));
//            }
//        }
//    }

    /**
     * 设置所有的分类名称
     *
     * @param commodityListVOS
     */
    private void setAllCatalogueName(List<CommodityListVO> commodityListVOS) {
        if (commodityListVOS.size() > 0) {
            ICommodityCatalogueService commodityCatalogueService = SpringUtil.getBean(ICommodityCatalogueService.class);
			/*
			  一级分类
			 */
            List<CommodityCatalogue> topOne = commodityCatalogueService.lambdaQuery().eq(CommodityCatalogue::getParentId, 0).list();
            Set<Long> topOneIds = topOne.stream().map(CommodityCatalogue::getId).collect(Collectors.toSet());
			/*
			 二级分类
			 */
            List<CommodityCatalogue> topTwo = commodityCatalogueService.lambdaQuery().in(topOneIds.size() > 0, CommodityCatalogue::getParentId, topOneIds).list();
            Set<Long> topTwoIds = topTwo.stream().map(CommodityCatalogue::getId).collect(Collectors.toSet());
            /**
             * 三级分类
             */
            List<CommodityCatalogue> topThree = commodityCatalogueService.lambdaQuery().in(topTwoIds.size() > 0, CommodityCatalogue::getParentId, topTwoIds).list();
            Set<Long> topThreeIds = topThree.stream().map(CommodityCatalogue::getId).collect(Collectors.toSet());
			/*
			  分类设置
			 */
            commodityListVOS.forEach(e -> {
                if (topOneIds.contains(e.getCatalogueId())) {
                    CommodityCatalogue commodityCatalogue = topOne.stream().filter(o -> o.getId().equals(e.getCatalogueId())).findFirst().orElse(new CommodityCatalogue());
                    e.setCommodityCatalogueName(commodityCatalogue.getDirectName());
                } else if (topTwoIds.contains(e.getCatalogueId())) {
                    CommodityCatalogue commodityCatalogue = topTwo.stream().filter(o -> o.getId().equals(e.getCatalogueId())).findFirst().orElse(new CommodityCatalogue());
                    CommodityCatalogue commodityParent = topOne.stream().filter(o -> o.getId().equals(commodityCatalogue.getParentId())).findFirst().orElse(new CommodityCatalogue());
                    e.setCommodityCatalogueName(commodityParent.getDirectName() + ">" + commodityCatalogue.getDirectName());
                } else if (topThreeIds.contains(e.getCatalogueId())) {
                    CommodityCatalogue commodityThree = topThree.stream().filter(o -> o.getId().equals(e.getCatalogueId())).findFirst().orElse(new CommodityCatalogue());
                    CommodityCatalogue commodityTwo = topTwo.stream().filter(o -> o.getId().equals(commodityThree.getParentId())).findFirst().orElse(new CommodityCatalogue());
                    CommodityCatalogue commodityOne = topOne.stream().filter(o -> o.getId().equals(commodityTwo.getParentId())).findFirst().orElse(new CommodityCatalogue());
                    e.setCommodityCatalogueName(commodityOne.getDirectName() + ">" + commodityTwo.getDirectName() + ">" + commodityThree.getDirectName());
                }
            });
        }
        return;
    }
//	/**
//	 * 设置供应商名称
//	 *
//	 * @param listVO
//	 */
//	private void setSupplierName(List<CommodityListVO> listVO) {
//		List<Long> map = StreamUtil.map(listVO, CommodityListVO::getSupplierId);
//		Map<Long, String> customerSupplierMap = StreamUtil.toMap(customerSupplierService.listByIds(map), CustomerSupplier::getId, CustomerSupplier::getSupperName);
//		for (CommodityListVO vo : listVO) {
//			Long commoditySupplierId = vo.getSupplierId();
//			if (customerSupplierMap.containsKey(commoditySupplierId)) {
//				vo.setSupplierName(customerSupplierMap.get(commoditySupplierId));
//			}
//		}
//	}

//	/**
//	 * 设置所有的分类名称
//	 *
//	 * @param commodityListVOS
//	 */
//	private void setAllCatalogueName(List<CommodityListVO> commodityListVOS) {
//		if (commodityListVOS.size() > 0) {
//			ICommodityCatalogueService commodityCatalogueService = SpringUtil.getBean(ICommodityCatalogueService.class);
//			/*
//			  一级分类
//			 */
//			List<CommodityCatalogue> topOne = commodityCatalogueService.lambdaQuery().eq(CommodityCatalogue::getParentId, 0).list();
//			Set<Long> topOneIds = topOne.stream().map(CommodityCatalogue::getId).collect(Collectors.toSet());
//			/*
//			 二级分类
//			 */
//			List<CommodityCatalogue> topTwo = commodityCatalogueService.lambdaQuery().in(topOneIds.size() > 0, CommodityCatalogue::getParentId, topOneIds).list();
//			Set<Long> topTwoIds = topTwo.stream().map(CommodityCatalogue::getId).collect(Collectors.toSet());
//			/**
//			 * 三级分类
//			 */
//			List<CommodityCatalogue> topThree = commodityCatalogueService.lambdaQuery().in(topTwoIds.size() > 0, CommodityCatalogue::getParentId, topTwoIds).list();
//			Set<Long> topThreeIds = topThree.stream().map(CommodityCatalogue::getId).collect(Collectors.toSet());
//			/*
//			  分类设置
//			 */
//			commodityListVOS.forEach(e -> {
//				if (topOneIds.contains(e.getCatalogueId())) {
//					CommodityCatalogue commodityCatalogue = topOne.stream().filter(o -> o.getId().equals(e.getCatalogueId())).findFirst().orElse(new CommodityCatalogue());
//					e.setCommodityCatalogueName(commodityCatalogue.getDirectName());
//				} else if (topTwoIds.contains(e.getCatalogueId())) {
//					CommodityCatalogue commodityCatalogue = topTwo.stream().filter(o -> o.getId().equals(e.getCatalogueId())).findFirst().orElse(new CommodityCatalogue());
//					CommodityCatalogue commodityParent = topOne.stream().filter(o -> o.getId().equals(commodityCatalogue.getParentId())).findFirst().orElse(new CommodityCatalogue());
//					e.setCommodityCatalogueName(commodityParent.getDirectName() + ">" + commodityCatalogue.getDirectName());
//				} else if (topThreeIds.contains(e.getCatalogueId())) {
//					CommodityCatalogue commodityThree = topThree.stream().filter(o -> o.getId().equals(e.getCatalogueId())).findFirst().orElse(new CommodityCatalogue());
//					CommodityCatalogue commodityTwo = topTwo.stream().filter(o -> o.getId().equals(commodityThree.getParentId())).findFirst().orElse(new CommodityCatalogue());
//					CommodityCatalogue commodityOne = topOne.stream().filter(o -> o.getId().equals(commodityTwo.getParentId())).findFirst().orElse(new CommodityCatalogue());
//					e.setCommodityCatalogueName(commodityOne.getDirectName() + ">" + commodityTwo.getDirectName() + ">" + commodityThree.getDirectName());
//				}
//			});
//		}
//		return;
//	}

    @Override
    public List<CommodityList> selectManhourPage(IPage page, CommodityList commodityList) {
        return null;
    }

    @Override
    public List<TreeDTO> findCategoryAndChild(Long parentId) {
        ICommodityCatalogueService commodityCatalogueService = SpringUtil.getBean(ICommodityCatalogueService.class);
        //1.先查出以它为父类的子类
        List<CommodityCatalogue> CommodityCatalogues = commodityCatalogueService.findByParentId(parentId);
        //2.将数据进行递归  当datas为null的时候则表示没有下级  则推出
        return findChild(CommodityCatalogues, parentId);
    }

    /**
     * 根据commodityListDTO保存
     *
     * @param commodityListDTO
     * @return
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean commodityDTOSave(CommodityListDTO commodityListDTO) {

        List<AttachDTO> attachDTOList = commodityListDTO.getAttachList();
        String attachIds = attachDTOList.stream().map(AttachDTO::getAttachId).collect(Collectors.joining(","));
        commodityListDTO.setAttachCommodityId(attachIds);

        CommodityList commodityList = BeanUtil.copy(commodityListDTO, CommodityList.class);

        // 保存商品表数据，如果保存失败，抛出异常
        boolean result1 = saveOrUpdate(commodityList);
        if (!result1) {
            throw new ServiceException("商品数据保存失败");
        }

        // 遍历commoditySpecList集合，重新设置属性内容
        List<CommoditySpec> commoditySpecList = commodityListDTO.getCommoditySpecList();
        commoditySpecList.forEach(commoditySpec -> {
            // 设置商品规格为上架状态
            commoditySpec.setStatus(CommodityEnum.UP_SHELF.getCode());
            // 将保存商品数据得到的商品id，设置到commoditySpecList集合的实体对象的属性上
            commoditySpec.setCommodityListId(commodityList.getId());
            //补充价格日期和默认市场价格
            setDateAndPrice(commoditySpec);
            //比较价格状态
            comparePrice(commoditySpec);
        });


        // 批量保存商品规格数据，如果保存失败，抛出异常
        boolean result2 = commoditySpecService.saveBatch(commoditySpecList);
        if (!result2) {
            throw new ServiceException("商品规格数据保存失败");
        }

        //保存商品价格日志表
        saveCommodityPriceLog(commoditySpecList);


        // 到此，说明result1，result2都为true，直接返回成功即可
        return result1 && result2;
    }

    /**
     * 保存商品价格日志表
     *
     * @param commoditySpecList
     */
    private void saveCommodityPriceLog(List<CommoditySpec> commoditySpecList) {
        List<CommoditySpecLog> commoditySpecLogs = commoditySpecList.stream().map(commoditySpec -> {
            CommoditySpecLog commoditySpecLog = BeanUtil.copy(commoditySpec, CommoditySpecLog.class);
            commoditySpecLog.setCommoditySpecId(commoditySpec.getId());
            return commoditySpecLog;
        }).collect(Collectors.toList());
        commoditySpecLogService.saveBatch(commoditySpecLogs);
    }

    /**
     * 设置对比价格结果
     *
     * @param commoditySpec
     */
    public void comparePrice(CommoditySpec commoditySpec) {
        //对比价格得出状态,比较市价得出状态，调用拼接价格方法
        BigDecimal marketPrice = commoditySpec.getMarketPrice();
        BigDecimal minPrice = commoditySpec.getCommodityMinPrice();
        BigDecimal maxPrice = commoditySpec.getCommodityMaxPrice();
        //市场价格不能为空
        if (ObjectUtil.isNotEmpty(marketPrice)) {
            //大于结果值为1  小于结果值为-1
            int min = minPrice.compareTo(marketPrice);
            int max = maxPrice.compareTo(marketPrice);
            //正常的对，异常的少
            if ((min == -1 && max == 1) || min == 0 || max == 0) {
                commoditySpec.setAbnormal(PriceStatusEnum.NORMAL.getCode());
                commoditySpec.setComparePrices(PriceStatusEnum.NORMAL.getCode());
            } else {
                //市价大于最大价格
                if (max == -1) {
                    commoditySpec.setAbnormal(PriceStatusEnum.ABNORMAL.getCode());
                    commoditySpec.setComparePrices(PriceStatusEnum.UP.getCode());
                } else {
                    commoditySpec.setAbnormal(PriceStatusEnum.ABNORMAL.getCode());
                    commoditySpec.setComparePrices(PriceStatusEnum.DOWN.getCode());
                }
            }
        }
    }

    @Override
    public Page<CommoditySpecVO> pageByCondition(CommodityListDTO commodityListDTO, Query query) {


        //根据条件查询：商品分类 状态 商品名称 商品编号
        List<CommoditySpecVO> commoditySpecVOS = commodityListMapper.selectCondition(commodityListDTO);

        //查询商品表，构建vo
        commoditySpecVOS = commoditySpecService.getCommodityList(commoditySpecVOS);
        if (ObjectUtil.isEmpty(commoditySpecVOS)) {
            return null;
        }
        //分页
        int start = (query.getCurrent() - 1) * query.getSize();
        List<CommoditySpecVO> record = commoditySpecVOS.stream()
                .sorted(Comparator.comparing(BaseEntity::getUpdateTime).reversed())
                .skip(start)
                .limit(query.getSize())
                .collect(Collectors.toList());

        Page<CommoditySpecVO> page = new Page<>();
        page.setRecords(record);
        page.setTotal(commoditySpecVOS.size());
        page.setCurrent(query.getCurrent());
        page.setSize(query.getSize());

        return page;
    }

    /**
     * 根据融资编号查询代采商品
     *
     * @param financingNo
     * @return
     */
    @Override
    public List<CommoditySpecVO> selectFinanceNoAgent(String financingNo) {
        //根据融资编号查询融资编号下的代采商品
        List<CommoditySpecVO> commoditySpecVOList = commodityListMapper.selectFinanceNoAgent(financingNo);
        if (CollectionUtil.isEmpty(commoditySpecVOList)) {
            return Collections.EMPTY_LIST;
        }
        //查询商品表，构建vo
        commoditySpecVOList = commoditySpecService.getCommodityList(commoditySpecVOList);

        return commoditySpecVOList;
    }


    /**
     * 设置价格日期和默认市场价格
     *
     * @param commoditySpec
     */
    private void setDateAndPrice(CommoditySpec commoditySpec) {
        //设置价格日期
        commoditySpec.setPriceDate(new Date());
        //设置默认市场单价（最高价格+最低价格)/2
        BigDecimal maxPrice = commoditySpec.getCommodityMaxPrice();
        BigDecimal minPrice = commoditySpec.getCommodityMinPrice();
        BigDecimal add = maxPrice.add(minPrice);
        BigDecimal Price = add.divide(new BigDecimal(2), 2, BigDecimal.ROUND_HALF_UP);
        commoditySpec.setMarketPrice(Price);
    }


    @Override
    public Map<Long, CommodityList> getMapInId(List<Long> goodsId) {
        if (CollectionUtils.isEmpty(goodsId)) {
            return Collections.emptyMap();
        }
        return baseMapper.selectBatchIds(goodsId).stream().collect(Collectors.toMap(CommodityList::getId, obj -> obj));
    }

    /**
     * 根据商品和规格信息，更新操作
     *
     * @param commodityListDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCommodityAndSpec(CommodityListDTO commodityListDTO) {

        List<AttachDTO> attachDTOList = commodityListDTO.getAttachList();
        String attachIds = attachDTOList.stream().map(AttachDTO::getAttachId).collect(Collectors.joining(","));

        // 将附件ids设置到commodityListDTO
        commodityListDTO.setAttachCommodityId(attachIds);

        // 将commodityListDTO中属性，拷贝到commodityList
        CommodityList commodityList = BeanUtil.copy(commodityListDTO, CommodityList.class);

        // 更新jrzh_commodity_list商品信息，如果保存失败，抛出异常
        boolean result1 = saveOrUpdate(commodityList);
        if (!result1) {
            throw new ServiceException("商品数据更新失败");
        }

        // 根据商品id，删除jrzh_commodity_spec表对应的规格信息，存在问题，启用状态的规格数据也会被逻辑删除
        boolean result2 = commoditySpecService.remove(Wrappers.<CommoditySpec>lambdaQuery().eq(CommoditySpec::getCommodityListId, commodityList.getId()));
        if (!result2) {
            throw new ServiceException("商品规格数据删除失败");
        }

        // 重新保存商品规格表信息
        // 获取商品规格列表
        List<CommoditySpec> commoditySpecList = commodityListDTO.getCommoditySpecList();

        // 将commoditySpecList集合中的commoditySpec对象的主键id设为null，启用状态设为1
        // 将商品的id，设置到commoditySpecList集合中的commoditySpec对象的commodityListId属性中
        commoditySpecList.forEach(commoditySpec -> {
            commoditySpec.setId(null);
            commoditySpec.setStatus(CommodityEnum.UP_SHELF.getCode());
            commoditySpec.setCommodityListId(commodityList.getId());
            //补充价格日期和默认市场价格
            setDateAndPrice(commoditySpec);
            //比较价格状态
            comparePrice(commoditySpec);
        });

        // 批量保存，如果保存失败，抛出异常
        boolean result3 = commoditySpecService.saveBatch(commoditySpecList);
        if (!result3) {
            throw new ServiceException("商品规格数据更新失败");
        }
        //保存商品价格日志
        saveCommodityPriceLog(commoditySpecList);

        // 到此，说明result1，result2， result3都位true，直接返回成功即可
        return result1 && result2 && result3;
    }
//
//	// 校验传过来的供应商id，是否在数据库中的供应商列表中
//	public void checkValid(CommodityListDTO commodityListDTO) {
//		List<CustomerSupplier> customerSuppliers = SpringUtil.getBean(ICustomerSupplierService.class).lambdaQuery().eq(CustomerSupplier::getStatus, CommonConstant.OPENSTATUS).list();
//		List<Long> customerSupplerIds = customerSuppliers.stream().map(CustomerSupplier::getId).collect(Collectors.toList());
//		Assert.isFalse(!customerSupplerIds.contains(commodityListDTO.getSupplierId()), "请输入合法的供应商");
//	}

    /**
     * 金额格式化
     *
     * @param commodityList
     */
    private CommodityList formatAmount(CommodityList commodityList) {
        BigDecimal min = commodityList.getMinUnitPrice();
        BigDecimal max = commodityList.getMaxUnitPrice();

        String minAmount = NumberUtil.decimalFormat("##,##0.00", min);
        String maxAmount = NumberUtil.decimalFormat("##,##0.00", max);
        commodityList.setUnitPriceSection(minAmount + "元" + "~" + maxAmount + "元");
        return commodityList;
    }

    /**
     * TreeDTO 只是用来封装数据的模型类  不用太在意这个
     */
    public List<TreeDTO> findChild(List<CommodityCatalogue> commodityCatalogueList, Long parentId) {
        ICommodityCatalogueService commodityCatalogueService = SpringUtil.getBean(ICommodityCatalogueService.class);
        ArrayList<TreeDTO> treeDTOS = new ArrayList<>();
        //退出条件  没有子类数据则退出
        if (commodityCatalogueList != null) {
            //遍历传递过来这一级别的数据  获取id  根据id查询下一级
            for (CommodityCatalogue commodityCatalogue : commodityCatalogueList) {
                //查出以此为父类的所有子类数据
                List<CommodityCatalogue> commodityCatalogueList2 = commodityCatalogueService.findByParentId(commodityCatalogue.getId());
                //进行数据封装   start
                TreeDTO treeDTO = new TreeDTO();
                treeDTO.setId(commodityCatalogue.getId());
                treeDTO.setLabel(commodityCatalogue.getDirectName());
                treeDTO.setParentId(commodityCatalogue.getParentId());
                //判断  如果这条数据的parentId与传递过来的parentId相等则添加进childList中
                if (commodityCatalogue.getParentId().equals(parentId)) {
                    //并进行 递归操作
                    treeDTO.setChild(findChild(commodityCatalogueList2, commodityCatalogue.getId()));
                }
                //进行数据封装   end
                treeDTOS.add(treeDTO);
            }
            return treeDTOS;
        }
        return treeDTOS;
    }

    @Override
    public IPage<CommodityListVO> selectPurchaseCommodityReleasePage(IPage<CommodityListVO> page, CommodityListVO commodityListVO) {
        commodityListVO.setStatus(1);
        IPage<CommodityListVO> commodityListVOIPage = this.selectCommodityListPage(page, commodityListVO);
//		 List<CommodityListVO> records = commodityListVOIPage.getRecords();
//		 List<CommodityListVO> collect = records.stream()
//			 .filter(record -> record.getStatus() == 1)
//			 .collect(Collectors.toList());
//		 commodityListVOIPage.setRecords(collect);
        return commodityListVOIPage;
    }

    @Override
    public List<CommodityListVO> listCommodity(Long goodsId) {
        //TODO 白名单需要重新考虑
        //Long commodityWhit = commodityWhiteListId(goodsId);
//		if (ObjectUtil.isEmpty(commodityWhit)) {
//			return new ArrayList<>();
//		}
//		List<CommodityWhiteListCommodity> list = SpringUtil.getBean(ICommodityWhiteListCommodityService.class).lambdaQuery()
//				.eq(CommodityWhiteListCommodity::getCommodityWhiteListId, commodityWhit).list();
//		List<Long> commodityIds = StreamUtil.map(list, CommodityWhiteListCommodity::getCommodityId);
//		if (ObjectUtil.isEmpty(commodityIds)) {
//			return new ArrayList<>();
//		}
//		List<CommodityListVO> commodityLists = baseMapper.selectPurchaseCommodityPageLimit(commodityIds);
//
//		List<CommodityListVO> commodityList = getCommodityList(commodityLists);
//
//		return commodityList;

        return new ArrayList<>();
    }

//	private Long commodityWhiteListId(Long goodsId) {
//		AgentGoods ags = SpringUtil.getBean(IAgentGoodsService.class).lambdaQuery().eq(BaseEntity::getId, goodsId).one();
//		if (ObjectUtil.isNotEmpty(ags)) {
//			return ags.getCommodityWhiteListId();
//		}
//		return null;
//	}

}
