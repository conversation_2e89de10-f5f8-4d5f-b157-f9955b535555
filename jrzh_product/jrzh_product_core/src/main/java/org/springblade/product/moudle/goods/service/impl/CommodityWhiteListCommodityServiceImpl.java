/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.goods.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.product.common.entity.CommodityWhiteListCommodity;
import org.springblade.product.common.vo.CommodityWhiteListCommodityVO;
import org.springblade.product.moudle.goods.mapper.CommodityWhiteListCommodityMapper;
import org.springblade.product.moudle.goods.service.ICommodityWhiteListCommodityService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品白名单关联商品中间表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Service
public class CommodityWhiteListCommodityServiceImpl extends BaseServiceImpl<CommodityWhiteListCommodityMapper, CommodityWhiteListCommodity> implements ICommodityWhiteListCommodityService {

	@Override
	public IPage<CommodityWhiteListCommodityVO> selectCommodityWhiteListCommodityPage(IPage<CommodityWhiteListCommodityVO> page, CommodityWhiteListCommodityVO commodityWhiteListCommodity) {
		return page.setRecords(baseMapper.selectCommodityWhiteListCommodityPage(page, commodityWhiteListCommodity));
	}

	@Override
	public List<CommodityWhiteListCommodity> listByTemplateIds(List<Long> templateIds) {
		return list(Wrappers.<CommodityWhiteListCommodity>lambdaQuery()
			.in(CommodityWhiteListCommodity::getCommodityWhiteListId, templateIds));
	}

}
