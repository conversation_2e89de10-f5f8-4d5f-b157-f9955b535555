/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.qualityproduct.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantContextHolder;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.product.common.dto.ProductSelect;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.entity.QualityProducts;
import org.springblade.product.common.vo.QualityProductsVO;
import org.springblade.product.moudle.qualityproduct.service.IQualityProductsRelationService;
import org.springblade.product.moudle.qualityproduct.service.IQualityProductsService;
import org.springblade.product.moudle.qualityproduct.wrapper.QualityProductsWrapper;
import org.springblade.resource.cache.ParamCache;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * 优质产品 控制器
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_FRONT + CommonConstant.WEB_BACK + "/front/qualityProducts")
@Api(value = "优质产品", tags = "优质产品接口")
public class QualityProductsController extends BladeController {

    private final IQualityProductsService qualityProductsService;
    private final IQualityProductsRelationService qualityProductsRelationService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入qualityProducts")
    @PreAuth("hasPermission('front:qualityProducts:detail') or hasRole('administrator')")
    public R<QualityProductsVO> detail(QualityProducts qualityProducts) {
        QualityProducts detail = qualityProductsService.getOne(Condition.getQueryWrapper(qualityProducts));
        return R.data(QualityProductsWrapper.build().entityVO(detail));
    }

    /**
     * 分页 优质产品
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入qualityProducts")
    @PreAuth("hasPermission('front:qualityProducts:list') or hasRole('administrator')")
    public R<IPage<QualityProductsVO>> list(QualityProducts qualityProducts, Query query) {
        IPage<QualityProducts> pages = qualityProductsService.page(Condition.getPage(query), Condition.getQueryWrapper(qualityProducts)
                .orderByAsc("sort")
        );
        return R.data(QualityProductsWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 优质产品
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入qualityProducts")
    @PreAuth("hasPermission('front:qualityProducts:page') or hasRole('administrator')")
    public R<IPage<QualityProductsVO>> page(QualityProductsVO qualityProducts, Query query) {
        IPage<QualityProductsVO> pages = qualityProductsService.selectQualityProductsPage(Condition.getPage(query), qualityProducts);
        return R.data(pages);
    }

    /**
     * 新增 优质产品
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入qualityProducts")
    @PreAuth("hasPermission('front:qualityProducts:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody QualityProducts qualityProducts) {
        return R.status(qualityProductsRelationService.saveQualityProducts(qualityProducts));
    }

    /**
     * 修改 优质产品
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入qualityProducts")
    @PreAuth("hasPermission('front:qualityProducts:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody QualityProducts qualityProducts) {
        return R.status(qualityProductsRelationService.updateQualityProducts(qualityProducts));
    }

    /**
     * 新增或修改 优质产品
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入qualityProducts")
    @PreAuth("hasPermission('front:qualityProducts:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody QualityProducts qualityProducts) {
        if (qualityProducts.getId() == null) {
            int count = qualityProductsService.count(Wrappers.<QualityProducts>lambdaQuery().eq(QualityProducts::getProductsId, qualityProducts.getProductsId()));
            Assert.isFalse(count > 0, "优质产品不能重复添加");
        } else {
            int calculate = qualityProductsService.count(Wrappers.<QualityProducts>lambdaQuery().eq(QualityProducts::getProductsId, qualityProducts.getProductsId()));
            Assert.isFalse(calculate >= 2, "优质产品不能重复添加");
        }
        if (ObjectUtil.isEmpty(qualityProducts.getId())) {
            qualityProductsRelationService.saveQualityProducts(qualityProducts);
        } else {
            qualityProductsRelationService.updateQualityProducts(qualityProducts);
        }
        return R.status(true);
    }


    /**
     * 删除 优质产品
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('front:qualityProducts:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {

        List<Long> longs = Func.toLongList(ids);
        int count = qualityProductsService.count(Wrappers.<QualityProducts>lambdaQuery().in(BaseEntity::getId, longs).eq(BaseEntity::getStatus, CommonConstant.OPEN_RELEASE));
        if (count > 0) {
            return R.fail("存在已发布的数据,请检查后删除！");
        }

        return R.status(qualityProductsService.deleteLogic(Func.toLongList(ids)));
    }

    @GetMapping("/getProductsTypeList")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "根据不同的类别获取数据", notes = "传入ids")
//	@PreAuth("hasPermission('front:qualityProducts:getProductsTypeList') or hasRole('administrator')")
    public R<Object> getProductsTypeList(String productsType) {
        if ("undefined".equals(productsType)) {
            return R.data(null);
        }
        return R.data(qualityProductsService.getProductsTypeList(Func.toInt(productsType)));
    }

    @GetMapping("/product/select")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "产品查询", notes = "产品查询")
    public R<List<ProductSelect>> getProductsSelectAll() {
        String tenantId = Func.toStr(AuthUtil.getTenantId(), Func.toStr(TenantContextHolder.getTenantId(), "000000"));
        //如果系统配置 优先系统配置
        String value = ParamCache.getValue("GOODS_TYPE_LIST");
        Map<String, String> map = JSONUtil.toBean(value, Map.class);
        String s = map.get(tenantId);
        List<Integer> productTypes = Func.toIntList(s);

        List<ProductSelect> productSelectList = new ArrayList<>();
        Set<Long> processedProductIds = new HashSet<>();

        productTypes.forEach(type->{
            List<Product> productList = (List<Product>)qualityProductsService.getProductsTypeList(type);
            if (CollUtil.isNotEmpty(productList)){
                productList.forEach(product -> {
                    if (processedProductIds.contains(product.getId())) {
                        return;
                    }
                    ProductSelect productSelect = new ProductSelect();
                    productSelect.setProductId(product.getId());
                    productSelect.setProductName(product.getGoodsName());
                    StringBuilder fullName = new StringBuilder(GoodsEnum.getNameByCode(type));
                    fullName.append("|").append(product.getGoodsName());
                    productSelect.setProductFullName(fullName.toString());
                    productSelect.setProductFullName(fullName.toString());
                    productSelectList.add(productSelect);
                    processedProductIds.add(product.getId());
                });
            }
        });

        return R.data(productSelectList);
    }


    /**
     * 批量禁用
     */
    @PostMapping("/conlistoff")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "批量禁用", notes = "传入ids")
    public R conlistoff(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(qualityProductsRelationService.qualityCommdityOff(Func.toLongList(ids)));
    }

    /**
     * 发布
     */
    @PostMapping("/conlistput")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "发布", notes = "传入ID")
    public R conlistput(Long id) {
        return R.status(qualityProductsRelationService.qualityCommdityPut(id));
    }

    /**
     * 批量启用
     */
    @PostMapping("/handlerOnShelf")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "批量启用", notes = "传入ids")
    public R<Integer> handlerOnShelf(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.data(qualityProductsRelationService.handlerOnShelf(Func.toLongList(ids)));
    }
}
