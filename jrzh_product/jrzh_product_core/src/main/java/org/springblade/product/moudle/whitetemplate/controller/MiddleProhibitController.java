/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.whitetemplate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.product.common.entity.MiddleProhibit;
import org.springblade.product.common.vo.MiddleProhibitVO;
import org.springblade.product.moudle.whitetemplate.service.IMiddleProhibitService;
import org.springblade.product.moudle.whitetemplate.wrapper.MiddleProhibitWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 禁止入 控制器
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK+CommonConstant.WEB_BACK+"/customer/middleprohibit")
@Api(value = "禁止入", tags = "禁入接口")
public class MiddleProhibitController extends BladeController {

	private final IMiddleProhibitService middleProhibitService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入middleProhibit")
	public R<MiddleProhibitVO> detail(MiddleProhibit middleProhibit) {
		MiddleProhibit detail = middleProhibitService.getOne(Condition.getQueryWrapper(middleProhibit));
		return R.data(MiddleProhibitWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 禁止入
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入middleProhibit")
	public R<IPage<MiddleProhibitVO>> list(MiddleProhibit middleProhibit, Query query) {
		IPage<MiddleProhibit> pages = middleProhibitService.page(Condition.getPage(query), Condition.getQueryWrapper(middleProhibit));
		return R.data(MiddleProhibitWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 禁止入
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入middleProhibit")
	public R<IPage<MiddleProhibitVO>> page(MiddleProhibitVO middleProhibit, Query query) {
		IPage<MiddleProhibitVO> pages = middleProhibitService.selectMiddleProhibitPage(Condition.getPage(query), middleProhibit);
		return R.data(pages);
	}

	/**
	 * 新增 禁止入
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入middleProhibit")
	public R save(@Valid @RequestBody MiddleProhibit middleProhibit) {
		return R.status(middleProhibitService.save(middleProhibit));
	}

	/**
	 * 修改 禁止入
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入middleProhibit")
	public R update(@Valid @RequestBody MiddleProhibit middleProhibit) {
		return R.status(middleProhibitService.updateById(middleProhibit));
	}

	/**
	 * 新增或修改 禁止入
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入middleProhibit")
	public R submit(@Valid @RequestBody MiddleProhibit middleProhibit) {
		return R.status(middleProhibitService.saveOrUpdate(middleProhibit));
	}


	/**
	 * 删除 禁止入
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(middleProhibitService.deleteLogic(Func.toLongList(ids)));
	}


}
