package org.springblade.product.moudle.productgroup.controller.front;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>angkai
 * @CreateTime: 2024-04-23  17:54
 * @Description: 产品组控制器
 * @Version: 1.0
 * @module 平台接口/资金路由
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS + CommonConstant.WEB_FRONT + "/product-group")
@Api(value = "平台接口/资金路由", tags = "平台接口/资金路由")
public class ProductFrontGroupController {

}
