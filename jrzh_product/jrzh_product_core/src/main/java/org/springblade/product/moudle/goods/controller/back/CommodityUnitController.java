/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.goods.controller.back;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.product.common.constant.CommodityEnum;
import org.springblade.product.common.entity.CommodityList;
import org.springblade.product.common.entity.CommodityUnit;
import org.springblade.product.common.vo.CommodityUnitVO;
import org.springblade.product.moudle.goods.service.ICommodityListService;
import org.springblade.product.moudle.goods.service.ICommodityUnitService;
import org.springblade.product.moudle.goods.wrapper.CommodityUnitWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2021-12-17
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_COMMODITY + CommonConstant.WEB_BACK + "/commodityunit")
@Api(value = "商品单位", tags = "接口")
public class CommodityUnitController extends BladeController {

	private final ICommodityUnitService commodityUnitService;
	private final ICommodityListService iCommodityListService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入commodityUnit")
	public R<CommodityUnitVO> detail(CommodityUnit commodityUnit) {
		CommodityUnit detail = commodityUnitService.getOne(Condition.getQueryWrapper(commodityUnit));
		return R.data(CommodityUnitWrapper.build().entityVO(detail));
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入commodityUnit")
	public R<IPage<CommodityUnitVO>> list(CommodityUnit commodityUnit, Query query) {
		IPage<CommodityUnit> pages = commodityUnitService.page(Condition.getPage(query), Condition.getQueryWrapper(commodityUnit)
			.lambda().orderByAsc(CommodityUnit::getSort));
		return R.data(CommodityUnitWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入commodityUnit")
	public R<IPage<CommodityUnitVO>> page(CommodityUnitVO commodityUnit, Query query) {
		IPage<CommodityUnitVO> pages = commodityUnitService.selectCommodityUnitPage(Condition.getPage(query), commodityUnit);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入commodityUnit")
	public R<Boolean> save(@Valid @RequestBody CommodityUnit commodityUnit) {

		// 如果前端传过来的status值不是1-启用，就设置为0-禁用
		if (!CommodityEnum.ABLE.getCode().equals(commodityUnit.getStatus())) {
			commodityUnit.setStatus(CommodityEnum.DISABLE.getCode());
		}

		// 如果计量单位名已存在，提示，该计量单位名以存在，请修改后重试
		CommodityUnit commodityUnit1 = commodityUnitService.getOne(Wrappers.<CommodityUnit>lambdaQuery().eq(CommodityUnit::getUnitName, commodityUnit.getUnitName()));
		if (ObjectUtil.isNotEmpty(commodityUnit1)) {
			return R.fail("该计量单位名已存在，请修改后重试");
		}
		return R.status(commodityUnitService.save(commodityUnit));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入commodityUnit")
	public R<Boolean> update(@Valid @RequestBody CommodityUnit commodityUnit) {
		return R.status(commodityUnitService.updateById(commodityUnit));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入commodityUnit")
	public R<Boolean> submit(@Valid @RequestBody CommodityUnit commodityUnit) {
		return R.status(commodityUnitService.saveOrUpdate(commodityUnit));
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> listIds = Func.toLongList(ids);

		// 根据计量单位的主键ids，作为主键id，查询计量单位表，获取已启用的计量单位列表
		List<CommodityUnit> enableList = commodityUnitService.lambdaQuery().in(listIds.size() > 0, CommodityUnit::getId, listIds).eq(CommodityUnit::getStatus, CommodityEnum.ABLE.getCode()).list();

		// 根据计量单位的主键ids，作为unitId，查询商品表，获取商品列表
		List<CommodityList> commodityLists = iCommodityListService.lambdaQuery().in(listIds.size() > 0, CommodityList::getUnitId, listIds).list();

		if (enableList.size() > 0) {
			return R.fail("请先禁用后再进行删除");
		} else if (commodityLists.size() > 0) {
			return R.fail("该计量单位已绑定商品，不能删除");
		}
		return R.status(commodityUnitService.deleteLogic(Func.toLongList(ids)));
	}

	@PostMapping("/changeStatus")
	public R changeStatus(@ApiParam(value = "主键集合", required = true) @RequestParam String ids, @RequestParam Integer status) {
		return commodityUnitService.changeUnitStatus(Func.toLongList(ids), status);
	}

	@GetMapping("/all")
	public R<List<CommodityUnit>> getAll() {
		return R.data(commodityUnitService.lambdaQuery().eq(CommodityUnit::getStatus, 1).list());
	}

	@GetMapping("/unit-all")
	public R<List<CommodityUnit>> geUnitAll() {
		return R.data(commodityUnitService.list());
	}
}
