<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.product.moudle.goods.mapper.CommodityUnitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="goodsUnitResultMap" type="org.springblade.product.common.entity.CommodityUnit">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="unit_name" property="unitName"/>
        <result column="sort" property="sort"/>
    </resultMap>


    <select id="selectCommodityUnitPage" resultMap="goodsUnitResultMap">
        select * from jrzh_commodity_unit where is_deleted = 0
    </select>

</mapper>
