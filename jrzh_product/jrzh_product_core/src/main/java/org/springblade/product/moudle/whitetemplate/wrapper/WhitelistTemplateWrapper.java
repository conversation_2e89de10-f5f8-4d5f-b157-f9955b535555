/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.whitetemplate.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.product.common.entity.WhitelistTemplate;
import org.springblade.product.common.vo.WhitelistTemplateVO;

import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
public class WhitelistTemplateWrapper extends BaseEntityWrapper<WhitelistTemplate, WhitelistTemplateVO>  {

	public static WhitelistTemplateWrapper build() {
		return new WhitelistTemplateWrapper();
 	}

	@Override
	public WhitelistTemplateVO entityVO(WhitelistTemplate whitelistTemplate) {
		WhitelistTemplateVO whitelistTemplateVO = Objects.requireNonNull(BeanUtil.copy(whitelistTemplate, WhitelistTemplateVO.class));

		//User createUser = UserCache.getUser(whitelistTemplate.getCreateUser());
		//User updateUser = UserCache.getUser(whitelistTemplate.getUpdateUser());
		//whitelistTemplateVO.setCreateUserName(createUser.getName());
		//whitelistTemplateVO.setUpdateUserName(updateUser.getName());

		return whitelistTemplateVO;
	}

}
