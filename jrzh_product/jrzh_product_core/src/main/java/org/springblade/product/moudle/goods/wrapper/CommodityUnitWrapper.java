/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.goods.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.product.common.entity.CommodityUnit;
import org.springblade.product.common.vo.CommodityUnitVO;

import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 * <p>
 * author z<PERSON><PERSON>an
 *
 * @since 2021-12-17
 */
public class CommodityUnitWrapper extends BaseEntityWrapper<CommodityUnit, CommodityUnitVO> {

	public static CommodityUnitWrapper build() {
		return new CommodityUnitWrapper();
	}

	@Override
	public CommodityUnitVO entityVO(CommodityUnit commodityUnit) {


		return Objects.requireNonNull(BeanUtil.copy(commodityUnit, CommodityUnitVO.class));
	}

}
