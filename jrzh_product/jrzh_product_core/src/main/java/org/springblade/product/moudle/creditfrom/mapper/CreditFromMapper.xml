<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.product.moudle.creditfrom.mapper.CreditFromMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="creditFromResultMap" type="org.springblade.product.common.entity.CreditFrom">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="from_name" property="fromName"/>
    </resultMap>


    <select id="selectCreditFromPage" resultType="org.springblade.product.common.vo.CreditFromVO">
        SELECT
            a.id,
            a.from_name,
            a.goods_type,
            a.update_time,
            b.real_name,
            a.STATUS
        FROM
            jrzh_riskmana_credit_from a
                LEFT JOIN blade_user b ON a.create_user = b.id
        WHERE
            a.is_deleted = 0
    </select>

</mapper>
