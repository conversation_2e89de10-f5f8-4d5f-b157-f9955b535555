<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.product.moudle.whitetemplate.mapper.WhitelistTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="whitelistTemplateResultMap" type="org.springblade.product.common.entity.WhitelistTemplate">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="template_name" property="templateName"/>
        <result column="whitelist_type" property="whitelistType"/>
        <result column="sort" property="sort"/>
        <result column="whitelist_template_code" property="whitelistTemplateCode"/>
        <result column="black_white_type" property="blackWhiteType"/>
        <result column="goods_codes" property="goodsCodes"/>
        <result column="business_type" property="businessType"/>
        <result column="tenant_id" property="tenantId"></result>
    </resultMap>


    <select id="selectWhitelistTemplatePage" resultMap="whitelistTemplateResultMap">
        select * from jrzh_whitelist_template where is_deleted = 0
    </select>

</mapper>
