/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.timing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.product.common.dto.GoodsTimingDTO;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.entity.GoodsTiming;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.moudle.timing.mapper.GoodsTimingMapper;
import org.springblade.product.moudle.timing.service.IProductTimingConfigInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品标签关联操作 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
@RequiredArgsConstructor
public class ProductTimingConfigInfoIml extends BaseServiceImpl<GoodsTimingMapper, GoodsTiming> implements IProductTimingConfigInfo {

    /**
     * 保存/修改产品标签
     *
     * @param productDTO
     */
    private void saveOrUpdateProductTiming(ProductDTO productDTO) {
        Long goodsId = productDTO.getId();
        List<GoodsTimingDTO> goodsTimingList = productDTO.getGoodsTimingList();
        removeByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(goodsTimingList)) {
            return;
        }
        List<GoodsTiming> list = BeanUtil.copyToList(goodsTimingList, GoodsTiming.class);
        list.forEach(goodsTiming -> {
            goodsTiming.setGoodsId(goodsId);
            goodsTiming.setId(null);
        });
        saveBatch(list);
    }

    private void removeByGoodsId(Long goodsId) {
        baseMapper.delete(Wrappers.<GoodsTiming>lambdaQuery().eq(GoodsTiming::getGoodsId, goodsId));
    }

    @Override
    public Integer support() {
        return GoodsEnum.PRODUCT_CONFIG_TIMING.getCode();
    }

    @Override
    public List<GoodsTimingDTO> configInfo(List<Long> goodsId, LambdaQueryWrapper<GoodsTiming> queryWrapper) {
        if (queryWrapper == null) {
            queryWrapper = new LambdaQueryWrapper<>();
        }
        List<GoodsTiming> expenseAccountRelationList = baseMapper.selectList(queryWrapper.in(GoodsTiming::getGoodsId, goodsId));
        return BeanUtil.copyToList(expenseAccountRelationList, GoodsTimingDTO.class);
    }

    @Override
    public void copyWithGoods(ProductDTO productDTO, Long oldGoodsId) {
        if (CollUtil.isNotEmpty(productDTO.getGoodsTimingList())) {
            List<GoodsTiming> goodsTimingList = new ArrayList<>();
            productDTO.getGoodsTimingList().forEach(e -> {
                GoodsTiming goodsTiming = BeanUtil.copyProperties(e, GoodsTiming.class, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
                goodsTiming.setGoodsId(productDTO.getId());
                goodsTimingList.add(goodsTiming);
            });
            saveBatch(goodsTimingList);
        }
    }


    @Override
    public void assembleProduct(ProductVO productVO) {
        Long goodsId = productVO.getId();
        List<GoodsTiming> list = this.list(Wrappers.<GoodsTiming>lambdaQuery().eq(GoodsTiming::getGoodsId, goodsId));
        productVO.setGoodsTimingList(BeanUtil.copyToList(list, GoodsTimingDTO.class));
    }

    @Override
    public void saveWithProduct(ProductDTO productDTO) {
        saveOrUpdateProductTiming(productDTO);
    }

    @Override
    public void updateWithProduct(ProductDTO productDTO) {
        saveOrUpdateProductTiming(productDTO);
    }

    @Override
    public void onShelfWithProduct(List<Long> ids) {
    }

    @Override
    public void onShelfWithProduct(Product product) {
        Long goodsId = product.getId();
        List<GoodsTiming> goodsTimingList = list(Wrappers.<GoodsTiming>lambdaQuery().eq(GoodsTiming::getGoodsId, goodsId));
        if (product.getType().equals(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode())) {
            if (CollUtil.isEmpty(goodsTimingList) || goodsTimingList.size() < 4) {
                throw new ServiceException("请将定时任务的信息填写完成,再上架");
            }
        } else {
            if (CollUtil.isEmpty(goodsTimingList) || goodsTimingList.size() < 3) {
                throw new ServiceException("请将定时任务的信息填写完成,再上架");
            }
        }
    }

    @Override
    public void offShelfWithProduct(List<Long> ids) {

    }
}
