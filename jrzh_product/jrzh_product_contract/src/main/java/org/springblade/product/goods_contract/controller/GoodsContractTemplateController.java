/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.goods_contract.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.product.common.vo.GoodsContractTemplateVO;
import org.springblade.product.goods_contract.service.IGoodsContractTemplateService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 产品合同模板 控制器
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS+CommonConstant.WEB_BACK+"/goods/goodsContractTemplate")
@Api(value = "产品合同模板", tags = "产品合同模板接口")
public class GoodsContractTemplateController extends BladeController {

	private final IGoodsContractTemplateService goodsContractTemplateService;

	/**
	 * 分页 产品合同模板
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入goodsContractTemplate")
   @PreAuth( "hasPermission('goods:detail') or hasRole('administrator')")
	public R<List<GoodsContractTemplateVO>> list(Long goodsId) {
		//List<GoodsContractTemplateVO> goodsContractTemplateVOS = goodsContractTemplateService.selectGoodsContractTemplateListByGoodsId(goodsId);
		List<GoodsContractTemplateVO> vos = goodsContractTemplateService.getGoodsContractTemplateListByGoodsId(goodsId);
		return R.data(vos);
	}

}
