<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.product.goods_contract.mapper.GoodsContractTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="goodsContractTemplateResultMap" type="org.springblade.product.common.entity.GoodsContractTemplate">
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="goods_id" property="goodsId"/>
        <result column="contract_template_name" property="contractTemplateName"/>
        <result column="sort" property="sort"/>
        <result column="link" property="link"/>
    </resultMap>


</mapper>
