/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.goods_contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.contract.entity.ContractSignConfig;
import org.springblade.modules.contract.entity.ContractTemplate;
import org.springblade.modules.contract.service.IContractSignConfigService;
import org.springblade.modules.contract.service.IContractTemplateService;
import org.springblade.product.common.dto.ContractNodeDTO;
import org.springblade.product.common.dto.GoodsContractTemplateDTO;
import org.springblade.product.common.dto.GoodsContractTemplateInfo;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.entity.GoodsContractTemplate;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.goods_contract.mapper.GoodsContractTemplateMapper;
import org.springblade.product.goods_contract.service.IBaseContractService;
import org.springblade.product.goods_contract.service.IProductContractConfigInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品合同模板 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Service
@RequiredArgsConstructor
public class IProductContractConfigInfoImpl extends BaseServiceImpl<GoodsContractTemplateMapper, GoodsContractTemplate> implements IProductContractConfigInfo {
    private final IContractTemplateService contractTemplateService;
    private final GoodsContractTemplateMapper goodsContractTemplateMapper;
    private final IContractSignConfigService contractSignConfigService;
    private final IBaseContractService baseContractService;

    @Override
    public void assembleProduct(ProductVO productVO) {
        List<GoodsContractTemplateDTO> dtoList = baseContractService.selectGoodsContractTemplateListByGoodsId(Collections.singletonList(productVO.getId()));
        productVO.setGoodsContractTemplates(dtoList);
    }

    @Override
    public void saveWithProduct(ProductDTO productVO) {
        List<GoodsContractTemplateDTO> dtoList = productVO.getGoodsContractTemplates();
        //拆分合同签署节点
        List<GoodsContractTemplate> goodsContractTemplates = getGoodsContractTemplates(dtoList);
        Long goodsId = productVO.getId();
        if (CollectionUtils.isEmpty(goodsContractTemplates)) {
            return;
        }
        saveGoodsContractTemplate(goodsContractTemplates, goodsId);
    }

    @Override
    public void updateWithProduct(ProductDTO productVO) {
        List<GoodsContractTemplateDTO> dtoList = productVO.getGoodsContractTemplates();
        //拆分合同签署节点
        List<GoodsContractTemplate> goodsContractTemplates = getGoodsContractTemplates(dtoList);
        Long goodsId = productVO.getId();
        // 删除当前产品关联的合同模板
        goodsContractTemplateMapper.deleteByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(goodsContractTemplates)) {
            return;
        }
        saveGoodsContractTemplate(goodsContractTemplates, goodsId);
    }

    private void saveGoodsContractTemplate(List<GoodsContractTemplate> goodsContractTemplates, Long goodsId) {
        // 查询合同模板
        List<String> goodsContractTemplateIds = StreamUtil.map(goodsContractTemplates, GoodsContractTemplate::getContractTemplateId);
        List<ContractTemplate> contractTemplates = contractTemplateService.listByTemplates(goodsContractTemplateIds);
        Map<String, ContractTemplate> contractTemplateMap = contractTemplates.stream().collect(Collectors.toMap(ContractTemplate::getTemplateId, obj -> obj, (oldVal, newVal) -> oldVal));

        goodsContractTemplates = goodsContractTemplates.stream().map(goodsContractTemplate1 -> {
            ContractTemplate contractTemplate = contractTemplateMap.get(goodsContractTemplate1.getContractTemplateId());
            if (Objects.nonNull(contractTemplate)) {
                goodsContractTemplate1.setContractTemplateName(contractTemplate.getTemplateName());
            }
            goodsContractTemplate1.setGoodsId(goodsId);
            return goodsContractTemplate1;
        }).collect(Collectors.toList());
        saveBatch(goodsContractTemplates);
    }

    private void checkContractTemplate(List<GoodsContractTemplateDTO> goodsContractTemplates) {
        if (CollectionUtil.isEmpty(goodsContractTemplates)) {
            return;
        }
        Set<String> templateIds = goodsContractTemplates.stream().map(GoodsContractTemplateDTO::getContractTemplateId).collect(Collectors.toSet());
        Map<String, List<ContractSignConfig>> templateSignConfigMap = contractSignConfigService.mapByTemplateIds(new ArrayList<>(templateIds));
        for (GoodsContractTemplateDTO goodsContractTemplate : goodsContractTemplates) {
            GoodsContractTemplateInfo contractTemplate = goodsContractTemplate.getContractTemplate();
            String signNode = contractTemplate.getSignNode();
            String goodsSignNodes = goodsContractTemplate.getSignNode();
            if (StringUtil.isBlank(goodsSignNodes)) {
                throw new ServiceException("请将合同签署节点补充完毕");
            }
            for (String node : Func.toStrList(goodsSignNodes)) {
                if (!signNode.contains(node)) {
                    throw new ServiceException(String.format("合同模板[ %s ]签署节点设置错误", contractTemplate.getTemplateName()));
                }
            }
            if (!templateSignConfigMap.containsKey(contractTemplate.getTemplateId())) {
                throw new ServiceException(String.format("合同模板[ %s ]签署关键字未配置", contractTemplate.getTemplateName()));
            }
        }
    }


    /**
     * 拆分合同签署节点
     *
     * @param dtoList
     */
    private List<GoodsContractTemplate> getGoodsContractTemplates(List<GoodsContractTemplateDTO> dtoList) {
        List<GoodsContractTemplate> goodsContractTemplates = new ArrayList<>();
        for (GoodsContractTemplateDTO dto : dtoList) {
            List<ContractNodeDTO> userNodeMap = dto.getUserNodeList();
            for (ContractNodeDTO contractNode : userNodeMap) {
                GoodsContractTemplate template = new GoodsContractTemplate();
                template.setContractTemplateId(dto.getContractTemplateId());
                template.setTemplateId(dto.getTemplateId());
                template.setSort(dto.getSort());
                template.setSignUser(contractNode.getSignUser());
                template.setSignNode(contractNode.getSignNode());
                goodsContractTemplates.add(template);
            }
        }
        return goodsContractTemplates;
    }

    @Override
    public void onShelfWithProduct(List<Long> goodsIds) {
        //查询
        List<GoodsContractTemplateDTO> goodsContractTemplateVOList = baseContractService.selectGoodsContractTemplateListByGoodsId(goodsIds);
        //合同配置检查
        checkContractTemplate(goodsContractTemplateVOList);
    }

    @Override
    public void offShelfWithProduct(List<Long> goodsIds) {

    }

    @Override
    public Integer support() {
        return GoodsEnum.PRODUCT_CONFIG_CONTRACT.getCode();
    }

    @Override
    public List<GoodsContractTemplateDTO> configInfo(List<Long> goodsId, LambdaQueryWrapper<GoodsContractTemplate> queryWrapper) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void copyWithGoods(ProductDTO productDTO, Long oldGoodsId) {
        if (CollUtil.isNotEmpty(productDTO.getGoodsContractTemplates())) {
            List<GoodsContractTemplate> goodsContractTemplateList = new ArrayList<>();
            productDTO.getGoodsContractTemplates().forEach(e -> {
                GoodsContractTemplate goodsContractTemplate = BeanUtil.copyProperties(e, GoodsContractTemplate.class, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
                goodsContractTemplate.setGoodsId(productDTO.getId());
                goodsContractTemplateList.add(goodsContractTemplate);
            });
            saveBatch(goodsContractTemplateList);
        }
    }
}
