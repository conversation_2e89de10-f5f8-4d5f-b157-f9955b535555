package org.springblade.product.expense_relation.handler.impl;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.product.expense_relation.handler.GoodsBillBankCardBankCleanHandler;
import org.springblade.product.expense_relation.handler.GoodsBillBankCardHandler;
import org.springblade.product.expense_relation.handler.GoodsBillBankCardIndependenceHandler;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-04-15  14:09
 * @Description: 产品账户策略
 * @Version: 1.0
 */
@Component
@RequiredArgsConstructor
public class GoodsBillBankCardStrategy {
    //银行清分类型
    private final List<GoodsBillBankCardBankCleanHandler> cardBankCleanHandlerList;
    //银行独立收取类型
    private final List<GoodsBillBankCardIndependenceHandler> cardIndependenceHandlerList;

    //获取实现类
    public GoodsBillBankCardHandler instance(Integer chargeMethod, Integer type) {
        if (GoodsEnum.UNIFIED.getCode().equals(chargeMethod)) {
            return getBankCleanHandler(type);
        }
        if (GoodsEnum.ALONE.getCode().equals(chargeMethod)) {
            return getIndependenceHandler(type);
        }
        throw new UnsupportedOperationException();
    }

    //银行清分策略
    public GoodsBillBankCardBankCleanHandler getBankCleanHandler(Integer type) {
        for (GoodsBillBankCardBankCleanHandler cleanHandler : cardBankCleanHandlerList) {
            if (type.equals(cleanHandler.support())) {
                return cleanHandler;
            }
        }
        throw new UnsupportedOperationException();
    }

    //银行独立收取策略
    public GoodsBillBankCardIndependenceHandler getIndependenceHandler(Integer type) {
        for (GoodsBillBankCardIndependenceHandler cleanHandler : cardIndependenceHandlerList) {
            if (type.equals(cleanHandler.support())) {
                return cleanHandler;
            }
        }
        throw new UnsupportedOperationException();
    }
}
