package org.springblade.product.expense.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.product.common.entity.ExpenseType;
import org.springblade.product.common.vo.ExpenseTypeVO;

import java.util.Objects;

/**
 * 费用类型包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
public class ExpenseTypeWrapper extends BaseEntityWrapper<ExpenseType, ExpenseTypeVO> {

    public static ExpenseTypeWrapper build() {
        return new ExpenseTypeWrapper();
    }

    @Override
    public ExpenseTypeVO entityVO(ExpenseType ExpenseType) {
        ExpenseTypeVO ExpenseTypeVO = Objects.requireNonNull(BeanUtil.copy(ExpenseType, ExpenseTypeVO.class));

        //User createUser = UserCache.getUser(ExpenseType.getCreateUser());
        //User updateUser = UserCache.getUser(ExpenseType.getUpdateUser());
        //ExpenseTypeVO.setCreateUserName(createUser.getName());
        //ExpenseTypeVO.setUpdateUserName(updateUser.getName());

        return ExpenseTypeVO;
    }
}
