<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.product.expense_relation.mapper.BillBankCardaRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="billBankCardaRelationResultMap" type="org.springblade.product.common.entity.BillBankCardaRelation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="goods_id" property="goodsId"/>
        <result column="bill_bank_carda_id" property="billBankCardaId"/>
        <result column="product_type" property="productType"/>
    </resultMap>
    <delete id="deleteByGoodsId">
        delete from jrzh_bill_bank_carda_relation where goods_id = #{goodsId}
    </delete>


    <select id="selectBillBankCardaRelationPage" resultMap="billBankCardaRelationResultMap">
        select * from jrzh_bill_bank_carda_relation where is_deleted = 0
    </select>

</mapper>
