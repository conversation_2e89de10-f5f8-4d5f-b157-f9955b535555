package org.springblade.product.expense.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */
public interface ExpenseConstant {

    @AllArgsConstructor
    @Getter
    enum FeeNodeEnum {

        /**
         * 费用节点
         */
        LOAN_APPLY("放款申请", 1),
        NORMAL_REPAYMENT("正常还款", 2),
        OVERDUE_REPAYMENT("逾期还款", 3),
        ADVANCE_REPAYMENT("提前还款", 4),
        EXTENSION_APPLY("展期申请", 5),
        REDEMPTION_CONFIRM("赎货确认", 6),
        ADVANCE_SETTLE("提前结清", 7),
        FINANCE_APPLY("融资申请", 8),
        REDEMPTION_APPLY("赎货申请", 9),
        RETURN_BOND("退还保证金", 10),
        OVERDUE_CONSULT("逾期协商申请", 11),
        CARGO_SOLVE("货物处置", 13),
        ADJUST_INTEREST("调息", 14),
        ;

        private final String name;
        private final Integer code;
    }

    @AllArgsConstructor
    @Getter
    enum ExpenseTypeEnum {

        /**
         * 费用类型
         */
        SERVICE_CHARGE("服务费", 1),
        STORAGE_FEE("仓储费", 2),
        LOGISTICS_FEE("物流费", 3),
        INTEREST("利息", 4),
        OVERDUE_INTEREST("逾期利息", 5),
        PREPAYMENT_SERVICE_FEE("提前还款服务费", 6),
        ADVANCE_SETTLE_SERVICE_FEE("提前结清服务费", 7),
        EXTENSION_INTEREST("展期利息", 8),
        BOND("保证金", 9),
        TAX("增值税", 10),
        CUSTOMS_DUTY("海关关税", 11),
        PRINCIPAL("本金", 30),
        ;


        private final String name;
        private final Integer code;


        /**
         * 根据传入的key态获取value
         *
         * @param key 状态
         * @return value
         * <AUTHOR>
         */
        public static String getValueByKey(Integer key) {
            for (ExpenseTypeEnum s : ExpenseTypeEnum.values()) {
                if (s.getCode() == key) {
                    return s.getName();
                }
            }
            return "";
        }
    }

    @AllArgsConstructor
    @Getter
    enum FeeTypeEnum {

        /**
         * 费用类型
         */
        CAPITAL_CHARGE("资方费", 1),
        PLATFORM_CHARGE("平台费", 2);


        private final String name;
        private final Integer code;

        /**
         * 根据传入的key态获取value
         *
         * @param key 状态
         * @return value
         * <AUTHOR>
         */
        public static String getValueByKey(Integer key) {
            for (ExpenseTypeEnum s : ExpenseTypeEnum.values()) {
                if (s.getCode() == key) {
                    return s.getName();
                }
            }
            return "";
        }
    }

    @AllArgsConstructor
    @Getter
    enum ChargeMethodEnum {

        /**
         * 收费收取方式
         */
        ONE_TIME_PAYMENT("一次性支付", 1),
        INSTALLMENT("分期支付", 2);

        private final String name;
        private final Integer code;

    }
}
