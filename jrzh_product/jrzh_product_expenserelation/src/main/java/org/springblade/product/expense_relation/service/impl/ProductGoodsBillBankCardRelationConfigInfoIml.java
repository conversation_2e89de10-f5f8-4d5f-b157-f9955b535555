/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.expense_relation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.product.common.entity.BillBankCardaRelation;
import org.springblade.product.expense_relation.mapper.BillBankCardaRelationMapper;
import org.springblade.product.expense_relation.service.IProductGoodsBillBankCardRelationConfigInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品标签关联操作 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
@RequiredArgsConstructor
public class ProductGoodsBillBankCardRelationConfigInfoIml extends BaseServiceImpl<BillBankCardaRelationMapper, BillBankCardaRelation> implements IProductGoodsBillBankCardRelationConfigInfo {


    @Override
    public List<BillBankCardaRelation> configInfo(List<Long> goodsId, LambdaQueryWrapper<BillBankCardaRelation> queryWrapper) {
        if (queryWrapper == null) {
            queryWrapper = new LambdaQueryWrapper<>();
        }
        List<BillBankCardaRelation> expenseAccountRelationList = baseMapper.selectList(queryWrapper.in(BillBankCardaRelation::getGoodsId, goodsId));
        return BeanUtil.copyToList(expenseAccountRelationList, BillBankCardaRelation.class);
    }

    @Override
    public Integer support() {
        return GoodsEnum.PRODUCT_CONFIG_BILL_BANK_CARD_RELATION.getCode();
    }
}
