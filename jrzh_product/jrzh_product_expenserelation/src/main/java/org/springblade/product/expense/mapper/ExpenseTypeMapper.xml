<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.product.expense.mapper.ExpenseTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="expenseTypeResultMap" type="org.springblade.product.common.entity.ExpenseType">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="goods_type_id" property="goodsTypeId"/>
    </resultMap>


    <select id="selectExpenseTypePage" resultMap="expenseTypeResultMap">
        select * from jrzh_expense_type where is_deleted = 0
    </select>
    <select id="tree" resultType="org.springblade.product.common.vo.ExpenseTypeVO">
        SELECT
            id,
            parent_id,
            expense_name ,
            id AS "value",
            id AS "key"
        FROM
            jrzh_expense_type
        WHERE
            is_deleted = 0
        ORDER BY
            expense_key
    </select>
</mapper>
