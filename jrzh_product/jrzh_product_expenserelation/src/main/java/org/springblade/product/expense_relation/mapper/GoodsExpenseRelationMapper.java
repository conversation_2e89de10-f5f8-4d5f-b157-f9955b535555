/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.expense_relation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springblade.product.common.entity.GoodsExpenseRelation;

/**
 * 产品费用关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
public interface GoodsExpenseRelationMapper extends BaseMapper<GoodsExpenseRelation> {

}
