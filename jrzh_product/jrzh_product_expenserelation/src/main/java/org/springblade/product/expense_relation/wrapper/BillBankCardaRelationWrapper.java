package org.springblade.product.expense_relation.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.product.common.entity.BillBankCardaRelation;
import org.springblade.product.common.vo.BillBankCardaRelationVO;

import java.util.Objects;

/**
 * 产品账户关联表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
public class BillBankCardaRelationWrapper extends BaseEntityWrapper<BillBankCardaRelation, BillBankCardaRelationVO> {

    public static BillBankCardaRelationWrapper build() {
        return new BillBankCardaRelationWrapper();
    }

    @Override
    public BillBankCardaRelationVO entityVO(BillBankCardaRelation BillBankCardaRelation) {
        BillBankCardaRelationVO BillBankCardaRelationVO = Objects.requireNonNull(BeanUtil.copy(BillBankCardaRelation, BillBankCardaRelationVO.class));

        //User createUser = UserCache.getUser(BillBankCardaRelation.getCreateUser());
        //User updateUser = UserCache.getUser(BillBankCardaRelation.getUpdateUser());
        //BillBankCardaRelationVO.setCreateUserName(createUser.getName());
        //BillBankCardaRelationVO.setUpdateUserName(updateUser.getName());

        return BillBankCardaRelationVO;
    }
}

