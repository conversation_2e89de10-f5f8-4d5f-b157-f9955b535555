/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.expense.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.GoodsConstant;
import org.springblade.common.enums.DictBizEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.product.common.entity.GoodsExpenseRule;
import org.springblade.product.common.entity.GoodsExpenseRuleBusiness;
import org.springblade.product.common.vo.GoodsExpenseRuleBusinessVO;
import org.springblade.product.expense.service.IGoodsExpenseRuleBusinessService;
import org.springblade.product.expense.service.IGoodsExpenseRuleService;
import org.springblade.product.expense.wrapper.GoodsExpenseRuleBusinessWrapper;
import org.springblade.resource.cache.DictBizCache;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 费用规则-计算字段 控制器
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS + CommonConstant.WEB_BACK + "/goods/expenseRuleBusiness")
@Api(value = "费用规则-计算字段", tags = "费用规则-计算字段接口")
public class GoodsExpenseRuleBusinessController extends BladeController {

	private final IGoodsExpenseRuleBusinessService goodsExpenseRuleBusinessService;

	private final IGoodsExpenseRuleService goodsExpenseRuleService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入goodsExpenseRuleBusiness")
   @PreAuth( "hasPermission('goods:goodsExpenseRuleBusiness') or hasRole('administrator')")
	public R<GoodsExpenseRuleBusinessVO> detail(GoodsExpenseRuleBusiness goodsExpenseRuleBusiness) {
		GoodsExpenseRuleBusiness detail = goodsExpenseRuleBusinessService.getOne(Condition.getQueryWrapper(goodsExpenseRuleBusiness));
		return R.data(GoodsExpenseRuleBusinessWrapper.build().entityVO(detail));
	}

	/**
	 * 根据收费节点查询出所有的业务字段
	 */
	@GetMapping("/fee-node")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "收费节点", notes = "传入feeNode")
	@PreAuth("hasPermission('goods:goodsExpenseRuleBusiness') or hasRole('administrator')")
	public R<List<GoodsExpenseRuleBusiness>> feeDetail(@ApiParam(value = "收费节点", required = true) @RequestParam String feeNode,@ApiParam(value = "业务类型", required = true) @RequestParam String businessType) {
		List<GoodsExpenseRuleBusiness> list = goodsExpenseRuleBusinessService.list(Wrappers.<GoodsExpenseRuleBusiness>lambdaQuery().like(GoodsExpenseRuleBusiness::getFeeNode, feeNode).like(GoodsExpenseRuleBusiness::getBusinessType,businessType));
		return R.data(list);
	}

	/**
	 * 分页 费用规则-计算字段
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入goodsExpenseRuleBusiness")
   @PreAuth( "hasPermission('goods:goodsExpenseRuleBusiness') or hasRole('administrator')")
	public R<IPage<GoodsExpenseRuleBusinessVO>> list(GoodsExpenseRuleBusiness goodsExpenseRuleBusiness, Query query) {
		IPage<GoodsExpenseRuleBusiness> pages = goodsExpenseRuleBusinessService.page(Condition.getPage(query), Condition.getQueryWrapper(goodsExpenseRuleBusiness));
		return R.data(GoodsExpenseRuleBusinessWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 费用规则-计算字段
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入goodsExpenseRuleBusiness")
   @PreAuth( "hasPermission('goods:goodsExpenseRuleBusiness') or hasRole('administrator')")
	public R<IPage<GoodsExpenseRuleBusinessVO>> page(GoodsExpenseRuleBusinessVO goodsExpenseRuleBusiness, Query query) {
		IPage<GoodsExpenseRuleBusinessVO> pages = goodsExpenseRuleBusinessService.selectGoodsExpenseRuleBusinessPage(Condition.getPage(query), goodsExpenseRuleBusiness);
		return R.data(pages);
	}


	/**
	 * 新增或修改 费用规则-计算字段
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入goodsExpenseRuleBusiness")
   @PreAuth( "hasPermission('goods:goodsExpenseRuleBusiness') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody GoodsExpenseRuleBusiness goodsExpenseRuleBusiness) {

		String nameField = null;
		String businessType = null;
		GoodsExpenseRuleBusiness goodsExpenseRuleBusinessServiceById = null;
		if (goodsExpenseRuleBusiness.getId() != null){
			 goodsExpenseRuleBusinessServiceById = goodsExpenseRuleBusinessService.getById(goodsExpenseRuleBusiness.getId());
			 nameField = goodsExpenseRuleBusinessServiceById.getNameField();
			 businessType = goodsExpenseRuleBusinessServiceById.getBusinessType();
			goodsExpenseRuleBusiness.setNameField(null);
		}

		List<String> split = StrUtil.split(goodsExpenseRuleBusiness.getFeeNode(), ",");

		//收费节点
		for (String s : split) {
			String feeNode = DictBizCache.getValue(DictBizEnum.GOODS_RULE_FEE_NODE.getCode(),s );
//			if (StringUtil.isBlank(feeNode)){
//				throw new ServiceException(DictBizEnum.GOODS_RULE_FEE_NODE.getName()+"没有查询到 请填写正确的值！");
//			}
		}

		List<GoodsExpenseRule> list = goodsExpenseRuleService.list(Wrappers.<GoodsExpenseRule>query().lambda().eq(GoodsExpenseRule::getCalculation, GoodsConstant.CALCULATION_TWO));
		if (StrUtil.isNotBlank(nameField)){
			for (GoodsExpenseRule goodsExpenseRule : list) {
//				if (goodsExpenseRule.getFeeFormula().contains(nameField) && !goodsExpenseRuleBusiness.getFeeNode().contains(goodsExpenseRule.getFeeNode().toString()) ) {
//					return R.fail("费用规则存在该字段不能进行修改！");
//				}
//				if (goodsExpenseRule.getBusinessType().toString().contains(businessType) && !goodsExpenseRuleBusiness.getBusinessType().contains(goodsExpenseRule.getBusinessType().toString()) ) {
//					return R.fail("费用规则存在该业务类型不能进行修改！");
//				}
			}
		}
		try {
			goodsExpenseRuleBusinessService.saveOrUpdate(goodsExpenseRuleBusiness);
		}catch (Exception e){
			if (e instanceof DuplicateKeyException) {
				return R.fail("计算业务字段请不要重复添加");
			}else {
				return R.status(false);
			}
		}

		return R.status(true);
	}

}
