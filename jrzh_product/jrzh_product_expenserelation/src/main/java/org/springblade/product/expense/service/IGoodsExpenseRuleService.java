/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.expense.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.product.common.entity.GoodsExpenseRule;
import org.springblade.product.common.vo.GoodsExpenseRuleVO;

import java.util.List;
import java.util.Map;

/**
 * 费用规则 服务类
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
public interface IGoodsExpenseRuleService extends BaseService<GoodsExpenseRule> {

    /**
     * 自定义分页
     *
     * @param page
     * @param goodsExpenseRule
     * @return
     */
    IPage<GoodsExpenseRuleVO> selectGoodsExpenseRulePage(IPage<GoodsExpenseRuleVO> page, GoodsExpenseRuleVO goodsExpenseRule);

    /**
     * 新增费用规则
     *
     * @param goodsExpenseRule
     * @return
     */
    boolean saveExpenseRule(GoodsExpenseRule goodsExpenseRule);

    /**
     * 分页
     *
     * @param page
     * @return
     */
    IPage<GoodsExpenseRuleVO> selectGoodsExpenseRuleList(IPage<GoodsExpenseRuleVO> page);

	/**
	 * 查询产品费用规则
	 * @param goodsId 产品id
	 * @return List<GoodsExpenseRule>
	 */
	Map<Long,List<GoodsExpenseRule>> selectByGoodsId(List<Long> goodsId);

    /**
     * 费用类型查询费用规则
     *
     * @param expenseType
     * @return
     */
    List<GoodsExpenseRule> selectListByExpenseType(String expenseType);

    /**
     * TODO
     * @param goodsId
     * @return
     */
    List<GoodsExpenseRule> selectExpenseRuleList(Long goodsId);
}
