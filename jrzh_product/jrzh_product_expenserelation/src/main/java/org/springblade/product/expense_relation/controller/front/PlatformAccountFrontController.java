package org.springblade.product.expense_relation.controller.front;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.product.common.constant.AccountTypeEnum;
import org.springblade.product.common.entity.PlatformAccount;
import org.springblade.product.common.vo.PlatformAccountVO;
import org.springblade.product.expense_relation.service.IPlatformAccountService;
import org.springblade.product.expense_relation.wrapper.PlatformAccountWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/10/30
 * @description 平台账户
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_ACCOUNT+CommonConstant.WEB_FRONT+"/platformAccount/platformAccount")
@Api(value = "平台账户", tags = "平台账户接口")
public class PlatformAccountFrontController extends BladeController {
    private final IPlatformAccountService platformAccountService;

    /**
     * 根据融资编号查询平台账户
     */
    @GetMapping("/getAccountByFinanceNo")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据融资编号查询平台账户", notes = "传入financeNo")
    public R<PlatformAccountVO> list(@RequestParam String financeNo) {
        PlatformAccount accountByFinanceNo = platformAccountService.getAccountByFinanceNo(financeNo);
        return R.data(PlatformAccountWrapper.build().entityVO(accountByFinanceNo));
    }
    /**
     *  根据融资编号查询保证金监管账户
     */
    @GetMapping("/getCashDepositTakeAccount")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据融资编号查询保证金监管账户", notes = "传入financeNo")
    public R<PlatformAccountVO> getCashDepositTakeAccount(@RequestParam String financeNo) {
        PlatformAccount accountByFinanceNo = platformAccountService.getOne(Wrappers.<PlatformAccount>lambdaQuery()
                .eq(PlatformAccount::getFinanceNo,financeNo)
                .eq(PlatformAccount::getAccountType, AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode()));
        return R.data(PlatformAccountWrapper.build().entityVO(accountByFinanceNo));
    }
}
