/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.expense.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.product.common.entity.ExpenseType;
import org.springblade.product.common.vo.ExpenseTypeVO;
import org.springblade.product.expense.service.IExpenseTypeService;
import org.springblade.product.expense.wrapper.ExpenseTypeWrapper;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 费用类型 控制器
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_BILL + CommonConstant.WEB_BACK + "/bill/expenseType")
@Api(value = "费用类型", tags = "费用类型接口")
public class ExpenseTypeController extends BladeController {

	private final IExpenseTypeService expenseTypeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入expenseType")
    @PreAuth("hasPermission('bill:expenseType:detail') or hasRole('administrator')")
	public R<ExpenseTypeVO> detail(ExpenseType expenseType) {
		ExpenseType detail = expenseTypeService.getOne(Condition.getQueryWrapper(expenseType));
		return R.data(ExpenseTypeWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 费用类型
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入expenseType")
	public R<List<ExpenseTypeVO>> list(ExpenseTypeVO expenseType) {
		return R.data(expenseTypeService.selectExpenseTypeList(expenseType));
	}

	/**
	 * 获取费用分类树形结构的列表
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<ExpenseTypeVO>> tree() {
		return R.data(expenseTypeService.tree());
	}

	/**
	 * 自定义分页 费用类型
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入expenseType")
    @PreAuth("hasPermission('bill:expenseType:page') or hasRole('administrator')")
	public R<IPage<ExpenseTypeVO>> page(ExpenseTypeVO expenseType, Query query) {
		IPage<ExpenseTypeVO> pages = expenseTypeService.selectExpenseTypePage(Condition.getPage(query), expenseType);
		return R.data(pages);
	}

	/**
	 * 新增 费用类型
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入expenseType")
    @PreAuth("hasPermission('bill:expenseType:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody ExpenseType expenseType) {
		return R.status(expenseTypeService.save(expenseType));
	}

	/**
	 * 修改 费用类型
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入expenseType")
    @PreAuth("hasPermission('bill:expenseType:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody ExpenseType expenseType) {
		return R.status(expenseTypeService.updateById(expenseType));
	}

	/**
	 * 启用与禁用
	 */
	@PostMapping("/changeStatus")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "启用与禁用", notes = "传入ids 与状态")
	public R<Boolean> changeStatus(@ApiParam(value = "主键集合") @RequestParam String ids, @RequestParam Integer status) {
		if (status.equals(0)){
			List<ExpenseType> expenseTypeList = expenseTypeService.list(Wrappers.<ExpenseType>lambdaQuery().in(ExpenseType::getParentId, ids).eq(ExpenseType::getStatus,1));
			if (!CollectionUtils.isEmpty(expenseTypeList)){
				throw new ServiceException("请先禁用子节点!");
			}
		}
		return R.status(expenseTypeService.changeStatus(Func.toLongList(ids), status));
	}

	/**
	 * 新增或修改 费用类型
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入expenseType")
    @PreAuth("hasPermission('bill:expenseType:submit') or hasRole('administrator')")
	public R<Boolean> submit(@RequestBody ExpenseType expenseType) {
		return R.status(expenseTypeService.saveOrUpdateExpenseType(expenseType));
	}

	/**
	 * 删除 费用类型
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('bill:expenseType:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<ExpenseType> expenseTypeList = expenseTypeService.list(Wrappers.<ExpenseType>lambdaQuery().in(ExpenseType::getParentId, ids));
		if (!CollectionUtils.isEmpty(expenseTypeList)) {
			throw new ServiceException("请先删除子节点");
		}
		return R.status(expenseTypeService.deleteLogic(Func.toLongList(ids)));
	}

	@PostMapping("/buildExpenseTypeList")
	@ApiOperationSupport(order = 8)
	public R<List<ExpenseType>> buildExpenseTypeList(String tenantId) {
		List<ExpenseType> needSaveExpenseTypeList = new CopyOnWriteArrayList<>();
		List<ExpenseType> expenseTypeList = expenseTypeService.list(Wrappers.<ExpenseType>query().lambda()
				.eq(ExpenseType::getTenantId, BladeConstant.ADMIN_TENANT_ID)
				.eq(ExpenseType::getIsDeleted, BladeConstant.DB_NOT_DELETED)
		);
		// 根据父id进行分组
		Map<Long, List<ExpenseType>> parentIdMap = expenseTypeList.stream().collect(Collectors.groupingBy(ExpenseType::getParentId));
		// 父级数据
		List<ExpenseType> parentList = parentIdMap.get(0L);
		for (ExpenseType expenseType : parentList) {
			Long oldParentId = expenseType.getId();
			Long newParentId = IdWorker.getId();
			expenseType.setId(newParentId);
			expenseType.setTenantId(tenantId);
			expenseType.setCreateTime(null);
			expenseType.setUpdateTime(null);
			needSaveExpenseTypeList.add(expenseType);
			if (parentIdMap.containsKey(oldParentId)) {
				List<ExpenseType> expenseTypes = parentIdMap.get(oldParentId);
				for (ExpenseType type : expenseTypes) {
					type.setId(null);
					type.setTenantId(tenantId);
					type.setParentId(newParentId);
					type.setCreateTime(null);
					type.setUpdateTime(null);
					needSaveExpenseTypeList.add(type);
				}
			}
		}
		expenseTypeService.saveBatch(needSaveExpenseTypeList);
		return R.data(needSaveExpenseTypeList);
	}

}
