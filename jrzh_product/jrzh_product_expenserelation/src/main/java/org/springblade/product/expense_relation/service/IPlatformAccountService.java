/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.expense_relation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.product.common.entity.PlatformAccount;
import org.springblade.product.common.vo.PlatformAccountVO;


/**
 * 平台账户 服务类
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
public interface IPlatformAccountService extends BaseService<PlatformAccount> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param platformAccount
	 * @return
	 */
	IPage<PlatformAccountVO> selectPlatformAccountPage(IPage<PlatformAccountVO> page, PlatformAccountVO platformAccount);

	/**
	 * 保存平台账户
	 */
	void savePlatformAccount(Long goodsId,String financeNo,Integer accountType);

	/**
	 * 根据融资编号查询平台账户
	 * @param financeNo
	 * @return
	 */
	PlatformAccount getAccountByFinanceNo(String financeNo);
}
