/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.process_relation.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.product.common.entity.GoodsProcess;

import java.util.List;
import java.util.Map;

/**
 * 产品流程 服务类
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
public interface IGoodsProcessService extends BaseService<GoodsProcess> {


    /**
     * 保存产品流程
     *
     * @param goodsProcessList 产品流程列表
     * @param goodsId          产品id
     * @return true
     */
    boolean saveGoodsProcess(List<GoodsProcess> goodsProcessList, Long goodsId);

    /**
     * 根据产品id查询产品绑定流程
     *
     * @param goodsId 产品id
     * @return List<GoodsProcess>
     */
    List<GoodsProcess> listByGoodsId(Long goodsId);

    /**
     * 根据产品id查询产品绑定流程
     *
     * @param goodsIdList 产品id
     * @return Map<Long, List < GoodsProcess>>
     */
    Map<Long, List<GoodsProcess>> listInGoodsId(List<Long> goodsIdList);

    /**
     * 查询流程key
     *
     * @param goodsId     产品id
     * @param processType 流程类型
     * @return 流程key
     */
    String selectProcessKey(Long goodsId, Integer processType);

    /**
     * 检查产品流程是否填写
     *
     * @param goodsProcessList 产品流程列表
     * @return true
     */
    boolean checkGoodsProcess(List<GoodsProcess> goodsProcessList);
}
