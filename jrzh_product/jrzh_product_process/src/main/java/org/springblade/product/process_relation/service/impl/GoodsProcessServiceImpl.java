/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.process_relation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.product.common.entity.GoodsProcess;
import org.springblade.product.process_relation.mapper.GoodsProcessMapper;
import org.springblade.product.process_relation.service.IGoodsProcessService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 产品流程 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
@RequiredArgsConstructor
public class GoodsProcessServiceImpl extends BaseServiceImpl<GoodsProcessMapper, GoodsProcess> implements IGoodsProcessService {


    @Override
    public boolean saveGoodsProcess(List<GoodsProcess> goodsProcessList, Long goodsId) {
        if (CollectionUtils.isEmpty(goodsProcessList)) {
            return false;
        }
        goodsProcessList.forEach(goodsProcess -> goodsProcess.setGoodsId(goodsId));
        removeByGoodsId(goodsId);
        saveBatch(goodsProcessList);
        return true;
    }

    @Override
    public List<GoodsProcess> listByGoodsId(Long goodsId) {
        return baseMapper.selectList(Wrappers.<GoodsProcess>lambdaQuery().eq(GoodsProcess::getGoodsId, goodsId));
    }

    @Override
    public Map<Long, List<GoodsProcess>> listInGoodsId(List<Long> goodsIdList) {
        return baseMapper.selectList(Wrappers.<GoodsProcess>lambdaQuery().in(GoodsProcess::getGoodsId, goodsIdList))
                .stream().collect(Collectors.groupingBy(GoodsProcess::getGoodsId));
    }

    @Override
    public String selectProcessKey(Long goodsId, Integer processType) {
        GoodsProcess goodsProcess = baseMapper.selectOne(Wrappers.<GoodsProcess>lambdaQuery()
                .eq(GoodsProcess::getGoodsId, goodsId)
                .eq(GoodsProcess::getProcessType, processType));
        String str = Objects.nonNull(goodsProcess) ? goodsProcess.getProcessKey() : "";
        return str;
    }

    @Override
    public boolean checkGoodsProcess(List<GoodsProcess> goodsProcessList) {
        if (CollectionUtils.isEmpty(goodsProcessList)) {
            return true;
        }
        int size = goodsProcessList.size();
        int processCount = 4;
        if (size < processCount) {
            return true;
        }

        long count = goodsProcessList.parallelStream().filter(goodsProcess -> {
            String processKey = goodsProcess.getProcessKey();
            Long goodsId = goodsProcess.getGoodsId();
            Integer processType = goodsProcess.getProcessType();
            return Func.hasEmpty(processKey, goodsId, processType);
        }).count();
        return count > 0;
    }

    public boolean removeByGoodsId(Long goodsId) {
        return remove(Wrappers.<GoodsProcess>lambdaQuery().eq(GoodsProcess::getGoodsId, goodsId));
    }

}
