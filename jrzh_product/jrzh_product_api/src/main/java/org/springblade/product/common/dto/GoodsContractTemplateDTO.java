/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 产品合同模板数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Data
public class GoodsContractTemplateDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty("产品id")
    @NotNull(message = "产品id不能为空")
    private Long goodsId;

    @NotNull(message = "合同模板不能为空")
    @Valid
    private GoodsContractTemplateInfo contractTemplate;

    //合同模板id
    private String contractTemplateId;

    //模板id(上上签模板id)
    private String templateId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty("用户合同节点")
    private List<ContractNodeDTO> userNodeList;
    /**
     * 合同当前url
     */
    private String pdfUrl;

    private Integer contractGenType;

    private String templateUrl;
    /**
     * 合同模板名称
     */
    @ApiModelProperty(value = "合同模板名称")
    private String contractTemplateName;
    /**
     * 流程id
     */
    @ApiModelProperty(value = "签署节点")
    private String signNode;


    @ApiModelProperty(value = "链接")
    private String link;

    @ApiModelProperty(value = "签署用户 多选")
    private String signUser;

}
