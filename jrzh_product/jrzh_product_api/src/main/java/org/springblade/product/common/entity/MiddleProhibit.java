/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 禁止入实体类
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Data
@TableName("jrzh_middle_prohibit")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiddleProhibit对象", description = "禁入行页中间表")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MiddleProhibit extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 白名单表ID
	*/
		@ApiModelProperty(value = "白名单表ID")
		private Long whitelistTemplateId;
	/**
	* 禁入ID
	*/
		@ApiModelProperty(value = "行业表ID")
		private Long prohibitId;


}
