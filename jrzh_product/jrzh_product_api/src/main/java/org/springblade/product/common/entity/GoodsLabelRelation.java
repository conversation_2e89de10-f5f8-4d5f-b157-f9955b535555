/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 产品标签关联实体类
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@TableName("jrzh_goods_label_relation")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GoodsLabelRelation对象", description = "产品标签关联")
public class GoodsLabelRelation extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 产品id
	*/
		@ApiModelProperty(value = "产品id")
		private Long goodsId;
	/**
	* 标签id
	*/
		@ApiModelProperty(value = "标签id")
		private Long labelId;


}
