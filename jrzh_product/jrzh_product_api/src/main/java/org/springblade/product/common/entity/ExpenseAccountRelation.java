/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 产品费用账户关联实体类
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@TableName("jrzh_expense_account_relation")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ExpenseAccountRelation对象", description = "产品费用账户关联")
public class ExpenseAccountRelation extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 产品id
	*/
		@ApiModelProperty(value = "产品id")
		private Long goodsId;
	/**
	* 企业类型
	*/
		@ApiModelProperty(value = "企业类型")
		private Integer enterpriseType;
	/**
	* 费用类型key
	*/
		@ApiModelProperty(value = "费用类型key")
		private Integer expenseKey;
	/**
	* 费用名称
	*/
		@ApiModelProperty(value = "费用名称")
		private String expenseName;

	@ApiModelProperty(value = "费用id")
	private Long expenseId;


}
