/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;

/**
 * 产品合同模板实体类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Data
@TableName("jrzh_goods_contract_template")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GoodsContractTemplate对象", description = "产品合同模板")
public class GoodsContractTemplate extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 产品id
	 */
	@ApiModelProperty(value = "产品id")
	private Long goodsId;
	/**
	 * 合同模板id
	 */
	@ApiModelProperty(value = "合同模板id")
	@NotNull(message = "合同模板id不能为空")
	private String contractTemplateId;

	@ApiModelProperty(value = "模板id(上上签模板id)")
	private String templateId;
	/**
	 * 合同模板名称
	 */
	@ApiModelProperty(value = "合同模板名称")
	private String contractTemplateName;
	/**
	 * 流程id
	 */
	@ApiModelProperty(value = "签署节点")
	@NotNull(message = "签署节点不能为空")
	private String signNode;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	@NotNull(message = "排序不能为空")
	private Integer sort;


	@ApiModelProperty(value = "链接")
	private String link;
	@ApiModelProperty(value = "签署用户 多选")
	private String signUser;
}
