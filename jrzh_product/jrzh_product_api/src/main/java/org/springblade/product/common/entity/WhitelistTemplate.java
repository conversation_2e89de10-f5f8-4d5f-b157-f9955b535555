/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Data
@TableName("jrzh_whitelist_template")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WhitelistTemplate对象", description = "WhitelistTemplate对象")
public class WhitelistTemplate extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 模板名称
	 */
	@ApiModelProperty(value = "模板名称")
	private String templateName;
	/**
	 * 名单类型0-融资企业，1-核心企业
	 */
	@ApiModelProperty(value = "名单类型0-融资企业，1-核心企业")
	private Integer whitelistType;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;
	/**
	 * 模板编号(或ID)
	 */
	@ApiModelProperty(value = "模板编号(或ID)")
	private String whitelistTemplateCode;

	/**
	 * 黑/白名单
	 */
	@ApiModelProperty(value = "0-白名单，1-黑名单")
		private Integer blackWhiteType;

	/**
	 * 选择产品绑定黑白名单
	 */
	@ApiModelProperty(value = "产品编码")
	private String goodsCodes;

	/**
	 * 业务类型 0-应收，1-云信
	 */
	@ApiModelProperty(value = "业务类型 1-应收，2-云信")
	private Integer businessType;

}
