/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 产品常见问题实体类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Data
@TableName("jrzh_goods_question")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GoodsQuestion对象", description = "产品常见问题")
public class GoodsQuestion extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 答案
	*/
		@ApiModelProperty(value = "答案")
		@NotBlank(message = "答案不能为空")
		private String answer;
	/**
	* 问题名称
	*/
		@ApiModelProperty(value = "问题名称")
		@NotBlank(message = "问题不能为空")
		private String name;
	/**
	* 排序
	*/
		@ApiModelProperty(value = "排序")
		@NotNull(message = "排序不能为空")
		private Integer sort;
	/**
	* 产品id
	*/
		@ApiModelProperty(value = "产品id")
		private Long goodsId;


}
