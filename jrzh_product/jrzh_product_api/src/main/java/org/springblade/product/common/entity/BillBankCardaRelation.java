/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 产品账户关联表实体类
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Data
@TableName("jrzh_bill_bank_carda_relation")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BillBankCardaRelation对象", description = "产品账户关联表")
public class BillBankCardaRelation extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 产品id
	 */
	@ApiModelProperty(value = "产品id")
	private Long goodsId;
	/**
	 * 账户id
	 */
	@ApiModelProperty(value = "账户id")
	private Long billBankCardaId;
	/**
	 * 产品类型
	 */
	@ApiModelProperty(value = "产品类型")
	private Integer productType;

	/**
	 * 账户类型 AccountTypeEnum
	 */
	@ApiModelProperty(value = "账户类型")
	private Integer accountType;


	@ApiModelProperty("平台费用支付方式 1线下支付 2线上支付 PayModeEnum")
	private Integer platformCostPayMode;

	/**
	 * 费用类型键
	 */
	@ApiModelProperty(value = "费用类型键")
	private Integer expenseKey;

	/**
	 * 机构id
	 */
	@ApiModelProperty(value = "机构id")
	private String deptId;

}
