/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023/3/29 18:13
 * @Description: 产品管理类
 * @Version: 1.0
 */
@Data
@TableName("jrzh_product_manager")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductManager extends TenantEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品id")
    private Long productId;

    /**
     * 产品一级分类
     */
    private Integer type;

    /**
     * 资方名称
     */
    private String capitalName;

    /**
     * 资方Logo
     */
    private String capitalLogo;
    /**
     * 产品名称
     */
    private String goodsName;
}
