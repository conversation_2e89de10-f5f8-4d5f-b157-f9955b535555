/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 商品白名单关联商品中间表实体类
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
@TableName("jrzh_commodity_white_list_commodity")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CommodityWhiteListCommodity对象", description = "商品白名单关联商品中间表")
public class CommodityWhiteListCommodity extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 商品白名单id
	 */
	@ApiModelProperty(value = "商品白名单id")
	private Long commodityWhiteListId;
	/**
	 * 商品id
	 */
	@ApiModelProperty(value = "商品id")
	private Long commodityId;
}
