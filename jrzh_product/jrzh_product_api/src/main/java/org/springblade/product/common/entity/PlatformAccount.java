/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 平台账户实体类
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Data
@TableName("jrzh_platform_account")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlatformAccount对象", description = "平台账户")
public class PlatformAccount extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 开户名
	*/
		@ApiModelProperty(value = "开户名")
		private String openHouseName;
	/**
	* 融资编号
	*/
		@ApiModelProperty(value = "融资编号")
		private String financeNo;
	/**
	* 开户银行
	*/
		@ApiModelProperty(value = "开户银行")
		private String bankDeposit;
	/**
	* 银行账户号
	*/
		@ApiModelProperty(value = "银行账户号")
		private String bankCardNo;

	/**
	 * 银行账户号
	 */
	@ApiModelProperty(value = "账户类型")
	private Integer accountType;
	/**
	* 备注
	*/

	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 账户名称
	 */
	@ApiModelProperty(value = "账户名称(公司名称)")
	private String enterpriseName;



}
