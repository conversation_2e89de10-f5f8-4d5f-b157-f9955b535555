#数据源配置
spring:
  rabbitmq:
    host: ************
#    host: 127.0.0.1
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    publisher-confirm-type: correlated
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          max-attempts: 3
          enabled: true
      type: simple
    connection-timeout: 15000
  redis:
    host: ************
    port: 9009
    password: Jrzh@2022
    database: 14
    ##redis 单机环境配置
    #    host: **************
    #    port: 6379
    #    password: zc533995
    #    database: 0
    #    ssl: false
#    host: 127.0.0.1
#    port: 6379
   # password: 123456
#    database: 0
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000
  datasource:
    # MySql
    #    url: jdbc:mysql://************:3306/jrzh_bladex_supply_chain?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true
    #    url: jdbc:mysql://************:3306/supply_chain_core_two?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true
#    url: jdbc:mysql://************/jrzh_supplier_3?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true
#    #    url: *******************************************************************************************************************************************************************************************************************************************************
#    username: jrzh_supplier_3
#    #    username: jrzh
#    password: Jrzh@2022_supplier_3
#    url: jdbc:mysql://************:3306/supply_3_dev?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true
    #url: **********************************************************************************************************************************************************************************************************************************************
        url: jdbc:mysql://************:3306/jrzh_supplier_3?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true
        username: jrzh_supplier_3
        password: Jrzh@2022_supplier_3
#    url: *************************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: root
#    username: jrzh_supplier_3
#    password: Jrzh@2022_supplier_3
    #password: wujm2020.
    #    password: Jrzh@2019
    #    password: Jrzh@2022.
    # PostgreSQL
    #url: ********************************************
    #username: postgres
    #password: 123456
    # Oracle
    #url: *************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # SqlServer
    #url: ********************************************************
    #username: bladex_boot
    #password: bladex_boot

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888
#blade配置
blade:
  #长链接转链接head
  linkChange:
    preUrl: http://localhost:8007
  #平台默认商户号
  onlinePay:
    accountName: 深圳市精锐纵横网络技术有限公司
    bankDeposit: 中国邮政储蓄银行股份有限公司苏州市木渎支行
    bankCardNo: 44201521700052531237
    merchantNo: E1807603333
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: true
    ##redis服务地址
#    address: redis://127.0.0.1:6379
    #password: 123456
    address: redis://************:9009
    password: Jrzh@2022
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html
##不能改变  作为api加密解密秘钥，如若改变则之前加密的数据无法还原
otherApi:
  encrypt:
    des: ci7VGO31ZnC6Nxcl
#华为云ocr
huawei:
  auth:
    ak: N70HQ9BBHKZJC1O7Q6M7
    sk: FgJawGvrlqTgS2Zl9HjgxumegJM0i7q8HkAVhzjd

###微信支付
weixin:
  pay:
    v2:
      ###公众号id
      appid: wx45af3c8bd749306c
      ###商户id
      mch-id: **********
      ##商户秘钥
      partnerKey: 6337774a39f813e29a4dba0c8d01d33f
      ###回调通知
      domain: https://www.jingruiit.com
      ###加密证书文件地址
      certPath: classpath:/pay/wxpay/apiclient_cert.p12

    v3:
      appid: wx45af3c8bd749306c
      #####商户id
      mch-id: **********
      ####apiV3秘钥
      apiKey3: jingruiitlijing12345678920220224
      ####apiV2秘钥
      partnerKey: 6337774a39f813e29a4dba0c8d01d33f
      domain: https://www.jingruiit.com
      ###加密证书文件地址
      certPath: classpath:/pay/wxpay/apiclient_cert.p12
      keyPemPath: classpath:/pay/wxpay/apiclient_key.pem
      certPemPath: classpath:/pay/wxpay/apiclient_cert.pem


#阿里支付正式环境
#ali:
#  pay:
#    ###appId
#    appid: 2021002193607697
#    ####私钥
#    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCCV9Zmis4YIO4/XvbPUH8sblmr4M7O5VQAYwYXmC0rgJyTm2xcH20SWD1D+ktvBMWpW4pvzX7RmtILmjCZPlIKZRsNANNV7ZZAaqciRTkdvItseTpzfTtgHTV9mZo1hFNRMoBvk4NHEqg2TAqdiaD+QD8H34w6wpB+DgHocy7rW+RjoOE5py1AscB5eJUSrM15C3D9bzwDkl58CuiTbTAKtRTQ4GYKXQk5F8WMja7vo8F2QUkms1Yb44ejRDCXCzwYOC+VzRpuaXwlR+5ML6nQwBBm+ab+/KeVlbcMCt0WK6x5Krym41X0nUBUsY9GPIm7wsrmbTx30HIME6sZB+j1AgMBAAECggEAT0WAnai5oy8T9vKYhIlebvVGECSPYoFSz1FqORtSDW0QzSzvgC+UtF16FUe6bPCoDlIgJdMC2BOiv2+tMuYWG0BWbVgdL9tpUBANSehzzkQHP5eTFv4JCHF7QMIUaYvq6C0gs7sxUh5ks3EX26wCf98u9mjc7je6jQ+T1X2IAZdfS78EPlVY6nsC4Pe7JqqNhmLoB45i9hrypEQKzF3d5I9zKzR+WKBy3qfeY9E23gsH5rgxm3H8gIXfl8BJ4ssYue4LSY4m6qBHI5UAqBhfJHeNi/AxvwdTr8tjfj5Gj557rCN5kMBqZ5h627rQ+Pt6jRakkWsNouZZO/DJP9u2wQKBgQD2qMHSmFz/bxT7L4IcNil/l+EZyo0j5rz9OeaQckwWo4+JK7TV8SdWXFvRGEqRFXyEyH7njiMuap+HrtZ8QO8SSq6qizy8HWcNjKPBJxb3NENr5m2dTnd+zDqst+IWn+JVqNHk+AbjO/ArOWY5yKxThqVZY/Izxr1HMkCurrRdDQKBgQCHR3Oaj7TuRdBtS/Mu0RhWcQiKiidNj12QbrY5lHc26BHAjVFr+C25jDaVzANVc+PcuUSyoNnjns3YYUHaFSgF2rLwXmxy95iwQ40XXVPyTqaigGG2r62k4XW88v8jMfQ6jK6QKEPZCVqIoDJr/AM1ZQIzOJHG2G8TqacswxVRiQKBgQDZDeIZ9zgHE3IsJD9jogncnQNaFdMj3505XOVXAS2R0nax58MnbSIHrhiiJLUhfP3pPyHllD/DTdJ6rNFT4DRBLXUM4KAejeUsmDFV/JcKDHS0E78r6WvgKcoMSXhsHmAmrvudeOFev1T2RHl/qeZzTg5dXkU5aM8RQtiGn3haVQKBgDS9SHaDfBhAo4dYys//uKtKmffY8IWuVN3QK767ANcpFnKpJYsgUEM8RLOVu97+tWEB4aR6TTgOW1X2ZMPu55GQt546UbFokeB2FXV8bxPSUeicgHvGdb4IH+ywF4PTH9ILsqqE6B6fJOqK+d1bfKy+WmM5tKt/CZ7yzzgGPjNxAoGBAN1Exx6YzOvmeAfPKaN3djYbSQ
#    publicKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCCV9Zmis4YIO4/XvbPUH8sblmr4M7O5VQAYwYXmC0rgJyTm2xcH20SWD1D+ktvBMWpW4pvzX7RmtILmjCZPlIKZRsNANNV7ZZAaqciRTkdvItseTpzfTtgHTV9mZo1hFNRMoBvk4NHEqg2TAqdiaD+QD8H34w6wpB+DgHocy7rW+RjoOE5py1AscB5eJUSrM15C3D9bzwDkl58CuiTbTAKtRTQ4GYKXQk5F8WMja7vo8F2QUkms1Yb44ejRDCXCzwYOC+VzRpuaXwlR+5ML6nQwBBm+ab+/KeVlbcMCt0WK6x5Krym41X0nUBUsY9GPIm7wsrmbTx30HIME6sZB+j1AgMBAAECggEAT0WAnai5oy8T9vKYhIlebvVGECSPYoFSz1FqORtSDW0QzSzvgC+UtF16FUe6bPCoDlIgJdMC2BOiv2+tMuYWG0BWbVgdL9tpUBANSehzzkQHP5eTFv4JCHF7QMIUaYvq6C0gs7sxUh5ks3EX26wCf98u9mjc7je6jQ+T1X2IAZdfS78EPlVY6nsC4Pe7JqqNhmLoB45i9hrypEQKzF3d5I9zKzR+WKBy3qfeY9E23gsH5rgxm3H8gIXfl8BJ4ssYue4LSY4m6qBHI5UAqBhfJHeNi/AxvwdTr8tjfj5Gj557rCN5kMBqZ5h627rQ+Pt6jRakkWsNouZZO/DJP9u2wQKBgQD2qMHSmFz/bxT7L4IcNil/l+EZyo0j5rz9OeaQckwWo4+JK7TV8SdWXFvRGEqRFXyEyH7njiMuap+HrtZ8QO8SSq6qizy8HWcNjKPBJxb3NENr5m2dTnd+zDqst+IWn+JVqNHk+AbjO/ArOWY5yKxThqVZY/Izxr1HMkCurrRdDQKBgQCHR3Oaj7TuRdBtS/Mu0RhWcQiKiidNj12QbrY5lHc26BHAjVFr+C25jDaVzANVc+PcuUSyoNnjns3YYUHaFSgF2rLwXmxy95iwQ40XXVPyTqaigGG2r62k4XW88v8jMfQ6jK6QKEPZCVqIoDJr/AM1ZQIzOJHG2G8TqacswxVRiQKBgQDZDeIZ9zgHE3IsJD9jogncnQNaFdMj3505XOVXAS2R0nax58MnbSIHrhiiJLUhfP3pPyHllD/DTdJ6rNFT4DRBLXUM4KAejeUsmDFV/JcKDHS0E78r6WvgKcoMSXhsHmAmrvudeOFev1T2RHl/qeZzTg5dXkU5aM8RQtiGn3haVQKBgDS9SHaDfBhAo4dYys//uKtKmffY8IWuVN3QK767ANcpFnKpJYsgUEM8RLOVu97+tWEB4aR6TTgOW1X2ZMPu55GQt546UbFokeB2FXV8bxPSUeicgHvGdb4IH+ywF4PTH9ILsqqE6B6fJOqK+d1bfKy+WmM5tKt/CZ7yzzgGPjNxAoGBAN1Exx6YzOvmeAfPKaN3djYbSQ
#    domain: https://openapi.alipay.com/gateway.do
#    appCertPath: /pay/alipay/appCertPublicKey_2021002193607697.crt
#    aliPayCertPath: /pay/alipay/alipayCertPublicKey_RSA2.crt
#    aliPayRootCertPath: /pay/alipay/alipayRootCert.crt
#    serverUrl: https://openapi.alipay.com/gateway.do


#####阿里支付沙箱环境
ali:
  pay:
    appId: 2021000119623176
    privateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCfzZ5X1oVbIwnALtrphlOkWLu+U8Jid1vFCMLOeKabintuyHKn9y2+ZvxjuajMXm2fuVsvGkPhOnLo3P5wJq6u/cYWPEcTVqzqFBT83IV+Mi94Lh2o3PHDKTk43K7xDmlKHtDWQLgBGjKhL336hL5avIzAtmVQtCHcIhtfEFeRMpN9eJq0o49RrXK+G7VUYQww3YRfhDP07TvnogKuEhz8q2Lq+knVv+LlMYII38tSV+a0xuizP1V4kvmA1Bh+EsaCXzlQ0e2CCd9yz338D8s/QETc8FBB5iuJlbpQ2ewugMOZUR5Z6HF7rb/z817702B7O+RBoERV5t134Gq0drRjAgMBAAECggEAPVtKf7lbLJnwZXzYCq/Lr59uPm6UhRAFCUsK2RHUDoaGTAvkkf9C7gdGwteHKyjaeGhHzXcNNw4lDqnDe7YuffvsMZ9lPtSZwaqCQHA/RSvptAAo3SwwG1nZoa6B8QYygQYB8zGYNLwlUmQUv7p2CAq7FEcz8V+oXIgv8DkrFhE044SHxjgHWhZBh56Z54ZI/B5nr1f3Zb+BNSB+0aeUZSHwdHEtVdVBCgB/SrtN/6TWE304OLz3I4c5/j7Hkxf2HXpsQRAv9HJYNmNBPaU5zWoumznHNrSe4hKlVh+MFaHgeGeGVAq7puFKYAOTTgvOj+UaF1aaRvj4VhXbLgI2+QKBgQDLi+NjYi27RAOtbon5xmJkI7cYRlqIMzG9Mk2SzxWbp9FvW8EF2R1osVaHzkxNA9sV1AdRaehxXxWhfEff2Fd8w6rFliVQ+Y95HaluHGHfexMF+SyUOmK5tWpE22Vsjq86NByotfR5qk6QHMiE3d1P/EcJUmc9za3iaagXjkGkDwKBgQDI+/SsMyM2XD+h/TZL6vB/7By6j2eZxcrtycWRmGCFo0l1gPv7TlqPnZMeK4iMnH2h9r2pMUwRRpsq7vYmvjbTJWHa0v65EvBOS4JsHpF4iGgXBm66oXFW9peR+lrZPgeUA4cP8E5FyvPsXZpNIlrmTwq6jrAcuadnDGg62xyGbQKBgAWf6PmvFdpt57gcLN3lGITx47ZdA1VuLLqwsCoVZOB3Q7dTDsNtm31wIRcBoWQjbiNtR4J9bbnsSbm7qHTMEqbU9VweaQuSyd8r7amoSSAW05C/sParolzFYZljgqr8SmVOWVhkJsxR43fiA7yAMQRr0T61szqm+14dU1LLUI8LAoGAWhKo7b+hSUyAbB2qBs4M6JetrGJ2gLDnKDhkJcMPhvTOCC49P7w20+Q/uaay9KlxmHOXPktMlvV4tv4PLP/UXD6Fo2cqJ1F4hu5KTiBI+6bOV3tXhqaxv9KVzSNRMckd5lTKY/dDbP3u4YB1lZoO+OZ1nXQzMzx07Lbfw3jNSOUCgYBYVg1ZV1b/QWkCPpD1sx077MO9lncanispee1LnZrg1kICijDCX0PUOsNi7pekaI2ZzItjVBpGR3QvDd/WwU32mJG1t2OBIiBmuEnxxRanil/AXjhv7F02SPZxh7BrFv72G0zAIwVE6KxxRWsXiyN5LmADC3RWX5A7hfuN/PJ+9Q==
    publicKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCCV9Zmis4YIO4/XvbPUH8sblmr4M7O5VQAYwYXmC0rgJyTm2xcH20SWD1D+ktvBMWpW4pvzX7RmtILmjCZPlIKZRsNANNV7ZZAaqciRTkdvItseTpzfTtgHTV9mZo1hFNRMoBvk4NHEqg2TAqdiaD+QD8H34w6wpB+DgHocy7rW+RjoOE5py1AscB5eJUSrM15C3D9bzwDkl58CuiTbTAKtRTQ4GYKXQk5F8WMja7vo8F2QUkms1Yb44ejRDCXCzwYOC+VzRpuaXwlR+5ML6nQwBBm+ab+/KeVlbcMCt0WK6x5Krym41X0nUBUsY9GPIm7wsrmbTx30HIME6sZB+j1AgMBAAECggEAT0WAnai5oy8T9vKYhIlebvVGECSPYoFSz1FqORtSDW0QzSzvgC+UtF16FUe6bPCoDlIgJdMC2BOiv2+tMuYWG0BWbVgdL9tpUBANSehzzkQHP5eTFv4JCHF7QMIUaYvq6C0gs7sxUh5ks3EX26wCf98u9mjc7je6jQ+T1X2IAZdfS78EPlVY6nsC4Pe7JqqNhmLoB45i9hrypEQKzF3d5I9zKzR+WKBy3qfeY9E23gsH5rgxm3H8gIXfl8BJ4ssYue4LSY4m6qBHI5UAqBhfJHeNi/AxvwdTr8tjfj5Gj557rCN5kMBqZ5h627rQ+Pt6jRakkWsNouZZO/DJP9u2wQKBgQD2qMHSmFz/bxT7L4IcNil/l+EZyo0j5rz9OeaQckwWo4+JK7TV8SdWXFvRGEqRFXyEyH7njiMuap+HrtZ8QO8SSq6qizy8HWcNjKPBJxb3NENr5m2dTnd+zDqst+IWn+JVqNHk+AbjO/ArOWY5yKxThqVZY/Izxr1HMkCurrRdDQKBgQCHR3Oaj7TuRdBtS/Mu0RhWcQiKiidNj12QbrY5lHc26BHAjVFr+C25jDaVzANVc+PcuUSyoNnjns3YYUHaFSgF2rLwXmxy95iwQ40XXVPyTqaigGG2r62k4XW88v8jMfQ6jK6QKEPZCVqIoDJr/AM1ZQIzOJHG2G8TqacswxVRiQKBgQDZDeIZ9zgHE3IsJD9jogncnQNaFdMj3505XOVXAS2R0nax58MnbSIHrhiiJLUhfP3pPyHllD/DTdJ6rNFT4DRBLXUM4KAejeUsmDFV/JcKDHS0E78r6WvgKcoMSXhsHmAmrvudeOFev1T2RHl/qeZzTg5dXkU5aM8RQtiGn3haVQKBgDS9SHaDfBhAo4dYys//uKtKmffY8IWuVN3QK767ANcpFnKpJYsgUEM8RLOVu97+tWEB4aR6TTgOW1X2ZMPu55GQt546UbFokeB2FXV8bxPSUeicgHvGdb4IH+ywF4PTH9ILsqqE6B6fJOqK+d1bfKy+WmM5tKt/CZ7yzzgGPjNxAoGBAN1Exx6YzOvmeAfPKaN3djYbSQ
    domain: https://openapi.alipay.com/gateway.do
    aliPayRootCertPath: /cert/ali/alipayRootCert.crt
    appCertPath: /cert/ali/appCertPublicKey_2021000119623176.crt
    aliPayCertPath: /cert/ali/alipayCertPublicKey_RSA2.crt
    serverUrl: https://openapi.alipaydev.com/gateway.do

######union pay
union:
  pay:
    machId: 777290058196963
    key: 979da4cfccbae7923641daa5dd7047c2
    serverUrl: https://qra.95516.com/pay/gateway
    domain: https://jingruiit.com
feign:
  url: http://127.0.0.1:8001

# 外部系统集成配置
external:
  # B2B交易系统配置
  b2b:
#    host: http://**************:8890
    host: http://*************:8891
    order-status-change-method: /common/common/order/order-financing-paid-back

