spring:
  resource:
    static-locations: classpath:/static/
#JimuReport[minidao配置]
minidao :
  base-package: org.jeecg.modules.jmreport.desreport.dao*
  db-type: mysql
#JimuReport[上传配置]
jeecg :
  jmreport:
    # 自动保存
    autoSave: true
    # 单位毫秒 默认5*60*1000
    interval: 10000
  # local|minio|alioss
  uploadType: local
  # local
  path :
    #文件路径A
    upload: ~/jimu/data
  # alioss
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKey: ??
    secretKey: ??
    staticDomain: ??
    bucketName: ??
  # minio
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: ??
