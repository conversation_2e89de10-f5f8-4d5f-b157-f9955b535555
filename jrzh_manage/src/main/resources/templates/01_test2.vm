package com.drools.rules;
dialect "mvel";
import org.springblade.riskcore.modlue.riskmana.entity.RatingNormRecord
## 循环公共包
#foreach($pkg in $commonPkg)
import $pkg
#end

## 循环全局变量
###foreach($businessField in $businessFieldList)
##global ${businessField.fieldType} ${businessField.paramName}
###end

##绘制规则
#foreach($ruleInfo in $ruleList)

rule "${normTemplates.normNo}_${ruleInfo.name}_${foreach.index}"
    ##设置优先级 固定分组 命中一条规则后不再执行
    salience ${ruleInfo.sort}
    activation-group "${normTemplates.templateId}_${normTemplates.name}"
##绘制规则属性
when
    #[[$param]]#:JSONObject()

##渲染需要声明的实体/字段
#set($definitionBusinessStr=$ruleInfo.buildDefinitionBusiness())
#if($ruleInfo.buildDefinitionBusiness()!="")
    $_isExist:Boolean($definitionBusinessStr)
#end
    #*
        条件绘制过程 循环拼接表达式
        绘制集合函数 若存在 result+1-->result+2 以函数名做判断 依次绘画 为什么需要先绘画 因为result结果项（result1+result2>0） 需要 result1 result2 先进行声明后才可使用
    *#
    ##绘制规则表达式
#set($droolsExpressionStr="")
    ##循环集合函数
    #foreach($definitionFunction in $ruleInfo.definitionFunctionList)
        ##获取下标
        #set($_listFunctionCount=$ruleInfo.definitionFunctionList.size()-$foreach.index)
        ##集合累加类型
        #if($definitionFunction.functionValue=='_functionListAdd')
            ##获取累加字段参数
            #set($functionListParam=$definitionFunction.getElem(0))
        #[[$resultList]]#$_listFunctionCount:
        Double(this!=null) from accumulate(#[[$_m]]#:JSONObject(),init(double _sum=0.0),action(
            List _list=CommonUtil.getByMap(#[[$_m]]#,"$functionListParam.entityName");
            if(CollUtil.isNotEmpty(_list)){
                for (int i = 0; i < _list.size(); i++) {
                        if($definitionFunction.buildListNullExpression()){}
                        else if($definitionFunction.getExpressionByParamIndex(2)){
                            _sum=CommonUtil.sumAdd(_list.get(i),_sum,"$functionListParam.fieldName");
                        }
                }
            }
        ),result(_sum))
        ##集合的条件平均
        #elseif($definitionFunction.functionValue=='_functionListAvg')
            ##获取字段参数
            #set($functionListParam=$definitionFunction.getElem(0))
            $resultList$_listFunctionCount:
                Double(this!=null) from accumulate(#[[$_m]]#:JSONObject(),init(double _sum=0.0;Integer _countNum=0;double _resultAvg=0.0),action(
                List _list=CommonUtil.getByMap(#[[$_m]]#,"$functionListParam.entityName");
                if(CollUtil.isNotEmpty(_list)){
                    for (int i = 0; i < _list.size(); i++) {
                        if($definitionFunction.buildListNullExpression()){}
                            else if($definitionFunction.getExpressionByParamIndex(2)){
                            _countNum++;
                            _sum=CommonUtil.sumAdd(_list.get(i),_sum,"$functionListParam.fieldName");
                        }
                    }
                    if(_countNum>0){
                        _resultAvg=_sum/_countNum;
                    }
                }
        ),result(_resultAvg))
        ##集合的条件最高
        #elseif($definitionFunction.functionValue=='_functionListMax')
            ##获取字段参数
            #set($functionListParam=$definitionFunction.getElem(0))
        #[[$resultList]]#$_listFunctionCount:
                Double(this!=null) from accumulate(#[[$_m]]#:JSONObject(),init(double _currentNum=0.0;double _max=0.0;boolean flag=true),action(
                List _list=CommonUtil.getByMap(#[[$_m]]#,"$functionListParam.entityName");
                if(CollUtil.isNotEmpty(_list)){
                    for (int i = 0; i < _list.size(); i++) {
                        if($definitionFunction.buildListNullExpression()){}
                            else if($definitionFunction.getExpressionByParamIndex(2)){
                                if(flag){
                                    _max=$functionListParam.realValue;
                                    flag=false;
                                }
                                _currentNum=$functionListParam.realValue;
                                if(_currentNum>_max){
                                     _max=_currentNum;
                                }
                            }
                    }
                }
        ),result(_max))
        ##集合的条件最低
        #elseif($definitionFunction.functionValue=='_functionListMin')
            ##获取字段参数
            #set($functionListParam=$definitionFunction.getElem(0))
        #[[$resultList]]#$_listFunctionCount:
                Double(this!=null) from accumulate(#[[$_m]]#:JSONObject(),init(double _currentNum=0.0;double _min=0.0;boolean flag=true),action(
                    List _list=CommonUtil.getByMap(#[[$_m]]#,"$functionListParam.entityName");
                    if(CollUtil.isNotEmpty(_list)){
                        for (int i = 0; i < _list.size(); i++) {
                            if($definitionFunction.buildListNullExpression()){}
                            else if($definitionFunction.getExpressionByParamIndex(2)){
                                if(flag){
                                    _min=$functionListParam.realValue;
                                    flag=false;
                                }
                                _currentNum=$functionListParam.realValue;
                                if(_currentNum<=_min){
                                    _min=_currentNum;
                                }
                            }
                        }
                    }
                ),result(_min))
            ##集合的条件条数
        #elseif($definitionFunction.functionValue=='_functionListCount')
            ##获取字段参数
            #set($functionListParam=$definitionFunction.getElem(0))
        #[[$resultList]]#$_listFunctionCount:
            Double(this!=null) from accumulate(#[[$_m]]#:JSONObject(),init(Integer _count=0),action(
                List _list=CommonUtil.getByMap(#[[$_m]]#,"$functionListParam.entityName");
                if(CollUtil.isNotEmpty(_list)){
                    for (int i = 0; i < _list.size(); i++) {
                        if($definitionFunction.buildListNullExpression()){}
                        else if($definitionFunction.getExpressionByParamIndex(2)){
                            _count++;
                        }
                    }
                }
            ),result(_count))
        #end
    #end
## 绘制表达式
    #[[$_ratingNormRecord_face_result:RatingNormRecord]]#(${ruleInfo.rule})
then
## 绘制结果
    #[[$_ratingNormRecord_face_result.setFinalScore(]]#BigDecimal.valueOf(${ruleInfo.score}));
    #[[$_ratingNormRecord_face_result.setRuleType(]]#${ruleInfo.ruleType});
    #[[$_ratingNormRecord_face_result.setResultExpression(]]#"$ruleInfo.ruleForUser");
    #[[$_ratingNormRecord_face_result.setRuleName(]]#"${ruleInfo.name}");
    #[[$_ratingNormRecord_face_result.setRemark(]]#"${ruleInfo.remark}");
    ##绘制结果表达式
    System.out.println("执行成功");
        ##渲染需要返回的系统实体/字段结果
##    #foreach($field in $businessFieldList)
##        #if($field.innerField==1)
##            #if($field.dataForm==0)
##                ##_result.put("定义名",$定义名) 放入到结果map中
##                _result.put#[[(]]#"$field.tableName",#[[$]]#$field.tableName#[[)]]#;
##            #end
##            #if($field.dataForm==1)
##                _result.put#[[(]]#"$field.customizeName",#[[$]]#$field.customizeName#[[)]]#;
##            #end
##        #end
##    #end
end
#end
