package com.drools.rules;
dialect "mvel";
global java.util.Map _result;
import java.lang.String;
import java.util.Map;
import java.util.List;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.Date;
import cn.hutool.core.date.LocalDateTimeUtil
import org.springblade.modules.riskmana.entity.RatingNormRecord
## 循环全局变量
#foreach($businessField in $businessFieldList)
global ${businessField.fieldType} ${businessField.paramName}
#end

##绘制规则
#foreach($ruleInfo in $ruleList)

rule "${normTemplates.templateId}_${normTemplates.name}_${foreach.index}"
    ##设置优先级 固定分组 命中一条规则后不再执行
    salience ${ruleInfo.sort}
    activation-group "${normTemplates.templateId}_${normTemplates.name}"
##绘制规则属性
when
## 绘制表达式
    #[[$ratingNormRecord_face_result:RatingNormRecord]]#(${ruleInfo.rule})
then
## 绘制结果
    #[[$ratingNormRecord_face_result.setFinalScore(]]#BigDecimal.valueOf(${ruleInfo.score}));
    #[[$ratingNormRecord_face_result.setRuleType(]]#${ruleInfo.ruleType});
    System.out.println("执行成功");
end
#end

##绘制公共方法
