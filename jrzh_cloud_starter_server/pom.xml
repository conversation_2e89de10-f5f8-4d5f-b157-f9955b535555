<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh_platform_manage</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>jrzh_cloud_starter_server</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springblade.finance</groupId>
            <artifactId>jrzh_cloud_finance</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

<!--        <dependency>
            <groupId>org.springblade.cloud</groupId>
            <artifactId>jrzh_cloud_mq</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>-->

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_cloud_product</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>