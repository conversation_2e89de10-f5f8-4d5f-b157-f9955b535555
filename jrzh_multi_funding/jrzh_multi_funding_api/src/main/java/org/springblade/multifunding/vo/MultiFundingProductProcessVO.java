/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.multifunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 多资方产品开通进度表视图实体类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MultiFundingProductProcessVO对象", description = "多资方产品开通进度表")
public class MultiFundingProductProcessVO extends TenantEntity{
    /**
     * 产品组id
     */
    @ApiModelProperty(value = "产品组id")
    private Long groupId;
    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;
    /**
     * 申请用户
     */
    @ApiModelProperty(value = "申请用户")
    private Long applyUser;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private Integer goodsType;
    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    private Integer userType;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;
    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private Long businessId;
    /**
     * 进度 同 ProcessProgressEnum 枚举类（根据不同流程类型取值不同）
     */
    @ApiModelProperty(value = "进度 同 ProcessProgressEnum 枚举类（根据不同流程类型取值不同）")
    private Integer progress;
    /**
     * 流程类型 同 ProcessTypeEnum 枚举类
     */
    @ApiModelProperty(value = "流程类型 同 ProcessTypeEnum 枚举类")
    private Integer type;
    /**
     * 前端用户能否操作 1 可 0 非
     */
    @ApiModelProperty(value = "前端用户能否操作 1 可 0 非")
    private Integer frontOperateAbility;
    /**
     * 流程信息
     */
    @ApiModelProperty(value = "流程信息")
    private String processJson;
    /**
     * 客户产品id
     */
    @ApiModelProperty(value = "客户产品id")
    private Long customerGoodsId;
}
