/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.multifunding.vo;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.utils.Func;
import org.springblade.product.common.dto.*;
import org.springblade.product.common.vo.GoodsLabelRelationVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 多资方产品表视图实体类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "MultiFundingProductVO对象", description = "多资方产品表")
public class MultiFundingProductVO extends TenantEntity {

	/**
	 * 是否有客户可开通产品
	 */
	@ApiModelProperty(value = "是否有客户可开通产品")
	private Boolean haveCustomerCanOpenProduct = true;

	/**
	 * 融资产品开通流程是否已完成
	 */
	@ApiModelProperty(value = "融资产品开通流程是否已完成")
	private Boolean isCompleted;

	/**
	 * 可开通标识
	 */
	private Boolean canOpenStatus;
	/**
	 * 客户产品id
	 */
	private Long customerGoodsId;
	/**
	 * 额度id
	 */
	private Long enterpriseQuotaId;
	/**
	 * 产品包id
	 */
	private Long groupId;


	@ApiModelProperty(value = "标签列表")
	private List<GoodsLabelRelationVO> labelList;

	@ApiModelProperty(value = "产品常见问题")
	private List<GoodsQuestionDTO> goodsQuestions;

	@ApiModelProperty(value = "产品开通流程")
	private List<GoodsOpeningProcessDTO> goodsOpeningProcesses;

	/**
	 * 操作人
	 */
	@ApiModelProperty(value = "操作人")
	private String operator;

	/**
	 * 标签id
	 */
	@ApiModelProperty(value = "标签id")
	private List<Long> labelIds;

	/**
	 * 产品资料
	 */
	@ApiModelProperty(value = "产品资料")
	private List<GoodsMaterialDTO> goodsMaterials;

	/**
	 * 产品绑定流程
	 */
	@ApiModelProperty(value = "产品绑定流程")
	private List<GoodsProcessDTO> goodsProcessList;

	@ApiModelProperty(value = "定时器管理")
	private List<GoodsTimingDTO> goodsTimingList;

	/**
	 * 产品编号
	 */
	@ApiModelProperty(value = "产品编号")
	private String goodsCode;
	/**
	 * 产品一级分类
	 */
	@ApiModelProperty(value = "产品一级分类")
	private Integer type;
	/**
	 * 产品名称
	 */
	@ApiModelProperty(value = "产品名称")
	private String goodsName;
	/**
	 * 二级产品分类id
	 */
	@ApiModelProperty(value = "二级产品分类id")
	private Long goodsTypeId;

	/**
	 * 二级产品分类名称
	 */
	@ApiModelProperty(value = "二级产品分类名称")
	private String goodsTypeName;
	/**
	 * 产品说明
	 */
	@ApiModelProperty(value = "产品说明")
	private String goodsExplain;
	/**
	 * 产品背景
	 */
	@ApiModelProperty(value = "产品背景")
	private String background;
	/**
	 * 借款金额
	 */
	@ApiModelProperty(value = "借款最低金额")
	private BigDecimal loanAmountStart;

	@ApiModelProperty(value = "借款最高金额")
	private BigDecimal loanAmountEnd;
	/**
	 * 年利率
	 */
	@ApiModelProperty(value = "最低年利率")
	private BigDecimal annualInterestRateStart;

	/**
	 * 年利率
	 */
	@ApiModelProperty(value = "最高年利率")
	private BigDecimal annualInterestRateEnd;
	/**
	 * 授信表单
	 */
	@ApiModelProperty(value = "授信表单")
	private Long creditFormId;
	/**
	 * 借款期限单位
	 */
	@ApiModelProperty(value = "借款期限单位")
	private Integer loadTermUnit;
	/**
	 * 借款期限
	 */
	@ApiModelProperty(value = "借款最低期限")
	private Integer loadTermStart;

	@ApiModelProperty(value = "借款最高期限")
	private Integer loadTermEnd;
	/**
	 * 关联的产品数量
	 */
	private Integer relationNum;

	@ApiModelProperty(value = "是否为优质产品")
	private Integer isHighQuality;

	@ApiModelProperty(value = "资金方id")
	private Long capitalId;

	/**
	 * 资方Logo
	 */
	@ApiModelProperty(value = "资金方Logo")
	private String capitalLogo;

	/**
	 * 还款类型(OverdueAlterationRepaymentModeEnum分期：1随借：2)
	 */
	@ApiModelProperty(value = "还款类型")
	private Integer repaymentType;




	/**
	 * 年利率类型
	 */
	@ApiModelProperty(value = "年利率类型")
	private Integer annualInterestRateType;

	@ApiModelProperty(value = "银行卡代收类型")
	private Integer bankCardCollectionType;

	@ApiModelProperty(value = "计费方式")
	private String billingMethod;

	@ApiModelProperty("收费方式 GoodsEnum.UNIFIED 1 资方统一收费 2 平台资方单独收取 ")
	private Integer chargeMethod;

	@ApiModelProperty("随借随还-计息天数")
	private Integer interestDay;

	@ApiModelProperty(value = "是否支持展期 0不支持 1支持")
	private Integer isDelay;

	/**
	 * 放款方式 1、自动放款 2、手动放款
	 */
	private Integer lendingMethod;

	@ApiModelProperty("最高可贷成数")
	private BigDecimal loanableEnd;

	@ApiModelProperty("最低可贷成数")
	private BigDecimal loanableStart;

	/**
	 * 提前还款类型
	 */
	@ApiModelProperty(value = "提前还款类型")
	private Integer prepaymentType;

	@ApiModelProperty(value = "规则场景id")
	private Long ruleSceneId;

	/**
	 * 评分模板id
	 */
	@ApiModelProperty("评分模板")
	private Long scoreTemplateId;

	/**
	 * 拼接借款最高期限+单位
	 *
	 * @return
	 */
	public String getLoadTermEndAndUnit() {
		// 获取最高借款期限数值
		Integer loadTermEnd = getLoadTermEnd();
		if (ObjectUtil.isEmpty(loadTermEnd) || loadTermEnd.intValue() <= 0) {
			return "";
		}

		// 获取借款期限单位数值，1-天，2-期
		Integer loadTermUnit = getLoadTermUnit();
		// 根据借款单位数值，得到对应的单位字符串
		String loadTermUnitStr = getLoadTermUnitStr(loadTermUnit);

		return loadTermEnd + loadTermUnitStr;
	}

	/**
	 * 根据借款单位数值，得到对应的单位，1-天，2-期，其他情况返回空字符串
	 *
	 * @param loadTermUnit
	 * @return
	 */
	private String getLoadTermUnitStr(Integer loadTermUnit) {
		if (ObjectUtil.isEmpty(loadTermUnit) || loadTermUnit <= 0) {
			return "";
		}

		if (GoodsEnum.TERM.getCode().equals(loadTermUnit)) {
			return GoodsEnum.TERM.getName();
		} else if (GoodsEnum.DAY.getCode().equals(loadTermUnit)) {
			return GoodsEnum.DAY.getName();
		} else {
			return "";
		}
	}


	/**
	 * 获取借款金额最低-最高区间，字符串形式
	 *
	 * @return
	 */
	public String getLoanAmountStr() {
		BigDecimal loanAmountStart = getLoanAmountStart();
		BigDecimal loanAmountEnd = getLoanAmountEnd();
		if (Func.hasEmpty(loanAmountStart, loanAmountEnd) ||
				loanAmountStart.compareTo(BigDecimal.ZERO) == 0 ||
				loanAmountEnd.compareTo(BigDecimal.ZERO) == 0) {
			return "";
		}
		return loanAmountStart.toString().concat("~").concat(loanAmountEnd.toString()).concat("万");
	}

	/**
	 * 获取借款期限最低-最高区间（单位）
	 *
	 * @return
	 */
	public String getLoadTermStr() {
		Integer loadTermEnd = getLoadTermEnd();
		Integer loadTermStart = getLoadTermStart();
		Integer loadTermUnit = getLoadTermUnit();
		if (Func.hasEmpty(loadTermStart, loadTermEnd, loadTermUnit) ||
				loadTermStart == 0 || loadTermEnd == 0) {
			return "";
		}
		String unit;
		if (loadTermUnit.equals(GoodsEnum.TERM.getCode())) {
			unit = GoodsEnum.TERM.getName();
		} else {
			unit = GoodsEnum.DAY.getName();
		}
		return String.valueOf(loadTermStart).concat("~").concat(String.valueOf(loadTermEnd)).concat(unit);
	}
}
