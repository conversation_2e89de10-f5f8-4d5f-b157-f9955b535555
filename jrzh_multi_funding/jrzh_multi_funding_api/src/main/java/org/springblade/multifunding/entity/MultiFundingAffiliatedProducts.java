/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.multifunding.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 多资方-产品组关联产品实体类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Data
@TableName("jrzh_multi_funding_affiliated_products")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MultiFundingAffiliatedProducts对象", description = "多资方-产品组关联产品")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultiFundingAffiliatedProducts extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 产品id
	*/
	@ApiModelProperty(value = "产品id")
	private Long goodsId;

	/**
	* 产品组id
	*/
	@ApiModelProperty(value = "产品组id")
	private Long groupId;

	/**
	 * 资金方id
	 */
	@ApiModelProperty(value = "资金方id")
	private Long capitalId;

	/**
	 * 是否需要准入(1: 需要准入， 0: 无需准入)
	 */
	@ApiModelProperty(value = "是否需要准入(1: 需要准入， 0: 无需准入)")
	private Integer isRisk;

	/**
	 * 产品名称
	 */
	@ApiModelProperty(value = "产品名称")
	private String goodsName;
}
