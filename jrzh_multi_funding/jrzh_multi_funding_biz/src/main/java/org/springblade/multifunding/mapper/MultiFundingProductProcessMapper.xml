<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.multifunding.mapper.MultiFundingProductProcessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="multiFundingProductProcessResultMap" type="org.springblade.multifunding.entity.MultiFundingProductProcess">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="group_id" property="groupId"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="goods_type" property="goodsType"/>
        <result column="user_type" property="userType"/>
        <result column="user_id" property="userId"/>
        <result column="business_id" property="businessId"/>
        <result column="progress" property="progress"/>
        <result column="type" property="type"/>
        <result column="front_operate_ability" property="frontOperateAbility"/>
        <result column="process_json" property="processJson"/>
        <result column="customer_goods_id" property="customerGoodsId"/>
    </resultMap>


    <select id="selectMultiFundingProductProcessPage" resultMap="multiFundingProductProcessResultMap">
        select * from jrzh_multi_funding_product_process where is_deleted = 0
    </select>

</mapper>
