<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.multifunding.mapper.MultiFundingProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="multiFundingProductResultMap" type="org.springblade.multifunding.entity.MultiFundingProduct">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="goods_code" property="goodsCode"/>
        <result column="type" property="type"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_type_id" property="goodsTypeId"/>
        <result column="loan_amount_start" property="loanAmountStart"/>
        <result column="loan_amount_end" property="loanAmountEnd"/>
        <result column="load_term_start" property="loadTermStart"/>
        <result column="load_term_end" property="loadTermEnd"/>
        <result column="load_term_unit" property="loadTermUnit"/>
        <result column="annual_interest_rate_start" property="annualInterestRateStart"/>
        <result column="annual_interest_rate_end" property="annualInterestRateEnd"/>
        <result column="annual_interest_rate_type" property="annualInterestRateType"/>
        <result column="credit_form_id" property="creditFormId"/>
        <result column="goods_explain" property="goodsExplain"/>
        <result column="background" property="background"/>
        <result column="relation_num" property="relationNum"/>
        <result column="is_high_quality" property="isHighQuality"/>
        <result column="repayment_type" property="repaymentType"/>
    </resultMap>


    <select id="selectMultiFundingProductPage" resultMap="multiFundingProductResultMap">
        select * from jrzh_multi_funding_product where is_deleted = 0
    </select>

</mapper>
