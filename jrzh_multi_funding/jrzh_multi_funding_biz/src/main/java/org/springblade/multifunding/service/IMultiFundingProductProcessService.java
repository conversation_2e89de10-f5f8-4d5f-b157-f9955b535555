/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.multifunding.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.multifunding.dto.SubmitMultiFundingProductDTO;
import org.springblade.multifunding.entity.MultiFundingProductProcess;
import org.springblade.multifunding.vo.MultiFundingProductProcessVO;


/**
 * 多资方产品开通进度表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface IMultiFundingProductProcessService extends BaseService<MultiFundingProductProcess> {

	/**
	 * 提交多资方产品 额度申请流程
	 * @param submitMultiFundingProductDTO
	 * @return
	 */
	Boolean submitMultiFundingProductApply(SubmitMultiFundingProductDTO submitMultiFundingProductDTO);

	/**
	 * 获取多资方产品开通流程状态
	 * @param businessId
	 * @param type
	 * @return
	 */
	MultiFundingProductProcessVO getMultiFundingProductProcessStatus(Long businessId, Integer type);

	/**
	 * 更新多资方产品开通流程进度
	 * @param businessId 业务id
	 * @param progress 进度
	 * @param type 类型
	 * @param processInstanceId 流程实例id
	 * @param userId 用户id
	 * @param status 状态
	 * @return Boolean
	 */
	Boolean updateMultiFundingProductProcessById(Long businessId, Integer progress, Integer type, String processInstanceId, Long userId, Integer status);

	/**
	 * 保存多资方产品开通进度
	 * @param gruopId 多资方产品id 业务id
	 * @param progress 进度
	 * @param type 类型
	 * @param processInstanceId 流程实例id
	 * @return MultiFundingProductProcessVO
	 */
	MultiFundingProductProcessVO saveBusinessProcessProgress(Long gruopId, Integer progress, Integer type, String processInstanceId, Integer status);

	/**
	 * 保存可开通产品信息
	 * @param processJson
	 * @param businessId
	 * @param type
	 * @return
	 */
	Boolean saveProcessdata(String processJson, Long businessId, Integer type);
}
