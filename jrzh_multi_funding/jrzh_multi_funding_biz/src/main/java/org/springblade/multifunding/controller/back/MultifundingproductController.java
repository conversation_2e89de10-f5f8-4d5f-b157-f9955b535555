package org.springblade.multifunding.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.multifunding.dto.multiFundingProductSaveAndUpdateDTO;
import org.springblade.multifunding.service.IMultiFundingProductService;
import org.springblade.multifunding.vo.MultiFundingProductVO;
import org.springframework.web.bind.annotation.*;


import javax.validation.Valid;
import java.util.Map;

/**
 * 多资方产品表 控制器
 * <AUTHOR>
 * @description:
 * @date 2025/1/17 14:35
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS + CommonConstant.WEB_BACK + "/multi-funding/product")
@Api(value = "多资方产品", tags = "多资方产品/平台接口")
public class MultifundingproductController {

    /**
     * 多资方产品表 服务类
     */
    private final IMultiFundingProductService multiFundingProductService;

    /**
     * 多资方产品详情
     * @param id 融资产品id
     * @return 多资方产品详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @PreAuth("hasPermission('multi-funding:product:detail') or hasRole('administrator')")
    @ApiOperation(value = "多资方产品详情")
    public R<multiFundingProductSaveAndUpdateDTO> detail(@RequestParam Long id) {
        return R.data(multiFundingProductService.detail(id));
    }

    /**
     * 分页 多资方产品列表
     * @param goods 多资方产品
     * @param query 查询条件
     * @return 多资方产品列表
     */
    @PostMapping("/list")
    @ApiOperationSupport(order = 2)
    @PreAuth("hasPermission('multi-funding:product:list') or hasRole('administrator')")
    @ApiOperation(value = "多资方产品列表")
    public R<IPage<MultiFundingProductVO>> list(@RequestBody Map<String, Object> goods, @RequestBody Query query) {
        goods.put("typeEqual",GoodsEnum.PRODUCT_GROUP_ROUTE.getCode());
        return R.data(multiFundingProductService.getMultiFundingProductPage(query, goods));
    }

    /**
     * 新增(保存) 多资方产品
     * @param multiFundingProduct 多资方产品
     * @return 是否成功
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @PreAuth("hasPermission('multi-funding:product:save') or hasRole('administrator')")
    @ApiOperation(value = "新增多资方产品")
    public R<Long> save(@Valid @RequestBody multiFundingProductSaveAndUpdateDTO multiFundingProduct) {
        multiFundingProduct.setType(GoodsEnum.PRODUCT_GROUP_ROUTE.getCode());
        return R.data(multiFundingProductService.saveProduct(multiFundingProduct));
    }

    /**
     * 修改 多资方产品
     * @param multiFundingProduct 多资方产品
     * @return 是否成功
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @PreAuth("hasPermission('multi-funding:product:update') or hasRole('administrator')")
    @ApiOperation(value = "修改多资方产品")
    public R<Boolean> update(@Valid @RequestBody multiFundingProductSaveAndUpdateDTO multiFundingProduct) {
        multiFundingProduct.setType(GoodsEnum.PRODUCT_GROUP_ROUTE.getCode());
        return R.data(multiFundingProductService.updateProduct(multiFundingProduct));
    }

    /**
     * 逻辑删除 多资方产品
     * @param id
     * @return
     */
    @GetMapping("/remove")
    @ApiOperationSupport(order = 7)
    @PreAuth("hasPermission('multi-funding:product:remove') or hasRole('administrator')")
    @ApiOperation(value = "逻辑删除多资方产品")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String id) {
        return R.data(multiFundingProductService.deleteLogic(Func.toLongList(id)));
    }

    /**
     * 上架 多资方产品
     * @param ids
     * @return
     */
    @GetMapping("/on-shelf")
    @PreAuth("hasPermission('multi-funding:product:on-shelf') or hasRole('administrator')")
    @ApiOperation("上架")
    public R<Boolean> onShelf(@RequestParam String ids) {
        return R.data(multiFundingProductService.changeStatus(Func.toLongList(ids), GoodsEnum.ON_SHELF.getCode()));
    }

    /**
     * 批量上架 多资方产品
     * @param ids
     * @return
     */
    @GetMapping("/batch-on-shelf")
    @PreAuth("hasPermission('multi-funding:product:batch-on-shelf') or hasRole('administrator')")
    @ApiOperation("批量上架")
    public R<Boolean> batchOnShelf(@RequestParam String ids) {
        return R.data(multiFundingProductService.changeStatus(Func.toLongList(ids), GoodsEnum.ON_SHELF.getCode()));
    }

    /**
     * 下架 多资方产品
     * @param ids
     * @return
     */
    @GetMapping("/off-shelf")
    @PreAuth("hasPermission('multi-funding:product:off-shelf') or hasRole('administrator')")
    @ApiOperation("下架")
    public R<Boolean> offShelf(@RequestParam String ids) {
        return R.data(multiFundingProductService.changeStatus(Func.toLongList(ids), GoodsEnum.OFF_SHELF.getCode()));
    }

    /**
     * 批量下架 多资方产品
     * @param ids
     * @return
     */
    @GetMapping("/batch-off-shelf")
    @PreAuth("hasPermission('multi-funding:product:batch-off-shelf') or hasRole('administrator')")
    @ApiOperation("批量下架")
    public R<Boolean> batchOffShelf(@RequestParam String ids) {
        return R.data(multiFundingProductService.changeStatus(Func.toLongList(ids), GoodsEnum.OFF_SHELF.getCode()));
    }
}
