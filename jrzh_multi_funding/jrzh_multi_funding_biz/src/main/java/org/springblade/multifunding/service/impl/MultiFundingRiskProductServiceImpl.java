/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.multifunding.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.*;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.dto.CustomerGoodsCoreDTO;
import org.springblade.customer.dto.ProductRouteRunRiskResultDTO;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.service.ICustomerMaterialService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.modules.othersapi.riskordertwo.dto.RiskTwoQuotaProcessDTO;
import org.springblade.multifunding.constant.MultiFundingProcessEnum;
import org.springblade.multifunding.vo.MultiFundingProductProcessVO;
import org.springblade.multifunding.vo.productPlusDTO;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.vo.GoodsLabelRelationVO;
import org.springblade.product.moudle.goodstype.service.IGoodsTypeService;
import org.springblade.multifunding.mapper.MultiFundingRiskProductMapper;
import org.springblade.multifunding.service.IMultiFundingAffiliatedProductsService;
import org.springblade.multifunding.service.IMultiFundingProductProcessService;
import org.springblade.multifunding.service.IMultiFundingProductService;
import org.springblade.multifunding.service.IMultiFundingRiskProductService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.multifunding.dto.MultiFundingRiskProductDTO;
import org.springblade.multifunding.entity.MultiFundingAffiliatedProducts;
import org.springblade.riskmana.api.dto.RatingRecordDTO;
import org.springblade.riskmana.api.entity.RatingRule;
import org.springblade.riskmana.core.handler.RiskOrderApiHandlerFactory;
import org.springblade.riskmana.core.service.IRatingRuleService;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;
import org.springblade.multifunding.entity.MultiFundingRiskProduct;
import org.springblade.multifunding.vo.MultiFundingRiskProductVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 多资方-资金产品风控准入表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
@RequiredArgsConstructor
public class MultiFundingRiskProductServiceImpl extends BaseServiceImpl<MultiFundingRiskProductMapper, MultiFundingRiskProduct> implements IMultiFundingRiskProductService {

    private final ICustomerInfoService customerInfoService;

    private final ICustomerBusinessInfoService customerBusinessInfoService;

    private final IMultiFundingProductProcessService multiFundingProductProcessService;

    private final ICustomerMaterialService customerMaterialService;

    private final IMultiFundingAffiliatedProductsService multiFundingAffiliatedProductsService;

    private final ICustomerGoodsService customerGoodsService;

    private final ProductDirector productDirector;

    private final IRatingRuleService ratingRuleService;

    private final RiskOrderApiHandlerFactory riskOrderApiHandlerFactory;

    private final IGoodsTypeService goodsTypeService;

    private final IBusinessProcessProgressService businessProcessProgressService;

    /**
     * 多资方产品服务类
     */
    private final IMultiFundingProductService multiFundingProductService;

    /**
     * 获取融资产品对应 准入资金产品列表
     * @param groupId
     * @return
     */
    @Override
    public List<MultiFundingRiskProductVO> getListByGroupId(Long groupId) {
        List<MultiFundingRiskProduct> multiFundingRiskProducts = baseMapper.selectList(Wrappers.<MultiFundingRiskProduct>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(groupId), MultiFundingRiskProduct::getGroupId, groupId));
        return BeanUtil.copyProperties(multiFundingRiskProducts, MultiFundingRiskProductVO.class);
    }

    /**
     * 获取可开通产品列表
     * @param multiFundingRiskProductDTO 多资方产品信息
     * @return 可开通产品列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<productPlusDTO> getAvailableProductList(MultiFundingRiskProductDTO multiFundingRiskProductDTO) {
        // 融资产品id
        Long groupId = multiFundingRiskProductDTO.getGroupId();
        // 用户id
        Long userId = MyAuthUtil.getUserId();
        // 企业类型
        Integer enterpriseType = UserUtils.getEnterpriseType();
        // 判断 上架 关联数量大于0
        Integer count = multiFundingProductService.getCountByIdAndStatusAndRelationNum(groupId, GoodsEnum.ON_SHELF.getCode(), 0);
        if (count <= 0) {
            throw new ServiceException("请联系管理员, 该融资产品存在无关联资金产品、未上架、不存在等问题!");
        }
        // 融资产品关联的 资金产品列表
        List<MultiFundingAffiliatedProducts> affiliatedProducts = multiFundingAffiliatedProductsService.getAffiliatedProductIdListByGroupId(groupId);
        // 关联的资金产品id列表
        List<Long> goodsIdList = CollStreamUtil.toList(affiliatedProducts, MultiFundingAffiliatedProducts::getGoodsId);
        // 检查客户是否开通过该产品 并获取出客户未开通的资金产品
        List<Long> canOpenGoodsIdList = probeAndScreening(goodsIdList, groupId, userId, enterpriseType);
        if (ObjectUtil.isEmpty(canOpenGoodsIdList)) {
            return Collections.emptyList();
        }
        // 设置关联的资金产品id列表
        multiFundingRiskProductDTO.setGoodsIdList(canOpenGoodsIdList);
//        multiFundingRiskProductDTO.setGoodsIdList(goodsIdList);
        // 保存客户资料
        saveCustomerMaterial(multiFundingRiskProductDTO);
        // 根据公司id 查询实名企业信息
        CustomerInfo customerInfo = customerInfoService.getByCompanyId(userId);
        // 通过工商注册号 查询工商信息
        CustomerBusinessInfo businessInfo = customerBusinessInfoService.getByCreditCode(customerInfo.getBusinessLicenceNumber());

        // 1.存在准入列表的需要准入，不存在准入列表不需要准入
        // 2.已经准入过的不需要准入（1.准入成功展示可开通列表中 2.准入成功但是已经开通成功的不需要展示 3.准入失败过滤掉）
        // 是否需要准入的产品列表
        List<MultiFundingAffiliatedProducts> needAdmittanceGoodsList = StreamUtil.filter(affiliatedProducts, e -> Objects.equals(MultiFundingProcessEnum.NEED_ADMITTANCE.getCode(), e.getIsRisk()));
        List<Long> needAdmittanceGoodsIdList = CollStreamUtil.toList(needAdmittanceGoodsList, MultiFundingAffiliatedProducts::getGoodsId);
        // 可开通 并且需要准入
        List<Long> canOpenAndNeedAdmittance = StreamUtil.filter(canOpenGoodsIdList, needAdmittanceGoodsIdList::contains);
        // 可开通 并且不需要准入
        List<Long> canOpenAndNotNeedAdmittance = StreamUtil.filter(canOpenGoodsIdList, e -> !canOpenAndNeedAdmittance.contains(e));
        // 风控准入 目前风控 默认成功
        List<ProductRouteRunRiskResultDTO> productRouteRunRiskResultDTOList = runRisk(multiFundingRiskProductDTO, businessInfo, canOpenAndNeedAdmittance);
        // 获取产品类型
        Long typeId = multiFundingProductService.getById(groupId).getGoodsTypeId();
        Integer goodsType = goodsTypeService.getById(typeId).getGoodsType();
        // 目前只考虑订单融资业务类型
        if (!Objects.equals(goodsType, GoodsEnum.ORDER_FINANCING.getCode())) {
            throw new ServiceException("暂不支持该业务类型");
        }
        // 可开通资金产品列表
        List<Product> productList = CollStreamUtil.toList(productRouteRunRiskResultDTOList, ProductRouteRunRiskResultDTO::getProduct);
        // 将不需风控准入的产品加入可开通列表中
        if (ObjectUtil.isNotEmpty(canOpenAndNotNeedAdmittance)) {
            productList.addAll(productDirector.selectList(canOpenAndNotNeedAdmittance));
        }
        if (ObjectUtil.isEmpty(productList)) {
            throw new ServiceException("准入失败或客户无可开通产品！");
        }
        // 保存融资产品开通进度
        checksMultiFundingProductProcess(groupId);
        // 融资产品 保存客户产品
        saveCustomerGoodsPlus(groupId, GoodsEnum.PRODUCT_GROUP_ROUTE.getCode(), userId, groupId);
        // 获取标签 map<资金产品id, 标签列表>
        Map<Long, List<GoodsLabelRelationVO>> labelMap = multiFundingProductService.getMapLabelInGoodsId(StreamUtil.map(productList, Product::getId));
        List<productPlusDTO> productPlusDTOList = BeanUtil.copyProperties(productList, productPlusDTO.class);
        // 依次执行单个产品的业务逻辑
        productPlusDTOList.forEach(product -> {
            Long goodsId = product.getId();
            // 保存信息
            saveCustomerGoodsPlus(goodsId, GoodsEnum.ORDER_FINANCING.getCode(), userId, groupId);
            // 检查流程是否存在 不存在则保存进度 返回申请额度的流程进度信息
            BusinessProcessProgress businessProcessProgress = checksAndUpdate(goodsId);
            // 设置合同业务编号
            product.setBizNos(businessProcessProgress.getId());
            // 设置标签
            product.setLabelList(labelMap.get(goodsId));
        });
        // 返回准入产品列表
        return productPlusDTOList;
    }

    /**
     * 检查客户产品中是否存在 筛选出客户未开通的产品
     * @param goodsIdList 关联产品id列表
     * @param groupId 融资产品id
     * @param userId 客户id
     * @param enterpriseType 企业类型
     * @return 客户未开通产品id列表
     */
    @Override
    public List<Long> probeAndScreening(List<Long> goodsIdList, Long groupId, Long userId, Integer enterpriseType) {
        // 查询客户是否在其它融资产品中开通了该融资产品的关联产品businessProcessProgressService
        List<CustomerGoods> customerGoodsList = customerGoodsService.getByEnterpriseIdAndEnterpriseTypeAndGroupIdAndGoodsIdsList(userId, enterpriseType, groupId, goodsIdList);
        // 客户已开通产品id列表
        List<Long> goodsIds = CollStreamUtil.toList(customerGoodsList, CustomerGoods::getGoodsId);
        // 返回客户未开通产品id
        return StreamUtil.filter(goodsIdList, goodsId -> !goodsIds.contains(goodsId));
//        return CollStreamUtil.toList(
//                customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery()
//                        .eq(CustomerGoods::getEnterpriseId, userId)
//                        .eq(CustomerGoods::getEnterpriseType, enterpriseType)
//                        .in(CustomerGoods::getGoodsId, goodsIdList)
//                        .in(CustomerGoods::getStatus, Arrays.asList(CustomerGoodsEnum.QUOTA_APPLICATION.getCode(),null)))
//                , CustomerGoods::getGoodsId);
    }

    /**
     * 检查融资产品流程是否存在 不存在则保存开通进度
     * @param groupId 融资产品id
     */
    public void checksMultiFundingProductProcess(Long groupId) {
        // 查询融资产品流程进度信息
        MultiFundingProductProcessVO multiFundingProductProcessStatus = multiFundingProductProcessService
                .getMultiFundingProductProcessStatus(groupId, ProcessTypeEnum.APPLY_QUOTA.getCode());
        // 如果流程为空 则保存流程进度
        if (ObjectUtil.isEmpty(multiFundingProductProcessStatus)) {
            multiFundingProductProcessService.saveBusinessProcessProgress(groupId, MultiFundingProcessEnum.PRODUCT_SELECTION.getCode(),
                    ProcessTypeEnum.APPLY_QUOTA.getCode(), null,null);
        }
    }

    /**
     * 融资产品 检查是否存在 不存在则保存客户产品
     * @param goodsId 资金产品id
     * @param goodsType 产品类型
     * @param userId 客户id
     * @param groupId 融资产品id
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCustomerGoodsPlus(Long goodsId, Integer goodsType, Long userId, Long groupId) {
        // 检查流程是否存在 不存在则保存进度
        Integer count = customerGoodsService.getCountByGoodsIdAndGoodsTypeAndEnterpriseIdAndEnterpriseTypeAndGroupId(goodsId, goodsType, userId, UserUtils.getEnterpriseType(), groupId);
        // 没有数据插入数据
        if (count < 1) {
            // 融资产品 保存信息
            CustomerGoodsCoreDTO customerGoodsCore = new CustomerGoodsCoreDTO();
            customerGoodsCore.setGoodsId(goodsId);
            customerGoodsCore.setGoodsType(goodsType);
            customerGoodsCore.setEnterpriseType(UserUtils.getEnterpriseType());
            customerGoodsCore.setEnterpriseId(userId);
            customerGoodsCore.setGroupId(groupId);
            // 判断是否未融资产品
            if (Objects.equals(goodsId, groupId)) {
                // 融资产品保存客户产品
                saveCustomerOpenGoods(customerGoodsCore);
            } else {
                // 资金产品 调用保存客户产品接口
                customerGoodsService.saveCustomerOpenGoods(customerGoodsCore);
            }
        }
    }

    /**
     * 检查资金产品是否保存流程 没保存执行保存操作
     * @param goodsId 资金产品id
     * @return 流程进度信息
     */
    public BusinessProcessProgress checksAndUpdate(Long goodsId) {
        BusinessProcessProgress business = businessProcessProgressService.getByBusinessIdAndType(goodsId, ProcessTypeEnum.APPLY_QUOTA.getCode(), MyAuthUtil.getUserId());
        if (ObjectUtil.isNotEmpty(business)) {
            return business;
        } else {
            // 保存进度 额度申请进度
            BusinessProcessProgress businessProcessProgress = businessProcessProgressService.saveBusinessProcessProgress(goodsId, MultiFundingProcessEnum.PRODUCT_SELECTION.getCode(),
                    ProcessTypeEnum.APPLY_QUOTA.getCode(), null);
            // 保存进度 额度激活进度
            businessProcessProgressService.saveBusinessProcessProgress(goodsId, ProcessProgressEnum.QUOTA_ACTIVE_SIGN_CONTRACT.getCode(), ProcessTypeEnum.QUOTA_ACTIVE.getCode(), null);
            return businessProcessProgress;
        }
    }

    /**
     * 融资 保存客户产品
     * @param customerGoodsDTO 客户产品信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCustomerOpenGoods(CustomerGoodsCoreDTO customerGoodsDTO) {
        // 产品id
        Long goodsId = customerGoodsDTO.getGoodsId();
        // 产品类型
        Integer goodsType = customerGoodsDTO.getGoodsType();
        // 融资产品id
        Long groupId = customerGoodsDTO.getGroupId();
        // 企业类型
        Integer enterpriseType = UserUtils.getEnterpriseType();
        // 客户id
        Long enterpriseId = customerGoodsDTO.getEnterpriseId();
        // 用户id
        Long userId = AuthUtil.getUserId();
        // 融资产品
        Product product = BeanUtil.copyProperties(multiFundingProductService.getById(groupId), Product.class);
        if(ObjectUtil.isEmpty(product)) {
            throw new ServiceException("该融资产品不存在!");
        }
        // 参数校验
        if (ObjectUtil.isEmpty(goodsId) || ObjectUtil.isEmpty(goodsType)) {
            throw new ServiceException(ResultCode.PARAM_VALID_ERROR);
        }
        // 融资用户应收业务排除，在选择贸易伙伴确定save后会顺带把客户产品给保存
        if (GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode().equals(goodsType) && EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode() == enterpriseType) {
            if (groupId != null){
                // 根据产品id、企业id(用户id) 获取客户产品信息
                CustomerGoods customerGoods = customerGoodsService.getOneByGoodsIdAndEnterpriseId(goodsId, userId);
                customerGoods.setGroupId(groupId);
                customerGoodsService.updateById(customerGoods);
                return;
            }
            return;
        }
        // 融资产品id不为空 且 资金方id为空
        if(ObjectUtil.isNotEmpty(groupId) && ObjectUtil.isEmpty(product.getCapitalId())){
            CustomerGoods customerGoods = CustomerGoods.builder()
                    .enterpriseId(enterpriseId)
                    .enterpriseType(enterpriseType)
                    .goodsName(product.getGoodsName())
                    .goodsType(goodsType)
                    .capitalId(0L)
                    .goodsId(goodsId)
                    .groupId(groupId)
                    .openNo(CodeUtil.generateCode(CodeEnum.OPEN_NO))
                    .build();
            // 获取过期产品
            CustomerGoods oldGoods = customerGoodsService.getOneByEnterpriseIdAndGoodsIdAndEnterpriseTypeAndStatus(enterpriseId, goodsId, enterpriseType, CustomerGoodsEnum.EXPIRE.getCode());
            // 如果存在过期产品 把过期产品删除
            if (oldGoods != null) {
                baseMapper.deleteById(oldGoods.getId());
            }
            customerGoodsService.save(customerGoods);
        }
    }

    /**
     * 运行风控
     * @param multiFundingRiskProductDTO 准入风控需要的信息
     * @param businessInfo 企业信息
     * @return List<ProductRouteRunRiskResultDTO>
     */
    private List<ProductRouteRunRiskResultDTO> runRisk(MultiFundingRiskProductDTO multiFundingRiskProductDTO, CustomerBusinessInfo businessInfo, List<Long> goodsIdList) {
        String tenantId = multiFundingRiskProductDTO.getTenantId() == null ? UserUtils.getTenantId() : multiFundingRiskProductDTO.getTenantId();
        // 如果为空 则返回空集合
        if (ObjectUtil.isEmpty(goodsIdList)) {
            return Collections.emptyList();
        }
        List<Product> productList = productDirector.selectList(goodsIdList);
        for (int i = 0; i < productList.size(); i++) {
            Product product = productList.get(i);
            product.setSort(i);
            product.setIsShow(1);
        }
        return productList.parallelStream().map(e ->
                // runRiskByProduct(产品信息, 客户资料, 工商信息)
                TenantBroker.applyAs(tenantId, el -> runRiskByProduct(e, multiFundingRiskProductDTO, businessInfo)))
                .filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
    }

    /**
     * 执行单个产品的对应风控
     */
    private ProductRouteRunRiskResultDTO runRiskByProduct(Product product, MultiFundingRiskProductDTO multiFundingRiskProductDTO, CustomerBusinessInfo businessInfo) {
        // 获取评分模板id
        Long scoreTemplateId = product.getScoreTemplateId();
        // 客户补充资料json值
        JSONObject runData = JSONUtil.parseObj(multiFundingRiskProductDTO.getData());
        // 评分规则
        RatingRule ratingRule = ratingRuleService.getById(scoreTemplateId);
        // 组装数据
        RatingRecordDTO ratingRecord = new RatingRecordDTO();
        // 评分模板id
        ratingRecord.setRatingId(scoreTemplateId);
        // 设置企业类型
        ratingRecord.setEnterpriseType(EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode());
        // 企业名称
        ratingRecord.setEnpName(businessInfo.getCompanyName());
        // 社会统一代码
        ratingRecord.setEnpSocialCode(businessInfo.getCreditCode());
        ratingRecord.setObj(runData);
        // 评分规则
        ratingRecord.setRatingRule(ratingRule);
        // 用户名称
        ratingRecord.setUserName(businessInfo.getCompanyName());
        ratingRecord.setWaitSysPass(CommonConstant.YES);
        // 用户类型 1个人 2企业
        ratingRecord.setUserType(CustomerTypeEnum.ENTERPRISE.getCode());
        // 用3.0的默认风控系统，但直接准入，不做直拒判断
        RatingRecordDTO ratingRecordDTO = new RatingRecordDTO();
        RiskTwoQuotaProcessDTO apply = riskOrderApiHandlerFactory.template(ratingRule.getSupplierNo()).apply(ratingRecordDTO);
        if (apply.getWhitePass()) {
            ProductRouteRunRiskResultDTO productRouteRunRiskResultDTO = new ProductRouteRunRiskResultDTO();
            // 设置产品信息
            productRouteRunRiskResultDTO.setProduct(product);
            // 设置运行的风控模板id
            productRouteRunRiskResultDTO.setRatingTemplateId(ratingRule.getTemplateId());
            // 风控规则id
            productRouteRunRiskResultDTO.setRuleId(scoreTemplateId);
            return productRouteRunRiskResultDTO;
        }
        return null;
    }

    /**
     * 保存客户资料
     * @param multiFundingRiskProductDTO
     */
    public void saveCustomerMaterial(MultiFundingRiskProductDTO multiFundingRiskProductDTO) {
        // 如果没有userId，则设置当前用户
        Long userId = multiFundingRiskProductDTO.getUserId() == null ? AuthUtil.getUserId() : multiFundingRiskProductDTO.getUserId();
        // 关联的资金列表
        List<Long> goodsIdList = multiFundingRiskProductDTO.getGoodsIdList();
        // 客户资料
        CustomerMaterial customerMaterial = CustomerMaterial
                .builder()
                .creditForm(multiFundingRiskProductDTO.getCreditForm())
                .data(multiFundingRiskProductDTO.getData())
                .supplementMaterial(multiFundingRiskProductDTO.getSupplementMaterial())
                .userId(userId)
                .build();
        if (ObjectUtil.isNotEmpty(goodsIdList)) {
            // 遍历保存客户资料
            goodsIdList.forEach(goodsId -> {
                // 检查是否保存过
                Integer count = customerMaterialService.getCountByUserIdAndGoodsId(userId, goodsId);
                // 存在直接返回
                if (count > 0) {
                    return;
                }
                // 设置产品id
                customerMaterial.setGoodsId(goodsId);
                // 资金产品 保存客户资料
                customerMaterialService.save(customerMaterial);
            });
        }
    }

}
