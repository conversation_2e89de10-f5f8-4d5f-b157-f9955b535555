body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-97px;
  width:2074px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
  margin-top: -53px;
  transform: scaleX(0.75) translateX(-306px);
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:1304px;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:151px;
  top:170px;
  width:288px;
  height:1304px;
  display:flex;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2074px;
  height:60px;
  background:inherit;
  background-color:rgba(247, 208, 22, 0.67843137254902);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:68px;
  width:2074px;
  height:60px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:1347px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:127px;
  width:54px;
  height:1347px;
  display:flex;
}
#u2 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:43px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:151px;
  top:127px;
  width:288px;
  height:43px;
  display:flex;
}
#u3 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:137px;
  width:181px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u4 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:125px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:617px;
  width:21px;
  height:125px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u5 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:1304px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:170px;
  width:288px;
  height:1304px;
  display:flex;
}
#u6 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:1304px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:170px;
  width:290px;
  height:1304px;
  display:flex;
}
#u7 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:289px;
  height:1305px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:1017px;
  top:170px;
  width:289px;
  height:1305px;
  display:flex;
}
#u8 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u9_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:1304px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:1595px;
  top:170px;
  width:288px;
  height:1304px;
  display:flex;
}
#u9 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u9_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u10_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:289px;
  height:1304px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:170px;
  width:289px;
  height:1304px;
  display:flex;
}
#u10 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u11_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:65px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:222px;
  top:207px;
  width:133px;
  height:65px;
  display:flex;
}
#u11 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:320px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u12 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u12.mouseOver {
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:611px;
  top:-89px;
  width:0px;
  height:0px;
}
#u13_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:32px;
}
#u13_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:22px;
  width:515px;
  height:10px;
}
#u13_seg2 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:-15px;
  width:34px;
  height:34px;
}
#u13_seg3 {
  border-width:0px;
  position:absolute;
  left:498px;
  top:17px;
  width:20px;
  height:20px;
}
#u13_text {
  border-width:0px;
  position:absolute;
  left:192px;
  top:19px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:272px;
  width:0px;
  height:0px;
}
#u14_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:53px;
}
#u14_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:29px;
  width:34px;
  height:34px;
}
#u14_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:16px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:392px;
  width:0px;
  height:0px;
}
#u15_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:56px;
}
#u15_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:32px;
  width:34px;
  height:34px;
}
#u15_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:18px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u16_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:443px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u16 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u16_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u17_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:559px;
  width:150px;
  height:72px;
  display:flex;
  color:#FFFFFF;
}
#u17 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u17_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:515px;
  width:0px;
  height:0px;
}
#u18_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:49px;
}
#u18_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:25px;
  width:34px;
  height:34px;
}
#u18_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:14px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u19_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:559px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u19 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u19_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:693px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u20 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:631px;
  width:0px;
  height:0px;
}
#u21_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:67px;
}
#u21_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:43px;
  width:34px;
  height:34px;
}
#u21_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:23px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u22_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:803px;
  top:559px;
  width:150px;
  height:72px;
  display:flex;
  color:#FFFFFF;
}
#u22 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u22_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u23_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:679px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u24_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:1085px;
  top:559px;
  width:150px;
  height:72px;
  display:flex;
  color:#FFFFFF;
}
#u24 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u25_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:812px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u25 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:751px;
  width:0px;
  height:0px;
}
#u26_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:66px;
}
#u26_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:42px;
  width:34px;
  height:34px;
}
#u26_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:631px;
  width:0px;
  height:0px;
}
#u27_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:53px;
}
#u27_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:29px;
  width:34px;
  height:34px;
}
#u27_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:16px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u28_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:1087px;
  top:679px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:1160px;
  top:631px;
  width:0px;
  height:0px;
}
#u29_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:53px;
}
#u29_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:29px;
  width:34px;
  height:34px;
}
#u29_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:16px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:1380px;
  top:559px;
  width:150px;
  height:72px;
  display:flex;
  color:#FFFFFF;
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:148px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:1364px;
  top:679px;
  width:72px;
  height:148px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u31 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:1455px;
  top:631px;
  width:0px;
  height:0px;
}
#u32_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:24px;
}
#u32_seg1 {
  border-width:0px;
  position:absolute;
  left:-60px;
  top:14px;
  width:65px;
  height:10px;
}
#u32_seg2 {
  border-width:0px;
  position:absolute;
  left:-60px;
  top:14px;
  width:10px;
  height:34px;
}
#u32_seg3 {
  border-width:0px;
  position:absolute;
  left:-72px;
  top:29px;
  width:34px;
  height:34px;
}
#u32_text {
  border-width:0px;
  position:absolute;
  left:-82px;
  top:11px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:815px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u33 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:941px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u34 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:884px;
  width:0px;
  height:0px;
}
#u35_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:62px;
}
#u35_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:38px;
  width:34px;
  height:34px;
}
#u35_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:765px;
  width:0px;
  height:0px;
}
#u36_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:55px;
}
#u36_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:31px;
  width:34px;
  height:34px;
}
#u36_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:17px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u37_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:1087px;
  top:812px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u37 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:1160px;
  top:751px;
  width:0px;
  height:0px;
}
#u38_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:66px;
}
#u38_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:42px;
  width:34px;
  height:34px;
}
#u38_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u39_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:1379px;
  top:1298px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u39 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u40_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:148px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:1470px;
  top:679px;
  width:72px;
  height:148px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u40 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:1455px;
  top:631px;
  width:0px;
  height:0px;
}
#u41_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:23px;
}
#u41_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:13px;
  width:61px;
  height:10px;
}
#u41_seg2 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:13px;
  width:10px;
  height:35px;
}
#u41_seg3 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:29px;
  width:34px;
  height:34px;
}
#u41_text {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:10px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u42_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:148px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:1470px;
  top:876px;
  width:72px;
  height:148px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u42 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u42_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u43_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:148px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:1470px;
  top:1074px;
  width:72px;
  height:148px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:827px;
  width:0px;
  height:0px;
}
#u44_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:54px;
}
#u44_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:30px;
  width:34px;
  height:34px;
}
#u44_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:16px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:1024px;
  width:0px;
  height:0px;
}
#u45_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:55px;
}
#u45_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:31px;
  width:34px;
  height:34px;
}
#u45_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:17px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:1222px;
  width:0px;
  height:0px;
}
#u46_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:39px;
}
#u46_seg1 {
  border-width:0px;
  position:absolute;
  left:-54px;
  top:29px;
  width:59px;
  height:10px;
}
#u46_text {
  border-width:0px;
  position:absolute;
  left:-60px;
  top:26px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u47_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:1667px;
  top:559px;
  width:150px;
  height:72px;
  display:flex;
  color:#FFFFFF;
}
#u47 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u47_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:1400px;
  top:827px;
  width:0px;
  height:0px;
}
#u48_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:240px;
}
#u48_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:230px;
  width:62px;
  height:10px;
}
#u48_seg2 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:230px;
  width:10px;
  height:241px;
}
#u48_seg3 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:452px;
  width:34px;
  height:34px;
}
#u48_text {
  border-width:0px;
  position:absolute;
  left:-24px;
  top:227px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u49_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:1669px;
  top:681px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u49 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:1742px;
  top:631px;
  width:0px;
  height:0px;
}
#u50_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:55px;
}
#u50_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:31px;
  width:34px;
  height:34px;
}
#u50_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:18px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u51_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:1669px;
  top:812px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u51 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u51_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:1742px;
  top:753px;
  width:0px;
  height:0px;
}
#u52_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:64px;
}
#u52_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:40px;
  width:34px;
  height:34px;
}
#u52_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:751px;
  width:0px;
  height:0px;
}
#u53_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:66px;
}
#u53_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:42px;
  width:34px;
  height:34px;
}
#u53_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:884px;
  width:0px;
  height:0px;
}
#u54_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:62px;
}
#u54_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:38px;
  width:34px;
  height:34px;
}
#u54_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:1013px;
  width:0px;
  height:0px;
}
#u55_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:60px;
}
#u55_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:36px;
  width:34px;
  height:34px;
}
#u55_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:1140px;
  width:0px;
  height:0px;
}
#u56_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:64px;
}
#u56_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:40px;
  width:34px;
  height:34px;
}
#u56_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:2104px;
  top:595px;
  width:0px;
  height:0px;
}
#u57_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:21px;
  height:10px;
}
#u57_seg1 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:-5px;
  width:10px;
  height:392px;
}
#u57_seg2 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:377px;
  width:24px;
  height:10px;
}
#u57_seg3 {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:365px;
  width:34px;
  height:34px;
}
#u57_text {
  border-width:0px;
  position:absolute;
  left:-34px;
  top:185px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:595px;
  width:0px;
  height:0px;
}
#u58_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:149px;
  height:10px;
}
#u58_seg1 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:-17px;
  width:34px;
  height:34px;
}
#u58_text {
  border-width:0px;
  position:absolute;
  left:22px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:631px;
  width:0px;
  height:0px;
}
#u59_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:225px;
}
#u59_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:215px;
  width:224px;
  height:10px;
}
#u59_seg2 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:203px;
  width:34px;
  height:34px;
}
#u59_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:212px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u60_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:656px;
  width:14px;
  height:16px;
  display:flex;
}
#u60 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u60_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u61_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:574px;
  width:14px;
  height:16px;
  display:flex;
}
#u61 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u61_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u62 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:851px;
  width:0px;
  height:0px;
}
#u62_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:42px;
  height:10px;
}
#u62_seg1 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:-261px;
  width:10px;
  height:266px;
}
#u62_seg2 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:-261px;
  width:118px;
  height:10px;
}
#u62_seg3 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:-273px;
  width:34px;
  height:34px;
}
#u62_text {
  border-width:0px;
  position:absolute;
  left:-13px;
  top:-174px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:595px;
  width:0px;
  height:0px;
}
#u63_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:22px;
  height:10px;
}
#u63_seg1 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:-5px;
  width:10px;
  height:392px;
}
#u63_seg2 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:377px;
  width:25px;
  height:10px;
}
#u63_seg3 {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:365px;
  width:34px;
  height:34px;
}
#u63_text {
  border-width:0px;
  position:absolute;
  left:-33px;
  top:185px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u64 {
  border-width:0px;
  position:absolute;
  left:1235px;
  top:595px;
  width:0px;
  height:0px;
}
#u64_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:20px;
  height:10px;
}
#u64_seg1 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:-5px;
  width:10px;
  height:263px;
}
#u64_seg2 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:248px;
  width:23px;
  height:10px;
}
#u64_seg3 {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:236px;
  width:34px;
  height:34px;
}
#u64_text {
  border-width:0px;
  position:absolute;
  left:-35px;
  top:120px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:1163px;
  top:887px;
  width:0px;
  height:0px;
}
#u65_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:21px;
}
#u65_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:11px;
  width:136px;
  height:10px;
}
#u65_seg2 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:-297px;
  width:10px;
  height:318px;
}
#u65_seg3 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:-297px;
  width:96px;
  height:10px;
}
#u65_seg4 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:-309px;
  width:34px;
  height:34px;
}
#u65_text {
  border-width:0px;
  position:absolute;
  left:76px;
  top:-120px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u66_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:43px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u66 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:127px;
  width:288px;
  height:43px;
  display:flex;
}
#u66 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u66_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u67_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:43px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:127px;
  width:290px;
  height:43px;
  display:flex;
}
#u67 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u67_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u68_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:289px;
  height:43px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u68 {
  border-width:0px;
  position:absolute;
  left:1017px;
  top:127px;
  width:289px;
  height:43px;
  display:flex;
}
#u68 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u68_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u69_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:289px;
  height:43px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:127px;
  width:289px;
  height:43px;
  display:flex;
}
#u69 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u69_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u70_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:43px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u70 {
  border-width:0px;
  position:absolute;
  left:1595px;
  top:127px;
  width:288px;
  height:43px;
  display:flex;
}
#u70 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u70_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u71_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:137px;
  width:179px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u71 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u71_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u72_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:803px;
  top:137px;
  width:158px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u72 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u72_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u73_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:1073px;
  top:137px;
  width:178px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u73 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u73_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u74_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:1372px;
  top:137px;
  width:177px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u74 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u74_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u75_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:43px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:1883px;
  top:127px;
  width:288px;
  height:43px;
  display:flex;
}
#u75 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u75_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u76_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:174px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:1653px;
  top:137px;
  width:174px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u76 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u76_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u77_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:1944px;
  top:137px;
  width:168px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
}
#u77 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u77_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u78_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:1304px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 8, 51, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:1883px;
  top:170px;
  width:288px;
  height:1304px;
  display:flex;
}
#u78 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u78_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u79_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:1954px;
  top:559px;
  width:150px;
  height:72px;
  display:flex;
  color:#FFFFFF;
}
#u79 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u79_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u80_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:1956px;
  top:679px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u80 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u80_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u81_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:1956px;
  top:812px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u81 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u81_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u82_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:1956px;
  top:941px;
  width:145px;
  height:72px;
  display:flex;
  text-decoration:underline ;
  color:#FFFFFF;
}
#u82 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u82_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u83_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:72px;
  background:inherit;
  background-color:rgba(97, 137, 239, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(97, 137, 239, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:1956px;
  top:1068px;
  width:145px;
  height:72px;
  display:flex;
  color:#FFFFFF;
}
#u83 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u83_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u84_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:65px;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:1962px;
  top:1199px;
  width:133px;
  height:65px;
  display:flex;
}
#u84 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u84_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:1013px;
  width:0px;
  height:0px;
}
#u85_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:22px;
}
#u85_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:12px;
  width:190px;
  height:10px;
}
#u85_seg2 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:-423px;
  width:10px;
  height:445px;
}
#u85_seg3 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:-423px;
  width:32px;
  height:10px;
}
#u85_seg4 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:-435px;
  width:34px;
  height:34px;
}
#u85_text {
  border-width:0px;
  position:absolute;
  left:130px;
  top:-123px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u86_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:9px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:1464px;
  top:631px;
  width:36px;
  height:9px;
  display:flex;
}
#u86 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u86_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:1530px;
  top:595px;
  width:0px;
  height:0px;
}
#u87_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:39px;
  height:10px;
}
#u87_seg1 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:-5px;
  width:10px;
  height:749px;
}
#u87_seg2 {
  border-width:0px;
  position:absolute;
  left:-6px;
  top:734px;
  width:45px;
  height:10px;
}
#u87_seg3 {
  border-width:0px;
  position:absolute;
  left:-21px;
  top:722px;
  width:34px;
  height:34px;
}
#u87_text {
  border-width:0px;
  position:absolute;
  left:-16px;
  top:365px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:1452px;
  top:1370px;
  width:0px;
  height:0px;
}
#u88_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:15px;
}
#u88_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:5px;
  width:192px;
  height:10px;
}
#u88_seg2 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:-780px;
  width:10px;
  height:795px;
}
#u88_seg3 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:-780px;
  width:38px;
  height:10px;
}
#u88_seg4 {
  border-width:0px;
  position:absolute;
  left:196px;
  top:-792px;
  width:34px;
  height:34px;
}
#u88_text {
  border-width:0px;
  position:absolute;
  left:132px;
  top:-311px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:1817px;
  top:595px;
  width:0px;
  height:0px;
}
#u89_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:18px;
  height:10px;
}
#u89_seg1 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:-5px;
  width:10px;
  height:263px;
}
#u89_seg2 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:248px;
  width:21px;
  height:10px;
}
#u89_seg3 {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:236px;
  width:34px;
  height:34px;
}
#u89_text {
  border-width:0px;
  position:absolute;
  left:-37px;
  top:120px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:1742px;
  top:884px;
  width:0px;
  height:0px;
}
#u90_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:21px;
}
#u90_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:11px;
  width:191px;
  height:10px;
}
#u90_seg2 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:-294px;
  width:10px;
  height:315px;
}
#u90_seg3 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:-294px;
  width:36px;
  height:10px;
}
#u90_seg4 {
  border-width:0px;
  position:absolute;
  left:193px;
  top:-306px;
  width:34px;
  height:34px;
}
#u90_text {
  border-width:0px;
  position:absolute;
  left:131px;
  top:-62px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u91 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:631px;
  width:0px;
  height:0px;
}
#u91_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:53px;
}
#u91_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:29px;
  width:34px;
  height:34px;
}
#u91_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:16px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:751px;
  width:0px;
  height:0px;
}
#u92_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:66px;
}
#u92_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:42px;
  width:34px;
  height:34px;
}
#u92_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:884px;
  width:0px;
  height:0px;
}
#u93_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:62px;
}
#u93_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:38px;
  width:34px;
  height:34px;
}
#u93_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:1013px;
  width:0px;
  height:0px;
}
#u94_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:60px;
}
#u94_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:36px;
  width:34px;
  height:34px;
}
#u94_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:2029px;
  top:1140px;
  width:0px;
  height:0px;
}
#u95_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:64px;
}
#u95_seg1 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:40px;
  width:34px;
  height:34px;
}
#u95_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:2104px;
  top:595px;
  width:0px;
  height:0px;
}
#u96_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:21px;
  height:10px;
}
#u96_seg1 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:-5px;
  width:10px;
  height:392px;
}
#u96_seg2 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:377px;
  width:24px;
  height:10px;
}
#u96_seg3 {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:365px;
  width:34px;
  height:34px;
}
#u96_text {
  border-width:0px;
  position:absolute;
  left:-34px;
  top:185px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
