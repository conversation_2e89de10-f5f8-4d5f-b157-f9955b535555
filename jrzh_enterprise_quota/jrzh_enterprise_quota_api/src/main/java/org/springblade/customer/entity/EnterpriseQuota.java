/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.utils.DateUtil;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 核心企业额度实体类
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Data
@TableName("jrzh_enterprise_quota")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CoreEnterpriseQuota对象", description = "核心企业额度")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnterpriseQuota extends TenantEntity {

	private static final long serialVersionUID = 1L;
	/**
	 * 额度编号
	 */
	@ApiModelProperty(value = "额度编号")
	private String quotaNo;
	/**
	 * 任务编号
	 */
	@ApiModelProperty(value = "任务编号")
	private String taskNo;


	/**
	 * 核心企业用户id
	 */
	@ApiModelProperty(value = "核心企业用户id")
	@NotNull(message = "核心企业用户id不能为空")
	private Long enterpriseId;

	/**
	 * 资方id
	 */
	@ApiModelProperty(value = "资方id")
	@NotNull(message = "资方id不能为空")
	private Long capitalId;

	/**
	 * 授信额度
	 */
	@ApiModelProperty(value = "授信额度")
	@NotNull(message = "授信额度不能为空")
	private BigDecimal creditAmount;
	/**
	 * 年利率
	 */
	@ApiModelProperty(value = "年利率")
	private BigDecimal annualInterestRate;

	@ApiModelProperty("应收账款可贷成数")
	private BigDecimal loanable;

	/**
	 * 日利率
	 */
	@ApiModelProperty(value = "日利率")
	private BigDecimal dailyInterestRate;
	/**
	 * 已使用额度
	 */
	@ApiModelProperty(value = "已使用额度")
	private BigDecimal usedAmount;
	/**
	 * 冻结额度
	 */
	@ApiModelProperty(value = "冻结额度")
	private BigDecimal frozenAmount;
	/**
	 * 可用额度
	 */
	@ApiModelProperty(value = "可用额度")
	private BigDecimal availableAmount;

	@ApiModelProperty(value = "申请中额度")
	private BigDecimal applyAmount;
	/**
	 * 生效时间
	 */
	@ApiModelProperty(value = "生效时间")
	@NotNull(message = "生效日不能为空")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private LocalDateTime effectiveTime;
	/**
	 * 过期时间
	 */
	@ApiModelProperty(value = "过期时间")
	@NotNull(message = "到期日不能为空")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	private LocalDateTime expireTime;


	@ApiModelProperty(value = "额度类型 1 循环 2 非循环")
	@NotNull(message = "额度类型不能为空")
	private Integer quotaType;

	@ApiModelProperty("循环类型： 1 还款后循环 2 结清后循环")
	private Integer recycleType;

	@ApiModelProperty(value = "企业类型 1 融资 2 核心")
	@NotNull(message = "企业类型不能为空")
	private Integer enterpriseType;

	@ApiModelProperty("开户行")
	@NotBlank(message = "开户行不能为空")
	private String bank;

	@ApiModelProperty("银行卡号")
	@NotBlank(message = "银行卡号不能为空")
	private String bankCardNo;

	@ApiModelProperty("银行Id 使用本地银行数据才有值")
	private Long bankId;

	@ApiModelProperty("银联号")
	private String bankUnionCode;

	@ApiModelProperty("冻结原因")
	private String frozenReason;

	@ApiModelProperty("解冻原因")
	private String thawReason;

	@ApiModelProperty("禁用原因")
	private String disableReason;

	@ApiModelProperty(value = "产品名称")
	private String goodsName;

	@ApiModelProperty(value = "产品id")
	@NotNull(message = "产品id不能为空")
	private Long goodsId;

	@ApiModelProperty(value = "产品类型 1应收 2代采 3云信")
	private Integer productType;

	@ApiModelProperty("服务费率")
	private BigDecimal serviceRate;

	@ApiModelProperty("流程实例id")
	private String processInstanceId;

	@ApiModelProperty("任务id")
	private String taskId;

	@ApiModelProperty("城市")
	private String city;

	@ApiModelProperty("保证金比例")
	private BigDecimal bondProportion;

	@ApiModelProperty("最高融资比例")
	private BigDecimal financingProportion;
	private Integer status;

}
