/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.dto;

import lombok.Data;
import org.springblade.customer.entity.EnterpriseQuota;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 核心企业额度数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
public class EnterpriseQuotaDTO extends EnterpriseQuota {
	private static final long serialVersionUID = 1L;


	private String taskNoEqual;

	private Integer statusEqual;

	private String capitalName;

	private String enterpriseName;

	private Integer enterpriseTypeEqual;

	private Integer statusGt;




	/**
	 * 调整后授信额度
	 */
	private BigDecimal newCreditAmount;

	/**
	 * 调整后额度类型
	 */
	private Integer newQuotaType;

	/**
	 * 调整后年利率
	 */
	private BigDecimal newAnnualInterestRate;

	/**
	 * 调整后到期日
	 */
	private LocalDateTime newExpireTime;

	/**
	 * 额度调整原因
	 */
	private String reason;



}
