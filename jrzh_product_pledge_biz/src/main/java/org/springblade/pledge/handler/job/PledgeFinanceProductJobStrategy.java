package org.springblade.pledge.handler.job;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.handler.IProductFinanceJobStrategy;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.pledge.constant.PledgeEnum;
import org.springblade.pledge.entity.PledgeFinance;
import org.springblade.pledge.service.IPledgeFinanceService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-11-18  14:31
 * @Description: TODO
 * @Version: 1.0
 */
@Service("PLEDGE_FINANCE_PRODUCT_JOB_STRATEGY")
@RequiredArgsConstructor
public class PledgeFinanceProductJobStrategy implements IProductFinanceJobStrategy {
    private final IFinanceApplyService financeApplyService;
    private final IPledgeFinanceService pledgeFinanceService;

    @Override
    public void overdueFinanceDeal(FinanceApply financeApply, LocalDate repaymentTime) {
        financeApply.setStatus(PledgeEnum.FinanceStatusEnum.PURCHASE_STATUS_TEN.getCode());
        financeApply.setExpireTime(repaymentTime.atStartOfDay());
        financeApplyService.updateById(financeApply);
        pledgeFinanceService.update(Wrappers.<PledgeFinance>lambdaUpdate()
                .eq(PledgeFinance::getFinanceNo, financeApply.getFinanceNo())
                .set(BaseEntity::getStatus, PledgeEnum.FinanceStatusEnum.PURCHASE_STATUS_TEN.getCode()));
    }

    @Override
    public GoodsEnum support() {
        return GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE;
    }
}
