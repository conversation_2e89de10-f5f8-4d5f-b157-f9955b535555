/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.pledge.dto.PledgeQuotaDTO;
import org.springblade.pledge.entity.PledgeQuota;
import org.springblade.pledge.vo.CustomerGoodsPledgeVO;
import org.springblade.pledge.vo.PledgeQuotaVO;

import java.math.BigDecimal;

/**
 * 动产质押-质押额度
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface IPledgeQuotaService extends BaseService<PledgeQuota> {

    /**
     * 自定义分页
     *
     * @param page
     * @param pledgeQuota
     * @return
     */
    IPage<PledgeQuotaVO> selectPledgeQuotaPage(IPage<PledgeQuotaVO> page, PledgeQuotaVO pledgeQuota);

    /**
     * id 获取动产质押信息
     *
     * @param id
     * @return
     */
    CustomerGoodsPledgeVO getPledgeInfo(Long id);

    /**
     * 获取质押记录分页列表
     *
     * @param pledgeQuotaDTO
     * @param query
     * @return
     */
    IPage<PledgeQuotaVO> pageVo(PledgeQuotaDTO pledgeQuotaDTO, Query query);

    /**
     * 保存动产质押收款账户
     *
     * @param id
     * @param customerBankCardId
     * @return
     */
    Boolean savePledgeBank(Long id, Long customerBankCardId);

    /**
     * 扣减额度
     *
     * @param pledgeQuota
     */
    PledgeQuota deductionAmount(PledgeQuota pledgeQuota, BigDecimal subtractAmount);

    /**
     * 回滚额度
     *
     * @param pledgeQuota
     */
    PledgeQuota rollbackAmount(PledgeQuota pledgeQuota, BigDecimal rollbackAmount);

    /**
     * 获取回款账款后缀  中国银行(9875)
     *
     * @param goodsId 产品id
     * @param userId  用户id
     * @return 中国银行(9875)
     */
    String getRepaymentAccountSuffix(Long quotaId, Long goodsId, Long userId);

    /**
     * 根据id查询动产质押额度信息
     *
     * @param quotaId
     * @return
     */
    PledgeQuotaVO getByGoodsIdAndEnterpriseTypeAndEnterpriseId(Long quotaId);
}
