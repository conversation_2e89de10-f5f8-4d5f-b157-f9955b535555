/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pledge.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.ProcessTypeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.Condition;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.mapper.CustomerGoodsMapper;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.dto.FinanceApplyDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.FinanceApplyVO;
import org.springblade.finance.wrapper.FinanceApplyWrapper;
import org.springblade.modules.contract.service.IContractService;
import org.springblade.pledge.constant.PledgeEnum;
import org.springblade.pledge.dto.PledgeFinanceDTO;
import org.springblade.pledge.entity.PledgeFinance;
import org.springblade.pledge.entity.PledgeQuota;
import org.springblade.pledge.entity.PledgeRedeemCargo;
import org.springblade.pledge.mapper.PledgeCommodityMapper;
import org.springblade.pledge.mapper.PledgeFinanceMapper;
import org.springblade.pledge.mapper.PledgeQuotaMapper;
import org.springblade.pledge.service.IPledgeFinanceService;
import org.springblade.pledge.service.IPledgeGoodsService;
import org.springblade.pledge.service.IPledgePurchaseCommodityService;
import org.springblade.pledge.service.IPledgeRedeemCargoService;
import org.springblade.pledge.vo.PledgeFinanceVO;
import org.springblade.pledge.vo.PledgeRedeemCargoVO;
import org.springblade.pledge.vo.PledgeWarehouseVo;
import org.springblade.pledge.wrapper.PledgeFinanceWrapper;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.procurement.finance.entity.PurchaseCommodity;
import org.springblade.redeem.entity.RedeemCommodity;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.mapper.RedeemCommodityMapper;
import org.springblade.redeem.vo.RedeemSpec;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.warehouse.dto.WarehouseDetailsDTO;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.warehouse.mapper.WarehouseDetailsMapper;
import org.springblade.warehouse.service.IRedemptionWarehouseEnteringService;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.warehouse.service.IWarehouseService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 质押融资表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
@RequiredArgsConstructor
public class PledgeFinanceServiceImpl extends BaseServiceImpl<PledgeFinanceMapper, PledgeFinance> implements IPledgeFinanceService {

    private final IFinanceApplyService financeApplyService;
    private final IPledgeGoodsService pledgeGoodsService;
    private final IContractService contractService;
    private final IRedemptionWarehouseEnteringService redemptionWarehouseEnteringService;
    private final IWfProcessService wfProcessService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final FinanceApplyMapper financeApplyMapper;
    private final IWarehouseDetailsService warehouseDetailsService;
    private final PledgeQuotaMapper pledgeQuotaMapper;
    private final WarehouseDetailsMapper warehouseDetailsMapper;
    private final PledgeCommodityMapper pledgeCommodityMapper;
    private final IAttachService attachService;
    private final RedeemCommodityMapper redeemCommodityMapper;
    private final IWarehouseService warehouseService;
    private final IBusinessProcessService businessProcessService;
    private final CustomerGoodsMapper customerGoodsMapper;
    private final IPledgePurchaseCommodityService purchaseCommodityService;
    private final RemoteDeptSearchService remoteDeptSearchService;
    private final IExpenseOrderService expenseOrderService;
    private final IExpenseOrderDetailService expenseOrderDetailService;
    private final IExpenseDepositService expenseDepositService;
    private final IBusinessProcessProductService businessProcessProductService;


    @Override
    public IPage<PledgeFinanceVO> selectPledgeFinancePage(IPage<PledgeFinanceVO> page, PledgeFinanceVO pledgeFinance) {
        return page.setRecords(baseMapper.selectPledgeFinancePage(page, pledgeFinance));
    }

    @Override
    public IPage<FinanceApplyVO> financePage(FinanceApplyDTO financeApplyDTO, Query query) {
        financeApplyDTO.setGoodsType(GoodsEnum.GOODS_PLEDGE.getCode());
        financeApplyDTO.setUserId(MyAuthUtil.getUserId());
        IPage<FinanceApply> page = financeApplyService.page(Condition.getPage(query),
                Condition.getQueryWrapper(financeApplyDTO, FinanceApply.class).lambda()
                        .orderByDesc(FinanceApply::getUpdateTime));
        return FinanceApplyWrapper.build().pageVO(page);
    }


    @Override
    public Boolean warehouseConfirm(PledgeFinanceDTO pledgeFinanceDTO) {
//        String financeNo = pledgeFinanceDTO.getFinanceNo();
//        Assert.isTrue(StringUtil.isNotBlank(financeNo), "融资编号不能为空");
//        FinanceApply financeApply = financeApplyMapper.getByFinanceNoApply(financeNo);
//
//        //检查合同是否签署完成
//        contractService.checkContractIsSign(pledgeFinanceDTO.getContractIdList());
//        //动产质押待入库，列表完成入库
//        redemptionWarehouseEnteringService.pledgeCompleteWarehousing(pledgeFinanceDTO.getFinanceNo());
//
//        //封装审核资料
//        Map<String, Object> variables = new HashMap<>();
//        variables.put(ProcessConstant.PLEDGE_QUOTA_ID, pledgeFinanceDTO);
//
//
//        //发起流程
//        ProcessDefinition processDefinition = wfProcessService.selectProcessDefinitionByKey(LoanConstant.PLEDGE_GOODS_FINANCE);
//        String processInstanceId = wfProcessService.startProcessInstanceById(processDefinition.getId(), variables);
//
//        //String processInstanceId = businessProcessService.startProcess(financeApply.getGoodsId(),ProcessTypeEnum.PLEDGE_GOODS_FINANCE.getCode(), variables);
//        //更新流程进度
//        //businessProcessProgressService.updateBusinessProcessProgress()
//
        return true;
    }



    @Override
    public PledgeFinance getPledgeFinance(String financeNo) {
        return lambdaQuery().eq(PledgeFinance::getFinanceNo, financeNo).one();
    }
    @Override
    public IPage<PledgeRedeemCargoVO> pledgeRedemptionList(WarehouseDetailsDTO warehouseDetailsDTO, Query query) {

//        IPage<PledgeFinance> page = super.page(Condition.getPage(query), getFinanceWrapper(warehouseDetailsDTO));
//        cn.hutool.core.bean.BeanUtil.copyProperties(page, pageVO);
//        List<PledgeFinance> financeList = page.getRecords();
//        if (CollUtil.isEmpty(financeList)) {
//            return pageVO;
//        }
//        //获取库存信息
//        List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = getPledgeRedeemCargoVOList(financeList);

        return baseMapper.selectPledgeRedemptionPage(Condition.getPage(query),warehouseDetailsDTO);
    }

    /**
     * 查询指定融资编号的库存信息
     * @param financeNo
     * @return
     */
    @Override
    public List<PledgeWarehouseVo> selectPledgeWarehouseList(String financeNo){
        return baseMapper.selectPledgeWarehouseList(financeNo);
    }
    /**
     * 根据融资列表构建 动产赎货列表
     *
     * @param financeList
     * @return
     */
    public List<PledgeRedeemCargoVO> getPledgeRedeemCargoVOList(List<PledgeFinance> financeList) {
        IPledgeRedeemCargoService redeemCargoService =
                SpringUtil.getBean(IPledgeRedeemCargoService.class);
        List<String> financeNoList = financeList.stream().map(PledgeFinance::getFinanceNo).collect(Collectors.toList());
        Map<String, PledgeFinance> financeApplyMap = financeList.stream().collect(Collectors.toMap(PledgeFinance::getFinanceNo, e -> e));
        //查询库存明细
        List<WarehouseDetails> warehouseDetails = warehouseDetailsMapper.selectList(Wrappers.<WarehouseDetails>lambdaQuery().in(WarehouseDetails::getFinanceNo, financeNoList));
        Map<String, List<WarehouseDetails>> warehouseDetailMap = warehouseDetails.stream().collect(Collectors.groupingBy(WarehouseDetails::getFinanceNo));
        List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = Lists.newArrayList();

        //查询融资编号所有赎货单
        Map<String, List<RedeemCommodity>> financeCommodityMap = getFinanceCommodityMap(redeemCargoService, financeNoList);

        //查询有效赎货单
        for (String financeNo : financeNoList) {
            PledgeRedeemCargoVO vo = new PledgeRedeemCargoVO();
            PledgeFinance pledgeFinance = financeApplyMap.get(financeNo);
            //获得当前融资订单库存
            List<WarehouseDetails> warehouseDetailsList = warehouseDetailMap.get(pledgeFinance.getFinanceNo());
            if (CollUtil.isEmpty(warehouseDetailsList)) {
                break;
            }
            WarehouseDetails warehouseDetails2 = warehouseDetailsList.get(0);
            vo.setFinanceNo(pledgeFinance.getFinanceNo());
            vo.setGoodsName(warehouseDetails2.getGoodsName());
            vo.setCommodityLogo(warehouseDetails2.getLogo());
            vo.setRedemptionDate(warehouseDetails2.getRedemptionDate());
            vo.setCommodityId(warehouseDetails2.getGoodsId());
            vo.setGoodsUnit(warehouseDetails2.getGoodsUnitValue());
            Map<String, List<WarehouseDetails>> specMap = warehouseDetailsList.stream().collect(Collectors.groupingBy(WarehouseDetails::getGoodsSpec));
            List<RedeemSpec> specVOList = Lists.newArrayList();
            //赎货中商品信息
            List<RedeemCommodity> redeemCommodityList = financeCommodityMap.get(financeNo);
            for (String spec : specMap.keySet()) {
                int inUse = 0;
                if (CollUtil.isNotEmpty(redeemCommodityList)) {
                    List<RedeemCommodity> commodityList = redeemCommodityList.stream().filter(e -> spec.equals(e.getSpec())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(commodityList)) {
                        inUse = commodityList.stream().mapToInt(RedeemCommodity::getNum).sum();
                    }
                }
                //构建不同规格的商品vo
                RedeemSpec specVO = new RedeemSpec();
                List<WarehouseDetails> warehouseDetails1 = specMap.get(spec);
                WarehouseDetails warehouseSpec = warehouseDetails1.get(0);
                int sum = warehouseDetails1.stream().mapToInt(WarehouseDetails::getWarehouseNum).sum();
                //可赎货数量 = 在库总数 - 使用中数量
                specVO.setRedeemNum(Math.max(sum - inUse, 0));
                specVO.setRedeemNumIng(inUse);
                specVO.setSpec(warehouseSpec.getGoodsSpec());
                specVO.setPurchasePrice(warehouseSpec.getPurchasePrice());
                specVO.setFinancingPrice(warehouseSpec.getFinancingPrice());
                specVOList.add(specVO);
            }
            vo.setRedeemSpecList(specVOList);
            vo.setRedeemTotal(specVOList.stream().mapToInt(RedeemSpec::getRedeemNum).sum());
            pledgeRedeemCargoVOList.add(vo);
        }
        return pledgeRedeemCargoVOList.stream().filter(e -> e.getRedeemTotal()>0)
                //.sorted(Comparator.comparing(PledgeRedeemCargoVO::getRedemptionDate))
                .collect(Collectors.toList());
    }
    private Map<String, List<RedeemCommodity>> getFinanceCommodityMap(IPledgeRedeemCargoService redeemCargoService, List<String> financeNoList) {
        List<Integer> statusList = Arrays.asList(RedeemCargoStatusEnum.REDEEM_CARGO_EXAMINEING.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_CONFIRM.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_EXAMINEING.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_SEND.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_RELEASEPLEDGE.getKey());
        List<PledgeRedeemCargo> redeemList = redeemCargoService.list(Wrappers.<PledgeRedeemCargo>lambdaQuery()
                .in(PledgeRedeemCargo::getFinanceNo, financeNoList)
                .in(BaseEntity::getStatus, statusList));
        if (CollUtil.isEmpty(redeemList)) {
            return MapUtil.newHashMap();
        }
        //查询符合条件的赎货的赎货信息
        List<String> redeemNoList = redeemList.stream().map(PledgeRedeemCargo::getRedeemNo).collect(Collectors.toList());
        List<RedeemCommodity> redeemCommodityList = redeemCommodityMapper.selectList(Wrappers.<RedeemCommodity>lambdaQuery().in(RedeemCommodity::getRedeemNo, redeemNoList));
        if (CollUtil.isEmpty(redeemCommodityList)) {
            return MapUtil.newHashMap();
        }
        Map<String, List<RedeemCommodity>> financeCommodityMap = redeemCommodityList.stream().collect(Collectors.groupingBy(RedeemCommodity::getFinancingNo));
        return financeCommodityMap;
    }
//
//    @Override
//    public PledgeRedeemCargoVO fontWarehousDetailByPledge(String financeNo) {
//        PledgeFinance pledgeFinance = lambdaQuery()
//                .eq(PledgeFinance::getFinanceNo, financeNo)
//                .one();
//        List<PledgeFinance> financeApplyList = Lists.newArrayList();
//        financeApplyList.add(pledgeFinance);
//        //List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = warehouseDetailsService.getPledgeRedeemCargoVOList(financeApplyList);
//        List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = getPledgeRedeemCargoVOList(financeApplyList);
//
//        PledgeRedeemCargoVO pledgeRedeemCargoVO = pledgeRedeemCargoVOList.get(0);
//        Optional.ofNullable(pledgeFinance).ifPresent(e -> {
//            pledgeRedeemCargoVO.setExtractType(e.getPickUpManner());
//            pledgeRedeemCargoVO.setAddress(e.getReceiveAddress());
//        });
//        return pledgeRedeemCargoVO;
//    }

    @Override
    public PledgeFinanceVO pledgeOneDetail(String financeNo) {
       PledgeFinance pledgeFinance = lambdaQuery().eq(PledgeFinance::getFinanceNo, financeNo).one();
		PledgeFinanceVO pledgeFinanceVO = PledgeFinanceWrapper.build().entityVO(pledgeFinance);
		if (Objects.isNull(pledgeFinanceVO)) {
			throw new ServiceException("查询不到数据");
		}
		FinanceApply financeApply = financeApplyService.getOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeNo));
		List<PurchaseCommodity> purchaseCommodities = purchaseCommodityService.getListByQuotaId(pledgeFinance.getPledgeQuotaId());
		CustomerGoods customerGoods = customerGoodsMapper.selectById(financeApply.getCustomerGoodsId());
		AgentGoods agentGoods = pledgeGoodsService.getById(financeApply.getGoodsId());
		pledgeFinanceVO.setFinanceApply(FinanceApplyWrapper.build().entityVO(financeApply));
		pledgeFinanceVO.setPurchaseCommodityList(purchaseCommodities);
		pledgeFinanceVO.setBondReleaseMode(agentGoods.getBondReleaseMode());
		if (Objects.nonNull(customerGoods)) {
			pledgeFinanceVO.setCapitalName(customerGoods.getCapitalName());
			pledgeFinanceVO.setCapitalAvatar(customerGoods.getCapitalAvatar());
		}
		return pledgeFinanceVO;
    }

    private LambdaQueryWrapper<PledgeFinance> getFinanceWrapper(WarehouseDetailsDTO warehouseDetailsDTO) {
        List<Integer> statusList = Arrays.asList(PledgeEnum.FinanceStatusEnum.PURCHASE_STATUS_SEVEN.getCode(),
                PledgeEnum.FinanceStatusEnum.PURCHASE_STATUS_TEN.getCode());
        LambdaQueryWrapper<PledgeFinance> lqw = new LambdaQueryWrapper<>();
        lqw.in(BaseEntity::getStatus, statusList);
        lqw.orderByDesc(BaseEntity::getCreateTime);
        lqw.eq(PledgeFinance::getUserId, MyAuthUtil.getUserId());
        lqw.eq(StringUtil.isNotBlank(warehouseDetailsDTO.getFinanceNo()), PledgeFinance::getFinanceNo, warehouseDetailsDTO.getFinanceNo());
        lqw.eq(StringUtil.isNotBlank(warehouseDetailsDTO.getGoodsName()), PledgeFinance::getCommodityName, warehouseDetailsDTO.getGoodsName());
        return lqw;
    }

//    /**
//     * 根据融资列表构建 动产赎货列表
//     *
//     * @param financeList
//     * @return
//     */
//    public List<PledgeRedeemCargoVO> getPledgeRedeemCargoVOList(List<PledgeFinance> financeList) {
//        IPledgeRedeemCargoService redeemCargoService = SpringUtil.getBean(IPledgeRedeemCargoService.class);
//        List<String> financeNoList = financeList.stream().map(PledgeFinance::getFinanceNo).collect(Collectors.toList());
//        Map<String, PledgeFinance> financeApplyMap = financeList.stream().collect(Collectors.toMap(PledgeFinance::getFinanceNo, e -> e));
//        //查询库存明细
//        List<WarehouseDetails> warehouseDetails = warehouseDetailsMapper.selectList(Wrappers.<WarehouseDetails>lambdaQuery().in(WarehouseDetails::getFinanceNo, financeNoList));
//        Map<String, List<WarehouseDetails>> warehouseDetailMap = warehouseDetails.stream().collect(Collectors.groupingBy(WarehouseDetails::getFinanceNo));
//        List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = Lists.newArrayList();
//
//        //查询融资编号所有赎货单
//        Map<String, List<RedeemCommodity>> financeCommodityMap = getFinanceCommodityMap(redeemCargoService, financeNoList);
//
//        //查询有效赎货单
//        for (String financeNo : financeNoList) {
//            PledgeRedeemCargoVO vo = new PledgeRedeemCargoVO();
//            PledgeFinance pledgeFinance = financeApplyMap.get(financeNo);
//            //获得当前融资订单库存
//            List<WarehouseDetails> warehouseDetailsList = warehouseDetailMap.get(pledgeFinance.getFinanceNo());
//            if (CollUtil.isEmpty(warehouseDetailsList)) {
//                break;
//            }
//            WarehouseDetails warehouseDetails2 = warehouseDetailsList.get(0);
//            vo.setFinanceNo(pledgeFinance.getFinanceNo());
//            vo.setGoodsName(warehouseDetails2.getGoodsName());
//            vo.setCommodityLogo(warehouseDetails2.getLogo());
//            vo.setRedemptionDate(warehouseDetails2.getRedemptionDate());
//            vo.setCommodityId(warehouseDetails2.getGoodsId());
//            vo.setGoodsUnit(warehouseDetails2.getGoodsUnitValue());
//            Map<String, List<WarehouseDetails>> specMap = warehouseDetailsList.stream().collect(Collectors.groupingBy(WarehouseDetails::getGoodsSpec));
//            List<RedeemSpec> specVOList = Lists.newArrayList();
//            //赎货中商品信息
//            List<RedeemCommodity> redeemCommodityList = financeCommodityMap.get(financeNo);
//            for (String spec : specMap.keySet()) {
//                int inUse = 0;
//                if (CollUtil.isNotEmpty(redeemCommodityList)) {
//                    List<RedeemCommodity> commodityList = redeemCommodityList.stream().filter(e -> spec.equals(e.getSpec())).collect(Collectors.toList());
//                    if (CollUtil.isNotEmpty(commodityList)) {
//                        inUse = commodityList.stream().mapToInt(RedeemCommodity::getNum).sum();
//                    }
//                }
//                //构建不同规格的商品vo
//                RedeemSpec specVO = new RedeemSpec();
//                List<WarehouseDetails> warehouseDetails1 = specMap.get(spec);
//                WarehouseDetails warehouseSpec = warehouseDetails1.get(0);
//                int sum = warehouseDetails1.stream().mapToInt(WarehouseDetails::getWarehouseNum).sum();
//                //可赎货数量 = 在库总数 - 使用中数量
//                specVO.setRedeemNum(Math.max(sum - inUse, 0));
//                specVO.setRedeemNumIng(inUse);
//                specVO.setSpec(warehouseSpec.getGoodsSpec());
//                specVO.setPurchasePrice(warehouseSpec.getPurchasePrice());
//                specVO.setFinancingPrice(warehouseSpec.getFinancingPrice());
//                specVOList.add(specVO);
//            }
//            vo.setRedeemSpecList(specVOList);
//            vo.setRedeemTotal(specVOList.stream().mapToInt(RedeemSpec::getRedeemNum).sum());
//            pledgeRedeemCargoVOList.add(vo);
//        }
//        return pledgeRedeemCargoVOList.stream()
//                .sorted(Comparator.comparing(PledgeRedeemCargoVO::getRedemptionDate))
//                .collect(Collectors.toList());
//    }

    //    private Map<String, List<RedeemCommodity>> getFinanceCommodityMap(IPledgeRedeemCargoService redeemCargoService, List<String> financeNoList) {
//        List<Integer> statusList = Arrays.asList(RedeemCargoStatusEnum.REDEEM_CARGO_EXAMINEING.getKey(),
//                RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_CONFIRM.getKey(),
//                RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_EXAMINEING.getKey(),
//                RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_SEND.getKey());
//        List<PledgeRedeemCargo> redeemList = redeemCargoService.list(Wrappers.<PledgeRedeemCargo>lambdaQuery()
//                .in(PledgeRedeemCargo::getFinanceNo, financeNoList)
//                .in(BaseEntity::getStatus, statusList));
//        if (CollUtil.isEmpty(redeemList)) {
//            return MapUtil.newHashMap();
//        }
//        //查询符合条件的赎货的赎货信息
//        List<String> redeemNoList = redeemList.stream().map(PledgeRedeemCargo::getRedeemNo).collect(Collectors.toList());
//        List<RedeemCommodity> redeemCommodityList = redeemCommodityMapper.selectList(Wrappers.<RedeemCommodity>lambdaQuery().in(RedeemCommodity::getRedeemNo, redeemNoList));
//        if (CollUtil.isEmpty(redeemCommodityList)) {
//            return MapUtil.newHashMap();
//        }
//        Map<String, List<RedeemCommodity>> financeCommodityMap = redeemCommodityList.stream().collect(Collectors.groupingBy(RedeemCommodity::getFinancingNo));
//        return financeCommodityMap;
//    }
//
    private Map<String, Object> getStringObjectMap(PledgeFinanceDTO pledgeFinanceDTO, PledgeFinance pledgeFinance, FinanceApply financeApply, PledgeQuota pledgeQuota, List<PurchaseCommodity> purchaseCommodityList, List<WarehouseDetails> warehouseDetails, List<ExpenseOrderDetail> platformExpensesList, ExpenseDeposit cashDeposit) {
        AgentGoods agentGoods = pledgeGoodsService.getById(financeApply.getGoodsId());
        //查询费用附件详情
        String bondAttachStr = pledgeFinanceDTO.getBondAttachList();
        String costAttachStr = pledgeFinanceDTO.getCostAttachList();
        List<Long> BondAttachIds = Func.toLongList(bondAttachStr);
        List<Attach> bondAttachList = attachService.getAttachListByIds(BondAttachIds);
        List<Long> costAttachIds = Func.toLongList(costAttachStr);
        List<Attach> costAttachList = attachService.getAttachListByIds(costAttachIds);
        pledgeFinanceDTO.setBondAttaches(bondAttachList);
        pledgeFinanceDTO.setCostAttaches(costAttachList);

        Map<String, Object> variables = new HashMap<>();
        variables.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        variables.put(ProcessConstant.FINANCE_APPLY_ID, pledgeFinance.getFinanceId());
        variables.put(ProcessConstant.FINANCE_NO, pledgeFinance.getFinanceNo());
        variables.put(ProcessConstant.CUSTOMER_GOODS_ID, financeApply.getCustomerGoodsId());
        variables.put(ProcessConstant.BUSINESS_ID, financeApply.getGoodsId());
        variables.put(ProcessConstant.CUSTOMER_ID, MyAuthUtil.getCustomerId());
        variables.put(ProcessConstant.USER_ID, MyAuthUtil.getUserId());
        variables.put(ProcessConstant.PROCESS_TYPE, ProcessTypeEnum.FINANCE_APPLY.getCode());
        variables.put(ProcessConstant.CHARGE_METHOD, financeApply.getChargeMethod());
        variables.put(ProcessConstant.FINANCE_APPLY, financeApply);
        variables.put(ProcessConstant.PROCESS_GOODS_INFO, agentGoods);


        variables.put(ProcessConstant.PLEDGE_FINANCE, pledgeFinanceDTO);
        variables.put(ProcessConstant.PLEDGE_QUOTA, pledgeQuota);
        variables.put(ProcessConstant.PLEDGE_COMMODITY_INFO, purchaseCommodityList);
        variables.put(ProcessConstant.WAREHOUSE_DETAILS, warehouseDetails);
        variables.put(ProcessConstant.PLATFORM_EXPENSES, platformExpensesList);
        variables.put(ProcessConstant.CASH_DEPOSIT, cashDeposit);
        return variables;
    }



}
