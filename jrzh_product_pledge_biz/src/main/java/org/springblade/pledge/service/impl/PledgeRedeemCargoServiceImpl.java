/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.entity.FinancingAddress;
import org.springblade.customer.service.IFinancingAddressService;
import org.springblade.customer.vo.FinancingAddressVO;
import org.springblade.customer.wrapper.FinancingAddressWrapper;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.entity.ExpenseDepositBill;
import org.springblade.deposit.service.IExpenseDepositBillService;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseInfoBizService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.expense.vo.ExpenseInfoVO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.finance.vo.ExpenseOrderDetailFinanceVo;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.enums.IouEnum;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springblade.loan.vo.LoanManageRepaymentVO;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.pledge.dto.PledgeRedeemCargoDTO;
import org.springblade.pledge.entity.PledgeFinance;
import org.springblade.pledge.entity.PledgeRedeemCargo;
import org.springblade.pledge.mapper.PledgeRedeemCargoMapper;
import org.springblade.pledge.service.IPledgeFinanceApplyService;
import org.springblade.pledge.service.IPledgeFinanceService;
import org.springblade.pledge.service.IPledgeRedeemCargoService;
import org.springblade.pledge.vo.PledgeRedeemCargoVO;
import org.springblade.pledge.vo.PledgeRedeemDetailBackVO;
import org.springblade.pledge.vo.PledgeRedeemDetailCargoCurrencyVO;
import org.springblade.pledge.vo.PledgeRedeemDetailFrontVO;
import org.springblade.pledge.wrapper.PledgeRedeemCargoWrapper;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.product.common.constant.AccountTypeEnum;
import org.springblade.product.common.entity.BillBankCarda;
import org.springblade.product.common.entity.ExpenseType;
import org.springblade.product.common.entity.MergePay;
import org.springblade.product.common.entity.Product;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense.constant.PayModeEnum;
import org.springblade.product.expense.service.IExpenseTypeService;
import org.springblade.product.moudle.billbankcard.service.IBillBankCardaService;
import org.springblade.product.moudle.billbankcard.wrapper.BillBankCardaWrapper;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.product.moudle.timing.service.IGoodsTimingService;
import org.springblade.redeem.constant.RedeemConstant;
import org.springblade.redeem.dto.*;
import org.springblade.redeem.entity.*;
import org.springblade.redeem.enums.ExtractTypeEnum;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.enums.RedeemTypeEnum;
import org.springblade.redeem.mapper.RedeemCommodityMapper;
import org.springblade.redeem.service.*;
import org.springblade.redeem.vo.*;
import org.springblade.redeem.wrapper.RedeemCommodityWrapper;
import org.springblade.redeem.wrapper.RedeemObjectionWrapper;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.service.IRefundBizService;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteRegionService;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.system.utils.UserUtils;
import org.springblade.warehouse.dto.WarehouseDetailsDTO;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.warehouse.mapper.WarehouseDetailsMapper;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

;

/**
 * 动产赎货表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
@RequiredArgsConstructor
public class PledgeRedeemCargoServiceImpl extends BaseServiceImpl<PledgeRedeemCargoMapper, PledgeRedeemCargo> implements IPledgeRedeemCargoService {

    private final IRedeemCommodityService redeemCommodityService;
    private final IRedeemUserService redeemUserService;
    private final IFinanceApplyService financeApplyService;
    private final IRedeemExpenseService redeemExpenseService;
    private final IRedeemCargoService redeemCargoService;
    private final PledgeRedeemCargoMapper pledgeRedeemCargoMapper;
    private final RemoteUserService userService;
    private final IAttachService attachService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final IRedeemObjectionService redeemObjectionService;
    private final IWarehouseDetailsService warehouseDetailsService;
    private final IExpenseTypeService expenseTypeService;
    private final IBillBankCardaService billBankCardaService;
    private final IFinancingAddressService financingAddressService;
    private final RemoteRegionService regionService;
    private final IRedeemSendService redeemSendService;
    private final IPledgeFinanceService pledgeFinanceService;
    private final IGoodsTimingService goodsTimingService;
    private final IExpenseDepositService cashDepositService;

    private final LoanManageIouMapper loanManageIouMapper;
    private final IBusinessProcessProductService businessProcessProductService;
    private final IExpenseOrderService expenseOrderService;
    private final IExpenseDepositBillService expenseDepositBillService;
    private final ProductDirector productDirector;
    private final IPledgeFinanceApplyService pledgeFinanceApplyService;
    private final WarehouseDetailsMapper warehouseDetailsMapper;
    private final RedeemCommodityMapper redeemCommodityMapper;
    private final IProductExpenseService productExpenseService;
    private final IExpenseInfoBizService expenseInfoBizService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;


    @Override
    public IPage<PledgeRedeemCargoVO> selectPledgeRedeemCargoPage(IPage<PledgeRedeemCargoVO> page, PledgeRedeemCargoVO pledgeRedeemCargo) {
        return page.setRecords(baseMapper.selectPledgeRedeemCargoPage(page, pledgeRedeemCargo));
    }


    @Override
    public PledgeRedeemDetailBackVO backDetail(String redeemNo) {
        Assert.isTrue(StrUtil.isNotBlank(redeemNo), "赎货单号不能为空");
        PledgeRedeemDetailBackVO redeemDetailVo = new PledgeRedeemDetailBackVO();
        PledgeRedeemCargo redeemCargo = lambdaQuery().eq(PledgeRedeemCargo::getRedeemNo, redeemNo).one();
        redeemDetailVo.setYearRate(financeApplyService.getByFinanceNo(redeemCargo.getFinanceNo()).getAnnualInterestRate());
        redeemDetailVo.setPledgeRedeemDetailCargoCurrencyVO(redeemDetailCargoCurrency(redeemCargo));
        redeemDetailVo.setCreateTime(redeemCargo.getCreateTime());
        User user = userService.getUserById(redeemCargo.getApplyUser(), FeignConstants.FROM_IN).getData();
        redeemDetailVo.setCreateUser(user != null ? user.getName() : "");

        if (redeemCargo.getExpenseOrderId() != null) {
            ExpenseOrder expenseOrder = expenseOrderService.getById(redeemCargo.getExpenseOrderId());
            redeemDetailVo.setBillPayStatus(expenseOrder.getPaymentStatus());
        }
        if (redeemCargo.getRepaymentRecordId() != null) {
            LoanManageRepayment repayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
            redeemDetailVo.setRepaymentStatus(repayment.getStatus());
        }
        //处理记录
        if (RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey().equals(redeemCargo.getStatus())) {
            List<RedeemObjection> redeemObjectionList = redeemObjectionService.lambdaQuery().eq(RedeemObjection::getRedeemNo, redeemNo).list();
            if (!redeemObjectionList.isEmpty()) {
                List<Long> userList = redeemObjectionList.stream().map(RedeemObjection::getUpdateUser).collect(Collectors.toList());
                Map<Long, User> userMap = userService.listByUser(userList, FeignConstants.FROM_IN).getData().stream().collect(Collectors.toMap(User::getId, e -> e));
                List<RedeemObjectionVO> redeemObjectionVOS = RedeemObjectionWrapper.build().listVO(redeemObjectionList);
                redeemObjectionVOS.forEach(e -> e.setUserName(userMap.get(e.getUpdateUser()).getName()));
                redeemDetailVo.setRedeemObjectionVoList(redeemObjectionVOS);
            }

        }
        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
        LoanManageRepaymentVO loanManageRepaymentVO = LoanManageRepaymentWrapper.build().entityVO(loanManageRepayment);
        if (loanManageRepaymentVO != null && StrUtil.isNotBlank(loanManageRepaymentVO.getVoucher())) {
            loanManageRepaymentVO.setAttachList(attachService.listByIds(Func.toLongList(loanManageRepaymentVO.getVoucher())));
        }
        redeemDetailVo.setLoanManageRepayment(loanManageRepaymentVO);
        return redeemDetailVo;
    }

    @Override
    public PledgeRedeemDetailFrontVO forntDetail(String redeemNo) {

        PledgeRedeemDetailFrontVO redeemDetailFrontVO = new PledgeRedeemDetailFrontVO();
        //基础信息
        PledgeRedeemCargo pledgeRedeemCargo = baseMapper.selectOne(Wrappers.<PledgeRedeemCargo>lambdaQuery().eq(PledgeRedeemCargo::getRedeemNo, redeemNo));
        Assert.isTrue(Objects.nonNull(pledgeRedeemCargo), "赎货单不存在");
        redeemDetailFrontVO.setPledgeRedeemDetailCargoCurrencyVO(redeemDetailCargoCurrency(pledgeRedeemCargo));

        redeemDetailFrontVO.setCreateTime(pledgeRedeemCargo.getCreateTime());
        User user = userService.getUserById(pledgeRedeemCargo.getApplyUser(), FeignConstants.FROM_IN).getData();
        redeemDetailFrontVO.setCreateUser(user != null ? user.getName() : "");
        redeemDetailFrontVO.setCountdownExpireTime(pledgeRedeemCargo.getCountdownExpireTime());
        //待发货等后续状态展示实付金额
        if (RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_SUSPEND.getKey() < pledgeRedeemCargo.getStatus()) {
            ExpenseOrder billExpenseOrder = expenseOrderService.getById(pledgeRedeemCargo.getExpenseOrderId());
            LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(pledgeRedeemCargo.getRepaymentRecordId());
            redeemDetailFrontVO.
                    setActualAmount(NumberUtil.add(billExpenseOrder != null ? billExpenseOrder.getAmount() : BigDecimal.ZERO,
                            loanManageRepayment != null ? loanManageRepayment.getActualAmount() : BigDecimal.ZERO));
        }
        //已完成显示签收人
        if (RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey().equals(pledgeRedeemCargo.getStatus())) {
            redeemDetailFrontVO.setReceivedPeople(pledgeRedeemCargo.getReceivedPeople());
            redeemDetailFrontVO.setReceivedTime(pledgeRedeemCargo.getReceivedTime());
        }
        return redeemDetailFrontVO;
    }

    @Override
    public IPage<PledgeRedeemCargoVO> frontPage(PledgeRedeemCargoDTO pledgeRedeemCargo, Query query) {
        IPage<PledgeRedeemCargo> pages =
                baseMapper.selectPage(Condition.getPage(query), org.springblade.common.utils.Condition.getQueryWrapper(pledgeRedeemCargo, PledgeRedeemCargo.class).orderByDesc("update_time"));
        IPage<PledgeRedeemCargoVO> pledgeRedeemCargoVOPage = PledgeRedeemCargoWrapper.build().pageVO(pages);
        List<PledgeRedeemCargoVO> records = pledgeRedeemCargoVOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return pledgeRedeemCargoVOPage;
        }
        //封装赎货商品信息
        List<String> redeemNoList = records.stream().map(PledgeRedeemCargo::getRedeemNo).collect(Collectors.toList());
        List<RedeemCommodity> redeemCommodityList = redeemCommodityService.getCommodityListByRedeemList(redeemNoList);
        Map<String, List<RedeemCommodity>> redeemCommodityMap = redeemCommodityList.stream().collect(Collectors.groupingBy(RedeemCommodity::getRedeemNo));

        List<String> financeNoiList = records.stream().map(PledgeRedeemCargo::getFinanceNo).distinct().collect(Collectors.toList());
        List<WarehouseDetails> warehouseDetailsList = warehouseDetailsService.list(Wrappers.<WarehouseDetails>lambdaQuery().in(WarehouseDetails::getFinanceNo, financeNoiList));
        Map<String, List<WarehouseDetails>> warehouseDetailsMap = warehouseDetailsList.stream().collect(Collectors.groupingBy(WarehouseDetails::getFinanceNo));
        for (PledgeRedeemCargoVO record : records) {
            List<RedeemCommodity> redeemCommodity = redeemCommodityMap.get(record.getRedeemNo());
            List<WarehouseDetails> warehouseDetails = warehouseDetailsMap.get(record.getFinanceNo());
            BigDecimal financeAmount = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(redeemCommodity)) {
                for (RedeemCommodity commodity : redeemCommodity) {
                    BigDecimal multiply = commodity.getFinancingPrice().multiply(new BigDecimal(commodity.getNum()));
                    financeAmount = financeAmount.add(multiply);
                }
            }
            record.setFinanceAmount(financeAmount);
            record.setRedeemCommodityList(redeemCommodity);
            if (CollUtil.isNotEmpty(warehouseDetails)) {
                record.setCommodityLogo(warehouseDetails.get(0).getLogo());
            }
        }
        return pledgeRedeemCargoVOPage;
    }

    @Override
    public IPage<PledgeRedeemCargoVO> backPage(PledgeRedeemCargoDTO pledgeRedeemCargo, Query query) {
        IPage<PledgeRedeemCargo> pages =
                baseMapper.selectPage(Condition.getPage(query), org.springblade.common.utils.Condition.getQueryWrapper(pledgeRedeemCargo, PledgeRedeemCargo.class).orderByDesc("update_time"));
        IPage<PledgeRedeemCargoVO> pledgeRedeemCargoVOPage = PledgeRedeemCargoWrapper.build().pageVO(pages);
        List<PledgeRedeemCargoVO> records = pledgeRedeemCargoVOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return pledgeRedeemCargoVOPage;
        }
        //封装平台端赎货信息
        List<String> redeemNoList = records.stream().map(PledgeRedeemCargo::getRedeemNo).collect(Collectors.toList());
        List<RedeemCommodity> redeemCommodityList = redeemCommodityService.getCommodityListByRedeemList(redeemNoList);
        Map<String, List<RedeemCommodity>> redeemCommodityMap = redeemCommodityList.stream().collect(Collectors.groupingBy(RedeemCommodity::getRedeemNo));

        List<Long> userIdList = records.stream().map(BaseEntity::getCreateUser).collect(Collectors.toList());
        Map<Long, User> mapInId = userService.getMapInId(userIdList).getData();
        //赎货商品信息
        List<String> financeNoiList = records.stream().map(PledgeRedeemCargo::getFinanceNo).distinct().collect(Collectors.toList());
        List<WarehouseDetails> warehouseDetailsList = warehouseDetailsService.list(Wrappers.<WarehouseDetails>lambdaQuery().in(WarehouseDetails::getFinanceNo, financeNoiList));
        Map<String, List<WarehouseDetails>> warehouseDetailsMap = warehouseDetailsList.stream().collect(Collectors.groupingBy(WarehouseDetails::getFinanceNo));
        for (PledgeRedeemCargoVO record : records) {
            User user = mapInId.get(record.getCreateUser());
            record.setCreateUserStr(ObjectUtil.isNotEmpty(user) ? user.getName() : "");
            List<RedeemCommodity> redeemCommodity = redeemCommodityMap.get(record.getRedeemNo());
            BigDecimal financeTotal = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(redeemCommodity)) {
                for (RedeemCommodity commodity : redeemCommodity) {
                    BigDecimal multiply = commodity.getFinancingPrice().multiply(new BigDecimal(commodity.getNum()));
                    financeTotal = multiply.add(financeTotal);
                }
            }
            List<WarehouseDetails> warehouseDetails = warehouseDetailsMap.get(record.getFinanceNo());
            record.setRedeemCommodityList(redeemCommodity);
            record.setCommodityLogo(CollUtil.isNotEmpty(warehouseDetails) ? warehouseDetails.get(0).getLogo() : "");
            record.setGoodsUnit(CollUtil.isNotEmpty(redeemCommodity) ? redeemCommodity.get(0).getUnit() : "");
            record.setFinanceAmount(financeTotal);
        }
        return pledgeRedeemCargoVOPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendSaveByPledge(RedeemSendDTO redeemSendDTO) {
        PledgeRedeemCargo pledgeRedeemCargo = lambdaQuery().eq(PledgeRedeemCargo::getRedeemNo, redeemSendDTO.getRedeemNo()).one();
        pledgeRedeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_SEND.getKey());
        List<WarehouseDetailsDTO> warehouseDetailsList = redeemSendDTO.getWarehouseDetailsList();
        Assert.isTrue(CollUtil.isNotEmpty(warehouseDetailsList), "货物出库信息不能为空");
        //发货总数要等于赎货总数
        int sendSum = warehouseDetailsList.stream().mapToInt(WarehouseDetailsDTO::getSendNum).sum();
        Assert.isTrue(pledgeRedeemCargo.getNum().equals(sendSum), "发货数量需要与赎货数不相等");
        //对库存做增减  并且记录出入库
        warehouseDetailsService.subtractRedemptionList(warehouseDetailsList);
        //判断是否需要修改动产质押融资订单状态
        if (isChangeStatus(pledgeRedeemCargo.getFinanceNo(), pledgeRedeemCargo.getNum())) {
            PledgeFinance pledgeFinance = pledgeFinanceService.getPledgeFinance(pledgeRedeemCargo.getFinanceNo());
            pledgeFinance.setStatus(PurchaseEnum.PURCHASE_STATUS_NINE.getCode());
            pledgeFinanceService.updateById(pledgeFinance);
        }
        return redeemSendService.save(redeemSendDTO) && updateById(pledgeRedeemCargo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(ChangStatusDTO changStatusDTO) {
        PledgeRedeemCargo pledgeRedeemCargo = getById(changStatusDTO.getId());
        FinanceApply financeApply = financeApplyService.getByFinanceNo(pledgeRedeemCargo.getFinanceNo());
        User user = userService.getUserById(financeApply.getUserId(), FeignConstants.FROM_IN).getData();
        if (RedeemTypeEnum.REDEEM_CARGO_DISSENT.getCode().equals(changStatusDTO.getStatus())) {
            //有异议

        } else if (RedeemTypeEnum.CONFIRM.getCode().equals(changStatusDTO.getStatus())) {
            //确认收货
            if (RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey().equals(pledgeRedeemCargo.getStatus())
                    || RedeemCargoStatusEnum.REDEEM_CARGO_DISSENT.getKey().equals(pledgeRedeemCargo.getStatus())) {
                pledgeRedeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey());
                pledgeRedeemCargo.setReceivedTime(LocalDateTime.now());
                pledgeRedeemCargo.setReceivedPeople(Objects.nonNull(user) ? user.getName() : "");
                //保证金释放
                refundOfDepositByPledge(pledgeRedeemCargo.getFinanceNo(), pledgeRedeemCargo.getRedeemNo());
                changOrderStatus(pledgeRedeemCargo);
                //Integer i = 1 / 0;
            }
        }
        return updateById(pledgeRedeemCargo);
    }

    /***
     * 保证金退款申请 动产质押
     * @param financingNo 融资编号
     * @param redeemNo 赎货单号
     * @return
     */
    private Boolean refundOfDepositByPledge(String financingNo, String redeemNo) {
        ExpenseDeposit cashDeposit = cashDepositService.getOne(Wrappers.<ExpenseDeposit>lambdaQuery().eq(ExpenseDeposit::getFinancingNo, financingNo));
        List<ExpenseDepositBill> list = expenseDepositBillService.lambdaQuery().eq(ExpenseDepositBill::getDepositId, cashDeposit.getId()).list();
        FinanceApply financeApply = financeApplyService.getOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financingNo));
        Product goodsVO = productDirector.detailBase(financeApply.getGoodsId());

        BigDecimal sum = new BigDecimal("0");
        for (int i = 0; i < list.size(); i++) {
            ExpenseDepositBill cashDepositBill = list.get(i);
            if (Integer.valueOf("1").equals(cashDepositBill.getPayRefundType())) {
                sum = sum.add(cashDepositBill.getAmount());
            }
        }
        LoanManageIou loanManageIou = SpringUtil.getBean(ILoanManageIouService.class).getByFinancingNo(financingNo);

        //根据赎货单号查询到还款记录的金额
        PledgeRedeemCargo pledgeRedeemCargo = pledgeRedeemCargoMapper.selectOne(Wrappers.<PledgeRedeemCargo>lambdaQuery().eq(PledgeRedeemCargo::getRedeemNo, redeemNo));
        LoanManageRepayment manageRepayment = loanManageRepaymentService.getById(pledgeRedeemCargo.getRepaymentRecordId());
        BigDecimal repaymentTotal = loanManageRepaymentService.list(Wrappers.<LoanManageRepayment>lambdaQuery().eq(LoanManageRepayment::getStatus, 3)
                        .eq(LoanManageRepayment::getIouNo, loanManageIou.getIouNo())).stream().map(LoanManageRepayment::getPrincipal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //如果是信贷结清后退还查询还款记录是否全部还完
        if (goodsVO.getBondReleaseMode() == 1) {
            //应还总额
            CostCalculusVO costCalculusVO = pledgeFinanceApplyService.repaymentFinanceApplyCalculationByFinanceNo(financingNo);
            List<StagRecordVO> stagRecords = costCalculusVO.getShowRepaymentPlan().getStagRecords();
            BigDecimal reduce = stagRecords.stream().map(StagRecordVO::getMonthlyPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (reduce.compareTo(repaymentTotal) == 0) {
//                if (true) {
//                    throw new ServiceException("1");
//                }
                //获取退还金额
                Refund refund = new Refund();
                //保证金退款
                refund.setFinanceNo(financingNo);
                refund.setRefundType(2);
                refund.setBillExpenseNo(cashDeposit.getCashDepositNo());
                refund.setUserId(cashDeposit.getFinancingUserId());
                refund.setRefundAmount(sum);
                refund.setPaymentMethod(cashDeposit.getPayType().toString());
                refund.setCashDepositRate(cashDeposit.getCashDepositRate());
                refund.setBondRefundType(2);
                SpringUtil.getBean(IRefundBizService.class).refundSave(refund);
                return Boolean.TRUE;
            }

            return Boolean.TRUE;
            //如果是同还款比例等比还款，客户部分还款，保证金需要按照客户的还款比例进行退还
        } else if (goodsVO.getBondReleaseMode() == 2) {
            BigDecimal ratioAmount = manageRepayment.getPrincipal().multiply(cashDeposit.getCashDepositRate()).divide(new BigDecimal("100"));
            //获取退还金额
            Refund refund = new Refund();
            //保证金退款
            refund.setRefundType(2);
            refund.setBillExpenseNo(cashDeposit.getCashDepositNo());
            refund.setFinanceNo(financingNo);
            refund.setUserId(cashDeposit.getFinancingUserId());
            refund.setRefundAmount(ratioAmount);
            refund.setPaymentMethod(cashDeposit.getPayType().toString());
            refund.setCashDepositRate(cashDeposit.getCashDepositRate());
            refund.setBondRefundType(1);
            SpringUtil.getBean(IRefundBizService.class).refundSave(refund);

            return Boolean.TRUE;
        }
        return false;
    }

    private void changOrderStatus(PledgeRedeemCargo pledgeRedeemCargo) {
        FinanceApply financeApply = financeApplyService.getByFinanceNo(pledgeRedeemCargo.getFinanceNo());
        PledgeFinance pledgeFinance = pledgeFinanceService.getPledgeFinance(pledgeRedeemCargo.getFinanceNo());
        //到期时间
        LocalDate expireDate = financeApplyService.getByFinanceNo(pledgeRedeemCargo.getFinanceNo()).getExpireTime().toLocalDate();
        //赎货时间
        LocalDate updateDate = pledgeRedeemCargo.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if (expireDate.equals(updateDate)) {
            pledgeFinanceChangeStatus(pledgeFinance, financeApply, PurchaseEnum.PURCHASE_STATUS_NINE.getCode(), IouStatusEnum.NORMAL_SETTLE.getCode());
        } else if (updateDate.isAfter(expireDate)) {
            pledgeFinanceChangeStatus(pledgeFinance, financeApply, PurchaseEnum.PURCHASE_STATUS_ELEVEN.getCode(), IouEnum.OVERDUE_FINISH.getCode());
        } else {
            pledgeFinanceChangeStatus(pledgeFinance, financeApply, PurchaseEnum.PURCHASE_STATUS_EIGHT.getCode(), IouEnum.BEFORE_FINISH.getCode());
        }
    }


    /**
     * 动产质押是否已经完成所有赎货
     *
     * @param financeNo
     * @return
     */
    private Boolean isChangeStatus(String financeNo, Integer outNum) {
        PledgeRedeemCargoVO pledgeRedeemCargoVO = fontWarehousDetailByPledge(financeNo);
        List<RedeemSpec> redeemSpecList = pledgeRedeemCargoVO.getRedeemSpecList();
        Integer redeemNum = redeemSpecList.stream().mapToInt(RedeemSpec::getRedeemNum).sum();
        //赎货中数量 - 本次赎货数量
        Integer redeemNumIng = redeemSpecList.stream().mapToInt(RedeemSpec::getRedeemNumIng).sum() - outNum;
        Boolean isChange = true;
        //赎货中和待赎货数量均为0
        if (!redeemNum.equals(0) || !redeemNumIng.equals(0)) {
            isChange = false;
        }
        return isChange;
    }

    public PledgeRedeemCargoVO fontWarehousDetailByPledge(String financeNo) {
        PledgeFinance pledgeFinance = pledgeFinanceService.getOne(Wrappers.<PledgeFinance>lambdaQuery()
                .eq(PledgeFinance::getFinanceNo, financeNo));
        List<PledgeFinance> financeApplyList = Lists.newArrayList();
        financeApplyList.add(pledgeFinance);
        //List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = warehouseDetailsService.getPledgeRedeemCargoVOList(financeApplyList);
        List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = getPledgeRedeemCargoVOList(financeApplyList);

        PledgeRedeemCargoVO pledgeRedeemCargoVO = pledgeRedeemCargoVOList.get(0);
        Optional.ofNullable(pledgeFinance).ifPresent(e -> {
            pledgeRedeemCargoVO.setExtractType(e.getPickUpManner());
            pledgeRedeemCargoVO.setAddress(e.getReceiveAddress());
        });
        return pledgeRedeemCargoVO;
    }

    /**
     * 根据融资列表构建 动产赎货列表
     *
     * @param financeList
     * @return
     */
    public List<PledgeRedeemCargoVO> getPledgeRedeemCargoVOList(List<PledgeFinance> financeList) {
        IPledgeRedeemCargoService redeemCargoService = SpringUtil.getBean(IPledgeRedeemCargoService.class);
        List<String> financeNoList = financeList.stream().map(PledgeFinance::getFinanceNo).collect(Collectors.toList());
        Map<String, PledgeFinance> financeApplyMap = financeList.stream().collect(Collectors.toMap(PledgeFinance::getFinanceNo, e -> e));
        //查询库存明细
        List<WarehouseDetails> warehouseDetails = warehouseDetailsMapper.selectList(Wrappers.<WarehouseDetails>lambdaQuery().in(WarehouseDetails::getFinanceNo, financeNoList));
        Map<String, List<WarehouseDetails>> warehouseDetailMap = warehouseDetails.stream().collect(Collectors.groupingBy(WarehouseDetails::getFinanceNo));
        List<PledgeRedeemCargoVO> pledgeRedeemCargoVOList = Lists.newArrayList();

        //查询融资编号所有赎货单
        Map<String, List<RedeemCommodity>> financeCommodityMap = getFinanceCommodityMap(redeemCargoService, financeNoList);

        //查询有效赎货单
        for (String financeNo : financeNoList) {
            PledgeRedeemCargoVO vo = new PledgeRedeemCargoVO();
            PledgeFinance pledgeFinance = financeApplyMap.get(financeNo);
            //获得当前融资订单库存
            List<WarehouseDetails> warehouseDetailsList = warehouseDetailMap.get(pledgeFinance.getFinanceNo());
            if (CollUtil.isEmpty(warehouseDetailsList)) {
                break;
            }
            WarehouseDetails warehouseDetails2 = warehouseDetailsList.get(0);
            vo.setFinanceNo(pledgeFinance.getFinanceNo());
            vo.setGoodsName(warehouseDetails2.getGoodsName());
            vo.setCommodityLogo(warehouseDetails2.getLogo());
            vo.setRedemptionDate(warehouseDetails2.getRedemptionDate());
            vo.setCommodityId(warehouseDetails2.getGoodsId());
            vo.setGoodsUnit(warehouseDetails2.getGoodsUnitValue());
            Map<String, List<WarehouseDetails>> specMap = warehouseDetailsList.stream().collect(Collectors.groupingBy(WarehouseDetails::getGoodsSpec));
            List<RedeemSpec> specVOList = Lists.newArrayList();
            //赎货中商品信息
            List<RedeemCommodity> redeemCommodityList = financeCommodityMap.get(financeNo);
            for (String spec : specMap.keySet()) {
                int inUse = 0;
                if (CollUtil.isNotEmpty(redeemCommodityList)) {
                    List<RedeemCommodity> commodityList = redeemCommodityList.stream().filter(e -> spec.equals(e.getSpec())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(commodityList)) {
                        inUse = commodityList.stream().mapToInt(RedeemCommodity::getNum).sum();
                    }
                }
                //构建不同规格的商品vo
                RedeemSpec specVO = new RedeemSpec();
                List<WarehouseDetails> warehouseDetails1 = specMap.get(spec);
                WarehouseDetails warehouseSpec = warehouseDetails1.get(0);
                int sum = warehouseDetails1.stream().mapToInt(WarehouseDetails::getWarehouseNum).sum();
                //可赎货数量 = 在库总数 - 使用中数量
                specVO.setRedeemNum(Math.max(sum - inUse, 0));
                specVO.setRedeemNumIng(inUse);
                specVO.setSpec(warehouseSpec.getGoodsSpec());
                specVO.setPurchasePrice(warehouseSpec.getPurchasePrice());
                specVO.setFinancingPrice(warehouseSpec.getFinancingPrice());
                specVOList.add(specVO);
            }
            vo.setRedeemSpecList(specVOList);
            vo.setRedeemTotal(specVOList.stream().mapToInt(RedeemSpec::getRedeemNum).sum());
            pledgeRedeemCargoVOList.add(vo);
        }
        return pledgeRedeemCargoVOList.stream()
                .sorted(Comparator.comparing(PledgeRedeemCargoVO::getRedemptionDate))
                .collect(Collectors.toList());
    }

    private Map<String, List<RedeemCommodity>> getFinanceCommodityMap(IPledgeRedeemCargoService redeemCargoService, List<String> financeNoList) {
        List<Integer> statusList = Arrays.asList(RedeemCargoStatusEnum.REDEEM_CARGO_EXAMINEING.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_CONFIRM.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_EXAMINEING.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_SEND.getKey());
        List<PledgeRedeemCargo> redeemList = redeemCargoService.list(Wrappers.<PledgeRedeemCargo>lambdaQuery()
                .in(PledgeRedeemCargo::getFinanceNo, financeNoList)
                .in(BaseEntity::getStatus, statusList));
        if (CollUtil.isEmpty(redeemList)) {
            return MapUtil.newHashMap();
        }
        //查询符合条件的赎货的赎货信息
        List<String> redeemNoList = redeemList.stream().map(PledgeRedeemCargo::getRedeemNo).collect(Collectors.toList());
        List<RedeemCommodity> redeemCommodityList = redeemCommodityMapper.selectList(Wrappers.<RedeemCommodity>lambdaQuery().in(RedeemCommodity::getRedeemNo, redeemNoList));
        if (CollUtil.isEmpty(redeemCommodityList)) {
            return MapUtil.newHashMap();
        }
        Map<String, List<RedeemCommodity>> financeCommodityMap = redeemCommodityList.stream().collect(Collectors.groupingBy(RedeemCommodity::getFinancingNo));
        return financeCommodityMap;
    }

    /**
     * 动产质押是否已经完成所有赎货
     *
     * @param financeNo
     * @return
     */
    private Boolean isChangeStatus(String financeNo) {
        PledgeRedeemCargoVO pledgeRedeemCargoVO = fontWarehousDetailByPledge(financeNo);
        List<RedeemSpec> redeemSpecList = pledgeRedeemCargoVO.getRedeemSpecList();
        Boolean isChange = true;
        for (RedeemSpec redeemSpec : redeemSpecList) {
            //赎货中和待赎货数量均为0
            if (!redeemSpec.getRedeemNum().equals(0) || !redeemSpec.getRedeemNumIng().equals(0)) {
                isChange = false;
            }
        }
        return isChange;
    }

    private void pledgeFinanceChangeStatus(PledgeFinance pledgeFinance, FinanceApply financeApply, Integer status, Integer iouStatus) {
        Boolean changeStatus = isChangeStatus(financeApply.getFinanceNo());
        //判断是否更新状态
        if (changeStatus) {
            LoanManageIou iou = loanManageIouMapper.selectLoanByFinanceNo(financeApply.getFinanceNo());
            iou.setStatus(iouStatus);
            loanManageIouMapper.updateById(iou);
            pledgeFinance.setStatus(status);
            pledgeFinanceService.updateById(pledgeFinance);
            financeApply.setStatus(status);
            financeApplyService.updateById(financeApply);
        }
    }


    @Override
    public BigDecimal getPrincipal(List<RedeemCommodity> commodityList) {
        BigDecimal principal = BigDecimal.ZERO;
        if (CollUtil.isEmpty(commodityList)) {
            return principal;
        }
        for (RedeemCommodity redeemCommodity : commodityList) {
            BigDecimal multiply = redeemCommodity.getFinancingPrice().multiply(new BigDecimal(redeemCommodity.getNum()));
            principal = principal.add(multiply);
        }
        return principal;
    }

    @Override
    public SubLedgerInfoVO getRedeemExpenseAndAccountByPledge(String redeemNo) {
        PledgeRedeemCargo pledgeRedeemCargo = getOne(Wrappers.<PledgeRedeemCargo>lambdaQuery().eq(PledgeRedeemCargo::getRedeemNo, redeemNo));
        if (ObjectUtil.isEmpty(pledgeRedeemCargo)) {
            throw new ServiceException("赎货订单不存在!");
        }
        /*WarehouseDetails warehouseDetails = warehouseDetailsService.getById(redeemCargo.getStockId());
        if (ObjectUtil.isEmpty(warehouseDetails)) {
            throw new ServiceException("库存订单不存在!");
        }*/
        //费用信息
        List<RedeemExpense> redeemExpenseList = redeemExpenseService
                .lambdaQuery()
                .eq(RedeemExpense::getRedeemNo, redeemNo)
                .list();
        if (CollectionUtil.isEmpty(redeemExpenseList)) {
            return null;
        }
        //查询当前赎货的费用订单
        Map<String, ExpenseOrder> expenseOrderMap = expenseOrderService.getMapInExpenseNo(StreamUtil.map(redeemExpenseList, RedeemExpense::getExpenseOrderNo));


        //BigDecimal financingPrice = warehouseDetails.getFinancingPrice();
        //Integer num = redeemCargo.getNum();
        List<RedeemCommodity> commodityList = redeemCommodityService.getCommodityList(redeemNo);
        BigDecimal capitalAmount = getPrincipal(commodityList);
        //线下支付的费用
        List<RedeemExpense> belowRedeemExpenses = redeemExpenseList.stream()
                .filter(redeemExpense -> PayModeEnum.PAY_MODE_BELOW.getCode().equals(redeemExpense.getCostPayMode()))
                .collect(Collectors.toList());

        //线上支付的费用
        List<RedeemExpense> onlineRedeemExpenses = redeemExpenseList.stream()
                .filter(redeemExpense -> PayModeEnum.PAY_MODE_ONLINE.getCode().equals(redeemExpense.getCostPayMode()))
                .collect(Collectors.toList());
        //线下支付总额
        BigDecimal belowReduce = belowRedeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (RedeemExpense belowRedeemExpense : belowRedeemExpenses) {
            if (belowRedeemExpense.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                belowReduce = belowReduce.add(capitalAmount);
            }
        }
        //线上支付总额
        BigDecimal onlineReduce = onlineRedeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (RedeemExpense onlineRedeemExpens : onlineRedeemExpenses) {
            //如果是资方费用需要将本金计算
            if (onlineRedeemExpens.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                onlineReduce = onlineReduce.add(capitalAmount);
                break;
            }
        }
        //查询费用对应的账户
        List<Long> bankCardaIds = StreamUtil.map(redeemExpenseList, RedeemExpense::getAccountId);
        List<BillBankCarda> billBankCardas = billBankCardaService.listByIds(bankCardaIds);
        Map<Long, BillBankCarda> bankCardaMap = StreamUtil.toMap(billBankCardas, BillBankCarda::getId, obj -> obj);
        //查询费用类型
        Map<Integer, ExpenseType> expenseTypeMap = expenseTypeService.getMapByExpenseType();
        //查询资方的支付凭证
        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(pledgeRedeemCargo.getRepaymentRecordId());

        //线下支付根据费用类型分组
        Map<Integer, List<RedeemExpense>> belowRedeemExpenseMap = StreamUtil.groupBy(belowRedeemExpenses, RedeemExpense::getType);
        //线下支付数据
        List<BelowPayExpensesVO> redeemCostVOList = Lists.newArrayList();
        for (Map.Entry<Integer, List<RedeemExpense>> entry : belowRedeemExpenseMap.entrySet()) {
            BelowPayExpensesVO redeemCostVO = new BelowPayExpensesVO();
            List<RedeemExpense> redeemExpenses = belowRedeemExpenseMap.get(entry.getKey());
            BigDecimal expenseReduce = redeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            RedeemExpense redeemExpense = redeemExpenses.get(0);
            ExpenseOrder billExpenseOrder = expenseOrderMap.get(redeemExpense.getExpenseOrderNo());
            if (redeemExpense.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                expenseReduce = expenseReduce.add(capitalAmount);
                redeemCostVO.setPrincipal(capitalAmount);
            }
            BillBankCarda billBankCarda = bankCardaMap.get(redeemExpense.getAccountId());
            ExpenseType expenseType = expenseTypeMap.get(redeemExpense.getType());
            redeemCostVO.setBillBankCarda(billBankCarda);
            redeemCostVO.setBelowExpenseVoList(redeemExpenses);
            redeemCostVO.setExpenseType(Objects.nonNull(expenseType) ? expenseType.getExpenseName() : null);
            redeemCostVO.setBelowAmount(expenseReduce);
            redeemCostVO.setBelowPayStatus(Objects.nonNull(billExpenseOrder) ? billExpenseOrder.getPaymentStatus() : null);
            redeemCostVO.setExpenseOrderNo(redeemExpense.getExpenseOrderNo());
            //线下支付凭证
            if (Objects.nonNull(billExpenseOrder) && StringUtil.isNotBlank(billExpenseOrder.getPayAttachId())) {
                redeemCostVO.setAttachList(attachService.listByIds(Func.toLongList(billExpenseOrder.getPayAttachId())));
            }
            //资方支付凭证
            if (ObjectUtil.isNotEmpty(loanManageRepayment) && StringUtil.isNotBlank(loanManageRepayment.getVoucher())) {
                redeemCostVO.setAttachList(attachService.listByIds(Func.toLongList(loanManageRepayment.getVoucher())));
            }
            redeemCostVO.setExpenseOrder(billExpenseOrder);
            redeemCostVOList.add(redeemCostVO);
        }
        //线上支付根据费用类型分组
        Map<Integer, List<RedeemExpense>> onlineRedeemExpenseMap = StreamUtil.groupBy(onlineRedeemExpenses, RedeemExpense::getType);
        //线上支付查询虚拟账户
        List<MergePay> onlineAccount = billBankCardas.stream().filter(e -> StringUtil.isNotBlank(e.getMerchantNo())).map(e -> {
            return BillBankCardaWrapper.build().billToMergePay(e);
        }).collect(Collectors.toList());
        Map<Long, MergePay> mergePayMap = StreamUtil.toMap(onlineAccount, MergePay::getId, mergePay -> mergePay);

        //是否包含资方费用  1.是 .2否
        int IsContainCapitalCost = GoodsEnum.IS_NO_HIGH_QUALITY.getCode();
        //线上支付数据
        List<OnlinePayExpense> redeemAccountVOList = Lists.newArrayList();
        for (Map.Entry<Integer, List<RedeemExpense>> entry : onlineRedeemExpenseMap.entrySet()) {
            OnlinePayExpense redeemAccountVO = new OnlinePayExpense();
            List<RedeemExpense> redeemExpenses = onlineRedeemExpenseMap.get(entry.getKey());
            BigDecimal expenseReduce = redeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            RedeemExpense redeemExpense = redeemExpenses.get(0);
            ExpenseOrder billExpenseOrder = expenseOrderMap.get(redeemExpense.getExpenseOrderNo());
            if (redeemExpense.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                expenseReduce = expenseReduce.add(capitalAmount);
                redeemAccountVO.setPrincipal(capitalAmount);
                IsContainCapitalCost = GoodsEnum.IS_HIGH_QUALITY.getCode();
            }
            ExpenseType expenseType = expenseTypeMap.get(redeemExpense.getType());
            MergePay mergePay = mergePayMap.get(redeemExpense.getAccountId());
            redeemAccountVO.setMergePay(mergePay);
            redeemAccountVO.setExpenseType(Objects.nonNull(expenseType) ? expenseType.getExpenseName() : null);
            redeemAccountVO.setOnlineAmount(expenseReduce);
            redeemAccountVO.setOnlineExpenseVoList(redeemExpenses);
            redeemAccountVO.setOnlinePayStatus(Objects.nonNull(billExpenseOrder) ? billExpenseOrder.getPaymentStatus() : null);
            redeemAccountVO.setExpenseOrder(billExpenseOrder);
            redeemAccountVOList.add(redeemAccountVO);
        }
        SubLedgerInfoVO redeemCostAccountVO = new SubLedgerInfoVO();
        redeemCostAccountVO.setBelowAmount(belowReduce);
        redeemCostAccountVO.setOnlineAmount(onlineReduce);
        redeemCostAccountVO.setBelowPayExpensesList(redeemCostVOList);
        redeemCostAccountVO.setOnlinePayExpenseList(redeemAccountVOList);
        redeemCostAccountVO.setIsContainCapitalCost(IsContainCapitalCost);
        redeemCostAccountVO.setOnlinePayStatus(Objects.nonNull(pledgeRedeemCargo.getPaymentStatus()) ? pledgeRedeemCargo.getPaymentStatus() : 1);
        return redeemCostAccountVO;
    }

    @Override
    public Boolean redeemConfirm(RedeemCargoConfirmDTO redeemCargoConfirmDTO) {
        IPledgeRedeemCargoService pledgeRedeemCargoService = SpringUtil.getBean(IPledgeRedeemCargoService.class);
        //费用订单添加支付凭证
        List<RedeemCargoVoucherDTO> cargoVoucherDTOList = redeemCargoConfirmDTO.getRedeemCargoVoucherDTOS();
        //根据费用单号分组
//        List<String> map = StreamUtil.map(cargoVoucherDTOList, RedeemCargoVoucherDTO::getExpenseOrderNo);
//        Map<String, ExpenseOrder> expenseOrderMap = expenseOrderService.getMapInExpenseNo(map);
//        String bankRepaymentVoucher = redeemCargoConfirmDTO.getBankRepaymentVoucher();
//        //线下支付给对应的费用添加凭证
//        if (CollectionUtil.isNotEmpty(cargoVoucherDTOList)) {
//            for (RedeemCargoVoucherDTO cargoVoucherDTO : cargoVoucherDTOList) {
//                if (Objects.isNull(cargoVoucherDTO.getExpenseOrderNo())) {
//                    bankRepaymentVoucher = cargoVoucherDTO.getRepaymentVoucher();
//                } else {
//                    ExpenseOrder billExpenseOrder = expenseOrderMap.get(cargoVoucherDTO.getExpenseOrderNo());
//                    billExpenseOrder.setPayAttachId(cargoVoucherDTO.getRepaymentVoucher());
//                    expenseOrderService.updateById(billExpenseOrder);
//                }
//            }
//        }
        //费用订单添加支付凭证
        PledgeRedeemCargo pledgeRedeemCargo = lambdaQuery().eq(PledgeRedeemCargo::getRedeemNo, redeemCargoConfirmDTO.getRedeemNo()).one();
//        RedeemExpense redeemExpense = redeemExpenseService.getOne(Wrappers.<RedeemExpense>lambdaUpdate()
//                .eq(RedeemExpense::getRedeemNo, redeemCargoConfirmDTO.getRedeemNo())
//                .eq(RedeemExpense::getType, GoodsEnum.GOODS_TYPE_FUND.getCode())
//                .last("limit 1"));
        //还款记录添加支付凭证
        //资方是线上支付
//        if (PayModeEnum.PAY_MODE_BELOW.getCode().equals(redeemExpense.getCostPayMode())) {
//            LoanManageRepayment manageRepayment = loanManageRepaymentService.getById(pledgeRedeemCargo.getRepaymentRecordId());
//            if (manageRepayment != null && !StrUtil.isNullOrUndefined(bankRepaymentVoucher)) {
//                manageRepayment.setVoucher(bankRepaymentVoucher);
//                loanManageRepaymentService.updateById(manageRepayment);
//            }
//        }
        FinanceApply financeApply = financeApplyService.getByFinanceNo(pledgeRedeemCargo.getFinanceNo());
        PledgeFinance pledgeFinance = pledgeFinanceService.getPledgeFinance(pledgeRedeemCargo.getFinanceNo());
        PledgeRedeemDetailBackVO pledgeRedeemDetailBackVO = pledgeRedeemCargoService.backDetail(pledgeRedeemCargo.getRedeemNo());
        //发起赎货流程
        HashMap<String, Object> variables = MapUtil.newHashMap();

        variables.put(RedeemConstant.REDEEMNO, pledgeRedeemCargo.getRedeemNo());
        variables.put(RedeemConstant.REDEEM_DETAIL, pledgeRedeemDetailBackVO);
        variables.put(ProcessConstant.GOODS_ID, financeApply.getGoodsId());
        variables.put(ProcessConstant.USER_ID, financeApply.getUserId());
        variables.put(ProcessConstant.ENTERPRISE_TYPE, UserUtils.getEnterpriseType());
        variables.put(ProcessConstant.PLEDGE_QUOTA_ID, pledgeFinance.getPledgeQuotaId());
        variables.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        variables.put(ProcessConstant.EXPENSE_INFO_LIST, redeemCargoConfirmDTO.getExpenseInfoExpenseList());

        //ProcessDefinition processDefinition = wfProcessService.selectProcessDefinitionByKey(LoanConstant.PLEDGE_REDEEM_CONFIRM);
        //String processInstanceId = wfProcessService.startProcessInstanceById(processDefinition.getId(), variables);
        businessProcessProductService.startProcess(financeApply.getGoodsId(), ProcessTypeEnum.PLEDGE_REDEEM_CONFIRM, variables);
        //赎货状态修改
        pledgeRedeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_EXAMINEING.getKey());

        return updateById(pledgeRedeemCargo);
    }

    @Override
    public Boolean overRegister(OverRegisterDTO overRegisterDTO) {
        PledgeRedeemCargo pledgeRedeemCargo = getById(overRegisterDTO.getRedeemId());
        if (RedeemCargoStatusEnum.REDEEM_CARGO_SEND.getKey().equals(pledgeRedeemCargo.getStatus())) {
            pledgeRedeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey());
            FinanceApply financeApply = financeApplyService.getByFinanceNo(pledgeRedeemCargo.getFinanceNo());
            LocalDateTime countdownExpireTime = goodsTimingService.getByExpireTime(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_ACCEPTANCE.getCode());
            pledgeRedeemCargo.setCountdownExpireTime(countdownExpireTime);

            updateById(pledgeRedeemCargo);
            RedeemSend redeemSend = redeemSendService.lambdaQuery()
                    .eq(RedeemSend::getRedeemNo, pledgeRedeemCargo.getRedeemNo())
                    .orderByDesc(BaseEntity::getCreateTime).last("limit 1").one();
            redeemSend.setArrivalAddress(overRegisterDTO.getArrivalAddress());
            redeemSend.setArrivalTime(overRegisterDTO.getArrivalTime());
            redeemSendService.updateById(redeemSend);
            // 发送mq延时消息，超时未操作 代采-待验收状态 则关闭该申请
//            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
//                    .messageType(MessageTypeEnum.PURCHASE_REDEEMCARGO_APPLY.getCode())
//                    .status(RabbitMqStatusEnum.PLEDGE_REDEEMCARGO_ACCEPTANCE.getCode())
//                    .goodsTimingType(GoodsTimingEnum.GOODS_TIMING_PLEDGE_ACCEPTANCE.getCode())
//                    .goodsId(financeApply.getGoodsId())
//                    .msg(pledgeRedeemCargo.getId().toString())
//                    .build());
        }
        return updateById(pledgeRedeemCargo);
    }


    /**
     * 赎货通用信息
     *
     * @param redeemCargo 赎货单
     * @return RedeemDetailCargoCurrencyVO
     */
    private PledgeRedeemDetailCargoCurrencyVO redeemDetailCargoCurrency(PledgeRedeemCargo redeemCargo) {

        List<RedeemCommodity> redeemCommodityList = redeemCommodityService.getCommodityList(redeemCargo.getRedeemNo());
        WarehouseDetails warehouseDetails = warehouseDetailsService.getOne(Wrappers.<WarehouseDetails>lambdaQuery()
                .eq(WarehouseDetails::getFinanceNo, redeemCargo.getFinanceNo()).last("limit 1"));
//        Map<Integer, ExpenseType> expenseTypeMap = expenseTypeService.getMapByExpenseType();
        FinanceApply financeApply = financeApplyService.getByFinanceNo(redeemCargo.getFinanceNo());
//        List<BillBankCardaRelationVO> bankCardaRelationVOS = billBankCardaRelationService.getByGoodsId(financeApply.getGoodsId());
//        List<Long> bankCardaIds = StreamUtil.map(bankCardaRelationVOS, BillBankCardaRelationVO::getBillBankCardaId);
//        Map<Long, BillBankCarda> bankCardaMap = Collections.emptyMap();
//        Map<Long, BillBankCarda> mergePayMap = Collections.emptyMap();
//        if (CollectionUtil.isNotEmpty(bankCardaRelationVOS)) {
//            List<BillBankCarda> billBankCardas = billBankCardaService.listByIds(bankCardaIds);
//            bankCardaMap = StreamUtil.toMap(billBankCardas, BillBankCarda::getId, billBankCarda -> billBankCarda);
//            //线上支付查询虚拟账户
//            List<BillBankCarda> mergePays = billBankCardaService.listByIds(bankCardaIds).stream()
//                    .filter(e -> ObjectUtil.isNotEmpty(e.getMerchantNo())).collect(Collectors.toList());
//            mergePayMap = StreamUtil.toMap(mergePays, BillBankCarda::getId, mergePay -> mergePay);
//        }

        PledgeRedeemDetailCargoCurrencyVO detailCargoCurrencyVO = new PledgeRedeemDetailCargoCurrencyVO();
        detailCargoCurrencyVO.setNum(redeemCargo.getNum());
        detailCargoCurrencyVO.setRedeemNo(redeemCargo.getRedeemNo());
        detailCargoCurrencyVO.setExtractType(ExtractTypeEnum.getValueByKey(redeemCargo.getExtractType()));
        detailCargoCurrencyVO.setGoodLogo(StrUtil.isNotBlank(warehouseDetails.getGoodsInfo()) ?
                JsonUtil.parse(warehouseDetails.getGoodsInfo(), GoodsDetailsDTO.class).getLogo() : "");
        detailCargoCurrencyVO.setGoodsName(warehouseDetails.getGoodsName());
        detailCargoCurrencyVO.setGoodsUnitValue(warehouseDetails.getGoodsUnitValue());
        detailCargoCurrencyVO.setSupplierName(warehouseDetails.getSupplierName());
        detailCargoCurrencyVO.setFinanceNo(warehouseDetails.getFinanceNo());
        detailCargoCurrencyVO.setRedemptionDate(warehouseDetails.getRedemptionDate());
        detailCargoCurrencyVO.setStatus(redeemCargo.getStatus());
        detailCargoCurrencyVO.setRedeemCommodityVOS(RedeemCommodityWrapper.build().listVO(redeemCommodityList));
        detailCargoCurrencyVO.setShouldInterest(BigDecimal.ZERO);

        //收货地址信息
        if (ExtractTypeEnum.WOM_EXTRACT.getKey().equals(redeemCargo.getExtractType())) {
            detailCargoCurrencyVO.setRedeemUser(redeemUserService.lambdaQuery().eq(RedeemUser::getCargoId, redeemCargo.getId()).one());
        } else {

            PledgeFinance pledgeFinance = pledgeFinanceService.getPledgeFinance(redeemCargo.getFinanceNo());
//            PurchaseInformation purchaseInformation = pledge.getById(redeemCargo.getExtractId());
            FinancingAddressVO financingAddressVO = new FinancingAddressVO();
////            //TODO 改成查一个地址
//            if (pledgeFinance != null) {
//                financingAddressVO.setEnterpriseName(pledgeFinance.getReceiveCompanyName());
//                financingAddressVO.setUrbanAreas(pledgeFinance.getReceiveAddress());
//                financingAddressVO.setContacts(pledgeFinance.getReceiveName());
//                financingAddressVO.setAddressPhone(pledgeFinance.getReceiveNumber());
//                financingAddressVO.setCreateTime(pledgeFinance.getCreateTime());
//            } else {
            FinancingAddress financingAddress = financingAddressService.getById(redeemCargo.getExtractId());
            financingAddressVO = FinancingAddressWrapper.build().entityVO(financingAddress);
            Region region = regionService.getOne(financingAddressVO.getLocation()).getData();
            financingAddressVO.setEnterpriseName(financingAddressVO.getAddressTarget());
            financingAddressVO.setAddressTarget(region.getProvinceName() + region.getCityName() + region.getDistrictName() + financingAddress.getAddressDetailed());
//            }
            detailCargoCurrencyVO.setFinancingAddress(financingAddressVO);
//            if(true){
//                throw new ServiceException("1");
//            }
        }
        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
//缴费信息
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS = expenseInfoBizService
                .listExpenseInfoList(redeemCargo.getRedeemNo(), PlatformExpensesEnum.PLAT_TYPE_REDEEM.getCode(), ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode(), -1L);
        Map<String, ExpenseInfoExpenseVO> expenseInfoExpenseVOSMap = expenseInfoExpenseVOS.stream().collect(Collectors.toMap(ExpenseInfoVO::getBillExpenseNo, e -> e));
        // 资方费用=还款利息+本金 资方类型的费用
        Optional<ExpenseInfoExpenseVO> first = expenseInfoExpenseVOS.stream().filter(e -> e.getExAmount().compareTo(BigDecimal.ZERO) > 0).findFirst();
        if (first.isPresent()) {
            ExpenseInfoExpenseVO expenseInfoExpenseVO = first.get();
            CapitalExpenseManVO capitalExpenseManVO = new CapitalExpenseManVO();
            capitalExpenseManVO.setLoanManageRepayment(loanManageRepayment);
            capitalExpenseManVO.setPayMode(expenseInfoExpenseVO.getPayMode());
            capitalExpenseManVO.setAttachList(expenseInfoExpenseVO.getOriginAttachList());
            //资方费用转化
            List<ExpenseOrderDetail> expenseOrderDetails = expenseInfoExpenseVOS.stream().flatMap(e -> e.getExpenseOrderDetail().stream()
                    .filter(ex -> AccountTypeEnum.CAPITAL_ACCOUNT.getCode().equals(ex.getAccountType()))).collect(Collectors.toList());
            List<RedeemExpense> collect = expenseOrderDetails.stream().map(e -> redeemCargoService.transferToRedeemExpensesList(e, warehouseDetails.getGoodsId(), e.getAmount(), -1L))
                    .collect(Collectors.toList());
            capitalExpenseManVO.setManExpenseCulationVoList(collect);
            detailCargoCurrencyVO.setManExpenseCulationVo(capitalExpenseManVO);
            detailCargoCurrencyVO.setManExpenseCulationVoList(collect);
            detailCargoCurrencyVO.setShouldTotal(loanManageRepayment.getInterest());
            //费用信息
            LoanManageRepaymentPlan repaymentPlan = loanManageRepaymentPlanService.getById(loanManageRepayment.getRepaymentPlanId());
            detailCargoCurrencyVO.setShouldInterest(loanManageRepayment.getInterest());
            List<ExpenseOrderDetail> collect1 = expenseInfoExpenseVOS.stream().flatMap(e -> e.getExpenseOrderDetail().stream()).collect(Collectors.toList());
            CostCalculusVO costCalculusVO = redeemCargoService.buildCostCalculusVO(loanManageRepayment.getPrincipal(), loanManageRepayment.getInterest(), repaymentPlan.getAnnualInterestRate(), repaymentPlan.getDayRate(), collect1);
            //费用订单填充
            if (costCalculusVO.getExpenseOrderDetailFinanceVos() != null) {
                for (ExpenseOrderDetailFinanceVo expenseOrderDetailFinanceVo : costCalculusVO.getExpenseOrderDetailFinanceVos()) {
                    if (expenseOrderDetailFinanceVo.getBillExpenseNo() != null) {
                        ExpenseInfoExpenseVO infoExpenseVO = expenseInfoExpenseVOSMap.get(expenseOrderDetailFinanceVo.getBillExpenseNo());
                        expenseOrderDetailFinanceVo.setPaymentStatus(infoExpenseVO.getPaymentStatus());
                        expenseOrderDetailFinanceVo.setAttachInfoDTOList(infoExpenseVO.getAttachList());
                    }
                }
            }
            detailCargoCurrencyVO.setCostCalculusVO(costCalculusVO);
        }
        //其他费用转化

        List<PlaExpenseCulationVO> plaExpenseVOList = Lists.newArrayList();
        Map<Integer, List<ExpenseOrderDetail>> redeemExpenseMap = expenseInfoExpenseVOS.stream().flatMap(e -> e.getExpenseOrderDetail().stream()
                        .filter(ex -> !AccountTypeEnum.CAPITAL_ACCOUNT.getCode().equals(ex.getAccountType())))
                .collect(Collectors.groupingBy(ExpenseOrderDetail::getAccountType));
        for (Map.Entry<Integer, List<ExpenseOrderDetail>> expenseList : redeemExpenseMap.entrySet()) {
            List<ExpenseOrderDetail> expenseOrderDetails = expenseList.getValue();
            ExpenseOrderDetail expenseOrderDetail = CollUtil.getFirst(expenseOrderDetails);
            ExpenseInfoExpenseVO expenseInfoExpenseVO = expenseInfoExpenseVOSMap.get(expenseOrderDetail.getBillExpenseNo());
            PlaExpenseCulationVO plaExpenseCulationVO = new PlaExpenseCulationVO();
            plaExpenseCulationVO.setAccountId(expenseOrderDetail.getAccountId());
            plaExpenseCulationVO.setExpenseTypeStr(expenseOrderDetail.getParenExpenseName());
            List<RedeemExpense> collect = expenseOrderDetails.stream().map(e -> redeemCargoService.transferToRedeemExpensesList(e, warehouseDetails.getGoodsId(), e.getAmount(), -1L))
                    .collect(Collectors.toList());
            plaExpenseCulationVO.setPlaExpenseCulationVoList(collect);
            plaExpenseCulationVO.setAccountName(expenseOrderDetail.getAccountName());
            plaExpenseCulationVO.setExpenseStatus(expenseInfoExpenseVO.getPaymentStatus());
            plaExpenseCulationVO.setPayMode(expenseInfoExpenseVO.getPayMode());
            ExpenseOrder expenseOrder = BeanUtil.copyProperties(expenseInfoExpenseVO, ExpenseOrder.class);
            plaExpenseCulationVO.setBillExpenseOrder(expenseOrder);
            plaExpenseCulationVO.setAttachList(expenseInfoExpenseVO.getOriginAttachList());
            plaExpenseVOList.add(plaExpenseCulationVO);
        }
        detailCargoCurrencyVO.setPlaExpenseVOList(plaExpenseVOList);
        if (ObjectUtil.isNotEmpty(detailCargoCurrencyVO.getPlaExpenseVOList())) {
            detailCargoCurrencyVO.setPlaExpenseCulationVoList(detailCargoCurrencyVO.getPlaExpenseVOList()
                    .stream().flatMap(e -> e.getPlaExpenseCulationVoList().stream()).collect(Collectors.toList()));
        }
        //附件信息
        if (RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_CONFIRM.getKey() < redeemCargo.getStatus()) {
            if (ObjectUtil.isNotEmpty(detailCargoCurrencyVO.getPlaExpenseVOList())) {
                List<Attach> collect = detailCargoCurrencyVO.getPlaExpenseVOList().stream()
                        .filter(e -> CollUtil.isNotEmpty(e.getAttachList()))
                        .flatMap(e -> e.getAttachList().stream()).collect(Collectors.toList());
                detailCargoCurrencyVO.setPlaAttachList(collect);
            }
            if (loanManageRepayment != null && StrUtil.isNotBlank(loanManageRepayment.getVoucher())) {
                detailCargoCurrencyVO.setManAttachList(attachService.listByIds(Func.toLongList(loanManageRepayment.getVoucher())));
            }

        }
        //发货信息
        if (RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_SEND.getKey() < redeemCargo.getStatus()) {
            detailCargoCurrencyVO.setRedeemSend(redeemSendService.lambdaQuery()
                    .eq(RedeemSend::getRedeemNo, redeemCargo.getRedeemNo())
                    .orderByDesc(BaseEntity::getCreateTime)
                    .last("limit 1").one());
        }

//        detailCargoCurrencyVO.setExpenseInfoExpenseList(expenseInfoExpenseVOS);
        detailCargoCurrencyVO.setShouldTotal(getShouldTotal(detailCargoCurrencyVO.getRedeemCommodityVOS(), detailCargoCurrencyVO.getManExpenseCulationVoList(), detailCargoCurrencyVO.getPlaExpenseCulationVoList())
                .add(detailCargoCurrencyVO.getShouldInterest()));
        detailCargoCurrencyVO.setRealTotal(getRealTotal(expenseInfoExpenseVOS));
        return detailCargoCurrencyVO;

    }

    private BigDecimal getRealTotal(List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS) {
        return expenseInfoExpenseVOS.stream().filter(e -> ObjectUtil.isNotEmpty(e.getPayAmount())).map(ExpenseInfoVO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getShouldTotal(List<RedeemCommodityVO> redeemCommodityVOS, List<RedeemExpense> manExpenseCulationVoList, List<RedeemExpense> plaExpenseVOList) {
        BigDecimal reduce = redeemCommodityVOS.stream().map(e -> e.getFinancingPrice().multiply(BigDecimal.valueOf(e.getNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (CollUtil.isNotEmpty(plaExpenseVOList)) {
            BigDecimal platAmount = plaExpenseVOList.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            reduce = reduce.add(platAmount);
        }
        if (CollUtil.isNotEmpty(manExpenseCulationVoList)) {
            BigDecimal platAmount = manExpenseCulationVoList.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            reduce = reduce.add(platAmount);
        }
        return reduce;
    }
}
