package org.springblade.pledge.service.impl;

import lombok.RequiredArgsConstructor;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.pledge.service.IPledgeProcessService;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: <PERSON><PERSON>g<PERSON>ang<PERSON>
 * @CreateTime: 2023-11-02  17:03
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class PledgeProcessServiceImpl implements IPledgeProcessService {
    private final IBusinessProcessProgressService businessProcessProgressService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessProcessProgress openGoodsProgress(Long businessId, Integer type, Long userId) {
        return businessProcessProgressService.openGoodsProgress(businessId, type, MyAuthUtil.getUserId());
    }
}
