package org.springblade.pledge.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.pledge.entity.PledgeQuota;
import org.springblade.pledge.vo.PledgeQuotaVO;

import java.util.Objects;

/**
 * 质押额度表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public class PledgeQuotaWrapper extends BaseEntityWrapper<PledgeQuota, PledgeQuotaVO> {

    public static PledgeQuotaWrapper build() {
        return new PledgeQuotaWrapper();
    }

    @Override
    public PledgeQuotaVO entityVO(PledgeQuota PledgeQuota) {
        PledgeQuotaVO PledgeQuotaVO = Objects.requireNonNull(BeanUtil.copy(PledgeQuota, PledgeQuotaVO.class));

        //User createUser = UserCache.getUser(PledgeQuota.getCreateUser());
        //User updateUser = UserCache.getUser(PledgeQuota.getUpdateUser());
        //PledgeQuotaVO.setCreateUserName(createUser.getName());
        //PledgeQuotaVO.setUpdateUserName(updateUser.getName());

        return PledgeQuotaVO;
    }

}
