package org.springblade.pledge.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.pledge.entity.PledgeCommodity;
import org.springblade.pledge.vo.PledgeCommodityVO;

import java.util.Objects;

/**
 * 质押货物表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public class PledgeCommodityWrapper extends BaseEntityWrapper<PledgeCommodity, PledgeCommodityVO> {

    public static PledgeCommodityWrapper build() {
        return new PledgeCommodityWrapper();
    }

    @Override
    public PledgeCommodityVO entityVO(PledgeCommodity PledgeCommodity) {
        PledgeCommodityVO PledgeCommodityVO = Objects.requireNonNull(BeanUtil.copy(PledgeCommodity, PledgeCommodityVO.class));

        //User createUser = UserCache.getUser(PledgeCommodity.getCreateUser());
        //User updateUser = UserCache.getUser(PledgeCommodity.getUpdateUser());
        //PledgeCommodityVO.setCreateUserName(createUser.getName());
        //PledgeCommodityVO.setUpdateUserName(updateUser.getName());

        return PledgeCommodityVO;
    }

}

