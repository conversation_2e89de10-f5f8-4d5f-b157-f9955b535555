/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.pledge.controller.front;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.product.expense.constant.ExpenseConstant;;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.finance.vo.LoanInfo;
import org.springblade.pledge.dto.*;
import org.springblade.pledge.service.*;
import org.springblade.pledge.vo.PledgeFinanceVO;
import org.springblade.pledge.vo.PledgeRedeemCargoVO;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.redeem.vo.RedeemCargoCalculationVO;
import org.springblade.system.utils.UserUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 动产质押流程接口
 *
 * @module 动产质押融资申请
 * @Author: zhengchuangkai
 * @CreateTime: 2023/11/4 15:08
 * @Description: TODO
 * @Version: 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_PLEDGE + CommonConstant.WEB_FRONT + "/process")
@Api(value = "动产质押流程接口", tags = "接口")
public class PledgeProcessFrontController extends BladeController {

    private final IPledgeFinanceApplyService pledgeFinanceApplyService;
    private final IPledgeFinanceService pledgeFinanceService;
    private final IPledgeProcessService pledgeProcessService;
    private final IPledgeGoodsOpenService pledgeGoodsOpenService;
    private final IPledgeFinanceLoanApplyService pledgeFinanceLoanApplyService;
    private final IPledgeRedeemApplyService pledgeRedeemApplyService;

    /**
     * 获取动产质押流程进度
     *
     * @param businessId 业务id
     * @param type       业务类型
     * @return
     */
    @GetMapping("/openGoodsProgress")
    @ApiOperation("获取动产质押流程进度")
    public R<BusinessProcessProgress> openGoods(@RequestParam Long businessId, @RequestParam Integer type) {
        return R.data(pledgeProcessService.openGoodsProgress(businessId, type, MyAuthUtil.getUserId()));
    }

    /**
     * 提交评估申请(产品开通)
     *
     * @param openPledgeGoodsDto 动产质押产品开通
     * @return
     */
    @PostMapping("/submitPledge")
    @ApiOperation("提交评估申请(产品开通)")
    public R<Boolean> submitPledge(@RequestBody OpenPledgeGoodsDto openPledgeGoodsDto) {
        return R.data(pledgeGoodsOpenService.submitPledge(openPledgeGoodsDto));
    }

    /**
     * 融资申请还款试算还款试算
     */
    @PostMapping("/repaymentFinanceApplyCalculation")
    public R<CostCalculusVO> repaymentFinanceApplyCalculation(@RequestBody PledgeCostCalculusDto pledgeCostCalculusDto) {
        pledgeCostCalculusDto.setUserId(AuthUtil.getUserId());
        pledgeCostCalculusDto.setEnterpriseType(UserUtils.getEnterpriseType());
        pledgeCostCalculusDto.setCurrentFeeNode(ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode());
        pledgeCostCalculusDto.setExpenseOrderDetailPass(false);
        pledgeCostCalculusDto.setExpenseOrderDetailPassIds("");
        return R.data(pledgeFinanceApplyService.repaymentFinanceApplyCalculation(pledgeCostCalculusDto));
    }

    /**
     * 融资申请还款试算还款试算
     */
    @GetMapping("/repaymentCalculation-financeNo")
    public R<CostCalculusVO> repaymentFinanceApplyCalculation(String financeNo) {
        return R.data(pledgeFinanceApplyService.repaymentFinanceApplyCalculationByFinanceNo(financeNo));
    }

    /**
     * 动产质押融资提交申请审核
     */
    @PostMapping("/submit-finance-apply")
    @ApiOperationSupport(order = 4)
    //@PreAuth("hasPermission('pledgeCommodity:pledgeCommodity:save') or hasRole('administrator')")
    public R<Boolean> submitFinanceApply(@Valid @RequestBody PledgeFinanceApplyDTO pledgeFinanceApplyDTO) {
        return R.status(pledgeFinanceApplyService.savePledgeCommodity(pledgeFinanceApplyDTO));
    }

    /**
     * 动产质押 融资申请确认详情
     */
    @GetMapping("/pledge-wait-submit")
    @ApiOperation(value = "动产质押融资申请确认", notes = "传入financeNo")
    public R<PledgeFinanceVO> pledgeWaitSubmit(@RequestParam String financeNo) {
        return R.data(pledgeFinanceLoanApplyService.pledgeWaitSubmit(financeNo));
    }

    /**
     * 动产质押 融资放款申请
     */
    @PostMapping("/loanApply")
    public R<Boolean> loanApply(@RequestBody PledgeFinanceDTO pledgeFinanceDTO) {
        return R.status(pledgeFinanceLoanApplyService.loanApply(pledgeFinanceDTO));
    }

    @GetMapping("/loanInfoByPledge")
    @ApiOperation("动产质押借款信息")
    public R<LoanInfo> loanInfoByPledge(@RequestParam Long id) {
        return R.data(pledgeFinanceLoanApplyService.loanInfoByPledge(id));
    }

    /**
     * 动产质押 发起赎货申请页面信息
     */
    @GetMapping("/applyInfoByPledge")
    @ApiOperation(value = "待赎发起赎货申请页面信息列表", notes = "传入financeNo")
    public R<PledgeRedeemCargoVO> applyInfoByPledge(@RequestParam String financeNo) {
        return R.data(pledgeRedeemApplyService.fontWarehousDetailByPledge(financeNo));
    }

    /**
     * 动产质押 发起赎货申请计算费用
     */
    @PostMapping("/calculationRedeemByPledge")
    public R<RedeemCargoCalculationVO> calculationRedeemByPledge(@Valid @RequestBody PledgeRedeemCargoVO pledgeRedeemCargoVO) {
        return R.data(pledgeRedeemApplyService.calculationRedeemByPledge(pledgeRedeemCargoVO));
    }

    /**
     * 动产质押 发起赎货申请
     */
    @PostMapping("/savePledge")
    public R<Boolean> saveRedeemByPledge(@Valid @RequestBody PledgeRedeemCargoDTO pledgeRedeemCargoDTO) {
        return R.status(pledgeRedeemApplyService.saveRedeemByPledge(pledgeRedeemCargoDTO));
    }

}
