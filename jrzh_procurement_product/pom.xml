<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh_product_proxy_procurement</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>jrzh_procurement_product</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_common</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>jrzh_product_core</artifactId>
            <groupId>org.springblade</groupId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>jrzh_workflow</artifactId>
            <groupId>org.springblade</groupId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>jrzh_system_api</artifactId>
            <groupId>org.springblade</groupId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_resource</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_expenserelation</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_process</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_contract</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_proxy_procurement_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>jrzh_customer_biz</artifactId>
            <groupId>org.springblade</groupId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>


</project>