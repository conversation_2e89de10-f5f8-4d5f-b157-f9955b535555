package org.springblade.procurement.controller.applet;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.procurement.service.IAgentGoodsService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序 代采产品表 控制器
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS + CommonConstant.WEB_APPLET + "/goods/agentGoods")
@Api(value = "代采产品表", tags = "代采产品表接口")
public class AgentGoodsAppletController {

	private final IAgentGoodsService agentGoodsService;

}
