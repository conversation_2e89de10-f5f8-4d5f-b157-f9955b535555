package org.springblade.procurement.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.procurement.service.IAgentGoodsService;
import org.springblade.procurement.vo.AgentGoodsVO;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.resource.service.IParamService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 代采产品表 控制器
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS + CommonConstant.WEB_BACK + "/goods/agentGoods")
@Api(value = "代采产品表", tags = "代采产品表接口")
public class AgentGoodsController extends BladeController {

    private final IAgentGoodsService agentGoodsService;
    private final IParamService paramService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入goods")
    @PreAuth("hasPermission('agent:goods:detail') or hasRole('administrator')")
    public R<ProductVO> detail(@RequestParam Long id) {
        return R.data(agentGoodsService.detail(id));
    }

    /**
     * 分页 产品表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入AgentGoods")
    @PreAuth("hasPermission('agent:goods:list') or hasRole('administrator')")
    public R<IPage<ProductVO>> list(@RequestParam Map<String, Object> goods, Query query) {
        return R.data(agentGoodsService.selectGoodsPage(goods, query));
    }

    /**
     * 新增 产品表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入goods")
    @PreAuth("hasPermission('agent:goods:save') or hasRole('administrator')")
    public R<Long> save(@Valid @RequestBody ProductDTO goods) {
        return R.data(agentGoodsService.saveGoods(goods).getId());
    }

    /**
     * 修改 产品表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入goods")
    @PreAuth("hasPermission('agent:goods:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody ProductDTO goods) {
        agentGoodsService.updateGoods(goods);
        return R.status(true);
    }


//    @PostMapping("/updateGoodsRiskControl")
//    @ApiOperation("更新产品风控规则")
//    public R<Boolean> updateGoodsRiskControl(@RequestBody AgentGoods goods) {
//        return R.status(agentGoodsService.updateGoodsRiskControl(goods));
//    }

    /**
     * 删除 产品表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('agent:goods:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(agentGoodsService.deleteLogic(Func.toLongList(ids)));
    }


    @GetMapping("/onShelf")
    @ApiOperation("上架")
    public R<Boolean> onShelf(@RequestParam Long id) {
        agentGoodsService.onShelf(id);
        return R.status(true);
    }

    @GetMapping("/onShelfGoodsList")
    @ApiOperation("查询所有上架产品")
    public R<List<AgentGoods>> onShelfGoodsList() {
        return R.data(agentGoodsService.list(Wrappers.<AgentGoods>lambdaQuery().eq(AgentGoods::getStatus, GoodsEnum.ON_SHELF.getCode())));
    }

    @GetMapping("/batchOnShelf")
    @ApiOperation("批量上架")
    public R<Integer> batchOnShelf(@RequestParam String ids) {
        List<Long> idList = Func.toLongList(ids);
        agentGoodsService.batchOnShelf(idList);
        return R.data(idList.size());
    }

    @GetMapping("/offShelf")
    @ApiOperation("下架")
    public R<Boolean> offShelf(@RequestParam String ids) {
        agentGoodsService.offShelf(Func.toLongList(ids));
        return R.status(true);
    }

    @GetMapping("/setIsHighQuality")
    @ApiOperation("设置产品为优质产品")
    public R<Boolean> setIsHighQuality(@RequestParam Long id, @RequestParam Integer isHighQuality) {
        return R.status(agentGoodsService.update(Wrappers.<AgentGoods>lambdaUpdate().eq(AgentGoods::getId, id).set(AgentGoods::getIsHighQuality, isHighQuality)));
    }

    @GetMapping("/processGoodsInfo")
    @ApiOperation("流程产品信息")
    public R<AgentGoodsVO> processGoodsInfo(Long goodsId) {
        return R.data(agentGoodsService.processGoodsInfo(goodsId));
    }

    @GetMapping("/allOnShelfGoodsList")
    @ApiOperation("查询所有上架产品")
    public R<List<AgentGoods>> allOnShelfGoodsList(@RequestParam Integer type) {
        List<AgentGoods> goodsList = agentGoodsService.list(Wrappers.<AgentGoods>lambdaQuery()
                .eq(AgentGoods::getType, type)
                .eq(AgentGoods::getStatus, GoodsEnum.ON_SHELF.getCode()));
        return R.data(goodsList);
    }

    @GetMapping("/getAgentCapitaList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入AgentGoods")
    @PreAuth("hasPermission('agent:goods:getAgentCapitaList') or hasRole('administrator')")
    public R<IPage<AgentGoodsVO>> getAgentCapitaList(@RequestParam Map<String, Object> goods, Query query, Long companyId) {
        if (ObjectUtil.isNotEmpty(goods)) {
            goods.remove("companyId");
        }
        return R.data(agentGoodsService.getAgentCapitaList(goods, query, companyId));
    }

    @GetMapping("/copyGoods")
    @ApiOperation("产品复制")
    public R<Boolean> copyGoods(Long id) {
        agentGoodsService.copyById(id);
        return R.data(true);
    }

    @GetMapping("/canOperator")
    @ApiOperation("是否在非正常情况下可操作产品")
    public R getOperator() {
        return R.data(agentGoodsService.canOperator(paramService));
    }
}
