package org.springblade.procurement.controller.front;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.procurement.service.IAgentCustomerGoodsService;
import org.springblade.procurement.service.IAgentGoodsService;
import org.springblade.procurement.vo.AgentGoodsVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代采产品表 控制器
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_GOODS + CommonConstant.WEB_FRONT + "/goods/agentGoods")
@Api(value = "代采产品表", tags = "代采产品表接口")
public class AgentGoodsFrontController extends BladeController {

    private final IAgentGoodsService agentGoodsService;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final ICustomerGoodsService customerGoodsService;
    private final IAgentCustomerGoodsService agentCustomerGoodsService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入goods")
    public R<AgentGoodsVO> detail(@RequestParam Long id) {
        return R.data(agentCustomerGoodsService.detail(id,MyAuthUtil.getUserId()));
    }


//	@GetMapping("/list")
//	@ApiOperation("产品列表")
//	public R<IPage<AgentGoodsVO>> list(GoodsSearchDTO goodsSearchDTO, Query query) {
//		String tenantId=goodsSearchDTO.getTenantId();
//		if (StringUtil.isNotBlank(tenantId) && StringUtil.isBlank(AuthUtil.getTenantId())) {
//			IPage<AgentGoodsVO> data = (IPage<AgentGoodsVO>) TenantBroker.applyAs(tenantId, e -> {
//				return agentGoodsService.selectClientGoodsList(goodsSearchDTO,query);
//			});
//			return R.data(data);
//		}
//		return R.data(agentGoodsService.selectClientGoodsList(goodsSearchDTO,query));
//	}

}
