<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.procurement.mapper.AgentGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentGoodsResultMap" type="org.springblade.procurement.entity.AgentGoods">
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="goods_code" property="goodsCode"/>
        <result column="type" property="type"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_type_id" property="goodsTypeId"/>
        <result column="capital_id" property="capitalId"/>
        <result column="loan_amount_start" property="loanAmountStart"/>
        <result column="loan_amount_end" property="loanAmountEnd"/>
        <result column="load_term_start" property="loadTermStart"/>
        <result column="load_term_end" property="loadTermEnd"/>
        <result column="load_term_unit" property="loadTermUnit"/>
        <result column="annual_interest_rate_start" property="annualInterestRateStart"/>
        <result column="annual_interest_rate_end" property="annualInterestRateEnd"/>
        <result column="annual_interest_rate_type" property="annualInterestRateType"/>
        <result column="repayment_type" property="repaymentType"/>
        <result column="lending_method" property="lendingMethod"/>
        <result column="prepayment_type" property="prepaymentType"/>
        <result column="credit_form_id" property="creditFormId"/>
        <result column="white_list_template_id" property="whiteListTemplateId"/>
        <result column="is_pay_bond" property="isPayBond"/>
        <result column="bond_pay_type" property="bondPayType"/>
        <result column="bond_pay_proportion_start" property="bondPayProportionStart"/>
        <result column="bond_release_mode" property="bondReleaseMode"/>
        <result column="collection_type" property="collectionType"/>
        <result column="goods_explain" property="goodsExplain"/>
        <result column="background" property="background"/>
        <result column="is_high_quality" property="isHighQuality"/>
        <result column="score_template_id" property="scoreTemplateId"/>
        <result column="finance_proportion_start" property="financeProportionStart"/>
        <result column="finance_proportion_end" property="financeProportionEnd"/>
        <result column="bond_pay_proportion_end" property="bondPayProportionEnd"/>
        <result column="commodity_white_list_id" property="commodityWhiteListId"/>
    </resultMap>


    <select id="selectAgentGoodsPage" resultMap="agentGoodsResultMap">
        select * from jrzh_agent_goods where is_deleted = 0
    </select>

</mapper>
