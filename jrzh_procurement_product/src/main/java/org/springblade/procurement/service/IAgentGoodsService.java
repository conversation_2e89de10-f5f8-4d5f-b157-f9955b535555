/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.procurement.vo.AgentGoodsVO;
import org.springblade.product.moudle.pubproduct.service.IProductService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 代采产品表 服务类
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
public interface IAgentGoodsService extends BaseService<AgentGoods>, IProductService<AgentGoods> {

    /**
     * 查询客户端产品详情
     *
     * @param id 产品id
     * @return GoodsVO
     */
    AgentGoodsVO selectDetailById(Long id);

//    /**
//     * 查询客户端产品列表
//     *
//     * @param goodsSearchDTO 查询条件
//     * @return IPage<AgentGoodsVO>
//     */
//    IPage<AgentGoodsVO> selectClientGoodsList(GoodsSearchDTO goodsSearchDTO, Query query);


    /**
     * 获取流程产品信息
     *
     * @param id 产品id
     * @return AgentGoodsVO
     */
    AgentGoodsVO processGoodsInfo(Long id);

    /**
     * 根据id批量查询并根据id分组
     *
     * @param goodsIdList 产品id
     * @return Map<Long, AgentGoods>
     */
    Map<Long, AgentGoods> getMapInId(List<Long> goodsIdList);

    /***
     *
     * @param goods 产品查询
     * @param query 分页查询
     * @param companyId 企业ID
     * @return
     */
    IPage<AgentGoodsVO> getAgentCapitaList(Map<String, Object> goods, Query query, Long companyId);

    /**
     * 根据产品id以及定时任务类型，获取倒计时时间
     *
     * @param goodsId 产品id
     * @param type    类型
     * @return 倒计时时间：LocalDateTime
     */
    LocalDateTime getByExpireTime(Long goodsId, Integer type);

    //    /**
//     * 获取代采业务产品下拉列表
//     *
//     * @return
//     */
//    List<GoodsVO> getAgentGoodsList();
}
