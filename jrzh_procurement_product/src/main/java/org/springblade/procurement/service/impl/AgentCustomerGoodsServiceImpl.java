/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.EnterpriseTypeEnum;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.procurement.service.IAgentCustomerGoodsService;
import org.springblade.procurement.service.IAgentGoodsService;
import org.springblade.procurement.vo.AgentGoodsVO;
import org.springframework.stereotype.Service;

/**
 * 代采产品表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@Service
@RequiredArgsConstructor
public class AgentCustomerGoodsServiceImpl implements IAgentCustomerGoodsService {
    private final ICustomerGoodsService customerGoodsService;
    private final IAgentGoodsService agentGoodsService;
    private final IEnterpriseQuotaService enterpriseQuotaService;

    @Override
    public AgentGoodsVO detail(Long goodsId, Long userId) {
        CustomerGoods customerGoods = customerGoodsService.getOne(Wrappers.<CustomerGoods>lambdaQuery()
                .eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
                .eq(CustomerGoods::getGoodsId, goodsId)
                .eq(CustomerGoods::getEnterpriseType, EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode()));
        AgentGoodsVO detail = BeanUtil.copyProperties(agentGoodsService.detail(goodsId), AgentGoodsVO.class);
        if (customerGoods != null) {
            EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
            if (ObjectUtil.isNotEmpty(enterpriseQuota)) {
                detail.setCurrentAvailableAmount(enterpriseQuota.getAvailableAmount());
                detail.setGoodsAnnualInterestRateStr(enterpriseQuota.getAnnualInterestRate().toString());
                detail.setGoodsDailyInterestRateStr(enterpriseQuota.getDailyInterestRate().toString());
                detail.setGoodsExpireTime(enterpriseQuota.getExpireTime());
                detail.setFinancingProportion(enterpriseQuota.getFinancingProportion());
                detail.setCustomerGoodsId(customerGoods.getId());
                detail.setBondProportion(enterpriseQuota.getBondProportion());
            }
        }
        return detail;
    }
}
