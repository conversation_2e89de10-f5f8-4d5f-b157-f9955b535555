/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.procurement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.procurement.entity.AgentGoods;
import org.springblade.procurement.mapper.AgentGoodsMapper;
import org.springblade.procurement.service.IAgentGoodsService;
import org.springblade.procurement.vo.AgentGoodsVO;
import org.springblade.procurement.wrapper.AgentGoodsWrapper;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.entity.ProductManager;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.moudle.pubproduct.service.IProductConfigInfoHandler;
import org.springblade.product.moudle.pubproduct.service.IProductManagerService;
import org.springblade.product.moudle.timing.service.IGoodsTimingService;
import org.springblade.resource.service.IParamService;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteUserSearchService;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 代采产品表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-29
 */
@Service
@RequiredArgsConstructor
public class AgentGoodsServiceImpl extends BaseServiceImpl<AgentGoodsMapper, AgentGoods> implements IAgentGoodsService {

    private final RemoteUserSearchService userService;
    private final RemoteDeptSearchService deptService;
    private final IProductManagerService productManagerService;
    private final IGoodsTimingService goodsTimingService;
    //	private final IGoodsLabelRelationService goodsLabelRelationService;
//	private final IGoodsOpeningProcessService goodsOpeningProcessService;
//	private final IGoodsQuestionService goodsQuestionService;
//	private final IGoodsMaterialService goodsMaterialService;
//	private final IGoodsExpenseRelationService goodsExpenseRelationService;
//	private final IGoodsContractTemplateService goodsContractTemplateService;
//	private final IGoodsProcessService goodsProcessService;
//	private final IGoodsTimingService goodsTimingService;
//	private final IBillBankCardaRelationService billBankCardaRelationService;
//	private final IContractService contractService;
    private final IParamService paramService;
    private final List<IProductConfigInfoHandler> productConfigInfoHandlers;

    //	private final IExpenseAccountRelationService expenseAccountRelationService;
    private MPJLambdaWrapper<AgentGoods> leftJoinProductManager(MPJLambdaWrapper<AgentGoods> wrapper) {
        return wrapper.selectAll(AgentGoods.class)
                .eq(AgentGoods::getType, GoodsEnum.AGENT_PURCHASE_FINANCING.getCode())
                .leftJoin(ProductManager.class, ProductManager::getProductId, AgentGoods::getId)
                .selectAs(ProductManager::getCapitalLogo, Product::getCapitalLogo)
                .selectAs(ProductManager::getCapitalName, Product::getCapitalName);
    }

    @Override
    public IPage<ProductVO> selectGoodsPage(Map<String, Object> goods, Query query) {

        IPage<ProductVO> page = leftJoinProductManager(Condition.getMPJLambdaWrapper(goods, AgentGoods.class))
                .page(Condition.getPage(query), ProductVO.class);
        if (page.getTotal() <= 0) {
            return page;
        }
        List<ProductVO> records = page.getRecords();
        // 从产品列表中获取用户id，并查询用户
        Map<Long, String> userNameMap = UserUtils.mapUserName(StreamUtil.map(records, ProductVO::getUpdateUser));
        // 查询资方信息
        Map<Long, Dept> deptMap = UserUtils.mapDept(StreamUtil.map(records, ProductVO::getCapitalId));
        // 组装数据
        records = records.stream().peek(goodsVO -> {
            // 设置资金方logo和名称
            Dept dept = deptMap.get(goodsVO.getCapitalId());
            if (Objects.nonNull(dept)) {
                goodsVO.setCapitalLogo(dept.getLogoSrc());
                goodsVO.setCapitalName(dept.getDeptName());
            }
            // 设置操作人名称
            goodsVO.setOperator(userNameMap.getOrDefault(goodsVO.getUpdateUser(), ""));
        }).collect(Collectors.toList());
        page.setRecords(records);
        return page;
    }

    @Override
    public Product detailBase(Long id) {
        Product product = AgentGoodsWrapper.build().entityProduct(getById(id));
        settingPMInfo(product);
        return product;
    }

    private void settingPMInfo(Product product) {
        ProductManager productManager = productManagerService.getByProductId(product.getId());
        product.setCapitalName(productManager.getCapitalName());
        product.setCapitalLogo(productManager.getCapitalLogo());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductDTO saveGoods(ProductDTO goodsDTO) {
        saveBaseProduct(goodsDTO);
        productConfigInfoHandlers.forEach(e -> e.updateWithProduct(goodsDTO));
        return goodsDTO;
    }

    @Override
    public boolean saveBaseProduct(ProductDTO goodsDTO) {
        //检查
        checkGoodsNameIsRepeat(goodsDTO);
        //保存产品
        if (StringUtil.isBlank(goodsDTO.getGoodsCode())) {
            goodsDTO.setGoodsCode(CodeUtil.generateCode(CodeEnum.GOODS_CODE));
        }
        AgentGoods agentGoods = BeanUtil.copyProperties(goodsDTO, AgentGoods.class);
        saveOrUpdate(agentGoods);
        goodsDTO.setId(agentGoods.getId());
        //产品管理保存
        saveOrUpdateProductManager(agentGoods);
        return true;
    }

    private void saveOrUpdateProductManager(AgentGoods goods) {
        //获取资方信息
        Dept capitalInfo = deptService.getDeptById(goods.getCapitalId()).getData();
        ProductManager productManager = ProductManager.builder()
                .productId(goods.getId())
                .type(goods.getType())
                .capitalName(capitalInfo.getDeptName())
                .capitalLogo(capitalInfo.getLogoSrc())
                .build();
        productManagerService.saveOrUpdateProductManager(productManager);
    }

    private void checkGoodsNameIsRepeat(ProductDTO goods) {
        LambdaQueryWrapper<AgentGoods> wrapper = Wrappers.<AgentGoods>lambdaQuery().eq(AgentGoods::getGoodsName, goods.getGoodsName().trim());
        if (Objects.nonNull(goods.getId())) {
            wrapper.ne(AgentGoods::getId, goods.getId());
        }
        wrapper.eq(AgentGoods::getStatus, GoodsEnum.ON_SHELF.getCode());
        Integer count = baseMapper.selectCount(wrapper);
        Assert.isFalse(count > 0, "产品名称不能重复");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductDTO updateGoods(ProductDTO goodsDTO) {
        AgentGoods agentGoods = BeanUtil.copyProperties(goodsDTO, AgentGoods.class);
        checkGoodsIsOnShelf(goodsDTO.getId());
        checkGoodsNameIsRepeat(goodsDTO);
        updateById(agentGoods);
        saveOrUpdateProductManager(agentGoods);
        productConfigInfoHandlers.forEach(e -> e.updateWithProduct(goodsDTO));
        return goodsDTO;
    }

    private void checkGoodsIsOnShelf(Long id) {
        AgentGoods goods = baseMapper.selectById(id);
        if (canOperator(paramService) && !GoodsEnum.UN_ON_SHELF.getCode().equals(goods.getStatus())) {
            throw new ServiceException("修改失败，仅未上架商品才可修改");
        }
    }

//    private void submitGoods(AgentGoodsDTO goods) {
//        //构建产品费用产品账户参数
//        buildCloudProductParameter(goods);
//        Long id = goods.getId();
//        goodsLabelRelationService.saveGoodsLabelRelation(id, goods.getGoodsLabelIds());
//        // 保存产品开通流程
//        goodsOpeningProcessService.saveGoodsOpeningProcess(id, goods.getGoodsOpeningProcesses());
//        // 保存产品常见问题
//        goodsQuestionService.saveGoodsQuestion(id, goods.getGoodsQuestions());
////		// 保存产品费用
////		goodsExpenseRelationService.saveOrUpdateGoodsExpenseRelation(id, goods.getCapitalExpenses(), goods.getPlatformExpenses());
////		//保存产品账户
////		billBankCardaRelationService.saveOrUpdateGoodsAccount(id, goods.getCapitalBillBankCardas(), goods.getPlatformBillBankCardas(), goods.getCashDepositTakeBillBankCardas());
//        // 保存产品费用
//        goodsExpenseRelationService.saveOrUpdateGoodsExpenseRelation(id, goods.getGoodsExpenseRelations());
//        //保存产品账户关联
//        expenseAccountRelationService.saveExpenseAccountRelation(id, goods.getExpenseAccountRelations());
//        //保存产品账户
//        billBankCardaRelationService.saveGoodsAccount(id, goods.getBillBankCardaRelation(), goods.getCashDepositTakeBillBankCardas());
//        // 保存产品资料
//        goodsMaterialService.saveOrUpdateGoodsMaterial(id, goods.getGoodsMaterials());
//        // 保存产品合同模板
//        goodsContractTemplateService.submitGoodsContractTemplate(id, goods.getGoodsContractTemplates());
//        // 保存产品绑定流程
//        goodsProcessService.saveGoodsProcess(goods.getGoodsProcessList(), id);
//        // 保存产品定时任务
//        goodsTimingService.saveGoodsTiming(goods.getGoodsTimingList(), id);
//    }

//    /**
//     * 构建产品费用产品账户参数
//     *
//     * @param goods
//     */
//    private void buildCloudProductParameter(AgentGoodsDTO goods) {
//        List<GoodsExpenseBankVO> goodsExpense = goods.getGoodsExpense();
//        //产品费用参数
//        List<GoodsExpenseRelation> goodsExpenseRelationList = Lists.newArrayList();
//        //产品账户参数
//        List<BillBankCardaRelation> billBankCardaRelationList = Lists.newArrayList();
//        //产品费用账户关联
//        List<ExpenseAccountRelation> expenseAccountRelations = Lists.newArrayList();
//        MergePay mergePay = cn.hutool.extra.spring.SpringUtil.getBean(IPayCombineMergeService.class).getOne(Wrappers.<MergePay>lambdaQuery().eq(MergePay::getDeptId, AuthUtil.getDeptId()));
//        if (!cn.hutool.core.collection.CollectionUtil.isEmpty(goodsExpense)) {
//            for (GoodsExpenseBankVO goodsExpenseBankVO : goodsExpense) {
//                ExpenseAccountRelation expenseAccountRelation = new ExpenseAccountRelation();
//                List<GoodsExpenseRelation> goodsExpenseRelations = goodsExpenseBankVO.getGoodsExpenseRelations();
//                BillBankCardaRelation billBankCardaRelation = goodsExpenseBankVO.getBillBankCardaRelation();
//                if (ObjectUtil.isNotEmpty(billBankCardaRelation) && Objects.nonNull(billBankCardaRelation.getBillBankCardaId()) || Objects.nonNull(billBankCardaRelation.getPlatformCostPayMode())) {
//                    if (GoodsEnum.GOODS_TYPE_PLATFORM.getCode().equals(billBankCardaRelation.getExpenseKey()) && billBankCardaRelation.getPlatformCostPayMode().equals(PayModeEnum.PAY_MODE_ONLINE.getCode())) {
//                        billBankCardaRelation.setDeptId(AuthUtil.getDeptId());
//                        billBankCardaRelation.setBillBankCardaId(Objects.nonNull(mergePay) ? mergePay.getId() : null);
//                    }
//                    billBankCardaRelationList.add(billBankCardaRelation);
//                }
//                goodsExpenseRelations.forEach(goodsExpenseRelation -> {
//                    goodsExpenseRelation.setType(goodsExpenseBankVO.getExpenseKey());
//                });
//                //设置产品费用账户关联表参数
//                expenseAccountRelation.setExpenseId(goodsExpenseBankVO.getExpenseId());
//                expenseAccountRelation.setExpenseKey(goodsExpenseBankVO.getExpenseKey());
//                expenseAccountRelation.setGoodsId(goods.getId());
//                expenseAccountRelation.setEnterpriseType(goodsExpenseBankVO.getEnterpriseType());
//                expenseAccountRelation.setExpenseName(goodsExpenseBankVO.getExpenseName());
//                expenseAccountRelations.add(expenseAccountRelation);
//                goodsExpenseRelationList.addAll(goodsExpenseRelations);
//            }
//        }
//        goods.setGoodsExpenseRelations(goodsExpenseRelationList);
//        goods.setBillBankCardaRelation(billBankCardaRelationList);
//        goods.setExpenseAccountRelations(expenseAccountRelations);
//    }

//    @Override
//    public boolean updateGoodsRiskControl(AgentGoods goods) {
//        return false;
//    }

    @Override
    public ProductVO detail(Long id) {

//        AgentGoodsVO goodsVO = AgentGoodsWrapper.build().entityVO(goods);
//        // 查询产品标签
//        CompletableFuture<Map<Long, List<LabelVO>>> labelMapFuture = ThreadUtils.supplyAsync(() -> goodsLabelRelationService.getLabelMapInGoodsId(Lists.newArrayList(id)));
//        // 查询常见问题
//        CompletableFuture<List<GoodsQuestion>> goodsQuestionFuture = ThreadUtils.supplyAsync(() -> goodsQuestionService.selectGoodsQuestionListByGoodsId(id));
//        // 查询产品开通流程
//        CompletableFuture<List<GoodsOpeningProcess>> goodsOpeningProcessFuture = ThreadUtils.supplyAsync(() -> goodsOpeningProcessService.selectGoodsOpeningProcessListByGoodsId(id));
//        // 查询定时任务
//        CompletableFuture<List<GoodsTiming>> goodsTimingFuture = ThreadUtils.supplyAsync(() -> goodsTimingService.selectGoodsTiming(goodsVO.getId()));
//
//        // 阻塞等待执行完成
//        List<?> list = ThreadUtils.allOf(labelMapFuture, goodsQuestionFuture, goodsOpeningProcessFuture, goodsTimingFuture).join();
//        // 设置参数
//        Map<Long, List<LabelVO>> labelMap = (Map<Long, List<LabelVO>>) list.get(0);
//        List<LabelVO> labelVOList = labelMap.getOrDefault(id, Lists.newArrayList());
//        Dept dept = deptService.getById(goodsVO.getCapitalId());
//        if (Objects.nonNull(dept)) {
//            goodsVO.setCapitalName(dept.getDeptName());
//            goodsVO.setCapitalLogo(dept.getLogoSrc());
//        }
//        goodsVO.setLabelList(labelVOList);
//        goodsVO.setLabelIds(labelVOList.parallelStream().map(LabelVO::getId).collect(Collectors.toList()));
//        goodsVO.setGoodsQuestions((List<GoodsQuestion>) list.get(1));
//        goodsVO.setGoodsOpeningProcesses((List<GoodsOpeningProcess>) list.get(2));
//        goodsVO.setGoodsTimingList((List<GoodsTiming>) list.get(3));
//        if (Objects.nonNull(MyAuthUtil.getUserId())) {
//            ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//            Integer financingCode = CustomerGoodsEnum.FINANCING.getCode();
//            Integer expireCode = CustomerGoodsEnum.EXPIRE.getCode();
//            Integer disableCode = CustomerGoodsEnum.DISABLE.getCode();
//            Integer quotaChangeCode = CustomerGoodsEnum.QUOTA_CHANGE.getCode();
//            List<CustomerGoods> customerGoodsList = customerGoodsService.lambdaQuery()
//                    .in(CustomerGoods::getStatus, Lists.newArrayList(financingCode, expireCode, disableCode, quotaChangeCode))
//                    .eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
//                    .eq(CustomerGoods::getGoodsId, id)
//                    .orderByDesc(CustomerGoods::getCreateTime)
//                    .list();
//            if (!CollectionUtils.isEmpty(customerGoodsList)) {
//                CustomerGoods customerGoods = customerGoodsList.get(0);
//                goodsVO.setCustomerGoodsId(customerGoods.getId());
//                goodsVO.setStatus(customerGoods.getStatus());
//            }
//        }
        AgentGoods goods = baseMapper.selectById(id);
        ProductVO productVO = BeanUtil.copyProperties(goods, ProductVO.class);
        final String tenantId = UserUtils.getTenantId();
        productConfigInfoHandlers.parallelStream().forEach(e -> TenantBroker.runAs(tenantId, a -> {
            e.assembleProduct(productVO);
        }));
        return productVO;
    }

    @Override
    public ProductVO detailByConfigType(Long id, List<Integer> configType) {
        AgentGoods goods = baseMapper.selectById(id);
        ProductVO productVO = BeanUtil.copyProperties(goods, ProductVO.class);
        productConfigInfoHandlers.parallelStream().forEach(configInfoHandler -> TenantBroker.runAs(AuthUtil.getTenantId(), tenant -> {
            if (configType.contains(configInfoHandler.support())) {
                configInfoHandler.assembleProduct(productVO);
            }
        }));
        return productVO;
    }


    @Override
    public List<Long> onShelf(Long id) {
        // 更新状态
        List<Long> ids = Collections.singletonList(id);
        changeStatus(ids, GoodsEnum.ON_SHELF.getCode());
        productConfigInfoHandlers.forEach(e -> e.onShelfWithProduct(ids));
        return ids;
    }

//    private boolean verifyParameterGoodsIsComplete(AgentGoods goods, List<GoodsMaterial> goodsMaterials, List<GoodsExpenseRelation> goodsExpenseRelations, List<GoodsContractTemplateVO> goodsContractTemplates, List<GoodsOpeningProcess> goodsOpeningProcesses, List<GoodsQuestion> goodsQuestions, List<GoodsProcess> goodsProcessList) {
//        if (goodsOpeningProcessService.checkGoodsOpeningProcess(goodsOpeningProcesses)) {
//            throw new ServiceException("请检查产品开通流程是否填写完整!");
//        }
//        if (goodsQuestionService.checkGoodsQuestions(goodsQuestions)) {
//            throw new ServiceException("请检查产品问题是否填写完成!");
//        }
//        if (goodsExpenseRelationService.checkGoodsExpenseRelations(goodsExpenseRelations)) {
//            throw new ServiceException("请检查产品费用是否填写完成!");
//        }
//        if (goodsMaterialService.checkGoodsMaterials(goodsMaterials)) {
//            throw new ServiceException("请检查产品资料是否填写完整!");
//        }
//        if (goodsContractTemplateService.checkGoodsContractTemplates(goodsContractTemplates)) {
//            throw new ServiceException("请检查合同模板信息是否填写完整!");
//        }
//        if (goodsProcessService.checkGoodsProcess(goodsProcessList)) {
//            throw new ServiceException("请检查产品流程是否填写!");
//        }
//        checkGoodsInfo(goods);
//        if (checkGoodsInfoIsComplete(goods)) {
//            throw new ServiceException("请检查基本数据是否填写完整!");
//        }
//        //检查重复合同模板
//        contractService.checkDuplicateContracts(goodsContractTemplates);
//        //检查重复的资料收集节点
//        goodsMaterialService.checkDuplicateMaterials(goodsMaterials);
//        return false;
//    }

//    private void checkGoodsInfo(AgentGoods goods) {
//        String background = goods.getBackground();
//        Long creditFormId = goods.getCreditFormId();
//        Long scoreTemplateId = goods.getScoreTemplateId();
//        Long commodityWhiteListId = goods.getCommodityWhiteListId();
////		Integer platformCostPayMode = goods.getPlatformCostPayMode();
//        Integer bondPayType = goods.getBondPayType();
//        Long capitalId = goods.getCapitalId();
//        //收费方式 1 资方统一收费 2 平台资方单独收取
//        Integer chargeMethod = goods.getChargeMethod();
//        if (StringUtil.isBlank(background)) {
//            throw new ServiceException("产品背景不能为空!");
//        }
//        if (Objects.isNull(creditFormId)) {
//            throw new ServiceException("融资企业表单不能为空!");
//        }
//        if (Objects.isNull(scoreTemplateId)) {
//            throw new ServiceException("评分模板不能为空!");
//        }
//        if (Objects.isNull(commodityWhiteListId)) {
//            throw new ServiceException("商品白名单不能为空!");
//        }
//        if (bondPayType != null) {
//            checkCashDepositAccountIsComplete(bondPayType, goods.getId());
//        }
//        if (capitalId != null) {
//            checkCapitalAccountIsComplete(capitalId, goods.getId());
//        }
//        if (CapitalTypeEnum.PLATFORM_CHARGE_MODE.getCode().equals(chargeMethod)) {
//            checkPlatformCostPayModeIsComplete(goods.getId());
//        }
//    }

    private void checkPlatformCostPayModeIsComplete(Long goodsId) {
//		BillBankCardaRelation cardaRelation = billBankCardaRelationService.getBillBankCardaByGoodsId(goodsId, AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
//		Assert.notNull(cardaRelation, "平台费用支付方式不能为空");
//		if (CapitalTypeEnum.CAPITAL_TYPE_BANK.getCode().equals(cardaRelation.getPlatformCostPayMode())) {
//			Assert.notNull(cardaRelation.getBillBankCardaId(), "平台账户不能为空");
//		}
//		List<BillBankCardaRelation> bankCardaRelationList = billBankCardaRelationService.list(Wrappers.<BillBankCardaRelation>lambdaQuery()
//			.eq(BillBankCardaRelation::getGoodsId, goodsId)
//			.eq(BillBankCardaRelation::getAccountType, AccountTypeEnum.PLATFORM_ACCOUNT.getCode()));

    }

    private void checkCapitalAccountIsComplete(Long capitalId, Long goodsId) {
//        Dept capital = deptService.getById(capitalId);
//        if (!CapitalTypeEnum.CAPITAL_TYPE_BANK.getCode().equals(capital.getCapitalType())) {
//            BillBankCardaRelation cardaRelation = billBankCardaRelationService.getBillBankCardaByGoodsId(goodsId, AccountTypeEnum.CAPITAL_ACCOUNT.getCode());
//            Assert.notNull(cardaRelation, "资方账户不能为空");
//        }
    }

    private void checkCashDepositAccountIsComplete(Integer bondPayType, Long goodsId) {
//        if (CapitalTypeEnum.PLATFORM_COST_PAY_MODE.getCode().equals(bondPayType)) {
////			BillBankCardaRelation cardaRelation = billBankCardaRelationService.getBillBankCardaByGoodsId(goodsId, AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());
////			Assert.notNull(cardaRelation, "保证金监管账户不能为空");
//        }
    }

//    private boolean checkGoodsIsComplete(AgentGoods goods, List<GoodsMaterial> goodsMaterials, List<GoodsExpenseRelation> goodsExpenseRelations, List<GoodsContractTemplateVO> goodsContractTemplates, List<GoodsOpeningProcess> goodsOpeningProcesses, List<GoodsQuestion> goodsQuestions, List<GoodsProcess> goodsProcessList) {
//        return checkGoodsInfoIsComplete(goods)
//                || goodsContractTemplateService.checkGoodsContractTemplates(goodsContractTemplates)
//                || goodsMaterialService.checkGoodsMaterials(goodsMaterials)
//                || goodsOpeningProcessService.checkGoodsOpeningProcess(goodsOpeningProcesses)
//                || goodsQuestionService.checkGoodsQuestions(goodsQuestions)
//                || goodsExpenseRelationService.checkGoodsExpenseRelations(goodsExpenseRelations)
//                || goodsProcessService.checkGoodsProcess(goodsProcessList);
//    }


    private boolean checkGoodsInfoIsComplete(AgentGoods goods) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(goods, SerializerFeature.WriteMapNullValue), Map.class);
        boolean checkResult = false;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();

            if (value instanceof String) {
                checkResult = StringUtil.isBlank((CharSequence) value);
            } else {
                checkResult = Objects.isNull(value);
            }
        }
        return checkResult;
    }

    @Override
    public List<Long> offShelf(List<Long> ids) {
        List<AgentGoods> goodsList = baseMapper.selectBatchIds(ids);
        goodsList.forEach(e -> {
            //下架检查
            if (!GoodsEnum.ON_SHELF.getCode().equals(e.getStatus())) {
                throw new ServiceException("下架失败,产品未上架");
            }
        });
        changeStatus(ids, GoodsEnum.OFF_SHELF.getCode());
        productConfigInfoHandlers.forEach(e -> e.offShelfWithProduct(ids));
//        //将所有相关客户产品设为禁用
//        ICustomerGoodsService customerGoodsService = cn.hutool.extra.spring.SpringUtil.getBean(ICustomerGoodsService.class);
//        IEnterpriseQuotaService quotaService = cn.hutool.extra.spring.SpringUtil.getBean(IEnterpriseQuotaService.class);
//        List<CustomerGoods> list = customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery().in(CustomerGoods::getGoodsId, ids));
//        if (CollUtil.isNotEmpty(list)) {
//            List<Long> enterpriseQuotaId = list.stream().map(CustomerGoods::getEnterpriseQuotaId).collect(Collectors.toList());
//            customerGoodsService.update(Wrappers.<CustomerGoods>lambdaUpdate()
//                    .in(CustomerGoods::getGoodsId, ids)
//                    .set(CustomerGoods::getStatus, CustomerGoodsEnum.DISABLE.getCode()));
//            quotaService.update(Wrappers.<EnterpriseQuota>lambdaUpdate().in(EnterpriseQuota::getId, enterpriseQuotaId)
//                    .set(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.DISABLE.getCode())
//                    .set(EnterpriseQuota::getDisableReason, "产品已永久下架，请关注最新产品进行开通"));
//        }
//        SpringUtil.getBean(IQualityProductsService.class)
//                .update(Wrappers.<QualityProducts>lambdaUpdate().in(QualityProducts::getProductsId, ids).set(BaseEntity::getStatus, 1));
        return ids;
    }

    @Override
    public List<Long> batchOnShelf(List<Long> ids) {
        changeStatus(ids, GoodsEnum.ON_SHELF.getCode());
        return ids;
    }

    @Override
    public List<ProductVO> selectHighQualityGoodsList() {
        return leftJoinProductManager(Condition.getMPJLambdaWrapper(new AgentGoods()))
                .eq(AgentGoods::getIsHighQuality, GoodsEnum.IS_HIGH_QUALITY.getCode())
                .eq(AgentGoods::getStatus, GoodsEnum.ON_SHELF.getCode())
                .last("limit 6")
                .orderByDesc(AgentGoods::getCreateTime).list(ProductVO.class);
    }

    @Override
    public Integer support() {
        return GoodsEnum.AGENT_PURCHASE_FINANCING.getCode();
    }

    @Override
    public AgentGoodsVO selectDetailById(Long id) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }

//        AgentGoodsVO goodsVO = detail(id);
//        if (Objects.isNull(goodsVO)) {
//            return null;
//        }
//        Dept dept = deptService.getById(goodsVO.getCapitalId());
//        if (Objects.nonNull(dept)) {
//            goodsVO.setCapitalName(dept.getDeptName());
//            goodsVO.setCapitalLogo(dept.getLogoSrc());
//        }
//        if (Objects.nonNull(MyAuthUtil.getUserId())) {
//            ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//            Integer financingCode = CustomerGoodsEnum.FINANCING.getCode();
//            Integer expireCode = CustomerGoodsEnum.EXPIRE.getCode();
//            Integer disableCode = CustomerGoodsEnum.DISABLE.getCode();
//            Integer quotaChangeCode = CustomerGoodsEnum.QUOTA_CHANGE.getCode();
//            List<CustomerGoods> customerGoodsList = customerGoodsService.lambdaQuery()
//                    .in(CustomerGoods::getStatus, Lists.newArrayList(financingCode, expireCode, disableCode, quotaChangeCode))
//                    .eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
//                    .eq(CustomerGoods::getGoodsId, id)
//                    .orderByDesc(CustomerGoods::getCreateTime)
//                    .list();
//            if (!CollectionUtils.isEmpty(customerGoodsList)) {
//                CustomerGoods customerGoods = customerGoodsList.get(0);
//                goodsVO.setCustomerGoodsId(customerGoods.getId());
//            }
//        }
//        return goodsVO;

        return null;
    }

//    @Override
//    public IPage<AgentGoodsVO> selectClientGoodsList(GoodsSearchDTO goodsSearchDTO, Query query) {
//        IPage<AgentGoods> page = baseMapper.selectPage(Condition.getPage(query), buildQueryWrapper(goodsSearchDTO));
//        if (page.getTotal() <= 0) {
//            return null;
//        }
//        Page<AgentGoodsVO> pageVO = AgentGoodsWrapper.build().pageVO(page);
//        List<AgentGoodsVO> goodsVOList = pageVO.getRecords();
//        List<Long> goodsIdList = StreamUtil.map(goodsVOList, AgentGoods::getId);
//        List<Long> capitalIdList = StreamUtil.map(goodsVOList, AgentGoods::getCapitalId);
//        //获取当前用户关联的核心企业已生效的产品 k:产品id v:产品id
//        if (MyAuthUtil.isLogin()) {
//            ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//            List<CustomerGoods> customerGoodsList = customerGoodsService.lambdaQuery()
//                    .in(CustomerGoods::getGoodsId, goodsIdList)
//                    .eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
//                    .eq(ObjectUtil.isNotEmpty(goodsSearchDTO.getEnterpriseType()), CustomerGoods::getEnterpriseType, goodsSearchDTO.getEnterpriseType())
//                    .list();
//            Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getGoodsId, e -> e);
//            goodsVOList.forEach(goodsVO -> {
//                CustomerGoods customerGoods = customerGoodsMap.get(goodsVO.getId());
//                if (ObjectUtil.isNotEmpty(customerGoods)) {
//                    goodsVO.setCustomerGoodsId(customerGoods.getId());
//                    goodsVO.setStatus(customerGoods.getStatus());
//                }
//                goodsVO.setCanOpenStatus(!Objects.isNull(goodsVO.getCustomerGoodsId()));
//            });
//        }
//        Integer searchType = goodsSearchDTO.getSearchType();
//        long count = 0;
//        if (Objects.nonNull(searchType) && searchType == 1) {
//            count = StreamUtil.filterToStream(goodsVOList, goodsVO -> !Objects.isNull(goodsVO.getCustomerGoodsId())).count();
//            goodsVOList.removeIf(goodsVO -> !Objects.isNull(goodsVO.getCustomerGoodsId()));
//        }
//        if (!CollectionUtils.isEmpty(goodsVOList)) {
//            Map<Long, List<LabelVO>> labelMap = goodsLabelRelationService.getLabelMapInGoodsId(goodsIdList);
//            Map<Long, Dept> deptMap = deptService.getMapInId(capitalIdList);
//            goodsVOList.forEach(goodsVO -> {
//                goodsVO.setLabelList(labelMap.get(goodsVO.getId()));
//                Dept dept = deptMap.get(goodsVO.getCapitalId());
//                if (Objects.nonNull(dept)) {
//                    goodsVO.setCapitalLogo(dept.getLogoSrc());
//                }
//            });
//        }
//        pageVO.setTotal(pageVO.getTotal() - count);
//        pageVO.setRecords(goodsVOList);
//        return pageVO;
//    }

//    private LambdaQueryWrapper<AgentGoods> buildQueryWrapper(GoodsSearchDTO goodsSearchDTO) {
//        LambdaQueryWrapper<AgentGoods> wrapper = Wrappers.<AgentGoods>lambdaQuery()
//                .eq(AgentGoods::getStatus, GoodsEnum.ON_SHELF.getCode());
//        Integer searchType = goodsSearchDTO.getSearchType();
//        if (Objects.nonNull(searchType)) {
//            // 优质产品
//            if (searchType == 2) {
//                wrapper.eq(AgentGoods::getIsHighQuality, GoodsEnum.IS_HIGH_QUALITY.getCode());
//            }
//            // 按低额度升序
//            if (searchType == 3) {
//                wrapper.orderByAsc(AgentGoods::getAnnualInterestRateStart);
//            } else if (searchType == 4) {
//                // 按高额度降序
//                wrapper.orderByDesc(AgentGoods::getLoanAmountEnd);
//            } else {
//                // 默认安装时间倒序
//                wrapper.orderByDesc(AgentGoods::getCreateTime);
//            }
//        }
//        Integer goodsType = goodsSearchDTO.getGoodsType();
//        if (Objects.nonNull(goodsType)) {
//            wrapper.eq(AgentGoods::getType, goodsType);
//        }
//        Integer lendingMethod = goodsSearchDTO.getLendingMethod();
//        if (Objects.nonNull(lendingMethod)) {
//            wrapper.eq(AgentGoods::getLendingMethod, lendingMethod);
//        }
//        Long capitalId = goodsSearchDTO.getCapitalId();
//        if (Objects.nonNull(capitalId)) {
//            wrapper.eq(AgentGoods::getCapitalId, capitalId);
//        }
//        String goodsName = goodsSearchDTO.getGoodsName();
//        if (StringUtil.isNotBlank(goodsName)) {
//            wrapper.like(AgentGoods::getGoodsName, goodsName);
//        }
//        return wrapper;
//    }

    @Override
    public AgentGoodsVO processGoodsInfo(Long id) {
        AgentGoods goods = baseMapper.selectById(id);
        if (Objects.isNull(goods)) {
            return null;
        }
        AgentGoodsVO goodsVO = AgentGoodsWrapper.build().entityVO(goods);
        Dept dept = deptService.getDeptById(goods.getCapitalId()).getData();
        if (Objects.nonNull(dept)) {
            goodsVO.setCapitalName(dept.getDeptName());
            goodsVO.setCapitalLogo(dept.getLogoSrc());
        }
        return goodsVO;
    }

    @Override
    public Map<Long, AgentGoods> getMapInId(List<Long> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return Collections.emptyMap();
        }
        return baseMapper.selectBatchIds(goodsIdList).stream().collect(Collectors.toMap(AgentGoods::getId, obj -> obj));
    }

    @Override
    public IPage<AgentGoodsVO> getAgentCapitaList(Map<String, Object> goods, Query query, Long companyId) {
        if(true){
            throw new UnsupportedOperationException("TODO");
        }
//        IPage<AgentGoodsVO> agentGoodsVOIPage = selectGoodsPage(goods, query);
//        if (ObjectUtil.isNotEmpty(agentGoodsVOIPage.getRecords())) {
//            List<AgentGoodsVO> agentGoodsVo = StreamUtil.filter(agentGoodsVOIPage.getRecords(), goodsVo -> {
//                return companyId.equals(goodsVo.getCapitalId());
//            });
//            agentGoodsVOIPage.setRecords(agentGoodsVo);
//        }
//        return agentGoodsVOIPage;
        return null;
    }

    @Override
    public List<Product> selectList(Map<String, Object> product, List<Long> ids) {
        return leftJoinProductManager(Condition.getMPJLambdaWrapper(product, AgentGoods.class))
                .in(ObjectUtil.isNotEmpty(ids), AgentGoods::getId, ids)
                .list(Product.class);
    }

    /**
     * 根据产品id以及定时任务类型，获取倒计时时间
     *
     * @param goodsId 产品id
     * @param type    类型
     * @return 倒计时时间：LocalDateTime
     */
    @Override
    public LocalDateTime getByExpireTime(Long goodsId, Integer type) {
        return goodsTimingService.getByExpireTime(goodsId, type);
    }

//    @Override
//    public List<GoodsVO> getAgentGoodsList() {
//        //状态2 已上架
//        List<AgentGoods> goods = baseMapper.selectList(Wrappers.<AgentGoods>lambdaQuery().eq(BaseEntity::getStatus, 2));
//        if (CollUtil.isEmpty(goods)) {
//            return Collections.emptyList();
//        }
//        List<GoodsVO> goodsVOS = goods.stream().map(good -> {
//            GoodsVO goodsVO = new GoodsVO();
//            goodsVO.setId(good.getId());
//            goodsVO.setGoodsName(good.getGoodsName());
//            return goodsVO;
//        }).collect(Collectors.toList());
//        return goodsVOS;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyById(Long id) {
        //产品基础信息复制保存
        ProductVO detailVO = BeanUtil.copyProperties(detail(id), ProductVO.class, "goodsCode", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
        detailVO.setStatus(CommonConstant.OFF_RELEASE);
        detailVO.setIsHighQuality(GoodsEnum.IS_NO_HIGH_QUALITY.getCode());
        detailVO.setGoodsName(detailVO.getGoodsName().concat("copy").concat(RandomUtil.randomNumbers(3)));
        ProductDTO productDTO = entityProductDTO(detailVO);
        saveBaseProduct(productDTO);
        //产品配置信息复制保存
        productConfigInfoHandlers.forEach(e -> {
            e.copyWithGoods(productDTO, id);
        });
//        List<Long> labelIds = detail.getLabelIds();
//        AgentGoodsDTO goods = new AgentGoodsDTO();
//        BeanUtil.copyProperties(detail, goods, "goodsCode", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//        goods.setStatus(CommonConstant.OFF_RELEASE);
//        goods.setGoodsName(goods.getGoodsName().concat("copy").concat(RandomUtil.randomNumbers(3)));
//        goods.setGoodsLabelIds(labelIds);
//        goods.setIsHighQuality(2);
//        List<GoodsQuestion> goodsQuestions = goods.getGoodsQuestions();
//        if (CollUtil.isNotEmpty(goodsQuestions)) {
//            goodsQuestions = goodsQuestions.stream().map(e -> {
//                GoodsQuestion newTemp = new GoodsQuestion();
//                BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                return newTemp;
//            }).collect(Collectors.toList());
//            goods.setGoodsQuestions(goodsQuestions);
//        }
//        List<GoodsOpeningProcess> goodsOpeningProcesses = goods.getGoodsOpeningProcesses();
//        if (CollUtil.isNotEmpty(goodsOpeningProcesses)) {
//            goodsOpeningProcesses = goodsOpeningProcesses.stream().map(e -> {
//                GoodsOpeningProcess newTemp = new GoodsOpeningProcess();
//                BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                return newTemp;
//            }).collect(Collectors.toList());
//            goods.setGoodsOpeningProcesses(goodsOpeningProcesses);
//        }
//        List<GoodsMaterialVO> goodsMaterialVOS = goodsMaterialService.selectGoodsMaterial(id);
//        if (CollUtil.isNotEmpty(goodsMaterialVOS)) {
//            List<GoodsMaterialDTO> copy = BeanUtil.copy(goodsMaterialVOS, GoodsMaterialDTO.class);
//            if (CollUtil.isNotEmpty(copy)) {
//                copy = copy.stream().map(e -> {
//                    GoodsMaterialDTO newTemp = new GoodsMaterialDTO();
//                    BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                    return newTemp;
//                }).collect(Collectors.toList());
//            }
//            goods.setGoodsMaterials(copy);
//        }
//
//        List<GoodsTiming> list = goodsTimingService.lambdaQuery()
//                .eq(GoodsTiming::getGoodsId, id)
//                .list();
//        if (CollUtil.isNotEmpty(list)) {
//            list = list.stream().map(e -> {
//                GoodsTiming newTemp = new GoodsTiming();
//                BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                return newTemp;
//            }).collect(Collectors.toList());
//            goods.setGoodsTimingList(list);
//        }
//
//        List<GoodsContractTemplateVO> goodsContractTemplateListByGoodsId = goodsContractTemplateService.getGoodsContractTemplateListByGoodsId(id);
//        if (CollUtil.isNotEmpty(goodsContractTemplateListByGoodsId)) {
//            List<GoodsContractTemplateDTO> copy = BeanUtil.copy(goodsContractTemplateListByGoodsId, GoodsContractTemplateDTO.class);
//            if (CollUtil.isNotEmpty(copy)) {
//                copy = copy.stream().map(e -> {
//                    GoodsContractTemplateDTO newTemp = new GoodsContractTemplateDTO();
//                    BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                    return newTemp;
//                }).collect(Collectors.toList());
//            }
//            goods.setGoodsContractTemplates(copy);
//        }
//
//        Map<Integer, List<GoodsExpenseRelationVO>> integerListMap = StreamUtil.groupBy(goodsExpenseRelationService.getByGoodsId(id), GoodsExpenseRelationVO::getType);
//        if (CollUtil.isNotEmpty(integerListMap)) {
//            List<GoodsExpenseRelation> capitalExpenses = BeanUtil.copy(Objects.requireNonNull(integerListMap.get(1)), GoodsExpenseRelation.class);
//            if (CollUtil.isNotEmpty(capitalExpenses)) {
//                capitalExpenses = capitalExpenses.stream().map(e -> {
//                    GoodsExpenseRelation newTemp = new GoodsExpenseRelation();
//                    BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                    return newTemp;
//                }).collect(Collectors.toList());
//            }
//            List<GoodsExpenseRelation> platFromExpense = BeanUtil.copy(Objects.requireNonNull(integerListMap.get(2)), GoodsExpenseRelation.class);
//            if (CollUtil.isNotEmpty(platFromExpense)) {
//                platFromExpense = platFromExpense.stream().map(e -> {
//                    GoodsExpenseRelation newTemp = new GoodsExpenseRelation();
//                    BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                    return newTemp;
//                }).collect(Collectors.toList());
//            }
//            goods.setPlatformExpenses(platFromExpense);
//            goods.setCapitalExpenses(capitalExpenses);
//        }
//
//        Map<Integer, List<BillBankCardaRelationVO>> collect = billBankCardaRelationService.getByGoodsId(id).stream()
//                .collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getAccountType()).orElse(1)));
//        if (CollUtil.isNotEmpty(collect)) {
//            List<BillBankCardaRelationVO> platAccount = collect.get(1);
//            if (CollUtil.isNotEmpty(platAccount)) {
//                platAccount = platAccount.stream().map(e -> {
//                    BillBankCardaRelationVO newTemp = new BillBankCardaRelationVO();
//                    BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                    return newTemp;
//                }).collect(Collectors.toList());
//                goods.setPlatformBillBankCardas(platAccount.get(0));
//            }
//            List<BillBankCardaRelationVO> capitalAccount = collect.get(2);
//            if (CollUtil.isNotEmpty(capitalAccount)) {
//                capitalAccount = capitalAccount.stream().map(e -> {
//                    BillBankCardaRelationVO newTemp = new BillBankCardaRelationVO();
//                    BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                    return newTemp;
//                }).collect(Collectors.toList());
//                goods.setCapitalBillBankCardas(capitalAccount.get(0));
//            }
//            List<BillBankCardaRelationVO> cashDepositTakeBillBankCardas = collect.get(3);
//            if (CollUtil.isNotEmpty(cashDepositTakeBillBankCardas)) {
//                cashDepositTakeBillBankCardas = cashDepositTakeBillBankCardas.stream().map(e -> {
//                    BillBankCardaRelationVO newTemp = new BillBankCardaRelationVO();
//                    BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//                    return newTemp;
//                }).collect(Collectors.toList());
//                goods.setCashDepositTakeBillBankCardas(cashDepositTakeBillBankCardas.get(0));
//            }
//        }
//        List<GoodsProcess> goodsProcesses = goodsProcessService.listByGoodsId(id);
//        goodsProcesses = goodsProcesses.stream().map(e -> {
//            GoodsProcess newTemp = new GoodsProcess();
//            BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//            return newTemp;
//        }).collect(Collectors.toList());
//        goods.setGoodsProcessList(goodsProcesses);
//        saveGoods(goods);
    }


}
