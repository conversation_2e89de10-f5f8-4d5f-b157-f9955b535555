package org.springblade.procurement.handler;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.customer.handler.CustomerProductHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2023年04月07日 15:19
 */
@Service
@RequiredArgsConstructor
public class ProcurementCustomerProductHandler implements CustomerProductHandler {

    @Override
    public GoodsEnum support() {
        return GoodsEnum.AGENT_PURCHASE_FINANCING;
    }

    @Override
    public List<Long> canOpenProduct(List<Long> goodsIdList, Long userId, Integer userType) {
        return goodsIdList;
    }
}
